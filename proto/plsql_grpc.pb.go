// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: proto/plsql.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PlsqlService_Ping_FullMethodName                     = "/plsql.PlsqlService/Ping"
	PlsqlService_ValidateJwt_FullMethodName              = "/plsql.PlsqlService/ValidateJwt"
	PlsqlService_ParseToJson_FullMethodName              = "/plsql.PlsqlService/ParseToJson"
	PlsqlService_AnalyzePlsql_FullMethodName             = "/plsql.PlsqlService/AnalyzePlsql"
	PlsqlService_GetPackageBodyDependency_FullMethodName = "/plsql.PlsqlService/GetPackageBodyDependency"
)

// PlsqlServiceClient is the client API for PlsqlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PLSQL gRPC Service Definition
type PlsqlServiceClient interface {
	// Health check endpoint
	Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error)
	// JWT token validation
	ValidateJwt(ctx context.Context, in *JwtRequest, opts ...grpc.CallOption) (*JwtResponse, error)
	// Parse PLSQL to JSON AST
	ParseToJson(ctx context.Context, in *ParseJsonRequest, opts ...grpc.CallOption) (*ParseJsonResponse, error)
	// Comprehensive PLSQL analysis
	AnalyzePlsql(ctx context.Context, in *AnalyzePlsqlRequest, opts ...grpc.CallOption) (*AnalyzePlsqlResponse, error)
	// Package body dependency analysis
	GetPackageBodyDependency(ctx context.Context, in *GetDependencyRequest, opts ...grpc.CallOption) (*GetDependencyResponse, error)
}

type plsqlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlsqlServiceClient(cc grpc.ClientConnInterface) PlsqlServiceClient {
	return &plsqlServiceClient{cc}
}

func (c *plsqlServiceClient) Ping(ctx context.Context, in *PingRequest, opts ...grpc.CallOption) (*PingResponse, error) {
	out := new(PingResponse)
	err := c.cc.Invoke(ctx, PlsqlService_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlServiceClient) ValidateJwt(ctx context.Context, in *JwtRequest, opts ...grpc.CallOption) (*JwtResponse, error) {
	out := new(JwtResponse)
	err := c.cc.Invoke(ctx, PlsqlService_ValidateJwt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlServiceClient) ParseToJson(ctx context.Context, in *ParseJsonRequest, opts ...grpc.CallOption) (*ParseJsonResponse, error) {
	out := new(ParseJsonResponse)
	err := c.cc.Invoke(ctx, PlsqlService_ParseToJson_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlServiceClient) AnalyzePlsql(ctx context.Context, in *AnalyzePlsqlRequest, opts ...grpc.CallOption) (*AnalyzePlsqlResponse, error) {
	out := new(AnalyzePlsqlResponse)
	err := c.cc.Invoke(ctx, PlsqlService_AnalyzePlsql_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlServiceClient) GetPackageBodyDependency(ctx context.Context, in *GetDependencyRequest, opts ...grpc.CallOption) (*GetDependencyResponse, error) {
	out := new(GetDependencyResponse)
	err := c.cc.Invoke(ctx, PlsqlService_GetPackageBodyDependency_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlsqlServiceServer is the server API for PlsqlService service.
// All implementations should embed UnimplementedPlsqlServiceServer
// for forward compatibility.
//
// PLSQL gRPC Service Definition
type PlsqlServiceServer interface {
	// Health check endpoint
	Ping(context.Context, *PingRequest) (*PingResponse, error)
	// JWT token validation
	ValidateJwt(context.Context, *JwtRequest) (*JwtResponse, error)
	// Parse PLSQL to JSON AST
	ParseToJson(context.Context, *ParseJsonRequest) (*ParseJsonResponse, error)
	// Comprehensive PLSQL analysis
	AnalyzePlsql(context.Context, *AnalyzePlsqlRequest) (*AnalyzePlsqlResponse, error)
	// Package body dependency analysis
	GetPackageBodyDependency(context.Context, *GetDependencyRequest) (*GetDependencyResponse, error)
}

// UnimplementedPlsqlServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPlsqlServiceServer struct{}

func (UnimplementedPlsqlServiceServer) Ping(context.Context, *PingRequest) (*PingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedPlsqlServiceServer) ValidateJwt(context.Context, *JwtRequest) (*JwtResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateJwt not implemented")
}
func (UnimplementedPlsqlServiceServer) ParseToJson(context.Context, *ParseJsonRequest) (*ParseJsonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParseToJson not implemented")
}
func (UnimplementedPlsqlServiceServer) AnalyzePlsql(context.Context, *AnalyzePlsqlRequest) (*AnalyzePlsqlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnalyzePlsql not implemented")
}
func (UnimplementedPlsqlServiceServer) GetPackageBodyDependency(context.Context, *GetDependencyRequest) (*GetDependencyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPackageBodyDependency not implemented")
}
func (UnimplementedPlsqlServiceServer) testEmbeddedByValue() {}

// UnsafePlsqlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlsqlServiceServer will
// result in compilation errors.
type UnsafePlsqlServiceServer interface {
	mustEmbedUnimplementedPlsqlServiceServer()
}

func RegisterPlsqlServiceServer(s grpc.ServiceRegistrar, srv PlsqlServiceServer) {
	// If the following call pancis, it indicates UnimplementedPlsqlServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PlsqlService_ServiceDesc, srv)
}

func _PlsqlService_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlsqlServiceServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlsqlService_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlsqlServiceServer).Ping(ctx, req.(*PingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlsqlService_ValidateJwt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JwtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlsqlServiceServer).ValidateJwt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlsqlService_ValidateJwt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlsqlServiceServer).ValidateJwt(ctx, req.(*JwtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlsqlService_ParseToJson_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ParseJsonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlsqlServiceServer).ParseToJson(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlsqlService_ParseToJson_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlsqlServiceServer).ParseToJson(ctx, req.(*ParseJsonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlsqlService_AnalyzePlsql_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnalyzePlsqlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlsqlServiceServer).AnalyzePlsql(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlsqlService_AnalyzePlsql_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlsqlServiceServer).AnalyzePlsql(ctx, req.(*AnalyzePlsqlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlsqlService_GetPackageBodyDependency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDependencyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlsqlServiceServer).GetPackageBodyDependency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlsqlService_GetPackageBodyDependency_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlsqlServiceServer).GetPackageBodyDependency(ctx, req.(*GetDependencyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PlsqlService_ServiceDesc is the grpc.ServiceDesc for PlsqlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlsqlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "plsql.PlsqlService",
	HandlerType: (*PlsqlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Ping",
			Handler:    _PlsqlService_Ping_Handler,
		},
		{
			MethodName: "ValidateJwt",
			Handler:    _PlsqlService_ValidateJwt_Handler,
		},
		{
			MethodName: "ParseToJson",
			Handler:    _PlsqlService_ParseToJson_Handler,
		},
		{
			MethodName: "AnalyzePlsql",
			Handler:    _PlsqlService_AnalyzePlsql_Handler,
		},
		{
			MethodName: "GetPackageBodyDependency",
			Handler:    _PlsqlService_GetPackageBodyDependency_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/plsql.proto",
}
