// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.4
// 	protoc        v5.29.3
// source: proto/plsql.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Common Messages
type PingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingRequest) Reset() {
	*x = PingRequest{}
	mi := &file_proto_plsql_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingRequest) ProtoMessage() {}

func (x *PingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingRequest.ProtoReflect.Descriptor instead.
func (*PingRequest) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{0}
}

type PingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PingResponse) Reset() {
	*x = PingResponse{}
	mi := &file_proto_plsql_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PingResponse) ProtoMessage() {}

func (x *PingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PingResponse.ProtoReflect.Descriptor instead.
func (*PingResponse) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{1}
}

func (x *PingResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PingResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// JWT Related Messages
type JwtRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jwt           string                 `protobuf:"bytes,1,opt,name=jwt,proto3" json:"jwt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JwtRequest) Reset() {
	*x = JwtRequest{}
	mi := &file_proto_plsql_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JwtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtRequest) ProtoMessage() {}

func (x *JwtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtRequest.ProtoReflect.Descriptor instead.
func (*JwtRequest) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{2}
}

func (x *JwtRequest) GetJwt() string {
	if x != nil {
		return x.Jwt
	}
	return ""
}

type JwtResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Subject       string                 `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	Audience      string                 `protobuf:"bytes,2,opt,name=audience,proto3" json:"audience,omitempty"`
	Id            string                 `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Expiration    int64                  `protobuf:"varint,4,opt,name=expiration,proto3" json:"expiration,omitempty"`                // Unix timestamp
	IssuedAt      int64                  `protobuf:"varint,5,opt,name=issued_at,json=issuedAt,proto3" json:"issued_at,omitempty"`    // Unix timestamp
	NotBefore     int64                  `protobuf:"varint,6,opt,name=not_before,json=notBefore,proto3" json:"not_before,omitempty"` // Unix timestamp
	Issuer        string                 `protobuf:"bytes,7,opt,name=issuer,proto3" json:"issuer,omitempty"`
	Exp           string                 `protobuf:"bytes,8,opt,name=exp,proto3" json:"exp,omitempty"` // Expiration as string
	IsExpired     bool                   `protobuf:"varint,9,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
	IsExpiredFunc bool                   `protobuf:"varint,10,opt,name=is_expired_func,json=isExpiredFunc,proto3" json:"is_expired_func,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JwtResponse) Reset() {
	*x = JwtResponse{}
	mi := &file_proto_plsql_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JwtResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtResponse) ProtoMessage() {}

func (x *JwtResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtResponse.ProtoReflect.Descriptor instead.
func (*JwtResponse) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{3}
}

func (x *JwtResponse) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *JwtResponse) GetAudience() string {
	if x != nil {
		return x.Audience
	}
	return ""
}

func (x *JwtResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *JwtResponse) GetExpiration() int64 {
	if x != nil {
		return x.Expiration
	}
	return 0
}

func (x *JwtResponse) GetIssuedAt() int64 {
	if x != nil {
		return x.IssuedAt
	}
	return 0
}

func (x *JwtResponse) GetNotBefore() int64 {
	if x != nil {
		return x.NotBefore
	}
	return 0
}

func (x *JwtResponse) GetIssuer() string {
	if x != nil {
		return x.Issuer
	}
	return ""
}

func (x *JwtResponse) GetExp() string {
	if x != nil {
		return x.Exp
	}
	return ""
}

func (x *JwtResponse) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *JwtResponse) GetIsExpiredFunc() bool {
	if x != nil {
		return x.IsExpiredFunc
	}
	return false
}

// PLSQL Parsing Messages
type ParseJsonRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plsql         string                 `protobuf:"bytes,1,opt,name=plsql,proto3" json:"plsql,omitempty"`     // PLSQL content (raw string, no encoding needed)
	Comment       string                 `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"` // Optional comment
	Jwt           string                 `protobuf:"bytes,3,opt,name=jwt,proto3" json:"jwt,omitempty"`         // JWT token for authentication
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseJsonRequest) Reset() {
	*x = ParseJsonRequest{}
	mi := &file_proto_plsql_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseJsonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseJsonRequest) ProtoMessage() {}

func (x *ParseJsonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseJsonRequest.ProtoReflect.Descriptor instead.
func (*ParseJsonRequest) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{4}
}

func (x *ParseJsonRequest) GetPlsql() string {
	if x != nil {
		return x.Plsql
	}
	return ""
}

func (x *ParseJsonRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *ParseJsonRequest) GetJwt() string {
	if x != nil {
		return x.Jwt
	}
	return ""
}

type ParseJsonResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Root          *JSONTreeNode          `protobuf:"bytes,1,opt,name=root,proto3" json:"root,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParseJsonResponse) Reset() {
	*x = ParseJsonResponse{}
	mi := &file_proto_plsql_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseJsonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseJsonResponse) ProtoMessage() {}

func (x *ParseJsonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseJsonResponse.ProtoReflect.Descriptor instead.
func (*ParseJsonResponse) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{5}
}

func (x *ParseJsonResponse) GetRoot() *JSONTreeNode {
	if x != nil {
		return x.Root
	}
	return nil
}

type JSONTreeNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StmtType      string                 `protobuf:"bytes,1,opt,name=stmt_type,json=stmtType,proto3" json:"stmt_type,omitempty"`    // Statement type
	StmtName      string                 `protobuf:"bytes,2,opt,name=stmt_name,json=stmtName,proto3" json:"stmt_name,omitempty"`    // Statement name
	StmtValue     string                 `protobuf:"bytes,3,opt,name=stmt_value,json=stmtValue,proto3" json:"stmt_value,omitempty"` // Statement value/content
	Children      []*JSONTreeNode        `protobuf:"bytes,4,rep,name=children,proto3" json:"children,omitempty"`                    // Child nodes
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JSONTreeNode) Reset() {
	*x = JSONTreeNode{}
	mi := &file_proto_plsql_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JSONTreeNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JSONTreeNode) ProtoMessage() {}

func (x *JSONTreeNode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JSONTreeNode.ProtoReflect.Descriptor instead.
func (*JSONTreeNode) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{6}
}

func (x *JSONTreeNode) GetStmtType() string {
	if x != nil {
		return x.StmtType
	}
	return ""
}

func (x *JSONTreeNode) GetStmtName() string {
	if x != nil {
		return x.StmtName
	}
	return ""
}

func (x *JSONTreeNode) GetStmtValue() string {
	if x != nil {
		return x.StmtValue
	}
	return ""
}

func (x *JSONTreeNode) GetChildren() []*JSONTreeNode {
	if x != nil {
		return x.Children
	}
	return nil
}

// PLSQL Analysis Messages
type AnalyzePlsqlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plsql         string                 `protobuf:"bytes,1,opt,name=plsql,proto3" json:"plsql,omitempty"`                           // PLSQL content (raw string, no encoding needed)
	Comment       string                 `protobuf:"bytes,2,opt,name=comment,proto3" json:"comment,omitempty"`                       // Optional comment
	Jwt           string                 `protobuf:"bytes,3,opt,name=jwt,proto3" json:"jwt,omitempty"`                               // JWT token
	IsEncoded     bool                   `protobuf:"varint,4,opt,name=is_encoded,json=isEncoded,proto3" json:"is_encoded,omitempty"` // Whether the PLSQL content is encoded
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnalyzePlsqlRequest) Reset() {
	*x = AnalyzePlsqlRequest{}
	mi := &file_proto_plsql_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzePlsqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzePlsqlRequest) ProtoMessage() {}

func (x *AnalyzePlsqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzePlsqlRequest.ProtoReflect.Descriptor instead.
func (*AnalyzePlsqlRequest) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{7}
}

func (x *AnalyzePlsqlRequest) GetPlsql() string {
	if x != nil {
		return x.Plsql
	}
	return ""
}

func (x *AnalyzePlsqlRequest) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

func (x *AnalyzePlsqlRequest) GetJwt() string {
	if x != nil {
		return x.Jwt
	}
	return ""
}

func (x *AnalyzePlsqlRequest) GetIsEncoded() bool {
	if x != nil {
		return x.IsEncoded
	}
	return false
}

type AnalyzePlsqlResponse struct {
	state              protoimpl.MessageState      `protogen:"open.v1"`
	MethodInvokeList   []*SQLFunctionProcedureCall `protobuf:"bytes,1,rep,name=method_invoke_list,json=methodInvokeList,proto3" json:"method_invoke_list,omitempty"`
	IdentifierList     []*SQLFunctionProcedureCall `protobuf:"bytes,2,rep,name=identifier_list,json=identifierList,proto3" json:"identifier_list,omitempty"`
	TableReferenceList []*TableReference           `protobuf:"bytes,3,rep,name=table_reference_list,json=tableReferenceList,proto3" json:"table_reference_list,omitempty"`
	ReservedWordList   []*ReservedWord             `protobuf:"bytes,4,rep,name=reserved_word_list,json=reservedWordList,proto3" json:"reserved_word_list,omitempty"`
	PlsqlSegment       *PLSQLSegment               `protobuf:"bytes,5,opt,name=plsql_segment,json=plsqlSegment,proto3" json:"plsql_segment,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *AnalyzePlsqlResponse) Reset() {
	*x = AnalyzePlsqlResponse{}
	mi := &file_proto_plsql_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzePlsqlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzePlsqlResponse) ProtoMessage() {}

func (x *AnalyzePlsqlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzePlsqlResponse.ProtoReflect.Descriptor instead.
func (*AnalyzePlsqlResponse) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{8}
}

func (x *AnalyzePlsqlResponse) GetMethodInvokeList() []*SQLFunctionProcedureCall {
	if x != nil {
		return x.MethodInvokeList
	}
	return nil
}

func (x *AnalyzePlsqlResponse) GetIdentifierList() []*SQLFunctionProcedureCall {
	if x != nil {
		return x.IdentifierList
	}
	return nil
}

func (x *AnalyzePlsqlResponse) GetTableReferenceList() []*TableReference {
	if x != nil {
		return x.TableReferenceList
	}
	return nil
}

func (x *AnalyzePlsqlResponse) GetReservedWordList() []*ReservedWord {
	if x != nil {
		return x.ReservedWordList
	}
	return nil
}

func (x *AnalyzePlsqlResponse) GetPlsqlSegment() *PLSQLSegment {
	if x != nil {
		return x.PlsqlSegment
	}
	return nil
}

type SQLFunctionProcedureCall struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Owner                 string                 `protobuf:"bytes,1,opt,name=owner,proto3" json:"owner,omitempty"`                    // Owner/schema name
	FunName               string                 `protobuf:"bytes,2,opt,name=fun_name,json=funName,proto3" json:"fun_name,omitempty"` // Function/procedure name
	Type                  string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                      // FUNCTION or PROCEDURE
	Count                 int32                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                   // Number of invocations
	Parents               []*SQLParent           `protobuf:"bytes,5,rep,name=parents,proto3" json:"parents,omitempty"`                // Where it's called from
	IsOwnerInReservedWord bool                   `protobuf:"varint,6,opt,name=is_owner_in_reserved_word,json=isOwnerInReservedWord,proto3" json:"is_owner_in_reserved_word,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *SQLFunctionProcedureCall) Reset() {
	*x = SQLFunctionProcedureCall{}
	mi := &file_proto_plsql_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SQLFunctionProcedureCall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SQLFunctionProcedureCall) ProtoMessage() {}

func (x *SQLFunctionProcedureCall) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SQLFunctionProcedureCall.ProtoReflect.Descriptor instead.
func (*SQLFunctionProcedureCall) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{9}
}

func (x *SQLFunctionProcedureCall) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *SQLFunctionProcedureCall) GetFunName() string {
	if x != nil {
		return x.FunName
	}
	return ""
}

func (x *SQLFunctionProcedureCall) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SQLFunctionProcedureCall) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SQLFunctionProcedureCall) GetParents() []*SQLParent {
	if x != nil {
		return x.Parents
	}
	return nil
}

func (x *SQLFunctionProcedureCall) GetIsOwnerInReservedWord() bool {
	if x != nil {
		return x.IsOwnerInReservedWord
	}
	return false
}

type SQLParent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ParentType    string                 `protobuf:"bytes,1,opt,name=parent_type,json=parentType,proto3" json:"parent_type,omitempty"` // Type of parent
	ParentName    string                 `protobuf:"bytes,2,opt,name=parent_name,json=parentName,proto3" json:"parent_name,omitempty"` // Name of parent
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SQLParent) Reset() {
	*x = SQLParent{}
	mi := &file_proto_plsql_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SQLParent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SQLParent) ProtoMessage() {}

func (x *SQLParent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SQLParent.ProtoReflect.Descriptor instead.
func (*SQLParent) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{10}
}

func (x *SQLParent) GetParentType() string {
	if x != nil {
		return x.ParentType
	}
	return ""
}

func (x *SQLParent) GetParentName() string {
	if x != nil {
		return x.ParentName
	}
	return ""
}

type TableReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StatementType string                 `protobuf:"bytes,1,opt,name=statement_type,json=statementType,proto3" json:"statement_type,omitempty"` // Type of statement
	StatementName string                 `protobuf:"bytes,2,opt,name=statement_name,json=statementName,proto3" json:"statement_name,omitempty"` // Name of statement
	TableName     string                 `protobuf:"bytes,3,opt,name=table_name,json=tableName,proto3" json:"table_name,omitempty"`             // Name of table
	Count         int32                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                                     // Reference count
	DatabaseLink  bool                   `protobuf:"varint,5,opt,name=database_link,json=databaseLink,proto3" json:"database_link,omitempty"`   // Whether it's a database link
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TableReference) Reset() {
	*x = TableReference{}
	mi := &file_proto_plsql_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TableReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TableReference) ProtoMessage() {}

func (x *TableReference) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TableReference.ProtoReflect.Descriptor instead.
func (*TableReference) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{11}
}

func (x *TableReference) GetStatementType() string {
	if x != nil {
		return x.StatementType
	}
	return ""
}

func (x *TableReference) GetStatementName() string {
	if x != nil {
		return x.StatementName
	}
	return ""
}

func (x *TableReference) GetTableName() string {
	if x != nil {
		return x.TableName
	}
	return ""
}

func (x *TableReference) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TableReference) GetDatabaseLink() bool {
	if x != nil {
		return x.DatabaseLink
	}
	return false
}

type ReservedWord struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Value         string                   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Count         int32                    `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Highlight     []*ReservedWordHighlight `protobuf:"bytes,3,rep,name=highlight,proto3" json:"highlight,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReservedWord) Reset() {
	*x = ReservedWord{}
	mi := &file_proto_plsql_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReservedWord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReservedWord) ProtoMessage() {}

func (x *ReservedWord) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReservedWord.ProtoReflect.Descriptor instead.
func (*ReservedWord) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{12}
}

func (x *ReservedWord) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ReservedWord) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ReservedWord) GetHighlight() []*ReservedWordHighlight {
	if x != nil {
		return x.Highlight
	}
	return nil
}

type ReservedWordHighlight struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         string                 `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReservedWordHighlight) Reset() {
	*x = ReservedWordHighlight{}
	mi := &file_proto_plsql_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReservedWordHighlight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReservedWordHighlight) ProtoMessage() {}

func (x *ReservedWordHighlight) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReservedWordHighlight.ProtoReflect.Descriptor instead.
func (*ReservedWordHighlight) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{13}
}

func (x *ReservedWordHighlight) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *ReservedWordHighlight) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type PLSQLSegment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SegmentPrefix []*SQLSegment          `protobuf:"bytes,1,rep,name=segment_prefix,json=segmentPrefix,proto3" json:"segment_prefix,omitempty"`
	SegmentSuffix []*SQLSegment          `protobuf:"bytes,2,rep,name=segment_suffix,json=segmentSuffix,proto3" json:"segment_suffix,omitempty"`
	Declares      []*SQLSegment          `protobuf:"bytes,3,rep,name=declares,proto3" json:"declares,omitempty"`
	Functions     []*SQLSegment          `protobuf:"bytes,4,rep,name=functions,proto3" json:"functions,omitempty"`
	Procedures    []*SQLSegment          `protobuf:"bytes,5,rep,name=procedures,proto3" json:"procedures,omitempty"`
	LeftStartPos  int32                  `protobuf:"varint,6,opt,name=left_start_pos,json=leftStartPos,proto3" json:"left_start_pos,omitempty"`
	RightStartPos int32                  `protobuf:"varint,7,opt,name=right_start_pos,json=rightStartPos,proto3" json:"right_start_pos,omitempty"`
	AllPositions  []*Position            `protobuf:"bytes,8,rep,name=all_positions,json=allPositions,proto3" json:"all_positions,omitempty"` // Position array with full structure
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PLSQLSegment) Reset() {
	*x = PLSQLSegment{}
	mi := &file_proto_plsql_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PLSQLSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PLSQLSegment) ProtoMessage() {}

func (x *PLSQLSegment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PLSQLSegment.ProtoReflect.Descriptor instead.
func (*PLSQLSegment) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{14}
}

func (x *PLSQLSegment) GetSegmentPrefix() []*SQLSegment {
	if x != nil {
		return x.SegmentPrefix
	}
	return nil
}

func (x *PLSQLSegment) GetSegmentSuffix() []*SQLSegment {
	if x != nil {
		return x.SegmentSuffix
	}
	return nil
}

func (x *PLSQLSegment) GetDeclares() []*SQLSegment {
	if x != nil {
		return x.Declares
	}
	return nil
}

func (x *PLSQLSegment) GetFunctions() []*SQLSegment {
	if x != nil {
		return x.Functions
	}
	return nil
}

func (x *PLSQLSegment) GetProcedures() []*SQLSegment {
	if x != nil {
		return x.Procedures
	}
	return nil
}

func (x *PLSQLSegment) GetLeftStartPos() int32 {
	if x != nil {
		return x.LeftStartPos
	}
	return 0
}

func (x *PLSQLSegment) GetRightStartPos() int32 {
	if x != nil {
		return x.RightStartPos
	}
	return 0
}

func (x *PLSQLSegment) GetAllPositions() []*Position {
	if x != nil {
		return x.AllPositions
	}
	return nil
}

type SQLSegment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StartPos      int32                  `protobuf:"varint,1,opt,name=start_pos,json=startPos,proto3" json:"start_pos,omitempty"` // Start position in SQL
	EndPos        int32                  `protobuf:"varint,2,opt,name=end_pos,json=endPos,proto3" json:"end_pos,omitempty"`       // End position in SQL
	Sql           string                 `protobuf:"bytes,3,opt,name=sql,proto3" json:"sql,omitempty"`                            // SQL segment content
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                          // Segment name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SQLSegment) Reset() {
	*x = SQLSegment{}
	mi := &file_proto_plsql_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SQLSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SQLSegment) ProtoMessage() {}

func (x *SQLSegment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SQLSegment.ProtoReflect.Descriptor instead.
func (*SQLSegment) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{15}
}

func (x *SQLSegment) GetStartPos() int32 {
	if x != nil {
		return x.StartPos
	}
	return 0
}

func (x *SQLSegment) GetEndPos() int32 {
	if x != nil {
		return x.EndPos
	}
	return 0
}

func (x *SQLSegment) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

func (x *SQLSegment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Position struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bp            int32                  `protobuf:"varint,1,opt,name=bp,proto3" json:"bp,omitempty"`                               // BP position
	StartPos      int32                  `protobuf:"varint,2,opt,name=start_pos,json=startPos,proto3" json:"start_pos,omitempty"`   // Start position
	Sp            int32                  `protobuf:"varint,3,opt,name=sp,proto3" json:"sp,omitempty"`                               // SP position
	Np            int32                  `protobuf:"varint,4,opt,name=np,proto3" json:"np,omitempty"`                               // NP position
	Ch            string                 `protobuf:"bytes,5,opt,name=ch,proto3" json:"ch,omitempty"`                                // Character
	Token         string                 `protobuf:"bytes,6,opt,name=token,proto3" json:"token,omitempty"`                          // Token
	StringVal     string                 `protobuf:"bytes,7,opt,name=string_val,json=stringVal,proto3" json:"string_val,omitempty"` // String value
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Position) Reset() {
	*x = Position{}
	mi := &file_proto_plsql_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position) ProtoMessage() {}

func (x *Position) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position.ProtoReflect.Descriptor instead.
func (*Position) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{16}
}

func (x *Position) GetBp() int32 {
	if x != nil {
		return x.Bp
	}
	return 0
}

func (x *Position) GetStartPos() int32 {
	if x != nil {
		return x.StartPos
	}
	return 0
}

func (x *Position) GetSp() int32 {
	if x != nil {
		return x.Sp
	}
	return 0
}

func (x *Position) GetNp() int32 {
	if x != nil {
		return x.Np
	}
	return 0
}

func (x *Position) GetCh() string {
	if x != nil {
		return x.Ch
	}
	return ""
}

func (x *Position) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *Position) GetStringVal() string {
	if x != nil {
		return x.StringVal
	}
	return ""
}

// Package Body Dependency Messages
type GetDependencyRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Plsql                string                 `protobuf:"bytes,1,opt,name=plsql,proto3" json:"plsql,omitempty"`                                                              // PLSQL content (raw string, no encoding needed)
	OwnerName            string                 `protobuf:"bytes,2,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`                                     // Schema/owner name
	PackageName          string                 `protobuf:"bytes,3,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                               // Package name
	Jwt                  string                 `protobuf:"bytes,4,opt,name=jwt,proto3" json:"jwt,omitempty"`                                                                  // JWT token
	ExcludeAmbiguityWord bool                   `protobuf:"varint,5,opt,name=exclude_ambiguity_word,json=excludeAmbiguityWord,proto3" json:"exclude_ambiguity_word,omitempty"` // Whether to exclude ambiguous references
	IsEncoded            bool                   `protobuf:"varint,6,opt,name=is_encoded,json=isEncoded,proto3" json:"is_encoded,omitempty"`                                    // Whether the PLSQL content is encoded
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetDependencyRequest) Reset() {
	*x = GetDependencyRequest{}
	mi := &file_proto_plsql_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDependencyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDependencyRequest) ProtoMessage() {}

func (x *GetDependencyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDependencyRequest.ProtoReflect.Descriptor instead.
func (*GetDependencyRequest) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{17}
}

func (x *GetDependencyRequest) GetPlsql() string {
	if x != nil {
		return x.Plsql
	}
	return ""
}

func (x *GetDependencyRequest) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *GetDependencyRequest) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *GetDependencyRequest) GetJwt() string {
	if x != nil {
		return x.Jwt
	}
	return ""
}

func (x *GetDependencyRequest) GetExcludeAmbiguityWord() bool {
	if x != nil {
		return x.ExcludeAmbiguityWord
	}
	return false
}

func (x *GetDependencyRequest) GetIsEncoded() bool {
	if x != nil {
		return x.IsEncoded
	}
	return false
}

type GetDependencyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dependencies  []*PackageDependency   `protobuf:"bytes,1,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDependencyResponse) Reset() {
	*x = GetDependencyResponse{}
	mi := &file_proto_plsql_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDependencyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDependencyResponse) ProtoMessage() {}

func (x *GetDependencyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDependencyResponse.ProtoReflect.Descriptor instead.
func (*GetDependencyResponse) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{18}
}

func (x *GetDependencyResponse) GetDependencies() []*PackageDependency {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *GetDependencyResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type PackageDependency struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	PackageName           string                 `protobuf:"bytes,1,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	Owner                 string                 `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	Name                  string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Type                  string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	ReferencedPackageName string                 `protobuf:"bytes,5,opt,name=referenced_package_name,json=referencedPackageName,proto3" json:"referenced_package_name,omitempty"`
	ReferencedOwner       string                 `protobuf:"bytes,6,opt,name=referenced_owner,json=referencedOwner,proto3" json:"referenced_owner,omitempty"`
	ReferencedName        string                 `protobuf:"bytes,7,opt,name=referenced_name,json=referencedName,proto3" json:"referenced_name,omitempty"`
	ReferencedType        string                 `protobuf:"bytes,8,opt,name=referenced_type,json=referencedType,proto3" json:"referenced_type,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *PackageDependency) Reset() {
	*x = PackageDependency{}
	mi := &file_proto_plsql_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageDependency) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageDependency) ProtoMessage() {}

func (x *PackageDependency) ProtoReflect() protoreflect.Message {
	mi := &file_proto_plsql_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageDependency.ProtoReflect.Descriptor instead.
func (*PackageDependency) Descriptor() ([]byte, []int) {
	return file_proto_plsql_proto_rawDescGZIP(), []int{19}
}

func (x *PackageDependency) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *PackageDependency) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *PackageDependency) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PackageDependency) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *PackageDependency) GetReferencedPackageName() string {
	if x != nil {
		return x.ReferencedPackageName
	}
	return ""
}

func (x *PackageDependency) GetReferencedOwner() string {
	if x != nil {
		return x.ReferencedOwner
	}
	return ""
}

func (x *PackageDependency) GetReferencedName() string {
	if x != nil {
		return x.ReferencedName
	}
	return ""
}

func (x *PackageDependency) GetReferencedType() string {
	if x != nil {
		return x.ReferencedType
	}
	return ""
}

var File_proto_plsql_proto protoreflect.FileDescriptor

var file_proto_plsql_proto_rawDesc = string([]byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x22, 0x0d, 0x0a, 0x0b, 0x50, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x46, 0x0a, 0x0c, 0x50, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x22, 0x1e, 0x0a, 0x0a, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x10, 0x0a, 0x03, 0x6a, 0x77, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x77,
	0x74, 0x22, 0xa0, 0x02, 0x0a, 0x0b, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x75, 0x64, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x73, 0x75, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x73, 0x75,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x74, 0x5f, 0x62, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x78, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x69, 0x73, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x66, 0x75, 0x6e, 0x63, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64,
	0x46, 0x75, 0x6e, 0x63, 0x22, 0x54, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4a, 0x73, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6c, 0x73, 0x71,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6a, 0x77, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x77, 0x74, 0x22, 0x3c, 0x0a, 0x11, 0x50, 0x61,
	0x72, 0x73, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x27, 0x0a, 0x04, 0x72, 0x6f, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x4a, 0x53, 0x4f, 0x4e, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x74, 0x22, 0x98, 0x01, 0x0a, 0x0c, 0x4a, 0x53, 0x4f,
	0x4e, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6d,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74,
	0x6d, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6d, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x6d, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x6d, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x6d, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x4a, 0x53, 0x4f,
	0x4e, 0x54, 0x72, 0x65, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64,
	0x72, 0x65, 0x6e, 0x22, 0x76, 0x0a, 0x13, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x50, 0x6c,
	0x73, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6c,
	0x73, 0x71, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6c, 0x73, 0x71, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6a, 0x77,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x77, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x64, 0x22, 0xf5, 0x02, 0x0a, 0x14,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x50, 0x6c, 0x73, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x12, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69,
	0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x46, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x43, 0x61, 0x6c,
	0x6c, 0x52, 0x10, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x6e, 0x76, 0x6f, 0x6b, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x70,
	0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x0e, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a,
	0x14, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x70, 0x6c,
	0x73, 0x71, 0x6c, 0x2e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x12, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x64, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x10, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x64, 0x57, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x0d, 0x70, 0x6c, 0x73,
	0x71, 0x6c, 0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x4c, 0x53, 0x51, 0x4c, 0x53, 0x65,
	0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x53, 0x65, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x22, 0xdb, 0x01, 0x0a, 0x18, 0x53, 0x51, 0x4c, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x43, 0x61, 0x6c, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x75, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x75, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x07, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x07,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x38, 0x0a, 0x19, 0x69, 0x73, 0x5f, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x57, 0x6f, 0x72,
	0x64, 0x22, 0x4d, 0x0a, 0x09, 0x53, 0x51, 0x4c, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xb8, 0x01, 0x0a, 0x0e, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x22, 0x76, 0x0a, 0x0c, 0x52,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x09, 0x68, 0x69, 0x67, 0x68, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x70, 0x6c, 0x73,
	0x71, 0x6c, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x64, 0x48,
	0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x52, 0x09, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69,
	0x67, 0x68, 0x74, 0x22, 0x43, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x57,
	0x6f, 0x72, 0x64, 0x48, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x99, 0x03, 0x0a, 0x0c, 0x50, 0x4c, 0x53,
	0x51, 0x4c, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0e, 0x73, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x12, 0x38, 0x0a, 0x0e, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x75, 0x66, 0x66, 0x69, 0x78, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x6c,
	0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d,
	0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x75, 0x66, 0x66, 0x69, 0x78, 0x12, 0x2d, 0x0a,
	0x08, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x73, 0x12, 0x2f, 0x0a, 0x09,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x09, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x31, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x51, 0x4c, 0x53, 0x65, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x64, 0x75, 0x72, 0x65, 0x73,
	0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70,
	0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6c, 0x65, 0x66, 0x74, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x72, 0x69, 0x67, 0x68, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x34,
	0x0a, 0x0d, 0x61, 0x6c, 0x6c, 0x5f, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x6c, 0x6c, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x68, 0x0a, 0x0a, 0x53, 0x51, 0x4c, 0x53, 0x65, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x71, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9c,
	0x01, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x62,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x62, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6e, 0x70, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6e, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x68, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x63, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x22, 0xd5, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6a, 0x77, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6a, 0x77, 0x74,
	0x12, 0x34, 0x0a, 0x16, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x6d, 0x62, 0x69,
	0x67, 0x75, 0x69, 0x74, 0x79, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x14, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x6d, 0x62, 0x69, 0x67, 0x75, 0x69,
	0x74, 0x79, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x64, 0x22, 0x6b, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3c,
	0x0a, 0x0c, 0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x0c,
	0x64, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0xa9, 0x02, 0x0a, 0x11, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x5f,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x27, 0x0a, 0x0f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x32, 0xd7,
	0x02, 0x0a, 0x0c, 0x50, 0x6c, 0x73, 0x71, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x2f, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e,
	0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x70, 0x6c,
	0x73, 0x71, 0x6c, 0x2e, 0x50, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x34, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x77, 0x74, 0x12,
	0x11, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x4a, 0x77, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x12, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x4a, 0x77, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x73, 0x65, 0x54,
	0x6f, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x17, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x61,
	0x72, 0x73, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18,
	0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x4a, 0x73, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x7a, 0x65, 0x50, 0x6c, 0x73, 0x71, 0x6c, 0x12, 0x1a, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c,
	0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x50, 0x6c, 0x73, 0x71, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x7a, 0x65, 0x50, 0x6c, 0x73, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x55, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x42,
	0x6f, 0x64, 0x79, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x1b, 0x2e,
	0x70, 0x6c, 0x73, 0x71, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70, 0x6c, 0x73,
	0x71, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x09, 0x5a, 0x07, 0x2e, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_proto_plsql_proto_rawDescOnce sync.Once
	file_proto_plsql_proto_rawDescData []byte
)

func file_proto_plsql_proto_rawDescGZIP() []byte {
	file_proto_plsql_proto_rawDescOnce.Do(func() {
		file_proto_plsql_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_plsql_proto_rawDesc), len(file_proto_plsql_proto_rawDesc)))
	})
	return file_proto_plsql_proto_rawDescData
}

var file_proto_plsql_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_proto_plsql_proto_goTypes = []any{
	(*PingRequest)(nil),              // 0: plsql.PingRequest
	(*PingResponse)(nil),             // 1: plsql.PingResponse
	(*JwtRequest)(nil),               // 2: plsql.JwtRequest
	(*JwtResponse)(nil),              // 3: plsql.JwtResponse
	(*ParseJsonRequest)(nil),         // 4: plsql.ParseJsonRequest
	(*ParseJsonResponse)(nil),        // 5: plsql.ParseJsonResponse
	(*JSONTreeNode)(nil),             // 6: plsql.JSONTreeNode
	(*AnalyzePlsqlRequest)(nil),      // 7: plsql.AnalyzePlsqlRequest
	(*AnalyzePlsqlResponse)(nil),     // 8: plsql.AnalyzePlsqlResponse
	(*SQLFunctionProcedureCall)(nil), // 9: plsql.SQLFunctionProcedureCall
	(*SQLParent)(nil),                // 10: plsql.SQLParent
	(*TableReference)(nil),           // 11: plsql.TableReference
	(*ReservedWord)(nil),             // 12: plsql.ReservedWord
	(*ReservedWordHighlight)(nil),    // 13: plsql.ReservedWordHighlight
	(*PLSQLSegment)(nil),             // 14: plsql.PLSQLSegment
	(*SQLSegment)(nil),               // 15: plsql.SQLSegment
	(*Position)(nil),                 // 16: plsql.Position
	(*GetDependencyRequest)(nil),     // 17: plsql.GetDependencyRequest
	(*GetDependencyResponse)(nil),    // 18: plsql.GetDependencyResponse
	(*PackageDependency)(nil),        // 19: plsql.PackageDependency
}
var file_proto_plsql_proto_depIdxs = []int32{
	6,  // 0: plsql.ParseJsonResponse.root:type_name -> plsql.JSONTreeNode
	6,  // 1: plsql.JSONTreeNode.children:type_name -> plsql.JSONTreeNode
	9,  // 2: plsql.AnalyzePlsqlResponse.method_invoke_list:type_name -> plsql.SQLFunctionProcedureCall
	9,  // 3: plsql.AnalyzePlsqlResponse.identifier_list:type_name -> plsql.SQLFunctionProcedureCall
	11, // 4: plsql.AnalyzePlsqlResponse.table_reference_list:type_name -> plsql.TableReference
	12, // 5: plsql.AnalyzePlsqlResponse.reserved_word_list:type_name -> plsql.ReservedWord
	14, // 6: plsql.AnalyzePlsqlResponse.plsql_segment:type_name -> plsql.PLSQLSegment
	10, // 7: plsql.SQLFunctionProcedureCall.parents:type_name -> plsql.SQLParent
	13, // 8: plsql.ReservedWord.highlight:type_name -> plsql.ReservedWordHighlight
	15, // 9: plsql.PLSQLSegment.segment_prefix:type_name -> plsql.SQLSegment
	15, // 10: plsql.PLSQLSegment.segment_suffix:type_name -> plsql.SQLSegment
	15, // 11: plsql.PLSQLSegment.declares:type_name -> plsql.SQLSegment
	15, // 12: plsql.PLSQLSegment.functions:type_name -> plsql.SQLSegment
	15, // 13: plsql.PLSQLSegment.procedures:type_name -> plsql.SQLSegment
	16, // 14: plsql.PLSQLSegment.all_positions:type_name -> plsql.Position
	19, // 15: plsql.GetDependencyResponse.dependencies:type_name -> plsql.PackageDependency
	0,  // 16: plsql.PlsqlService.Ping:input_type -> plsql.PingRequest
	2,  // 17: plsql.PlsqlService.ValidateJwt:input_type -> plsql.JwtRequest
	4,  // 18: plsql.PlsqlService.ParseToJson:input_type -> plsql.ParseJsonRequest
	7,  // 19: plsql.PlsqlService.AnalyzePlsql:input_type -> plsql.AnalyzePlsqlRequest
	17, // 20: plsql.PlsqlService.GetPackageBodyDependency:input_type -> plsql.GetDependencyRequest
	1,  // 21: plsql.PlsqlService.Ping:output_type -> plsql.PingResponse
	3,  // 22: plsql.PlsqlService.ValidateJwt:output_type -> plsql.JwtResponse
	5,  // 23: plsql.PlsqlService.ParseToJson:output_type -> plsql.ParseJsonResponse
	8,  // 24: plsql.PlsqlService.AnalyzePlsql:output_type -> plsql.AnalyzePlsqlResponse
	18, // 25: plsql.PlsqlService.GetPackageBodyDependency:output_type -> plsql.GetDependencyResponse
	21, // [21:26] is the sub-list for method output_type
	16, // [16:21] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_proto_plsql_proto_init() }
func file_proto_plsql_proto_init() {
	if File_proto_plsql_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_plsql_proto_rawDesc), len(file_proto_plsql_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_plsql_proto_goTypes,
		DependencyIndexes: file_proto_plsql_proto_depIdxs,
		MessageInfos:      file_proto_plsql_proto_msgTypes,
	}.Build()
	File_proto_plsql_proto = out.File
	file_proto_plsql_proto_goTypes = nil
	file_proto_plsql_proto_depIdxs = nil
}
