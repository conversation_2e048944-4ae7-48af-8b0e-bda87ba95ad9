syntax = "proto3";

package plsql;

option go_package = "./proto";

// PLSQL gRPC Service Definition
service PlsqlService {
    // Health check endpoint
    rpc Ping(PingRequest) returns (PingResponse);
    
    // JWT token validation
    rpc ValidateJwt(JwtRequest) returns (JwtResponse);
    
    // Parse PLSQL to JSON AST
    rpc ParseToJson(ParseJsonRequest) returns (ParseJsonResponse);
    
    // Comprehensive PLSQL analysis
    rpc AnalyzePlsql(AnalyzePlsqlRequest) returns (AnalyzePlsqlResponse);
    
    // Package body dependency analysis
    rpc GetPackageBodyDependency(GetDependencyRequest) returns (GetDependencyResponse);
}

// Common Messages
message PingRequest {
    // Empty request for health check
}

message PingResponse {
    string message = 1;
    int64 timestamp = 2;
}

// JWT Related Messages
message JwtRequest {
    string jwt = 1;
}

message JwtResponse {
    string subject = 1;
    string audience = 2;
    string id = 3;
    int64 expiration = 4;      // Unix timestamp
    int64 issued_at = 5;       // Unix timestamp
    int64 not_before = 6;      // Unix timestamp
    string issuer = 7;
    string exp = 8;            // Expiration as string
    bool is_expired = 9;
    bool is_expired_func = 10;
}

// PLSQL Parsing Messages
message ParseJsonRequest {
    string plsql = 1;          // PLSQL content (raw string, no encoding needed)
    string comment = 2;        // Optional comment
    string jwt = 3;            // JWT token for authentication
}

message ParseJsonResponse {
    JSONTreeNode root = 1;
}

message JSONTreeNode {
    string stmt_type = 1;      // Statement type
    string stmt_name = 2;      // Statement name  
    string stmt_value = 3;     // Statement value/content
    repeated JSONTreeNode children = 4; // Child nodes
}

// PLSQL Analysis Messages
message AnalyzePlsqlRequest {
    string plsql = 1;          // PLSQL content (raw string, no encoding needed)
    string comment = 2;        // Optional comment
    string jwt = 3;            // JWT token
    bool is_encoded = 4;       // Whether the PLSQL content is encoded
}

message AnalyzePlsqlResponse {
    repeated SQLFunctionProcedureCall method_invoke_list = 1;
    repeated SQLFunctionProcedureCall identifier_list = 2;
    repeated TableReference table_reference_list = 3;
    repeated ReservedWord reserved_word_list = 4;
    PLSQLSegment plsql_segment = 5;
}

message SQLFunctionProcedureCall {
    string owner = 1;           // Owner/schema name
    string fun_name = 2;        // Function/procedure name
    string type = 3;            // FUNCTION or PROCEDURE
    int32 count = 4;            // Number of invocations
    repeated SQLParent parents = 5; // Where it's called from
    bool is_owner_in_reserved_word = 6;
}

message SQLParent {
    string parent_type = 1;     // Type of parent
    string parent_name = 2;     // Name of parent
}

message TableReference {
    string statement_type = 1;  // Type of statement
    string statement_name = 2;  // Name of statement
    string table_name = 3;      // Name of table
    int32 count = 4;            // Reference count
    bool database_link = 5;     // Whether it's a database link
}

message ReservedWord {
    string value = 1;
    int32 count = 2;
    repeated ReservedWordHighlight highlight = 3;
}

message ReservedWordHighlight {
    string value = 1;
    int32 count = 2;
}

message PLSQLSegment {
    repeated SQLSegment segment_prefix = 1;
    repeated SQLSegment segment_suffix = 2;
    repeated SQLSegment declares = 3;
    repeated SQLSegment functions = 4;
    repeated SQLSegment procedures = 5;
    int32 left_start_pos = 6;
    int32 right_start_pos = 7;
    repeated Position all_positions = 8;  // Position array with full structure
}

message SQLSegment {
    int32 start_pos = 1;        // Start position in SQL
    int32 end_pos = 2;          // End position in SQL
    string sql = 3;             // SQL segment content
    string name = 4;            // Segment name
}

message Position {
    int32 bp = 1;               // BP position
    int32 start_pos = 2;        // Start position
    int32 sp = 3;               // SP position  
    int32 np = 4;               // NP position
    string ch = 5;              // Character
    string token = 6;           // Token
    string string_val = 7;      // String value
}

// Package Body Dependency Messages
message GetDependencyRequest {
    string plsql = 1;              // PLSQL content (raw string, no encoding needed)
    string owner_name = 2;         // Schema/owner name
    string package_name = 3;       // Package name
    string jwt = 4;                // JWT token
    bool exclude_ambiguity_word = 5; // Whether to exclude ambiguous references
    bool is_encoded = 6;           // Whether the PLSQL content is encoded
}

message GetDependencyResponse {
    repeated PackageDependency dependencies = 1;
    int32 total = 2;
}

message PackageDependency {
    string package_name = 1;
    string owner = 2;
    string name = 3;
    string type = 4;
    string referenced_package_name = 5;
    string referenced_owner = 6;
    string referenced_name = 7;
    string referenced_type = 8;
}