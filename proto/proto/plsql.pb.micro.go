// Code generated by protoc-gen-micro. DO NOT EDIT.
// source: plsql.proto

package proto

import (
	fmt "fmt"
	proto "google.golang.org/protobuf/proto"
	math "math"
)

import (
	context "context"
	api "github.com/asim/go-micro/v3/api"
	client "github.com/asim/go-micro/v3/client"
	server "github.com/asim/go-micro/v3/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// Reference imports to suppress errors if they are not otherwise used.
var _ api.Endpoint
var _ context.Context
var _ client.Option
var _ server.Option

// Api Endpoints for PlsqlService service

func NewPlsqlServiceEndpoints() []*api.Endpoint {
	return []*api.Endpoint{}
}

// Client API for PlsqlService service

type PlsqlService interface {
	// Health check endpoint
	Ping(ctx context.Context, in *PingRequest, opts ...client.CallOption) (*PingResponse, error)
	// JWT token validation
	ValidateJwt(ctx context.Context, in *JwtRequest, opts ...client.CallOption) (*JwtResponse, error)
	// Parse PLSQL to JSON AST
	ParseToJson(ctx context.Context, in *ParseJsonRequest, opts ...client.CallOption) (*ParseJsonResponse, error)
	// Comprehensive PLSQL analysis
	AnalyzePlsql(ctx context.Context, in *AnalyzePlsqlRequest, opts ...client.CallOption) (*AnalyzePlsqlResponse, error)
	// Package body dependency analysis
	GetPackageBodyDependency(ctx context.Context, in *GetDependencyRequest, opts ...client.CallOption) (*GetDependencyResponse, error)
}

type plsqlService struct {
	c    client.Client
	name string
}

func NewPlsqlService(name string, c client.Client) PlsqlService {
	return &plsqlService{
		c:    c,
		name: name,
	}
}

func (c *plsqlService) Ping(ctx context.Context, in *PingRequest, opts ...client.CallOption) (*PingResponse, error) {
	req := c.c.NewRequest(c.name, "PlsqlService.Ping", in)
	out := new(PingResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlService) ValidateJwt(ctx context.Context, in *JwtRequest, opts ...client.CallOption) (*JwtResponse, error) {
	req := c.c.NewRequest(c.name, "PlsqlService.ValidateJwt", in)
	out := new(JwtResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlService) ParseToJson(ctx context.Context, in *ParseJsonRequest, opts ...client.CallOption) (*ParseJsonResponse, error) {
	req := c.c.NewRequest(c.name, "PlsqlService.ParseToJson", in)
	out := new(ParseJsonResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlService) AnalyzePlsql(ctx context.Context, in *AnalyzePlsqlRequest, opts ...client.CallOption) (*AnalyzePlsqlResponse, error) {
	req := c.c.NewRequest(c.name, "PlsqlService.AnalyzePlsql", in)
	out := new(AnalyzePlsqlResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *plsqlService) GetPackageBodyDependency(ctx context.Context, in *GetDependencyRequest, opts ...client.CallOption) (*GetDependencyResponse, error) {
	req := c.c.NewRequest(c.name, "PlsqlService.GetPackageBodyDependency", in)
	out := new(GetDependencyResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for PlsqlService service

type PlsqlServiceHandler interface {
	// Health check endpoint
	Ping(context.Context, *PingRequest, *PingResponse) error
	// JWT token validation
	ValidateJwt(context.Context, *JwtRequest, *JwtResponse) error
	// Parse PLSQL to JSON AST
	ParseToJson(context.Context, *ParseJsonRequest, *ParseJsonResponse) error
	// Comprehensive PLSQL analysis
	AnalyzePlsql(context.Context, *AnalyzePlsqlRequest, *AnalyzePlsqlResponse) error
	// Package body dependency analysis
	GetPackageBodyDependency(context.Context, *GetDependencyRequest, *GetDependencyResponse) error
}

func RegisterPlsqlServiceHandler(s server.Server, hdlr PlsqlServiceHandler, opts ...server.HandlerOption) error {
	type plsqlService interface {
		Ping(ctx context.Context, in *PingRequest, out *PingResponse) error
		ValidateJwt(ctx context.Context, in *JwtRequest, out *JwtResponse) error
		ParseToJson(ctx context.Context, in *ParseJsonRequest, out *ParseJsonResponse) error
		AnalyzePlsql(ctx context.Context, in *AnalyzePlsqlRequest, out *AnalyzePlsqlResponse) error
		GetPackageBodyDependency(ctx context.Context, in *GetDependencyRequest, out *GetDependencyResponse) error
	}
	type PlsqlService struct {
		plsqlService
	}
	h := &plsqlServiceHandler{hdlr}
	return s.Handle(s.NewHandler(&PlsqlService{h}, opts...))
}

type plsqlServiceHandler struct {
	PlsqlServiceHandler
}

func (h *plsqlServiceHandler) Ping(ctx context.Context, in *PingRequest, out *PingResponse) error {
	return h.PlsqlServiceHandler.Ping(ctx, in, out)
}

func (h *plsqlServiceHandler) ValidateJwt(ctx context.Context, in *JwtRequest, out *JwtResponse) error {
	return h.PlsqlServiceHandler.ValidateJwt(ctx, in, out)
}

func (h *plsqlServiceHandler) ParseToJson(ctx context.Context, in *ParseJsonRequest, out *ParseJsonResponse) error {
	return h.PlsqlServiceHandler.ParseToJson(ctx, in, out)
}

func (h *plsqlServiceHandler) AnalyzePlsql(ctx context.Context, in *AnalyzePlsqlRequest, out *AnalyzePlsqlResponse) error {
	return h.PlsqlServiceHandler.AnalyzePlsql(ctx, in, out)
}

func (h *plsqlServiceHandler) GetPackageBodyDependency(ctx context.Context, in *GetDependencyRequest, out *GetDependencyResponse) error {
	return h.PlsqlServiceHandler.GetPackageBodyDependency(ctx, in, out)
}
