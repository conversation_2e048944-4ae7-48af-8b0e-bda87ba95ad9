package statistics

import (
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// GetTidbStatisticsSummaryBySchema query tidb statistics schema summary
// @Summary query a tidb statistics schema summary
// @Description query a tidb statisticsk schema summary
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StatisticsSummarySchemaReq true "get tidb statistics schema request parameter"
// @Success 200 {object} message.CommonResult{data=message.StatisticsSummarySchemaResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/summary/schema [post]
func GetTidbStatisticsSummaryBySchema(c *gin.Context) {
	req := &message.StatisticsSummarySchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTidbStatisticsSummaryBySchema controller receive request %s", requestString)
		resp, err := service.GetStatisticsService().GetTidbStatisticsSummaryBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTidbStatisticsDetailTableBySchema query tidb statistics detail tables by schema
// @Summary query detail tables by schema
// @Description query detail tables by schema
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StatisticsTablesReq true "tidb statistics detail tables parameter"
// @Success 200 {object} message.ResultWithPage{data=message.StatisticsTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/detail-table/schema [post]
func GetTidbStatisticsDetailTableBySchema(c *gin.Context) {
	req := &message.StatisticsTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTidbStatisticsDetailTableBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStatisticsService().GetTidbStatisticsTableBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetTidbStatisticsAndCSTableBySchema query tidb statistics detail and channel schema tables by schema
// @Summary query detail and channel schema tables by schema
// @Description query detail tables and channel schema by schema
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StatisticsSchemaStateReq true "tidb statistics detail and channel schema tables parameter"
// @Success 200 {object} message.ResultWithPage{data=message.StatisticsSchemaStateResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/detail-table/channel-schema-table/schema [post]
func GetTidbStatisticsAndCSTableBySchema(c *gin.Context) {
	req := &message.StatisticsSchemaStateReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTidbStatisticsAndCSTableBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStatisticsService().GetTidbStatisticsAndCSTableBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetStatsTaskConflictTableBySchema query tidb statistics conflict tables by schema
// @Summary query conflict tables by schema
// @Description query conflict tables by schema
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StatisticsTaskTableDuplReq true "tidb statistics conflict tables parameter"
// @Success 200 {object} message.ResultWithPage{data=message.StatisticsTaskTableDuplResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/detail-table/conflict [post]
func GetStatsTaskConflictTableBySchema(c *gin.Context) {
	req := &message.StatisticsTaskTableDuplReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTidbStatisticsDetailTableBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStatisticsService().GetStatsTaskConflictTableBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// InitTidbStatistics init tidb statistics tables
// @Summary init tidb statistics tables
// @Description init tidb statistics tables
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.InitTidbStatisticsReq true "init tidb statistics tables request parameter"
// @Success 200 {object} message.CommonResult{data=message.InitTidbStatisticsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/detail-table/init [post]
func InitTidbStatistics(c *gin.Context) {
	req := &message.InitTidbStatisticsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("InitTidbStatistics controller receive request %s", requestString)
		resp, err := service.GetStatisticsService().InitTidbStatistics(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTidbStatsTable update tidb statistics tables
// @Summary update tidb statistics tables
// @Description update tidb statistics tables
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.UpdateTidbStatisticsReq true "update tidb statistics tables request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateTidbStatisticsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/detail-table/update [post]
func UpdateTidbStatsTable(c *gin.Context) {
	req := &message.UpdateTidbStatisticsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("InitTidbStatistics controller receive request %s", requestString)
		resp, err := service.GetStatisticsService().UpdateTidbStatsTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteStatsTaskConflictTables batch delete task tables by table name interface
// @Summary batch delete task tables by table name
// @Description batch delete task tables by table name
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param taskIds body message.DeleteStatsTaskConflictTablesReq true "batch delete task tables  request parameter"
// @Success 200 {object} message.CommonResult{data=message.DeleteStatsTaskConflictTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router  /tidb-statistics/detail-table/delete [delete]
func DeleteStatsTaskConflictTables(c *gin.Context) {
	log.Infof("DeleteStatsTaskConflictTables controller receive channelId:%s taskId:%d", c.Param("channelId"), c.Param("taskId"))
	req := &message.DeleteStatsTaskConflictTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DeleteStatsTaskConflictTables controller receive request: %s", requestString)
		resp, err := service.GetStatisticsService().DeleteTidbStatsTables(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTidbStatsTableCount get tidb tables count
// @Summary get tidb tables count
// @Description get tidb tables count
// @Tags statistics
// @Accept application/json
// @Produce application/json
// @Param objReq body message.GetTidbStatsTableCountReq true "get tidb tables count request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTidbStatsTableCountResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /tidb-statistics/detail-table/get-count [post]
func GetTidbStatsTableCount(c *gin.Context) {
	req := &message.GetTidbStatsTableCountReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTidbStatsTableCount controller receive request %s", requestString)
		resp, err := service.GetStatisticsService().GetTidbStatsTableCount(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
