package datacompare

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// DataCompareReExecution data compare task re-execution interface
// @Summary data compare task re-execution
// @Description data compare task re-execution
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param dataCompareReExecutionReq body message.DataCompareReExecutionReq true "data compare task re-execution request parameter"
// @Success 200 {object} message.CommonResult{data=message.DataCompareReExecutionResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/re-execution [post]
func DataCompareReExecution(c *gin.Context) {
	log.Infof("DataCompareReExecution controller receive request channelId:%s, taskId:%s", c.<PERSON>("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.DataCompareReExecutionReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DataCompareReExecution controller receive request: %s", requestString)

		validateErr := service.GetDataCompareService().ValidateDataCompareSummaryStatus(c, req.TaskId, req.Ids)
		if validateErr != nil {
			controller.HandleResponse(c, nil, validateErr, nil)
			return
		}

		resp, err := service.GetDataCompareService().DataCompareReExecution(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDataCompareSummaryBySchema query a compare summary schema
// @Summary query data compare summary schema
// @Description query data compare summary schema
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DataCompareSchemaReq true "get data compare request parameter"
// @Success 200 {object} message.CommonResult{data=message.DataCompareSchemaResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /data-compare/summary/schema [post]
func GetDataCompareSummaryBySchema(c *gin.Context) {
	req := &message.DataCompareSchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetDataCompareSummaryBySchema controller receive request %s", requestString)
		resp, err := service.GetDataCompareService().GetDataCompareSummaryBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDataCompareDetailTableBySchema query a compare summary schema
// @Summary query data compare summary schema
// @Description query data compare summary schema
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DataCompareSchemaStateReq true "get data compare request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.DataCompareSchemaStateResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /data-compare/detail-table/schema [post]
func GetDataCompareDetailTableBySchema(c *gin.Context) {
	req := &message.DataCompareSchemaStateReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetDataCompareDetailTableBySchema controller receive request %s", requestString)
		resp, page, err := service.GetDataCompareService().GetDataCompareDetailTableBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetDataCompareDetailChunkByTable query a compare table chunk detail
// @Summary query data compare table chunk detail
// @Description query data compare table chunk detail
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DataCompareTableChunkReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.DataCompareTableChunkResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /data-compare/detail-chunk/table [post]
func GetDataCompareDetailChunkByTable(c *gin.Context) {
	req := &message.DataCompareTableChunkReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetDataCompareDetailChunkByTable controller receive request %s", requestString)
		resp, err := service.GetDataCompareService().GetDataCompareDetailChunkByTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ExecuteDataCompareEnvDeployTask execute data compare env deploy tasks
// @Summary execute data compare env deploy tasks
// @Description execute data compare env deploy tasks, runMode = [ 1,2 ], 1 means execute(ignore success task), 2 means re-execute(all task status)
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.ExecuteDataCompareEnvDeployTaskReq true "execute data compare env deploy tasks request parameter"
// @Success 200 {object} message.CommonResult{data=message.ExecuteDataCompareEnvDeployTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/env-deploy/{taskId}/execute [post]
func ExecuteDataCompareEnvDeployTask(c *gin.Context) {
	req := &message.ExecuteDataCompareEnvDeployTaskReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ExecuteDataCompareEnvDeployTask controller receive body %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetDataCompareService().ExecuteDataCompareEnvDeployTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListDataCompareEnvDeployTask list data compare env deploy tasks
// @Summary list data compare env deploy tasks
// @Description list data compare env deploy tasks
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListDataCompareEnvDeployTaskReq true "list data compare env deploy task request parameter"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.ListDataCompareEnvDeployTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/env-deploy/{taskId} [get]
func ListDataCompareEnvDeployTask(c *gin.Context) {
	req := &message.ListDataCompareEnvDeployTaskReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListDataCompareEnvDeployTask controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetDataCompareService().ListDataCompareEnvDeployTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateDataCompareEnvDeployTask update data compare env deploy task
// @Summary update data compare env deploy task
// @Description update data compare env deploy task
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.UpdateDataCompareEnvDeployTaskReq true "update data compare env deploy task request parameter"
// @Param taskId path int true "taskId"
// @Param envDeployId path int true "envDeployId"
// @Success 200 {object} message.CommonResult{data=message.UpdateDataCompareEnvDeployTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/env-deploy/{taskId}/{envDeployId} [post]
func UpdateDataCompareEnvDeployTask(c *gin.Context) {
	req := &message.UpdateDataCompareEnvDeployTaskReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	envDeployId, _ := strconv.Atoi(c.Param("envDeployId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateDataCompareEnvDeployTask controller receive body %s", requestString)
		req.TaskId = taskId
		req.EnvDeployId = envDeployId
		resp, err := service.GetDataCompareService().UpdateDataCompareEnvDeployTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DownloadFixSQL download fix sql
// @Summary download fix sql
// @Description download fix sql (supports both bulk download and filtered single file download)
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DownloadFixSQLReq true "download fix sql parameter. For filtered download, provide file_path and filter parameters"
// @Success 200 {object} message.CommonResult{data=message.DownloadFixSQLResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/fix-sql/download [post]
func DownloadFixSQL(c *gin.Context) {
	req := &message.DownloadFixSQLReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadFixSQL controller receive request: %s", requestString)
		resp, err := service.GetDataCompareService().DownloadFixSQL(c, req)
		if err != nil {
			log.Errorf("DownloadFixSQL controller failed, taskId:%d, err:%s", req.TaskId, err)
		}
		controller.HandleFileResponse(c, resp, err)
	}
}

// ListFixSQL list fix sql files with pagination
// @Summary list fix sql files with pagination
// @Description list fix sql files with conditions and pagination
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.ListFixSQLReq true "list fix sql request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.ListFixSQLResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/fix-sql/list [post]
func ListFixSQL(c *gin.Context) {
	req := &message.ListFixSQLReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListFixSQL controller receive request: %s", requestString)
		resp, page, err := service.GetDataCompareService().ListFixSQL(c, req)
		if err != nil {
			log.Errorf("ListFixSQL controller failed, taskId:%d, err:%s", req.TaskId, err)
		}
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetFixSQLContent get fix sql file content with pagination and filtering
// @Summary get fix sql file content
// @Description get fix sql file content with page-based pagination and optional filtering
// @Tags datacompare
// @Accept application/json
// @Produce application/json
// @Param objReq body message.GetFixSQLContentReq true "get fix sql content request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.GetFixSQLContentResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /data-compare/fix-sql/content [post]
func GetFixSQLContent(c *gin.Context) {
	req := &message.GetFixSQLContentReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFixSQLContent controller receive request: %s", requestString)
		resp, page, err := service.GetDataCompareService().GetFixSQLContent(c, req)
		if err != nil {
			log.Errorf("GetFixSQLContent controller failed, taskId:%d, filePath:%s, err:%s", req.TaskId, req.FilePath, err)
		}
		controller.HandleResponse(c, resp, err, page)
	}
}
