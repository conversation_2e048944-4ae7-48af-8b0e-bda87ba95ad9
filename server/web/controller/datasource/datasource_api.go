package datasource

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/util/log"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"github.com/gin-gonic/gin"
)

// Create datasource interface
// @Summary create  datasource
// @Description create datasource
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param datasource body message.CreateOrUpdateDataSourceReq true "create datasource request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateDataSourceResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/ [post]
func Create(c *gin.Context) {
	req := &message.CreateOrUpdateDataSourceReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Create datasource controller receive request: %s", requestString)
		resp, err := service.GetDatasourceService().CreateDataSource(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Update datasource interface
// @Summary update  datasource
// @Description update datasource
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Param datasource body message.CreateOrUpdateDataSourceReq true "update datasource request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateDataSourceResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /datasource/{dataSourceId}/update [post]
func Update(c *gin.Context) {
	req := &message.CreateOrUpdateDataSourceReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Update datasource controller receive dataSourceId:%s, receive request: %s", c.Param("dataSourceId"), requestString)
		req.DataSourceID, _ = strconv.Atoi(c.Param("dataSourceId"))
		resp, err := service.GetDatasourceService().UpdateDataSource(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Get datasource version interface
// @Summary get datasource version
// @Description get datasource version
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param datasource body message.CreateOrUpdateDataSourceReq true "get datasource version request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateDataSourceResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/version [post]
func Version(c *gin.Context) {
	req := &message.CreateOrUpdateDataSourceReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Get datasource version controller receive request: %s", requestString)
		resp, err := service.GetDatasourceService().GetDataSourceVersion(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Get datasource version code interface
// @Summary get datasource version code
// @Description get datasource version code
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param datasource body message.CreateOrUpdateDataSourceReq true "get datasource version code request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateDataSourceResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/oracle/versionCode [post]
func VersionCode(c *gin.Context) {
	req := &message.CreateOrUpdateDataSourceReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Get datasource version code controller receive request: %s", requestString)
		resp, err := service.GetDatasourceService().GetOracleVersionCode(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// BatchDelete datasource interface
// @Summary batch delete  datasource
// @Description batch delete datasource
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceIds body message.BatchDeleteDataSourceReq true "batch delete datasource request parameter"
// @Success 200 {object} message.CommonResult{data=message.BatchDeleteDataSourceResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/delete [post]
func BatchDelete(c *gin.Context) {
	req := &message.BatchDeleteDataSourceReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchDelete datasource controller receive request: %s", requestString)
		resp, err := service.GetDatasourceService().BatchDelete(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Detail query a datasource detail
// @Summary query a datasource detail
// @Description query a datasource detail
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.CommonResult{data=message.DetailDataSourceResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /datasource/{dataSourceId}/ [get]
func Detail(c *gin.Context) {
	log.Infof("DsgTask datasource controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	req := &message.DetailDataSourceReq{DataSourceID: iDataSourceId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetDatasourceService().Detail(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// TestConnection test datasource connection interface
// @Summary test  datasource connection
// @Description test  datasource connection
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.CommonResult{data=message.TestConnectionResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/{dataSourceId}/test [get]
func TestConnection(c *gin.Context) {
	log.Infof("test datasource connection controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	req := &message.TestConnectionReq{DataSourceID: iDataSourceId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetDatasourceService().TestConnection(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// TryConnection test datasource connection interface
// @Summary try datasource connection
// @Description try datasource connection
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.CommonResult{data=message.TestConnectionResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/try [post]
func TryConnection(c *gin.Context) {
	log.Infof("try datasource connection controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	req := &message.TryDataSourceReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("try datasource controller receive request: %s", requestString)
		resp, err := service.GetDatasourceService().TryConnection(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// List list datasource interface
// @Summary list  datasource
// @Description list  datasource
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param page body message.ListDataSourcesReq true "list datasource request"
// @Success 200 {object} message.ResultWithPage{data=[]message.DataSource}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/list [post]
func List(c *gin.Context) {
	req := &message.ListDataSourcesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("List datasource controller receive request %s", requestString)
		resp, page, err := service.GetDatasourceService().List(c, req)
		controller.HandleResponse(c, resp.DataSources, err, page)
	}
}

// GetDataSourceSchemas get datasource schemas interface
// @Summary get datasource schemas
// @Description get datasource schemas
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.CommonResult{data=message.GetDataSourceSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /datasource/:dataSourceId/schemas [get]
func GetDataSourceSchemas(c *gin.Context) {
	log.Infof("get datasource schemas controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	req := &message.GetDataSourceSchemasReq{DataSourceID: iDataSourceId}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetDatasourceService().GetDataSourceSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDataSourceSchemaTables get datasource tables in schemas interface
// @Summary get datasource tables in schemas
// @Description get datasource tables in schemas
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Param schemas body message.GetDataSourceSchemaTablesReq true "get datasource tables in schemas request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDataSourceSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/:dataSourceId/schema/tables [post]
func GetDataSourceSchemaTables(c *gin.Context) {
	log.Infof("GetDataSourceSchemaTables controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	req := &message.GetDataSourceSchemaTablesReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		req.DataSourceID = iDataSourceId
		resp, err := service.GetDatasourceService().GetDataSourceSchemaTables(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

func Hello(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "pong",
	})
}

// GetPrivilegeCheckList get datasource schemas interface
// @Summary get datasource schemas
// @Description get datasource schemas
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.CommonResult{data=message.GetDataSourceSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /datasource/:dataSourceId/getPrivilegeCheckList [get]
func GetPrivilegeCheckList(c *gin.Context) {
	log.Infof("get datasource schemas controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	req := &message.DetailDataSourceReq{DataSourceID: iDataSourceId}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetDatasourceService().GetPrivilegeCheckList(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTablePartitions get datasource schemas interface
// @Summary get datasource schemas
// @Description get datasource schemas
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.CommonResult{data=message.GetDataSourceSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /datasource/:dataSourceId/getPartitions [POST]
func GetTablePartitions(c *gin.Context) {
	log.Infof("get datasource schemas controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	req := &message.GetTablePartitionReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetDatasourceService().GetTablePartitions(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Get datasource version by id
// @Summary get datasource version by id
// @Description get datasource version by id
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.GetDataSourceVersionResp
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/{dataSourceId}/version [get]
func VersionById(c *gin.Context) {
	log.Infof("Get datasource version by id controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	resp, err := service.GetDatasourceService().GetDataSourceVersionById(c, iDataSourceId)
	controller.HandleResponse(c, resp, err, nil)
}

// Get datasource character set by id
// @Summary get datasource character set by id
// @Description get datasource character set by id
// @Tags datasource
// @Accept application/json
// @Produce application/json
// @Param dataSourceId path int true "dataSourceId"
// @Success 200 {object} message.GetDataSourceCharacterSetResp
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /datasource/{dataSourceId}/charset [get]
func CharacterSetById(c *gin.Context) {
	log.Infof("Get datasource character set by id controller receive request dataSourceId: %s", c.Param("dataSourceId"))
	iDataSourceId, _ := strconv.Atoi(c.Param("dataSourceId"))
	resp, err := service.GetDatasourceService().GetDataSourceCharacterSetByIdResp(c, iDataSourceId)
	controller.HandleResponse(c, resp, err, nil)
}
