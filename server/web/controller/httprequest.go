package controller

import (
	"encoding/json"
	"net/http"

	"gitee.com/pingcap_enterprise/tms/common/errors"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

// HandleJsonRequestWithBuiltReq
// @Description: handle common json request with a built request
// validate request using validator.New().Struct(req)
// serialize request using json.Marshal(req)
// @Parameter c
// @Parameter req
// @return bool
// @return requestBody
func HandleJsonRequestWithBuiltReq(c *gin.Context, req interface{}) (string, bool) {
	return HandleRequest(c,
		req,
		func(c *gin.Context, req interface{}) error {
			return nil
		},
		func(req interface{}) error {
			return validator.New().Struct(req)
		},
		func(req interface{}) ([]byte, error) {
			return json.Marshal(req)
		},
	)
}

// HandleJsonRequestFromQuery
// @Description: handle common json request from query
// build request using c.<PERSON>y(req, binding.JSON)
// validate request using validator.New().Struct(req)
// serialize request using json.Marshal(req)
// @Parameter c
// @Parameter req
// @Parameter appenders append request data out of http body, such as id in path
// @return ok
// @return requestBody
func HandleJsonRequestFromQuery(c *gin.Context,
	req interface{},
	appenders ...func(c *gin.Context, req interface{}) error,
) (requestBody string, ok bool) {
	return HandleRequest(c,
		req,
		func(c *gin.Context, req interface{}) error {
			err := c.ShouldBindQuery(req)
			if err != nil {
				return err
			}
			for _, appender := range appenders {
				err = appender(c, req)
				if err != nil {
					return err
				}
			}
			return nil
		},
		func(req interface{}) error {
			return validator.New().Struct(req)
		},
		func(req interface{}) ([]byte, error) {
			return json.Marshal(req)
		},
	)
}

// HandleJsonRequestFromBody
// @Description: handle common json request from body
// build request using c.ShouldBindBodyWith(req, binding.JSON)
// validate request using validator.New().Struct(req)
// serialize request using json.Marshal(req)
// @Parameter c
// @Parameter req
// @Parameter appenders append request data out of http body, such as id in path
// @return err
// @return requestBody
func HandleJsonRequestFromBody(c *gin.Context,
	req interface{},
	appenders ...func(c *gin.Context, req interface{}) error,
) (requestBody string, ok bool) {
	return HandleRequest(c,
		req,
		func(c *gin.Context, req interface{}) error {
			err := c.ShouldBindBodyWith(req, binding.JSON)
			if err != nil {
				return err
			}
			for _, appender := range appenders {
				err = appender(c, req)
				if err != nil {
					return err
				}
			}
			return nil
		},
		func(req interface{}) error {
			return validator.New().Struct(req)
		},
		func(req interface{}) ([]byte, error) {
			return json.Marshal(req)
		},
	)
}

// HandleRequest
// @Description: handle request
// @Parameter c
// @Parameter req
// @Parameter builder build request from gin.Context
// @Parameter validator validate request
// @Parameter serializer serialize request to string
// @return err
// @return requestBody
func HandleRequest(c *gin.Context,
	req interface{},
	builder func(c *gin.Context, req interface{}) error,
	validator func(req interface{}) error,
	serializer func(req interface{}) ([]byte, error)) (string, bool) {

	requestContent := ""
	ok := errors.OfNullable(nil).
		BreakIf(func() error {
			return errors.OfNullable(builder(c, req)).
				Map(func(err error) error {
					return errors.NewError(errors.TIMS_MARSHAL_ERROR, err.Error())
				}).
				Present()
		}).
		BreakIf(func() error {
			return errors.OfNullable(validator(req)).
				Map(func(err error) error {
					return errors.NewError(errors.TIMS_PARAMETER_INVALID, err.Error())
				}).
				Present()
		}).
		BreakIf(func() error {
			bytes, err := serializer(req)
			return errors.OfNullable(err).
				Map(func(err error) error {
					return errors.NewError(errors.TIMS_MARSHAL_ERROR, err.Error())
				}).
				Else(func() {
					requestContent = string(bytes)
				}).
				Present()
		}).
		If(func(err error) {
			c.JSON(http.StatusBadRequest, Fail(int(err.(errors.MSError).GetCode()), err.Error()))
		}).
		IfNil()
	return requestContent, ok
}
