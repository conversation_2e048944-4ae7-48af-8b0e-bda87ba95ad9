package sqlanalyzer

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"

	sqlanalyzepkg "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze"
	"gitee.com/pingcap_enterprise/tms/server/service"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// DownloadSqlAnalyzerReport Download SqlAnalyzer Report
// @Summary Download SqlAnalyzer Report
// @Description Download SqlAnalyzer Report
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DownloadSqlReportBySchemaReq true "download sql report by schema"
// @Success 200 {object} message.CommonResult{data=message.DownloadSqlReportBySchemaResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/report/download [post]
func DownloadSqlAnalyzerReport(c *gin.Context) {
	req := &message.DownloadSqlReportBySchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadSqlAnalyzerReport controller receive request %s", requestString)
		reportPath := config.GetGlobalConfig().DataDir
		reportName := fmt.Sprintf("%s/sqlanalyzer/tms_o2t_sql_analyzer_%d_%s.json_%s.html", reportPath, req.TaskId, strings.Join(req.Schema, "_"), strings.ToLower(req.ReportType))
		req.ReportFilePath = reportName

		resp, err := sqlanalyzepkg.DownloadSqlAnalyzeReportBySchema(c, req)
		if err != nil {
			controller.HandleResponse(c, resp, err, nil)
			return
		}

		if _, err := os.Stat(reportName); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				log.Infof("report file %s not exists, err:%s", reportName, err)
				controller.HandleResponse(c, &message.MigrationStructureDDLProgressResp{}, err, nil)
			}
			return
		}

		log.Infof("start to download sql analyzer html file:%s", reportName)
		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+reportName)
		c.Header("Content-Transfer-Encoding", "binary")
		// c.Header("Transfer-Encoding", "true")
		c.Header("Cache-Control", "no-cache")
		c.File(reportName)
	}
}

// DownloadSqlAnalyzerReportZip Download SqlAnalyzer Report
// @Summary Download SqlAnalyzer Report
// @Description Download SqlAnalyzer Report
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DownloadSqlReportBySchemaReq true "download sql report by schema"
// @Success 200 {object} message.CommonResult{data=message.DownloadSqlReportBySchemaResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/report/downloadzip [post]
func DownloadSqlAnalyzerReportZip(c *gin.Context) {
	req := &message.DownloadSqlReportBySchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadSqlAnalyzerReport controller receive request %s", requestString)
		reportName := fmt.Sprintf("%s/sqlanalyzer/sqlanalyzer_%d.zip", config.GetGlobalConfig().DataDir, req.TaskId)
		req.ReportFilePath = reportName

		resp, err := sqlanalyzepkg.DownloadSqlAnalyzeReportZipBySchema(c, req)
		controller.HandleFileResponse(c, resp, err)
	}
}

// ListSQLAnalyzerTask list sql analyzer task
// @Summary list sql analyzer task
// @Description list sql analyzer task , taskModeInt = [ 0,1 ], which 0 means LOCAL, 1 means 非LOCAL
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListSQLAnalyzeTaskReq true "list sql analyze task request parameter"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.ListSQLAnalyzeTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId} [get]
func ListSQLAnalyzerTask(c *gin.Context) {
	req := &message.ListSQLAnalyzeTaskReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListSQLAnalyzeTask controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().ListSQLAnalyzeTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateSQLAnalyzerTask update sql analyzer task
// @Summary update sql analyzer task
// @Description update sql analyzer task
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.UpdateSQLAnalyzeTaskReq true "update sql analyze task request parameter"
// @Param taskId path int true "taskId"
// @Param sqlanalyzeId path int true "sqlanalyzeId"
// @Success 200 {object} message.CommonResult{data=message.UpdateSQLAnalyzeTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/{sqlanalyzeId} [post]
func UpdateSQLAnalyzerTask(c *gin.Context) {
	req := &message.UpdateSQLAnalyzeTaskReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	sqlanalyzeId, _ := strconv.Atoi(c.Param("sqlanalyzeId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateSQLAnalyzerTask controller receive body %s", requestString)
		req.TaskId = taskId
		req.SQLAnalyzeId = sqlanalyzeId
		resp, err := service.GetSQLAnalyzerService().UpdateSQLAnalyzeTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ExecuteSQLAnalyzerEnvDeployTask execute sql analyzer env deploy task
// @Summary execute sql analyzer env deploy task
// @Description execute sql analyzer env deploy task,  taskModeInt = [ 0,1 ], which 0 means LOCAL, 1 means 非LOCAL, runMode = [ 1,2 ], 1 means execute(ignore success task), 2 means re-execute(all task status)
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.ExecuteSQLAnalyzeEnvDeployTaskReq true "execute sql analyze task request parameter"
// @Success 200 {object} message.CommonResult{data=message.ExecuteSQLAnalyzeEnvDeployTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/execute [post]
func ExecuteSQLAnalyzerEnvDeployTask(c *gin.Context) {
	req := &message.ExecuteSQLAnalyzeEnvDeployTaskReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ExecuteSQLAnalyzerEnvDeployTask controller receive body %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().ExecuteSQLAnalyzeEnvDeployTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// StartSpaCollect start dbms job
// @Summary start dbms job
// @Description start dbms job ,which job id is contains in task comment
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StartSpaCollectReq true "empty"
// @Success 200 {object} message.CommonResult{data=message.StartSpaCollectResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/{channelId}/{taskId}/start-spa-collect [post]
func StartSpaCollect(c *gin.Context) {
	req := &message.StartSpaCollectReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	log.Infof("StartSpaCollect controller receive taskId:%d, channelId:%d", taskId, channelId)
	req.TaskId = taskId
	req.ChannelId = channelId
	resp, err := service.GetSQLAnalyzerService().StartSpaCollect(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// StartSQLFileReplay start sql replay by file
// @Summary start sql replay by file
// @Description start sql replay by file
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StartSQLFileReplayReq true "empty"
// @Success 200 {object} message.CommonResult{data=message.StartSQLFileReplayResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/{channelId}/{taskId}/start-sqlfile-replay [post]
func StartSQLFileReplay(c *gin.Context) {
	req := &message.StartSQLFileReplayReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	log.Infof("StartSQLFileReplay controller receive taskId:%d, channelId:%d", taskId, channelId)
	req.TaskId = taskId
	req.ChannelId = channelId
	resp, err := service.GetSQLAnalyzerService().StartSQLFileReplay(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// CallSpaCollect call and exec dbms job
// @Summary call and exec dbms job
// @Description call and exec dbms job , callType 0=all, 1=当前SQL任务, 2=历史SQL任务
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.CallSpaCollectReq true "empty"
// @Success 200 {object} message.CommonResult{data=message.CallSpaCollectResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/{channelId}/{taskId}/call-spa-collect [post]
func CallSpaCollect(c *gin.Context) {
	req := &message.CallSpaCollectReq{}
	req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))
	req.TaskId, _ = strconv.Atoi(c.Param("taskId"))
	log.Infof("CallSpaCollect controller receive channelId:%d", req.ChannelId)
	resp, err := service.GetSQLAnalyzerService().CallSpaCollect(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetConfiguration get sqlanalyzer configuration
// @Summary get sqlanalyzer configuration
// @Description get sqlanalyzer configuration
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.GetConfigurationReq true "empty"
// @Success 200 {object} message.CommonResult{data=message.GetConfigurationResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/{channelId}/{taskId}/configuration [post]
func GetConfiguration(c *gin.Context) {
	req := &message.GetConfigurationReq{}
	req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))
	req.TaskId, _ = strconv.Atoi(c.Param("taskId"))
	log.Infof("GetConfiguration controller receive channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	resp, err := service.GetSQLAnalyzerService().GetConfiguration(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// StopSpaCollect stop dbms job
// @Summary stop dbms job
// @Description stop dbms job ,which job id is contains in task comment
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.StopSpaCollectReq true "empty"
// @Success 200 {object} message.CommonResult{data=message.StopSpaCollectResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/{channelId}/{taskId}/stop-spa-collect [post]
func StopSpaCollect(c *gin.Context) {
	req := &message.StopSpaCollectReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	log.Infof("StopSpaCollect controller receive taskId:%d, channelId:%d", taskId, channelId)
	req.TaskId = taskId
	req.ChannelId = channelId
	resp, err := service.GetSQLAnalyzerService().StopSpaCollect(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ListSQLAnalyzerTaskJobs list sql analyzer task's jobs
// @Summary list sql analyzer task' jobs
// @Description list sql analyzer task's jobs
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListSQLAnalyzeTaskJobsReq true "list sql analyze task's jobs request parameter"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.ListSQLAnalyzeTaskJobsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/jobs [get]
func ListSQLAnalyzerTaskJobs(c *gin.Context) {
	req := &message.ListSQLAnalyzeTaskJobsReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListSQLAnalyzeTaskJobs controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().ListSQLAnalyzerTaskJobs(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTaskDeployStatus get sql analyzer task's deployment status
// @Summary get sql analyzer task's deployment status
// @Description get sql analyzer task's deployment status
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTaskDeployStatusReq true "get sql analyzer task's deployment status"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetTaskDeployStatusResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/task-deploy-status [get]
func GetTaskDeployStatus(c *gin.Context) {
	req := &message.GetTaskDeployStatusReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetTaskDeployStatus controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().GetTaskDeployStatus(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetSQLSets get sqlsets, sourceType = [ 1,2 ], which 1 means FILE, 2 or others means DB
// @Summary get sqlsets
// @Description get sqlsets
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetSQLSetsReq true "get sqlsets"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetSQLSetsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/sqlsets [get]
func GetSQLSets(c *gin.Context) {
	req := &message.GetSQLSetsReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetSQLSets controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().GetSQLSets(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetSQLSetStatements get sqlset statements, sourceType = [ 1,2 ], which 1 means FILE, 2 or others means DB
// @Summary get sqlset statements
// @Description get sqlset statements
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetSQLSetStatementsReq true "get sqlset statements"
// @Param taskId path int true "taskId"
// @Param sqlSetName query string true "sqlset name"
// @Param sqlSetOwner query string true "sqlset owner"
// @Param schemaName query string true "schema name"
// @Param pageSize query int true "pageSize"
// @Param page query int true "page"
// @Success 200 {object} message.CommonResult{data=message.GetSQLSetStatementsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/sqlset/statements [get]
func GetSQLSetStatements(c *gin.Context) {
	req := &message.GetSQLSetStatementsReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetSQLSetStatements controller receive query %s", requestString)
		req.TaskId = taskId
		resp, page, err := service.GetSQLAnalyzerService().GetSQLSetStatements(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetSQLSetStatementCount get sqlsets, sourceType = [ 1,2 ], which 1 means FILE, 2 or others means DB
// @Summary get sqlsets
// @Description get sqlsets
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetSQLSetsReq true "get sqlsets"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetSQLSetsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/analyze-task/{taskId}/sqlset/statement-count [get]
func GetSQLSetStatementCount(c *gin.Context) {
	req := &message.GetSQLSetsReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetSQLSetStatementCount controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().GetSQLSetsStatementCount(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateSqlResultUserOperation update user operation status for SQL execution results
// @Summary Update user operation status for SQL execution results
// @Description Batch update user operation status (normal/ignored/resolved) for multiple SQL execution results
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.UpdateSqlResultUserOperationReq true "update sql result user operation request"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.UpdateSqlResultUserOperationResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/results/{taskId}/user-operations [post]
func UpdateSqlResultUserOperation(c *gin.Context) {
	req := &message.UpdateSqlResultUserOperationReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateSqlResultUserOperation controller receive request %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().UpdateSqlResultUserOperation(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListSqlResultsWithPagination list SQL execution results with pagination and filtering
// @Summary List SQL execution results with pagination
// @Description Get paginated list of SQL execution results with filtering by status and user operation status
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListSqlResultsWithPaginationReq true "list sql results with pagination request"
// @Param taskId path int true "taskId"
// @Param schemas query []string false "schema names filter"
// @Param statusList query []string false "execution status filter"
// @Param userOperateStatus query []string false "user operation status filter"
// @Param page query int true "page number"
// @Param pageSize query int true "page size"
// @Success 200 {object} message.ResultWithPage{data=message.ListSqlResultsWithPaginationResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/results/{taskId}/paginated [post]
func ListSqlResultsWithPagination(c *gin.Context) {
	req := &message.ListSqlResultsWithPaginationReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListSqlResultsWithPagination controller receive query %s", requestString)
		req.TaskId = taskId
		resp, page, err := service.GetSQLAnalyzerService().ListSqlResultsWithPagination(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetSqlResultStatistics get statistics for SQL execution results
// @Summary Get SQL execution result statistics
// @Description Get statistics including counts by execution status and user operation status
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetSqlResultStatisticsReq true "get sql result statistics request"
// @Param taskId path int true "taskId"
// @Param schemas query []string false "schema names filter"
// @Success 200 {object} message.CommonResult{data=message.GetSqlResultStatisticsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/results/{taskId}/statistics [post]
func GetSqlResultStatistics(c *gin.Context) {
	req := &message.GetSqlResultStatisticsReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetSqlResultStatistics controller receive query %s", requestString)
		req.TaskId = taskId
		resp, err := service.GetSQLAnalyzerService().GetSqlResultStatistics(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetSqlResultHistory get operation history for SQL execution results
// @Summary Get SQL execution result operation history
// @Description Get audit trail of user operations on SQL execution results
// @Tags sqlanalyzer
// @Accept application/json
// @Produce application/json
// @Param objReq body message.GetSqlResultHistoryReq true "get sql result history request"
// @Param taskId path int true "taskId"
// @Param sqlExecId query int false "specific SQL execution ID"
// @Success 200 {object} message.CommonResult{data=message.GetSqlResultHistoryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /sql-analyzer/results/{taskId}/history [get]
func GetSqlResultHistory(c *gin.Context) {
	req := &message.GetSqlResultHistoryReq{}
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	sqlExecId, _ := strconv.Atoi(c.Query("sqlExecId"))

	req.TaskId = taskId
	req.SqlExecId = sqlExecId

	log.Infof("GetSqlResultHistory controller receive request taskId:%d, sqlExecId:%d", taskId, sqlExecId)
	resp, err := service.GetSQLAnalyzerService().GetSqlResultHistory(c, req)
	controller.HandleResponse(c, resp, err, nil)
}
