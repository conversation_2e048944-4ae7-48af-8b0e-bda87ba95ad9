package objectparser

import (
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CheckAdaptorInitialized check if the increment service is enabled
func CheckAdaptorInitialized() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		err := service.GetObjectParserService().CheckAdaptor()
		if err != nil {
			controller.HandleResponse(ctx, nil, err, nil)
			ctx.Abort()
			return
		}
		ctx.Next()
	}
}

// ParsePLSQLToAST parse plsql to json format
// @Summary parse plsql to json format
// @Description parse plsql to json format
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.PlSQLToJSONReq true "parse plsql to json format"
// @Success 200 {object} message.CommonResult{data=message.PlSQLToJSONResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/metadata/plsql/json [post]
func ParsePLSQLToAST(c *gin.Context) {
	req := &message.PlSQLToJSONReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ParsePLSQLToAST controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().ParsePLSQLToAST(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDependencyFromMetadata get datasource package/procedure dependency from metadata
// @Summary get datasource package/procedure dependency from metadata
// @Description get datasource package/procedure dependency from metadata，fields control output field, 1 means dependencies(for backend only),2 means tree,3 means graph
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDependencyFromMetadataReq true "get datasource package/procedure dependency parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDependencyFromMetadataResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/metadata/dependency [post]
func GetDependencyFromMetadata(c *gin.Context) {
	req := &message.GetDependencyFromMetadataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetDependencyFromMetadata controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetDependencyFromMetadata(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetObjectDetail get object detail
// @Summary get object detail
// @Description get object detail
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetObjectDetailReq true "get get object detail parameter"
// @Success 200 {object} message.CommonResult{data=message.GetObjectDetailResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/test/object-detail [post]
func GetObjectDetail(c *gin.Context) {
	req := &message.GetObjectDetailReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetObjectDetail controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetObjectDetail(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDefinitionsFromMetadata get datasource package/procedure/function definition from metadata
// @Summary get datasource package/procedure/function definition from metadata
// @Description get datasource package/procedure/function definition from metadata
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDefinitionsFromMetadataReq true "get get datasource package/procedure/function definition parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDefinitionsFromMetadataResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/metadata/definitions [post]
func GetDefinitionsFromMetadata(c *gin.Context) {
	req := &message.GetDefinitionsFromMetadataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetDefinitionsFromMetadata controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetDefinitionsFromMetadata(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetArchiveTimes get histogram of archive times
// @Summary get histogram of archive times
// @Description get histogram of archive times
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetArchiveTimesReq true "get histogram of archive times"
// @Success 200 {object} message.CommonResult{data=message.GetArchiveTimesResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/histogram/archive-times [post]
func GetArchiveTimes(c *gin.Context) {
	req := &message.GetArchiveTimesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetArchiveTimes controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetGetArchiveTimes(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListAnalyzeDetail list analyze details by page
// @Summary list analyze details by page
// @Description list analyze details by page
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListAnalyzeDetailReq true "list analyze details by page"
// @Success 200 {object} message.CommonResult{data=message.ListAnalyzeDetailResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/metadata/analyze/details [post]
func ListAnalyzeDetail(c *gin.Context) {
	req := &message.ListAnalyzeDetailReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListAnalyzeDetail controller receive query %s", requestString)
		resp, page, err := service.GetObjectParserService().ListAnalyzeDetail(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetArchiveData get histogram of archive data
// @Summary get histogram of archive data
// @Description get histogram of archive data
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetArchiveDataReq true "get histogram of archive data"
// @Success 200 {object} message.CommonResult{data=message.GetArchiveDataResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/histogram/archive-data [post]
func GetArchiveData(c *gin.Context) {
	req := &message.GetArchiveDataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetArchiveData controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetArchiveData(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetLogVolumePerSecond get histogram of log volume per second
// @Summary get histogram of log volume per second
// @Description get histogram of log volume per second
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetLogVolumePerSecondReq true "get histogram of log volume per second"
// @Success 200 {object} message.CommonResult{data=message.GetLogVolumePerSecondResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/histogram/log-volume-per-second [post]
func GetLogVolumePerSecond(c *gin.Context) {
	req := &message.GetLogVolumePerSecondReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetLogVolumePerSecond controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetLogVolumePerSecond(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTransactionLogVolume get histogram of transaction log volume
// @Summary get histogram of transaction log volume
// @Description get histogram of transaction log volume
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTransactionLogVolumeReq true "get histogram of transaction log volume"
// @Success 200 {object} message.CommonResult{data=message.GetTransactionLogVolumeResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/histogram/transaction-log-volume [post]
func GetTransactionLogVolume(c *gin.Context) {
	req := &message.GetTransactionLogVolumeReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTransactionLogVolume controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetTransactionLogVolume(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTransactionDataBlocks get histogram of transaction data blocks
// @Summary get histogram of transaction data blocks
// @Description get histogram of transaction data blocks
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTransactionDataBlocksReq true "get histogram of transaction data blocks"
// @Success 200 {object} message.CommonResult{data=message.GetTransactionDataBlocksResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/histogram/transaction-data-blocks [post]
func GetTransactionDataBlocks(c *gin.Context) {
	req := &message.GetTransactionDataBlocksReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTransactionDataBlocks controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetTransactionDataBlocks(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTransactionPerSecond get histogram of transaction data blocks
// @Summary get histogram of transaction data blocks
// @Description get histogram of transaction data blocks
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTransactionPerSecondReq true "get histogram of transaction data blocks"
// @Success 200 {object} message.CommonResult{data=message.GetTransactionPerSecondResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/histogram/transaction-per-second [post]
func GetTransactionPerSecond(c *gin.Context) {
	req := &message.GetTransactionPerSecondReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTransactionPerSecond controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetTransactionPerSecond(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListBasicIncompatibleFeature list basic incompatible feature
// @Summary list basic incompatible feature
// @Description  list basic incompatible feature
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListBasicIncompatibleFeatureReq true "list basic incompatible feature"
// @Success 200 {object} message.CommonResult{data=message.ListBasicIncompatibleFeatureResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/incompatible-feature/basic [post]
func ListBasicIncompatibleFeature(c *gin.Context) {
	req := &message.ListBasicIncompatibleFeatureReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListBasicIncompatibleFeature controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().ListBasicIncompatibleFeature(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListTaskObjectPromptRelation list task object prompt relation
// @Summary list task object prompt relation
// @Description list task object prompt relation
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListTaskObjectPromptRelationReq true "list task object prompt relation"
// @Success 200 {object} message.CommonResult{data=message.ListTaskObjectPromptRelationResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/prompt/relation/list [post]
func ListTaskObjectPromptRelation(c *gin.Context) {
	req := &message.ListTaskObjectPromptRelationReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListTaskObjectPromptRelation controller receive query %s", requestString)
		resp, page, err := service.GetObjectParserService().ListTaskObjectPromptRelation(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// ListPrompt list task prompt
// @Summary list task prompt
// @Description list task prompt
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListPromptReq true "list task prompt"
// @Success 200 {object} message.CommonResult{data=message.ListPromptResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/prompt/relation/list [post]
func ListPrompt(c *gin.Context) {
	req := &message.ListPromptReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListPrompt controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().ListPrompt(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListTaskIncompatibleFeature list task incompatible feature
// @Summary list task incompatible feature
// @Description  list task incompatible feature
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListTaskIncompatibleFeatureReq true "list task incompatible feature"
// @Success 200 {object} message.CommonResult{data=message.ListTaskIncompatibleFeatureResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/incompatible-feature/task [post]
func ListTaskIncompatibleFeature(c *gin.Context) {
	req := &message.ListTaskIncompatibleFeatureReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListTaskIncompatibleFeature controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().ListTaskIncompatibleFeature(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTaskIncompatibleFeature update task incompatible feature
// @Summary update task incompatible feature
// @Description  update task incompatible feature
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.UpdateTaskIncompatibleFeatureReq true "update task incompatible feature"
// @Success 200 {object} message.CommonResult{data=message.UpdateTaskIncompatibleFeatureResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/incompatible-feature/task/update [post]
func UpdateTaskIncompatibleFeature(c *gin.Context) {
	req := &message.UpdateTaskIncompatibleFeatureReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskIncompatibleFeature controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().UpdateTaskIncompatibleFeature(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateBasicIncompatibleFeature update basic incompatible feature
// @Summary update basic incompatible feature
// @Description  update basic incompatible feature
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.UpdateBasicIncompatibleFeatureReq true "update basic incompatible feature"
// @Success 200 {object} message.CommonResult{data=message.UpdateBasicIncompatibleFeatureResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/incompatible-feature/basic/update [post]
func UpdateBasicIncompatibleFeature(c *gin.Context) {
	req := &message.UpdateBasicIncompatibleFeatureReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskIncompatibleFeature controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().UpdateBasicIncompatibleFeature(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// SavePrompt save task prompt
// @Summary save task prompt
// @Description  save task prompt
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.SavePromptReq true "save task prompt"
// @Success 200 {object} message.CommonResult{data=message.SavePromptResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/prompt/save [post]
func SavePrompt(c *gin.Context) {
	req := &message.SavePromptReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("SavePrompt controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().SavePrompt(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeletePrompt delete task prompt
// @Summary delete task prompt
// @Description  delete task prompt
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.DeletePromptReq true "delete task prompt"
// @Success 200 {object} message.CommonResult{data=message.DeletePromptResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/prompt/delete [delete]
func DeletePrompt(c *gin.Context) {
	req := &message.DeletePromptReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DeletePrompt controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().DeletePrompt(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// SaveTaskObjectPromptRelation save task object prompt relation
// @Summary save task object prompt relation
// @Description save task object prompt relation
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.SaveTaskObjectPromptRelationReq true "save task object prompt relation"
// @Success 200 {object} message.CommonResult{data=message.SaveTaskObjectPromptRelationResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/prompt/relation/binding [post]
func SaveTaskObjectPromptRelation(c *gin.Context) {
	req := &message.SaveTaskObjectPromptRelationReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("SaveTaskObjectPromptRelation controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().SaveTaskObjectPromptRelation(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteTaskObjectPromptRelation delete task object prompt relation
// @Summary delete task object prompt relation
// @Description delete task object prompt relation
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.DeleteTaskObjectPromptRelationReq true "delete task object prompt relation"
// @Success 200 {object} message.CommonResult{data=message.DeleteTaskObjectPromptRelationResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/prompt/relation/binding [delete]
func DeleteTaskObjectPromptRelation(c *gin.Context) {
	req := &message.DeleteTaskObjectPromptRelationReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DeleteTaskObjectPromptRelation controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().DeleteTaskObjectPromptRelation(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetIncompatibleFeatureScoring get incompatible scoring
// @Summary get incompatible scoring
// @Description  get incompatible scoring
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetIncompatibleFeatureScoringReq true "get incompatible scoring"
// @Success 200 {object} message.CommonResult{data=message.GetIncompatibleFeatureScoringResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/incompatible-feature/scoring [post]
func GetIncompatibleFeatureScoring(c *gin.Context) {
	req := &message.GetIncompatibleFeatureScoringReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetIncompatibleFeatureScoring controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().GetIncompatibleFeatureScoring(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// TestAIConnect test ai connect
// @Summary test ai connect
// @Description test ai connect
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.TestAIConnectReq true "test ai connect"
// @Success 200 {object} message.CommonResult{data=message.TestAIConnectResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/test-connect [post]
func TestAIConnect(c *gin.Context) {
	req := &message.TestAIConnectReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("TestAIConnect controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().TestAIConnect(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ConvertPLSQLToJava convert plsql to java
// @Summary convert plsql to java
// @Description convert plsql to java
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ConvertPLSQLToJavaReq true "convert plsql to java"
// @Success 200 {object} message.CommonResult{data=message.ConvertPLSQLToJavaResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql-to-java [post]
func ConvertPLSQLToJava(c *gin.Context) {
	req := &message.ConvertPLSQLToJavaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Convert controller receive query %s", requestString)
		go service.GetObjectParserService().ConvertPLSQLToJava(c, req)
		controller.HandleResponse(c, nil, nil, nil)
	}
}

// ConvertPLSQLToJavaInManual convert plsql to java in manual
// @Summary convert plsql to java in manual
// @Description convert plsql to java in manual
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ConvertPLSQLToJavaInManualReq true "convert plsql to java in manual"
// @Success 200 {object} message.CommonResult{data=message.ConvertPLSQLToJavaInManualResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql-to-java [post]
func ConvertPLSQLToJavaInManual(c *gin.Context) {
	req := &message.ConvertPLSQLToJavaInManualReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ConvertPLSQLToJavaInManual controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().ConvertPLSQLToJavaInManual(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetPLSQLToJavaResults get convert plsql to java results
// @Summary get convert plsql to java results
// @Description get convert plsql to java results
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetPLSQLToJavaResultsReq true "get convert plsql to java results"
// @Success 200 {object} message.CommonResult{data=message.GetPLSQLToJavaResultsResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql/current-results [post]
func GetPLSQLToJavaResults(c *gin.Context) {
	req := &message.GetPLSQLToJavaResultsReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetObjectParserService().GetPLSQLToJavaResults(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetPLSQLToJavaHistoryResults get convert plsql to java results
// @Summary get convert plsql to java results
// @Description get convert plsql to java results
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetPLSQLToJavaHistoryResultsReq true "get convert plsql to java results"
// @Success 200 {object} message.CommonResult{data=message.GetPLSQLToJavaHistoryResultsResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql/history-results [post]
func GetPLSQLToJavaHistoryResults(c *gin.Context) {
	req := &message.GetPLSQLToJavaHistoryResultsReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetObjectParserService().GetPLSQLToJavaHistoryResults(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetPLSQLToJavaLogs get convert plsql to java logs
// @Summary get convert plsql to java logs
// @Description get convert plsql to java logs
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetPLSQLToJavaLogsReq true "get convert plsql to java logs"
// @Success 200 {object} message.CommonResult{data=message.GetPLSQLToJavaLogsResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql/logs [post]
func GetPLSQLToJavaLogs(c *gin.Context) {
	req := &message.GetPLSQLToJavaLogsReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetObjectParserService().GetPLSQLToJavaLogs(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetPLSQLToJavaSummary get convert plsql to java summary
// @Summary get convert plsql to java summary
// @Description get convert plsql to java summary
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetPLSQLToJavaSummaryReq true "get convert plsql to java summary"
// @Success 200 {object} message.CommonResult{data=message.GetPLSQLToJavaSummaryResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql/summary [post]
func GetPLSQLToJavaSummary(c *gin.Context) {
	req := &message.GetPLSQLToJavaSummaryReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetObjectParserService().GetPLSQLToJavaSummary(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DownloadHistoryJavaCodes download history java code
// @Summary download history java code
// @Description download history java code
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.DownloadHistoryJavaCodesReq true "download history java code"
// @Success 200 {object} message.CommonResult{data=message.DownloadJavaCodesResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql/history-download [post]
func DownloadHistoryJavaCodes(c *gin.Context) {
	req := &message.DownloadHistoryJavaCodesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadHistoryJavaCodes controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().DownloadHistoryJavaCodes(c, req)
		controller.HandleFileResponse(c, resp, err)
	}
}

// DownloadJavaCodes download java code
// @Summary download java code
// @Description download java code
// @Tags objectParser
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.DownloadJavaCodesReq true "download java code"
// @Success 200 {object} message.CommonResult{data=message.DownloadJavaCodesResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /object-parser/ai/convert/plsql/current-download [post]
func DownloadJavaCodes(c *gin.Context) {
	req := &message.DownloadJavaCodesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadJavaCodes controller receive query %s", requestString)
		resp, err := service.GetObjectParserService().DownloadJavaCodes(c, req)
		controller.HandleFileResponse(c, resp, err)
	}
}
