package controller

import (
	"net/http"
	"path"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

func HandleResponse(c *gin.Context, resp interface{}, err error, page *message.Page) {
	if err == nil {
		if page != nil {
			c.JSON(http.StatusOK, SuccessWithPage(resp, *page))
		} else {
			c.JSON(http.StatusOK, Success(resp))
		}
		return
	}
	if msError, ok := err.(errors.MSError); ok {
		code := msError.GetCode()
		if msError.GetCode() != errors.TIMS_SUCCESS {
			log.Error(msError.Error())
			c.<PERSON>(code.GetHttpCode(), Fail(int(code), msError.Error()))
		} else {
			if page != nil {
				c.<PERSON>(http.StatusOK, SuccessWithPage(resp, *page))
			} else {
				c.<PERSON>(http.StatusOK, Success(resp))
			}
		}
	} else {
		log.Error(err.Error())
		c.<PERSON><PERSON><PERSON>(http.StatusInternalServerError, Fail(int(errors.TIMS_UNRECOGNIZED_ERROR), err.Error()))
	}
	return
}

func SaveUploadFile(c *gin.Context, fileFieldName string, fileDir string) (string, error) {
	f, err := c.FormFile(fileFieldName)
	if err != nil {
		return "", err
	}
	fp := path.Join(fileDir, f.Filename)
	saveErr := c.SaveUploadedFile(f, fp)
	return f.Filename, saveErr
}

func HandleFileResponse(c *gin.Context, fileResponse message.FileResponseInterface, err error) {
	if err == nil {
		fullPathName := fileResponse.GetFilePath()
		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+path.Base(fullPathName))
		c.Header("Content-Transfer-Encoding", "binary")
		c.Header("Cache-Control", "no-cache")
		c.File(fullPathName)
		return
	}
	if msError, ok := err.(errors.MSError); ok {
		code := msError.GetCode()
		if msError.GetCode() != errors.TIMS_SUCCESS {
			log.Error(msError.Error())
			c.JSON(code.GetHttpCode(), Fail(int(code), msError.Error()))
		}
	} else {
		log.Error(err.Error())
		c.JSON(http.StatusInternalServerError, Fail(int(errors.TIMS_UNRECOGNIZED_ERROR), err.Error()))
	}
	return
}

func Success(data interface{}) *message.CommonResult {
	return &message.CommonResult{ResultMark: message.ResultMark{Code: 0, Message: "OK"}, Data: data}
}

func SuccessWithPage(data interface{}, page message.Page) *message.ResultWithPage {
	return &message.ResultWithPage{ResultMark: message.ResultMark{Code: 0, Message: "OK"}, Data: data, Page: page}
}

func Fail(code int, messageContent string) *message.CommonResult {
	return &message.CommonResult{ResultMark: message.ResultMark{Code: code, Message: messageContent}, Data: struct{}{}}
}
