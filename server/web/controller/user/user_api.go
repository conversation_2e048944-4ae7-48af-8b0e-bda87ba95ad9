package user

import (
	"net/http"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service/users"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

var userService = users.NewUserService()

// Register 用户注册
// @Summary 注册
// @Description 注册
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param registerInfo body message.RegisterReq true "注册信息"
// @Success 200 {object} map[string]interface{}
// @Router /user/register [post]
func Register(c *gin.Context) {
	var req message.RegisterReq
	if requestString, ok := controller.HandleJsonRequestFromBody(c, &req); ok {
		log.Infof("Register controller receive request %s", requestString)
		resp := userService.Register(c.Request.Context(), req.Username, req.Password, req.Email)
		controller.HandleResponse(c, resp, nil, nil)
	}
}

// Login 用户登录
// @Summary login
// @Description login
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param loginInfo body message.LoginReq true "login info"
// @Success 200 {object} message.LoginResp
// @Router /user/login [post]
func Login(c *gin.Context) {
	c.JSON(http.StatusOK, &message.LoginResp{
		Status:           "ok",
		Type:             "account",
		CurrentAuthority: "admin",
	})
}

// Login 用户登录
// @Summary login
// @Description login
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param loginInfo body message.LoginReq true "login info"
// @Success 200 {object} message.LoginResp
// @Router /user/security_login [post]
func SecurityLogin(c *gin.Context) {
	var req message.LoginReq
	if requestString, ok := controller.HandleJsonRequestFromBody(c, &req); ok {
		log.Infof("SecurityLogin controller receive request %s", requestString)
		resp, err := userService.Login(c.Request.Context(), req.Username, req.Password)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetUserInfo get current user information
// @Summary get current user information
// @Description get current user information
// @Tags user
// @Accept application/json
// @Produce application/json
// @Router /user/currentUser [get]
func GetUserInfo(c *gin.Context) {
	message := "{\"success\":true,\"data\":{\"name\":\"tms\",\"avatar\":\"/icons/avatar_01.png\",\"userid\":\"00000001\",\"email\":\"<EMAIL>\",\"signature\":\"海纳百川，有容乃大\",\"title\":\"交互专家\",\"group\":\"pingcap\",\"tags\":[{\"key\":\"0\",\"label\":\"很有想法的\"},{\"key\":\"1\",\"label\":\"专注设计\"},{\"key\":\"2\",\"label\":\"辣~\"},{\"key\":\"3\",\"label\":\"大长腿\"},{\"key\":\"4\",\"label\":\"川妹子\"},{\"key\":\"5\",\"label\":\"海纳百川\"}],\"notifyCount\":12,\"unreadCount\":11,\"country\":\"China\",\"access\":\"admin\",\"geographic\":{\"province\":{\"label\":\"北京市\",\"key\":\"330000\"},\"city\":{\"label\":\"北京市\",\"key\":\"330100\"}},\"address\":\"129号\",\"phone\":\"010-268888888\"}}"
	c.String(http.StatusOK, message)
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户密码
// @Tags user
// @Accept application/json
// @Produce application/json
// @Param changePasswordReq body message.ChangePasswordReq true "修改密码信息"
// @Success 200 {object} message.ChangePasswordResp
// @Router /user/change-password [post]
func ChangePassword(c *gin.Context) {
	var req message.ChangePasswordReq
	if requestString, ok := controller.HandleJsonRequestFromBody(c, &req); ok {
		log.Infof("ChangePassword controller receive request %s", requestString)
		err := userService.ChangePassword(c.Request.Context(), req.Username, req.OldPassword, req.NewPassword)
		resp := &message.CommonResp{}
		controller.HandleResponse(c, resp, err, nil)
	}
}
