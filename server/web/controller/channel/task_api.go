package channel

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CreateDefaultTasks create default tasks interface
// @Summary create default tasks
// @Description create default tasks
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.CreateDefaultTasksResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/create-default [post]
func CreateDefaultTasks(c *gin.Context) {
	log.Infof("CreateDefaultTasks controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.CreateDefaultTasksReq{}
	req.ChannelId = channelId
	resp, err := service.GetChannelService().CreateDefaultTasks(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ListTasksByChannelId list tasks by channelId interface
// @Summary list tasks by channelId
// @Description list tasks by channelId
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param objectQuery query string false "object query"
// @Param parentTaskId query int false "use for cluster mode to list subtasks"
// @Success 200 {object} message.CommonResult{data=message.ListTasksByChannelIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/list [get]
func ListTasksByChannelId(c *gin.Context) {
	log.Infof("ListTasksByChannelId controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.ListTasksByChannelIdReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListTasksByChannelId controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().ListTasksByChannelId(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTaskAndChannelSchemaObjectById update task info and channel schema object interface
// @Summary update task info and channel schema object
// @Description update task info and channel schema object
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param taskReq body message.UpdateTaskAndChannelSchemaObjectByIdReq true "update task info and channel schema object  request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateTaskAndChannelSchemaObjectByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId} [post]
func UpdateTaskAndChannelSchemaObjectById(c *gin.Context) {
	log.Infof("UpdateTaskAndChannelSchemaObjectById controller receive channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	req := &message.UpdateTaskAndChannelSchemaObjectByIdReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskAndChannelSchemaObjectById controller receive request: %s", requestString)
		resp, err := service.GetChannelService().UpdateTaskAndChannelSchemaObjectById(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTaskCfgAndChannelSchemaObjectById update task info and channel schema object interface
// @Summary update task info and channel schema object
// @Description update task info and channel schema object
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param taskReq body message.UpdateTaskAndChannelSchemaObjectByIdReq true "update task info and channel schema object  request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateTaskAndChannelSchemaObjectByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/config [post]
func UpdateTaskCfgAndChannelSchemaObjectById(c *gin.Context) {
	log.Infof("UpdateTaskAndChannelSchemaObjectById controller receive channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	req := &message.UpdateTaskAndChannelSchemaObjectByIdReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskAndChannelSchemaObjectById controller receive request: %s", requestString)
		resp, err := service.GetChannelService().UpdateTaskCfgAndChannelSchemaObjectById(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTaskInfoAndChannelSchemaObjectById get task info and channel schema object interface
// @Summary get task info and channel schema object
// @Description get task info and channel schema object
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetTaskInfoAndChannelSchemaObjectByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/ [get]
func GetTaskInfoAndChannelSchemaObjectById(c *gin.Context) {
	log.Infof("GetTaskInfoAndChannelSchemaObjectById controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetTaskInfoAndChannelSchemaObjectByIdReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().GetTaskInfoAndChannelSchemaObjectById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// SubmitTaskSchemaTables submit task schemas and tables interface
// @Summary submit task schemas and tables
// @Description submit task schemas and tables
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param tablesReq body message.SubmitTaskSchemaTablesReq true "submit task schemas and tables request parameter"
// @Success 200 {object} message.CommonResult{data=message.SubmitTaskSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/schema-tables [post]
func SubmitTaskSchemaTables(c *gin.Context) {
	log.Infof("SubmitTaskSchemaTables controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.SubmitTaskSchemaTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("SubmitTaskSchemaTables controller receive request: %s", requestString)
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().SubmitTaskChannelTables(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTaskSchemaTables submit task schemas and tables interface
// @Summary submit task schemas and tables
// @Description submit task schemas and tables
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param tablesReq body message.SubmitTaskSchemaTablesReq true "submit task schemas and tables request parameter"
// @Success 200 {object} message.CommonResult{data=message.SubmitTaskSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/update-schema-tables [post]
func UpdateTaskSchemaTables(c *gin.Context) {
	log.Infof("UpdateTaskSchemaTables controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.SubmitTaskSchemaTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskSchemaTables controller receive request: %s", requestString)
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().UpdateTaskChannelTables(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetUnSelectedTaskSchemaTables get un-selected channel tables interface
// @Summary get un-selected channel tables
// @Description get un-selected channel tables
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskType path int true "taskType"
// @Param queryBody body message.GetUnSelectedTaskSchemaTablesReq true "get un-selected channel tables request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetUnSelectedTaskSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task-type/{taskType}/un-selected-tables [post]
func GetUnSelectedTaskSchemaTables(c *gin.Context) {
	log.Infof("GetUnSelectedTaskSchemaTables controller receive request channelId: %s, taskType:%s", c.Param("channelId"), c.Param("taskType"))
	req := &message.GetUnSelectedTaskSchemaTablesReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetChannelService().GetUnSelectedTaskSchemaTables(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetSelectedTaskSchemaTables get selected channel tables interface
// @Summary get selected channel tables
// @Description get selected channel tables
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param query body message.GetSelectedTaskSchemaTablesReq true "get selected channel tables request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.GetSelectedTaskSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/selected-tables [post]
func GetSelectedTaskSchemaTables(c *gin.Context) {
	log.Infof("GetSelectedTaskSchemaTables controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	req := &message.GetSelectedTaskSchemaTablesReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetChannelService().GetSelectedTaskSchemaTables(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// ReferenceTables reference tables interface
// @Summary reference tables
// @Description reference tables
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param query body message.ReferenceTablesReq true "reference tables request parameter"
// @Success 200 {object} message.CommonResult{data=message.ReferenceTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/reference-tables [post]
func ReferenceTables(c *gin.Context) {
	log.Infof("ReferenceTables controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	req := &message.ReferenceTablesReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetChannelService().ReferenceTables(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTaskparamTemplateListByTaskType get task param template list by task type interface
// @Summary get task param template list by task type
// @Description get task param template list by task type
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param taskType path int true "taskType"
// @Success 200 {object} message.CommonResult{data=message.GetTaskParamTemplateListByTaskTypeResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/task/{taskType} [get]
func GetTaskparamTemplateListByTaskType(c *gin.Context) {
	log.Infof("GetTaskparamTemplateList controller receive request taskType:%s", c.Param("taskType"))
	taskType, _ := strconv.Atoi(c.Param("taskType"))
	req := &message.GetTaskParamTemplateListByTaskTypeReq{TaskType: taskType}
	resp, err := service.GetChannelService().GetTaskparamTemplateListByTaskType(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// TaskExecution task execution interface
// @Summary task execution
// @Description task execution
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.TaskExecutionResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/execution [get]
func TaskExecution(c *gin.Context) {
	log.Infof("TaskExecution controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.TaskExecutionReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	resp, err := service.GetChannelService().TaskExecution(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// TaskExecutionPreCheck task execution precheck interface
// @Summary task execution precheck
// @Description task execution precheck
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.TaskExecutionPreCheckResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/execution-precheck [post]
func TaskExecutionPreCheck(c *gin.Context) {
	log.Infof("TaskExecutionPreCheck controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.TaskExecutionPreCheckReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	resp, err := service.GetChannelService().TaskExecutionPreCheckReq(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetTaskDetailResult task execution result interface
// @Summary task execution result
// @Description task execution result
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param query body message.GetTaskDetailResultReq true "get task detail request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTaskDetailResultResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/detail-result [get]
func GetTaskDetailResult(c *gin.Context) {
	log.Infof("GetTaskDetailResult controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetTaskDetailResultReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	resp, err := service.GetChannelService().GetTaskDetailResult(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetTaskProgress task execution progress interface
// @Summary task execution progress
// @Description task execution progress
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param query body message.GetTaskProgressReq true "get task progress request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTaskProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/progress [post]
func GetTaskProgress(c *gin.Context) {
	log.Infof("GetTaskProgress controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	req := &message.GetTaskProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))
		req.TaskId, _ = strconv.Atoi(c.Param("taskId"))
		log.Infof("GetTaskProgress requestString %s", requestString)
		resp, err := service.GetChannelService().GetTaskProgress(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetObjectAssessResultSummary Get O2T object assess-task result summary interface
// @Summary Get O2T object assess-task result summary
// @Description Get O2T object assess-task result summary
// @Tags assessment
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetObjectAssessResultSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /assessment/channel/{channelId}/task/{taskId}/GetObjectAssessResultSummary [get]
func GetObjectAssessResultSummary(c *gin.Context) {
	log.Infof("GetO2TObjectAssessSummary controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetObjectAssessResultSummaryReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	resp, err := service.GetChannelService().GetObjectAssessResultSummary(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetObjectAssessResultSummaryBySchema Get O2T object assess-task result summary BySchema interface
// @Summary Get O2T object assess-task result summary BySchema
// @Description Get O2T object assess-task result summary BySchema
// @Tags assessment
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetObjectAssessResultSummaryBySchemaResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /assessment/channel/{channelId}/task/{taskId}/GetObjectAssessResultSummaryBySchema [get]
func GetObjectAssessResultSummaryBySchema(c *gin.Context) {
	log.Infof("GetO2TObjectAssessSummaryBySchema controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetObjectAssessResultSummaryBySchemaReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	resp, err := service.GetChannelService().GetObjectAssessResultSummaryBySchema(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetObjectAssessResultDetail Get O2T object assess-task result detail interface
// @Summary Get O2T object assess-task result detail
// @Description Get O2T object assess-task result detail
// @Tags assessment
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param schema path int true "schema"
// @Success 200 {object} message.CommonResult{data=message.GetObjectAssessResultDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /assessment/channel/{channelId}/task/{taskId}/GetObjectAssessResultDetail/{schema} [get]
func GetObjectAssessResultDetail(c *gin.Context) {
	log.Infof("GetObjectAssessResultDetail controller receive request channelId: %s, taskId:%s, schema:%s", c.Param("channelId"), c.Param("taskId"), c.Param("schema"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	schema := c.Param("schema")
	req := &message.GetObjectAssessResultDetailReq{
		ChannelId: channelId,
		TaskId:    taskId,
		Schema:    schema,
	}
	resp, err := service.GetChannelService().GetObjectAssessResultDetail(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// DownloadObjectAssessResultReport Download O2T object assess-task result report interface
// @Summary Download O2T object assess-task result report
// @Description Download O2T object assess-task result report
// @Tags assessment
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.DownloadObjectAssessResultReportResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /assessment/channel/{channelId}/task/{taskId}/DownloadObjectAssessResultReport [get]
func DownloadObjectAssessResultReport(c *gin.Context) {
	log.Infof("DownloadObjectAssessResultReport controller receive request channelId: %s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	reportPath := config.GetGlobalConfig().DataDir
	reportName := reportPath + "/assessment/assessment_channel" + c.Param("channelId") + "_task" + c.Param("taskId") + ".zip"

	_, err := os.Stat(reportName)
	if err == nil {
		log.Infof("start to download report file:%s", reportName)

		c.Header("Content-Type", "application/octet-stream")
		c.Header("Content-Disposition", "attachment; filename="+reportName)
		c.Header("Content-Transfer-Encoding", "binary")
		// c.Header("Transfer-Encoding", "true")
		c.Header("Cache-Control", "no-cache")
		c.File(reportName)
	} else if errors.Is(err, os.ErrNotExist) {
		log.Infof("report file %s not exists, err:%s", reportName, err)

		controller.HandleResponse(c, &message.DownloadObjectAssessResultReportResp{}, err, nil)
	}
}

// GetMergedTaskParamListByTaskparamTemplateId get task param list by TaskparamTemplateId interface
// @Summary get task param list by TaskparamTemplateId
// @Description get task param list by TaskparamTemplateId
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param taskparamTemplateId path int true "taskparamTemplateId"
// @Success 200 {object} message.CommonResult{data=message.GetMergedTaskParamListByTaskparamTemplateIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/task-params/{taskparamTemplateId} [get]
func GetMergedTaskParamListByTaskparamTemplateId(c *gin.Context) {
	log.Infof("GetMergedTaskParamListByTaskparamTemplateId controller receive request channelId:%s, taskId:%s, taskparamTemplateId:%s",
		c.Param("channelId"), c.Param("taskId"), c.Param("taskparamTemplateId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	taskparamTemplateId, _ := strconv.Atoi(c.Param("taskparamTemplateId"))
	req := &message.GetMergedTaskParamListByTaskparamTemplateIdReq{
		ChannelId:           channelId,
		TaskId:              taskId,
		TaskparamTemplateId: taskparamTemplateId,
	}
	resp, err := service.GetChannelService().GetMergedTaskParamListByTaskparamTemplateId(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// SaveTaskParams save task params interface
// @Summary save task params
// @Description save task params
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param taskparamTemplateId path int true "taskparamTemplateId"
// @Param tablesReq body message.SaveTaskParamsReq true "save task params request parameter"
// @Success 200 {object} message.CommonResult{data=message.SaveTaskParamsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/task-params/{taskparamTemplateId} [post]
func SaveTaskParams(c *gin.Context) {
	log.Infof("SaveTaskParams controller receive request channelId:%s, taskId:%s, taskparamTemplateId:%s",
		c.Param("channelId"), c.Param("taskId"), c.Param("taskparamTemplateId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	taskparamTemplateId, _ := strconv.Atoi(c.Param("taskparamTemplateId"))
	req := &message.SaveTaskParamsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("SaveTaskParams controller receive request: %s", requestString)
		req.ChannelId = channelId
		req.TaskId = taskId
		req.TaskparamTemplateId = taskparamTemplateId
		resp, err := service.GetChannelService().SaveTaskParams(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// CreateTaskTableConfigs create task table configs interface
// @Summary create task table configs
// @Description create task table configs
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param createTaskTableConfigsReq body message.CreateTaskTableConfigsReq true "create task table configs request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateTaskTableConfigsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/task-table [post]
func CreateTaskTableConfigs(c *gin.Context) {
	log.Infof("CreateTaskTableConfigs controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.CreateTaskTableConfigsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTaskTableConfigs controller receive request: %s", requestString)
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().CreateTaskTableConfigs(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTaskTableConfigs update task table configs interface
// @Summary update task table configs
// @Description update task table configs
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param updateTaskTableConfigsReq body message.UpdateTaskTableConfigsReq true "update task table configs request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateTaskTableConfigsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/task-table/update [post]
func UpdateTaskTableConfigs(c *gin.Context) {
	log.Infof("UpdateTaskTableConfigs controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.UpdateTaskTableConfigsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskTableConfigs controller receive request: %s", requestString)
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().UpdateTaskTableConfigs(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteTaskTableConfigs delete task table configs interface
// @Summary delete task table configs
// @Description delete task table configs
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param ids body message.DeleteTaskTableConfigsReq true "delete task table configs request parameter"
// @Success 200 {object} message.CommonResult{data=message.DeleteTaskTableConfigsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/task-table [delete]
func DeleteTaskTableConfigs(c *gin.Context) {
	log.Infof("DeleteTaskTableConfigs controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.DeleteTaskTableConfigsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DeleteTaskTableConfigs controller receive request: %s", requestString)
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().DeleteTaskTableConfigs(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTaskTableConfigsByTaskIdAndChannelId get task table config list by taskId and channelId
// @Summary get task table config list
// @Description get task table config list
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetTaskTableConfigsByTaskIdAndChannelIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/task-table/list [get]
func GetTaskTableConfigsByTaskIdAndChannelId(c *gin.Context) {
	log.Infof("DeleteTaskTableConfigs controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetTaskTableConfigsByTaskIdAndChannelIdReq{ChannelId: channelId, TaskId: taskId}
	resp, err := service.GetChannelService().GetTaskTableConfigsByTaskIdAndChannelId(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetColumnNamesTiDB get column name list interface
// @Summary get column name list
// @Description get column name list
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param getColumnNamesReq body message.GetColumnNamesReq true "get column list request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetColumnNamesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/column-names [post]
func GetColumnNamesTiDB(c *gin.Context) {
	log.Infof("GetColumnNames controller receive request channelId:%s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetColumnNamesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetColumnNames controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().GetColumnNamesTidb(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetColumnNamesOracle get column name list interface
// @Summary get column name list
// @Description get column name list
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param getColumnNamesReq body message.GetColumnNamesReq true "get column list request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetColumnNamesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/column-names-ora [post]
func GetColumnNamesOracle(c *gin.Context) {
	log.Infof("GetColumnNames controller receive request channelId:%s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetColumnNamesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetColumnNames controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().GetColumnNamesOracle(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// CreateTask create a new task interface
// @Summary create a new task
// @Description create a new task
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param task body message.CreateTaskReq true "create a new task request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/create-new [post]
func CreateTask(c *gin.Context) {
	log.Infof("CreateTask controller receive channelId:%s", c.Param("channelId"))
	req := &message.CreateTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTask controller receive request: %s", requestString)
		resp, err := service.GetChannelService().CreateTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// BatchDeleteTasks batch delete tasks by ids interface
// @Summary batch delete tasks by ids
// @Description batch delete tasks by ids
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param taskIds body message.BatchDeleteTasksReq true "batch delete datasource request parameter"
// @Success 200 {object} message.CommonResult{data=message.BatchDeleteTasksResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task [delete]
func BatchDeleteTasks(c *gin.Context) {
	log.Infof("BatchDeleteTasks controller receive channelId:%s", c.Param("channelId"))
	req := &message.BatchDeleteTasksReq{}
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchDeleteTasks controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().BatchDeleteTasks(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// AddAllChannelSchemaTables add all channel schema tables from unselected tables interface
// @Summary add all channel schema tables from unselected tables interface
// @Description add all channel schema tables from unselected tables interface
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.OperAllChannelSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/add-all [get]
func AddAllChannelSchemaTables(c *gin.Context) {
	log.Infof("AddAllChannelSchemaTables controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().AddAllChannelSchemaTables(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// DeleteAllChannelSchemaTables delete all channel schema tables from unselected tables
// @Summary delete all channel schema tables from unselected tables
// @Description delete all channel schema tables from unselected tables
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.OperAllChannelSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/del-all [get]
func DeleteAllChannelSchemaTables(c *gin.Context) {
	log.Infof("DeleteAllChannelSchemaTables controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().DeleteAllChannelSchemaTables(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ToUpperCaseTableNameT update channel schema tables to upper case
// @Summary update channel schema tables to upper case
// @Description update channel schema tables to upper case
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.OperAllChannelSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/to-upper [get]
func ToUpperCaseTableNameT(c *gin.Context) {
	log.Infof("ToUpperCaseTableNameT controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().ToUpperCaseTableNameT(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ToLowerCaseTableNameT update channel schema tables to lower case
// @Summary update channel schema tables to lower case
// @Description update channel schema tables to lower case
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.OperAllChannelSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/to-lower [get]
func ToLowerCaseTableNameT(c *gin.Context) {
	log.Infof("ToLowerCaseTableNameT controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().ToLowerCaseTableNameT(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ToUpperCaseSchemaNameT update channel schema tables to upper case
// @Summary update channel schema tables to upper case
// @Description update channel schema tables to upper case
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.OperAllChannelSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/to-upper-schema [get]
func ToUpperCaseSchemaNameT(c *gin.Context) {
	log.Infof("ToUpperCaseSchemaNameT controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().ToUpperCaseSchemaNameT(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ToLowerCaseSchemaNameT update channel schema tables to lower case
// @Summary update channel schema tables to lower case
// @Description update channel schema tables to lower case
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.OperAllChannelSchemaTablesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/to-lower-schema [get]
func ToLowerCaseSchemaNameT(c *gin.Context) {
	log.Infof("ToLowerCaseSchemaNameT controller receive request,channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().ToLowerCaseSchemaNameT(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ModifyChannelSchemaTable update channel schema tables to lower case
// @Summary update channel schema tables to lower case
// @Description update channel schema tables to lower case
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/modify-channel-schema-tables [get]
func ModifyChannelSchemaTable(c *gin.Context) {
	log.Infof("ModifyChannelSchemaTable controller receive request,channelId:%s, taskId:%s, modifyTag:%s, tableNameS:%v", c.Param("channelId"), c.Param("taskId"), c.Param("modifyTag"), c.Param("tableNameS"))
	req := &message.OperChannelSchemaTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ModifyChannelSchemaTable controller receive request: %s", requestString)
		resp, err := service.GetChannelService().ModifyChannelSchemaTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ChangePartitionTypeT change selected table partition
// @Summary change selected table partition
// @Description change selected table partition
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param task body message.OperAllChannelSchemaTablesReq true "create a new task request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/partition [post]
func ChangePartitionTypeT(c *gin.Context) {
	log.Infof("ChangePartitionTypeT controller receive channelId:%s", c.Param("channelId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ChangePartitionTypeT controller receive request: %s", requestString)
		resp, err := service.GetChannelService().ChangePartitioningTypeT(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ChangeClusterTypeT change selected table partition
// @Summary change selected table partition
// @Description change selected table partition
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param task body message.OperAllChannelSchemaTablesReq true "create a new task request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/cluster [post]
func ChangeClusterTypeT(c *gin.Context) {
	log.Infof("ChangeClusterTypeT controller receive channelId:%s", c.Param("channelId"))
	req := &message.OperAllChannelSchemaTablesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ChangeClusterTypeT controller receive request: %s", requestString)
		resp, err := service.GetChannelService().ChangeClusterTypeT(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Upload upload csv file
// @Summary upload csv file
// @Description upload csv file
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/upload [post]
func Upload(c *gin.Context) {
	log.Infof("Upload controller receive request channelId:%s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))

	//检查文件目录是否存在，不存在则创建文件目录
	filepath := fmt.Sprintf("./data/channel/%d/", channelId)
	_, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			err := os.MkdirAll(filepath, 0766)
			if err != nil {
				log.Errorf("os.MkdirAll err")
				return
			}
		}
	}

	//保存文件
	f, _ := c.FormFile("file")
	err = c.SaveUploadedFile(f, filepath+f.Filename)
	if err != nil {
		log.Errorf("SaveUploadedFile err")
		return
	}
	log.Infof("Upload receive request: %s", f.Filename)

	//查询channel信息
	channelResp, err := service.GetChannelService().GetChannel(c, &message.GetChannelReq{
		ChannelId: channelId,
	})
	if err != nil {
		log.Errorf("get channel info err")
	}

	//上传csv信息并校验内容
	res, err := service.GetChannelService().UploadCSV(c, channelId, channelResp.DatasourceIdS, filepath+f.Filename)
	if err != nil {
		log.Errorf("Parsing csv error")
	}

	controller.HandleResponse(c, res, err, nil)
}

// CreateTaskByCSV create a new task interface
// @Summary create a new task
// @Description create a new task
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param task body message.CreateTaskReq true "create a new task request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/create-bycsv [post]
func CreateTaskByCSV(c *gin.Context) {
	log.Infof("CreateTaskByCSV controller receive channelId:%s", c.Param("channelId"))
	req := &message.CreateTaskByCsvReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTaskByCSV controller receive request: %s", requestString)
		filepath := fmt.Sprintf("./data/channel/%d/", req.ChannelId)
		csvData, err := service.GetChannelService().ParsingCSV(c, req.ChannelId, filepath+req.FileName)
		if err != nil {
			log.Errorf("CreateTaskByCSV parsing csv error, err:%v", err)
			controller.HandleResponse(c, &message.CreateTaskResp{}, err, nil)
			return
		}

		isNeedDivide := service.GetChannelService().NeedDivideCSVData(req, csvData)
		if isNeedDivide {
			divideCSVDatas := service.GetChannelService().DivideCSVData(csvData)
			for Index, divideCSVDatasItem := range divideCSVDatas {
				log.Debugf("Index:%d, divideCSVDatasItem:%v", Index, divideCSVDatasItem)
			}
			var resp *message.CreateTaskResp = nil
			for DatasIndex, divideCSVDatasItem := range divideCSVDatas {
				csvreq := &message.CreateTaskByCsvReq{
					ChannelId: req.ChannelId,
					TaskName:  req.TaskName + strconv.Itoa(DatasIndex),
					TaskType:  req.TaskType,
				}
				resp, err = service.GetChannelService().CreateTaskByCSV(c, csvreq, divideCSVDatasItem)
				if err != nil {
					break
				}
			}
			controller.HandleResponse(c, resp, err, nil)
		} else {
			resp, err := service.GetChannelService().CreateTaskByCSV(c, req, csvData)
			controller.HandleResponse(c, resp, err, nil)
		}

	}
}

// CreateTaskByRefTask create a new task interface
// @Summary create a new task
// @Description create a new task
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param task body message.CreateTaskReq true "create a new task request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/create-byref [post]
func CreateTaskByRefTask(c *gin.Context) {
	log.Infof("CreateTaskByRefTask controller receive channelId:%s", c.Param("channelId"))
	req := &message.CreateTaskByRefTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTaskByRefTask controller receive request: %s", requestString)

		resp, err := service.GetChannelService().CreateTaskByRefTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// Upload upload csv file for task_table_config
// @Summary upload csv file for task_table_config
// @Description upload csv file for task_table_config
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param channelId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/{taskId}/upload [post]
func UploadForTaskTabCfg(c *gin.Context) {
	log.Infof("UploadForTaskTabCfg controller receive request channelId:%s", c.Param("channelId"))
	log.Infof("UploadForTaskTabCfg controller receive request taskId:%s", c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))

	//检查文件目录是否存在，不存在则创建文件目录
	filepath := fmt.Sprintf("./data/channel/%d/%d/", channelId, taskId)
	_, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			err := os.MkdirAll(filepath, 0766)
			if err != nil {
				log.Errorf("os.MkdirAll err")
				return
			}
		}
	}

	//保存文件
	f, _ := c.FormFile("file")
	err = c.SaveUploadedFile(f, filepath+f.Filename)
	if err != nil {
		log.Errorf("SaveUploadedFile err")
	}
	log.Infof("Upload receive request: %s", f.Filename)

	res := &message.CommonResp{}

	controller.HandleResponse(c, res, err, nil)
}

// CreateTaskByCSV create a new task interface
// @Summary create a new task
// @Description create a new task
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param task body message.CreateTaskReq true "create a new task request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateTaskTabCfgByCsvReq}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/{taskId}/create-tasktabcfg-bycsv [post]
func CreateTaskTableConfigByCSV(c *gin.Context) {
	log.Infof("CreateTaskTableConfigByCSV controller receive channelId:%s", c.Param("channelId"))
	req := &message.CreateTaskTabCfgByCsvReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTaskTableConfigByCSV controller receive request: %s", requestString)
		filepath := fmt.Sprintf("./data/channel/%d/%d/", req.ChannelId, req.TaskId)
		csvData, err := service.GetChannelService().ParsingCSVForTaskTableConfigs(c, req.ChannelId, req.TaskId, filepath+req.FileName)
		if err != nil {
			log.Errorf("CreateTaskTableConfigByCSV parsing csv error")
		}
		csvreq := &message.CreateTaskTableConfigsReq{
			ChannelId:        req.ChannelId,
			TaskId:           req.TaskId,
			TaskTableConfigs: csvData,
		}
		resp, err := service.GetChannelService().CreateTaskTableConfigsByCSV(c, csvreq)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// FixTaskStatus task execution progress interface
// @Summary task execution progress
// @Description task execution progress
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param query body message.GetTaskProgressReq true "get task progress request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTaskProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/fix-task-status [post]
func FixTaskStatus(c *gin.Context) {
	log.Infof("FixTaskStatus controller receive request channelId:%s", c.Param("channelId"))
	req := &message.FixTaskStatusReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("FixTaskStatus requestString %s", requestString)
		resp, err := service.GetChannelService().FixTaskStatus(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// SplitTask split task into multi task interface
// @Summary split task into multi task
// @Description split task into multi task
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param query body message.SplitTaskReq true "split task request parameter"
// @Success 200 {object} message.CommonResult{data=message.SplitTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/split-task [post]
func SplitTask(c *gin.Context) {
	log.Infof("SplitTask controller receive request channelId:%s", c.Param("channelId"))
	req := &message.SplitTaskReq{}
	req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("SplitTask requestString %s", requestString)
		resp, err := service.GetChannelService().SplitTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// VerifySplitTask verify task split interface
// @Summary verify task split
// @Description verify task split
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param query body message.VerifySplitTaskReq true "verify split task request parameter"
// @Success 200 {object} message.CommonResult{data=message.VerifySplitTaskResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/split-task/verify [post]
func VerifySplitTask(c *gin.Context) {
	log.Infof("VerifySplitTask controller receive request channelId:%s", c.Param("channelId"))
	req := &message.VerifySplitTaskReq{}
	req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("VerifySplitTask requestString %s", requestString)
		resp, page, err := service.GetChannelService().VerifySplitTask(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetSourceTableColumns get table columns by selected table
// @Summary get table columns by selected table
// @Description get table columns by selected table, filterQueries in [SCHEMA_NAMES TABLE_NAME COLUMN_NAME DATA_TYPE DATA_DEFAULT DATA_LENGTH DATA_PRECISION DATA_SCALE NULLABLE COLUMN_COMMENT]
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param filterQueries body string true "enable query condition, values in [SCHEMA_NAMES TABLE_NAME COLUMN_NAME DATA_TYPE DATA_DEFAULT DATA_LENGTH DATA_PRECISION DATA_SCALE NULLABLE COLUMN_COMMENT] "
// @Param schemaNames body string false "schemaNames, enable when filterQueries contains SCHEMA_NAMES"
// @Param tableName body string false "tableName, enable when filterQueries contains TABLE_NAME"
// @Param columnName body string false "columnName, enable when filterQueries contains COLUMN_NAME"
// @Param dataType body string false "dataType, enable when filterQueries contains DATA_TYPE"
// @Param dataDefault body string false "dataDefault, enable when filterQueries contains DATA_DEFAULT"
// @Param dataPrecision body int false "dataPrecision, enable when filterQueries contains DATA_PRECISION"
// @Param dataLength body int false "dataLength, enable when filterQueries contains DATA_LENGTH"
// @Param dataScale body int false "dataScale, enable when filterQueries contains DATA_SCALE"
// @Param nullable body string false "nullable, enable when filterQueries contains NULLABLE"
// @Param columnComment body string false "columnComment, enable when filterQueries contains COLUMN_COMMENT"
// @Success 200 {object} message.CommonResult{data=message.GetSourceTableColumnsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/source-table/columns [get]
func GetSourceTableColumns(c *gin.Context) {
	log.Infof("GetSourceTableColumns controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetSourceTableColumnsReq{}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, page, err := service.GetChannelService().GetSourceTableColumns(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetSyncSourceTableColumnsStatus get table columns sync status
// @Summary get table columns sync status
// @Description get table columns sync status, status in [NODATA, FETCHING, FETCHED, FAILED]
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.GetSyncSourceTableColumnsStatusResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/source-table/columns/sync/status [get]
func GetSyncSourceTableColumnsStatus(c *gin.Context) {
	log.Infof("GetSyncSourceTableColumnsStatus controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetSyncSourceTableColumnsStatusReq{}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().GetSyncSourceTableColumnsStatus(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// SyncSourceTableColumns sync all table columns by channel_schema_tables
// @Summary sync all table columns by channel_schema_tables
// @Description sync all table columns by all tables in channel, not just specified task
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.SyncSourceTableColumnsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/source-table/columns/sync [post]
func SyncSourceTableColumns(c *gin.Context) {
	log.Infof("GetSourceTableColumns controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.SyncSourceTableColumnsReq{}
	req.ChannelId = channelId
	req.TaskId = taskId
	resp, err := service.GetChannelService().SyncSourceTableColumns(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// BatchUpdateSourceTableColumns batch update sourceTableColumns interface
// @Summary batch update sourceTableColumns
// @Description batch update sourceTableColumns
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param taskId path int true "taskId"
// @Param data body []message.TableColumnCustomMapRule true "all table columns data needed to update, and inner columnDigest is required to avoid sensitive issue"
// @Success 200 {object} message.CommonResult{data=message.BatchUpdateSourceTableColumnsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/source-table/columns [post]
func BatchUpdateSourceTableColumns(c *gin.Context) {
	log.Infof("BatchUpdateSourceTableColumns controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))

	req := &message.BatchUpdateSourceTableColumnsReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, err := service.GetChannelService().BatchUpdateSourceTableColumns(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteSourceTableColumns delete table columns by conditions
// @Summary delete table columns by conditions
// @Description delete table columns by conditions
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param tableColumnId body int false "table column id"
// @Param schemaName body string false "schema name"
// @Param tableName body string false "table name"
// @Param columnName body string false "column name"
// @Success 200 {object} message.CommonResult{data=message.DeleteSourceTableColumnsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/source-table/columns [delete]
func DeleteSourceTableColumns(c *gin.Context) {
	log.Infof("DeleteSourceTableColumns controller receive request channelId:%s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.DeleteSourceTableColumnsReq{}
	req.ChannelId = channelId
	resp, err := service.GetChannelService().DeleteSourceTableColumns(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// DownloadCreateChunkSql Download Reverse OBJ DDL By IncludingSchema interface
// @Summary Download Reverse OBJ DDL By IncludingSchema
// @Description Download Reverse OBJ DDL By IncludingSchema
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.CreateChunkReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateChunkResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/create_chunk/download [post]
func DownloadCreateChunkSql(c *gin.Context) {
	req := &message.CreateChunkReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadCreateChunkSql controller receive request %s", requestString)

		createChunkSql, err := service.GetChannelService().GetCreateChunkSql(c, req)
		if err != nil {
			log.Infof("DownloadCreateChunkSql run failed, err:%s", err)
			controller.HandleResponse(c, &message.CreateChunkResp{}, err, nil)
		}

		reportPath := config.GetGlobalConfig().DataDir
		service.GetChannelService().MkdirIfNotExist(fmt.Sprintf("%s/create_chunk/", reportPath))
		reportName := fmt.Sprintf("%s/create_chunk/create_chunk_%d_%d_%d.sql", reportPath, req.ChannelId, req.TaskId, time.Now().Unix())
		err = service.GetChannelService().WriteSql(createChunkSql, reportName)
		if err != nil {
			log.Infof("DownloadCreateChunkSql run failed, err:%s", err)
			controller.HandleResponse(c, &message.CreateChunkResp{}, err, nil)
		}

		_, err = os.Stat(reportName)
		if err == nil {
			log.Infof("start to download sql file:%s", reportName)
			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Disposition", "attachment; filename="+reportName)
			c.Header("Content-Transfer-Encoding", "binary")
			// c.Header("Transfer-Encoding", "true")
			c.Header("Cache-Control", "no-cache")
			c.File(reportName)
		} else if errors.Is(err, os.ErrNotExist) {
			log.Infof("report file %s not exists, err:%s", reportName, err)
			controller.HandleResponse(c, &message.CreateChunkResp{}, err, nil)
		}
	}
}

// DownloadDropChunkSql Download Reverse OBJ DDL By IncludingSchema interface
// @Summary Download Reverse OBJ DDL By IncludingSchema
// @Description Download Reverse OBJ DDL By IncludingSchema
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.CreateChunkReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateChunkResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/drop_chunk/download [post]
func DownloadDropChunkSql(c *gin.Context) {
	req := &message.CreateChunkReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadDropChunkSql controller receive request %s", requestString)

		createChunkSql, err := service.GetChannelService().GetDropChunkSql(c, req)
		if err != nil {
			log.Infof("DownloadDropChunkSql run failed, err:%s", err)
			controller.HandleResponse(c, &message.CreateChunkResp{}, err, nil)
		}

		reportPath := config.GetGlobalConfig().DataDir
		service.GetChannelService().MkdirIfNotExist(fmt.Sprintf("%s/drop_chunk/", reportPath))
		reportName := fmt.Sprintf("%s/drop_chunk/drop_chunk_%d_%d_%d.sql", reportPath, req.ChannelId, req.TaskId, time.Now().Unix())
		err = service.GetChannelService().WriteSql(createChunkSql, reportName)
		if err != nil {
			log.Infof("DownloadDropChunkSql run failed, err:%s", err)
			controller.HandleResponse(c, &message.CreateChunkResp{}, err, nil)
		}

		_, err = os.Stat(reportName)
		if err == nil {
			log.Infof("start to download sql file:%s", reportName)
			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Disposition", "attachment; filename="+reportName)
			c.Header("Content-Transfer-Encoding", "binary")
			// c.Header("Transfer-Encoding", "true")
			c.Header("Cache-Control", "no-cache")
			c.File(reportName)
		} else if errors.Is(err, os.ErrNotExist) {
			log.Infof("report file %s not exists, err:%s", reportName, err)
			controller.HandleResponse(c, &message.CreateChunkResp{}, err, nil)
		}
	}
}

// UploadForObjectParserCfg upload object parser cfg file
// @Summary upload object parser cfg file
// @Description upload object parser cfg file
// @Tags channel
// @Accept multipart/form-data
// @Produce application/json
// @Param file formData file true "object parser cfg file"
// @Success 200 {object} message.CommonResult{data=message.CreateObjectParserCfgResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/object-parser-cfg/upload [post]
func UploadForObjectParserCfg(c *gin.Context) {
	log.Infof("UploadForObjectParserCfg controller receive request channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))

	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))

	//检查文件目录是否存在，不存在则创建文件目录
	filepath := fmt.Sprintf("./data/object_parser/%d/%d/", channelId, taskId)
	_, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			err := os.MkdirAll(filepath, 0766)
			if err != nil {
				log.Errorf("os.MkdirAll err")
				return
			}
		}
	}

	//保存文件
	f, _ := c.FormFile("file")
	err = c.SaveUploadedFile(f, filepath+f.Filename)
	if err != nil {
		log.Errorf("SaveUploadedFile err")
	}
	log.Infof("Upload receive request: %s", f.Filename)

	res := &message.CommonResp{}

	controller.HandleResponse(c, res, err, nil)
}

// CreateObjectParserCfg create object parser cfg
// @Summary create object parser cfg
// @Description create object parser cfg
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param data body message.CreateObjectParserCfgDataReq true "object parser cfg data"
// @Success 200 {object} message.CommonResult{data=message.CreateObjectParserCfgResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/object-parser-cfg/create [post]
func CreateObjectParserCfg(c *gin.Context) {
	log.Infof("CreateObjectParserCfg controller receive channelId:%s", c.Param("channelId"))
	req := &message.CreateObjectParserCfgReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateObjectParserCfg controller receive request: %s", requestString)
		filepath := fmt.Sprintf("./data/object_parser/%d/%d/", req.ChannelId, req.TaskId)
		csvData, err := service.GetChannelService().ParsingCSVForObjectParserCfg(c, req.ChannelId, req.TaskId, filepath+req.FileName)
		if err != nil {
			log.Errorf("SaveObjectParserCfg parsing csv error")
		}
		csvreq := &message.CreateObjectParserCfgDataReq{
			ChannelId:        req.ChannelId,
			TaskId:           req.TaskId,
			ObjectParserCfgs: csvData,
		}
		resp, err := service.GetChannelService().CreateObjectParserCfgByCSV(c, csvreq)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListObjectParserCfg list object parser cfg with filters
// @Summary List object parser configurations with pagination and filters
// @Tags channel
// @Accept json
// @Produce json
// @Param channelId path int true "Channel ID"
// @Param taskId path int true "Task ID"
// @Param data body message.ListObjectParserCfgReq true "list request with filters"
// @Success 200 {object} message.CommonResult{data=message.ListObjectParserCfgResp,page=message.Page}
// @Failure 400 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Router /channel/{channelId}/task/{taskId}/object-parser-cfg/list [post]
func ListObjectParserCfg(c *gin.Context) {
	log.Infof("ListObjectParserCfg controller receive channelId:%s, taskId:%s", c.Param("channelId"), c.Param("taskId"))
	req := &message.ListObjectParserCfgReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListObjectParserCfg controller receive request: %s", requestString)
		channelId, err := strconv.Atoi(c.Param("channelId"))
		if err != nil {
			controller.HandleResponse(c, nil, err, nil)
			return
		}
		taskId, err := strconv.Atoi(c.Param("taskId"))
		if err != nil {
			controller.HandleResponse(c, nil, err, nil)
			return
		}
		req.ChannelId = channelId
		req.TaskId = taskId
		resp, page, err := service.GetChannelService().ListObjectParserCfg(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// CreateDefaultTasksForM2T create default tasks for m2t
// @Summary create default tasks for m2t
// @Description create default tasks for m2t
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.CreateDefaultTasksForM2TResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/create-default-tasks-for-m2t [post]
func CreateDefaultTasksForM2T(c *gin.Context) {
	log.Infof("CreateDefaultTasksForM2T controller receive request channelId: %s", c.Param("channelId"))

	req := &message.CreateDefaultTasksForM2TReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("create-default-tasks-for-m2t request: %s", requestString)
		resp, err := service.GetChannelService().CreateDefaultTasksForM2T(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOneTaskByChannelId get one task by channelId
// @Summary get one task by channelId
// @Description get one task by channelId
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetOneTaskByChannelIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/task/get-one [get]
func GetOneTaskByChannelId(c *gin.Context) {
	log.Infof("GetTop1TaskByChannelId controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetOneTaskByChannelIdReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetOneTaskByChannelId controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().GetOneTaskByChannelId(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
