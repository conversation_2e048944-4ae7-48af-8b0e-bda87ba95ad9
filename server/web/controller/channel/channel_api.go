package channel

import (
	"fmt"
	"os"
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CreateChannel Create channel interface
// @Summary Create channel
// @Description Create channel
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelInfo body message.CreateOrUpdateChannelReq true "create channel request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateChannelResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/ [post]
func CreateChannel(c *gin.Context) {
	req := &message.CreateOrUpdateChannelReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateChannel controller receive request: %s", requestString)
		resp, err := service.GetChannelService().CreateChannel(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateChannel update a channel interface
// @Summary Update a channel
// @Description Update a channel
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param channelInfo body message.CreateOrUpdateChannelReq true "update channel request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateChannelResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/update [post]
func UpdateChannel(c *gin.Context) {
	req := &message.CreateOrUpdateChannelReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateChannel controller, receive channelId:%s, receive request: %s", c.Param("channelId"), requestString)
		req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))
		resp, err := service.GetChannelService().UpdateChannel(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteChannel delete a channel interface
// @Summary delete a channel
// @Description delete a channel
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.DeleteChannelResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId} [delete]
func DeleteChannel(c *gin.Context) {
	log.Infof("DeleteChannel controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.DeleteChannelReq{ChannelId: channelId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().DeleteChannel(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetChannel query a channel information
// @Summary query a channel information
// @Description query a channel information
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetChannelResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/ [get]
func GetChannel(c *gin.Context) {
	log.Infof("GetChannel controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetChannelReq{ChannelId: channelId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetChannel(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListChannels list channel interface
// @Summary list channel
// @Description list channel
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param page body message.ListChannelsReq true "list channel request"
// @Success 200 {object} message.ResultWithPage{data=[]message.Channel}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/list [post]
func ListChannels(c *gin.Context) {
	req := &message.ListChannelsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListChannels controller receive request %s", requestString)
		resp, page, err := service.GetChannelService().ListChannels(c, req)
		controller.HandleResponse(c, resp.Channels, err, page)
	}
}

// CreateChannelSchemas Create channel schemas interface
// @Summary Create channel schemas
// @Description Create channel schemas
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param channelObjects body message.CreateOrUpdateChannelSchemasReq true "create channel schemas request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateChannelSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/schemas/ [post]
func CreateChannelSchemas(c *gin.Context) {
	log.Infof("CreateChannelSchemas controller receive request channelId: %s", c.Param("channelId"))
	req := &message.CreateOrUpdateChannelSchemasReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateChannelSchemas controller receive request: %s", requestString)
		channelId, _ := strconv.Atoi(c.Param("channelId"))
		req.ChannelId = channelId
		resp, err := service.GetChannelService().CreateChannelSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateChannelSchemas Update channel schemas interface
// @Summary Update channel schemas
// @Description Update channel schemas
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param channelObjects body message.CreateOrUpdateChannelSchemasReq true "update channel schemas request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateChannelSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/schemas/update [post]
func UpdateChannelSchemas(c *gin.Context) {
	log.Infof("UpdateChannelSchemas controller receive request channelId: %s", c.Param("channelId"))
	req := &message.CreateOrUpdateChannelSchemasReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateChannelSchemas controller receive request: %s", requestString)
		channelId, _ := strconv.Atoi(c.Param("channelId"))
		req.ChannelId = channelId
		resp, err := service.GetChannelService().UpdateChannelSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetChannelSchemas get channel schemas interface
// @Summary get channel schemas
// @Description get channel schemas
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetChannelSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/schemas/ [get]
func GetChannelSchemas(c *gin.Context) {
	log.Infof("GetChannelSchemas controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetChannelSchemasReq{ChannelId: channelId}
	if requestString, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		log.Infof("GetChannelSchemas controller receive request: %s", requestString)
		resp, err := service.GetChannelService().GetChannelSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ExecutionPreCheck submit and execution pre-check infos interface
// @Summary submit and execution pre-check infos
// @Description submit and execution pre-check infos
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param preCheckInfos body message.ExecutionPreCheckReq true "submit and execution pre-check infos request parameter"
// @Success 200 {object} message.CommonResult{data=message.ExecutionPreCheckResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/pre-check/execution [post]
func ExecutionPreCheck(c *gin.Context) {
	log.Infof("ExecutionPreCheck controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.ExecutionPreCheckReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ExecutionPreCheck controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().ExecutionPreCheck(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetPreCheckInfosByChannelId get channel pre-check infos interface
// @Summary get channel pre-check infos
// @Description get channel pre-check infos
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetPreCheckInfosByChannelIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/pre-check [get]
func GetPreCheckInfosByChannelId(c *gin.Context) {
	log.Infof("GetPreCheckInfosByChannelId controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetPreCheckInfosByChannelIdReq{ChannelId: channelId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetPreCheckInfosByChannelId(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdatePreCheckInfo update channel pre-check info interface
// @Summary update channel pre-check info
// @Description update channel pre-check info
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param preCheckInfo body message.UpdatePreCheckInfoReq true "update channel pre-check info request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdatePreCheckInfoResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/pre-check/update [post]
func UpdatePreCheckInfo(c *gin.Context) {
	log.Infof("UpdatePreCheckInfo controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.UpdatePreCheckInfoReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdatePreCheckInfo controller receive request: %s", requestString)
		req.ChannelId = channelId
		resp, err := service.GetChannelService().UpdatePreCheckInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetPreCheckInfoById get channel pre-check info by id interface
// @Summary get channel pre-check info by id
// @Description get channel pre-check info by id
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetPreCheckInfoByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/{channelId}/pre-check/:preCheckId [get]
func GetPreCheckInfoById(c *gin.Context) {
	log.Infof("GetPreCheckInfoById controller receive request channelId: %s, preCheckId:%s", c.Param("channelId"), c.Param("preCheckId"))
	preCheckId, _ := strconv.Atoi(c.Param("preCheckId"))
	req := &message.GetPreCheckInfoByIdReq{PreCheckId: preCheckId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetPreCheckInfoById(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// BatchDeleteChannelSchemas delete channel schemas interface
// @Summary delete channel schemas
// @Description delete channel schemas
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.BatchDeleteChannelSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/schemas/delete [post]
func BatchDeleteChannelSchemas(c *gin.Context) {
	log.Infof("BatchDeleteChannelSchemas controller receive request channelId: %s", c.Param("channelId"))

	req := &message.BatchDeleteChannelSchemasReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchDeleteChannelSchemas controller receive request: %s", requestString)
		resp, err := service.GetChannelService().BatchDeleteChannelSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetUnSelectedChannelSchemas get channel schemas interface
// @Summary get channel schemas
// @Description get channel schemas
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetUnselectedDataSourceSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/datasource/{datasourceId} [get]
func GetUnSelectedChannelSchemas(c *gin.Context) {
	log.Infof("GetUnSelectedChannelSchemas controller receive request datasourceId: %s", c.Param("datasourceId"))
	datasourceId, _ := strconv.Atoi(c.Param("datasourceId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetUnselectedDataSourceSchemasReq{DataSourceID: datasourceId,
		ChannelID: channelId}
	if requestString, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		log.Infof("GetUnSelectedChannelSchemas controller receive request: %s", requestString)
		resp, err := service.GetChannelService().GetUnselectedDataSourceSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// SearchUnSelectedChannelSchemas get channel schemas interface
// @Summary get channel schemas
// @Description get channel schemas
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.GetUnselectedDataSourceSchemasResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /channel/{channelId}/datasource/{datasourceId} [post]
func SearchUnSelectedChannelSchemas(c *gin.Context) {
	log.Infof("SearchUnSelectedChannelSchemas controller receive request datasourceId: %s", c.Param("datasourceId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.SearchUnselectedDataSourceSchemasReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("SearchUnSelectedChannelSchemas controller receive request: %s", requestString)
		req.ChannelID = channelId
		resp, err := service.GetChannelService().SearchUnselectedDataSourceSchemas(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DownloadTemplate Download Reverse OBJ DDL By IncludingSchema interface
// @Summary Download Reverse OBJ DDL By IncludingSchema
// @Description Download Reverse OBJ DDL By IncludingSchema
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDDLDownloadReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/download/template [post]
func DownloadTemplate(c *gin.Context) {
	req := &message.DownloadTemplateReq{}
	var err error
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadTemplate controller receive request %s", requestString)
		templateName := ""
		templatePath := ""
		if req.TemplateType == 1 {
			templatePath = "./data/task_template"
			templateName = "sample_task_cfg.csv"
			if !IsFileExist(fmt.Sprintf("%s/%s", templatePath, templateName)) {
				var record = [][]string{
					{"源DB", "源schema", "源表", "目标DB", "目标schema", "目标表"},
					{"findpt", "FINDPT", "ACCOUNT", "timsdev", "FINDPT0626A", "ACOUNT01A"},
					{"findpt", "TEST", "TEST11", "timsdev", "TEST0626A", "TEST11"},
				}
				err = file.OpenAndWriteToCSVFile(templatePath, templateName, record)
			}
		} else if req.TemplateType == 4 {
			templatePath = "./data/object_parser/template"
			templateName = "object_parser_sample_cfg.csv"
			if !IsFileExist(fmt.Sprintf("%s/%s", templatePath, templateName)) {
				var record = [][]string{
					{"SCHEMA", "OBJECT_TYPE(PROCEDURE|FUNCTION|PACKAGE)", "OBJECT_NAME"},
					{"FINDPT", "PACKAGE", "DEMO"},
					{"FINDPT", "PROCEDURE", "A"},
					{"FINDPT", "PROCEDURE", "B"},
					{"FINDPT", "PROCEDURE", "C"},
					{"FINDPT", "PROCEDURE", "D"},
				}
				err = file.OpenAndWriteToCSVFile(templatePath, templateName, record)
			}
		} else {
			templatePath = "./data/task_template"
			templateName = "sample_task_tab_cfg.csv"
			if !IsFileExist(fmt.Sprintf("%s/%s", templatePath, templateName)) {
				var record = [][]string{
					{"源DB", "源schema", "源表", "目标DB", "目标schema", "目标表", "列筛选操作(包含：Y，忽略：N)", "源库列清单", "目标库列清单", "源库数据筛选条件（where以后的部分）", "目标库数据筛选条件（where以后的部分）", "源库SQL HINT", "目标库SQL HINT", "是否启用chunk分片（Y/N）", "备注"},
					{"findpt", "FINDPT", "ACCOUNT", "timsdev", "FINDPT0724C", "ACOUNT01A", "", "", "", "ACCOUNT_ID < **********", "", "", "", "Y", "中文测试"},
					{"findpt", "FINDPT", "ACCOUNT2", "timsdev", "FINDPT0724C", "ACOUNT02A", "", "", "", "ACCOUNT_ID < **********", "", "", "", "Y", "ABC123！@#"},
				}
				err = file.OpenAndWriteToCSVFile(templatePath, templateName, record)
			}
		}
		if err != nil {
			log.Infof("create template file %s failed, err:%s", templateName, err)
			controller.HandleResponse(c, &message.CommonResp{}, err, nil)
		}

		fullPathName := fmt.Sprintf("%s/%s", templatePath, templateName)
		_, err = os.Stat(fullPathName)
		if err == nil {
			log.Infof("start to download template file:%s", fullPathName)
			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Disposition", "attachment; filename="+fullPathName)
			c.Header("Content-Transfer-Encoding", "binary")
			// c.Header("Transfer-Encoding", "true")
			c.Header("Cache-Control", "no-cache")
			c.File(fullPathName)
		} else {
			log.Infof("template file %s not exists, err:%s", fullPathName, err)
			controller.HandleResponse(c, &message.CommonResp{}, err, nil)
		}
	}
}

func IsFileExist(filepath string) bool {
	_, err := os.Stat(filepath)
	if err == nil {
		return true
	}
	return false
}

// ExportSourceTableColumnsToCSV export source table columns to csv
// @Summary export source table columns to csv
// @Description export source table columns to csv
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param objReq body message.ExportSourceTableColumnsToCSVReq true "export source table columns to csv request parameter"
// @Success 200 {object} message.CommonResult{data=message.ExportSourceTableColumnsToCSVResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/:channelId/source-table/columns/csv/export [get]
func ExportSourceTableColumnsToCSV(c *gin.Context) {
	req := &message.ExportSourceTableColumnsToCSVReq{}
	req.ChannelId, _ = strconv.Atoi(c.Param("channelId"))

	log.Infof("ExportSourceTableColumnsToCSV controller receive request channelId: %d", req.ChannelId)
	resp, err := service.GetChannelService().ExportSourceTableColumnsToCSV(c, req)
	if err != nil {
		log.Errorf("ExportSourceTableColumnsToCSV controller failed, channelId:%d, err:%s", req.ChannelId, err)
	}
	controller.HandleFileResponse(c, resp, err)
}

// ImportSourceTableColumnByCSV import source table columns by csv
// @Summary import source table columns by csv
// @Description import source table columns by csv
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param fileName body string true "filename"
// @Param fileDir body string true "fileDir"
// @Success 200 {object} message.CommonResult{data=message.ImportSourceTableColumnByCSVResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/:channelId/source-table/columns/csv/import [POST]
func ImportSourceTableColumnByCSV(c *gin.Context) {
	log.Infof("ImportSourceTableColumnByCSV controller receive request channelId: %s", c.Param("channelId"))
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.ImportSourceTableColumnByCSVReq{}

	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		req.ChannelId = channelId
		log.Infof("BatchDeleteChannelSchemas controller receive request: %s", requestString)
		resp, err := service.GetChannelService().ImportSourceTableColumnByCSV(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UploadSourceTableColumnCSV upload source table columns csv
// @Summary upload source table columns csv
// @Description upload source table columns csv
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.CommonResult{data=message.UploadSourceTableColumnCSVResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /channel/:channelId/source-table/columns/csv/upload [POST]
func UploadSourceTableColumnCSV(c *gin.Context) {
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	log.Infof("UploadSourceTableColumnCSV controller receive channelId:%d", channelId)

	req := &message.UploadSourceTableColumnCSVReq{}

	fileDir := fmt.Sprintf("./data/column_data_import/%d", channelId)
	createDirErr := file.CreateIfNotExist(fileDir)
	if createDirErr != nil {
		log.Errorf("CreateIfNotExist failed, channelId:%d, err:%s", req.ChannelId, createDirErr)
		controller.HandleResponse(c, nil, createDirErr, nil)
		return
	}

	fileName, saveFileErr := controller.SaveUploadFile(c, "file", fileDir)
	if saveFileErr != nil {
		log.Errorf("SaveUploadFile failed, channelId:%d, err:%s", req.ChannelId, saveFileErr)
		return
	}

	resp := message.UploadSourceTableColumnCSVResp{
		ChannelId: channelId,
		FileName:  fileName,
		FileDir:   fileDir,
	}
	controller.HandleResponse(c, resp, nil, nil)
}

// GetActiveTmsSessions get active TMS sessions
// @Summary get active TMS sessions
// @Description get active TMS sessions from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetActiveTmsSessionsResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/sessions-active [get]
func GetActiveTmsSessions(c *gin.Context) {
	log.Infof("GetActiveTmsSessions controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetActiveTmsSessionsReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetActiveTmsSessions(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTmsSessionProgress get TMS session progress
// @Summary get TMS session progress
// @Description get TMS session progress from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetTmsSessionProgressResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/sessions-progress [get]
func GetTmsSessionProgress(c *gin.Context) {
	log.Infof("GetTmsSessionProgress controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetTmsSessionProgressReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetTmsSessionProgress(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTmsSessionRatio get TMS session ratio
// @Summary get TMS session ratio
// @Description get TMS session ratio from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetTmsSessionRatioResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/sessions-ratio [get]
func GetTmsSessionRatio(c *gin.Context) {
	log.Infof("GetTmsSessionRatio controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetTmsSessionRatioReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetTmsSessionRatio(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetPhysicalFileIOStats get physical file I/O stats
// @Summary get physical file I/O stats
// @Description get physical file I/O stats from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetPhysicalFileIOStatsResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/io-file-stats [get]
func GetPhysicalFileIOStats(c *gin.Context) {
	log.Infof("GetPhysicalFileIOStats controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetPhysicalFileIOStatsReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetPhysicalFileIOStats(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetHotFileInformation get hot file information
// @Summary get hot file information
// @Description get hot file information from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetHotFileInformationResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/io-hot-files [get]
func GetHotFileInformation(c *gin.Context) {
	log.Infof("GetHotFileInformation controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetHotFileInformationReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetHotFileInformation(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTiDBCpuRate get tidb cpu rate
// @Summary get tidb cpu rate
// @Description get tidb cpu rate from tidb database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetTiDBPerformanceResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/tidb/:reqType/:StartTime/:EndTime/:Step [get]
func GetTiDBPerformance(c *gin.Context) {
	log.Infof("GetTiDBCpuRate controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	StartTime := c.Param("StartTime")
	EndTime := c.Param("EndTime")
	Step := c.Param("Step")
	reqType := c.Param("reqType")
	req := &message.GetTiDBPerformanceReq{
		ChannelId: channelId,
		StartTime: StartTime,
		EndTime:   EndTime,
		Step:      Step,
		ReqType:   reqType,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetTiDBPerformance(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTiDBCpuRate get tidb cpu rate
// @Summary get tidb cpu rate
// @Description get tidb cpu rate from tidb database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetTiDBPerformanceResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/tidb/query-range/:reqType/ [get]
func GetTiDBPerformanceByRange(c *gin.Context) {
	log.Infof("GetTiDBCpuRate controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	reqType := c.Param("reqType")
	req := &message.GetTiDBPerformanceReq{}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.ChannelId = channelId
		req.ReqType = reqType
		resp, err := service.GetChannelService().GetTiDBPerformance(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTiDBCpuRate get tidb cpu rate
// @Summary get tidb cpu rate
// @Description get tidb cpu rate from tidb database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetTiDBPerformanceResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/tidb/query/:reqType/ [get]
func QueryTiDBMetaByPrometheus(c *gin.Context) {
	log.Infof("GetTiDBCpuRate controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	reqType := c.Param("reqType")
	req := &message.GetTiDBPerformanceReq{}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.ChannelId = channelId
		req.ReqType = reqType
		resp, err := service.GetChannelService().QueryTiDBMetaByPrometheus(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetHotFileInformation get hot file information
// @Summary get hot file information
// @Description get hot file information from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetTableSpaceStatsReq}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/tablespace-stats [get]
func GetOracleTableSpaceStats(c *gin.Context) {
	log.Infof("GetHotFileInformation controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetTableSpaceStatsReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetOracleTableSpaceStats(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOracleObjectStats get oracle object stats
// @Summary get oracle object stats
// @Description get oracle object stats from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetOracleObjectStatsResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/object-stats [get]
func GetOracleObjectStats(c *gin.Context) {
	log.Infof("GetOracleObjectStats controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetOracleObjectStatsReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetOracleObjectStats(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOracleObjectStatsBySchema get oracle object stats by schema
// @Summary get oracle object stats by schema
// @Description get oracle object stats by schema from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetOracleObjectStatsResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/object-stats-by-schema [get]
func GetOracleObjectStatsBySchema(c *gin.Context) {
	log.Infof("GetOracleObjectStatsBySchema controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetOracleObjectStatsReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetOracleObjectStatsBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOracleMeta get oracle meta
// @Summary get oracle meta
// @Description get oracle meta from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetOracleMetaResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/meta/:reqType [get]
func GetOracleMeta(c *gin.Context) {
	log.Infof("GetOracleMeta controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	reqType := c.Param("reqType")
	req := &message.GetOracleMetaReq{
		ChannelId: channelId,
		ReqType:   reqType,
	}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetChannelService().GetOracleMeta(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOracleTableNumberRowsBySchema get oracle table number rows by schema
// @Summary get oracle table number rows by schema
// @Description get oracle table number rows by schema from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetOracleTableNumberRowsBySchemaResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/performance/oracle/table-number-rows-by-schema [get]
func GetOracleTableNumberRowsBySchema(c *gin.Context) {
	log.Infof("GetOracleTableNumberRowsBySchema controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	req := &message.GetOracleTableNumberRowsBySchemaReq{
		ChannelId: channelId,
	}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetChannelService().GetOracleTableNumberRowsBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetObjectAssessOverview get object assess overview
// @Summary get object assess overview
// @Description get object assess overview from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetObjectAssessOverviewResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/task/:taskId/object-assess-overview/ [get]
func GetObjectAssessOverview(c *gin.Context) {
	log.Infof("GetObjectAssessOverview controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetObjectAssessOverviewReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetChannelService().GetObjectAssessOverview(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOracleObjectList get oracle object list
// @Summary get oracle object list
// @Description get oracle object list from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetOracleObjectListResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/task/:taskId/oracle/object-list [get]
func GetOracleObjectList(c *gin.Context) {
	log.Infof("GetOracleObjectList controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetOracleObjectListReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetChannelService().GetOracleObjectList(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetAndSaveOracleSegmentSizes get and save oracle segment sizes with gorm
// @Summary get and save oracle segment sizes with gorm
// @Description get and save oracle segment sizes with gorm from Oracle database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.GetOracleSegmentSizesResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/task/:taskId/oracle/build-segment-sizes [get]
func GetAndSaveOracleSegmentSizes(c *gin.Context) {
	log.Infof("GetAndSaveOracleSegmentSizes controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.GetOracleSegmentSizesReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetChannelService().GetAndSaveOracleSegmentSizes(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// QueryOracleSegmentSizes query oracle segment sizes
// @Summary query oracle segment sizes
// @Description query oracle segment sizes from TiDB database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.QueryOracleSegmentSizesResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/task/:taskId/oracle/query-segment-sizes [get]
func QueryOracleSegmentSizes(c *gin.Context) {
	log.Infof("QueryOracleSegmentSizes controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.QueryOracleSegmentSizesReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetChannelService().GetOracleSegmentSizesByTaskId(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// QueryOracleSegmentSumSizesSum query oracle segment sum sizes
// @Summary query oracle segment sum sizes
// @Description query oracle segment sum sizes from TiDB database
// @Tags channel
// @Accept json
// @Produce json
// @Success 200 {object} message.CommonResult{data=message.QueryOracleSegmentSumSizesResp}
// @Failure 500 {object} message.CommonResult
// @Router /channel/:channelId/task/:taskId/oracle/sum-segment-sizes [get]
func QueryOracleSegmentSumSizes(c *gin.Context) {
	log.Infof("QueryOracleSegmentSumSizes controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.QueryOracleSegmentSizesReq{
		ChannelId: channelId,
		TaskId:    taskId,
	}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetChannelService().GetOracleSegmentSumSizesByTaskId(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

func QueryTiDBBaseInfo(c *gin.Context) {
	log.Infof("QueryTiDBBaseInfo controller receive request")
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	reqType := c.Param("reqType")
	req := &message.GetTiDBPerformanceReq{}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.ChannelId = channelId
		req.ReqType = reqType
		resp, err := service.GetChannelService().QueryTiDBBaseInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// AddChannelDatasource 新增channel-datasource关联
// @Summary 新增channel-datasource关联
// @Description 新增channel-datasource关联
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param datasourceId path int true "datasourceId"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Router /channel/{channelId}/add-datasource/{datasourceId} [get]
func AddChannelDatasource(c *gin.Context) {
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	datasourceId, _ := strconv.Atoi(c.Param("datasourceId"))
	resp, err := service.GetChannelService().AddChannelDatasource(c, channelId, datasourceId)
	controller.HandleResponse(c, resp, err, nil)
}

// GetChannelDatasourceList 获取channel关联的数据源列表
// @Summary 获取channel关联的数据源列表
// @Description 根据channelId查询channel_datasources表，然后获取对应的datasource信息
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Success 200 {object} message.GetChannelDatasourceListResp
// @Failure 500 {object} message.CommonResult
// @Router /channel/{channelId}/get-datasource-list/ [get]
func GetChannelDatasourceList(c *gin.Context) {
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	resp, err := service.GetChannelService().GetChannelDatasourceList(c, channelId)
	controller.HandleResponse(c, resp, err, nil)
}

// DeleteChannelDatasource 删除 channel-datasource 关联
// @Summary 删除channel-datasource关联
// @Description 根据datasourceId删除ChannelDatasource的记录
// @Tags channel
// @Accept application/json
// @Produce application/json
// @Param channelId path int true "channelId"
// @Param datasourceId path int true "datasourceId"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Router /channel/{channelId}/del-datasource/{datasourceId} [delete]
func DeleteChannelDatasource(c *gin.Context) {
	channelId, _ := strconv.Atoi(c.Param("channelId"))
	datasourceId, _ := strconv.Atoi(c.Param("datasourceId"))
	resp, err := service.GetChannelService().DeleteChannelDatasourceByChannelIdAndDatasourceId(c, channelId, datasourceId)
	controller.HandleResponse(c, resp, err, nil)
}
