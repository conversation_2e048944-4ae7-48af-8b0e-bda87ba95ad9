package template

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CreateTemplateParamDetails Create template param detail interface
// @Summary Create template param detail
// @Description Create template param detail
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param template body message.CreateOrUpdateTemplateParamDetailReq true "create template param detail request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTemplateParamDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/param-detail [post]
func CreateTemplateParamDetails(c *gin.Context) {
	req := &message.CreateOrUpdateTemplateParamDetailReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTemplateParamDetail controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateTemplateParamDetail(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListTemplateParamDetails list template param detail interface
// @Summary list template param detail
// @Description list template param detail
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param page body message.ListTemplateParamDetailsReq true "list template param detail request"
// @Success 200 {object} message.ResultWithPage{data=message.ListTemplateParamDetailsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/param-detail/{taskparamTemplateId}/list [get]
func ListTemplateParamDetails(c *gin.Context) {
	log.Infof("ListTemplateParamDetails controller receive request taskparamTemplateId:%s", c.Param("taskparamTemplateId"))
	req := &message.ListTemplateParamDetailsReq{}
	req.TaskparamTemplateId, _ = strconv.Atoi(c.Param("taskparamTemplateId"))
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetTemplateService().ListTemplateParamDetails(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTemplateParamDetails update a template interface
// @Summary Update a template
// @Description Update a template
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param template body message.CreateOrUpdateTemplateParamDetailReq true "update template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTemplateParamDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/param-detail/update [post]
func UpdateTemplateParamDetails(c *gin.Context) {
	req := &message.CreateOrUpdateTemplateParamDetailReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTemplateParamDetails controller, receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateTemplateParamDetail(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// BatchDeleteTemplateParamDetails batch delete template params by ids interface
// @Summary batch delete template params by ids
// @Description batch delete template params by ids
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplateIds body message.BatchDeleteTemplateParamDetailsReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.BatchDeleteTemplateParamDetailsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/param-detail/delete [post]
func BatchDeleteTemplateParamDetails(c *gin.Context) {
	req := &message.BatchDeleteTemplateParamDetailsReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchDeleteTaskParamTemplates controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().BatchDeleteTemplateParamDetails(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// CreateTabcolCustMapRules  Create TabcolCustMapRules
// @Summary Create TabcolCustMapRules
// @Description Create TabcolCustMapRules
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplateIds body message.DeleteTabcolCustMapRulesReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/cust/create [post]
func CreateTabcolCustMapRules(c *gin.Context) {
	req := &message.TabcolCustMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTabcolCustMapRules controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateTabcolCustMapRules(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTabcolCustMapRules  Update TabcolCustMapRules
// @Summary Update TabcolCustMapRules
// @Description Update TabcolCustMapRules
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplateIds body message.DeleteTabcolCustMapRulesReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/cust/update [post]
func UpdateTabcolCustMapRules(c *gin.Context) {
	req := &message.TabcolCustMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTabcolCustMapRules controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateTabcolCustMapRules(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteTabcolCustMapRules  Delete TabcolCustMapRules
// @Summary Delete TabcolCustMapRules by id
// @Description Delete TabcolCustMapRules by id
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplateIds body message.DeleteTabcolCustMapRulesReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/cust/delete [post]
func DeleteTabcolCustMapRules(c *gin.Context) {
	req := &message.DeleteTabcolCustMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DeleteTabcolCustMapRules controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().DeleteTabcolCustMapRules(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTabcolCustMapRules  get TabcolCustMapRules list
// @Summary get TabcolCustMapRules list
// @Description get TabcolCustMapRules list
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplateIds body message.GetTabcolCustMapRulesReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CommonResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/cust/list [post]
func GetTabcolCustMapRules(c *gin.Context) {
	req := &message.GetTabcolCustMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTabcolCustMapRules controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().GetTabcolCustMapRules(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
