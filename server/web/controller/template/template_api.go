package template

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CreateTemplateInfo Create template interface
// @Summary Create template
// @Description Create template
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param template body message.CreateOrUpdateTemplateInfoReq true "create template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTemplateInfoResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/ [post]
func CreateTemplateInfo(c *gin.Context) {
	req := &message.CreateOrUpdateTemplateInfoReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTemplate controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateTemplateInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTemplateInfo update a template interface
// @Summary Update a template
// @Description Update a template
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param templateId path int true "templateId"
// @Param template body message.CreateOrUpdateTemplateInfoReq true "update template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTemplateInfoResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/{templateId}/update [post]
func UpdateTemplateInfo(c *gin.Context) {
	req := &message.CreateOrUpdateTemplateInfoReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTemplateInfo controller, receive templateId:%s, receive request: %s", c.Param("templateId"), requestString)
		req.TemplateId, _ = strconv.Atoi(c.Param("templateId"))
		resp, err := service.GetTemplateService().UpdateTemplateInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// BatchDeleteTemplateInfos batch delete templateInfos interface
// @Summary batch delete templateInfos
// @Description batch delete templateInfos
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param templateIds body message.BatchDeleteTemplateInfosReq true "batch delete templateInfos request parameter"
// @Success 200 {object} message.CommonResult{data=message.BatchDeleteTemplateInfosResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /template/delete [post]
func BatchDeleteTemplateInfos(c *gin.Context) {
	req := &message.BatchDeleteTemplateInfosReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchDeleteTemplateInfos controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().BatchDeleteTemplateInfos(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DetailTemplateInfo query a template detail
// @Summary query a template detail
// @Description query a template detail
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param templateId path int true "templateId"
// @Success 200 {object} message.CommonResult{data=message.DetailTemplateInfoResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /template/{templateId}/ [get]
func DetailTemplateInfo(c *gin.Context) {
	log.Infof("DetailTemplateInfo controller receive request templateId: %s", c.Param("templateId"))
	templateId, _ := strconv.Atoi(c.Param("templateId"))
	req := &message.DetailTemplateInfoReq{TemplateId: templateId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetTemplateService().DetailTemplateInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListTemplateInfosByType list template by type interface
// @Summary list template by type
// @Description list template by type
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param page body message.ListTemplatesReq true "list template by type request"
// @Success 200 {object} message.ResultWithPage{data=message.ListTemplatesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/list [post]
func ListTemplateInfosByType(c *gin.Context) {
	req := &message.ListTemplatesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListTemplateInfos controller receive request %s", requestString)
		resp, page, err := service.GetTemplateService().ListTemplateInfosByType(c, req)
		controller.HandleResponse(c, resp.Templates, err, page)
	}
}

// CreateTaskParamTemplate create task param template interface
// @Summary create task param template
// @Description create task param template
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplate body message.CreateOrUpdateTaskParamTemplateReq true "create task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTaskParamTemplateResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/task-param [post]
func CreateTaskParamTemplate(c *gin.Context) {
	req := &message.CreateOrUpdateTaskParamTemplateReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTaskParamTemplate controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateTaskParamTemplate(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTaskParamTemplate update task param template interface
// @Summary update task param template
// @Description update task param template
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplate body message.CreateOrUpdateTaskParamTemplateReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTaskParamTemplateResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/task-param/update [post]
func UpdateTaskParamTemplate(c *gin.Context) {
	req := &message.CreateOrUpdateTaskParamTemplateReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTaskParamTemplate controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateTaskParamTemplate(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// BatchDeleteTaskParamTemplates batch delete task param templates by ids interface
// @Summary batch delete task param templates by ids
// @Description batch delete task param templates by ids
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param taskParamTemplateIds body message.BatchDeleteTaskParamTemplatesReq true "update task param template request parameter"
// @Success 200 {object} message.CommonResult{data=message.BatchDeleteTaskParamTemplatesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/task-param/delete [post]
func BatchDeleteTaskParamTemplates(c *gin.Context) {
	req := &message.BatchDeleteTaskParamTemplatesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchDeleteTaskParamTemplates controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().BatchDeleteTaskParamTemplates(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListTaskParamTemplates list task param templates interface
// @Summary list task param templates
// @Description list task param templates
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param page body message.ListTaskParamTemplatesReq true "list task param templates request parameter"
// @Success 200 {object} message.CommonResult{data=message.ListTaskParamTemplatesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/task-param/list [post]
func ListTaskParamTemplates(c *gin.Context) {
	req := &message.ListTaskParamTemplatesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListTaskParamTemplates controller receive request: %s", requestString)
		resp, page, err := service.GetTemplateService().ListTaskParamTemplates(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetTaskParamTemplate get task param templates interface
// @Summary get task param template by id
// @Description get task param template by id
// @Tags template
// @Accept application/json
// @Produce application/json
// @Param page body message.GetTaskParamTemplateReq true "get task param templates request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTaskParamTemplateResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /template/task-param/{taskparamTemplateId} [get]
func GetTaskParamTemplate(c *gin.Context) {
	req := &message.GetTaskParamTemplateReq{}
	log.Infof("GetTaskParamTemplate controller receive request, taskparamTemplateId: %s", c.Param("taskparamTemplateId"))
	req.TaskparamTemplateID, _ = strconv.Atoi(c.Param("taskparamTemplateId"))
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetTemplateService().GetTaskParamTemplate(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
