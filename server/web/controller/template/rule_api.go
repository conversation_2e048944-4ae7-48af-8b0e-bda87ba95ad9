package template

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CreateTabColMapRule create table column map rule interface
// @Summary Create table column map rule
// @Description Create table column map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param tabColMapRule body message.CreateOrUpdateTabColMapRuleReq true "create table column map rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTabColMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/table-column [post]
func CreateTabColMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateTabColMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTabColMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateTabColMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTabColMapRule update a table column map rule interface
// @Summary update a table column map rule
// @Description update a table column map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param tabColMapRuleId path int true "tabColMapRuleId"
// @Param tabColMapRule body message.CreateOrUpdateTabColMapRuleReq true "update a table column map rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateTabColMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/table-column/{tabColMapRuleId}/update [post]
func UpdateTabColMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateTabColMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateTabColMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateTabColMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteTabColMapRuleById delete a table column map rule by id interface
// @Summary delete a table column map rule by id
// @Description delete a table column map rule by id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param tabColMapRuleId path int true "tabColMapRuleId"
// @Param templateId path int true "templateId"
// @Success 200 {object} message.CommonResult{data=message.DeleteTabColMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /rule/table-column/{templateId}/{tabColMapRuleId} [delete]
func DeleteTabColMapRuleById(c *gin.Context) {
	log.Infof("DeleteTabColMapRuleById controller receive request tabColMapRuleId:%s,templateId:%s", c.Param("tabColMapRuleId"), c.Param("templateId"))
	tabColMapRuleId, _ := strconv.Atoi(c.Param("tabColMapRuleId"))
	templateId, _ := strconv.Atoi(c.Param("templateId"))
	req := &message.DeleteTabColMapRuleByIdReq{TabColMapRuleId: tabColMapRuleId, TmplateId: templateId}
	resp, err := service.GetTemplateService().DeleteTabColMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetTabColMapRuleById query a table column map rule detail by id interface
// @Summary query a table column map rule detail by id
// @Description query a table column map rule detail by id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param tabColMapRuleId path int true "tabColMapRuleId"
// @Success 200 {object} message.CommonResult{data=message.GetTabColMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /rule/table-column/{tabColMapRuleId} [get]
func GetTabColMapRuleById(c *gin.Context) {
	log.Infof("GetTabColMapRuleById controller receive request tabColMapRuleId: %s", c.Param("tabColMapRuleId"))
	tabColMapRuleId, _ := strconv.Atoi(c.Param("tabColMapRuleId"))
	req := &message.GetTabColMapRuleReq{TabColMapRuleId: tabColMapRuleId}
	resp, err := service.GetTemplateService().GetTabColMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ListTabColMapRules list table column map rules interface
// @Summary list table column map rules by template id
// @Description list table column map rules by template id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param page body message.ListTabColMapRulesReq true "list table column map rules by template id request"
// @Success 200 {object} message.ResultWithPage{data=message.ListTabColMapRulesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/table-column/list [post]
func ListTabColMapRules(c *gin.Context) {
	req := &message.ListTabColMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListTabColMapRules controller receive request: %s", requestString)
		resp, page, err := service.GetTemplateService().ListTabColMapRules(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// CreateObjMapRule create a object map rule interface
// @Summary Create a object map rule
// @Description Create a object map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param objMapRule body message.CreateOrUpdateObjMapRuleReq true "create a object map rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateObjMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/object/ [post]
func CreateObjMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateObjMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateObjMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateObjMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateObjMapRule update a object map rule interface
// @Summary update a object map rule
// @Description update a object map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param objMapRuleId path int true "objMapRuleId"
// @Param objMapRule body message.CreateOrUpdateObjMapRuleReq true "update a object map rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateObjMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/object/{objMapRuleId}/update [post]
func UpdateObjMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateObjMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateObjMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateObjMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteObjMapRuleById delete a object map rule interface
// @Summary  delete a object map rule
// @Description  delete a object map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param objMapRuleId path int true "objMapRuleId"
// @Param templateId path int true "templateId"
// @Success 200 {object} message.CommonResult{data=message.DeleteObjMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/object/{templateId}/{objMapRuleId} [delete]
func DeleteObjMapRuleById(c *gin.Context) {
	log.Infof("DeleteObjMapRuleById controller receive request objMapRuleId:%s, templateId:%s", c.Param("objMapRuleId"), c.Param("templateId"))
	objMapRuleId, _ := strconv.Atoi(c.Param("objMapRuleId"))
	templateId, _ := strconv.Atoi(c.Param("templateId"))
	req := &message.DeleteObjMapRuleByIdReq{ObjMapRuleId: objMapRuleId, TmplateId: templateId}
	resp, err := service.GetTemplateService().DeleteObjMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetObjMapRuleById query object map rule detail interface
// @Summary query object map rule detail
// @Description query object map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param objMapRuleId path int true "objMapRuleId"
// @Success 200 {object} message.CommonResult{data=message.GetObjMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /rule/object/{objMapRuleId} [get]
func GetObjMapRuleById(c *gin.Context) {
	log.Infof("GetObjMapRuleById controller receive request objMapRuleId: %s", c.Param("objMapRuleId"))
	objMapRuleId, _ := strconv.Atoi(c.Param("objMapRuleId"))
	req := &message.GetObjMapRuleByIdReq{ObjMapRuleId: objMapRuleId}
	resp, err := service.GetTemplateService().GetObjMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ListObjMapRules list object map rules interface
// @Summary list object map rules
// @Description list object map rules
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param page body message.ListObjMapRulesReq true "list object map rules by template id request"
// @Success 200 {object} message.ResultWithPage{data=message.ListObjMapRulesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/object/list [post]
func ListObjMapRules(c *gin.Context) {
	req := &message.ListObjMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListObjMapRules controller receive request: %s", requestString)
		resp, page, err := service.GetTemplateService().ListObjMapRules(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// CreateSqlMapRule create sql map rule interface
// @Summary Create sql map rule
// @Description Create sql map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param sqlMapRule body message.CreateOrUpdateSqlMapRuleReq true "create sql map rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateSqlMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/sql/ [post]
func CreateSqlMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateSqlMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateSqlMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateSqlMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateSqlMapRule update sql map rule interface
// @Summary update sql map rule
// @Description update sql map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param sqlMapRuleId path int true "sqlMapRuleId"
// @Param sqlMapRule body message.CreateOrUpdateSqlMapRuleReq true "update sql map rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateSqlMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/sql/{sqlMapRuleId}/update [post]
func UpdateSqlMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateSqlMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateSqlMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateSqlMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteSqlMapRuleById delete sql map rule interface
// @Summary delete sql map rule
// @Description delete sql map rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param sqlMapRuleId path int true "sqlMapRuleId"
// @Param templateId path int true "templateId"
// @Success 200 {object} message.CommonResult{data=message.DeleteSqlMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/sql/{templateId}/{sqlMapRuleId} [delete]
func DeleteSqlMapRuleById(c *gin.Context) {
	log.Infof("DeleteSqlMapRuleById controller receive request sqlMapRuleId:%s,templateId:%s", c.Param("sqlMapRuleId"), c.Param("templateId"))
	sqlMapRuleId, _ := strconv.Atoi(c.Param("sqlMapRuleId"))
	templateId, _ := strconv.Atoi(c.Param("templateId"))
	req := &message.DeleteSqlMapRuleByIdReq{SqlMapRuleId: sqlMapRuleId, TmplateId: templateId}
	resp, err := service.GetTemplateService().DeleteSqlMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetSqlMapRuleById query sql map rule detail by id interface
// @Summary query sql map rule detail by id
// @Description query sql map rule by id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param sqlMapRuleId path int true "sqlMapRuleId"
// @Success 200 {object} message.CommonResult{data=message.GetSqlMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /rule/sql/{sqlMapRuleId} [get]
func GetSqlMapRuleById(c *gin.Context) {
	log.Infof("GetObjMapRuleById controller receive request sqlMapRuleId: %s", c.Param("sqlMapRuleId"))
	sqlMapRuleId, _ := strconv.Atoi(c.Param("sqlMapRuleId"))
	req := &message.GetSqlMapRuleByIdReq{SqlMapRuleId: sqlMapRuleId}
	resp, err := service.GetTemplateService().GetSqlMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ListSqlMapRules list sql map rules by template id interface
// @Summary list sql map rules by template id
// @Description list sql map rules by template id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param page body message.ListSqlMapRulesReq true "list object map rules by template id request"
// @Success 200 {object} message.ResultWithPage{data=message.ListSqlMapRulesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/sql/list [post]
func ListSqlMapRules(c *gin.Context) {
	req := &message.ListSqlMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListSqlMapRules controller receive request: %s", requestString)
		resp, page, err := service.GetTemplateService().ListSqlMapRules(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// CreateColDefaultMapRule create a column default value rule interface
// @Summary Create a column default value rule
// @Description Create a column default value rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param colDefaultMapRule body message.CreateOrUpdateColDefaultMapRuleReq true "create a column default value rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateColDefaultMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/default-value/ [post]
func CreateColDefaultMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateColDefaultMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("CreateColDefaultMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().CreateColDefaultMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateColDefaultMapRule update a column default value rule interface
// @Summary update a column default value rule
// @Description update a column default value rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param colDefaultMapRuleId path int true "colDefaultMapRuleId"
// @Param colDefaultMapRule body message.CreateOrUpdateColDefaultMapRuleReq true "update a column default value rule request parameter"
// @Success 200 {object} message.CommonResult{data=message.CreateOrUpdateColDefaultMapRuleResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/default-value/{colDefaultMapRuleId}/update [post]
func UpdateColDefaultMapRule(c *gin.Context) {
	req := &message.CreateOrUpdateColDefaultMapRuleReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateSqlMapRule controller receive request: %s", requestString)
		resp, err := service.GetTemplateService().UpdateColDefaultMapRule(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteColDefaultMapRuleById delete a column default value rule interface
// @Summary delete a column default value rule
// @Description delete a column default value rule
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param colDefaultMapRuleId path int true "colDefaultMapRuleId"
// @Param templateId path int true "templateId"
// @Success 200 {object} message.CommonResult{data=message.DeleteColDefaultMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/default-value/{templateId}/{colDefaultMapRuleId} [delete]
func DeleteColDefaultMapRuleById(c *gin.Context) {
	log.Infof("DeleteColDefaultMapRuleById controller receive request colDefaultMapRuleId:%s,templateId:%s", c.Param("colDefaultMapRuleId"), c.Param("templateId"))
	colDefaultMapRuleId, _ := strconv.Atoi(c.Param("colDefaultMapRuleId"))
	templateId, _ := strconv.Atoi(c.Param("templateId"))
	req := &message.DeleteColDefaultMapRuleByIdReq{ColDefaultMapRuleId: colDefaultMapRuleId, TmplateId: templateId}
	resp, err := service.GetTemplateService().DeleteColDefaultMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// GetColDefaultMapRuleById query column default value rule detail by id interface
// @Summary query column default value rule detail by id
// @Description query column default value rule detail by id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param colDefaultMapRuleId path int true "colDefaultMapRuleId"
// @Success 200 {object} message.CommonResult{data=message.GetColDefaultMapRuleByIdResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /rule/default-value/{colDefaultMapRuleId} [get]
func GetColDefaultMapRuleById(c *gin.Context) {
	log.Infof("GetColDefaultMapRuleById controller receive request colDefaultMapRuleId: %s", c.Param("colDefaultMapRuleId"))
	colDefaultMapRuleId, _ := strconv.Atoi(c.Param("colDefaultMapRuleId"))
	req := &message.GetColDefaultMapRuleByIdReq{ColDefaultMapRuleId: colDefaultMapRuleId}
	resp, err := service.GetTemplateService().GetColDefaultMapRuleById(c, req)
	controller.HandleResponse(c, resp, err, nil)
}

// ListColDefaultMapRules list column default value rules by template id interface
// @Summary list column default value rules by template id
// @Description list column default value rules by template id
// @Tags rule
// @Accept application/json
// @Produce application/json
// @Param page body message.ListColDefaultMapRulesReq true "list column default value rules by template id request"
// @Success 200 {object} message.ResultWithPage{data=message.ListColDefaultMapRulesResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /rule/default-value/list [post]
func ListColDefaultMapRules(c *gin.Context) {
	req := &message.ListColDefaultMapRulesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListColDefaultMapRules controller receive request: %s", requestString)
		resp, page, err := service.GetTemplateService().ListColDefaultMapRulesByTemplateId(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}
