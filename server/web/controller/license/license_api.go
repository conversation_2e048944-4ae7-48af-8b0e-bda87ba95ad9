package license

import (
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/versioninfo"
	"github.com/gin-gonic/gin"
)

// GetLicenseInfo get license expired time interface
// @Summary get license expired time
// @Description get license expired time
// @Tags license
// @Accept application/json
// @Produce application/json
// @Success 200 {object} message.CommonResult{data=message.GetLicenseInfoResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /license [get]
func GetLicenseInfo(c *gin.Context) {
	log.Infof("GetLicenseInfo controller receive request")
	expiredTime := license.GetExpiredTime()
	delta := expiredTime - time.Now().Unix()
	var remainingDays int64
	if delta <= 0 {
		license.SetExpired(true)
		remainingDays = 0
	} else {
		remainingDays = delta / (24 * 60 * 60)
		if delta%(24*60*60) > 0 {
			remainingDays++
		}
	}
	controller.HandleResponse(c, &message.GetLicenseInfoResp{ExpiredTime: license.GetExpiredTime(), RemainingDays: remainingDays}, nil, nil)
}

// Genkey get key
// @Summary get key
// @Description get key
// @Tags license
// @Accept application/json
// @Produce application/json
// @Success 200 {object} message.CommonResult{data=message.GenKeyResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /genkey [get]
func Genkey(c *gin.Context) {
	log.Infof("GetLicenseInfo controller receive request")
	ls := license.NewLicenseService()
	key, err := ls.GenerateKey()
	controller.HandleResponse(c, &message.GenKeyResp{TimsKey: key}, err, nil)
}

// GetVersion Get Version
// @Summary Get Version
// @Description Get Version
// @Tags license
// @Accept application/json
// @Produce application/json
// @Success 200 {object} message.CommonResult{data=message.VersionResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /version [get]
func GetVersion(c *gin.Context) {
	version := versioninfo.ReleaseVersion
	controller.HandleResponse(c, &message.VersionResp{Version: version}, nil, nil)
}

// ListLicenseFeatures list license features
// @Summary list license features
// @Description list license features
// @Tags license
// @Accept application/json
// @Produce application/json
// @Success 200 {object} message.CommonResult{data=message.ListLicenseFeaturesReq}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /license/features [get]
func ListLicenseFeatures(c *gin.Context) {
	log.Infof("ListLicenseFeatures controller receive request")
	req := &message.ListLicenseFeaturesReq{}
	if _, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		resp, err := service.GetSystemInfoService().ListLicenseFeatures(c)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateLicenseFeatures update license features
// @Summary update license features
// @Description update license features
// @Tags license
// @Accept application/json
// @Produce application/json
// @Success 200 {object} message.CommonResult{data=message.UpdateLicenseFeaturesReq}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /license/features [post]
func UpdateLicenseFeatures(c *gin.Context) {
	log.Infof("UpdateLicenseFeatures controller receive request")
	req := &message.UpdateLicenseFeaturesReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetSystemInfoService().UpdateLicenseFeatures(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
