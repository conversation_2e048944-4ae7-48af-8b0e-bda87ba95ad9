package migration

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// GetReverseOBJSummaryByTask query a reverse object task summary
// @Summary query a reverse object task summary
// @Description query a reverse object task summary
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureTaskSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/summary/{taskId}/ [get]
func GetReverseOBJSummaryByTask(c *gin.Context) {
	log.Infof("GetReverseOBJSummaryByTask controller receive request channelId: %s", c.Param("taskId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.MigrationTaskReq{TaskID: taskId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetStructMigrationService().GetReverseOBJSummaryByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJSummaryBySchema query a reverse object schema detail
// @Summary query a reverse object schema detail
// @Description query a reverse object schema detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureSchemaSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/summary/schema [post]
func GetReverseOBJSummaryBySchema(c *gin.Context) {
	req := &message.MigrationSchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseOBJSummaryBySchema controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetReverseOBJSummaryBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJDetailBySchema query a reverse schema object success detail
// @Summary query a schema reverse object success detail
// @Description query a schema reverse object success detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaPageReq true "get reverse success obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/byschema [post]
func GetReverseOBJDetailBySchema(c *gin.Context) {
	req := &message.MigrationSchemaPageReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseSuccessOBJDetailBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStructMigrationService().GetReverseOBJDetailBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetReverseSuccessOBJDetailBySchema query a reverse schema object success detail
// @Summary query a schema reverse object success detail
// @Description query a schema reverse object success detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaPageReq true "get reverse success obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/success [post]
func GetReverseSuccessOBJDetailBySchema(c *gin.Context) {
	req := &message.MigrationSchemaPageReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseSuccessOBJDetailBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStructMigrationService().GetReverseOBJSuccessDetailBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetReverseFailedOBJDetailBySchema query a reverse schema object failed detail
// @Summary query a schema reverse object failed detail
// @Description query a schema reverse object failed detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaPageReq true "get reverse failed obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/failed [post]
func GetReverseFailedOBJDetailBySchema(c *gin.Context) {
	req := &message.MigrationSchemaPageReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseFailedOBJDetailBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStructMigrationService().GetReverseOBJFailedDetailBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetReverseCompatibleOBJDetailBySchema query a reverse schema object compatible detail
// @Summary query a schema reverse object compatible detail
// @Description query a schema reverse object compatible detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaReq true "get reverse compatible obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/compatible [post]
func GetReverseCompatibleOBJDetailBySchema(c *gin.Context) {
	req := &message.MigrationSchemaPageReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseCompatibleOBJDetailBySchema controller receive request %s", requestString)
		resp, page, err := service.GetStructMigrationService().GetReverseOBJCompatibleDetailBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// RunReverseOBJDDLBySchema run a reverse schema object ddl
// @Summary run a reverse schema object ddl
// @Description run a reverse schema object ddl
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDDLReRunReq true "reverse obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLReRunResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/rerun [post]
func RunReverseOBJDDLBySchema(c *gin.Context) {
	req := &message.MigrationDDLReRunReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("RunReverseOBJDDLBySchema controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().ReRunReverseOBJDDLBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJDDLByTask query a task reverse object ddl detail
// @Summary query a task reverse object ddl detail
// @Description query a task reverse object ddl detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationTaskReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/task [post]
func GetReverseOBJDDLByTask(c *gin.Context) {
	req := &message.MigrationTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseOBJDDLByTask controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetReverseOBJDDLByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJDDLBySchema query a schema reverse object ddl detail
// @Summary query a schema reverse object ddl detail
// @Description query a schema reverse object ddl detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/schema [post]
func GetReverseOBJDDLBySchema(c *gin.Context) {
	req := &message.MigrationSchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseOBJDDLBySchema controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetReverseOBJDDLBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJProgressBar query a reverse object progress bar
// @Summary query a reverse object progress bar
// @Description query a reverse object progress bar
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationProgressReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/progressbar [post]
func GetReverseOBJProgressBar(c *gin.Context) {
	req := &message.MigrationProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseOBJProgressBar controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetReverseOBJProgressBarByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJProgressLog query a reverse object progress log
// @Summary query a reverse object progress log
// @Description query a reverse object progress log
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationProgressReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/progresslog [post]
func GetReverseOBJProgressLog(c *gin.Context) {
	req := &message.MigrationProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseOBJProgressLog controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetReverseOBJProgressLogByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

func mkdirIfNotExist(reportPath string) error {
	if reportPath != "" {
		_, err := os.Stat(reportPath)
		if err != nil {
			log.Infof("report path not exists, start create it. %v", reportPath)
			if mkdirErr := os.MkdirAll(reportPath, os.ModePerm); mkdirErr != nil {
				log.Errorf("create path failed. err:%s", mkdirErr)
				return mkdirErr
			}
		}
	}
	return nil
}

// DownloadReverseOBJDDLBySchema Download Reverse OBJ DDL By IncludingSchema interface
// @Summary Download Reverse OBJ DDL By IncludingSchema
// @Description Download Reverse OBJ DDL By IncludingSchema
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDDLDownloadReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /migration/reverse/ddl/download [post]
func DownloadReverseOBJDDLBySchema(c *gin.Context) {
	req := &message.MigrationDDLDownloadReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadReverseOBJDDLBySchema controller receive request %s", requestString)
		reportPath := config.GetGlobalConfig().DataDir
		mkdirIfNotExist(fmt.Sprintf("%s/migration/", reportPath))
		reportName := fmt.Sprintf("%s/migration/ddl_taskid%d_%s_%d.sql", reportPath, req.TaskID, req.SchemaNameS, time.Now().Unix())
		err := service.GetStructMigrationService().DownloadReverseOBJDDLBySchema(c, req, reportName)
		if err != nil {
			log.Infof("DownloadReverseOBJDDLBySchema run failed, err:%s", err)
			controller.HandleResponse(c, &message.MigrationStructureDDLProgressResp{}, err, nil)
		}

		_, err = os.Stat(reportName)
		if err == nil {
			log.Infof("start to download sql file:%s", reportName)
			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Disposition", "attachment; filename="+reportName)
			c.Header("Content-Transfer-Encoding", "binary")
			// c.Header("Transfer-Encoding", "true")
			c.Header("Cache-Control", "no-cache")
			c.File(reportName)
		} else if errors.Is(err, os.ErrNotExist) {
			log.Infof("report file %s not exists, err:%s", reportName, err)
			controller.HandleResponse(c, &message.MigrationStructureDDLProgressResp{}, err, nil)
		}
	}
}

// DownloadFailedReverseOBJDDLBySchema Download Reverse OBJ DDL By IncludingSchema interface
// @Summary Download Reverse OBJ DDL By IncludingSchema
// @Description Download Reverse OBJ DDL By IncludingSchema
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDDLDownloadReq true "get reverse ddl obj request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationStructureDDLProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /migration/reverse/ddl/download [post]
func DownloadFailedReverseOBJDDLBySchema(c *gin.Context) {
	req := &message.MigrationDDLDownloadReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DownloadReverseOBJDDLBySchema controller receive request %s", requestString)
		reportPath := config.GetGlobalConfig().DataDir
		mkdirIfNotExist(fmt.Sprintf("%s/migration/", reportPath))
		reportName := fmt.Sprintf("%s/migration/failed_ddl_taskid%d_%s_%d.sql", reportPath, req.TaskID, req.SchemaNameS, time.Now().Unix())
		err := service.GetStructMigrationService().DownloadReverseOBJDDLBySchemaAndStatus(c, req, constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED, reportName)
		if err != nil {
			log.Infof("DownloadReverseOBJDDLBySchema run failed, err:%s", err)
			controller.HandleResponse(c, &message.MigrationStructureDDLProgressResp{}, err, nil)
		}

		_, err = os.Stat(reportName)
		if err == nil {
			log.Infof("start to download sql file:%s", reportName)
			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Disposition", "attachment; filename="+reportName)
			c.Header("Content-Transfer-Encoding", "binary")
			// c.Header("Transfer-Encoding", "true")
			c.Header("Cache-Control", "no-cache")
			c.File(reportName)
		} else if errors.Is(err, os.ErrNotExist) {
			log.Infof("report file %s not exists, err:%s", reportName, err)
			controller.HandleResponse(c, &message.MigrationStructureDDLProgressResp{}, err, nil)
		}
	}
}

// GetOriginDDL query a reverse schema object success detail
// @Summary query a schema reverse object success detail
// @Description query a schema reverse object success detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaPageReq true "get reverse success obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/originddl [post]
func GetOriginDDL(c *gin.Context) {
	req := &message.GetOriginDDLReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseSuccessOBJDetailBySchema controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetOriginDDL(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetReverseOBJDetailByTable query a reverse schema object success detail
// @Summary query a schema reverse object success detail
// @Description query a schema reverse object success detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaPageReq true "get reverse success obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/table [post]
func GetReverseOBJDetailByTable(c *gin.Context) {
	req := &message.MigrationSchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseSuccessOBJDetailBySchema controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetReverseOBJDetailByTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetOracleDDL query a reverse schema object success detail
// @Summary query a schema reverse object success detail
// @Description query a schema reverse object success detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationSchemaPageReq true "get reverse success obj request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationStructureTableDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/reverse/ddl/oracleddl [post]
func GetOracleDDL(c *gin.Context) {
	req := &message.GetOracleDDLReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetReverseSuccessOBJDetailBySchema controller receive request %s", requestString)
		resp, err := service.GetStructMigrationService().GetOracleDDL(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
