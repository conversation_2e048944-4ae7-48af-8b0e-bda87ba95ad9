package migration

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// GetCSVMigrationSummaryByTask query a full data migration task summary
// @Summary query a full data migration task summary
// @Description query a full data migration task summary
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataTaskSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/summary/{taskId}/ [get]
func GetCSVMigrationSummaryByTask(c *gin.Context) {
	log.Infof("GetCSVMigrationSummaryByTask controller receive request channelId: %s", c.<PERSON><PERSON>("taskId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.MigrationTaskReq{TaskID: taskId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetCSVMigrationService().GetCSVMigrationSummaryByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetCSVMigrationSummaryBySchema query a full data migration task schema summary
// @Summary query a full data migration task schema summary
// @Description query a full data migration task schema summary
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataSchemaReq true "get full data migration schema request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataSchemaSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/summary/schema [post]
func GetCSVMigrationSummaryBySchema(c *gin.Context) {
	req := &message.MigrationDataSchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCSVMigrationSummaryBySchema controller receive request %s", requestString)
		resp, err := service.GetCSVMigrationService().GetCSVMigrationSummaryBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetCSVMigrationDetailBySchema query a full data migration task schema detail
// @Summary query a full data migration task schema detail
// @Description query a full data migration task schema detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataSchemaReq true "get full data migration task schema detail request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationDataSchemaDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/detail/schema [post]
func GetCSVMigrationDetailBySchema(c *gin.Context) {
	req := &message.MigrationDataSchemaPageReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCSVMigrationDetailBySchema controller receive request %s", requestString)
		resp, page, err := service.GetCSVMigrationService().GetCSVMigrationDetailBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// BatchRetryCSVMigrationTaskTable batch retry a full data migration task by table
// @Summary batch retry a full data migration task by table
// @Description batch retry a full data migration task by table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataBatchRetryReq true "batch try a full data migration task by table request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/retry/batch-table [post]
func BatchRetryCSVMigrationTaskTable(c *gin.Context) {
	req := &message.MigrationDataBatchRetryReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchRetryCSVMigrationTaskTable controller receive request %s", requestString)
		validateErr := service.GetCSVMigrationService().ValidateMultiSummaryStatus(c, req.TaskID, req.TableID)
		if validateErr != nil {
			controller.HandleResponse(c, nil, validateErr, nil)
			return
		}
		go service.GetCSVMigrationService().BatchRetryCSVMigrationTaskTable(c, req)
		controller.HandleResponse(c, nil, nil, nil)
	}
}

// GetCSVMigrationFailedChunkByTable query a full data migration failed chunk in a table
// @Summary query a full data migration failed chunk in a table
// @Description query a full data migration failed chunk in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataChunkFailedAndWaitingReq true "get full data migration failed chunk request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataChunkFailedAndWaitingResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk/failed [post]
func GetCSVMigrationFailedChunkByTable(c *gin.Context) {
	req := &message.MigrationDataChunkFailedAndWaitingReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCSVMigrationFailedChunkByTable controller receive request %s", requestString)
		resp, err := service.GetCSVMigrationService().GetCSVMigrationFailedChunkByTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetCSVMigrationFailedChunkErrorDetailByTable query a full data migration failed chunk error detail in a table
// @Summary query a full data migration failed chunk error detail in a table
// @Description query a full data migration failed chunk error detail in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataChunkErrorReq true "get full data migration failed chunk error detail request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataChunkErrorResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk/error [post]
func GetCSVMigrationFailedChunkErrorDetailByTable(c *gin.Context) {
	req := &message.MigrationDataChunkErrorReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCSVMigrationFailedChunkErrorDetailByTable controller receive request %s", requestString)
		resp, err := service.GetCSVMigrationService().GetCSVMigrationFailedChunkErrorDetailByTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// RetryCSVMigrationTaskTable retry a csv data migration task some failed chunk in a table
// @Summary retry a csv data migration task some failed chunk in a table
// @Description retry a csv data migration task some failed chunk in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataFailedChunkBatchRetryReq true "try a csv data migration task some failed chunk by table request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/retry/table [post]
func RetryCSVMigrationTaskTable(c *gin.Context) {
	req := &message.MigrationDataFailedChunkBatchRetryReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("RetryCSVMigrationTaskTable controller receive request %s", requestString)
		validateErr := service.GetCSVMigrationService().ValidateSingleSummaryStatus(c, req.TaskID, req.SchemaNameS, req.TableNameS)
		if validateErr != nil {
			controller.HandleResponse(c, nil, validateErr, nil)
			return
		}
		go service.GetCSVMigrationService().RetryCSVMigrationTaskByTable(c, req)
		controller.HandleResponse(c, nil, nil, nil)
	}
}

// GetCSVMigrationProgressBarByTask query a full data migration task progress bar
// @Summary query a full data migration task progress bar
// @Description query a full data migration task progress bar
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationProgressReq true "get a full data migration task request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/progressbar [post]
func GetCSVMigrationProgressBarByTask(c *gin.Context) {
	req := &message.MigrationProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCSVMigrationProgressBarByTask controller receive request %s", requestString)
		resp, err := service.GetCSVMigrationService().GetCSVMigrationProgressBarByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetCSVMigrationProgressLogByTask query a full data migration task progress log
// @Summary query a full data migration task progress log
// @Description query a full data migration task progress log
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationProgressReq true "get a full data migration task request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/progresslog [post]
func GetCSVMigrationProgressLogByTask(c *gin.Context) {
	req := &message.MigrationProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCSVMigrationProgressLogByTask controller receive request %s", requestString)
		resp, err := service.GetCSVMigrationService().GetCSVMigrationProgressLogByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// WalkCSVDir walk csv dir for get all csv files
// @Summary walk csv dir for get all csv files
// @Description walk csv dir for get all csv files
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.WalkCSVDirResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk-meta/walk-dir [post]
func WalkCSVDir(c *gin.Context) {
	log.Infof("WalkCSVDir controller receive request")
	req := &message.WalkCSVDirReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetCSVMigrationService().WalkCSVDir(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// QueryMigrationDetails query migration details
// @Summary query migration details
// @Description query migration details
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.QueryMigrationDetailsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk-meta/migration-data/details [post]
func QueryMigrationDetails(c *gin.Context) {
	log.Infof("QueryMigrationDetails controller receive request")
	req := &message.QueryMigrationDetailsReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetCSVMigrationService().QueryMigrationDetails(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// UpdateMigrationDetail update migration details
// @Summary update migration details
// @Description update migration details
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.QueryMigrationDetailsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk-meta/migration-data/update-detail [patch]
func UpdateMigrationDetail(c *gin.Context) {
	log.Infof("UpdateMigrationDetail controller receive request")
	req := &message.UpdateMigrationDetailReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetCSVMigrationService().UpdateMigrationDetail(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// MigrationDataStatistic get migration data statistic
// @Summary get migration data statistic
// @Description get migration data statistic
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataStatisticResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk-meta/migration-data/statistic [post]
func MigrationDataStatistic(c *gin.Context) {
	log.Infof("MigrationDataStatistic controller receive request")
	req := &message.MigrationDataStatisticReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, page, err := service.GetCSVMigrationService().MigrationDataStatistic(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// SaveMigrationData save migration data by walk path
// @Summary save migration data by walk path
// @Description save migration data by walk path
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.SaveMigrationDataResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/csvdata/chunk-meta/save-migration-data [post]
func SaveMigrationData(c *gin.Context) {
	log.Infof("SaveMigrationData controller receive request")
	req := &message.SaveMigrationDataReq{}
	if _, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		resp, err := service.GetCSVMigrationService().SaveMigrationData(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
