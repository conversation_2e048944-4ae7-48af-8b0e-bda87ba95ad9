package migration

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// GetFullDataMigrationSummaryByTask query a full data migration task summary
// @Summary query a full data migration task summary
// @Description query a full data migration task summary
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param taskId path int true "taskId"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataTaskSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/summary/{taskId}/ [get]
func GetFullDataMigrationSummaryByTask(c *gin.Context) {
	log.Infof("GetFullDataMigrationSummaryByTask controller receive request channelId: %s", c.<PERSON><PERSON>("taskId"))
	taskId, _ := strconv.Atoi(c.Param("taskId"))
	req := &message.MigrationTaskReq{TaskID: taskId}
	if _, ok := controller.HandleJsonRequestWithBuiltReq(c, req); ok {
		resp, err := service.GetFullDataMigrationService().GetFullDataMigrationSummaryByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetFullDataMigrationSummaryBySchema query a full data migration task schema summary
// @Summary query a full data migration task schema summary
// @Description query a full data migration task schema summary
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataSchemaReq true "get full data migration schema request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataSchemaSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/summary/schema [post]
func GetFullDataMigrationSummaryBySchema(c *gin.Context) {
	req := &message.MigrationDataSchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFullDataMigrationSummaryBySchema controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().GetFullDataMigrationSummaryBySchema(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetFullDataMigrationDetailBySchema query a full data migration task schema detail
// @Summary query a full data migration task schema detail
// @Description query a full data migration task schema detail
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataSchemaReq true "get full data migration task schema detail request parameter"
// @Success 200 {object} message.ResultWithPage{data=message.MigrationDataSchemaDetailResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/detail/schema [post]
func GetFullDataMigrationDetailBySchema(c *gin.Context) {
	req := &message.MigrationDataSchemaPageReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFullDataMigrationDetailBySchema controller receive request %s", requestString)
		resp, page, err := service.GetFullDataMigrationService().GetFullDataMigrationDetailBySchema(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// BatchRetryFullDataMigrationByTable batch retry a full data migration task by table
// @Summary batch retry a full data migration task by table
// @Description batch retry a full data migration task by table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataBatchRetryReq true "batch try a full data migration task by table request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/retry/batch-table [post]
func BatchRetryFullDataMigrationByTable(c *gin.Context) {
	req := &message.MigrationDataBatchRetryReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchTryFullDataMigrationByTable controller receive request %s", requestString)

		validateErr := service.GetFullDataMigrationService().ValidateMultiSummaryStatus(c, req.TaskID, req.SchemaNameS, req.TableID)
		if validateErr != nil {
			controller.HandleResponse(c, nil, validateErr, nil)
			return
		}

		go service.GetFullDataMigrationService().BatchRetryFullDataMigrationByTableFailed(c, req)
		controller.HandleResponse(c, nil, nil, nil)
	}
}

// GetFullDataMigrationFailedChunkByTable query a full data migration failed chunk in a table
// @Summary query a full data migration failed chunk in a table
// @Description query a full data migration failed chunk in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataChunkFailedAndWaitingReq true "get full data migration failed chunk request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataChunkFailedAndWaitingResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk/failed [post]
func GetFullDataMigrationFailedChunkByTable(c *gin.Context) {
	req := &message.MigrationDataChunkFailedAndWaitingReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFullDataMigrationFailedChunkByTable controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().GetFullDataMigrationFailedChunkByTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetFullDataMigrationFailedChunkErrorDetailByTable query a full data migration failed chunk error detail in a table
// @Summary query a full data migration failed chunk error detail in a table
// @Description query a full data migration failed chunk error detail in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataChunkErrorReq true "get full data migration failed chunk error detail request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataChunkErrorResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk/error [post]
func GetFullDataMigrationFailedChunkErrorDetailByTable(c *gin.Context) {
	req := &message.MigrationDataChunkErrorReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFullDataMigrationFailedChunkErrorDetailByTable controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().GetFullDataMigrationFailedChunkErrorDetailByTable(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// RetryFullDataMigrationFailedChunkDetailByTable retry a full data migration task some failed chunk in a table
// @Summary retry a full data migration task some failed chunk in a table
// @Description retry a full data migration task some failed chunk in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataFailedChunkRetryReq true "try a full data migration task some failed chunk by table request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/retry/chunk [post]
func RetryFullDataMigrationFailedChunkDetailByTable(c *gin.Context) {
	req := &message.MigrationDataFailedChunkRetryReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("RetryFullDataMigrationFailedChunkDetailByTable controller receive request %s", requestString)

		validateErr := service.GetFullDataMigrationService().ValidateSingleSummaryStatus(c, req.TaskID, req.SchemaNameS, req.TableNameS)
		if validateErr != nil {
			controller.HandleResponse(c, nil, validateErr, nil)
			return
		}

		go service.GetFullDataMigrationService().RetryFullDataMigrationFailedChunkDetailByTable(c, req)
		controller.HandleResponse(c, nil, nil, nil)
	}
}

// BatchRetryFullDataMigrationFailedChunkDetailByTable retry a full data migration task some failed chunk in a table
// @Summary retry a full data migration task some failed chunk in a table
// @Description retry a full data migration task some failed chunk in a table
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationDataFailedChunkBatchRetryReq true "try a full data migration task some failed chunk by table request parameter"
// @Success 200 {object} message.CommonResult
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/retry/table [post]
func BatchRetryFullDataMigrationFailedChunkDetailByTable(c *gin.Context) {
	req := &message.MigrationDataFailedChunkBatchRetryReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("BatchRetryFullDataMigrationFailedChunkDetailByTable controller receive request %s", requestString)
		validateErr := service.GetFullDataMigrationService().ValidateSingleSummaryStatus(c, req.TaskID, req.SchemaNameS, req.TableNameS)
		if validateErr != nil {
			controller.HandleResponse(c, nil, validateErr, nil)
			return
		}

		go service.GetFullDataMigrationService().BatchRetryFullDataMigrationFailedChunkDetailByTable(c, req)
		controller.HandleResponse(c, nil, nil, nil)
	}
}

// GetFullDataMigrationProgressBarByTask query a full data migration task progress bar
// @Summary query a full data migration task progress bar
// @Description query a full data migration task progress bar
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationProgressReq true "get a full data migration task request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/progressbar [post]
func GetFullDataMigrationProgressBarByTask(c *gin.Context) {
	req := &message.MigrationProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFullDataMigrationProgressBarByTask controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().GetFullDataMigrationProgressBarByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetFullDataMigrationProgressLogByTask query a full data migration task progress log
// @Summary query a full data migration task progress log
// @Description query a full data migration task progress log
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.MigrationProgressReq true "get a full data migration task request parameter"
// @Success 200 {object} message.CommonResult{data=message.MigrationDataProgressResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/progresslog [post]
func GetFullDataMigrationProgressLogByTask(c *gin.Context) {
	req := &message.MigrationProgressReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetFullDataMigrationProgressLogByTask controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().GetFullDataMigrationProgressLogByTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// FetchChunkData fetch incorrect table data by schema name and table name
// @Summary fetch incorrect table data by schema name and table name
// @Description fetch incorrect table data by schema name and table name
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.FetchChunkDataReq true "fetch incorrect table data by schema name and table name"
// @Success 200 {object} message.CommonResult{data=message.FetchChunkDataResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk-data/fetch [post]
func FetchChunkData(c *gin.Context) {
	req := &message.FetchChunkDataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("FetchChunkData controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().FetchChunkData(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ReFetchChunkData fetch incorrect table data by schema name and table name
// @Summary fetch incorrect table data by schema name and table name
// @Description fetch incorrect table data by schema name and table name
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.FetchChunkDataReq true "fetch incorrect table data by schema name and table name"
// @Success 200 {object} message.CommonResult{data=message.FetchChunkDataResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk-data/refetch [post]
func ReFetchChunkData(c *gin.Context) {
	req := &message.FetchChunkDataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ReFetchChunkData controller receive request %s", requestString)
		// 调用新的ReFetchChunkData服务方法
		resp, err := service.GetFullDataMigrationService().ReFetchChunkData(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ReplayChunkData replay incorrect table data by schema name and table name
// @Summary replay incorrect table data by schema name and table name
// @Description replay incorrect table data by schema name and table name
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.ReplayChunkDataReq true "replay incorrect table data by schema name and table name"
// @Success 200 {object} message.CommonResult{data=message.ReplayChunkDataResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk-data/replay [post]
func ReplayChunkData(c *gin.Context) {
	req := &message.ReplayChunkDataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ReplayChunkData controller receive request %s", requestString)
		resp, err := service.GetFullDataMigrationService().ReplayChunkData(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListChunkData list incorrect table data by schema name and table name
// @Summary list incorrect table data by schema name and table name
// @Description list incorrect table data by schema name and table name
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.ListChunkDataReq true "list incorrect table data by schema name and table name"
// @Success 200 {object} message.CommonResult{data=message.ListChunkDataResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk-data/list [get]
func ListChunkData(c *gin.Context) {
	req := &message.ListChunkDataReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListChunkData controller receive query %s", requestString)
		resp, page, err := service.GetFullDataMigrationService().ListChunkData(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetChunkDataSummary get chunk data status, fetching or replaying or finished or waiting
// @Summary get chunk data status, fetching or replaying or finished or waiting
// @Description get chunk data status, fetching or replaying or finished or waiting
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.GetChunkDataSummaryReq true "list incorrect table data by schema name and table name"
// @Success 200 {object} message.CommonResult{data=message.GetChunkDataSummaryResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk-data/summary [get]
func GetChunkDataSummary(c *gin.Context) {
	req := &message.GetChunkDataSummaryReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetChunkDataSummary controller receive query %s", requestString)
		resp, err := service.GetFullDataMigrationService().GetChunkDataSummary(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// QueryChunkData list incorrect table data by schema name and table name
// @Summary list incorrect table data by schema name and table name
// @Description list incorrect table data by schema name and table name
// @Tags migration
// @Accept application/json
// @Produce application/json
// @Param objReq body message.ListChunkDataReq true "list incorrect table data by schema name and table name"
// @Success 200 {object} message.CommonResult{data=message.ListChunkDataResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Failure 404 {object} message.CommonResult
// @Router /migration/fulldata/chunk-data/query [post]
func QueryChunkData(c *gin.Context) {
	req := &message.QueryChunkDataReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListChunkData controller receive query %s", requestString)
		resp, page, err := service.GetFullDataMigrationService().QueryChunkData(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}
