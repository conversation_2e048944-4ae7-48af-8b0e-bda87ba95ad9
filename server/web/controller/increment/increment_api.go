package increment

import (
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// CheckDsgAdaptorInitialized check if the increment service is enabled
func CheckDsgAdaptorInitialized() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		//b, _ := io.ReadAll(c.Request.Body)
		//c.Request.Body = io.NopCloser(bytes.NewBuffer(b))
		err := service.GetIncrementService().CheckDSGAdaptor()
		if err != nil {
			controller.HandleResponse(ctx, nil, err, nil)
			ctx.Abort()
			return
		}
		//c.Request.Body = io.NopCloser(bytes.NewBuffer(b))
		ctx.Next()
	}
}

// RefreshTokenIfNecessary refresh token if necessary
func RefreshTokenIfNecessary() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		err := service.GetIncrementService().RefreshTokenIfNecessary()
		if err != nil {
			controller.HandleResponse(ctx, nil, err, nil)
			ctx.Abort()
			return
		}
		ctx.Next()
	}
}

// ListAllDsgTask list all dsg tasks
// @Summary list all dsg tasks
// @Description list all dsg tasks
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListAllDsgTaskReq true "list all dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.ListAllDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/tasks [get]
func ListAllDsgTask(c *gin.Context) {
	req := &message.ListAllDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListAllDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().ListAllDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// VerifyHost verify host interface
// @Summary verify host interface
// @Description verify host interface
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param page body message.VerifyHostReq true "verify host request"
// @Success 200 {object} message.CommonResult{data=message.VerifyHostResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/host/verify [get]
func VerifyHost(c *gin.Context) {
	req := &message.VerifyHostReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("Verify Host controller receive request %s", requestString)
		resp, err := service.GetIncrementService().VerifyHost(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDsgTask get a dsg tasks
// @Summary get a dsg tasks
// @Description get a dsg tasks
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskReq true "get a dsg tasks request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/detail [get]
func GetDsgTask(c *gin.Context) {
	req := &message.GetDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetInstallationInfo get installation info
// @Summary get installation info
// @Description get installation info
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetInstallationInfoReq true "get installation info request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetInstallationInfoResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/installation/info [get]
func GetInstallationInfo(c *gin.Context) {
	req := &message.GetInstallationInfoReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListAllDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetInstallationInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetSourceConfig get source config [vm]
// @Summary get source config [vm]
// @Description get source config [vm]
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetSourceConfigReq true "get source config info request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetSourceConfigResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/source [get]
func GetSourceConfig(c *gin.Context) {
	req := &message.GetSourceConfigReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetSourceConfig controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetSourceConfig(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTableMapping get dsg task table mapping
// @Summary get dsg task table mapping
// @Description get dsg task table mapping
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTableMappingReq true "get dsg task table mapping request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTableMappingResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/table/mapping [get]
func GetTableMapping(c *gin.Context) {
	req := &message.GetTableMappingReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetTableMapping controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetTableMapping(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// VerifyTableMapping verify dsg task table mapping with tms
// @Summary verify dsg task table mapping with tms
// @Description verify dsg task table mapping with tms
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.VerifyTableMappingReq true "verify dsg task table mapping with tms request parameter"
// @Success 200 {object} message.CommonResult{data=message.VerifyTableMappingResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/table/mapping/verify [get]
func VerifyTableMapping(c *gin.Context) {
	req := &message.VerifyTableMappingReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("VerifyTableMapping controller receive query %s", requestString)
		resp, err := service.GetIncrementService().VerifyTableMapping(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTableMapping update dsg task table mapping
// @Summary update dsg task table mapping
// @Description update dsg task table mapping
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.UpdateTableMappingReq true "update dsg task table mapping request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateTableMappingResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/table/mapping [patch]
func UpdateTableMapping(c *gin.Context) {
	req := &message.UpdateTableMappingReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTableMapping controller receive body %s", requestString)
		resp, err := service.GetIncrementService().UpdateTableMapping(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateSourceConfig update source config [vm]
// @Summary update source config [vm]
// @Description update source config [vm]
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.UpdateSourceConfigReq true "update source config info request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateSourceConfigResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/source [patch]
func UpdateSourceConfig(c *gin.Context) {
	req := &message.UpdateSourceConfigReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateSourceConfig controller receive body %s", requestString)
		resp, err := service.GetIncrementService().UpdateSourceConfig(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// UpdateTargetConfig update target config [yloader.ini]
// @Summary update target config [yloader.ini]
// @Description update target config [yloader.ini]
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.UpdateTargetConfigReq true "update target config info request parameter"
// @Success 200 {object} message.CommonResult{data=message.UpdateTargetConfigResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/target [patch]
func UpdateTargetConfig(c *gin.Context) {
	req := &message.UpdateTargetConfigReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("UpdateTargetConfig controller receive body %s", requestString)
		resp, err := service.GetIncrementService().UpdateTargetConfig(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetTargetConfig get target config [yloader.ini]
// @Summary get target config [yloader.ini]
// @Description get target config [yloader.ini]
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTargetConfigReq true "get target config info request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetTargetConfigResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/config/target [get]
func GetTargetConfig(c *gin.Context) {
	req := &message.GetTargetConfigReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetTargetConfig controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetTargetConfig(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListPhysicalSubSystem list all physical sub system
// @Summary list all physical sub system
// @Description list all physical sub system
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListPhysicalSubSystemReq true "list physical sub system request parameter"
// @Success 200 {object} message.CommonResult{data=message.ListPhysicalSubSystemResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/config/physical-sub-systems [get]
func ListPhysicalSubSystem(c *gin.Context) {
	req := &message.ListPhysicalSubSystemReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("ListPhysicalSubSystem controller receive query %s", requestString)
		resp, err := service.GetIncrementService().ListPhysicalSubSystem(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetToken get token
// @Summary get token
// @Description get token
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetTokenReq true "get token"
// @Success 200 {object} message.CommonResult{data=message.GetTokenResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/token [get]
func GetToken(c *gin.Context) {
	req := &message.GetTokenReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.UserAgent = c.Request.Header.Get("User-Agent")
		req.AcceptLanguage = c.Request.Header.Get("Accept-Language")
		req.AcceptEncoding = c.Request.Header.Get("Accept-Encoding")
		req.AcceptCharset = c.Request.Header.Get("Accept-Charset")
		log.Infof("GetToken controller receive UserAgent:%s, AcceptLanguage:%s, AcceptEncoding:%s, AcceptCharset:%s, requestString:%s", req.UserAgent, req.AcceptLanguage, req.AcceptEncoding, req.AcceptCharset, requestString)
		resp, err := service.GetIncrementService().GetToken(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// StopDsgTask stop dsg task
// @Summary stop dsg task
// @Description stop dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.StopDsgTaskReq true "stop dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.StopDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/stop-binary [post]
func StopDsgTask(c *gin.Context) {
	req := &message.StopDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("StopDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().StopDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetInstallDsgTaskStatus get install dsg task status
// @Summary get install dsg task status
// @Description get install dsg task status
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetInstallDsgTaskStatusReq true "get install dsg task status request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetInstallDsgTaskStatusResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/install-status [get]
func GetInstallDsgTaskStatus(c *gin.Context) {
	req := &message.GetInstallDsgTaskStatusReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetInstallDsgTaskStatus controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetInstallDsgTaskStatus(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDsgTaskMonitorInfo get dsg task monitor info
// @Summary get dsg task monitor info
// @Description get dsg task monitor info
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskMonitorInfoReq true "get dsg task monitor info request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskMonitorInfoResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/monitor-info [get]
func GetDsgTaskMonitorInfo(c *gin.Context) {
	req := &message.GetDsgTaskMonitorInfoReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTaskMonitorInfo controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetDsgTaskMonitorInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDsgTaskOperationLogs get dsg task operation logs
// @Summary get dsg task operation logs
// @Description get dsg task operation logs
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskOperationLogsReq true "get dsg task operation logs request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskOperationLogsResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/logs [get]
func GetDsgTaskOperationLogs(c *gin.Context) {
	req := &message.GetDsgTaskOperationLogsReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTaskOperationLogs controller receive query %s", requestString)
		resp, page, err := service.GetIncrementService().GetDsgTaskOperationLogs(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetDsgTaskWarningInfo get dsg task warning info
// @Summary get dsg task warning info
// @Description get dsg task warning info
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskWarningInfoReq true "get dsg task warning info request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskWarningInfoResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/warning-info [get]
func GetDsgTaskWarningInfo(c *gin.Context) {
	req := &message.GetDsgTaskWarningInfoReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTaskWarningInfo controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetDsgTaskWarningInfo(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDsgTaskPerformanceStat get dsg task performance stat, including each table's performance
// @Summary get dsg task performance stat, including each table's performance
// @Description get dsg task performance stat, including each table's performance
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskPerformanceStatReq true "get dsg task performance stat request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskPerformanceStatResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/performance-stat [get]
func GetDsgTaskPerformanceStat(c *gin.Context) {
	req := &message.GetDsgTaskPerformanceStatReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTaskPerformanceStat controller receive query %s", requestString)
		resp, page, err := service.GetIncrementService().GetDsgTaskPerformanceStat(c, req)
		controller.HandleResponse(c, resp, err, page)
	}
}

// GetDsgTaskPerformanceStatDetail get dsg task performance stat, including each table's performance
// @Summary get dsg task performance stat, including each table's performance
// @Description get dsg task performance stat, including each table's performance
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskPerformanceStatDetailReq true "get dsg task performance stat request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskPerformanceStatDetailResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/performance-stat/detail [get]
func GetDsgTaskPerformanceStatDetail(c *gin.Context) {
	req := &message.GetDsgTaskPerformanceStatDetailReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTaskPerformanceStatDetail controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetDsgTaskPerformanceStatDetail(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetDsgTaskPerformanceStatTop get dsg task performance top stat, including each table's performance
// @Summary get dsg task performance top stat, including each table's performance
// @Description get dsg task performance top stat, including each table's performance
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetDsgTaskPerformanceStatTopReq true "get dsg task performance top stat request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetDsgTaskPerformanceStatTopResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/performance-stat/top [get]
func GetDsgTaskPerformanceStatTop(c *gin.Context) {
	req := &message.GetDsgTaskPerformanceStatTopReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetDsgTaskPerformanceStatTop controller receive query %s", requestString)
		resp, err := service.GetIncrementService().GetDsgTaskPerformanceStatTop(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// InstallDsgTask install dsg task
// @Summary install dsg task
// @Description install dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.InstallDsgTaskReq true "install dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.InstallDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/install-binary [post]
func InstallDsgTask(c *gin.Context) {
	req := &message.InstallDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("InstallDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().InstallDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// StartDsgTask start dsg task
// @Summary start dsg task
// @Description start dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.StartDsgTaskReq true "start dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.StartDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/start-binary [post]
func StartDsgTask(c *gin.Context) {
	req := &message.StartDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("StartDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().StartDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteDsgTask delete dsg task
// @Summary delete dsg task
// @Description delete dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.DeleteDsgTaskReq true "delete dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.DeleteDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/delete [delete]
func DeleteDsgTask(c *gin.Context) {
	req := &message.DeleteDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("DeleteDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().DeleteDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// StartSyncDsgTask sync dsg task
// @Summary sync dsg task
// @Description sync dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.StartSyncDsgTaskReq true "sync dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.StartSyncDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/stop-migrate [post]
func StartSyncDsgTask(c *gin.Context) {
	req := &message.StartSyncDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("StartSyncDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().StartSyncDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// PauseSyncDsgTask pause sync dsg task
// @Summary pause sync dsg task
// @Description pause sync dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.PauseSyncDsgTaskReq true "pause sync dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.PauseSyncDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/pause-migrate [post]
func PauseSyncDsgTask(c *gin.Context) {
	req := &message.PauseSyncDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("PauseSyncDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().PauseSyncDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ResumeSyncDsgTask resume sync dsg task
// @Summary resume sync dsg task
// @Description resume sync dsg task
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ResumeSyncDsgTaskReq true "resume sync dsg task request parameter"
// @Success 200 {object} message.CommonResult{data=message.PauseSyncDsgTaskResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/resume-migrate [post]
func ResumeSyncDsgTask(c *gin.Context) {
	req := &message.ResumeSyncDsgTaskReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ResumeSyncDsgTask controller receive query %s", requestString)
		resp, err := service.GetIncrementService().ResumeSyncDsgTask(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ClearCache clear dsg task cache
// @Summary clear dsg task cache
// @Description clear dsg task cache
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ClearCacheReq true "clear dsg task cache request parameter"
// @Success 200 {object} message.CommonResult{data=message.ClearCacheResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/task/operation/clear-cache [post]
func ClearCache(c *gin.Context) {
	req := &message.ClearCacheReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ClearCache controller receive query %s", requestString)
		resp, err := service.GetIncrementService().ClearCache(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListHost list datasource interface
// @Summary list  datasource
// @Description list  datasource
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param page body message.ListDataSourceHostReq true "list datasource host request"
// @Success 200 {object} message.ResultWithPage{data=[]message.OpMachineConf}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/host/list [get]
func ListHost(c *gin.Context) {
	req := &message.ListDataSourceHostReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("List Host controller receive request %s", requestString)
		resp, page, err := service.GetIncrementService().ListHost(c, req)
		if err != nil {
			controller.HandleResponse(c, nil, err, page)
		} else {
			controller.HandleResponse(c, resp.Hosts, err, page)
		}
	}
}

// GetHost get datasource host interface
// @Summary get datasource host interface
// @Description get datasource host interface
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Success 200 {object} message.CommonResult{data=message.OpMachineConf}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/host/{macId} [get]
func GetHost(c *gin.Context) {
	req := &message.GetHostReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		req.MacID, _ = strconv.Atoi(c.Param("macId"))
		log.Infof("List Host controller receive request %s", requestString)
		resp, err := service.GetIncrementService().GetHost(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetHostUsedPorts get host used ports
// @Summary get host used ports
// @Description get host used ports
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param page body message.GetHostUsedPortsReq true "get host used ports req"
// @Success 200 {object} message.CommonResult{data=message.GetHostUsedPortsResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/host/used-ports [get]
func GetHostUsedPorts(c *gin.Context) {
	req := &message.GetHostUsedPortsReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("GetHostUsedPorts controller receive request %s", requestString)
		resp, err := service.GetIncrementService().GetHostUsedPorts(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// SaveHost save datasource host interface
// @Summary save datasource host interface
// @Description save datasource host interface
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param page body message.SaveHostReq true "save datasource host request"
// @Success 200 {object} message.CommonResult{data=message.SaveHostResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/host/save [post]
func SaveHost(c *gin.Context) {
	req := &message.SaveHostReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Save Host controller receive request %s", requestString)
		resp, err := service.GetIncrementService().SaveHost(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DeleteHost delete datasource host interface
// @Summary delete datasource host interface
// @Description delete datasource host interface
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param page body message.DeleteHostReq true "delete datasource host request"
// @Success 200 {object} message.CommonResult{data=message.DeleteHostResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/host/delete [DELETE]
func DeleteHost(c *gin.Context) {
	req := &message.DeleteHostReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("Delete Host controller receive request %s", requestString)
		resp, err := service.GetIncrementService().DeleteHost(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// DownloadSupplementalLog download supplement log sql
// @Summary download supplement log sql
// @Description download supplement log sql
// @Tags increment
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DownloadSupplementalLogReq true "download supplement log sql parameter"
// @Success 200 {object} message.CommonResult{data=message.DownloadSupplementalLogResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /increment/dsg/config/supplemental-log [get]
func DownloadSupplementalLog(c *gin.Context) {
	req := &message.DownloadSupplementalLogReq{}
	if requestString, ok := controller.HandleJsonRequestFromQuery(c, req); ok {
		log.Infof("DownloadSupplementalLog controller receive request: %s", requestString)
		resp, err := service.GetIncrementService().DownloadSupplementalLog(c, req)
		if err != nil {
			log.Errorf("DownloadSupplementalLog controller failed, taskId:%d, err:%s", req.TaskId, err)
		}
		controller.HandleFileResponse(c, resp, err)
	}
}
