package mysql

import (
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// ListMembers list dm members
// @Summary list dm members
// @Description list dm members
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListMembersReq true "list dm members request parameter"
// @Success 200 {object} message.CommonResult{data=message.ListMembersResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/cli/list-members [post]
func ListMembers(c *gin.Context) {
	req := &message.ListMembersReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListMembers controller receive query %s", requestString)
		resp, err := service.GetMySQLService().ListMembers(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// ListSources list dm sources
// @Summary list dm sources
// @Description list dm sources
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.ListSourcesReq true "list dm sources request parameter"
// @Success 200 {object} message.CommonResult{data=message.ListSourcesResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/cli/list-sources [post]
func ListSources(c *gin.Context) {
	req := &message.ListSourcesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("ListSources controller receive query %s", requestString)
		resp, err := service.GetMySQLService().ListSources(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// PreviewSources preview selected source to dm sources
// @Summary preview selected source to dm sources
// @Description preview selected source to dm sources
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.PreviewSourcesReq true "preview selected source to dm sources request parameter"
// @Success 200 {object} message.CommonResult{data=message.PreviewSourcesResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/preview/sources [post]
func PreviewSources(c *gin.Context) {
	req := &message.PreviewSourcesReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("PreviewSources controller receive query %s", requestString)
		resp, err := service.GetMySQLService().PreviewSources(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// PreviewConfiguration preview selected source to dm configuration
// @Summary preview selected source to dm configuration
// @Description preview selected source to dm configuration
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.PreviewConfigurationReq true "preview selected source to dm configuration request parameter"
// @Success 200 {object} message.CommonResult{data=message.PreviewConfigurationResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/preview/configuration [post]
func PreviewConfiguration(c *gin.Context) {
	req := &message.PreviewConfigurationReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("PreviewConfiguration controller receive query %s", requestString)
		resp, err := service.GetMySQLService().PreviewConfiguration(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// PreviewAll preview all DM configurations in tree structure
// @Summary preview all DM configurations in tree structure
// @Description preview all DM configurations including sources and task configuration in a tree structure with YAML context
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.PreviewAllReq true "preview all configurations request parameter"
// @Success 200 {object} message.CommonResult{data=message.PreviewAllResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/preview/all-tree [post]
func PreviewAll(c *gin.Context) {
	req := &message.PreviewAllReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("PreviewAll controller receive query %s", requestString)
		resp, err := service.GetMySQLService().PreviewAll(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// TriggerCheck trigger comprehensive DM check
// @Summary trigger comprehensive DM check
// @Description trigger comprehensive check including table consistency, worker bindings, and source permissions
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.TriggerCheckReq true "trigger check request parameter"
// @Success 200 {object} message.CommonResult{data=message.TriggerCheckResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/check/trigger [post]
func TriggerCheck(c *gin.Context) {
	req := &message.TriggerCheckReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("TriggerCheck controller receive query %s", requestString)
		resp, err := service.GetMySQLService().TriggerCheck(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}

// GetCheckStatus get comprehensive DM check status and results
// @Summary get comprehensive DM check status and results
// @Description get check progress, results and fix suggestions by request ID
// @Tags mysql
// @Accept application/json
// @Produce application/json
// @Param objReq formData message.GetCheckStatusReq true "get check status request parameter"
// @Success 200 {object} message.CommonResult{data=message.GetCheckStatusResp} "success"
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /mysql/dm/check/status [post]
func GetCheckStatus(c *gin.Context) {
	req := &message.GetCheckStatusReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetCheckStatus controller receive query %s", requestString)
		resp, err := service.GetMySQLService().GetCheckStatus(c, req)
		controller.HandleResponse(c, resp, err, nil)
	}
}
