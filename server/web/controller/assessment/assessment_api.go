package assessment

import (
	"errors"
	"fmt"
	"os"

	"gitee.com/pingcap_enterprise/tms/server/message"
	assessmentSvc "gitee.com/pingcap_enterprise/tms/server/service/assessment"
	"gitee.com/pingcap_enterprise/tms/server/web/controller"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

// DownloadObjectAssessResultReportBySchema Download O2T object assess-task result report interface
// @Summary Download O2T object assess-task result report
// @Description Download O2T object assess-task result report
// @Tags assessment
// @Accept application/json
// @Produce application/json
// @Param objReq body message.DownloadObjectAssessResultReportBySchemaReq true "download assess schema report request parameter"
// @Success 200 {object} message.CommonResult{data=message.DownloadObjectAssessResultReportBySchemaResp}
// @Failure 500 {object} message.CommonResult
// @Failure 400 {object} message.CommonResult
// @Router /assessment/download [post]
func DownloadObjectAssessResultReportBySchema(c *gin.Context) {
	req := &message.DownloadObjectAssessResultReportBySchemaReq{}
	if requestString, ok := controller.HandleJsonRequestFromBody(c, req); ok {
		log.Infof("GetTidbStatisticsSummaryBySchema controller receive request %s", requestString)
		reportPath := config.GetGlobalConfig().DataDir
		reportName := fmt.Sprintf("%s/assessment/assessment_channel%d_task%d.zip", reportPath, req.ChannelId, req.TaskId)
		_, err := assessmentSvc.DownloadObjectAssessResultReportBySchema(c, req, config.GetGlobalConfig())
		if err != nil {
			log.Infof("run DownloadObjectAssessResultReportBySchema failed, err:%s", err)
		}
		// controller.HandleResponse(c, resp, err, nil)

		_, err = os.Stat(reportName)
		if err == nil {
			log.Infof("start to download report file:%s", reportName)

			c.Header("Content-Type", "application/octet-stream")
			c.Header("Content-Disposition", "attachment; filename="+reportName)
			c.Header("Content-Transfer-Encoding", "binary")
			// c.Header("Transfer-Encoding", "true")
			c.Header("Cache-Control", "no-cache")
			c.File(reportName)
		} else if errors.Is(err, os.ErrNotExist) {
			log.Infof("report file %s not exists, err:%s", reportName, err)

			controller.HandleResponse(c, &message.DownloadObjectAssessResultReportBySchemaResp{}, err, nil)
		}
	}
}
