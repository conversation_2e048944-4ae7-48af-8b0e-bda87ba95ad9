package route

import (
	assessmentApi "gitee.com/pingcap_enterprise/tms/server/web/controller/assessment"
	channelApi "gitee.com/pingcap_enterprise/tms/server/web/controller/channel"
	dataCompareApi "gitee.com/pingcap_enterprise/tms/server/web/controller/datacompare"
	datasourceApi "gitee.com/pingcap_enterprise/tms/server/web/controller/datasource"
	incrementApi "gitee.com/pingcap_enterprise/tms/server/web/controller/increment"
	licenseApi "gitee.com/pingcap_enterprise/tms/server/web/controller/license"
	migrationApi "gitee.com/pingcap_enterprise/tms/server/web/controller/migration"
	mysqlApi "gitee.com/pingcap_enterprise/tms/server/web/controller/mysql"
	objectParserApi "gitee.com/pingcap_enterprise/tms/server/web/controller/objectparser"
	sqlanalyzerApi "gitee.com/pingcap_enterprise/tms/server/web/controller/sqlanalyzer"
	statisticsApi "gitee.com/pingcap_enterprise/tms/server/web/controller/statistics"
	templateApi "gitee.com/pingcap_enterprise/tms/server/web/controller/template"
	userApi "gitee.com/pingcap_enterprise/tms/server/web/controller/user"
	"gitee.com/pingcap_enterprise/tms/server/web/interceptor"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

func Route(g *gin.Engine) {
	// support swagger
	swagger := g.Group("/swagger")
	{
		swagger.GET("/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	}

	// api
	apiV1 := g.Group("/api/v1")
	{
		apiV1.Use(interceptor.AccessLog(), gin.Recovery(), interceptor.CheckLicenseHandler())

		apiV1.GET("/hello", datasourceApi.Hello)

		user := apiV1.Group("/user")
		{
			user.POST("/login", userApi.Login)
			user.POST("/security_login", userApi.SecurityLogin)
			user.POST("/register", userApi.Register)
			user.GET("/currentUser", userApi.GetUserInfo)
			user.POST("/change_password", userApi.ChangePassword)
		}
		apiV1.GET("/license", licenseApi.GetLicenseInfo)
		apiV1.GET("/genkey", licenseApi.Genkey)
		apiV1.GET("/version", licenseApi.GetVersion)
		apiV1.GET("/license/features", licenseApi.ListLicenseFeatures)
		apiV1.POST("/license/features", licenseApi.UpdateLicenseFeatures)

		datasource := apiV1.Group("/datasource")
		{
			datasource.POST("/", datasourceApi.Create)
			datasource.POST("/delete", datasourceApi.BatchDelete)
			datasource.POST("/:dataSourceId/update", datasourceApi.Update)
			datasource.POST("/version", datasourceApi.Version)
			datasource.POST("/oracle/versionCode", datasourceApi.VersionCode)
			datasource.GET("/:dataSourceId", datasourceApi.Detail)
			datasource.GET("/:dataSourceId/test", datasourceApi.TestConnection)
			datasource.POST("/try", datasourceApi.TryConnection)
			datasource.POST("/list", datasourceApi.List)
			datasource.GET("/:dataSourceId/schemas", datasourceApi.GetDataSourceSchemas)
			datasource.POST("/:dataSourceId/schema/tables", datasourceApi.GetDataSourceSchemaTables)
			datasource.GET("/:dataSourceId/getPrivilegeCheckList", datasourceApi.GetPrivilegeCheckList)
			datasource.POST("/:dataSourceId/getTablePartitions", datasourceApi.GetTablePartitions)
			datasource.GET("/:dataSourceId/version", datasourceApi.VersionById)
			datasource.GET("/:dataSourceId/charset", datasourceApi.CharacterSetById)
		}
		template := apiV1.Group("/template")
		{
			//rule template
			template.POST("/", templateApi.CreateTemplateInfo)
			template.POST("/delete", templateApi.BatchDeleteTemplateInfos)
			template.POST("/:templateId/update", templateApi.UpdateTemplateInfo)
			template.GET("/:templateId", templateApi.DetailTemplateInfo)
			template.POST("/list", templateApi.ListTemplateInfosByType)

			//task param template
			template.POST("/task-param", templateApi.CreateTaskParamTemplate)
			template.POST("/task-param/update", templateApi.UpdateTaskParamTemplate)
			template.POST("/task-param/delete", templateApi.BatchDeleteTaskParamTemplates)
			template.POST("/task-param/list", templateApi.ListTaskParamTemplates)
			template.GET("/task-param/:taskparamTemplateId", templateApi.GetTaskParamTemplate)

			//template param detail
			template.POST("/param-detail", templateApi.CreateTemplateParamDetails)
			template.POST("/param-detail/update", templateApi.UpdateTemplateParamDetails)
			template.POST("/param-detail/delete", templateApi.BatchDeleteTemplateParamDetails)
			template.GET("/param-detail/:taskparamTemplateId/list", templateApi.ListTemplateParamDetails)

			//TabcolCustMapRules
			template.POST("/cust/create", templateApi.CreateTabcolCustMapRules)
			template.POST("/cust/update", templateApi.UpdateTabcolCustMapRules)
			template.POST("/cust/delete", templateApi.DeleteTabcolCustMapRules)
			template.POST("/cust/list/", templateApi.GetTabcolCustMapRules)
		}
		rule := apiV1.Group("/rule")
		{
			rule.POST("/table-column", templateApi.CreateTabColMapRule)
			rule.DELETE("/table-column/:templateId/:tabColMapRuleId", templateApi.DeleteTabColMapRuleById)
			rule.POST("/table-column/:tabColMapRuleId/update", templateApi.UpdateTabColMapRule)
			rule.GET("/table-column/:tabColMapRuleId", templateApi.GetTabColMapRuleById)
			rule.POST("/table-column/list", templateApi.ListTabColMapRules)

			rule.POST("/object/", templateApi.CreateObjMapRule)
			rule.DELETE("/object/:templateId/:objMapRuleId", templateApi.DeleteObjMapRuleById)
			rule.POST("/object/:objMapRuleId/update", templateApi.UpdateObjMapRule)
			rule.GET("/object/:objMapRuleId", templateApi.GetObjMapRuleById)
			rule.POST("/object/list", templateApi.ListObjMapRules)

			rule.POST("/sql/", templateApi.CreateSqlMapRule)
			rule.DELETE("/sql/:templateId/:sqlMapRuleId", templateApi.DeleteSqlMapRuleById)
			rule.POST("/sql/:sqlMapRuleId/update", templateApi.UpdateSqlMapRule)
			rule.GET("/sql/:sqlMapRuleId", templateApi.GetSqlMapRuleById)
			rule.POST("/sql/list", templateApi.ListSqlMapRules)

			rule.POST("/default-value/", templateApi.CreateColDefaultMapRule)
			rule.DELETE("/default-value/:templateId/:colDefaultMapRuleId", templateApi.DeleteColDefaultMapRuleById)
			rule.POST("/default-value/:colDefaultMapRuleId/update", templateApi.UpdateColDefaultMapRule)
			rule.GET("/default-value/:colDefaultMapRuleId", templateApi.GetColDefaultMapRuleById)
			rule.POST("/default-value/list", templateApi.ListColDefaultMapRules)
		}
		channel := apiV1.Group("/channel")
		{
			channel.POST("/download/template", channelApi.DownloadTemplate)
			channel.POST("/", channelApi.CreateChannel)
			channel.DELETE("/:channelId", channelApi.DeleteChannel)
			channel.POST("/:channelId/update", channelApi.UpdateChannel)
			channel.GET("/:channelId", channelApi.GetChannel)
			channel.GET("/:channelId/add-datasource/:datasourceId", channelApi.AddChannelDatasource)
			channel.DELETE("/:channelId/del-datasource/:datasourceId", channelApi.DeleteChannelDatasource)
			channel.GET("/:channelId/get-datasource-list/", channelApi.GetChannelDatasourceList)
			channel.POST("/list", channelApi.ListChannels)
			channel.POST("/:channelId/schemas", channelApi.CreateChannelSchemas)
			channel.GET("/:channelId/schemas", channelApi.GetChannelSchemas)
			channel.POST("/:channelId/schemas/update", channelApi.UpdateChannelSchemas)
			channel.POST("/:channelId/schemas/delete", channelApi.BatchDeleteChannelSchemas)
			channel.GET("/:channelId/schemas/datasource/:datasourceId", channelApi.GetUnSelectedChannelSchemas)
			channel.POST("/:channelId/schemas/datasource/:datasourceId", channelApi.SearchUnSelectedChannelSchemas)
			channel.GET("/:channelId/pre-check", channelApi.GetPreCheckInfosByChannelId)
			channel.POST("/:channelId/pre-check/update", channelApi.UpdatePreCheckInfo)
			channel.GET("/:channelId/pre-check/:preCheckId", channelApi.GetPreCheckInfoById)
			channel.POST("/:channelId/pre-check/execution", channelApi.ExecutionPreCheck)
			//task
			channel.POST("/:channelId/task/create-default", channelApi.CreateDefaultTasks)
			channel.POST("/:channelId/task/create-default-tasks-for-m2t", channelApi.CreateDefaultTasksForM2T)
			channel.POST("/:channelId/task/create-new", channelApi.CreateTask)
			channel.POST("/:channelId/task/create-bycsv", channelApi.CreateTaskByCSV)
			channel.POST("/:channelId/task/create-byref", channelApi.CreateTaskByRefTask)
			channel.DELETE("/:channelId/task/", channelApi.BatchDeleteTasks)
			channel.GET("/:channelId/task/list", channelApi.ListTasksByChannelId)
			channel.GET("/:channelId/task/get-one", channelApi.GetOneTaskByChannelId)
			channel.POST("/:channelId/task/:taskId", channelApi.UpdateTaskAndChannelSchemaObjectById)
			channel.POST("/:channelId/task/:taskId/config", channelApi.UpdateTaskCfgAndChannelSchemaObjectById)
			channel.GET("/:channelId/task/:taskId", channelApi.GetTaskInfoAndChannelSchemaObjectById)
			channel.POST("/:channelId/task/:taskId/schema-tables", channelApi.SubmitTaskSchemaTables)
			channel.POST("/:channelId/task/:taskId/update-schema-tables", channelApi.UpdateTaskSchemaTables)
			channel.GET("/:channelId/task/:taskId/add-all", channelApi.AddAllChannelSchemaTables)
			channel.GET("/:channelId/task/:taskId/del-all", channelApi.DeleteAllChannelSchemaTables)
			channel.GET("/:channelId/task/:taskId/to-upper", channelApi.ToUpperCaseTableNameT)
			channel.GET("/:channelId/task/:taskId/to-lower", channelApi.ToLowerCaseTableNameT)
			channel.GET("/:channelId/task/:taskId/to-upper-schema", channelApi.ToUpperCaseSchemaNameT)
			channel.GET("/:channelId/task/:taskId/to-lower-schema", channelApi.ToLowerCaseSchemaNameT)
			channel.POST("/:channelId/task/:taskId/modify-channel-schema-tables", channelApi.ModifyChannelSchemaTable)
			channel.POST("/:channelId/task/:taskId/partition", channelApi.ChangePartitionTypeT)
			channel.POST("/:channelId/task/:taskId/cluster", channelApi.ChangeClusterTypeT)
			channel.POST("/:channelId/task-type/:taskType/un-selected-tables", channelApi.GetUnSelectedTaskSchemaTables)
			channel.POST("/:channelId/task/:taskId/selected-tables", channelApi.GetSelectedTaskSchemaTables)
			channel.POST("/:channelId/task/:taskId/reference-tables", channelApi.ReferenceTables)
			channel.GET("/task/:taskType", channelApi.GetTaskparamTemplateListByTaskType)
			channel.GET("/:channelId/task/:taskId/execution", channelApi.TaskExecution)
			channel.POST("/:channelId/task/:taskId/execution-precheck", channelApi.TaskExecutionPreCheck)
			channel.GET("/:channelId/task/:taskId/detail-result", channelApi.GetTaskDetailResult)
			channel.POST("/:channelId/task/:taskId/progress", channelApi.GetTaskProgress)
			channel.POST("/:channelId/fix-task-status", channelApi.FixTaskStatus)
			channel.POST("/:channelId/split-task/verify", channelApi.VerifySplitTask)
			channel.POST("/:channelId/split-task", channelApi.SplitTask)
			channel.GET("/:channelId/task/:taskId/task-params/:taskparamTemplateId", channelApi.GetMergedTaskParamListByTaskparamTemplateId)
			channel.POST("/:channelId/task/:taskId/task-params/:taskparamTemplateId", channelApi.SaveTaskParams)
			channel.POST("/:channelId/task/:taskId/task-table/", channelApi.CreateTaskTableConfigs)
			channel.POST("/:channelId/task/:taskId/task-table/update", channelApi.UpdateTaskTableConfigs)
			channel.DELETE("/:channelId/task/:taskId/task-table", channelApi.DeleteTaskTableConfigs)
			channel.GET("/:channelId/task/:taskId/task-table/list", channelApi.GetTaskTableConfigsByTaskIdAndChannelId)
			channel.POST("/:channelId/column-names", channelApi.GetColumnNamesTiDB)
			channel.POST("/:channelId/column-names-ora", channelApi.GetColumnNamesOracle)
			channel.POST("/:channelId/upload", channelApi.Upload)
			channel.POST("/:channelId/:taskId/upload", channelApi.UploadForTaskTabCfg)
			channel.POST("/:channelId/:taskId/create-tasktabcfg-bycsv", channelApi.CreateTaskTableConfigByCSV)

			channel.POST("/:channelId/task/:taskId/source-table/columns/sync", channelApi.SyncSourceTableColumns)
			channel.POST("/:channelId/task/:taskId/source-table/columns", channelApi.BatchUpdateSourceTableColumns)
			channel.GET("/:channelId/task/:taskId/source-table/columns", channelApi.GetSourceTableColumns)
			channel.DELETE("/:channelId/task/:taskId/source-table/columns", channelApi.DeleteSourceTableColumns)
			channel.GET("/:channelId/task/:taskId/source-table/columns/sync/status", channelApi.GetSyncSourceTableColumnsStatus)

			channel.GET("/:channelId/source-table/columns/csv/export", channelApi.ExportSourceTableColumnsToCSV)
			channel.POST("/:channelId/source-table/columns/csv/upload", channelApi.UploadSourceTableColumnCSV)
			channel.POST("/:channelId/source-table/columns/csv/import", channelApi.ImportSourceTableColumnByCSV)
			channel.POST("/:channelId/task/:taskId/create_chunk/download", channelApi.DownloadCreateChunkSql)
			channel.POST("/:channelId/task/:taskId/drop_chunk/download", channelApi.DownloadDropChunkSql)

			channel.GET("/:channelId/performance/oracle/sessions-active", channelApi.GetActiveTmsSessions)
			channel.GET("/:channelId/performance/oracle/sessions-progress", channelApi.GetTmsSessionProgress)
			channel.GET("/:channelId/performance/oracle/sessions-ratio", channelApi.GetTmsSessionRatio)
			channel.GET("/:channelId/performance/oracle/io-file-stats", channelApi.GetPhysicalFileIOStats)
			channel.GET("/:channelId/performance/oracle/io-hot-files", channelApi.GetHotFileInformation)
			channel.GET("/:channelId/performance/oracle/tablespace-stats", channelApi.GetOracleTableSpaceStats)
			channel.GET("/:channelId/performance/oracle/object-stats", channelApi.GetOracleObjectStats)
			channel.GET("/:channelId/performance/oracle/meta/:reqType", channelApi.GetOracleMeta)
			channel.GET("/:channelId/performance/oracle/object-stats-by-schema", channelApi.GetOracleObjectStatsBySchema)
			channel.GET("/:channelId/performance/oracle/table-number-rows-by-schema", channelApi.GetOracleTableNumberRowsBySchema)

			channel.GET("/:channelId/task/:taskId/object-assess-overview", channelApi.GetObjectAssessOverview)
			channel.GET("/:channelId/task/:taskId/oracle/object-list", channelApi.GetOracleObjectList)
			channel.GET("/:channelId/task/:taskId/oracle/fetch-segment-sizes", channelApi.GetAndSaveOracleSegmentSizes)
			channel.GET("/:channelId/task/:taskId/oracle/query-segment-sizes", channelApi.QueryOracleSegmentSizes)
			channel.GET("/:channelId/task/:taskId/oracle/sum-segment-sizes", channelApi.QueryOracleSegmentSumSizes)

			channel.GET("/:channelId/performance/tidb/:reqType/:StartTime/:EndTime/:Step", channelApi.GetTiDBPerformance)
			channel.GET("/:channelId/performance/tidb/query-range/:reqType/", channelApi.GetTiDBPerformance)
			channel.GET("/:channelId/performance/tidb/query/:reqType/", channelApi.QueryTiDBMetaByPrometheus)

			channel.GET("/:channelId/baseinfo/tidb/query/:reqType/", channelApi.QueryTiDBBaseInfo)

			channel.POST("/:channelId/task/:taskId/object-parser-cfg/upload", channelApi.UploadForObjectParserCfg)
			channel.POST("/:channelId/task/:taskId/object-parser-cfg/create", channelApi.CreateObjectParserCfg)
			channel.POST("/:channelId/task/:taskId/object-parser-cfg/list", channelApi.ListObjectParserCfg)
		}
		dataCompare := apiV1.Group("/data-compare")
		{
			dataCompare.POST("/re-execution", dataCompareApi.DataCompareReExecution)
			dataCompare.POST("/summary/schema", dataCompareApi.GetDataCompareSummaryBySchema)
			dataCompare.POST("/detail-table/schema", dataCompareApi.GetDataCompareDetailTableBySchema)
			dataCompare.POST("/detail-chunk/table", dataCompareApi.GetDataCompareDetailChunkByTable)
			dataCompare.POST("/env-deploy/:taskId/execute", dataCompareApi.ExecuteDataCompareEnvDeployTask)
			dataCompare.GET("/env-deploy/:taskId", dataCompareApi.ListDataCompareEnvDeployTask)
			dataCompare.POST("/env-deploy/:taskId/:envDeployId", dataCompareApi.UpdateDataCompareEnvDeployTask)

			dataCompare.POST("/fix-sql/download", dataCompareApi.DownloadFixSQL)
			dataCompare.POST("/fix-sql/list", dataCompareApi.ListFixSQL)
			dataCompare.POST("/fix-sql/content", dataCompareApi.GetFixSQLContent)

		}
		assessment := apiV1.Group("/assessment")
		{
			assessment.GET("/channel/:channelId/task/:taskId/GetObjectAssessResultSummary", channelApi.GetObjectAssessResultSummary)
			assessment.GET("/channel/:channelId/task/:taskId/GetObjectAssessResultSummaryBySchema", channelApi.GetObjectAssessResultSummaryBySchema)
			assessment.GET("/channel/:channelId/task/:taskId/GetObjectAssessResultDetail/:schema", channelApi.GetObjectAssessResultDetail)
			assessment.GET("/channel/:channelId/task/:taskId/DownloadObjectAssessResultReport", channelApi.DownloadObjectAssessResultReport)
			assessment.POST("/download", assessmentApi.DownloadObjectAssessResultReportBySchema)
		}
		reverse := apiV1.Group("/migration/reverse")
		{
			reverse.GET("/summary/:taskId", migrationApi.GetReverseOBJSummaryByTask)
			reverse.POST("/summary/schema", migrationApi.GetReverseOBJSummaryBySchema)
			reverse.POST("/progressbar", migrationApi.GetReverseOBJProgressBar)
			reverse.POST("/progresslog", migrationApi.GetReverseOBJProgressLog)

			ddl := reverse.Group("/ddl")
			{
				ddl.POST("/byschema", migrationApi.GetReverseOBJDetailBySchema)
				ddl.POST("/success", migrationApi.GetReverseSuccessOBJDetailBySchema)
				ddl.POST("/failed", migrationApi.GetReverseFailedOBJDetailBySchema)
				ddl.POST("/compatible", migrationApi.GetReverseCompatibleOBJDetailBySchema)
				ddl.POST("/rerun", migrationApi.RunReverseOBJDDLBySchema)
				ddl.POST("/task", migrationApi.GetReverseOBJDDLByTask)
				ddl.POST("/schema", migrationApi.GetReverseOBJDDLBySchema)
				ddl.POST("/download", migrationApi.DownloadReverseOBJDDLBySchema)
				ddl.POST("/download/failed", migrationApi.DownloadFailedReverseOBJDDLBySchema)
				ddl.POST("/originddl", migrationApi.GetOriginDDL)
				ddl.POST("/table", migrationApi.GetReverseOBJDetailByTable)
				ddl.POST("/oracleddl", migrationApi.GetOracleDDL)
			}
		}
		migrationData := apiV1.Group("/migration")
		{
			fullData := migrationData.Group("/fulldata")
			{
				fullData.GET("/summary/:taskId", migrationApi.GetFullDataMigrationSummaryByTask)
				fullData.POST("/summary/schema", migrationApi.GetFullDataMigrationSummaryBySchema)
				fullData.POST("/detail/schema", migrationApi.GetFullDataMigrationDetailBySchema)
				fullData.POST("/progressbar", migrationApi.GetFullDataMigrationProgressBarByTask)
				fullData.POST("/progresslog", migrationApi.GetFullDataMigrationProgressLogByTask)

				fullData.POST("/retry/batch-table", migrationApi.BatchRetryFullDataMigrationByTable)
				fullData.POST("/chunk/failed", migrationApi.GetFullDataMigrationFailedChunkByTable)
				fullData.POST("/chunk/error", migrationApi.GetFullDataMigrationFailedChunkErrorDetailByTable)
				fullData.POST("/retry/chunk", migrationApi.RetryFullDataMigrationFailedChunkDetailByTable)
				fullData.POST("/retry/table", migrationApi.BatchRetryFullDataMigrationFailedChunkDetailByTable)

				fullData.GET("/chunk-data/summary", migrationApi.GetChunkDataSummary)
				fullData.POST("/chunk-data/fetch", migrationApi.FetchChunkData)
				fullData.POST("/chunk-data/refetch", migrationApi.ReFetchChunkData)
				fullData.GET("/chunk-data/list", migrationApi.ListChunkData)
				fullData.POST("/chunk-data/replay", migrationApi.ReplayChunkData)
				fullData.POST("/chunk-data/query", migrationApi.QueryChunkData)

			}
			csvData := migrationData.Group("/csvdata")
			{
				csvData.GET("/summary/:taskId", migrationApi.GetCSVMigrationSummaryByTask)
				csvData.POST("/summary/schema", migrationApi.GetCSVMigrationSummaryBySchema)
				csvData.POST("/detail/schema", migrationApi.GetCSVMigrationDetailBySchema)
				csvData.POST("/progressbar", migrationApi.GetCSVMigrationProgressBarByTask)
				csvData.POST("/progresslog", migrationApi.GetCSVMigrationProgressLogByTask)

				csvData.POST("/retry/table", migrationApi.RetryCSVMigrationTaskTable)
				csvData.POST("/retry/batch-table", migrationApi.BatchRetryCSVMigrationTaskTable)

				csvData.POST("/chunk-meta/migration-data/statistic", migrationApi.MigrationDataStatistic)
				csvData.POST("/chunk-meta/migration-data/details", migrationApi.QueryMigrationDetails)
				csvData.PATCH("/chunk-meta/migration-data/update-detail", migrationApi.UpdateMigrationDetail)
				csvData.POST("/chunk-meta/walk-dir", migrationApi.WalkCSVDir)
				csvData.POST("/chunk-meta/save-migration-data", migrationApi.SaveMigrationData)
			}
		}
		sqlAnalyzer := apiV1.Group("/sql-analyzer")
		{
			sqlAnalyzer.POST("/report/download", sqlanalyzerApi.DownloadSqlAnalyzerReport)
			sqlAnalyzer.POST("/report/downloadzip", sqlanalyzerApi.DownloadSqlAnalyzerReportZip)
			sqlAnalyzer.GET("/:channelId/:taskId/configuration", sqlanalyzerApi.GetConfiguration)
			sqlAnalyzer.POST("/:channelId/:taskId/start-sqlfile-replay", sqlanalyzerApi.StartSQLFileReplay)
			sqlAnalyzer.POST("/:channelId/:taskId/start-spa-collect", sqlanalyzerApi.StartSpaCollect)
			sqlAnalyzer.POST("/:channelId/:taskId/stop-spa-collect", sqlanalyzerApi.StopSpaCollect)
			sqlAnalyzer.POST("/:channelId/:taskId/call-spa-collect", sqlanalyzerApi.CallSpaCollect)
			sqlAnalyzer.GET("/analyze-task/:taskId", sqlanalyzerApi.ListSQLAnalyzerTask)
			sqlAnalyzer.POST("/analyze-task/:taskId/:sqlanalyzeId", sqlanalyzerApi.UpdateSQLAnalyzerTask)
			sqlAnalyzer.POST("/analyze-task/:taskId/execute", sqlanalyzerApi.ExecuteSQLAnalyzerEnvDeployTask)
			sqlAnalyzer.GET("/analyze-task/:taskId/jobs", sqlanalyzerApi.ListSQLAnalyzerTaskJobs)
			sqlAnalyzer.GET("/analyze-task/:taskId/task-deploy-status", sqlanalyzerApi.GetTaskDeployStatus)
			sqlAnalyzer.GET("/analyze-task/:taskId/sqlsets", sqlanalyzerApi.GetSQLSets)
			sqlAnalyzer.GET("/analyze-task/:taskId/sqlset/statements", sqlanalyzerApi.GetSQLSetStatements)
			sqlAnalyzer.GET("/analyze-task/:taskId/sqlset/statement-count", sqlanalyzerApi.GetSQLSetStatementCount)

			sqlAnalyzer.POST("/results/:taskId/paginated", sqlanalyzerApi.ListSqlResultsWithPagination)
			sqlAnalyzer.POST("/results/:taskId/user-operations", sqlanalyzerApi.UpdateSqlResultUserOperation)
			sqlAnalyzer.POST("/results/:taskId/statistics", sqlanalyzerApi.GetSqlResultStatistics)
			sqlAnalyzer.GET("/results/:taskId/history", sqlanalyzerApi.GetSqlResultHistory)
		}
		statistics := apiV1.Group("/tidb-statistics")
		{
			statistics.POST("/summary/schema", statisticsApi.GetTidbStatisticsSummaryBySchema)
			statistics.POST("/detail-table/init", statisticsApi.InitTidbStatistics)
			statistics.POST("/detail-table/update", statisticsApi.UpdateTidbStatsTable)
			statistics.POST("/detail-table/schema", statisticsApi.GetTidbStatisticsDetailTableBySchema)
			statistics.POST("/detail-table/conflict", statisticsApi.GetStatsTaskConflictTableBySchema)
			statistics.DELETE("/detail-table/delete", statisticsApi.DeleteStatsTaskConflictTables)
			statistics.POST("/detail-table/get-count", statisticsApi.GetTidbStatsTableCount)
			statistics.POST("/detail-table/channel-schema-table/schema", statisticsApi.GetTidbStatisticsAndCSTableBySchema)
		}
		increment := apiV1.Group("/increment")
		increment.Use(incrementApi.CheckDsgAdaptorInitialized()) // 检查DSG HTTP Client是否已经初始化完成
		increment.Use(incrementApi.RefreshTokenIfNecessary())    // 刷新Token
		{
			increment.GET("/dsg/host/:macId", incrementApi.GetHost)
			increment.GET("/dsg/host/list", incrementApi.ListHost)
			increment.POST("/dsg/host/save", incrementApi.SaveHost)
			increment.GET("/dsg/host/used-ports", incrementApi.GetHostUsedPorts)
			increment.DELETE("/dsg/host/delete", incrementApi.DeleteHost)
			increment.GET("/dsg/host/verify", incrementApi.VerifyHost)

			increment.GET("/dsg/tasks", incrementApi.ListAllDsgTask)
			increment.GET("/dsg/task/detail", incrementApi.GetDsgTask)
			increment.GET("/dsg/task/installation/info", incrementApi.GetInstallationInfo)

			increment.GET("/dsg/task/operation/logs", incrementApi.GetDsgTaskOperationLogs)
			increment.GET("/dsg/task/operation/monitor-info", incrementApi.GetDsgTaskMonitorInfo)
			increment.GET("/dsg/task/operation/warning-info", incrementApi.GetDsgTaskWarningInfo)
			increment.GET("/dsg/task/operation/performance-stat", incrementApi.GetDsgTaskPerformanceStat)
			increment.GET("/dsg/task/operation/performance-stat/detail", incrementApi.GetDsgTaskPerformanceStatDetail)
			increment.GET("/dsg/task/operation/performance-stat/top", incrementApi.GetDsgTaskPerformanceStatTop)
			increment.GET("/dsg/task/operation/install-status", incrementApi.GetInstallDsgTaskStatus)
			increment.POST("/dsg/task/operation/install-binary", incrementApi.InstallDsgTask)
			increment.POST("/dsg/task/operation/start-binary", incrementApi.StartDsgTask) // 启动
			increment.POST("/dsg/task/operation/stop-binary", incrementApi.StopDsgTask)
			increment.POST("/dsg/task/operation/start-migrate", incrementApi.StartSyncDsgTask)   // 开始同步
			increment.POST("/dsg/task/operation/stop-migrate", incrementApi.PauseSyncDsgTask)    // 停止同步
			increment.POST("/dsg/task/operation/pause-migrate", incrementApi.PauseSyncDsgTask)   // 暂停同步
			increment.POST("/dsg/task/operation/resume-migrate", incrementApi.ResumeSyncDsgTask) // 恢复同步
			increment.POST("/dsg/task/operation/clear-cache", incrementApi.ClearCache)
			increment.DELETE("/dsg/task/operation/delete", incrementApi.DeleteDsgTask)

			increment.GET("/dsg/task/config/source", incrementApi.GetSourceConfig)
			increment.GET("/dsg/task/config/target", incrementApi.GetTargetConfig)
			increment.PATCH("/dsg/task/config/source", incrementApi.UpdateSourceConfig)
			increment.PATCH("/dsg/task/config/target", incrementApi.UpdateTargetConfig)

			increment.GET("/dsg/task/config/table/mapping", incrementApi.GetTableMapping)
			increment.PATCH("/dsg/task/config/table/mapping", incrementApi.UpdateTableMapping)
			increment.GET("/dsg/task/config/table/mapping/verify", incrementApi.VerifyTableMapping)

			increment.GET("/dsg/config/physical-sub-systems", incrementApi.ListPhysicalSubSystem)
			increment.GET("/dsg/config/supplemental-log", incrementApi.DownloadSupplementalLog)
			increment.GET("/dsg/token", incrementApi.GetToken)
		}
		objectParser := apiV1.Group("/object-parser")
		objectParser.Use(objectParserApi.CheckAdaptorInitialized()) // 检查Client是否已经初始化完成
		{
			objectParser.POST("/metadata/dependency", objectParserApi.GetDependencyFromMetadata)
			objectParser.POST("/metadata/definitions", objectParserApi.GetDefinitionsFromMetadata)
			objectParser.POST("/metadata/analyze/details", objectParserApi.ListAnalyzeDetail)
			objectParser.POST("/metadata/plsql/json", objectParserApi.ParsePLSQLToAST)

			objectParser.POST("/incompatible-feature/basic", objectParserApi.ListBasicIncompatibleFeature)
			objectParser.POST("/incompatible-feature/basic/update", objectParserApi.UpdateBasicIncompatibleFeature)
			objectParser.POST("/incompatible-feature/task", objectParserApi.ListTaskIncompatibleFeature)
			objectParser.POST("/incompatible-feature/task/update", objectParserApi.UpdateTaskIncompatibleFeature)
			objectParser.POST("/incompatible-feature/scoring", objectParserApi.GetIncompatibleFeatureScoring)

			objectParser.POST("/histogram/archive-times", objectParserApi.GetArchiveTimes)                    // 归档次数
			objectParser.POST("/histogram/archive-data", objectParserApi.GetArchiveData)                      // 归档数据量
			objectParser.POST("/histogram/transaction-data-blocks", objectParserApi.GetTransactionDataBlocks) // 每个事务改变多少个数据块
			objectParser.POST("/histogram/transaction-log-volume", objectParserApi.GetTransactionLogVolume)   // 每事务日志量
			objectParser.POST("/histogram/log-volume-per-second", objectParserApi.GetLogVolumePerSecond)      // 每秒日志量
			objectParser.POST("/histogram/transaction-per-second", objectParserApi.GetTransactionPerSecond)   // 每秒事务量

			objectParser.POST("/ai/test-connect", objectParserApi.TestAIConnect)
			objectParser.POST("/ai/convert/plsql-to-java", objectParserApi.ConvertPLSQLToJava)
			objectParser.POST("/ai/convert/plsql-to-java/manual-convert", objectParserApi.ConvertPLSQLToJavaInManual)
			objectParser.POST("/ai/convert/plsql-to-java/results", objectParserApi.GetPLSQLToJavaResults)
			objectParser.POST("/ai/convert/plsql-to-java/history-results", objectParserApi.GetPLSQLToJavaHistoryResults)
			objectParser.POST("/ai/convert/plsql-to-java/logs", objectParserApi.GetPLSQLToJavaLogs)
			objectParser.POST("/ai/convert/plsql-to-java/summary", objectParserApi.GetPLSQLToJavaSummary)
			objectParser.POST("/ai/convert/plsql-to-java/download", objectParserApi.DownloadJavaCodes)
			objectParser.POST("/ai/convert/plsql-to-java/history-download", objectParserApi.DownloadHistoryJavaCodes)

			objectParser.POST("/ai/convert/plsql/process", objectParserApi.ConvertPLSQLToJava)
			objectParser.POST("/ai/convert/plsql/manual-process", objectParserApi.ConvertPLSQLToJavaInManual)
			objectParser.POST("/ai/convert/plsql/current-results", objectParserApi.GetPLSQLToJavaResults)
			objectParser.POST("/ai/convert/plsql/history-results", objectParserApi.GetPLSQLToJavaHistoryResults)
			objectParser.POST("/ai/convert/plsql/logs", objectParserApi.GetPLSQLToJavaLogs)
			objectParser.POST("/ai/convert/plsql/summary", objectParserApi.GetPLSQLToJavaSummary)
			objectParser.POST("/ai/convert/plsql/current-download", objectParserApi.DownloadJavaCodes)
			objectParser.POST("/ai/convert/plsql/history-download", objectParserApi.DownloadHistoryJavaCodes)

			objectParser.POST("/prompt/list", objectParserApi.ListPrompt)
			objectParser.POST("/prompt/save", objectParserApi.SavePrompt)
			objectParser.DELETE("/prompt/delete", objectParserApi.DeletePrompt)

			objectParser.POST("/prompt/relation/binding", objectParserApi.SaveTaskObjectPromptRelation)
			objectParser.DELETE("/prompt/relation/binding", objectParserApi.DeleteTaskObjectPromptRelation)
			objectParser.POST("/prompt/relation/list", objectParserApi.ListTaskObjectPromptRelation)

			objectParser.POST("/test/object-detail", objectParserApi.GetObjectDetail) // 旧的接口，实时读Oracle对象定义
		}
		mysql := apiV1.Group("/mysql")
		{
			mysql.POST("/dm/cli/list-members", mysqlApi.ListMembers)
			mysql.POST("/dm/cli/list-sources", mysqlApi.ListSources)
			mysql.POST("/dm/preview/sources", mysqlApi.PreviewSources)
			mysql.POST("/dm/preview/configuration", mysqlApi.PreviewConfiguration)
			mysql.POST("/dm/preview/all-tree", mysqlApi.PreviewAll)
			mysql.POST("/dm/check/trigger", mysqlApi.TriggerCheck)
			mysql.POST("/dm/check/status", mysqlApi.GetCheckStatus)
		}
	}
}
