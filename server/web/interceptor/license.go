package interceptor

import (
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gin-gonic/gin"
)

func CheckLicenseHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		if license.IsExpired() {
			log.Warn("license expired")
			if strings.Contains(c.FullPath(), "license") {
				return
			}
			if strings.Contains(c.FullPath(), "genkey") {
				return
			}
			if strings.Contains(c.FullPath(), "version") {
				return
			}
			c.AbortWithStatusJSON(errors.TIMS_LICENSE_EXPIRED.GetHttpCode(),
				&message.CommonResult{ResultMark: message.ResultMark{Code: int(errors.TIMS_LICENSE_EXPIRED), Message: errors.TIMS_LICENSE_EXPIRED.Explain()}, Data: struct{}{}})
			return
		}
		c.Next()
	}
}
