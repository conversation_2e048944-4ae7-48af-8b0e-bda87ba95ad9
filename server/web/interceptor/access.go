package interceptor

import (
	"fmt"
	"math"
	"net/http"
	"os"
	"time"

	"gitee.com/pingcap_enterprise/tms/util/log"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func AccessLog() gin.HandlerFunc {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		stop := time.Since(start)
		remoteIP := c.RemoteIP()
		latency := int(math.Ceil(float64(stop.Nanoseconds()) / 1000000.0))
		entry := log.GetRootLogger().ForkFile(constants.LogFileAccess).WithFields(
			logrus.Fields{
				"hostname":   hostname,
				"clientIP":   c.ClientIP(),
				"remoteIP":   remoteIP,
				"status":     c.Writer.Status(),
				"latency":    latency, // time to process
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"dataLength": c.<PERSON>.<PERSON>(),
				"referer":    c.Request.Referer(),
				"userAgent":  c.Request.UserAgent(),
			})

		if len(c.Errors) > 0 {
			entry.Error(c.Errors.ByType(gin.ErrorTypePrivate).String())
		} else {
			msg := fmt.Sprintf("%s - %s \"%s %s\" %d %d \"%s\" \"%s\" (%dms)",
				c.ClientIP(), hostname, c.Request.Method, c.Request.URL.Path, c.Writer.Status(), c.Writer.Size(),
				c.Request.Referer(), c.Request.UserAgent(), latency)
			if c.Writer.Status() >= http.StatusInternalServerError {
				entry.Error(msg)
			} else if c.Writer.Status() >= http.StatusBadRequest {
				entry.Warn(msg)
			} else {
				entry.Info(msg)
			}
		}
	}
}
