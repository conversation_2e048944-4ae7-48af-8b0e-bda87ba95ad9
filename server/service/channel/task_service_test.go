package channel

import (
	"reflect"
	"testing"

	"gitee.com/pingcap_enterprise/tms/server/models/channel"
)

func Test_fillBucketIndex(t *testing.T) {
	type args struct {
		splitNum int
		size     int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{
			name: "3-15",
			args: args{
				splitNum: 3,
				size:     15,
			},
			want: []int{
				0, 1, 2,
				2, 1, 0,
				0, 1, 2,
				2, 1, 0,
				0, 1, 2},
		},
		{
			name: "3-8",
			args: args{
				splitNum: 3,
				size:     8,
			},
			want: []int{
				0, 1, 2,
				2, 1, 0,
				0, 1},
		},
		{
			name: "4-17",
			args: args{
				splitNum: 4,
				size:     17,
			},
			want: []int{
				0, 1, 2, 3,
				3, 2, 1, 0,
				0, 1, 2, 3,
				3, 2, 1, 0,
				0},
		},
		{
			name: "4-20",
			args: args{
				splitNum: 4,
				size:     20,
			},
			want: []int{
				0, 1, 2, 3,
				3, 2, 1, 0,
				0, 1, 2, 3,
				3, 2, 1, 0,
				0, 1, 2, 3,
			},
		},
		{
			name: "5-18",
			args: args{
				splitNum: 5,
				size:     18,
			},
			want: []int{
				0, 1, 2, 3, 4,
				4, 3, 2, 1, 0,
				0, 1, 2, 3, 4,
				4, 3, 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := fillBucketIndex(tt.args.splitNum, tt.args.size); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("fillBucketIndex() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_splitTableIntoBucketInZOrder(t *testing.T) {
	type args struct {
		allTables []*channel.ChannelSchemaTable
		splitNum  int
	}
	tests := []struct {
		name  string
		args  args
		want  []int
		want1 map[int][]*channel.ChannelSchemaTable
		want2 map[int][]string
	}{
		{
			name: "",
			args: args{
				allTables: []*channel.ChannelSchemaTable{
					{TableNameS: "1", SchemaNameS: "1", TableSizeM: 1},
					{TableNameS: "5", SchemaNameS: "5", TableSizeM: 5},
					{TableNameS: "3", SchemaNameS: "3", TableSizeM: 3},
					{TableNameS: "2", SchemaNameS: "2", TableSizeM: 2},
					{TableNameS: "4", SchemaNameS: "4", TableSizeM: 4},
				},
				splitNum: 3,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := splitTableIntoBucketInZOrder(tt.args.allTables, tt.args.splitNum)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("splitTableIntoBucketInZOrder() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("splitTableIntoBucketInZOrder() got1 = %v, want %v", got1, tt.want1)
			}
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("splitTableIntoBucketInZOrder() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
