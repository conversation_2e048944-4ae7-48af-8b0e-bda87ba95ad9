package channel

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

const (
	OracleRolePrivsSql = `select count(1) from dba_sys_privs a where a.grantee=upper('%s') and a.privilege in(
'ALTER USER','CREATE JOB','CREATE ANY PROCEDURE','ADMINISTER SQL TUNING SET','CREATE LIBRARY',
'EXECUTE ANY PROCEDURE','SELECT ANY DICTIONARY','SELECT ANY TABLE','CREATE PROCEDURE',
'UNLIMITED TABLESPACE','ADMINISTER ANY SQL TUNING SET'
)`

	OracleSysPrivsListSql      = `SELECT GRANTEE,PRIVILEGE FROM dba_sys_privs WHERE GRANTEE = upper('%s')`
	OracleRolePrivsListSql     = `SELECT GRANTEE,GRANTED_ROLE AS PRIVILEGE FROM dba_role_privs WHERE GRANTEE = upper('%s')`
	OracleSysTablePrivsListSql = `SELECT a.GRANTEE,CONCAT(CONCAT(a.PRIVILEGE,' ON '),CASE WHEN a.TABLE_NAME='USER$' THEN CONCAT('SYS.',a.TABLE_NAME) ELSE a.TABLE_NAME END ) AS PRIVILEGE FROM dba_tab_privs a WHERE a.grantee = upper('%s') AND a.OWNER='SYS'`

	OracleSessionPrivsSql  = `select count(PRIVILEGE) from session_privs where PRIVILEGE in ('SELECT ANY DICTIONARY', 'SELECT ANY TABLE', 'CREATE SESSION')`
	OracleNotExistIndexSql = `select a.owner, a.table_name
								from dba_tables a
								where a.owner in (%s)
								  and a.table_name not in (
									select table_name
									from (
											 select b.owner, b.table_name, count(b.index_name)
											 from dba_indexes b
											 where b.owner in (%s)
											 group by b.owner, b.table_name
											 having count(b.index_name) >= 1
										 ) n
								  )
								  AND ROWNUM <= 10`

	OracleNoPKUKSql = `SELECT A.OWNER, A.TABLE_NAME
						FROM DBA_TABLES A
						WHERE NOT EXISTS(SELECT *
										 FROM DBA_CONSTRAINTS B
										 WHERE A.TABLE_NAME = B.TABLE_NAME
										   AND A.OWNER = B.OWNER
										   AND B.CONSTRAINT_TYPE = 'P'
										   AND B.OWNER IN (%s))
						  AND NOT EXISTS(SELECT *
										 FROM dba_indexes C
										 WHERE A.TABLE_NAME = C.TABLE_NAME
										   AND A.OWNER = C.OWNER
										   AND C.uniqueness = 'UNIQUE'
										   AND C.OWNER IN (%s))
						  AND A.OWNER IN (%s)
						  AND ROWNUM <= 10`

	OracleTableRelationSql = `SELECT A.OWNER, A.TABLE_NAME
								FROM DBA_TABLES A
								WHERE EXISTS(SELECT *
											 FROM DBA_CONSTRAINTS B
											 WHERE A.TABLE_NAME = B.TABLE_NAME
											   AND A.OWNER = B.OWNER
											   AND B.CONSTRAINT_TYPE = 'R'
											   AND B.OWNER IN (%s))
								  AND A.OWNER IN (%s)
								  AND ROWNUM <= 10`

	TidbPrivsSql = `SELECT User,Host,Select_priv,Insert_priv,Update_priv,Delete_priv,Create_priv,Drop_priv,Alter_priv,Create_view_priv,Index_priv 
	                FROM mysql.user
					where Select_priv='Y' 
					and Insert_priv='Y' 
					and Update_priv='Y' 
					and Delete_priv='Y' 
					and Create_priv='Y' 
					and Drop_priv='Y' 
					and Alter_priv='Y' 
					and Index_priv='Y' 
					and User='%s'`

	TidbPrivsListSql = `
SELECT User as GRANTEE,Host,'Select_priv' as PRIVILEGE, Select_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Insert_priv' as PRIVILEGE, Insert_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Update_priv' as PRIVILEGE, Update_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Delete_priv' as PRIVILEGE, Delete_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Create_priv' as PRIVILEGE, Create_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Drop_priv' as PRIVILEGE, Drop_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Alter_priv' as PRIVILEGE, Alter_priv as ispass
FROM mysql.user	
where User='%s'
union all
SELECT User as GRANTEE,Host,'Index_priv' as PRIVILEGE, Index_priv as ispass
FROM mysql.user	
where User='%s'`
)

func doPreCheck(ctx context.Context, channelInfo *channel.ChannelInformation, datasourceS *datasource.Datasource, datasourceT *datasource.Datasource, checks []*channel.PrecheckInfo) error {
	dbS, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(datasourceS, ""))
	if err != nil {
		log.Errorf("do pre-check failed,open oracle failed. channelId:%d, datasourceId:%d. err:%s", channelInfo.ChannelId, datasourceS.DatasourceId, err)
		return err
	}
	dbT, err := models.OpenMysql(datasourceT.UserName, datasourceT.PasswordValue, datasourceT.HostIp, datasourceT.HostPort, "")
	if err != nil {
		log.Errorf("do pre-check failed,open tidb failed. channelId:%d, datasourceId:%d. err:%s", channelInfo.ChannelId, datasourceT.DatasourceId, err)
		return err
	}
	schemaNamesS, schemaNamesT, err := getSchemaNames(ctx, channelInfo)
	if err != nil {
		return err
	}
	sqlSchemaNamesS := genSqlSchemaNames(schemaNamesS)
	sqlSchemaNameT := genSqlSchemaNames(schemaNamesT)
	baseSourcePrivilegeChecked := false
	baseSourcePrivilegeStatus := ""
	baseSourcePrivilegeResult := ""
	resultBuilder := &strings.Builder{}
	for _, check := range checks {
		if check.CheckObject == "A" || check.CheckObject == "B" || check.CheckObject == "C" || check.CheckObject == "D" {
			if !baseSourcePrivilegeChecked {
				baseSourcePrivilegeStatus = sourceBasePrivilegeCheck(ctx, dbS, datasourceS, resultBuilder)
				baseSourcePrivilegeChecked = true
				baseSourcePrivilegeResult = resultBuilder.String()
				resultBuilder.Reset()
			}
			check.CheckStatus = baseSourcePrivilegeStatus
			check.CheckResultInfo = baseSourcePrivilegeResult
			_, err = models.GetChannelReaderWriter().UpdatePreCheckInfo(ctx, check)
			if err != nil {
				return err
			}
			continue
		}
		if check.CheckObject == "E" {
			check.CheckStatus = targetPrivilegeCheck(ctx, dbT, datasourceT, sqlSchemaNameT, len(schemaNamesT), resultBuilder)
		}
		if check.CheckObject == "F" {
			check.CheckStatus = checkOracleTableWithNotIndex(ctx, dbS, sqlSchemaNamesS, resultBuilder)
		}
		if check.CheckObject == "G" {
			check.CheckStatus = checkOracleTableWithNotPKUK(ctx, dbS, sqlSchemaNamesS, resultBuilder)
		}
		if check.CheckObject == "H" {
			check.CheckStatus = checkOracleTableWithRelation(ctx, dbS, sqlSchemaNamesS, resultBuilder)
		}
		check.CheckResultInfo = resultBuilder.String()
		resultBuilder.Reset()
		_, err = models.GetChannelReaderWriter().UpdatePreCheckInfo(ctx, check)
		if err != nil {
			return err
		}
	}
	return nil
}

func sourceBasePrivilegeCheck(ctx context.Context, db *sql.DB, datasourceS *datasource.Datasource, resultBuilder *strings.Builder) string {
	//role privilege
	sql := fmt.Sprintf(OracleRolePrivsSql, datasourceS.UserName)
	status := constants.PRE_CHECK_STATUS_PASS
	count := 0
	err := db.QueryRowContext(ctx, sql).Scan(&count)
	if err != nil {
		log.Errorf("base privilege check failed.exec sql failed, sql:%s, err:%s", sql, err)
		resultBuilder.Reset()
		resultBuilder.WriteString(err.Error())
		return constants.PRE_CHECK_STATUS_NOT_PASS
	}
	if count < 11 {
		resultBuilder.WriteString("not exist privilege in('ALTER USER','CREATE JOB','CREATE ANY PROCEDURE','ADMINISTER SQL TUNING SET','CREATE LIBRARY','EXECUTE ANY PROCEDURE','SELECT ANY DICTIONARY','SELECT ANY TABLE','CREATE PROCEDURE','UNLIMITED TABLESPACE','ADMINISTER ANY SQL TUNING SET'),")
		status = constants.PRE_CHECK_STATUS_NOT_PASS
	}

	////session privilege
	//err = db.QueryRowContext(ctx, OracleSessionPrivsSql).Scan(&count)
	//if err != nil {
	//	log.Errorf("base privilege check failed.exec sql failed, sql:%s, err:%s", sql, err)
	//	resultBuilder.Reset()
	//	resultBuilder.WriteString(err.Error())
	//	return constants.PRE_CHECK_STATUS_NOT_PASS
	//}
	//if count < 3 {
	//	resultBuilder.WriteString("not exist 'SELECT ANY DICTIONARY' or 'SELECT ANY TABLE' or 'CREATE SESSION' privilege,")
	//	status = constants.PRE_CHECK_STATUS_NOT_PASS
	//}
	//if status == constants.PRE_CHECK_STATUS_PASS {
	//	resultBuilder.WriteString("pass")
	//}
	return status
}

func GetOraclePrivilegeCheckList(ctx context.Context, db *sql.DB, datasourceS *datasource.Datasource) ([]*structs.Privilege, error) {

	var privileges []*structs.Privilege
	var err error

	//sys privilege
	checkSysPrivilegeSQL := fmt.Sprintf(OracleSysPrivsListSql, datasourceS.UserName)
	log.Infof("GetOraclePrivilegeCheckList, query SQL: %s", checkSysPrivilegeSQL)
	privileges, err = getOraclePrivilegeCheckList(ctx, db, checkSysPrivilegeSQL)
	if err != nil {
		log.Errorf("get OracleSysPrivsListSql failed, sql:%s, err:%v", checkSysPrivilegeSQL, err)
		return nil, err
	}

	// 如果不开启增量同步，检查结束
	if !datasourceS.EnableIncrementSync {
		return privileges, nil
	}

	checkRolePrivilegeSQL := fmt.Sprintf(OracleRolePrivsListSql, datasourceS.UserName)
	log.Infof("GetOraclePrivilegeCheckList, query SQL: %s", checkRolePrivilegeSQL)
	rolePrivileges, err := getOraclePrivilegeCheckList(ctx, db, checkRolePrivilegeSQL)
	if err != nil {
		log.Errorf("get OracleRolePrivsListSql failed, sql:%s, err:%v", checkRolePrivilegeSQL, err)
		return nil, err
	}

	checkSysTablePrivilegeSQL := fmt.Sprintf(OracleSysTablePrivsListSql, datasourceS.UserName)
	log.Infof("GetOraclePrivilegeCheckList, query SQL: %s", checkSysTablePrivilegeSQL)
	sysTablePrivileges, err := getOraclePrivilegeCheckList(ctx, db, checkSysTablePrivilegeSQL)
	if err != nil {
		log.Errorf("get OracleRolePrivsListSql failed, sql:%s, err:%v", checkRolePrivilegeSQL, err)
		return nil, err
	}

	ps := append(privileges, rolePrivileges...)
	ps = append(ps, sysTablePrivileges...)

	return ps, nil
}

func getOraclePrivilegeCheckList(ctx context.Context, db *sql.DB, checkSQL string) ([]*structs.Privilege, error) {
	rows, err := db.QueryContext(ctx, checkSQL)
	if err != nil {
		log.Errorf("getOraclePrivilegeCheckList, invoke QueryContext sql:%s, err: %v", checkSQL, err)
		return nil, err
	}
	defer rows.Close()

	privileges := make([]*structs.Privilege, 0, 0)

	for rows.Next() {
		var grantee, privilege string
		if err := rows.Scan(&grantee, &privilege); err != nil {
			log.Error(err)
		}
		privilegeObj := &structs.Privilege{
			Grantee:   grantee,
			Privilege: privilege,
		}
		privileges = append(privileges, privilegeObj)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("base privilege check failed.exec sql failed, sql:%s, err:%s", checkSQL, err)
		return nil, err
	}
	return privileges, nil
}

func targetPrivilegeCheck(ctx context.Context, db *sql.DB, datasourceT *datasource.Datasource, sqlSchemaNames string, schemaCount int, resultBuilder *strings.Builder) string {
	sql := fmt.Sprintf(TidbPrivsSql, datasourceT.UserName)
	log.Infof("targetPrivilegeCheck sql:%v", sql)
	rows, err := db.QueryContext(ctx, sql)
	if err != nil {
		log.Errorf("targetPrivilegeCheck err:%v", err)
		resultBuilder.Reset()
		resultBuilder.WriteString(fmt.Sprintf("targetPrivilegeCheck err:%v", err))
		return constants.PRE_CHECK_STATUS_NOT_PASS
	}
	count := 0
	for rows.Next() {
		count++
	}
	if count > 0 {
		resultBuilder.WriteString("pass")
		return constants.PRE_CHECK_STATUS_PASS
	} else if count == 0 {
		resultBuilder.WriteString(fmt.Sprintf("%s have been checked.The schemas meet privilege requirements are none", sqlSchemaNames))
	}
	return constants.PRE_CHECK_STATUS_NOT_PASS
}

func GetTargetBasePrivilegeCheckList(ctx context.Context, db *sql.DB, datasourceT *datasource.Datasource) ([]*structs.Privilege, error) {
	//role privilege
	sql := fmt.Sprintf(TidbPrivsListSql, datasourceT.UserName, datasourceT.UserName, datasourceT.UserName, datasourceT.UserName, datasourceT.UserName, datasourceT.UserName, datasourceT.UserName, datasourceT.UserName)
	rows, err := db.QueryContext(ctx, sql)
	if err != nil {
		log.Errorf(" get TiDBRolePrivsListSql err: %v", err)
		return nil, err
	}
	defer rows.Close()

	privileges := make([]*structs.Privilege, 0, 0)

	for rows.Next() {
		var grantee, privilege, host, result string
		if err := rows.Scan(&grantee, &host, &privilege, &result); err != nil {
			log.Fatal(err)
		}
		privilegeObj := &structs.Privilege{
			Grantee:      grantee,
			PrivilegeVal: privilege,
			Host:         host,
			Result:       result,
		}
		privileges = append(privileges, privilegeObj)
	}

	if err := rows.Err(); err != nil {
		log.Errorf("base privilege check failed.exec sql failed, sql:%s, err:%s", sql, err)
		return nil, err
	}
	return privileges, nil
}

func checkOracleTableWithNotIndex(ctx context.Context, db *sql.DB, sqlSchemaNames string, resultBuilder *strings.Builder) string {
	sql := fmt.Sprintf(OracleNotExistIndexSql, sqlSchemaNames, sqlSchemaNames)
	rows, err := db.QueryContext(ctx, sql)
	count := 0
	for rows.Next() {
		var schema, table string
		err = rows.Scan(&schema, &table)
		if err != nil {
			log.Errorf("check oracle schema with not exist index tables failed. sql:%s, err: %s", sql, err)
			resultBuilder.Reset()
			resultBuilder.WriteString(fmt.Sprintf("check oracle schema with not exist index tables failed. err: %s", err))
			return constants.PRE_CHECK_STATUS_NOT_PASS
		}
		if count == 0 {
			resultBuilder.WriteString(fmt.Sprintf("tables have not index,they are %s.%s", schema, table))
		} else {
			resultBuilder.WriteString(fmt.Sprintf(",%s.%s", schema, table))
		}
		count++
	}
	if count > 0 {
		return constants.PRE_CHECK_STATUS_NOT_PASS
	}
	resultBuilder.WriteString("pass")
	return constants.PRE_CHECK_STATUS_PASS
}

func checkOracleTableWithNotPKUK(ctx context.Context, db *sql.DB, sqlSchemaNames string, resultBuilder *strings.Builder) string {
	sql := fmt.Sprintf(OracleNoPKUKSql, sqlSchemaNames, sqlSchemaNames, sqlSchemaNames)
	rows, err := db.QueryContext(ctx, sql)
	count := 0
	for rows.Next() {
		var schema, table string
		err = rows.Scan(&schema, &table)
		if err != nil {
			log.Errorf("check oracle schema with not exist pk or uk tables failed. sql:%s, err : %s", sql, err)
			resultBuilder.Reset()
			resultBuilder.WriteString(fmt.Sprintf("check oracle schema with not exist pk or uk tables failed. err : %s", err))
			return constants.PRE_CHECK_STATUS_NOT_PASS
		}
		if count == 0 {
			resultBuilder.WriteString(fmt.Sprintf("tables have not pk or uk,they are %s.%s", schema, table))
		} else {
			resultBuilder.WriteString(fmt.Sprintf(",%s.%s", schema, table))
		}
		count++
	}
	if count > 0 {
		return constants.PRE_CHECK_STATUS_NOT_PASS
	}
	resultBuilder.WriteString("pass")
	return constants.PRE_CHECK_STATUS_PASS
}

func checkOracleTableWithRelation(ctx context.Context, db *sql.DB, sqlSchemaNames string, resultBuilder *strings.Builder) string {
	sql := fmt.Sprintf(OracleTableRelationSql, sqlSchemaNames, sqlSchemaNames)
	rows, err := db.QueryContext(ctx, sql)
	count := 0
	for rows.Next() {
		var schema, table string
		err = rows.Scan(&schema, &table)
		if err != nil {
			log.Errorf("check oracle schema with relation tables failed. sql:%s, err : %s", sql, err)
			resultBuilder.Reset()
			resultBuilder.WriteString(fmt.Sprintf("check oracle schema with relation tables failed. err : %s", err))
			return constants.PRE_CHECK_STATUS_NOT_PASS
		}
		if count == 0 {
			resultBuilder.WriteString(fmt.Sprintf("tables have relation with each other,they are %s.%s", schema, table))
		} else {
			resultBuilder.WriteString(fmt.Sprintf(",%s.%s", schema, table))
		}
		count++
	}
	if count > 0 {
		return constants.PRE_CHECK_STATUS_NOT_PASS
	}
	resultBuilder.WriteString("pass")
	return constants.PRE_CHECK_STATUS_PASS
}

func genSqlSchemaNames(schemaNamesS []string) string {
	schemasBuilder := &strings.Builder{}
	for index, name := range schemaNamesS {
		if index > 0 {
			schemasBuilder.WriteString(",")
		}
		schemasBuilder.WriteString("'")
		schemasBuilder.WriteString(name)
		schemasBuilder.WriteString("'")
	}
	return schemasBuilder.String()
}

func getSchemaNames(ctx context.Context, channelInfo *channel.ChannelInformation) ([]string, []string, error) {
	schemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, channelInfo.ChannelId)
	if err != nil {
		log.Errorf("do pre-check failed, get channel schemas failed. channelId:%d, err:%v", channelInfo.ChannelId, err)
		return nil, nil, err
	}
	namesS := make([]string, 0, len(schemas))
	namesT := make([]string, 0, len(schemas))
	for _, schema := range schemas {
		namesS = append(namesS, schema.SchemaNameS)
		namesT = append(namesT, schema.SchemaNameT)
	}
	return namesS, namesT, nil
}

func existCheckingPreCheck(ctx context.Context, channelId int) (bool, error) {
	checking, err := models.GetChannelReaderWriter().GetCheckingPreCheckByChannelId(ctx, channelId)
	if err != nil {
		return false, err
	}
	if len(checking) > 0 {
		return true, nil
	}
	return false, nil
}
