package channel

import (
	"bufio"
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	sqlanalyzepkg "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/deploy"

	transdbconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/o2t"
	channelpkg "gitee.com/pingcap_enterprise/tms/pkg/channel"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	datacompare3 "gitee.com/pingcap_enterprise/tms/pkg/datacompare"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	incrementpkg "gitee.com/pingcap_enterprise/tms/pkg/increment"
	migrationpkg "gitee.com/pingcap_enterprise/tms/pkg/migration"
	objectparsercore "gitee.com/pingcap_enterprise/tms/pkg/objectparser/core"
	sqlanalyzepkgapi "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/api"
	"gitee.com/pingcap_enterprise/tms/pkg/structure"
	commonmodels "gitee.com/pingcap_enterprise/tms/server/models/common"
	datacompare2 "gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	sqlanalyzermodel "gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	fileutil "gitee.com/pingcap_enterprise/tms/util/file"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/coreos/go-semver/semver"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	compare "gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/sync_diff_inspector"
	"gitee.com/pingcap_enterprise/tms/server/service/assessment"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/server/service/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/service/migration"
	"gitee.com/pingcap_enterprise/tms/server/service/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/server/service/statistics"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	migmodels "gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/models/template"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"

	transferdbCommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
)

func (s *Service) CreateDefaultTasks(ctx context.Context, req *message.CreateDefaultTasksReq) (*message.CreateDefaultTasksResp, error) {
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("create default tasks failed. channelInfo does not exist, channelId:%d", req.ChannelId)
		return &message.CreateDefaultTasksResp{}, err
	}
	log.Debugf("CreateDefaultTasks GetChannel")

	channelInfo, updateChannelErr := models.GetChannelReaderWriter().UndeleteChannel(ctx, channelInfo)
	if updateChannelErr != nil {
		log.Errorf("UndeleteChannel failed. channelInfo:%v, DeletedAt:%v,  err:%v", channelInfo, channelInfo.DeletedAt, updateChannelErr)
		return &message.CreateDefaultTasksResp{}, updateChannelErr
	}

	taskInfos := make([]*task.Task, 0, 5)

	createTaskFunc := func() {
		defaultTemplates, getGefaultTemplatesErr := models.GetTemplateReaderWriter().GetAllDefaultTemplates(ctx)
		if getGefaultTemplatesErr != nil {
			log.Errorf("create default tasks failed. get default templates failed. err:%v", getGefaultTemplatesErr)
			return
		}
		log.Debugf("CreateDefaultTasks GetAllDefaultTemplates")

		taskSeq := 1
		defaultTime, _ := time.Parse(time.RFC3339, "1970-01-01T00:00:00+08:00")
		if channelInfo.ObjAssessment == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_OBJ_ASSESSMENT)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:object assessment, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_OBJ_ASSESSMENT, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_OBJ_ASSESSMENT), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID,
				OnlyIncompatibleDetail: constants.ONLY_INCOMPATIBLE_DETAIL_DEFAULT, //对象兼容性评估-明细显示
			}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		if channelInfo.MigrateStructure == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_MIGRATE_STRUCTURE)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:migrate structure, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_MIGRATE_STRUCTURE, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_MIGRATE_STRUCTURE), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		if channelInfo.MigrateFullData == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_MIGRATE_FULL_DATA)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:migrate full data, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfos = append(taskInfos, &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_MIGRATE_FULL_DATA, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_MIGRATE_FULL_DATA), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID})
			taskSeq++
		}
		if channelInfo.MigrateCsvData == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_MIGRATE_CSV_DATA)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:migrate csv data, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_MIGRATE_CSV_DATA, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_MIGRATE_CSV_DATA), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		if channelInfo.DataCompare == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_DATA_COMPARE)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:data compare, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfos = append(taskInfos, &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_DATA_COMPARE, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_DATA_COMPARE), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID})
			taskSeq++
		}
		if channelInfo.SqlAssessment == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_SQL_ASSESSMENT)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:sql assessment, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_SQL_ASSESSMENT, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_SQL_ASSESSMENT), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		if channelInfo.Increment == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_INCREMENT)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:increment, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_INCREMENT, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_INCREMENT), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		if channelInfo.ObjParser == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_OBJ_PARSER)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type: TASK_TYPE_OBJ_PARSER, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			log.Debugf("CreateDefaultTasks ObjParser GetDefaultTaskParamTemplateByTaskType")

			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_OBJ_PARSER, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_OBJ_PARSER), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID,
			}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		//create default task info
		taskInfos, err = models.GetTaskReaderWriter().BatchCreateTask(ctx, taskInfos)
		if err != nil {
			log.Errorf("create default tasks failed. data:%v, err:%s", taskInfos, err)
			return
		}
	}

	createTasksParamsFunc := func() {
		channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
		if getSchemaErr != nil {
			log.Errorf("create default tasks failed. get channel schemas failed, channelId:%d", req.ChannelId)
			return
		}
		log.Debugf("CreateDefaultTasks GetChannelSchemas")

		datasourceS, datasourceT, err := getChannelDataSource(ctx, channelInfo, channelSchemas)
		if err != nil {
			log.Errorf("create default tasks failed. getChannelDataSource failed, channelId:%d", req.ChannelId)
			return
		}

		log.Debugf("CreateDefaultTasks getSchemaTablesAndDataSource")

		err = models.Transaction(ctx, func(transactionCtx context.Context) error {

			// create sqlanalyze env deploy tasks after task got an task_id
			if channelInfo.SqlAssessment == constants.TASK_TYPE_CHECKED {
				tasks := lo.Filter(taskInfos, func(taskInfo *task.Task, _ int) bool {
					return taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT
				})
				if len(tasks) != 0 {
					taskInfo := tasks[0]
					envDeployTasks := buildSQLAnalyzeEnvDeployTasks(taskInfo)
					createErr := models.GetSqlAnalyzerReaderWriter().BatchCreateEnvDeployTask(transactionCtx, envDeployTasks)
					if createErr != nil {
						log.Errorf("create default sqlanalyze deploy tasks failed. data:%v, err:%s", envDeployTasks, createErr)
						return createErr
					}
					channelSchemaStrs := lo.Map(channelSchemas, func(channelSchema *channel.ChannelSchema, _ int) string {
						return channelSchema.SchemaNameS
					})
					if len(channelSchemaStrs) != 0 {
						_, saveErr := models.GetTaskReaderWriter().BatchSaveTaskSubParamConfigs(transactionCtx, buildSQLIncludingSchemaParam(taskInfo, lo.Uniq(channelSchemaStrs)))
						if saveErr != nil {
							log.Errorf("save sqlanalyzer task sub param config failed, taskId:%d, err:%v", taskInfo.TaskID, saveErr)
							return saveErr
						}
					}
				}
			}

			// create data compare env deploy tasks after task got an task_id
			if channelInfo.DataCompare == constants.TASK_TYPE_CHECKED {
				tasks := lo.Filter(taskInfos, func(taskInfo *task.Task, _ int) bool {
					return taskInfo.TaskType == constants.TASK_TYPE_DATA_COMPARE
				})
				if len(tasks) != 0 {
					taskInfo := tasks[0]
					envDeployTasks := buildDataCompareEnvDeployTasks(taskInfo)
					createErr := models.GetDataCompareReaderWriter().BatchCreateEnvDeployTask(transactionCtx, envDeployTasks)
					if createErr != nil {
						log.Errorf("create data compare env deploy tasks failed. data:%v, err:%s", envDeployTasks, createErr)
						return createErr
					}
				}
			}

			// create object parser incompatible features
			if channelInfo.ObjParser == constants.TASK_TYPE_CHECKED {
				tasks := lo.Filter(taskInfos, func(taskInfo *task.Task, _ int) bool {
					return taskInfo.TaskType == constants.TASK_TYPE_OBJ_PARSER
				})
				if len(tasks) != 0 {
					taskInfo := tasks[0]

					features, getErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(transactionCtx)
					if getErr != nil {
						log.Errorf("ListBasicOracleIncompatibleFeature failed. err:%v", getErr)
						return getErr
					}
					taskFeatures := lo.Map(features, func(feature *objectparser.OracleIncompatibleFeature, _ int) *objectparser.OracleTaskIncompatibleFeature {
						return &objectparser.OracleTaskIncompatibleFeature{
							TaskId:       taskInfo.TaskID,
							FeatureType:  feature.FeatureType,
							FeatureKey:   feature.FeatureKey,
							FeatureScore: feature.FeatureScore,
							FeatureDesc:  feature.FeatureDesc,
						}
					})
					createErr := models.GetObjectParserWriter().CreateTaskOracleIncompatibleFeatures(transactionCtx, taskFeatures)
					if createErr != nil {
						log.Errorf("create object parser incompatible features failed. data:%v, err:%s", taskFeatures, createErr)
						return createErr
					}
				}
			}

			schemaTables := make([]*structs.SchemaInfo, 0, len(channelSchemas))
			if channelInfo.MigrateStructure == constants.TASK_TYPE_CHECKED || channelInfo.MigrateFullData == constants.TASK_TYPE_CHECKED || channelInfo.MigrateCsvData == constants.TASK_TYPE_CHECKED || channelInfo.DataCompare == constants.TASK_TYPE_CHECKED || channelInfo.ObjParser == constants.TASK_TYPE_CHECKED || channelInfo.Increment == constants.TASK_TYPE_CHECKED {
				mSchemaTables, getTableErr := getChannelSchemaTables(ctx, channelInfo, channelSchemas, datasourceS)
				if getTableErr != nil {
					log.Errorf("create default tasks failed. getChannelSchemaTables failed, channelId:%d", req.ChannelId)
					return getTableErr
				}
				schemaTables = mSchemaTables
			}

			//save default task tables
			genTaskTablesErr := genTaskTables(transactionCtx, schemaTables, taskInfos, datasourceS, datasourceT)
			if genTaskTablesErr != nil {
				return genTaskTablesErr
			}

			channelInfo.TaskCreated = "Y"
			_, updateChannelErr = models.GetChannelReaderWriter().UpdateChannel(transactionCtx, channelInfo)
			if updateChannelErr != nil {
				log.Errorf("update task_created in channel info failed. channelInfo:%v, err:%v", channelInfo, updateChannelErr)
				return updateChannelErr
			}
			// create task default object
			channelObjects := make([]*channel.ChannelSchemaObject, 0, 2)
			for _, taskInfo := range taskInfos {
				if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
					channelObjects = append(channelObjects, &channel.ChannelSchemaObject{TaskId: taskInfo.TaskID, ChannelId: taskInfo.ChannelId, OnlyTableandindex: "Y", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "N"})
					continue
				}
				if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
					channelObjects = append(channelObjects, &channel.ChannelSchemaObject{TaskId: taskInfo.TaskID, ChannelId: taskInfo.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"})
				}
				if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
					channelObjects = append(channelObjects, &channel.ChannelSchemaObject{TaskId: taskInfo.TaskID, ChannelId: taskInfo.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"})
				}
			}
			if len(channelObjects) > 0 {
				_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObjects(transactionCtx, channelObjects)
				if saveChannelObjectsErr != nil {
					log.Errorf("create default channel schema objects failed. err:%v", saveChannelObjectsErr)
					return saveChannelObjectsErr
				}
			}
			return nil
		})
		if err != nil {
			log.Errorf("create default tasks failed. channelId:%d, err:%v", req.ChannelId, err)
		}
	}

	updateTaskStatusFunc := func() {
		taskIds := make([]int, 0, len(taskInfos))
		for _, t := range taskInfos {
			taskIds = append(taskIds, t.TaskID)
		}
		if len(taskIds) == 0 {
			return
		}
		err := models.GetTaskReaderWriter().BatchUpdateTaskStatusByIds(ctx, taskIds, constants.TASK_STATUS_NOT_RUNNING)
		if err != nil {
			log.Errorf("create default tasks failed. batch update task status failed, channelId:%d, err:%v", req.ChannelId, err)
			return
		}
	}

	go func() {
		createTaskFunc()
		createTasksParamsFunc()
		updateTaskStatusFunc()
	}()
	return &message.CreateDefaultTasksResp{}, nil
}

func buildSQLAnalyzeEnvDeployTasks(taskInfo *task.Task) []sqlanalyzermodel.EnvDeployTask {
	defaultDeployTasks := deploy.GetDefaultSQLAnalyzeDeployTasks()
	deployTasks := make([]sqlanalyzermodel.EnvDeployTask, 0, len(defaultDeployTasks))
	for _, item := range defaultDeployTasks {
		deployTasks = append(deployTasks, sqlanalyzermodel.EnvDeployTask{
			TaskNumber:  item.TaskNumber,
			TaskName:    item.TaskName,
			IsIgnore:    constants.SQLAnalyzeTaskNotIgnore,
			TaskStatus:  int(constants.SQLAnalyzeTaskStatusNotRunning),
			TaskSQL:     item.GetTaskSQL(),
			SQLModel:    item.SQLModel,
			TaskMode:    item.TaskMode.String(),
			TaskId:      taskInfo.TaskID, // fill task id
			LastRunTime: timeutil.GetTMSNullTime(),
		})
	}
	return deployTasks
}

func buildDataCompareEnvDeployTasks(taskInfo *task.Task) []datacompare2.EnvDeployTask {
	defaultDeployTasks := datacompare3.GetDefaultDataCompareDeployTasks()
	deployTasks := make([]datacompare2.EnvDeployTask, 0, len(defaultDeployTasks))
	for _, item := range defaultDeployTasks {
		deployTasks = append(deployTasks, datacompare2.EnvDeployTask{
			TaskNumber:  item.TaskNumber,
			TaskName:    item.TaskName,
			IsIgnore:    "N",
			TaskStatus:  int(constants.TASK_STATUS_NOT_RUNNING),
			TaskSQL:     item.GetTaskSQL(),
			SQLModel:    item.SQLModel,
			TaskId:      taskInfo.TaskID, // fill task id
			LastRunTime: timeutil.GetTMSNullTime(),
		})
	}
	return deployTasks
}

func GenTaskTables(cxt context.Context, schemaTables []*structs.SchemaInfo, taskInfos []*task.Task, datasourceS, datasourceT *datasource.Datasource) error {
	return genTaskTables(cxt, schemaTables, taskInfos, datasourceS, datasourceT)
}

func genTaskTables(cxt context.Context, schemaTables []*structs.SchemaInfo, taskInfos []*task.Task, datasourceS, datasourceT *datasource.Datasource) error {
	for _, taskInfo := range taskInfos {
		if taskInfo.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT || taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT || taskInfo.TaskType == constants.TASK_TYPE_OBJ_PARSER {
			continue
		}
		for _, schema := range schemaTables {
			result := make([]*channel.ChannelSchemaTable, 0, 1000)
			for _, table := range schema.ObjectTypes[0].Tables {
				result = append(result, &channel.ChannelSchemaTable{
					ChannelId:             taskInfo.ChannelId,
					TaskId:                taskInfo.TaskID,
					DbNameS:               datasourceS.DbName,
					SchemaNameS:           schema.SchemaName,
					TableNameS:            table.TableName,
					DbNameT:               datasourceT.DbName,
					SchemaNameT:           schema.SchemaNameT,
					TableNameT:            table.TableName,
					TaskType:              taskInfo.TaskType,
					PartitioningTypeS:     table.PartitioningType,
					PartitioningCountS:    table.PartitionCount,
					SubPartitioningTypeS:  table.SubPartitioningType,
					SubPartitioningCountS: table.SubPartitionCount,
					TableSizeM:            table.TableSizeM,
					UkS:                   table.Uk,
					PkS:                   table.Pk,
					PkT:                   table.Pk,
					PartitioningTypeT:     "N",
					ClusterTypeT:          "N",
				})
			}

			log.Debugf(" taskId:%d, channelId:%d, SchemaNameS:%s, length=%d", taskInfo.TaskID, taskInfo.ChannelId, schema.SchemaName, len(result))
			if len(result) > 0 {
				_, saveErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(cxt, result)
				if saveErr != nil {
					log.Errorf("save default channel task tables failed. taskId:%d, channelId:%d, SchemaNameS:%s, err:%v", taskInfo.TaskID, taskInfo.ChannelId, schema.SchemaName, saveErr)
					return saveErr
				}
			}
		}
	}
	return nil
}

func getSchemaTablesAndDataSource(ctx context.Context, channelInfo *channel.ChannelInformation, channelSchemas []*channel.ChannelSchema) ([]*structs.SchemaInfo, *datasource.Datasource, *datasource.Datasource, error) {
	schemasArray := make([]string, 0, len(channelSchemas))
	seen := make(map[string]struct{}) // 用于存储已添加的值

	for _, s := range channelSchemas {
		if _, exists := seen[s.SchemaNameS]; !exists {
			schemasArray = append(schemasArray, s.SchemaNameS)
			seen[s.SchemaNameS] = struct{}{} // 标记为已添加
		}
	}

	datasourceS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		return nil, nil, nil, err
	}
	datasourceT, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		return nil, nil, nil, err
	}
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if datasourceS.DbType == constants.DB_TYPE_ORACLE || datasourceS.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err = models.OpenOracle(models.BuildOracleConfigFromDatasource(datasourceS, ""))
		if err != nil {
			return nil, nil, nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleTables(ctx, db, schemasArray)
	} else if datasourceS.DbType == constants.DB_TYPE_MYSQL || datasourceS.DbType == constants.DB_TYPE_TIDB || datasourceS.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err = models.OpenMysql(datasourceS.UserName, datasourceS.PasswordValue, datasourceS.HostIp, datasourceS.HostPort, "")
		if err != nil {
			return nil, nil, nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlTables(ctx, db, schemasArray)
	} else {
		return nil, nil, nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", datasourceS.DbType)
	}
	if err != nil {
		log.Errorf("get tables from datasource failed. err:%v", err)
		return nil, nil, nil, err
	}
	for _, schema := range schemas {
		for _, channelSchema := range channelSchemas {
			if strings.EqualFold(schema.SchemaName, channelSchema.SchemaNameS) {
				schema.SchemaNameT = channelSchema.SchemaNameT
			}
		}
	}
	return schemas, datasourceS, datasourceT, nil
}

func getChannelDataSource(ctx context.Context, channelInfo *channel.ChannelInformation, channelSchemas []*channel.ChannelSchema) (*datasource.Datasource, *datasource.Datasource, error) {
	schemasArray := make([]string, 0, len(channelSchemas))
	seen := make(map[string]struct{}) // 用于存储已添加的值

	for _, s := range channelSchemas {
		if _, exists := seen[s.SchemaNameS]; !exists {
			schemasArray = append(schemasArray, s.SchemaNameS)
			seen[s.SchemaNameS] = struct{}{} // 标记为已添加
		}
	}

	datasourceS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		return nil, nil, err
	}
	datasourceT, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		return nil, nil, err
	}
	return datasourceS, datasourceT, nil
}

func GetChannelSchemaTables(ctx context.Context, channelInfo *channel.ChannelInformation, channelSchemas []*channel.ChannelSchema, datasourceS *datasource.Datasource) ([]*structs.SchemaInfo, error) {
	return getChannelSchemaTables(ctx, channelInfo, channelSchemas, datasourceS)
}

func getChannelSchemaTables(ctx context.Context, channelInfo *channel.ChannelInformation, channelSchemas []*channel.ChannelSchema, datasourceS *datasource.Datasource) ([]*structs.SchemaInfo, error) {
	schemasArray := make([]string, 0, len(channelSchemas))
	seen := make(map[string]struct{}) // 用于存储已添加的值

	for _, s := range channelSchemas {
		if _, exists := seen[s.SchemaNameS]; !exists {
			schemasArray = append(schemasArray, s.SchemaNameS)
			seen[s.SchemaNameS] = struct{}{} // 标记为已添加
		}
	}

	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if datasourceS.DbType == constants.DB_TYPE_ORACLE || datasourceS.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(datasourceS, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleTables(ctx, db, schemasArray)
	} else if datasourceS.DbType == constants.DB_TYPE_MYSQL || datasourceS.DbType == constants.DB_TYPE_TIDB || datasourceS.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err := models.OpenMysql(datasourceS.UserName, datasourceS.PasswordValue, datasourceS.HostIp, datasourceS.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlTables(ctx, db, schemasArray)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", datasourceS.DbType)
	}
	//if err != nil {
	//	log.Errorf("get tables from datasource failed. err:%v", err)
	//	return nil, err
	//}
	for _, schema := range schemas {
		for _, channelSchema := range channelSchemas {
			if strings.EqualFold(schema.SchemaName, channelSchema.SchemaNameS) {
				schema.SchemaNameT = channelSchema.SchemaNameT
			}
		}
	}
	return schemas, nil
}

func (s *Service) ListTasksByChannelId(ctx context.Context, req *message.ListTasksByChannelIdReq) (*message.ListTasksByChannelIdResp, error) {
	log.Infof("ListTasksByChannelId service request channelId: %d, parent taskId: %d, objectQuery: %s", req.ChannelId, req.ParentTaskId, req.ObjectQuery)

	var tasks []*task.Task
	var getTaskErr error

	// for single mode, parentTaskId == 0, that's the default value of this column
	tasks, getTaskErr = models.GetTaskReaderWriter().GetTasksByChannelAndTaskParentId(ctx, req.ChannelId, req.ParentTaskId)
	if getTaskErr != nil {
		log.Errorf("get tasks by channel id failed. channelId: %d, parent taskId: %d, err:%s", req.ChannelId, req.ParentTaskId, getTaskErr)
		return nil, getTaskErr
	}

	if req.ObjectQuery != "" {
		validTaskIds, getTaskIdErr := models.GetChannelReaderWriter().GetChannelTaskIdsByTaskIdAndTableNamePrefix(ctx, req.ChannelId, req.ObjectQuery)
		if getTaskIdErr != nil {
			log.Errorf("get channel task ids by task id and table name prefix failed. channelId: %d, objectQuery: %s, err:%s", req.ChannelId, req.ObjectQuery, getTaskIdErr)
			return nil, getTaskIdErr
		}
		tasks = lo.Filter(tasks, func(taskItem *task.Task, _ int) bool {
			return lo.Contains(validTaskIds, taskItem.TaskID)
		})
	}

	var mTasks []*message.Task
	if config.GetGlobalConfig().IsClusterMode() {
		mTasks = lo.Map(tasks, func(taskItem *task.Task, _ int) *message.Task {
			return buildTaskMessageFromModelInClusterMode(ctx, taskItem)
		})
	} else {
		mTasks = lo.Map(tasks, func(taskItem *task.Task, _ int) *message.Task {
			return buildTaskMessageFromModel(taskItem)
		})
	}
	return &message.ListTasksByChannelIdResp{Tasks: mTasks}, nil
}

func (s *Service) UpdateTaskAndChannelSchemaObjectById(ctx context.Context, req *message.UpdateTaskAndChannelSchemaObjectByIdReq) (*message.UpdateTaskAndChannelSchemaObjectByIdResp, error) {
	log.Infof("UpdateTaskAndChannelSchemaObjectById service request: task:%+v, schemaObject:%+v", req.Task, req.ChannelSchemaObject)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		_, saveTaskErr := models.GetTaskReaderWriter().UpdateTask(transactionCtx, buildTaskModelFromMessage(req.Task))
		if saveTaskErr != nil {
			return saveTaskErr
		}
		_, saveSchemaObjectErr := models.GetChannelReaderWriter().SaveChannelSchemaObject(transactionCtx, buildChannelSchemaObjectModelFromMessage(req.ChannelSchemaObject))
		if saveSchemaObjectErr != nil {
			return saveSchemaObjectErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("update task by id failed. task:%+v, schemaObject:%+v, err:%v", req.Task, req.ChannelSchemaObject, err)
	}
	return &message.UpdateTaskAndChannelSchemaObjectByIdResp{}, nil
}

func (s *Service) UpdateTaskCfgAndChannelSchemaObjectById(ctx context.Context, req *message.UpdateTaskAndChannelSchemaObjectByIdReq) (*message.UpdateTaskAndChannelSchemaObjectByIdResp, error) {
	log.Infof("UpdateTaskAndChannelSchemaObjectById service request: task:%+v, schemaObject:%+v", req.Task, req.ChannelSchemaObject)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		_, saveTaskErr := models.GetTaskReaderWriter().UpdateTaskCfg(transactionCtx, buildTaskModelFromMessage(req.Task))
		if saveTaskErr != nil {
			return saveTaskErr
		}
		_, saveSchemaObjectErr := models.GetChannelReaderWriter().SaveChannelSchemaObject(transactionCtx, buildChannelSchemaObjectModelFromMessage(req.ChannelSchemaObject))
		if saveSchemaObjectErr != nil {
			return saveSchemaObjectErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("update task by id failed. task:%+v, schemaObject:%+v, err:%v", req.Task, req.ChannelSchemaObject, err)
	}
	return &message.UpdateTaskAndChannelSchemaObjectByIdResp{}, nil
}

func (s *Service) GetTaskInfoAndChannelSchemaObjectById(ctx context.Context, req *message.GetTaskInfoAndChannelSchemaObjectByIdReq) (*message.GetTaskInfoAndChannelSchemaObjectByIdResp, error) {
	log.Infof("GetTaskInfoAndChannelSchemaObjectById service request: %v", req)
	task, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		return nil, getTaskErr
	}
	cso, getObjectErr := models.GetChannelReaderWriter().GetChannelSchemaObjectByTaskId(ctx, req.TaskId)
	if getObjectErr != nil {
		return nil, getObjectErr
	}
	return &message.GetTaskInfoAndChannelSchemaObjectByIdResp{Task: buildTaskMessageFromModel(task), ChannelSchemaObject: buildChannelSchemaObjectMessageFromModel(cso)}, nil
}

func (s *Service) GetTaskparamTemplateListByTaskType(ctx context.Context, req *message.GetTaskParamTemplateListByTaskTypeReq) (*message.GetTaskParamTemplateListByTaskTypeResp, error) {
	log.Infof("GetTaskparamTemplateList service request: %v", req)
	list, err := models.GetTaskReaderWriter().GetTaskparamTemplateListByTaskType(ctx, req.TaskType)
	if err != nil {
		log.Errorf("get taskparam tempalte list by taskType failed. err:%v", err)
		return nil, err
	}
	templateList := make([]*message.TaskParamTemplate, 0, len(list))
	for _, template := range list {
		templateList = append(templateList, BuildTaskParamTemplateMessageFromModel(template))
	}
	return &message.GetTaskParamTemplateListByTaskTypeResp{TaskParamTemplateList: templateList}, nil
}

func getParamValues(cc []*task.TaskSubParamConfig) []string {
	if cc == nil {
		return []string{}
	}
	arr := make([]string, 0)
	for _, c := range cc {
		if c.ParamValue == constants.FakeParamValue {
			continue
		}
		arr = append(arr, c.ParamValue)
	}
	return arr
}

func (s *Service) GetMergedTaskParamListByTaskparamTemplateId(ctx context.Context, req *message.GetMergedTaskParamListByTaskparamTemplateIdReq) (*message.GetMergedTaskParamListByTaskparamTemplateIdResp, error) {
	log.Infof("GetMergedTaskParamListByTaskparamTemplateId service request: %v", req)
	taskParams, getTaskPramsErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{ChannelID: req.ChannelId, TaskID: req.TaskId, TaskparamTemplateID: req.TaskparamTemplateId})
	if getTaskPramsErr != nil {
		log.Errorf("get task param list by TaskparamTemplateId and channelId, taskId failed, taskId: %d, err:%v", req.TaskId, getTaskPramsErr)
		return nil, getTaskPramsErr
	}
	templateParams, getParamDetailsErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, req.TaskparamTemplateId)
	if getParamDetailsErr != nil {
		log.Errorf("get template param detail list by TaskparamTemplateId failed, taskId: %d, err:%v", req.TaskId, getParamDetailsErr)
		return nil, getParamDetailsErr
	}

	templateSubParams, getSubParamsErr := models.GetTaskReaderWriter().BatchGetTaskSubParamConfigs(ctx, 0, 0)
	if getSubParamsErr != nil {
		log.Errorf("get template sub param list by TaskparamTemplateId  failed, err:%v", getSubParamsErr)
		return nil, getSubParamsErr
	}
	taskSubParams, getTaskSubParamsErr := models.GetTaskReaderWriter().BatchGetTaskSubParamConfigs(ctx, req.ChannelId, req.TaskId)
	if getTaskSubParamsErr != nil {
		log.Errorf("get task sub param list by TaskparamTemplateId and channelId, taskId  failed. err:%v", getTaskSubParamsErr)
		return nil, getTaskSubParamsErr
	}
	templateSubParamMapping := make(map[string][]*task.TaskSubParamConfig)
	taskSubParamMapping := make(map[string][]*task.TaskSubParamConfig)
	for _, subParam := range templateSubParams {
		if _, ok := templateSubParamMapping[subParam.ParamName]; !ok {
			templateSubParamMapping[subParam.ParamName] = make([]*task.TaskSubParamConfig, 0)
		}
		templateSubParamMapping[subParam.ParamName] = append(templateSubParamMapping[subParam.ParamName], subParam)
	}
	for _, subParam := range taskSubParams {
		if _, ok := taskSubParamMapping[subParam.ParamName]; !ok {
			taskSubParamMapping[subParam.ParamName] = make([]*task.TaskSubParamConfig, 0)
		}
		taskSubParamMapping[subParam.ParamName] = append(taskSubParamMapping[subParam.ParamName], subParam)
	}

	taskParamsMap := make(map[string]*task.TaskParamConfig)
	for _, taskParam := range taskParams {
		taskParamsMap[taskParam.ParamName] = taskParam
	}
	mergedParamList := make([]*message.TaskParam, 0, len(templateParams))

	for _, templateParam := range templateParams {
		if templateParam.HasSubParams {
			// 如果Task有自定义的子参数列表，使用Task的子参数列表
			// 如果Task的自定义子参数列表只有一个，并且是FAKE_PARAM_VALUE，则表示列表为空
			if taskSubParamValues, ok := taskSubParamMapping[templateParam.ParamName]; ok {
				taskSubParam := taskSubParamValues[0]
				mergedParamList = append(mergedParamList, &message.TaskParam{
					TaskParamID:           taskSubParam.TaskSubParamID,
					ChannelID:             taskSubParam.ChannelID,
					TaskID:                taskSubParam.TaskID,
					ParamName:             taskSubParam.ParamName,
					TaskparamTemplateID:   taskSubParam.TaskparamTemplateID,
					HasSubParams:          true,
					SubParamValuesCurrent: getParamValues(taskSubParamValues),
					SubParamValuesDefault: getParamValues(templateSubParamMapping[templateParam.ParamName]),
					BaseFields:            common.BuildBaseFieldsMessageFromModel(taskSubParam.Entity),
				})
				continue
			}
			// 如果Task没有自定义的子参数列表，使用模板的子参数列表
			if templateSubParamList, ok := templateSubParamMapping[templateParam.ParamName]; ok {
				templateSubParam := templateSubParamList[0]
				mergedParamList = append(mergedParamList, &message.TaskParam{
					TaskParamID:           templateSubParam.TaskSubParamID,
					ChannelID:             templateSubParam.ChannelID,
					TaskID:                templateSubParam.TaskID,
					TaskparamTemplateID:   templateSubParam.TaskparamTemplateID,
					ParamName:             templateSubParam.ParamName,
					HasSubParams:          true,
					SubParamValuesCurrent: getParamValues(templateSubParamList),
					SubParamValuesDefault: getParamValues(templateSubParamList),
					BaseFields:            common.BuildBaseFieldsMessageFromModel(templateParam.Entity),
				})
				continue
			}
			// 如果子参数列表本来就是空的，增加空字段，以供前端编辑
			mergedParamList = append(mergedParamList, &message.TaskParam{
				ChannelID:             req.ChannelId,
				TaskID:                req.TaskId,
				TaskparamTemplateID:   templateParam.TaskparamTemplateID,
				ParamName:             templateParam.ParamName,
				HasSubParams:          true,
				SubParamValuesCurrent: []string{},
				SubParamValuesDefault: []string{},
				BaseFields:            common.BuildBaseFieldsMessageFromModel(templateParam.Entity),
			})
			continue
		}
		if taskParam, ok := taskParamsMap[templateParam.ParamName]; ok {
			mergedParamList = append(mergedParamList, &message.TaskParam{
				TaskParamID:         taskParam.TaskParamID,
				ChannelID:           taskParam.ChannelID,
				TaskID:              taskParam.TaskID,
				ParamName:           taskParam.ParamName,
				ParamValueCurrent:   taskParam.ParamValueCurrent,
				ParamValueDefault:   templateParam.ParamValueDefault,
				TaskparamTemplateID: taskParam.TaskparamTemplateID,
				BaseFields:          common.BuildBaseFieldsMessageFromModel(taskParam.Entity),
			})
		} else {
			mergedParamList = append(mergedParamList, &message.TaskParam{
				ChannelID:           req.ChannelId,
				TaskID:              req.TaskId,
				ParamName:           templateParam.ParamName,
				ParamValueCurrent:   templateParam.ParamValueDefault,
				ParamValueDefault:   templateParam.ParamValueDefault,
				TaskparamTemplateID: templateParam.TaskparamTemplateID,
				BaseFields:          common.BuildBaseFieldsMessageFromModel(templateParam.Entity),
			})

		}
	}
	return &message.GetMergedTaskParamListByTaskparamTemplateIdResp{TaskParams: mergedParamList}, nil
}

func (s *Service) SaveTaskParams(ctx context.Context, req *message.SaveTaskParamsReq) (*message.SaveTaskParamsResp, error) {
	reqBytes, _ := json.Marshal(req)
	log.Infof("SaveTaskParams service request: %v", string(reqBytes))

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("get task[%d] failed. err:%v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	taskParamsModels := make([]*task.TaskParamConfig, 0, len(req.TaskParams))
	taskSubParamsModels := make([]*task.TaskSubParamConfig, 0, len(req.TaskParams))
	taskSubParamNames := make([]string, 0, len(req.TaskParams))

	for _, param := range req.TaskParams {
		if param.HasSubParams {
			taskSubParamsModels = append(taskSubParamsModels, buildTaskSubParamConfigsModelFromMessage(param, taskInfo)...)
			taskSubParamNames = append(taskSubParamNames, param.ParamName)
		}
		updatedTaskParam := buildTaskParamConfigModelFromMessage(param)
		log.Debugf("save task param, taskId:%d, param:%v", req.TaskId, updatedTaskParam)
		taskParamsModels = append(taskParamsModels, updatedTaskParam)
	}
	taskSubParamNames = lo.Uniq(taskSubParamNames)

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		log.Infof("save task params, taskId:%d, len:%d", req.TaskId, len(taskParamsModels))
		_, saveErr := models.GetTaskReaderWriter().SaveTaskParamConfigs(ctx, taskParamsModels)
		if saveErr != nil {
			log.Errorf("save task params failed, taskId:%d, err:%v", req.TaskId, saveErr)
			return saveErr
		}

		for _, param := range taskSubParamNames {
			log.Infof("delete old default subparams, paramName:%s", param)
			deleteErr := models.GetTaskReaderWriter().BatchDeleteTaskSubParamConfigs(ctx, req.TaskId, param)
			if deleteErr != nil {
				log.Errorf("delete old sub param configs failed, paramName:%s, error: %v", param, deleteErr)
				return deleteErr
			}
		}
		if len(taskSubParamsModels) == 0 {
			log.Infof("create new default subparams, len is zero, skip")
			return nil
		}
		log.Infof("create new default subparams, paramNames:%v, len:%d", taskSubParamNames, len(taskSubParamsModels))
		_, createErr := models.GetTaskReaderWriter().BatchSaveTaskSubParamConfigs(ctx, taskSubParamsModels)
		if createErr != nil {
			log.Errorf("create sub param configs failed, taskId:%d, err:%v", req.TaskId, createErr)
			return createErr
		}
		return nil
	})

	if trxErr != nil {
		log.Errorf("save task params failed, taskId:%d, err:%v", req.TaskId, trxErr)
		return nil, trxErr
	}
	return &message.SaveTaskParamsResp{}, nil
}

func (s *Service) TaskExecutionPreCheckReq(ctx context.Context, req *message.TaskExecutionPreCheckReq) (*message.TaskExecutionPreCheckResp, error) {
	log.Infof("TaskExecutionPreCheckReq service request: %v", req)

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get task[%d] failed. err:%v", req.TaskId, err)
		return nil, err
	}

	var validateErr error
	var shouldReturn bool
	var resp *message.TaskExecutionPreCheckResp

	switch taskInfo.TaskType {
	case constants.TASK_TYPE_MIGRATE_FULL_DATA:
		resp, validateErr, shouldReturn = validateOracleDatasource(ctx, taskInfo)
		if shouldReturn {
			return resp, validateErr
		}
	case constants.TASK_TYPE_MIGRATE_CSV_DATA:
		resp, validateErr, shouldReturn = validateOracleDatasource(ctx, taskInfo)
		if shouldReturn {
			return resp, validateErr
		}
		resp, validateErr, shouldReturn = validateCSVParam(ctx, taskInfo)
		if shouldReturn {
			return resp, validateErr
		}
	case constants.TASK_TYPE_SQL_ASSESSMENT:
		resp, validateErr, shouldReturn = validateSQLAnalyzerParam(ctx, taskInfo)
		if shouldReturn {
			return resp, validateErr
		}
	}
	return &message.TaskExecutionPreCheckResp{PreCheckPass: "Y"}, nil
}

func validateSQLAnalyzerParam(ctx context.Context, taskInfo *task.Task) (*message.TaskExecutionPreCheckResp, error, bool) {
	parameter, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if err != nil {
		log.Errorf("build sql analyze parameter failed. err:%v", err)
		return nil, err, true
	}
	validateErr := parameter.ValidateParam()
	if validateErr != nil {
		return &message.TaskExecutionPreCheckResp{
			PreCheckPass:    "N",
			PreCheckMessage: validateErr.Error(),
		}, nil, true
	}
	return nil, nil, false
}

func validateOracleDatasource(ctx context.Context, taskInfo *task.Task) (*message.TaskExecutionPreCheckResp, error, bool) {
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel[%d] failed. err:%v", taskInfo.ChannelId, err)
		return nil, err, true
	}
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		log.Errorf("get source datasource[%d] failed. err:%v", channelInfo.DatasourceIdS, err)
		return nil, err, true
	}
	if sourceDS.DbType == constants.DB_TYPE_ORACLE_ADG {
		sourceDataSourceConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
		if err != nil {
			log.Errorf("set up source database connection failed. err:%v", err)
			return nil, err, true
		}
		_, jobRets, err := oracle.Query(ctx, sourceDataSourceConn.OracleDB, "SELECT database_role, open_mode FROM v$database")
		if err != nil {
			log.Errorf("query oracle database role failed. err:%v", err)
			return nil, err, true
		}
		if len(jobRets) == 0 {
			err = errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "query oracle database role failed. err:%v", err)
			log.Errorf("query oracle database role failed. err:%v", err)
			return nil, err, true
		}
		databaseRole := jobRets[0]["DATABASE_ROLE"]
		openMode := jobRets[0]["OPEN_MODE"]
		log.Infof("channelId:%d, datasouceType:%s, databaseRole:%s, openMode:%s", channelInfo.ChannelId, sourceDS.DbType, databaseRole, openMode)
		if openMode != "READ ONLY WITH APPLY" { // 删除 databaseRole != "PHYSICAL STANDBY" ||
			return &message.TaskExecutionPreCheckResp{
				PreCheckPass:    "N",
				PreCheckMessage: "当前数据库运行状态不是ADG，是否继续？",
			}, nil, true
		}
	}
	return &message.TaskExecutionPreCheckResp{PreCheckPass: "Y"}, nil, false
}

func validateCSVParam(ctx context.Context, taskInfo *task.Task) (*message.TaskExecutionPreCheckResp, error, bool) {
	taskParamTempConfig, err := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if err != nil {
		log.Errorf("get task param template failed. err:%v", err)
		return nil, err, true
	}
	csvParam, err := migrationpkg.BuildCSVMigrationParam(ctx, taskInfo, taskParamTempConfig.TaskparamTemplateID)
	if err != nil {
		log.Errorf("build csv param failed. err:%v", err)
		return nil, err, true
	}

	validateErr := csvParam.Validate()
	if validateErr != nil {
		return &message.TaskExecutionPreCheckResp{
			PreCheckPass:    "N",
			PreCheckMessage: validateErr.Error(),
		}, nil, true
	}

	lightningPath := csvParam.GetLightningBinaryPath()
	// check if lightningPath is not exist , or version lower than 7.4.0
	if exist, _ := fileutil.Exists(lightningPath); !exist {
		return &message.TaskExecutionPreCheckResp{
			PreCheckPass:    "N",
			PreCheckMessage: "lightning path is not exist：" + lightningPath,
		}, nil, true
	}
	lightningVersion, getVersionErr := migrationpkg.GetLightningVersion(lightningPath)
	if getVersionErr != nil {
		return &message.TaskExecutionPreCheckResp{
			PreCheckPass:    "N",
			PreCheckMessage: getVersionErr.Error(),
		}, nil, true
	}

	minimalVersion := semver.Version{
		Major: 7,
		Minor: 4,
		Patch: 0,
	}
	versionValid := !lightningVersion.ServerVersion.LessThan(minimalVersion)
	if !versionValid {
		return &message.TaskExecutionPreCheckResp{
			PreCheckPass:    "N",
			PreCheckMessage: fmt.Sprintf("lightning version is lower than %s, current version is %s", minimalVersion.String(), lightningVersion.ServerVersion.String()),
		}, nil, true

	}

	return &message.TaskExecutionPreCheckResp{PreCheckPass: "Y"}, nil, false
}

func (s *Service) TaskExecution(ctx context.Context, req *message.TaskExecutionReq) (*message.TaskExecutionResp, error) {
	log.Infof("TaskExecution service request: %v", req)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	if taskInfo.TaskStatus == constants.TASK_STATUS_NOT_SETUP || taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		return nil, errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "task[%d] status is %d, it must be 2[not running],4[finish],5[failed]", taskInfo.TaskID, taskInfo.TaskStatus)
	}
	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	taskInfo.StartTime = time.Now()
	taskInfo.ErrorDetail = ""
	_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		return nil, errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to running failed.", taskInfo.TaskID)
	}

	if taskInfo.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		err := assessment.ObjectAssessmentRun(ctx, config.GetGlobalConfig(), req.ChannelId, req.TaskId)
		if err != nil {
			log.Errorf("execution ObjectAssessmentRun failed, err:%s", err)
			return nil, err
		}
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
		log.Infof("TaskExecution MIGRATE_STRUCTURE")
		migration.StartReverseOBJ(ctx, &message.MigrationTaskReq{
			TaskID: req.TaskId,
		})
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
		migrationpkg.StartFullDataMigrationTask(ctx, req.ChannelId, req.TaskId) // 重构后代码
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
		migrationpkg.StartCSVMigrationTask(ctx, req.ChannelId, req.TaskId) // 重构后代码
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_DATA_COMPARE {
		log.Infof("start data compare taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		compare.StartCompare(ctx, req.ChannelId, req.TaskId, config.GetGlobalConfig(), nil)
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
		log.Infof("sqlanalyzer ExecuteSQLAnalyzer async start")
		err := sqlanalyzepkg.ExecuteSQLAnalyzerByDefault(ctx, req.ChannelId, req.TaskId)
		if err != nil {
			log.Errorf("execution ExecuteSQLAnalyzer failed, err:%s", err)
			return nil, err
		}
		return nil, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_STATISTICS {
		err := statistics.TiDBStatsRun(ctx, req.ChannelId, req.TaskId)
		if err != nil {
			log.Errorf("execution TiDBStatsRun failed, err:%s", err)
			return nil, err
		}
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_INCREMENT {
		incrementpkg.StartIncrementTask(ctx, req.ChannelId, req.TaskId)
		return &message.TaskExecutionResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_OBJ_PARSER {
		objectparsercore.StartObjectParserTask(ctx, req.ChannelId, req.TaskId)
		return &message.TaskExecutionResp{}, nil
	}
	return nil, errors.NewErrorf(errors.TIMS_UNKOWN_TASK_TYPE, "task type[%d] unknown", taskInfo.TaskType)
}

func (s *Service) GetTaskDetailResult(ctx context.Context, req *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	log.Infof("TaskExecutionResult service request: %v", req)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	if taskInfo.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		return &message.GetTaskDetailResultResp{}, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
		resp, err := migration.NewStructureMigrationService().GetReverseOBJSummaryByTask2(ctx, req)
		if err != nil {
			return &message.GetTaskDetailResultResp{}, nil
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
		resp, err := migration.NewFullDataMigrationService().GetFullDataMigrationSummaryByTask2(ctx, req)
		if err != nil {
			return &message.GetTaskDetailResultResp{}, nil
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
		resp, err := migration.NewCSVMigrationService().GetCSVMigrationSummaryByTask2(ctx, req)
		if err != nil {
			return &message.GetTaskDetailResultResp{}, nil
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_DATA_COMPARE {
		resp, err := datacompare.NewDataCompareService().GetDataCompareSummaryByTask(ctx, req)
		if err != nil {
			return &message.GetTaskDetailResultResp{}, nil
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
		resp, err := sqlanalyzepkgapi.GetSqlAnalyzerSummary(ctx, req)
		if err != nil {
			return &message.GetTaskDetailResultResp{}, nil
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_STATISTICS {
		resp, err := statistics.NewTidbStatsService().GetTidbStatsSummaryByTask(ctx, req)
		if err != nil {
			return &message.GetTaskDetailResultResp{}, nil
		}
		return resp, nil
	}
	return nil, errors.NewErrorf(errors.TIMS_UNKOWN_TASK_TYPE, "task type[%d] unknown", taskInfo.TaskType)
}

func (s *Service) GetTaskProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	log.Infof("GetTaskProgress service req: %v", req)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	if taskInfo.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		log.Debugf("GET OBJ_ASSESSMENT PROGRESS，taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := assessment.GetAssessmentProgress(ctx, req)
		if err != nil {
			log.Errorf("execution GetAssessmentProgress failed, err:%s", err)
			return nil, err
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
		log.Debugf("GET MIGRATE_STRUCTURE PROGRESS，taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := migration.NewStructureMigrationService().GetStructureProgress(ctx, req)
		// resp, err := migration.GetStructureProgress(ctx, req)
		if err != nil {
			log.Errorf("execution migration GetStructureProgress failed, err:%s", err)
			return resp, err
		}
		resp.Progress = stringutil.Decimal(taskInfo.Progress)
		return resp, err
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
		log.Debugf("GET MIGRATE_FULL_DATA PROGRESS，taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := migration.NewFullDataMigrationService().GetFullDataMigrationProgress(ctx, req)
		if err != nil {
			log.Errorf("execution migration GetFullDataMigrationProgress failed, err:%s", err)
			return resp, err
		}
		return resp, err
	} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
		log.Debugf("GET MIGRATE_CSV_DATA PROGRESS，taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := migration.NewCSVMigrationService().GetCSVMigrationProgress(ctx, req)
		if err != nil {
			log.Errorf("execution migration GetCSVDataMigrationProgress failed, err:%s", err)
			return resp, err
		}
		return resp, err
	} else if taskInfo.TaskType == constants.TASK_TYPE_DATA_COMPARE {
		log.Debugf("GET DATA_COMPARE PROGRESS，taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := datacompare.NewDataCompareService().GetCompareProgress(ctx, req)
		if err != nil {
			log.Errorf("execution datacompare GetCompareProgress failed, err:%s", err)
			return resp, err
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
		log.Infof("sqlanalyzer ExecuteSQLAnalyzer progress")
		resp, err := sqlanalyzer.GetSqlAnalyzerProgress(ctx, req)
		if err != nil {
			log.Errorf("execution GetSqlAnalyzerProgress failed, err:%s", err)
			return nil, err
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_OBJ_PARSER {
		log.Infof("get object parser task progress, taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := objectparsercore.GetProgress(ctx, req.TaskId)
		if err != nil {
			log.Errorf("execution GetProgress failed, taskId:%d, channelId:%d, err:%s", req.TaskId, req.ChannelId, err)
			return nil, err
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_STATISTICS {
		log.Debugf("GET STATISTICS PROGRESS，taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
		resp, err := statistics.NewTidbStatsService().GetTidbStatsProgress(ctx, req)
		if err != nil {
			log.Errorf("execution GetTidbStatsProgress failed, err:%s", err)
			return nil, err
		}
		return resp, nil
	} else if taskInfo.TaskType == constants.TASK_TYPE_INCREMENT {
		return &message.GetTaskProgressResp{}, nil
	}

	return nil, errors.NewErrorf(errors.TIMS_UNKOWN_TASK_TYPE, "task type[%d] unknown", taskInfo.TaskType)
}

func (s *Service) GetObjectAssessResultSummary(ctx context.Context, req *message.GetObjectAssessResultSummaryReq) (*message.GetObjectAssessResultSummaryResp, error) {
	log.Infof("GetO2TObjectAssessSummary service request channel id : %d, task id:%v", req.ChannelId, req.TaskId)
	globalConfig := config.GetGlobalConfig()
	returndata, err := assessment.GetObjectAssessResultSummary(ctx, *globalConfig, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("execution O2TObjectAssessSummary failed, err:%s", err)
		return nil, err
	}
	log.Debugf("returndata: %v\n", returndata)

	return returndata, nil
}

func (s *Service) GetObjectAssessResultSummaryBySchema(ctx context.Context, req *message.GetObjectAssessResultSummaryBySchemaReq) (*message.GetObjectAssessResultSummaryBySchemaResp, error) {
	log.Infof("GetObjectAssessResultSummaryBySchema service request channel id : %d, task id:%v", req.ChannelId, req.TaskId)
	globalConfig := config.GetGlobalConfig()
	returndata, err := assessment.GetObjectAssessResultSummaryBySchema(ctx, *globalConfig, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("execution O2TObjectAssessSummaryBySchema failed, err:%s", err)
		return nil, err
	}
	log.Debugf("returndata: %v\n", returndata)

	return returndata, nil
}

func (s *Service) GetObjectAssessResultDetail(ctx context.Context, req *message.GetObjectAssessResultDetailReq) (*message.GetObjectAssessResultDetailResp, error) {
	log.Infof("GetObjectAssessResultDetail service request channel id : %d, task id:%v, schema:%v", req.ChannelId, req.TaskId, req.Schema)
	globalConfig := config.GetGlobalConfig()
	returndata, err := assessment.GetObjectAssessResultDetail(ctx, *globalConfig, req.ChannelId, req.TaskId, req.Schema)
	if err != nil {
		log.Errorf("execution GetObjectAssessResultDetail failed, err:%s", err)
		return nil, err
	}
	log.Debugf("returndata: %v\n", returndata)

	return returndata, nil
}

func (s *Service) CreateTaskTableConfigs(ctx context.Context, req *message.CreateTaskTableConfigsReq) (*message.CreateTaskTableConfigsResp, error) {
	log.Infof("CreateTaskTableConfigs service request channel id : %d, task id:%v, data:%v", req.ChannelId, req.TaskId, req.TaskTableConfigs)

	taskTableConfigs := make([]*task.TaskTableConfig, 0, len(req.TaskTableConfigs))
	for _, taskTableConfig := range req.TaskTableConfigs {
		taskTableConfigs = append(taskTableConfigs, buildTaskTableConfigModelFromMessage(taskTableConfig))
	}
	_, err := models.GetTaskReaderWriter().CreateTaskTableConfigs(ctx, taskTableConfigs)
	if err != nil {
		log.Errorf("create TaskTableConfigs failed. err:%v", err)
		return nil, err
	}
	return &message.CreateTaskTableConfigsResp{}, nil
}

func (s *Service) CreateTaskTableConfigsByCSV(ctx context.Context, req *message.CreateTaskTableConfigsReq) (*message.CreateTaskTableConfigsResp, error) {
	log.Infof("CreateTaskTableConfigs service request channel id : %d, task id:%v, data:%v", req.ChannelId, req.TaskId, req.TaskTableConfigs)

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel info failed. channelId:%d, err:%v", req.ChannelId, err)
		return nil, err
	}

	//从csv数据组装ChannelSchema，确保数据一致
	var csvChannelSchemas []*channel.ChannelSchema
	for _, csvLine := range req.TaskTableConfigs {
		csvChannelSchemas = append(csvChannelSchemas, &channel.ChannelSchema{
			ChannelId:   req.ChannelId,
			TaskId:      req.TaskId,
			DbNameS:     csvLine.DbNameS,
			SchemaNameS: csvLine.SchemaNameS,
			DbNameT:     csvLine.DbNameT,
			SchemaNameT: csvLine.SchemaNameT,
		})
	}

	//从源库获取schemaTable信息，用于后面channleSchemaTables入库
	schemaTableMap, err := getSchemaTableMapFromSourceDB(ctx, channelInfo, csvChannelSchemas)
	if err != nil {
		return nil, err
	}

	taskTableConfigs := make([]*task.TaskTableConfig, 0, len(req.TaskTableConfigs))
	for _, taskTableConfig := range req.TaskTableConfigs {
		schemaTableMapKey := structs.SchemaTablePair{SchemaName: strings.ToUpper(taskTableConfig.SchemaNameS), TableName: strings.ToUpper(taskTableConfig.TableNameS)}
		if schemaTable, ok := schemaTableMap[schemaTableMapKey]; ok {
			taskTableConfig.SchemaNameS = strings.ToUpper(taskTableConfig.SchemaNameS)
			taskTableConfig.TableNameS = schemaTable.TableName
		}
		taskTableConfigs = append(taskTableConfigs, buildTaskTableConfigModelFromMessage(taskTableConfig))
	}
	_, err = models.GetTaskReaderWriter().CreateTaskTableConfigs(ctx, taskTableConfigs)
	if err != nil {
		log.Errorf("create TaskTableConfigs failed. err:%v", err)
		return nil, err
	}
	return &message.CreateTaskTableConfigsResp{}, nil
}

func (s *Service) UpdateTaskTableConfigs(ctx context.Context, req *message.UpdateTaskTableConfigsReq) (*message.UpdateTaskTableConfigsResp, error) {
	log.Infof("UpdateTaskTableConfigs service request channel id : %d, task id:%v, data:%v", req.ChannelId, req.TaskId, req.TaskTableConfigs)
	taskTableConfigs := make([]*task.TaskTableConfig, 0, len(req.TaskTableConfigs))
	for _, taskTableConfig := range req.TaskTableConfigs {
		taskTableConfigs = append(taskTableConfigs, buildTaskTableConfigModelFromMessage(taskTableConfig))
	}
	_, err := models.GetTaskReaderWriter().UpdateTaskTableConfigs(ctx, taskTableConfigs)
	if err != nil {
		log.Errorf("update TaskTableConfigs failed. err:%v", err)
		return nil, err
	}
	return &message.UpdateTaskTableConfigsResp{}, nil
}

func (s *Service) DeleteTaskTableConfigs(ctx context.Context, req *message.DeleteTaskTableConfigsReq) (*message.DeleteTaskTableConfigsResp, error) {
	log.Infof("DeleteTaskTableConfigs service request channel id : %d, task id:%v, ids:%v", req.ChannelId, req.TaskId, req.Ids)
	err := models.GetTaskReaderWriter().DeleteTaskTableConfigs(ctx, req.Ids)
	if err != nil {
		log.Errorf("delete TaskTableConfigs failed. err:%v", err)
		return nil, err
	}
	return &message.DeleteTaskTableConfigsResp{}, nil
}

func (s *Service) GetTaskTableConfigsByTaskIdAndChannelId(ctx context.Context, req *message.GetTaskTableConfigsByTaskIdAndChannelIdReq) (*message.GetTaskTableConfigsByTaskIdAndChannelIdResp, error) {
	log.Infof("GetTaskTableConfigsByTaskIdAndChannelId service request channel id : %d, task id:%v", req.ChannelId, req.TaskId)
	list, err := models.GetTaskReaderWriter().GetTaskTableConfigsByTaskIdAndChannelId(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("get task table configs list failed.err:%v", err)
		return nil, err
	}
	listMessage := make([]*message.TaskTableConfig, 0, len(list))
	for _, taskTableConfig := range list {
		listMessage = append(listMessage, buildTaskTableConfigMessageFromModel(taskTableConfig))
	}
	return &message.GetTaskTableConfigsByTaskIdAndChannelIdResp{TaskTableConfigs: listMessage}, nil
}

func (s *Service) GetColumnNamesTidb(ctx context.Context, req *message.GetColumnNamesReq) (*message.GetColumnNamesResp, error) {
	log.Infof("GetColumnNames service request schema names : %v, table names:%v, channelId:%d", req.SchemaNames, req.TableNames, req.ChannelId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed. err :%s", getChannelErr)
		return nil, getChannelErr
	}
	datasourceT, getDataSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if getDataSourceErr != nil {
		log.Errorf("get target datasource failed. err :%s", getDataSourceErr)
		return nil, getDataSourceErr
	}
	db, openMysqlErr := models.OpenMysql(datasourceT.UserName, datasourceT.PasswordValue, datasourceT.HostIp, datasourceT.HostPort, "")
	if openMysqlErr != nil {
		log.Errorf("get tidb connection failed. dataSourceT:%v, err:%v", datasourceT, openMysqlErr)
		return nil, openMysqlErr
	}
	columns, err := models.GetTaskReaderWriter().GetColumnNamesFromTidb(ctx, db, req.SchemaNames, req.TableNames)
	if err != nil {
		log.Errorf("get column names from tidb failed. err:%v", err)
		return nil, err
	}
	return &message.GetColumnNamesResp{ColumnNames: columns}, nil
}

func (s *Service) GetColumnNamesOracle(ctx context.Context, req *message.GetColumnNamesReq) (*message.GetColumnNamesResp, error) {
	log.Infof("GetColumnNames service request schema names : %v, table names:%v, channelId:%d", req.SchemaNames, req.TableNames, req.ChannelId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed. err :%s", getChannelErr)
		return nil, getChannelErr
	}
	datasourceS, getDataSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getDataSourceErr != nil {
		log.Errorf("get source datasource failed. err :%s", getDataSourceErr)
		return nil, getDataSourceErr
	}
	oraInfo := transdbconfig.OracleConfig{
		Host:        datasourceS.HostIp,
		Port:        datasourceS.HostPort,
		Username:    datasourceS.UserName,
		Password:    datasourceS.PasswordValue,
		ServiceName: datasourcepkg.GetServiceName(datasourceS),
	}
	oracleDB, err := oracle.NewOracleDBEngine(ctx, oraInfo, "")
	if err != nil {
		log.Errorf("get datasource failed, datasource id is %d, error: %v", channelInfo.DatasourceIdS, err)
		return nil, err
	}
	columns, err := models.GetTaskReaderWriter().GetColumnNamesFromOracle(ctx, oracleDB.OracleDB, req.SchemaNames, req.TableNames)
	if err != nil {
		log.Errorf("get column names from tidb failed. err:%v", err)
		return nil, err
	}
	return &message.GetColumnNamesResp{ColumnNames: columns}, nil
}

func (s *Service) CreateTask(ctx context.Context, req *message.CreateTaskReq) (*message.CreateTaskResp, error) {
	log.Infof("CreateTask service request data:%v", req.Task)
	//屏蔽更新channle_schema_tables,避免影响已有任务的配置 2023-05-24 wuchao
	// unSelectedTables, getUnSelectedTablesErr := models.GetChannelReaderWriter().GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId(ctx, req.ChannelId, req.TaskType, nil, "")
	// if getUnSelectedTablesErr != nil {
	// 	log.Errorf("get un-selected tables failed. channelId:%d, taskType:%d", req.ChannelId, req.TaskType)
	// 	return nil, getUnSelectedTablesErr
	// }
	taskModel := buildTaskModelFromMessage(req.Task)
	maxSeq, err := models.GetTaskReaderWriter().GetMaxTaskSeqByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get task max seq failed when create new task. data:%v, err:%v", taskModel, err)
		return nil, err
	}
	defaultTemplates, getDefaultTemplatesErr := models.GetTemplateReaderWriter().GetAllDefaultTemplates(ctx)
	if getDefaultTemplatesErr != nil {
		log.Errorf("get default template failed when create new task. data:%v, err:%v", taskModel, getDefaultTemplatesErr)
		return nil, getDefaultTemplatesErr
	}
	var (
		defaultTaskParamTemplate       *task.TaskparamTemplateConfig
		getDefaultTaskParamTemplateErr error
	)
	if req.TaskType != constants.TASK_TYPE_STATISTICS {
		defaultTaskParamTemplate, getDefaultTaskParamTemplateErr = models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, taskModel.TaskType)
		if getDefaultTaskParamTemplateErr != nil {
			log.Errorf("get default task param template failed when create new task. data:%v, err:%v", taskModel, getDefaultTaskParamTemplateErr)
			return nil, getDefaultTaskParamTemplateErr
		}
	}
	//对象兼容性评估-明细显示
	if req.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		taskModel.OnlyIncompatibleDetail = constants.ONLY_INCOMPATIBLE_DETAIL_DEFAULT
	}
	err = models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskModel.TaskSeq = maxSeq + 1
		taskModel.TaskStatus = constants.TASK_STATUS_NOT_RUNNING
		defaultTime, _ := time.Parse(time.RFC3339, "1970-01-01T00:00:00+08:00")
		taskModel.StartTime = defaultTime
		taskModel.EndTime = defaultTime
		chooseTemplateId(taskModel, defaultTemplates)
		if defaultTaskParamTemplate != nil {
			taskModel.TaskParamTmplateId = defaultTaskParamTemplate.TaskparamTemplateID
		}
		taskModel, err = models.GetTaskReaderWriter().CreateTask(ctx, taskModel)
		if err != nil {
			log.Errorf("create new task failed. data:%v, err:%v", taskModel, err)
			return err
		}

		//屏蔽更新channle_schema_tables,避免影响已有任务的配置 2023-05-24 wuchao
		//assign tables to new created task
		// if len(unSelectedTables) == 0 {
		// 	log.Infof("when create new task, it have no un-selected tables. taskType:%d, taskName:%s, taskId:%d", req.TaskType, req.TaskName, taskModel.TaskID)
		// 	return nil
		// }
		// for _, table := range unSelectedTables {
		// 	table.TaskId = taskModel.TaskID
		// }
		//屏蔽更新channle_schema_tables,避免影响已有任务的配置 2023-05-24 wuchao
		// _, updateTablesErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(ctx, unSelectedTables)
		// if updateTablesErr != nil {
		// 	log.Errorf("when create new task, update channel schema tables failed.taskType: %d, taskName:%s, taskId:%d, err:%s", req.TaskType, req.TaskName, taskModel.TaskID, updateTablesErr)
		// }

		//配置channel_schema_objects 2023-05-24 wuchao
		if req.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "Y", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "N"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		// create sqlanalyze env deploy tasks
		if req.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
			envDeployTasks := buildSQLAnalyzeEnvDeployTasks(taskModel)
			createErr := models.GetSqlAnalyzerReaderWriter().BatchCreateEnvDeployTask(transactionCtx, envDeployTasks)
			if createErr != nil {
				log.Errorf("create default sqlanalyze tasks failed. data:%v, err:%s", envDeployTasks, createErr)
				return createErr
			}

			channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(transactionCtx, req.ChannelId)
			if err != nil {
				log.Errorf("get channel schemas failed. channelId:%d, err:%v", req.ChannelId, err)
				return err
			}
			channelSchemaStrs := lo.Map(channelSchemas, func(channelSchema *channel.ChannelSchema, _ int) string {
				return channelSchema.SchemaNameS
			})
			if len(channelSchemaStrs) != 0 {
				_, saveErr := models.GetTaskReaderWriter().BatchSaveTaskSubParamConfigs(transactionCtx, buildSQLIncludingSchemaParam(taskModel, lo.Uniq(channelSchemaStrs)))
				if saveErr != nil {
					log.Errorf("save sqlanalyzer task sub param config failed, taskId:%d, err:%v", taskModel.TaskID, saveErr)
					return saveErr
				}
			}
		}

		// create object parser incompatible features
		if taskModel.TaskType == constants.TASK_TYPE_OBJ_PARSER {

			features, getErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(transactionCtx)
			if getErr != nil {
				log.Errorf("ListBasicOracleIncompatibleFeature failed. err:%v", getErr)
				return getErr
			}
			taskFeatures := lo.Map(features, func(feature *objectparser.OracleIncompatibleFeature, _ int) *objectparser.OracleTaskIncompatibleFeature {
				return &objectparser.OracleTaskIncompatibleFeature{
					TaskId:       taskModel.TaskID,
					FeatureType:  feature.FeatureType,
					FeatureKey:   feature.FeatureKey,
					FeatureScore: feature.FeatureScore,
					FeatureDesc:  feature.FeatureDesc,
				}
			})
			createErr := models.GetObjectParserWriter().CreateTaskOracleIncompatibleFeatures(transactionCtx, taskFeatures)
			if createErr != nil {
				log.Errorf("create object parser incompatible features failed. data:%v, err:%s", taskFeatures, createErr)
				return createErr
			}
		}

		// create data compare env deploy tasks after task got an task_id
		if req.TaskType == constants.TASK_TYPE_DATA_COMPARE {
			envDeployTasks := buildDataCompareEnvDeployTasks(taskModel)
			createErr := models.GetDataCompareReaderWriter().BatchCreateEnvDeployTask(transactionCtx, envDeployTasks)
			if createErr != nil {
				log.Errorf("create data compare env deploy tasks failed. data:%v, err:%s", envDeployTasks, createErr)
				return createErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_INCREMENT {
			// TODO
		}

		return nil
	})
	if err != nil {
		log.Errorf("create new task failed. err:%v", err)
		return nil, err
	}
	log.Infof("create new task successfully.taskId:%d", taskModel.TaskID)
	return &message.CreateTaskResp{}, nil
}

func (s *Service) BatchDeleteTasks(ctx context.Context, req *message.BatchDeleteTasksReq) (*message.BatchDeleteTasksResp, error) {
	log.Infof("BatchDeleteTasks service request ids:%v, channelId:%d", req.TaskIDs, req.ChannelId)

	tasks, err := models.GetTaskReaderWriter().GetTasks(ctx, req.ChannelId, req.TaskIDs)
	if err != nil {
		log.Errorf("get tasks failed. channelId:%d, err:%v", req.ChannelId, err)
		return nil, err
	}

	var sqlAnalyzeTasks []*task.Task
	var sqlAnalyzeTaskIds []int
	for _, taskInfo := range tasks {
		if taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
			sqlAnalyzeTasks = append(sqlAnalyzeTasks, taskInfo)
			sqlAnalyzeTaskIds = append(sqlAnalyzeTaskIds, taskInfo.TaskID)
		}
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		err := models.GetTaskReaderWriter().BatchDeleteTasksByIds(transactionCtx, req.TaskIDs)
		if err != nil {
			log.Errorf("batch delete tasks failed. taskIds:%v, err: %v", req.TaskIDs, err)
			return err
		}

		err = models.GetChannelReaderWriter().UnSelectChannelSchemaTablesByTaskId(transactionCtx, req.TaskIDs)
		if err != nil {
			log.Errorf("UnSelectChannelSchemaTablesByTaskId failed. taskIds:%v, err: %v", req.TaskIDs, err)
			return err
		}

		err = models.GetTaskReaderWriter().BatchDeleteTaskSubParamConfigsByTaskIds(transactionCtx, req.TaskIDs)
		if err != nil {
			log.Errorf("batch delete task sub param configs failed. taskIds:%v, err: %v", req.TaskIDs, err)
			return err
		}

		if len(sqlAnalyzeTaskIds) >= 1 {
			err = models.GetSqlAnalyzerReaderWriter().BatchDeleteSQLAnalyzeTasksByTaskIds(transactionCtx, sqlAnalyzeTaskIds)
			if err != nil {
				log.Errorf("batch delete sql analyze tasks failed. taskIds:%v, err: %v", sqlAnalyzeTaskIds, err)
				return err
			}
		}

		return nil
	})
	if trxErr != nil {
		log.Errorf("batch delete tasks failed. taskIds:%v, err: %v", req.TaskIDs, trxErr)
		return nil, trxErr
	}

	for _, taskInfo := range tasks {
		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			deleteLightningTableMetas(ctx, taskInfo) // 删除元数据，是旁路
		}
	}

	log.Infof("batch delete tasks successfully")
	return &message.BatchDeleteTasksResp{}, nil
}

func chooseTemplateId(task *task.Task, defaultTemplates []*template.TmplateInformation) {
	if task.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		count := 0
		for _, defaultTemplate := range defaultTemplates {
			if defaultTemplate.TemplateType == constants.TEMPLATE_TYPE_OBJMAP_RULE {
				task.ObjmapTmplateId = defaultTemplate.TmplateId
				//break
			}
			if defaultTemplate.TemplateType == constants.TEMPLATE_TYPE_TABCOL_MAP_RULE {
				task.TabcolmapTmplateId = defaultTemplate.TmplateId
				count++
			} else if defaultTemplate.TemplateType == constants.TEMPLATE_TYPE_COLDEFAULT_MAP_RULE {
				task.ColdefaultmapTmplateId = defaultTemplate.TmplateId
				count++
			}
			if count == 3 {
				break
			}
		}
	} else if task.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
		count := 0
		for _, defaultTemplate := range defaultTemplates {
			if defaultTemplate.TemplateType == constants.TEMPLATE_TYPE_TABCOL_MAP_RULE {
				task.TabcolmapTmplateId = defaultTemplate.TmplateId
				count++
			} else if defaultTemplate.TemplateType == constants.TEMPLATE_TYPE_COLDEFAULT_MAP_RULE {
				task.ColdefaultmapTmplateId = defaultTemplate.TmplateId
				count++
			}
			if count == 2 {
				break
			}
		}
	} else if task.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
		for _, defaultTemplate := range defaultTemplates {
			if defaultTemplate.TemplateType == constants.TEMPLATE_TYPE_SQLMAP_RULE {
				task.SqlmapTmplateId = defaultTemplate.TmplateId
				break
			}
		}
	}
}

func buildTaskTableConfigModelFromMessage(taskTableConfig *message.TaskTableConfig) *task.TaskTableConfig {
	return &task.TaskTableConfig{
		TaskTableId:        taskTableConfig.TaskTableId,
		ChannelId:          taskTableConfig.ChannelId,
		TaskId:             taskTableConfig.TaskId,
		DbNameS:            taskTableConfig.DbNameS,
		SchemaNameS:        taskTableConfig.SchemaNameS,
		TableNameS:         taskTableConfig.TableNameS,
		DbNameT:            taskTableConfig.DbNameT,
		SchemaNameT:        taskTableConfig.SchemaNameT,
		TableNameT:         taskTableConfig.TableNameT,
		OperatorTag:        taskTableConfig.OperatorTag,
		ColumnslistTidb:    taskTableConfig.ColumnslistTidb,
		ColumnslistOracle:  taskTableConfig.ColumnslistOracle,
		FilterClauseTidb:   taskTableConfig.FilterClauseTidb,
		FilterClauseOracle: taskTableConfig.FilterClauseOracle,
		SqlhintTidb:        taskTableConfig.SqlhintTidb,
		SqlhintOracle:      taskTableConfig.SqlhintOracle,
		ColmapTidb:         taskTableConfig.ColmapTidb,
		ColmapOracle:       taskTableConfig.ColmapOracle,
		EnableChunkSplit:   taskTableConfig.EnableChunkSplit,
		TablePartition:     taskTableConfig.TablePartition,
		Entity:             common.BuildEntityModelFromMessage(taskTableConfig.BaseFields),
	}
}

func buildTaskTableConfigMessageFromModel(taskTableConfig *task.TaskTableConfig) *message.TaskTableConfig {
	return &message.TaskTableConfig{
		TaskTableId:        taskTableConfig.TaskTableId,
		ChannelId:          taskTableConfig.ChannelId,
		TaskId:             taskTableConfig.TaskId,
		DbNameS:            taskTableConfig.DbNameS,
		SchemaNameS:        taskTableConfig.SchemaNameS,
		TableNameS:         taskTableConfig.TableNameS,
		DbNameT:            taskTableConfig.DbNameT,
		SchemaNameT:        taskTableConfig.SchemaNameT,
		TableNameT:         taskTableConfig.TableNameT,
		OperatorTag:        taskTableConfig.OperatorTag,
		ColumnslistTidb:    taskTableConfig.ColumnslistTidb,
		ColumnslistOracle:  taskTableConfig.ColumnslistOracle,
		FilterClauseTidb:   taskTableConfig.FilterClauseTidb,
		FilterClauseOracle: taskTableConfig.FilterClauseOracle,
		SqlhintTidb:        taskTableConfig.SqlhintTidb,
		SqlhintOracle:      taskTableConfig.SqlhintOracle,
		ColmapTidb:         taskTableConfig.ColmapTidb,
		ColmapOracle:       taskTableConfig.ColmapOracle,
		EnableChunkSplit:   taskTableConfig.EnableChunkSplit,
		TablePartition:     taskTableConfig.TablePartition,
		BaseFields:         common.BuildBaseFieldsMessageFromModel(taskTableConfig.Entity),
	}
}

func buildTaskParamConfigModelFromMessage(param *message.TaskParam) *task.TaskParamConfig {
	return &task.TaskParamConfig{
		TaskParamID:         param.TaskParamID,
		ChannelID:           param.ChannelID,
		TaskID:              param.TaskID,
		ParamName:           param.ParamName,
		ParamValueCurrent:   param.ParamValueCurrent,
		ParamValueDefault:   param.ParamValueDefault,
		TaskparamTemplateID: param.TaskparamTemplateID,
		HasSubParams:        param.HasSubParams,
		Entity:              common.BuildEntityModelFromMessage(param.BaseFields),
	}
}

func buildTaskSubParamConfigsModelFromMessage(param *message.TaskParam, taskInfo *task.Task) []*task.TaskSubParamConfig {
	arr := make([]*task.TaskSubParamConfig, 0, len(param.SubParamValuesCurrent))
	if len(param.SubParamValuesCurrent) == 0 {
		arr = append(arr, &task.TaskSubParamConfig{
			ChannelID:           taskInfo.ChannelId,
			TaskID:              taskInfo.TaskID,
			ParamName:           param.ParamName,
			TaskparamTemplateID: param.TaskparamTemplateID,
			ParamValue:          constants.FakeParamValue,
			Entity:              common.BuildEntityModelFromMessage(param.BaseFields),
		})
	} else {
		for _, subParam := range param.SubParamValuesCurrent {
			arr = append(arr, &task.TaskSubParamConfig{
				ChannelID:           taskInfo.ChannelId,
				TaskID:              taskInfo.TaskID,
				ParamName:           param.ParamName,
				TaskparamTemplateID: param.TaskparamTemplateID,
				ParamValue:          subParam,
				Entity:              common.BuildEntityModelFromMessage(param.BaseFields),
			})
		}
	}

	return arr
}

func BuildTaskParamTemplateMessageFromModel(taskParamTemplate *task.TaskparamTemplateConfig) *message.TaskParamTemplate {
	return &message.TaskParamTemplate{
		TaskParamTemplateID: taskParamTemplate.TaskparamTemplateID,
		ParamTemplateName:   taskParamTemplate.ParamTemplateName,
		DefaultTag:          taskParamTemplate.DefaultTag,
		TaskType:            taskParamTemplate.TaskType,
		TemplateType:        constants.TEMPLATE_TYPE_STANDARD_PARAM,
		BaseFields:          common.BuildBaseFieldsMessageFromModel(taskParamTemplate.Entity),
	}
}

func BuildTaskParamTemplateModelFromMessage(taskParamTemplate *message.TaskParamTemplate) *task.TaskparamTemplateConfig {
	return &task.TaskparamTemplateConfig{
		TaskparamTemplateID: taskParamTemplate.TaskParamTemplateID,
		ParamTemplateName:   taskParamTemplate.ParamTemplateName,
		DefaultTag:          taskParamTemplate.DefaultTag,
		TaskType:            taskParamTemplate.TaskType,
		Entity:              common.BuildEntityModelFromMessage(taskParamTemplate.BaseFields),
	}
}

func buildTaskMessageFromModel(taskModel *task.Task) *message.Task {
	return &message.Task{
		TaskID:                  taskModel.TaskID,
		TaskType:                taskModel.TaskType,
		TaskName:                taskModel.TaskName,
		StartTime:               taskModel.StartTime,
		EndTime:                 taskModel.EndTime,
		TaskStatus:              taskModel.TaskStatus,
		ChannelId:               taskModel.ChannelId,
		TaskParamTemplateId:     taskModel.TaskParamTmplateId,
		TabcolmapTemplateId:     taskModel.TabcolmapTmplateId,
		ObjmapTemplateId:        taskModel.ObjmapTmplateId,
		SqlmapTemplateId:        taskModel.SqlmapTmplateId,
		ColDefaultMapTemplateId: taskModel.ColdefaultmapTmplateId,
		TaskSeq:                 taskModel.TaskSeq,
		ServerId:                taskModel.ServerId,
		ErrorDetail:             taskModel.ErrorDetail,
		IncrementId:             taskModel.IncrementId,
		TaskObjRef:              taskModel.TaskObjRef,
		TaskReftask:             taskModel.TaskReftask,
		TaskWarning:             taskModel.TaskWarning,
		RunParams:               taskModel.RunParams,
		OnlyIncompatibleDetail:  taskModel.OnlyIncompatibleDetail,
		Progress:                taskModel.Progress,
		ScnNumber:               taskModel.ScnNumber,
		BaseFields:              common.BuildBaseFieldsMessageFromModel(taskModel.Entity),
	}
}

func buildTaskModelFromMessage(taskMessage *message.Task) *task.Task {
	return &task.Task{
		TaskID:                 taskMessage.TaskID,
		TaskType:               taskMessage.TaskType,
		TaskName:               taskMessage.TaskName,
		StartTime:              taskMessage.StartTime,
		EndTime:                taskMessage.EndTime,
		TaskStatus:             taskMessage.TaskStatus,
		ServerId:               taskMessage.ServerId,
		TaskSeq:                taskMessage.TaskSeq,
		ChannelId:              taskMessage.ChannelId,
		TaskParamTmplateId:     taskMessage.TaskParamTemplateId,
		TabcolmapTmplateId:     taskMessage.TabcolmapTemplateId,
		ColdefaultmapTmplateId: taskMessage.ColDefaultMapTemplateId,
		ObjmapTmplateId:        taskMessage.ObjmapTemplateId,
		SqlmapTmplateId:        taskMessage.SqlmapTemplateId,
		IncrementId:            taskMessage.IncrementId,
		ErrorDetail:            taskMessage.ErrorDetail,
		TaskObjRef:             taskMessage.TaskObjRef,
		TaskReftask:            taskMessage.TaskReftask,
		TaskWarning:            taskMessage.TaskWarning,
		OnlyIncompatibleDetail: taskMessage.OnlyIncompatibleDetail,
		ScnNumber:              taskMessage.ScnNumber,
		Entity:                 common.BuildEntityModelFromMessage(taskMessage.BaseFields),
	}
}

func (s *Service) AddAllChannelSchemaTables(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("AddAllChannelSchemaTables service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("AddAllChannelSchemaTables.GetTaskReaderWriter failed. err:%v", err)
		return nil, err
	}

	err = models.GetChannelReaderWriter().AddAllChannelSchemaTables(ctx, req.ChannelId, req.TaskId, taskInfo.TaskType)
	if err != nil {
		log.Errorf("AddAllChannelSchemaTables failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) DeleteAllChannelSchemaTables(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("DeleteAllChannelSchemaTables service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.GetChannelReaderWriter().DeleteAllChannelSchemaTables(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("DeleteAllChannelSchemaTables failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) ToUpperCaseTableNameT(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("ToUpperCaseTableNameT service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		err := models.GetChannelReaderWriter().ToUpperCaseTableNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		err = models.GetTaskReaderWriter().ToUpperCaseTableNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Errorf("ToUpperCaseTableNameT failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) ToLowerCaseTableNameT(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("ToLowerCaseTableNameT service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		err := models.GetChannelReaderWriter().ToLowerCaseTableNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		err = models.GetTaskReaderWriter().ToLowerCaseTableNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Errorf("ToLowerCaseTableNameT failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) ToUpperCaseSchemaNameT(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("ToUpperCaseSchemaNameT service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		err := models.GetChannelReaderWriter().ToUpperCaseSchemaNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		err = models.GetTaskReaderWriter().ToUpperCaseSchemaNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Errorf("ToUpperCaseSchemaNameT failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) ToLowerCaseSchemaNameT(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("ToLowerCaseSchemaNameT service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		err := models.GetChannelReaderWriter().ToLowerCaseSchemaNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		err = models.GetTaskReaderWriter().ToLowerCaseSchemaNameT(transactionCtx, req.ChannelId, req.TaskId)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		log.Errorf("ToLowerCaseSchemaNameT failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) ModifyChannelSchemaTable(ctx context.Context, req *message.OperChannelSchemaTablesReq) (*message.CommonResp, error) {
	log.Infof("ModifyChannelSchemaTable service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.GetChannelReaderWriter().ModifyChannelSchemaTable(ctx, req.ChannelId, req.TaskId, req.ModifyTag, req.TableNameS)
	if err != nil {
		log.Errorf("ModifyChannelSchemaTable failed. err:%v", err)
		return nil, err
	}
	return &message.CommonResp{}, nil
}

func (s *Service) ChangePartitioningTypeT(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("ChangePartitioningTypeT service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	err := models.GetChannelReaderWriter().ChangePartitioningTypeT(ctx, req.ChannelId, req.TaskId, req.ChannelSchtableIds, req.PartitionTypeT)
	if err != nil {
		log.Errorf("ChangePartitioningTypeT failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

func (s *Service) ChangeClusterTypeT(ctx context.Context, req *message.OperAllChannelSchemaTablesReq) (*message.OperAllChannelSchemaTablesResp, error) {
	log.Infof("ChangeClusterTypeT service request channel id : %d, task id:%d", req.ChannelId, req.TaskId)
	if strings.EqualFold(req.ClusterTypeT, "Y") {
		records, _ := models.GetChannelReaderWriter().CheckChannelSchemaTableByCluster(ctx, req.ChannelId, req.TaskId, req.ChannelSchtableIds, req.ClusterTypeT)
		if records != nil && len(records) > 0 {
			var builder strings.Builder
			builder.WriteString("表")
			for _, record := range records {
				builder.WriteString("[")
				builder.WriteString(record.SchemaNameS)
				builder.WriteString(".")
				builder.WriteString(record.TableNameS)
				builder.WriteString("]")
			}
			builder.WriteString("不能转为聚簇表")
			return nil, errors.NewErrorf(errors.TIMS_CHANGE_CLUSTER_TYPE_FAILED, builder.String())
		}
	}

	err := models.GetChannelReaderWriter().ChangeClusterTypeT(ctx, req.ChannelId, req.TaskId, req.ChannelSchtableIds, req.ClusterTypeT)
	if err != nil {
		log.Errorf("ChangeClusterTypeT failed. err:%v", err)
		return nil, err
	}
	return &message.OperAllChannelSchemaTablesResp{}, nil
}

/*
2023-06-20
wuchao
从CSV创建任务
*/
func (s *Service) CreateTaskByCSV(ctx context.Context, req *message.CreateTaskByCsvReq, csvDatas []*structs.CsvSchemaTables) (*message.CreateTaskResp, error) {
	log.Infof("CreateTaskByCSV service request data:%v", req)

	taskModel := &task.Task{
		TaskType:  req.TaskType,
		TaskName:  req.TaskName,
		ChannelId: req.ChannelId,
	}
	defaultTemplates, getDefaultTemplatesErr := models.GetTemplateReaderWriter().GetAllDefaultTemplates(ctx)
	if getDefaultTemplatesErr != nil {
		log.Errorf("CreateTaskByCSV get default template failed when create new task. data:%v, err:%v", taskModel, getDefaultTemplatesErr)
		return nil, getDefaultTemplatesErr
	}
	var (
		defaultTaskParamTemplate       *task.TaskparamTemplateConfig
		getDefaultTaskParamTemplateErr error
	)

	//非统计信息任务，查询默认参数模版
	if req.TaskType != constants.TASK_TYPE_STATISTICS {
		defaultTaskParamTemplate, getDefaultTaskParamTemplateErr = models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, taskModel.TaskType)
		if getDefaultTaskParamTemplateErr != nil {
			log.Errorf("CreateTaskByCSV get default task param template failed when create new task. data:%v, err:%v", taskModel, getDefaultTaskParamTemplateErr)
			return nil, getDefaultTaskParamTemplateErr
		}
	}
	//对象兼容性评估-明细显示
	if req.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		taskModel.OnlyIncompatibleDetail = constants.ONLY_INCOMPATIBLE_DETAIL_DEFAULT
	}
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskModel.TaskStatus = constants.TASK_STATUS_NOT_RUNNING
		defaultTime, _ := time.Parse(time.RFC3339, "1970-01-01T00:00:00+08:00")
		taskModel.StartTime = defaultTime
		taskModel.EndTime = defaultTime
		chooseTemplateId(taskModel, defaultTemplates)
		if defaultTaskParamTemplate != nil {
			taskModel.TaskParamTmplateId = defaultTaskParamTemplate.TaskparamTemplateID
		}
		taskModel, err := models.GetTaskReaderWriter().CreateTask(transactionCtx, taskModel)
		if err != nil {
			log.Errorf("CreateTaskByCSV create new task failed. data:%v, err:%v", taskModel, err)
			return err
		}

		channelInfo, err := models.GetChannelReaderWriter().GetChannel(transactionCtx, taskModel.ChannelId)
		if err != nil {
			log.Errorf("get channel info failed. channelId:%d, err:%v", req.ChannelId, err)
			return err
		}

		//从csv数据组装ChannelSchema，确保数据一致
		var csvChannelSchemas []*channel.ChannelSchema
		for _, csvLine := range csvDatas {
			csvChannelSchemas = append(csvChannelSchemas, &channel.ChannelSchema{
				ChannelId:   req.ChannelId,
				TaskId:      taskModel.TaskID,
				DbNameS:     csvLine.DbNameS,
				SchemaNameS: csvLine.SchemaNameS,
				DbNameT:     csvLine.DbNameT,
				SchemaNameT: csvLine.SchemaNameT,
			})
		}

		//从源库获取schemaTable信息，用于后面channleSchema，channleSchemaTables入库
		dataSourceSchemasMap, err := GetDataSourceSchemas(transactionCtx, channelInfo.DatasourceIdS)
		if err != nil {
			log.Errorf("create default tasks failed. get data source schemas failed, channelId:%d", channelInfo.ChannelId)
			return err
		}

		//从源库获取schemaTable信息，用于后面channleSchemaTables入库
		schemaTableMap, err := getSchemaTableMapFromSourceDB(transactionCtx, channelInfo, csvChannelSchemas)
		if err != nil {
			log.Errorf("CreateTaskByCSV getSchemaTableMapFromSourceDB failed. data:%v, err:%v", taskModel, err)
			return err
		}

		//如果channelSchema表里面不存在csv导入的schema，就插入新的channelSchema记录
		channelSchemas, err := createChannelSchemaByCsvIfNotExist(transactionCtx, req.ChannelId, taskModel.TaskID, csvDatas, dataSourceSchemasMap)
		if err != nil {
			log.Errorf("CreateTaskByCSV create channel schema failed. data:%v, err:%v", taskModel, err)
			return err
		}

		//如果不存在已有的channelSchemaTables，就插入新的channelSchemaTables记录；如果存在，报冲突错误。
		err = createChannleSchemaTablesByCsvIfNotExist(transactionCtx, req, taskModel, csvDatas, schemaTableMap, dataSourceSchemasMap)
		if err != nil {
			log.Errorf("CreateTaskByCSV create channel schema tables failed. data:%v, err:%v", taskModel, err)
			return err
		}

		//配置channel_schema_objects
		if req.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "Y", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "N"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByCSV create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByCSV create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByCSV create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		// create object parser incompatible features
		if req.TaskType == constants.TASK_TYPE_OBJ_PARSER {
			features, getErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(transactionCtx)
			if getErr != nil {
				log.Errorf("ListBasicOracleIncompatibleFeature failed. err:%v", getErr)
				return getErr
			}
			taskFeatures := lo.Map(features, func(feature *objectparser.OracleIncompatibleFeature, _ int) *objectparser.OracleTaskIncompatibleFeature {
				return &objectparser.OracleTaskIncompatibleFeature{
					TaskId:       taskModel.TaskID,
					FeatureType:  feature.FeatureType,
					FeatureKey:   feature.FeatureKey,
					FeatureScore: feature.FeatureScore,
					FeatureDesc:  feature.FeatureDesc,
				}
			})
			createErr := models.GetObjectParserWriter().CreateTaskOracleIncompatibleFeatures(transactionCtx, taskFeatures)
			if createErr != nil {
				log.Errorf("create object parser incompatible features failed. data:%v, err:%s", taskFeatures, createErr)
				return createErr
			}
		}

		// create sqlanalyze env deploy tasks
		if req.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
			sqlAnalyzeTasks := buildSQLAnalyzeEnvDeployTasks(taskModel)
			createErr := models.GetSqlAnalyzerReaderWriter().BatchCreateEnvDeployTask(transactionCtx, sqlAnalyzeTasks)
			if createErr != nil {
				log.Errorf("CreateTaskByCSV create default sqlanalyze tasks failed. data:%v, err:%s", sqlAnalyzeTasks, createErr)
				return createErr
			}

			channelSchemaStrs := lo.Map(channelSchemas, func(channelSchema *channel.ChannelSchema, _ int) string {
				return channelSchema.SchemaNameS
			})
			if len(channelSchemaStrs) != 0 {
				_, saveErr := models.GetTaskReaderWriter().BatchSaveTaskSubParamConfigs(transactionCtx, buildSQLIncludingSchemaParam(taskModel, lo.Uniq(channelSchemaStrs)))
				if saveErr != nil {
					log.Errorf("CreateTaskByCSV save sqlanalyzer task param config failed, taskId:%d, err:%v", taskModel.TaskID, saveErr)
					return saveErr
				}
			}
		}

		// create data compare env deploy tasks
		if req.TaskType == constants.TASK_TYPE_DATA_COMPARE {
			envDeployTasks := buildDataCompareEnvDeployTasks(taskModel)
			createErr := models.GetDataCompareReaderWriter().BatchCreateEnvDeployTask(transactionCtx, envDeployTasks)
			if createErr != nil {
				log.Errorf("CreateTaskByCSV create data compare env deploy tasks failed. data:%v, err:%s", envDeployTasks, createErr)
				return createErr
			}
		}

		return nil
	})
	if err != nil {
		log.Errorf("CreateTaskByCSV failed. err:%v", err)
		return nil, err
	}
	log.Infof("CreateTaskByCSV successfully.taskId:%d", taskModel.TaskID)
	return &message.CreateTaskResp{}, nil
}

func createChannelSchemaByCsvIfNotExist(transactionCtx context.Context, channelId int, taskId int, csvDatas []*structs.CsvSchemaTables, datasourceSchemaMap map[string]*structs.SchemaInfo) ([]*channel.ChannelSchema, error) {
	//写入channel_schema（需要去重）
	currentChannelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(transactionCtx, channelId)
	if err != nil {
		log.Errorf("CreateTaskByCSV GetChannelSchemas failed. err:%v", err)
		return nil, err
	}
	var channelSchemas []*channel.ChannelSchema
	channelSchemaMap := make(map[string]string)
	for _, csvLine := range csvDatas {
		existInTable := false
		for _, currentData := range currentChannelSchemas {
			if strings.ToUpper(csvLine.DbNameS) == strings.ToUpper(currentData.DbNameS) && strings.ToUpper(csvLine.SchemaNameS) == strings.ToUpper(currentData.SchemaNameS) {
				//channel_schema表里已有，跳过
				existInTable = true
				break
			}
		}
		if !existInTable { //channel_schema表里没有
			key := fmt.Sprintf("%s-%s", strings.ToUpper(csvLine.DbNameS), strings.ToUpper(csvLine.SchemaNameS))
			if _, ok := channelSchemaMap[key]; ok {
				//已加入channleSchemas，跳过
			} else {
				channelSchemaMap[key] = key
				channelSchemas = append(channelSchemas, &channel.ChannelSchema{
					ChannelId:   channelId,
					TaskId:      taskId,
					DbNameS:     csvLine.DbNameS,
					SchemaNameS: datasourceSchemaMap[strings.ToUpper(csvLine.SchemaNameS)].SchemaName,
					DbNameT:     csvLine.DbNameT,
					SchemaNameT: strings.ToUpper(csvLine.SchemaNameT),
				})
			}
		}
	}
	if len(channelSchemas) > 0 {
		channelSchemas, err = models.GetChannelReaderWriter().CreateChannelSchemas(transactionCtx, channelSchemas)
		if err != nil {
			log.Errorf("CreateTaskByCSV CreateChannelSchemas failed. channelId:%d, taskType:%d", channelId, taskId)
			return channelSchemas, err
		}
	}
	return channelSchemas, nil
}

func GetDataSourceSchemas(ctx context.Context, dataSourceID int) (map[string]*structs.SchemaInfo, error) {
	log.Infof("GetDataSourceSchemas service request DataSourceID: %d", dataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, dataSourceID)
	if err != nil {
		return nil, err
	}
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err = models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleSchemas(ctx, db)
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err = models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlSchemas(ctx, db)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
	if err != nil {
		return nil, err
	}

	schemaMap := make(map[string]*structs.SchemaInfo)
	for _, schema := range schemas {
		schemaMap[strings.ToUpper(schema.SchemaName)] = schema
	}
	return schemaMap, nil
}

func getSchemaTableMapFromSourceDB(transactionCtx context.Context, channelInfo *channel.ChannelInformation, csvChannelSchemas []*channel.ChannelSchema) (map[structs.SchemaTablePair]*structs.TableInfo, error) {
	schemaTables, _, _, getTableErr := getSchemaTablesAndDataSource(transactionCtx, channelInfo, csvChannelSchemas)
	if getTableErr != nil {
		log.Errorf("create default tasks failed. get channel schema tables failed, channelId:%d", channelInfo.ChannelId)
		return nil, getTableErr
	}
	schemaTableMap := make(map[structs.SchemaTablePair]*structs.TableInfo)
	for _, schema := range schemaTables {
		for _, table := range schema.ObjectTypes[0].Tables {
			mTable := table
			schemaTableMap[structs.SchemaTablePair{SchemaName: strings.ToUpper(schema.SchemaName), TableName: strings.ToUpper(table.TableName)}] = &mTable
		}
	}
	return schemaTableMap, nil
}

func getSchemaTableMapByTaskType(transactionCtx context.Context, taskType int, channelId int) (map[structs.SchemaTablePair]*channel.ChannelSchemaTable, error) {
	sameTypeChannleSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskTypeAndChannelId(transactionCtx, taskType, channelId)
	if err != nil {
		log.Errorf("CreateTaskByCSV GetChannelSchemaTablesByTaskTypeAndChannelId failed. taskType:%v, channelId:%v, err:%v", taskType, channelId, err)
		return nil, err
	}
	schemaTableMap := make(map[structs.SchemaTablePair]*channel.ChannelSchemaTable)
	for _, data := range sameTypeChannleSchemaTables {
		schemaTableMap[structs.SchemaTablePair{SchemaName: strings.ToUpper(data.SchemaNameS), TableName: strings.ToUpper(data.TableNameS)}] = data
	}
	return schemaTableMap, nil
}

func getUnselectSchemaTableMapByTaskType(transactionCtx context.Context, taskType int, channelId int) (map[structs.SchemaTablePair]*channel.ChannelSchemaTable, error) {
	sameTaskTypeUnselectedChannleSchemaTables, err := models.GetChannelReaderWriter().GetUnselectedChannelSchemaTablesByTaskTypeAndChannelId(transactionCtx, taskType, channelId)
	if err != nil {
		log.Errorf("CreateTaskByCSV GetChannelSchemaTablesByTaskTypeAndChannelId failed. taskType:%v, channelId:%v, err:%v", taskType, channelId, err)
		return nil, err
	}
	schemaTableMap := make(map[structs.SchemaTablePair]*channel.ChannelSchemaTable)
	for _, data := range sameTaskTypeUnselectedChannleSchemaTables {
		schemaTableMap[structs.SchemaTablePair{SchemaName: strings.ToUpper(data.SchemaNameS), TableName: strings.ToUpper(data.TableNameS)}] = data
	}
	return schemaTableMap, nil
}

func createChannleSchemaTablesByCsvIfNotExist(transactionCtx context.Context, req *message.CreateTaskByCsvReq, taskModel *task.Task, csvDatas []*structs.CsvSchemaTables, schemaTableMap map[structs.SchemaTablePair]*structs.TableInfo, datasourceSchemaMap map[string]*structs.SchemaInfo) error {
	//写入channle_schema_tables(需要判断冲突)
	sameTaskTypeChannleSchemaTablesMap, err := getSchemaTableMapByTaskType(transactionCtx, req.TaskType, req.ChannelId)
	if err != nil {
		log.Errorf("getSchemaTableMapByTaskType failed. data:%v, err:%v", taskModel, err)
	}

	sameTaskTypeUnselectedChannleSchemaTablesMap, err := getUnselectSchemaTableMapByTaskType(transactionCtx, req.TaskType, req.ChannelId)
	if err != nil {
		log.Errorf("getUnselectSchemaTableMapByTaskType failed. data:%v, err:%v", taskModel, err)
	}

	var channleSchemaTables []*channel.ChannelSchemaTable
	for _, csvLine := range csvDatas {
		isConflict := false
		schemaTableMapKey := structs.SchemaTablePair{SchemaName: strings.ToUpper(csvLine.SchemaNameS), TableName: strings.ToUpper(csvLine.TableNameS)}
		if sameTypeChannelSchemaTable, ok := sameTaskTypeChannleSchemaTablesMap[schemaTableMapKey]; ok {
			if strings.EqualFold(sameTypeChannelSchemaTable.SchemaNameT, csvLine.SchemaNameT) && strings.EqualFold(sameTypeChannelSchemaTable.TableNameT, csvLine.TableNameT) {
				isConflict = true
			}
		}
		if !isConflict {
			if schemaTable, ok := schemaTableMap[schemaTableMapKey]; ok {
				newData := &channel.ChannelSchemaTable{
					ChannelId:             req.ChannelId,
					TaskId:                taskModel.TaskID,
					TaskType:              req.TaskType,
					DbNameS:               csvLine.DbNameS,
					SchemaNameS:           datasourceSchemaMap[strings.ToUpper(csvLine.SchemaNameS)].SchemaName,
					TableNameS:            schemaTable.TableName,
					DbNameT:               csvLine.DbNameT,
					SchemaNameT:           strings.ToUpper(csvLine.SchemaNameT),
					TableNameT:            strings.ToUpper(csvLine.TableNameT),
					PartitioningTypeS:     schemaTable.PartitioningType,
					PartitioningCountS:    schemaTable.PartitionCount,
					SubPartitioningTypeS:  schemaTable.SubPartitioningType,
					SubPartitioningCountS: schemaTable.SubPartitionCount,
					TableSizeM:            schemaTable.TableSizeM,
					UkS:                   schemaTable.Uk,
					PkS:                   schemaTable.Pk,
					PkT:                   schemaTable.Pk,
					PartitioningTypeT:     "N",
					ClusterTypeT:          "N",
				}
				if sameTypeUnselectedChannelSchemaTable, ok := sameTaskTypeUnselectedChannleSchemaTablesMap[schemaTableMapKey]; ok {
					newData.ChannelSchtableId = sameTypeUnselectedChannelSchemaTable.ChannelSchtableId
				}
				channleSchemaTables = append(channleSchemaTables, newData)
			}
		} else {
			log.Errorf("导入CSV与现有同类型任务配置冲突，冲突数据:%s|%s|%s|%s|%s|%s", csvLine.DbNameS, csvLine.SchemaNameS, csvLine.TableNameS, csvLine.DbNameT, csvLine.SchemaNameT, csvLine.TableNameT)
			return errors.NewErrorf(errors.TIMS_CREATE_TASK_FAILED, "导入CSV与现有同类型任务配置冲突，冲突数据:%s|%s|%s|%s|%s|%s", csvLine.DbNameS, csvLine.SchemaNameS, csvLine.TableNameS, csvLine.DbNameT, csvLine.SchemaNameT, csvLine.TableNameT)
		}
	}
	if len(channleSchemaTables) > 0 {
		channleSchemaTables, err = models.GetChannelReaderWriter().SaveChannelSchemaTables(transactionCtx, channleSchemaTables)
		if err != nil {
			log.Errorf("CreateTaskByCSV SaveChannelSchemaTables failed. channelId:%d, taskType:%d", req.ChannelId, req.TaskType)
			return err
		}
	}
	return nil
}

/*
2023-06-20
wuchao
参考已有任务创建任务
*/
func (s *Service) CreateTaskByRefTask(ctx context.Context, req *message.CreateTaskByRefTaskReq) (*message.CreateTaskResp, error) {
	log.Infof("CreateTaskByRefTask service request data:%v", req)

	taskModel := &task.Task{
		TaskType:    req.TaskType,
		TaskName:    req.TaskName,
		ChannelId:   req.ChannelId,
		TaskReftask: req.TaskRefTask,
	}
	defaultTemplates, getDefaultTemplatesErr := models.GetTemplateReaderWriter().GetAllDefaultTemplates(ctx)
	if getDefaultTemplatesErr != nil {
		log.Errorf("CreateTaskByRefTask get default template failed when create new task. data:%v, err:%v", taskModel, getDefaultTemplatesErr)
		return nil, getDefaultTemplatesErr
	}
	var (
		defaultTaskParamTemplate       *task.TaskparamTemplateConfig
		getDefaultTaskParamTemplateErr error
	)

	//非统计信息任务，查询默认参数模版
	if req.TaskType != constants.TASK_TYPE_STATISTICS {
		defaultTaskParamTemplate, getDefaultTaskParamTemplateErr = models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, taskModel.TaskType)
		if getDefaultTaskParamTemplateErr != nil {
			log.Errorf("CreateTaskByRefTask get default task param template failed when create new task. data:%v, err:%v", taskModel, getDefaultTaskParamTemplateErr)
			return nil, getDefaultTaskParamTemplateErr
		}
	}
	//对象兼容性评估-明细显示
	if req.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {
		taskModel.OnlyIncompatibleDetail = constants.ONLY_INCOMPATIBLE_DETAIL_DEFAULT
	}
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskModel.TaskStatus = constants.TASK_STATUS_NOT_RUNNING
		defaultTime, _ := time.Parse(time.RFC3339, "1970-01-01T00:00:00+08:00")
		taskModel.StartTime = defaultTime
		taskModel.EndTime = defaultTime
		chooseTemplateId(taskModel, defaultTemplates)
		if defaultTaskParamTemplate != nil {
			taskModel.TaskParamTmplateId = defaultTaskParamTemplate.TaskparamTemplateID
		}
		taskModel, err := models.GetTaskReaderWriter().CreateTask(transactionCtx, taskModel)
		if err != nil {
			log.Errorf("CreateTaskByRefTask create new task failed. data:%v, err:%v", taskModel, err)
			return err
		}

		//不写入channel_schema

		//写入channle_schema_tables
		// var channleSchemaTables []*channel.ChannelSchemaTable

		// C D ~ task_id = ref_task_id
		channleSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(transactionCtx, req.TaskRefTask)
		if err != nil {
			log.Errorf("CreateTaskByRefTask GetChannelSchemaTablesByTaskId failed. data:%v, err:%v", taskModel, err)
			return err
		}

		// A B C D ~ task_id = 0
		sameTypeChannleSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskTypeAndChannelId(transactionCtx, req.TaskType, req.ChannelId)
		if err != nil {
			log.Errorf("CreateTaskByRefTask GetChannelSchemaTablesByTaskTypeAndChannelId failed. data:%v, err:%v", taskModel, err)
		}

		//对channelSchemaTables校验冲突,更新字段
		var newChannelSchemaTables []*channel.ChannelSchemaTable
		for _, data := range channleSchemaTables {
			isConflict := false
			for _, conflictData := range sameTypeChannleSchemaTables {
				if data.SchemaNameS == conflictData.SchemaNameS && data.TableNameS == conflictData.TableNameS {
					isConflict = true
				}
			}
			if !isConflict {
				data.TaskId = taskModel.TaskID
				data.TaskType = taskModel.TaskType
				data.ChannelSchtableId = 0
				data.CreatedAt = time.Now()
				newChannelSchemaTables = append(newChannelSchemaTables, data)
			} else {
				log.Errorf("创建任务与现有同类型任务配置冲突，冲突数据:%s|%s|%s|%s|%s|%s", data.DbNameS, data.SchemaNameS, data.TableNameS, data.DbNameT, data.SchemaNameT, data.TableNameT)
				return errors.NewErrorf(errors.TIMS_CREATE_TASK_FAILED, "创建任务与现有同类型任务配置冲突，冲突数据:%s|%s|%s|%s|%s|%s", data.DbNameS, data.SchemaNameS, data.TableNameS, data.DbNameT, data.SchemaNameT, data.TableNameT)
			}
		}

		for _, newChannelSchemaTable := range newChannelSchemaTables {
			deleteErr := models.GetChannelReaderWriter().DeleteChannelSchemaTablesByTaskTypeAndChannelIdAndSchemaTableName(transactionCtx, req.TaskType, req.ChannelId, newChannelSchemaTable.SchemaNameS, newChannelSchemaTable.TableNameS)
			if deleteErr != nil {
				log.Errorf("CreateTaskByRefTask DeleteChannelSchemaTablesByTaskTypeAndChannelIdAndSchemaTableName failed. data:%v, err:%v", taskModel, err)
				return deleteErr
			}
		}

		if len(newChannelSchemaTables) > 0 {
			_, err = models.GetChannelReaderWriter().SaveChannelSchemaTables(transactionCtx, newChannelSchemaTables)
			if err != nil {
				log.Errorf("CreateTaskByRefTask SaveChannelSchemaTables failed. channelId:%d, taskType:%d", req.ChannelId, req.TaskType)
				return err
			}
		}

		//配置channel_schema_objects
		if req.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "Y", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "N"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByRefTask create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByRefTask create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByRefTask create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		if req.TaskType == constants.TASK_TYPE_INCREMENT {
			channelObject := &channel.ChannelSchemaObject{TaskId: taskModel.TaskID, ChannelId: req.ChannelId, OnlyTableandindex: "N", OnlyTable: "N", OnlyIndex: "N", Partitiontable: "N", AppendData: "N", ReloadData: "N", LoadData: "Y"}
			_, saveChannelObjectsErr := models.GetChannelReaderWriter().CreateChannelSchemaObject(transactionCtx, channelObject)
			if saveChannelObjectsErr != nil {
				log.Errorf("CreateTaskByRefTask create default channel schema object failed. err:%v", saveChannelObjectsErr)
				return saveChannelObjectsErr
			}
		}
		// create object parser incompatible features
		if req.TaskType == constants.TASK_TYPE_OBJ_PARSER {

			features, getErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(transactionCtx)
			if getErr != nil {
				log.Errorf("ListBasicOracleIncompatibleFeature failed. err:%v", getErr)
				return getErr
			}
			taskFeatures := lo.Map(features, func(feature *objectparser.OracleIncompatibleFeature, _ int) *objectparser.OracleTaskIncompatibleFeature {
				return &objectparser.OracleTaskIncompatibleFeature{
					TaskId:       taskModel.TaskID,
					FeatureType:  feature.FeatureType,
					FeatureKey:   feature.FeatureKey,
					FeatureScore: feature.FeatureScore,
					FeatureDesc:  feature.FeatureDesc,
				}
			})
			createErr := models.GetObjectParserWriter().CreateTaskOracleIncompatibleFeatures(transactionCtx, taskFeatures)
			if createErr != nil {
				log.Errorf("create object parser incompatible features failed. data:%v, err:%s", taskFeatures, createErr)
				return createErr
			}
		}

		// create sqlanalyze env deploy tasks
		if req.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {
			sqlAnalyzeTasks := buildSQLAnalyzeEnvDeployTasks(taskModel)
			createErr := models.GetSqlAnalyzerReaderWriter().BatchCreateEnvDeployTask(transactionCtx, sqlAnalyzeTasks)
			if createErr != nil {
				log.Errorf("create default sqlanalyze tasks failed. data:%v, err:%s", sqlAnalyzeTasks, createErr)
				return createErr
			}

			channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(transactionCtx, req.ChannelId)
			if err != nil {
				log.Errorf("get channel schemas failed. channelId:%d, err:%v", req.ChannelId, err)
				return err
			}
			channelSchemaStrs := lo.Map(channelSchemas, func(channelSchema *channel.ChannelSchema, _ int) string {
				return channelSchema.SchemaNameS
			})
			if len(channelSchemaStrs) != 0 {
				_, saveErr := models.GetTaskReaderWriter().BatchSaveTaskSubParamConfigs(transactionCtx, buildSQLIncludingSchemaParam(taskModel, lo.Uniq(channelSchemaStrs)))
				if saveErr != nil {
					log.Errorf("save sqlanalyzer task param config failed, taskId:%d, err:%v", taskModel.TaskID, saveErr)
					return saveErr
				}
			}
		}

		// create data compare env deploy tasks
		if req.TaskType == constants.TASK_TYPE_DATA_COMPARE {
			envDeployTasks := buildDataCompareEnvDeployTasks(taskModel)
			createErr := models.GetDataCompareReaderWriter().BatchCreateEnvDeployTask(transactionCtx, envDeployTasks)
			if createErr != nil {
				log.Errorf("create data compare env deploy tasks failed. data:%v, err:%s", envDeployTasks, createErr)
				return createErr
			}
		}

		return nil
	})
	if err != nil {
		log.Errorf("CreateTaskByRefTask failed. err:%v", err)
		return nil, err
	}
	log.Infof("CreateTaskByRefTask successfully.taskId:%d", taskModel.TaskID)
	return &message.CreateTaskResp{}, nil
}

func (s *Service) ParsingCSVForTaskTableConfigs(ctx context.Context, channelId int, taskId int, filePath string) ([]*message.TaskTableConfig, error) {
	log.Infof("ParsingCSVForTaskTableConfigs service request channel id : %d, task id:%v, filePath:%v", channelId, taskId, filePath)

	csvFile, _ := os.Open(filePath)
	reader := csv.NewReader(bufio.NewReader(csvFile))
	lineCount := 0

	var taskTabCfgs []*message.TaskTableConfig
	for {
		line, error := reader.Read()
		if error == io.EOF {
			break
		} else if error != nil {
			log.Fatal(error)
		}
		lineCount++
		if lineCount > 1 { //跳过第一行表头
			for i := 0; i < len(line); i++ {
				log.Infof("csv data: %d : %s", i, line[i])
			}
			if len(line) < 14 {
				log.Errorf("csv data error at line %d", lineCount)
				continue
			}
			cfg := &message.TaskTableConfig{
				ChannelId:   channelId,
				TaskId:      taskId,
				DbNameS:     line[0],
				SchemaNameS: line[1],
				TableNameS:  line[2],
				DbNameT:     line[3],
				SchemaNameT: line[4],
				TableNameT:  line[5],
				OperatorTag: line[6],

				ColumnslistOracle: line[7],
				ColumnslistTidb:   line[8],

				FilterClauseOracle: line[9],
				FilterClauseTidb:   line[10],

				SqlhintOracle: line[11],
				SqlhintTidb:   line[12],

				EnableChunkSplit: line[13],
				BaseFields:       &message.BaseFields{Comment: line[14]},
			}
			taskTabCfgs = append(taskTabCfgs, cfg)
		}
	}
	return taskTabCfgs, nil
}

func (s *Service) FixTaskStatus(ctx context.Context, req *message.FixTaskStatusReq) (*message.CommonResp, error) {
	log.Infof("FixTaskStatus service request: %v", req)

	for _, taskId := range req.TaskIdS {
		err := fixTaskStatusByTaskId(ctx, taskId)
		if err != nil {
			return nil, err
		}
	}

	return &message.CommonResp{}, nil
}

func fixTaskStatusByTaskId(ctx context.Context, taskId int) error {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		return err
	}
	if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING || taskInfo.TaskStatus == constants.TASK_STATUS_FAILED {
		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		taskInfo.ErrorDetail = "Manually modifying task status."
		taskInfo.EndTime = time.Now()
		_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
		if err != nil {
			return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to running failed.", taskInfo.TaskID)
		}
		if taskInfo.TaskType == constants.TASK_TYPE_OBJ_ASSESSMENT {

		} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_STRUCTURE {
			models.GetStructureMigrationReaderWriter().ResetTableResultDetail(ctx, taskId)
		} else if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_FULL_DATA || taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			log.Infof("reset table migration summary and detail. task id:%d", taskInfo.TaskID)
			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				errS := models.GetFullDataMigrationReaderWriter().ResetTableMigrationSummary(transactionCtx,
					&migmodels.TableMigrationSummary{
						TaskID: taskInfo.TaskID,
					})
				if errS != nil {
					return errS
				}

				errD := models.GetFullDataMigrationReaderWriter().ResetTableMigrationDetail(transactionCtx,
					&migmodels.TableMigrationDetail{
						TaskID: taskInfo.TaskID,
					})
				if errD != nil {
					return errD
				}
				return nil
			})
			if err != nil {
				return err
			}
		} else if taskInfo.TaskType == constants.TASK_TYPE_DATA_COMPARE {
			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				errS := models.GetDataCompareReaderWriter().ResetCompareSummary(transactionCtx,
					taskId)
				if errS != nil {
					return errS
				}

				errD := models.GetDataCompareReaderWriter().ResetDataCompareChunks(transactionCtx,
					taskId)
				if errD != nil {
					return errD
				}
				return nil
			})
			if err != nil {
				return err
			}
		} else if taskInfo.TaskType == constants.TASK_TYPE_SQL_ASSESSMENT {

		} else if taskInfo.TaskType == constants.TASK_TYPE_STATISTICS {

		}
	}
	return nil
}

func (s *Service) VerifySplitTask(ctx context.Context, req *message.VerifySplitTaskReq) (*message.VerifySplitTaskResp, *message.Page, error) {
	log.Infof("VerifySplitTask service request: %v", req)

	taskIds := req.TaskIds
	channelId := req.ChannelId

	// 如果勾选的任务只有一个，不需要校验
	if len(taskIds) <= 1 {
		return &message.VerifySplitTaskResp{}, &message.Page{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    0,
		}, nil
	}

	taskId := taskIds[0]

	diffCountTables, err := verifyTaskChannelSchemaTables(ctx, taskIds)
	if err != nil {
		return nil, &message.Page{
			Page:     req.Page,
			PageSize: req.PageSize,
			Total:    0,
		}, err
	}

	allTableSchemas, getSchemasErr := models.GetChannelReaderWriter().GetChannelSchemasByTaskId(ctx, taskId)
	if getSchemasErr != nil {
		log.Errorf("get channel schemas by taskId failed, taskId:%d, err:%v", taskId, getSchemasErr)
		return nil, nil, getSchemasErr
	}
	schemaTableSizeMapping := make(map[structs.SchemaTablePair]float64)
	channelInfo, _ := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if channelInfo != nil {
		datasourceS, _ := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
		if datasourceS != nil && (datasourceS.DbType == constants.DB_TYPE_ORACLE || datasourceS.DbType == constants.DB_TYPE_ORACLE_ADG) {
			var getErr error
			schemaTableSizeMapping, getErr = common.GetSchemasTablesSegmentSize(ctx, channelId, taskId, allTableSchemas)
			if getErr != nil {
				log.Errorf("get schema table size failed, taskId:%d, err:%v", taskId, getErr)
				return nil, nil, getErr
			}
		}
	}

	diffCountTablesLen := len(diffCountTables)

	if diffCountTablesLen >= 1 {

		pageNumber := req.Page
		pageSize := req.PageSize

		diffCountTables = lo.Subset(diffCountTables, (pageNumber-1)*pageSize, uint(pageNumber*pageSize))

		tableList := lo.Map(diffCountTables, func(table structs.SplitSchemaTable, _ int) message.SplitSchemaTable {
			return message.SplitSchemaTable{
				SchemaName: table.SchemaName,
				TableName:  table.TableName,
				TaskId:     table.TaskId,
				TaskName:   table.TaskName,
				TaskType:   table.TaskType,
				TableSizeM: schemaTableSizeMapping[structs.SchemaTablePair{SchemaName: table.SchemaName, TableName: table.TableName}],
			}
		})

		return &message.VerifySplitTaskResp{
				IsFailed:   true,
				Message:    "勾选的任务中，表不一致，无法拆分",
				DiffTables: tableList,
			}, &message.Page{
				Page:     req.Page,
				PageSize: req.PageSize,
				Total:    int64(diffCountTablesLen),
			}, nil
	}

	return &message.VerifySplitTaskResp{}, &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    0,
	}, nil
}

func (s *Service) SplitTask(ctx context.Context, req *message.SplitTaskReq) (*message.SplitTaskResp, error) {
	log.Infof("SplitTask service request: %v", req)

	channelId := req.ChannelId
	taskIds := req.TaskIds

	hasInvalidStatusTask := false
	for _, taskId := range taskIds {
		taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
		if err != nil {
			log.Errorf("get task failed. taskId:%d, err:%v", taskId, err)
			return nil, err
		}
		if taskInfo.TaskStatus == constants.TASK_STATUS_NOT_SETUP || taskInfo.TaskStatus == constants.TASK_STATUS_NOT_RUNNING {
			continue
		} else {
			hasInvalidStatusTask = true
			break
		}
	}
	if hasInvalidStatusTask {
		return &message.SplitTaskResp{
			IsFailed: true,
			Message:  "勾选的任务已运行过，禁止拆分。",
		}, nil
	}
	if config.GetGlobalConfig().IsClusterMode() {
		hasSubTasks := false
		for _, taskId := range taskIds {
			subTasks, err := models.GetTaskReaderWriter().BatchGetSubTasksByParentTaskId(ctx, req.ChannelId, taskId)
			if err != nil {
				log.Errorf("get sub tasks failed. taskId:%d, err:%v", taskId, err)
				return nil, err
			}
			if len(subTasks) > 0 {
				hasSubTasks = true
				break
			}
		}
		if hasSubTasks {
			return &message.SplitTaskResp{
				IsFailed: true,
				Message:  "勾选的任务中，存在子任务，禁止拆分。",
			}, nil
		}
	}

	if req.SplitNum <= 1 {
		return &message.SplitTaskResp{
			IsFailed: true,
			Message:  "拆分数量小于等于1，无需拆分。",
		}, nil
	}

	diffCountTables, err := verifyTaskChannelSchemaTables(ctx, taskIds)
	if err != nil {
		return nil, err
	}

	if len(diffCountTables) >= 1 {
		return nil, errors.NewErrorf(errors.TIMS_SPLIT_TASK_FAILED, "勾选的任务中，表不一致，无法拆分")
	}

	taskId := taskIds[0]
	taskAllTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		log.Errorf("get channel schema allTables failed. taskId:%d, err:%v", taskId, err)
		return nil, err
	}

	splitNum := req.SplitNum
	if splitNum > len(taskAllTables) {
		splitNum = len(taskAllTables)
	}
	if splitNum <= 1 {
		return &message.SplitTaskResp{
			IsFailed: true,
			Message:  "表数量小于等于1，无需拆分。",
		}, nil
	}

	allTableSchemas := make([]string, 0)
	for _, table := range taskAllTables {
		allTableSchemas = append(allTableSchemas, table.SchemaNameS)
	}
	allTableSchemas = lo.Uniq(allTableSchemas)
	if len(allTableSchemas) == 0 {
		return &message.SplitTaskResp{
			IsFailed: true,
			Message:  "查找SCHEMA信息失败。",
		}, nil
	}

	schemaTableSizeMapping := make(map[structs.SchemaTablePair]float64)
	channelInfo, _ := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if channelInfo != nil {
		datasourceS, _ := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
		if datasourceS != nil && (datasourceS.DbType == constants.DB_TYPE_ORACLE || datasourceS.DbType == constants.DB_TYPE_ORACLE_ADG) {
			var getErr error
			schemaTableSizeMapping, getErr = common.GetSchemasTablesSegmentSize(ctx, channelId, taskId, allTableSchemas)
			if getErr != nil {
				log.Errorf("get schema table size failed, taskId:%d, err:%v", taskId, getErr)
				return nil, getErr
			}
		}
	}

	for _, taskId := range taskIds {
		newTasks := make([]*task.Task, 0)
		allTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
		if getTableErr != nil {
			log.Errorf("get channel schema allTables failed. taskId:%d, err:%v", taskId, getTableErr)
			return nil, getTableErr
		}

		log.Infof("split task tables, setting table size, taskId:%d", taskId)
		for idx, table := range allTables {
			pair := structs.SchemaTablePair{SchemaName: table.SchemaNameS, TableName: table.TableNameS}
			tableSizeM := schemaTableSizeMapping[pair]
			log.Infof("split task tables, setting table size, taskId:%d, schema:%s, table:%s, size:%f", taskId, table.SchemaNameS, table.TableNameS, tableSizeM)
			allTables[idx].TableSizeM = tableSizeM
		}

		bucketIndex, tableBuckets, tableDisplayBuckets := splitTableIntoBucketInZOrder(allTables, splitNum)
		log.Infof("taskIds:%v, bucketIndex:%v, tableDisplayBuckets:%v", taskIds, bucketIndex, tableDisplayBuckets)

		taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
		if err != nil {
			log.Errorf("get task failed. taskId:%d, err:%v", taskId, err)
			return nil, err
		}
		customTaskCfgs, err := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
			ChannelID:           taskInfo.ChannelId,
			TaskID:              taskInfo.TaskID,
			TaskparamTemplateID: taskInfo.TaskParamTmplateId,
		})
		if err != nil {
			log.Errorf("get task param config failed. taskId:%d, err:%v", taskId, err)
			return nil, err
		}

		channelSchemaObject, err := models.GetChannelReaderWriter().GetChannelSchemaObjectByTaskId(ctx, taskId)
		if err != nil {
			log.Errorf("get channel schema object failed. taskId:%d, err:%v", taskId, err)
			return nil, err
		}
		for i := 0; i < splitNum; i++ {
			newTask := buildAdjacentTasksWithTaskName(taskInfo, taskInfo.TaskName+"_"+strconv.Itoa(i+1))
			newTasks = append(newTasks, newTask)
		}

		trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
			newChannelSchemaObjects := make([]*channel.ChannelSchemaObject, 0)
			newCustTaskParamConfigs := make([]*task.TaskParamConfig, 0)
			newChannelSchemaTables := make([]*channel.ChannelSchemaTable, 0)

			newAdjacentTasks, createTaskErr := models.GetTaskReaderWriter().BatchCreateTask(trxCtx, newTasks)
			if createTaskErr != nil {
				return createTaskErr
			}

			for idx, newAdjacentTask := range newAdjacentTasks {
				if channelSchemaObject != nil {
					newChannelSchemaObjects = append(newChannelSchemaObjects, &channel.ChannelSchemaObject{
						TaskId:            newAdjacentTask.TaskID,
						ChannelId:         channelSchemaObject.ChannelId,
						OnlyTableandindex: channelSchemaObject.OnlyTableandindex,
						OnlyTable:         channelSchemaObject.OnlyTable,
						OnlyIndex:         channelSchemaObject.OnlyIndex,
						Partitiontable:    channelSchemaObject.Partitiontable,
						AppendData:        channelSchemaObject.AppendData,
						ReloadData:        channelSchemaObject.ReloadData,
						LoadData:          channelSchemaObject.LoadData,
					})
				}
				for _, customTaskCfg := range customTaskCfgs {
					newCustTaskParamConfigs = append(newCustTaskParamConfigs, &task.TaskParamConfig{
						TaskID:              newAdjacentTask.TaskID,
						ChannelID:           customTaskCfg.ChannelID,
						ParamName:           customTaskCfg.ParamName,
						ParamValueCurrent:   customTaskCfg.ParamValueCurrent,
						ParamValueDefault:   customTaskCfg.ParamValueDefault,
						TaskparamTemplateID: customTaskCfg.TaskparamTemplateID,
						HasSubParams:        customTaskCfg.HasSubParams,
					})
				}
				log.Infof("newAdjacentTask:%d, splitIndex:%d, tableLen:%d, tables:%v", newAdjacentTask.TaskID, idx, len(tableDisplayBuckets[idx]), tableDisplayBuckets[idx])
				channelSchemaTables := tableBuckets[idx]
				for tableIdx := range channelSchemaTables {
					channelSchemaTables[tableIdx].TaskId = newAdjacentTask.TaskID
					channelSchemaTables[tableIdx].TableSizeM = 0
				}
				newChannelSchemaTables = append(newChannelSchemaTables, channelSchemaTables...)
			}
			if len(newCustTaskParamConfigs) > 0 {
				_, createCustomConfigErr := models.GetTaskReaderWriter().SaveTaskParamConfigs(trxCtx, newCustTaskParamConfigs)
				if createCustomConfigErr != nil {
					return createCustomConfigErr
				}
			}
			if len(newChannelSchemaObjects) > 0 {
				_, createObjErr := models.GetChannelReaderWriter().CreateChannelSchemaObjects(trxCtx, newChannelSchemaObjects)
				if createObjErr != nil {
					return createObjErr
				}
			}
			if len(newChannelSchemaTables) > 0 {
				_, createTableErr := SaveChannelSchemaTablesInBatchs(trxCtx, newChannelSchemaTables)
				if createTableErr != nil {
					return createTableErr
				}
			}
			err := models.GetTaskReaderWriter().BatchDeleteTasksByIds(trxCtx, []int{taskId})
			if err != nil {
				return err
			}
			err = models.GetChannelReaderWriter().UnSelectChannelSchemaTablesByTaskId(trxCtx, []int{taskId})
			if err != nil {
				return err
			}
			return nil
		})
		if trxErr != nil {
			log.Errorf("split task in transaction failed. taskId:%d, err:%v", taskId, trxErr)
			return nil, trxErr
		}

	}

	return &message.SplitTaskResp{
		IsFailed: false,
		Message:  "拆分成功",
	}, nil
}

func SaveChannelSchemaTablesInBatchs(ctx context.Context, newChannelSchemaTables []*channel.ChannelSchemaTable) ([]*channel.ChannelSchemaTable, error) {
	// 设置每个小批次的大小
	batchSize := 1000

	// 拆分成小批次并保存
	for i := 0; i < len(newChannelSchemaTables); i += batchSize {
		j := i + batchSize
		if j > len(newChannelSchemaTables) {
			j = len(newChannelSchemaTables)
		}

		// 创建小批次
		batch := newChannelSchemaTables[i:j]

		_, createTableErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(ctx, batch)
		if createTableErr != nil {
			return nil, createTableErr
		}
	}

	return newChannelSchemaTables, nil
}

func splitTableIntoBucketInZOrder(allTables []*channel.ChannelSchemaTable, splitNum int) ([]int, map[int][]*channel.ChannelSchemaTable, map[int][]string) {
	sort.Slice(allTables, func(i, j int) bool {
		if allTables[i].TableSizeM == allTables[j].TableSizeM {
			if allTables[i].SchemaNameS == allTables[j].SchemaNameS {
				return allTables[i].TableNameS < allTables[j].TableNameS
			} else {
				return allTables[i].SchemaNameS < allTables[j].SchemaNameS
			}
		}
		return allTables[i].TableSizeM > allTables[j].TableSizeM
	})

	bucketIndex := fillBucketIndex(splitNum, len(allTables))

	// 按照不同大小排序，并且依次放入不同的桶中，其中桶的大小为splitNum
	tableBuckets := make(map[int][]*channel.ChannelSchemaTable, 0)
	tableDisplayBuckets := make(map[int][]string, 0)
	for idx, table := range allTables {
		currentBucketIndex := bucketIndex[idx]
		if len(tableBuckets[currentBucketIndex]) == 0 {
			tableDisplayBuckets[currentBucketIndex] = make([]string, 0)
			tableBuckets[currentBucketIndex] = make([]*channel.ChannelSchemaTable, 0)
		}
		tableDisplayBuckets[currentBucketIndex] = append(tableDisplayBuckets[currentBucketIndex], table.TableNameS)
		tableBuckets[currentBucketIndex] = append(tableBuckets[currentBucketIndex], table)
	}
	return bucketIndex, tableBuckets, tableDisplayBuckets
}

func fillBucketIndex(splitNum, size int) []int {
	array := make([]int, size)
	forward := true
	count := 0

	for i := 0; i < size; i++ {
		array[i] = count
		if forward {
			count++
			if count > splitNum-1 {
				forward = false
				count = splitNum - 1
			}
		} else {
			count--
			if count < 0 {
				forward = true
				count = 0
			}
		}
	}
	return array
}

// buildAdjacentTasksWithTaskName
func buildAdjacentTasksWithTaskName(t *task.Task, taskName string) *task.Task {
	return &task.Task{
		TaskType:               t.TaskType,
		TaskName:               taskName,
		StartTime:              timeutil.GetTMSNullTime(),
		EndTime:                timeutil.GetTMSNullTime(),
		TaskStatus:             constants.TASK_STATUS_NOT_RUNNING,
		ServerId:               "", // 置空
		TaskSeq:                t.TaskSeq,
		RunParams:              "", // 置空，防止意外
		ChannelId:              t.ChannelId,
		TaskParamTmplateId:     t.TaskParamTmplateId,
		TabcolmapTmplateId:     t.TabcolmapTmplateId,
		ColdefaultmapTmplateId: t.ColdefaultmapTmplateId,
		ObjmapTmplateId:        t.ObjmapTmplateId,
		SqlmapTmplateId:        t.SqlmapTmplateId,
		ErrorDetail:            "",
		TaskObjRef:             t.TaskObjRef,
		TaskReftask:            t.TaskReftask,
		TaskWarning:            t.TaskWarning,
		OnlyIncompatibleDetail: t.OnlyIncompatibleDetail,
		ParentTaskID:           t.ParentTaskID,
	}
}

func verifyTaskChannelSchemaTables(ctx context.Context, taskIds []int) ([]structs.SplitSchemaTable, error) {
	// 检查勾选的任务的表是否一致
	tableOccurrenceCount := make(map[structs.SplitSchemaTable][]*channel.ChannelSchemaTable)
	allTaskInfos := make([]*task.Task, 0)
	allTaskMap := make(map[int]*task.Task)
	for _, taskId := range taskIds {
		taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
		if err != nil {
			log.Errorf("get task failed. taskId:%d, err:%v", taskId, err)
			return nil, err
		}
		allTaskInfos = append(allTaskInfos, taskInfo)
		allTaskMap[taskId] = taskInfo

		tables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
		if err != nil {
			log.Errorf("get channel schema tables failed. taskId:%d, err:%v", taskId, err)
			return nil, err
		}
		for _, table := range tables {
			pair := structs.SplitSchemaTable{
				SchemaName: table.SchemaNameS,
				TableName:  table.TableNameS,
				TableSizeM: table.TableSizeM,
			}
			if _, ok := tableOccurrenceCount[pair]; !ok {
				tableOccurrenceCount[pair] = make([]*channel.ChannelSchemaTable, 0)
			}
			tableOccurrenceCount[pair] = append(tableOccurrenceCount[pair], table)
		}
	}

	diffCountTables := make([]structs.SplitSchemaTable, 0)
	for schemaTable, tables := range tableOccurrenceCount {
		if len(tables) == len(taskIds) {
			continue
		}
		hitTaskInfos := lo.Map(tables, func(table *channel.ChannelSchemaTable, _ int) *task.Task {
			return allTaskMap[table.TaskId]
		})

		missedTaskInfos, _ := lo.Difference(allTaskInfos, hitTaskInfos)
		for _, missedTaskInfo := range missedTaskInfos {
			diffCountTables = append(diffCountTables, structs.SplitSchemaTable{
				SchemaName: schemaTable.SchemaName,
				TableName:  schemaTable.TableName,
				TableSizeM: schemaTable.TableSizeM,

				TaskId:   missedTaskInfo.TaskID,
				TaskName: missedTaskInfo.TaskName,
				TaskType: missedTaskInfo.TaskType,
			})
		}
	}

	sort.Slice(diffCountTables, func(i, j int) bool {
		// order by TableSizeM, schemaName, tableName
		if diffCountTables[i].TableSizeM == diffCountTables[j].TableSizeM {
			if diffCountTables[i].SchemaName == diffCountTables[j].SchemaName {
				return diffCountTables[i].TableName < diffCountTables[j].TableName
			} else {
				return diffCountTables[i].SchemaName < diffCountTables[j].SchemaName
			}
		}
		return diffCountTables[i].TableSizeM > diffCountTables[j].TableSizeM
	})
	return diffCountTables, nil
}

func buildTaskMessageFromModelInClusterMode(ctx context.Context, taskModel *task.Task) *message.Task {
	var startTime = taskModel.StartTime
	var endTime = taskModel.EndTime
	var taskStatus = taskModel.TaskStatus

	var isAllSubTaskDone = true // means FINISH OR FAILED
	var hasFailedTask = false
	var hasRunningOrQueuedTask = false

	var subTasks []*task.Task
	var err error

	switch taskModel.TaskType {
	case constants.TASK_TYPE_MIGRATE_FULL_DATA, constants.TASK_TYPE_MIGRATE_CSV_DATA:
		subTasks, err = models.GetTaskReaderWriter().BatchGetSubTasksByParentTaskId(ctx, taskModel.ChannelId, taskModel.TaskID)
		if err != nil {
			log.Errorf("get sub task ids failed. channelId:%d, taskIds:%v, err: %v", taskModel.ChannelId, taskModel.TaskID, err)
		}
	}

	totalSubTaskNum := len(subTasks)
	finishSubTaskNum := 0
	if len(subTasks) != 0 {
		startTime = time.Now()
		endTime = subTasks[0].EndTime

		// get minimal start time and maximal end time
		for _, subTask := range subTasks {
			if subTask.StartTime.Year() != 1970 && !subTask.StartTime.IsZero() && subTask.StartTime.Before(startTime) {
				startTime = subTask.StartTime
			}
			if subTask.EndTime.Year() != 1970 && !subTask.EndTime.IsZero() && subTask.EndTime.After(endTime) {
				endTime = subTask.EndTime
			}
			if subTask.TaskStatus != constants.TASK_STATUS_FAILED && subTask.TaskStatus != constants.TASK_STATUS_FINISH {
				isAllSubTaskDone = false
			}
			if subTask.TaskStatus == constants.TASK_STATUS_FAILED {
				hasFailedTask = true
			}
			if subTask.TaskStatus == constants.TASK_STATUS_FINISH {
				finishSubTaskNum++
			}
			if subTask.TaskStatus == constants.TASK_STATUS_QUEUED || subTask.TaskStatus == constants.TASK_STATUS_RUNNING {
				hasRunningOrQueuedTask = true
			}
		}
		if !isAllSubTaskDone {
			endTime = timeutil.GetTMSNullTime()
		}
		log.Infof("parentTaskId:%d, subTasks:%d, minimal startTime:%v, maximal endTime:%v", taskModel.TaskID, len(subTasks), startTime, endTime)
	}

	if totalSubTaskNum != 0 && totalSubTaskNum == finishSubTaskNum {
		taskStatus = constants.TASK_STATUS_FINISH
	} else if hasFailedTask {
		taskStatus = constants.TASK_STATUS_FAILED
	} else if hasRunningOrQueuedTask || finishSubTaskNum != 0 {
		taskStatus = constants.TASK_STATUS_RUNNING
	}

	return &message.Task{
		TaskID:                  taskModel.TaskID,
		TaskType:                taskModel.TaskType,
		TaskName:                taskModel.TaskName,
		StartTime:               startTime,
		EndTime:                 endTime,
		TaskStatus:              taskStatus,
		ChannelId:               taskModel.ChannelId,
		TaskParamTemplateId:     taskModel.TaskParamTmplateId,
		TabcolmapTemplateId:     taskModel.TabcolmapTmplateId,
		ObjmapTemplateId:        taskModel.ObjmapTmplateId,
		SqlmapTemplateId:        taskModel.SqlmapTmplateId,
		ColDefaultMapTemplateId: taskModel.ColdefaultmapTmplateId,
		TaskSeq:                 taskModel.TaskSeq,
		ServerId:                taskModel.ServerId,
		ErrorDetail:             taskModel.ErrorDetail,
		IncrementId:             taskModel.IncrementId,
		TaskObjRef:              taskModel.TaskObjRef,
		TaskReftask:             taskModel.TaskReftask,
		TaskWarning:             taskModel.TaskWarning,
		OnlyIncompatibleDetail:  taskModel.OnlyIncompatibleDetail,
		ScnNumber:               taskModel.ScnNumber,
		ParentTaskId:            taskModel.ParentTaskID,
		Progress:                taskModel.Progress,
		BaseFields:              common.BuildBaseFieldsMessageFromModel(taskModel.Entity),
	}
}

func buildSQLIncludingSchemaParam(taskModel *task.Task, channelSchemaStrs []string) []*task.TaskSubParamConfig {
	return lo.Map(channelSchemaStrs, func(channelSchemaStr string, _ int) *task.TaskSubParamConfig {
		return &task.TaskSubParamConfig{
			TaskparamTemplateID: 2, // id 在tms_init.sql中，这里只能写死
			ChannelID:           taskModel.ChannelId,
			TaskID:              taskModel.TaskID,
			ParamName:           "including_schema",
			ParamValue:          channelSchemaStr,
			Entity:              &commonmodels.Entity{Comment: "采集SQL的SCHEMA"},
		}
	})
}

func deleteLightningTableMetas(ctx context.Context, taskInfo *task.Task) (*message.BatchDeleteSubTasksByIdResp, error) {
	log.Infof("delete lightning table metas, task id: %d", taskInfo.TaskID)
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel by channel id failed. channelId: %d, err:%s", taskInfo.ChannelId, err)
		return nil, err
	}

	targetDBConn, err := migrationpkg.SetUpTargetDatabaseConn(ctx, channelInfo)
	if err != nil {
		log.Errorf("set up target database conn failed, taskId: %d, err:%s", taskInfo.TaskID, err)
		return nil, err
	}

	// task params merge
	defaultTaskCfgs, err := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, taskInfo.TaskParamTmplateId)
	if err != nil {
		return nil, fmt.Errorf("get task params detail info failed: %v", err)
	}
	taskParamMap := make(map[string]string)

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, err := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.TaskID,
		TaskID:              taskInfo.ChannelId,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if err != nil {
		return nil, fmt.Errorf("get task params info failed: %v", err)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}
	metaSchemaName, ok := taskParamMap[constants.ParamsCsvLightningMetaSchemaName]
	if !ok {
		return nil, fmt.Errorf("get task params info failed, lightning.meta-schema-name is empty")
	}

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
	if err != nil {
		log.Errorf("get channel schema tables failed. err:%v", err)
		return nil, err
	}
	tableNames := make([]string, 0, len(channelSchemaTables)*2)
	for _, channelSchemaTable := range channelSchemaTables {
		tableNames = append(tableNames, fmt.Sprintf("`%s`.`%s`", strings.ToLower(channelSchemaTable.SchemaNameS), strings.ToLower(channelSchemaTable.TableNameS)))
		tableNames = append(tableNames, fmt.Sprintf("`%s`.`%s`", strings.ToUpper(channelSchemaTable.SchemaNameS), strings.ToUpper(channelSchemaTable.TableNameS)))

		// delete lightning table meta, in batch
		if len(tableNames) >= 40 {
			const deleteMetaSQL = `DELETE FROM %s.table_meta WHERE table_name IN (%s)`
			deleteSQL := fmt.Sprintf(deleteMetaSQL, metaSchemaName, `"`+strings.Join(tableNames, `","`)+`"`)
			log.Infof("BatchDeleteSubTasksById, delete meta sql: %s", deleteSQL)
			_, execErr := targetDBConn.MySQLDB.ExecContext(ctx, deleteSQL)
			if execErr != nil {
				log.Errorf("delete meta failed. sql:%s, err:%s", deleteSQL, execErr)
				return nil, execErr
			}
		}
		tableNames = tableNames[:0]
	}
	if len(tableNames) > 0 {
		const deleteMetaSQL = `DELETE FROM %s.table_meta WHERE table_name IN (%s)`
		deleteSQL := fmt.Sprintf(deleteMetaSQL, metaSchemaName, `"`+strings.Join(tableNames, `","`)+`"`)
		log.Infof("BatchDeleteSubTasksById, delete meta sql: %s", deleteSQL)
		_, execErr := targetDBConn.MySQLDB.ExecContext(ctx, deleteSQL)
		if execErr != nil {
			log.Errorf("delete meta failed. sql:%s, err:%s", deleteSQL, execErr)
			return nil, execErr
		}
	}

	return nil, nil
}

func (s *Service) DeleteSourceTableColumns(ctx context.Context, req *message.DeleteSourceTableColumnsReq) (*message.DeleteSourceTableColumnsResp, error) {
	log.Infof("DeleteSourceTableColumns service request: %v", req)
	channelId := req.ChannelId

	deleteQuery := &channel.TableColumnCustomMapRule{
		ChannelId:   channelId,
		SchemaNameS: req.SchemaName,
		TableNameS:  req.TableName,
		ColumnNameS: req.ColumnName,
	}

	var (
		affectedColumnNum  int64
		affectedSummaryNum int64
		deleteRuleErr      error
		deleteSummaryErr   error
	)

	deleteQueryBytes, _ := json.Marshal(deleteQuery)
	log.Infof("DeleteSourceTableColumns service, delete table columns, channelId:%d, query:%s", channelId, string(deleteQueryBytes))

	deleteDataStartTime := time.Now()
	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		affectedColumnNum, deleteRuleErr = models.GetChannelReaderWriter().RemoveTableColumnCustomMapRule(ctx, deleteQuery)
		if deleteRuleErr != nil {
			log.Errorf("delete table columns failed. channelId:%d, err:%v", channelId, deleteRuleErr)
			return deleteRuleErr
		}
		affectedSummaryNum, deleteSummaryErr = models.GetChannelReaderWriter().RemoveTableColumnCustomSummary(ctx, channelId)
		if deleteSummaryErr != nil {
			log.Errorf("delete table columns summary failed. channelId:%d, err:%v", channelId, deleteSummaryErr)
			return deleteSummaryErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("delete table columns and summary in transaction failed. channelId:%d, err:%v", channelId, trxErr)
		return nil, trxErr
	}

	log.Infof("delete table columns success. channelId:%d, query:%v, affected:%d, consumed:%s", channelId, deleteQuery, affectedColumnNum, time.Since(deleteDataStartTime))
	return &message.DeleteSourceTableColumnsResp{
		AffectedColumns: affectedColumnNum,
		AffectedSummary: affectedSummaryNum,
	}, nil
}

func (s *Service) BatchUpdateSourceTableColumns(ctx context.Context, req *message.BatchUpdateSourceTableColumnsReq) (*message.BatchUpdateSourceTableColumnsResp, error) {
	log.Infof("BatchUpdateSourceTableColumns service request: %v", req)
	taskId, channelId := req.TaskId, req.ChannelId

	columnDataHandler := commonpkg.GetDefaultColumnDataHandler()

	validateErr := columnDataHandler.ValidateColumnMessages(req.Data)
	if validateErr != nil {
		log.Errorf("batch update table columns failed, validate column messages failed, channelId:%d, err:%v", channelId, validateErr)
		return nil, validateErr
	}

	columnDataDigests := lo.Map(req.Data, func(item *message.TableColumnCustomMapRule, _ int) string {
		return item.ColumnDigest
	})

	trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
		columnDataList, getDataErr := models.GetChannelReaderWriter().QueryTableColumnCustomMapRuleByChannelAndDigests(trxCtx, channelId, columnDataDigests)
		if getDataErr != nil {
			log.Errorf("batch get table columns failed. channelId:%d, err:%v", channelId, getDataErr)
			return getDataErr
		}

		columnDataMap := lo.SliceToMap(columnDataList, func(item *channel.TableColumnCustomMapRule) (string, *channel.TableColumnCustomMapRule) {
			return item.ColumnDigest, item
		})

		lo.ForEach(req.Data, func(updateItem *message.TableColumnCustomMapRule, _ int) {
			_, ok := columnDataMap[updateItem.ColumnDigest]
			if !ok {
				err := errors.NewErrorf(errors.TMS_UPDATE_COLUMN_DIGEST_NOT_FOUND, fmt.Sprintf("Schema:%s, Table:%s, Column:%s", updateItem.SchemaNameS, updateItem.TableNameS, updateItem.ColumnNameS))
				log.Errorf("batch update table columns failed. channelId:%d, column not found, columnDigest:%s, err:%v", channelId, updateItem.ColumnDigest, err)
				return
			}
			// 只更新TARGET字段
			columnDataMap[updateItem.ColumnDigest].ColumnNameT = updateItem.ColumnNameT
			columnDataMap[updateItem.ColumnDigest].ColumnCommentT = updateItem.ColumnCommentT
			columnDataMap[updateItem.ColumnDigest].DataDefaultT = updateItem.DataDefaultT
			columnDataMap[updateItem.ColumnDigest].DataTypeT = updateItem.DataTypeT
			columnDataMap[updateItem.ColumnDigest].DataLengthT = updateItem.DataLengthT
			columnDataMap[updateItem.ColumnDigest].DataPrecisionT = updateItem.DataPrecisionT
			columnDataMap[updateItem.ColumnDigest].DataScaleT = updateItem.DataScaleT
			columnDataMap[updateItem.ColumnDigest].NullableT = updateItem.NullableT
		})

		_, _, updateErr := models.GetChannelReaderWriter().SaveTableColumnCustomMapRule(trxCtx, lo.Values(columnDataMap))
		if updateErr != nil {
			log.Errorf("batch update table columns failed. taskId:%d, err:%v", taskId, updateErr)
			return updateErr
		}
		return nil
	})

	if trxErr != nil {
		log.Errorf("batch update table columns in transaction failed. taskId:%d, err:%v", taskId, trxErr)
		return nil, trxErr
	}

	log.Infof("batch update table columns success. taskId:%d, affected:%d", taskId, len(columnDataDigests))
	return &message.BatchUpdateSourceTableColumnsResp{Affected: len(columnDataDigests)}, nil
}

func (s *Service) SyncSourceTableColumns(ctx context.Context, req *message.SyncSourceTableColumnsReq) (*message.SyncSourceTableColumnsResp, error) {
	log.Infof("SyncSourceTableColumns service request: %v", req)
	taskId, channelId := req.TaskId, req.ChannelId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("get task failed. taskId:%d, err:%v", taskId, getTaskErr)
		return nil, getTaskErr
	}
	structureParam, buildParamErr := structure.BuildStructureMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build structure migration param failed. taskId:%d, channelId:%d, err:%v", taskId, channelId, buildParamErr)
		return nil, buildParamErr
	}

	summaryObj, getSummaryErr := models.GetChannelReaderWriter().GetTableColumnCustomSummary(ctx, req.ChannelId)
	if getSummaryErr != nil {
		log.Errorf("get table column custom summary failed. channelId:%d, channelId:%d, err:%v", req.ChannelId, req.ChannelId, getSummaryErr)
		return nil, getSummaryErr
	}
	if summaryObj != nil {
		log.Errorf("table column custom summary already exists. channelId:%d, summary:%v", req.ChannelId, summaryObj)
		return nil, fmt.Errorf("table column custom summary already exists")
	}

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel failed. channelId:%d, err:%v", channelId, getChannelErr)
		return nil, getChannelErr
	}

	log.Debugf("SyncSourceTableColumns service, channelInfo:%v", channelInfo)
	log.Debugf("SyncSourceTableColumns service, taskInfo:%v", taskInfo)

	allTables, getTableNumErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelId(ctx, channelId)
	if getTableNumErr != nil {
		log.Errorf("get channel schema tables failed. channelId:%d, err:%v", channelId, getTableNumErr)
		return nil, getTableNumErr
	}

	uniqueSchemas := make(map[string]bool)
	uniqueSchemaTables := make(map[structs.SchemaTablePair]bool)
	for _, table := range allTables {
		schemaTablePair := structs.SchemaTablePair{SchemaName: table.SchemaNameS, TableName: table.TableNameS}
		if _, ok := uniqueSchemaTables[schemaTablePair]; ok {
			continue
		}
		uniqueSchemaTables[schemaTablePair] = true
		uniqueSchemas[table.SchemaNameS] = true
	}
	totalTableNum := int64(len(uniqueSchemaTables))
	log.Debugf("SyncSourceTableColumns service, total table num:%d, tables:%v", totalTableNum, uniqueSchemaTables)

	sqlBuilder := channelpkg.DefaultSourceTableColumnSQLBuilder()
	sqlBuilder.SetSchemaNames(lo.Keys(uniqueSchemas))
	sqlBuilder.SetSQLHint(structureParam.GetSyncMetaSQLHint())
	sqlBuilder.SetPunctReplacement(structureParam.GetPunctReplacement())

	log.Infof("SyncSourceTableColumns service, set up oracle database conn, taskId:%d", taskId)
	oracleConn, connErr := migrationpkg.SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
	if connErr != nil {
		log.Errorf("set up oracle database conn failed, taskId: %d, channelId: %d, err: %v", taskId, channelId, connErr)
		return nil, connErr
	}
	oracleStore := commonpkg.NewOracleStore(oracleConn.GetSourceDB())

	fetchDataStartTime := time.Now()

	//countSQL := sqlBuilder.CountSQL()
	//log.Infof("GetSourceTableColumns service, fetching columns, taskId:%d, channelId:%d,countSQL:%s",
	//	taskId, channelId, stringutil.RemoveSpecialLetterForLog(countSQL))
	//
	//totalColumnNum, getColumnNumErr := oracleStore.CountColumnsWithSQL(ctx, countSQL)
	//if getColumnNumErr != nil {
	//	log.Errorf("get source table columns failed. channelId:%d, err:%v", channelId, getColumnNumErr)
	//	return nil, getColumnNumErr
	//}

	querySQL := sqlBuilder.QuerySQL()
	log.Infof("GetSourceTableColumns service, fetching columns, taskId:%d, channelId:%d,querySQL:%s",
		taskId, channelId, stringutil.RemoveSpecialLetterForLog(querySQL))

	eg := errgroup.Group{}

	ruleChan := make(chan *channel.TableColumnCustomMapRule, 1000)
	eg.Go(func() error {
		getColumnErr := oracleStore.TableColumnsChanWithSQL(ctx, querySQL, ruleChan)
		if getColumnErr != nil {
			log.Errorf("get source table columns failed. channelId:%d, err:%v", channelId, getColumnErr)
		}
		return getColumnErr
	})

	log.Infof("GetSourceTableColumns service, prepare to save columns into metadb, channelId:%d, taskId:%d, table nums:%d, consume time:%v",
		channelId, taskId, totalTableNum, time.Since(fetchDataStartTime))

	var saveSummaryErr error
	summary := &channel.TableColumnCustomSummary{
		ChannelId:         channelId,
		Status:            constants.ColumnDataStatusFetching,
		TotalTableNum:     totalTableNum,
		FinishedTableNum:  0,
		FinishedColumnNum: 0,
	}

	var savedNum int64
	finishedTableCounter := make(map[structs.SchemaTablePair]bool)

	updateSummary := func(transactionCtx context.Context, status string) error {
		if status != "" {
			summary.Status = status
		}
		summary, saveSummaryErr = models.GetChannelReaderWriter().SaveTableColumnCustomSummary(transactionCtx, summary)
		if saveSummaryErr != nil {
			log.Errorf("save table columns summary failed. taskId:%d, summary:%v, err:%v", taskId, summary, saveSummaryErr)
			return saveSummaryErr
		}
		return saveSummaryErr
	}
	saveInTransaction := func(dataChunk []*channel.TableColumnCustomMapRule) error {
		trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
			log.Infof("GetSourceTableColumns service, saving columns to metadb, taskId:%d, total columns:%d, saved columns:%d, current size:%d", taskId, totalTableNum, savedNum, len(dataChunk))
			summary.FinishedTableNum = int64(len(finishedTableCounter))
			summary.FinishedColumnNum += int64(len(dataChunk))
			savedNum += int64(len(dataChunk))

			if saveSummaryErr = updateSummary(transactionCtx, constants.ColumnDataStatusFetching); saveSummaryErr != nil {
				return saveSummaryErr
			}

			_, _, saveErr := models.GetChannelReaderWriter().SaveTableColumnCustomMapRule(transactionCtx, dataChunk)
			if saveErr != nil {
				log.Errorf("save table columns failed. taskId:%d, err:%v", taskId, saveErr)
				return saveErr
			}
			return nil
		})
		if trxErr != nil {
			log.Errorf("save table columns failed. taskId:%d, err:%v", taskId, trxErr)
			if summary != nil {
				updateSummary(ctx, constants.ColumnDataStatusFailed)
			}
			return trxErr
		}
		return trxErr
	}

	chanFetchDataNum := 0
	saveDataStartTime := time.Now()
	columnsDataChunk := make([]*channel.TableColumnCustomMapRule, 0)

	columnCsvHandler := commonpkg.GetDefaultColumnDataHandler()

	// 缓存结构：按 SchemaNameS 缓存 tableColumnRuleMap 和 tableDefaultRuleMap
	type schemaRuleMaps struct {
		tableColumnRuleMap  map[string]map[string]string
		tableDefaultRuleMap map[string]map[string]string
	}
	schemaRuleMapsCache := make(map[string]*schemaRuleMaps)

	// 预先获取所有需要的 schema 列表
	schemaSet := make(map[string]bool)
	for schemaTablePair := range uniqueSchemaTables {
		schemaSet[schemaTablePair.SchemaName] = true
	}

	// 为每个 schema 预先构建并缓存 rule maps
	for schemaName := range schemaSet {
		log.Debugf("SyncSourceTableColumns service, get schema tables, taskId:%d, schema:%s", taskId, schemaName)
		schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelTaskSchemaS(ctx, channelInfo.ChannelId, taskInfo.TaskID, schemaName)
		if err != nil {
			return nil, fmt.Errorf("get task channel schema tables failed for schema %s: %v", schemaName, err)
		}

		_, tableColumnRuleMap, _, tableDefaultRuleMap, err := buildO2tTablesRuleMaps(ctx, taskInfo, channelInfo, schemaName, "", schemaTables)
		if err != nil {
			log.Errorf("build o2t tables rule maps failed for schema %s. taskId:%d, err:%v", schemaName, taskId, err)
			return nil, err
		}

		schemaRuleMapsCache[schemaName] = &schemaRuleMaps{
			tableColumnRuleMap:  tableColumnRuleMap,
			tableDefaultRuleMap: tableDefaultRuleMap,
		}
	}

	log.Infof("SyncSourceTableColumns service, cached rule maps for %d schemas, taskId:%d", len(schemaRuleMapsCache), taskId)

	for rule := range ruleChan {
		chanFetchDataNum++
		schemaTablePair := structs.SchemaTablePair{SchemaName: rule.SchemaNameS, TableName: rule.TableNameS}
		if _, ok := uniqueSchemaTables[schemaTablePair]; !ok {
			log.Debugf("SyncSourceTableColumns service, skip table columns, taskId:%d, schema:%s, table:%s", taskId, rule.SchemaNameS, rule.TableNameS)
			continue
		}
		finishedTableCounter[schemaTablePair] = true

		// 从缓存中获取对应 schema 的 rule maps
		cachedRuleMaps, exists := schemaRuleMapsCache[rule.SchemaNameS]
		if !exists {
			log.Errorf("schema rule maps not found in cache for schema %s, taskId:%d", rule.SchemaNameS, taskId)
			return nil, fmt.Errorf("schema rule maps not found in cache for schema %s", rule.SchemaNameS)
		}

		rule.ChannelId = channelId
		dataTypeT := cachedRuleMaps.tableColumnRuleMap[rule.TableNameS][rule.ColumnNameS]

		// 使用正则表达式处理两种情况：(数字,数字) 和 (数字)
		reTwoNums := regexp.MustCompile(`\((\d+),(\d+)\)$`) // 匹配 (数字,数字)
		reOneNum := regexp.MustCompile(`\((\d+)\)$`)        // 匹配 (数字)

		if matches := reTwoNums.FindStringSubmatch(dataTypeT); matches != nil {
			// 处理 VARCHAR2(50,10) 类型的情况
			firstNum, err1 := strconv.Atoi(matches[1])
			secondNum, err2 := strconv.Atoi(matches[2])

			if err1 == nil && err2 == nil {
				log.Debugf("SyncSourceTableColumns service, parsed datatype with precision and scale: schema:%s, table:%s, column:%s, dataType:%s, precision:%d, scale:%d",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, firstNum, secondNum)

				// 设置精度和标度
				if firstNum > 0 {
					rule.DataPrecisionT = uint(firstNum)
				} else {
					rule.DataPrecisionT = 38
				}

				rule.DataScaleT = uint(secondNum)

				// 去掉括号及其内容，只保留数据类型名称
				rule.DataTypeT = reTwoNums.ReplaceAllString(dataTypeT, "")
			} else {
				log.Warnf("SyncSourceTableColumns service, failed to parse datatype precision and scale: schema:%s, table:%s, column:%s, dataType:%s, err1:%v, err2:%v",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, err1, err2)
				rule.DataTypeT = dataTypeT
			}
		} else if matches := reOneNum.FindStringSubmatch(dataTypeT); matches != nil {
			// 处理 VARCHAR2(100) 类型的情况
			length, err := strconv.Atoi(matches[1])

			if err == nil {
				log.Debugf("SyncSourceTableColumns service, parsed datatype with length: schema:%s, table:%s, column:%s, dataType:%s, length:%d",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, length)

				// 设置长度
				rule.DataLengthT = uint(length)

				// 去掉括号及其内容，只保留数据类型名称
				rule.DataTypeT = reOneNum.ReplaceAllString(dataTypeT, "")

				// 处理 DECIMAL 类型，如果长度为 0，则设置为 38
				if strings.EqualFold(rule.DataTypeT, "DECIMAL") {
					if length == 0 {
						rule.DataPrecisionT = 38
					} else {
						rule.DataPrecisionT = uint(length)
					}
				}
			} else {
				log.Warnf("SyncSourceTableColumns service, failed to parse datatype length: schema:%s, table:%s, column:%s, dataType:%s, err:%v",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, err)
				rule.DataTypeT = dataTypeT
			}
		} else {
			// 没有匹配到任何括号格式，直接使用原值
			rule.DataTypeT = dataTypeT
		}

		rule.DataDefaultT = cachedRuleMaps.tableDefaultRuleMap[rule.TableNameS][rule.ColumnNameS]
		rule.ColumnDigest = columnCsvHandler.CalculateFieldsChecksum(rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS)
		columnsDataChunk = append(columnsDataChunk, rule)
		if uint(len(columnsDataChunk)) < structureParam.GetSyncMetaBatchSize() {
			continue
		}
		if saveErr := saveInTransaction(columnsDataChunk); saveErr != nil {
			log.Errorf("save table columns failed. taskId:%d, err:%v", taskId, saveErr)
			return nil, saveErr
		}
		columnsDataChunk = columnsDataChunk[:0]
	}
	if len(columnsDataChunk) != 0 {
		saveErr := saveInTransaction(columnsDataChunk)
		if saveErr != nil {
			log.Errorf("save table columns failed. taskId:%d, err:%v", taskId, saveErr)
			return nil, saveErr
		}
	}

	var status string
	if waitErr := eg.Wait(); waitErr != nil {
		status = constants.ColumnDataStatusFailed
	} else {
		status = constants.ColumnDataStatusFetched
	}
	if saveSummaryErr = updateSummary(ctx, status); saveSummaryErr != nil {
		log.Errorf("save table columns summary failed. taskId:%d, err:%v", taskId, saveSummaryErr)
		return nil, saveSummaryErr
	}

	log.Infof("GetSourceTableColumns service, save columns success, taskId:%d, chanFetchDataNum:%d, total table nums:%d, saved columns:%d, consume time:%v", taskId, chanFetchDataNum, totalTableNum, summary.FinishedColumnNum, time.Since(saveDataStartTime))

	return &message.SyncSourceTableColumnsResp{Affected: summary.FinishedColumnNum}, nil
}

func buildO2tTablesRuleMaps(ctx context.Context, taskInfo *task.Task, channelInfo *channel.ChannelInformation, sourceSchema string, targetSchema string, schemaTables []*channel.ChannelSchemaTable) (map[string]string, map[string]map[string]string, map[string]map[string]bool, map[string]map[string]string, error) {
	tableColRules, err := models.GetTemplateReaderWriter().GetTemplateRuleID(ctx, taskInfo.TabcolmapTmplateId)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task column template rule id failed: %v", err)
	}

	colDefaultRules, err := models.GetTemplateReaderWriter().GetTemplateRuleID(ctx, taskInfo.ColdefaultmapTmplateId)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task column default template rule id failed: %v", err)
	}

	var (
		tableColRuleIds   []string
		colDefaultRuleIds []string
	)

	for _, r := range tableColRules {
		tableColRuleIds = append(tableColRuleIds, strconv.Itoa(r.MapRuleId))
	}

	for _, r := range colDefaultRules {
		colDefaultRuleIds = append(colDefaultRuleIds, strconv.Itoa(r.MapRuleId))
	}

	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}
	targetDS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task target datasource info failed: %v", err)
	}

	sourceDB, err := migration.GetSourceDB(ctx, sourceDS)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	oracleDBVersion, err := sourceDB.GetOracleDBVersion()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get oracledb version failed: %v", err)
	}

	oracleCollation := false
	if transferdbCommon.VersionOrdinal(oracleDBVersion) >= transferdbCommon.VersionOrdinal(transferdbCommon.OracleTableColumnCollationDBVersion) {
		oracleCollation = true
	}

	// 获取上下游列类型和默认值映射关系
	tableNameRuleMap, tableColumnRuleMap, tableDefaultValueSourceMap, tableDefaultRuleMap, err := o2t.IChanger(&migration.Change{
		Ctx:               ctx,
		ChannelId:         taskInfo.ChannelId,
		TaskId:            taskInfo.TaskID,
		SourceSchemaName:  sourceSchema,
		TargetSchemaName:  targetSchema,
		SourceTables:      schemaTables,
		TableColRuleIds:   tableColRuleIds,
		ColDefaultRuleIds: colDefaultRuleIds,
		OracleCollation:   oracleCollation,
		Oracle:            sourceDB,
		SourceDBCharset:   sourceDS.Charset,
		TargetDBCharset:   targetDS.Charset,
		TableThreads:      structs.MigrationTableParallelDefault,
	})
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task rule maps failed: %v", err)
	}
	return tableNameRuleMap, tableColumnRuleMap, tableDefaultValueSourceMap, tableDefaultRuleMap, nil
}

func buildTableColumnCustomMapRuleUpdateEntity(item *message.TableColumnCustomMapRule) *channel.TableColumnCustomMapRule {
	return &channel.TableColumnCustomMapRule{
		SchemaNameS:    item.SchemaNameS,
		TableNameS:     item.TableNameS,
		ColumnNameS:    item.ColumnNameS,
		ChannelId:      item.ChannelId,
		ColumnID:       item.ColumnID,
		ColumnNameT:    item.ColumnNameT,
		ColumnCommentS: item.ColumnCommentS,
		ColumnCommentT: item.ColumnCommentT,
		DataDefaultS:   item.DataDefaultS,
		DataDefaultT:   item.DataDefaultT,
		DataTypeS:      item.DataTypeS,
		DataTypeT:      item.DataTypeT,
		DataLengthS:    item.DataLengthS,
		DataLengthT:    item.DataLengthT,
		DataPrecisionS: item.DataPrecisionS,
		DataPrecisionT: item.DataPrecisionT,
		DataScaleS:     item.DataScaleS,
		DataScaleT:     item.DataScaleT,
		NullableS:      item.NullableS,
		NullableT:      item.NullableT,
		ColumnDigest:   item.ColumnDigest,
	}
}

func (s *Service) GetSyncSourceTableColumnsStatus(ctx context.Context, req *message.GetSyncSourceTableColumnsStatusReq) (*message.GetSyncSourceTableColumnsStatusResp, error) {
	log.Infof("GetSyncSourceTableColumnsStatus service request: %v", req)

	summary, getSummaryErr := models.GetChannelReaderWriter().GetTableColumnCustomSummary(ctx, req.ChannelId)
	if getSummaryErr != nil {
		log.Errorf("get table column custom summary failed. channelId:%d, err:%v", req.ChannelId, getSummaryErr)
		return nil, getSummaryErr
	}
	if summary == nil {
		return &message.GetSyncSourceTableColumnsStatusResp{
			ChannelId:         req.ChannelId,
			Status:            constants.ColumnDataStatusNoData,
			TotalTableNum:     0,
			FinishedColumnNum: 0,
			FinishedTableNum:  0,
		}, nil
	}
	return &message.GetSyncSourceTableColumnsStatusResp{
		ChannelId:         req.ChannelId,
		Status:            summary.Status,
		TotalTableNum:     summary.TotalTableNum,
		FinishedColumnNum: summary.FinishedColumnNum,
		FinishedTableNum:  summary.FinishedTableNum,
		TableProgress:     float64(summary.FinishedTableNum) * 100 / float64(summary.TotalTableNum),
	}, nil
}

func (s *Service) GetSourceTableColumns(ctx context.Context, req *message.GetSourceTableColumnsReq) (*message.GetSourceTableColumnsResp, *message.Page, error) {
	reqStr, _ := json.Marshal(req)
	log.Infof("GetSourceTableColumns service request: %v", string(reqStr))
	taskId, channelId := req.TaskId, req.ChannelId

	condition, args := buildQueryConditionAndArgs(req)

	log.Infof("GetSourceTableColumns service, query table columns, taskId:%d, condition:%s, args:%v", taskId, condition, args)
	columns, totalNum, getErr := models.GetChannelReaderWriter().QueryTableColumnCustomMapRuleByChannelTaskAndArgs(ctx, channelId, taskId, req.Page, req.PageSize, condition, args)
	if getErr != nil {
		log.Errorf("query table columns failed. taskId:%d, err:%v", taskId, getErr)
		return nil, nil, getErr
	}

	resp := &message.GetSourceTableColumnsResp{
		Data: lo.Map(columns, func(item *channel.TableColumnCustomMapRule, _ int) *message.TableColumnCustomMapRule {
			return buildTableColumnCustomMapRuleMessage(item)
		}),
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    totalNum,
	}

	return resp, page, nil
}

func buildTableColumnCustomMapRuleMessage(item *channel.TableColumnCustomMapRule) *message.TableColumnCustomMapRule {
	return &message.TableColumnCustomMapRule{
		ChannelId:      item.ChannelId,
		SchemaNameS:    item.SchemaNameS,
		TableNameS:     item.TableNameS,
		ColumnID:       item.ColumnID,
		ColumnNameS:    item.ColumnNameS,
		ColumnNameT:    item.ColumnNameT,
		ColumnCommentS: item.ColumnCommentS,
		ColumnCommentT: item.ColumnCommentT,
		DataDefaultS:   item.DataDefaultS,
		DataDefaultT:   item.DataDefaultT,
		DataTypeS:      item.DataTypeS,
		DataTypeT:      item.DataTypeT,
		DataLengthS:    item.DataLengthS,
		DataLengthT:    item.DataLengthT,
		DataPrecisionS: item.DataPrecisionS,
		DataPrecisionT: item.DataPrecisionT,
		DataScaleS:     item.DataScaleS,
		DataScaleT:     item.DataScaleT,
		NullableS:      item.NullableS,
		NullableT:      item.NullableT,
		ColumnDigest:   item.ColumnDigest,
	}
}

func buildQueryConditionAndArgs(req *message.GetSourceTableColumnsReq) (string, []any) {
	var conditionBuff bytes.Buffer
	var args []any

	for _, filterQuery := range strings.Split(req.FilterQueries, ",") {
		switch constants.FilterQueryType(filterQuery) {
		case constants.FilterSchemaNames:
			if len(req.SchemaNames) == 0 {
				continue
			}
			conditionBuff.WriteString(" AND r.schema_name_s IN (")
			schemaNameSlice := strings.Split(req.SchemaNames, ",")
			for i, schemaName := range schemaNameSlice {
				if i == 0 {
					conditionBuff.WriteString("?")
				} else {
					conditionBuff.WriteString(",?")
				}
				args = append(args, schemaName)
			}
			conditionBuff.WriteString(")")
		case constants.FilterTableName:
			conditionBuff.WriteString(" AND r.table_name_s like ?")
			args = append(args, req.TableName+"%")
		case constants.FilterColumnName:
			conditionBuff.WriteString(" AND r.column_name_s = ?")
			args = append(args, req.ColumnName)
		case constants.FilterDataType:
			conditionBuff.WriteString(" AND r.data_type_s = ?")
			args = append(args, req.DataType)
		case constants.FilterColumnComment:
			conditionBuff.WriteString(" AND r.column_comment_s = ?")
			args = append(args, req.ColumnComment)
		case constants.FilterDataDefault:
			conditionBuff.WriteString(" AND r.data_default_s = ?")
			args = append(args, req.DataDefault)
		case constants.FilterDataLength:
			conditionBuff.WriteString(" AND r.data_length_s = ?")
			args = append(args, req.DataLength)
		case constants.FilterDataPrecision:
			conditionBuff.WriteString(" AND r.data_precision_s = ?")
			args = append(args, req.DataPrecision)
		case constants.FilterDataScale:
			conditionBuff.WriteString(" AND r.data_scale_s = ?")
			args = append(args, req.DataScale)
		case constants.FilterNullable:
			conditionBuff.WriteString(" AND r.nullable_s = ?")
			args = append(args, req.Nullable)
		}
	}

	condition := conditionBuff.String()
	return condition, args
}

func (s *Service) GetCreateChunkSql(ctx context.Context, req *message.CreateChunkReq) (string, error) {
	return migrationpkg.GetCreateChunkSql(ctx, req.ChannelId, req.TaskId)
}

func (s *Service) GetDropChunkSql(ctx context.Context, req *message.CreateChunkReq) (string, error) {
	return migrationpkg.GetDropChunkSql(ctx, req.ChannelId, req.TaskId)
}

func (s *Service) WriteSql(createChunkSql, sqlFileName string) error {
	// 创建目录
	sqlFileName_lst := strings.Split(sqlFileName, "/")
	sqlFile := sqlFileName_lst[len(sqlFileName_lst)-1]
	sqlPath := strings.Replace(sqlFileName, sqlFile, "", 1)
	if sqlPath != "" {
		_, err := os.Stat(sqlPath)
		if err != nil {
			log.Infof("sql file path not exists, start create it. %v", sqlPath)
			err = os.MkdirAll(sqlPath, os.ModePerm)
			if err != nil {
				log.Errorf("create path failed. err:%s", err)
			}
		}
	}
	file, err := os.OpenFile(sqlFileName, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		log.Errorf("open file %s failed. err:%s", sqlFileName, err)
	}
	defer file.Close()

	// 写sql文件

	_, err = file.WriteString(createChunkSql)
	if err != nil {
		log.Errorf("file.WriteString create chunk sql failed. err:%s", err)
		return err
	}

	return nil
}

func (s *Service) MkdirIfNotExist(reportPath string) error {
	if reportPath != "" {
		_, err := os.Stat(reportPath)
		if err != nil {
			log.Infof("report path not exists, start create it. %v", reportPath)
			if mkdirErr := os.MkdirAll(reportPath, os.ModePerm); mkdirErr != nil {
				log.Errorf("create path failed. err:%s", mkdirErr)
				return mkdirErr
			}
		}
	}
	return nil
}

func (s *Service) BatchUpdateTaskStatus(ctx context.Context) error {
	//return models.GetTaskReaderWriter().BatchUpdateTaskStatus(ctx, []int{constants.TASK_TYPE_INCREMENT})
	tasks, err := models.GetTaskReaderWriter().BatchGetRunningTasks(ctx, []int{constants.TASK_TYPE_INCREMENT})
	if err != nil {
		log.Errorf("batch get running tasks failed. err:%v", err)
		return err
	}
	for _, task := range tasks {
		err = fixTaskStatusByTaskId(ctx, task.TaskID)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) ParsingCSVForObjectParserCfg(ctx context.Context, channelId int, taskId int, filePath string) ([]*message.ObjectParserCfg, error) {
	log.Infof("ParsingCSVForObjectParserCfg service request channel id : %d, task id:%v, filePath:%v", channelId, taskId, filePath)

	csvFile, _ := os.Open(filePath)
	reader := csv.NewReader(bufio.NewReader(csvFile))
	lineCount := 0

	var taskTabCfgs []*message.ObjectParserCfg
	for {
		line, error := reader.Read()
		if error == io.EOF {
			break
		} else if error != nil {
			log.Fatal(error)
		}
		lineCount++
		if lineCount > 1 { //跳过第一行表头
			for i := 0; i < len(line); i++ {
				log.Infof("csv data: %d : %s", i, line[i])
			}
			if len(line) < 3 {
				log.Errorf("csv data error at line %d", lineCount)
				continue
			}
			cfg := &message.ObjectParserCfg{
				ChannelId:  channelId,
				TaskId:     taskId,
				SchemaName: line[0],
				ObjectType: line[1],
				ObjectName: line[2],
			}
			taskTabCfgs = append(taskTabCfgs, cfg)
		}
	}
	return taskTabCfgs, nil
}

func (s *Service) CreateObjectParserCfgByCSV(ctx context.Context, req *message.CreateObjectParserCfgDataReq) (*message.CreateObjectParserCfgResp, error) {
	log.Infof("CreateObjectParserCfgByCSV service request channel id : %d, task id:%v, ", req.ChannelId, req.TaskId)

	// 验证请求参数
	if req.ChannelId <= 0 || req.TaskId <= 0 {
		return nil, fmt.Errorf("invalid channel id or task id")
	}

	if len(req.ObjectParserCfgs) == 0 {
		log.Warnf("no object parser configs to create")
		return &message.CreateObjectParserCfgResp{}, nil
	}

	// 将 message.ObjectParserCfg 转换为 model.ObjectParserCfg
	objectParserCfgs := make([]*objectparser.ObjectParserCfg, 0, len(req.ObjectParserCfgs))
	for _, cfg := range req.ObjectParserCfgs {
		objectParserCfgs = append(objectParserCfgs, &objectparser.ObjectParserCfg{
			ChannelId:  req.ChannelId,
			TaskId:     req.TaskId,
			SchemaName: cfg.SchemaName,
			ObjectType: cfg.ObjectType,
			ObjectName: cfg.ObjectName,
		})
	}

	// 先删除该任务下的旧配置
	err := models.GetObjectParserWriter().DeleteObjectParserCfgsByTaskId(ctx, req.TaskId)
	if err != nil {
		log.Errorf("delete existing object parser configs failed. taskId:%d, err:%v", req.TaskId, err)
		return nil, err
	}

	channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if getSchemaErr != nil {
		log.Errorf("create default tasks failed. get channel schemas failed, channelId:%d", req.ChannelId)
		return nil, getSchemaErr
	}
	log.Debugf("CreateObjectParserCfgByCSV GetChannelSchemas")

	// 根据 channelSchemas 的 SchemaNameS 过滤 objectParserCfgs
	validSchemaNames := make(map[string]bool)
	for _, schema := range channelSchemas {
		validSchemaNames[schema.SchemaNameS] = true
	}

	filteredObjectParserCfgs := make([]*objectparser.ObjectParserCfg, 0, len(objectParserCfgs))
	for _, cfg := range objectParserCfgs {
		if validSchemaNames[cfg.SchemaName] {
			filteredObjectParserCfgs = append(filteredObjectParserCfgs, cfg)
		} else {
			log.Warnf("skip object parser config with invalid schema name: %s, taskId: %d", cfg.SchemaName, req.TaskId)
		}
	}

	// 批量创建新的配置
	_, err = models.GetObjectParserWriter().CreateObjectParserCfgs(ctx, filteredObjectParserCfgs)
	if err != nil {
		log.Errorf("create object parser configs failed. err:%v", err)
		return nil, err
	}

	objectTypeCount, err := models.GetObjectParserWriter().CountObjectParserCfgObjectTypeByTaskId(ctx, req.TaskId)
	if err != nil {
		log.Errorf("count object parser cfg object type failed. taskId:%d, err:%v", req.TaskId, err)
		return nil, err
	}
	log.Infof("object type count: %v", objectTypeCount)

	log.Infof("create object parser configs successfully. channelId:%d, taskId:%d, original count:%d, filtered count:%d", req.ChannelId, req.TaskId, len(objectParserCfgs), len(filteredObjectParserCfgs))
	return &message.CreateObjectParserCfgResp{ObjectTypeCount: objectTypeCount}, nil
}

func (s *Service) ListObjectParserCfg(ctx context.Context, req *message.ListObjectParserCfgReq) (*message.ListObjectParserCfgResp, *message.Page, error) {
	log.Infof("ListObjectParserCfg service request channelId: %d, taskId: %d, page: %d, pageSize: %d", req.ChannelId, req.TaskId, req.Page, req.PageSize)

	// Call model layer to get paginated results
	cfgs, total, err := models.GetObjectParserWriter().ListObjectParserCfgWithFilter(ctx, req.TaskId, req.SchemaName, req.ObjectType, req.ObjectName, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("ListObjectParserCfg failed. taskId:%d, err:%v", req.TaskId, err)
		return nil, nil, err
	}

	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}

	cfsMessages := lo.Map(cfgs, func(item *objectparser.ObjectParserCfg, index int) message.ObjectParserCfg {
		return message.ObjectParserCfg{
			ChannelId:  item.ChannelId,
			TaskId:     item.TaskId,
			SchemaName: item.SchemaName,
			ObjectType: item.ObjectType,
			ObjectName: item.ObjectName,
		}
	})

	resp := &message.ListObjectParserCfgResp{
		TaskId:   req.TaskId,
		CfgItems: cfsMessages,
	}

	log.Infof("ListObjectParserCfg success. taskId:%d, total:%d, currentPage:%d", req.TaskId, total, req.Page)
	return resp, page, nil
}

func (s *Service) CreateDefaultTasksForM2T(ctx context.Context, req *message.CreateDefaultTasksForM2TReq) (*message.CreateDefaultTasksForM2TResp, error) {
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("create default tasks failed. channelInfo does not exist, channelId:%d", req.ChannelId)
		return &message.CreateDefaultTasksForM2TResp{}, err
	}
	log.Debugf("CreateDefaultTasksForM2T GetChannel %v", channelInfo)

	channelInfo, updateChannelErr := models.GetChannelReaderWriter().UndeleteChannel(ctx, channelInfo)
	if updateChannelErr != nil {
		log.Errorf("UndeleteChannel failed. channelInfo:%v, DeletedAt:%v,  err:%v", channelInfo, channelInfo.DeletedAt, updateChannelErr)
		return &message.CreateDefaultTasksForM2TResp{}, updateChannelErr
	}

	channelInfo.FullInitialization = req.FullInitialization
	channelInfo.MigrateFullAndIncrementData = req.MigrateFullAndIncrementData
	channelInfo.Increment = req.Increment
	channelInfo.DataCompare = req.DataCompare
	channelInfo.DatasourceIdS = req.DatasourceIdS
	channelInfo.DatasourceIdT = req.DatasourceIdT

	if req.DatasourceIdS != 0 {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdS)
		if err != nil {
			log.Errorf("when create channel, get source datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
			return nil, err
		}
		channelInfo.DatasourceNameS = ds.DatasourceName
	}

	if req.DatasourceIdT != 0 {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdT)
		if err != nil {
			log.Errorf("when create channel, get target datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
			return nil, err
		}
		channelInfo.DatasourceNameT = ds.DatasourceName
	}

	channelInfo, updateChannelErr = models.GetChannelReaderWriter().UpdateChannel(ctx, channelInfo)
	if updateChannelErr != nil {
		log.Errorf("UndeleteChannel failed. channelInfo:%v, DeletedAt:%v,  err:%v", channelInfo, channelInfo.DeletedAt, updateChannelErr)
		return &message.CreateDefaultTasksForM2TResp{}, updateChannelErr
	}

	taskInfos := make([]*task.Task, 0, 4)

	createTaskFunc := func() {
		defaultTemplates, getGefaultTemplatesErr := models.GetTemplateReaderWriter().GetAllDefaultTemplates(ctx)
		if getGefaultTemplatesErr != nil {
			log.Errorf("create default tasks failed. get default templates failed. err:%v", getGefaultTemplatesErr)
			return
		}
		log.Debugf("CreateDefaultTasks GetAllDefaultTemplates")

		taskSeq := 1
		defaultTime, _ := time.Parse(time.RFC3339, "1970-01-01T00:00:00+08:00")

		if channelInfo.FullInitialization == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_FULL_INITIALIZATION)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:TASK_TYPE_FULL_INITIALIZATION, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfos = append(taskInfos, &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_FULL_INITIALIZATION, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_FULL_INITIALIZATION), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID})
			taskSeq++
		}
		if channelInfo.MigrateFullAndIncrementData == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_MIGRATE_FULL_AND_INCREMENT_DATA)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:TASK_TYPE_MIGRATE_FULL_AND_INCREMENT_DATA, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_MIGRATE_FULL_AND_INCREMENT_DATA, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_MIGRATE_FULL_AND_INCREMENT_DATA), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}
		if channelInfo.DataCompare == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_DATA_COMPARE)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:data compare, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfos = append(taskInfos, &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_DATA_COMPARE, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_DATA_COMPARE), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID})
			taskSeq++
		}

		if channelInfo.Increment == constants.TASK_TYPE_CHECKED {
			defaultTaskParamTemplate, getDefaultTaskParamTemplateErr := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, constants.TASK_TYPE_INCREMENT)
			if getDefaultTaskParamTemplateErr != nil {
				log.Errorf("get default task param template failed when create default tasks. type:increment, err:%v", getDefaultTaskParamTemplateErr)
				return
			}
			taskInfo := &task.Task{ChannelId: req.ChannelId, TaskType: constants.TASK_TYPE_INCREMENT, TaskName: fmt.Sprintf("task_%d", constants.TASK_TYPE_INCREMENT), TaskSeq: taskSeq,
				TaskStatus: constants.TASK_STATUS_NOT_SETUP, StartTime: defaultTime, EndTime: defaultTime, TaskParamTmplateId: defaultTaskParamTemplate.TaskparamTemplateID}
			chooseTemplateId(taskInfo, defaultTemplates)
			taskInfos = append(taskInfos, taskInfo)
			taskSeq++
		}

		//create default task info
		taskInfos, err = models.GetTaskReaderWriter().BatchCreateTask(ctx, taskInfos)
		if err != nil {
			log.Errorf("create default tasks failed. data:%v, err:%s", taskInfos, err)
			return
		}
	}

	updateTaskStatusFunc := func() {
		taskIds := make([]int, 0, len(taskInfos))
		for _, t := range taskInfos {
			taskIds = append(taskIds, t.TaskID)
		}
		if len(taskIds) == 0 {
			return
		}
		err := models.GetTaskReaderWriter().BatchUpdateTaskStatusByIds(ctx, taskIds, constants.TASK_STATUS_NOT_RUNNING)
		if err != nil {
			log.Errorf("create default tasks failed. batch update task status failed, channelId:%d, err:%v", req.ChannelId, err)
			return
		}
	}

	updateChannelTaskCreatedFunc := func() {
		channelInfo.TaskCreated = "Y"
		channelInfo, err = models.GetChannelReaderWriter().UpdateChannel(ctx, channelInfo)
		if err != nil {
			log.Errorf("update channel task created failed. channelInfo:%v, err:%v", channelInfo, err)
		}
	}

	// 使用 errgroup 保护 goroutine，避免 panic 导致程序崩溃
	// 由于 updateTaskStatusFunc 依赖于 createTaskFunc 的结果，在同一个 goroutine 中顺序执行
	g, ctx := errgroup.WithContext(ctx)

	g.Go(func() error {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("background tasks panic recovered: %v", r)
			}
		}()

		// 顺序执行三个函数
		if strings.EqualFold(channelInfo.TaskCreated, "N") {
			createTaskFunc()
			updateTaskStatusFunc()
			updateChannelTaskCreatedFunc()
		}

		return nil
	})

	// 等待后台任务完成，但不阻塞主流程
	go func() {
		if err := g.Wait(); err != nil {
			log.Errorf("background tasks failed: %v", err)
		}
	}()

	return &message.CreateDefaultTasksForM2TResp{}, nil
}

func (s *Service) GetOneTaskByChannelId(ctx context.Context, req *message.GetOneTaskByChannelIdReq) (*message.GetOneTaskByChannelIdResp, error) {
	log.Infof("GetOneTaskByChannelId service request channelId: %d", req.ChannelId)

	var tasks *task.Task
	var getTaskErr error

	// for single mode, parentTaskId == 0, that's the default value of this column
	tasks, getTaskErr = models.GetTaskReaderWriter().GetOneTaskByChannelId(ctx, req.ChannelId)
	if getTaskErr != nil {
		log.Errorf("get tasks by channel id failed. channelId: %d, err:%s", req.ChannelId, getTaskErr)
		return nil, getTaskErr
	}

	return &message.GetOneTaskByChannelIdResp{Task: buildTaskMessageFromModel(tasks)}, nil
}
