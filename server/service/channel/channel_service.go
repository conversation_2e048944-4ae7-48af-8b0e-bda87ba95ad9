package channel

import (
	"bufio"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	pkgcommon "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/prometheus"
	"golang.org/x/sync/errgroup"
)

type Service struct {
}

func NewChannelService() *Service {
	return &Service{}
}

// getOracleConnectionFromChannel is a helper function to get Oracle connection from channel
// This reduces code duplication across Oracle-related functions
func (s *Service) getOracleConnectionFromChannel(ctx context.Context, channelId int) (*sql.DB, error) {
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf("get channel failed. channelId: %d, error: %v", channelId, err)
		return nil, err
	}

	ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		log.Errorf("get source datasource failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdS, err)
		return nil, err
	}

	oracleConn, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
	if err != nil {
		log.Errorf("open Oracle connection failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdS, err)
		return nil, err
	}

	return oracleConn, nil
}

// getTiDBConnectionFromChannel is a helper function to get TiDB connection from channel
// This reduces code duplication across TiDB-related functions
func (s *Service) getTiDBConnectionFromChannel(ctx context.Context, channelId int) (*sql.DB, error) {
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf("get channel failed. channelId: %d, error: %v", channelId, err)
		return nil, err
	}

	ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		log.Errorf("get target datasource failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdT, err)
		return nil, err
	}

	tidbConn, err := models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
	if err != nil {
		log.Errorf("open TiDB connection failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdT, err)
		return nil, err
	}

	return tidbConn, nil
}

func (s *Service) CreateChannel(ctx context.Context, req *message.CreateOrUpdateChannelReq) (*message.CreateOrUpdateChannelResp, error) {
	log.Infof("CreateChannel service request: %+v", req.Channel)
	channel := buildChannelModelFromMessage(req.Channel)
	channel.TaskCreated = "N"
	//get source datasource
	if req.DatasourceIdS != 0 {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdS)
		if err != nil {
			log.Errorf("when create channel, get source datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
			return nil, err
		}
		channel.DatasourceNameS = ds.DatasourceName
	} else {
		channel.DatasourceNameS = ""
	}
	//get target datasource
	if req.DatasourceIdT != 0 {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdT)
		if err != nil {
			log.Errorf("when create channel, get target datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
			return nil, err
		}
		channel.DatasourceNameT = ds.DatasourceName
	} else {
		channel.DatasourceNameT = ""
	}

	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		//write to db
		channel, err := models.GetChannelReaderWriter().CreateChannel(transactionCtx, channel)
		if err != nil {
			log.Errorf("create channel %d failed. error: %v", req.ChannelId, err)
			return nil
		}

		if !strings.EqualFold(channel.ChannelMode, "CSV") {
			err = models.GetChannelReaderWriter().DeleteChannel(transactionCtx, channel.ChannelId)
			if err != nil {
				log.Errorf("delete channel %d failed. error: %v", req.ChannelId, err)
				return nil
			}
		}

		return nil
	})
	if err != nil {
		log.Errorf("create channel failed. channel: %+v, error: %s", channel, err)
		return nil, err
	}

	log.Infof("create channel successfully, ds: %+v", channel)
	return &message.CreateOrUpdateChannelResp{ChannelId: channel.ChannelId}, nil

}

func (s *Service) UpdateChannel(ctx context.Context, req *message.CreateOrUpdateChannelReq) (*message.CreateOrUpdateChannelResp, error) {
	log.Infof("UpdateChannel service request: %+v", req.Channel)
	_, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("when update channel, get old channel failed. channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}
	channel := buildChannelModelFromMessage(req.Channel)
	//get source datasource
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdS)
	if err != nil {
		log.Errorf("when create channel, get source datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
		return nil, err
	}
	channel.DatasourceNameS = ds.DatasourceName
	//get target datasource
	ds, err = models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdT)
	if err != nil {
		log.Errorf("when create channel, get target datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
		return nil, err
	}
	channel.DatasourceNameT = ds.DatasourceName

	err = models.Transaction(ctx, func(transactionCtx context.Context) error {
		//write to db
		channel, err = models.GetChannelReaderWriter().UndeleteChannel(transactionCtx, channel)
		if err != nil {
			log.Errorf("undelete channel %d failed. error: %v", req.ChannelId, err)
			return nil
		}

		if !strings.EqualFold(channel.ChannelMode, "CSV") && channel.TaskCreated == "N" {
			err = models.GetChannelReaderWriter().DeleteChannel(transactionCtx, channel.ChannelId)
			if err != nil {
				log.Errorf("delete channel %d failed. error: %v", req.ChannelId, err)
				return nil
			}
		}
		return nil
	})
	if err != nil {
		log.Errorf("update(undelete) channel error, err:%v", err)
		return nil, err
	}

	log.Infof("update channel successfully, channel: %+v", channel)
	return &message.CreateOrUpdateChannelResp{ChannelId: channel.ChannelId}, nil
}

func (s *Service) DeleteChannel(ctx context.Context, req *message.DeleteChannelReq) (*message.DeleteChannelResp, error) {
	log.Infof("DeleteChannel service request: %d", req.ChannelId)
	rw := models.GetChannelReaderWriter()
	channel, err := rw.GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("when delete channel, get channel by id failed., channel id is %d, error: %v", req.ChannelId, err)
		return nil, err
	}
	err = rw.DeleteChannel(ctx, channel.ChannelId)
	if err != nil {
		log.Errorf("delete channel failed, ChannelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}
	return &message.DeleteChannelResp{}, nil
}

func (s *Service) GetChannel(ctx context.Context, req *message.GetChannelReq) (*message.GetChannelResp, error) {
	log.Infof("GetChannel service request: %d", req.ChannelId)
	channel, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel failed, channel id is %d, error: %v", req.ChannelId, err)
		return nil, err
	}
	return &message.GetChannelResp{Channel: buildChannelMessageFromModel(channel)}, nil
}

func (s *Service) ListChannels(ctx context.Context, req *message.ListChannelsReq) (*message.ListChannelsResp, *message.Page, error) {
	log.Infof("list channels service request: %+v", req.PageRequest)
	channels, total, err := models.GetChannelReaderWriter().ListChannels(ctx, req.Page, req.PageSize, req.ChannelName, req.ChannelType)
	if err != nil {
		log.Errorf("list channels failed. err:%v", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	channelsMessage := make([]*message.Channel, 0, req.PageSize)
	for _, channel := range channels {
		channelsMessage = append(channelsMessage, buildChannelMessageFromModel(channel))
	}
	return &message.ListChannelsResp{Channels: channelsMessage}, page, nil
}

func (s *Service) UpdateChannelSchemas(ctx context.Context, req *message.CreateOrUpdateChannelSchemasReq) (*message.CreateOrUpdateChannelSchemasResp, error) {
	log.Infof("UpdateChannelSchemas service request, channelId:%d, data: %+v", req.ChannelId, req.ChannelSchemas)
	ch, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("when update channel schemas, get channel info failed. err:%v", err)
		return nil, err
	}
	//get source datasource
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, ch.DatasourceIdS)
	if err != nil {
		log.Errorf("when update channel schemas, get source datasource failed. datasourceId:%d, err:%v", ch.DatasourceIdS, err)
		return nil, err
	}
	DbNameS := ds.DbName
	//get target datasource
	ds, err = models.GetDatasourceReaderWriter().Get(ctx, ch.DatasourceIdT)
	if err != nil {
		log.Errorf("when update channel schemas, get target datasource failed. datasourceId:%d, err:%v", ch.DatasourceIdT, err)
		return nil, err
	}
	DbNameT := ds.DbName

	schemas := make([]*channel.ChannelSchema, 0, len(req.ChannelSchemas))
	for _, schema := range req.ChannelSchemas {
		schema.DbNameS = DbNameS
		schema.DbNameT = DbNameT
		schemas = append(schemas, buildChannelSchemaModelFromMessage(schema))
	}

	schemas, err = models.GetChannelReaderWriter().UpdateChannelSchemas(ctx, schemas)
	if err != nil {
		log.Errorf("update channel schemas failed. channel object:%+v, error: %s", schemas, err)
		return nil, err
	}
	log.Infof("update channel schemas successfully")
	return &message.CreateOrUpdateChannelSchemasResp{}, nil
}

func (s *Service) CreateChannelSchemas(ctx context.Context, req *message.CreateOrUpdateChannelSchemasReq) (*message.CreateOrUpdateChannelSchemasResp, error) {
	log.Infof("CreateChannelSchemas service request, channelId:%d, data: %+v", req.ChannelId, req.ChannelSchemas)
	var ch *channel.ChannelInformation
	var dsS *datasource.Datasource
	var dsT *datasource.Datasource
	channelSchemas := make([]*channel.ChannelSchema, 0, len(req.ChannelSchemas))

	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		// log.Infof("delete old channel schemas by channelId:%d", req.ChannelId)
		// if err := models.GetChannelReaderWriter().DeleteChannelSchemasByChannelId(transactionCtx, req.ChannelId); err != nil {
		// 	log.Errorf("delete old  channel schemas failed. err:%v", err)
		// 	return err
		// }
		//get channel info
		var err error
		ch, err = models.GetChannelReaderWriter().GetChannel(transactionCtx, req.ChannelId)
		if err != nil {
			log.Errorf("when create channel schemas, get channel info failed. ChannelId:%d, err:%v", req.ChannelId, err)
			return err
		}
		//get source datasource
		dsS, err = models.GetDatasourceReaderWriter().Get(transactionCtx, ch.DatasourceIdS)
		if err != nil {
			log.Errorf("when create channel schemas, get source datasource failed. datasourceId:%d, err:%v", ch.DatasourceIdS, err)
			return err
		}
		dbNameS := dsS.DbName
		//get target datasource
		dsT, err = models.GetDatasourceReaderWriter().Get(transactionCtx, ch.DatasourceIdT)
		if err != nil {
			log.Errorf("when create channel schemas, get target datasource failed. datasourceId:%d, err:%v", ch.DatasourceIdT, err)
			return err
		}
		dbNameT := dsT.DbName

		for _, cs := range req.ChannelSchemas {
			cs.DbNameS = dbNameS
			cs.DbNameT = dbNameT
			channelSchemas = append(channelSchemas, buildChannelSchemaModelFromMessage(cs))
		}
		if _, err := models.GetChannelReaderWriter().CreateChannelSchemas(transactionCtx, channelSchemas); err != nil {
			log.Errorf("create channel schemas failed. channelSchemas:%+v, err:%v", channelSchemas, err)
			return err
		}
		return nil
	})
	if err != nil {
		log.Errorf("create channel schemas failed. channel object:%+v, error: %s", req.ChannelSchemas, err)
		return nil, err
	}

	// 异步初始化 schema tables（仅 M2T）
	if strings.EqualFold(ch.ChannelType, constants.CHANNEL_TYPE_M2T) {
		dsS, err = models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdS)
		if err != nil {
			log.Errorf("when create channel schemas, get source datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, err)
			return nil, err
		}
		g := &errgroup.Group{}
		g.Go(func() error {
			return initChannelSchemaTablesForMysql(ctx, ch, channelSchemas, dsS, dsT)
		})
		go func() {
			if err := g.Wait(); err != nil {
				log.Errorf("init channel schema tables for mysql failed. channel id:%d, error: %v", ch.ChannelId, err)
			} else {
				log.Infof("init channel schema tables for mysql finished. channel id:%d", ch.ChannelId)
			}
		}()
	}
	log.Info("create channel schemas successfully")
	return &message.CreateOrUpdateChannelSchemasResp{}, nil
}

func initChannelSchemaTablesForMysql(ctx context.Context, channelInfo *channel.ChannelInformation, channelSchemas []*channel.ChannelSchema, datasourceS *datasource.Datasource, datasourceT *datasource.Datasource) error {

	channelSchemaTables, err := GetChannelSchemaTables(ctx, channelInfo, channelSchemas, datasourceS)
	if err != nil {
		log.Errorf("get channel schema tables failed. channel id:%d, error: %s", channelInfo.ChannelId, err)
		return err
	}

	taskInfos, err := models.GetTaskReaderWriter().GetTasksByChannelId(ctx, channelInfo.ChannelId)
	if err != nil {
		log.Errorf("get task infos failed. channel id:%d, error: %s", channelInfo.ChannelId, err)
		return err
	}

	GenTaskTables(ctx, channelSchemaTables, taskInfos, datasourceS, datasourceT)

	return nil
}

func (s *Service) BatchDeleteChannelSchemas(ctx context.Context, req *message.BatchDeleteChannelSchemasReq) (*message.BatchDeleteChannelSchemasResp, error) {
	log.Infof("BatchDeleteChannelSchemas service request, channelSchemaId:%v ", req.ChannelSchemaIds)

	if err := models.GetChannelReaderWriter().BatchDeleteChannelSchemas(ctx, req.ChannelSchemaIds); err != nil {
		log.Errorf("BatchDeleteChannelSchemas failed. channelSchemaId:%v, err:%v", req.ChannelSchemaIds, err)
		return &message.BatchDeleteChannelSchemasResp{}, err
	}
	log.Info("BatchDeleteChannelSchemas successfully")
	return &message.BatchDeleteChannelSchemasResp{}, nil
}

func (s *Service) GetUnselectedDataSourceSchemas(ctx context.Context, req *message.GetUnselectedDataSourceSchemasReq) (*message.GetUnselectedDataSourceSchemasResp, error) {
	log.Infof("GetDataSourceSchemas service request DataSourceID: %d", req.DataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		return nil, err
	}
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleSchemas(ctx, db)
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err := models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlSchemas(ctx, db)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
	if err != nil {
		return nil, err
	}

	selectedSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelID)
	if err != nil {
		log.Errorf("get channel schemas failed. channel id:%d, error: %s", req.ChannelID, err)
		return nil, err
	}

	var unselectedSchemas []*structs.SchemaInfo

	for _, schema := range schemas {
		isSelected := false
		for _, selectedData := range selectedSchemas {
			if selectedData.SchemaNameS == schema.SchemaName {
				isSelected = true
				break
			}
		}
		if isSelected == false {
			unselectedSchemas = append(unselectedSchemas, schema)
		}
	}

	log.Debugf("datasource id :%d unselectedSchemas:%v", req.DataSourceID, unselectedSchemas)
	return &message.GetUnselectedDataSourceSchemasResp{SchemaInfos: unselectedSchemas}, nil
}

func (s *Service) SearchUnselectedDataSourceSchemas(ctx context.Context, req *message.SearchUnselectedDataSourceSchemasReq) (*message.GetUnselectedDataSourceSchemasResp, error) {
	log.Infof("GetDataSourceSchemas service request DataSourceID: %d", req.DataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		return nil, err
	}
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err = models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().SearchOracleSchemas(ctx, db, req.SchemaNameS)
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err = models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlSchemas(ctx, db)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
	if err != nil {
		return nil, err
	}

	selectedSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelID)
	if err != nil {
		log.Errorf("get channel schemas failed. channel id:%d, error: %s", req.ChannelID, err)
		return nil, err
	}

	var unselectedSchemas []*structs.SchemaInfo

	for _, schema := range schemas {
		isSelected := false
		for _, selectedData := range selectedSchemas {
			if selectedData.SchemaNameS == schema.SchemaName {
				isSelected = true
				break
			}
		}
		if isSelected == false {
			unselectedSchemas = append(unselectedSchemas, schema)
		}
	}

	log.Debugf("datasource id :%d unselectedSchemas:%v", req.DataSourceID, unselectedSchemas)
	return &message.GetUnselectedDataSourceSchemasResp{SchemaInfos: unselectedSchemas}, nil
}

func (s *Service) GetChannelSchemas(ctx context.Context, req *message.GetChannelSchemasReq) (*message.GetChannelSchemasResp, error) {
	log.Infof("GetChannelSchemas service request channel id : %d", req.ChannelId)
	schemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel schemas failed. channel id:%d, error: %s", req.ChannelId, err)
		return nil, err
	}
	csMessages := make([]*message.ChannelSchema, 0, len(schemas))
	for _, s := range schemas {
		csMessages = append(csMessages, buildChannelSchemaMessageFromModel(s))
	}
	return &message.GetChannelSchemasResp{ChannelSchemas: csMessages}, nil
}

func (s *Service) ExecutionPreCheck(ctx context.Context, req *message.ExecutionPreCheckReq) (*message.ExecutionPreCheckResp, error) {
	log.Infof("ExecutionPreCheck service request channel id : %d, data:%v", req.ChannelId, req.PrecheckInfors)

	checkingPrecheks, err := existCheckingPreCheck(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("Determine whether there is a checking status pre-check in channel failed. channelId:%d", req.ChannelId)
		return nil, err
	}
	if checkingPrecheks {
		return nil, errors.NewErrorf(errors.TIMS_EXIST_CHECKING_STATUS_PRECHECK, "execution pre-check failed.")
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("execution pre-check failed,get channel failed. channelId:%d. err:%s", req.ChannelId, err)
		return nil, err
	}
	dataSourceS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		log.Errorf("execution pre-check failed,get source datasource failed. datasourceId:%d. err:%s", channelInfo.DatasourceIdS, err)
		return nil, err
	}
	if dataSourceS.DbType != constants.DB_TYPE_ORACLE && dataSourceS.DbType != constants.DB_TYPE_ORACLE_ADG {
		return nil, errors.NewErrorf(errors.TIMS_NOT_ORACLE_DATASOURCE, "source datasource must be oracle, channelId:%d, datasourceS:%d, type:%s", req.ChannelId, channelInfo.DatasourceIdS, dataSourceS.DbType)
	}
	dataSourceT, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		log.Errorf("execution pre-check failed,get target datasource failed. datasourceId:%d. err:%s", channelInfo.DatasourceIdT, err)
		return nil, err
	}

	checks := make([]*channel.PrecheckInfo, 0, len(req.PrecheckInfors))
	for _, checkReq := range req.PrecheckInfors {
		checkReq.CheckStatus = constants.PRE_CHECK_STATUS_CHECKING
		checkReq.CheckResultInfo = ""
		checks = append(checks, buildPreCheckModelFromMessage(checkReq))
	}
	checks, err = models.GetChannelReaderWriter().SavePreChecks(ctx, checks)
	if err != nil {
		log.Errorf("create channel pre-check infos failed. channel id:%d, data:%v, error: %s", req.ChannelId, checks, err)
		return nil, err
	}
	err = doPreCheck(ctx, channelInfo, dataSourceS, dataSourceT, checks)
	if err != nil {
		log.Errorf("execution pre-check failed, err:%s", err)
		return nil, err
	}
	return &message.ExecutionPreCheckResp{}, nil
}

func (s *Service) GetPreCheckInfosByChannelId(ctx context.Context, req *message.GetPreCheckInfosByChannelIdReq) (*message.GetPreCheckInfosByChannelIdResp, error) {
	log.Infof("GetPreCheckInfosByChannelId service request channel id : %d", req.ChannelId)
	checks, err := models.GetChannelReaderWriter().GetChannelPreCheckInfosByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel pre-check infos failed. channel id:%d, error: %s", req.ChannelId, err)
		return nil, err
	}
	mChecks := make([]*message.PrecheckInfo, 0, len(checks))
	for _, check := range checks {
		mChecks = append(mChecks, buildPreCheckMessageFromModel(check))
	}
	return &message.GetPreCheckInfosByChannelIdResp{PrecheckInfors: mChecks}, nil
}

func (s *Service) UpdatePreCheckInfo(ctx context.Context, req *message.UpdatePreCheckInfoReq) (*message.UpdatePreCheckInfoResp, error) {
	log.Infof("UpdatePreCheckInfo service request : %v", req.PrecheckInfo)
	_, err := models.GetChannelReaderWriter().UpdatePreCheckInfo(ctx, buildPreCheckModelFromMessage(req.PrecheckInfo))
	if err != nil {
		log.Errorf("update channel pre-check infos failed. data:%v, error: %s", req.PrecheckInfo, err)
		return nil, err
	}
	log.Infof("UpdatePreCheckInfo successfully")
	return &message.UpdatePreCheckInfoResp{}, nil
}

func (s *Service) GetPreCheckInfoById(ctx context.Context, req *message.GetPreCheckInfoByIdReq) (*message.GetPreCheckInfoByIdResp, error) {
	log.Infof("GetPreCheckInfoById service request preCheckId: %d", req.PreCheckId)
	check, err := models.GetChannelReaderWriter().GetPreCheckById(ctx, req.PreCheckId)
	if err != nil {
		log.Errorf("get channel pre-check info by id failed. preCheckId:%d, error: %s", req.PreCheckId, err)
		return nil, err
	}
	return &message.GetPreCheckInfoByIdResp{PrecheckInfo: buildPreCheckMessageFromModel(check)}, nil
}

func (s *Service) SubmitTaskChannelTables(ctx context.Context, req *message.SubmitTaskSchemaTablesReq) (*message.SubmitTaskSchemaTablesResp, error) {
	log.Infof("SubmitChannelTable service request,channelId:%d, taskId:%d, saveTables: %v, deleteTables:%v, saveSchemas:%v, deleteSchemas:%d", req.ChannelId, req.TaskId, req.SaveTables, req.DeleteTables, req.SaveSchemas, req.DeleteSchemas)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		//save schemas
		if req.SaveSchemas != nil {
			taskId := 0
			saveSchemas := make([]int, 0, len(req.SaveSchemas))
			for _, table := range req.SaveSchemas {
				saveSchemas = append(saveSchemas, table.ChannelSchtableId)
				taskId = table.TaskId
			}
			saveErr := models.GetChannelReaderWriter().SelectChannelSchemaTables(transactionCtx, saveSchemas, taskId)
			if saveErr != nil {
				log.Errorf("save channel schemas failed. saveSchemas: %v, err:%v", saveSchemas, saveErr)
				return saveErr
			}
		}
		//delete schemas
		if req.DeleteSchemas != nil {
			deleteErr := models.GetChannelReaderWriter().UnSelectChannelSchemaTables(transactionCtx, req.DeleteSchemas)
			if deleteErr != nil {
				log.Errorf("un-selected channel schemas failed. deleteSchemas: %v, err:%v", req.DeleteSchemas, deleteErr)
				return deleteErr
			}
		}
		//save tables
		if req.SaveTables != nil {
			taskId := 0
			saveTables := make([]int, 0, len(req.SaveTables))
			for _, table := range req.SaveTables {
				saveTables = append(saveTables, table.ChannelSchtableId)
				taskId = table.TaskId
			}
			saveErr := models.GetChannelReaderWriter().SelectChannelSchemaTables(transactionCtx, saveTables, taskId)
			if saveErr != nil {
				log.Errorf("save channel tables failed. saveTables: %v, err:%v", saveTables, saveErr)
				return saveErr
			}
		}
		//delete tables
		if req.DeleteTables != nil {
			deleteErr := models.GetChannelReaderWriter().UnSelectChannelSchemaTables(transactionCtx, req.DeleteTables)
			if deleteErr != nil {
				log.Errorf("un-selected channel schema tables failed. deleteTables: %v, err:%v", req.DeleteTables, deleteErr)
				return deleteErr
			}
		}
		return nil
	})
	if err != nil {
		log.Errorf("submit channel tables failed. err:%v", err)
		return nil, err
	}

	//check conflict tables
	taskInfo, getTaskInfoErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskInfoErr != nil {
		log.Errorf("when submit channel schema tables. get task info failed. taskId:%d", req.TaskId)
		return nil, getTaskInfoErr
	}
	conflictTables, getConflictTablesErr := models.GetChannelReaderWriter().GetConflictTables(ctx, taskInfo.TaskID, taskInfo.ChannelId, taskInfo.TaskType)
	if getConflictTablesErr != nil {
		log.Errorf("when submit channel schema tables, check conflict tables failed. err:%v", getConflictTablesErr)
		return nil, getConflictTablesErr
	}
	if len(conflictTables) == 0 {
		return &message.SubmitTaskSchemaTablesResp{IsConflict: false}, nil
	}
	taskInfo.TaskWarning = constants.TASK_WARNING_TYPE_CONFLICT
	_, updateTaskInfoErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskInfoErr != nil {
		log.Errorf("update task warning status failed. taskId:%d, err:%v", taskInfo.TaskID, updateTaskInfoErr)
		return nil, updateTaskInfoErr
	}
	builder := new(strings.Builder)
	builder.WriteString("当前任务已选择的表同下面任务的已选择的表冲突:\n")
	for _, conflictTable := range conflictTables {
		builder.WriteString(fmt.Sprintf("任务ID:%d,Schema名:%s,表名:%s\n", conflictTable.TaskId, conflictTable.SchemaNameS, conflictTable.TableNameS))
	}
	return &message.SubmitTaskSchemaTablesResp{IsConflict: true, ConflictMessage: builder.String()}, nil
}

func (s *Service) UpdateTaskChannelTables(ctx context.Context, req *message.SubmitTaskSchemaTablesReq) (*message.SubmitTaskSchemaTablesResp, error) {
	log.Infof("UpdateTaskChannelTables service request,channelId:%d, taskId:%d, saveTables: %v", req.ChannelId, req.TaskId, &req.SaveTables)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		//save tables
		if req.SaveTables != nil {
			saveTables := make([]*channel.ChannelSchemaTable, 0, len(req.SaveTables))
			for _, table := range req.SaveTables {
				saveTables = append(saveTables, buildChannelSchemaTableModelFromMessage(table))
			}
			if len(saveTables) > 0 {
				_, saveErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(transactionCtx, saveTables)
				if saveErr != nil {
					log.Errorf("update channel tables failed. saveTables: %v, err:%v", saveTables, saveErr)
					return saveErr
				}
			}
		}
		return nil
	})
	if err != nil {
		log.Errorf("update channel tables failed. err:%v", err)
		return nil, err
	}
	return &message.SubmitTaskSchemaTablesResp{}, nil
}

func (s *Service) GetUnSelectedTaskSchemaTables(ctx context.Context, req *message.GetUnSelectedTaskSchemaTablesReq) (*message.GetUnSelectedTaskSchemaTablesResp, *message.Page, error) {
	log.Infof("GetUnSelectedTaskSchemaTables service request,channelId: %d, taskType:%d, schemaNames:%v, tableNamePrefix:%s", req.ChannelId, req.TaskType, req.SchemaNames, req.TableNamePrefix)
	tables, total, err := models.GetChannelReaderWriter().GetUnSelectedChannelSchemaTablesByPage(ctx, req.ChannelId, req.TaskType, req.SchemaNames, req.TableNamePrefix, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("get un-selected task schema tables failed, channelId: %d, taskType:%d, schemaNames:%v, tableNamePrefix:%s, err:%v", req.ChannelId, req.TaskType, req.SchemaNames, req.TableNamePrefix, err)
		return nil, nil, err
	}
	mTables := make([]*message.ChannelSchemaTable, 0, len(tables))
	for _, table := range tables {
		mTables = append(mTables, buildChannelSchemaTableMessageFromModel(table))
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	return &message.GetUnSelectedTaskSchemaTablesResp{Tables: mTables}, page, nil
}

func (s *Service) GetSelectedTaskSchemaTables(ctx context.Context, req *message.GetSelectedTaskSchemaTablesReq) (*message.GetSelectedTaskSchemaTablesResp, *message.Page, error) {
	log.Infof("GetSelectedTaskSchemaTables service request,channelId: %d, taskType:%d, schemaNames:%v, tableNamePrefix:%s", req.ChannelId, req.TaskId, req.SchemaNames, req.TableNamePrefix)

	channelId, taskId := req.ChannelId, req.TaskId

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed. channelId:%d, err:%v", channelId, getChannelErr)
		return nil, nil, getChannelErr
	}

	var datasourceS *datasource.Datasource
	var getDatasourceErr error
	if channelInfo.ChannelType == constants.CHANNEL_TYPE_M2T {
		datasourceS, getDatasourceErr = models.GetDatasourceReaderWriter().Get(ctx, req.DatasourceIdS)
		if getDatasourceErr != nil {
			log.Errorf("get source datasource failed. datasourceId:%d, err:%v", req.DatasourceIdS, getDatasourceErr)
			return nil, nil, getDatasourceErr
		}
	} else {
		datasourceS, getDatasourceErr = models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
		if getDatasourceErr != nil {
			log.Errorf("get source datasource failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdS, getDatasourceErr)
			return nil, nil, getDatasourceErr
		}
	}

	allTableSchemas, getSchemasErr := models.GetChannelReaderWriter().GetChannelSchemasByTaskId(ctx, taskId)
	if getSchemasErr != nil {
		log.Errorf("get channel schemas by taskId failed, taskId:%d, err:%v", taskId, getSchemasErr)
		return nil, nil, getSchemasErr
	}

	isDbTypeOracle := datasourceS.DbType == constants.DB_TYPE_ORACLE || datasourceS.DbType == constants.DB_TYPE_ORACLE_ADG
	var schemaTableSizeMapping map[structs.SchemaTablePair]float64
	var getSizeErr error

	if isDbTypeOracle {
		schemaTableSizeMapping, getSizeErr = common.GetSchemasTablesSegmentSize(ctx, channelId, taskId, allTableSchemas)
		if getSizeErr != nil {
			log.Errorf("get schema table size failed, taskId:%d, err:%v", taskId, getSizeErr)
			return nil, nil, getSizeErr
		}
	}

	tables, total, err := models.GetChannelReaderWriter().GetSelectedChannelSchemaTablesByTaskIdAndChannelId(ctx, req.ChannelId, req.TaskId, req.SchemaNames, req.TableNamePrefix, req.ClusterTypeT, req.PartitioningTypeS, req.PartitioningTypeT, req.OrderKeys, req.PkS, req.UkS, req.PkT, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("get selected schema tables by taskId failed, channelId: %d, taskType:%d, schemaNames:%v, tableNamePrefix:%s, err:%v", req.ChannelId, req.TaskId, req.SchemaNames, req.TableNamePrefix, err)
		return nil, nil, err
	}
	mTables := make([]*message.ChannelSchemaTable, 0, len(tables))
	for _, table := range tables {
		mTable := buildChannelSchemaTableMessageFromModel(table)
		pair := structs.SchemaTablePair{SchemaName: table.SchemaNameS, TableName: table.TableNameS}
		mTable.TableSizeM = schemaTableSizeMapping[pair]
		mTables = append(mTables, mTable)
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	return &message.GetSelectedTaskSchemaTablesResp{Tables: mTables}, page, nil
}

func (s *Service) ReferenceTables(ctx context.Context, req *message.ReferenceTablesReq) (*message.ReferenceTablesResp, error) {
	log.Infof("ReferenceTables service request, %v", req)
	isConflict := false
	builder := new(strings.Builder)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskInfo, getTaskInfoErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
		if getTaskInfoErr != nil {
			log.Errorf("reference tables failed. get task info failed. taskId:%d", req.TaskId)
			return getTaskInfoErr
		}
		if req.ReferenceType == constants.REFERENCE_TABLE_TYPE_CHANNEL {
			channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
			if getChannelErr != nil {
				log.Errorf("reference tables failed. channelInfo does not exist, channelId:%d", req.ChannelId)
				return getChannelErr
			}
			channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
			if getSchemaErr != nil {
				log.Errorf("reference tables failed. get channel schemas failed, channelId:%d", req.ChannelId)
				return getSchemaErr
			}

			schemaTables, datasourceS, datasourceT, getTableErr := getSchemaTablesAndDataSource(ctx, channelInfo, channelSchemas)
			if getTableErr != nil {
				log.Errorf("reference tables failed. get channel schema tables failed, channelId:%d", req.ChannelId)
				return getTableErr
			}
			//save default task tables
			taskInfos := []*task.Task{taskInfo}
			genTaskTablesErr := genTaskTables(ctx, schemaTables, taskInfos, datasourceS, datasourceT)
			if genTaskTablesErr != nil {
				log.Errorf("reference tables failed. generate task tables failed.")
				return genTaskTablesErr
			}
		} else if req.ReferenceType == constants.REFERENCE_TABLE_TYPE_TASKS {
			tables, getTablesErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.ReferenceTaskId)
			if getTablesErr != nil {
				log.Errorf("reference tables failed. get channel schema tables by taskId failed. taskId:%d", req.ReferenceTaskId)
				return getTablesErr
			}
			if len(tables) == 0 {
				log.Warnf("get empty channel schema tables by taskId.taskId:%d", req.ReferenceTaskId)
				return nil
			}
			for _, table := range tables {
				table.ChannelSchtableId = 0
				table.TaskId = taskInfo.TaskID
				table.TaskType = constants.TASK_TYPE_STATISTICS
				table.CreatedAt = time.Now()
				table.UpdatedAt = time.Now()
				table.Comment = fmt.Sprintf("reference from taskId[%d]", req.ReferenceTaskId)
			}
			_, saveTablesErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(ctx, tables)
			if saveTablesErr != nil {
				log.Errorf("reference tables failed.save channel schema tables failed.err:%v", saveTablesErr)
				return saveTablesErr
			}
		} else {
			return errors.NewErrorf(errors.TIMS_UNKNOWN_REFERENCE_TABLES_TYPE, "reference type is %s", req.ReferenceType)
		}

		//update task
		taskInfo.TaskObjRef = req.ReferenceType
		taskInfo.TaskReftask = req.ReferenceTaskId
		//check conflict tables
		conflictTables, getConflictTablesErr := models.GetChannelReaderWriter().GetConflictTables(ctx, taskInfo.TaskID, taskInfo.ChannelId, taskInfo.TaskType)
		if getConflictTablesErr != nil {
			log.Errorf("when reference tables, check conflict tables failed. err:%v", getConflictTablesErr)
			return getConflictTablesErr
		}
		if len(conflictTables) == 0 {
			_, updateTaskInfoErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
			if updateTaskInfoErr != nil {
				log.Errorf("update task warning status failed. taskId:%d, err:%v", taskInfo.TaskID, updateTaskInfoErr)
				return updateTaskInfoErr
			}
			return nil
		}
		isConflict = true
		taskInfo.TaskWarning = constants.TASK_WARNING_TYPE_CONFLICT
		_, updateTaskInfoErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
		if updateTaskInfoErr != nil {
			log.Errorf("update task warning status failed. taskId:%d, err:%v", taskInfo.TaskID, updateTaskInfoErr)
			return updateTaskInfoErr
		}
		builder.WriteString("当前任务已选择的表同下面任务的已选择的表冲突:\n")
		for _, conflictTable := range conflictTables {
			builder.WriteString(fmt.Sprintf("任务ID:%d,Schema名:%s,表名:%s\n", conflictTable.TaskId, conflictTable.SchemaNameS, conflictTable.TableNameS))
		}
		return nil
	})
	if err != nil {
		log.Errorf("reference tables failed. err:%v", err)
		return nil, err
	}
	if isConflict {
		return &message.ReferenceTablesResp{IsConflict: true, ConflictMessage: builder.String()}, nil
	}
	return &message.ReferenceTablesResp{IsConflict: false}, nil
}

func (s *Service) GetChannelSchemaTableByTaskIdReq(ctx context.Context, req *message.GetSchemaTableByTaskIdReq) (*message.GetSchemaTableByTaskIdResp, error) {
	log.Infof("GetSchemaTableByTaskIdReq service request,channelId: %d, taskId:%d", req.ChannelId, req.TaskId)
	tables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get channel schema tables by taskId failed, channelId: %d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
		return nil, err
	}
	mTables := make([]*message.ChannelSchemaTable, 0, len(tables))
	for _, table := range tables {
		mTables = append(mTables, buildChannelSchemaTableMessageFromModel(table))
	}
	return &message.GetSchemaTableByTaskIdResp{Tables: mTables}, nil
}

func (s *Service) GetTablesByTableNameAndSourceSchemas(ctx context.Context, req *message.GetTablesByTableNameAndSourceSchemasReq) (*message.GetTablesByTableNameAndSourceSchemasResp, error) {
	log.Infof("GetTablesByTableNameAndSourceSchemas service request,data:%v", req)
	tables, err := models.GetChannelReaderWriter().GetTablesByTableNameAndSourceSchemas(ctx, req.ChannelId, req.TaskId, req.TableName, req.SourceSchemas)
	if err != nil {
		log.Errorf("get tables by tableName and schemas failed, channelId: %d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
		return nil, err
	}
	mTables := make([]*message.ChannelSchemaTable, 0, len(tables))
	for _, table := range tables {
		mTables = append(mTables, buildChannelSchemaTableMessageFromModel(table))
	}
	return &message.GetTablesByTableNameAndSourceSchemasResp{Tables: mTables}, nil
}

func buildPreCheckMessageFromModel(infor *channel.PrecheckInfo) *message.PrecheckInfo {
	return &message.PrecheckInfo{
		PreCheckId:      infor.PrecheckId,
		ChannelId:       infor.ChannelId,
		CheckObject:     infor.CheckObject,
		CheckStatus:     infor.CheckStatus,
		CheckIgnore:     infor.CheckIgnore,
		CheckResultInfo: infor.CheckResultInfo,
		BaseFields:      common.BuildBaseFieldsMessageFromModel(infor.Entity),
	}
}

func buildPreCheckModelFromMessage(infor *message.PrecheckInfo) *channel.PrecheckInfo {
	return &channel.PrecheckInfo{
		PrecheckId:      infor.PreCheckId,
		ChannelId:       infor.ChannelId,
		CheckObject:     infor.CheckObject,
		CheckStatus:     infor.CheckStatus,
		CheckIgnore:     infor.CheckIgnore,
		CheckResultInfo: infor.CheckResultInfo,
		Entity:          common.BuildEntityModelFromMessage(infor.BaseFields),
	}
}

func buildChannelMessageFromModel(channel *channel.ChannelInformation) *message.Channel {
	return &message.Channel{
		ChannelId:            channel.ChannelId,
		ChannelName:          channel.ChannelName,
		ChannelType:          channel.ChannelType,
		DatasourceIdS:        channel.DatasourceIdS,
		DatasourceNameS:      channel.DatasourceNameS,
		DatasourceIdT:        channel.DatasourceIdT,
		DatasourceNameT:      channel.DatasourceNameT,
		DatasourceCharsetS:   channel.DatasourceCharsetS,
		DatasourceCharsetT:   channel.DatasourceCharsetT,
		ObjAssessment:        channel.ObjAssessment,
		ObjParser:            channel.ObjParser,
		SqlAssessment:        channel.SqlAssessment,
		MigrateStructure:     channel.MigrateStructure,
		MigrateFullData:      channel.MigrateFullData,
		MigrateCsvData:       channel.MigrateCsvData,
		DataCompare:          channel.DataCompare,
		Increment:            channel.Increment,
		TaskCreated:          channel.TaskCreated,
		ChannelMode:          channel.ChannelMode,
		SQLAnalyzerEnvStatus: boolToStr(channel.SQLAnalyzerEnvStatus),
		DataCompareEnvStatus: boolToStr(channel.DataCompareEnvStatus),
		BaseFields:           common.BuildBaseFieldsMessageFromModel(channel.Entity),
	}
}

func buildChannelModelFromMessage(messageContent *message.Channel) *channel.ChannelInformation {
	return &channel.ChannelInformation{
		ChannelId:          messageContent.ChannelId,
		ChannelName:        messageContent.ChannelName,
		ChannelType:        messageContent.ChannelType,
		DatasourceIdS:      messageContent.DatasourceIdS,
		DatasourceNameS:    messageContent.DatasourceNameS,
		DatasourceIdT:      messageContent.DatasourceIdT,
		DatasourceNameT:    messageContent.DatasourceNameT,
		DatasourceCharsetS: messageContent.DatasourceCharsetS,
		DatasourceCharsetT: messageContent.DatasourceCharsetT,
		ObjAssessment:      messageContent.ObjAssessment,
		ObjParser:          messageContent.ObjParser,
		SqlAssessment:      messageContent.SqlAssessment,
		MigrateStructure:   messageContent.MigrateStructure,
		MigrateFullData:    messageContent.MigrateFullData,
		MigrateCsvData:     messageContent.MigrateCsvData,
		DataCompare:        messageContent.DataCompare,
		Increment:          messageContent.Increment,
		TaskCreated:        messageContent.TaskCreated,
		ChannelMode:        messageContent.ChannelMode,
		Entity:             common.BuildEntityModelFromMessage(messageContent.BaseFields),
	}
}

func buildChannelSchemaObjectModelFromMessage(channelObject *message.ChannelSchemaObject) *channel.ChannelSchemaObject {
	return &channel.ChannelSchemaObject{
		ChannelObjectId:   channelObject.ChannelObjectId,
		TaskId:            channelObject.TaskId,
		ChannelId:         channelObject.ChannelId,
		OnlyTableandindex: channelObject.OnlyTableandindex,
		OnlyTable:         channelObject.OnlyTable,
		OnlyIndex:         channelObject.OnlyIndex,
		Partitiontable:    channelObject.Partitiontable,
		AppendData:        channelObject.AppendData,
		ReloadData:        channelObject.ReloadData,
		LoadData:          channelObject.LoadData,
		Entity:            common.BuildEntityModelFromMessage(channelObject.BaseFields),
	}
}

func buildChannelSchemaObjectMessageFromModel(channelObject *channel.ChannelSchemaObject) *message.ChannelSchemaObject {
	if channelObject == nil {
		return nil
	}
	return &message.ChannelSchemaObject{
		ChannelObjectId:   channelObject.ChannelObjectId,
		TaskId:            channelObject.TaskId,
		ChannelId:         channelObject.ChannelId,
		OnlyTableandindex: channelObject.OnlyTableandindex,
		OnlyTable:         channelObject.OnlyTable,
		OnlyIndex:         channelObject.OnlyIndex,
		Partitiontable:    channelObject.Partitiontable,
		AppendData:        channelObject.AppendData,
		ReloadData:        channelObject.ReloadData,
		LoadData:          channelObject.LoadData,
		BaseFields:        common.BuildBaseFieldsMessageFromModel(channelObject.Entity),
	}
}

func buildChannelSchemaModelFromMessage(schema *message.ChannelSchema) *channel.ChannelSchema {
	return &channel.ChannelSchema{
		ChannelSchemaId: schema.ChannelSchemaId,
		ChannelId:       schema.ChannelId,
		DbNameS:         schema.DbNameS,
		SchemaNameS:     schema.SchemaNameS,
		DbNameT:         schema.DbNameT,
		SchemaNameT:     schema.SchemaNameT,
		TaskId:          schema.TaskId,
		Entity:          common.BuildEntityModelFromMessage(schema.BaseFields),
	}
}
func buildChannelSchemaMessageFromModel(schema *channel.ChannelSchema) *message.ChannelSchema {
	return &message.ChannelSchema{
		ChannelSchemaId: schema.ChannelSchemaId,
		ChannelId:       schema.ChannelId,
		DbNameS:         schema.DbNameS,
		SchemaNameS:     schema.SchemaNameS,
		DbNameT:         schema.DbNameT,
		SchemaNameT:     schema.SchemaNameT,
		TaskId:          schema.TaskId,
		BaseFields:      common.BuildBaseFieldsMessageFromModel(schema.Entity),
	}
}

func buildChannelSchemaTableModelFromMessage(table *message.ChannelSchemaTable) *channel.ChannelSchemaTable {
	return &channel.ChannelSchemaTable{
		ChannelSchtableId:     table.ChannelSchtableId,
		ChannelId:             table.ChannelId,
		TaskId:                table.TaskId,
		DbNameS:               table.DbNameS,
		SchemaNameS:           table.SchemaNameS,
		TableNameS:            table.TableNameS,
		DbNameT:               table.DbNameT,
		SchemaNameT:           table.SchemaNameT,
		TableNameT:            table.TableNameT,
		TaskType:              table.TaskType,
		PartitioningTypeS:     table.PartitioningTypeS,
		PartitioningCountS:    table.PartitioningCountS,
		SubPartitioningTypeS:  table.SubPartitioningTypeS,
		SubPartitioningCountS: table.SubPartitioningCountS,
		UkS:                   table.UkS,
		PkS:                   table.PkS,
		PkT:                   table.PkT,
		PartitioningTypeT:     table.PartitioningTypeT,
		ClusterTypeT:          table.ClusterTypeT,
		Entity:                common.BuildEntityModelFromMessage(table.BaseFields),
	}
}
func buildChannelSchemaTableMessageFromModel(table *channel.ChannelSchemaTable) *message.ChannelSchemaTable {
	return &message.ChannelSchemaTable{
		ChannelSchtableId:     table.ChannelSchtableId,
		ChannelId:             table.ChannelId,
		TaskId:                table.TaskId,
		DbNameS:               table.DbNameS,
		SchemaNameS:           table.SchemaNameS,
		TableNameS:            table.TableNameS,
		DbNameT:               table.DbNameT,
		SchemaNameT:           table.SchemaNameT,
		TableNameT:            table.TableNameT,
		TaskType:              table.TaskType,
		PartitioningTypeS:     table.PartitioningTypeS,
		PartitioningCountS:    table.PartitioningCountS,
		SubPartitioningTypeS:  table.SubPartitioningTypeS,
		SubPartitioningCountS: table.SubPartitioningCountS,
		UkS:                   table.UkS,
		PkS:                   table.PkS,
		PkT:                   table.PkT,
		PartitioningTypeT:     table.PartitioningTypeT,
		ClusterTypeT:          table.ClusterTypeT,
		TableSizeM:            table.TableSizeM,
		BaseFields:            common.BuildBaseFieldsMessageFromModel(table.Entity),
	}
}

func buildUnSelectedSchemaTablesTreeMessage(tables []*channel.ChannelSchemaTable) *message.TreeNode {
	root := &message.TreeNode{Data: "TABLES"}
	preSchemaName := ""
	var parent *message.TreeNode
	for _, table := range tables {
		if preSchemaName != table.SchemaNameS {
			parent = &message.TreeNode{Data: table.SchemaNameS, Children: make([]*message.TreeNode, 0, 0)}
			parent.AddChild(&message.TreeNode{Data: table.TableNameS, Children: nil})
			root.AddChild(parent)
			preSchemaName = table.SchemaNameS
		} else {
			parent.AddChild(&message.TreeNode{Data: table.TableNameS, Children: nil})
		}
	}
	return root
}

func (s *Service) UploadCSV(ctx context.Context, channelId int, datasourceId int, filePath string) (*message.CommonResp, error) {
	log.Debugf("UploadCSV 1")
	//ParsingCSV
	csvSchemaTables, err := parsingCSV(ctx, channelId, filePath)
	if err != nil {
		log.Errorf("parsing csv err, %v", err)
		return nil, err
	}

	log.Debugf("UploadCSV 2")
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, datasourceId)
	if err != nil {
		return nil, err
	}

	//log.Debugf("UploadCSV 3")
	////getSourceSchemas
	//sourceSchemas, err := getSourceSchemas(ctx, ds)
	//if err != nil {
	//	log.Errorf("getSourceSchemas err")
	//	return nil, err
	//}

	//log.Debugf("UploadCSV 4")
	//var schemasArray []string
	//for _, sourceSchema := range sourceSchemas {
	//	schemasArray = append(schemasArray, sourceSchema.SchemaName)
	//}

	//getSourceTables
	sourceTables, err := getSourceTablesByCsv(ctx, ds, csvSchemaTables)
	if err != nil {
		return nil, err
	}

	log.Debugf("UploadCSV 5")

	//check source tables
	err = checkSourceTable(csvSchemaTables, sourceTables)
	if err != nil {
		return nil, err
	}

	log.Debugf("UploadCSV 6")

	//check duplicate table
	err = checkDuplicateTable(csvSchemaTables)
	if err != nil {
		return nil, err
	}

	log.Debugf("UploadCSV 7")

	return &message.CommonResp{}, nil
}

func checkSourceTable(csvSchemaTables []*structs.CsvSchemaTables, sourceTables []*structs.SchemaInfo) error {
	for _, csvData := range csvSchemaTables {
		match := false
		for _, sourceTable := range sourceTables {
			if strings.ToUpper(sourceTable.SchemaName) == strings.ToUpper(csvData.SchemaNameS) {
				for _, obj := range sourceTable.ObjectTypes {
					if strings.ToUpper(obj.TypeName) == "TABLE" {
						for _, objName := range obj.ObjectNames {
							if strings.ToUpper(objName) == strings.ToUpper(csvData.TableNameS) {
								match = true
								break
							}
						}
					}
					if match {
						break
					}
				}
			}
			if match {
				break
			}
		}
		if !match {
			return errors.NewErrorf(errors.TIMS_UPLOAD_CSV_FAILED, "table not exist in source db, csv data %s.%s", csvData.SchemaNameS, csvData.TableNameS)
		}
	}
	return nil
}

func checkDuplicateTable(csvSchemaTables []*structs.CsvSchemaTables) error {
	tables := make(map[string]bool)
	for _, csvData := range csvSchemaTables {
		key := fmt.Sprintf("%s.%s->%s.%s", csvData.SchemaNameS, csvData.TableNameS, csvData.SchemaNameT, csvData.TableNameT)
		if _, ok := tables[key]; ok {
			return errors.NewErrorf(errors.TIMS_UPLOAD_CSV_FAILED, "duplicate table %s", key)
		}
		tables[key] = true
	}
	return nil
}

func parsingCSV(ctx context.Context, channelId int, filePath string) ([]*structs.CsvSchemaTables, error) {
	log.Infof("parsingCSV service request channel id : %d", channelId)
	csvFile, _ := os.Open(filePath)
	reader := csv.NewReader(bufio.NewReader(csvFile))
	lineCount := 0

	var csvSchemaTables []*structs.CsvSchemaTables
	for {
		line, error := reader.Read()
		if error == io.EOF {
			break
		} else if error != nil {
			log.Error(error)
			return nil, error
		}
		lineCount++
		if lineCount > 1 { //跳过第一行表头
			for i := 0; i < len(line); i++ {
				log.Debugf("csv data: %d : %s", i, line[i])
			}
			if len(line) < 6 {
				log.Errorf("csv data error at line %d", lineCount)
				continue
			}
			csvSchemaTable := &structs.CsvSchemaTables{
				DbNameS:     strings.TrimSpace(line[0]),
				SchemaNameS: strings.TrimSpace(line[1]),
				TableNameS:  strings.TrimSpace(line[2]),
				DbNameT:     strings.TrimSpace(line[3]),
				SchemaNameT: strings.TrimSpace(line[4]),
				TableNameT:  strings.TrimSpace(line[5]),
			}
			csvSchemaTables = append(csvSchemaTables, csvSchemaTable)
		}
	}
	return csvSchemaTables, nil
}

func (s *Service) ParsingCSV(ctx context.Context, channelId int, filePath string) ([]*structs.CsvSchemaTables, error) {
	return parsingCSV(ctx, channelId, filePath)
}

func IsElementInSlice(slice []int, target int) bool {
	for _, element := range slice {
		if element == target {
			return true
		}
	}
	return false
}

// NeedDivideCSVData
//  1. 任务类型为2，需要拆分
//  2. 其它任务类型，如果仅SchemaNameS重复，不需要拆分
//  3. 其它任务类型，如果SchemaNameS+TableNameS重复，代表一个源表需要迁移到多个目标库中，需要拆分。
//     举例：
//     FINDPT.ACCOUNT->FINDPT1.ACCOUNT
//     FINDPT.ACCOUNT->FINDPT2.ACCOUNT
//  4. SchemaNameS、TableNameS、SchemaNameT、TableNameT都重复，代表填写了冗余数据，这种情况在CSV导入阶段已排除
func (s *Service) NeedDivideCSVData(req *message.CreateTaskByCsvReq, csvData []*structs.CsvSchemaTables) bool {
	if req.TaskType == 2 {
		return true
	}
	tableMap := make(map[string]int)
	for _, data := range csvData {
		key := data.SchemaNameS + "-" + data.TableNameS
		if _, ok := tableMap[key]; ok {
			return true
		} else {
			tableMap[key] = 1
		}
	}
	return false
}

func (s *Service) DivideCSVData(csvData []*structs.CsvSchemaTables) [][]*structs.CsvSchemaTables {
	schemaMap := make(map[string][]string)
	maxLength := 0
	for _, data := range csvData {
		if schemaNameTs, ok := schemaMap[data.SchemaNameS]; ok {
			if len(schemaNameTs) > 0 {
				isExist := false
				for _, schemaNameT := range schemaNameTs {
					if schemaNameT == data.SchemaNameT {
						isExist = true
						break
					}
					//else {
					//	schemaMap[data.SchemaNameS] = append(schemaMap[data.SchemaNameS], data.SchemaNameT)
					//	if maxLength < len(schemaMap[data.SchemaNameS]) {
					//		maxLength = len(schemaMap[data.SchemaNameS])
					//	}
					//}
				}
				if !isExist {
					schemaMap[data.SchemaNameS] = append(schemaMap[data.SchemaNameS], data.SchemaNameT)
					if maxLength < len(schemaMap[data.SchemaNameS]) {
						maxLength = len(schemaMap[data.SchemaNameS])
					}
				}
			} else {
				schemaMap[data.SchemaNameS] = []string{data.SchemaNameT}
				if maxLength < len(schemaMap[data.SchemaNameS]) {
					maxLength = len(schemaMap[data.SchemaNameS])
				}
			}
		} else {
			schemaMap[data.SchemaNameS] = []string{data.SchemaNameT}
			if maxLength < len(schemaMap[data.SchemaNameS]) {
				maxLength = len(schemaMap[data.SchemaNameS])
			}
		}
	}

	divideCSVDatas := make([][]*structs.CsvSchemaTables, maxLength)
	for i := range divideCSVDatas {
		divideCSVDatas[i] = make([]*structs.CsvSchemaTables, 0)
	}

	for schemaNameS, schemaNameTs := range schemaMap {
		log.Debugf("schemaNameS: %s, schemaNameTs: %v", schemaNameS, schemaNameTs)
		index := 0
		for _, schemaNameT := range schemaNameTs {
			for _, data := range csvData {
				if data.SchemaNameS == schemaNameS && data.SchemaNameT == schemaNameT {
					divideCSVDatas[index] = append(divideCSVDatas[index], data)
				}
			}
			index++
		}
	}
	return divideCSVDatas
}

func getSourceSchemas(ctx context.Context, ds *datasource.Datasource) ([]*structs.SchemaInfo, error) {
	log.Infof("GetDataSourceSchemas service request DataSourceID: %d", ds.DatasourceId)

	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleSchemas(ctx, db)
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err := models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlSchemas(ctx, db)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
	return schemas, nil

}

func getSourceTablesByCsv(ctx context.Context, datasourceS *datasource.Datasource, schemasTables []*structs.CsvSchemaTables) ([]*structs.SchemaInfo, error) {
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemaTables []*structs.SchemaInfo
	if datasourceS.DbType == constants.DB_TYPE_ORACLE || datasourceS.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(datasourceS, ""))
		if err != nil {
			return nil, err
		}
		schemaTables, err = models.GetDatasourceReaderWriter().GetOracleTablesByCSV(ctx, db, schemasTables)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", datasourceS.DbType)
	}

	return schemaTables, nil
}

func boolToStr(v bool) string {
	if v {
		return "Y"
	}
	return "N"
}

func (s *Service) ImportSourceTableColumnByCSV(ctx context.Context, req *message.ImportSourceTableColumnByCSVReq) (*message.ImportSourceTableColumnByCSVResp, error) {
	log.Infof("ImportSourceTableColumnByCSV service request : %v", req)
	channelId, fileDir, fileName := req.ChannelId, req.FileDir, req.FileName

	csvHandler := pkgcommon.GetDefaultColumnDataHandler()

	csvHandler.SetCsvDir(fileDir)
	csvHandler.SetCsvFileName(fileName)
	csvHandler.SetChannelId(channelId)

	log.Infof("read csv file, channelId:%d, fileDir:%s, fileName:%s", channelId, fileDir, fileName)
	rows, readErr := csvHandler.ReadCSVFile()
	if readErr != nil {
		log.Errorf("read csv file failed, channelId:%d, err:%s", channelId, readErr)
		return nil, readErr
	}

	log.Infof("parse column records to wrappers, channelId:%d, fileDir:%s, fileName:%s", channelId, fileDir, fileName)
	columnWrappers, parseErr := csvHandler.ParseColumnRecordsToColumnWrappers(rows)
	if parseErr != nil {
		log.Errorf("parse column records to wrappers failed, channelId:%d, err:%s", channelId, parseErr)
		return nil, parseErr
	}

	log.Infof("got columnWrappers, validate column entity, channelId:%d, wrapperLen:%d", channelId, len(columnWrappers))
	validateErr := csvHandler.ValidateColumnEntity(columnWrappers)
	if validateErr != nil {
		log.Errorf("validate column entity failed, channelId:%d, err:%s", channelId, validateErr)
		return nil, validateErr
	}

	log.Infof("validate columnWrapper success, extract column data for update, channelId:%d, wrapperLen:%d", channelId, len(columnWrappers))
	rets, retSummary, extractErr := csvHandler.ExtractColumnDataForUpdate(columnWrappers)
	if extractErr != nil {
		log.Errorf("extract column data for update failed, channelId:%d, err:%v", channelId, extractErr)
		return nil, extractErr
	}

	log.Infof("extract column data for update, channelId:%d, tableNum:%d, totalColumnNum:%d, retColumnNum:%d, skipColumnNum:%d",
		channelId, retSummary.ValidTableNum, len(columnWrappers), retSummary.ValidColumnNum, retSummary.SkipColumnNum)

	retsBytes, _ := json.Marshal(&rets)
	log.Debugf("save rets, retsBytes:%v", string(retsBytes))
	_, _, updateErr := models.GetChannelReaderWriter().SaveTableColumnCustomMapRule(ctx, rets)
	if updateErr != nil {
		log.Errorf("update table column data failed, channelId:%d, err:%s", channelId, updateErr)
		return nil, updateErr
	}

	log.Infof("save table column data success, channelId:%d, affected:%d", channelId, len(rets))
	resp := &message.ImportSourceTableColumnByCSVResp{
		TableNum:         retSummary.ValidTableNum,
		ColumnNum:        retSummary.ValidColumnNum,
		UpdatedColumnNum: int64(len(rets)),
	}
	return resp, nil
}

func (s *Service) ExportSourceTableColumnsToCSV(ctx context.Context, req *message.ExportSourceTableColumnsToCSVReq) (*message.ExportSourceTableColumnsToCSVResp, error) {
	log.Infof("ExportSourceTableColumnsToCSV service request : %v", req)
	channelId := req.ChannelId

	workingDir, getWdErr := os.Getwd()
	if getWdErr != nil {
		log.Errorf("get pwd failed, channelId:%d, err:%s", channelId, getWdErr)
		return nil, getWdErr
	}

	timestamp := time.Now().Unix()
	resp := &message.ExportSourceTableColumnsToCSVResp{}
	exportCSVDir := path.Join(workingDir, "data/column_data_export")
	exportCSVFileName := fmt.Sprintf("channel_%d_columns_%d.csv", channelId, timestamp)
	resp.ExportCSVPath = path.Join(exportCSVDir, exportCSVFileName)

	log.Infof("get table column data from TMS database, channelId:%d", channelId)
	columns, getColumnDataErr := models.GetChannelReaderWriter().QueryTableColumnCustomMapRuleByChannel(ctx, channelId)
	if getColumnDataErr != nil {
		log.Errorf("get table column data failed, channelId:%d, err:%s", channelId, getColumnDataErr)
		return nil, getColumnDataErr
	}

	log.Infof("export table column data to csv file, channelId:%d, totalNum:%d, exportCSVDir:%s, exportCSVFileName:%s", channelId, len(columns), exportCSVDir, exportCSVFileName)
	csvHandler := pkgcommon.GetDefaultColumnDataHandler()

	writeCsvErr := csvHandler.SetCsvDir(exportCSVDir).SetCsvFileName(exportCSVFileName).WriteColumnData(columns)
	if writeCsvErr != nil {
		log.Errorf("write to csv file failed, channelId:%d, err:%s", channelId, writeCsvErr)
		return nil, writeCsvErr
	}

	return resp, nil
}

func (s *Service) GetActiveTmsSessions(ctx context.Context, req *message.GetActiveTmsSessionsReq) (*message.GetActiveTmsSessionsResp, error) {
	log.Infof("GetActiveTmsSessions service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	activeTmsSessionsVO, err := models.GetDatasourceReaderWriter().GetOracleActiveTmsSessions(ctx, oracleConn)
	if err != nil {
		return nil, err
	}

	activeTmsSessions := make([]message.ActiveTmsSession, 0, len(activeTmsSessionsVO))
	for _, sessionVO := range activeTmsSessionsVO {
		activeTmsSessions = append(activeTmsSessions, convertToActiveTmsSessionMessage(sessionVO))
	}

	return &message.GetActiveTmsSessionsResp{Sessions: activeTmsSessions}, nil
}

func (s *Service) GetTmsSessionProgress(ctx context.Context, req *message.GetTmsSessionProgressReq) (*message.GetTmsSessionProgressResp, error) {
	log.Infof("GetTmsSessionProgress service request: %+v", req.ChannelId)

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel failed. channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		log.Errorf("get source datasource failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdS, err)
		return nil, err
	}

	oracleConn, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
	if err != nil {
		log.Errorf("open Oracle connection failed. datasourceId:%d, err:%v", channelInfo.DatasourceIdS, err)
		return nil, err
	}
	defer oracleConn.Close()

	tmsSessionProgressVO, err := models.GetDatasourceReaderWriter().GetOracleTmsSessionProgress(ctx, oracleConn, ds.UserName)
	if err != nil {
		return nil, err
	}

	tmsSessionProgress := make([]message.TmsSessionProgress, 0, len(tmsSessionProgressVO))
	for _, progressVO := range tmsSessionProgressVO {
		tmsSessionProgress = append(tmsSessionProgress, convertToTmsSessionProgressMessage(progressVO))
	}

	return &message.GetTmsSessionProgressResp{Progress: tmsSessionProgress}, nil
}

func (s *Service) GetTmsSessionRatio(ctx context.Context, req *message.GetTmsSessionRatioReq) (*message.GetTmsSessionRatioResp, error) {
	log.Infof("GetTmsSessionRatio service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	tmsPct, err := models.GetDatasourceReaderWriter().GetOracleTmsSessionRatio(ctx, oracleConn)
	if err != nil {
		return nil, err
	}

	tmsUserStats := make([]message.TmsUserStats, 0, len(tmsPct))
	for _, statsVO := range tmsPct {
		tmsUserStats = append(tmsUserStats, convertToTmsUserStatsMessage(statsVO))
	}

	return &message.GetTmsSessionRatioResp{TmsUserStats: tmsUserStats}, nil
}

func convertToTmsUserStatsMessage(vo datasource.TmsUserStats) message.TmsUserStats {
	return message.TmsUserStats{
		TmsPct:    vo.TmsPct,
		TotalUser: vo.TotalUser,
		TmsUser:   vo.TmsUser,
	}
}

func (s *Service) GetPhysicalFileIOStats(ctx context.Context, req *message.GetPhysicalFileIOStatsReq) (*message.GetPhysicalFileIOStatsResp, error) {
	log.Infof("GetPhysicalFileIOStats service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	physicalFileIOStatsVO, err := models.GetDatasourceReaderWriter().GetOraclePhysicalFileIOStats(ctx, oracleConn)
	if err != nil {
		return nil, err
	}

	physicalFileIOStats := make([]message.PhysicalFileIOStats, 0, len(physicalFileIOStatsVO))
	for _, statsVO := range physicalFileIOStatsVO {
		physicalFileIOStats = append(physicalFileIOStats, convertToPhysicalFileIOStatsMessage(statsVO))
	}

	return &message.GetPhysicalFileIOStatsResp{Stats: physicalFileIOStats}, nil
}

func (s *Service) GetHotFileInformation(ctx context.Context, req *message.GetHotFileInformationReq) (*message.GetHotFileInformationResp, error) {
	log.Infof("GetHotFileInformation service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	hotFilesVO, err := models.GetDatasourceReaderWriter().GetOracleHotFileInformation(ctx, oracleConn)
	if err != nil {
		return nil, err
	}

	hotFiles := make([]message.HotFileInfo, 0, len(hotFilesVO))
	for _, fileVO := range hotFilesVO {
		hotFiles = append(hotFiles, convertToHotFileInfoMessage(fileVO))
	}

	return &message.GetHotFileInformationResp{Files: hotFiles}, nil
}

// Conversion helper functions to convert VO objects to message types

func convertToActiveTmsSessionMessage(vo datasource.OracleActiveTmsSession) message.ActiveTmsSession {
	return message.ActiveTmsSession{
		Sid:          vo.Sid,
		Serial:       vo.Serial,
		Username:     vo.Username,
		Status:       vo.Status,
		SqlID:        vo.SqlID,
		SqlExecStart: vo.SqlExecStart,
		Event:        vo.Event,
		Machine:      vo.Machine,
		Program:      vo.Program,
	}
}

func convertToTmsSessionProgressMessage(vo datasource.OracleTmsSessionProgress) message.TmsSessionProgress {
	return message.TmsSessionProgress{
		Username:             vo.Username,
		Target:               vo.Target,
		Sid:                  vo.Sid,
		Serial:               vo.Serial,
		Opname:               vo.Opname,
		Progress:             vo.Progress,
		TimeRemaining:        vo.TimeRemaining,
		TimeRemainingMinutes: vo.TimeRemainingMinutes,
		TimeRemainingHours:   vo.TimeRemainingHours,
		SqlText:              vo.SqlText,
		StartTime:            vo.StartTime,
	}
}

func convertToPhysicalFileIOStatsMessage(vo datasource.OraclePhysicalFileIOStats) message.PhysicalFileIOStats {
	return message.PhysicalFileIOStats{
		Fn:     vo.Fn,
		Ts:     vo.Ts,
		Reads:  vo.Reads,
		Writes: vo.Writes,
		Br:     vo.Br,
		Bw:     vo.Bw,
		RTimes: vo.RTimes,
		WTimes: vo.WTimes,
	}
}

func convertToHotFileInfoMessage(vo datasource.OracleHotFileInfo) message.HotFileInfo {
	return message.HotFileInfo{
		FileName:       vo.FileName,
		TablespaceName: vo.TablespaceName,
		CS:             vo.CS,
		ReadTimeS:      vo.ReadTimeS,
		WriteTimeS:     vo.WriteTimeS,
	}
}

func (s *Service) GetTiDBPerformance(ctx context.Context, req *message.GetTiDBPerformanceReq) (*message.GetTiDBPerformanceResp, error) {
	log.Infof("GetTiDBPerformance service request: %+v", req.ChannelId)

	// 获取 channel 信息（验证 channel 是否存在）
	channel, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel failed. channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	datasourceId := channel.DatasourceIdT
	datasource, err := models.GetDatasourceReaderWriter().Get(ctx, datasourceId)
	if err != nil {
		log.Errorf("get datasource failed. datasourceId: %d, error: %v", datasourceId, err)
		return nil, err
	}
	prometheusURL := datasource.PrometheusUrl

	orginPromQL := getPromQLByReqType(req.ReqType)
	if orginPromQL == "" {
		return nil, fmt.Errorf("invalid reqType")
	}

	log.Debugf("GetTiDBPerformance service request: %+v", orginPromQL)
	promQL := prometheus.AdjustPromQL(orginPromQL, "", "")
	log.Debugf("GetTiDBPerformance service request: %+v", promQL)
	result, err := prometheus.QueryRange(ctx, prometheusURL, promQL, req.StartTime, req.EndTime, req.Step)
	if err != nil {
		return nil, err
	}
	log.Debugf("QueryRange result: %+v", result)

	performance, err := buildTiDBPerformance(result)
	if err != nil {
		return nil, err
	}

	return &message.GetTiDBPerformanceResp{Performance: performance}, nil
}

func (s *Service) QueryTiDBMetaByPrometheus(ctx context.Context, req *message.GetTiDBPerformanceReq) (*message.GetTiDBPerformanceResp, error) {
	log.Infof("QueryTiDBMetaByPrometheus service request: %+v", req.ChannelId)

	// 获取 channel 信息（验证 channel 是否存在）
	channel, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel failed. channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	datasourceId := channel.DatasourceIdT
	datasource, err := models.GetDatasourceReaderWriter().Get(ctx, datasourceId)
	if err != nil {
		log.Errorf("get datasource failed. datasourceId: %d, error: %v", datasourceId, err)
		return nil, err
	}
	prometheusURL := datasource.PrometheusUrl

	orginPromQL := getPromQLByReqType(req.ReqType)
	if orginPromQL == "" {
		return nil, fmt.Errorf("invalid reqType")
	}
	log.Debugf("QueryTiDBMetaByPrometheus service request: %+v", orginPromQL)
	promQL := prometheus.AdjustPromQL(orginPromQL, "", "")
	log.Debugf("QueryTiDBMetaByPrometheus service request: %+v", promQL)
	result, err := prometheus.QueryInstant(ctx, prometheusURL, promQL)
	if err != nil {
		return nil, err
	}
	log.Debugf("QueryInstant result: %+v", result)

	performance, err := buildTiDBMetaByPrometheus(result)
	if err != nil {
		return nil, err
	}

	return &message.GetTiDBPerformanceResp{Performance: performance}, nil
}

func getPromQLByReqType(reqType string) string {
	orginPromQL := ""
	if reqType == "tikv-used-space" {
		orginPromQL = prometheus.PROMQL_TIKV_USED_SPACE
	} else if reqType == "tikv-free-space" {
		orginPromQL = prometheus.PROMQL_TIKV_FREE_SPACE
	} else if reqType == "tikv-total-space" {
		orginPromQL = prometheus.PROMQL_TIKV_TOTAL_SPACE
	} else if reqType == "tidb-cpu" {
		orginPromQL = prometheus.PROMQL_TIDB_CPU
	} else if reqType == "tidb-mem" {
		orginPromQL = prometheus.PROMQL_TIDB_MEM
	} else if reqType == "tidb-disk" {
		orginPromQL = prometheus.PROMQL_TIDB_DISK_IO
	} else if reqType == "tidb-tps" {
		orginPromQL = prometheus.PROMQL_TIDB_TPS
	} else if reqType == "tidb-qps" {
		orginPromQL = prometheus.PROMQL_TIDB_QPS
	} else if reqType == "tikv-cpu" {
		orginPromQL = prometheus.PROMQL_TIKV_CPU
	} else if reqType == "tikv-mem" {
		orginPromQL = prometheus.PROMQL_TIKV_MEM
	} else if reqType == "tikv-disk" {
		orginPromQL = prometheus.PROMQL_TIKV_DISK_IO
	} else if reqType == "tikv-tps" {
		orginPromQL = prometheus.PROMQL_TIKV_TPS
	} else if reqType == "tikv-qps" {
		orginPromQL = prometheus.PROMQL_TIKV_QPS
	} else if reqType == "tiflash-cpu" {
		orginPromQL = prometheus.PROMQL_TIFLASH_CPU
	} else if reqType == "tiflash-mem" {
		orginPromQL = prometheus.PROMQL_TIFLASH_MEM
	} else if reqType == "tidb-max-procs" {
		orginPromQL = prometheus.PROMQL_TIDB_MAX_PROCS
	} else if reqType == "tidb-max-procs-by-instance" {
		orginPromQL = prometheus.PROMQL_TIDB_MAX_PROCS_BY_INSTANCE
	} else if reqType == "tidb-mem-by-instance" {
		orginPromQL = prometheus.PROMQL_TIDB_MEM_BY_INSTANCE
	} else if reqType == "tidb-node-count" {
		orginPromQL = prometheus.PROMQL_TIDB_NODE_COUNT
	} else if reqType == "tikv-node-count" {
		orginPromQL = prometheus.PROMQL_TIKV_NODE_COUNT
	}
	return orginPromQL
}

func buildTiDBPerformance(result string) ([]message.TiDBPerformance, error) {
	// 解析 Prometheus 查询结果
	var prometheusResp struct {
		Status string `json:"status"`
		Data   struct {
			ResultType string `json:"resultType"`
			Result     []struct {
				Metric map[string]string `json:"metric"`
				Values [][]interface{}   `json:"values"`
			} `json:"result"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &prometheusResp); err != nil {
		log.Errorf("Failed to parse Prometheus response: %v", err)
		return nil, err
	}

	// 转换结果到 TiDBPerformance 结构体
	performance := make([]message.TiDBPerformance, 0)
	for _, series := range prometheusResp.Data.Result {
		instance := series.Metric["instance"]
		for _, point := range series.Values {
			if len(point) >= 2 {
				timestamp, _ := point[0].(float64)
				value, _ := point[1].(string)
				valueFloat, _ := strconv.ParseFloat(value, 64)

				performance = append(performance, message.TiDBPerformance{
					Instance:  instance,
					Value:     valueFloat,
					Timestamp: time.Unix(int64(timestamp), 0).Format("2006-01-02 15:04:05"),
				})
			}
		}
	}
	return performance, nil
}

func buildTiDBMetaByPrometheus(result string) ([]message.TiDBPerformance, error) {
	// 解析 Prometheus 查询结果
	var prometheusResp struct {
		Status string `json:"status"`
		Data   struct {
			ResultType string `json:"resultType"`
			Result     []struct {
				Metric map[string]string `json:"metric"`
				Values [][]interface{}   `json:"values"`
				Value  []interface{}     `json:"value"`
			} `json:"result"`
		} `json:"data"`
	}

	if err := json.Unmarshal([]byte(result), &prometheusResp); err != nil {
		log.Errorf("Failed to parse Prometheus response: %v", err)
		return nil, err
	}

	performance := make([]message.TiDBPerformance, 0)

	switch prometheusResp.Data.ResultType {
	case "matrix": // 区间查询
		for _, series := range prometheusResp.Data.Result {
			instance := series.Metric["instance"]
			for _, point := range series.Values {
				if len(point) >= 2 {
					timestamp, _ := point[0].(float64)
					value, _ := point[1].(string)
					valueFloat, _ := strconv.ParseFloat(value, 64)
					performance = append(performance, message.TiDBPerformance{
						Instance:  instance,
						Value:     valueFloat,
						Timestamp: time.Unix(int64(timestamp), 0).Format("2006-01-02 15:04:05"),
					})
				}
			}
		}
	case "vector": // 瞬时查询
		for _, series := range prometheusResp.Data.Result {
			instance := series.Metric["instance"]
			if len(series.Value) >= 2 {
				timestamp, _ := series.Value[0].(float64)
				value, _ := series.Value[1].(string)
				valueFloat, _ := strconv.ParseFloat(value, 64)
				performance = append(performance, message.TiDBPerformance{
					Instance:  instance,
					Value:     valueFloat,
					Timestamp: time.Unix(int64(timestamp), 0).Format("2006-01-02 15:04:05"),
				})
			}
		}
	default:
		log.Errorf("Unknown resultType: %s", prometheusResp.Data.ResultType)
	}

	return performance, nil
}

func (s *Service) GetOracleTableSpaceStats(ctx context.Context, req *message.GetTableSpaceStatsReq) (*message.GetTableSpaceStatsResp, error) {
	log.Infof("GetHotFileInformation service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	tableSpaceStatsVO, err := models.GetDatasourceReaderWriter().GetOracleTableSpaceStats(ctx, oracleConn)
	if err != nil {
		return nil, err
	}

	tableSpaceStats := make([]message.OracleTableSpaceStats, 0, len(tableSpaceStatsVO))
	for _, statusVO := range tableSpaceStatsVO {
		tableSpaceStats = append(tableSpaceStats, convertToOracleTableSpaceStatsMessage(statusVO))
	}

	return &message.GetTableSpaceStatsResp{Stats: tableSpaceStats}, nil
}

func convertToOracleTableSpaceStatsMessage(vo datasource.OracleTableSpaceStats) message.OracleTableSpaceStats {
	return message.OracleTableSpaceStats{
		TablespaceName: vo.TablespaceName,
		TotGrootteMb:   vo.TotGrootteMb,
		UsedSpace:      vo.UsedSpace,
		UsedRatio:      vo.UsedRatio,
		TotalBytes:     vo.TotalBytes,
		MaxBytes:       vo.MaxBytes,
	}
}

func (s *Service) GetOracleObjectStats(ctx context.Context, req *message.GetOracleObjectStatsReq) (*message.GetOracleObjectStatsResp, error) {
	log.Infof("GetHotFileInformation service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	statsVO, err := models.GetDatasourceReaderWriter().GetOracleObjectStats(ctx, oracleConn)
	if err != nil {
		return nil, err
	}

	stats := make([]message.OracleObjectStats, 0, len(statsVO))
	for _, statusVO := range statsVO {
		stats = append(stats, convertToOracleObjectStatsMessage(statusVO))
	}

	return &message.GetOracleObjectStatsResp{Stats: stats}, nil
}

func (s *Service) GetOracleObjectStatsBySchema(ctx context.Context, req *message.GetOracleObjectStatsReq) (*message.GetOracleObjectStatsResp, error) {
	log.Infof("GetHotFileInformation service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if getSchemaErr != nil {
		log.Errorf("get channel schemas failed, channelId:%d", req.ChannelId)
		return nil, getSchemaErr
	}

	statsVO, err := models.GetDatasourceReaderWriter().GetOracleObjectStatsBySchema(ctx, oracleConn, channelSchemas)
	if err != nil {
		return nil, err
	}

	stats := make([]message.OracleObjectStats, 0, len(statsVO))
	for _, statusVO := range statsVO {
		stats = append(stats, convertToOracleObjectStatsMessage(statusVO))
	}

	return &message.GetOracleObjectStatsResp{Stats: stats}, nil
}

func convertToOracleObjectStatsMessage(vo datasource.OracleObjectStats) message.OracleObjectStats {
	return message.OracleObjectStats{
		SchemaName:  vo.SchemaName,
		Tables:      vo.Tables,
		Views:       vo.Views,
		Sequences:   vo.Sequences,
		Indexes:     vo.Indexes,
		Procedures:  vo.Procedures,
		Packages:    vo.Packages,
		Constraints: vo.Constraints,
		Triggers:    vo.Triggers,
		Functions:   vo.Functions,
	}
}

func (s *Service) GetOracleMeta(ctx context.Context, req *message.GetOracleMetaReq) (*message.GetOracleMetaResp, error) {
	log.Infof("GetHotFileInformation service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	if req.ReqType == "cpu" {
		cpuNumber, err := models.GetDatasourceReaderWriter().GetOracleCpuNumber(ctx, oracleConn)
		if err != nil {
			return nil, err
		}
		return &message.GetOracleMetaResp{MetaValue: cpuNumber}, nil
	} else if req.ReqType == "memory" {
		memory, err := models.GetDatasourceReaderWriter().GetOracleMemory(ctx, oracleConn)
		if err != nil {
			return nil, err
		}
		return &message.GetOracleMetaResp{MetaValue: memory}, nil
	}
	return nil, fmt.Errorf("invalid reqType")
}

func (s *Service) GetOracleTableNumberRowsBySchema(ctx context.Context, req *message.GetOracleTableNumberRowsBySchemaReq) (*message.GetOracleTableNumberRowsBySchemaResp, error) {
	log.Infof("GetHotFileInformation service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	// 获取 channelInfo 信息（验证 channelInfo 是否存在）
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("get channel failed. channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	dataSourceT, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		log.Errorf("get datasource failed,get target datasource failed. datasourceId:%d. err:%s", channelInfo.DatasourceIdT, err)
		return nil, err
	}
	tiDBConn, err := models.OpenMysql(dataSourceT.UserName, dataSourceT.PasswordValue, dataSourceT.HostIp, dataSourceT.HostPort, "")
	if err != nil {
		log.Errorf("open mysql failed,open tidb failed. channelId:%d, datasourceId:%d. err:%s", channelInfo.ChannelId, channelInfo.DatasourceIdT, err)
		return nil, err
	}
	defer tiDBConn.Close()

	channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if getSchemaErr != nil {
		log.Errorf("get channel schemas failed, channelId:%d", req.ChannelId)
		return nil, getSchemaErr
	}

	statsVO, total, err := models.GetDatasourceReaderWriter().GetOracleTableNumberRowsBySchema(ctx, oracleConn, channelSchemas, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	stats := make([]message.OracleTableNumberRows, 0, len(statsVO))
	for _, statusVO := range statsVO {
		stats = append(stats, convertToOracleTableNumberRowsMessage(statusVO))
	}

	channelSchemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.TaskId)
	if getTableErr != nil {
		log.Error("get channel schema tables failed, taskId:%d, channelId:%d, err:%v", req.TaskId, req.ChannelId, getTableErr)
		return nil, getTableErr
	}

	schemaTableMap := make(map[structs.SchemaTablePair]*channel.ChannelSchemaTable)
	for _, stat := range stats {
		for _, schemaTable := range channelSchemaTables {
			if strings.EqualFold(stat.SchemaNameS, schemaTable.SchemaNameS) && strings.EqualFold(stat.TableNameS, schemaTable.TableNameS) {
				schemaTableMap[structs.SchemaTablePair{SchemaName: strings.ToUpper(stat.SchemaNameS), TableName: strings.ToUpper(stat.TableNameS)}] = schemaTable
			}
		}
	}

	log.Debugf("schemaTableMap: %+v", schemaTableMap)
	log.Debugf("len(schemaTableMap): %d", len(schemaTableMap))

	tidbRowCountVO, err := models.GetDatasourceReaderWriter().GetTiDBRowCount(ctx, tiDBConn, schemaTableMap)
	if err != nil {
		return nil, err
	}

	for i := range stats {
		if schemaTable, ok := schemaTableMap[structs.SchemaTablePair{SchemaName: strings.ToUpper(stats[i].SchemaNameS), TableName: strings.ToUpper(stats[i].TableNameS)}]; ok {
			stats[i].SchemaNameT = schemaTable.SchemaNameT
			stats[i].TableNameT = schemaTable.TableNameT
		}
	}

	log.Debugf("tidbRowCountVO: %+v", tidbRowCountVO)
	log.Debugf("len(tidbRowCountVO): %d", len(tidbRowCountVO))

	for i := range stats {
		for _, tidbRowCount := range tidbRowCountVO {
			if strings.EqualFold(stats[i].SchemaNameT, tidbRowCount.SchemaName) && strings.EqualFold(stats[i].TableNameT, tidbRowCount.TableName) {
				stats[i].TableRowsT = tidbRowCount.TableRows
			}
		}
	}

	return &message.GetOracleTableNumberRowsBySchemaResp{Stats: stats, Page: message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}}, nil
}

func convertToOracleTableNumberRowsMessage(vo datasource.OracleTableNumberRows) message.OracleTableNumberRows {
	return message.OracleTableNumberRows{
		SchemaNameS:   vo.Owner,
		TableNameS:    vo.TableName,
		TableRowsS:    vo.NumRows,
		LastAnalyzedS: vo.LastAnalyzed,
		RnS:           vo.Rn,
	}
}

func (s *Service) GetObjectAssessOverview(ctx context.Context, req *message.GetObjectAssessOverviewReq) (*message.GetObjectAssessOverviewResp, error) {
	log.Infof("GetObjectAssessOverview service request: %+v", req.ChannelId)

	timsConfig := config.GetGlobalConfig()

	metadataDB, err := models.OpenMysql(timsConfig.DBConfig.User, timsConfig.DBConfig.Password, timsConfig.DBConfig.Host, timsConfig.DBConfig.Port, timsConfig.DBConfig.Schema)
	if err != nil {
		return nil, err
	}
	defer metadataDB.Close()

	channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if getSchemaErr != nil {
		log.Errorf("get channel schemas failed, channelId:%d", req.ChannelId)
		return nil, getSchemaErr
	}

	oracleOverview, err := models.GetAssessReaderWriter().GetListOracleOverview(ctx, metadataDB, req.ChannelId, req.TaskId, channelSchemas)
	if err != nil {
		return nil, err
	}

	objectCompatibleBySchema, err := models.GetAssessReaderWriter().GetListObjectCompatibleBySchema(ctx, metadataDB, req.ChannelId, req.TaskId, channelSchemas)
	if err != nil {
		return nil, err
	}

	objectCompatibleByObject, err := models.GetAssessReaderWriter().GetListObjectCompatibleBySchemaObject(ctx, metadataDB, req.ChannelId, req.TaskId, channelSchemas)
	if err != nil {
		return nil, err
	}

	return &message.GetObjectAssessOverviewResp{
		OracleOverview:           oracleOverview,
		ObjectCompatibleBySchema: objectCompatibleBySchema,
		ObjectCompatibleByObject: objectCompatibleByObject,
	}, nil
}

func (s *Service) GetOracleObjectList(ctx context.Context, req *message.GetOracleObjectListReq) (*message.GetOracleObjectListResp, error) {
	log.Infof("GetOracleObjectList service request: %+v", req.ChannelId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	statsVO, total, err := models.GetDatasourceReaderWriter().GetOracleObjectList(ctx, oracleConn, req.SchemaName, req.ObjectType, req.Page, req.PageSize)
	if err != nil {
		return nil, err
	}

	stats := make([]message.OracleObjectList, 0, len(statsVO))
	for _, stat := range statsVO {
		stats = append(stats, convertToOracleObjectListMessage(stat))
	}

	return &message.GetOracleObjectListResp{Stats: stats, Page: message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}}, nil
}

func convertToOracleObjectListMessage(vo datasource.OracleObjectList) message.OracleObjectList {
	return message.OracleObjectList{
		Owner:      vo.Owner,
		ObjectName: vo.ObjectName,
		ObjectType: vo.ObjectType,
		Status:     vo.Status,
		Rn:         vo.Rn,
	}
}

// GetAndSaveOracleSegmentSizes 使用GORM查询Oracle segment size信息并保存到TiDB
func (s *Service) GetAndSaveOracleSegmentSizes(ctx context.Context, req *message.GetOracleSegmentSizesReq) (*message.GetOracleSegmentSizesResp, error) {
	log.Infof("GetAndSaveOracleSegmentSizes service request: channelId:%d, taskId:%d", req.ChannelId, req.TaskId)

	// 获取Oracle连接
	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if getSchemaErr != nil {
		log.Errorf("get channel schemas failed, channelId:%d", req.ChannelId)
		return nil, getSchemaErr
	}

	schemas := make([]string, 0, len(channelSchemas))
	for _, schema := range channelSchemas {
		schemas = append(schemas, schema.SchemaNameS)
	}

	// 从Oracle查询segment size信息
	segmentSizes, err := models.GetDatasourceReaderWriter().GetOracleSegmentSizes(ctx, oracleConn, schemas)
	if err != nil {
		log.Errorf("get oracle segment sizes failed. channelId:%d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
		return nil, err
	}

	if len(segmentSizes) == 0 {
		log.Infof("no segment sizes found for schemas: %v", schemas)
		return &message.GetOracleSegmentSizesResp{}, nil
	}

	// 为每个segment size设置taskId和创建时间
	now := time.Now()
	for i := range segmentSizes {
		segmentSizes[i].TaskId = req.TaskId
		segmentSizes[i].CreateTime = now
	}

	// 将VO对象转换为GORM实体
	entities := make([]*datasource.OracleSegmentSizeEntity, 0, len(segmentSizes))
	for _, segmentSize := range segmentSizes {
		entities = append(entities, buildOracleSegmentSizeEntity(segmentSize, req.TaskId))
	}

	// 在事务中执行删除和保存操作
	err = models.Transaction(ctx, func(transactionCtx context.Context) error {
		// 先删除该taskId下的所有segment size记录
		err := models.GetDatasourceReaderWriter().DeleteOracleSegmentSizesByTaskId(transactionCtx, req.TaskId)
		if err != nil {
			log.Errorf("delete oracle segment sizes by taskId failed. taskId:%d, err:%v", req.TaskId, err)
			return err
		}

		// 再保存新的segment size记录
		err = models.GetDatasourceReaderWriter().SaveOracleSegmentSizesToTiDB(transactionCtx, entities)
		if err != nil {
			log.Errorf("save oracle segment sizes to TiDB with gorm failed. channelId:%d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
			return err
		}

		return nil
	})

	if err != nil {
		log.Errorf("transaction failed for get and save oracle segment sizes. channelId:%d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
		return nil, err
	}

	log.Infof("get and save oracle segment sizes with gorm successfully. channelId:%d, taskId:%d, count:%d", req.ChannelId, req.TaskId, len(segmentSizes))
	return &message.GetOracleSegmentSizesResp{}, nil
}

func buildOracleSegmentSizeEntity(vo datasource.OracleSegmentSize, taskId int) *datasource.OracleSegmentSizeEntity {
	return &datasource.OracleSegmentSizeEntity{
		TaskId:      taskId,
		Owner:       vo.Owner,
		SegmentName: vo.SegmentName,
		SumT:        vo.SumT,
		SumTp:       vo.SumTp,
		SumTsup:     vo.SumTsup,
		SumI:        vo.SumI,
		SumIp:       vo.SumIp,
		SumIsp:      vo.SumIsp,
		CreateTime:  vo.CreateTime,
	}
}

// DeleteOracleSegmentSizesByTaskId 按taskId物理删除Oracle segment size信息
func (s *Service) DeleteOracleSegmentSizesByTaskId(ctx context.Context, taskId int) error {
	log.Infof("DeleteOracleSegmentSizesByTaskId service request: taskId:%d", taskId)

	err := models.GetDatasourceReaderWriter().DeleteOracleSegmentSizesByTaskId(ctx, taskId)
	if err != nil {
		log.Errorf("delete oracle segment sizes by taskId failed. taskId:%d, err:%v", taskId, err)
		return err
	}

	log.Infof("delete oracle segment sizes by taskId successfully. taskId:%d", taskId)
	return nil
}

func (s *Service) GetOracleSegmentSizesByTaskId(ctx context.Context, req *message.QueryOracleSegmentSizesReq) (*message.QueryOracleSegmentSizesResp, error) {
	log.Infof("GetOracleSegmentSizes service request: channelId:%d, taskId:%d", req.ChannelId, req.TaskId)

	oracleConn, err := s.getOracleConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer oracleConn.Close()

	segmentSizes, err := models.GetDatasourceReaderWriter().GetOracleSegmentSizesFromTiDB(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get oracle segment sizes failed. channelId:%d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
		return nil, err
	}

	stats := make([]*datasource.OracleSegmentSize, 0, len(segmentSizes))
	for _, segmentSize := range segmentSizes {
		stats = append(stats, convertToOracleSegmentSizeMessage(segmentSize))
	}

	return &message.QueryOracleSegmentSizesResp{Stats: stats}, nil
}

func convertToOracleSegmentSizeMessage(vo datasource.OracleSegmentSize) *datasource.OracleSegmentSize {
	return &datasource.OracleSegmentSize{
		TaskId:      vo.TaskId,
		Owner:       vo.Owner,
		SegmentName: vo.SegmentName,
		SumT:        vo.SumT,
		SumTp:       vo.SumTp,
		SumTsup:     vo.SumTsup,
		SumI:        vo.SumI,
		SumIp:       vo.SumIp,
		SumIsp:      vo.SumIsp,
		TotalSize:   vo.TotalSize,
		CreateTime:  vo.CreateTime,
	}
}

func (s *Service) GetOracleSegmentSumSizesByTaskId(ctx context.Context, req *message.QueryOracleSegmentSizesReq) (*message.QueryOracleSegmentSumSizesResp, error) {
	log.Infof("GetOracleSegmentSumSizes service request: channelId:%d, taskId:%d", req.ChannelId, req.TaskId)

	sumTable, sumIndex, err := models.GetDatasourceReaderWriter().GetOracleSegmentSumSizesFromTiDB(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get oracle segment sum sizes failed. channelId:%d, taskId:%d, err:%v", req.ChannelId, req.TaskId, err)
		return nil, err
	}

	return &message.QueryOracleSegmentSumSizesResp{SumTable: sumTable, SumIndex: sumIndex}, nil
}

// QueryTiDBBaseInfo 根据reqType查询TiDB集群硬件信息
func (s *Service) QueryTiDBBaseInfo(ctx context.Context, req *message.GetTiDBPerformanceReq) (*message.GetClusterHardwareResp, error) {
	db, err := s.getTiDBConnectionFromChannel(ctx, req.ChannelId)
	if err != nil {
		return nil, err
	}
	defer db.Close()

	drw := models.GetDatasourceReaderWriter()
	var resp message.GetClusterHardwareResp
	switch req.ReqType {
	case "tidb_mem_capacity":
		rows, err := drw.QueryClusterHardware(ctx, db, "tidb", "memory", "memory", "capacity")
		if err != nil {
			return nil, err
		}
		resp.Rows = rows
	case "tidb_instance":
		instances, err := drw.QueryClusterHardwareDistinctInstance(ctx, db, "tidb")
		if err != nil {
			return nil, err
		}
		resp.Instances = instances
	case "tidb_cpu_cores":
		rows, err := drw.QueryClusterHardware(ctx, db, "tidb", "", "", "cpu-physical-cores")
		if err != nil {
			return nil, err
		}
		resp.Rows = rows
	case "tikv_cpu_cores":
		rows, err := drw.QueryClusterHardware(ctx, db, "tikv", "", "", "cpu-physical-cores")
		if err != nil {
			return nil, err
		}
		resp.Rows = rows
	case "tikv_mem_capacity":
		rows, err := drw.QueryClusterHardware(ctx, db, "tikv", "memory", "memory", "capacity")
		if err != nil {
			return nil, err
		}
		resp.Rows = rows
	case "tikv_instance":
		instances, err := drw.QueryClusterHardwareDistinctInstance(ctx, db, "tikv")
		if err != nil {
			return nil, err
		}
		resp.Instances = instances
	default:
		return nil, fmt.Errorf("unsupported reqType: %s", req.ReqType)
	}
	return &resp, nil
}

func (s *Service) AddChannelDatasource(ctx context.Context, channelId, datasourceId int) (*message.CommonResp, error) {
	ds := &channel.ChannelDatasource{
		ChannelId:    channelId,
		DataSourceId: datasourceId,
	}
	_, err := models.GetChannelReaderWriter().CreateChannelDatasource(ctx, ds)
	if err != nil {
		return nil, err
	}
	return &message.CommonResp{}, nil
}

func (s *Service) GetChannelDatasourceList(ctx context.Context, channelId int) (*message.GetChannelDatasourceListResp, error) {
	log.Infof("GetChannelDatasourceList service request: channelId:%d", channelId)

	// 1. 根据channelId查询channel_datasources表里所有对应的data_source_id
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, channelId)
	if err != nil {
		log.Errorf("get channel datasources failed. channelId:%d, err:%v", channelId, err)
		return nil, err
	}

	// 2. 根据data_source_id查询datasource表里面的记录，并返回列表
	dataSources := make([]*message.DataSource, 0, len(channelDatasources))
	for _, cd := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, cd.DataSourceId)
		if err != nil {
			log.Errorf("get datasource failed. datasourceId:%d, err:%v", cd.DataSourceId, err)
			return nil, err
		}
		dataSources = append(dataSources, buildDataSourceMessageFromModel(ds))
	}

	return &message.GetChannelDatasourceListResp{DataSources: dataSources}, nil
}

func buildDataSourceMessageFromModel(model *datasource.Datasource) *message.DataSource {
	return &message.DataSource{
		DataSourceID:     model.DatasourceId,
		DataSourceName:   model.DatasourceName,
		DbType:           model.DbType,
		HostIp:           model.HostIp,
		HostPort:         model.HostPort,
		DbName:           model.DbName,
		ServiceName:      model.ServiceName,
		User:             model.UserName,
		PasswordEncrypt:  model.PasswordEncrypt,
		ConnectParams:    model.ConnectParams,
		TableOption:      model.TableOption,
		Charset:          model.Charset,
		ConnectionStatus: model.ConnectionStatus,
		ProxySourceId:    model.ProxySourceId,

		BaseFields: common.BuildBaseFieldsMessageFromModel(model.Entity),

		// for dsg, for enhance
		EnableIncrementSync: model.EnableIncrementSync,
		DBVersion:           model.DBVersion,
		PDBFlag:             model.PDBFlag,
		LinkFlag:            model.LinkFlag,
		PDBName:             model.PDBName,
		PDBDBName:           model.PDBDBName,
		ASMSid:              model.ASMSid,
		ASMOracleHome:       model.ASMOracleHome,
		ASMHome:             model.ASMHome,
		ASMDBUser:           model.ASMDBUser,
		ASMDBPasswdEncrypt:  model.ASMDBPasswdEncrypt,
		DBConnectionMode:    model.DBConnectionMode,
		SIDName:             model.SIDName,
		LogFileStoreMode:    model.LogFileStoreMode,
		ASMDBIp:             model.ASMDBIp,
		ASMDBPort:           model.ASMDBPort,
		ASMDBName:           model.ASMDBName,
		OracleHome:          model.OracleHome,
		OracleSID:           model.OracleSID,
		AsSysDBA:            model.AsSysDBA,
		HostList:            model.HostList,
		PrometheusUrl:       model.PrometheusUrl,
	}
}

// DeleteChannelDatasourceByChannelIdAndDatasourceId 删除 channel-datasource 关联
func (s *Service) DeleteChannelDatasourceByChannelIdAndDatasourceId(ctx context.Context, channelId int, datasourceId int) (*message.CommonResp, error) {
	_, err := models.GetChannelReaderWriter().DeleteChannelDatasourceByChannelIdAndDatasourceId(ctx, channelId, datasourceId)
	if err != nil {
		return nil, err
	}
	return &message.CommonResp{}, nil
}
