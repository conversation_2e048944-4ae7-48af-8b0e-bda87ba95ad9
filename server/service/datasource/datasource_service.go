package datasource

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	transdbconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/mysql"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	pkgcommon "gitee.com/pingcap_enterprise/tms/pkg/common"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/crypto"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/service/channel"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type Service struct {
}

func NewDataSourceService() *Service {
	return &Service{}
}

func GetDataSourceVersion(ctx context.Context, ds *datasource.Datasource) (string, error) {
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		oraDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			ServiceName: datasourcepkg.GetServiceName(ds),
			Charset:     ds.Charset,
		}, "")
		if err != nil {
			return "", fmt.Errorf("create datasource conn failed: %v", err)
		}
		oracleStoreImpl := pkgcommon.NewOracleStore(oraDB)
		version, err := oracleStoreImpl.DBVersion(ctx)
		if err != nil {
			log.Errorf("GetDataSourceVersion error: %v", err)
			return "", err
		}
		return version, nil
	} else if ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		tidb, err := mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
			Username:      ds.UserName,
			Password:      ds.PasswordValue,
			Host:          ds.HostIp,
			Port:          ds.HostPort,
			ConnectParams: ds.ConnectParams,
			Charset:       ds.Charset,
		})
		if err != nil {
			return "", fmt.Errorf("create datasource conn failed: %v", err)
		}
		version, err := tidb.GetMySQLDBVersion()
		idx := strings.Index(version, "-v")
		var vPart string
		if idx != -1 {
			vPart = version[idx+1:]
			version = vPart
		}
		if err != nil {
			log.Errorf("GetMySQLDBVersion error: %v", err)
			return "", err
		}
		return version, nil
	}
	return "", nil
}

func GetDataSourceVersionCode(ctx context.Context, ds *datasource.Datasource) (string, error) {
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		oraDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			ServiceName: datasourcepkg.GetServiceName(ds),
			Charset:     ds.Charset,
		}, "")
		if err != nil {
			return "", fmt.Errorf("create datasource conn failed: %v", err)
		}
		oracleStoreImpl := pkgcommon.NewOracleStore(oraDB)
		version, err := oracleStoreImpl.DBVersionCode(ctx)
		if err != nil {
			log.Errorf("GetDataSourceVersion error: %v", err)
			return "", err
		}
		return version, nil
	} else if ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		tidb, err := mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
			Username:      ds.UserName,
			Password:      ds.PasswordValue,
			Host:          ds.HostIp,
			Port:          ds.HostPort,
			ConnectParams: ds.ConnectParams,
			Charset:       ds.Charset,
		})
		if err != nil {
			return "", fmt.Errorf("create datasource conn failed: %v", err)
		}
		version, err := tidb.GetMySQLDBVersion()
		if err != nil {
			log.Errorf("GetMySQLDBVersion error: %v", err)
			return "", err
		}
		return version, nil
	}
	return "", nil
}

func GetDataSourceCharacterSet(ctx context.Context, ds *datasource.Datasource) (string, error) {
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		oraDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			ServiceName: datasourcepkg.GetServiceName(ds),
			Charset:     ds.Charset,
		}, "")
		if err != nil {
			return "", fmt.Errorf("create datasource conn failed: %v", err)
		}
		charset, err := oraDB.GetOracleDBCharacterSet()
		if err != nil {
			log.Errorf("GetOracleDBCharacterSet error: %v", err)
			return "", err
		}
		oracleDBCharset := strings.Split(charset, ".")[1]
		return oracleDBCharset, nil
	} else if ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		tidb, err := mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
			Username:      ds.UserName,
			Password:      ds.PasswordValue,
			Host:          ds.HostIp,
			Port:          ds.HostPort,
			ConnectParams: ds.ConnectParams,
			Charset:       ds.Charset,
		})
		if err != nil {
			return "", fmt.Errorf("create datasource conn failed: %v", err)
		}
		charset, err := tidb.GetMySQLDBServerCharacterSet()
		if err != nil {
			log.Errorf("GetMySQLDBServerCharacterSet error: %v", err)
			return "", err
		}
		return charset, nil
	}
	return "", nil
}

func CheckDataSource(ctx context.Context, ds *datasource.Datasource) error {
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		log.Infof("checking oracle datasource, host:%s, port:%d, serviceName:%s, charset:%s", ds.HostIp, ds.HostPort, datasourcepkg.GetServiceName(ds), ds.Charset)
		_, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			ServiceName: datasourcepkg.GetServiceName(ds),
			Charset:     ds.Charset,
		}, "")
		if err != nil {
			return fmt.Errorf("create datasource conn failed: %v", err)
		}
	} else if ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		log.Infof("checking tidb/mysql datasource, host:%s, port:%d, charset:%s", ds.HostIp, ds.HostPort, ds.Charset)
		tidb, err := mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
			Username:      ds.UserName,
			Password:      ds.PasswordValue,
			Host:          ds.HostIp,
			Port:          ds.HostPort,
			ConnectParams: ds.ConnectParams,
			//TableOption:   ds.TableOption,
			Charset: ds.Charset,
		})
		if err != nil {
			return fmt.Errorf("create datasource conn failed: %v", err)
		}

		collation, err := tidb.GetMySQLDBServerCollation()
		if err != nil {
			log.Errorf("GetMySQLDBServerCollation error: %v", err)
			return err
		}
		log.Infof("collation: %s", collation)
	}
	return nil
}

func (s *Service) GetDataSourceVersion(ctx context.Context, req *message.CreateOrUpdateDataSourceReq) (*message.GetDataSourceVersionResp, error) {
	log.Infof("ges data source version request: %v", req.DataSource)
	ds := buildDataSourceModelFromMessage(req.DataSource)
	ds.ConnectionStatus = constants.CONNECTION_NOT_TEST
	if ds.DbName == "" && (ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY) {
		ds.DbName = "tidb"
	}

	Version, err := GetDataSourceVersion(ctx, ds)
	if err != nil {
		log.Errorf("GetDataSourceVersion %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}

	return &message.GetDataSourceVersionResp{Version: Version}, nil
}

func (s *Service) GetOracleVersionCode(ctx context.Context, req *message.CreateOrUpdateDataSourceReq) (*message.GetDataSourceVersionResp, error) {
	log.Infof("ges data source charset request: %v", req.DataSource)
	ds := buildDataSourceModelFromMessage(req.DataSource)
	ds.ConnectionStatus = constants.CONNECTION_NOT_TEST
	if ds.DbName == "" && (ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY) {
		ds.DbName = "tidb"
	}

	Version, err := GetDataSourceVersionCode(ctx, ds)
	if err != nil {
		log.Errorf("GetOracleVersionCode %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}

	return &message.GetDataSourceVersionResp{Version: Version}, nil
}

func (s *Service) CreateDataSource(ctx context.Context, req *message.CreateOrUpdateDataSourceReq) (*message.CreateOrUpdateDataSourceResp, error) {
	log.Infof("create data source service request: %v", req.DataSource)
	ds := buildDataSourceModelFromMessage(req.DataSource)
	ds.ConnectionStatus = constants.CONNECTION_NOT_TEST
	if ds.DbName == "" && (ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY) {
		ds.DbName = "tidb"
	}

	charset, err := GetDataSourceCharacterSet(ctx, ds)
	if err != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}
	ds.Charset = strings.ToUpper(charset)

	version, err := GetDataSourceVersion(ctx, ds)
	if err != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}
	ds.DBVersion = version

	chkErr := CheckDataSource(ctx, ds)
	if chkErr != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, chkErr)
		return nil, chkErr
	}

	ds, err = models.GetDatasourceReaderWriter().CreateDataSource(ctx, ds)
	if err != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}
	log.Infof("create datasource successfully, ds: %v", ds)
	return &message.CreateOrUpdateDataSourceResp{}, nil
}

func (s *Service) UpdateDataSource(ctx context.Context, req *message.CreateOrUpdateDataSourceReq) (*message.CreateOrUpdateDataSourceResp, error) {
	log.Infof("update data source service request: %v", req.DataSource)
	_, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		log.Errorf("when update datasource, get old datasource failed. datasourceId: %d, error: %v", req.DataSourceID, err)
		return nil, err
	}
	req.ConnectionStatus = constants.CONNECTION_NOT_TEST
	if req.DbName == "" && req.DbType == constants.DB_TYPE_TIDB {
		req.DbName = "tidb"
	}

	ds := buildDataSourceModelFromMessage(req.DataSource)

	charset, err := GetDataSourceCharacterSet(ctx, ds)
	if err != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}
	ds.Charset = strings.ToUpper(charset)

	version, err := GetDataSourceVersion(ctx, ds)
	if err != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, err)
		return nil, err
	}
	ds.DBVersion = version

	chkErr := CheckDataSource(ctx, ds)
	if chkErr != nil {
		log.Errorf("create datasource %s failed. error: %v", req.DataSourceID, chkErr)
		return nil, chkErr
	}

	dataSource, err := models.GetDatasourceReaderWriter().UpdateDataSource(ctx, ds)
	if err != nil {
		log.Errorf("update datasource %d failed. error: %v", req.DataSourceID, err)
		return nil, err
	}
	log.Infof("update datasource successfully, dataSource: %v", dataSource)
	return &message.CreateOrUpdateDataSourceResp{}, nil
}

func (s *Service) BatchDelete(ctx context.Context, req *message.BatchDeleteDataSourceReq) (*message.BatchDeleteDataSourceResp, error) {
	log.Infof("batch delete datasource service request: %d", req.DataSourceIDs)
	if len(req.DataSourceIDs) > 999 {
		log.Errorf("batch size (%d) too large,greater than 999", len(req.DataSourceIDs))
		return nil, errors.NewError(errors.TIMS_BATCH_SIZE_TOO_LARGE, "batch size of datasourceIds too large")
	}
	err := models.GetDatasourceReaderWriter().BatchDelete(ctx, req.DataSourceIDs)
	if err != nil {
		log.Errorf("batch delete datasource failed, dataSourceIds: %v, error: %v", req.DataSourceIDs, err)
		return nil, err
	}
	return &message.BatchDeleteDataSourceResp{}, nil
}

func (s *Service) Detail(ctx context.Context, req *message.DetailDataSourceReq) (*message.DetailDataSourceResp, error) {
	log.Infof("detail datasource service request: %d", req.DataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		log.Errorf("get datasource failed, datasource id is %d, error: %v", req.DataSourceID, err)
		return nil, err
	}
	return &message.DetailDataSourceResp{DataSource: buildDataSourceMessageFromModel(ds)}, nil
}

func (s *Service) TestConnection(ctx context.Context, req *message.TestConnectionReq) (*message.TestConnectionResp, error) {
	log.Infof("test connection service request,DataSourceID: %d", req.DataSourceID)
	ds, getErr := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if getErr != nil {
		return nil, getErr
	}

	var connectType message.TryConnectionType
	if ds.EnableIncrementSync && ds.LogFileStoreMode == "A" {
		connectType = message.TryConnectionTypeDefaultAndASM
	} else {
		connectType = message.TryConnectionTypeDefault
	}

	status, err := getConnectionStatus(ds, connectType)

	ds.ConnectionStatus = status
	_, saveErr := models.GetDatasourceReaderWriter().UpdateDataSource(ctx, ds)
	if saveErr != nil {
		log.Errorf("save connection status failed, datasourcId is %d, status is %s. err: %v", ds.DatasourceId, status, saveErr)
		return nil, errors.NewErrorf(errors.TIMS_DATASOURCE_SAVE_CONNECT_STATUS_FAILED, "save connection status failed.connection status is %s. err: %v", status, saveErr)
	}

	if status == constants.CONNECTION_SUCCESS {
		log.Infof("test datasource %d connection successfully.", req.DataSourceID)
		return &message.TestConnectionResp{}, nil
	} else {
		log.Infof("test datasource %d connection failed.err:%v", req.DataSourceID, err)
		return nil, errors.NewErrorf(errors.TIMS_DATASOURCE_CONNECT_FAILED, "connect to db failed, %s", err)
	}
}

func (s *Service) TryConnection(ctx context.Context, req *message.TryDataSourceReq) (*message.TestConnectionResp, error) {
	log.Infof("TryConnection: %v", req)
	ds := buildDataSourceModelFromMessage(req.DataSource)

	status, err := getConnectionStatus(ds, req.TryConnectionType)
	if err != nil {
		return nil, errors.NewErrorf(errors.TIMS_DATASOURCE_CONNECT_FAILED, "connect to db failed, %s", err)
	}

	if status == constants.CONNECTION_SUCCESS {
		log.Infof("try datasource %d connection successfully.", req.DataSourceID)
		return &message.TestConnectionResp{}, nil
	} else {
		log.Infof("try datasource %d connection failed.err:%v", req.DataSourceID, err)
		return nil, errors.NewErrorf(errors.TIMS_DATASOURCE_CONNECT_FAILED, "connect to db failed, %s", err)
	}
}

func getConnectionStatus(ds *datasource.Datasource, connectionType message.TryConnectionType) (string, error) {
	var (
		err    error
		asmErr error
	)

	log.Infof("testing connection, dsName:%s, dbType:%s, connectionType:%s", ds.DatasourceName, ds.DbType, connectionType.String())
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		switch connectionType {
		case message.TryConnectionTypeASM:
			err = testOracleASMConnection(ds)
		case message.TryConnectionTypeDefaultAndASM:
			err = testOracleConnection(ds)
			if ds.EnableIncrementSync {
				asmErr = testOracleASMConnection(ds)
			}
		default:
			err = testOracleConnection(ds)
		}
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		err = testMySQLConnection(ds)
	} else {
		log.Errorf("unknown database type %s", ds.DbType)
		err = errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}

	if err != nil {
		return constants.CONNECTION_FAILED, err
	}
	if asmErr != nil {
		return constants.CONNECTION_FAILED, asmErr
	}

	return constants.CONNECTION_SUCCESS, nil
}

func testMySQLConnection(ds *datasource.Datasource) error {
	_, err := models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
	if err != nil {
		log.Infof("connect to mysql failed. err: %v", err)
		return err
	}
	return nil
}

func testOracleConnection(ds *datasource.Datasource) error {
	oracleConn, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
	if err != nil {
		log.Errorf("connect to oracle failed. err: %v", err)
		return err
	}
	log.Infof("testing oracle, setting module and action")
	if _, setModuleErr := oracleConn.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
		log.Errorf("connect to oracle success, set module failed. err: %v", setModuleErr)
		return err
	}
	return nil
}

func testOracleASMConnection(ds *datasource.Datasource) error {
	_, err := models.PingOracle(ds.ASMDBUser, ds.ASMDBPasswdValue, ds.ASMDBIp, ds.ASMDBPort, ds.ASMDBName, "", true)
	if err != nil {
		log.Errorf("connect to oracle asm failed. err: %v", err)
		return err
	}
	return nil
}

func (s *Service) List(ctx context.Context, req *message.ListDataSourcesReq) (*message.ListDataSourcesResp, *message.Page, error) {
	log.Infof("list datasource service request: %v", req.PageRequest)

	dsName := strings.TrimSpace(req.DataSourceName)
	rw := models.GetDatasourceReaderWriter()
	dataSources, total, err := rw.List(ctx, req.Page, req.PageSize, dsName, req.DbType, req.ConnectionStatus)
	if err != nil {
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	dataSourcesMessage := make([]*message.DataSource, 0, req.PageSize)
	for _, ds := range dataSources {
		dataSourcesMessage = append(dataSourcesMessage, buildDataSourceMessageFromModel(ds))
	}
	return &message.ListDataSourcesResp{DataSources: dataSourcesMessage}, page, nil
}

func (s *Service) GetDataSourceSchemas(ctx context.Context, req *message.GetDataSourceSchemasReq) (*message.GetDataSourceSchemasResp, error) {
	log.Infof("GetDataSourceSchemas service request DataSourceID: %d", req.DataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		return nil, err
	}
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err = models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleSchemas(ctx, db)
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err = models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlSchemas(ctx, db)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
	if err != nil {
		return nil, err
	}
	log.Debugf("datasource id :%d schemas:%v", req.DataSourceID, schemas)
	return &message.GetDataSourceSchemasResp{SchemaInfos: schemas}, nil
}

func (s *Service) GetDataSourceSchemaTables(ctx context.Context, req *message.GetDataSourceSchemaTablesReq) (*message.GetDataSourceSchemaTablesResp, error) {
	log.Infof("GetDataSourceSchemaTables service request, DataSourceID: %d", req.DataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		return nil, err
	}
	var db *sql.DB
	defer func() {
		if db != nil {
			db.Close()
		}
	}()
	var schemas []*structs.SchemaInfo
	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		db, err = models.OpenOracle(models.BuildOracleConfigFromDatasource(ds, ""))
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetOracleTables(ctx, db, req.Schemas)
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		db, err = models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			return nil, err
		}
		schemas, err = models.GetDatasourceReaderWriter().GetMysqlTables(ctx, db, req.Schemas)
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
	if err != nil {
		return nil, err
	}
	log.Debugf("datasource id :%d tables:%v", req.DataSourceID, schemas)
	return &message.GetDataSourceSchemaTablesResp{SchemaInfos: schemas}, nil
}

func buildDataSourceMessageFromModel(model *datasource.Datasource) *message.DataSource {
	return &message.DataSource{
		DataSourceID:   model.DatasourceId,
		DataSourceName: model.DatasourceName,
		DbType:         model.DbType,
		HostIp:         model.HostIp,
		HostPort:       model.HostPort,
		DbName:         model.DbName,
		ServiceName:    model.ServiceName,
		User:           model.UserName,
		//Password:         model.PasswordValue,
		PasswordEncrypt:  model.PasswordEncrypt,
		ConnectParams:    model.ConnectParams,
		TableOption:      model.TableOption,
		Charset:          model.Charset,
		ConnectionStatus: model.ConnectionStatus,
		ProxySourceId:    model.ProxySourceId,

		BaseFields: common.BuildBaseFieldsMessageFromModel(model.Entity),

		// for dsg, for enhance
		EnableIncrementSync: model.EnableIncrementSync,
		DBVersion:           model.DBVersion,
		PDBFlag:             model.PDBFlag,
		LinkFlag:            model.LinkFlag,
		PDBName:             model.PDBName,
		PDBDBName:           model.PDBDBName,
		ASMSid:              model.ASMSid,
		ASMOracleHome:       model.ASMOracleHome,
		ASMHome:             model.ASMHome,
		ASMDBUser:           model.ASMDBUser,
		ASMDBPasswdEncrypt:  model.ASMDBPasswdEncrypt,
		DBConnectionMode:    model.DBConnectionMode,
		SIDName:             model.SIDName,
		LogFileStoreMode:    model.LogFileStoreMode,
		ASMDBIp:             model.ASMDBIp,
		ASMDBPort:           model.ASMDBPort,
		ASMDBName:           model.ASMDBName,
		OracleHome:          model.OracleHome,
		OracleSID:           model.OracleSID,
		AsSysDBA:            model.AsSysDBA,
		HostList:            model.HostList,
		PrometheusUrl:       model.PrometheusUrl,
	}
}

func buildDataSourceModelFromMessage(messageContent *message.DataSource) *datasource.Datasource {
	if strings.EqualFold(messageContent.Password, "") {
		messageContent.Password = crypto.AesDefaultDecrypt(messageContent.PasswordEncrypt, crypto.DefaultKey)
	}
	if strings.EqualFold(messageContent.ASMDBPasswd, "") {
		messageContent.ASMDBPasswd = crypto.AesDefaultDecrypt(messageContent.ASMDBPasswdEncrypt, crypto.DefaultKey)
	}
	datasource := &datasource.Datasource{
		DatasourceId:     messageContent.DataSourceID,
		DatasourceName:   messageContent.DataSourceName,
		DbType:           messageContent.DbType,
		HostIp:           messageContent.HostIp,
		HostPort:         messageContent.HostPort,
		DbName:           messageContent.DbName,
		ServiceName:      messageContent.ServiceName,
		UserName:         messageContent.User,
		PasswordValue:    messageContent.Password,
		PasswordEncrypt:  messageContent.PasswordEncrypt,
		ConnectParams:    messageContent.ConnectParams,
		TableOption:      messageContent.TableOption,
		Charset:          messageContent.Charset,
		ConnectionStatus: messageContent.ConnectionStatus,
		ProxySourceId:    messageContent.ProxySourceId,

		EnableIncrementSync: messageContent.EnableIncrementSync,
		DBVersion:           messageContent.DBVersion,
		PDBFlag:             messageContent.PDBFlag,
		LinkFlag:            messageContent.LinkFlag,
		PDBName:             messageContent.PDBName,
		PDBDBName:           messageContent.PDBDBName,
		ASMSid:              messageContent.ASMSid,
		ASMOracleHome:       messageContent.ASMOracleHome,
		ASMHome:             messageContent.ASMHome,
		ASMDBUser:           messageContent.ASMDBUser,
		ASMDBPasswdValue:    messageContent.ASMDBPasswd,
		ASMDBPasswdEncrypt:  messageContent.ASMDBPasswdEncrypt,
		DBConnectionMode:    messageContent.DBConnectionMode,
		SIDName:             messageContent.SIDName,
		LogFileStoreMode:    messageContent.LogFileStoreMode,
		ASMDBIp:             messageContent.ASMDBIp,
		ASMDBPort:           messageContent.ASMDBPort,
		ASMDBName:           messageContent.ASMDBName,
		OracleHome:          messageContent.OracleHome,
		OracleSID:           messageContent.OracleSID,
		AsSysDBA:            messageContent.AsSysDBA,
		HostList:            messageContent.HostList,
		PrometheusUrl:       messageContent.PrometheusUrl,

		Entity: common.BuildEntityModelFromMessage(messageContent.BaseFields),
	}
	if datasource.OracleSID == "" {
		datasource.OracleSID = datasource.SIDName
	}
	return datasource
}

func (s *Service) GetPrivilegeCheckList(ctx context.Context, req *message.DetailDataSourceReq) ([]*structs.Privilege, error) {
	log.Infof("GetPrivilegeCheckList service request: %d", req.DataSourceID)
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		log.Errorf("get datasource failed, datasource id is %d, error: %v", req.DataSourceID, err)
		return nil, err
	}

	var checkPrivileges []*structs.Privilege

	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		if ds.EnableIncrementSync {
			checkPrivileges = getOracleIncrementUserDefaultPrivileges(ds)
		} else {
			checkPrivileges = getOracleUserDefaultPrivileges(ds)
		}
	}

	if ds.DbType == constants.DB_TYPE_ORACLE || ds.DbType == constants.DB_TYPE_ORACLE_ADG {
		// 获取oracle连接
		oraInfo := transdbconfig.OracleConfig{
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			ServiceName: datasourcepkg.GetServiceName(ds),
		}
		oracleDB, err := oracle.NewOracleDBEngine(ctx, oraInfo, "")
		if err != nil {
			log.Errorf("get datasource failed, datasource id is %d, error: %v", req.DataSourceID, err)
			return checkPrivileges, nil
		}

		privileges, err := channel.GetOraclePrivilegeCheckList(ctx, oracleDB.OracleDB, ds)
		if err != nil {
			log.Errorf("GetOraclePrivilegeCheckList failed, datasource id is %d, error: %v", req.DataSourceID, err)
			return checkPrivileges, nil
		}
		for _, p := range privileges {
			for _, cp := range checkPrivileges {
				if strings.EqualFold(p.Grantee, cp.Grantee) && strings.EqualFold(p.Privilege, cp.Privilege) {
					cp.Result = "Y"
					break
				}
			}
		}
		return checkPrivileges, nil
	} else if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB || ds.DbType == constants.DB_TYPE_TIDB_PROXY {
		checkPrivileges = getTiDBUserDefaultPrivileges(ds)

		// 获取mysql连接
		dbT, err := models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
		if err != nil {
			log.Errorf("get datasource failed, datasource id is %d, error: %v", req.DataSourceID, err)
			return checkPrivileges, nil
		}
		privileges, err := channel.GetTargetBasePrivilegeCheckList(ctx, dbT, ds)
		if err != nil {
			log.Errorf("GetTargetBasePrivilegeCheckList failed, datasource id is %d, error: %v", req.DataSourceID, err)
			return checkPrivileges, nil
		}
		for _, p := range privileges {
			for _, cp := range checkPrivileges {
				if strings.EqualFold(p.Grantee, cp.Grantee) && strings.EqualFold(p.PrivilegeVal, cp.PrivilegeVal) {
					cp.Result = "Y"
					break
				}
			}
		}
		return checkPrivileges, nil
	} else {
		return nil, errors.NewErrorf(errors.TIMS_UNKNOWN_DATABASE_TYPE, "unknown database type %s", ds.DbType)
	}
}

func getTiDBUserDefaultPrivileges(ds *datasource.Datasource) []*structs.Privilege {
	return []*structs.Privilege{
		{
			Grantee:      ds.UserName,
			Privilege:    "ALTER ANY TABLE",
			PrivilegeVal: "Alter_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "CREATE ANY TABLE",
			PrivilegeVal: "Create_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "DELETE ANY TABLE",
			PrivilegeVal: "Delete_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "DROP ANY TABLE",
			PrivilegeVal: "Drop_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "CREATE INDEX",
			PrivilegeVal: "Index_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "Insert ANY TABLE",
			PrivilegeVal: "Insert_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "Select ANY TABLE",
			PrivilegeVal: "Select_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
		{
			Grantee:      ds.UserName,
			Privilege:    "Update ANY TABLE",
			PrivilegeVal: "Update_priv",
			Result:       "N",
			TmsTask:      "通用权限",
		},
	}
}

func getOracleIncrementUserDefaultPrivileges(ds *datasource.Datasource) []*structs.Privilege {
	ps := getOracleUserDefaultPrivileges(ds)
	ps = append(ps,
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "ALTER SYSTEM",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "EXP_FULL_DATABASE",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "CONNECT",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "RESOURCE",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "SELECT ON SYS.USER$",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "EXECUTE ON DBMS_METADATA",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
		&structs.Privilege{
			Grantee:   ds.UserName,
			Privilege: "EXECUTE ON DBMS_FLASHBACK",
			Result:    "N",
			TmsTask:   "增量数据迁移",
		},
	)

	return ps
}
func getOracleUserDefaultPrivileges(ds *datasource.Datasource) []*structs.Privilege {
	return []*structs.Privilege{
		{
			Grantee:   ds.UserName,
			Privilege: "ADMINISTER ANY SQL TUNING SET",
			Result:    "N",
			TmsTask:   "兼容性评估",
		},
		{
			Grantee:   ds.UserName,
			Privilege: "CREATE JOB",
			Result:    "N",
			TmsTask:   "兼容性评估",
		},
		{
			Grantee:   ds.UserName,
			Privilege: "CREATE LIBRARY",
			Result:    "N",
			TmsTask:   "数据校验",
		},
		{
			Grantee:   ds.UserName,
			Privilege: "SELECT ANY DICTIONARY",
			Result:    "N",
			TmsTask:   "通用权限",
		},
		{
			Grantee:   ds.UserName,
			Privilege: "SELECT ANY TABLE",
			Result:    "N",
			TmsTask:   "通用权限",
		},
	}
}

func (s *Service) GetTablePartitions(ctx context.Context, req *message.GetTablePartitionReq) ([]*structs.TablePartitions, error) {
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, req.DataSourceID)
	if err != nil {
		log.Errorf("get datasource failed, datasource id is %d, error: %v", req.DataSourceID, err)
		return nil, err
	}
	// 获取oracle连接
	oraInfo := transdbconfig.OracleConfig{
		Host:        ds.HostIp,
		Port:        ds.HostPort,
		Username:    ds.UserName,
		Password:    ds.PasswordValue,
		ServiceName: datasourcepkg.GetServiceName(ds),
	}
	oracleDB, err := oracle.NewOracleDBEngine(ctx, oraInfo, "")
	return models.GetDatasourceReaderWriter().GetOracleTablePartitions(ctx, oracleDB.OracleDB, req.SchemaName, req.TableName)
}

func (s *Service) GetDataSourceVersionById(ctx context.Context, dataSourceId int) (*message.GetDataSourceVersionResp, error) {
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, dataSourceId)
	if err != nil {
		return nil, err
	}
	version, err := GetDataSourceVersion(ctx, ds)
	if err != nil {
		return nil, err
	}
	return &message.GetDataSourceVersionResp{Version: version}, nil
}

func (s *Service) GetDataSourceCharacterSetById(ctx context.Context, dataSourceId int) (string, error) {
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, dataSourceId)
	if err != nil {
		log.Errorf("get datasource failed, datasource id is %d, error: %v", dataSourceId, err)
		return "", err
	}
	charset, err := GetDataSourceCharacterSet(ctx, ds)
	if err != nil {
		log.Errorf("GetDataSourceCharacterSet failed, datasource id is %d, error: %v", dataSourceId, err)
		return "", err
	}
	return charset, nil
}

func (s *Service) GetDataSourceCharacterSetByIdResp(ctx context.Context, dataSourceId int) (*message.GetDataSourceCharacterSetResp, error) {
	charset, err := s.GetDataSourceCharacterSetById(ctx, dataSourceId)
	if err != nil {
		return nil, err
	}
	return &message.GetDataSourceCharacterSetResp{CharacterSet: charset}, nil
}
