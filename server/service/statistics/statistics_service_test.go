package statistics

import (
	"testing"

	"github.com/coreos/go-semver/semver"
	"github.com/pingcap/tidb/br/pkg/version"
)

func Test_buildAnalyzeSQL(t *testing.T) {
	type args struct {
		schemaName     string
		tableName      string
		tidbServerInfo version.ServerInfo
		sampleRate     float64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "5.1",
			args: args{
				schemaName: "test_schema",
				tableName:  "test_table",
				tidbServerInfo: version.ServerInfo{ServerVersion: &semver.Version{
					Major: 5,
					Minor: 1,
				}},
				sampleRate: 0.51,
			},
			want: "analyze table test_schema.test_table",
		},
		{
			name: "5.4",
			args: args{
				schemaName: "test_schema",
				tableName:  "test_table",
				tidbServerInfo: version.ServerInfo{ServerVersion: &semver.Version{
					Major: 5,
					Minor: 4,
				}},
				sampleRate: 0.54,
			},
			want: "analyze table test_schema.test_table with 0.540000 samplerate",
		},
		{
			name: "6.0",
			args: args{
				schemaName: "test_schema",
				tableName:  "test_table",
				tidbServerInfo: version.ServerInfo{ServerVersion: &semver.Version{
					Major: 6,
					Minor: 0,
				}},
				sampleRate: 0.60,
			},
			want: "analyze table test_schema.test_table with 0.600000 samplerate",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := buildAnalyzeSQL(tt.args.schemaName, tt.args.tableName, tt.args.tidbServerInfo, tt.args.sampleRate); got != tt.want {
				t.Errorf("buildAnalyzeSQL() = %v, want %v", got, tt.want)
			}
		})
	}
}
