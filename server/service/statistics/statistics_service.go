package statistics

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/dbutil"
	commonlog "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/pkg/statistics"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	statisticsentity "gitee.com/pingcap_enterprise/tms/server/models/statistics"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

type Service struct {
}

func NewTidbStatsService() *Service {
	return &Service{}
}

func TiDBStatsRun(ctx context.Context, channelId int, taskId int) error {
	// run async
	go statistics.ExecuteTask(ctx, channelId, taskId)
	return nil
}

func (s *Service) GetTidbStatsProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	log.Infof("start GetTidbStatsProgress. taskId:%d", req.TaskId)
	statsTableResult, err := models.GetStatisticsReaderWriter().BatchGetTaskStats(ctx, &statisticsentity.TaskStatistics{
		TaskId: req.TaskId,
	})
	if err != nil {
		log.Errorf(fmt.Sprintf("get statsTableResult failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}
	var (
		TotalNum          = 0
		SuccNum           = 0
		FailNum           = 0
		RunNum            = 0
		WaitNum           = 0
		LastUpdateTime    = req.StartTime
		LogLastUpdateTime = req.StartTime
	)
	for _, v := range statsTableResult {
		TotalNum += 1
		if v.AnalyzeStatus == constants.StatStatusSuccess.String() {
			SuccNum += 1
		} else if v.AnalyzeStatus == constants.StatStatusFailed.String() {
			FailNum += 1
		} else if v.AnalyzeStatus == constants.StatStatusRunning.String() {
			RunNum += 1
		} else if v.AnalyzeStatus == constants.StatStatusWaiting.String() {
			WaitNum += 1
		}
		if v.UpdatedAt.After(LastUpdateTime) {
			LastUpdateTime = v.UpdatedAt
		}
	}

	TidbStatsProgress := &message.GetTaskProgressResp{
		TaskId:         req.TaskId,
		StartTime:      req.StartTime,
		TotalNums:      TotalNum,
		SuccessNums:    SuccNum,
		FailedNums:     FailNum,
		RunningNums:    RunNum,
		LastUpdateTime: LastUpdateTime,
		TotalDuration:  LastUpdateTime.Sub(req.StartTime).String(),
		TaskLogFile:    "./data/logs/tidbstats.log",
	}

	statsLogList, err := models.GetTaskReaderWriter().ListTaskLogDetailByTaskIdAndLogTime(ctx, req.TaskId, req.StartTime)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskLogDetailByTaskIdAndLogTime failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}

	var progressLog []*message.TaskProgressLogDetail
	for _, v := range statsLogList {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.LogTime,
			LogLevel:   v.LogLevel,
			LogMessage: v.LogMessage,
		})
		if v.UpdatedAt.After(LogLastUpdateTime) {
			LogLastUpdateTime = v.UpdatedAt
		}
	}

	TidbStatsProgress.ProgressLogInfo = &message.TaskProgressLogInfo{
		TaskId:        req.TaskId,
		LogStartTime:  req.StartTime,
		LogCount:      len(progressLog),
		LogDetailList: progressLog,
	}

	return TidbStatsProgress, nil
}

func (s *Service) GetTidbStatsSummaryByTask(ctx context.Context, req *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	log.Infof("start GetTidbStatsSummaryByTask taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	sumByStateList, err := models.GetStatisticsReaderWriter().GetTaskStatsCountsByStatusSchema(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumByState data failed, err: %v", err))
		return &message.GetTaskDetailResultResp{}, err
	}
	if len(sumByStateList) == 0 {
		err := statistics.InitStatsTables(ctx, req.ChannelId, req.TaskId)
		if err != nil {
			log.Errorf("run InitStatsTables failed, task id is %d, error: %v", req.TaskId, err)
		}
		sumByStateList, err = models.GetStatisticsReaderWriter().GetTaskStatsCountsByStatusSchema(ctx, req.TaskId)
		if err != nil {
			log.Errorf(fmt.Sprintf("get sumByState data failed, err: %v", err))
			return &message.GetTaskDetailResultResp{}, err
		}
	}

	schemaMap := make(map[string]int)
	statusMap := make(map[string]int)
	totalCount := 0

	for _, v := range sumByStateList {
		totalCount += v.Count
		schemaMap[v.SchemaName] += v.Count
		statusMap[v.AnalyzeStatus] += v.Count
	}

	chartData := make([]message.TaskDetailChartData, 0, len(statusMap))
	for k, v := range statusMap {
		chartData = append(chartData, message.TaskDetailChartData{
			Total: totalCount,
			Type:  k,
			Count: v,
			Pct:   fmt.Sprintf("%.2f%%", float64(v)/float64(totalCount)*100),
		})
	}

	// sumBySchema, err := models.GetStatisticsReaderWriter().GetTaskStatsCountsBySchema(ctx, req.TaskId)
	sumBySchema, err := models.GetStatisticsReaderWriter().GetTaskStatsCountsJoinByTask(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumBySchema data failed, err: %v", err))
		return &message.GetTaskDetailResultResp{}, err
	}

	schemaData := lo.Map(sumBySchema, func(v *statisticsentity.TaskStatisticsJoinBySchema, _ int) message.StatisticsSummaryDetail {
		return message.StatisticsSummaryDetail{
			TaskId:        v.TaskId,
			Schema:        v.Schema,
			TotalNums:     v.TotalNums,
			SuccessNums:   v.SuccessNums,
			FailedNums:    v.FailedNums,
			RunningNums:   v.RunningNums,
			WaitingNums:   v.WaitingNums,
			DuplicateNums: v.DuplicateNums,
			SuccessRatio:  fmt.Sprintf("%.2f%%", float64(v.SuccessNums)/float64(v.TotalNums)*100),
			Duration:      v.LastAnalyzeEndtime.Sub(v.LastAnalyzeStarttime).String(),
			StartTime:     v.LastAnalyzeStarttime,
			EndTime:       v.LastAnalyzeEndtime,
		}
	})
	taskDetailSchemaData := message.TaskDetailSchemaData{
		Statistics: schemaData,
	}

	if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		taskInfo.EndTime = time.Now()
	}
	return &message.GetTaskDetailResultResp{
		TaskId:                  req.TaskId,
		StartTime:               taskInfo.StartTime,
		TotalTables:             totalCount,
		TotalDuration:           taskInfo.EndTime.Sub(taskInfo.StartTime).String(),
		TaskDetailChartDataList: chartData,
		TaskDetailSchemaData:    taskDetailSchemaData,
	}, nil
}

func (s *Service) GetTidbStatisticsSummaryBySchema(ctx context.Context, req *message.StatisticsSummarySchemaReq) (*message.StatisticsSummarySchemaResp, error) {
	returnData := make([]*message.StatisticsSummaryDetail, 0)
	log.Infof("start GetDataCompareSummaryBySchema taskId:%d, schema:%s", req.TaskId, req.Schema)
	sumBySchema, err := models.GetStatisticsReaderWriter().GetTaskStatsCountsJoinByTask(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumBySchema data failed, err: %v", err))
		return &message.StatisticsSummarySchemaResp{}, err
	}
	sumData := &message.StatisticsSummaryDetail{}
	for _, v := range sumBySchema {
		if v.Schema == req.Schema {
			sumData.TaskId = v.TaskId
			sumData.Schema = v.Schema
			sumData.TotalNums = v.TotalNums
			sumData.SuccessNums = v.SuccessNums
			sumData.FailedNums = v.FailedNums
			sumData.RunningNums = v.RunningNums
			sumData.WaitingNums = v.WaitingNums
			sumData.DuplicateNums = v.DuplicateNums
			sumData.Duration = v.Duration
			sumData.StartTime = v.LastAnalyzeStarttime
			sumData.EndTime = v.LastAnalyzeEndtime
			sumData.SuccessRatio = fmt.Sprintf("%.2f%%", float64(v.SuccessNums)/float64(v.TotalNums)*100)

			returnData = append(returnData, sumData)
		}
	}

	return &message.StatisticsSummarySchemaResp{StatsSchemaSummary: returnData}, nil
}

func (s *Service) GetTidbStatisticsTableBySchema(ctx context.Context, req *message.StatisticsTablesReq) (*message.StatisticsTablesResp, *message.Page, error) {
	log.Infof("start GetTidbStatisticsTableBySchema taskId:%d, schema:%s", req.TaskId, req.Schema)
	statsBySchemaSate, total, err := models.GetStatisticsReaderWriter().ListTaskStatsTablesByTablePage(ctx, req.TaskId, req.Schema, req.Table, req.Page, req.PageSize)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskStatsTablesByTablePage data failed, err: %v", err))
		return &message.StatisticsTablesResp{}, nil, err
	}

	modelToMsg := make([]*message.TaskStatisticsTable, 0)
	for _, v := range statsBySchemaSate {
		modelToMsg = append(modelToMsg, buildTaskStatisticsMessageFromModel(v))
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	return &message.StatisticsTablesResp{
		ChannelId:   req.ChannelId,
		TaskId:      req.TaskId,
		Schema:      req.Schema,
		TableCount:  int(total),
		TableDetail: modelToMsg,
	}, page, nil
}

func (s *Service) GetTidbStatisticsAndCSTableBySchema(ctx context.Context, req *message.StatisticsSchemaStateReq) (*message.StatisticsSchemaStateResp, *message.Page, error) {
	log.Infof("start GetTidbStatisticsAndCSTableBySchema taskId:%d, schema:%s, status:%s", req.TaskId, req.Schema, req.State)
	statsBySchemaSate, total, err := models.GetStatisticsReaderWriter().ListTaskStatsTablesByPage(ctx, &statisticsentity.TaskStatistics{
		ChannelId:     req.ChannelId,
		TaskId:        req.TaskId,
		SchemaName:    req.Schema,
		AnalyzeStatus: strings.ToLower(req.State),
	}, req.Page, req.PageSize)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskStatsTablesByPage data failed, err: %v", err))
		return &message.StatisticsSchemaStateResp{}, nil, err
	}
	duplicateTable, err := models.GetStatisticsReaderWriter().ListTaskStatsTablesDuplicate(ctx, req.ChannelId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskStatsTablesDuplicate data failed, err: %v", err))
		return &message.StatisticsSchemaStateResp{}, nil, err
	}
	duplicateTableTaskMap := make(map[structs.SchemaTablePair]string)
	duplicateTableCountMap := make(map[structs.SchemaTablePair]int)
	for _, v := range duplicateTable {
		pair := structs.SchemaTablePair{SchemaName: v.SchemaName, TableName: v.TableName}
		duplicateTableTaskMap[pair] = v.DuplicateTasks
		duplicateTableCountMap[pair] = v.DuplicateCount
	}

	modelToMsg := make([]*message.TaskStatisticsJoinDuplicate, 0)
	for _, v := range statsBySchemaSate {
		pair := structs.SchemaTablePair{SchemaName: v.SchemaName, TableName: v.TableName}
		modelToMsg = append(modelToMsg, &message.TaskStatisticsJoinDuplicate{
			TaskStatsId:          v.TaskStatsId,
			SchemaName:           v.SchemaName,
			TableName:            v.TableName,
			LastAnalyzeStarttime: v.LastAnalyzeStarttime,
			LastAnalyzeEndtime:   v.LastAnalyzeEndtime,
			LastAnalyzedRows:     v.LastAnalyzedRows,
			AnalyzeStatus:        v.AnalyzeStatus,
			AnalyzeDuration:      v.LastAnalyzeEndtime.Sub(v.LastAnalyzeStarttime).String(),
			Message:              v.Message,
			DuplicateTasks:       duplicateTableTaskMap[pair],
			DuplicateCount:       duplicateTableCountMap[pair],
		})
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	return &message.StatisticsSchemaStateResp{
		ChannelId:   req.ChannelId,
		TaskId:      req.TaskId,
		Schema:      req.Schema,
		State:       req.State,
		TableCount:  int(total),
		TableDetail: modelToMsg,
	}, page, nil
}

func (s *Service) GetStatsTaskConflictTableBySchema(ctx context.Context, req *message.StatisticsTaskTableDuplReq) (*message.StatisticsTaskTableDuplResp, *message.Page, error) {
	log.Infof("start GetStatsTaskConflictTableBySchema taskId:%d, schema:%s", req.TaskId, req.Schema)
	statsBySchemaSate, total, err := models.GetStatisticsReaderWriter().ListTaskStatsConflictTable(ctx, req.ChannelId, req.TaskId, req.Schema, req.Page, req.PageSize)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskStatsConflictTable data failed, err: %v", err))
		return &message.StatisticsTaskTableDuplResp{}, nil, err
	}

	modelToMsg := make([]*message.ChannelTaskSchemaStatisticsDuplicate, 0)
	for _, v := range statsBySchemaSate {
		modelToMsg = append(modelToMsg, buildTaskStatisticsDuplicateMessageFromModel(v))
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	return &message.StatisticsTaskTableDuplResp{
		ChannelId:   req.ChannelId,
		TaskId:      req.TaskId,
		Schema:      req.Schema,
		TableCount:  int(total),
		TableDetail: modelToMsg,
	}, page, nil
}

func (s *Service) InitTidbStatistics(ctx context.Context, req *message.InitTidbStatisticsReq) (*message.InitTidbStatisticsResp, error) {
	log.Infof("start InitTidbStatistics channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	err := statistics.InitStatsTables(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("run InitStatsTables failed, task id is %d, error: %v", req.TaskId, err)
		return &message.InitTidbStatisticsResp{}, err
	}
	return &message.InitTidbStatisticsResp{}, nil
}

func (s *Service) UpdateTidbStatsTable(ctx context.Context, req *message.UpdateTidbStatisticsReq) (*message.UpdateTidbStatisticsResp, error) {
	log.Infof("start UpdateTidbStatsTable channelId:%d, taskId:%d, tables:%d", req.ChannelId, req.TaskId, len(req.StatsTables))
	listTableStats := make([]*statisticsentity.TaskStatistics, 0, len(req.StatsTables))
	for _, v := range req.StatsTables {
		listTableStats = append(listTableStats, buildTaskStatisticsModelFromMessage(v))
	}
	err := models.GetStatisticsReaderWriter().SaveTaskStats(ctx, listTableStats)
	if err != nil {
		log.Errorf("run SaveTaskStats failed, task id is %d, error: %v", req.TaskId, err)
		return &message.UpdateTidbStatisticsResp{}, err
	}
	return &message.UpdateTidbStatisticsResp{}, nil
}

func (s *Service) DeleteTidbStatsTables(ctx context.Context, req *message.DeleteStatsTaskConflictTablesReq) (*message.DeleteStatsTaskConflictTablesResp, error) {
	log.Infof("start DeleteTidbStatsTables, taskId:%d, tables:%d", req.TaskId, len(req.StatsTables))
	for _, v := range req.StatsTables {
		err := models.GetStatisticsReaderWriter().DeleteTaskStatsByTaskTable(ctx, req.TaskId, v.SchemaName, v.TableName)
		if err != nil {
			log.Errorf("run DeleteTaskStatsByTaskTable failed, task id is %d, table:%s.%s error: %v", req.TaskId, v.SchemaName, v.TableName, err)
		}
	}
	return &message.DeleteStatsTaskConflictTablesResp{}, nil
}

func (s *Service) GetTidbStatsTableCount(ctx context.Context, req *message.GetTidbStatsTableCountReq) (*message.GetTidbStatsTableCountResp, error) {
	log.Infof("start GetTidbStatsTableCount, taskId:%d, tables:%d", req.TaskId, len(req.StatsTables))
	statLogger := commonlog.InitStatisticTaskLogContextHandler(ctx, req.ChannelId, req.TaskId)

	log.Infof("TiDBStatsCtl start, channelid:%d, taskid:%d", req.ChannelId, req.TaskId)
	statLogger.Info("TiDBStatsCtl start")

	channel, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		statLogger.Errorf("get channel failed, channel id is %d, error: %v", req.ChannelId, err)
		return &message.GetTidbStatsTableCountResp{}, err
	}
	statLogger.Infof("Get channel datasource info, sourcedb:%d, tartgetdb:%d", channel.DatasourceIdS, channel.DatasourceIdT)

	targetDB, err := models.GetDatasourceReaderWriter().Get(ctx, channel.DatasourceIdT)
	if err != nil {
		statLogger.Errorf("get databsource failed, Datasource id is %d, error: %v", channel.DatasourceIdS, err)
		return &message.GetTidbStatsTableCountResp{}, err
	}
	dbT := tidbConnConfig{
		Host:     targetDB.HostIp,
		Port:     targetDB.HostPort,
		User:     targetDB.UserName,
		Password: targetDB.PasswordValue,
		Database: targetDB.DbName,
	}
	statLogger.Infof("Get channel datasource info, sourcedb:%d %v", channel.DatasourceIdS, dbT.Host)
	// 获取TiDB数据量
	dbTidb, err := models.OpenMysql(dbT.User, dbT.Password, dbT.Host, dbT.Port, "")
	if err != nil {
		statLogger.Infof("do stats task failed, open target tidb failed. err:%s", err)
		statLogger.Infof("dbConfig:%v", dbT)
		return &message.GetTidbStatsTableCountResp{}, err
	}
	defer dbTidb.Close()
	for _, v := range req.StatsTables {
		rowsNum, err := dbutil.GetTidbTableActualRows(ctx, dbTidb, v.SchemaName, v.TableName)
		if err != nil {
			statLogger.Infof("do stats task failed, get table rows failed. err:%s", err)
			return &message.GetTidbStatsTableCountResp{}, err
		}
		statLogger.Infof("Get tidb table:%s.%s, rows:%d.", v.SchemaName, v.TableName, rowsNum)
		updateErr := models.GetStatisticsReaderWriter().UpdateTaskStatsRowsByTable(ctx, &statisticsentity.TaskStatistics{
			TaskId:     req.TaskId,
			SchemaName: v.SchemaName,
			TableName:  v.TableName,
			TableRows:  rowsNum,
		})
		if updateErr != nil {
			statLogger.Infof("run SaveTaskStats failed, task id is %d, table:%s.%s, error: %v", req.TaskId, v.SchemaName, v.TableName, updateErr)
			return &message.GetTidbStatsTableCountResp{}, updateErr
		}
	}

	log.Infof("GetTidbStatsTableCount finish, taskId:%d, tables:%d", req.TaskId, len(req.StatsTables))
	return &message.GetTidbStatsTableCountResp{}, nil
}

func buildTaskStatisticsModelFromMessage(taskstats *message.TaskStatisticsTable) *statisticsentity.TaskStatistics {
	return &statisticsentity.TaskStatistics{
		TaskStatsId:          taskstats.TaskStatsId,
		ChannelId:            taskstats.ChannelId,
		TaskId:               taskstats.TaskId,
		SchemaName:           taskstats.SchemaName,
		TableName:            taskstats.TableName,
		LastAnalyzeStarttime: taskstats.LastAnalyzeStarttime,
		LastAnalyzeEndtime:   taskstats.LastAnalyzeEndtime,
		AnalyzeStatus:        taskstats.AnalyzeStatus,
		LastAnalyzedRows:     taskstats.LastAnalyzedRows,
		Samplerate:           taskstats.Samplerate,
		TableRows:            taskstats.TableRows,
		Priority:             taskstats.Priority,
		Message:              taskstats.Message,
	}
}

func buildTaskStatisticsMessageFromModel(taskstats *statisticsentity.TaskStatistics) *message.TaskStatisticsTable {
	return &message.TaskStatisticsTable{
		TaskStatsId:          taskstats.TaskStatsId,
		ChannelId:            taskstats.ChannelId,
		TaskId:               taskstats.TaskId,
		SchemaName:           taskstats.SchemaName,
		TableName:            taskstats.TableName,
		LastAnalyzeStarttime: taskstats.LastAnalyzeStarttime,
		LastAnalyzeEndtime:   taskstats.LastAnalyzeEndtime,
		AnalyzeStatus:        taskstats.AnalyzeStatus,
		LastAnalyzedRows:     taskstats.LastAnalyzedRows,
		Samplerate:           taskstats.Samplerate,
		TableRows:            taskstats.TableRows,
		Priority:             taskstats.Priority,
		Message:              taskstats.Message,
		AnalyzeDuration:      taskstats.LastAnalyzeEndtime.Sub(taskstats.LastAnalyzeStarttime).String(),
	}
}

func buildTaskStatisticsDuplicateMessageFromModel(taskstats *statisticsentity.ChannelTaskSchemaStatisticsDuplicate) *message.ChannelTaskSchemaStatisticsDuplicate {
	return &message.ChannelTaskSchemaStatisticsDuplicate{
		ChannelSchtableId:    taskstats.ChannelSchtableId,
		ChannelId:            taskstats.ChannelId,
		TaskId:               taskstats.TaskId,
		SchemaName:           taskstats.SchemaName,
		TableName:            taskstats.TableName,
		LastAnalyzeStarttime: taskstats.LastAnalyzeStarttime,
		LastAnalyzeEndtime:   taskstats.LastAnalyzeEndtime,
		AnalyzeStatus:        taskstats.AnalyzeStatus,
		LastAnalyzedRows:     taskstats.LastAnalyzedRows,
		Samplerate:           taskstats.Samplerate,
		TableRows:            taskstats.TableRows,
		Priority:             taskstats.Priority,
		Message:              taskstats.Message,
		AnalyzeDuration:      taskstats.LastAnalyzeEndtime.Sub(taskstats.LastAnalyzeStarttime).String(),
	}
}

type tidbConnConfig struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Database string `json:"database"`
}
