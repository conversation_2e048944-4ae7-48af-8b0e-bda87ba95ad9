package migration

import (
	"time"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/o2t"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

func GenReverseTableTask(r *o2t.Reverse, tableNameRule map[string]string, tableColumnRule map[string]map[string]string, tableDefaultSourceRule map[string]map[string]bool, tableDefaultRule map[string]map[string]string, tableClusteredRuleMap map[string]struct{}, tableNonClusteredRuleMap map[string]string, oracleDBVersion string, oracleDBCharset, targetDBCharset string, oracleCollation bool, lowerCaseFieldName string, lowerCaseTableName string, exporters []string, nlsSort, nlsComp string) ([]*o2t.Table, error) {
	var tables []*o2t.Table

	beginTime := time.Now()
	defer func() {
		endTime := time.Now()
		log.Infof("gen oracle table list finished")
		log.Infof("schema %v", r.Cfg.SchemaConfig.SourceSchema)
		log.Infof("table totals %v", len(exporters))
		log.Infof("table gens %v", len(tables))
		log.Infof("cost %v", endTime.Sub(beginTime).String())
	}()

	log.Infof("get oracle db character and version finished")
	log.Infof("schema %v", r.Cfg.SchemaConfig.SourceSchema)
	log.Infof("db version %v", oracleDBVersion)
	log.Infof("db character %v", oracleDBCharset)
	log.Infof("table totals %v", len(exporters))
	log.Infof("table collation %v", oracleCollation)

	var (
		tblCollation    map[string]string
		schemaCollation string
	)

	if oracleCollation {
		startTime := time.Now()
		schemaCollation, err := models.GetDatasourceReaderWriter().GetOracleSchemaCollation(r.Ctx, r.Oracle.OracleDB, r.Cfg.SchemaConfig.SourceSchema)
		if err != nil {
			return tables, err
		}
		tblCollation, err = r.Oracle.GetOracleSchemaTableCollation(r.Cfg.SchemaConfig.SourceSchema, schemaCollation)
		if err != nil {
			return tables, err
		}
		endTime := time.Now()
		log.Infof("get oracle schema and table collation finished")
		log.Infof("schema %v", r.Cfg.SchemaConfig.SourceSchema)
		log.Infof("db version %v", oracleDBVersion)
		log.Infof("db character %v", oracleDBCharset)
		log.Infof("table totals %v", len(exporters))
		log.Infof("table collation %v", oracleCollation)
		log.Infof("cost %v", endTime.Sub(startTime).String())
	}

	startTime := time.Now()
	tablesMap, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableType(r.Ctx, r.Oracle.OracleDB, r.Cfg.SchemaConfig.SourceSchema)
	if err != nil {
		return tables, err
	}
	endTime := time.Now()
	log.Infof("get oracle table type finished")
	log.Infof("schema %v", r.Cfg.SchemaConfig.SourceSchema)
	log.Infof("db version %v", oracleDBVersion)
	log.Infof("db character %v", oracleDBCharset)
	log.Infof("table totals %v", len(exporters))
	log.Infof("table collation %v", oracleCollation)
	log.Infof("cost %v", endTime.Sub(startTime).String())

	// 获取 MySQL 版本
	mysqlVersion, err := r.Mysql.GetMySQLDBVersion()
	if err != nil {
		return nil, err
	}

	startTime = time.Now()
	g1 := &errgroup.Group{}
	tableChan := make(chan *o2t.Table, common.ChannelBufferSize)

	g1.Go(func() error {
		g2 := &errgroup.Group{}
		g2.SetLimit(r.Cfg.ReverseConfig.ReverseThreads)
		for _, exporter := range exporters {
			t := exporter
			g2.Go(func() error {
				// 库名、表名规则
				var targetTableName string
				if val, ok := tableNameRule[t]; ok {
					targetTableName = val
				} else {
					targetTableName = common.StringUPPER(t)
				}

				tbl := &o2t.Table{
					Ctx:                             r.Ctx,
					SourceSchemaName:                r.Cfg.SchemaConfig.SourceSchema,
					TargetSchemaName:                common.StringUPPER(r.Cfg.SchemaConfig.TargetSchema),
					SourceTableName:                 t,
					TargetDBVersion:                 mysqlVersion,
					TargetTableName:                 targetTableName,
					TargetTableOption:               r.Cfg.SchemaConfig.GlobalTableOption,
					TargetTableClustered:            tableClusteredRuleMap,
					TargetTableNonClustered:         tableNonClusteredRuleMap,
					SourceDBCharset:                 oracleDBCharset,
					TargetDBCharset:                 targetDBCharset,
					SourceTableType:                 tablesMap[t],
					SourceDBNLSSort:                 nlsSort,
					SourceDBNLSComp:                 nlsComp,
					LowerCaseFieldName:              lowerCaseFieldName,
					LowerCaseTableName:              lowerCaseTableName,
					TableColumnDatatypeRule:         tableColumnRule[t],
					TableColumnDefaultValRule:       tableDefaultRule[t],
					TableColumnDefaultValSourceRule: tableDefaultSourceRule[t],
					Overwrite:                       r.Cfg.MySQLConfig.Overwrite,
					Oracle:                          r.Oracle,
					MySQL:                           r.Mysql,
					MetaDB:                          r.MetaDB,
				}
				tbl.OracleCollation = oracleCollation
				if oracleCollation {
					tbl.SourceSchemaCollation = schemaCollation
					tbl.SourceTableCollation = tblCollation[t]
				}
				tableChan <- tbl
				return nil
			})
		}

		err = g2.Wait()
		if err != nil {
			return err
		}
		close(tableChan)
		return nil
	})

	// 数据通道接收
	for c := range tableChan {
		tables = append(tables, c)
	}

	err = g1.Wait()
	if err != nil {
		return nil, err
	}

	endTime = time.Now()
	log.Info("gen oracle slice table finished",
		zap.String("schema", r.Cfg.SchemaConfig.SourceSchema),
		zap.Int("table totals", len(exporters)),
		zap.Int("table gens", len(tables)),
		zap.String("cost", endTime.Sub(startTime).String()))

	return tables, nil
}
