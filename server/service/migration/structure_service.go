package migration

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"fmt"
	"math"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync/atomic"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/o2t"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/public"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/pkg/structure"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/logger"
	config2 "gitee.com/pingcap_enterprise/tms/util/config"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/mysql"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	common2 "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"golang.org/x/sync/errgroup"
)

type StructureMigrationService struct {
}
type SchemaNameStr string
type TableNameStr string

func NewStructureMigrationService() *StructureMigrationService {
	return &StructureMigrationService{}
}

func StartReverseOBJ(ctx context.Context, msg *message.MigrationTaskReq) {
	InfoLog(ctx, msg.TaskID, "[T02-001]:StartReverseOBJ start")
	go func() {
		InfoLog(ctx, msg.TaskID, "[T02-001]:go func() start")
		defer func() {
			if err := recover(); err != nil {
				ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-001]:StartReverseOBJ failed. defer(). taskId:%d, err:%s", msg.TaskID, err))
			}
		}()
		_, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, msg.TaskID, task.Task{
			StartTime:  time.Now(),
			TaskStatus: constants.TASK_STATUS_RUNNING,
		})
		if err != nil {
			ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-001]:task exec failed, update task id [%v] status [%d] failed: %v", msg.TaskID, constants.TASK_STATUS_RUNNING, err))
		} else {
			InfoLog(ctx, msg.TaskID, fmt.Sprintf("[T02-001]:MIGRATE_STRUCTURE task id [%v] createReverseOBJ start", msg.TaskID))
			if err = createReverseOBJ(ctx, msg); err != nil {
				ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:createReverseOBJ err: %v", err))
				if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, msg.TaskID, task.Task{
					EndTime:     time.Now(),
					TaskStatus:  constants.TASK_STATUS_FAILED,
					ErrorDetail: fmt.Sprintf("[T02-005]:task exec failed, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, "createReverseOBJ err"),
				}); err != nil {
					ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec failed, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err))
				}
			} else {
				err = models.Transaction(ctx, func(transactionCtx context.Context) error {
					tableFailedResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailWithoutIndex(transactionCtx, &migration.TableResultDetail{
						TaskID: msg.TaskID,
						Status: constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED,
					})
					if err != nil {
						ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec failed, get table result detail by task id [%d] and status [%d] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err))
						return fmt.Errorf("[T02-005]:task exec failed, get table result detail by task id [%d] and status [%d] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err)
					}

					if len(tableFailedResults) == 0 {
						if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(transactionCtx, msg.TaskID, task.Task{
							EndTime:    time.Now(),
							TaskStatus: constants.TASK_STATUS_FINISH,
						}); err != nil {
							ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec success, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FINISH, err))
							return fmt.Errorf("[T02-005]:task exec success, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FINISH, err)
						}
						InfoLog(ctx, msg.TaskID, "[T02-005]:MIGRATE_STRUCTURE task id [%v] exec success", msg.TaskID)
					} else {
						// 使用strings.Builder拼接字符串
						var builder strings.Builder
						var abstractErrorCount int
						for _, failedResult := range tableFailedResults {
							builder.WriteString("\n")
							builder.WriteString(failedResult.ErrorDetail)
							abstractErrorCount++
							if abstractErrorCount >= 5 {
								break
							}
						}
						// 获取拼接后的结果
						abstractErrorInfo := builder.String()

						if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, msg.TaskID, task.Task{
							EndTime:     time.Now(),
							TaskStatus:  constants.TASK_STATUS_FAILED,
							ErrorDetail: fmt.Sprintf("[T02-005]:task exec finished, update task id [%v] status [%v], failed ddl count: %d, Please check the details for the reason of the failure. A summary of the failure information is as follows：%s", msg.TaskID, constants.TASK_STATUS_FAILED, len(tableFailedResults), abstractErrorInfo),
						}); err != nil {
							ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec failed, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err))
						}
						InfoLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec finished, update task id [%v] status [%v], failed ddl count: %d", msg.TaskID, constants.TASK_STATUS_FAILED, len(tableFailedResults)))
					}
					return nil
				})
				if err != nil {
					if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, msg.TaskID, task.Task{
						EndTime:     time.Now(),
						TaskStatus:  constants.TASK_STATUS_FAILED,
						ErrorDetail: fmt.Sprintf("[T02-005]:task exec failed or exist panic, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, "failed exec models.Transaction"),
					}); err != nil {
						ErrorLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec failed, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err))
					}
					InfoLog(ctx, msg.TaskID, fmt.Sprintf("[T02-005]:task exec failed or exist panic, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, "failed exec models.Transaction"))
				}
			}
		}
	}()
}

func createReverseOBJ(ctx context.Context, msg *message.MigrationTaskReq) error {
	// pre-get
	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] pre-get start", msg.TaskID))
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task info failed: %v", err)
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return fmt.Errorf("[T02-002]:get channel info failed: %v", err)
	}

	structureParam, buildParamErr := structure.BuildStructureMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build structure migration param failed. taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, taskInfo.ChannelId, buildParamErr)
		return buildParamErr
	}

	// init logger
	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] GetGlobalConfig start", msg.TaskID))
	globalCfg := config2.GetGlobalConfig()
	logger.NewZapLogger(&config.Config{LogConfig: config.LogConfig{
		LogLevel:   globalCfg.LogConfig.LogLevel,
		LogFile:    filepath.Join(globalCfg.LogConfig.LogFileRoot, "migrate_struct.log"),
		MaxSize:    globalCfg.LogConfig.LogMaxSize,
		MaxDays:    globalCfg.LogConfig.LogMaxAge,
		MaxBackups: globalCfg.LogConfig.LogMaxBackups,
	}})

	// create database conn
	var (
		sourceDB *oracle.Oracle
		targetDB *mysql.MySQL
	)
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task source datasource info failed: %v", err)
	}
	targetDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdT)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task target datasource info failed: %v", err)
	}

	// source database is ORACLE
	// target database is TiDB
	if strings.EqualFold(detailChannelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		checkDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    sourceDS.UserName,
			Password:    sourceDS.PasswordValue,
			Host:        sourceDS.HostIp,
			Port:        sourceDS.HostPort,
			ServiceName: datasourcepkg.GetServiceName(sourceDS),
		}, "")
		if err != nil {
			return fmt.Errorf("[T02-002]:create source datasource conn failed: %v", err)
		}

		// 数据库字符集
		charset, err := checkDB.GetOracleDBCharacterSet()
		if err != nil {
			return err
		}
		sourceDBCharset := strings.Split(charset, ".")[1]

		if !strings.EqualFold(sourceDS.Charset, sourceDBCharset) {
			WarnLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:oracle server charset [%v] and oracle config charset [%v] aren't different, would be ignore, running with oracle server charset [%v]", sourceDBCharset, sourceDS.Charset, sourceDBCharset))
		}

		if _, ok := common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(sourceDBCharset)]; !ok {
			return fmt.Errorf("[T02-002]:oracle current charset [%v] isn't support, support charset [%v]", sourceDBCharset, common.MigrateOracleCharsetStringConvertMapping)
		}
		if !strings.EqualFold(common.MYSQLCharsetUTF8MB4, targetDS.Charset) {
			return fmt.Errorf("[T02-002]:tidb current config charset [%v] isn't support, tidb only support charset [%v]", targetDS.Charset, common.MYSQLCharsetUTF8MB4)
		}

		sourceDB, err = getSourceDB(ctx, sourceDS)
		if err != nil {
			return fmt.Errorf("[T02-002]:create source datasource conn failed: %v", err)
		}

		targetDB, err = getTargetDB(ctx, targetDS)
		if err != nil {
			return err
		}
	} else {
		return fmt.Errorf("[T02-002]:channel type [%s] isn' support, please agagin choose", detailChannelInfo.ChannelType)

	}

	// get table by schema
	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] GetChannelSchemaTablesDistinctSchemaSByChannelTaskId start", msg.TaskID))
	sourceSchemas, err := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, detailChannelInfo.ChannelId, msg.TaskID)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task target datasource info failed: %v", err)
	}

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] GetOracleDBCharacterNLSCompCollation start", msg.TaskID))
	nlsComp, err := sourceDB.GetOracleDBCharacterNLSCompCollation()
	if err != nil {
		return fmt.Errorf("[T02-002]:get oracledb character nls_comp collation failed: %v", err)
	}

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] GetOracleDBCharacterNLSSortCollation start", msg.TaskID))
	nlsSort, err := sourceDB.GetOracleDBCharacterNLSSortCollation()
	if err != nil {
		return fmt.Errorf("[T02-002]:get oracledb character nls_sort collation failed: %v", err)
	}

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] GetOracleDBCharacterSet start", msg.TaskID))
	charset, err := sourceDB.GetOracleDBCharacterSet()
	if err != nil {
		return err
	}

	oracleDBCharset := strings.Split(charset, ".")[1]

	if _, ok := common.MigrateTableStructureDatabaseCollationMap[common.TaskTypeOracle2TiDB][common.StringUPPER(nlsComp)][common.MigrateTableStructureDatabaseCharsetMap[common.TaskTypeOracle2TiDB][oracleDBCharset]]; !ok {
		return fmt.Errorf("[T02-002]:oracle db nls comp [%s] , mysql db isn't support", nlsComp)
	}
	if _, ok := common.MigrateTableStructureDatabaseCollationMap[common.TaskTypeOracle2TiDB][common.StringUPPER(nlsSort)][common.MigrateTableStructureDatabaseCharsetMap[common.TaskTypeOracle2TiDB][oracleDBCharset]]; !ok {
		return fmt.Errorf("[T02-002]:oracle db nls sort [%s] , mysql db isn't support", nlsSort)
	}

	if !strings.EqualFold(nlsSort, nlsComp) {
		return fmt.Errorf("[T02-002]:oracle db nls_sort [%s] and nls_comp [%s] isn't different, need be equal; because mysql db isn't support", nlsSort, nlsComp)

	}

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] GetOracleDBVersion start", msg.TaskID))
	oracleDBVersion, err := sourceDB.GetOracleDBVersion()
	if err != nil {
		return fmt.Errorf("[T02-002]:get oracledb version failed: %v", err)
	}

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] oracleCollation start", msg.TaskID))
	oracleCollation := false
	if common.VersionOrdinal(oracleDBVersion) >= common.VersionOrdinal(common.OracleTableColumnCollationDBVersion) {
		oracleCollation = true
	}

	// models
	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] tableColRules start", msg.TaskID))
	tableColRules, err := models.GetTemplateReaderWriter().GetTemplateRuleID(ctx, taskInfo.TabcolmapTmplateId)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task column template rule id failed: %v", err)
	}

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-002]:MIGRATE_STRUCTURE task id [%v] colDefaultRules start", msg.TaskID))
	colDefaultRules, err := models.GetTemplateReaderWriter().GetTemplateRuleID(ctx, taskInfo.ColdefaultmapTmplateId)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task column default template rule id failed: %v", err)
	}
	channelObj, err := models.GetChannelReaderWriter().GetChannelSchemaObjectByTaskId(ctx, msg.TaskID)
	if err != nil {
		return fmt.Errorf("[T02-002]:get task schema object info failed: %v", err)
	}

	var (
		tableColRuleIds   []string
		colDefaultRuleIds []string
	)

	for _, r := range tableColRules {
		tableColRuleIds = append(tableColRuleIds, strconv.Itoa(r.MapRuleId))
	}

	for _, r := range colDefaultRules {
		colDefaultRuleIds = append(colDefaultRuleIds, strconv.Itoa(r.MapRuleId))
	}

	UpdateTaskProgress(ctx, msg.TaskID, 0.1)
	channelSchemaTableCount, err := models.GetChannelReaderWriter().GetChannelSchemaTablesCountsByTask(ctx, detailChannelInfo.ChannelId, msg.TaskID)
	if err != nil {
		return fmt.Errorf("[T02-003]:GetChannelSchemaTablesCountsByTask failed: %v", err)
	}
	var convertCounter int32 = 0

	InfoLog(ctx, msg.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] start", msg.TaskID))
	for _, sourceSchema := range sourceSchemas {
		InfoLog(ctx, msg.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] schema [%s] begin get metadata", msg.TaskID, sourceSchema))
		// schema 前置条件
		DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS start: detailChannelInfo.ChannelId=%d, msg.TaskID=%d, sourceSchema=%s", msg.TaskID, detailChannelInfo.ChannelId, msg.TaskID, sourceSchema))
		targetSchema, err := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS(ctx, detailChannelInfo.ChannelId, msg.TaskID, sourceSchema)
		if err != nil {
			return fmt.Errorf("get task channel target schema failed: %v", err)
		}
		DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] GetChannelSchemaTablesByChannelTaskSchemaS start: detailChannelInfo.ChannelId=%d, msg.TaskID=%d, sourceSchema=%s", msg.TaskID, detailChannelInfo.ChannelId, msg.TaskID, sourceSchema))
		schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelTaskSchemaS(ctx, detailChannelInfo.ChannelId, msg.TaskID, sourceSchema)
		if err != nil {
			return fmt.Errorf("[T02-003]:get task channel schema tables failed: %v", err)
		}

		// 表任务
		var (
			schemaExporters []string
			//compatibleTableTypes []*migration.TableCompatibleDetail
		)

		tableNameTMap := make(map[string]string)
		tableNameSMap := make(map[string]string)
		nonClusteredTableMap := make(map[string]string)
		clusteredTableMap := make(map[string]struct{})
		partitionTableMap := make(map[string]string)

		for _, t := range schemaTables {
			schemaExporters = append(schemaExporters, t.TableNameS)
			tableNameTMap[t.TableNameS] = t.TableNameT
			tableNameSMap[t.TableNameS] = t.TableNameS

			if strings.EqualFold(t.PkS, "Y") && strings.EqualFold(structureParam.CreatePkWithTable, "Y") {
				clusteredTableMap[t.TableNameS] = struct{}{}
			} else if !strings.EqualFold(strings.TrimSpace(targetDS.TableOption), "") {
				//SHARD_ROW_ID_BITS = 4 PRE_SPLIT_REGIONS = 4
				nonClusteredTableMap[t.TableNameS] = targetDS.TableOption
			}

			if !strings.EqualFold(t.PartitioningTypeT, "N") && !strings.EqualFold(strings.TrimSpace(t.PartitioningTypeT), "") {
				partitionTableMap[t.TableNameS] = t.PartitioningTypeT
			} else {
				partitionTableMap[t.TableNameS] = "N"
			}
		}

		DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] FilterOracleCompatibleTable start", msg.TaskID))
		_, _, _, materializedView, exporters, err := public.FilterOracleCompatibleTable(&config.Config{SchemaConfig: config.SchemaConfig{SourceSchema: sourceSchema}}, sourceDB, schemaExporters)
		if err != nil {
			return fmt.Errorf("[T02-003]:filter oracle compatible failed: %v", err)
		}
		SaveMaterializedView(ctx, materializedView, taskInfo, sourceSchema, targetSchema, sourceDS, tableNameTMap, convertCounter, channelSchemaTableCount)

		//UpdateTaskProgress(ctx, msg.TaskID, 0.1+0.3*float64(len(compatibleTableTypes)/int(channelSchemaTableCount)))

		CreateTargetSchemaIfNotExists(ctx, taskInfo, sourceSchema, targetSchema, sourceDS, targetDS, oracleCollation, strconv.Itoa(int(structureParam.LowerCaseFieldNames)), nlsComp)

		DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] o2t.GenReverseTableTask start", msg.TaskID))

		tables, err := buildO2tTables(ctx, taskInfo, sourceSchema, targetSchema, sourceDS, targetDS, schemaTables, oracleDBVersion, oracleCollation, nlsSort, nlsComp, tableColRuleIds, colDefaultRuleIds, structureParam, exporters)
		if err != nil {
			return fmt.Errorf("[T02-003]:gen table reverse task failed: %v", err)
		}
		SaveTableAndIndex(ctx, taskInfo, channelObj, structureParam, tables, sourceSchema, sourceDS, convertCounter, channelSchemaTableCount, tableNameSMap, partitionTableMap)
	}
	UpdateTaskProgress(ctx, msg.TaskID, 0.4)

	DebugLog(ctx, msg.TaskID, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] range sourceSchemas 2 start", msg.TaskID))
	execTableDDL(ctx, targetDB, sourceSchemas, msg.TaskID, int(structureParam.MigrationTableParallel))
	execIndexDDL(ctx, targetDB, sourceSchemas, msg.TaskID, int(structureParam.MigrationIndexParallel))
	resetStatusForTableResultDetail(ctx, msg.TaskID)
	UpdateTaskProgress(ctx, msg.TaskID, 1.0)

	return nil
}

func buildO2tTables(ctx context.Context, taskInfo *task.Task, sourceSchema string, targetSchema string, sourceDS, targetDS *datasource.Datasource, schemaTables []*channel.ChannelSchemaTable, oracleDBVersion string, oracleCollation bool, nlsSort, nlsComp string, tableColRuleIds, colDefaultRuleIds []string, structureParam *structs.StructureParam, exporters []string) ([]*o2t.Table, error) {
	DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] o2t.IChanger start,SourceDBCharset:%s,TargetDBCharset:%s", taskInfo.TaskID, sourceDS.Charset, targetDS.Charset))

	sourceDB, err := getSourceDB(ctx, sourceDS)
	if err != nil {
		return nil, err
	}

	targetDB, err := getTargetDB(ctx, targetDS)
	if err != nil {
		return nil, err
	}

	tableNameRuleMap, tableColumnRuleMap, tableDefaultValueSourceMap, tableDefaultRuleMap, err := o2t.IChanger(&Change{
		Ctx:               ctx,
		ChannelId:         taskInfo.ChannelId,
		TaskId:            taskInfo.TaskID,
		SourceSchemaName:  sourceSchema,
		TargetSchemaName:  targetSchema,
		SourceTables:      schemaTables,
		TableColRuleIds:   tableColRuleIds,
		ColDefaultRuleIds: colDefaultRuleIds,
		OracleCollation:   oracleCollation,
		Oracle:            sourceDB,
		SourceDBCharset:   sourceDS.Charset,
		TargetDBCharset:   targetDS.Charset,
		TableThreads:      int(structureParam.MigrationTableParallel),
	})
	if err != nil {
		ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:get task rule maps failed: %v", err))
		return nil, fmt.Errorf("[T02-003]:get task rule maps failed: %v", err)
	}
	InfoLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] schema [%s] colType and default value finish", taskInfo.TaskID, sourceSchema))

	DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] o2t.GenReverseTableTask start", taskInfo.TaskID))

	nonClusteredTableMap := make(map[string]string)
	clusteredTableMap := make(map[string]struct{})
	partitionTableMap := make(map[string]string)

	for _, t := range schemaTables {
		if strings.EqualFold(t.PkS, "Y") && strings.EqualFold(structureParam.CreatePkWithTable, "Y") {
			clusteredTableMap[t.TableNameS] = struct{}{}
		} else if !strings.EqualFold(strings.TrimSpace(targetDS.TableOption), "") {
			//SHARD_ROW_ID_BITS = 4 PRE_SPLIT_REGIONS = 4
			nonClusteredTableMap[t.TableNameS] = targetDS.TableOption
		}

		if !strings.EqualFold(t.PartitioningTypeT, "N") && !strings.EqualFold(strings.TrimSpace(t.PartitioningTypeT), "") {
			partitionTableMap[t.TableNameS] = t.PartitioningTypeT
		} else {
			partitionTableMap[t.TableNameS] = "N"
		}
	}

	tables, err := GenReverseTableTask(&o2t.Reverse{
		Ctx: ctx,
		Cfg: &config.Config{
			ReverseConfig: config.ReverseConfig{ReverseThreads: int(structureParam.MigrationTableParallel)},
			SchemaConfig:  config.SchemaConfig{SourceSchema: sourceSchema, TargetSchema: targetSchema},
		},
		Oracle: sourceDB,
		Mysql:  targetDB,
	}, tableNameRuleMap, tableColumnRuleMap, tableDefaultValueSourceMap, tableDefaultRuleMap, clusteredTableMap, nonClusteredTableMap, oracleDBVersion, strings.ToUpper(sourceDS.Charset), strings.ToUpper(targetDS.Charset), oracleCollation,
		strconv.Itoa(int(structureParam.LowerCaseFieldNames)),
		strconv.Itoa(int(structureParam.LowerCaseTableNames)),
		exporters, nlsSort, nlsComp)
	if err != nil {
		return nil, fmt.Errorf("[T02-003]:gen table reverse task failed: %v", err)
	}
	return tables, nil
}

func getSourceDB(ctx context.Context, sourceDS *datasource.Datasource) (*oracle.Oracle, error) {
	sourceDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
		Username:    sourceDS.UserName,
		Password:    sourceDS.PasswordValue,
		Host:        sourceDS.HostIp,
		Port:        sourceDS.HostPort,
		ServiceName: datasourcepkg.GetServiceName(sourceDS),
		Charset:     sourceDS.Charset,
		SessionParams: []string{
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SQLTERMINATOR', true); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'PRETTY', true); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SEGMENT_ATTRIBUTES', false); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'TABLESPACE', false); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'STORAGE', false); END;",
		},
	}, "")
	if err != nil {
		return nil, fmt.Errorf("[T02-002]:create source datasource conn failed: %v", err)
	} else {
		return sourceDB, nil
	}
}

func getTargetDB(ctx context.Context, targetDS *datasource.Datasource) (*mysql.MySQL, error) {
	targetDB, err := mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
		Username:      targetDS.UserName,
		Password:      targetDS.PasswordValue,
		Host:          targetDS.HostIp,
		Port:          targetDS.HostPort,
		ConnectParams: targetDS.ConnectParams,
		//TableOption:   targetDS.TableOption,
		Charset: targetDS.Charset,
	})
	if err != nil {
		return nil, fmt.Errorf("[T02-002]:create target datasource conn failed: %v", err)
	} else {
		return targetDB, nil
	}
}

func CreateTargetSchemaIfNotExists(ctx context.Context, taskInfo *task.Task, sourceSchema string, targetSchema string, sourceDS, targetDS *datasource.Datasource, oracleCollation bool, lowerCaseFieldNames string, nlsComp string) error {
	var createSchema string
	sourceDB, err := getSourceDB(ctx, sourceDS)
	if err != nil {
		return err
	}

	targetDB, err := getTargetDB(ctx, targetDS)
	if err != nil {
		return err
	}

	if oracleCollation {
		DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] GetOracleSchemaCollation start, sourceSchema[%v]", taskInfo.TaskID, sourceSchema))
		schemaCollation, err := models.GetDatasourceReaderWriter().GetOracleSchemaCollation(ctx, sourceDB.OracleDB, sourceSchema)
		DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE [%v] GetOracleSchemaCollation end ", schemaCollation))
		if err != nil {
			return fmt.Errorf("[T02-003]:get oracle schema collation failed: %v", err)
		}
		targetSchemaCollation, ok := common.MigrateTableStructureDatabaseCollationMap[common.TaskTypeOracle2TiDB][common.StringUPPER(schemaCollation)][targetDS.Charset]

		if !ok {
			return fmt.Errorf("[T02-003]:oracle schema collation [%s] isn't support", schemaCollation)
		}
		createSchema = fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARACTER SET %s COLLATE %s;\n\n", targetSchema, targetDS.Charset, targetSchemaCollation)
	} else {
		targetSchemaCollation, ok := common.MigrateTableStructureDatabaseCollationMap[common.TaskTypeOracle2TiDB][common.StringUPPER(nlsComp)][targetDS.Charset]
		if !ok {
			return fmt.Errorf("[T02-003]:oracle db nls_comp collation [%s] isn't support", nlsComp)
		}
		createSchema = fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARACTER SET %s COLLATE %s;\n\n", targetSchema, targetDS.Charset, targetSchemaCollation)
	}

	schemaTime := time.Now()
	if strings.EqualFold(lowerCaseFieldNames, constants.ParamsMigrationStructLowerCaseTableNamesUpper) {
		createSchema = strings.ToUpper(createSchema)
	}
	if strings.EqualFold(lowerCaseFieldNames, constants.ParamsMigrationStructLowerCaseTableNamesLower) {
		createSchema = strings.ToLower(createSchema)
	}

	err = targetDB.WriteMySQLTable(createSchema)
	if err != nil {
		_, err = models.GetStructureMigrationReaderWriter().CreateTableResultDetail(ctx, &migration.TableResultDetail{
			ChannelID:    taskInfo.ChannelId,
			TaskID:       taskInfo.TaskID,
			ServiceNameS: sourceDS.ServiceName,
			DBNameS:      sourceDS.DbName,
			Status:       constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED,
			Duration:     time.Now().Sub(schemaTime).Seconds(),
			SchemaNameS:  sourceSchema,
			TableNameS:   "",
			ReverseDDL:   createSchema,
			ErrorDetail:  fmt.Sprintf("exec create target schema ddl failed: %v", err),
		})
		if err != nil {
			return fmt.Errorf("[T02-003]:create table result detail ddl record failed, ddl: [%v], len(createSchema): %d,error: %v", createSchema, len(createSchema), err)
		}
		return fmt.Errorf("[T02-003]:create target schema ddl failed, ddl: [%v], error: %v", createSchema, err)
	}
	return nil
}

func SaveTableAndIndex(ctx context.Context, taskInfo *task.Task, channelObj *channel.ChannelSchemaObject, structureParam *structs.StructureParam, tables []*o2t.Table, sourceSchema string, sourceDS *datasource.Datasource, convertCounter int32, channelSchemaTableCount int64, tableNameSMap map[string]string, partitionTableMap map[string]string) error {
	InfoLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] schema [%s] SaveTableAndIndex begin", taskInfo.TaskID, sourceSchema))

	if structureParam.RunVersion == "v2" {
		summray, err := models.GetChannelReaderWriter().GetTableColumnCustomSummary(ctx, taskInfo.ChannelId)
		if err != nil {
			return err
		}
		if summray == nil || summray.Status == constants.ColumnDataStatusNoData {
			channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
			if err != nil {
				return err
			}
			log.Infof("sync source table columns begin. taskId:%d, channelId:%d", taskInfo.TaskID, taskInfo.ChannelId)
			err = SyncSourceTableColumns(ctx, taskInfo, channelInfo)
			if err != nil {
				log.Errorf("sync source table columns failed. taskId:%d, err:%v", taskInfo.TaskID, err)
				return err
			}
			log.Infof("sync source table columns end. taskId:%d, channelId:%d", taskInfo.TaskID, taskInfo.ChannelId)
		}
	}

	// schema table 任务处理
	g := &errgroup.Group{}
	g.SetLimit(int(structureParam.MigrationTableParallel))
	for _, table := range tables {
		t := table
		g.Go(func() error {
			defer func() {
				if err := recover(); err != nil {
					ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] schema table task failed. defer(). err:%s", taskInfo.TaskID, err))
				}
			}()

			tableResultDetail := &migration.TableResultDetail{
				ChannelID:    taskInfo.ChannelId,
				TaskID:       taskInfo.TaskID,
				ServiceNameS: sourceDS.ServiceName,
				DBNameS:      sourceDS.DbName,
				Status:       constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING,
				SchemaNameS:  t.SourceSchemaName,
				TableNameS:   t.SourceTableName,
				TableTypeS:   t.SourceTableType,
				SchemaNameT:  t.TargetSchemaName,
				TableNameT:   t.TargetTableName,
				TableTypeT:   "NORMAL TABLE",
				OriginDDL:    "",
				ReverseDDL:   "",
			}

			startTime := time.Now()
			originDDL := ""
			tableResultDetail.OriginDDL = originDDL

			partitionTable := partitionTableMap[t.SourceTableName]

			if !strings.EqualFold(partitionTable, "N") && !strings.EqualFold(partitionTable, "") {
				sourceDB, err := getSourceDB(ctx, sourceDS)
				if err != nil {
					return err
				}

				originDDL, err = models.GetDatasourceReaderWriter().GetOracleSchemaTableOriginDDL(ctx, "TABLE", sourceSchema, tableNameSMap[t.SourceTableName], sourceDB.OracleDB)
				if err != nil {
					CreateTableResultDetail(ctx, taskInfo.TaskID, tableResultDetail)
					return fmt.Errorf("t02-003:table [%v][%v] GetOracleSchemaTableOriginDDL failed, %v", t.SourceTableName, tableNameSMap[t.SourceTableName], err)
				} else {
					tableResultDetail.OriginDDL = originDDL
				}
			}

			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse start", taskInfo.TaskID)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  sourceDS.ServiceName=%s", taskInfo.TaskID, sourceDS.ServiceName)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  sourceDS.DbName=%s", taskInfo.TaskID, sourceDS.DbName)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  originDDL=%s", taskInfo.TaskID, originDDL)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  detailChannelInfo.ChannelId=%d", taskInfo.TaskID, taskInfo.ChannelId)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  t=%v", taskInfo.TaskID, t)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  channelObj.OnlyTable=%s", taskInfo.TaskID, channelObj.OnlyTable)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  channelObj.OnlyIndex=%s", taskInfo.TaskID, channelObj.OnlyIndex)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  channelObj.OnlyTableandindex=%s", taskInfo.TaskID, channelObj.OnlyTableandindex)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  channelObj.Partitiontable=%s", taskInfo.TaskID, channelObj.Partitiontable)
			log.Debugf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse param:  lowerCaseFieldNames=%s", taskInfo.TaskID, structureParam.LowerCaseFieldNames)

			startTime = time.Now()
			t.SourceTableName = tableNameSMap[t.SourceTableName]
			var (
				tableDDL      string
				compatibleDDL []*migration.TableCompatibleDetail
				indexKeyDDL   []*migration.TableResultDetail
				err           error
			)
			if structureParam.RunVersion == "v2" {
				var args []any
				args = append(args, t.SourceSchemaName)
				args = append(args, t.SourceTableName)
				condition := "AND r.schema_name_s = ? AND r.table_name_s COLLATE utf8mb4_bin = ?"
				columns, _, getErr := models.GetChannelReaderWriter().QueryTableColumnCustomMapRuleByChannelTaskAndArgs(ctx, taskInfo.ChannelId, taskInfo.TaskID, 1, 10000, condition, args)
				if getErr != nil {
					log.Errorf("query table columns failed. taskId:%d, err:%v", taskInfo.TaskID, getErr)
					return getErr
				}
				log.Debugf("len(columns) : %d", len(columns))

				//log.Debugf("QueryTableColumnCustomMapRuleByChannelTaskAndArgs columns: %+v", columns)
				log.Debugf("tableReverseV2 begin")
				tableDDL, indexKeyDDL, compatibleDDL, err = tableReverseV2(sourceDS.ServiceName, sourceDS.DbName, originDDL, taskInfo.ChannelId, taskInfo.TaskID, t, channelObj.OnlyTable, channelObj.OnlyTableandindex, channelObj.OnlyIndex, strconv.Itoa(int(structureParam.LowerCaseFieldNames)), partitionTableMap[t.SourceTableName], structureParam, columns)
				if err != nil {
					tableResultDetail.ErrorDetail = err.Error()
					tableResultDetail.Status = constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED
					CreateTableResultDetail(ctx, taskInfo.TaskID, tableResultDetail)
					ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:%v oracle table [%s.%s] tableReverse failed: %v", time.Now().Format("2006-01-02 15:04:05"), t.SourceSchemaName, t.SourceTableName, err))
					return err
				} else {
					tableResultDetail.ReverseDDL = tableDDL
				}
			} else {
				tableDDL, indexKeyDDL, compatibleDDL, err = tableReverse(sourceDS.ServiceName, sourceDS.DbName, originDDL, taskInfo.ChannelId, taskInfo.TaskID, t, channelObj.OnlyTable, channelObj.OnlyTableandindex, channelObj.OnlyIndex, strconv.Itoa(int(structureParam.LowerCaseFieldNames)), partitionTableMap[t.SourceTableName], structureParam)
				if err != nil {
					tableResultDetail.ErrorDetail = err.Error()
					tableResultDetail.Status = constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED
					CreateTableResultDetail(ctx, taskInfo.TaskID, tableResultDetail)
					ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:%v oracle table [%s.%s] tableReverse failed: %v", time.Now().Format("2006-01-02 15:04:05"), t.SourceSchemaName, t.SourceTableName, err))
					return err
				} else {
					tableResultDetail.ReverseDDL = tableDDL
				}
			}

			DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] tableReverse :t.SourceTableName=%s cost time:%v ms", taskInfo.TaskID, t.SourceTableName, time.Now().Sub(startTime).Milliseconds()))

			if !strings.EqualFold(tableDDL, "") {
				DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] oracle table [%s.%s] CreateTableResultDetail start", taskInfo.TaskID, t.SourceSchemaName, t.SourceTableName))
				CreateTableResultDetail(ctx, taskInfo.TaskID, tableResultDetail)
			}

			if len(compatibleDDL) > 0 {
				DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] BatchCreateTableCompatible for index start", taskInfo.TaskID))
				_, err = models.GetStructureMigrationReaderWriter().BatchCreateTableCompatible(ctx, compatibleDDL, 5)
				if err != nil {
					//2023-08-08 吴超 出错返回error的逻辑改为记log日志
					//return fmt.Errorf("batch create table compatible record failed: %v", err.Error())
					ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:batch create table compatible record failed: %v", err.Error()))
				}
			}

			if len(indexKeyDDL) > 0 {
				DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] BatchCreateTableResultDetail for index start", taskInfo.TaskID))
				_, err = models.GetStructureMigrationReaderWriter().BatchCreateTableResultDetail(ctx, indexKeyDDL, 5)
				if err != nil {
					ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:batch create table compatible record failed: %v", err.Error()))
				}
			}

			atomic.AddInt32(&convertCounter, 1)
			// 防止除零错误
			var progress float64
			if channelSchemaTableCount > 0 {
				progress = 0.1 + 0.3*(float64(convertCounter)/float64(channelSchemaTableCount))
			} else {
				progress = 0.1
			}
			UpdateTaskProgress(ctx, taskInfo.TaskID, progress)

			return nil
		})
	}
	if err := g.Wait(); err != nil {
		ErrorLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] save ddl error [%v]", taskInfo.TaskID, err))
	}
	InfoLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] schema [%s] SaveTableAndIndex finish", taskInfo.TaskID, sourceSchema))
	return nil
}

func SaveMaterializedView(ctx context.Context, materializedView []string, taskInfo *task.Task, sourceSchema string, targetSchema string, sourceDS *datasource.Datasource, tableNameTMap map[string]string, convertCounter int32, channelSchemaTableCount int64) error {
	if len(materializedView) > 0 {
		for _, view := range materializedView {
			DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] GetOracleSchemaTableOriginDDL start:sourceSchema=%s, common.StringUPPER(view)=%s", taskInfo.TaskID, sourceSchema, common.StringUPPER(view)))
			//originDDL, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableOriginDDL(ctx, "MATERIALIZED_VIEW", sourceSchema, common.StringUPPER(view), sourceDB.OracleDB)
			//if err != nil {
			//	return err
			//}
			originDDL := ""
			_, err := models.GetStructureMigrationReaderWriter().CreateTableResultDetail(ctx, &migration.TableResultDetail{
				ChannelID:    taskInfo.ChannelId,
				TaskID:       taskInfo.TaskID,
				ServiceNameS: sourceDS.ServiceName,
				DBNameS:      sourceDS.DbName,
				Status:       constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE,
				SchemaNameS:  sourceSchema,
				TableNameS:   common.StringUPPER(view),
				TableTypeS:   "MATERIALIZED VIEW",
				SchemaNameT:  targetSchema,
				TableNameT:   tableNameTMap[common.StringUPPER(view)],
				TableTypeT:   "NONE",
				OriginDDL:    originDDL,
				ReverseDDL:   "-- please manual enter reverse sql",
			})
			atomic.AddInt32(&convertCounter, 1)
			// 防止除零错误
			var progress float64
			if channelSchemaTableCount > 0 {
				progress = 0.1 + 0.3*(float64(convertCounter)/float64(channelSchemaTableCount))
			} else {
				progress = 0.1
			}
			UpdateTaskProgress(ctx, taskInfo.TaskID, progress)
			DebugLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MATERIALIZED VIEW INCOMPATIBLE, name: [%s],status: [INCOMPATIBLE], originDDL: [%v],len(originDDL):%d", common.StringUPPER(view), originDDL, len(originDDL)))
			if err != nil {
				return err
			}
		}
	}
	InfoLog(ctx, taskInfo.TaskID, fmt.Sprintf("[T02-003]:MIGRATE_STRUCTURE task id [%v] schema [%s] SaveMaterializedView finish", taskInfo.TaskID, sourceSchema))
	return nil
}

func execTableDDL(ctx context.Context, targetDB *mysql.MySQL, sourceSchemas []string, taskId int, tableThreads int) error {
	for _, sourceSchema := range sourceSchemas {
		InfoLog(ctx, taskId, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] schema [%s] execTableDDL begin", taskId, sourceSchema))
		tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailWithoutIndex(ctx, &migration.TableResultDetail{
			TaskID:      taskId,
			SchemaNameS: sourceSchema,
			Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING,
		})
		if err != nil {
			ErrorLog(ctx, taskId, fmt.Sprintf("[T02-004]:%v oracle sourceSchema [%s] BatchGetTableResultDetail failed: %v", time.Now().Format("2006-01-02 15:04:05"), sourceSchema, err))
			return err
		}

		g := &errgroup.Group{}
		g.SetLimit(tableThreads)
		//执行非index的ddl（主要是表的DDL）
		for _, tr := range tableResults {
			t := tr
			g.Go(func() error {
				return execDdlByTableResultDetail(ctx, t, targetDB)
			})
		}
		if err = g.Wait(); err != nil {
			ErrorLog(ctx, taskId, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] run ddl error [%v]", taskId, err))
		}
		InfoLog(ctx, taskId, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] schema [%s] execTableDDL finish", taskId, sourceSchema))
	}
	return nil
}

func execIndexDDL(ctx context.Context, targetDB *mysql.MySQL, sourceSchemas []string, taskId int, tableThreads int) error {
	for _, sourceSchema := range sourceSchemas {
		InfoLog(ctx, taskId, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] schema [%s] execIndexDDL begin", taskId, sourceSchema))
		tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailForIndex(ctx, &migration.TableResultDetail{
			TaskID:      taskId,
			SchemaNameS: sourceSchema,
			Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING,
		})
		if err != nil {
			ErrorLog(ctx, taskId, fmt.Sprintf("[T02-004]:%v oracle sourceSchema [%s] BatchGetTableResultDetail failed: %v", time.Now().Format("2006-01-02 15:04:05"), sourceSchema, err))
			return err
		}

		g := &errgroup.Group{}
		g.SetLimit(tableThreads)
		//执行index的ddl
		for _, tr := range tableResults {
			t := tr
			g.Go(func() error {
				return execDdlByTableResultDetail(ctx, t, targetDB)
			})
		}
		if err = g.Wait(); err != nil {
			ErrorLog(ctx, taskId, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] run ddl error [%v]", taskId, err))
		}
		InfoLog(ctx, taskId, fmt.Sprintf("[T02-004]:MIGRATE_STRUCTURE task id [%v] schema [%s] execIndexDDL finish", taskId, sourceSchema))
	}
	return nil
}

/**对表创建成功，但是存在不兼容的索引和失败的索引，把tableResultDetail状态改为TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS
 */
func resetStatusForTableResultDetail(ctx context.Context, taskId int) error {
	models.GetStructureMigrationReaderWriter().BatchUpdateTableResultDetailToPartialSuccessByFailedIndex(ctx, taskId)
	models.GetStructureMigrationReaderWriter().BatchUpdateTableResultDetailToPartialSuccessByIncompatibalIndex(ctx, taskId)
	return nil
}

func execDdlByTableResultDetail(ctx context.Context, t *migration.TableResultDetail, targetDB *mysql.MySQL) error {
	sqlSTime := time.Now()
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		t.Status = constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING
		t.Duration = time.Now().Sub(sqlSTime).Seconds()

		_, err := models.GetStructureMigrationReaderWriter().UpdateTableResultDetail(ctx, t)
		if err != nil {
			ErrorLog(ctx, t.TaskID, fmt.Sprintf("[T02-004]:update table result detail status [running] failed: %v", err))
		}
		return nil
	})

	writeErr := targetDB.WriteMySQLTable(t.ReverseDDL)
	if writeErr != nil || strings.EqualFold(t.ReverseDDL, "") {
		err = models.Transaction(ctx, func(transactionCtx context.Context) error {
			t.Status = constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED
			t.Duration = time.Now().Sub(sqlSTime).Seconds()
			errorDetail := fmt.Sprintf("[T02-004]:oracle table [%s.%s] run ddl failed..., error: %v", t.SchemaNameS, t.TableNameS, writeErr)
			t.ErrorDetail = errorDetail

			_, err = models.GetStructureMigrationReaderWriter().UpdateTableResultDetail(ctx, t)
			if err != nil {
				ErrorLog(ctx, t.TaskID, fmt.Sprintf("[T02-004]:update table result detail status [failed] failed: %v", err))
			}
			_, err = models.GetProgressLogReaderWriter().UpdateProgressLogDetail(ctx, &common2.ProgressLogDetail{
				ChannelID:   t.ChannelID,
				TaskID:      t.TaskID,
				SchemaNameS: t.SchemaNameS,
				TableNameS:  t.TableNameS,
				Detail:      errorDetail,
				LogLevel:    "error",
			})
			if err != nil {
				ErrorLog(ctx, t.TaskID, fmt.Sprintf("[T02-004]:update taskid [%v] status [%v] progress log detail record failed: %v", t.TaskID, t.Status, err))
			}
			return nil
		})
	} else {
		err = models.Transaction(ctx, func(transactionCtx context.Context) error {
			t.Status = constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS
			t.Duration = time.Now().Sub(sqlSTime).Seconds()
			_, err = models.GetStructureMigrationReaderWriter().UpdateTableResultDetail(ctx, t)
			if err != nil {
				ErrorLog(ctx, t.TaskID, fmt.Sprintf("[T02-004]:update table result detail status [finish] failed: %v", err))
			}

			objName := t.ObjectNameS
			if t.ObjectNameS == "" {
				objName = t.TableNameS
			}
			_, err = models.GetProgressLogReaderWriter().UpdateProgressLogDetail(ctx, &common2.ProgressLogDetail{
				ChannelID:   t.ChannelID,
				TaskID:      t.TaskID,
				SchemaNameS: t.SchemaNameS,
				TableNameS:  t.TableNameS,
				LogLevel:    "info",
				Detail:      fmt.Sprintf("[T02-004]:oracle table [%s.%s] object_type_s[%s] object_name[%s] create success...", t.SchemaNameS, t.TableNameS, t.TableTypeS, objName),
			})
			if err != nil {
				ErrorLog(ctx, t.TaskID, fmt.Sprintf("[T02-004]:update taskid [%v] status [%v] progress log detail record failed: %v", t.TaskID, t.Status, err))
			}
			return nil
		})
	}
	reverseCount, err := GetReverseOBJCount(ctx, &message.MigrationProgressReq{TaskID: t.TaskID})
	if err == nil {
		// 防止除零错误和 NaN 计算
		var progress float64
		if reverseCount.TotalMigrationNums > 0 {
			completedNums := reverseCount.SuccessMigrationNums + reverseCount.FailedMigrationNums
			progress = 0.4 + 0.6*(float64(completedNums)/float64(reverseCount.TotalMigrationNums))
		} else {
			progress = 0.4
		}
		UpdateTaskProgress(ctx, t.TaskID, progress)
	}
	return nil
}

func (s *StructureMigrationService) GetReverseOBJSummaryByTask(ctx context.Context, msg *message.MigrationTaskReq) (*message.MigrationStructureTaskSummaryResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return nil, err
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, err
	}
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return nil, err
	}

	tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetail(ctx, &migration.TableResultDetail{
		TaskID: msg.TaskID,
	})
	if err != nil {
		return nil, err
	}

	tableCompatibles, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetail(ctx, &migration.TableCompatibleDetail{
		TaskID: msg.TaskID,
	})
	if err != nil {
		return nil, err
	}
	var (
		totalNums   int
		successNums int
		failedNums  int
		runningNums int
		waitingNums int
		compNums    int
	)
	totalNums = len(tableResults)
	compNums = len(tableCompatibles)
	for _, r := range tableResults {
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
			successNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
			failedNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
			runningNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
			waitingNums += 1
		}
	}

	sourceSchemas, err := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, detailChannelInfo.ChannelId, msg.TaskID)
	if err != nil {
		return nil, err
	}

	var migrationDetails []message.MigrationStructureTaskSummaryDetail

	for _, sourceSchema := range sourceSchemas {
		var (
			detailTotalNums         int
			detailSuccessNums       int
			detailFailedNums        int
			detailRunningNums       int
			detailWaitingNums       int
			detailCompNums          int
			detailSuccessRatio      string
			detailMigrationDuration float64
		)
		for _, r := range tableResults {
			if strings.EqualFold(sourceSchema, r.SchemaNameS) {
				detailTotalNums += 1

				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
					detailSuccessNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
					detailFailedNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
					detailRunningNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
					detailWaitingNums += 1
				}

				detailMigrationDuration += r.Duration
			}
		}

		for _, c := range tableCompatibles {
			if strings.EqualFold(sourceSchema, c.SchemaNameS) {
				detailCompNums += 1
			}
		}

		successRatio := 0.0
		if detailTotalNums != 0 {
			successRatio = float64(detailSuccessNums) / float64(detailTotalNums)
		}
		detailSuccessRatio = fmt.Sprintf("%.2f%%", successRatio*100)

		detailDurationStr := time.Duration(detailMigrationDuration * float64(time.Second)).String()

		migrationDetails = append(migrationDetails, message.MigrationStructureTaskSummaryDetail{
			ServiceName:       sourceDS.ServiceName,
			SchemaName:        sourceSchema,
			MigrationNums:     detailTotalNums,
			SuccessNums:       detailSuccessNums,
			FailedNums:        detailFailedNums,
			RunningNums:       detailRunningNums,
			WaitingNums:       detailWaitingNums,
			CompatibleNums:    detailCompNums,
			SuccessRatio:      detailSuccessRatio,
			MigrationDuration: detailDurationStr,
		})
	}

	return &message.MigrationStructureTaskSummaryResp{
		TaskID:               msg.TaskID,
		TotalMigrationNums:   totalNums,
		SuccessMigrationNums: successNums,
		FailedMigrationNums:  failedNums,
		RunningMigrationNums: runningNums,
		WaitingMigrationNums: waitingNums,
		CompatibleNums:       compNums,
		MigrationDetails:     migrationDetails,
	}, nil

}

func (s *StructureMigrationService) GetReverseOBJSummaryByTask2(ctx context.Context, msg *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskId)
	if err != nil {
		return nil, err
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, err
	}
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return nil, err
	}

	tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailWithoutIndex(ctx, &migration.TableResultDetail{
		TaskID: msg.TaskId,
	})
	if err != nil {
		return nil, err
	}

	tableResultDetailSumDuration, err := models.GetStructureMigrationReaderWriter().SumTableResultDetailDurationBySchema(ctx,
		msg.TaskId,
	)
	if err != nil {
		return nil, err
	}

	var (
		totalNums          int
		successNums        int
		failedNums         int
		runningNums        int
		waitingNums        int
		compNums           int
		partialSuccessNums int
	)
	totalNums = len(tableResults)

	for _, r := range tableResults {
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
			successNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
			failedNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
			runningNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
			waitingNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS {
			partialSuccessNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE {
			compNums += 1
		}
	}

	sourceSchemas, err := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, detailChannelInfo.ChannelId, msg.TaskId)
	if err != nil {
		return nil, err
	}

	var migrationDetails []message.MigrationStructureTaskSummaryDetail

	for _, sourceSchema := range sourceSchemas {
		var (
			detailTotalNums          int
			detailSuccessNums        int
			detailPartialSuccessNums int
			detailFailedNums         int
			detailRunningNums        int
			detailWaitingNums        int
			detailCompNums           int
			detailSuccessRatio       string
		)
		for _, r := range tableResults {
			if strings.EqualFold(sourceSchema, r.SchemaNameS) {
				detailTotalNums += 1

				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
					detailSuccessNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
					detailFailedNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
					detailRunningNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
					detailWaitingNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS {
					detailPartialSuccessNums += 1
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE {
					detailCompNums += 1
				}
			}
		}

		successRatio := 0.0
		if detailTotalNums != 0 {
			successRatio = float64(detailSuccessNums) / float64(detailTotalNums)
		}
		detailSuccessRatio = fmt.Sprintf("%.2f%%", successRatio*100)

		if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
			taskInfo.EndTime = time.Now()
		}

		detailDurationStr := "0S"
		for _, r := range tableResultDetailSumDuration {
			if strings.EqualFold(sourceSchema, r.SchemaNameS) {
				detailDurationStr = time.Duration(r.Duration * float64(time.Second)).String()
			}
		}

		migrationDetails = append(migrationDetails, message.MigrationStructureTaskSummaryDetail{
			ServiceName:        sourceDS.ServiceName,
			SchemaName:         sourceSchema,
			MigrationNums:      detailTotalNums,
			SuccessNums:        detailSuccessNums,
			PartialSuccessNums: detailPartialSuccessNums,
			FailedNums:         detailFailedNums,
			RunningNums:        detailRunningNums,
			WaitingNums:        detailWaitingNums,
			CompatibleNums:     detailCompNums,
			SuccessRatio:       detailSuccessRatio,
			MigrationDuration:  detailDurationStr,
		})
	}

	var ChartData []message.TaskDetailChartData
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: totalNums,
		Type:  "转换成功",
		Count: successNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(successNums)/float64(totalNums)*100),
	})
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: totalNums,
		Type:  "部分成功",
		Count: partialSuccessNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(partialSuccessNums)/float64(totalNums)*100),
	})
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: totalNums,
		Type:  "转换失败",
		Count: failedNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(failedNums)/float64(totalNums)*100),
	})
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: totalNums,
		Type:  "不兼容",
		Count: compNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(compNums)/float64(totalNums)*100),
	})

	DDLBySchema := message.TaskDetailSchemaData{
		DdlMigration: migrationDetails,
	}

	return &message.GetTaskDetailResultResp{
		TaskId:                  msg.TaskId,
		StartTime:               taskInfo.StartTime,
		DBName:                  detailChannelInfo.DatasourceNameS,
		TotalTables:             totalNums,
		TotalDuration:           taskInfo.EndTime.Sub(taskInfo.StartTime).String(),
		TaskDetailChartDataList: ChartData,
		TaskDetailSchemaData:    DDLBySchema,
	}, nil

}

func (s *StructureMigrationService) GetReverseOBJSummaryBySchema(ctx context.Context, msg *message.MigrationSchemaReq) (*message.MigrationStructureSchemaSummaryResp, error) {
	tableGroupResults, err := models.GetStructureMigrationReaderWriter().BatchGetGroupTableResultDetail(ctx, &migration.TableResultDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
	})
	if err != nil {
		return nil, err
	}

	tableTypeMap := make(map[string]string)
	for _, r := range tableGroupResults {
		if strings.EqualFold(r.TableTypeS, "UNIQUE KEYS") {
			continue
		}
		if strings.EqualFold(r.TableTypeS, "UNIQUE INDEX") {
			continue
		}
		if strings.EqualFold(r.TableTypeS, "NORMAL INDEX") {
			continue
		}
		if strings.EqualFold(r.TableTypeS, "PRIMARY KEY") {
			continue
		}
		tableTypeMap[r.TableTypeS] = r.ServiceNameS
	}

	var schemaSummarys []message.MigrationStructureSchemaSummary

	var tableTypeMapKeys []string
	for key := range tableTypeMap {
		tableTypeMapKeys = append(tableTypeMapKeys, key)
	}
	sort.Strings(tableTypeMapKeys)

	for _, k := range tableTypeMapKeys {
		v := tableTypeMap[k]
		var schemaSummary message.MigrationStructureSchemaSummary
		schemaSummary.ServiceName = v
		schemaSummary.SchemaNameS = msg.SchemaNameS
		schemaSummary.TableTypeS = k
		for _, r := range tableGroupResults {
			if strings.EqualFold(k, r.TableTypeS) {
				schemaSummary.TableTypeT = r.TableTypeT
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
					schemaSummary.SuccessMigrationNums += r.Count
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
					schemaSummary.FailedMigrationNums += r.Count
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
					schemaSummary.RunningMigrationNums += r.Count
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
					schemaSummary.WaitingMigrationNums += r.Count
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE {
					schemaSummary.CompatibleNums += r.Count
				}
				if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS {
					schemaSummary.PartialSuccessMigrationNums += r.Count
				}
			}
		}
		schemaSummary.TotalMigrationNums = schemaSummary.SuccessMigrationNums + schemaSummary.FailedMigrationNums + schemaSummary.RunningMigrationNums + schemaSummary.WaitingMigrationNums + schemaSummary.CompatibleNums + schemaSummary.PartialSuccessMigrationNums

		schemaSummarys = append(schemaSummarys, schemaSummary)
	}

	return &message.MigrationStructureSchemaSummaryResp{MigrationSchemaSummary: schemaSummarys}, nil

}

func (s *StructureMigrationService) GetReverseOBJDetailBySchema(ctx context.Context, msg *message.MigrationSchemaPageReq) (*message.MigrationStructureTableDetailResp, *message.Page, error) {
	tableResults, total, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailPageWithoutIndex(ctx, &migration.TableResultDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableTypeS:  msg.TableTypeS,
		Status:      msg.Status,
	}, msg.Page, msg.PageSize)
	if err != nil {
		return nil, nil, err
	}
	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}

	var migrationFailedDetails []*message.MigrationStructureTableDetail

	status := ""
	if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
		status = "Waiting"
	} else if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
		status = "Running"
	} else if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
		status = "Success"
	} else if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
		status = "Failed"
	} else if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE {
		status = "Incompatible"
	} else if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_COMPATIBLE {
		status = "Compatible"
	} else if msg.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS {
		status = "PartialSuccess"
	}

	for _, r := range tableResults {
		tableGroupCompatible, err := models.GetStructureMigrationReaderWriter().GetTableCompatibleDetailCount(ctx, &migration.TableCompatibleDetail{
			TaskID:      r.TaskID,
			SchemaNameS: r.SchemaNameS,
			TableNameS:  r.TableNameS,
		})
		if err != nil {
			tableGroupCompatible.Count = 0
		}
		if tableGroupCompatible == nil {
			tableGroupCompatible = &migration.TableGroupResultDetail{
				Count: 0,
			}
		}

		indexSuccessResults, err := models.GetStructureMigrationReaderWriter().GetTableResultDetailCountForIndex(ctx, &migration.TableResultDetail{
			TaskID:      r.TaskID,
			SchemaNameS: r.SchemaNameS,
			TableNameS:  r.TableNameS,
			Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS,
		})
		if err != nil {
			return nil, nil, err
		}
		if indexSuccessResults == nil {
			indexSuccessResults = &migration.TableGroupResultDetail{
				Count: 0,
			}
		}

		indexFailedResults, err := models.GetStructureMigrationReaderWriter().GetTableResultDetailCountForIndex(ctx, &migration.TableResultDetail{
			TaskID:      r.TaskID,
			SchemaNameS: r.SchemaNameS,
			TableNameS:  r.TableNameS,
			Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED,
		})
		if err != nil {
			return nil, nil, err
		}
		if indexFailedResults == nil {
			indexFailedResults = &migration.TableGroupResultDetail{
				Count: 0,
			}
		}

		migrationFailedDetails = append(migrationFailedDetails, &message.MigrationStructureTableDetail{
			TaskRunID:         r.TaskRunID,
			ServiceNameS:      r.ServiceNameS,
			SchemaNameS:       r.SchemaNameS,
			TableNameS:        r.TableNameS,
			TableTypeS:        r.TableTypeS,
			TableTypeT:        r.TableTypeT,
			TableNameT:        r.TableNameT,
			Status:            status,
			OriginSQL:         r.OriginDDL,
			ReverseSQL:        r.ReverseDDL,
			FixedDDL:          r.FixedDDL,
			ErrorDetail:       r.ErrorDetail,
			Comment:           r.Comment,
			CreatedAt:         r.CreatedAt,
			UpdatedAt:         r.UpdatedAt,
			IncompatibleCount: tableGroupCompatible.Count,
			SuccessCount:      indexSuccessResults.Count,
			FailedCount:       indexFailedResults.Count,
		})
	}
	return &message.MigrationStructureTableDetailResp{MigrationStructureTableDetail: migrationFailedDetails}, page, nil
}

func (s *StructureMigrationService) GetReverseOBJSuccessDetailBySchema(ctx context.Context, msg *message.MigrationSchemaPageReq) (*message.MigrationStructureTableDetailResp, *message.Page, error) {
	tableResults, total, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailPage(ctx, &migration.TableResultDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableTypeS:  msg.TableTypeS,
		Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS,
	}, msg.Page, msg.PageSize)
	if err != nil {
		return nil, nil, err
	}
	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}

	var migrationFailedDetails []*message.MigrationStructureTableDetail
	for _, r := range tableResults {
		migrationFailedDetails = append(migrationFailedDetails, &message.MigrationStructureTableDetail{
			TaskRunID:    r.TaskRunID,
			ServiceNameS: r.ServiceNameS,
			SchemaNameS:  r.SchemaNameS,
			TableNameS:   r.TableNameS,
			TableTypeS:   r.TableTypeS,
			TableTypeT:   r.TableTypeT,
			TableNameT:   r.TableNameT,
			Status:       "Success",
			OriginSQL:    r.OriginDDL,
			ReverseSQL:   r.ReverseDDL,
			FixedDDL:     r.FixedDDL,
			ErrorDetail:  r.ErrorDetail,
			Comment:      r.Comment,
			CreatedAt:    r.CreatedAt,
			UpdatedAt:    r.UpdatedAt,
		})
	}
	return &message.MigrationStructureTableDetailResp{MigrationStructureTableDetail: migrationFailedDetails}, page, nil
}

func (s *StructureMigrationService) GetReverseOBJFailedDetailBySchema(ctx context.Context, msg *message.MigrationSchemaPageReq) (*message.MigrationStructureTableDetailResp, *message.Page, error) {
	tableResults, total, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailPage(ctx, &migration.TableResultDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableTypeS:  msg.TableTypeS,
		Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED,
	}, msg.Page, msg.PageSize)
	if err != nil {
		return nil, nil, err
	}
	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}

	var migrationFailedDetails []*message.MigrationStructureTableDetail
	for _, r := range tableResults {
		migrationFailedDetails = append(migrationFailedDetails, &message.MigrationStructureTableDetail{
			TaskRunID:    r.TaskRunID,
			ServiceNameS: r.ServiceNameS,
			SchemaNameS:  r.SchemaNameS,
			TableNameS:   r.TableNameS,
			TableTypeS:   r.TableTypeS,
			TableTypeT:   r.TableTypeT,
			TableNameT:   r.TableNameT,
			Status:       "Failed",
			OriginSQL:    r.OriginDDL,
			ReverseSQL:   r.ReverseDDL,
			ErrorDetail:  r.ErrorDetail,
			Comment:      r.Comment,
			CreatedAt:    r.CreatedAt,
			UpdatedAt:    r.UpdatedAt,
		})
	}
	return &message.MigrationStructureTableDetailResp{MigrationStructureTableDetail: migrationFailedDetails}, page, nil
}

func (s *StructureMigrationService) GetReverseOBJCompatibleDetailBySchema(ctx context.Context, msg *message.MigrationSchemaPageReq) (*message.MigrationStructureTableDetailResp, *message.Page, error) {
	tableCompatibles, total, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetailPage(ctx, &migration.TableCompatibleDetail{
		TaskID:      msg.TaskID,
		TableTypeS:  msg.TableTypeS,
		SchemaNameS: msg.SchemaNameS,
		Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE,
	}, msg.Page, msg.PageSize)
	if err != nil {
		return nil, nil, err
	}
	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}
	var migrationCompDetails []*message.MigrationStructureTableDetail
	for _, r := range tableCompatibles {
		migrationCompDetails = append(migrationCompDetails, &message.MigrationStructureTableDetail{
			TaskRunID:    r.TableCompID,
			ServiceNameS: r.ServiceNameS,
			SchemaNameS:  r.SchemaNameS,
			TableNameS:   r.TableNameS,
			TableTypeS:   r.TableTypeS,
			TableTypeT:   r.TableTypeT,
			TableNameT:   r.TableNameT,
			Status:       "Function Isn't Compatible",
			OriginSQL:    r.OriginDDL,
			ReverseSQL:   r.CompatibleDDL,
			ErrorDetail:  r.CompatibleDetail,
			Comment:      r.Comment,
			CreatedAt:    r.CreatedAt,
			UpdatedAt:    r.UpdatedAt,
		})
	}
	return &message.MigrationStructureTableDetailResp{MigrationStructureTableDetail: migrationCompDetails}, page, nil
}

func (s *StructureMigrationService) ReRunReverseOBJDDLBySchema(ctx context.Context, msg *message.MigrationDDLReRunReq) (*message.MigrationStructureDDLReRunResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return nil, fmt.Errorf("get task info failed: %v", err)
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, fmt.Errorf("get channel info failed: %v", err)
	}
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdT)
	if err != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}

	var db *sql.DB

	db, err = models.OpenMysql(ds.UserName, ds.PasswordValue, ds.HostIp, ds.HostPort, "")
	if err != nil {
		return nil, err
	}
	defer func() {
		if db != nil {
			db.Close()
		}
	}()

	decoded, err := base64.StdEncoding.DecodeString(msg.FixedDDL)
	if err != nil {
		return nil, err
	}
	msg.FixedDDL = string(decoded)
	_, err = db.ExecContext(ctx, msg.FixedDDL)
	if err != nil {
		return nil, err
	}

	// table result and compatible status update
	if msg.IsCompatible == "Function Isn't Compatible" {
		_, err := models.GetStructureMigrationReaderWriter().UpdateTableCompatibleDetailStatusFixed(ctx, &migration.TableCompatibleDetail{
			TableCompID:   msg.TaskRunId,
			TaskID:        msg.TaskID,
			SchemaNameS:   msg.SchemaNameS,
			TableNameS:    msg.TableNameS,
			TableTypeS:    msg.TableTypeS,
			CompatibleDDL: msg.ReverseDDL,
			FixedDDL:      msg.FixedDDL,
			Status:        constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_COMPATIBLE,
		})
		if err != nil {
			return nil, err
		}
	} else {
		_, err := models.GetStructureMigrationReaderWriter().UpdateTableResultDetailStatusFixed(ctx, &migration.TableResultDetail{
			TaskRunID:   msg.TaskRunId,
			TaskID:      msg.TaskID,
			SchemaNameS: msg.SchemaNameS,
			TableNameS:  msg.TableNameS,
			TableTypeS:  msg.TableTypeS,
			ReverseDDL:  msg.ReverseDDL,
			FixedDDL:    msg.FixedDDL,
			Status:      constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS,
		})
		if err != nil {
			return nil, err
		}
	}

	// task status
	tableFailedResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetail(ctx, &migration.TableResultDetail{
		TaskID: msg.TaskID,
		Status: constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED,
	})
	if err != nil {
		return nil, fmt.Errorf("task exec failed, get table result detail by task id [%v] and status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err)
	}

	tableCompResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetail(ctx, &migration.TableCompatibleDetail{
		TaskID: msg.TaskID,
		Status: constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE,
	})
	if err != nil {
		return nil, fmt.Errorf("task exec failed, get table compatible detail by task id [%v] and status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FAILED, err)
	}

	if len(tableFailedResults) == 0 && len(tableCompResults) == 0 {
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, msg.TaskID, task.Task{
			EndTime:    time.Now(),
			TaskStatus: constants.TASK_STATUS_FINISH,
		}); err != nil {
			return nil, fmt.Errorf("task rerun exec success, update task id [%v] status [%v] failed: %v", msg.TaskID, constants.TASK_STATUS_FINISH, err)
		}
	}

	return &message.MigrationStructureDDLReRunResp{
		TaskName: taskInfo.TaskName,
		FixedDDL: msg.FixedDDL,
	}, nil
}

func (s *StructureMigrationService) GetReverseOBJDDLByTask(ctx context.Context, msg *message.MigrationTaskReq) (*message.MigrationStructureDDLDetailResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return nil, fmt.Errorf("get task info failed: %v", err)
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, fmt.Errorf("get channel info failed: %v", err)
	}
	// get table by schema
	sourceSchemas, err := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, detailChannelInfo.ChannelId, msg.TaskID)
	if err != nil {
		return nil, fmt.Errorf("get task target datasource info failed: %v", err)

	}

	var ddls []message.MigrationStructureDDLDetail
	for _, sourceSchema := range sourceSchemas {
		tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetail(ctx, &migration.TableResultDetail{
			TaskID:      msg.TaskID,
			SchemaNameS: sourceSchema,
		})
		if err != nil {
			return nil, err
		}

		tableComps, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetail(ctx, &migration.TableCompatibleDetail{
			TaskID:      msg.TaskID,
			SchemaNameS: sourceSchema,
		})
		if err != nil {
			return nil, err
		}

		var (
			br bytes.Buffer
			bc bytes.Buffer
		)
		for _, t := range tableResults {
			br.WriteString(t.ReverseDDL + ";\n\n")
		}
		for _, t := range tableComps {
			bc.WriteString(t.CompatibleDDL + ";\n\n")
		}

		ddls = append(ddls, message.MigrationStructureDDLDetail{
			SchemaNameS:     sourceSchema,
			ReverseDDL:      br.String(),
			IncompatibleDDL: bc.String()})
	}

	return &message.MigrationStructureDDLDetailResp{
		SchemaDDLS: ddls,
	}, nil
}

func (s *StructureMigrationService) GetReverseOBJDDLBySchema(ctx context.Context, msg *message.MigrationSchemaReq) (*message.MigrationStructureDDLDetailResp, error) {
	tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetail(ctx, &migration.TableResultDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
	})
	if err != nil {
		return nil, err
	}

	tableComps, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetail(ctx, &migration.TableCompatibleDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
	})
	if err != nil {
		return nil, err
	}

	var (
		br   bytes.Buffer
		bc   bytes.Buffer
		ddls []message.MigrationStructureDDLDetail
	)
	for _, t := range tableResults {
		br.WriteString(t.ReverseDDL + ";\n\n")
	}
	for _, t := range tableComps {
		bc.WriteString(t.CompatibleDDL + ";\n\n")
	}

	ddls = append(ddls,
		message.MigrationStructureDDLDetail{
			SchemaNameS:     msg.SchemaNameS,
			ReverseDDL:      br.String(),
			IncompatibleDDL: bc.String()})

	return &message.MigrationStructureDDLDetailResp{
		SchemaDDLS: ddls,
	}, nil
}

func (s *StructureMigrationService) GetReverseOBJProgressBarByTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationStructureDDLProgressResp, error) {
	var (
		totalNums                   int
		successNums                 int
		failedNums                  int
		runningNums                 int
		waitingNums                 int
		partialSuccessMigrationNums int
		incompatibleMigrationNums   int
		lastUpdateTime              time.Time
	)

	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		tableIntervalResults, err := models.GetStructureMigrationReaderWriter().GetTableResultDetailByInterval(transactionCtx, &migration.TableResultDetail{
			TaskID: msg.TaskID,
			Entity: &common2.Entity{
				CreatedAt: msg.ProgressStartTime,
				UpdatedAt: msg.ProgressStartTime,
			},
		})
		if err != nil {
			return err
		}

		lastUpdateTime = msg.ProgressStartTime
		totalNums = len(tableIntervalResults)
		for _, r := range tableIntervalResults {
			if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
				successNums += 1
			}
			if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
				failedNums += 1
			}
			if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
				runningNums += 1
			}
			if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
				waitingNums += 1
			}
			if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE {
				incompatibleMigrationNums += 1
			}
			if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS {
				partialSuccessMigrationNums += 1
			}
			if r.UpdatedAt.After(lastUpdateTime) {
				lastUpdateTime = r.UpdatedAt
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &message.MigrationStructureDDLProgressResp{
		TaskID:                      msg.TaskID,
		TotalMigrationNums:          totalNums,
		SuccessMigrationNums:        successNums,
		PartialSuccessMigrationNums: partialSuccessMigrationNums,
		IncompatibleMigrationNums:   incompatibleMigrationNums,
		FailedMigrationNums:         failedNums,
		RunningMigrationNums:        runningNums,
		WaitingMigrationNums:        waitingNums,
		ProgressStartTime:           msg.ProgressStartTime,
		LastUpdateTime:              lastUpdateTime,
	}, nil
}

func GetReverseOBJCount(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationStructureDDLProgressResp, error) {
	var (
		totalNums      int
		successNums    int
		failedNums     int
		runingNums     int
		waitingNums    int
		lastUpdateTime time.Time
	)
	tableIntervalResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetail(ctx, &migration.TableResultDetail{
		TaskID: msg.TaskID,
	})
	if err != nil {
		return nil, err
	}
	totalNums = len(tableIntervalResults)
	for _, r := range tableIntervalResults {
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
			successNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
			failedNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
			runingNums += 1
		}
		if r.Status == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
			waitingNums += 1
		}
	}
	return &message.MigrationStructureDDLProgressResp{
		TaskID:               msg.TaskID,
		TotalMigrationNums:   totalNums,
		SuccessMigrationNums: successNums,
		FailedMigrationNums:  failedNums,
		RunningMigrationNums: runingNums,
		WaitingMigrationNums: waitingNums,
		ProgressStartTime:    msg.ProgressStartTime,
		LastUpdateTime:       lastUpdateTime,
	}, nil
}

func (s *StructureMigrationService) GetReverseOBJProgressLogByTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationStructureDDLProgressResp, error) {
	progressLogIntervals, err := models.GetProgressLogReaderWriter().GetProcessLogDetailByInterval(ctx, &common2.ProgressLogDetail{
		TaskID: msg.TaskID,
		Entity: &common2.Entity{
			CreatedAt: msg.ProgressStartTime,
			UpdatedAt: msg.ProgressStartTime,
		},
	})
	if err != nil {
		return nil, err
	}

	var progressLogs []*message.MigrationLogDetail
	for _, p := range progressLogIntervals {
		progressLogs = append(progressLogs, buildMigrationStructureLogDetailMessageFromModel(p))
	}

	return &message.MigrationStructureDDLProgressResp{
		TaskID:            msg.TaskID,
		ProgressLog:       progressLogs,
		ProgressStartTime: msg.ProgressStartTime,
	}, err
}

func buildMigrationStructureLogDetailMessageFromModel(table *common2.ProgressLogDetail) *message.MigrationLogDetail {
	return &message.MigrationLogDetail{
		ProgressID:  table.ProgressID,
		ChannelID:   table.ChannelID,
		TaskID:      table.TaskID,
		SchemaNameS: table.SchemaNameS,
		TableNameS:  table.TableNameS,
		Detail:      table.Detail,
		Comment:     table.Comment,
		CreatedAt:   table.CreatedAt,
		UpdatedAt:   table.UpdatedAt,
		LogLevel:    table.LogLevel,
	}
}

func (s *StructureMigrationService) GetStructureProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	log.Infof("start migration structure GetStructureProgress taskId:%d, channelId:%d, startTM:%v", req.TaskId, req.ChannelId, req.StartTime)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	// total tables GetChannelSchemaTablesCountsByTask
	//totalTaskTableCount, err := models.GetChannelReaderWriter().GetChannelSchemaTablesCountsByTask(ctx, req.ChannelId, req.TaskId)
	//if err != nil {
	//	log.Errorf("get totalTaskTableCount failed, err:%v", err)
	//}
	DDLReq := &message.MigrationProgressReq{
		TaskID:            req.TaskId,
		ProgressStartTime: req.StartTime,
	}
	BarData, err := NewStructureMigrationService().GetReverseOBJProgressBarByTask(ctx, DDLReq)
	if err != nil {
		log.Errorf(fmt.Sprintf("get migration structure BarData data failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}

	StructureProgress := &message.GetTaskProgressResp{
		TaskId:             req.TaskId,
		StartTime:          req.StartTime,
		TotalNums:          BarData.TotalMigrationNums, //int(totalTaskTableCount)
		SuccessNums:        BarData.SuccessMigrationNums,
		FailedNums:         BarData.FailedMigrationNums,
		LastUpdateTime:     BarData.LastUpdateTime,
		IncompatibleNums:   BarData.IncompatibleMigrationNums,
		PartialSuccessNums: BarData.PartialSuccessMigrationNums,
		TaskLogFile:        "./data/logs/tims.log",
		Progress:           stringutil.Decimal(taskInfo.Progress),
	}
	LogData, err := NewStructureMigrationService().GetReverseOBJProgressLogByTask(ctx, DDLReq)
	if err != nil {
		log.Errorf(fmt.Sprintf("get migration structure LogData data failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}

	var progressLog []*message.TaskProgressLogDetail
	logStartTime := req.StartTime
	for _, v := range LogData.ProgressLog {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.CreatedAt,
			LogMessage: v.Detail,
			LogLevel:   v.LogLevel,
		})
		if v.CreatedAt.Before(logStartTime) {
			logStartTime = v.CreatedAt
		}
	}

	StructureProgress.ProgressLogInfo = &message.TaskProgressLogInfo{
		TaskId:        req.TaskId,
		LogStartTime:  logStartTime,
		LogCount:      len(progressLog),
		LogDetailList: progressLog,
	}

	return StructureProgress, nil
}

func (s *StructureMigrationService) DownloadReverseOBJDDLBySchema(ctx context.Context, msg *message.MigrationDDLDownloadReq, sqlFileName string) error {
	// 创建目录
	sqlFileName_lst := strings.Split(sqlFileName, "/")
	sqlFile := sqlFileName_lst[len(sqlFileName_lst)-1]
	sqlPath := strings.Replace(sqlFileName, sqlFile, "", 1)
	if sqlPath != "" {
		_, err := os.Stat(sqlPath)
		if err != nil {
			log.Infof("sql file path not exists, start create it. %v", sqlPath)
			err = os.MkdirAll(sqlPath, os.ModePerm)
			if err != nil {
				log.Errorf("create path failed. err:%s", err)
			}
		}
	}
	file, err := os.OpenFile(sqlFileName, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		log.Errorf("open file %s failed. err:%s", sqlFileName, err)
	}
	defer file.Close()
	// 获取数据
	tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailBySchema(ctx, msg.TaskID, msg.SchemaNameS)
	if err != nil {
		log.Errorf("get tableResults failed. err:%s", err)
		return err
	}
	tableComps, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetailBySchema(ctx, msg.TaskID, msg.SchemaNameS)
	if err != nil {
		log.Errorf("get tableComps failed. err:%s", err)
		return err
	}
	// 写sql文件
	var (
		br bytes.Buffer
		bc bytes.Buffer
		// ddls []message.MigrationStructureDDLDetail
	)
	for _, t := range tableResults {
		if t.FixedDDL != "" {
			br.WriteString(t.FixedDDL + "\n\n")
		} else {
			br.WriteString(t.ReverseDDL + "\n\n")
		}
	}
	file.WriteString(fmt.Sprintf(`
	--TaskID:%d 
	--Schemas:%s 
	--ReverseDDL
	--total tables:%d 
	
`, msg.TaskID, msg.SchemaNameS, len(tableResults)))
	_, err = file.WriteString(br.String())
	if err != nil {
		log.Errorf("file.WriteString ReverseDDL failed. err:%s", err)
		return err
	}

	for _, t := range tableComps {
		if t.CompatibleDDL != "" && t.CompatibleDDL != "-- please manual enter reverse sql" {
			bc.WriteString(t.CompatibleDDL + "\n\n")
		} else {
			bc.WriteString(t.OriginDDL + "\n\n")
		}

	}
	file.WriteString(fmt.Sprintf("\n\n--CompatibleDDL\n--total tables:%d \n\n", len(tableComps)))
	_, err = file.WriteString(bc.String())
	if err != nil {
		log.Errorf("file.WriteString CompatibleDDL failed. err:%s", err)
		return err
	}

	return nil
}

func (s *StructureMigrationService) DownloadReverseOBJDDLBySchemaAndStatus(ctx context.Context, msg *message.MigrationDDLDownloadReq, status int, sqlFileName string) error {
	// 创建目录
	sqlFileName_lst := strings.Split(sqlFileName, "/")
	sqlFile := sqlFileName_lst[len(sqlFileName_lst)-1]
	sqlPath := strings.Replace(sqlFileName, sqlFile, "", 1)
	if sqlPath != "" {
		_, err := os.Stat(sqlPath)
		if err != nil {
			log.Infof("sql file path not exists, start create it. %v", sqlPath)
			err = os.MkdirAll(sqlPath, os.ModePerm)
			if err != nil {
				log.Errorf("create path failed. err:%s", err)
			}
		}
	}
	file, err := os.OpenFile(sqlFileName, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		log.Errorf("open file %s failed. err:%s", sqlFileName, err)
	}
	defer file.Close()
	// 获取数据
	tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetailBySchemaAndStatus(ctx, msg.TaskID, msg.SchemaNameS, status)
	if err != nil {
		log.Errorf("get tableResults failed. err:%s", err)
		return err
	}
	tableComps, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetailBySchema(ctx, msg.TaskID, msg.SchemaNameS)
	if err != nil {
		log.Errorf("get tableComps failed. err:%s", err)
		return err
	}
	// 写sql文件
	var (
		br bytes.Buffer
		bc bytes.Buffer
		// ddls []message.MigrationStructureDDLDetail
	)
	for _, t := range tableResults {
		if t.FixedDDL != "" {
			br.WriteString(t.FixedDDL + "\n\n")
		} else {
			br.WriteString(t.ReverseDDL + "\n\n")
		}
	}
	file.WriteString(fmt.Sprintf(`
	--TaskID:%d 
	--Schemas:%s 
	--ReverseDDL
	--total tables:%d 
	
`, msg.TaskID, msg.SchemaNameS, len(tableResults)))
	_, err = file.WriteString(br.String())
	if err != nil {
		log.Errorf("file.WriteString ReverseDDL failed. err:%s", err)
		return err
	}

	for _, t := range tableComps {
		if t.CompatibleDDL != "" && t.CompatibleDDL != "-- please manual enter reverse sql" {
			bc.WriteString(t.CompatibleDDL + "\n\n")
		} else {
			bc.WriteString(t.OriginDDL + "\n\n")
		}
	}
	file.WriteString(fmt.Sprintf("\n\n--CompatibleDDL\n--total tables:%d \n\n", len(tableComps)))
	_, err = file.WriteString(bc.String())
	if err != nil {
		log.Errorf("file.WriteString CompatibleDDL failed. err:%s", err)
		return err
	}

	return nil
}

/*
* 把ChannelID、SchemaNameS、TableNameS做成可选参数
 */
func FastCreateProgrLogDetail(ctx context.Context, taskId int, logLevel string, msg string, args ...interface{}) {
	channelID := 0
	schemaNameS := SchemaNameStr("")
	tableNameS := TableNameStr("")
	for _, arg := range args { //遍历切片，根据不同的类型获取可选参数值
		switch arg.(type) {
		case int:
			channelID = arg.(int)
		case SchemaNameStr:
			schemaNameS = arg.(SchemaNameStr)
		case TableNameStr:
			tableNameS = arg.(TableNameStr)
		}
	}
	_, err := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common2.ProgressLogDetail{
		ChannelID:   channelID,
		TaskID:      taskId,
		SchemaNameS: string(schemaNameS),
		TableNameS:  string(tableNameS),
		LogLevel:    logLevel,
		Detail:      msg,
	})
	if err != nil {
		ErrorLog(ctx, taskId, fmt.Sprintf("[T02-003]:create progress log detail record failed: %v", err))
	}
}

func DebugLog(ctx context.Context, taskId int, msg string, args ...interface{}) {
	log.Debugf(msg)
	FastCreateProgrLogDetail(ctx, taskId, "debug", msg, args)
}

func InfoLog(ctx context.Context, taskId int, msg string, args ...interface{}) {
	log.Infof(msg)
	FastCreateProgrLogDetail(ctx, taskId, "info", msg, args)
}

func ErrorLog(ctx context.Context, taskId int, msg string, args ...interface{}) {
	log.Errorf(msg)
	FastCreateProgrLogDetail(ctx, taskId, "error", msg, args)
}

func WarnLog(ctx context.Context, taskId int, msg string, args ...interface{}) {
	log.Warnf(msg)
	FastCreateProgrLogDetail(ctx, taskId, "warn", msg, args)
}

func UpdateTaskProgress(ctx context.Context, taskId int, progress float64) {
	// 检查 progress 是否为 NaN 或 Infinity
	if math.IsNaN(progress) || math.IsInf(progress, 0) {
		ErrorLog(ctx, taskId, fmt.Sprintf("[T02-001]:task exec failed, update task id [%v] progress [%g] is NaN or Infinity, skipping update", taskId, progress))
		return
	}

	// 确保 progress 在有效范围内 (0.0 到 100.0)
	if progress < 0.0 {
		progress = 0.0
	} else if progress > 100.0 {
		progress = 100.0
	}

	_, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
		Progress: progress,
	})
	if err != nil {
		ErrorLog(ctx, taskId, fmt.Sprintf("[T02-001]:task exec failed, update task id [%v] progress [%g] failed: %v", taskId, progress, err))
	}
}

func CreateTableResultDetail(ctx context.Context, taskId int, trd *migration.TableResultDetail) {
	_, err := models.GetStructureMigrationReaderWriter().CreateTableResultDetail(ctx, trd)
	if err != nil {
		ErrorLog(ctx, taskId, fmt.Sprintf("[T02-003]:create table result detail ddl record failed, status: [failed],error: %v", err))
	}
}

func (s *StructureMigrationService) GetOriginDDL(ctx context.Context, req *message.GetOriginDDLReq) (*message.GetOriginDDLResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskID)
	if err != nil {
		return nil, fmt.Errorf("get task info failed: %v", err)
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, fmt.Errorf("get channel info failed: %v", err)
	}
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}

	dt, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdT)
	if err != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}

	if strings.EqualFold(detailChannelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		oracle, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			ServiceName: ds.ServiceName,
			Charset:     ds.Charset,
			SessionParams: []string{
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SQLTERMINATOR', true); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'PRETTY', true); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SEGMENT_ATTRIBUTES', false); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'TABLESPACE', false); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'STORAGE', false); END;",
			},
		}, "")
		if err != nil {
			return nil, fmt.Errorf("[T02-002]:create source datasource conn failed: %v", err)
		}

		originDDL, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableOriginDDL(ctx, "TABLE", req.SchemaNameS, req.TableNameS, oracle.OracleDB)
		if err != nil {
			return nil, fmt.Errorf("table [%v][%v] GetOracleSchemaTableOriginDDL failed, %v", strings.ToUpper(req.SchemaNameS), req.TableNameS, err)
		}

		convertUtf8Raw, err := common.CharsetConvert([]byte(originDDL), common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(ds.Charset)], common.MYSQLCharsetUTF8MB4)
		if err != nil {
			return nil, fmt.Errorf("table [%v] ddl charset convert failed, %v", req.TableNameS, err)
		}

		convertTargetRaw, err := common.CharsetConvert(convertUtf8Raw, common.MYSQLCharsetUTF8MB4, common.StringUPPER(dt.Charset))
		if err != nil {
			return nil, fmt.Errorf("table [%v] ddl charset convert failed, %v", req.TableNameS, err)
		} else {
			originDDL = string(convertTargetRaw)
		}
		return &message.GetOriginDDLResp{
			OriginDDL: originDDL,
		}, nil
	}

	return nil, nil
}

func (s *StructureMigrationService) GetReverseOBJDetailByTable(ctx context.Context, msg *message.MigrationSchemaReq) (*message.MigrationStructureTableDetailResp, error) {
	tableResults, err := models.GetStructureMigrationReaderWriter().BatchGetTableResultDetail(ctx, &migration.TableResultDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
	})
	if err != nil {
		return nil, err
	}

	tableComps, err := models.GetStructureMigrationReaderWriter().BatchGetTableCompatibleDetailByTable(ctx, msg.TaskID, msg.SchemaNameS, msg.TableNameS)
	if err != nil {
		log.Errorf("get tableComps failed. err:%s", err)
		return nil, err
	}

	var migrationDetails []*message.MigrationStructureTableDetail

	for _, tableResult := range tableResults {
		migrationDetails = append(migrationDetails, &message.MigrationStructureTableDetail{
			TaskRunID:    tableResult.TaskRunID,
			ServiceNameS: tableResult.ServiceNameS,
			SchemaNameS:  tableResult.SchemaNameS,
			SchemaNameT:  tableResult.SchemaNameT,
			TableNameS:   tableResult.TableNameS,
			ObjectNameS:  tableResult.ObjectNameS,
			TableTypeS:   tableResult.TableTypeS,
			TableTypeT:   tableResult.TableTypeT,
			TableNameT:   tableResult.TableNameT,
			Status:       getTableStatus(tableResult.Status),
			OriginSQL:    tableResult.OriginDDL,
			ReverseSQL:   tableResult.ReverseDDL,
			FixedDDL:     tableResult.FixedDDL,
			ErrorDetail:  tableResult.ErrorDetail,
			Comment:      tableResult.Comment,
			CreatedAt:    tableResult.CreatedAt,
			UpdatedAt:    tableResult.UpdatedAt,
		})
	}

	for _, r := range tableComps {
		migrationDetails = append(migrationDetails, &message.MigrationStructureTableDetail{
			TaskRunID:    r.TableCompID,
			ServiceNameS: r.ServiceNameS,
			SchemaNameS:  r.SchemaNameS,
			SchemaNameT:  r.SchemaNameT,
			TableNameS:   r.TableNameS,
			ObjectNameS:  r.ObjectNameS,
			TableTypeS:   r.TableTypeS,
			TableTypeT:   r.TableTypeT,
			TableNameT:   r.TableNameT,
			Status:       getTableStatus(r.Status),
			OriginSQL:    r.OriginDDL,
			ReverseSQL:   r.CompatibleDDL,
			FixedDDL:     r.FixedDDL,
			ErrorDetail:  r.CompatibleDetail,
			Comment:      r.Comment,
			CreatedAt:    r.CreatedAt,
			UpdatedAt:    r.UpdatedAt,
		})
	}
	return &message.MigrationStructureTableDetailResp{MigrationStructureTableDetail: migrationDetails}, nil
}

func getTableStatus(statusCode int) string {
	status := ""
	if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING {
		status = "Waiting"
	} else if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_RUNNING {
		status = "Running"
	} else if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_SUCCESS {
		status = "Success"
	} else if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED {
		status = "Failed"
	} else if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE {
		status = "Incompatible"
	} else if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_COMPATIBLE {
		status = "Compatible"
	} else if statusCode == constants.TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS {
		status = "PartialSuccess"
	}
	return status
}

func (s *StructureMigrationService) GetOracleDDL(ctx context.Context, req *message.GetOracleDDLReq) (*message.GetOriginDDLResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskID)
	if err != nil {
		return nil, fmt.Errorf("get task info failed: %v", err)
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, fmt.Errorf("get channel info failed: %v", err)
	}
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}

	dt, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdT)
	if err != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}

	if strings.EqualFold(detailChannelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		oracle, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    ds.UserName,
			Password:    ds.PasswordValue,
			Host:        ds.HostIp,
			Port:        ds.HostPort,
			ServiceName: ds.ServiceName,
			Charset:     ds.Charset,
			SessionParams: []string{
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SQLTERMINATOR', true); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'PRETTY', true); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SEGMENT_ATTRIBUTES', false); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'TABLESPACE', false); END;",
				"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'STORAGE', false); END;",
			},
		}, "")
		if err != nil {
			return nil, fmt.Errorf("[T02-002]:create source datasource conn failed: %v", err)
		}

		originDDL, err := models.GetDatasourceReaderWriter().GetOracleDDL(ctx, req.SchemaNameS, req.ObjectNameS, req.ObjectTypeS, oracle.OracleDB)
		if err != nil {
			return nil, fmt.Errorf("table [%v][%v] GetOracleSchemaTableOriginDDL failed, %v", strings.ToUpper(req.SchemaNameS), req.ObjectNameS, err)
		}

		convertUtf8Raw, err := common.CharsetConvert([]byte(originDDL), common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(ds.Charset)], common.MYSQLCharsetUTF8MB4)
		if err != nil {
			return nil, fmt.Errorf("table [%v] ddl charset convert failed, %v", req.ObjectNameS, err)
		}

		convertTargetRaw, err := common.CharsetConvert(convertUtf8Raw, common.MYSQLCharsetUTF8MB4, common.StringUPPER(dt.Charset))
		if err != nil {
			return nil, fmt.Errorf("table [%v] ddl charset convert failed, %v", req.ObjectNameS, err)
		} else {
			originDDL = string(convertTargetRaw)
		}
		return &message.GetOriginDDLResp{
			OriginDDL: originDDL,
		}, nil
	}

	return nil, nil
}
