/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package migration

import (
	"context"
	"fmt"
	"path"
	"sort"
	"strconv"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	migrationpkg "gitee.com/pingcap_enterprise/tms/pkg/migration"
	commonservice "gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/file"
	stringUtil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/pingcap/errors"
	"github.com/samber/lo"

	tmsconfig "gitee.com/pingcap_enterprise/tms/util/config"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	commonmodel "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type CSVMigrationService struct {
}

func NewCSVMigrationService() *CSVMigrationService {
	return &CSVMigrationService{}
}

func doEscapeCharacter(value string) string {
	if strings.Contains(value, "\\n") {
		value = strings.ReplaceAll(value, "\\n", "\n")
	}
	if strings.Contains(value, "\\r") {
		value = strings.ReplaceAll(value, "\\r", "\r")
	}
	if strings.Contains(value, "\\t") {
		value = strings.ReplaceAll(value, "\\t", "\t")
	}
	if strings.Contains(value, "\\\\") {
		value = strings.ReplaceAll(value, "\\\\", "\\")
	}
	return value
}

func (s *CSVMigrationService) GetCSVMigrationSummaryByTask(ctx context.Context, msg *message.MigrationTaskReq) (*message.MigrationDataTaskSummaryResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return nil, err
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, err
	}
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return nil, err
	}

	tableSummaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID: msg.TaskID,
	})
	if err != nil {
		return nil, err
	}

	var (
		totalNums   int
		successNums int
		runningNums int
		waitingNums int
		failedNums  int
	)
	totalNums = len(tableSummaries)
	for _, r := range tableSummaries {
		if r.TaskStatus == common.TaskStatusSuccess {
			successNums += 1
		}
		if r.TaskStatus == common.TaskStatusFailed || r.TaskStatus == constants.MigrationStatusInvalid {
			failedNums += 1
		}
		if r.TaskStatus == common.TaskStatusRunning {
			runningNums += 1
		}
		if r.TaskStatus == common.TaskStatusWaiting {
			waitingNums += 1
		}
	}

	schemaTableNums, getNumErr := models.GetChannelReaderWriter().GroupChannelSchemaTablesCountsByTask(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if getNumErr != nil {
		log.Errorf("get channel schema table num failed, taskId:%d, err: %v", msg.TaskID, getNumErr)
		return nil, getNumErr
	}

	var migrationDetails []message.MigrationDataTaskSummaryDetail

	schemas := lo.Keys(schemaTableNums)

	for _, sourceSchema := range schemas {
		log.Debugf("sourceSchema: %s,tableNums: %d", sourceSchema, schemaTableNums[sourceSchema])
		var (
			detailTotalNums         int
			detailSuccessNums       int
			detailFailedNums        int
			detailRunningNums       int
			detailWaitingNums       int
			detailSuccessRatio      string
			detailMigrationResult   string
			detailMigrationDuration float64
		)
		for _, r := range tableSummaries {
			if strings.EqualFold(sourceSchema, r.SchemaNameS) {
				detailTotalNums += 1

				if r.TaskStatus == common.TaskStatusSuccess {
					detailSuccessNums += 1
				}
				if r.TaskStatus == common.TaskStatusFailed || r.TaskStatus == constants.MigrationStatusInvalid {
					detailFailedNums += 1
				}
				if r.TaskStatus == common.TaskStatusRunning {
					detailRunningNums += 1
				}
				if r.TaskStatus == common.TaskStatusWaiting {
					detailWaitingNums += 1
				}

				detailMigrationDuration += r.Duration
			}
		}

		successRatio, err := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(detailSuccessNums)/float64(detailTotalNums)), 64)
		if err != nil {
			return nil, err
		}
		detailSuccessRatio = fmt.Sprintf("%.2f", successRatio*100)

		detailDurationStr := time.Duration(detailMigrationDuration * float64(time.Second)).String()

		if detailFailedNums != 0 {
			detailMigrationResult = common.TaskStatusFailed
		} else if detailFailedNums == 0 && detailRunningNums != 0 {
			detailMigrationResult = common.TaskStatusRunning
		} else if detailFailedNums == 0 && detailWaitingNums != 0 && detailRunningNums == 0 {
			detailMigrationResult = common.TaskStatusWaiting
		} else {
			detailMigrationResult = common.TaskStatusSuccess
		}

		migrationDetails = append(migrationDetails, message.MigrationDataTaskSummaryDetail{
			ServiceName:       sourceDS.ServiceName,
			SchemaName:        sourceSchema,
			MigrationNums:     detailTotalNums,
			TotalTableNums:    schemaTableNums[sourceSchema],
			SuccessNums:       detailSuccessNums,
			RunningNums:       detailRunningNums,
			WaitingNums:       detailWaitingNums,
			FailedNums:        detailFailedNums,
			SuccessRatio:      detailSuccessRatio,
			MigrationResult:   detailMigrationResult,
			MigrationDuration: detailDurationStr,
		})
	}

	return &message.MigrationDataTaskSummaryResp{
		TaskID:               msg.TaskID,
		TotalMigrationNums:   totalNums,
		SuccessMigrationNums: successNums,
		RunningMigrationNums: runningNums,
		WaitingMigrationNums: waitingNums,
		FailedMigrationNums:  failedNums,
		MigrationDetails:     migrationDetails,
	}, nil
}

func (s *CSVMigrationService) GetCSVMigrationSummaryBySchema(ctx context.Context, msg *message.MigrationDataSchemaReq) (*message.MigrationDataSchemaSummaryResp, error) {
	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if getTaskErr != nil {
		log.Errorf("get task info failed: %v", getTaskErr)
		return nil, getTaskErr
	}

	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
	})
	if getSummaryErr != nil {
		log.Errorf("get csv migration summary failed, taskId:%d, schemaNameS:%s, err: %v", msg.TaskID, msg.SchemaNameS, getSummaryErr)
		return nil, getSummaryErr
	}
	channelSchemaTableNum, getNumErr := models.GetChannelReaderWriter().GroupChannelSchemaTablesCountsByTask(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if getNumErr != nil {
		log.Errorf("get channel schema table num failed, taskId:%d, err: %v", msg.TaskID, getNumErr)
		return nil, getNumErr
	}

	var (
		serviceName string
		totalNums   int
		successNums int
		runningNums int
		waitingNums int
		failedNums  int
		duration    float64
	)
	// totalNums = len(tableSummaries)
	for _, r := range tableSummaries {
		totalNums += 1
		duration += r.Duration
		serviceName = r.ServiceNameS
		if r.TaskStatus == common.TaskStatusSuccess {
			successNums += 1
		}
		if r.TaskStatus == common.TaskStatusFailed || r.TaskStatus == constants.MigrationStatusInvalid {
			failedNums += 1
		}
		if r.TaskStatus == common.TaskStatusRunning {
			runningNums += 1
		}
		if r.TaskStatus == common.TaskStatusWaiting {
			waitingNums += 1
		}
	}

	successRatio, err := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(successNums)/float64(totalNums)), 64)
	if err != nil {
		log.Errorf("get csv migration summary, parse float failed, taskId:%d, schemaNameS:%s, err: %v", msg.TaskID, msg.SchemaNameS, err)
		return nil, err
	}
	successRatioStr := fmt.Sprintf("%.2f", successRatio*100)

	detailDurationStr := time.Duration(duration * float64(time.Second)).String()

	var schemaSummaries []message.MigrationDataTaskSummaryDetail
	schemaSummaries = append(schemaSummaries, message.MigrationDataTaskSummaryDetail{
		ServiceName:       serviceName,
		SchemaName:        msg.SchemaNameS,
		TotalTableNums:    channelSchemaTableNum[msg.SchemaNameS],
		MigrationNums:     totalNums,
		SuccessNums:       successNums,
		FailedNums:        failedNums,
		RunningNums:       runningNums,
		WaitingNums:       waitingNums,
		SuccessRatio:      successRatioStr,
		MigrationDuration: detailDurationStr,
	})
	return &message.MigrationDataSchemaSummaryResp{MigrationSchemaSummary: schemaSummaries}, nil
}

func (s *CSVMigrationService) GetCSVMigrationDetailBySchema(ctx context.Context, msg *message.MigrationDataSchemaPageReq) (*message.MigrationDataSchemaDetailResp, *message.Page, error) {

	taskId := msg.TaskID
	schemaName := msg.SchemaNameS

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("get task info failed: %v", getTaskErr)
		return nil, nil, getTaskErr
	}
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed: %v", getChannelErr)
		return nil, nil, getChannelErr
	}

	dataSources, getSourceErr := migrationpkg.GetDatabaseSources(ctx, channelInfo, taskInfo.TaskID)
	if getSourceErr != nil {
		log.Errorf("get database sources failed: %v", getSourceErr)
		return nil, nil, getSourceErr
	}

	var queryStatus []string
	if msg.QueryStatus != "" {
		queryStatus = append(queryStatus, msg.QueryStatus)
	}
	if msg.QueryStatus == transfercommon.TaskStatusFailed {
		queryStatus = append(queryStatus, constants.MigrationStatusInvalid)
	}

	log.Infof("query csv migration summary, taskId: %d, schemaNameS: %s, queryStatus: %v, page: %d, pageSize: %d",
		taskId, schemaName, queryStatus, msg.Page, msg.PageSize)
	queryCondition := &migration.TableMigrationSummary{
		TaskID:      taskId,
		SchemaNameS: schemaName,
	}

	tableSummaries, total, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummaryPageWithStatus(ctx, queryCondition, queryStatus, msg.Page, msg.PageSize)
	if err != nil {
		log.Errorf("get csv migration summary failed, taskId:%d, err: %v", taskId, err)
		return nil, nil, err
	}

	progressList, err := models.GetFullDataMigrationReaderWriter().GetSchemaSTableSLatestLightningProgressByTaskId(ctx, taskId)
	if err != nil {
		log.Errorf("get lightning progress failed, taskId:%d, err: %v", taskInfo.TaskID, err)
		return nil, nil, err
	}
	tableProgress := make(map[structs.SchemaTablePair]migration.LightningProgress)
	for _, p := range progressList {
		tableProgress[structs.SchemaTablePair{SchemaName: p.SchemaNameS, TableName: p.TableNameS}] = p
	}

	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}
	var sd []message.MigrationDataSchemaDetail

	for _, t := range tableSummaries {
		tableDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
			TaskID:      t.TaskID,
			SchemaNameS: t.SchemaNameS,
			TableNameS:  t.TableNameS,
		})
		if err != nil {
			log.Errorf("get table migration detail failed: %v", err)
			return nil, nil, err
		}

		csvStage, err := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, t.TaskID, t.SchemaNameS, t.TableNameS)
		if err != nil {
			log.Errorf("get csv stage failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err: %v", t.TaskID, t.SchemaNameS, t.TableNameS, err)
			return nil, nil, err
		}

		var lightningProgressVal float64
		// TODO 如果dbType=CSV，不通过这种方式计算进度了，直接用Chunk的import_status
		if dataSources.GetSourceDS().DbType == constants.DB_TYPE_CSV {
			log.Infof("db_type is %s, calcaulate lightning progress by table migration detail import status count, taskId:%d", dataSources.GetSourceDS().DbType, t.TaskID)
			val, getErr := models.GetFullDataMigrationReaderWriter().GroupTableMigrationDetailImportStatusCount(ctx, t.TaskID, t.SchemaNameS, t.TableNameS)
			if getErr != nil {
				log.Errorf("get table migration detail import status count failed: %v", getErr)
				return nil, nil, getErr
			}
			lightningProgressVal = stringUtil.Decimal(val.GetProgressVal())
		} else {
			log.Infof("db_type is %s, calcaulate lightning progress by lightning progress, taskId:%d", dataSources.GetSourceDS().DbType, t.TaskID)
			lightningProgress := tableProgress[structs.SchemaTablePair{SchemaName: t.SchemaNameS, TableName: t.TableNameS}]
			lightningProgressVal = parseTotalStrToFloat(lightningProgress.Total)
			lightningProgressVal = stringUtil.Decimal(lightningProgressVal)
		}

		var (
			successChunks int64
			failedChunks  int64
			runningChunks int64
			waitingChunks int64
		)
		var errInfo = ""
		for _, d := range tableDetails {
			if strings.EqualFold(d.TaskStatus, common.TaskStatusSuccess) {
				successChunks += 1
			}
			if strings.EqualFold(d.TaskStatus, common.TaskStatusFailed) || strings.EqualFold(d.TaskStatus, constants.MigrationStatusInvalid) {
				failedChunks += 1
				errInfo = d.ErrorDetail
			}
			if strings.EqualFold(d.TaskStatus, common.TaskStatusRunning) {
				runningChunks += 1
			}
			if strings.EqualFold(d.TaskStatus, common.TaskStatusWaiting) {
				waitingChunks += 1
			}
		}
		if errInfo == "" {
			errInfo = csvStage.ErrorMessage
		}

		var result = "运行中"

		switch t.TaskStatus {
		case common.TaskStatusWaiting:
			result = "未运行"
		case common.TaskStatusRunning:
			if csvStage.ExportToCSV {
				result = "导入中"
			} else {
				result = "导出中"
			}
		case common.TaskStatusSuccess:
			if csvStage.ImportToDB {
				result = "完成"
			} else {
				result = "导出成功，导入中"
			}
		case common.TaskStatusFailed, constants.MigrationStatusInvalid:
			if csvStage.ExportToCSV {
				result = "导入失败"
			} else {
				result = "导出失败"
			}
		}

		sd = append(sd, message.MigrationDataSchemaDetail{
			ID:                t.ID,
			ServiceName:       t.ServiceNameS,
			SchemaNameS:       t.SchemaNameS,
			TableNameS:        t.TableNameS,
			TotalChunks:       t.ChunkTotalNums,
			ChunkSize:         t.ChunkSize,
			SuccessChunks:     successChunks,
			FailedChunks:      failedChunks,
			RunningChunks:     runningChunks,
			WaitingChunks:     waitingChunks,
			MigrationResult:   t.TaskStatus,
			MigrationDuration: time.Duration(t.Duration * float64(time.Second)).String(),
			MigrationMessage:  errInfo,

			IsCSVExportFinished:     csvStage.ExportToCSV,
			IsCSVImportFinished:     csvStage.ImportToDB,
			LightningImportProgress: lightningProgressVal,

			Result: result,
		})
	}

	return &message.MigrationDataSchemaDetailResp{MigrationDataSchemaDetail: sd}, page, nil
}

// 校验多个Summary，判断status可否重试
func (s *CSVMigrationService) ValidateMultiSummaryStatus(ctx context.Context, taskId int, summaryIDs []uint) error {
	_, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		log.Errorf("get task info failed: %v", err)
		return fmt.Errorf("get task info failed: %v", err)
	}
	summaries, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryBySummaryIds(ctx, summaryIDs)
	if err != nil {
		log.Errorf("get migration summary failed, err: %v", err)
		return err
	}

	if len(summaries) == 0 {
		err = errors.New("no migration summary match")
		log.Warnf("no migration summary match, taskId:%d, summaryIDs:%v", taskId, summaryIDs)
		return err
	}

	hasInvalidSummary := false
	hasSuccessSummary := false
	var invalidSummary migration.TableMigrationSummary
	for _, summary := range summaries {
		if summary.TaskStatus == constants.MigrationStatusInvalid {
			hasInvalidSummary = true
			invalidSummary = *summary
			continue
		}
		if summary.TaskStatus == common.TaskStatusSuccess {
			hasSuccessSummary = true
			continue
		}
	}
	if hasInvalidSummary {
		return fmt.Errorf("table [%s.%s] or [%s.%s] may not exist or split chunks failed, please check again",
			invalidSummary.SchemaNameS, invalidSummary.TableNameS,
			invalidSummary.SchemaNameT, invalidSummary.TableNameT,
		)
	}
	if hasSuccessSummary {
		return fmt.Errorf("some table has been migrated successfully, please check again")
	}
	return nil
}

// 批量重试所有失败表的 chunk
func (s *CSVMigrationService) BatchRetryCSVMigrationTaskTable(ctx context.Context, msg *message.MigrationDataBatchRetryReq) error {
	defer UpdateTaskIfFullDataRetryFinishOrFailed(ctx, msg.TaskID)

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		log.Errorf("get task info failed: %v", err)
		return fmt.Errorf("get task info failed: %v", err)
	}
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel info failed: %v", err)
		return fmt.Errorf("get channel info failed: %v", err)
	}
	summaries, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryBySummaryIds(ctx, msg.TableID)
	if err != nil {
		log.Errorf("get csv migration summary failed, err: %v", err)
		return err
	}
	if len(summaries) == 0 {
		err = errors.New("no csv migration summary match")
		log.Warnf("no csv migration summary match, req: %v", msg)
		return err
	}
	csvParam, buildParamErr := migrationpkg.BuildCSVMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build csv migration param failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, buildParamErr)
		return buildParamErr
	}
	dataSources, getErr := migrationpkg.GetDatabaseSources(ctx, channelInfo, taskInfo.TaskID)
	if getErr != nil {
		log.Errorf("get database sources failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, getErr)
		return getErr
	}
	taskTableConfigMap, buildErr := migrationpkg.BuildTaskTableConfigMap(ctx, channelInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		log.Errorf("build task table config map failed, channelId: %d, taskId: %d, err: %v", channelInfo.ChannelId, taskInfo.TaskID, buildErr)
		return buildErr
	}

	helper := migrationpkg.NewCSVMigrationTask(channelInfo, taskInfo, csvParam, dataSources, taskTableConfigMap)
	helper.SetExecuteMode(migrationpkg.ExecuteModeSingle)

	for _, summary := range summaries {
		csvStage, getStageErr := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, taskInfo.TaskID, summary.SchemaNameS, summary.TableNameS)
		if getStageErr != nil {
			log.Errorf("get csv stage failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err: %v", taskInfo.TaskID, summary.SchemaNameS, summary.TableNameS, getStageErr)
			return fmt.Errorf("get csv stage failed: %v", getStageErr)
		}
		retryErr := helper.ProcessRetry(ctx, summary, csvStage)
		if retryErr != nil {
			log.Errorf("%s.%s table, retry csv task in single mode failed, taskId:%d, err: %v", summary.SchemaNameS, summary.TableNameS, taskInfo.TaskID, retryErr)
			return retryErr
		}
	}

	// 根据任务执行产生的元数据，判断当前任务是否执行成功
	updateErr := helper.SetTaskStatusByExecuteResult(ctx)
	if updateErr != nil {
		log.Errorf("update task to failed if contains failed details or summaries failed, taskId:%d, err: %v", taskInfo.TaskID, updateErr)
		return updateErr
	}

	return nil

}

func (s *CSVMigrationService) GetCSVMigrationFailedChunkByTable(ctx context.Context, msg *message.MigrationDataChunkFailedAndWaitingReq) (*message.MigrationDataChunkFailedAndWaitingResp, error) {
	failedDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		TaskStatus:  common.TaskStatusFailed,
	})
	if err != nil {
		return nil, err
	}

	var faileds []message.MigrationDataTableFailedAndWaitingChunkDetail
	for _, f := range failedDetails {
		faileds = append(faileds, message.MigrationDataTableFailedAndWaitingChunkDetail{
			TaskID:      f.TaskID,
			SchemaNameS: f.SchemaNameS,
			TableNameS:  f.TableNameS,
			TaskStatus:  f.TaskStatus,
			ErrorDetail: f.ErrorDetail,
		})
	}
	return &message.MigrationDataChunkFailedAndWaitingResp{MigrationDataFailedAndWaitingChunkDetail: faileds}, nil
}

func (s *CSVMigrationService) GetCSVMigrationFailedChunkErrorDetailByTable(ctx context.Context, msg *message.MigrationDataChunkErrorReq) (*message.MigrationDataChunkErrorResp, error) {
	failedDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		TaskStatus:  common.TaskStatusFailed,
	})
	if err != nil {
		return nil, err
	}

	var faileds []message.MigrationDataTableFailedAndWaitingChunkDetail
	for _, f := range failedDetails {
		faileds = append(faileds, message.MigrationDataTableFailedAndWaitingChunkDetail{
			TaskID:      f.TaskID,
			SchemaNameS: f.SchemaNameS,
			TableNameS:  f.TableNameS,
			ChunkDetail: f.ChunkDetailS,
			TaskStatus:  f.TaskStatus,
			ErrorDetail: f.ErrorDetail,
		})
	}
	return &message.MigrationDataChunkErrorResp{MigrationDataFailedChunkErrorDetail: faileds}, nil
}

// ValidateSingleSummaryStatus 校验单个表是否有问题, status = INVALID
func (s *CSVMigrationService) ValidateSingleSummaryStatus(ctx context.Context, taskId int, schemaNameS, tableNameS string) error {
	return migrationpkg.ValidateRetrySummary(ctx, taskId, schemaNameS, tableNameS)
}

// 重试一张表的所有失败chunk
func (s *CSVMigrationService) RetryCSVMigrationTaskByTable(ctx context.Context, msg *message.MigrationDataFailedChunkBatchRetryReq) error {
	defer UpdateTaskIfFullDataRetryFinishOrFailed(ctx, msg.TaskID)

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		log.Errorf("get task info failed, taskId:%d, err: %v", msg.TaskID, err)
		return fmt.Errorf("get task info failed: %v", err)
	}
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel info failed, channelId:%d, taskId:%d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, err)
		return fmt.Errorf("get channel info failed: %v", err)
	}
	csvStage, err := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, taskInfo.TaskID, msg.SchemaNameS, msg.TableNameS)
	if err != nil {
		log.Errorf("get csv stage failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err: %v", taskInfo.TaskID, msg.SchemaNameS, msg.TableNameS, err)
		return fmt.Errorf("get csv stage failed: %v", err)
	}
	summary, getErr := migrationpkg.GetTableMigrationSummary(ctx, msg.TaskID, msg.SchemaNameS, msg.TableNameS)
	if getErr != nil {
		log.Errorf("get csv migration summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err: %v", msg.TaskID, msg.SchemaNameS, msg.TableNameS, getErr)
		return getErr
	}

	csvParam, buildParamErr := migrationpkg.BuildCSVMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build csv migration param failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, buildParamErr)
		return buildParamErr
	}

	dataSources, getErr := migrationpkg.GetDatabaseSources(ctx, channelInfo, taskInfo.TaskID)
	if getErr != nil {
		log.Errorf("get database sources failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, getErr)
		return getErr
	}

	taskTableConfigMap, buildErr := migrationpkg.BuildTaskTableConfigMap(ctx, channelInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		log.Errorf("build task table config map failed, channelId: %d, taskId: %d, err: %v", channelInfo.ChannelId, taskInfo.TaskID, buildErr)
		return buildErr
	}

	helper := migrationpkg.NewCSVMigrationTask(channelInfo, taskInfo, csvParam, dataSources, taskTableConfigMap)
	helper.SetExecuteMode(migrationpkg.ExecuteModeSingle)
	retryErr := helper.ProcessRetry(ctx, summary, csvStage)
	if retryErr != nil {
		log.Errorf("%s.%s table, retry csv task in single mode failed, taskId:%d, err: %v", msg.SchemaNameS, msg.TableNameS, taskInfo.TaskID, retryErr)
		return retryErr
	}
	return nil
}

func (s *CSVMigrationService) GetCSVMigrationProgressBarByTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationDataProgressResp, error) {
	var (
		totalNums      int
		successNums    int
		failedNums     int
		runningNums    int
		chunkNums      int
		successChunks  int
		failedChunks   int
		runningChunks  int
		lastUpdateTime time.Time
	)

	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		summaryInterval, err := models.GetFullDataMigrationReaderWriter().GetTableMigrationSummaryByInterval(transactionCtx, &migration.TableMigrationSummary{
			TaskID: msg.TaskID,
			Entity: &commonmodel.Entity{
				CreatedAt: msg.ProgressStartTime,
				UpdatedAt: msg.ProgressStartTime,
			},
		})
		if err != nil {
			return err
		}

		detailInterval, err := models.GetFullDataMigrationReaderWriter().GetTableMigrationDetailByInterval(transactionCtx, &migration.TableMigrationDetail{
			TaskID: msg.TaskID,
			Entity: &commonmodel.Entity{
				CreatedAt: msg.ProgressStartTime,
				UpdatedAt: msg.ProgressStartTime,
			},
		})
		if err != nil {
			return err
		}
		lastUpdateTime = msg.ProgressStartTime
		totalNums = len(summaryInterval)
		for _, si := range summaryInterval {
			if strings.EqualFold(si.TaskStatus, common.TaskStatusSuccess) {
				successNums += 1
			}
			if strings.EqualFold(si.TaskStatus, common.TaskStatusFailed) || strings.EqualFold(si.TaskStatus, constants.MigrationStatusInvalid) {
				failedNums += 1
			}
			if strings.EqualFold(si.TaskStatus, common.TaskStatusRunning) {
				runningNums += 1
			}
			if si.UpdatedAt.After(lastUpdateTime) {
				lastUpdateTime = si.UpdatedAt
			}
		}

		chunkNums = len(detailInterval)
		for _, di := range detailInterval {
			if strings.EqualFold(di.TaskStatus, common.TaskStatusSuccess) {
				successChunks += 1
			}
			if strings.EqualFold(di.TaskStatus, common.TaskStatusFailed) || strings.EqualFold(di.TaskStatus, constants.MigrationStatusInvalid) {
				failedChunks += 1
			}
			if strings.EqualFold(di.TaskStatus, common.TaskStatusRunning) {
				runningChunks += 1
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &message.MigrationDataProgressResp{
		TaskID:               msg.TaskID,
		TotalMigrationNums:   totalNums,
		RunningMigrationNums: runningNums,
		SuccessMigrationNums: successNums,
		FailedMigrationNums:  failedNums,
		TotalChunkNums:       chunkNums,
		SuccessChunkNums:     successChunks,
		FailedChunkNums:      failedChunks,
		RunningChunkNums:     runningChunks,
		ProgressStartTime:    msg.ProgressStartTime,
		LastUpdateTime:       lastUpdateTime,
	}, nil
}

func (s *CSVMigrationService) GetCSVMigrationProgressLogByTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationDataProgressResp, error) {
	progressLogIntervals, err := models.GetProgressLogReaderWriter().GetProcessLogDetailByInterval(ctx, &commonmodel.ProgressLogDetail{
		TaskID: msg.TaskID,
		Entity: &commonmodel.Entity{
			CreatedAt: msg.ProgressStartTime,
			UpdatedAt: msg.ProgressStartTime,
		},
	})
	if err != nil {
		return nil, err
	}

	var progressLogs []*message.MigrationLogDetail
	for _, p := range progressLogIntervals {
		progressLogs = append(progressLogs, buildMigrationStructureLogDetailMessageFromModel(p))
	}

	return &message.MigrationDataProgressResp{
		TaskID:            msg.TaskID,
		ProgressLog:       progressLogs,
		ProgressStartTime: msg.ProgressStartTime,
	}, err
}

func (s *CSVMigrationService) GetCSVMigrationProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	MigReq := &message.MigrationProgressReq{
		ChannelID:         req.ChannelId,
		TaskID:            req.TaskId,
		ProgressStartTime: req.StartTime,
	}

	var BarData *message.MigrationDataProgressResp
	var taskInfo *task.Task
	var err error
	taskInfo, err = models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get task info failed, err: %v", err))
		return nil, err
	}

	isParentTask := taskInfo.ParentTaskID == 0 && tmsconfig.GetGlobalConfig().IsClusterMode()
	if isParentTask {
		BarData, err = commonservice.GetFullDataMigrationProgressBarByParentTask(ctx, MigReq)
	} else {
		BarData, err = s.GetCSVMigrationProgressBarByTask(ctx, MigReq)
	}
	if err != nil {
		log.Errorf(fmt.Sprintf("get migration structure BarData data failed, err: %v", err))
		return nil, err
	}

	var progress float64

	if isParentTask {
		if BarData.TotalTaskNums != 0 {
			progress = float64(BarData.SuccessTaskNums) / float64(BarData.TotalTaskNums)
		}
	} else {
		if tmsconfig.GetGlobalConfig().IsClusterMode() {
			lightningProgress, err := models.GetFullDataMigrationReaderWriter().GetLatestLightningProgressByTaskId(ctx, req.TaskId)
			if err != nil && !strings.Contains(err.Error(), "record not found") {
				log.Errorf(fmt.Sprintf("get migration progress data failed, taskId:%d, err: %v", req.TaskId, err))
				return nil, err
			}
			var lightningProgressTotal float64
			if lightningProgress == nil {
				lightningProgressTotal = 0
			} else {
				lightningProgressTotal = parseTotalStrToFloat(lightningProgress.Total)
			}
			var csvExportProgressTotal float64
			if BarData.TotalChunkNums != 0 {
				csvExportProgressTotal = float64(BarData.SuccessChunkNums) / float64(BarData.TotalChunkNums)
			}
			progress = (lightningProgressTotal + csvExportProgressTotal) / 2.0
		} else {
			lightningProgresses, err := models.GetFullDataMigrationReaderWriter().GetSchemaSTableSLatestLightningProgressByTaskId(ctx, req.TaskId)
			if err != nil {
				log.Errorf(fmt.Sprintf("GetSchemaSTableSLatestLightningProgressByTaskId failed, taskId:%d, err: %v", req.TaskId, err))
				return nil, err
			}
			log.Debugf("lightningProgresses: %v", lightningProgresses)
			chunks, err := models.GetFullDataMigrationReaderWriter().GetTableMigrationDetailsByTaskId(ctx, req.TaskId)
			if err != nil {
				log.Errorf(fmt.Sprintf("GetTableMigrationDetailsByTaskId failed, taskId:%d, err: %v", req.TaskId, err))
				return nil, err
			}

			tables := make([]structs.SchemaTablePair, 0)
			tableChunkNums := make(map[structs.SchemaTablePair]int)
			tableChunkSuccessNums := make(map[structs.SchemaTablePair]int)
			tableLightningProgresses := make(map[structs.SchemaTablePair]migration.LightningProgress)
			for _, chunk := range chunks {
				tables = append(tables, structs.SchemaTablePair{
					SchemaName: chunk.SchemaNameS,
					TableName:  chunk.TableNameS,
				})
				tableChunkNums[structs.SchemaTablePair{
					SchemaName: chunk.SchemaNameS,
					TableName:  chunk.TableNameS,
				}]++
				if strings.EqualFold(chunk.TaskStatus, common.TaskStatusSuccess) {
					tableChunkSuccessNums[structs.SchemaTablePair{
						SchemaName: chunk.SchemaNameS,
						TableName:  chunk.TableNameS,
					}]++
				}
			}
			for _, lightningProgress := range lightningProgresses {
				tableLightningProgresses[structs.SchemaTablePair{
					SchemaName: lightningProgress.SchemaNameS,
					TableName:  lightningProgress.TableNameS,
				}] = lightningProgress
			}

			progress = 0
			var totalChunkNum int
			for _, table := range lo.Uniq(tables) {
				var lightningProgress float64
				tableChunkNum := tableChunkNums[table]
				totalChunkNum += tableChunkNum
				tableSuccessChunkNum := tableChunkSuccessNums[table]
				tableLightningProgressObj, ok := tableLightningProgresses[table]
				if ok {
					lightningProgress = parseTotalStrToFloat(tableLightningProgressObj.Total)
				} else {
					lightningProgress = 0
				}
				tmpProgress := (lightningProgress + float64(tableSuccessChunkNum)/float64(tableChunkNum)) / 2.0
				weightedProgress := tmpProgress * float64(tableChunkNum)
				progress += weightedProgress
				log.Debugf("table: %v, tableChunkNum: %v, successChunkNum: %v, tableLightningProgress: %v, tmpProgress: %v, weightedProgress: %v, progress: %v", table, tableChunkNum, tableSuccessChunkNum, lightningProgress, tmpProgress, weightedProgress, progress)
			}
			if totalChunkNum != 0 {
				progress = progress / float64(totalChunkNum)
			}
		}
	}

	csvDir, sortedKvDir, err := migrationpkg.GetCSVTaskDirParamValues(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if err != nil {
		log.Errorf("get csv dir failed, taskId:%d, err:%v", req.TaskId, err)
		return nil, err
	}

	FullDataMigrationProgress := &message.GetTaskProgressResp{
		TaskId:           req.TaskId,
		StartTime:        req.StartTime,
		TotalNums:        BarData.TotalMigrationNums,
		SuccessNums:      BarData.SuccessMigrationNums,
		RunningNums:      BarData.RunningMigrationNums,
		FailedNums:       BarData.FailedMigrationNums,
		LastUpdateTime:   BarData.LastUpdateTime,
		TotalChunkNums:   BarData.TotalChunkNums,
		SuccessChunkNums: BarData.SuccessChunkNums,
		RunningChunkNums: BarData.RunningChunkNums,
		TaskLogFile:      tmsconfig.BuildMigrationCSVLogFilePath(req.ChannelId, req.TaskId),
		Progress:         stringUtil.Decimal(progress),
		OtherFiles: map[string]string{
			"lightning_log": tmsconfig.BuildLightningLogFilePath(req.ChannelId, req.TaskId),
			"csv_dir":       path.Join(csvDir, fmt.Sprintf("export_data-%d-%d", req.ChannelId, req.TaskId)),
			"sorted_kv_dir": path.Join(sortedKvDir, fmt.Sprintf("export_data-%d-%d", req.ChannelId, req.TaskId)),
		},
	}
	LogData, err := s.GetCSVMigrationProgressLogByTask(ctx, MigReq)
	if err != nil {
		log.Errorf(fmt.Sprintf("get migration structure LogData data failed, err: %v", err))
		return FullDataMigrationProgress, err
	}

	var progressLog []*message.TaskProgressLogDetail
	logStartTime := req.StartTime
	for _, v := range LogData.ProgressLog {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.CreatedAt,
			LogMessage: v.Detail,
			LogLevel:   v.LogLevel,
		})
		if v.CreatedAt.Before(logStartTime) {
			logStartTime = v.CreatedAt
		}
	}
	FullDataMigrationProgress.ProgressLogInfo = &message.TaskProgressLogInfo{
		TaskId:        req.TaskId,
		LogStartTime:  logStartTime,
		LogCount:      len(progressLog),
		LogDetailList: progressLog,
	}

	return FullDataMigrationProgress, nil
}

func (s *CSVMigrationService) GetCSVMigrationSummaryByTask2(ctx context.Context, msg *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	log.Infof("start GetFullDataMigrationSummaryByTask2.")
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskId)
	if err != nil {
		return nil, err
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, err
	}

	sumData, err := s.GetCSVMigrationSummaryByTask(ctx, &message.MigrationTaskReq{TaskID: msg.TaskId})
	if err != nil {
		log.Errorf(fmt.Sprintf("get GetCSVMigrationSummaryByTask failed, err: %v", err))
		return nil, err
	}
	var chartData []message.TaskDetailChartData
	chartData = append(chartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "SUCCESS",
		Count: sumData.SuccessMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.SuccessMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})
	chartData = append(chartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "FAILED",
		Count: sumData.FailedMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.FailedMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})
	chartData = append(chartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "RUNNING",
		Count: sumData.RunningMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.RunningMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})
	chartData = append(chartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "WAITING",
		Count: sumData.WaitingMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.WaitingMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})

	fullBySchema := message.TaskDetailSchemaData{
		FullDataMigration: sumData.MigrationDetails,
	}

	if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		taskInfo.EndTime = time.Now()
	}
	return &message.GetTaskDetailResultResp{
		TaskId:                  msg.TaskId,
		StartTime:               taskInfo.StartTime,
		DBName:                  detailChannelInfo.DatasourceNameS,
		TotalTables:             sumData.TotalMigrationNums,
		TotalDuration:           taskInfo.EndTime.Sub(taskInfo.StartTime).String(),
		TaskDetailChartDataList: chartData,
		TaskDetailSchemaData:    fullBySchema,
	}, nil
}

func UpdateTaskIfCSVRetryFinish(ctx context.Context, taskId int) {
	sumData, sumErr := NewCSVMigrationService().GetCSVMigrationSummaryByTask(ctx, &message.MigrationTaskReq{
		TaskID: taskId,
	})
	if sumErr != nil {
		log.Errorf("GetFullDataMigrationSummaryByTask failed, task id [%v] , task status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, sumErr)
	}
	//如果总数和成功数相等，把task状态修改为完成
	if sumData.TotalMigrationNums == sumData.SuccessMigrationNums {
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
			EndTime:    time.Now(),
			TaskStatus: constants.TASK_STATUS_FINISH,
		}); err != nil {
			log.Errorf("task exec success, update task id [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, err)
		}
	}

}

func parseTotalStrToFloat(str string) float64 {
	if str == "" {
		return 0
	}
	// 去除百分号
	str = str[:len(str)-1]

	// 将字符串转换为浮点数
	f, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0
	}

	// 将百分比转换为小数
	f = f / 100
	return f
}

func (s *CSVMigrationService) WalkCSVDir(ctx context.Context, msg *message.WalkCSVDirReq) (*message.WalkCSVDirResp, *message.Page, error) {
	walkPaths := uniqueSortStringSlice(msg.WalkPaths)

	log.Infof("start WalkCSVDir, walkPaths:%v", walkPaths)

	csvFiles, walkErr := file.WalkForCSVFiles(walkPaths)
	if walkErr != nil {
		log.Errorf("walk csv dir failed, walkPaths:%v, err:%v", walkPaths, walkErr)
		return nil, nil, tmserrors.NewErrorf(tmserrors.TMS_MIGRATION_CSV_WALK_DIR_FAILED, walkErr.Error())
	}
	csvFiles = lo.Filter(csvFiles, func(fi message.CSVFileInfo, _ int) bool {
		if msg.SchemaName != "" && fi.TableProperty.GetSchemaNameT() != msg.SchemaName {
			return false
		}
		if msg.TableName != "" && fi.TableProperty.GetTableNameT() != msg.TableName {
			return false
		}
		return true
	})

	validCsvFiles := lo.Filter(csvFiles, func(fi message.CSVFileInfo, _ int) bool {
		if !fi.IsValid {
			return false
		}
		if msg.SchemaName != "" && fi.TableProperty.GetSchemaNameT() != msg.SchemaName {
			return false
		}
		if msg.TableName != "" && fi.TableProperty.GetTableNameT() != msg.TableName {
			return false
		}
		return true
	})

	helper := migrationpkg.NewChunkMetaHelper(walkPaths, len(csvFiles))

	validCsvTableProperties := helper.ExtractValidTableProperties(validCsvFiles)

	limit := uint(msg.PageSize)
	offset := (msg.Page - 1) * msg.PageSize

	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    int64(len(csvFiles)),
	}
	retCSVFiles := lo.Subset(csvFiles, offset, limit)
	for idx := range retCSVFiles {
		retCSVFiles[idx].TableProperty = nil
	}

	return &message.WalkCSVDirResp{
		WalkPaths:       walkPaths,
		CSVFiles:        retCSVFiles,
		Tables:          validCsvTableProperties,
		TotalCSVFileNum: int64(len(csvFiles)),
		ValidCSVFileNum: int64(len(validCsvFiles)),
		ErrorCSVFileNum: int64(len(csvFiles) - len(validCsvFiles)),
		TotalTableNum:   int64(len(validCsvTableProperties)),
	}, page, nil

}

func uniqueSortStringSlice(walkPaths []string) []string {
	ret := make([]string, 0)
	for _, walkPath := range walkPaths {
		wp := strings.TrimSpace(walkPath)
		if wp == "" {
			continue
		}
		ret = append(ret, wp)
	}
	ret = lo.Uniq(ret)
	sort.Strings(ret)
	return ret
}

func (s *CSVMigrationService) MigrationDataStatistic(ctx context.Context, msg *message.MigrationDataStatisticReq) (*message.MigrationDataStatisticResp, *message.Page, error) {
	_, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, int(msg.TaskId))
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", msg.TaskId, getTaskErr)
		return nil, nil, getTaskErr
	}
	summaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryByTaskIds(ctx, []uint{msg.TaskId})
	if getSummaryErr != nil {
		log.Errorf("get table migration summary failed, taskId:%d, err:%v", msg.TaskId, getSummaryErr)
		return nil, nil, getSummaryErr
	}

	data := lo.Map(summaries, func(summary *migration.TableMigrationSummary, _ int) *message.SchemaTableWithCSV {
		return &message.SchemaTableWithCSV{
			SchemaNameT: summary.SchemaNameT,
			TableNameT:  summary.TableNameT,
			CSVFileNum:  summary.ChunkTotalNums,
		}
	})

	data = lo.Subset(data, (msg.Page-1)*msg.PageSize, uint(msg.PageSize))
	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    int64(len(summaries)),
	}

	return &message.MigrationDataStatisticResp{Data: data}, page, nil
}

func (s *CSVMigrationService) QueryMigrationDetails(ctx context.Context, msg *message.QueryMigrationDetailsReq) (*message.QueryMigrationDetailsResp, *message.Page, error) {
	_, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, int(msg.TaskId))
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", msg.TaskId, getTaskErr)
		return nil, nil, getTaskErr
	}

	details, getDetailErr := models.GetFullDataMigrationReaderWriter().GetTableMigrationDetailsByCSVCondition(ctx, int(msg.TaskId), msg.SchemaName, msg.TableName, msg.PathFilter)
	if getDetailErr != nil {
		log.Errorf("get table migration details failed, taskId:%d, err:%v", msg.TaskId, getDetailErr)
		return nil, nil, getDetailErr
	}

	limit := uint(msg.PageSize)
	offset := (msg.Page - 1) * msg.PageSize

	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    int64(len(details)),
	}
	retDetails := lo.Subset(details, offset, limit)

	retDetailMessages := lo.Map(retDetails, func(detail *migration.TableMigrationDetail, _ int) *message.CSVTableMigrationDetail {
		return &message.CSVTableMigrationDetail{
			ID:           detail.ID,
			TaskID:       detail.TaskID,
			SchemaNameS:  detail.SchemaNameS,
			TableNameS:   detail.TableNameS,
			SchemaNameT:  detail.SchemaNameT,
			TableNameT:   detail.TableNameT,
			TaskStatus:   detail.TaskStatus,
			ImportStatus: detail.ImportStatus,
			ImportError:  detail.ImportError,
			FilePath:     detail.CSVFile,
			FileName:     path.Base(detail.CSVFile),
			FileSize:     detail.CSVSize,
			CreatedAt:    detail.Entity.CreatedAt,
		}
	})

	return &message.QueryMigrationDetailsResp{Data: retDetailMessages}, page, nil

}

func (s *CSVMigrationService) UpdateMigrationDetail(ctx context.Context, msg *message.UpdateMigrationDetailReq) (*message.UpdateMigrationDetailResp, error) {
	_, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskId)
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", msg.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailImportStatusByIDsV2(ctx, msg.TaskId, msg.ChunkIds, msg.ImportStatus)
	if updateErr != nil {
		log.Errorf("update table migration detail failed, taskId:%d, err:%v", msg.TaskId, updateErr)
		return nil, updateErr
	}
	return &message.UpdateMigrationDetailResp{}, nil
}

func (s *CSVMigrationService) SaveMigrationData(ctx context.Context, msg *message.SaveMigrationDataReq) (*message.SaveMigrationDataResp, error) {
	walkPaths := uniqueSortStringSlice(msg.WalkPaths)

	log.Infof("SaveMigrationData, taskId:%d, walkPath:%s, totalCSVFileNum:%d, resetChannelSchemaTable:%v, resetMigrationMeta:%v",
		msg.TaskID, walkPaths, msg.TotalCSVFileNum, msg.ResetChannelSchemaTable, msg.ResetMigrationMeta)

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", msg.TaskID, getTaskErr)
		return nil, getTaskErr
	}

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, taskId:%d, err:%v", msg.TaskID, getChannelErr)
		return nil, getChannelErr
	}

	dbSources, getSourceErr := migrationpkg.GetDatabaseSources(ctx, channelInfo, taskInfo.TaskID)
	if getSourceErr != nil {
		log.Errorf("get database source failed, taskId:%d, err:%v", msg.TaskID, getSourceErr)
		return nil, getSourceErr
	}

	helper := migrationpkg.NewChunkMetaHelper(walkPaths, msg.TotalCSVFileNum)

	csvFiles, walkErr := helper.WalkForValidCSVFile()
	if walkErr != nil {
		log.Errorf("walk csv dir failed, taskId:%d, walkPath:%v, err:%v", msg.TaskID, walkPaths, walkErr)
		return nil, tmserrors.NewErrorf(tmserrors.TMS_MIGRATION_CSV_WALK_DIR_FAILED, walkErr.Error())
	}

	csvTableProperties := helper.ExtractValidTableProperties(csvFiles)

	// 如果开启了保存channel schema table，则清空并保存channel schema table
	// 否则，判断通过CSV文件提取的表信息是否包含在channel schema table中
	var (
		savedChannelSchemaNum      int
		savedChannelSchemaTableNum int
	)
	newChannelSchemas := helper.BuildChannelSchemas(taskInfo, csvTableProperties, dbSources)
	newChannelSchemaTables := helper.BuildChannelSchemaTables(taskInfo, csvTableProperties)

	log.Infof("SaveMigrationData, reset or validate channelSchemaTable, taskId:%d, resetChannelSchemaTable:%v, newChannelSchemaTableNum:%d", msg.TaskID, msg.ResetChannelSchemaTable, len(newChannelSchemaTables))
	if msg.ResetChannelSchemaTable {
		// 开事务进行元数据操作, 如果参数不变，再次请求后的结果是幂等的
		trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
			deleteSErr := models.GetChannelReaderWriter().DeleteChannelSchemasByChannelId(transactionCtx, taskInfo.ChannelId)
			if deleteSErr != nil {
				log.Errorf("delete all channel schemas failed, taskId:%d, err:%v", msg.TaskID, deleteSErr)
				return deleteSErr
			}
			deleteTErr := models.GetChannelReaderWriter().PhysicalDeleteAllChannelSchemaTables(transactionCtx, taskInfo.ChannelId, taskInfo.TaskID)
			if deleteTErr != nil {
				log.Errorf("delete all channel schema tables failed, taskId:%d, err:%v", msg.TaskID, deleteTErr)
				return deleteTErr
			}
			_, saveSErr := models.GetChannelReaderWriter().CreateChannelSchemas(transactionCtx, newChannelSchemas)
			if saveSErr != nil {
				log.Errorf("save channel schemas failed, taskId:%d, err:%v", msg.TaskID, saveSErr)
				return saveSErr
			}
			if len(newChannelSchemaTables) == 0 {
				_, saveTErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(transactionCtx, newChannelSchemaTables)
				if saveTErr != nil {
					log.Errorf("save channel schema tables failed, taskId:%d, err:%v", msg.TaskID, saveTErr)
					return saveTErr
				}
			}
			return nil
		})
		if trxErr != nil {
			log.Errorf("transaction failed, taskId:%d, err:%v", msg.TaskID, trxErr)
			return nil, trxErr
		}
		savedChannelSchemaNum = len(newChannelSchemas)
		savedChannelSchemaTableNum = len(newChannelSchemaTables)
	} else {
		oldChannelSchemaTables, getErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
		if getErr != nil {
			log.Errorf("get channel schema tables by task id failed, taskId:%d, err:%v", msg.TaskID, getErr)
			return nil, getErr
		}
		checkErr := helper.CheckChannelSchemaTables(ctx, oldChannelSchemaTables, newChannelSchemaTables)
		if checkErr != nil {
			log.Errorf("check channel schema tables failed, taskId:%d, err:%v", msg.TaskID, checkErr)
			return nil, checkErr
		}
		savedChannelSchemaNum = 0
		savedChannelSchemaTableNum = 0
	}

	const BATCH_INSERT_SIZE = 60
	details := helper.BuildMigrationDetails(taskInfo, csvFiles)
	summaries := helper.BuildMigrationSummaries(taskInfo, csvTableProperties)
	csvStages := helper.BuildCSVStages(taskInfo, csvTableProperties)

	log.Infof("SaveMigrationData, reset and save migration data, taskId:%d, resetMigrationMeta:%v, detailNum:%d, summaryNum:%d, csvStageNum:%d",
		msg.TaskID, msg.ResetMigrationMeta, len(details), len(summaries), len(csvStages))
	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskInfo.RunParams = strings.Join(walkPaths, ",")
		updateTaskErr := models.GetTaskReaderWriter().UpdateTaskStatusAndRunParam(ctx, taskInfo.TaskID, constants.TASK_STATUS_NOT_RUNNING, taskInfo.RunParams)
		if updateTaskErr != nil {
			log.Errorf("update task run param failed, taskId:%d, err:%v", msg.TaskID, updateTaskErr)
			return updateTaskErr
		}

		deleteLogErr := models.GetProgressLogReaderWriter().DeleteProgressLogDetailByChannelIdTaskIds(transactionCtx, taskInfo.ChannelId, []int{taskInfo.TaskID})
		if deleteLogErr != nil {
			log.Errorf("delete progress log failed, taskId:%d, err:%v", msg.TaskID, deleteLogErr)
			return deleteLogErr
		}

		deleteProgressErr := models.GetFullDataMigrationReaderWriter().DeleteLightningProgressByTaskId(transactionCtx, taskInfo.TaskID)
		if deleteProgressErr != nil {
			log.Errorf("delete lightning progress failed, taskId:%d, err:%v", msg.TaskID, deleteProgressErr)
			return deleteProgressErr
		}

		if msg.ResetMigrationMeta {
			deleteStageErr := models.GetFullDataMigrationReaderWriter().BatchDeleteCSVStageByTaskId(transactionCtx, taskInfo.ChannelId, taskInfo.TaskID)
			if deleteStageErr != nil {
				log.Errorf("delete csv stages failed, taskId:%d, err:%v", msg.TaskID, deleteStageErr)
				return deleteStageErr
			}
			deleteDetailErr := models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetailByTaskIds(transactionCtx, []int{taskInfo.TaskID})
			if deleteDetailErr != nil {
				log.Errorf("delete table migration details failed, taskId:%d, err:%v", msg.TaskID, deleteDetailErr)
				return deleteDetailErr
			}
			deleteSummaryErr := models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummaryByTaskIds(transactionCtx, []int{taskInfo.TaskID})
			if deleteSummaryErr != nil {
				log.Errorf("delete table migration summaries failed, taskId:%d, err:%v", msg.TaskID, deleteSummaryErr)
				return deleteSummaryErr
			}
		} else {
			validateErr := helper.ValidateTaskMigrationMetadata(transactionCtx, taskInfo)
			if validateErr != nil {
				log.Errorf("validate task migration metadata failed, taskId:%d, resetMigrationMeta:%v, err:%v", msg.TaskID, msg.ResetMigrationMeta, validateErr)
				return validateErr
			}
		}

		saveStageErr := models.GetFullDataMigrationReaderWriter().BatchSaveCSVStage(transactionCtx, csvStages)
		if saveStageErr != nil {
			log.Errorf("save csv stages failed, taskId:%d, err:%v", msg.TaskID, saveStageErr)
			return saveStageErr
		}
		_, saveDetailErr := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(transactionCtx, details, BATCH_INSERT_SIZE)
		if saveDetailErr != nil {
			log.Errorf("save table migration details failed, taskId:%d, err:%v", msg.TaskID, saveDetailErr)
			return saveDetailErr
		}
		saveSummaryErr := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationSummary(transactionCtx, summaries, BATCH_INSERT_SIZE)
		if saveSummaryErr != nil {
			log.Errorf("save table migration summaries failed, taskId:%d, err:%v", msg.TaskID, saveSummaryErr)
			return saveSummaryErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("transaction failed, taskId:%d, err:%v", msg.TaskID, trxErr)
		return nil, trxErr
	}
	return &message.SaveMigrationDataResp{
		SavedChannelSchemaNum:      savedChannelSchemaNum,
		SavedChannelSchemaTableNum: savedChannelSchemaTableNum,
		SavedMigrationDetailNum:    len(details),
		SavedMigrationSummaryNum:   len(summaries),
		SavedTableNum:              len(newChannelSchemaTables),
	}, nil
}
