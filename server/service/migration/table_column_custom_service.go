package migration

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transferdbCommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/o2t"
	channelpkg "gitee.com/pingcap_enterprise/tms/pkg/channel"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	migrationpkg "gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/pkg/structure"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

func BuildO2tTablesRuleMaps(ctx context.Context, taskInfo *task.Task, channelInfo *channel.ChannelInformation, sourceSchema string, targetSchema string, schemaTables []*channel.ChannelSchemaTable) (map[string]string, map[string]map[string]string, map[string]map[string]bool, map[string]map[string]string, error) {
	tableColRules, err := models.GetTemplateReaderWriter().GetTemplateRuleID(ctx, taskInfo.TabcolmapTmplateId)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task column template rule id failed: %v", err)
	}

	colDefaultRules, err := models.GetTemplateReaderWriter().GetTemplateRuleID(ctx, taskInfo.ColdefaultmapTmplateId)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task column default template rule id failed: %v", err)
	}

	var (
		tableColRuleIds   []string
		colDefaultRuleIds []string
	)

	for _, r := range tableColRules {
		tableColRuleIds = append(tableColRuleIds, strconv.Itoa(r.MapRuleId))
	}

	for _, r := range colDefaultRules {
		colDefaultRuleIds = append(colDefaultRuleIds, strconv.Itoa(r.MapRuleId))
	}

	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}
	targetDS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task target datasource info failed: %v", err)
	}

	sourceDB, err := GetSourceDB(ctx, sourceDS)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	oracleDBVersion, err := sourceDB.GetOracleDBVersion()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get oracledb version failed: %v", err)
	}

	oracleCollation := false
	if transferdbCommon.VersionOrdinal(oracleDBVersion) >= transferdbCommon.VersionOrdinal(transferdbCommon.OracleTableColumnCollationDBVersion) {
		oracleCollation = true
	}

	// 获取上下游列类型和默认值映射关系
	tableNameRuleMap, tableColumnRuleMap, tableDefaultValueSourceMap, tableDefaultRuleMap, err := o2t.IChanger(&Change{
		Ctx:               ctx,
		ChannelId:         taskInfo.ChannelId,
		TaskId:            taskInfo.TaskID,
		SourceSchemaName:  sourceSchema,
		TargetSchemaName:  targetSchema,
		SourceTables:      schemaTables,
		TableColRuleIds:   tableColRuleIds,
		ColDefaultRuleIds: colDefaultRuleIds,
		OracleCollation:   oracleCollation,
		Oracle:            sourceDB,
		SourceDBCharset:   sourceDS.Charset,
		TargetDBCharset:   targetDS.Charset,
		TableThreads:      structs.MigrationTableParallelDefault,
	})
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task rule maps failed: %v", err)
	}
	return tableNameRuleMap, tableColumnRuleMap, tableDefaultValueSourceMap, tableDefaultRuleMap, nil
}

func UpdateSummary(transactionCtx context.Context, summary *channel.TableColumnCustomSummary) error {
	// if status != "" {
	// 	summary.Status = status
	// }
	summary, saveSummaryErr := models.GetChannelReaderWriter().SaveTableColumnCustomSummary(transactionCtx, summary)
	if saveSummaryErr != nil {
		log.Errorf("save table columns summary failed. ChannelId:%d, summary:%v, err:%v", summary.ChannelId, summary, saveSummaryErr)
		return saveSummaryErr
	}
	return saveSummaryErr
}

func SyncSourceTableColumns(ctx context.Context, taskInfo *task.Task, channelInfo *channel.ChannelInformation) error {
	allTables, getTableNumErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelId(ctx, channelInfo.ChannelId)
	if getTableNumErr != nil {
		log.Errorf("get channel schema tables failed. channelId:%d, err:%v", channelInfo.ChannelId, getTableNumErr)
		return getTableNumErr
	}

	uniqueSchemas := make(map[string]bool)
	uniqueSchemaTables := make(map[structs.SchemaTablePair]bool)
	for _, table := range allTables {
		schemaTablePair := structs.SchemaTablePair{SchemaName: table.SchemaNameS, TableName: table.TableNameS}
		if _, ok := uniqueSchemaTables[schemaTablePair]; ok {
			continue
		}
		uniqueSchemaTables[schemaTablePair] = true
		uniqueSchemas[table.SchemaNameS] = true
	}
	totalTableNum := int64(len(uniqueSchemaTables))
	log.Debugf("SyncSourceTableColumns service, total table num:%d, tables:%v", totalTableNum, uniqueSchemaTables)

	structureParam, buildParamErr := structure.BuildStructureMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build structure migration param failed. taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, channelInfo.ChannelId, buildParamErr)
		return buildParamErr
	}

	sqlBuilder := channelpkg.DefaultSourceTableColumnSQLBuilder()
	sqlBuilder.SetSchemaNames(lo.Keys(uniqueSchemas))
	sqlBuilder.SetSQLHint(structureParam.GetSyncMetaSQLHint())
	sqlBuilder.SetPunctReplacement(structureParam.GetPunctReplacement())
	querySQL := sqlBuilder.QuerySQL()
	log.Infof("GetSourceTableColumns service, fetching columns, taskId:%d, channelId:%d,querySQL:%s",
		taskInfo.TaskID, taskInfo.ChannelId, stringutil.RemoveSpecialLetterForLog(querySQL))

	eg := errgroup.Group{}

	ruleChan := make(chan *channel.TableColumnCustomMapRule, 1000)
	log.Infof("SyncSourceTableColumns service, set up oracle database conn, taskId:%d", taskInfo.TaskID)
	oracleConn, connErr := migrationpkg.SetUpOracleDatabaseConns(ctx, channelInfo, taskInfo.TaskID)
	if connErr != nil {
		log.Errorf("set up oracle database conn failed, taskId: %d, channelId: %d, err: %v", taskInfo.TaskID, channelInfo.ChannelId, connErr)
		return connErr
	}
	oracleStore := commonpkg.NewOracleStore(oracleConn.GetSourceDB())

	eg.Go(func() error {
		getColumnErr := oracleStore.TableColumnsChanWithSQL(ctx, querySQL, ruleChan)
		if getColumnErr != nil {
			log.Errorf("get source table columns failed. channelId:%d, err:%v", channelInfo.ChannelId, getColumnErr)
		}
		return getColumnErr
	})

	log.Infof("GetSourceTableColumns service, prepare to save columns into metadb, channelId:%d, taskId:%d, table nums:%d ",
		channelInfo.ChannelId, taskInfo.TaskID, totalTableNum)

	var saveSummaryErr error
	summary := &channel.TableColumnCustomSummary{
		ChannelId:         channelInfo.ChannelId,
		Status:            constants.ColumnDataStatusFetching,
		TotalTableNum:     totalTableNum,
		FinishedTableNum:  0,
		FinishedColumnNum: 0,
	}

	var savedNum int64
	finishedTableCounter := make(map[structs.SchemaTablePair]bool)

	saveInTransaction := func(dataChunk []*channel.TableColumnCustomMapRule) error {
		trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
			log.Infof("GetSourceTableColumns service, saving columns to metadb, taskId:%d, total columns:%d, saved columns:%d, current size:%d", taskInfo.TaskID, totalTableNum, savedNum, len(dataChunk))
			summary.FinishedTableNum = int64(len(finishedTableCounter))
			summary.FinishedColumnNum += int64(len(dataChunk))
			savedNum += int64(len(dataChunk))

			if saveSummaryErr = UpdateSummary(transactionCtx, summary); saveSummaryErr != nil {
				return saveSummaryErr
			}

			_, _, saveErr := models.GetChannelReaderWriter().SaveTableColumnCustomMapRule(transactionCtx, dataChunk)
			if saveErr != nil {
				log.Errorf("save table columns failed. taskId:%d, err:%v", taskInfo.TaskID, saveErr)
				return saveErr
			}
			return nil
		})
		if trxErr != nil {
			log.Errorf("save table columns failed. taskId:%d, err:%v", taskInfo.TaskID, trxErr)
			if summary != nil {
				summary.Status = constants.ColumnDataStatusFailed
				UpdateSummary(ctx, summary)
			}
			return trxErr
		}
		return trxErr
	}

	chanFetchDataNum := 0
	columnsDataChunk := make([]*channel.TableColumnCustomMapRule, 0)

	columnCsvHandler := commonpkg.GetDefaultColumnDataHandler()

	// 缓存结构：按 SchemaNameS 缓存 tableColumnRuleMap 和 tableDefaultRuleMap
	type schemaRuleMaps struct {
		tableColumnRuleMap  map[string]map[string]string
		tableDefaultRuleMap map[string]map[string]string
	}
	schemaRuleMapsCache := make(map[string]*schemaRuleMaps)

	// 预先获取所有需要的 schema 列表
	schemaSet := make(map[string]bool)
	for schemaTablePair := range uniqueSchemaTables {
		schemaSet[schemaTablePair.SchemaName] = true
	}

	// 为每个 schema 预先构建并缓存 rule maps
	for schemaName := range schemaSet {
		log.Debugf("SyncSourceTableColumns service, get schema tables, taskId:%d, schema:%s", taskInfo.TaskID, schemaName)
		schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelTaskSchemaS(ctx, channelInfo.ChannelId, taskInfo.TaskID, schemaName)
		if err != nil {
			return fmt.Errorf("get task channel schema tables failed for schema %s: %v", schemaName, err)
		}

		_, tableColumnRuleMap, _, tableDefaultRuleMap, err := BuildO2tTablesRuleMaps(ctx, taskInfo, channelInfo, schemaName, "", schemaTables)
		if err != nil {
			log.Errorf("build o2t tables rule maps failed for schema %s. taskId:%d, err:%v", schemaName, taskInfo.TaskID, err)
			return err
		}

		schemaRuleMapsCache[schemaName] = &schemaRuleMaps{
			tableColumnRuleMap:  tableColumnRuleMap,
			tableDefaultRuleMap: tableDefaultRuleMap,
		}
	}

	log.Infof("SyncSourceTableColumns service, cached rule maps for %d schemas, taskId:%d", len(schemaRuleMapsCache), taskInfo.TaskID)

	for rule := range ruleChan {
		chanFetchDataNum++
		schemaTablePair := structs.SchemaTablePair{SchemaName: rule.SchemaNameS, TableName: rule.TableNameS}
		if _, ok := uniqueSchemaTables[schemaTablePair]; !ok {
			log.Debugf("SyncSourceTableColumns service, skip table columns, taskId:%d, schema:%s, table:%s", taskInfo.TaskID, rule.SchemaNameS, rule.TableNameS)
			continue
		}
		finishedTableCounter[schemaTablePair] = true

		// 从缓存中获取对应 schema 的 rule maps
		cachedRuleMaps, exists := schemaRuleMapsCache[rule.SchemaNameS]
		if !exists {
			log.Errorf("schema rule maps not found in cache for schema %s, taskId:%d", rule.SchemaNameS, taskInfo.TaskID)
			return fmt.Errorf("schema rule maps not found in cache for schema %s", rule.SchemaNameS)
		}

		rule.ChannelId = channelInfo.ChannelId
		dataTypeT := cachedRuleMaps.tableColumnRuleMap[rule.TableNameS][rule.ColumnNameS]

		// 使用正则表达式处理两种情况：(数字,数字) 和 (数字)
		reTwoNums := regexp.MustCompile(`\((\d+),(\d+)\)$`) // 匹配 (数字,数字)
		reOneNum := regexp.MustCompile(`\((\d+)\)$`)        // 匹配 (数字)

		if matches := reTwoNums.FindStringSubmatch(dataTypeT); matches != nil {
			// 处理 VARCHAR2(50,10) 类型的情况
			firstNum, err1 := strconv.Atoi(matches[1])
			secondNum, err2 := strconv.Atoi(matches[2])

			if err1 == nil && err2 == nil {
				log.Debugf("SyncSourceTableColumns service, parsed datatype with precision and scale: schema:%s, table:%s, column:%s, dataType:%s, precision:%d, scale:%d",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, firstNum, secondNum)

				// 设置精度和标度
				if firstNum > 0 {
					rule.DataPrecisionT = uint(firstNum)
				} else {
					rule.DataPrecisionT = 38
				}

				rule.DataScaleT = uint(secondNum)

				// 去掉括号及其内容，只保留数据类型名称
				rule.DataTypeT = reTwoNums.ReplaceAllString(dataTypeT, "")
			} else {
				log.Warnf("SyncSourceTableColumns service, failed to parse datatype precision and scale: schema:%s, table:%s, column:%s, dataType:%s, err1:%v, err2:%v",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, err1, err2)
				rule.DataTypeT = dataTypeT
			}
		} else if matches := reOneNum.FindStringSubmatch(dataTypeT); matches != nil {
			// 处理 VARCHAR2(100) 类型的情况
			length, err := strconv.Atoi(matches[1])

			if err == nil {
				log.Debugf("SyncSourceTableColumns service, parsed datatype with length: schema:%s, table:%s, column:%s, dataType:%s, length:%d",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, length)

				// 设置长度
				rule.DataLengthT = uint(length)

				// 去掉括号及其内容，只保留数据类型名称
				rule.DataTypeT = reOneNum.ReplaceAllString(dataTypeT, "")

				// 处理 DECIMAL 类型，如果长度为 0，则设置为 38
				if strings.EqualFold(rule.DataTypeT, "DECIMAL") {
					if length == 0 {
						rule.DataPrecisionT = 38
					} else {
						rule.DataPrecisionT = uint(length)
					}
				}
			} else {
				log.Warnf("SyncSourceTableColumns service, failed to parse datatype length: schema:%s, table:%s, column:%s, dataType:%s, err:%v",
					rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS, dataTypeT, err)
				rule.DataTypeT = dataTypeT
			}
		} else {
			// 没有匹配到任何括号格式，直接使用原值
			rule.DataTypeT = dataTypeT
		}

		rule.DataDefaultT = cachedRuleMaps.tableDefaultRuleMap[rule.TableNameS][rule.ColumnNameS]
		rule.ColumnDigest = columnCsvHandler.CalculateFieldsChecksum(rule.SchemaNameS, rule.TableNameS, rule.ColumnNameS)
		columnsDataChunk = append(columnsDataChunk, rule)
		if uint(len(columnsDataChunk)) < structureParam.GetSyncMetaBatchSize() {
			continue
		}
		if saveErr := saveInTransaction(columnsDataChunk); saveErr != nil {
			log.Errorf("save table columns failed. taskId:%d, err:%v", taskInfo.TaskID, saveErr)
			return saveErr
		}
		columnsDataChunk = columnsDataChunk[:0]
	}
	if len(columnsDataChunk) != 0 {
		saveErr := saveInTransaction(columnsDataChunk)
		if saveErr != nil {
			log.Errorf("save table columns failed. taskId:%d, err:%v", taskInfo.TaskID, saveErr)
			return saveErr
		}
	}

	if waitErr := eg.Wait(); waitErr != nil {
		summary.Status = constants.ColumnDataStatusFailed
	} else {
		summary.Status = constants.ColumnDataStatusFetched
	}
	if saveSummaryErr = UpdateSummary(ctx, summary); saveSummaryErr != nil {
		log.Errorf("save table columns summary failed. taskId:%d, err:%v", taskInfo.TaskID, saveSummaryErr)
		return saveSummaryErr
	}
	return nil
}
