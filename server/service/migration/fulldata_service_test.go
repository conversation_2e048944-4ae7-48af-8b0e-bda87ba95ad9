/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package migration_test

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

func TestFullDataService(t *testing.T) {
	log.NewRootLoggerFromConfig(&log.LogConfig{
		LogLevel:    "info",
		LogFileName: "/Users/<USER>/gostore/tims/server/service/migration/test.log",
	})
	log.NewAssessmentLogger()
	log.NewTidbStatsLogLogger()

	ctx := context.Background()
	err := models.CreateDatabase(&models.DBConfig{
		Host:     "xxx.xxx.xxx.xxx",
		Port:     5000,
		User:     "root",
		Password: "",
		Schema:   "tims",
	})
	if err != nil {
		panic(err)
	}

	// 2 GBK  5 UTF8
	taskIDS := []int{5, 2}

	var wg sync.WaitGroup

	for _, taskID := range taskIDS {
		wg.Add(1)
		go func(Ctx context.Context, ID int) {
			defer wg.Done()
			run(Ctx, &message.MigrationTaskReq{TaskID: ID})
		}(ctx, taskID)
	}

	wg.Wait()

	//for _, taskID := range taskIDS {
	//	sourceConn, targetConn, err := InitFullDataMigrationConn(ctx, &message.MigrationTaskReq{
	//		TaskID: taskID,
	//	})
	//	if err != nil {
	//		panic(err)
	//	}
	//	run(ctx, &message.MigrationTaskReq{TaskID: taskID}, sourceConn, targetConn)
	//
	//}

	fmt.Println("success")

	//initTask(ctx)
	//err = taskRun(ctx, &models.DBConfig{}, &message.MigrationTaskReq{TaskID: 401})
	//if err != nil {
	//	panic(err)
	//}

}
