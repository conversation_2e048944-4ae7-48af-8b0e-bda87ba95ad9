package migration

import (
	"context"
	"fmt"
	"sort"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	tmso2t "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/o2t"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	migrationpkg "gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	commonservice "gitee.com/pingcap_enterprise/tms/server/service/common"
	stringUtil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/samber/lo"

	"regexp"
	"strconv"
	"strings"
	"time"

	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/public"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/logger"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	common2 "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	tmsconfig "gitee.com/pingcap_enterprise/tms/util/config"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/mysql"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"golang.org/x/sync/errgroup"
)

type FullDataMigrationService struct {
}

func NewFullDataMigrationService() *FullDataMigrationService {
	return &FullDataMigrationService{}
}

func (s *FullDataMigrationService) GetFullDataMigrationSummaryByTask(ctx context.Context, msg *message.MigrationTaskReq) (*message.MigrationDataTaskSummaryResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return nil, err
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, err
	}
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return nil, err
	}

	tableSummaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID: msg.TaskID,
	})
	if err != nil {
		return nil, err
	}

	var (
		totalNums   int
		successNums int
		runningNums int
		waitingNums int
		failedNums  int
	)
	totalNums = len(tableSummaries)
	for _, r := range tableSummaries {
		if r.TaskStatus == common.TaskStatusSuccess {
			successNums += 1
		}
		if r.TaskStatus == common.TaskStatusFailed || r.TaskStatus == constants.MigrationStatusInvalid {
			failedNums += 1
		}
		if r.TaskStatus == common.TaskStatusRunning {
			runningNums += 1
		}
		if r.TaskStatus == common.TaskStatusWaiting {
			waitingNums += 1
		}
	}

	schemaTables, err := models.GetChannelReaderWriter().GroupChannelSchemaTablesCountsByTask(ctx, detailChannelInfo.ChannelId, msg.TaskID)
	if err != nil {
		return nil, err
	}

	var migrationDetails []message.MigrationDataTaskSummaryDetail

	schemas := lo.Keys(schemaTables)
	sort.Strings(schemas)
	for _, sourceSchema := range schemas {
		var (
			detailTotalNums         int
			detailSuccessNums       int
			detailFailedNums        int
			detailRunningNums       int
			detailWaitingNums       int
			detailSuccessRatio      string
			detailMigrationResult   string
			detailMigrationDuration float64
		)
		for _, r := range tableSummaries {
			if strings.EqualFold(sourceSchema, r.SchemaNameS) {
				detailTotalNums += 1

				if r.TaskStatus == common.TaskStatusSuccess {
					detailSuccessNums += 1
				}
				if r.TaskStatus == common.TaskStatusFailed || r.TaskStatus == constants.MigrationStatusInvalid {
					detailFailedNums += 1
				}
				if r.TaskStatus == common.TaskStatusRunning {
					detailRunningNums += 1
				}
				if r.TaskStatus == common.TaskStatusWaiting {
					detailWaitingNums += 1
				}

				detailMigrationDuration += r.Duration
			}
		}

		successRatio, err := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(detailSuccessNums)/float64(detailTotalNums)), 64)
		if err != nil {
			return nil, err
		}
		detailSuccessRatio = fmt.Sprintf("%.2f", successRatio*100)

		detailDurationStr := time.Duration(detailMigrationDuration * float64(time.Second)).String()

		if detailFailedNums != 0 {
			detailMigrationResult = common.TaskStatusFailed
		} else if detailFailedNums == 0 && detailRunningNums != 0 {
			detailMigrationResult = common.TaskStatusRunning
		} else if detailFailedNums == 0 && detailWaitingNums != 0 && detailRunningNums == 0 {
			detailMigrationResult = common.TaskStatusWaiting
		} else {
			detailMigrationResult = common.TaskStatusSuccess
		}

		migrationDetails = append(migrationDetails, message.MigrationDataTaskSummaryDetail{
			ServiceName:       datasourcepkg.GetServiceName(sourceDS),
			SchemaName:        sourceSchema,
			TotalTableNums:    schemaTables[sourceSchema],
			MigrationNums:     detailTotalNums,
			SuccessNums:       detailSuccessNums,
			RunningNums:       detailRunningNums,
			WaitingNums:       detailWaitingNums,
			FailedNums:        detailFailedNums,
			SuccessRatio:      detailSuccessRatio,
			MigrationResult:   detailMigrationResult,
			MigrationDuration: detailDurationStr,
		})
	}

	return &message.MigrationDataTaskSummaryResp{
		TaskID:               msg.TaskID,
		TotalMigrationNums:   totalNums,
		SuccessMigrationNums: successNums,
		RunningMigrationNums: runningNums,
		WaitingMigrationNums: waitingNums,
		FailedMigrationNums:  failedNums,
		MigrationDetails:     migrationDetails,
	}, nil
}

func (s *FullDataMigrationService) GetFullDataMigrationSummaryBySchema(ctx context.Context, msg *message.MigrationDataSchemaReq) (*message.MigrationDataSchemaSummaryResp, error) {

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", msg.TaskID, getTaskErr)
		return nil, getTaskErr
	}

	tableSummaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
	})
	if err != nil {
		log.Errorf("get table migration summary failed, taskId:%d, schemaName:%s, err:%v", msg.TaskID, msg.SchemaNameS, err)
		return nil, err
	}

	channelSchemaTableNum, getNumErr := models.GetChannelReaderWriter().GroupChannelSchemaTablesCountsByTask(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if getNumErr != nil {
		log.Errorf("get channel schema table num failed, taskId:%d, err:%v", taskInfo.TaskID, getNumErr)
		return nil, getNumErr
	}

	var (
		serviceName string
		totalNums   int
		successNums int
		runningNums int
		waitingNums int
		failedNums  int
		duration    float64
	)
	// totalNums = len(tableSummaries)
	for _, r := range tableSummaries {
		totalNums += 1
		duration += r.Duration
		serviceName = r.ServiceNameS
		if r.TaskStatus == common.TaskStatusSuccess {
			successNums += 1
		}
		if r.TaskStatus == common.TaskStatusFailed || r.TaskStatus == constants.MigrationStatusInvalid {
			failedNums += 1
		}
		if r.TaskStatus == common.TaskStatusRunning {
			runningNums += 1
		}
		if r.TaskStatus == common.TaskStatusWaiting {
			waitingNums += 1
		}
	}

	successRatio, err := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(successNums)/float64(totalNums)), 64)
	if err != nil {
		return nil, err
	}
	successRatioStr := fmt.Sprintf("%.2f", successRatio*100)

	detailDurationStr := time.Duration(duration * float64(time.Second)).String()
	var schemaSummarys []message.MigrationDataTaskSummaryDetail
	schemaSummarys = append(schemaSummarys, message.MigrationDataTaskSummaryDetail{
		ServiceName:       serviceName,
		SchemaName:        msg.SchemaNameS,
		TotalTableNums:    channelSchemaTableNum[msg.SchemaNameS],
		MigrationNums:     totalNums,
		SuccessNums:       successNums,
		FailedNums:        failedNums,
		RunningNums:       runningNums,
		WaitingNums:       waitingNums,
		SuccessRatio:      successRatioStr,
		MigrationDuration: detailDurationStr,
	})
	return &message.MigrationDataSchemaSummaryResp{MigrationSchemaSummary: schemaSummarys}, nil
}

func (s *FullDataMigrationService) GetFullDataMigrationDetailBySchema(ctx context.Context, msg *message.MigrationDataSchemaPageReq) (*message.MigrationDataSchemaDetailResp, *message.Page, error) {

	var queryStatus []string
	if msg.QueryStatus != "" {
		queryStatus = append(queryStatus, msg.QueryStatus)
	}
	if msg.QueryStatus == transfercommon.TaskStatusFailed {
		queryStatus = append(queryStatus, constants.MigrationStatusInvalid)
	}

	log.Infof("query fulldata migration detail by schema, taskID: %d, schemaName: %s, queryStatus: %v, page: %d, pageSize: %d", msg.TaskID, msg.SchemaNameS, queryStatus, msg.Page, msg.PageSize)
	tableSummaries, total, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummaryPageWithStatus(ctx, &migration.TableMigrationSummary{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
	}, queryStatus, msg.Page, msg.PageSize)
	if err != nil {
		return nil, nil, err
	}

	page := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}
	var sd []message.MigrationDataSchemaDetail

	for _, t := range tableSummaries {

		tableDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
			TaskID:      t.TaskID,
			SchemaNameS: t.SchemaNameS,
			TableNameS:  t.TableNameS,
		})
		if err != nil {
			log.Errorf("get table migration detail failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", t.TaskID, t.SchemaNameS, t.TableNameS, err)
			return nil, nil, err
		}

		var (
			successChunks int64
			failedChunks  int64
			runningChunks int64
			waitingChunks int64
		)
		var errInfo = ""
		for _, d := range tableDetails {
			if strings.EqualFold(d.TaskStatus, common.TaskStatusSuccess) {
				successChunks += 1
			}
			if strings.EqualFold(d.TaskStatus, common.TaskStatusFailed) || strings.EqualFold(d.TaskStatus, constants.MigrationStatusInvalid) {
				failedChunks += 1
				errInfo = d.ErrorDetail
			}
			if strings.EqualFold(d.TaskStatus, common.TaskStatusRunning) {
				runningChunks += 1
			}
			if strings.EqualFold(d.TaskStatus, common.TaskStatusWaiting) {
				waitingChunks += 1
			}
		}

		sd = append(sd, message.MigrationDataSchemaDetail{
			ID:                t.ID,
			ServiceName:       t.ServiceNameS,
			SchemaNameS:       t.SchemaNameS,
			TableNameS:        t.TableNameS,
			TableTypeS:        t.TableTypeS,
			ChunkSize:         t.ChunkSize,
			TotalChunks:       t.ChunkTotalNums,
			SuccessChunks:     successChunks,
			FailedChunks:      failedChunks,
			RunningChunks:     runningChunks,
			WaitingChunks:     waitingChunks,
			MigrationResult:   t.TaskStatus,
			MigrationDuration: time.Duration(t.Duration * float64(time.Second)).String(),
			MigrationMessage:  errInfo,
		})
	}

	return &message.MigrationDataSchemaDetailResp{MigrationDataSchemaDetail: sd}, page, nil
}

// 批量重试所有失败表的 chunk
func (s *FullDataMigrationService) BatchRetryFullDataMigrationByTableFailed(ctx context.Context, msg *message.MigrationDataBatchRetryReq) error {
	summarys, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummary(ctx, msg.TaskID, msg.TableID, msg.QueryStatus)
	if err != nil {
		return err
	}
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return fmt.Errorf("get task info failed: %v", err)
	}

	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	taskInfo.ErrorDetail = ""
	_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to running failed.", taskInfo.TaskID)
	}

	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return fmt.Errorf("get channel info failed: %v", err)
	}

	// init datasource and conns
	migrationDBConns, setUpErr := migrationpkg.SetUpDatabaseConnsAndCollation(ctx, detailChannelInfo, taskInfo.TaskID)
	if setUpErr != nil {
		log.Errorf("set up database conns and collation failed, taskId:%d, err:%v", taskInfo.TaskID, setUpErr)
		return setUpErr
	}

	taskTableConfigMap, buildErr := migrationpkg.BuildTaskTableConfigMap(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		return buildErr
	}

	migrationParam, buildErr := migrationpkg.BuildMigrationConfigParams(ctx, taskInfo.ChannelId, taskInfo.TaskID, taskInfo.TaskParamTmplateId)
	if buildErr != nil {
		log.Errorf("build migration config params failed, taskId:%d, err:%v", taskInfo.TaskID, buildErr)
		return buildErr
	}

	// init logger
	globalCfg := tmsconfig.GetGlobalConfig()
	logger.NewZapLogger(&config.Config{LogConfig: config.LogConfig{
		LogLevel:   globalCfg.LogConfig.LogLevel,
		LogFile:    tmsconfig.BuildMigrationLogFileName(constants.MigrationTypeFullData, taskInfo.ChannelId, taskInfo.TaskID),
		MaxSize:    globalCfg.LogConfig.LogMaxSize,
		MaxDays:    globalCfg.LogConfig.LogMaxAge,
		MaxBackups: globalCfg.LogConfig.LogMaxBackups,
	}})

	// create database conn
	var (
		sourceDB *oracle.Oracle
		targetDB *mysql.MySQL
	)
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return fmt.Errorf("get task source datasource info failed: %v", err)
	}
	targetDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdT)
	if err != nil {
		return fmt.Errorf("get task target datasource info failed: %v", err)
	}

	// meta database is META
	// source database is ORACLE
	// target database is TiDB
	if strings.EqualFold(detailChannelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		checkDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    sourceDS.UserName,
			Password:    sourceDS.PasswordValue,
			Host:        sourceDS.HostIp,
			Port:        sourceDS.HostPort,
			ServiceName: datasourcepkg.GetServiceName(sourceDS),
		}, "")
		if err != nil {
			return fmt.Errorf("create source datasource conn failed: %v", err)
		}

		// 数据库字符集
		charset, err := checkDB.GetOracleDBCharacterSet()
		if err != nil {
			return err
		}
		sourceDBCharset := strings.Split(charset, ".")[1]

		if !strings.EqualFold(sourceDS.Charset, sourceDBCharset) {
			log.Warnf("oracle server charset [%v] and oracle config charset [%v] aren't different, would be ignore, running with oracle server charset [%v]", sourceDBCharset, sourceDS.Charset, sourceDBCharset)
		}

		if _, ok := common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(sourceDBCharset)]; !ok {
			return fmt.Errorf("oracle current charset [%v] isn't support, support charset [%v]", sourceDBCharset, common.MigrateOracleCharsetStringConvertMapping)
		}
		if !strings.EqualFold(common.MYSQLCharsetUTF8MB4, targetDS.Charset) {
			return fmt.Errorf("tidb current config charset [%v] isn't support, tidb only support charset [%v]", targetDS.Charset, common.MYSQLCharsetUTF8MB4)
		}

		sourceDB, err = oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    sourceDS.UserName,
			Password:    sourceDS.PasswordValue,
			Host:        sourceDS.HostIp,
			Port:        sourceDS.HostPort,
			ServiceName: datasourcepkg.GetServiceName(sourceDS),
			Charset:     sourceDBCharset,
		}, "")
		if err != nil {
			return fmt.Errorf("create source datasource conn failed: %v", err)
		}

		if _, setModuleErr := sourceDB.OracleDB.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
			return fmt.Errorf("set oracle module failed: %v", setModuleErr)
		}

		targetDB, err = mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
			Username:      targetDS.UserName,
			Password:      targetDS.PasswordValue,
			Host:          targetDS.HostIp,
			Port:          targetDS.HostPort,
			ConnectParams: targetDS.ConnectParams,
			//TableOption:   targetDS.TableOption,
			Charset: targetDS.Charset,
		})
		if err != nil {
			return fmt.Errorf("create target datasource conn failed: %v", err)
		}

	} else {
		return fmt.Errorf("channel type [%s] isn' support, please agagin choose", detailChannelInfo.ChannelType)
	}

	// get task table
	oracleDBVersion, err := sourceDB.GetOracleDBVersion()
	if err != nil {
		return err
	}
	if common.VersionOrdinal(oracleDBVersion) < common.VersionOrdinal(common.RequireOracleDBVersion) {
		return fmt.Errorf("oracle db version [%v] is less than 11g, can't be using transferdb tools", oracleDBVersion)
	}

	// 重运行失败表 chunk 的任务 failed
	gS := &errgroup.Group{}
	gS.SetLimit(migrationParam.GetTableThreads())

	for _, summary := range summarys {
		d := summary
		gS.Go(func() error {
			tableTime := time.Now()
			originSummary, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
				SchemaNameT: d.SchemaNameT,
				TableNameT:  d.TableNameT,
			})
			if err != nil {
				return err
			}

			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				if !strings.EqualFold(d.TaskStatus, common.TaskStatusRunning) {
					err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status": common.TaskStatusRunning,
					})
					if err != nil {
						return err
					}
				}

				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
					ChannelID:   taskInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      fmt.Sprintf("t03-001:oracle table task %v batch retry begin...", d.String()),
				})
				if err != nil {
					return fmt.Errorf("create progress log detail record failed: %v", err)
				}
				return nil
			})
			if err != nil {
				return err
			}

			failedDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
				TaskStatus:  common.TaskStatusFailed,
			})
			if err != nil {
				return err
			}

			if len(failedDetails) > 0 {
				g := &errgroup.Group{}
				g.SetLimit(migrationParam.GetSqlThreads())

				re := regexp.MustCompile("sql \\[(?s).*] execute")

				for idx := range failedDetails {
					m := &(failedDetails[idx])
					g.Go(func() error {
						chunkTime := time.Now()

						errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
							ID: m.ID,
						}, map[string]interface{}{
							"Task_Status": common.TaskStatusRunning,
							"Duration":    time.Now().Sub(chunkTime).Seconds(),
						})
						if errU != nil {
							return fmt.Errorf("UpdateTableMigrationDetailById [%v] chunk status failed: %v", m.String(), errU)
						}

						columnNameS, err := sourceDB.GetOracleTableRowsColumn(
							common.StringsBuilder(`SELECT *`, ` FROM `,
								common.StringUPPER(m.SchemaNameS), `.`, common.StringUPPER(m.TableNameS), ` WHERE ROWNUM = 1`),
							common.MigrateOracleCharsetStringConvertMapping[sourceDS.Charset],
							common.StringUPPER(targetDS.Charset))
						if err != nil {
							migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, err.Error(), constants.MigrationLogTypeFullData)
							migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)
							return nil
						}

						sqlStr00 := tmso2t.GenMySQLTablePrepareStmt(common.StringUPPER(m.SchemaNameT), m.TableNameT, columnNameS, migrationParam.GetInsertBatchSize(), migrationParam.GetSQLStatementType(true))
						stmt, err := targetDB.MySQLDB.PrepareContext(ctx, sqlStr00)
						if err != nil {
							migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, err.Error(), constants.MigrationLogTypeFullData)
							migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)
							return err
						}
						defer stmt.Close()

						if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
							SchemaName: m.SchemaNameS,
							TableName:  m.TableNameS,
						}]; ok {
							if taskTableConfig.OperatorTag == "Y" {
								oracleCollation := migrationDBConns.GetOracleCollation()
								oracleCols, err := migrationpkg.GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, m.SchemaNameS, m.TableNameS, oracleCollation, taskTableConfigMap)
								if err != nil {
									log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskInfo.TaskID, m.SchemaNameS, m.TableNameS, err)
									return err
								}
								m.ColumnDetailS = strings.Join(oracleCols, ",")
							}
						}

						if errm := public.IMigrate(tmso2t.NewRows(ctx, meta.FullSyncMeta{
							SchemaNameS:    m.SchemaNameS,
							TableNameS:     `"` + m.TableNameS + `"`,
							SchemaNameT:    m.SchemaNameT,
							TableNameT:     m.TableNameT,
							ConsistentRead: "NO",
							ColumnDetailS:  m.ColumnDetailS,
							ChunkDetailS:   m.ChunkDetailS,
						}, sourceDB, targetDB, stmt,
							common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(sourceDS.Charset)],
							common.StringUPPER(targetDS.Charset),
							migrationParam.GetApplyThreads(), migrationParam.GetInsertBatchSize(), migrationParam.GetCallTimeout(), migrationParam.GetSQLStatementType(true), columnNameS)); errm != nil {
							var (
								errMsg string
							)
							if re.MatchString(errm.Error()) {
								errMsg = re.ReplaceAllString(errMsg, "sql execute")
							} else {
								errMsg = errm.Error()
							}
							if errMsg == "" {
								errMsg = errm.Error()
							}
							migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, errMsg, constants.MigrationLogTypeFullData)
							migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)

							errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
								//TaskID:       m.TaskID,
								//SchemaNameS:  m.SchemaNameS,
								//TableNameS:   m.TableNameS,
								//ChunkDetailS: m.ChunkDetailS,
								ID: m.ID,
							}, map[string]interface{}{
								"Task_Status":     common.TaskStatusFailed,
								"Origin_SQL_HASH": stringUtil.MD5(sqlStr00),
								"Error_Detail":    errMsg,
								"Duration":        time.Now().Sub(chunkTime).Seconds(),
							})
							if errf != nil {
								return fmt.Errorf("get oracle schema table [%v] ITranslator failed: %v", m.String(), errf)
							}

							return nil
						}

						errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
							//TaskID:       m.TaskID,
							//SchemaNameS:  m.SchemaNameS,
							//TableNameS:   m.TableNameS,
							//ChunkDetailS: m.ChunkDetailS,
							ID: m.ID,
						}, map[string]interface{}{
							"Task_Status": common.TaskStatusSuccess,
							"Duration":    time.Now().Sub(chunkTime).Seconds(),
						})
						if errf != nil {
							return fmt.Errorf("get oracle schema table [%v] Success failed: %v", m.String(), errf)
						}
						return nil
					})
				}
				if err = g.Wait(); err != nil {
					return err
				}

				// 更新详情以及日志
				failedChunkDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					SchemaNameT: d.SchemaNameT,
					TableNameT:  d.TableNameT,
					TaskStatus:  common.TaskStatusFailed,
				})
				if err != nil {
					return err
				}
				if len(failedChunkDetails) == 0 {
					err = models.Transaction(ctx, func(transactionCtx context.Context) error {
						err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
							&migration.TableMigrationSummary{
								TaskID:      d.TaskID,
								SchemaNameS: d.SchemaNameS,
								TableNameS:  d.TableNameS,
							}, map[string]interface{}{
								"Task_Status":        common.TaskStatusSuccess,
								"Chunk_Success_Nums": originSummary[0].ChunkTotalNums,
								"Chunk_Failed_Nums":  0,
								"Duration":           time.Now().Sub(tableTime).Seconds(),
							})
						if err != nil {
							return err
						}

						_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
							ChannelID:   detailChannelInfo.ChannelId,
							TaskID:      d.TaskID,
							SchemaNameS: d.SchemaNameS,
							TableNameS:  d.TableNameS,
							Detail:      fmt.Sprintf("t03-001:oracle table task %v batch retry finished...", d.String()),
						})
						if err != nil {
							return fmt.Errorf("create progress log detail record failed: %v", err)
						}
						return nil
					})
					if err != nil {
						return err
					}
				} else {
					err = models.Transaction(ctx, func(transactionCtx context.Context) error {
						err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
							&migration.TableMigrationSummary{
								TaskID:      d.TaskID,
								SchemaNameS: d.SchemaNameS,
								TableNameS:  d.TableNameS,
							}, map[string]interface{}{
								"Task_Status":        common.TaskStatusFailed,
								"Chunk_Success_Nums": originSummary[0].ChunkTotalNums - int64(len(failedChunkDetails)),
								"Chunk_Failed_Nums":  len(failedChunkDetails),
								"Duration":           time.Now().Sub(tableTime).Seconds(),
							})
						if err != nil {
							return err
						}
						_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
							ChannelID:   detailChannelInfo.ChannelId,
							TaskID:      d.TaskID,
							SchemaNameS: d.SchemaNameS,
							TableNameS:  d.TableNameS,
							Detail:      fmt.Sprintf("t03-001:oracle table task %v batch retry failed...", d.String()),
						})
						if err != nil {
							return fmt.Errorf("create progress log detail record failed: %v", err)
						}
						return nil
					})
				}

				if err != nil {
					return err
				}
			} else {
				err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(ctx, &migration.TableMigrationSummary{
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
				}, map[string]interface{}{
					"Task_Status": common.TaskStatusSuccess,
				})
				if err != nil {
					return err
				}
			}
			return nil
		})
	}

	if err = gS.Wait(); err != nil {
		return err
	}

	UpdateTaskIfFullDataRetryFinish(ctx, msg.TaskID)

	return nil
}

func (s *FullDataMigrationService) GetFullDataMigrationFailedChunkByTable(ctx context.Context, msg *message.MigrationDataChunkFailedAndWaitingReq) (*message.MigrationDataChunkFailedAndWaitingResp, error) {
	failedDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		TaskStatus:  common.TaskStatusFailed,
	})
	if err != nil {
		return nil, err
	}

	var faileds []message.MigrationDataTableFailedAndWaitingChunkDetail
	for _, f := range failedDetails {
		faileds = append(faileds, message.MigrationDataTableFailedAndWaitingChunkDetail{
			TaskID:      f.TaskID,
			SchemaNameS: f.SchemaNameS,
			TableNameS:  f.TableNameS,
			TaskStatus:  f.TaskStatus,
			ErrorDetail: f.ErrorDetail,
		})
	}
	return &message.MigrationDataChunkFailedAndWaitingResp{MigrationDataFailedAndWaitingChunkDetail: faileds}, nil
}

func (s *FullDataMigrationService) GetFullDataMigrationFailedChunkErrorDetailByTable(ctx context.Context, msg *message.MigrationDataChunkErrorReq) (*message.MigrationDataChunkErrorResp, error) {
	failedDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		TaskStatus:  common.TaskStatusFailed,
	})
	if err != nil {
		return nil, err
	}

	var faileds []message.MigrationDataTableFailedAndWaitingChunkDetail
	for _, f := range failedDetails {
		faileds = append(faileds, message.MigrationDataTableFailedAndWaitingChunkDetail{
			TaskID:      f.TaskID,
			SchemaNameS: f.SchemaNameS,
			TableNameS:  f.TableNameS,
			ChunkDetail: f.ChunkDetailS,
			TaskStatus:  f.TaskStatus,
			ErrorDetail: f.ErrorDetail,
		})
	}
	return &message.MigrationDataChunkErrorResp{MigrationDataFailedChunkErrorDetail: faileds}, nil
}

// 重试某个表某个失败的 chunk
func (s *FullDataMigrationService) RetryFullDataMigrationFailedChunkDetailByTable(ctx context.Context, msg *message.MigrationDataFailedChunkRetryReq) error {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return fmt.Errorf("get task info failed: %v", err)
	}

	// 判断任务状态是否是失败
	if taskInfo.TaskStatus != constants.TASK_STATUS_FAILED {
		return fmt.Errorf("current task status [%d] isn't failed, please checking", taskInfo.TaskStatus)
	}

	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	taskInfo.ErrorDetail = ""
	_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to running failed.", taskInfo.TaskID)
	}

	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return fmt.Errorf("get channel info failed: %v", err)
	}

	// init datasource and conns
	migrationDBConns, setUpErr := migrationpkg.SetUpDatabaseConnsAndCollation(ctx, detailChannelInfo, taskInfo.TaskID)
	if setUpErr != nil {
		log.Errorf("set up database conns and collation failed, taskId:%d, err:%v", taskInfo.TaskID, setUpErr)
		return setUpErr
	}

	taskTableConfigMap, buildErr := migrationpkg.BuildTaskTableConfigMap(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		return buildErr
	}

	migrationParam, buildErr := migrationpkg.BuildMigrationConfigParams(ctx, taskInfo.ChannelId, taskInfo.TaskID, taskInfo.TaskParamTmplateId)
	if buildErr != nil {
		log.Errorf("build migration config params failed, taskId:%d, err:%v", taskInfo.TaskID, buildErr)
		return buildErr
	}

	// init logger
	globalCfg := tmsconfig.GetGlobalConfig()
	logger.NewZapLogger(&config.Config{LogConfig: config.LogConfig{
		LogLevel:   globalCfg.LogConfig.LogLevel,
		LogFile:    tmsconfig.BuildMigrationLogFileName(constants.MigrationTypeFullData, taskInfo.ChannelId, taskInfo.TaskID),
		MaxSize:    globalCfg.LogConfig.LogMaxSize,
		MaxDays:    globalCfg.LogConfig.LogMaxAge,
		MaxBackups: globalCfg.LogConfig.LogMaxBackups,
	}})

	// create database conn
	var (
		sourceDB *oracle.Oracle
		targetDB *mysql.MySQL
	)
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdS)
	if err != nil {
		return fmt.Errorf("get task source datasource info failed: %v", err)
	}
	targetDS, err := models.GetDatasourceReaderWriter().Get(ctx, detailChannelInfo.DatasourceIdT)
	if err != nil {
		return fmt.Errorf("get task target datasource info failed: %v", err)
	}

	// meta database is META
	// source database is ORACLE
	// target database is TiDB
	if strings.EqualFold(detailChannelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		checkDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    sourceDS.UserName,
			Password:    sourceDS.PasswordValue,
			Host:        sourceDS.HostIp,
			Port:        sourceDS.HostPort,
			ServiceName: datasourcepkg.GetServiceName(sourceDS),
		}, "")
		if err != nil {
			return fmt.Errorf("create source datasource conn failed: %v", err)
		}

		// 数据库字符集
		charset, err := checkDB.GetOracleDBCharacterSet()
		if err != nil {
			return err
		}
		sourceDBCharset := strings.Split(charset, ".")[1]

		if !strings.EqualFold(sourceDS.Charset, sourceDBCharset) {
			log.Warnf("oracle server charset [%v] and oracle config charset [%v] aren't different, would be ignore, running with oracle server charset [%v]", sourceDBCharset, sourceDS.Charset, sourceDBCharset)
		}

		if _, ok := common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(sourceDBCharset)]; !ok {
			return fmt.Errorf("oracle current charset [%v] isn't support, support charset [%v]", sourceDBCharset, common.MigrateOracleCharsetStringConvertMapping)
		}
		if !strings.EqualFold(common.MYSQLCharsetUTF8MB4, targetDS.Charset) {
			return fmt.Errorf("tidb current config charset [%v] isn't support, tidb only support charset [%v]", targetDS.Charset, common.MYSQLCharsetUTF8MB4)
		}

		sourceDB, err = oracle.NewOracleDBEngine(ctx, config.OracleConfig{
			Username:    sourceDS.UserName,
			Password:    sourceDS.PasswordValue,
			Host:        sourceDS.HostIp,
			Port:        sourceDS.HostPort,
			ServiceName: datasourcepkg.GetServiceName(sourceDS),
			Charset:     sourceDBCharset,
		}, "")
		if err != nil {
			return fmt.Errorf("create source datasource conn failed: %v", err)
		}

		if _, setModuleErr := sourceDB.OracleDB.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
			return fmt.Errorf("set oracle module failed: %v", setModuleErr)
		}

		targetDB, err = mysql.NewMySQLDBEngine(ctx, config.MySQLConfig{
			Username:      targetDS.UserName,
			Password:      targetDS.PasswordValue,
			Host:          targetDS.HostIp,
			Port:          targetDS.HostPort,
			ConnectParams: targetDS.ConnectParams,
			//TableOption:   targetDS.TableOption,
			Charset: targetDS.Charset,
		})
		if err != nil {
			return fmt.Errorf("create target datasource conn failed: %v", err)
		}
	} else {
		return fmt.Errorf("channel type [%s] isn' support, please agagin choose", detailChannelInfo.ChannelType)
	}

	// get task table
	oracleDBVersion, err := sourceDB.GetOracleDBVersion()
	if err != nil {
		return err
	}
	if common.VersionOrdinal(oracleDBVersion) < common.VersionOrdinal(common.RequireOracleDBVersion) {
		return fmt.Errorf("oracle db version [%v] is less than 11g, can't be using transferdb tools", oracleDBVersion)
	}

	// 获取失败 chunk
	tableTime := time.Now()
	tableSummaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
	})
	if err != nil {
		return err
	}
	d := tableSummaries[0]

	err = models.Transaction(ctx, func(transactionCtx context.Context) error {
		if !strings.EqualFold(d.TaskStatus, common.TaskStatusRunning) {
			err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
				TaskID:      msg.TaskID,
				SchemaNameS: msg.SchemaNameS,
				TableNameS:  msg.TableNameS,
			}, map[string]interface{}{
				"Task_Status": common.TaskStatusRunning,
			})
			if err != nil {
				return err
			}
		}

		_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
			ChannelID:   taskInfo.ChannelId,
			TaskID:      msg.TaskID,
			SchemaNameS: msg.SchemaNameS,
			TableNameS:  msg.TableNameS,
			Detail:      fmt.Sprintf("t03-001:oracle table task %v begin...", d.String()),
		})
		if err != nil {
			return fmt.Errorf("create progress log detail record failed: %v", err)
		}
		return nil
	})
	if err != nil {
		return err
	}

	failedDetails, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetail(ctx, msg.TaskID, msg.SchemaNameS, msg.TableNameS, msg.ChunkID, common.TaskStatusFailed)
	if err != nil {
		return err
	}

	if len(failedDetails) > 0 {
		g := &errgroup.Group{}
		g.SetLimit(migrationParam.GetSqlThreads())

		re := regexp.MustCompile("sql \\[(?s).*] execute")

		for _, detail := range failedDetails {
			m := detail
			g.Go(func() error {
				chunkTime := time.Now()

				errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
					ID: m.ID,
				}, map[string]interface{}{
					"Task_Status": common.TaskStatusRunning,
					"Duration":    time.Now().Sub(chunkTime).Seconds(),
				})
				if errU != nil {
					return fmt.Errorf("UpdateTableMigrationDetailById [%v] chunk status failed: %v", m.String(), errU)
				}

				columnNameS, err := sourceDB.GetOracleTableRowsColumn(
					common.StringsBuilder(`SELECT *`, ` FROM `,
						common.StringUPPER(m.SchemaNameS), `.`, common.StringUPPER(m.TableNameS), ` WHERE ROWNUM = 1`),
					common.MigrateOracleCharsetStringConvertMapping[sourceDS.Charset],
					common.StringUPPER(targetDS.Charset))
				if err != nil {
					return nil
				}

				sqlStr00 := tmso2t.GenMySQLTablePrepareStmt(common.StringUPPER(m.SchemaNameT), m.TableNameT, columnNameS, migrationParam.GetInsertBatchSize(), migrationParam.GetSQLStatementType(true))
				stmt, err := targetDB.MySQLDB.PrepareContext(ctx, sqlStr00)
				if err != nil {
					return err
				}
				defer stmt.Close()

				if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
					SchemaName: m.SchemaNameS,
					TableName:  m.TableNameS,
				}]; ok {
					if taskTableConfig.OperatorTag == "Y" {
						oracleCollation := migrationDBConns.GetOracleCollation()
						oracleCols, err := migrationpkg.GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, m.SchemaNameS, m.TableNameS, oracleCollation, taskTableConfigMap)
						if err != nil {
							log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskInfo.TaskID, m.SchemaNameS, m.TableNameS, err)
							return err
						}
						m.ColumnDetailS = strings.Join(oracleCols, ",")
					}
				}

				if errm := public.IMigrate(tmso2t.NewRows(ctx, meta.FullSyncMeta{
					SchemaNameS:    m.SchemaNameS,
					TableNameS:     `"` + m.TableNameS + `"`,
					SchemaNameT:    m.SchemaNameT,
					TableNameT:     m.TableNameT,
					ConsistentRead: "NO",
					ColumnDetailS:  m.ColumnDetailS,
					ChunkDetailS:   m.ChunkDetailS,
				}, sourceDB, targetDB, stmt,
					common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(sourceDS.Charset)],
					common.StringUPPER(targetDS.Charset),
					migrationParam.GetApplyThreads(), migrationParam.GetInsertBatchSize(), migrationParam.GetCallTimeout(), migrationParam.GetSQLStatementType(true), columnNameS)); errm != nil {
					var (
						errMsg string
					)
					if re.MatchString(errm.Error()) {
						errMsg = re.ReplaceAllString(errMsg, "sql execute")
					} else {
						errMsg = errm.Error()
					}
					if errMsg == "" {
						errMsg = errm.Error()
					}

					md5SqlStr := stringUtil.MD5(sqlStr00)
					log.Warnf("migrate database data chunk failed, taskId [%d], SchemaNameS [%s], TableNameS  [%s], chunk [%v], sql hash [%v], origin sql [%v], error [%v]", m.TaskID, m.SchemaNameS, m.TableNameS, m.ChunkDetailS, md5SqlStr, sqlStr00, errm.Error())

					errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
						//TaskID:       m.TaskID,
						//SchemaNameS:  m.SchemaNameS,
						//TableNameS:   m.TableNameS,
						//ChunkDetailS: m.ChunkDetailS,
						ID: m.ID,
					}, map[string]interface{}{
						"Task_Status":     common.TaskStatusFailed,
						"Origin_SQL_Hash": md5SqlStr,
						"Error_Detail":    errMsg,
						"Duration":        time.Now().Sub(chunkTime).Seconds(),
					})
					if errf != nil {
						return fmt.Errorf("get oracle schema table [%v] ITranslator failed: %v", m.String(), errf)
					}

					return nil
				}

				errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
					//TaskID:       m.TaskID,
					//SchemaNameS:  m.SchemaNameS,
					//TableNameS:   m.TableNameS,
					//ChunkDetailS: m.ChunkDetailS,
					ID: m.ID,
				}, map[string]interface{}{
					"Task_Status": common.TaskStatusSuccess,
					"Duration":    time.Now().Sub(chunkTime).Seconds(),
				})
				if errf != nil {
					return fmt.Errorf("get oracle schema table [%v] Success failed: %v", m.String(), errf)
				}
				return nil
			})
		}
		if err = g.Wait(); err != nil {
			return err
		}

		// 更新详情以及日志
		failedChunkDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
			TaskID:      d.TaskID,
			SchemaNameS: d.SchemaNameS,
			TableNameS:  d.TableNameS,
			SchemaNameT: d.SchemaNameT,
			TableNameT:  d.TableNameT,
			TaskStatus:  common.TaskStatusFailed,
		})
		if err != nil {
			return err
		}
		if len(failedChunkDetails) == 0 {
			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
					&migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status":        common.TaskStatusSuccess,
						"Chunk_Success_Nums": d.ChunkTotalNums,
						"Chunk_Failed_Nums":  0,
						"Duration":           time.Now().Sub(tableTime).Seconds(),
					})
				if err != nil {
					return err
				}

				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
					ChannelID:   detailChannelInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      fmt.Sprintf("t03-001:oracle table task %v finished...", d.String()),
				})
				if err != nil {
					return fmt.Errorf("create progress log detail record failed: %v", err)
				}
				return nil
			})
			if err != nil {
				return err
			}
		} else {
			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
					&migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status":        common.TaskStatusFailed,
						"Chunk_Success_Nums": d.ChunkTotalNums - int64(len(failedChunkDetails)),
						"Chunk_Failed_Nums":  len(failedChunkDetails),
						"Duration":           time.Now().Sub(tableTime).Seconds(),
					})
				if err != nil {
					return err
				}
				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
					ChannelID:   detailChannelInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      fmt.Sprintf("t03-001:oracle table task %v failed...", d.String()),
				})
				if err != nil {
					return fmt.Errorf("create progress log detail record failed: %v", err)
				}
				return nil
			})
			if err != nil {
				return err
			}
		}
	} else {
		err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(ctx, &migration.TableMigrationSummary{
			TaskID:      d.TaskID,
			SchemaNameS: d.SchemaNameS,
			TableNameS:  d.TableNameS,
		}, map[string]interface{}{
			"Task_Status": common.TaskStatusSuccess,
		})
	}

	UpdateTaskIfFullDataRetryFinish(ctx, msg.TaskID)
	return nil
}

// 批量重试某张表所有失败 chunk
func (s *FullDataMigrationService) BatchRetryFullDataMigrationFailedChunkDetailByTable(ctx context.Context, msg *message.MigrationDataFailedChunkBatchRetryReq) error {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
	if err != nil {
		return fmt.Errorf("get task info failed: %v", err)
	}

	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	taskInfo.ErrorDetail = ""
	_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to running failed.", taskInfo.TaskID)
	}

	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return fmt.Errorf("get channel info failed: %v", err)
	}

	// init datasource and conns
	migrationDBConns, setUpErr := migrationpkg.SetUpDatabaseConnsAndCollation(ctx, detailChannelInfo, taskInfo.TaskID)
	if setUpErr != nil {
		log.Errorf("set up database conns and collation failed, taskId:%d, err:%v", taskInfo.TaskID, setUpErr)
		return setUpErr
	}

	taskTableConfigMap, buildErr := migrationpkg.BuildTaskTableConfigMap(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		return buildErr
	}

	migrationParam, buildErr := migrationpkg.BuildMigrationConfigParams(ctx, taskInfo.ChannelId, taskInfo.TaskID, taskInfo.TaskParamTmplateId)
	if buildErr != nil {
		return buildErr
	}

	// init logger
	globalCfg := tmsconfig.GetGlobalConfig()
	logger.NewZapLogger(&config.Config{LogConfig: config.LogConfig{
		LogLevel:   globalCfg.LogConfig.LogLevel,
		LogFile:    tmsconfig.BuildMigrationLogFileName(constants.MigrationTypeFullData, taskInfo.ChannelId, taskInfo.TaskID),
		MaxSize:    globalCfg.LogConfig.LogMaxSize,
		MaxDays:    globalCfg.LogConfig.LogMaxAge,
		MaxBackups: globalCfg.LogConfig.LogMaxBackups,
	}})

	// create database conn
	conns, buildConnErr := migrationpkg.SetUpDatabaseConnsAndCollation(ctx, detailChannelInfo, taskInfo.TaskID)
	if buildConnErr != nil {
		return buildConnErr
	}

	// 获取失败 chunk
	tableTime := time.Now()
	tableSummaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
	})
	if err != nil {
		return err
	}
	d := tableSummaries[0]

	err = models.Transaction(ctx, func(transactionCtx context.Context) error {
		if !strings.EqualFold(d.TaskStatus, common.TaskStatusRunning) {
			err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
				TaskID:      msg.TaskID,
				SchemaNameS: msg.SchemaNameS,
				TableNameS:  msg.TableNameS,
			}, map[string]interface{}{
				"Task_Status": common.TaskStatusRunning,
			})
			if err != nil {
				return err
			}
		}

		_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
			ChannelID:   taskInfo.ChannelId,
			TaskID:      msg.TaskID,
			SchemaNameS: msg.SchemaNameS,
			TableNameS:  msg.TableNameS,
			Detail:      fmt.Sprintf("t03-001:oracle table task %v begin...", d.String()),
		})
		if err != nil {
			return fmt.Errorf("create progress log detail record failed: %v", err)
		}
		return nil
	})
	if err != nil {
		return err
	}

	failedStatus := []string{common.TaskStatusFailed, constants.MigrationStatusInvalid}
	failedDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetailWithStatus(ctx, &migration.TableMigrationDetail{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
	}, failedStatus)
	if err != nil {
		return err
	}

	sourceDB, sourceDS := conns.GetSourceDB(), conns.GetSourceDS()
	targetDB, targetDS := conns.GetTargetDB(), conns.GetTargetDS()

	if len(failedDetails) > 0 {
		g := &errgroup.Group{}
		g.SetLimit(migrationParam.GetSqlThreads())
		re := regexp.MustCompile("sql \\[(?s).*] execute")

		for idx := range failedDetails {
			m := &(failedDetails[idx])
			g.Go(func() error {
				chunkTime := time.Now()

				errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
					ID: m.ID,
				}, map[string]interface{}{
					"Task_Status": common.TaskStatusRunning,
					"Duration":    time.Now().Sub(chunkTime).Seconds(),
				})
				if errU != nil {
					migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, err.Error(), constants.MigrationLogTypeFullData)
					migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)
					return fmt.Errorf("UpdateTableMigrationDetailById [%v] chunk status failed: %v", m.String(), errU)
				}

				querySQL := common.StringsBuilder(`SELECT *`, ` FROM `,
					common.StringUPPER(m.SchemaNameS), `."`, m.TableNameS, `" WHERE ROWNUM = 1`)
				columnNameS, err := sourceDB.GetOracleTableRowsColumn(
					querySQL,
					common.MigrateOracleCharsetStringConvertMapping[sourceDS.Charset],
					common.StringUPPER(targetDS.Charset))
				if err != nil {
					log.Errorf("get oracle table rows column failed, taskId:%d, querySQL:%v, err:%v", m.TaskID, querySQL, err)
					migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, err.Error(), constants.MigrationLogTypeFullData)
					migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)
					return nil
				}

				sqlStr00 := tmso2t.GenMySQLTablePrepareStmt(common.StringUPPER(m.SchemaNameT), m.TableNameT, columnNameS, migrationParam.GetInsertBatchSize(), migrationParam.GetSQLStatementType(true))
				stmt, err := targetDB.MySQLDB.PrepareContext(ctx, sqlStr00)
				if err != nil {
					log.Errorf("prepare mysql sql failed, taskId:%d, err:%v", m.TaskID, err)
					migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, err.Error(), constants.MigrationLogTypeFullData)
					migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)
					return err
				}
				defer stmt.Close()

				if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
					SchemaName: m.SchemaNameS,
					TableName:  m.TableNameS,
				}]; ok {
					if taskTableConfig.OperatorTag == "Y" {
						oracleCollation := migrationDBConns.GetOracleCollation()
						oracleCols, err := migrationpkg.GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, m.SchemaNameS, m.TableNameS, oracleCollation, taskTableConfigMap)
						if err != nil {
							log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", msg.TaskID, m.SchemaNameS, m.TableNameS, err)
							return err
						}
						m.ColumnDetailS = strings.Join(oracleCols, ",")
					}
				}

				if errm := public.IMigrate(tmso2t.NewRows(ctx, meta.FullSyncMeta{
					SchemaNameS:    m.SchemaNameS,
					TableNameS:     `"` + m.TableNameS + `"`,
					SchemaNameT:    m.SchemaNameT,
					TableNameT:     m.TableNameT,
					ConsistentRead: "NO",
					ColumnDetailS:  m.ColumnDetailS,
					ChunkDetailS:   m.ChunkDetailS,
				}, sourceDB, targetDB, stmt,
					common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(sourceDS.Charset)],
					common.StringUPPER(targetDS.Charset),
					migrationParam.GetApplyThreads(),
					migrationParam.GetInsertBatchSize(),
					migrationParam.GetCallTimeout(),
					migrationParam.GetSQLStatementType(true), columnNameS)); errm != nil {
					var (
						errMsg string
					)
					if re.MatchString(errm.Error()) {
						errMsg = re.ReplaceAllString(errMsg, "sql execute")
					} else {
						errMsg = errm.Error()
					}
					if errMsg == "" {
						errMsg = errm.Error()
					}

					migrationpkg.SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, m, errMsg, constants.MigrationLogTypeFullData)
					migrationpkg.SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, chunkTime, constants.MigrationLogTypeFullData)

					md5SqlStr := stringUtil.MD5(sqlStr00)
					log.Warnf("migrate database data chunk failed, taskId [%d], SchemaNameS [%s], TableNameS  [%s], chunk [%v], sql hash [%v], origin sql [%v], error [%v]", m.TaskID, m.SchemaNameS, m.TableNameS, m.ChunkDetailS, md5SqlStr, sqlStr00, errm.Error())

					errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
						//TaskID:       m.TaskID,
						//SchemaNameS:  m.SchemaNameS,
						//TableNameS:   m.TableNameS,
						//ChunkDetailS: m.ChunkDetailS,
						ID: m.ID,
					}, map[string]interface{}{
						"Task_Status":     common.TaskStatusFailed,
						"Origin_SQL_Hash": md5SqlStr,
						"Error_Detail":    errMsg,
						"Duration":        time.Now().Sub(chunkTime).Seconds(),
					})
					if errf != nil {
						return fmt.Errorf("get oracle schema table [%v] ITranslator failed: %v", m.String(), errf)
					}

					return nil
				}

				errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
					//TaskID:       m.TaskID,
					//SchemaNameS:  m.SchemaNameS,
					//TableNameS:   m.TableNameS,
					//ChunkDetailS: m.ChunkDetailS,
					ID: m.ID,
				}, map[string]interface{}{
					"Task_Status": common.TaskStatusSuccess,
					"Duration":    time.Now().Sub(chunkTime).Seconds(),
				})
				if errf != nil {
					return fmt.Errorf("get oracle schema table [%v] Success failed: %v", m.String(), errf)
				}
				return nil
			})
		}
		if err = g.Wait(); err != nil {
			return err
		}

		// 更新详情以及日志
		failedChunkDetails, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
			TaskID:      d.TaskID,
			SchemaNameS: d.SchemaNameS,
			TableNameS:  d.TableNameS,
			SchemaNameT: d.SchemaNameT,
			TableNameT:  d.TableNameT,
			TaskStatus:  common.TaskStatusFailed,
		})
		if err != nil {
			return err
		}
		if len(failedChunkDetails) == 0 {
			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
					&migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status":        common.TaskStatusSuccess,
						"Chunk_Success_Nums": d.ChunkTotalNums,
						"Chunk_Failed_Nums":  0,
						"Duration":           time.Now().Sub(tableTime).Seconds(),
					})
				if err != nil {
					return err
				}

				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
					ChannelID:   detailChannelInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      fmt.Sprintf("t03-001:oracle table task %v finished...", d.String()),
				})
				if err != nil {
					return fmt.Errorf("create progress log detail record failed: %v", err)
				}
				return nil
			})
			if err != nil {
				return err
			}
			//return nil
		} else {
			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
					&migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status":        common.TaskStatusFailed,
						"Chunk_Success_Nums": d.ChunkTotalNums - int64(len(failedChunkDetails)),
						"Chunk_Failed_Nums":  len(failedChunkDetails),
						"Duration":           time.Now().Sub(tableTime).Seconds(),
					})
				if err != nil {
					return err
				}
				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common2.ProgressLogDetail{
					ChannelID:   detailChannelInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      fmt.Sprintf("t03-001:oracle table task %v failed...", d.String()),
				})
				if err != nil {
					return fmt.Errorf("create progress log detail record failed: %v", err)
				}
				return nil
			})
			if err != nil {
				return err
			}
		}
	} else {
		err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(ctx, &migration.TableMigrationSummary{
			TaskID:      d.TaskID,
			SchemaNameS: d.SchemaNameS,
			TableNameS:  d.TableNameS,
		}, map[string]interface{}{
			"Task_Status": common.TaskStatusSuccess,
		})
	}

	UpdateTaskIfFullDataRetryFinish(ctx, msg.TaskID)
	return nil
}

func (s *FullDataMigrationService) GetFullDataMigrationProgressBarByTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationDataProgressResp, error) {
	var (
		totalNums      int
		successNums    int
		failedNums     int
		chunkNums      int
		successChunks  int
		failedChunks   int
		lastUpdateTime time.Time
	)

	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		summaryInterval, err := models.GetFullDataMigrationReaderWriter().GetTableMigrationSummaryByInterval(transactionCtx, &migration.TableMigrationSummary{
			TaskID: msg.TaskID,
			Entity: &common2.Entity{
				CreatedAt: msg.ProgressStartTime,
				UpdatedAt: msg.ProgressStartTime,
			},
		})
		if err != nil {
			return err
		}

		detailInterval, err := models.GetFullDataMigrationReaderWriter().GetTableMigrationDetailsByTaskId(transactionCtx, msg.TaskID)
		if err != nil {
			return err
		}
		lastUpdateTime = msg.ProgressStartTime
		totalNums = len(summaryInterval)
		for _, si := range summaryInterval {
			if strings.EqualFold(si.TaskStatus, common.TaskStatusSuccess) {
				successNums += 1
			}
			if strings.EqualFold(si.TaskStatus, common.TaskStatusFailed) || strings.EqualFold(si.TaskStatus, constants.MigrationStatusInvalid) {
				failedNums += 1
			}
			if si.UpdatedAt.After(lastUpdateTime) {
				lastUpdateTime = si.UpdatedAt
			}
		}

		chunkNums = len(detailInterval)
		for _, di := range detailInterval {
			if strings.EqualFold(di.TaskStatus, common.TaskStatusSuccess) {
				successChunks += 1
			}
			if strings.EqualFold(di.TaskStatus, common.TaskStatusFailed) || strings.EqualFold(di.TaskStatus, constants.MigrationStatusInvalid) {
				failedChunks += 1
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &message.MigrationDataProgressResp{
		TaskID:               msg.TaskID,
		TotalMigrationNums:   totalNums,
		SuccessMigrationNums: successNums,
		FailedMigrationNums:  failedNums,
		TotalChunkNums:       chunkNums,
		SuccessChunkNums:     successChunks,
		FailedChunkNums:      failedChunks,
		ProgressStartTime:    msg.ProgressStartTime,
		LastUpdateTime:       lastUpdateTime,
	}, nil
}

func (s *FullDataMigrationService) GetFullDataMigrationProgressLogByTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationDataProgressResp, error) {
	progressLogIntervals, err := models.GetProgressLogReaderWriter().GetProcessLogDetailByInterval(ctx, &common2.ProgressLogDetail{
		TaskID: msg.TaskID,
		Entity: &common2.Entity{
			CreatedAt: msg.ProgressStartTime,
			UpdatedAt: msg.ProgressStartTime,
		},
	})
	if err != nil {
		return nil, err
	}

	var progressLogs []*message.MigrationLogDetail
	for _, p := range progressLogIntervals {
		progressLogs = append(progressLogs, buildMigrationStructureLogDetailMessageFromModel(p))
	}

	return &message.MigrationDataProgressResp{
		TaskID:            msg.TaskID,
		ProgressLog:       progressLogs,
		ProgressStartTime: msg.ProgressStartTime,
	}, err
}

func (s *FullDataMigrationService) GetFullDataMigrationProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	MigReq := &message.MigrationProgressReq{
		ChannelID:         req.ChannelId,
		TaskID:            req.TaskId,
		ProgressStartTime: req.StartTime,
	}

	var BarData *message.MigrationDataProgressResp
	var taskInfo *task.Task
	var err error

	taskInfo, err = models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get task info failed, err: %v", err))
		return nil, err
	}

	isParentTask := taskInfo.ParentTaskID == 0 && tmsconfig.GetGlobalConfig().IsClusterMode()
	var progress float64
	if isParentTask {
		BarData, err = commonservice.GetFullDataMigrationProgressBarByParentTask(ctx, MigReq)
	} else {
		BarData, err = s.GetFullDataMigrationProgressBarByTask(ctx, MigReq)
	}
	if err != nil {
		log.Errorf(fmt.Sprintf("get FullDataMigration BarData data failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}

	if isParentTask {
		if BarData.TotalTaskNums == 0 {
			progress = 0
		} else {
			progress = float64(BarData.SuccessTaskNums) / float64(BarData.TotalTaskNums)
		}
	} else {
		if BarData.TotalChunkNums == 0 {
			progress = 0
		} else {
			progress = float64(BarData.SuccessChunkNums) / float64(BarData.TotalChunkNums)
		}
	}
	FullDataMigrationProgress := &message.GetTaskProgressResp{
		TaskId:           req.TaskId,
		StartTime:        req.StartTime,
		TotalNums:        BarData.TotalMigrationNums,
		SuccessNums:      BarData.SuccessMigrationNums,
		FailedNums:       BarData.FailedMigrationNums,
		LastUpdateTime:   BarData.LastUpdateTime,
		TotalChunkNums:   BarData.TotalChunkNums,
		SuccessChunkNums: BarData.SuccessChunkNums,
		TaskLogFile:      tmsconfig.BuildMigrationFullDataLogFilePath(req.ChannelId, req.TaskId),
		Progress:         stringUtil.Decimal(progress),
	}
	LogData, err := s.GetFullDataMigrationProgressLogByTask(ctx, MigReq)
	if err != nil {
		log.Errorf(fmt.Sprintf("get migration structure LogData data failed, err: %v", err))
		return FullDataMigrationProgress, err
	}

	var progressLog []*message.TaskProgressLogDetail
	logStartTime := req.StartTime
	for _, v := range LogData.ProgressLog {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.CreatedAt,
			LogMessage: v.Detail,
			LogLevel:   v.LogLevel,
		})
		if v.CreatedAt.Before(logStartTime) {
			logStartTime = v.CreatedAt
		}
	}
	FullDataMigrationProgress.ProgressLogInfo = &message.TaskProgressLogInfo{
		TaskId:        req.TaskId,
		LogStartTime:  logStartTime,
		LogCount:      len(progressLog),
		LogDetailList: progressLog,
	}

	return FullDataMigrationProgress, nil
}

func (s *FullDataMigrationService) GetFullDataMigrationSummaryByTask2(ctx context.Context, msg *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	log.Infof("start GetFullDataMigrationSummaryByTask2.")
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskId)
	if err != nil {
		return nil, err
	}
	detailChannelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, err
	}

	sumData, err := s.GetFullDataMigrationSummaryByTask(ctx, &message.MigrationTaskReq{TaskID: msg.TaskId})
	if err != nil {
		log.Errorf(fmt.Sprintf("get GetFullDataMigrationSummaryByTask failed, err: %v", err))
		return nil, err
	}
	var ChartData []message.TaskDetailChartData
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "SUCCESS",
		Count: sumData.SuccessMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.SuccessMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "FAILED",
		Count: sumData.FailedMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.FailedMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "RUNNING",
		Count: sumData.RunningMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.RunningMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})
	ChartData = append(ChartData, message.TaskDetailChartData{
		Total: sumData.TotalMigrationNums,
		Type:  "WAITING",
		Count: sumData.WaitingMigrationNums,
		Pct:   fmt.Sprintf("%.2f%%", float64(sumData.WaitingMigrationNums)/float64(sumData.TotalMigrationNums)*100),
	})

	FullBySchema := message.TaskDetailSchemaData{
		FullDataMigration: sumData.MigrationDetails,
	}

	if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		taskInfo.EndTime = time.Now()
	}
	return &message.GetTaskDetailResultResp{
		TaskId:                  msg.TaskId,
		StartTime:               taskInfo.StartTime,
		DBName:                  detailChannelInfo.DatasourceNameS,
		TotalTables:             sumData.TotalMigrationNums,
		TotalDuration:           taskInfo.EndTime.Sub(taskInfo.StartTime).String(),
		TaskDetailChartDataList: ChartData,
		TaskDetailSchemaData:    FullBySchema,
	}, nil
}

func UpdateTaskIfFullDataRetryFinish(ctx context.Context, taskId int) {
	sumData, sumErr := NewFullDataMigrationService().GetFullDataMigrationSummaryByTask(ctx, &message.MigrationTaskReq{
		TaskID: taskId,
	})
	if sumErr != nil {
		log.Errorf("GetFullDataMigrationSummaryByTask failed, task id [%v] , task status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, sumErr)
	}
	log.Infof("UpdateTaskIfFullDataRetryFinish %+v", sumData)
	//如果总数和成功数相等，把task状态修改为完成
	if sumData.TotalMigrationNums == sumData.SuccessMigrationNums {
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
			EndTime:    time.Now(),
			TaskStatus: constants.TASK_STATUS_FINISH,
		}); err != nil {
			log.Errorf("task exec success, update task id [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, err)
		}
	} else {
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
			EndTime:    time.Now(),
			TaskStatus: constants.TASK_STATUS_FAILED,
		}); err != nil {
			log.Errorf("contains chunks or summaries failed, update task id [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FAILED, err)
		}
	}
}

func UpdateTaskIfFullDataRetryFinishOrFailed(ctx context.Context, taskId int) {
	sumData, sumErr := NewFullDataMigrationService().GetFullDataMigrationSummaryByTask(ctx, &message.MigrationTaskReq{
		TaskID: taskId,
	})
	if sumErr != nil {
		log.Errorf("GetFullDataMigrationSummaryByTask failed, task id [%v] , task status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, sumErr)
		return
	}
	//如果总数和成功数相等，把task状态修改为完成
	if sumData.TotalMigrationNums == sumData.SuccessMigrationNums {
		log.Infof("UpdateTaskIfFullDataRetryFinish, update task to finish, task_id:%d", taskId)
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
			EndTime:    time.Now(),
			TaskStatus: constants.TASK_STATUS_FINISH,
		}); err != nil {
			log.Errorf("task exec success, update task id [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, err)
			return
		}
	} else {
		log.Infof("UpdateTaskIfFullDataRetryFinish, update task to failed, task_id:%d", taskId)
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
			ErrorDetail: "retry exit, still has failed tables or chunks",
			EndTime:     time.Now(),
			TaskStatus:  constants.TASK_STATUS_FAILED,
		}); err != nil {
			log.Errorf("task exec success, update task id [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, err)
			return
		}
	}
}

func (s *FullDataMigrationService) DeleteChunkData(ctx context.Context, msg *message.FetchChunkDataReq) (
	*message.FetchChunkDataResp, error) {
	taskId := msg.TaskID

	deleteErr := models.GetFullDataMigrationReaderWriter().BatchDeleteChunkDataAnalyze(ctx, taskId)
	if deleteErr != nil {
		log.Errorf("batch delete chunk data analyze failed, taskId:%d ,err:%v", taskId, deleteErr)
		return nil, deleteErr
	}

	return &message.FetchChunkDataResp{}, nil
}

func (s *FullDataMigrationService) FetchChunkData(ctx context.Context, msg *message.FetchChunkDataReq) (
	*message.FetchChunkDataResp, error) {

	// 创建获取配置
	config := &migrationpkg.ChunkFetchConfig{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		DeleteFirst: false,
	}

	// 创建编排器
	orchestrator := migrationpkg.NewChunkFetchOrchestrator(config)
	
	// 初始化编排器
	if err := orchestrator.Initialize(ctx); err != nil {
		log.Errorf("initialize chunk fetch orchestrator failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 验证是否可以执行
	if err := orchestrator.Validate(ctx); err != nil {
		log.Errorf("validate chunk fetch failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 更新状态为获取中
	if err := orchestrator.UpdateStatus(ctx, constants.ChunkDataStatusFetching); err != nil {
		log.Errorf("update status to fetching failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 异步执行获取
	orchestrator.ExecuteAsync(ctx)
	
	log.Infof("chunk data fetch started, taskId:%d, schemaNameS:%s, tableNameS:%s", 
		msg.TaskID, msg.SchemaNameS, msg.TableNameS)

	return &message.FetchChunkDataResp{}, nil
}

func (s *FullDataMigrationService) ReFetchChunkData(ctx context.Context, msg *message.FetchChunkDataReq) (
	*message.FetchChunkDataResp, error) {

	// 创建获取配置，设置DeleteFirst为true
	config := &migrationpkg.ChunkFetchConfig{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		DeleteFirst: true,  // 关键差异：先删除现有数据
	}

	// 创建编排器
	orchestrator := migrationpkg.NewChunkFetchOrchestrator(config)
	
	// 初始化编排器
	if err := orchestrator.Initialize(ctx); err != nil {
		log.Errorf("initialize chunk fetch orchestrator failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 准备重新获取（删除现有数据）
	if err := orchestrator.PrepareForReFetch(ctx); err != nil {
		log.Errorf("prepare for refetch failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 验证是否可以执行
	if err := orchestrator.Validate(ctx); err != nil {
		log.Errorf("validate chunk fetch failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 更新状态为获取中
	if err := orchestrator.UpdateStatus(ctx, constants.ChunkDataStatusFetching); err != nil {
		log.Errorf("update status to fetching failed, taskId:%d, err:%v", msg.TaskID, err)
		return nil, err
	}
	
	// 异步执行获取
	orchestrator.ExecuteAsync(ctx)
	
	log.Infof("chunk data refetch started, taskId:%d, schemaNameS:%s, tableNameS:%s", 
		msg.TaskID, msg.SchemaNameS, msg.TableNameS)

	return &message.FetchChunkDataResp{}, nil
}

func (s *FullDataMigrationService) GetChunkDataSummary(ctx context.Context, msg *message.GetChunkDataSummaryReq) (
	*message.GetChunkDataSummaryResp, error) {
	taskId := msg.TaskID
	schemaNameS := msg.SchemaNameS
	tableNameS := msg.TableNameS

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	summary, getSummaryErr := models.GetFullDataMigrationReaderWriter().GetChunkDataAnalyzeSummary(ctx, taskId, schemaNameS, tableNameS)
	if getSummaryErr != nil {
		if !strings.Contains(getSummaryErr.Error(), "record not found") {
			log.Errorf("get chunk data analyze summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, getSummaryErr)
			return nil, getSummaryErr
		}
		var saveErr error
		summary, saveErr = models.GetFullDataMigrationReaderWriter().SaveChunkDataAnalyzeSummary(ctx, &migration.ChunkDataAnalyzeSummary{
			ChannelId:   taskInfo.ChannelId,
			TaskId:      taskInfo.TaskID,
			SchemaNameS: schemaNameS,
			TableNameS:  tableNameS,
			Status:      constants.ChunkDataStatusWaiting,
		})
		if saveErr != nil {
			log.Errorf("save chunk data analyze summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, saveErr)
			return nil, saveErr
		}
	}

	count, getCountErr := models.GetFullDataMigrationReaderWriter().CountChunkDataAnalyze(ctx, taskId, schemaNameS, tableNameS)
	if getCountErr != nil {
		log.Errorf("count chunk data analyze failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, getCountErr)
		return nil, getCountErr
	}

	// 获取各状态统计信息
	statusCounts, getStatusCountsErr := models.GetFullDataMigrationReaderWriter().CountChunkDataAnalyzeByStatus(ctx, taskId, schemaNameS, tableNameS)
	if getStatusCountsErr != nil {
		log.Errorf("count chunk data analyze by status failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, getStatusCountsErr)
		return nil, getStatusCountsErr
	}

	return &message.GetChunkDataSummaryResp{
		TaskID:           taskId,
		SchemaNameS:      schemaNameS,
		TableNameS:       tableNameS,
		Status:           summary.Status,
		RowIdCount:       count,
		TotalDetails:     summary.TotalDetails,
		ProcessedDetails: summary.ProcessedDetails,
		RemainingDetails: summary.RemainingDetails,
		
		// 新增的状态统计信息
		TotalCount:       statusCounts.TotalCount,
		SuccessCount:     statusCounts.SuccessCount,
		FailedCount:      statusCounts.FailedCount,
		RunningCount:     statusCounts.RunningCount,
		WaitingCount:     statusCounts.WaitingCount,
	}, nil
}

func (s *FullDataMigrationService) ListChunkData(ctx context.Context, msg *message.ListChunkDataReq) (
	*message.ListChunkDataResp, *message.Page, error) {
	taskId := msg.TaskID
	schemaNameS := msg.SchemaNameS
	tableNameS := msg.TableNameS
	page, pageSize := msg.PageRequest.Page, msg.PageRequest.PageSize
	rowExecStatus := msg.RowExecStatus

	var data []migration.ChunkDataAnalyze
	var total int64
	var getErr error

	if rowExecStatus == "" {
		data, total, getErr = models.GetFullDataMigrationReaderWriter().ListChunkDataAnalyzePage(ctx, taskId, schemaNameS, tableNameS, page, pageSize)
	} else {
		data, total, getErr = models.GetFullDataMigrationReaderWriter().ListChunkDataAnalyzePageWithStatus(ctx, taskId, schemaNameS, tableNameS, rowExecStatus, page, pageSize)
	}
	if getErr != nil {
		log.Errorf("list chunk data analyze failed, taskId:%d, schemaNameS:%s, tableNameS:%s, page:%d, pageSize:%d, err:%v", taskId, schemaNameS, tableNameS, page, pageSize, getErr)
		return nil, nil, getErr
	}

	rsp := &message.ListChunkDataResp{}
	rsp.Data = lo.Map(data, func(item migration.ChunkDataAnalyze, _ int) message.ChunkDataAnalyze {
		return message.ChunkDataAnalyze{
			ID:           item.ID,
			ChannelId:    item.ChannelId,
			TaskId:       item.TaskId,
			SchemaNameS:  item.SchemaNameS,
			TableNameS:   item.TableNameS,
			ChunkId:      item.ChunkId,
			RowIDStr:     item.RowIDStr,
			RowIDMD5:     item.RowIDMD5,
			Status:       item.Status,
			ErrorMessage: item.ErrorMessage,
			Comment:      item.Comment,
			CreatedAt:    item.CreatedAt,
			UpdatedAt:    item.UpdatedAt,
		}
	})
	pageObj := &message.Page{
		Page:     page,
		PageSize: pageSize,
		Total:    total,
	}
	return rsp, pageObj, nil
}

func (s *FullDataMigrationService) ReplayChunkData(ctx context.Context, msg *message.ReplayChunkDataReq) (
	*message.ReplayChunkDataResp, error) {

	// 创建重放配置
	config := &migrationpkg.ChunkReplayConfig{
		TaskID:      msg.TaskId,
		ChannelID:   msg.ChannelId,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
		BatchSize:   msg.BatchSize,
		ExecuteIDs:  msg.ExecuteIds,
		ExecuteAll:  msg.ExecuteAll,
	}

	// 创建编排器
	orchestrator := migrationpkg.NewChunkReplayOrchestrator(config)
	
	// 初始化编排器
	if err := orchestrator.Initialize(ctx); err != nil {
		log.Errorf("initialize chunk replay orchestrator failed, taskId:%d, err:%v", msg.TaskId, err)
		return nil, err
	}
	
	// 验证并准备
	if err := orchestrator.ValidateAndPrepare(ctx); err != nil {
		log.Errorf("validate and prepare chunk replay failed, taskId:%d, err:%v", msg.TaskId, err)
		return nil, err
	}
	
	// 更新状态为重放中
	if err := orchestrator.UpdateStatusToReplaying(ctx); err != nil {
		log.Errorf("update status to replaying failed, taskId:%d, err:%v", msg.TaskId, err)
		return nil, err
	}
	
	// 异步执行重放
	orchestrator.ExecuteAsync(ctx)
	
	log.Infof("chunk data replay started, taskId:%d, schemaNameS:%s, tableNameS:%s", 
		msg.TaskId, msg.SchemaNameS, msg.TableNameS)

	return &message.ReplayChunkDataResp{}, nil
}

func buildChunkDataAnalyzeList(rows []map[string]string, channelInfo *channel.ChannelInformation, taskId int, schemaName string, tableName string, detail migration.TableMigrationDetail) []migration.ChunkDataAnalyze {
	var chunkDataAnalyzers []migration.ChunkDataAnalyze
	for idx := range rows {
		rowid := rows[idx]["ROWID"]
		datum := migration.ChunkDataAnalyze{
			ChannelId:   channelInfo.ChannelId,
			TaskId:      taskId,
			SchemaNameS: schemaName,
			TableNameS:  tableName,
			ChunkId:     detail.ID,
			RowIDStr:    rowid,
			RowIDMD5:    stringUtil.MD5(rowid),
			Status:      "WAITING",
		}
		chunkDataAnalyzers = append(chunkDataAnalyzers, datum)
	}
	return chunkDataAnalyzers
}

func (s *FullDataMigrationService) QueryChunkData(ctx context.Context, msg *message.QueryChunkDataReq) (
	*message.ListChunkDataResp, *message.Page, error) {

	var data []migration.ChunkDataAnalyze
	var total int64
	var getErr error

	data, total, getErr = models.GetFullDataMigrationReaderWriter().QueryChunkDataAnalyzePageWithStatus(ctx, msg)
	if getErr != nil {
		log.Errorf("query chunk data analyze failed, msg:%v, err:%v", msg, getErr)
		return nil, nil, getErr
	}

	// 获取汇总信息以获取整体状态
	summary, summaryErr := models.GetFullDataMigrationReaderWriter().GetChunkDataAnalyzeSummary(
		ctx, msg.TaskID, msg.SchemaNameS, msg.TableNameS)
	
	// 计算进度统计
	var progress *message.ChunkReplayProgress
	if summaryErr == nil && summary != nil {
		// 统计各状态的数量
		totalCount, _ := models.GetFullDataMigrationReaderWriter().CountChunkDataAnalyze(
			ctx, msg.TaskID, msg.SchemaNameS, msg.TableNameS)
		
		// TODO: 优化查询，使用 COUNT 而不是分页查询
		// 目前为了快速实现，简单设置为 0
		progress = &message.ChunkReplayProgress{
			Total:      totalCount,
			Success:    0,
			Failed:     0, 
			Processing: 0,
			StartTime:  summary.CreatedAt,
		}
		
		// 设置结束时间
		if summary.Status == constants.ChunkDataStatusReplayed {
			progress.EndTime = &summary.UpdatedAt
		}
	}

	rsp := &message.ListChunkDataResp{
		Progress: progress,
	}
	
	rsp.Data = lo.Map(data, func(item migration.ChunkDataAnalyze, _ int) message.ChunkDataAnalyze {
		return message.ChunkDataAnalyze{
			ID:           item.ID,
			ChannelId:    item.ChannelId,
			TaskId:       item.TaskId,
			SchemaNameS:  item.SchemaNameS,
			TableNameS:   item.TableNameS,
			ChunkId:      item.ChunkId,
			RowIDStr:     item.RowIDStr,
			RowIDMD5:     item.RowIDMD5,
			Status:       item.Status,
			ErrorMessage: item.ErrorMessage,
			Comment:      item.Comment,
			CreatedAt:    item.CreatedAt,
			UpdatedAt:    item.UpdatedAt,
		}
	})
	pageObj := &message.Page{
		Page:     msg.Page,
		PageSize: msg.PageSize,
		Total:    total,
	}
	return rsp, pageObj, nil
}

// ValidateSingleSummaryStatus 校验单个表是否有问题, status = INVALID
func (s *FullDataMigrationService) ValidateSingleSummaryStatus(ctx context.Context, taskId int, schemaNameS, tableNameS string) error {
	return migrationpkg.ValidateRetrySummary(ctx, taskId, schemaNameS, tableNameS)
}

// ValidateSingleSummaryStatus 校验单个表是否有问题, status = INVALID
func (s *FullDataMigrationService) ValidateMultiSummaryStatus(ctx context.Context, taskId int, schemaNameS string, tableSummaryIds []uint) error {
	if len(tableSummaryIds) == 0 {
		return nil
	}
	summaries, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryByTaskIdSummaryIds(ctx, taskId, tableSummaryIds)
	if err != nil {
		return err
	}
	log.Infof("validate multi summary status, taskId:%d, schemaNameS:%s, tableSummaryIds:%v, count:%d", taskId, schemaNameS, tableSummaryIds, len(summaries))
	for _, summary := range summaries {
		validateErr := migrationpkg.ValidateRetrySummary(ctx, taskId, schemaNameS, summary.TableNameS)
		if validateErr != nil {
			return validateErr
		}
	}
	return nil
}
