/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package migration

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/public"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/template"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"golang.org/x/sync/errgroup"
)

type Change struct {
	Ctx               context.Context               `json:"-"`
	ChannelId         int                           `json:"channel_id"`
	TaskId            int                           `json:"task_id"`
	SourceSchemaName  string                        `json:"source_schema"`
	TargetSchemaName  string                        `json:"target_schema_name"`
	SourceTables      []*channel.ChannelSchemaTable `json:"source_tables"`
	TableColRuleIds   []string                      `json:"table_col_rule_ids"`
	ColDefaultRuleIds []string                      `json:"col_default_rule_ids"`
	OracleCollation   bool                          `json:"oracle_collation"`
	Oracle            *oracle.Oracle                `json:"-"`
	SourceDBCharset   string                        `json:"source_db_charset"`
	TargetDBCharset   string                        `json:"target_db_charset"`
	TableThreads      int                           `json:"table_threads"`
}

func (r *Change) ChangeTableName() (map[string]string, error) {
	log.Debugf("ChangeTableName begin")
	tableNameRuleMap := make(map[string]string)
	for _, tr := range r.SourceTables {
		tableNameRuleMap[tr.TableNameS] = strings.ToUpper(tr.TableNameT)
	}
	log.Debugf("ChangeTableName end")
	return tableNameRuleMap, nil
}

func (r *Change) ChangeTableColumnDatatype() (map[string]map[string]string, error) {
	log.Debugf("ChangeTableColumnDatatype begin")
	tableDatatypeMap := structs.NewConcurrentMap()

	dbDatatypeNames, err := models.GetTemplateReaderWriter().GetTabColMapRules(r.Ctx, constants.DB_TYPE_ORACLE, constants.DB_TYPE_TIDB, r.TableColRuleIds)
	if err != nil {
		return make(map[string]map[string]string), err
	}

	var datatypeNames []meta.BuildinDatatypeRule
	for _, d := range dbDatatypeNames {
		datatypeNames = append(datatypeNames, meta.BuildinDatatypeRule{
			DBTypeS:       strings.ToUpper(d.DbTypeS),
			DBTypeT:       strings.ToUpper(d.DbTypeT),
			DatatypeNameS: d.ColTypeNameS,
			DatatypeNameT: d.ColTypeNameT,
		})
	}

	taskTabColCustMapRules, err := models.GetTemplateReaderWriter().GetTabcolCustMapRules(r.Ctx, &template.TabcolCustMap{
		ChannelId: r.ChannelId,
		TaskId:    r.TaskId,
	})
	if err != nil {
		return nil, err
	}

	g1 := &errgroup.Group{}
	g1.SetLimit(r.TableThreads)

	for _, sourceTable := range r.SourceTables {
		st := sourceTable
		g1.Go(func() error {
			startTime1 := time.Now()
			tableColumnINFO, err := r.Oracle.GetOracleSchemaTableColumn(r.SourceSchemaName, st.TableNameS, r.OracleCollation)
			if err != nil {
				return err
			}
			log.Debugf("GetOracleSchemaTableColumn table name :%s,OracleCollation %v, cost time: %v", st.TableNameS, r.OracleCollation, time.Now().Sub(startTime1).Milliseconds())

			columnDatatypeMap := make(map[string]string)

			for _, rowCol := range tableColumnINFO {
				originColumnType, buildInColumnType, err := OracleTableColumnMapRule(r.SourceSchemaName, st.TableNameS, public.Column{
					DataType:   rowCol["DATA_TYPE"],
					CharUsed:   rowCol["CHAR_USED"],
					CharLength: rowCol["CHAR_LENGTH"],
					ColumnInfo: public.ColumnInfo{
						DataLength:    rowCol["DATA_LENGTH"],
						DataPrecision: rowCol["DATA_PRECISION"],
						DataScale:     rowCol["DATA_SCALE"],
						NULLABLE:      rowCol["NULLABLE"],
						DataDefault:   rowCol["DATA_DEFAULT"],
						Comment:       rowCol["COMMENTS"],
					},
				}, datatypeNames)
				if err != nil {
					buildInColumnType = originColumnType
					//return err
				}

				// charset
				columnName := rowCol["COLUMN_NAME"]
				convUtf8Raw, err := common.CharsetConvert([]byte(columnName), common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(r.SourceDBCharset)], common.CharsetUTF8MB4)
				if err != nil {
					return fmt.Errorf("table column name rule [%s] charset convert failed, %v", columnName, err)
				}

				convTargetRaw, err := common.CharsetConvert(convUtf8Raw, common.CharsetUTF8MB4, common.MigrateMYSQLCompatibleCharsetStringConvertMapping[common.StringUPPER(r.TargetDBCharset)])
				if err != nil {
					return fmt.Errorf("table column name rule [%s] charset convert failed, %v", columnName, err)
				}
				columnName = string(convTargetRaw)

				tabColCustMapRules := GetTabcolCustMapRules(taskTabColCustMapRules, st.SchemaNameS, st.TableNameS, columnName)

				var colDatatypeSlice []meta.ColumnDatatypeRule
				if len(tabColCustMapRules) > 0 {
					for _, c := range tabColCustMapRules {
						colDatatypeSlice = append(colDatatypeSlice, meta.ColumnDatatypeRule{
							DBTypeS:     strings.ToUpper(constants.DB_TYPE_ORACLE),
							DBTypeT:     strings.ToUpper(constants.DB_TYPE_TIDB),
							SchemaNameS: strings.ToUpper(c.SchemaNameS),
							TableNameS:  c.TableNameS,
							ColumnNameS: strings.ToUpper(c.ColNameS),
							ColumnTypeS: strings.ToUpper(c.ColStrS),
							ColumnTypeT: strings.ToUpper(c.ColStrT),
						})
					}
				}

				if len(tabColCustMapRules) == 0 {
					columnDatatypeMap[columnName] = public.LoadDataTypeRuleUsingTableOrSchema(originColumnType, buildInColumnType, nil, nil)
				}

				columnTypeFromColumn := public.LoadColumnTypeRuleOnlyUsingColumn(columnName, originColumnType, buildInColumnType, colDatatypeSlice)

				columnTypeFromOther := public.LoadDataTypeRuleUsingTableOrSchema(originColumnType, buildInColumnType, nil, nil)

				// column or other rule check, return column type
				switch {
				case columnTypeFromColumn != buildInColumnType && columnTypeFromOther == buildInColumnType:
					columnDatatypeMap[columnName] = strings.ToUpper(columnTypeFromColumn)
				case columnTypeFromColumn != buildInColumnType && columnTypeFromOther != buildInColumnType:
					columnDatatypeMap[columnName] = strings.ToUpper(columnTypeFromColumn)
				case columnTypeFromColumn == buildInColumnType && columnTypeFromOther != buildInColumnType:
					columnDatatypeMap[columnName] = strings.ToUpper(columnTypeFromOther)
				default:
					columnDatatypeMap[columnName] = strings.ToUpper(buildInColumnType)
				}
			}
			tableDatatypeMap.Set(st.TableNameS, columnDatatypeMap)
			return nil
		})
	}
	if err = g1.Wait(); err != nil {
		return nil, err
	}

	log.Debugf("ChangeTableColumnDatatype end")

	resultMap := make(map[string]map[string]string)
	for k, v := range tableDatatypeMap.GetInternal() {
		resultMap[k] = v.(map[string]string)
	}

	return resultMap, nil
}

func (r *Change) ChangeTableColumnDefaultValue() (map[string]map[string]bool, map[string]map[string]string, error) {
	log.Debugf("ChangeTableColumnDefaultValue begin")
	//tableDefaultValMap := make(map[string]map[string]string)
	//tableDefaultValSource := make(map[string]map[string]bool)
	tableDefaultValMap := structs.NewConcurrentMap()
	tableDefaultValSource := structs.NewConcurrentMap()

	dbColDefaultMapRules, err := models.GetTemplateReaderWriter().GetColDefaultMapRules(r.Ctx, constants.DB_TYPE_ORACLE, constants.DB_TYPE_TIDB, r.ColDefaultRuleIds)
	if err != nil {
		return nil, nil, err
	}
	var defaultValueMapSlice []meta.BuildinGlobalDefaultval
	for _, d := range dbColDefaultMapRules {
		defaultValueMapSlice = append(defaultValueMapSlice, meta.BuildinGlobalDefaultval{
			DBTypeS:       strings.ToUpper(d.DbTypeS),
			DBTypeT:       strings.ToUpper(d.DbTypeT),
			DefaultValueS: d.ColTypeDefaultValueS,
			DefaultValueT: d.ColTypeDefaultValueT,
		})
	}

	taskTabColCustMapRules, err := models.GetTemplateReaderWriter().GetTabcolCustMapRules(r.Ctx, &template.TabcolCustMap{
		ChannelId: r.ChannelId,
		TaskId:    r.TaskId,
	})
	if err != nil {
		return nil, nil, err
	}

	g1 := &errgroup.Group{}
	g1.SetLimit(r.TableThreads)
	//ch1 := make(chan map[string]string, 10)
	//ch2 := make(chan map[string]bool, 10)

	for _, sourceTable := range r.SourceTables {
		st := sourceTable
		g1.Go(func() error {
			startTime1 := time.Now()
			tableColumnINFO, err := r.Oracle.GetOracleSchemaTableColumn(r.SourceSchemaName, st.TableNameS, r.OracleCollation)
			if err != nil {
				return err
			}
			log.Debugf("GetOracleSchemaTableColumn table name :%s,OracleCollation %v, cost time: %v", st.TableNameS, r.OracleCollation, time.Now().Sub(startTime1).Milliseconds())

			columnDataDefaultValMap := make(map[string]string)
			columnDataDefaultValSource := make(map[string]bool)

			for _, rowCol := range tableColumnINFO {
				// charset
				columnName := rowCol["COLUMN_NAME"]
				convUtf8Raw, err := common.CharsetConvert([]byte(columnName), common.MigrateOracleCharsetStringConvertMapping[common.StringUPPER(r.SourceDBCharset)], common.CharsetUTF8MB4)
				if err != nil {
					return fmt.Errorf("table column default value rule [%s] charset convert failed, %v", columnName, err)
				}

				convTargetRaw, err := common.CharsetConvert(convUtf8Raw, common.CharsetUTF8MB4, common.MigrateMYSQLCompatibleCharsetStringConvertMapping[common.StringUPPER(r.TargetDBCharset)])
				if err != nil {
					return fmt.Errorf("table column default value rule [%s] charset convert failed, %v", columnName, err)
				}
				columnName = string(convTargetRaw)

				tabColCustMapRules := GetTabcolCustMapRules(taskTabColCustMapRules, st.SchemaNameS, st.TableNameS, columnName)
				var custDefaultValueMapSlice []meta.BuildinColumnDefaultval
				if len(tabColCustMapRules) > 0 {
					for _, c := range tabColCustMapRules {
						custDefaultValueMapSlice = append(custDefaultValueMapSlice, meta.BuildinColumnDefaultval{
							DBTypeS:       strings.ToUpper(constants.DB_TYPE_ORACLE),
							DBTypeT:       strings.ToUpper(constants.DB_TYPE_TIDB),
							SchemaNameS:   strings.ToUpper(c.SchemaNameS),
							TableNameS:    c.TableNameS,
							ColumnNameS:   strings.ToUpper(c.ColNameS),
							DefaultValueS: c.ColDefaultvalueS,
							DefaultValueT: c.ColDefaultvalueT,
						})
					}
				}
				columnDataDefaultValSource[columnName], columnDataDefaultValMap[columnName], err = public.LoadColumnDefaultValueRule(columnName, rowCol["DATA_DEFAULT"], custDefaultValueMapSlice, defaultValueMapSlice)
				if err != nil {
					return err
				}

			}
			//ch1 <- columnDataDefaultValMap
			//ch2 <- columnDataDefaultValSource
			tableDefaultValMap.Set(st.TableNameS, columnDataDefaultValMap)
			tableDefaultValSource.Set(st.TableNameS, columnDataDefaultValSource)
			//tableDefaultValMap[strings.ToUpper(st.TableNameS)] = columnDataDefaultValMap
			//tableDefaultValSource[strings.ToUpper(st.TableNameS)] = columnDataDefaultValSource
			return nil
		})

		//tableDefaultValMap[strings.ToUpper(st.TableNameS)] = <-ch1
		//tableDefaultValSource[strings.ToUpper(st.TableNameS)] = <-ch2
	}

	if err = g1.Wait(); err != nil {
		return nil, nil, err
	}
	//defer close(ch1) // 所有协程完成后关闭通道
	//defer close(ch2) // 所有协程完成后关闭通道

	log.Debugf("ChangeTableColumnDefaultValue end")

	tableDefaultValResultMap := make(map[string]map[string]string)
	tableDefaultValSourceResultMap := make(map[string]map[string]bool)

	for k, v := range tableDefaultValMap.GetInternal() {
		tableDefaultValResultMap[k] = v.(map[string]string)
	}

	for k, v := range tableDefaultValSource.GetInternal() {
		tableDefaultValSourceResultMap[k] = v.(map[string]bool)
	}

	return tableDefaultValSourceResultMap, tableDefaultValResultMap, nil
}

func GetTabcolCustMapRules(taskTabcolCustMap []*template.TabcolCustMap, schemaNameS, tableNameS, colNameS string) []*template.TabcolCustMap {
	tabcolCustMap := make([]*template.TabcolCustMap, 0, 0)
	for _, c := range taskTabcolCustMap {
		if strings.EqualFold(c.SchemaNameS, schemaNameS) && strings.EqualFold(c.TableNameS, tableNameS) && strings.EqualFold(c.ColNameS, colNameS) {
			tabcolCustMap = append(tabcolCustMap, c)
		}
	}
	return tabcolCustMap
}
