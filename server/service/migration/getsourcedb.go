package migration

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
)

func GetSourceDB(ctx context.Context, sourceDS *datasource.Datasource) (*oracle.Oracle, error) {
	sourceDB, err := oracle.NewOracleDBEngine(ctx, config.OracleConfig{
		Username:    sourceDS.UserName,
		Password:    sourceDS.PasswordValue,
		Host:        sourceDS.HostIp,
		Port:        sourceDS.HostPort,
		ServiceName: datasourcepkg.GetServiceName(sourceDS),
		Charset:     sourceDS.Charset,
		SessionParams: []string{
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SQLTERMINATOR', true); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'PRETTY', true); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'SEGMENT_ATTRIBUTES', false); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'TABLESPACE', false); END;",
			"BEGIN DBMS_METADATA.set_transform_param (DBMS_METADATA.session_transform, 'STORAGE', false); END;",
		},
	}, "")
	if err != nil {
		return nil, fmt.Errorf("create source datasource conn failed: %v", err)
	} else {
		return sourceDB, nil
	}
}
