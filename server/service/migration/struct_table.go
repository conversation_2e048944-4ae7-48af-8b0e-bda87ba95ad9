/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package migration

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/structs"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/public"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/reverse/oracle/o2t"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

func tableReverse(serviceNameS, dbNameS, originDDL string, channelID, taskID int, t *o2t.Table, onlyTable, onlyTableIndex, onlyIndex, lowerCaseFieldNames, partitionTable string, structureParam *structs.StructureParam) (string, []*migration.TableResultDetail, []*migration.TableCompatibleDetail, error) {
	var (
		tableDDL string
		//indexKeyDDL   []string
		compatibleDDL []*migration.TableCompatibleDetail
		indexDDL      []*migration.TableResultDetail
	)
	log.Debugf("MIGRATE_STRUCTURE task id [%v] GetTableInfo start", taskID)
	i, err := t.GetTableInfo()
	if err != nil {
		return tableDDL, indexDDL, compatibleDDL, fmt.Errorf("get table info failed: %v", err.Error())
	}
	r := &o2t.Rule{
		Table:          t,
		Info:           i.(*o2t.Info),
		OnlyTable:      onlyTable,
		OnlyIndex:      onlyIndex,
		OnlyTableIndex: onlyTableIndex,
	}

	log.Debugf("MIGRATE_STRUCTURE task id [%v] GenCreateTableDDL start", taskID)
	ddl, err := r.GenCreateTableDDLMap()
	if err != nil {
		return tableDDL, indexDDL, compatibleDDL, fmt.Errorf("gen create table ddl failed: %v", err.Error())
	}

	d := ddl.(*o2t.DDL)

	switch {
	case strings.EqualFold(onlyTable, "Y") && !strings.EqualFold(onlyIndex, "Y") && !strings.EqualFold(onlyTableIndex, "Y"):
		//reverseDDL := fmt.Sprintf("%s (\n%s\n)", d.TablePrefix, strings.Join(d.TableColumns, ",\n"))
		reverseDDL := fmt.Sprintf("CREATE TABLE %s.%s (\n%s\n)",
			d.TargetSchemaName,
			d.TargetTableName,
			strings.Join(d.TableColumns, ",\n"))
		if strings.EqualFold(d.TableComment, "") {
			tableDDL = fmt.Sprintf("%s %s;", reverseDDL, d.TableSuffix)
		} else {
			tableDDL = fmt.Sprintf("%s %s %s;", reverseDDL, d.TableSuffix, d.TableComment)
		}
		tableDDL = formalPartionDDL(tableDDL, originDDL, partitionTable)

	case !strings.EqualFold(onlyTable, "Y") && strings.EqualFold(onlyIndex, "Y") && !strings.EqualFold(onlyTableIndex, "Y"):
		indexDDL, compatibleDDL = buildIndexDDL(d, t, channelID, taskID, serviceNameS, dbNameS, compatibleDDL, indexDDL, onlyTableIndex, structureParam)
	default:
		var reverseDDL string
		if len(d.TablePrimaryKeyMap) > 0 && strings.EqualFold(structureParam.CreatePkWithTable, "Y") {
			pk := ""
			for _, v := range d.TablePrimaryKeyMap {
				pk = v
			}
			reverseDDL = fmt.Sprintf("CREATE TABLE %s.%s (\n%s,\n%s\n)",
				d.TargetSchemaName,
				d.TargetTableName,
				strings.Join(d.TableColumns, ",\n"),
				pk)
		} else {
			reverseDDL = fmt.Sprintf("CREATE TABLE %s.%s (\n%s\n)",
				d.TargetSchemaName,
				d.TargetTableName,
				strings.Join(d.TableColumns, ",\n"))
		}
		if strings.EqualFold(d.TableComment, "") {
			tableDDL = fmt.Sprintf("%s %s;", reverseDDL, d.TableSuffix)
		} else {
			tableDDL = fmt.Sprintf("%s %s %s;", reverseDDL, d.TableSuffix, d.TableComment)
		}
		indexDDL, compatibleDDL = buildIndexDDL(d, t, channelID, taskID, serviceNameS, dbNameS, compatibleDDL, indexDDL, onlyTableIndex, structureParam)

		// 分区表替换，截取保留分区信息拼接
		//if strings.EqualFold(partitionTable, "Y") {
		//	partitionFirstIndex := strings.Index(strings.ReplaceAll(originDDL, "`", ""), "PARTITION BY")
		//	tableDDL = fmt.Sprintf("%v %v", tableDDL, originDDL[partitionFirstIndex:])
		//}
		tableDDL = formalPartionDDL(tableDDL, originDDL, partitionTable)

	}
	//对NOT NULL DEFAULT NULL全部处理为NOT NULL
	tableDDL = strings.ReplaceAll(tableDDL, "NOT NULL DEFAULT NULL", "NOT NULL")

	log.Debugf("MIGRATE_STRUCTURE task id [%v] tableDDL=%s, indexKeyDDL=%s, compatibleDDL=%s", taskID, tableDDL, indexDDL, compatibleDDL)
	return tableDDL, indexDDL, compatibleDDL, nil
}

func tableReverseV2(serviceNameS, dbNameS, originDDL string, channelID, taskID int, t *o2t.Table, onlyTable, onlyTableIndex, onlyIndex, lowerCaseFieldNames, partitionTable string, structureParam *structs.StructureParam, columns []*channel.TableColumnCustomMapRule) (string, []*migration.TableResultDetail, []*migration.TableCompatibleDetail, error) {
	var (
		tableDDL      string
		compatibleDDL []*migration.TableCompatibleDetail
		indexDDL      []*migration.TableResultDetail
	)
	log.Debugf("MIGRATE_STRUCTURE task id [%v] GetTableInfo start", taskID)
	i, err := t.GetTableInfoV2()
	if err != nil {
		return tableDDL, indexDDL, compatibleDDL, fmt.Errorf("get table info failed: %v", err.Error())
	}
	r := &o2t.Rule{
		Table:          t,
		Info:           i.(*o2t.Info),
		OnlyTable:      onlyTable,
		OnlyIndex:      onlyIndex,
		OnlyTableIndex: onlyTableIndex,
	}

	log.Debugf("MIGRATE_STRUCTURE task id [%v] GenCreateTableDDL start", taskID)
	ddl, err := r.GenCreateTableDDLMapV2(columns)
	if err != nil {
		return tableDDL, indexDDL, compatibleDDL, fmt.Errorf("gen create table ddl failed: %v", err.Error())
	}

	d := ddl.(*o2t.DDL)

	switch {
	case strings.EqualFold(onlyTable, "Y") && !strings.EqualFold(onlyIndex, "Y") && !strings.EqualFold(onlyTableIndex, "Y"):
		//reverseDDL := fmt.Sprintf("%s (\n%s\n)", d.TablePrefix, strings.Join(d.TableColumns, ",\n"))
		reverseDDL := fmt.Sprintf("CREATE TABLE %s.%s (\n%s\n)",
			d.TargetSchemaName,
			d.TargetTableName,
			strings.Join(d.TableColumns, ",\n"))
		if strings.EqualFold(d.TableComment, "") {
			tableDDL = fmt.Sprintf("%s %s;", reverseDDL, d.TableSuffix)
		} else {
			tableDDL = fmt.Sprintf("%s %s %s;", reverseDDL, d.TableSuffix, d.TableComment)
		}
		tableDDL = formalPartionDDL(tableDDL, originDDL, partitionTable)

	case !strings.EqualFold(onlyTable, "Y") && strings.EqualFold(onlyIndex, "Y") && !strings.EqualFold(onlyTableIndex, "Y"):
		indexDDL, compatibleDDL = buildIndexDDL(d, t, channelID, taskID, serviceNameS, dbNameS, compatibleDDL, indexDDL, onlyTableIndex, structureParam)
	default:
		var reverseDDL string
		if len(d.TablePrimaryKeyMap) > 0 && strings.EqualFold(structureParam.CreatePkWithTable, "Y") {
			pk := ""
			for _, v := range d.TablePrimaryKeyMap {
				pk = v
			}
			reverseDDL = fmt.Sprintf("CREATE TABLE %s.%s (\n%s,\n%s\n)",
				d.TargetSchemaName,
				d.TargetTableName,
				strings.Join(d.TableColumns, ",\n"),
				pk)
		} else {
			reverseDDL = fmt.Sprintf("CREATE TABLE %s.%s (\n%s\n)",
				d.TargetSchemaName,
				d.TargetTableName,
				strings.Join(d.TableColumns, ",\n"))
		}
		if strings.EqualFold(d.TableComment, "") {
			tableDDL = fmt.Sprintf("%s %s;", reverseDDL, d.TableSuffix)
		} else {
			tableDDL = fmt.Sprintf("%s %s %s;", reverseDDL, d.TableSuffix, d.TableComment)
		}
		indexDDL, compatibleDDL = buildIndexDDL(d, t, channelID, taskID, serviceNameS, dbNameS, compatibleDDL, indexDDL, onlyTableIndex, structureParam)

		// 分区表替换，截取保留分区信息拼接
		//if strings.EqualFold(partitionTable, "Y") {
		//	partitionFirstIndex := strings.Index(strings.ReplaceAll(originDDL, "`", ""), "PARTITION BY")
		//	tableDDL = fmt.Sprintf("%v %v", tableDDL, originDDL[partitionFirstIndex:])
		//}
		tableDDL = formalPartionDDL(tableDDL, originDDL, partitionTable)

	}
	//对NOT NULL DEFAULT NULL全部处理为NOT NULL
	tableDDL = strings.ReplaceAll(tableDDL, "NOT NULL DEFAULT NULL", "NOT NULL")

	log.Debugf("MIGRATE_STRUCTURE task id [%v] tableDDL=%s, indexKeyDDL=%s, compatibleDDL=%s", taskID, tableDDL, indexDDL, compatibleDDL)
	return tableDDL, indexDDL, compatibleDDL, nil
}

func buildIndexDDL(d *o2t.DDL, t *o2t.Table, channelID, taskID int, serviceNameS, dbNameS string, compatibleDDL []*migration.TableCompatibleDetail, indexDDL []*migration.TableResultDetail, onlyTableIndex string, structureParam *structs.StructureParam) ([]*migration.TableResultDetail, []*migration.TableCompatibleDetail) {
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableCheckKeysMap, "CHECK KEY", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableForeignKeysMap, "FOREIGN KEY", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableFunctionBasedNormalIndexMap, "FUNCTION BASED NORMAL INDEX", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableBitmapIndexMap, "BITMAP INDEX", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableFunctionBasedBitmapIndexMap, "FUNCTION BASED BITMAP INDEX", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableDomainIndexMap, "DOMAIN INDEX", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)
	compatibleDDL = buildTableCompatibleDetailForIndexMap(d, t, d.TableNormalRevIndexMap, "NORMAL/REV INDEX", channelID, taskID, serviceNameS, dbNameS, compatibleDDL)

	indexDDL = buildTableResultDetailForIndexMap(d, t, d.TableUniqueKeysMap, "UNIQUE KEYS", channelID, taskID, serviceNameS, dbNameS, indexDDL)
	indexDDL = buildTableResultDetailForIndexMap(d, t, d.TableUniqueIndexMap, "UNIQUE INDEX", channelID, taskID, serviceNameS, dbNameS, indexDDL)
	indexDDL = buildTableResultDetailForIndexMap(d, t, d.TableNormalIndexMap, "NORMAL INDEX", channelID, taskID, serviceNameS, dbNameS, indexDDL)
	if !strings.EqualFold(onlyTableIndex, "Y") || !strings.EqualFold(structureParam.CreatePkWithTable, "Y") { //如果选择：迁移表结构和索引(缺省)并且参数create_pk_with_table为Y，那么主键不单独创建;其他场景主键单独创建
		indexDDL = buildTableResultDetailForIndexMap(d, t, d.TablePrimaryKeyMap, "PRIMARY KEY", channelID, taskID, serviceNameS, dbNameS, indexDDL)
	}
	return indexDDL, compatibleDDL
}

func buildTableCompatibleDetailForIndexMap(d *o2t.DDL, t *o2t.Table, indexMap map[string]string, indexTypeS string, channelID, taskID int, serviceNameS, dbNameS string, compatibleDDL []*migration.TableCompatibleDetail) []*migration.TableCompatibleDetail {
	if len(indexMap) > 0 {
		for keyName, ck := range indexMap {
			originIndexDDL := fmt.Sprintf("ALTER TABLE `%s`.`%s` ADD %s;",
				d.SourceSchemaName, d.SourceTableName, ck)

			compatibleDDL = append(compatibleDDL, &migration.TableCompatibleDetail{
				ChannelID:        channelID,
				TaskID:           taskID,
				ServiceNameS:     serviceNameS,
				DBNameS:          dbNameS,
				SchemaNameS:      t.SourceSchemaName,
				TableNameS:       t.SourceTableName,
				ObjectNameS:      keyName,
				TableTypeS:       indexTypeS,
				SchemaNameT:      t.TargetSchemaName,
				TableNameT:       t.TargetTableName,
				Status:           constants.TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE,
				TableTypeT:       indexTypeS,
				OriginDDL:        originIndexDDL,
				CompatibleDDL:    fmt.Sprintf("-- current object type %s isn't compatible, please check", indexTypeS),
				CompatibleDetail: "",
			})
		}
	}
	log.Debugf("buildTableCompatibleDetailForIndexMap task id [%v] compatibleDDL=%s", taskID, compatibleDDL)
	return compatibleDDL
}

func buildTableResultDetailForIndexMap(d *o2t.DDL, t *o2t.Table, indexMap map[string]string, indexTypeS string, channelID, taskID int, serviceNameS, dbNameS string, indexDDL []*migration.TableResultDetail) []*migration.TableResultDetail {
	if len(indexMap) > 0 {
		for keyName, ck := range indexMap {
			originIndexDDL := fmt.Sprintf("ALTER TABLE `%s`.`%s` ADD %s;",
				d.SourceSchemaName, d.SourceTableName, ck)
			reverseDDL := fmt.Sprintf("ALTER TABLE `%s`.`%s` ADD %s;",
				d.TargetSchemaName, d.TargetTableName, ck)

			indexDDL = append(indexDDL, &migration.TableResultDetail{
				ChannelID:    channelID,
				TaskID:       taskID,
				ServiceNameS: serviceNameS,
				DBNameS:      dbNameS,
				Status:       constants.TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING,
				SchemaNameS:  t.SourceSchemaName,
				TableNameS:   t.SourceTableName,
				ObjectNameS:  keyName,
				TableTypeS:   indexTypeS,
				SchemaNameT:  t.TargetSchemaName,
				TableNameT:   t.TargetTableName,
				TableTypeT:   indexTypeS,
				OriginDDL:    originIndexDDL,
				ReverseDDL:   reverseDDL,
			})
		}
	}
	log.Debugf("buildTableResultDetailForIndexMap task id [%v] indexDDL=%s", taskID, indexDDL)
	return indexDDL
}

func formalPartionDDL(tableDDL, originDDL, partitionTable string) string {
	log.Debugf("formalPartionDDL partitionTable=%s", partitionTable)
	log.Debugf("formalPartionDDL originDDL=%s", originDDL)
	log.Debugf("formalPartionDDL tableDDL=%s", tableDDL)
	if !strings.EqualFold(partitionTable, "N") && !strings.EqualFold(partitionTable, "") {
		partitionFirstIndex := strings.Index(strings.ReplaceAll(originDDL, "`", ""), "PARTITION BY")
		if partitionFirstIndex == -1 {
			// 没有分区信息，直接返回
			return tableDDL
		}
		partitionDDL := originDDL[partitionFirstIndex:]
		log.Debugf("formalPartionDDL partitionDDL0=%s", partitionDDL)

		// // 去除SUBPARTITION
		// pattern := `(?i)SUBPARTITION BY\s+[^;]+`
		// reg := regexp.MustCompile(pattern)
		// partitionDDL = reg.ReplaceAllStringFunc(partitionDDL, func(s string) string {
		// 	return ""
		// })

		log.Debugf("formalPartionDDL partitionDDL1=%s", partitionDDL)

		switch strings.ToUpper(partitionTable) {
		case "HASH":
			// 改进：自动统计分区数量并补全 PARTITIONS n（只统计括号内的分区项）
			// 1. 提取 PARTITION BY HASH (...)
			pattern := `(?i)PARTITION\s+BY\s+HASH\s*\([^)]*\)`
			reg := regexp.MustCompile(pattern)
			match := reg.FindString(partitionDDL)
			if match != "" {
				// 2. 只统计括号内的分区项
				start := strings.Index(partitionDDL, "(")
				end := strings.LastIndex(partitionDDL, ")")
				partCount := 0
				if start != -1 && end != -1 && end > start {
					partDefs := partitionDDL[start+1 : end]
					partReg := regexp.MustCompile(`(?i)PARTITION\s+\w+`)
					partCount = len(partReg.FindAllString(partDefs, -1))
				}
				if partCount > 0 {
					partitionDDL = fmt.Sprintf("%s PARTITIONS %d", match, partCount)
				} else {
					partitionDDL = match
				}
			}
			partitionDDL = strings.TrimSuffix(partitionDDL, ";")
			// TiDB语法：PARTITION BY HASH (col) PARTITIONS n
			pattern = `;`
			reg = regexp.MustCompile(pattern)
			tableDDL = reg.ReplaceAllString(tableDDL, "")
			log.Debugf("formalPartionDDL return %v , %v", tableDDL, partitionDDL)
			return fmt.Sprintf("%v %v", tableDDL, partitionDDL)
		case "RANGE":
			// 1. 提取分区字段
			fieldReg := regexp.MustCompile(`(?i)PARTITION\s+BY\s+RANGE\s*\(([^)]+)\)`)
			fieldMatch := fieldReg.FindStringSubmatch(originDDL)
			partitionField := "TIME_ID"
			if len(fieldMatch) > 1 {
				partitionField = strings.TrimSpace(fieldMatch[1])
			}

			// 2. 提取所有一级分区（忽略SUBPARTITION内容）
			partReg := regexp.MustCompile(`(?i)PARTITION\s+(\w+)\s+VALUES\s+LESS\s+THAN\s*\(TO_DATE\('([^']+)'`)
			matches := partReg.FindAllStringSubmatch(originDDL, -1)
			var partitionItems []string
			for _, m := range matches {
				name := m[1]
				date := strings.TrimSpace(m[2])
				partitionItems = append(partitionItems, fmt.Sprintf("PARTITION %s VALUES LESS THAN (UNIX_TIMESTAMP('%s'))", name, date))
			}

			if len(partitionItems) > 0 {
				partitionDDL = fmt.Sprintf("PARTITION BY RANGE(UNIX_TIMESTAMP(%s)) (\n    %s\n)", partitionField, strings.Join(partitionItems, ",\n    "))
				partitionDDL = strings.TrimSpace(partitionDDL)
				pattern := `;`
				reg := regexp.MustCompile(pattern)
				tableDDL = reg.ReplaceAllString(tableDDL, "")
				log.Debugf("formalPartionDDL return %v , %v", tableDDL, partitionDDL)
				return fmt.Sprintf("%v %v", tableDDL, partitionDDL)
			}
			// 检查是否为二级分区（SUBPARTITION结构）
			if strings.Contains(strings.ToUpper(partitionDDL), "SUBPARTITION") {
				// 提取所有SUBPARTITION分区项
				subReg := regexp.MustCompile(`(?i)SUBPARTITION\s+([\w\d_]+)\s+VALUES\s+LESS\s+THAN\s*\(TO_DATE\('([^']+)'`)
				matches := subReg.FindAllStringSubmatch(partitionDDL, -1)
				var partitionItems []string
				for _, m := range matches {
					name := m[1]
					date := strings.TrimSpace(m[2])
					partitionItems = append(partitionItems, fmt.Sprintf("PARTITION %s VALUES LESS THAN ('%s')", name, date))
				}
				if len(partitionItems) > 0 {
					// 用正则提取分区字段
					fieldReg := regexp.MustCompile(`(?i)PARTITION\s+BY\s+RANGE\s*\(([^)]+)\)`)
					fieldMatch := fieldReg.FindStringSubmatch(originDDL)
					partitionField := "TIME_ID"
					if len(fieldMatch) > 1 {
						partitionField = strings.TrimSpace(fieldMatch[1])
					}
					partitionDDL = fmt.Sprintf("PARTITION BY RANGE (%s) (\n    %s\n)", partitionField, strings.Join(partitionItems, ",\n    "))
					partitionDDL = strings.TrimSpace(partitionDDL)
					pattern := `;`
					reg := regexp.MustCompile(pattern)
					tableDDL = reg.ReplaceAllString(tableDDL, "")
					log.Debugf("formalPartionDDL return %v , %v", tableDDL, partitionDDL)
					return fmt.Sprintf("%v %v", tableDDL, partitionDDL)
				}
			}
			// 保留 PARTITION BY RANGE (col) (PARTITION ... VALUES LESS THAN ...)
			// 去除INTERVAL、SUBPARTITION等
			// 1. 去除INTERVAL
			pattern := `(?i)INTERVAL\s+[^)]+\)`
			reg := regexp.MustCompile(pattern)
			partitionDDL = reg.ReplaceAllString(partitionDDL, "")
			// 2. 提取PARTITION BY RANGE...到第一个右括号，支持PARTITION BY和RANGE之间有任意空格或换行
			reg = regexp.MustCompile(`(?i)PARTITION\s+BY\s+RANGE\s*\([^)]+\)`)
			loc := reg.FindStringIndex(partitionDDL)
			var match2 string
			if loc != nil {
				start := loc[0]
				rest := partitionDDL[start:]
				endIdx := strings.Index(rest, ")")
				if endIdx != -1 {
					// 找到第一个右括号后，继续查找后面是否有分区定义
					// 例如 PARTITION BY RANGE (...) (PARTITION ...)
					// 需要包含后面的分区定义
					// 先找到第一个右括号
					prefix := rest[:endIdx+1]
					// 查找第一个右括号后面的内容
					remain := rest[endIdx+1:]
					// 查找第一个分号或结尾
					semicolonIdx := strings.Index(remain, ";")
					if semicolonIdx != -1 {
						match2 = prefix + remain[:semicolonIdx]
					} else {
						match2 = prefix + remain
					}
				} else {
					match2 = rest
				}
				partitionDDL = match2
			}
			partitionDDL = strings.TrimSuffix(partitionDDL, ";")

			// 仅当originDDL中包含VALUES LESS THAN (TO_DATE(，且中间空格数不确定时，才做日期分区转换
			datePartitionPattern := regexp.MustCompile(`(?i)VALUES\s+LESS\s+THAN\s*\(\s*TO_DATE\s*\(`)
			if datePartitionPattern.MatchString(originDDL) {
				// 3. 替换分区表达式为UNIX_TIMESTAMP
				reg = regexp.MustCompile(`(?i)PARTITION\s+BY\s+RANGE\s*\(([^)]+)\)`)
				partitionDDL = reg.ReplaceAllString(partitionDDL, "PARTITION BY RANGE(UNIX_TIMESTAMP($1))")

				// 4. 替换VALUES LESS THAN (TO_DATE(...))为UNIX_TIMESTAMP
				reg = regexp.MustCompile(`(?i)VALUES\s+LESS\s+THAN\s+\(TO_DATE\('([^']+)'[^)]*\)\)`)
				partitionDDL = reg.ReplaceAllStringFunc(partitionDDL, func(s string) string {
					// 提取日期字符串
					subReg := regexp.MustCompile(`TO_DATE\('([^']+)'`)
					matches := subReg.FindStringSubmatch(s)
					if len(matches) > 1 {
						return "VALUES LESS THAN (UNIX_TIMESTAMP('" + matches[1] + "'))"
					}
					return s
				})
			}

			// 5. 去除多余空格和分号
			partitionDDL = strings.TrimSpace(partitionDDL)
			pattern = `;`
			reg = regexp.MustCompile(pattern)
			tableDDL = reg.ReplaceAllString(tableDDL, "")
			log.Debugf("formalPartionDDL return %v , %v", tableDDL, partitionDDL)
			return fmt.Sprintf("%v %v", tableDDL, partitionDDL)
		case "LIST":
			pattern := `(?i)VALUES[\s]*\([\s]*'`
			reg := regexp.MustCompile(pattern)
			if reg.MatchString(partitionDDL) {
				partitionTable = "LIST COLUMNS"
			}
			log.Debugf("formalPartionDDL partitionDDL2=%s", partitionDDL)
			pattern = `(?i)PARTITION\s+BY[^(]+`
			reg = regexp.MustCompile(pattern)
			partitionDDL = reg.ReplaceAllStringFunc(partitionDDL, func(s string) string {
				return fmt.Sprintf("PARTITION BY %s", partitionTable)
			})
			log.Debugf("formalPartionDDL partitionDDL3=%s", partitionDDL)
			pattern = `(?i)VALUES\s+LESS\s+THAN`
			reg = regexp.MustCompile(pattern)
			if !reg.MatchString(partitionDDL) {
				// 正则表达式匹配，使用断言确保不匹配到 "VALUES LESS THAN"，把VALUES替换为VALUES IN，考虑多个空格，大小写不敏感
				re := regexp.MustCompile(`(?i)\bVALUES\b`)
				partitionDDL = re.ReplaceAllString(partitionDDL, "VALUES IN")
			}
			log.Debugf("formalPartionDDL partitionDDL4=%s", partitionDDL)
			pattern = `;`
			reg = regexp.MustCompile(pattern)
			tableDDL = reg.ReplaceAllString(tableDDL, "")
			log.Debugf("formalPartionDDL return %v , %v", tableDDL, partitionDDL)
			return fmt.Sprintf("%v %v", tableDDL, partitionDDL)
		default:
			// 其他分区类型，按原有逻辑处理
			pattern := `(?i)PARTITION\s+BY[^(]+`
			reg := regexp.MustCompile(pattern)
			partitionDDL = reg.ReplaceAllStringFunc(partitionDDL, func(s string) string {
				return fmt.Sprintf("PARTITION BY %s", partitionTable)
			})
			pattern = `;`
			reg = regexp.MustCompile(pattern)
			tableDDL = reg.ReplaceAllString(tableDDL, "")
			log.Debugf("formalPartionDDL return %v , %v", tableDDL, partitionDDL)
			return fmt.Sprintf("%v %v", tableDDL, partitionDDL)
		}
	} else {
		pattern := `(?i)SUBPARTITION BY\s+[^;]+`
		reg := regexp.MustCompile(pattern)
		tableDDL = reg.ReplaceAllStringFunc(tableDDL, func(s string) string {
			return ""
		})

		pattern = `(?i)PARTITION\s+BY\s+[^;]+`
		reg = regexp.MustCompile(pattern)
		tableDDL = reg.ReplaceAllString(tableDDL, "")

		log.Debugf("formalPartionDDL return %v ", tableDDL)
		return tableDDL
	}
}

func filterSubPartition(tableDDL string) string {
	// 1. 定义正则表达式，用于匹配并移除 "SUBPARTITION BY RANGE (TIME_ID)" 这一行。
	// (?m) 开启多行模式，使 ^ 和 $ 匹配每行的开始和结束。
	reSubpartitionBy := regexp.MustCompile(`(?m)^\s*SUBPARTITION BY RANGE \(TIME_ID\)\s*$`)
	result := reSubpartitionBy.ReplaceAllString(tableDDL, "")

	// 2. 定义正则表达式，用于匹配并移除每个 PARTITION 块内部的整个 SUBPARTITION 内容。
	// 这个正则表达式会匹配：
	// `\(`: 匹配子分区列表的开括号。
	// `\s*`: 可选的空白字符。
	// `(SUBPARTITION[\s\S]*?\)\s*,?\s*)+`: 匹配一个或多个 SUBPARTITION 条目。
	//    - `SUBPARTITION`: 匹配 "SUBPARTITION" 关键字。
	//    - `[\s\S]*?`: 非贪婪地匹配任何字符（包括换行符），直到遇到下一个匹配项。
	//    - `\)`: 匹配单个子分区的闭括号。
	//    - `\s*,?\s*`: 可选的空白字符，可选的逗号，可选的空白字符。
	// `\s*\)`: 匹配子分区列表的闭括号。
	// `\s*\)`: 匹配主分区定义的闭括号。
	// 这样可以匹配到 `( SUBPARTITION ... ) )` 整个子分区定义块，包括其外部的括号。
	reSubpartitionContent := regexp.MustCompile(`\(\s*(SUBPARTITION[\s\S]*?\)\s*,?\s*)+\s*\)\s*\)`)
	result = reSubpartitionContent.ReplaceAllString(result, ")") // 将匹配到的整个子分区块替换为单个闭括号

	// 可选：进一步清理多余的空行，使输出更紧凑。
	// 替换连续的两个或更多换行符为一个换行符
	reMultipleNewlines := regexp.MustCompile(`\n{2,}`)
	result = reMultipleNewlines.ReplaceAllString(result, "\n")

	// 移除每行首尾的空白字符，并重新拼接
	lines := strings.Split(result, "\n")
	trimmedLines := make([]string, 0, len(lines))
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine != "" { // 只添加非空行
			trimmedLines = append(trimmedLines, trimmedLine)
		}
	}
	result = strings.Join(trimmedLines, "\n")

	return result
}

func OracleTableColumnMapRule(sourceSchema, sourceTable string, column public.Column, buildinDatatypes []meta.BuildinDatatypeRule) (string, string, error) {
	var (
		originColumnType  string
		buildInColumnType string
	)

	dataLength, err := strconv.Atoi(column.DataLength)
	if err != nil {
		return originColumnType, buildInColumnType, fmt.Errorf("oracle schema [%s] table [%s] reverser column data_length string to int failed: %v", sourceSchema, sourceTable, err)
	}
	dataPrecision, err := strconv.Atoi(column.DataPrecision)
	if err != nil {
		return originColumnType, buildInColumnType, fmt.Errorf("oracle schema [%s] table [%s] reverser column data_precision string to int failed: %v", sourceSchema, sourceTable, err)
	}
	dataScale, err := strconv.Atoi(column.DataScale)
	if err != nil {
		return originColumnType, buildInColumnType, fmt.Errorf("oracle schema [%s] table [%s] reverser column data_scale string to int failed: %v", sourceSchema, sourceTable, err)
	}

	buildinDatatypeMap := make(map[string]string)
	numberDatatypeMap := make(map[string]struct{})

	for _, b := range buildinDatatypes {
		buildinDatatypeMap[common.StringUPPER(b.DatatypeNameS)] = b.DatatypeNameT

		if strings.EqualFold(common.StringUPPER(b.DatatypeNameS), common.BuildInOracleDatatypeNumber) {
			for _, c := range strings.Split(b.DatatypeNameT, "/") {
				numberDatatypeMap[common.StringUPPER(c)] = struct{}{}
			}
		}
	}

	switch common.StringUPPER(column.DataType) {
	case common.BuildInOracleDatatypeNumber:
		maxDecimalP := 62
		maxDecimalS := 26
		if dataTypeS, ok := buildinDatatypeMap[common.BuildInOracleDatatypeNumber]; ok {
			switch {
			case dataScale > 0:
				switch {
				case dataPrecision == 38 && dataScale == 127:
					originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
					if _, ok = numberDatatypeMap["DECIMAL"]; ok {
						if strings.EqualFold(dataTypeS, "BIGINT/DECIMAL/DECIMAL(p,0)") {
							//国寿财的定制需求，number字段不带精度，默认是number(38,127)，要求转为decimal(38,0)
							buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", dataPrecision, 0)
						} else if strings.EqualFold(dataTypeS, "BIGINT/DECIMAL/BIGINT") {
							//中移互的定制需求，number字段不带精度，默认是number(38,127)，要求转为BIGINT
							buildInColumnType = "BIGINT"
						} else {
							buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", maxDecimalP, maxDecimalS)
						}
					} else {
						return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
					}
				case dataPrecision == 38 && dataScale > maxDecimalS:
					originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
					if _, ok = numberDatatypeMap["DECIMAL"]; ok {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", dataPrecision, maxDecimalS)
					} else {
						return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
					}
				case dataPrecision == 38 && dataScale <= maxDecimalS:
					originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
					if _, ok = numberDatatypeMap["DECIMAL"]; ok {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", dataPrecision, dataScale)
					} else {
						return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
					}
				default:
					if dataScale <= maxDecimalS {
						originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
						if _, ok = numberDatatypeMap["DECIMAL"]; ok {
							buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", dataPrecision, dataScale)
						} else {
							return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
						}
					} else {
						originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
						if _, ok = numberDatatypeMap["DECIMAL"]; ok {
							buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", dataPrecision, maxDecimalS)
						} else {
							return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
						}
					}
				}
			case dataScale == 0:
				//国寿财的定制需求，number(p,0)，要求转为decimal(p,0)
				if strings.EqualFold(dataTypeS, "BIGINT/DECIMAL/DECIMAL(p,0)") {
					if dataPrecision <= 38 {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", dataPrecision, 0)
					} else {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", maxDecimalP, 0)
					}
					return originColumnType, buildInColumnType, nil
				}
				switch {
				case dataPrecision >= 1 && dataPrecision < 19:
					originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
					if _, ok = numberDatatypeMap["BIGINT"]; ok {
						buildInColumnType = "BIGINT"
					} else if _, ok = numberDatatypeMap["DECIMAL"]; ok {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d)", dataPrecision)
					} else {
						return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [BIGINT]", originColumnType)
					}
				case dataPrecision >= 19 && dataPrecision <= 38:
					originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
					if _, ok = numberDatatypeMap["DECIMAL"]; ok {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d)", dataPrecision)
					} else {
						return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
					}
				default:
					originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumber, dataPrecision, dataScale)
					if _, ok = numberDatatypeMap["DECIMAL"]; ok {
						buildInColumnType = fmt.Sprintf("DECIMAL(%d,%d)", maxDecimalP, dataScale)
					} else {
						return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin mapping data type [DECIMAL]", originColumnType)
					}
				}
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeNumber)
		}
	case common.BuildInOracleDatatypeBfile:
		originColumnType = common.BuildInOracleDatatypeBfile
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeBfile]; ok {
			buildInColumnType = common.StringUPPER(val)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeBfile)
		}
	case common.BuildInOracleDatatypeChar:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeChar]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeChar, column.CharLength)
				// 将字符串转换为整数
				length, err := strconv.Atoi(column.CharLength)
				if err != nil {
					log.Errorf("common.BuildInOracleDatatypeChar => column.CharLength转换失败:%v", err)
					length = 255
				}
				if strings.EqualFold(val, "CHAR") && length > 255 {
					buildInColumnType = fmt.Sprintf("%s(%s)", "VARCHAR", column.CharLength)
				} else {
					buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
				}
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeChar, dataLength)
				if strings.EqualFold(val, "CHAR") && dataLength > 255 {
					buildInColumnType = fmt.Sprintf("%s(%d)", "VARCHAR", dataLength)
				} else {
					buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
				}
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeChar)
		}
	case common.BuildInOracleDatatypeCharacter:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeCharacter]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeCharacter, column.CharLength)
				buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeCharacter, dataLength)
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeCharacter)
		}
	case common.BuildInOracleDatatypeClob:
		originColumnType = common.BuildInOracleDatatypeClob
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeClob]; ok {
			buildInColumnType = common.StringUPPER(val)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeClob)
		}
	case common.BuildInOracleDatatypeBlob:
		originColumnType = common.BuildInOracleDatatypeBlob
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeBlob]; ok {
			buildInColumnType = common.StringUPPER(val)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeBlob)
		}
	case common.BuildInOracleDatatypeDate:
		originColumnType = common.BuildInOracleDatatypeDate
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeDate]; ok {
			buildInColumnType = common.StringUPPER(val)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeDate)
		}
	case common.BuildInOracleDatatypeDecimal:
		originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeDecimal, dataPrecision, dataScale)
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeDecimal]; ok {
			buildInColumnType = fmt.Sprintf("%s(%d,%d)", common.StringUPPER(val), dataPrecision, dataScale)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeDecimal)
		}
	case common.BuildInOracleDatatypeDec:
		originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeDecimal, dataPrecision, dataScale)
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeDec]; ok {
			buildInColumnType = fmt.Sprintf("%s(%d,%d)", common.StringUPPER(val), dataPrecision, dataScale)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeDec)
		}
	case common.BuildInOracleDatatypeDoublePrecision:
		originColumnType = common.BuildInOracleDatatypeDoublePrecision
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeDoublePrecision]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeDoublePrecision)
		}
	case common.BuildInOracleDatatypeFloat:
		originColumnType = common.BuildInOracleDatatypeFloat
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeFloat]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeFloat)
		}
	case common.BuildInOracleDatatypeInteger:
		originColumnType = common.BuildInOracleDatatypeInteger
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeInteger]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeInteger)
		}
	case common.BuildInOracleDatatypeInt:
		originColumnType = common.BuildInOracleDatatypeInteger
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeInt]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeInt)
		}
	case common.BuildInOracleDatatypeLong:
		originColumnType = common.BuildInOracleDatatypeLong
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeLong]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeLong)
		}
	case common.BuildInOracleDatatypeLongRAW:
		originColumnType = common.BuildInOracleDatatypeLongRAW
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeLongRAW]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeLongRAW)
		}
	case common.BuildInOracleDatatypeBinaryFloat:
		originColumnType = common.BuildInOracleDatatypeBinaryFloat
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeBinaryFloat]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeBinaryFloat)
		}
	case common.BuildInOracleDatatypeBinaryDouble:
		originColumnType = common.BuildInOracleDatatypeBinaryDouble
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeBinaryDouble]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeBinaryDouble)
		}
	case common.BuildInOracleDatatypeNchar:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeNchar]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeNchar, column.CharLength)
				buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeNchar, dataLength)
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeNchar)
		}
	case common.BuildInOracleDatatypeNcharVarying:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeNcharVarying]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeNcharVarying, column.CharLength)
				buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeNcharVarying, dataLength)
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeNcharVarying)
		}
	case common.BuildInOracleDatatypeNclob:
		originColumnType = common.BuildInOracleDatatypeNclob
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeNclob]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeNclob)
		}
	case common.BuildInOracleDatatypeNumeric:
		originColumnType = fmt.Sprintf("%s(%d,%d)", common.BuildInOracleDatatypeNumeric, dataPrecision, dataScale)
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeNumeric]; ok {
			buildInColumnType = fmt.Sprintf("%s(%d,%d)", common.StringUPPER(val), dataPrecision, dataScale)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeNumeric)
		}
	case common.BuildInOracleDatatypeNvarchar2:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeNvarchar2]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeNvarchar2, column.CharLength)
				buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeNvarchar2, dataLength)
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeNvarchar2)
		}
	case common.BuildInOracleDatatypeRaw:
		originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeRaw, dataLength)
		// Fixed: MySQL Binary 数据类型定长，长度不足补 0x00, 容易导致数据对比不一致，统一使用 Varbinary 数据类型
		//if dataLength < 256 {
		//	buildInColumnType = fmt.Sprintf("BINARY(%d)", dataLength)
		//} else {
		//	buildInColumnType = fmt.Sprintf("VARBINARY(%d)", dataLength)
		//}

		// 使用正则表达式来匹配括号内任意数字的模式，来判断是否是定长类型
		re := regexp.MustCompile(`\(\d+\)`)

		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeRaw]; ok {
			if strings.EqualFold(val, "LONGBLOB") {
				buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			} else if re.MatchString(val) {
				buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			} else {
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeRaw)
		}
	case common.BuildInOracleDatatypeReal:
		originColumnType = common.BuildInOracleDatatypeReal
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeReal]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeReal)
		}
	case common.BuildInOracleDatatypeRowid:
		originColumnType = common.BuildInOracleDatatypeRowid
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeRowid]; ok {
			buildInColumnType = fmt.Sprintf("%s(64)", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeRowid)
		}
	case common.BuildInOracleDatatypeSmallint:
		originColumnType = common.BuildInOracleDatatypeSmallint
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeSmallint]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeSmallint)
		}
	case common.BuildInOracleDatatypeUrowid:
		originColumnType = common.BuildInOracleDatatypeUrowid
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeUrowid]; ok {
			buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeUrowid)
		}
	case common.BuildInOracleDatatypeVarchar2:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeVarchar2]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeVarchar2, column.CharLength)
				buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeVarchar2, dataLength)
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeVarchar2)
		}
	case common.BuildInOracleDatatypeVarchar:
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeVarchar]; ok {
			if strings.EqualFold(column.CharUsed, "C") {
				originColumnType = fmt.Sprintf("%s(%s)", common.BuildInOracleDatatypeVarchar, column.CharLength)
				buildInColumnType = fmt.Sprintf("%s(%s)", common.StringUPPER(val), column.CharLength)
			} else {
				originColumnType = fmt.Sprintf("%s(%d)", common.BuildInOracleDatatypeVarchar, dataLength)
				buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataLength)
			}
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeVarchar)
		}
	case common.BuildInOracleDatatypeXmltype:
		originColumnType = common.BuildInOracleDatatypeXmltype
		if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeXmltype]; ok {
			buildInColumnType = fmt.Sprintf("%s", common.StringUPPER(val))
			return originColumnType, buildInColumnType, nil
		} else {
			return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.BuildInOracleDatatypeXmltype)
		}
	default:
		if strings.Contains(column.DataType, "INTERVAL YEAR") {
			originColumnType = column.DataType
			if val, ok := buildinDatatypeMap[common.StringUPPER(originColumnType)]; ok {
				buildInColumnType = fmt.Sprintf("%s(30)", common.StringUPPER(val))
				return originColumnType, buildInColumnType, nil
			} else {
				return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.StringUPPER(originColumnType))
			}
		} else if strings.Contains(column.DataType, "INTERVAL DAY") {
			originColumnType = column.DataType
			if val, ok := buildinDatatypeMap[common.BuildInOracleDatatypeIntervalDay]; ok {
				buildInColumnType = fmt.Sprintf("%s(30)", common.StringUPPER(val))
				return originColumnType, buildInColumnType, nil
			} else {
				return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.StringUPPER(originColumnType))
			}
		} else if strings.Contains(column.DataType, "TIMESTAMP") {
			originColumnType = column.DataType
			if dataScale <= 6 {
				if val, ok := buildinDatatypeMap["TIMESTAMP"]; ok {
					buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), dataScale)
					return originColumnType, buildInColumnType, nil
				} else {
					return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.StringUPPER(originColumnType))
				}
			} else {
				if val, ok := buildinDatatypeMap["TIMESTAMP"]; ok {
					buildInColumnType = fmt.Sprintf("%s(%d)", common.StringUPPER(val), 6)
					return originColumnType, buildInColumnType, nil
				} else {
					return originColumnType, buildInColumnType, fmt.Errorf("oracle table column type [%s] map mysql column type rule isn't exist, please checkin", common.StringUPPER(originColumnType))
				}
			}
		} else {
			originColumnType = column.DataType
			buildInColumnType = "TEXT"
		}
		return originColumnType, buildInColumnType, nil
	}
}
