package common

const BrokenSQLTemplate = `BEGIN
     DBMS_JOB.broken(
          JOB=>%s,
          broken=>true);
COMMIT;
END;`

const DisableBrokenSQLTemplate = `DECLARE JOB NUMBER;
BEGIN
     DBMS_JOB.broken(
          JOB=>%s,
          broken=>false);
COMMIT;
END;`

const QueryCursorJobSQL = `
SELECT /*+parallel(8)*/  
    job,
    what,
    to_char(THIS_DATE,'yyyy-mm-dd hh24:mi:ss') AS this_date,
    to_char(LAST_DATE,'yyyy-mm-dd hh24:mi:ss') AS last_date,
    to_char(NEXT_DATE,'yyyy-mm-dd hh24:mi:ss') AS next_date,
    broken 
FROM dba_jobs 
WHERE what LIKE '%${USERNAME}.spa_sqlcollect_cursor%'`

const QueryAwrJobSQL = `
SELECT /*+parallel(8)*/ 
    job,
    what,
    to_char(THIS_DATE,'yyyy-mm-dd hh24:mi:ss') as this_date,
    to_char(LAST_DATE,'yyyy-mm-dd hh24:mi:ss') AS last_date,
    to_char(NEXT_DATE,'yyyy-mm-dd hh24:mi:ss') AS next_date,
    broken 
FROM dba_jobs 
WHERE what LIKE '%${USERNAME}.spa_sqlcollect_awr%'`

const CountAllSchemaSQLSetSQLTemplate = `
SELECT /*+parallel(8)*/  count(*) as count 
FROM dba_sqlset_statements 
WHERE SQLSET_NAME='${SQLSET_NAME}'
  AND EXECUTIONS > 0
  AND PLAN_HASH_VALUE > 0
  AND (MODULE != 'TMS' OR MODULE IS NULL)
  AND SQLSET_OWNER='${USERNAME}'
  AND COMMAND_TYPE IN (${COMMAND_TYPE_LIST})
  AND PARSING_SCHEMA_NAME IN (${INCLUDING_SCHEMAS})
  AND 1=1
`

const CountSQLSetBySchemaSQLTemplate = `
SELECT /*+parallel(8)*/  count(*) as count 
FROM dba_sqlset_statements 
WHERE SQLSET_NAME='${SQLSET_NAME}'
  AND EXECUTIONS > 0
  AND PLAN_HASH_VALUE > 0
  AND (MODULE != 'TMS' OR MODULE IS NULL)
  AND SQLSET_OWNER='${USERNAME}'
  AND PARSING_SCHEMA_NAME='${SCHEMA_NAME}'
  AND COMMAND_TYPE IN (${COMMAND_TYPE_LIST})
  AND 1=1
`

const CountSQLSetBySchemaSQLDefaultTemplate = `
SELECT /*+rule*/ count(*) as count 
FROM dba_sqlset_statements 
WHERE SQLSET_NAME='${SQLSET_NAME}'
  AND EXECUTIONS > 0
  AND PLAN_HASH_VALUE > 0
  AND (MODULE != 'TMS' OR MODULE IS NULL)
  AND SQLSET_OWNER='${USERNAME}'
  AND PARSING_SCHEMA_NAME='${SCHEMA_NAME}'
`

const CountSQLSetGroupBySchemaSQLDefaultTemplate = `
SELECT /*+rule*/ 
    PARSING_SCHEMA_NAME,
    count(*) as count 
FROM dba_sqlset_statements 
WHERE SQLSET_NAME='${SQLSET_NAME}'
  AND EXECUTIONS > 0
  AND PLAN_HASH_VALUE > 0
  AND (MODULE != 'TMS' OR MODULE IS NULL)
  AND SQLSET_OWNER='${USERNAME}'
GROUP BY PARSING_SCHEMA_NAME`

const CountSQLSetStatementCountSQLDefaultTemplate = `
select /*+rule*/ a.NAME,a.OWNER,a.STATEMENT_COUNT 
from dba_sqlset a 
where a.NAME='${SQLSET_NAME}'
AND OWNER='${USERNAME}'
`

const CountSQLSetGroupBySchemaSQLTemplate = `
SELECT /*+parallel(8)*/  
    PARSING_SCHEMA_NAME,
    count(*) as count 
FROM dba_sqlset_statements 
WHERE SQLSET_NAME='${SQLSET_NAME}'
  AND EXECUTIONS > 0
  AND PLAN_HASH_VALUE > 0
  AND (MODULE != 'TMS' OR MODULE IS NULL)
  AND SQLSET_OWNER='${USERNAME}'
  AND COMMAND_TYPE IN (${COMMAND_TYPE_LIST})
  AND 1=1
GROUP BY PARSING_SCHEMA_NAME`

const PagedQuerySQLSetStatementSQLTemplate = `
SELECT M.* 
FROM (
	SELECT t.*, ROWNUM RN
	FROM (
		SELECT 
			PARSING_SCHEMA_NAME,rownum,
			a.sql_text,
			a.sql_id,
			CASE WHEN a.executions = 0 THEN 0 ELSE ROUND(a.elapsed_time / a.executions, 2) END AS per_exectime,
			a.executions 
		FROM dba_sqlset_statements a 
		WHERE EXECUTIONS > 0
		  AND PLAN_HASH_VALUE > 0
		  AND SQLSET_OWNER='${SQLSET_OWNER}' 
		  AND SQLSET_NAME='${SQLSET_NAME}'
		  AND (MODULE != 'TMS' OR MODULE IS NULL)
		  AND PARSING_SCHEMA_NAME='${SCHEMA_NAME}' 
		  AND COMMAND_TYPE IN (${COMMAND_TYPE_LIST})
		  AND 1=1
		  AND ROWNUM <= ${MAX_ROWS}
		ORDER BY sql_id 
		) T
	WHERE ROWNUM <= ${LIMIT_OFFSET} ) M
WHERE M.RN > ${OFFSET}`

const PagedQuerySQLSetStatementSQLDefaultTemplate = `
SELECT M.* 
FROM (
	SELECT t.*, ROWNUM RN
	FROM (
		SELECT 
			PARSING_SCHEMA_NAME,rownum,
			a.sql_text,
			a.sql_id,
			CASE WHEN a.executions = 0 THEN 0 ELSE ROUND(a.elapsed_time / a.executions, 2) END AS per_exectime,
			a.executions 
		FROM dba_sqlset_statements a 
		WHERE EXECUTIONS > 0
		  AND PLAN_HASH_VALUE > 0
		  AND SQLSET_OWNER='${SQLSET_OWNER}' 
		  AND SQLSET_NAME='${SQLSET_NAME}'
		  AND (MODULE != 'TMS' OR MODULE IS NULL)
		  AND PARSING_SCHEMA_NAME='${SCHEMA_NAME}' 
		  AND ROWNUM <= ${MAX_ROWS}
		ORDER BY sql_id 
		) T
	WHERE ROWNUM <= ${LIMIT_OFFSET} ) M
WHERE M.RN > ${OFFSET}`

const OracleSqlQuery = `
    SELECT
	    ROW_NUMBER() OVER(partition BY t1.sql_id ORDER BY t1.executions DESC ) AS rid,
        t1.sqlset_name as ora_sqlset_name,
        t1.sqlset_owner as ora_sqlset_owner,
        t1.module as ora_module,
        t1.sql_id as ora_sql_id,
        t1.sql_text as ora_sql_text,
        t1.parsing_schema_name as ora_parsing_schema_name,
        t1.plan_hash_value as ora_plan_hash_value,
        t2.position as ora_parameter_position,
        t2.value as ora_parameter_value,
        t1.executions as ora_executions,
        round(t1.elapsed_time/1000/t1.executions,2) as ora_elapsed_time_ms,
        '' as ora_last_exec_start_time,
        t1.plan_timestamp as ora_plan_timestamp,
        t1.command_type as ora_command_type
    FROM
        dba_sqlset_statements t1
    LEFT OUTER JOIN (
        SELECT
            sql_id,
            plan_hash_value ,
            listagg(position, ';,') within group(order by position) as position,
            listagg(getdata(value), ';,') within group(order by position) as value
        FROM
            dba_sqlset_binds
        WHERE
            value is not null
			AND plan_hash_value > 0
        GROUP BY
            sql_id,
            plan_hash_value) t2 
    ON
        t1.sql_id = t2.sql_id
        AND t1.plan_hash_value = t2.plan_hash_value
    WHERE t1.executions>0
      AND t1.plan_hash_value >0
      AND t1.module !='TMS'
      AND t1.sqlset_name='${SQLSET_NAME}'
      AND t1.sqlset_owner='${USERNAME}'
      AND t1.PARSING_SCHEMA_NAME IN (${INCLUDING_SCHEMAS})
	  AND 1=1
	`

const OracleSqlQueryV19 = `
    SELECT
	    ROW_NUMBER() OVER(partition BY t1.sql_id ORDER BY t1.executions DESC ) AS rid,
        t1.sqlset_name as ora_sqlset_name,
        t1.sqlset_owner as ora_sqlset_owner,
        t1.module as ora_module,
        t1.sql_id as ora_sql_id,
        t1.sql_text as ora_sql_text,
        t1.parsing_schema_name as ora_parsing_schema_name,
        t1.plan_hash_value as ora_plan_hash_value,
        t2.position as ora_parameter_position,
        t2.value as ora_parameter_value,
        t1.executions as ora_executions,
        round(t1.elapsed_time/1000/t1.executions,2) as ora_elapsed_time_ms,
        replace(t1.last_exec_start_time,'/',' ') as ora_last_exec_start_time,
        t1.plan_timestamp as ora_plan_timestamp,
        t1.command_type as ora_command_type
    FROM
        dba_sqlset_statements t1
    LEFT OUTER JOIN (
        SELECT
            sql_id,
            plan_hash_value ,
            listagg(position, ';,') within group(order by position) as position,
            listagg(getdata(value), ';,') within group(order by position) as value
        FROM
            dba_sqlset_binds
        WHERE
            value is not null
			AND plan_hash_value > 0
        GROUP BY
            sql_id,
            plan_hash_value) t2 
    ON
        t1.sql_id = t2.sql_id
        AND t1.plan_hash_value = t2.plan_hash_value
    WHERE t1.executions>0
      AND t1.plan_hash_value >0
      AND t1.module !='TMS'
      AND t1.sqlset_owner='${USERNAME}'
      AND t1.sqlset_name='${SQLSET_NAME}'
      AND t1.parsing_schema_name IN (${INCLUDING_SCHEMAS})
	  AND 1=1
	`

const OracleSqlQueryV2 = `
    SELECT  /*+parallel(8)*/  
	    ROW_NUMBER() OVER(partition BY t1.sql_id ORDER BY t1.executions DESC ) AS rid,
        t1.sqlset_name as ora_sqlset_name,
        t1.sqlset_owner as ora_sqlset_owner,
        t1.module as ora_module,
        t1.sql_id as ora_sql_id,
        t1.digest_id as ora_digest_id,
        t1.sql_text as ora_sql_text,
		t1.parsing_schema_name as ora_parsing_schema_name,
        t1.plan_hash_value as ora_plan_hash_value,
        t1.executions as ora_executions,
		t1.replay_exec_code AS tidb_exec_code,
		t1.rewrite_sql_text AS rewrite_sql_text,
		t1.rewrite_prompts AS rewrite_prompts,
        t2.position as ora_parameter_position,
        t2.value as ora_parameter_value,
        round(t1.elapsed_time/1000/t1.executions,2) as ora_elapsed_time_ms
    FROM
        tms_sqlset_statements t1
    LEFT OUTER JOIN (
        SELECT
            sql_id,
            plan_hash_value ,
            listagg(position, ';,') within group(order by position) as position,
            listagg(getdata(value), ';,') within group(order by position) as value
        FROM
            dba_sqlset_binds
        WHERE
            value is not null
			AND plan_hash_value > 0
        GROUP BY
            sql_id,
            plan_hash_value) t2 
    ON
        t1.sql_id = t2.sql_id
        AND t1.plan_hash_value = t2.plan_hash_value
    WHERE t1.sqlset_owner=${SQLSET_OWNER}
      AND t1.parsing_schema_name IN (${INCLUDING_SCHEMAS})
      AND t1.sqlset_name='${SQLSET_NAME}'
      AND t1.task_id='${TASK_ID}'
      AND ( t1.replay != 'Y' OR t1.replay IS NULL )
	`

const OracleSqlQueryV2WithExecCodes = `
    SELECT  /*+parallel(8)*/  
	    ROW_NUMBER() OVER(partition BY t1.sql_id ORDER BY t1.executions DESC ) AS rid,
        t1.sqlset_name as ora_sqlset_name,
        t1.sqlset_owner as ora_sqlset_owner,
        t1.module as ora_module,
        t1.sql_id as ora_sql_id,
        t1.digest_id as ora_digest_id,
        t1.sql_text as ora_sql_text,
        t1.parsing_schema_name as ora_parsing_schema_name,
        t1.plan_hash_value as ora_plan_hash_value,
        t1.executions as ora_executions,
		t1.replay_exec_code AS tidb_exec_code,
		t1.rewrite_sql_text AS rewrite_sql_text,
		t1.rewrite_prompts AS rewrite_prompts,
        t2.position as ora_parameter_position,
        t2.value as ora_parameter_value,
        round(t1.elapsed_time/1000/t1.executions,2) as ora_elapsed_time_ms
    FROM
        tms_sqlset_statements t1
    LEFT OUTER JOIN (
        SELECT
            sql_id,
            plan_hash_value ,
            listagg(position, ';,') within group(order by position) as position,
            listagg(getdata(value), ';,') within group(order by position) as value
        FROM
            dba_sqlset_binds
        WHERE
            value is not null
			AND plan_hash_value > 0
        GROUP BY
            sql_id,
            plan_hash_value) t2 
    ON
        t1.sql_id = t2.sql_id
        AND t1.plan_hash_value = t2.plan_hash_value
    WHERE t1.sqlset_owner=${SQLSET_OWNER}
      AND t1.parsing_schema_name IN (${INCLUDING_SCHEMAS})
      AND t1.sqlset_name='${SQLSET_NAME}'
      AND t1.task_id='${TASK_ID}'
      AND t1.replay_exec_code IN (${EXEC_CODES})
	`
