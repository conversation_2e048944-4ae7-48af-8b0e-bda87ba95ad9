package common

import (
	"context"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	migrationpkg "gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
)

func BuildBaseFieldsMessageFromModel(entity *common.Entity) *message.BaseFields {
	if entity == nil {
		return nil
	}
	return &message.BaseFields{
		Comment:   entity.Comment,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
	}
}

func BuildEntityModelFromMessage(baseFields *message.BaseFields) *common.Entity {
	if baseFields == nil {
		return nil
	}
	return &common.Entity{
		Comment: baseFields.Comment,
	}
}

func GetFullDataMigrationProgressBarByParentTask(ctx context.Context, msg *message.MigrationProgressReq) (*message.MigrationDataProgressResp, error) {
	var (
		runningNums int
		totalNums   int
		successNums int
		failedNums  int
		startTime   time.Time
		endTime     time.Time
	)

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, msg.TaskID)
		if err != nil {
			return err
		}

		subTasks, err := models.GetTaskReaderWriter().BatchGetSubTasksByParentTaskId(ctx, taskInfo.ChannelId, taskInfo.TaskID)
		if err != nil {
			return err
		}

		var isAllSubTaskDone = true // means FINISH OR FAILED

		if len(subTasks) == 0 {
			startTime = timeutil.GetTMSNullTime()
			endTime = timeutil.GetTMSNullTime()
		} else {
			startTime = time.Now()
			endTime = subTasks[0].EndTime

			totalNums = len(subTasks)

			// get minimal start time and maximal end time
			for _, subTask := range subTasks {
				if subTask.TaskStatus == constants.TASK_STATUS_FINISH {
					successNums++
				}
				if subTask.TaskStatus == constants.TASK_STATUS_FAILED {
					failedNums++
				}
				if subTask.TaskStatus == constants.TASK_STATUS_RUNNING {
					runningNums++
				}
				if timeutil.IsNotTMSNullTime(subTask.StartTime) && subTask.StartTime.Before(startTime) {
					startTime = subTask.StartTime
				}
				if timeutil.IsNotTMSNullTime(subTask.EndTime) && subTask.EndTime.After(endTime) {
					endTime = subTask.EndTime
				}
				if subTask.TaskStatus != constants.TASK_STATUS_FAILED && subTask.TaskStatus != constants.TASK_STATUS_FINISH {
					isAllSubTaskDone = false
				}
			}
			if !isAllSubTaskDone {
				endTime = timeutil.GetTMSNullTime()
			}
		}
		return nil
	})
	if trxErr != nil {
		return nil, trxErr
	}

	return &message.MigrationDataProgressResp{
		TaskID: msg.TaskID,

		TotalTaskNums:   totalNums,
		SuccessTaskNums: successNums,
		FailedTaskNums:  failedNums,
		RunningTaskNums: runningNums,

		TotalMigrationNums:   totalNums,
		SuccessMigrationNums: successNums,
		FailedMigrationNums:  failedNums,
		RunningMigrationNums: runningNums,

		TotalChunkNums:   totalNums,
		SuccessChunkNums: successNums,
		FailedChunkNums:  failedNums,
		RunningChunkNums: runningNums,

		ProgressStartTime: startTime,
		LastUpdateTime:    endTime,
	}, nil
}

func GetSchemasTablesSegmentSize(ctx context.Context, channelId int, taskId int, allTableSchemas []string) (map[structs.SchemaTablePair]float64, error) {
	// 增加实时获取Table SegmentSize逻辑
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed. channelId:%d, err:%v", channelId, getChannelErr)
		return nil, getChannelErr
	}

	oracleConn, setUpErr := migrationpkg.SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("set up oracle database conns failed, channelId:%d, taskId:%d, err:%v", channelId, taskId, setUpErr)
		return nil, setUpErr
	}

	querySegmentSizeSQLTemplate := `
select /*+rule parallel(8)*/ t.owner,t.segment_name,round(sum(t.bytes)/1024/1024,2) as table_sizeM
from dba_segments t
where t.owner IN (${SCHEMAS}) and t.segment_type in ('TABLE','TABLE PARTITION') and t.segment_name not like 'BIN$%%'
group by t.owner,t.segment_name
`

	querySegmentSizeSQL := strings.ReplaceAll(querySegmentSizeSQLTemplate, "${SCHEMAS}", "'"+strings.Join(allTableSchemas, "','")+"'")

	log.Infof("split task tables, query table size, taskId:%d, sql:%s", taskId, stringutil.RemoveSpecialLetterForLog(querySegmentSizeSQL))
	_, res, queryErr := oracle.Query(ctx, oracleConn.GetSourceDB().OracleDB, querySegmentSizeSQL)
	if queryErr != nil {
		log.Errorf("query segment size failed. taskId:%d, sql:%s, err:%v", taskId, stringutil.RemoveSpecialLetterForLog(querySegmentSizeSQL), queryErr)
		return nil, queryErr
	}

	schemaTableSizeMapping := make(map[structs.SchemaTablePair]float64)
	for _, row := range res {
		owner := row["OWNER"]
		segmentName := row["SEGMENT_NAME"]
		tableSizeM := row["TABLE_SIZEM"]
		tableSizeMInt, _ := strconv.ParseFloat(tableSizeM, 64)
		schemaTableSizeMapping[structs.SchemaTablePair{SchemaName: owner, TableName: segmentName}] = tableSizeMInt
	}
	return schemaTableSizeMapping, nil
}
