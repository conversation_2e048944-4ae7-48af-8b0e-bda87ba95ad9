package common

import (
	"context"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/denisbrodbeck/machineid"
	"github.com/samber/lo"
)

type Service struct {
}

func NewSystemInfoService() *Service {
	return &Service{}
}

// var assessmentLog = log.GetAssessmentLoggerEntry()

func (s *Service) InsertOrUpdateSystemInfo(ctx context.Context) error {

	machineID, err := machineid.ProtectedID(constants.APP_NAME)
	if err != nil {
		log.Errorf("InsertOrUpdateSystemInfo get machineID error")
		return err
	}

	sysinfo, err := models.GetSystemInfoReaderWriter().GetSystemInfo(ctx, machineID)
	if sysinfo == nil {
		models.GetSystemInfoReaderWriter().CreateSystemInfo(ctx, &common.SystemInfo{
			MachineId: machineID,
			Entity:    BuildEntityModelFromMessage(nil),
		})
	} else {
		models.GetSystemInfoReaderWriter().UpdateSystemInfo(ctx, sysinfo)
	}
	return nil

}

func (s *Service) GetSystemInfo(ctx context.Context) (*common.SystemInfo, error) {
	machineID, err := machineid.ProtectedID(constants.APP_NAME)
	if err != nil {
		log.Errorf("InsertOrUpdateSystemInfo get machineID error")
		return nil, err
	}
	return models.GetSystemInfoReaderWriter().GetSystemInfo(ctx, machineID)
}

func (s *Service) ListLicenseFeatures(ctx context.Context) (*message.ListLicenseFeaturesResp, error) {
	conf := config.GetGlobalConfig()

	manager, initErr := license.NewLicenseManager(conf.License, license.Config{
		EncryptionKey: license.EncryptionKey,
		HMACKey:       license.HMACKey,
	})
	if initErr != nil {
		log.Errorf("init license manager failed. err:%v", initErr)
		return nil, initErr
	}

	features, listErr := manager.ListFeatures(ctx, conf.License)
	if listErr != nil {
		log.Errorf("list license features failed. err:%v", listErr)
		return nil, listErr
	}

	arr := buildLicenseFeatureMessages(features)

	return &message.ListLicenseFeaturesResp{
		LicenseFeatures: arr,
	}, nil
}

func buildLicenseFeatureMessages(features []common.FeatureLicense) []message.LicenseFeature {
	return lo.Map(features, func(item common.FeatureLicense, index int) message.LicenseFeature {
		return message.LicenseFeature{
			FeatureCode:           item.FeatureCode,
			FeatureName:           item.FeatureName,
			MaxUsageCount:         item.MaxUsageCount,
			CurrentUsageCount:     item.CurrentUsageCount,
			LicenseKey:            item.LicenseKey,
			VerificationSignature: item.VerificationSignature,
		}
	})
}
func (s *Service) UpdateLicenseFeatures(ctx context.Context, req *message.UpdateLicenseFeaturesReq) (*message.UpdateLicenseFeaturesResp, error) {
	conf := config.GetGlobalConfig()

	// 初始化 LicenseManager
	manager, err := license.NewLicenseManager(conf.License, license.Config{
		EncryptionKey: license.EncryptionKey,
		HMACKey:       license.HMACKey,
	})
	if err != nil {
		log.Errorf("failed to initialize license manager: %v", err)
		return nil, fmt.Errorf("init license manager: %w", err)
	}

	// 获取初始功能列表
	beforeFeatures, err := manager.ListFeatures(ctx, conf.License)
	if err != nil {
		log.Errorf("failed to list license features: %v", err)
		return nil, fmt.Errorf("list features: %w", err)
	}

	// 执行功能更新
	featureUpdates := []struct {
		shouldUpdate bool
		featureID    string
	}{
		{req.IncreaseOpFunctionNum, constants.OpFunctionNum.String()},
		{req.IncreaseOpTriggerNum, constants.OpTriggerNum.String()},
		{req.IncreaseOpProcedureNum, constants.OpProcedureNum.String()},
		{req.IncreaseOpPackageNum, constants.OpPackageNum.String()},
	}

	for _, update := range featureUpdates {
		if !update.shouldUpdate {
			continue
		}
		if err := manager.ExecuteFeature(ctx, update.featureID); err != nil {
			log.Errorf("failed to execute feature %s: %v", update.featureID, err)
			return nil, fmt.Errorf("execute feature %s: %w", update.featureID, err)
		}
		log.Infof("feature %s updated", update.featureID)
	}

	// 获取更新后的功能列表
	afterFeatures, err := manager.ListFeatures(ctx, conf.License)
	if err != nil {
		log.Errorf("failed to list license features after update: %v", err)
		return nil, fmt.Errorf("list features after update: %w", err)
	}

	return &message.UpdateLicenseFeaturesResp{
		BeforeLicenseFeatures: buildLicenseFeatureMessages(beforeFeatures),
		AfterLicenseFeatures:  buildLicenseFeatureMessages(afterFeatures),
	}, nil
}
