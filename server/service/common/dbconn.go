package common

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	transferconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/mysql"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
)

type MigrationDBConns struct {
	SourceDS *datasource.Datasource
	TargetDS *datasource.Datasource
	SourceDB *oracle.Oracle
	TargetDB *mysql.MySQL
	// oracleCollation is true if oracle db version >= 12.1
	OracleCollation bool
}

// SetUpSourceDatabaseConnFunc set up database conn by channel info and check database collation
var SetUpSourceDatabaseConnFunc = func(ctx context.Context, channelInfo *channel.ChannelInformation) (*oracle.Oracle, *datasource.Datasource, error) {
	sourceDS, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if err != nil {
		return nil, nil, fmt.Errorf("get task source datasource info failed: %v", err)
	}

	sourceDB, err := oracle.NewOracleDBEngine(ctx, transferconfig.OracleConfig{
		Username:      sourceDS.UserName,
		Password:      sourceDS.PasswordValue,
		Host:          sourceDS.HostIp,
		Port:          sourceDS.HostPort,
		ServiceName:   datasourcepkg.GetServiceName(sourceDS),
		ConnectParams: sourceDS.ConnectParams,
		Charset:       sourceDS.Charset,
	}, "")
	if err != nil {
		return nil, nil, fmt.Errorf("create source datasource conn failed: %v", err)
	}

	if _, setModuleErr := sourceDB.OracleDB.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
		return nil, nil, fmt.Errorf("set oracle module failed: %v", setModuleErr)
	}

	return sourceDB, sourceDS, nil
}
