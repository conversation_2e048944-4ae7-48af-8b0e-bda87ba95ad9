package mysql

import (
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// TaskConfigBuilder builds DM task configuration from provided data
type TaskConfigBuilder struct {
	converter *DMConverter
}

// NewTaskConfigBuilder creates a new TaskConfigBuilder instance
func NewTaskConfigBuilder(converter *DMConverter) *TaskConfigBuilder {
	return &TaskConfigBuilder{
		converter: converter,
	}
}

// BuildTaskConfig orchestrates the building of the complete task configuration
func (b *TaskConfigBuilder) BuildTaskConfig(
	channelInfo *channel.ChannelInformation,
	targetDatasource *datasource.Datasource,
	sourceDatasources []*datasource.Datasource,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) (*message.TaskConfigDTO, error) {
	log.Infof("BuildTaskConfig: starting to build task config for channel: %s", channelInfo.ChannelName)

	// Validate input parameters
	if channelInfo == nil {
		return nil, errors.NewError(errors.TIMS_PARAMETER_INVALID, "channel info is required")
	}
	if targetDatasource == nil {
		return nil, errors.NewError(errors.TIMS_DM_TARGET_DB_NOT_FOUND, "target datasource is required")
	}
	if len(sourceDatasources) == 0 {
		return nil, errors.NewError(errors.TIMS_DM_SOURCE_DB_NOT_FOUND, "at least one source datasource is required")
	}

	// Initialize TaskConfigDTO
	config := &message.TaskConfigDTO{
		Routes:         make(map[string]*message.RouteRule),
		Filters:        make(map[string]*message.BinlogEventRule),
		BlockAllowList: make(map[string]*message.BlockAllowListRule),
		Mydumpers:      make(map[string]*message.MydumperConfig),
		Loaders:        make(map[string]*message.LoaderConfig),
		Syncers:        make(map[string]*message.SyncerConfig),
		Validators:     make(map[string]*message.ValidatorConfig),
	}

	// Step 1: Assemble basic configuration
	if err := b.AssembleBasicConfig(config, channelInfo, targetDatasource); err != nil {
		log.Errorf("BuildTaskConfig: failed to assemble basic config, error: %v", err)
		return nil, err
	}

	// Step 2: Assemble rules configuration (routes, filters, block-allow-list)
	if err := b.AssembleRulesConfig(config, schemaTables, channelSchemas); err != nil {
		log.Errorf("BuildTaskConfig: failed to assemble rules config, error: %v", err)
		return nil, err
	}

	// Step 3: Assemble processor configuration (mydumpers, loaders, syncers, validators)
	if err := b.AssembleProcessorConfig(config); err != nil {
		log.Errorf("BuildTaskConfig: failed to assemble processor config, error: %v", err)
		return nil, err
	}

	// Step 4: Assemble MySQL instances configuration
	if err := b.AssembleMySQLInstances(config, sourceDatasources, config.TaskMode); err != nil {
		log.Errorf("BuildTaskConfig: failed to assemble MySQL instances, error: %v", err)
		return nil, err
	}

	log.Infof("BuildTaskConfig: successfully built task config for channel: %s", channelInfo.ChannelName)
	return config, nil
}

// AssembleBasicConfig assembles basic task configuration
func (b *TaskConfigBuilder) AssembleBasicConfig(
	config *message.TaskConfigDTO,
	channelInfo *channel.ChannelInformation,
	targetDatasource *datasource.Datasource,
) error {
	log.Debugf("AssembleBasicConfig: assembling basic config for channel: %s", channelInfo.ChannelName)

	// Set task name from channel name
	config.Name = fmt.Sprintf("task_%s", channelInfo.ChannelName)

	// Determine task mode based on channel settings
	if channelInfo.MigrateFullAndIncrementData == "Y" {
		config.TaskMode = "all" // Full + incremental
	} else if channelInfo.Increment == "Y" {
		config.TaskMode = "incremental" // Only incremental
	} else {
		config.TaskMode = "full" // Only full data migration
	}

	// Set sharding mode (default: no sharding)
	config.IsSharding = false
	config.ShardMode = ""
	config.StrictOptimisticShardMode = false

	// Set meta configuration
	config.MetaSchema = "dm_meta"
	config.CaseSensitive = false
	config.CollationCompatible = "loose"

	// Set online DDL handling
	config.OnlineDDL = true
	config.OnlineDDLScheme = "gh-ost"

	// Set other basic configurations
	config.CleanDumpFile = true
	config.EnableHeartbeat = false
	config.Timezone = ""

	// Validate target database is MySQL/TiDB
	if targetDatasource.DbType != constants.DB_TYPE_MYSQL && targetDatasource.DbType != constants.DB_TYPE_TIDB {
		log.Errorf("AssembleBasicConfig: target datasource is not MySQL/TiDB, type: %s", targetDatasource.DbType)
		return errors.NewError(errors.TIMS_DM_TARGET_DB_NOT_FOUND, "target must be MySQL or TiDB")
	}

	// Configure target database
	config.TargetDatabase = &message.TaskTargetDatabase{
		Host:     targetDatasource.HostIp,
		Port:     targetDatasource.HostPort,
		User:     targetDatasource.UserName,
		Password: targetDatasource.PasswordValue,
		Session: map[string]string{
			"sql_mode":                "ANSI_QUOTES,NO_ZERO_IN_DATE,NO_ZERO_DATE",
			"tidb_skip_utf8_check":    "1",
			"sql_require_primary_key": "OFF",
		},
	}

	log.Debugf("AssembleBasicConfig: completed basic config assembly")
	return nil
}

// AssembleRulesConfig assembles routes, filters, and block-allow-list rules
func (b *TaskConfigBuilder) AssembleRulesConfig(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) error {
	log.Debugf("AssembleRulesConfig: assembling rules config")

	// 1. Generate route rules
	b.generateRouteRules(config, schemaTables, channelSchemas)

	// 2. Generate filter rules
	b.generateFilterRules(config)

	// 3. Generate block-allow-list rules (one do-tables entry per table)
	b.generateBlockAllowListRules(config, schemaTables, channelSchemas)

	log.Debugf("AssembleRulesConfig: completed rules config assembly")
	return nil
}

// generateRouteRules generates optimized routing rules
func (b *TaskConfigBuilder) generateRouteRules(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) {
	if len(schemaTables) > 0 {
		b.generateOptimizedTableRoutes(config, schemaTables)
	} else if len(channelSchemas) > 0 {
		b.generateSchemaOnlyRoutes(config, channelSchemas)
	} else {
		log.Warn("generateRouteRules: no schema tables or schemas found, no routes generated")
	}
}

// generateOptimizedTableRoutes generates routes with optimization for table renaming
func (b *TaskConfigBuilder) generateOptimizedTableRoutes(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
) {
	log.Infof("generateOptimizedTableRoutes: found %d schema tables, generating optimized routes", len(schemaTables))
	
	schemaGroups := groupTablesBySchema(schemaTables)
	log.Debugf("generateOptimizedTableRoutes: grouped tables into %d schemas", len(schemaGroups))
	
	for schemaS, group := range schemaGroups {
		b.processSchemaGroup(config, schemaS, group)
	}
	
	log.Infof("generateOptimizedTableRoutes: generated %d optimized route rules", len(config.Routes))
}

// processSchemaGroup processes a single schema and its tables
func (b *TaskConfigBuilder) processSchemaGroup(
	config *message.TaskConfigDTO,
	schemaS string,
	group *schemaGroup,
) {
	log.Debugf("processSchemaGroup: processing schema %s with %d tables", schemaS, len(group.tables))
	
	// Classify tables by whether they need renaming
	tablesNeedRename, tablesKeepName := b.classifyTables(group.tables)
	
	// Generate schema-level rule for tables keeping their names
	if len(tablesKeepName) > 0 {
		b.addSchemaRoute(config, schemaS, group.targetSchema, len(tablesKeepName))
	}
	
	// Generate specific rules for tables that need renaming
	for _, table := range tablesNeedRename {
		b.addTableRoute(config, table)
	}
}

// classifyTables separates tables that need renaming from those that don't
func (b *TaskConfigBuilder) classifyTables(
	tables []*channel.ChannelSchemaTable,
) ([]*channel.ChannelSchemaTable, []string) {
	tablesNeedRename := make([]*channel.ChannelSchemaTable, 0)
	tablesKeepName := make([]string, 0)
	
	for _, table := range tables {
		if table.TableNameT == "" {
			// Empty target name means keep original
			tablesKeepName = append(tablesKeepName, table.TableNameS)
		} else if needsRename(table.TableNameS, table.TableNameT) {
			// Table needs renaming
			tablesNeedRename = append(tablesNeedRename, table)
			log.Debugf("classifyTables: table %s.%s needs renaming to %s",
				table.SchemaNameS, table.TableNameS, table.TableNameT)
		} else {
			// Same name (case-insensitive)
			tablesKeepName = append(tablesKeepName, table.TableNameS)
		}
	}
	
	return tablesNeedRename, tablesKeepName
}

// generateSchemaOnlyRoutes generates simple schema-level routes
func (b *TaskConfigBuilder) generateSchemaOnlyRoutes(
	config *message.TaskConfigDTO,
	channelSchemas []*channel.ChannelSchema,
) {
	log.Info("generateSchemaOnlyRoutes: using schema-level routing")
	
	for _, schema := range channelSchemas {
		if schema.SchemaNameT == "" {
			continue
		}
		b.addSchemaRoute(config, schema.SchemaNameS, schema.SchemaNameT, 0)
	}
}

// addSchemaRoute adds a schema-level routing rule
func (b *TaskConfigBuilder) addSchemaRoute(
	config *message.TaskConfigDTO,
	sourceSchema, targetSchema string,
	tableCount int,
) {
	routeName := fmt.Sprintf("route-schema-%s", sanitizeName(sourceSchema))
	config.Routes[routeName] = &message.RouteRule{
		SchemaPattern: sourceSchema,
		TablePattern:  "*",
		TargetSchema:  targetSchema,
		TargetTable:   "", // Empty means keep original names
	}
	
	if tableCount > 0 {
		log.Infof("addSchemaRoute: created route '%s' for %d tables: %s->%s",
			routeName, tableCount, sourceSchema, targetSchema)
	} else {
		log.Infof("addSchemaRoute: created route '%s': %s->%s",
			routeName, sourceSchema, targetSchema)
	}
}

// addTableRoute adds a table-specific routing rule
func (b *TaskConfigBuilder) addTableRoute(
	config *message.TaskConfigDTO,
	table *channel.ChannelSchemaTable,
) {
	routeName := fmt.Sprintf("route-table-%s-%s",
		sanitizeName(table.SchemaNameS),
		sanitizeName(table.TableNameS))
	
	config.Routes[routeName] = &message.RouteRule{
		SchemaPattern: table.SchemaNameS,
		TablePattern:  table.TableNameS,
		TargetSchema:  table.SchemaNameT,
		TargetTable:   table.TableNameT,
	}
	
	log.Infof("addTableRoute: created route '%s': %s.%s->%s.%s",
		routeName, table.SchemaNameS, table.TableNameS,
		table.SchemaNameT, table.TableNameT)
}

// generateFilterRules generates binlog event filter rules
func (b *TaskConfigBuilder) generateFilterRules(config *message.TaskConfigDTO) {
	// DDL filter - ignore dangerous DDL operations
	config.Filters["filter-ddl-1"] = &message.BinlogEventRule{
		SchemaPattern: "*",
		TablePattern:  "*",
		Events:        []string{"truncate table", "drop table"},
		Action:        "Ignore",
	}
	
	// DML filter - migrate all DML events
	config.Filters["filter-dml-1"] = &message.BinlogEventRule{
		SchemaPattern: "*",
		TablePattern:  "*",
		Events:        []string{"all dml"},
		Action:        "Do",
	}
	
	log.Debug("generateFilterRules: generated DDL and DML filter rules")
}

// generateBlockAllowListRules generates block-allow-list rules
func (b *TaskConfigBuilder) generateBlockAllowListRules(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
	channelSchemas []*channel.ChannelSchema,
) {
	if len(schemaTables) > 0 {
		// When we have specific table information, create one do-tables entry per table
		b.generatePerTableBlockAllowList(config, schemaTables)
	} else if len(channelSchemas) > 0 {
		// When we only have schema information, use schema-level filtering
		b.generateSchemaLevelBlockAllowList(config, channelSchemas)
	}
}

// generatePerTableBlockAllowList creates block-allow-list with one entry per table
func (b *TaskConfigBuilder) generatePerTableBlockAllowList(
	config *message.TaskConfigDTO,
	schemaTables []*channel.ChannelSchemaTable,
) {
	// Collect all tables - one do-tables entry per table
	doTables := make([]*message.TableRef, 0, len(schemaTables))
	schemaMap := make(map[string]bool)
	
	for _, table := range schemaTables {
		// Each table gets its own do-tables entry for precise control
		doTables = append(doTables, &message.TableRef{
			DbName:  table.SchemaNameS,
			TblName: table.TableNameS,
		})
		schemaMap[table.SchemaNameS] = true
	}
	
	// Collect unique schema names
	doDbs := make([]string, 0, len(schemaMap))
	for schema := range schemaMap {
		doDbs = append(doDbs, schema)
	}
	
	// Create block-allow-list rule
	config.BlockAllowList["bw-rule-1"] = &message.BlockAllowListRule{
		DoDbs:        doDbs,                // Include these databases
		DoTables:     doTables,             // One entry per table for precise control
		IgnoreDbs:    getSystemDatabases(), // Always ignore system databases
		IgnoreTables: []*message.TableRef{},
	}
	
	log.Infof("generatePerTableBlockAllowList: created rule for %d schemas with %d individual table entries",
		len(doDbs), len(doTables))
}

// generateSchemaLevelBlockAllowList creates schema-level block-allow-list
func (b *TaskConfigBuilder) generateSchemaLevelBlockAllowList(
	config *message.TaskConfigDTO,
	channelSchemas []*channel.ChannelSchema,
) {
	doDbs := make([]string, 0, len(channelSchemas))
	for _, schema := range channelSchemas {
		doDbs = append(doDbs, schema.SchemaNameS)
	}
	
	config.BlockAllowList["bw-rule-1"] = &message.BlockAllowListRule{
		DoDbs:        doDbs,
		DoTables:     []*message.TableRef{}, // Empty means all tables in the schemas
		IgnoreDbs:    getSystemDatabases(),
		IgnoreTables: []*message.TableRef{},
	}
	
	log.Infof("generateSchemaLevelBlockAllowList: created rule for %d schemas", len(doDbs))
}

// getSystemDatabases returns the list of system databases to always ignore
func getSystemDatabases() []string {
	return []string{"mysql", "information_schema", "performance_schema", "sys"}
}

// AssembleProcessorConfig assembles mydumpers, loaders, syncers, and validators configuration
func (b *TaskConfigBuilder) AssembleProcessorConfig(config *message.TaskConfigDTO) error {
	log.Debugf("AssembleProcessorConfig: assembling processor config")

	// Configure mydumpers (for full data export)
	config.Mydumpers["global"] = &message.MydumperConfig{
		Threads:       4,
		ChunkFilesize: "64", // 64MB per chunk
		ExtraArgs:     "--consistency auto",
	}

	// Configure loaders (for full data import)
	config.Loaders["global"] = &message.LoaderConfig{
		PoolSize:            16,
		Dir:                 "./dumped_data",
		ImportMode:          "logical",
		OnDuplicateLogical:  "replace",
		OnDuplicatePhysical: "none",
		ChecksumPhysical:    "required",
		Analyze:             "off",
	}

	// Configure syncers (for incremental replication)
	config.Syncers["global"] = &message.SyncerConfig{
		WorkerCount:      16,
		Batch:            100,
		SafeMode:         false,
		SafeModeDuration: "60s",
		Compact:          false,
		MultipleRows:     false,
		EnableAnsiQuotes: true,
	}

	// Configure validators (for data validation)
	config.Validators["global"] = &message.ValidatorConfig{
		Mode:          "none", // Disabled by default
		WorkerCount:   4,
		RowErrorDelay: "30m",
	}

	log.Debugf("AssembleProcessorConfig: completed processor config assembly")
	return nil
}

// AssembleMySQLInstances assembles MySQL instances configuration with rule references
func (b *TaskConfigBuilder) AssembleMySQLInstances(
	config *message.TaskConfigDTO,
	sourceDatasources []*datasource.Datasource,
	taskMode string,
) error {
	log.Debugf("AssembleMySQLInstances: assembling MySQL instances")

	// Create MySQL instances for each source datasource
	instances := make([]*message.MySQLInstance, 0)

	for _, ds := range sourceDatasources {
		// Validate datasource is MySQL/TiDB
		if ds.DbType != constants.DB_TYPE_MYSQL && ds.DbType != constants.DB_TYPE_TIDB {
			log.Debugf("AssembleMySQLInstances: skipping non-MySQL datasource %s with type %s", ds.DatasourceName, ds.DbType)
			continue
		}

		// Create MySQL instance configuration
		instance := &message.MySQLInstance{
			SourceID: ds.DatasourceName, // Use datasource name as source-id
		}

		// Set binlog position for incremental tasks
		if taskMode == "incremental" || taskMode == "all" {
			// Default to current binlog position (will be overridden by checkpoint if exists)
			instance.Meta = &message.Meta{
				BinLogName: "binlog.000001",
				BinLogPos:  4,
				BinLogGTID: "", // Empty by default, can be set if GTID is enabled
			}
		}

		// Reference rule names
		// Collect all route rule names for this instance
		routeRules := make([]string, 0)
		for ruleName := range config.Routes {
			routeRules = append(routeRules, ruleName)
		}
		instance.RouteRules = routeRules

		// Collect all filter rule names
		filterRules := make([]string, 0)
		for ruleName := range config.Filters {
			filterRules = append(filterRules, ruleName)
		}
		instance.FilterRules = filterRules

		// Set block-allow-list reference
		if len(config.BlockAllowList) > 0 {
			instance.BlockAllowList = "bw-rule-1"
		}

		// Reference processor configurations
		instance.MydumperConfigName = "global"
		instance.LoaderConfigName = "global"
		instance.SyncerConfigName = "global"
		instance.ValidatorConfigName = "global"

		instances = append(instances, instance)
		log.Debugf("AssembleMySQLInstances: added instance for datasource %s", ds.DatasourceName)
	}

	config.MySQLInstances = instances

	log.Infof("AssembleMySQLInstances: assembled %d MySQL instances", len(instances))
	return nil
}

// Helper structures for route optimization

type schemaGroup struct {
	targetSchema string
	tables       []*channel.ChannelSchemaTable
}

type tableRenameInfo struct {
	sourceTable string
	targetTable string
	sourceSchema string
	targetSchema string
}

// Helper functions for route optimization

// needsRename checks if a table needs renaming (case-insensitive comparison)
func needsRename(sourceTable, targetTable string) bool {
	// If target table is empty, it means keeping the original name
	if targetTable == "" {
		return false
	}
	// Case-insensitive comparison
	return !strings.EqualFold(sourceTable, targetTable)
}

// sanitizeName makes names safe for use in route naming
func sanitizeName(name string) string {
	// Replace dots and special characters with underscores
	name = strings.ReplaceAll(name, ".", "_")
	name = strings.ReplaceAll(name, "-", "_")
	name = strings.ReplaceAll(name, " ", "_")
	// Convert to lowercase for consistency
	return strings.ToLower(name)
}

// groupTablesBySchema groups tables by their source schema
func groupTablesBySchema(tables []*channel.ChannelSchemaTable) map[string]*schemaGroup {
	groups := make(map[string]*schemaGroup)
	
	for _, table := range tables {
		// Skip tables without target schema
		if table.SchemaNameT == "" {
			continue
		}
		
		schemaKey := table.SchemaNameS
		if _, exists := groups[schemaKey]; !exists {
			groups[schemaKey] = &schemaGroup{
				targetSchema: table.SchemaNameT,
				tables:       make([]*channel.ChannelSchemaTable, 0),
			}
		}
		groups[schemaKey].tables = append(groups[schemaKey].tables, table)
	}
	
	return groups
}
