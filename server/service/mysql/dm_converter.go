package mysql

import (
	"gitee.com/pingcap_enterprise/tms/pkg/mysql/dm"
	"gitee.com/pingcap_enterprise/tms/server/message"
)

// DMConverter provides conversion methods between DM DTOs and message DTOs
type DMConverter struct{}

// NewDMConverter creates a new instance of DMConverter
func NewDMConverter() *DMConverter {
	return &DMConverter{}
}

// ToCustomSourceConfig converts dm.SourceConfigDTO to message.CustomSourceConfigDTO
func (c *DMConverter) ToCustomSourceConfig(dmConfig *dm.SourceConfigDTO) *message.CustomSourceConfigDTO {
	if dmConfig == nil {
		return nil
	}

	return &message.CustomSourceConfigDTO{
		SourceID:        dmConfig.SourceID,
		EnableGTID:      dmConfig.EnableGTID,
		EnableRelay:     dmConfig.EnableRelay,
		RelayBinlogName: dmConfig.RelayBinlogName,
		RelayBinlogGTID: dmConfig.RelayBinlogGTID,
		RelayDir:        dmConfig.RelayDir,
		From:            c.ToCustomDatabaseConfig(dmConfig.From),
		Purge:           dmConfig.Purge,
		Checker:         dmConfig.Checker,
		CaseSensitive:   dmConfig.CaseSensitive,
		Filters:         dmConfig.Filters,
	}
}

// ToCustomDatabaseConfig converts dm.DatabaseConfig to message.CustomDatabaseConfig
func (c *DMConverter) ToCustomDatabaseConfig(dmDBConfig dm.DatabaseConfig) message.CustomDatabaseConfig {
	return message.CustomDatabaseConfig{
		Host:            dmDBConfig.Host,
		Port:            dmDBConfig.Port,
		User:            dmDBConfig.User,
		Password:        dmDBConfig.Password,
		PasswordEncrypt: "", // Will be filled by fillSourcePasswords
		Security:        dmDBConfig.Security,
	}
}

// FromCustomSourceConfig converts message.CustomSourceConfigDTO to dm.SourceConfigDTO
func (c *DMConverter) FromCustomSourceConfig(msgConfig *message.CustomSourceConfigDTO) *dm.SourceConfigDTO {
	if msgConfig == nil {
		return nil
	}

	return &dm.SourceConfigDTO{
		SourceID:        msgConfig.SourceID,
		EnableGTID:      msgConfig.EnableGTID,
		EnableRelay:     msgConfig.EnableRelay,
		RelayBinlogName: msgConfig.RelayBinlogName,
		RelayBinlogGTID: msgConfig.RelayBinlogGTID,
		RelayDir:        msgConfig.RelayDir,
		From:            c.FromCustomDatabaseConfig(msgConfig.From),
		Purge:           msgConfig.Purge,
		Checker:         msgConfig.Checker,
		CaseSensitive:   msgConfig.CaseSensitive,
		Filters:         msgConfig.Filters,
	}
}

// FromCustomDatabaseConfig converts message.CustomDatabaseConfig to dm.DatabaseConfig
func (c *DMConverter) FromCustomDatabaseConfig(msgDBConfig message.CustomDatabaseConfig) dm.DatabaseConfig {
	// Use encrypted password if available, otherwise use plain password
	password := msgDBConfig.Password
	if msgDBConfig.PasswordEncrypt != "" {
		// In real implementation, you might want to decrypt here
		// For now, we'll use the plain password
		password = msgDBConfig.Password
	}

	return dm.DatabaseConfig{
		Host:     msgDBConfig.Host,
		Port:     msgDBConfig.Port,
		User:     msgDBConfig.User,
		Password: password,
		Security: msgDBConfig.Security,
	}
}

// ToOperateSourceResponse converts dm.OperateSourceResponseDTO to message.OperateSourceResponseDTO
func (c *DMConverter) ToOperateSourceResponse(dmResp *dm.OperateSourceResponseDTO) *message.OperateSourceResponseDTO {
	if dmResp == nil {
		return nil
	}

	msgResp := &message.OperateSourceResponseDTO{
		Result:  dmResp.Result,
		Msg:     dmResp.Msg,
		Sources: make([]*message.CommonWorkerResponseDTO, len(dmResp.Sources)),
	}

	for i, source := range dmResp.Sources {
		msgResp.Sources[i] = c.ToCommonWorkerResponse(source)
	}

	return msgResp
}

// ToCommonWorkerResponse converts dm.CommonWorkerResponseDTO to message.CommonWorkerResponseDTO
func (c *DMConverter) ToCommonWorkerResponse(dmWorker *dm.CommonWorkerResponseDTO) *message.CommonWorkerResponseDTO {
	if dmWorker == nil {
		return nil
	}

	return &message.CommonWorkerResponseDTO{
		Result: dmWorker.Result,
		Msg:    dmWorker.Msg,
		Source: dmWorker.Source,
		Worker: dmWorker.Worker,
	}
}

// ToStartTaskResponse converts dm.StartTaskResponseDTO to message.StartTaskResponseDTO
func (c *DMConverter) ToStartTaskResponse(dmResp *dm.StartTaskResponseDTO) *message.StartTaskResponseDTO {
	if dmResp == nil {
		return nil
	}

	msgResp := &message.StartTaskResponseDTO{
		Result:      dmResp.Result,
		Msg:         dmResp.Msg,
		CheckResult: dmResp.CheckResult,
		Sources:     make([]*message.CommonWorkerResponseDTO, len(dmResp.Sources)),
	}

	for i, source := range dmResp.Sources {
		msgResp.Sources[i] = c.ToCommonWorkerResponse(source)
	}

	return msgResp
}

// ToOperateTaskResponse converts dm.OperateTaskResponseDTO to message.OperateTaskResponseDTO
func (c *DMConverter) ToOperateTaskResponse(dmResp *dm.OperateTaskResponseDTO) *message.OperateTaskResponseDTO {
	if dmResp == nil {
		return nil
	}

	msgResp := &message.OperateTaskResponseDTO{
		Op:      dmResp.Op,
		Result:  dmResp.Result,
		Msg:     dmResp.Msg,
		Sources: make([]*message.CommonWorkerResponseDTO, len(dmResp.Sources)),
	}

	for i, source := range dmResp.Sources {
		msgResp.Sources[i] = c.ToCommonWorkerResponse(source)
	}

	return msgResp
}

// ToQueryStatusResponse converts dm.QueryStatusResponseDTO to message.QueryStatusResponseDTO
func (c *DMConverter) ToQueryStatusResponse(dmResp *dm.QueryStatusResponseDTO) *message.QueryStatusResponseDTO {
	if dmResp == nil {
		return nil
	}

	msgResp := &message.QueryStatusResponseDTO{
		Result:  dmResp.Result,
		Msg:     dmResp.Msg,
		Sources: make([]*message.QuerySourceStatus, len(dmResp.Sources)),
	}

	for i, source := range dmResp.Sources {
		msgResp.Sources[i] = c.ToQuerySourceStatus(source)
	}

	return msgResp
}

// ToQuerySourceStatus converts dm.QuerySourceStatus to message.QuerySourceStatus
func (c *DMConverter) ToQuerySourceStatus(dmStatus *dm.QuerySourceStatus) *message.QuerySourceStatus {
	if dmStatus == nil {
		return nil
	}

	msgStatus := &message.QuerySourceStatus{
		Result:       dmStatus.Result,
		Msg:          dmStatus.Msg,
		SourceStatus: c.ToSourceStatus(dmStatus.SourceStatus),
	}

	if len(dmStatus.SubTaskStatus) > 0 {
		msgStatus.SubTaskStatus = make([]*message.SubTaskStatus, len(dmStatus.SubTaskStatus))
		for i, subTask := range dmStatus.SubTaskStatus {
			msgStatus.SubTaskStatus[i] = c.ToSubTaskStatus(subTask)
		}
	}

	return msgStatus
}

// ToSourceStatus converts dm.SourceStatus to message.SourceStatus
func (c *DMConverter) ToSourceStatus(dmStatus *dm.SourceStatus) *message.SourceStatus {
	if dmStatus == nil {
		return nil
	}

	return &message.SourceStatus{
		Source: dmStatus.Source,
		Worker: dmStatus.Worker,
	}
}

// ToSubTaskStatus converts dm.SubTaskStatus to message.SubTaskStatus
func (c *DMConverter) ToSubTaskStatus(dmStatus *dm.SubTaskStatus) *message.SubTaskStatus {
	if dmStatus == nil {
		return nil
	}

	msgStatus := &message.SubTaskStatus{
		Name:       dmStatus.Name,
		Stage:      dmStatus.Stage,
		Unit:       dmStatus.Unit,
		Result:     c.ToSubTaskResult(dmStatus.Result),
		Status:     c.ToSubTaskStatusInfo(dmStatus.Status),
		Validation: c.ToValidationStatus(dmStatus.Validation),
	}

	return msgStatus
}

// ToSubTaskResult converts dm.SubTaskResult to message.SubTaskResult
func (c *DMConverter) ToSubTaskResult(dmResult *dm.SubTaskResult) *message.SubTaskResult {
	if dmResult == nil {
		return nil
	}

	msgResult := &message.SubTaskResult{
		IsCanceled: dmResult.IsCanceled,
	}

	if len(dmResult.Errors) > 0 {
		msgResult.Errors = make([]*message.SubTaskError, len(dmResult.Errors))
		for i, err := range dmResult.Errors {
			msgResult.Errors[i] = c.ToSubTaskError(err)
		}
	}

	return msgResult
}

// ToSubTaskError converts dm.SubTaskError to message.SubTaskError
func (c *DMConverter) ToSubTaskError(dmError *dm.SubTaskError) *message.SubTaskError {
	if dmError == nil {
		return nil
	}

	return &message.SubTaskError{
		ErrCode:    dmError.ErrCode,
		ErrClass:   dmError.ErrClass,
		ErrScope:   dmError.ErrScope,
		ErrLevel:   dmError.ErrLevel,
		Message:    dmError.Message,
		Workaround: dmError.Workaround,
	}
}

// ToSubTaskStatusInfo converts dm.SubTaskStatusInfo to message.SubTaskStatusInfo
func (c *DMConverter) ToSubTaskStatusInfo(dmInfo *dm.SubTaskStatusInfo) *message.SubTaskStatusInfo {
	if dmInfo == nil {
		return nil
	}

	return &message.SubTaskStatusInfo{
		Sync: c.ToSyncStatus(dmInfo.Sync),
	}
}

// ToSyncStatus converts dm.SyncStatus to message.SyncStatus
func (c *DMConverter) ToSyncStatus(dmSync *dm.SyncStatus) *message.SyncStatus {
	if dmSync == nil {
		return nil
	}

	return &message.SyncStatus{
		TotalEvents:      dmSync.TotalEvents,
		TotalTps:         dmSync.TotalTps,
		MasterBinlog:     dmSync.MasterBinlog,
		MasterBinlogGtid: dmSync.MasterBinlogGtid,
		SyncerBinlog:     dmSync.SyncerBinlog,
		SyncerBinlogGtid: dmSync.SyncerBinlogGtid,
		Synced:           dmSync.Synced,
		BinlogType:       dmSync.BinlogType,
		TotalRows:        dmSync.TotalRows,
		TotalRps:         dmSync.TotalRps,
	}
}

// ToValidationStatus converts dm.ValidationStatus to message.ValidationStatus
func (c *DMConverter) ToValidationStatus(dmValidation *dm.ValidationStatus) *message.ValidationStatus {
	if dmValidation == nil {
		return nil
	}

	return &message.ValidationStatus{
		Task:                dmValidation.Task,
		Source:              dmValidation.Source,
		Mode:                dmValidation.Mode,
		Stage:               dmValidation.Stage,
		ValidatorBinlog:     dmValidation.ValidatorBinlog,
		ValidatorBinlogGtid: dmValidation.ValidatorBinlogGtid,
		ProcessedRowsStatus: dmValidation.ProcessedRowsStatus,
		PendingRowsStatus:   dmValidation.PendingRowsStatus,
		ErrorRowsStatus:     dmValidation.ErrorRowsStatus,
	}
}

// ToTaskConfig converts dm.TaskConfigDTO to message.TaskConfigDTO
func (c *DMConverter) ToTaskConfig(dmTask *dm.TaskConfigDTO) *message.TaskConfigDTO {
	if dmTask == nil {
		return nil
	}

	msgTask := &message.TaskConfigDTO{
		Name:                       dmTask.Name,
		TaskMode:                   dmTask.TaskMode,
		IsSharding:                 dmTask.IsSharding,
		ShardMode:                  dmTask.ShardMode,
		StrictOptimisticShardMode:  dmTask.StrictOptimisticShardMode,
		IgnoreCheckingItems:        dmTask.IgnoreCheckingItems,
		MetaSchema:                 dmTask.MetaSchema,
		EnableHeartbeat:            dmTask.EnableHeartbeat,
		HeartbeatUpdateInterval:    dmTask.HeartbeatUpdateInterval,
		HeartbeatReportInterval:    dmTask.HeartbeatReportInterval,
		Timezone:                   dmTask.Timezone,
		CaseSensitive:              dmTask.CaseSensitive,
		CollationCompatible:        dmTask.CollationCompatible,
		OnlineDDL:                  dmTask.OnlineDDL,
		ShadowTableRules:           dmTask.ShadowTableRules,
		TrashTableRules:            dmTask.TrashTableRules,
		OnlineDDLScheme:            dmTask.OnlineDDLScheme,
		CleanDumpFile:              dmTask.CleanDumpFile,
		AnsiQuotes:                 dmTask.AnsiQuotes,
		RemoveMeta:                 dmTask.RemoveMeta,
	}

	// Convert target database
	if dmTask.TargetDatabase != nil {
		msgTask.TargetDatabase = &message.TaskTargetDatabase{
			Host:             dmTask.TargetDatabase.Host,
			Port:             dmTask.TargetDatabase.Port,
			User:             dmTask.TargetDatabase.User,
			Password:         dmTask.TargetDatabase.Password,
			MaxAllowedPacket: dmTask.TargetDatabase.MaxAllowedPacket,
			Session:          dmTask.TargetDatabase.Session,
			Security:         c.ToSecurityConfig(dmTask.TargetDatabase.Security),
		}
	}

	// Convert MySQL instances
	if len(dmTask.MySQLInstances) > 0 {
		msgTask.MySQLInstances = make([]*message.MySQLInstance, len(dmTask.MySQLInstances))
		for i, instance := range dmTask.MySQLInstances {
			msgTask.MySQLInstances[i] = c.ToMySQLInstance(instance)
		}
	}

	// Convert routes
	if len(dmTask.Routes) > 0 {
		msgTask.Routes = make(map[string]*message.RouteRule)
		for k, v := range dmTask.Routes {
			msgTask.Routes[k] = c.ToRouteRule(v)
		}
	}

	// Convert filters
	if len(dmTask.Filters) > 0 {
		msgTask.Filters = make(map[string]*message.BinlogEventRule)
		for k, v := range dmTask.Filters {
			msgTask.Filters[k] = c.ToBinlogEventRule(v)
		}
	}

	// Convert block allow list
	if len(dmTask.BlockAllowList) > 0 {
		msgTask.BlockAllowList = make(map[string]*message.BlockAllowListRule)
		for k, v := range dmTask.BlockAllowList {
			msgTask.BlockAllowList[k] = c.ToBlockAllowListRule(v)
		}
	}

	// Convert Mydumpers
	if len(dmTask.Mydumpers) > 0 {
		msgTask.Mydumpers = make(map[string]*message.MydumperConfig)
		for k, v := range dmTask.Mydumpers {
			msgTask.Mydumpers[k] = c.ToMydumperConfig(v)
		}
	}

	// Convert Loaders
	if len(dmTask.Loaders) > 0 {
		msgTask.Loaders = make(map[string]*message.LoaderConfig)
		for k, v := range dmTask.Loaders {
			msgTask.Loaders[k] = c.ToLoaderConfig(v)
		}
	}

	// Convert Syncers
	if len(dmTask.Syncers) > 0 {
		msgTask.Syncers = make(map[string]*message.SyncerConfig)
		for k, v := range dmTask.Syncers {
			msgTask.Syncers[k] = c.ToSyncerConfig(v)
		}
	}

	// Convert Validators
	if len(dmTask.Validators) > 0 {
		msgTask.Validators = make(map[string]*message.ValidatorConfig)
		for k, v := range dmTask.Validators {
			msgTask.Validators[k] = c.ToValidatorConfig(v)
		}
	}

	// Convert Experimental config
	if dmTask.Experimental != nil {
		msgTask.Experimental = c.ToExperimentalConfig(dmTask.Experimental)
	}

	return msgTask
}

// ToMySQLInstance converts dm.MySQLInstance to message.MySQLInstance
func (c *DMConverter) ToMySQLInstance(dmInstance *dm.MySQLInstance) *message.MySQLInstance {
	if dmInstance == nil {
		return nil
	}

	return &message.MySQLInstance{
		SourceID:            dmInstance.SourceID,
		Meta:                c.ToMeta(dmInstance.Meta),
		FilterRules:         dmInstance.FilterRules,
		ColumnMappingRules:  dmInstance.ColumnMappingRules,
		RouteRules:          dmInstance.RouteRules,
		ExpressionFilters:   dmInstance.ExpressionFilters,
		BlockAllowList:      dmInstance.BlockAllowList,
		MydumperConfigName:  dmInstance.MydumperConfigName,
		Mydumper:            c.ToMydumperConfig(dmInstance.Mydumper),
		MydumperThread:      dmInstance.MydumperThread,
		LoaderConfigName:    dmInstance.LoaderConfigName,
		Loader:              c.ToLoaderConfig(dmInstance.Loader),
		LoaderThread:        dmInstance.LoaderThread,
		SyncerConfigName:    dmInstance.SyncerConfigName,
		Syncer:              c.ToSyncerConfig(dmInstance.Syncer),
		SyncerThread:        dmInstance.SyncerThread,
		ValidatorConfigName: dmInstance.ValidatorConfigName,
	}
}

// ToRouteRule converts dm.RouteRule to message.RouteRule
func (c *DMConverter) ToRouteRule(dmRule *dm.RouteRule) *message.RouteRule {
	if dmRule == nil {
		return nil
	}

	return &message.RouteRule{
		SchemaPattern: dmRule.SchemaPattern,
		TablePattern:  dmRule.TablePattern,
		TargetSchema:  dmRule.TargetSchema,
		TargetTable:   dmRule.TargetTable,
	}
}

// ToBinlogEventRule converts dm.BinlogEventRule to message.BinlogEventRule
func (c *DMConverter) ToBinlogEventRule(dmRule *dm.BinlogEventRule) *message.BinlogEventRule {
	if dmRule == nil {
		return nil
	}

	return &message.BinlogEventRule{
		SchemaPattern: dmRule.SchemaPattern,
		TablePattern:  dmRule.TablePattern,
		Events:        dmRule.Events,
		SQLPattern:    dmRule.SQLPattern,
		Action:        dmRule.Action,
	}
}

// ToBlockAllowListRule converts dm.BlockAllowListRule to message.BlockAllowListRule
func (c *DMConverter) ToBlockAllowListRule(dmRule *dm.BlockAllowListRule) *message.BlockAllowListRule {
	if dmRule == nil {
		return nil
	}

	msgRule := &message.BlockAllowListRule{
		DoDbs:     dmRule.DoDbs,
		IgnoreDbs: dmRule.IgnoreDbs,
	}

	// Convert DoTables
	if len(dmRule.DoTables) > 0 {
		msgRule.DoTables = make([]*message.TableRef, len(dmRule.DoTables))
		for i, table := range dmRule.DoTables {
			msgRule.DoTables[i] = &message.TableRef{
				DbName:  table.DbName,
				TblName: table.TblName,
			}
		}
	}

	// Convert IgnoreTables
	if len(dmRule.IgnoreTables) > 0 {
		msgRule.IgnoreTables = make([]*message.TableRef, len(dmRule.IgnoreTables))
		for i, table := range dmRule.IgnoreTables {
			msgRule.IgnoreTables[i] = &message.TableRef{
				DbName:  table.DbName,
				TblName: table.TblName,
			}
		}
	}

	return msgRule
}

// ToWorkerConfig converts dm.WorkerConfigDTO to message.WorkerConfigDTO
func (c *DMConverter) ToWorkerConfig(dmWorker *dm.WorkerConfigDTO) *message.WorkerConfigDTO {
	if dmWorker == nil {
		return nil
	}

	return &message.WorkerConfigDTO{
		Name:              dmWorker.Name,
		LogLevel:          dmWorker.LogLevel,
		LogFile:           dmWorker.LogFile,
		LogFormat:         dmWorker.LogFormat,
		LogRotate:         dmWorker.LogRotate,
		Join:              dmWorker.Join,
		WorkerAddr:        dmWorker.WorkerAddr,
		AdvertiseAddr:     dmWorker.AdvertiseAddr,
		ConfigFile:        dmWorker.ConfigFile,
		KeepaliveTTL:      dmWorker.KeepaliveTTL,
		RelayKeepaliveTTL: dmWorker.RelayKeepaliveTTL,
		RelayDir:          dmWorker.RelayDir,
		SSLCA:             dmWorker.SSLCA,
		SSLCert:           dmWorker.SSLCert,
		SSLKey:            dmWorker.SSLKey,
		SSLCABase64:       dmWorker.SSLCABase64,
		SSLKeyBase64:      dmWorker.SSLKeyBase64,
		SSLCertBase64:     dmWorker.SSLCertBase64,
	}
}

// ToMasterConfig converts dm.MasterConfigDTO to message.MasterConfigDTO
func (c *DMConverter) ToMasterConfig(dmMaster *dm.MasterConfigDTO) *message.MasterConfigDTO {
	if dmMaster == nil {
		return nil
	}

	return &message.MasterConfigDTO{
		LogLevel:                dmMaster.LogLevel,
		LogFile:                 dmMaster.LogFile,
		LogFormat:               dmMaster.LogFormat,
		LogRotate:               dmMaster.LogRotate,
		RPCTimeout:              dmMaster.RPCTimeout,
		RPCRateLimit:            dmMaster.RPCRateLimit,
		RPCRateBurst:            dmMaster.RPCRateBurst,
		MasterAddr:              dmMaster.MasterAddr,
		AdvertiseAddr:           dmMaster.AdvertiseAddr,
		ConfigFile:              dmMaster.ConfigFile,
		Name:                    dmMaster.Name,
		DataDir:                 dmMaster.DataDir,
		PeerURLs:                dmMaster.PeerURLs,
		AdvertisePeerURLs:       dmMaster.AdvertisePeerURLs,
		InitialCluster:          dmMaster.InitialCluster,
		InitialClusterState:     dmMaster.InitialClusterState,
		Join:                    dmMaster.Join,
		MaxTxnOps:               dmMaster.MaxTxnOps,
		MaxRequestBytes:         dmMaster.MaxRequestBytes,
		AutoCompactionMode:      dmMaster.AutoCompactionMode,
		AutoCompactionRetention: dmMaster.AutoCompactionRetention,
		QuotaBackendBytes:       dmMaster.QuotaBackendBytes,
		OpenAPI:                 dmMaster.OpenAPI,
		V1SourcesPath:           dmMaster.V1SourcesPath,
		SSLCA:                   dmMaster.SSLCA,
		SSLCert:                 dmMaster.SSLCert,
		SSLKey:                  dmMaster.SSLKey,
		SSLCABase64:             dmMaster.SSLCABase64,
		SSLKeyBase64:            dmMaster.SSLKeyBase64,
		SSLCertBase64:           dmMaster.SSLCertBase64,
		SecretKeyPath:           dmMaster.SecretKeyPath,
		Experimental:            dmMaster.Experimental,
	}
}

// ToMeta converts dm.Meta to message.Meta
func (c *DMConverter) ToMeta(dmMeta *dm.Meta) *message.Meta {
	if dmMeta == nil {
		return nil
	}

	return &message.Meta{
		BinLogName: dmMeta.BinLogName,
		BinLogPos:  dmMeta.BinLogPos,
		BinLogGTID: dmMeta.BinLogGTID,
	}
}

// FromMeta converts message.Meta to dm.Meta
func (c *DMConverter) FromMeta(msgMeta *message.Meta) *dm.Meta {
	if msgMeta == nil {
		return nil
	}

	return &dm.Meta{
		BinLogName: msgMeta.BinLogName,
		BinLogPos:  msgMeta.BinLogPos,
		BinLogGTID: msgMeta.BinLogGTID,
	}
}

// ToSecurityConfig converts dm.SecurityConfig to message.SecurityConfig
func (c *DMConverter) ToSecurityConfig(dmSecurity *dm.SecurityConfig) *message.SecurityConfig {
	if dmSecurity == nil {
		return nil
	}

	return &message.SecurityConfig{
		SSLCa:   dmSecurity.SSLCa,
		SSLCert: dmSecurity.SSLCert,
		SSLKey:  dmSecurity.SSLKey,
	}
}

// FromSecurityConfig converts message.SecurityConfig to dm.SecurityConfig
func (c *DMConverter) FromSecurityConfig(msgSecurity *message.SecurityConfig) *dm.SecurityConfig {
	if msgSecurity == nil {
		return nil
	}

	return &dm.SecurityConfig{
		SSLCa:   msgSecurity.SSLCa,
		SSLCert: msgSecurity.SSLCert,
		SSLKey:  msgSecurity.SSLKey,
	}
}

// ToExperimentalConfig converts dm.ExperimentalConfig to message.ExperimentalConfig
func (c *DMConverter) ToExperimentalConfig(dmExp *dm.ExperimentalConfig) *message.ExperimentalConfig {
	if dmExp == nil {
		return nil
	}

	return &message.ExperimentalConfig{
		AsyncCheckpointFlush: dmExp.AsyncCheckpointFlush,
	}
}

// FromExperimentalConfig converts message.ExperimentalConfig to dm.ExperimentalConfig
func (c *DMConverter) FromExperimentalConfig(msgExp *message.ExperimentalConfig) *dm.ExperimentalConfig {
	if msgExp == nil {
		return nil
	}

	return &dm.ExperimentalConfig{
		AsyncCheckpointFlush: msgExp.AsyncCheckpointFlush,
	}
}

// ToMydumperConfig converts dm.MydumperConfig to message.MydumperConfig
func (c *DMConverter) ToMydumperConfig(dmConfig *dm.MydumperConfig) *message.MydumperConfig {
	if dmConfig == nil {
		return nil
	}

	return &message.MydumperConfig{
		MydumperPath:  dmConfig.MydumperPath,
		Threads:       dmConfig.Threads,
		ChunkFilesize: dmConfig.ChunkFilesize,
		StatementSize: dmConfig.StatementSize,
		Rows:          dmConfig.Rows,
		Where:         dmConfig.Where,
		SkipTzUtc:     dmConfig.SkipTzUtc,
		ExtraArgs:     dmConfig.ExtraArgs,
	}
}

// FromMydumperConfig converts message.MydumperConfig to dm.MydumperConfig
func (c *DMConverter) FromMydumperConfig(msgConfig *message.MydumperConfig) *dm.MydumperConfig {
	if msgConfig == nil {
		return nil
	}

	return &dm.MydumperConfig{
		MydumperPath:  msgConfig.MydumperPath,
		Threads:       msgConfig.Threads,
		ChunkFilesize: msgConfig.ChunkFilesize,
		StatementSize: msgConfig.StatementSize,
		Rows:          msgConfig.Rows,
		Where:         msgConfig.Where,
		SkipTzUtc:     msgConfig.SkipTzUtc,
		ExtraArgs:     msgConfig.ExtraArgs,
	}
}

// ToLoaderConfig converts dm.LoaderConfig to message.LoaderConfig
func (c *DMConverter) ToLoaderConfig(dmConfig *dm.LoaderConfig) *message.LoaderConfig {
	if dmConfig == nil {
		return nil
	}

	return &message.LoaderConfig{
		PoolSize:            dmConfig.PoolSize,
		Dir:                 dmConfig.Dir,
		SortingDirPhysical:  dmConfig.SortingDirPhysical,
		ImportMode:          dmConfig.ImportMode,
		OnDuplicate:         dmConfig.OnDuplicate,
		OnDuplicateLogical:  dmConfig.OnDuplicateLogical,
		OnDuplicatePhysical: dmConfig.OnDuplicatePhysical,
		DiskQuotaPhysical:   dmConfig.DiskQuotaPhysical,
		ChecksumPhysical:    dmConfig.ChecksumPhysical,
		Analyze:             dmConfig.Analyze,
		RangeConcurrency:    dmConfig.RangeConcurrency,
		CompressKVPairs:     dmConfig.CompressKVPairs,
		PdAddr:              dmConfig.PdAddr,
	}
}

// FromLoaderConfig converts message.LoaderConfig to dm.LoaderConfig
func (c *DMConverter) FromLoaderConfig(msgConfig *message.LoaderConfig) *dm.LoaderConfig {
	if msgConfig == nil {
		return nil
	}

	return &dm.LoaderConfig{
		PoolSize:            msgConfig.PoolSize,
		Dir:                 msgConfig.Dir,
		SortingDirPhysical:  msgConfig.SortingDirPhysical,
		ImportMode:          msgConfig.ImportMode,
		OnDuplicate:         msgConfig.OnDuplicate,
		OnDuplicateLogical:  msgConfig.OnDuplicateLogical,
		OnDuplicatePhysical: msgConfig.OnDuplicatePhysical,
		DiskQuotaPhysical:   msgConfig.DiskQuotaPhysical,
		ChecksumPhysical:    msgConfig.ChecksumPhysical,
		Analyze:             msgConfig.Analyze,
		RangeConcurrency:    msgConfig.RangeConcurrency,
		CompressKVPairs:     msgConfig.CompressKVPairs,
		PdAddr:              msgConfig.PdAddr,
	}
}

// ToSyncerConfig converts dm.SyncerConfig to message.SyncerConfig
func (c *DMConverter) ToSyncerConfig(dmConfig *dm.SyncerConfig) *message.SyncerConfig {
	if dmConfig == nil {
		return nil
	}

	return &message.SyncerConfig{
		MetaFile:                dmConfig.MetaFile,
		WorkerCount:             dmConfig.WorkerCount,
		Batch:                   dmConfig.Batch,
		QueueSize:               dmConfig.QueueSize,
		CheckpointFlushInterval: dmConfig.CheckpointFlushInterval,
		Compact:                 dmConfig.Compact,
		MultipleRows:            dmConfig.MultipleRows,
		MaxRetry:                dmConfig.MaxRetry,
		AutoFixGtid:             dmConfig.AutoFixGtid,
		EnableGtid:              dmConfig.EnableGtid,
		DisableDetect:           dmConfig.DisableDetect,
		SafeMode:                dmConfig.SafeMode,
		SafeModeDuration:        dmConfig.SafeModeDuration,
		EnableAnsiQuotes:        dmConfig.EnableAnsiQuotes,
	}
}

// FromSyncerConfig converts message.SyncerConfig to dm.SyncerConfig
func (c *DMConverter) FromSyncerConfig(msgConfig *message.SyncerConfig) *dm.SyncerConfig {
	if msgConfig == nil {
		return nil
	}

	return &dm.SyncerConfig{
		MetaFile:                msgConfig.MetaFile,
		WorkerCount:             msgConfig.WorkerCount,
		Batch:                   msgConfig.Batch,
		QueueSize:               msgConfig.QueueSize,
		CheckpointFlushInterval: msgConfig.CheckpointFlushInterval,
		Compact:                 msgConfig.Compact,
		MultipleRows:            msgConfig.MultipleRows,
		MaxRetry:                msgConfig.MaxRetry,
		AutoFixGtid:             msgConfig.AutoFixGtid,
		EnableGtid:              msgConfig.EnableGtid,
		DisableDetect:           msgConfig.DisableDetect,
		SafeMode:                msgConfig.SafeMode,
		SafeModeDuration:        msgConfig.SafeModeDuration,
		EnableAnsiQuotes:        msgConfig.EnableAnsiQuotes,
	}
}

// ToValidatorConfig converts dm.ValidatorConfig to message.ValidatorConfig
func (c *DMConverter) ToValidatorConfig(dmConfig *dm.ValidatorConfig) *message.ValidatorConfig {
	if dmConfig == nil {
		return nil
	}

	return &message.ValidatorConfig{
		Mode:               dmConfig.Mode,
		WorkerCount:        dmConfig.WorkerCount,
		ValidateInterval:   dmConfig.ValidateInterval,
		CheckInterval:      dmConfig.CheckInterval,
		RowErrorDelay:      dmConfig.RowErrorDelay,
		MetaFlushInterval:  dmConfig.MetaFlushInterval,
		BatchQuerySize:     dmConfig.BatchQuerySize,
		MaxPendingRowSize:  dmConfig.MaxPendingRowSize,
		MaxPendingRowCount: dmConfig.MaxPendingRowCount,
	}
}

// FromValidatorConfig converts message.ValidatorConfig to dm.ValidatorConfig
func (c *DMConverter) FromValidatorConfig(msgConfig *message.ValidatorConfig) *dm.ValidatorConfig {
	if msgConfig == nil {
		return nil
	}

	return &dm.ValidatorConfig{
		Mode:               msgConfig.Mode,
		WorkerCount:        msgConfig.WorkerCount,
		ValidateInterval:   msgConfig.ValidateInterval,
		CheckInterval:      msgConfig.CheckInterval,
		RowErrorDelay:      msgConfig.RowErrorDelay,
		MetaFlushInterval:  msgConfig.MetaFlushInterval,
		BatchQuerySize:     msgConfig.BatchQuerySize,
		MaxPendingRowSize:  msgConfig.MaxPendingRowSize,
		MaxPendingRowCount: msgConfig.MaxPendingRowCount,
	}
}