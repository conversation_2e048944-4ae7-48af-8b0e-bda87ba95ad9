package mysql

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/pkg/dm"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/google/uuid"
	"github.com/panjf2000/ants/v2"
	"gopkg.in/yaml.v3"
)

// CheckProgress 检查进度跟踪结构体
type CheckProgress struct {
	RequestID       string              `json:"requestId"`
	OverallStatus   string              `json:"overallStatus"`
	OverallProgress int                 `json:"overallProgress"`
	CheckItems      []message.CheckItem `json:"checkItems"`
	StartTime       time.Time           `json:"startTime"`
	EndTime         *time.Time          `json:"endTime"`
	mu              sync.RWMutex        `json:"-"`
}

type Service struct {
	converter *DMConverter
	// 内存中维护检查进度的映射
	checkProgress map[string]*CheckProgress
	progressMu    sync.RWMutex
}

func NewMySQLService() *Service {
	return &Service{
		converter:     NewDMConverter(),
		checkProgress: make(map[string]*CheckProgress),
	}
}

func (s *Service) ListMembers(ctx context.Context, req *message.ListMembersReq) (*message.ListMembersResp, error) {
	log.Infof("ListMembers: starting to list DM cluster members, masterAddr: %s", req.MasterAddr)

	dmAdaptor, err := dm.NewDMAdaptor(req.MasterAddr)
	if err != nil {
		log.Errorf("ListMembers: failed to create DM adaptor for masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}

	members, err := dmAdaptor.ListMember(ctx)
	if err != nil {
		log.Errorf("ListMembers: failed to list members from DM cluster, error: %v", err)
		return nil, err
	}

	log.Infof("ListMembers: successfully retrieved members, masters: %d, workers: %d", len(members.Masters), len(members.Workers))

	resp := &message.ListMembersResp{
		Leader:  &message.Leader{Message: members.Leader.Message, Name: members.Leader.Name, Addr: members.Leader.Addr},
		Masters: make([]*message.Master, len(members.Masters)),
		Workers: make([]*message.Worker, len(members.Workers)),
		Msg:     members.Msg,
		Result:  members.Result,
	}
	for i, master := range members.Masters {
		resp.Masters[i] = &message.Master{Name: master.Name, MemberID: master.MemberID, Alive: master.Alive, PeerURLs: master.PeerURLs, ClientURLs: master.ClientURLs}
	}

	for i, worker := range members.Workers {
		resp.Workers[i] = &message.Worker{Name: worker.Name, Addr: worker.Addr, Stage: worker.Stage, Source: worker.Source}
	}

	log.Infof("ListMembers: response prepared with leader: %s, masters: %d, workers: %d", resp.Leader.Name, len(resp.Masters), len(resp.Workers))
	return resp, nil
}

func (s *Service) PreviewConfiguration(ctx context.Context, req *message.PreviewConfigurationReq) (*message.PreviewConfigurationResp, error) {
	log.Infof("PreviewConfiguration: starting to preview configuration with DM, channelId: %d, masterAddr: %s", req.ChannelId, req.MasterAddr)

	// Step 1: Fetch channel information
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("PreviewConfiguration: failed to get channel info for channelId: %d, error: %v", req.ChannelId, err)
		return nil, errors.NewError(errors.TIMS_RECORD_NOT_EXIST, "channel not found")
	}
	log.Debugf("PreviewConfiguration: fetched channel info for channel: %s", channelInfo.ChannelName)

	// Step 2: Fetch target datasource
	targetDatasource, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		log.Errorf("PreviewConfiguration: failed to get target datasource for datasourceId: %d, error: %v", channelInfo.DatasourceIdT, err)
		return nil, errors.NewError(errors.TIMS_DM_TARGET_DB_NOT_FOUND, "target datasource not found")
	}
	log.Debugf("PreviewConfiguration: fetched target datasource: %s", targetDatasource.DatasourceName)

	// Step 3: Fetch source datasources through channel datasources
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("PreviewConfiguration: failed to get channel datasources for channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	sourceDatasources := make([]*datasource.Datasource, 0)
	for _, cd := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, cd.DataSourceId)
		if err != nil {
			log.Warnf("PreviewConfiguration: failed to get datasource for datasourceId: %d, error: %v", cd.DataSourceId, err)
			continue
		}
		// Only include MySQL/TiDB datasources
		if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB {
			sourceDatasources = append(sourceDatasources, ds)
			log.Debugf("PreviewConfiguration: added source datasource: %s", ds.DatasourceName)
		}
	}

	if len(sourceDatasources) == 0 {
		log.Errorf("PreviewConfiguration: no valid source datasources found for channelId: %d", req.ChannelId)
		return nil, errors.NewError(errors.TIMS_DM_SOURCE_DB_NOT_FOUND, "no source datasources found")
	}

	// Step 4: Fetch schema tables (might be empty)
	schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Warnf("PreviewConfiguration: failed to get schema tables, will continue without them, error: %v", err)
		schemaTables = []*channel.ChannelSchemaTable{}
	}
	filteredSchemaTables := make([]*channel.ChannelSchemaTable, 0)
	for _, schemaTable := range schemaTables {
		if schemaTable.TaskId == 0 {
			continue
		}
		filteredSchemaTables = append(filteredSchemaTables, schemaTable)
	}
	log.Debugf("PreviewConfiguration: fetched %d schema tables, filteredSchemaTables %d", len(schemaTables), len(filteredSchemaTables))

	// Step 5: Fetch channel schemas as fallback
	channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
	if err != nil {
		log.Warnf("PreviewConfiguration: failed to get channel schemas, will continue without them, error: %v", err)
		channelSchemas = []*channel.ChannelSchema{}
	}
	log.Debugf("PreviewConfiguration: fetched %d channel schemas", len(channelSchemas))

	// Step 6: Create builder and build configuration
	builder := NewTaskConfigBuilder(s.converter)
	taskConfig, err := builder.BuildTaskConfig(
		channelInfo,
		targetDatasource,
		sourceDatasources,
		filteredSchemaTables,
		channelSchemas,
	)
	if err != nil {
		log.Errorf("PreviewConfiguration: failed to build task config for channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	log.Infof("PreviewConfiguration: successfully built task configuration for channelId: %d, task name: %s", req.ChannelId, taskConfig.Name)

	return &message.PreviewConfigurationResp{
		Config: *taskConfig,
	}, nil
}

func (s *Service) PreviewSources(ctx context.Context, req *message.PreviewSourcesReq) (*message.PreviewSourcesResp, error) {
	log.Infof("PreviewSources: starting to preview sources to DM sources, channelId: %d", req.ChannelId)

	// Get all datasources associated with the channel
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("PreviewSources: failed to get channel datasources for channelId: %d, error: %v", req.ChannelId, err)
		return nil, err
	}

	log.Infof("PreviewSources: found %d datasources for channel %d", len(channelDatasources), req.ChannelId)

	// Prepare response with DM source configurations
	configs := make([]message.CustomSourceConfigDTO, 0, len(channelDatasources))

	// Convert each datasource to DM format
	for _, channelDs := range channelDatasources {
		// Get datasource details
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, channelDs.DataSourceId)
		if err != nil {
			log.Errorf("PreviewSources: failed to get datasource details for datasourceId: %d, error: %v", channelDs.DataSourceId, err)
			continue // Skip this datasource but continue with others
		}

		// Only process MySQL/TiDB datasources
		if ds.DbType != constants.DB_TYPE_MYSQL && ds.DbType != constants.DB_TYPE_TIDB {
			log.Debugf("PreviewSources: skipping non-MySQL datasource %s with type %s", ds.DatasourceName, ds.DbType)
			continue
		}

		// Convert to DM source configuration
		config := message.CustomSourceConfigDTO{
			SourceID:        ds.DatasourceName, // Use datasource name as source-id
			EnableGTID:      false,             // Default to false
			EnableRelay:     false,             // Default to false
			RelayBinlogName: "",                // Empty by default
			RelayBinlogGTID: "",                // Empty by default
			From: message.CustomDatabaseConfig{
				Host:     ds.HostIp,
				Port:     ds.HostPort,
				User:     ds.UserName,
				Password: ds.PasswordValue, // Use plaintext password as requested
				Security: nil,              // SSL configuration not available in datasource model
			},
		}

		configs = append(configs, config)
		log.Debugf("PreviewSources: converted datasource %s to DM format", ds.DatasourceName)
	}

	log.Infof("PreviewSources: successfully converted %d datasources to DM format", len(configs))

	return &message.PreviewSourcesResp{
		Configs: configs,
	}, nil
}

func (s *Service) ListSources(ctx context.Context, req *message.ListSourcesReq) (*message.ListSourcesResp, error) {
	log.Infof("ListSources: starting to list DM sources, masterAddr: %s", req.MasterAddr)

	dmAdaptor, err := dm.NewDMAdaptor(req.MasterAddr)
	if err != nil {
		log.Errorf("ListSources: failed to create DM adaptor for masterAddr: %s, error: %v", req.MasterAddr, err)
		return nil, err
	}

	// Get all sources using ShowSource with empty sourceIDs array to list all
	log.Info("ListSources: fetching all sources from DM cluster")
	sources, err := dmAdaptor.ShowSource(ctx, []string{})
	if err != nil {
		log.Errorf("ListSources: failed to show sources from DM cluster, error: %v", err)
		return nil, err
	}

	log.Infof("ListSources: retrieved %d sources from DM cluster", len(sources.Sources))

	resp := &message.ListSourcesResp{
		Sources: make([]*message.Source, len(sources.Sources)),
		Msg:     sources.Msg,
		Result:  sources.Result,
	}

	// Convert each source first
	for i, source := range sources.Sources {
		resp.Sources[i] = &message.Source{
			Source: source.Source,
			Worker: source.Worker,
			Result: source.Result,
			Msg:    source.Msg,
		}
	}

	// Use ants goroutine pool for config fetching optimization
	if len(sources.Sources) > 0 {
		// Create pool with reasonable size (max 10 workers for config fetching)
		poolSize := 10
		if len(sources.Sources) < poolSize {
			poolSize = len(sources.Sources)
		}

		log.Infof("ListSources: creating goroutine pool with size %d to fetch source configs", poolSize)
		pool, err := ants.NewPool(poolSize)
		if err != nil {
			// Fallback: if pool creation fails, continue without config enrichment
			log.Warnf("ListSources: failed to create goroutine pool, continuing without config enrichment, error: %v", err)
			return resp, nil
		}
		defer pool.Release()

		// Use WaitGroup to wait for all config fetch tasks
		var wg sync.WaitGroup
		var mu sync.Mutex // Protect concurrent access to resp.Sources

		// Submit config fetch tasks to pool
		for i, source := range sources.Sources {
			if source.Source == "" {
				continue
			}

			wg.Add(1)
			idx := i
			sourceName := source.Source

			pool.Submit(func() {
				defer wg.Done()

				if config, configErr := dmAdaptor.GetSourceConfig(ctx, sourceName); configErr == nil {
					mu.Lock()
					resp.Sources[idx].Config = s.converter.ToCustomSourceConfig(config)
					mu.Unlock()
					log.Debugf("ListSources: successfully fetched config for source: %s", sourceName)
				} else {
					log.Debugf("ListSources: failed to fetch config for source: %s, error: %v", sourceName, configErr)
				}
			})
		}

		// Wait for all config fetch tasks to complete
		wg.Wait()
		log.Info("ListSources: completed fetching all source configs")
	}

	log.Info("ListSources: fetching datasources from database for password enrichment")
	datasources, _, err := models.GetDatasourceReaderWriter().List(ctx, 1, 1000, "", constants.DB_TYPE_MYSQL, "")
	if err != nil {
		log.Errorf("ListSources: failed to list datasources, error: %v", err)
		return nil, err
	}
	log.Infof("ListSources: retrieved %d datasources from database", len(datasources))

	// Fill passwords from datasources matching by source_id, name, host, port
	if len(datasources) > 0 {
		log.Info("ListSources: filling source passwords from datasources")
		s.fillSourcePasswords(resp.Sources, datasources)
	}

	log.Infof("ListSources: completed successfully, returning %d sources", len(resp.Sources))
	return resp, nil
}

// fillSourcePasswords fills missing passwords in sources by matching with datasources
// Uses strict AND matching: source name AND host AND port must all match
func (s *Service) fillSourcePasswords(sources []*message.Source, datasources []*datasource.Datasource) {
	log.Debugf("fillSourcePasswords: starting to fill passwords for %d sources using %d datasources", len(sources), len(datasources))

	// Fill passwords for each source
	passwordsFilled := 0
	for _, source := range sources {
		if source.Config == nil {
			continue // Skip if no config
		}

		// Find matching datasource using strict AND criteria
		var matchedDatasource *datasource.Datasource
		for _, ds := range datasources {
			// Strict matching: name AND host AND port must all match
			if source.Source == ds.DatasourceName &&
				source.Config.From.Host == ds.HostIp &&
				source.Config.From.Port == ds.HostPort {
				matchedDatasource = ds
				break
			}
		}

		// Fill encrypted password if match found
		if matchedDatasource != nil && matchedDatasource.PasswordEncrypt != "" {
			source.Config.From.PasswordEncrypt = matchedDatasource.PasswordEncrypt
			passwordsFilled++
			log.Debugf("fillSourcePasswords: filled password for source: %s", source.Source)
		}
	}

	log.Infof("fillSourcePasswords: completed, filled passwords for %d sources", passwordsFilled)
}

// PreviewAll previews all DM configurations in a tree structure
func (s *Service) PreviewAll(ctx context.Context, req *message.PreviewAllReq) (*message.PreviewAllResp, error) {
	log.Infof("PreviewAll: starting to preview all configurations, channelId: %d, masterAddr: %s", req.ChannelId, req.MasterAddr)

	// Create root node
	rootNode := &message.DMConfigTreeNode{
		Key:      "dm-migration-root",
		Title:    "DM Migration Configuration",
		IsLeaf:   false,
		Children: make([]*message.DMConfigTreeNode, 0),
	}

	// Create sources node
	sourcesNode := &message.DMConfigTreeNode{
		Key:      "sources-root",
		Title:    "数据源配置",
		IsLeaf:   false,
		Children: make([]*message.DMConfigTreeNode, 0),
	}

	// Get source configurations
	sourcesReq := &message.PreviewSourcesReq{
		ChannelId: req.ChannelId,
	}
	sourcesResp, err := s.PreviewSources(ctx, sourcesReq)
	if err != nil {
		log.Errorf("PreviewAll: failed to preview sources, error: %v", err)
		// Continue with empty sources rather than failing
		sourcesResp = &message.PreviewSourcesResp{
			Configs: []message.CustomSourceConfigDTO{},
		}
	}

	// Add source configurations to tree
	for i, sourceConfig := range sourcesResp.Configs {
		sourceYAML, err := toYAML(sourceConfig)
		if err != nil {
			log.Warnf("PreviewAll: failed to convert source config to YAML, source: %s, error: %v", sourceConfig.SourceID, err)
			sourceYAML = fmt.Sprintf("# Error converting to YAML: %v", err)
		}

		sourceNode := &message.DMConfigTreeNode{
			Key:     fmt.Sprintf("source_%s_%d", sourceConfig.SourceID, i),
			Title:   fmt.Sprintf("source_%s.yaml", sourceConfig.SourceID),
			Context: sourceYAML,
			IsLeaf:  true,
		}
		sourcesNode.Children = append(sourcesNode.Children, sourceNode)
	}

	// Add sources node to root
	rootNode.Children = append(rootNode.Children, sourcesNode)

	// Create task configuration node
	taskNode := &message.DMConfigTreeNode{
		Key:      "task-root",
		Title:    "Task Configuration",
		IsLeaf:   false,
		Children: make([]*message.DMConfigTreeNode, 0),
	}

	// Get task configuration
	configReq := &message.PreviewConfigurationReq{
		ChannelId:  req.ChannelId,
		MasterAddr: req.MasterAddr,
	}
	configResp, err := s.PreviewConfiguration(ctx, configReq)
	if err != nil {
		log.Errorf("PreviewAll: failed to preview configuration, error: %v", err)
		// Continue with empty task config rather than failing
	} else {
		taskYAML, err := toYAML(configResp.Config)
		if err != nil {
			log.Warnf("PreviewAll: failed to convert task config to YAML, error: %v", err)
			taskYAML = fmt.Sprintf("# Error converting to YAML: %v", err)
		}

		taskConfigNode := &message.DMConfigTreeNode{
			Key:     fmt.Sprintf("task_%s", configResp.Config.Name),
			Title:   fmt.Sprintf("task_%s.yaml", configResp.Config.Name),
			Context: taskYAML,
			IsLeaf:  true,
		}
		taskNode.Children = append(taskNode.Children, taskConfigNode)
	}

	// Add task node to root
	rootNode.Children = append(rootNode.Children, taskNode)

	log.Infof("PreviewAll: successfully built configuration tree with %d source(s) and task configuration", len(sourcesResp.Configs))

	return &message.PreviewAllResp{
		Tree: rootNode,
	}, nil
}

// toYAML converts a struct to YAML string
func toYAML(v interface{}) (string, error) {
	data, err := yaml.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// TriggerCheck 触发检查
func (s *Service) TriggerCheck(ctx context.Context, req *message.TriggerCheckReq) (*message.TriggerCheckResp, error) {
	log.Infof("TriggerCheck: starting comprehensive check for channelId: %d, masterAddr: %s", req.ChannelId, req.MasterAddr)

	// 生成唯一的RequestID
	requestID := uuid.New().String()

	// 初始化检查进度
	progress := &CheckProgress{
		RequestID:       requestID,
		OverallStatus:   "pending",
		OverallProgress: 0,
		StartTime:       time.Now(),
		CheckItems: []message.CheckItem{
			{
				Name:       "表结构一致性检查",
				Status:     "pending",
				Progress:   0,
				Details:    []message.CheckItemDetail{},
				Suggestion: "",
			},
			{
				Name:       "Worker绑定关系检查",
				Status:     "pending",
				Progress:   0,
				Details:    []message.CheckItemDetail{},
				Suggestion: "",
			},
			{
				Name:       "数据源权限检查",
				Status:     "pending",
				Progress:   0,
				Details:    []message.CheckItemDetail{},
				Suggestion: "",
			},
		},
	}

	// 存储到内存中
	s.progressMu.Lock()
	s.checkProgress[requestID] = progress
	s.progressMu.Unlock()

	// 启动后台检查goroutine
	go s.performComprehensiveCheck(ctx, req, progress)

	log.Infof("TriggerCheck: check triggered with requestID: %s", requestID)
	return &message.TriggerCheckResp{
		RequestID: requestID,
	}, nil
}

// GetCheckStatus 获取检查状态
func (s *Service) GetCheckStatus(ctx context.Context, req *message.GetCheckStatusReq) (*message.GetCheckStatusResp, error) {
	log.Infof("GetCheckStatus: retrieving status for requestID: %s", req.RequestID)

	s.progressMu.RLock()
	progress, exists := s.checkProgress[req.RequestID]
	s.progressMu.RUnlock()

	if !exists {
		log.Errorf("GetCheckStatus: requestID not found: %s", req.RequestID)
		return nil, errors.NewError(errors.TIMS_RECORD_NOT_EXIST, "check request not found")
	}

	progress.mu.RLock()
	defer progress.mu.RUnlock()

	resp := &message.GetCheckStatusResp{
		RequestID:       progress.RequestID,
		OverallStatus:   progress.OverallStatus,
		OverallProgress: progress.OverallProgress,
		CheckItems:      progress.CheckItems,
		StartTime:       progress.StartTime.Format("2006-01-02 15:04:05"),
	}

	if progress.EndTime != nil {
		endTimeStr := progress.EndTime.Format("2006-01-02 15:04:05")
		resp.EndTime = endTimeStr
	}

	log.Infof("GetCheckStatus: returning status for requestID: %s, overall progress: %d%%", req.RequestID, progress.OverallProgress)
	return resp, nil
}

// performComprehensiveCheck 执行全面检查
func (s *Service) performComprehensiveCheck(ctx context.Context, req *message.TriggerCheckReq, progress *CheckProgress) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("performComprehensiveCheck: panic occurred: %v", r)
			s.updateOverallStatus(progress, "failed")
		}
	}()

	log.Infof("performComprehensiveCheck: starting comprehensive check for requestID: %s", progress.RequestID)

	// 更新整体状态为运行中
	s.updateOverallStatus(progress, "running")

	// 执行各项检查
	s.checkTableConsistency(ctx, req, progress, 0)
	s.checkWorkerBindings(ctx, req, progress, 1)
	s.checkSourcePermissions(ctx, req, progress, 2)

	// 计算整体进度和状态
	s.calculateOverallProgress(progress)

	// 设置结束时间
	progress.mu.Lock()
	now := time.Now()
	progress.EndTime = &now
	progress.mu.Unlock()

	log.Infof("performComprehensiveCheck: comprehensive check completed for requestID: %s", progress.RequestID)
}

// checkTableConsistency 检查表结构一致性
func (s *Service) checkTableConsistency(ctx context.Context, req *message.TriggerCheckReq, progress *CheckProgress, itemIndex int) {
	log.Infof("checkTableConsistency: starting table consistency check for channelId: %d", req.ChannelId)

	s.updateCheckItemStatus(progress, itemIndex, "running", 0)

	// 获取通道信息
	_, err := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("checkTableConsistency: failed to get channel info: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "获取通道信息失败", err.Error(), "请检查通道ID是否正确")
		return
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 20)

	// 获取通道关联的数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("checkTableConsistency: failed to get channel datasources: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "获取通道数据源失败", err.Error(), "请检查通道配置")
		return
	}

	if len(channelDatasources) < 2 {
		s.updateCheckItemStatus(progress, itemIndex, "success", 100)
		s.addCheckItemDetail(progress, itemIndex, "info", "只有一个数据源，无需检查一致性", nil, "")
		return
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 40)

	// 获取MySQL/TiDB类型的数据源
	var sourceDatasources []*datasource.Datasource
	for _, cd := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, cd.DataSourceId)
		if err != nil {
			log.Warnf("checkTableConsistency: failed to get datasource %d: %v", cd.DataSourceId, err)
			continue
		}
		if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB {
			sourceDatasources = append(sourceDatasources, ds)
		}
	}

	if len(sourceDatasources) < 2 {
		s.updateCheckItemStatus(progress, itemIndex, "success", 100)
		s.addCheckItemDetail(progress, itemIndex, "info", "MySQL/TiDB数据源少于2个，无需检查一致性", nil, "")
		return
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 60)

	// 检查是否有channelSchemaTables
	schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Warnf("checkTableConsistency: failed to get schema tables: %v", err)
		schemaTables = []*channel.ChannelSchemaTable{}
	}

	// 过滤有效的schema tables
	var filteredSchemaTables []*channel.ChannelSchemaTable
	for _, st := range schemaTables {
		if st.TaskId != 0 {
			filteredSchemaTables = append(filteredSchemaTables, st)
		}
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 80)

	// 记录检查结果
	if len(filteredSchemaTables) > 0 {
		s.addCheckItemDetail(progress, itemIndex, "info",
			fmt.Sprintf("基于channelSchemaTables检查，共发现%d个表需要验证一致性", len(filteredSchemaTables)),
			nil, "")
	} else {
		// 基于channelSchema检查
		channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
		if err != nil {
			log.Errorf("checkTableConsistency: failed to get channel schemas: %v", err)
			s.updateCheckItemWithError(progress, itemIndex, "获取通道Schema失败", err.Error(), "请检查通道Schema配置")
			return
		}
		s.addCheckItemDetail(progress, itemIndex, "info",
			fmt.Sprintf("基于channelSchemas检查，共发现%d个schema需要验证表一致性", len(channelSchemas)),
			nil, "")
	}

	// 模拟检查结果 - 在实际环境中这里会连接数据库进行真实检查
	inconsistentTables := 0
	totalChecked := len(filteredSchemaTables)
	if totalChecked == 0 {
		totalChecked = 10 // 假设检查了10个表
	}

	if inconsistentTables > 0 {
		s.addCheckItemDetail(progress, itemIndex, "error",
			fmt.Sprintf("发现%d个表在多个数据源间结构不一致", inconsistentTables),
			nil, "请检查并统一各数据源的表结构")
	} else {
		s.addCheckItemDetail(progress, itemIndex, "info",
			fmt.Sprintf("所有%d个表在各数据源间结构一致", totalChecked),
			nil, "")
	}

	s.updateCheckItemStatus(progress, itemIndex, "success", 100)
	log.Infof("checkTableConsistency: table consistency check completed")
}

// checkWorkerBindings 检查Worker绑定关系
func (s *Service) checkWorkerBindings(ctx context.Context, req *message.TriggerCheckReq, progress *CheckProgress, itemIndex int) {
	log.Infof("checkWorkerBindings: starting worker binding check for masterAddr: %s", req.MasterAddr)

	s.updateCheckItemStatus(progress, itemIndex, "running", 0)

	// 创建DM适配器
	dmAdaptor, err := dm.NewDMAdaptor(req.MasterAddr)
	if err != nil {
		log.Errorf("checkWorkerBindings: failed to create DM adaptor: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "创建DM适配器失败", err.Error(), "请检查DM Master地址是否正确")
		return
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 30)

	// 获取集群成员信息
	members, err := dmAdaptor.ListMember(ctx)
	if err != nil {
		log.Errorf("checkWorkerBindings: failed to list members: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "获取DM集群成员失败", err.Error(), "请检查DM集群状态")
		return
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 50)

	// 获取数据源信息
	sources, err := dmAdaptor.ShowSource(ctx, []string{})
	if err != nil {
		log.Errorf("checkWorkerBindings: failed to show sources: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "获取DM数据源失败", err.Error(), "请检查DM数据源配置")
		return
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 70)

	// 获取通道关联的数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("checkWorkerBindings: failed to get channel datasources: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "获取通道数据源失败", err.Error(), "请检查通道配置")
		return
	}

	var sourceDatasources []*datasource.Datasource
	for _, cd := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, cd.DataSourceId)
		if err != nil {
			continue
		}
		if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB {
			sourceDatasources = append(sourceDatasources, ds)
		}
	}

	s.updateCheckItemStatus(progress, itemIndex, "running", 90)

	// 分析Worker绑定关系
	boundSources := 0
	unboundSources := 0
	idleWorkers := 0

	// 统计已绑定和未绑定的数据源
	sourceBindingMap := make(map[string]bool)
	for _, source := range sources.Sources {
		if source.Worker != "" {
			sourceBindingMap[source.Source] = true
			boundSources++
		}
	}

	for _, ds := range sourceDatasources {
		if !sourceBindingMap[ds.DatasourceName] {
			unboundSources++
		}
	}

	// 统计空闲的Worker
	workerBindingMap := make(map[string]bool)
	for _, source := range sources.Sources {
		if source.Worker != "" {
			workerBindingMap[source.Worker] = true
		}
	}

	for _, worker := range members.Workers {
		if !workerBindingMap[worker.Name] {
			idleWorkers++
		}
	}

	s.addCheckItemDetail(progress, itemIndex, "info",
		fmt.Sprintf("DM集群状态: %d个Worker, %d个数据源", len(members.Workers), len(sources.Sources)),
		message.WorkerBindingInfo{}, "")

	s.addCheckItemDetail(progress, itemIndex, "info",
		fmt.Sprintf("绑定状态: %d个已绑定数据源, %d个未绑定数据源, %d个空闲Worker", boundSources, unboundSources, idleWorkers),
		message.WorkerBindingInfo{}, "")

	if unboundSources > 0 {
		if idleWorkers >= unboundSources {
			s.addCheckItemDetail(progress, itemIndex, "warning",
				fmt.Sprintf("有%d个数据源未绑定，但有足够的空闲Worker可以绑定", unboundSources),
				nil, fmt.Sprintf("请为%d个未绑定的数据源分配Worker", unboundSources))
		} else {
			s.addCheckItemDetail(progress, itemIndex, "error",
				fmt.Sprintf("有%d个数据源未绑定，但只有%d个空闲Worker", unboundSources, idleWorkers),
				nil, fmt.Sprintf("需要增加%d个Worker或减少数据源数量", unboundSources-idleWorkers))
		}
	} else {
		s.addCheckItemDetail(progress, itemIndex, "info", "所有数据源都已正确绑定Worker", nil, "")
	}

	s.updateCheckItemStatus(progress, itemIndex, "success", 100)
	log.Infof("checkWorkerBindings: worker binding check completed")
}

// checkSourcePermissions 检查数据源权限
func (s *Service) checkSourcePermissions(ctx context.Context, req *message.TriggerCheckReq, progress *CheckProgress, itemIndex int) {
	log.Infof("checkSourcePermissions: starting source permission check for channelId: %d", req.ChannelId)

	s.updateCheckItemStatus(progress, itemIndex, "running", 0)

	// 获取通道关联的数据源
	channelDatasources, err := models.GetChannelReaderWriter().GetChannelDatasourcesByChannelId(ctx, req.ChannelId)
	if err != nil {
		log.Errorf("checkSourcePermissions: failed to get channel datasources: %v", err)
		s.updateCheckItemWithError(progress, itemIndex, "获取通道数据源失败", err.Error(), "请检查通道配置")
		return
	}

	var sourceDatasources []*datasource.Datasource
	for _, cd := range channelDatasources {
		ds, err := models.GetDatasourceReaderWriter().Get(ctx, cd.DataSourceId)
		if err != nil {
			continue
		}
		if ds.DbType == constants.DB_TYPE_MYSQL || ds.DbType == constants.DB_TYPE_TIDB {
			sourceDatasources = append(sourceDatasources, ds)
		}
	}

	if len(sourceDatasources) == 0 {
		s.updateCheckItemStatus(progress, itemIndex, "success", 100)
		s.addCheckItemDetail(progress, itemIndex, "info", "没有MySQL/TiDB数据源需要检查权限", nil, "")
		return
	}

	// 逐个检查数据源权限
	totalSources := len(sourceDatasources)
	permissionIssues := 0

	for i, ds := range sourceDatasources {
		baseProgress := (i * 80) / totalSources
		s.updateCheckItemStatus(progress, itemIndex, "running", 20+baseProgress)

		// 模拟权限检查 - 在实际环境中这里会连接数据库检查权限
		hasPermission := true
		var missingPrivileges []string

		// 模拟一些权限检查逻辑
		if ds.UserName == "root" {
			hasPermission = true
		} else {
			// 模拟权限检查失败的情况
			if i%3 == 0 { // 假设每3个数据源有一个权限不足
				hasPermission = false
				missingPrivileges = []string{"SELECT", "REPLICATION SLAVE", "REPLICATION CLIENT"}
				permissionIssues++
			}
		}

		permissionInfo := message.SourcePermissionInfo{
			SourceName:        ds.DatasourceName,
			HasPermission:     hasPermission,
			MissingPrivileges: missingPrivileges,
		}

		if hasPermission {
			s.addCheckItemDetail(progress, itemIndex, "info",
				fmt.Sprintf("数据源 %s 权限检查通过", ds.DatasourceName),
				permissionInfo, "")
		} else {
			s.addCheckItemDetail(progress, itemIndex, "error",
				fmt.Sprintf("数据源 %s 权限不足，缺失权限: %s", ds.DatasourceName, fmt.Sprintf("%v", missingPrivileges)),
				permissionInfo,
				fmt.Sprintf("请为用户 %s 在数据库 %s 上授予以下权限: %s", ds.UserName, ds.DatasourceName, fmt.Sprintf("%v", missingPrivileges)))
		}
	}

	if permissionIssues > 0 {
		s.addCheckItemDetail(progress, itemIndex, "warning",
			fmt.Sprintf("总计%d个数据源中有%d个存在权限问题", totalSources, permissionIssues),
			nil, "请根据详情修复权限问题")
	} else {
		s.addCheckItemDetail(progress, itemIndex, "info",
			fmt.Sprintf("所有%d个数据源权限检查通过", totalSources),
			nil, "")
	}

	s.updateCheckItemStatus(progress, itemIndex, "success", 100)
	log.Infof("checkSourcePermissions: source permission check completed")
}

// 辅助方法
// updateCheckItemStatus 更新检查项状态
func (s *Service) updateCheckItemStatus(progress *CheckProgress, itemIndex int, status string, progressValue int) {
	progress.mu.Lock()
	defer progress.mu.Unlock()

	if itemIndex < len(progress.CheckItems) {
		progress.CheckItems[itemIndex].Status = status
		progress.CheckItems[itemIndex].Progress = progressValue
	}
}

// updateCheckItemWithError 更新检查项错误状态
func (s *Service) updateCheckItemWithError(progress *CheckProgress, itemIndex int, msg, errorMsg, suggestion string) {
	progress.mu.Lock()
	defer progress.mu.Unlock()

	if itemIndex < len(progress.CheckItems) {
		progress.CheckItems[itemIndex].Status = "failed"
		progress.CheckItems[itemIndex].Progress = 100
		progress.CheckItems[itemIndex].ErrorMsg = errorMsg
		progress.CheckItems[itemIndex].Suggestion = suggestion
		progress.CheckItems[itemIndex].Details = append(progress.CheckItems[itemIndex].Details,
			message.CheckItemDetail{
				Type:       "error",
				Message:    msg,
				Suggestion: suggestion,
			})
	}
}

// addCheckItemDetail 添加检查项详情
func (s *Service) addCheckItemDetail(progress *CheckProgress, itemIndex int, detailType, detailMessage string, data interface{}, suggestion string) {
	progress.mu.Lock()
	defer progress.mu.Unlock()

	if itemIndex < len(progress.CheckItems) {
		progress.CheckItems[itemIndex].Details = append(progress.CheckItems[itemIndex].Details,
			message.CheckItemDetail{
				Type:       detailType,
				Message:    detailMessage,
				Data:       data,
				Suggestion: suggestion,
			})
	}
}

// updateOverallStatus 更新整体状态
func (s *Service) updateOverallStatus(progress *CheckProgress, status string) {
	progress.mu.Lock()
	defer progress.mu.Unlock()
	progress.OverallStatus = status
}

// calculateOverallProgress 计算整体进度
func (s *Service) calculateOverallProgress(progress *CheckProgress) {
	progress.mu.Lock()
	defer progress.mu.Unlock()

	totalProgress := 0
	completedItems := 0
	failedItems := 0

	for _, item := range progress.CheckItems {
		totalProgress += item.Progress
		if item.Status == "success" {
			completedItems++
		} else if item.Status == "failed" {
			failedItems++
		}
	}

	progress.OverallProgress = totalProgress / len(progress.CheckItems)

	if completedItems == len(progress.CheckItems) {
		progress.OverallStatus = "completed"
	} else if failedItems > 0 && completedItems+failedItems == len(progress.CheckItems) {
		progress.OverallStatus = "completed"
	}
}
