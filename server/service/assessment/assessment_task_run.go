package assessment

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"os"
	"strconv"
	"strings"
	"text/template"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	tmscommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/assess/oracle/o2t"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/assess/oracle/public"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	transdbconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/assessment"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type dbaObject struct {
	Owner         string `json:"owner"`
	ObjectName    string `json:"objectName"`
	SubobjectName string `json:"subobjectName"`
	ObjectType    string `json:"objectType"`
	Status        string `json:"status"`
}

type dbaConstraint struct {
	Owner          string `json:"owner"`
	TableName      string `json:"tableName"`
	ConstraintName string `json:"constraintName"`
	ConstraintType string `json:"constraintType"`
}

type ObjectCompatible struct {
	DbTypeS       string `json:"dbTypeS"`
	DbTypeT       string `json:"dbTypeT"`
	ObjectType    string `json:"objectType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type dbaIndex struct {
	TableOwner  string `json:"tableOwner"`
	TableName   string `json:"tableName"`
	IndexName   string `json:"indexName"`
	IndexType   string `json:"indexType"`
	Partitioned string `json:"partitioned"`
}

type DbaTabColumns struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"OwnerName"`
	TableName     string `json:"tableName"`
	ColumnName    string `json:"columnName"`
	DataType      string `json:"dataType"`
	DataTypeT     string `json:"dataTypeT"`
	ColumnId      int    `json:"columnId"`
	DataLength    int    `json:"dataLength"`
	DataDesc      string `json:"dataDesc"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type DbaTabColumnsDefault struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"OwnerName"`
	TableName     string `json:"tableName"`
	ColumnName    string `json:"columnName"`
	DataType      string `json:"dataType"`
	DataDefault   string `json:"dataDefault"`
	DataDefaultT  string `json:"dataDefaultT"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type AllPartTables struct {
	ChannelId           int    `json:"channelId"`
	TaskId              int    `json:"taskId"`
	Owner               string `json:"owner"`
	TableName           string `json:"tableName"`
	PartitioningType    string `json:"partitioningType"`
	SubpartitioningType string `json:"subpartitioningType"`
	PartitionCount      string `json:"partitionCount"`
	IsCompatible        string `json:"isCompatible"`
	IsConvertible       string `json:"isConvertible"`
}

type DbaTables struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	TableName     string `json:"tableName"`
	Temporary     string `json:"temporary"`
	Duration      string `json:"duration"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type AllObjects struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ObjectName    string `json:"objectName"`
	ObjectType    string `json:"objectType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraLobs struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ObjectName    string `json:"objectName"`
	ObjectType    string `json:"objectType"`
	TableName     string `json:"tableName"`
	ColumnName    string `json:"columnName"`
	DataType      string `json:"dataType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraTableTypeCompatibles struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	TableName     string `json:"tableName"`
	TableType     string `json:"tableType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraDbaViews struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ViewType      string `json:"viewType"`
	ViewTypeOwner string `json:"viewTypeOwner"`
	ViewName      string `json:"viewName"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraSubPartitioning struct {
	ChannelId           int    `json:"channelId"`
	TaskId              int    `json:"taskId"`
	Owner               string `json:"owner"`
	TableName           string `json:"tableName"`
	SubPartitioningType string `json:"subPartitioningType"`
	IsCompatible        string `json:"isCompatible"`
	IsConvertible       string `json:"isConvertible"`
}

func assessTaskStatusUpdate(timsDB *sql.DB, tableName string, startTime string, endTime string, status string, taskId int) error {
	// 1:未配置 2:未运行 3:运行中 4:运行失败 5:运行完成
	var sql string
	sql = fmt.Sprintf(`update %s set 
	start_time=%s,
	end_time=%s,
	task_status=%s
	where task_id=%d`, tableName, startTime, endTime, status, taskId)

	_, err := timsDB.Exec(sql)
	if err != nil {
		log.Errorf("assessTaskStatusUpdate failed. err:%s", err)
		log.Errorf("Query Sql:%s", sql)
		return err
	}
	return nil
}

func assessObjectCheck(ctx context.Context, timsDB *sql.DB, oracleDB *oracle.Oracle, channelId int, taskId int, schemaName string, schemaSeq int) error {
	// log.Infof("assessObjectCheck start, channelId:%d, schemaName:%s", channelId, schemaName)

	defer func() {
		if err := recover(); err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessObjectCheck failed. defer(). schemaName:%s. err:%s", schemaName, err))
		}
	}()

	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessObjectCheck start, channelId:%d, schemaName:%s", channelId, schemaName))
	var sql string
	var detaildata []byte
	var ReportSummary_lst []public.ReportSummary
	start_ts := time.Now()
	end_ts := time.Now()
	duration_ts := end_ts.Sub(start_ts).String()

	// 获取TiMS的对象兼容性配置
	// sql = "select * from objmap_rules where db_type_s='oracle' and db_type_t='tidb'"
	sql = fmt.Sprintf(`select t1.* from objmap_rules t1 
	INNER JOIN tmplate_rules t2 ON t1.objmap_rule_id = t2.map_rule_id
	INNER JOIN tmplate_informations t3 ON t3.tmplate_id = t2.tmplate_id
	INNER JOIN tasks t4 ON t3.tmplate_id = t4.objmap_tmplate_id
	WHERE t1.db_type_s='oracle' and t1.db_type_t='tidb' 
	AND t4.task_id=%d`, taskId)
	_, compatibleObjConf, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		// log.Errorf("assessObjectCheck failed. schemaName:%s. err:%s", schemaName, err)
		// log.Errorf("Query Sql:%s", sql)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessObjectCheck failed. schemaName:%s. err:%s", schemaName, err))
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:Query Sql:%s", sql))
	}
	objAssessCompsMap := make(map[string]meta.BuildinObjectCompatible)
	for _, c := range compatibleObjConf {
		objAssessCompsMap[common.StringUPPER(c["object_type"])] = meta.BuildinObjectCompatible{
			DBTypeS:       c["db_type_s"],
			DBTypeT:       c["db_type_t"],
			ObjectNameS:   c["object_type"],
			IsCompatible:  c["is_compatibility"],
			IsConvertible: c["is_convertible"],
		}
	}

	// 获取TiMS的默认值映射规则配置
	colDefaultMap, err := getDefaultValueMap(ctx, timsDB, taskId)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessObjectCheck failed. schemaName:%s. err:%s", schemaName, err))
	}

	// sql = "select * from tabcol_map_rules where db_type_s='oracle' and db_type_t='tidb' "
	sql = fmt.Sprintf(`select t1.* from tabcol_map_rules t1 
	INNER JOIN tmplate_rules t2 ON t1.tabcolmap_rule_id = t2.map_rule_id 
	INNER JOIN tmplate_informations t3 ON t3.tmplate_id = t2.tmplate_id 
	INNER JOIN tasks t4 ON t3.tmplate_id = t4.tabcolmap_tmplate_id 
	WHERE t1.db_type_s='oracle' and t1.db_type_t='tidb' 
	AND t1.deleted_at is null 
	AND t4.task_id=%d`, taskId)
	_, buildDatatypeRules, err := oracle.Query(ctx, timsDB, sql)
	buildDatatypeMap := make(map[string]meta.BuildinDatatypeRule)
	for _, d := range buildDatatypeRules {
		buildDatatypeMap[common.StringUPPER(d["col_type_name_s"])] = meta.BuildinDatatypeRule{
			DBTypeS:       d["db_type_s"],
			DBTypeT:       d["db_type_t"],
			DatatypeNameS: d["col_type_name_s"],
			DatatypeNameT: d["col_type_name_t"],
		}
	}

	// 检查oracle对象兼容性
	// log.Infof("OBJECT_TYPE_COMPATIBLE start")
	logtofileanddb(ctx, channelId, taskId, "info", "t01-003:OBJECT_TYPE_COMPATIBLE start")
	var schema_lst []string
	schema_lst = append([]string{}, schemaName)

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleDBOverview start")
	start_ts = time.Now()
	reportOverview, reportSchema, err := o2t.AssessOracleDBOverview(oracleDB, objAssessCompsMap, "testuser", "O2T迁移对象兼容性评估报告.html")
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("err:", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}

	dbVersion, err := GetOracleDBVersion(ctx, oracleDB)
	if err != nil {
		dbVersion = "UNKNOWN"
	}

	totalUsedSize, err := strconv.ParseFloat(reportOverview.TotalUsedSize, 64)
	totalUsedSizeStr := ""
	if err != nil {
		totalUsedSizeStr = "UNKNOWN"
	} else {
		totalUsedSizeStr = strconv.FormatFloat(totalUsedSize, 'f', 2, 64)
	}

	hostMem, err := strconv.ParseFloat(reportOverview.HostMem, 64)
	hostMemStr := ""
	if err != nil {
		hostMemStr = "UNKNOWN"
	} else {
		hostMemStr = strconv.FormatFloat(hostMem, 'f', 2, 64)
	}

	oraOverview := &message.OracleOverview{
		ReportName:        reportOverview.ReportName,
		ReportUser:        reportOverview.ReportUser,
		HostName:          reportOverview.HostName,
		PlatformName:      reportOverview.PlatformName,
		DBName:            reportOverview.DBName,
		DBVersion:         dbVersion,
		GlobalDBName:      reportOverview.GlobalDBName,
		ClusterDB:         reportOverview.ClusterDB,
		ClusterDBInstance: reportOverview.ClusterDBInstance,
		InstanceName:      reportOverview.InstanceName,
		InstanceNumber:    reportOverview.InstanceNumber,
		ThreadNumber:      reportOverview.ThreadNumber,
		BlockSize:         reportOverview.BlockSize,
		TotalUsedSize:     totalUsedSizeStr,
		HostCPUS:          reportOverview.HostCPUS,
		HostMem:           hostMemStr,
		CharacterSet:      reportOverview.CharacterSet,
	}

	detaildata, err = json.Marshal(oraOverview)
	if err != nil {
		// log.Errorf("err:", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	var ListOracleOverview []public.ReportOverview
	ListOracleOverview = append(ListOracleOverview, *reportOverview)
	DBName := reportOverview.DBName
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListOracleOverview", duration_ts,
		common.AssessTypeDatabaseOverview, common.AssessNameDBOverview,
		reportSchema.AssessTotal, reportSchema.Compatible, reportSchema.Incompatible, reportSchema.Convertible, reportSchema.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaColumnTypeCompatible start")
	start_ts = time.Now()
	ListSchemaColumnTypeCompatibles, columnSummary, err := o2t.AssessOracleSchemaColumnTypeCompatible(schema_lst, oracleDB, buildDatatypeMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("AssessOracleSchemaColumnTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleSchemaColumnTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, columnSummary)
	detaildata, err = json.Marshal(ListSchemaColumnTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaColumnTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameColumnTypeCompatible,
		columnSummary.AssessTotal, columnSummary.Compatible, columnSummary.Incompatible, columnSummary.Convertible, columnSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaTableTypeCompatible start")
	start_ts = time.Now()
	ListSchemaTableTypeCompatibles, tableSummary, err := o2t.AssessOracleSchemaTableTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaTableTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleSchemaTableTypeCompatible err:", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, tableSummary)
	detaildata, err = json.Marshal(ListSchemaTableTypeCompatibles)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameTableTypeCompatible,
		tableSummary.AssessTotal, tableSummary.Compatible, tableSummary.Incompatible, tableSummary.Convertible, tableSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaConstraintTypeCompatible start")
	start_ts = time.Now()
	ListSchemaConstraintTypeCompatibles, constraintSummary, err := o2t.AssessOracleSchemaConstraintTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaConstraintTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaConstraintTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, constraintSummary)
	detaildata, err = json.Marshal(ListSchemaConstraintTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaConstraintTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameConstraintTypeCompatible,
		constraintSummary.AssessTotal, constraintSummary.Compatible, constraintSummary.Incompatible, constraintSummary.Convertible, constraintSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaIndexTypeCompatible start")
	start_ts = time.Now()
	ListSchemaIndexTypeCompatibles, indexSummary, err := o2t.AssessOracleSchemaIndexTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaIndexTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaIndexTypeCompatible err:%v", err))

	}
	ReportSummary_lst = append(ReportSummary_lst, indexSummary)
	detaildata, err = json.Marshal(ListSchemaIndexTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))

	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaIndexTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameIndexTypeCompatible,
		indexSummary.AssessTotal, indexSummary.Compatible, indexSummary.Incompatible, indexSummary.Convertible, indexSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:GetListSchemaSequenceCompatibles start")
	start_ts = time.Now()
	ListSchemaSequenceCompatibles, sequenceSummary, err := GetListSchemaSequenceCompatibles(ctx, oracleDB.OracleDB, schema_lst, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaIndexTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:GetListSchemaSequenceCompatibles err:%v", err))

	}
	ReportSummary_lst = append(ReportSummary_lst, sequenceSummary)
	detaildata, err = json.Marshal(ListSchemaSequenceCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))

	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaSequenceCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, "SEQUENCE_COMPATIBLE",
		sequenceSummary.AssessTotal, sequenceSummary.Compatible, sequenceSummary.Incompatible, sequenceSummary.Convertible, sequenceSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaDefaultValue start")
	start_ts = time.Now()
	ListSchemaDefaultValueCompatibles, defaultValSummary, err := o2t.AssessOracleSchemaDefaultValue(schema_lst, oracleDB, colDefaultMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaDefaultValue err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaDefaultValue err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, defaultValSummary)
	detaildata, err = json.Marshal(ListSchemaDefaultValueCompatibles)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	log.Infof("ListSchemaDefaultValueCompatibles detail: [%v], [%v].", ListSchemaDefaultValueCompatibles, defaultValSummary)
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaDefaultValueCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameDefaultValueCompatible,
		defaultValSummary.AssessTotal, defaultValSummary.Compatible, defaultValSummary.Incompatible, defaultValSummary.Convertible, defaultValSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaViewTypeCompatible start")
	start_ts = time.Now()
	ListSchemaViewTypeCompatibles, viewSummary, err := o2t.AssessOracleSchemaViewTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaViewTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaViewTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, viewSummary)
	detaildata, err = json.Marshal(ListSchemaViewTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err: %v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaViewTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameViewTypeCompatible,
		viewSummary.AssessTotal, viewSummary.Compatible, viewSummary.Incompatible, viewSummary.Convertible, viewSummary.InConvertible,
		string(detaildata))

	// log.Infof("ListSchemaViewTypeCompatibles: %v\n", ListSchemaViewTypeCompatibles)
	// log.Infof("viewSummary: %v\n", viewSummary)
	// log.Infof("string(detaildata): %v\n", string(detaildata))

	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "JOB", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "SYNONYM", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "PACKAGE BODY", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "TRIGGER", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "PACKAGE", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "PROCEDURE", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "FUNCTION", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "DATABASE LINK", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "LOB SUBPARTITION", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "TYPE", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "INDEX SUBPARTITION", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)
	assessObjectType(ctx, channelId, taskId, schema_lst, schemaName, "LOB PARTITION", oracleDB, objAssessCompsMap, ReportSummary_lst, DBName)

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaObjectTypeCompatible start")
	start_ts = time.Now()
	// ListSchemaObjectTypeCompatibles, codeSummary, err := o2t.AssessOracleSchemaObjectTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	ListSchemaObjectTypeCompatibles, codeSummary, err := AssessOracleSchemaObjectTypeCompatible(schema_lst, ctx, oracleDB, objAssessCompsMap)
	log.Infof("ListSchemaObjectTypeCompatibles size: %d", len(ListSchemaObjectTypeCompatibles))

	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaObjectTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaObjectTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, codeSummary)
	detaildata, err = json.Marshal(ListSchemaObjectTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaObjectTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameObjectTypeCompatible,
		codeSummary.AssessTotal, codeSummary.Compatible, codeSummary.Incompatible, codeSummary.Convertible, codeSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:AssessOracleLobTypeCompatible start")
	//get Lob summary
	start_ts = time.Now()
	ListLobTypeCompatibles, lobSummary, err := AssessOracleLobTypeCompatible(schema_lst, ctx, oracleDB, objAssessCompsMap)

	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleLobTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, lobSummary)
	detaildata, err = json.Marshal(ListLobTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListLobTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, AssessNameLobTypeCompatible,
		lobSummary.AssessTotal, lobSummary.Compatible, lobSummary.Incompatible, lobSummary.Convertible, lobSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaPartitionTypeCompatible start")
	start_ts = time.Now()
	ListSchemaPartitionTypeCompatibles, partitionSummary, err := o2t.AssessOracleSchemaPartitionTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaPartitionTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaPartitionTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, partitionSummary)
	detaildata, err = json.Marshal(ListSchemaPartitionTypeCompatibles)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaPartitionTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNamePartitionTypeCompatible,
		partitionSummary.AssessTotal, partitionSummary.Compatible, partitionSummary.Incompatible, partitionSummary.Convertible, partitionSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaSubPartitionTypeCompatible start")
	start_ts = time.Now()
	ListSchemaSubPartitionTypeCompatibles, subPartitionSummary, err := o2t.AssessOracleSchemaSubPartitionTypeCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaSubPartitionTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaSubPartitionTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, subPartitionSummary)
	detaildata, err = json.Marshal(ListSchemaSubPartitionTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaSubPartitionTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameSubPartitionTypeCompatible,
		subPartitionSummary.AssessTotal, subPartitionSummary.Compatible, subPartitionSummary.Incompatible, subPartitionSummary.Convertible, subPartitionSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaTemporaryTableCompatible start")
	start_ts = time.Now()
	ListSchemaTemporaryTableTypeCompatibles, tempSummary, err := o2t.AssessOracleSchemaTemporaryTableCompatible(schema_lst, oracleDB, objAssessCompsMap)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaTemporaryTableCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaTemporaryTableCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, tempSummary)
	detaildata, err = json.Marshal(ListSchemaTemporaryTableTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTemporaryTableTypeCompatibles", duration_ts,
		common.AssessTypeObjectTypeCompatible, common.AssessNameTemporaryTableTypeCompatible,
		tempSummary.AssessTotal, tempSummary.Compatible, tempSummary.Incompatible, tempSummary.Convertible, tempSummary.InConvertible,
		string(detaildata))

	// log.Infof("OBJECT_TYPE_CHECK start")
	logtofileanddb(ctx, channelId, taskId, "info", "t01-003:OBJECT_TYPE_CHECK start")
	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOraclePartitionTableCountsCheck start")
	start_ts = time.Now()
	ListSchemaPartitionTableCountsCheck, partitionSummary, err := o2t.AssessOraclePartitionTableCountsCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOraclePartitionTableCountsCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOraclePartitionTableCountsCheck err:%v", err))
	}
	// log.Infof("partitionSummary: %v\n", partitionSummary)
	detaildata, err = json.Marshal(ListSchemaPartitionTableCountsCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaPartitionTableCountsCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNamePartitionTableCountsCheck,
		partitionSummary.AssessTotal, partitionSummary.Compatible, partitionSummary.Incompatible, partitionSummary.Convertible, partitionSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleTableRowLengthCheck start")
	start_ts = time.Now()
	ListSchemaTableRowLengthCheck, tableSummary, err := o2t.AssessOracleTableRowLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOraclePartitionTableCountsCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOraclePartitionTableCountsCheck err:%v", err))
	}
	// log.Infof("tableSummary: %v\n", tableSummary)
	detaildata, err = json.Marshal(ListSchemaTableRowLengthCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableRowLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameTableRowLengthCheck,
		tableSummary.AssessTotal, tableSummary.Compatible, tableSummary.Incompatible, tableSummary.Convertible, tableSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleTableIndexRowLengthCheck start")
	start_ts = time.Now()
	ListSchemaTableIndexRowLengthCheck, indexSummary, err := o2t.AssessOracleTableIndexRowLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleTableIndexRowLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleTableIndexRowLengthCheckerr:%v", err))
	}
	// log.Infof("indexSummary: %v\n", indexSummary)
	detaildata, err = json.Marshal(ListSchemaTableIndexRowLengthCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableIndexRowLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameIndexRowLengthCheck,
		indexSummary.AssessTotal, indexSummary.Compatible, indexSummary.Incompatible, indexSummary.Convertible, indexSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleTableColumnCountsCheck start")
	start_ts = time.Now()
	ListSchemaTableColumnCountsCheck, columnSummary, err := o2t.AssessOracleTableColumnCountsCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleTableColumnCountsCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleTableColumnCountsCheck:%v", err))
	}
	// log.Infof("columnSummary: %v\n", columnSummary)
	detaildata, err = json.Marshal(ListSchemaTableColumnCountsCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableColumnCountsCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameTableColumnCountsCheck,
		columnSummary.AssessTotal, columnSummary.Compatible, columnSummary.Incompatible, columnSummary.Convertible, columnSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleTableIndexCountsCheck start")
	start_ts = time.Now()
	ListSchemaTableIndexCountsCheck, indexCSummary, err := o2t.AssessOracleTableIndexCountsCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleTableIndexCountsCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleTableIndexCountsCheck err:%v", err))
	}
	// log.Infof("indexCSummary: %v\n", indexCSummary)
	detaildata, err = json.Marshal(ListSchemaTableIndexCountsCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableIndexCountsCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameTableIndexCountsCheck,
		indexCSummary.AssessTotal, indexCSummary.Compatible, indexCSummary.Incompatible, indexCSummary.Convertible, indexCSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleTableNameLengthCheck start")
	start_ts = time.Now()
	ListSchemaTableNameLengthCheck, tableLSummary, err := o2t.AssessOracleTableNameLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleTableNameLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleTableNameLengthCheck err:%v", err))
	}
	// log.Infof("tableLSummary: %v\n", tableLSummary)
	detaildata, err = json.Marshal(ListSchemaTableNameLengthCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableNameLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameTableNameLengthCheck,
		tableLSummary.AssessTotal, tableLSummary.Compatible, tableLSummary.Incompatible, tableLSummary.Convertible, tableLSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleColumnNameLengthCheck start")
	start_ts = time.Now()
	ListSchemaTableColumnNameLengthCheck, columnLSummary, err := o2t.AssessOracleColumnNameLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleColumnNameLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleColumnNameLengthCheck err:%v", err))
	}
	// log.Infof("columnLSummary: %v\n", columnLSummary)
	detaildata, err = json.Marshal(ListSchemaTableColumnNameLengthCheck)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableColumnNameLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameColumnNameLengthCheck,
		columnLSummary.AssessTotal, columnLSummary.Compatible, columnLSummary.Incompatible, columnLSummary.Convertible, columnLSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleIndexNameLengthCheck start")
	start_ts = time.Now()
	ListSchemaTableIndexNameLengthCheck, indexLSummary, err := o2t.AssessOracleIndexNameLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleIndexNameLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleIndexNameLengthCheck err:%v", err))
	}
	// log.Infof("indexLSummary: %v\n", indexLSummary)
	detaildata, err = json.Marshal(ListSchemaTableIndexNameLengthCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableIndexNameLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameIndexNameLengthCheck,
		indexLSummary.AssessTotal, indexLSummary.Compatible, indexLSummary.Incompatible, indexLSummary.Convertible, indexLSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleViewNameLengthCheck start")
	start_ts = time.Now()
	ListSchemaViewNameLengthCheck, viewSummary, err := o2t.AssessOracleViewNameLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleViewNameLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleViewNameLengthCheck err:%v", err))
	}
	// log.Infof("viewSummary: %v\n", viewSummary)
	detaildata, err = json.Marshal(ListSchemaViewNameLengthCheck)
	if err != nil {
		// log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaViewNameLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameViewNameLengthCheck,
		viewSummary.AssessTotal, viewSummary.Compatible, viewSummary.Incompatible, viewSummary.Convertible, viewSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSequenceNameLengthCheck start")
	start_ts = time.Now()
	ListSchemaSequenceNameLengthCheck, seqSummary, err := o2t.AssessOracleSequenceNameLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSequenceNameLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleSequenceNameLengthCheck err:%v", err))

	}
	detaildata, err = json.Marshal(ListSchemaSequenceNameLengthCheck)
	if err != nil {
		log.Errorf("err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaSequenceNameLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameSequenceNameLengthCheck,
		seqSummary.AssessTotal, seqSummary.Compatible, seqSummary.Incompatible, seqSummary.Convertible, seqSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleUsernameLengthCheck start")
	start_ts = time.Now()
	ListUsernameLengthCheck, usernameLSummary, err := o2t.AssessOracleUsernameLengthCheck(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleUsernameLengthCheck err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:AssessOracleUsernameLengthCheck err:%v", err))
	}
	// log.Infof("usernameLSummary: %v\n", usernameLSummary)
	detaildata, err = json.Marshal(ListUsernameLengthCheck)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListUsernameLengthCheck", duration_ts,
		common.AssessTypeObjectTypeCheck, common.AssessNameUsernameLengthCheck,
		usernameLSummary.AssessTotal, usernameLSummary.Compatible, usernameLSummary.Incompatible, usernameLSummary.Convertible, usernameLSummary.InConvertible,
		string(detaildata))

	// log.Infof("KEY_OBJECT_CHECK start")
	logtofileanddb(ctx, channelId, taskId, "info", "t01-003:KEY_OBJECT_CHECK start")
	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleMaxActiveSessionCount start")
	start_ts = time.Now()
	ListSchemaActiveSession, sessionSummary, err := o2t.AssessOracleMaxActiveSessionCount(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleMaxActiveSessionCount err: %v", err)
	}
	// log.Infof("sessionSummary: %v\n", sessionSummary)
	detaildata, err = json.Marshal(ListSchemaActiveSession)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaActiveSession", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaActiveSessionRelated,
		sessionSummary.AssessTotal, sessionSummary.Compatible, sessionSummary.Incompatible, sessionSummary.Convertible, sessionSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaOverview start")
	start_ts = time.Now()
	ListSchemaTableSizeData, overviewSummary, err := o2t.AssessOracleSchemaOverview(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaOverview err: %v", err)
	}
	// log.Infof("overviewSummary: %v\n", overviewSummary)
	detaildata, err = json.Marshal(ListSchemaTableSizeData)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableSizeData", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaDataSizeRelated,
		overviewSummary.AssessTotal, overviewSummary.Compatible, overviewSummary.Incompatible, overviewSummary.Convertible, overviewSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaTableRowsTOP start")
	start_ts = time.Now()
	ListSchemaTableRowsTOP, tableSummary, err := o2t.AssessOracleSchemaTableRowsTOP(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaTableRowsTOP err: %v", err)
	}
	// log.Infof("tableSummary: %v\n", tableSummary)
	detaildata, err = json.Marshal(ListSchemaTableRowsTOP)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableRowsTOP", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaTableRowsTopRelated,
		tableSummary.AssessTotal, tableSummary.Compatible, tableSummary.Incompatible, tableSummary.Convertible, tableSummary.InConvertible,
		string(detaildata))

	// start_ts = time.Now()
	// ListSchemaTableObjectCounts, objSummary, err := o2t.AssessOracleSchemaObjectOverview(schema_lst, oracleDB)
	// end_ts = time.Now()
	// duration_ts = end_ts.Sub(start_ts).String()
	// if err != nil {
	// 	log.Errorf("err: %v", err)
	// }
	// // log.Infof("objSummary: %v\n", objSummary)
	// detaildata, err = json.Marshal(ListSchemaTableObjectCounts)
	// if err != nil {
	// 	log.Errorf("err: %v", err)
	// }
	// err = insertAssessDetail(timsDB, channelId, taskId, DBName, schemaName, "ListSchemaTableObjectCounts", duration_ts,
	// 	objSummary.AssessType, objSummary.AssessName,
	// 	objSummary.AssessTotal, objSummary.Compatible, objSummary.Incompatible, objSummary.Convertible, objSummary.InConvertible,
	// 	string(detaildata))
	// if err != nil {
	// 	log.Errorf("err: %v", err)
	// }

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaTableAvgRowLengthTOP start")
	start_ts = time.Now()
	ListSchemaTableAvgRowLengthTOP, tableTSummary, err := o2t.AssessOracleSchemaTableAvgRowLengthTOP(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaTableAvgRowLengthTOP err: %v", err)
	}
	// log.Infof("tableTSummary: %v\n", tableTSummary)
	detaildata, err = json.Marshal(ListSchemaTableAvgRowLengthTOP)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableAvgRowLengthTOP", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaTableAvgRowLengthTopRelated,
		tableTSummary.AssessTotal, tableTSummary.Compatible, tableTSummary.Incompatible, tableTSummary.Convertible, tableTSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaSynonymOverview start")
	start_ts = time.Now()
	ListSchemaSynonymObject, synonymSummary, err := o2t.AssessOracleSchemaSynonymOverview(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaSynonymOverview err: %v", err)
	}
	ReportSummary_lst = append(ReportSummary_lst, synonymSummary)
	detaildata, err = json.Marshal(ListSchemaSynonymObject)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaSynonymObject", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaSynonymObjectRelated,
		synonymSummary.AssessTotal, synonymSummary.Compatible, synonymSummary.Incompatible, synonymSummary.Convertible, synonymSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaCodeOverview start")
	start_ts = time.Now()
	ListSchemaCodeObject, objSummary, err := o2t.AssessOracleSchemaCodeOverview(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaCodeOverview err: %v", err)
	}
	ReportSummary_lst = append(ReportSummary_lst, objSummary)
	detaildata, err = json.Marshal(ListSchemaCodeObject)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaCodeObject", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaCodeObjectRelated,
		objSummary.AssessTotal, objSummary.Compatible, objSummary.Incompatible, objSummary.Convertible, objSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaMaterializedViewOverview start")
	start_ts = time.Now()
	ListSchemaMaterializedViewObject, mViewSummary, err := o2t.AssessOracleSchemaMaterializedViewOverview(schema_lst, oracleDB)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaMaterializedViewOverview err: %v", err)
	}
	ReportSummary_lst = append(ReportSummary_lst, mViewSummary)
	detaildata, err = json.Marshal(ListSchemaMaterializedViewObject)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaMaterializedViewObject", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaMaterializedViewRelated,
		mViewSummary.AssessTotal, mViewSummary.Compatible, mViewSummary.Incompatible, mViewSummary.Convertible, mViewSummary.InConvertible,
		string(detaildata))

	logtofileanddb(ctx, channelId, taskId, "debug", "t01-003:o2t.AssessOracleSchemaTableNumberTypeEqual0 start")
	start_ts = time.Now()
	ListSchemaTableNumberTypeEqual0, equalSummary, err := o2t.AssessOracleSchemaTableNumberTypeEqual0(schema_lst, oracleDB)
	var ListSchemaTableNumberTypeEqual0Filter []public.SchemaTableNumberTypeEqual0
	for _, ow := range ListSchemaTableNumberTypeEqual0 {
		if !strings.HasPrefix(ow.TableName, "BIN$") {
			ListSchemaTableNumberTypeEqual0Filter = append(ListSchemaTableNumberTypeEqual0Filter, ow)
		}
	}
	equalSummary.AssessTotal = len(ListSchemaTableNumberTypeEqual0Filter)
	end_ts = time.Now()
	duration_ts = end_ts.Sub(start_ts).String()
	if err != nil {
		log.Errorf("o2t.AssessOracleSchemaTableNumberTypeEqual0 err: %v", err)
	}
	// log.Infof("equalSummary: %v\n", equalSummary)
	detaildata, err = json.Marshal(ListSchemaTableNumberTypeEqual0Filter)
	if err != nil {
		log.Errorf("err: %v", err)
	}
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, "ListSchemaTableNumberTypeEqual0", duration_ts,
		common.AssessTypeObjectTypeRelated, common.AssessNameSchemaTableNumberTypeEqual0,
		equalSummary.AssessTotal, equalSummary.Compatible, equalSummary.Incompatible, equalSummary.Convertible, equalSummary.InConvertible,
		string(detaildata))

	// log.Infof("assessObjectCheck finish, channelId:%d, schemaName:%s", channelId, schemaName)
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessObjectCheck finish, channelId:%d, schemaName:%s", channelId, schemaName))
	return nil
}

func assessObjectType(ctx context.Context, channelId int, taskId int, schema_lst []string, schemaName, objType string, oracleDB *oracle.Oracle, objAssessCompsMap map[string]meta.BuildinObjectCompatible, ReportSummary_lst []public.ReportSummary, DBName string) error {
	logtofileanddb(ctx, channelId, taskId, "debug", fmt.Sprintf("t01-003:o2t.assess%s start", objType))
	start_ts := time.Now()
	log.Infof("assessObjectType %s start, objAssessCompsMap: %v", objType, objAssessCompsMap)
	ListSchemaObjectTypeCompatibles, codeSummary, err := AssessOracleSchemaObjectTypeCompatibleByObjectType(schema_lst, objType, ctx, oracleDB, objAssessCompsMap)
	log.Infof("ListSchemaObjectTypeCompatibles %s size: %d", objType, len(ListSchemaObjectTypeCompatibles))

	end_ts := time.Now()
	duration_ts := end_ts.Sub(start_ts).String()
	if err != nil {
		// log.Errorf("o2t.AssessOracleSchemaObjectTypeCompatible err: %v", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:o2t.AssessOracleSchemaObjectTypeCompatible err:%v", err))
	}
	ReportSummary_lst = append(ReportSummary_lst, codeSummary)
	detaildata, err := json.Marshal(ListSchemaObjectTypeCompatibles)
	if err != nil {
		// log.Errorf("err: %v", err)

		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}

	assessName := tmscommon.AssessNameObjectTypeCompatible
	if strings.EqualFold(objType, "JOB") {
		assessName = tmscommon.AssessNameJobTypeCompatible
	} else if strings.EqualFold(objType, "SYNONYM") {
		assessName = tmscommon.AssessNameSynonymTypeCompatible
	} else if strings.EqualFold(objType, "BODY") {
		assessName = tmscommon.AssessNameBodyCompatible
	} else if strings.EqualFold(objType, "PACKAGE BODY") {
		assessName = tmscommon.AssessNamePackageBodyCompatible
	} else if strings.EqualFold(objType, "PACKAGE") {
		assessName = tmscommon.AssessNamePackageCompatible
	} else if strings.EqualFold(objType, "TRIGGER") {
		assessName = tmscommon.AssessNameTriggerCompatible
	} else if strings.EqualFold(objType, "PROCEDURE") {
		assessName = tmscommon.AssessNameProcedureCompatible
	} else if strings.EqualFold(objType, "FUNCTION") {
		assessName = tmscommon.AssessNameFunctionCompatible
	} else if strings.EqualFold(objType, "DATABASE LINK") {
		assessName = tmscommon.AssessNameDatabaseLinkCompatible
	} else if strings.EqualFold(objType, "LOB PARTITION") {
		assessName = tmscommon.AssessNameLobPartitionCompatible
	} else if strings.EqualFold(objType, "LOB SUBPARTITION") {
		assessName = tmscommon.AssessNameLobSubpartitionCompatible
	} else if strings.EqualFold(objType, "SEQUENCE") {
		assessName = tmscommon.AssessNameSequenceCompatible
	} else if strings.EqualFold(objType, "TYPE") {
		assessName = tmscommon.AssessNameTypeCompatible
	} else if strings.EqualFold(objType, "INDEX SUBPARTITION") {
		assessName = tmscommon.AssessNameIndexSubpartitionCompatible
	}

	structName := capitalizeWordsAndJoin(objType)
	insertAssessDetail(ctx, channelId, taskId, DBName, schemaName, fmt.Sprintf("ListSchema%sCompatibles", structName), duration_ts,
		common.AssessTypeObjectTypeCompatible, assessName,
		codeSummary.AssessTotal, codeSummary.Compatible, codeSummary.Incompatible, codeSummary.Convertible, codeSummary.InConvertible,
		string(detaildata))

	return nil
}

func capitalizeWordsAndJoin(s string) string {
	// 按空格分割
	words := strings.Split(s, " ")
	if len(words) == 0 {
		return ""
	}

	// 处理每个单词：首字母大写，其余小写
	for i, word := range words {
		if word == "" {
			continue // 跳过空单词
		}
		words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
	}

	// 合并并去掉空格
	return strings.Join(words, "")
}

func assessDataFillHtml(dataMap *message.ObjectCompatibleALL, fileName string, templateFile string) error {
	file, err := os.OpenFile(fileName, os.O_WRONLY|os.O_CREATE|os.O_APPEND|os.O_TRUNC, 0666)
	if err != nil {
		log.Errorf("open html file failed, err:%v", err)
	}
	defer file.Close()

	tf, err := template.ParseFiles(templateFile)
	if err != nil {
		log.Errorf("template parse FS failed: %v", err)
		return fmt.Errorf("template parse FS failed: %v", err)
	}

	err = tf.ExecuteTemplate(file, "object_assessment_report", dataMap)
	if err != nil {
		log.Errorf("template FS Execute [report1] template HTML failed: %v", err)
		return fmt.Errorf("template FS Execute [report1] template HTML failed: %v", err)
	}
	return nil
}

func GetAssessTableData(ctx context.Context, dbS *sql.DB, timsDB *sql.DB, channelId int, taskId int, schemaList string, title string) (*message.ObjectCompatibleALL, error) {
	var (
		sql string
		// Oracle Object Compatibles
		// oListOracleOverview *message.OracleOverview
		// oListSchemaTableTypeCompatibles []*message.SchemaTableTypeCompatibles
		// oListSchemaColumnTypeCompatibles []*message.SchemaColumnTypeCompatibles
		// oListSchemaConstraintTypeCompatibles     []*message.SchemaConstraintTypeCompatibles
		// oListSchemaIndexTypeCompatibles          []*message.SchemaIndexTypeCompatibles
		// oListSchemaDefaultValueCompatibles       []*message.SchemaDefaultValueCompatibles
		// oListSchemaViewTypeCompatibles           []*message.SchemaViewTypeCompatibles
		// oListSchemaObjectTypeCompatibles         []*message.SchemaObjectTypeCompatibles
		// oListSchemaPartitionTypeCompatibles      []*message.SchemaPartitionTypeCompatibles
		// oListSchemaSubPartitionTypeCompatibles   []*message.SchemaSubPartitionTypeCompatibles
		// oListSchemaTemporaryTableTypeCompatibles []*message.SchemaTemporaryTableTypeCompatibles
		// TiDB Object Limitation
		// oListSchemaPartitionTableCountsCheck  []*message.SchemaPartitionTableCountsCheck
		// oListSchemaTableRowLengthCheck        []*message.SchemaTableRowLengthCheck
		// oListSchemaTableIndexRowLengthCheck   []*message.SchemaTableIndexRowLengthCheck
		// oListSchemaTableColumnCountsCheck     []*message.SchemaTableColumnCountsCheck
		// oListSchemaTableIndexCountsCheck      []*message.SchemaTableIndexCountsCheck
		// oListSchemaTableNameLengthCheck       []*message.SchemaTableNameLengthCheck
		// oListSchemaViewNameLengthCheck        []*message.SchemaViewNameLengthCheck
		// oListSchemaSequenceNameLengthCheck    []*message.SchemaSequenceNameLengthCheck
		// oListSchemaTableIndexNameLengthCheck  []*message.SchemaTableIndexNameLengthCheck
		// oListSchemaTableColumnNameLengthCheck []*message.SchemaTableColumnNameLengthCheck
		// oListUsernameLengthCheck []*message.UsernameLengthCheck
		// Oracle Key Infomation
		// oListSchemaActiveSession          []*message.SchemaActiveSession
		// oListSchemaTableSizeData          []*message.SchemaTableSizeData
		// oListSchemaTableRowsTOP           []*message.SchemaTableRowsTOP
		// oListSchemaTableAvgRowLengthTOP   []*message.SchemaTableAvgRowLengthTOP
		// oListSchemaCodeObject             []*message.SchemaCodeObject
		// oListSchemaSynonymObject          []*message.SchemaSynonymObject
		// oListSchemaMaterializedViewObject []*message.SchemaMaterializedViewObject
		// oListSchemaTableNumberTypeEqual0 []*message.SchemaTableNumberTypeEqual0

		// Oracle Object Compatiblesmessage.
		ListOracleOverview                     []*message.OracleOverview
		ListSchemaTableTypeCompatibles         []*message.SchemaTableTypeCompatibles
		ListSchemaColumnTypeCompatibles        []*message.SchemaColumnTypeCompatibles
		ListSchemaConstraintTypeCompatibles    []*message.SchemaConstraintTypeCompatibles
		ListSchemaIndexTypeCompatibles         []*message.SchemaIndexTypeCompatibles
		ListSchemaSequenceCompatibles          []*message.SchemaSequenceCompatibles
		ListSchemaJobCompatibles               []*message.SchemaObjectTypeCompatibles
		ListSchemaSynonymCompatibles           []*message.SchemaObjectTypeCompatibles
		ListSchemaPackageBodyCompatibles       []*message.SchemaObjectTypeCompatibles
		ListSchemaPackageCompatibles           []*message.SchemaObjectTypeCompatibles
		ListSchemaTriggerCompatibles           []*message.SchemaObjectTypeCompatibles
		ListSchemaProcedureCompatibles         []*message.SchemaObjectTypeCompatibles
		ListSchemaFunctionCompatibles          []*message.SchemaObjectTypeCompatibles
		ListSchemaDatabaseLinkCompatibles      []*message.SchemaObjectTypeCompatibles
		ListSchemaLobPartitionCompatibles      []*message.SchemaObjectTypeCompatibles
		ListSchemaLobSubpartitionCompatibles   []*message.SchemaObjectTypeCompatibles
		ListSchemaIndexSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
		ListSchemaTypeCompatibles              []*message.SchemaObjectTypeCompatibles

		ListSchemaObjectTypeCompatibles         []*message.SchemaObjectTypeCompatibles
		ListLobTypeCompatibles                  []*message.SchemaObjectTypeCompatibles
		ListSchemaPartitionTypeCompatibles      []*message.SchemaPartitionTypeCompatibles
		ListSchemaSubPartitionTypeCompatibles   []*message.SchemaSubPartitionTypeCompatibles
		ListSchemaTemporaryTableTypeCompatibles []*message.SchemaTemporaryTableTypeCompatibles
		ListSchemaDefaultValueCompatibles       []*message.SchemaDefaultValueCompatibles
		ListSchemaViewTypeCompatibles           []*message.SchemaViewTypeCompatibles
		// TiDB Object Limitation
		ListSchemaPartitionTableCountsCheck  []*message.SchemaPartitionTableCountsCheck
		ListSchemaTableRowLengthCheck        []*message.SchemaTableRowLengthCheck
		ListSchemaTableIndexRowLengthCheck   []*message.SchemaTableIndexRowLengthCheck
		ListSchemaTableColumnCountsCheck     []*message.SchemaTableColumnCountsCheck
		ListSchemaTableIndexCountsCheck      []*message.SchemaTableIndexCountsCheck
		ListSchemaTableNameLengthCheck       []*message.SchemaTableNameLengthCheck
		ListSchemaViewNameLengthCheck        []*message.SchemaViewNameLengthCheck
		ListSchemaSequenceNameLengthCheck    []*message.SchemaSequenceNameLengthCheck
		ListSchemaTableIndexNameLengthCheck  []*message.SchemaTableIndexNameLengthCheck
		ListSchemaTableColumnNameLengthCheck []*message.SchemaTableColumnNameLengthCheck
		ListUsernameLengthCheck              []*message.UsernameLengthCheck
		// Oracle Key Infomation
		ListSchemaActiveSession          []*message.SchemaActiveSession
		ListSchemaTableSizeData          []*message.SchemaTableSizeData
		ListSchemaTableRowsTOP           []*message.SchemaTableRowsTOP
		ListSchemaTableAvgRowLengthTOP   []*message.SchemaTableAvgRowLengthTOP
		ListSchemaCodeObject             []*message.SchemaCodeObject
		ListSchemaSynonymObject          []*message.SchemaSynonymObject
		ListSchemaMaterializedViewObject []*message.SchemaMaterializedViewObject
		ListSchemaTableNumberTypeEqual0  []*message.SchemaTableNumberTypeEqual0
	)

	// log.Infof("Query object_assess_details summary data start")
	logtofileanddb(ctx, channelId, taskId, "info", "t01-004:Query object_assess_details summary data start")
	sql = fmt.Sprintf(`select min(updated_at) starttime,max(updated_at) endtime from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0 `, channelId, taskId, schemaList)
	_, data, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query start end time failed, err: %v", err)
	}
	var checkStartTime string
	var checkEndTime string
	for _, ow := range data {
		checkStartTime = ow["starttime"]
		checkEndTime = ow["endtime"]
	}
	checkTime := fmt.Sprintf("%s - %s", checkStartTime, checkEndTime)

	// 对象兼容性汇总(1-4)
	sql = fmt.Sprintf(`select 
	sum(assess_total) assess_total,
	sum(compatible) compatible,
	sum(incompatible) incompatible, 
	sum(convertible) convertible, 
	sum(inconvertible) inconvertible 
    from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0 `, channelId, taskId, schemaList)
	_, data, err = oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleOverview failed, err: %v", err)
	}
	var listObjectCompatibleOverview []*message.ObjectCompatibleOverview
	for _, ow := range data {
		listObjectCompatibleOverview = append(listObjectCompatibleOverview, &message.ObjectCompatibleOverview{
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}

	sql = fmt.Sprintf(`select assess_name,
	sum(assess_total) assess_total,
	sum(compatible) compatible,
	sum(incompatible) incompatible, 
	sum(convertible) convertible, 
	sum(inconvertible) inconvertible 
    from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0
	group by assess_name`, channelId, taskId, schemaList)

	_, data, err = oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleByObject failed, err: %v", err)
	}

	var listObjectCompatibleByObject []*message.ObjectCompatibleByObject
	for _, ow := range data {
		var assessNameCh = ""
		switch ow["assess_name"] {
		case "COLUMN_TYPE_COMPATIBLE":
			{
				assessNameCh = "字段数据类型"
			}
		case "TABLE_TYPE_COMPATIBLE":
			{
				assessNameCh = "表兼容性"
			}
		case "CONSTRAINT_TYPE_COMPATIBLE":
			{
				assessNameCh = "约束"
			}
		case "INDEX_TYPE_COMPATIBLE":
			{
				assessNameCh = "索引"
			}
		case "DEFAULT_VALUE_COMPATIBLE":
			{
				assessNameCh = "字段默认值设置"
			}
		case "OBJECT_TYPE_COMPATIBLE":
			{
				assessNameCh = "其它对象"
			}
		case "VIEW_TYPE_COMPATIBLE":
			{
				assessNameCh = "视图"
			}
		case "PARTITION_TYPE_COMPATIBLE":
			{
				assessNameCh = "分区表"
			}
		case "TEMPORARY_TABLE_TYPE_COMPATIBLE":
			{
				assessNameCh = "临时表"
			}
		case "SEQUENCE_COMPATIBLE":
			{
				assessNameCh = "SEQUENCE"
			}
		case "JOB_TYPE_COMPATIBLE":
			{
				assessNameCh = "JOB"
			}
		case "PACKAGE_BODY_COMPATIBLE":
			{
				assessNameCh = "PACKAGE BODY"
			}
		case "PACKAGE_COMPATIBLE":
			{
				assessNameCh = "PACKAGE"
			}
		case "PROCEDURE_COMPATIBLE":
			{
				assessNameCh = "PROCEDURE"
			}
		case "FUNCTION_COMPATIBLE":
			{
				assessNameCh = "FUNCTION"
			}
		case "INDEX_SUBPARTITION_COMPATIBLE":
			{
				assessNameCh = "INDEX SUBPARTITION"
			}
		case "LOB_TYPE_COMPATIBLE":
			{
				assessNameCh = "LOB"
			}
		case "SUBPARTITION_TYPE_COMPATIBLE":
			{
				assessNameCh = "SUBPARTITION"
			}

		case "SYNONYM_TYPE_COMPATIBLE":
			{
				assessNameCh = "SYNONYM"
			}
		case "BODY_COMPATIBLE":
			{
				assessNameCh = "BODY"
			}
		case "TRIGGER_COMPATIBLE":
			{
				assessNameCh = "TRIGGER"
			}
		case "TYPE_COMPATIBLE":
			{
				assessNameCh = "TYPE"
			}
		case "DATABASE_LINK_COMPATIBLE":
			{
				assessNameCh = "DATABASE LINK"
			}
		case "LOB_PARTITION_COMPATIBLE":
			{
				assessNameCh = "LOB PARTITION"
			}
		case "LOB_SUBPARTITION_COMPATIBLE":
			{
				assessNameCh = "LOB SUBPARTITION"
			}
		default:
			{
				assessNameCh = ow["assess_name"]
			}
		}
		listObjectCompatibleByObject = append(listObjectCompatibleByObject, &message.ObjectCompatibleByObject{
			AssessName:    assessNameCh,
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}

	sql = fmt.Sprintf(`select schema_name,
	sum(assess_total) assess_total,
	sum(compatible) compatible,
	sum(incompatible) incompatible, 
	sum(convertible) convertible, 
	sum(inconvertible) inconvertible 
    from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0
	group by schema_name`, channelId, taskId, schemaList)

	_, data, err = oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleBySchema failed, err: %v", err)
	}

	var listObjectCompatibleBySchema []*message.ObjectCompatibleBySchema
	for _, ow := range data {
		listObjectCompatibleBySchema = append(listObjectCompatibleBySchema, &message.ObjectCompatibleBySchema{
			Schema:        ow["schema_name"],
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}

	sql = fmt.Sprintf(`select schema_name,assess_name,
	sum(assess_total) assess_total,
	sum(compatible) compatible,
	sum(incompatible) incompatible, 
	sum(convertible) convertible, 
	sum(inconvertible) inconvertible 
    from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0
	group by schema_name,assess_name`, channelId, taskId, schemaList)

	_, data, err = oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleBySchemaObject failed, err: %v", err)
	}

	var listObjectCompatibleBySchemaObject []*message.ObjectCompatibleBySchemaObject
	for _, ow := range data {
		listObjectCompatibleBySchemaObject = append(listObjectCompatibleBySchemaObject, &message.ObjectCompatibleBySchemaObject{
			Schema:        ow["schema_name"],
			AssessName:    ow["assess_name"],
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}

	var schemasStr = strings.Replace(schemaList, "'", "", -1)
	var schemas = strings.Split(schemasStr, ",")

	taskInfo, getTaskInfoErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskInfoErr != nil {
		log.Errorf("get task info failed. taskId=%d", taskId)
	}

	//约束明细
	listOraDbaConstraint := getListOraDbaConstraint(ctx, channelId, taskId, schemas, taskInfo)

	//索引明细
	listOraDbaIndex := getListOraDbaIndex(ctx, channelId, taskId, schemas, taskInfo)

	//sequence明细
	listOraDbaSequence := getListOraDbaSequence(ctx, channelId, taskId, schemas, taskInfo)

	//字段数据类型明细
	listOraDbaTabColumns := getListOraDbaTabColumns(ctx, channelId, taskId, schemas, taskInfo)

	//字段默认值明细
	listDbaTabColumnsDefault := getListDbaTabColumnsDefault(ctx, channelId, taskId, schemas, taskInfo)

	//分区表明细
	listAllPartTables := getListAllPartTables(ctx, channelId, taskId, schemas, taskInfo)

	//临时表明细
	listDbaTables := getListDbaTables(ctx, channelId, taskId, schemas, taskInfo)

	//其它对象明细
	listAllObjects := getListOthersObjects(ctx, channelId, taskId, schemas, taskInfo)

	listJob := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "JOB", taskInfo)
	listSynonym := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "SYNONYM", taskInfo)
	listPackageBody := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "PACKAGE BODY", taskInfo)
	listPackage := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "PACKAGE", taskInfo)
	listTrigger := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "TRIGGER", taskInfo)
	listProcedure := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "PROCEDURE", taskInfo)
	listFunction := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "FUNCTION", taskInfo)
	listDataBaseLink := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "DATABASE LINK", taskInfo)
	listLobSubpartition := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "LOB SUBPARTITION", taskInfo)
	listType := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "TYPE", taskInfo)
	listIndexSubpartition := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "INDEX SUBPARTITION", taskInfo)
	listLobPartition := getListAllObjectsByObjType(ctx, channelId, taskId, schemas, "LOB PARTITION", taskInfo)

	//LOB明细
	listOraLobs := getListOraLobs(ctx, channelId, taskId, schemas, taskInfo)

	//表兼容性明细
	listOraTableTypeCompatibles := getListTableTypeCompatibles(ctx, channelId, taskId, schemas, taskInfo)

	//视图明细
	listOraDbaViews := getListOraDbaViews(ctx, channelId, taskId, schemas, taskInfo)

	//复合分区表明细
	listOraSubPartitioning := getListOraSubPartitioning(ctx, channelId, taskId, schemas, taskInfo)

	// log.Infof("Query object_assess_details detail data start")
	logtofileanddb(ctx, channelId, taskId, "info", "t01-004:Query object_assess_details detail data start")
	// 获取明细结果数据
	sql = fmt.Sprintf(`select channel_id,schema_name,assess_type,assess_name, detail,struct_name 
		from object_assess_details oad 
		where channel_id =%d 
		and task_id=%d 
		and schema_name in (%s) 
		and assess_total>0
		order by assess_type,schema_name`, channelId, taskId, schemaList)
	_, data, err = oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("t01-004:Query object_assess_details failed, err: %v", err)
	}
	for _, v := range data {
		// fmt.Printf("v[struct_name]: %v\n", v["struct_name"])
		jsonData := []byte(v["detail"])
		switch v["struct_name"] {
		case "ListOracleOverview":
			var oListOracleOverview *message.OracleOverview
			err = json.Unmarshal(jsonData, &oListOracleOverview)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			oListOracleOverview.CheckTime = checkTime
			ListOracleOverview = []*message.OracleOverview{}
			ListOracleOverview = append(ListOracleOverview, oListOracleOverview)
		case "ListSchemaColumnTypeCompatibles":
			var oListSchemaColumnTypeCompatibles []*message.SchemaColumnTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaColumnTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaColumnTypeCompatibles {
				ListSchemaColumnTypeCompatibles = append(ListSchemaColumnTypeCompatibles, o)
			}
		case "ListSchemaTableTypeCompatibles":
			var oListSchemaTableTypeCompatibles []*message.SchemaTableTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTableTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableTypeCompatibles {
				ListSchemaTableTypeCompatibles = append(ListSchemaTableTypeCompatibles, o)
			}
		case "ListSchemaConstraintTypeCompatibles":
			var oListSchemaConstraintTypeCompatibles []*message.SchemaConstraintTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaConstraintTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaConstraintTypeCompatibles {
				ListSchemaConstraintTypeCompatibles = append(ListSchemaConstraintTypeCompatibles, o)
			}

		case "ListSchemaIndexTypeCompatibles":
			var oListSchemaIndexTypeCompatibles []*message.SchemaIndexTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaIndexTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaIndexTypeCompatibles {
				ListSchemaIndexTypeCompatibles = append(ListSchemaIndexTypeCompatibles, o)
			}

		case "ListSchemaSequenceCompatibles":
			var oListSchemaSequenceCompatibles []*message.SchemaSequenceCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaSequenceCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSequenceCompatibles {
				ListSchemaSequenceCompatibles = append(ListSchemaSequenceCompatibles, o)
			}

		case "ListSchemaJobCompatibles":
			var oListSchemaJobCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaJobCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaJobCompatibles {
				ListSchemaJobCompatibles = append(ListSchemaJobCompatibles, o)
			}

		case "ListSchemaSynonymCompatibles":
			var oListSchemaSynonymCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaSynonymCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSynonymCompatibles {
				ListSchemaSynonymCompatibles = append(ListSchemaSynonymCompatibles, o)
			}

		case "ListSchemaPackageBodyCompatibles":
			var oListSchemaPackageBodyCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaPackageBodyCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPackageBodyCompatibles {
				ListSchemaPackageBodyCompatibles = append(ListSchemaPackageBodyCompatibles, o)
			}

		case "ListSchemaPackageCompatibles":
			var oListSchemaPackageCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaPackageCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPackageCompatibles {
				ListSchemaPackageCompatibles = append(ListSchemaPackageCompatibles, o)
			}

		case "ListSchemaTriggerCompatibles":
			var oListSchemaTriggerCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTriggerCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTriggerCompatibles {
				ListSchemaTriggerCompatibles = append(ListSchemaTriggerCompatibles, o)
			}

		case "ListSchemaProcedureCompatibles":
			var oListSchemaProcedureCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaProcedureCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaProcedureCompatibles {
				ListSchemaProcedureCompatibles = append(ListSchemaProcedureCompatibles, o)
			}

		case "ListSchemaFunctionCompatibles":
			var oListSchemaFunctionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaFunctionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaFunctionCompatibles {
				ListSchemaFunctionCompatibles = append(ListSchemaFunctionCompatibles, o)
			}

		case "ListSchemaDatabaseLinkCompatibles":
			var oListSchemaDatabaseLinkCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaDatabaseLinkCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaDatabaseLinkCompatibles {
				ListSchemaDatabaseLinkCompatibles = append(ListSchemaDatabaseLinkCompatibles, o)
			}

		case "ListSchemaLobPartitionCompatibles":
			var oListSchemaLobPartitionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaLobPartitionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaLobPartitionCompatibles {
				ListSchemaLobPartitionCompatibles = append(ListSchemaLobPartitionCompatibles, o)
			}

		case "ListSchemaLobSubpartitionCompatibles":
			var oListSchemaLobSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaLobSubpartitionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaLobSubpartitionCompatibles {
				ListSchemaLobSubpartitionCompatibles = append(ListSchemaLobSubpartitionCompatibles, o)
			}

		case "ListSchemaIndexSubpartitionCompatibles":
			var oListSchemaIndexSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaIndexSubpartitionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaIndexSubpartitionCompatibles {
				ListSchemaIndexSubpartitionCompatibles = append(ListSchemaIndexSubpartitionCompatibles, o)
			}

		case "ListSchemaTypeCompatibles":
			var oListSchemaTypeCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTypeCompatibles {
				ListSchemaTypeCompatibles = append(ListSchemaTypeCompatibles, o)
			}

		case "ListSchemaDefaultValueCompatibles":
			var oListSchemaDefaultValueCompatibles []*message.SchemaDefaultValueCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaDefaultValueCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaDefaultValueCompatibles {
				if o.ColumnDefaultValue == "" {
					o.ColumnDefaultValue = "NULL"
				}
				if o.DefaultValueMap == "" {
					o.DefaultValueMap = "NULL"
				}
				ListSchemaDefaultValueCompatibles = append(ListSchemaDefaultValueCompatibles, o)
			}
		case "ListSchemaViewTypeCompatibles":
			var oListSchemaViewTypeCompatibles []*message.SchemaViewTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaViewTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaViewTypeCompatibles {
				ListSchemaViewTypeCompatibles = append(ListSchemaViewTypeCompatibles, o)
			}
		case "ListSchemaObjectTypeCompatibles":
			var oListSchemaObjectTypeCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaObjectTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaObjectTypeCompatibles {
				ListSchemaObjectTypeCompatibles = append(ListSchemaObjectTypeCompatibles, o)
			}
		case "ListLobTypeCompatibles":
			var oListLobTypeCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListLobTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListLobTypeCompatibles {
				ListLobTypeCompatibles = append(ListLobTypeCompatibles, o)
			}
		case "ListSchemaPartitionTypeCompatibles":
			var oListSchemaPartitionTypeCompatibles []*message.SchemaPartitionTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaPartitionTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPartitionTypeCompatibles {
				ListSchemaPartitionTypeCompatibles = append(ListSchemaPartitionTypeCompatibles, o)
			}
		case "ListSchemaSubPartitionTypeCompatibles":
			var oListSchemaSubPartitionTypeCompatibles []*message.SchemaSubPartitionTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaSubPartitionTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSubPartitionTypeCompatibles {
				ListSchemaSubPartitionTypeCompatibles = append(ListSchemaSubPartitionTypeCompatibles, o)
			}
		case "ListSchemaTemporaryTableTypeCompatibles":
			var oListSchemaTemporaryTableTypeCompatibles []*message.SchemaTemporaryTableTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTemporaryTableTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTemporaryTableTypeCompatibles {
				ListSchemaTemporaryTableTypeCompatibles = append(ListSchemaTemporaryTableTypeCompatibles, o)
			}

		case "ListSchemaPartitionTableCountsCheck":
			var oListSchemaPartitionTableCountsCheck []*message.SchemaPartitionTableCountsCheck
			err = json.Unmarshal(jsonData, &oListSchemaPartitionTableCountsCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPartitionTableCountsCheck {
				ListSchemaPartitionTableCountsCheck = append(ListSchemaPartitionTableCountsCheck, o)
			}
		case "ListSchemaTableRowLengthCheck":
			var oListSchemaTableRowLengthCheck []*message.SchemaTableRowLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableRowLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableRowLengthCheck {
				ListSchemaTableRowLengthCheck = append(ListSchemaTableRowLengthCheck, o)
			}
		case "ListSchemaTableIndexRowLengthCheck":
			var oListSchemaTableIndexRowLengthCheck []*message.SchemaTableIndexRowLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableIndexRowLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableIndexRowLengthCheck {
				ListSchemaTableIndexRowLengthCheck = append(ListSchemaTableIndexRowLengthCheck, o)
			}
		case "ListSchemaTableColumnCountsCheck":
			var oListSchemaTableColumnCountsCheck []*message.SchemaTableColumnCountsCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableColumnCountsCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableColumnCountsCheck {
				ListSchemaTableColumnCountsCheck = append(ListSchemaTableColumnCountsCheck, o)
			}
		case "ListSchemaTableIndexCountsCheck":
			var oListSchemaTableIndexCountsCheck []*message.SchemaTableIndexCountsCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableIndexCountsCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableIndexCountsCheck {
				ListSchemaTableIndexCountsCheck = append(ListSchemaTableIndexCountsCheck, o)
			}
		case "ListSchemaTableNameLengthCheck":
			var oListSchemaTableNameLengthCheck []*message.SchemaTableNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableNameLengthCheck {
				ListSchemaTableNameLengthCheck = append(ListSchemaTableNameLengthCheck, o)
			}
		case "ListSchemaViewNameLengthCheck":
			var oListSchemaViewNameLengthCheck []*message.SchemaViewNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaViewNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaViewNameLengthCheck {
				ListSchemaViewNameLengthCheck = append(ListSchemaViewNameLengthCheck, o)
			}
		case "ListSchemaSequenceNameLengthCheck":
			var oListSchemaSequenceNameLengthCheck []*message.SchemaSequenceNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaSequenceNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSequenceNameLengthCheck {
				ListSchemaSequenceNameLengthCheck = append(ListSchemaSequenceNameLengthCheck, o)
			}
		case "ListSchemaTableIndexNameLengthCheck":
			var oListSchemaTableIndexNameLengthCheck []*message.SchemaTableIndexNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableIndexNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableIndexNameLengthCheck {
				ListSchemaTableIndexNameLengthCheck = append(ListSchemaTableIndexNameLengthCheck, o)
			}
		case "ListSchemaTableColumnNameLengthCheck":
			var oListSchemaTableColumnNameLengthCheck []*message.SchemaTableColumnNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableColumnNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableColumnNameLengthCheck {
				ListSchemaTableColumnNameLengthCheck = append(ListSchemaTableColumnNameLengthCheck, o)
			}
		case "ListUsernameLengthCheck":
			var oListUsernameLengthCheck []*message.UsernameLengthCheck
			err = json.Unmarshal(jsonData, &oListUsernameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListUsernameLengthCheck {
				ListUsernameLengthCheck = append(ListUsernameLengthCheck, o)
			}

		case "ListSchemaActiveSession":
			var oListSchemaActiveSession []*message.SchemaActiveSession
			err = json.Unmarshal(jsonData, &oListSchemaActiveSession)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaActiveSession {
				ListSchemaActiveSession = append(ListSchemaActiveSession, o)
			}
		case "ListSchemaTableSizeData":
			var oListSchemaTableSizeData []*message.SchemaTableSizeData
			err = json.Unmarshal(jsonData, &oListSchemaTableSizeData)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableSizeData {
				ListSchemaTableSizeData = append(ListSchemaTableSizeData, o)
			}
		case "ListSchemaTableRowsTOP":
			var oListSchemaTableRowsTOP []*message.SchemaTableRowsTOP
			err = json.Unmarshal(jsonData, &oListSchemaTableRowsTOP)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableRowsTOP {
				ListSchemaTableRowsTOP = append(ListSchemaTableRowsTOP, o)
			}
		case "ListSchemaTableAvgRowLengthTOP":
			var oListSchemaTableAvgRowLengthTOP []*message.SchemaTableAvgRowLengthTOP
			err = json.Unmarshal(jsonData, &oListSchemaTableAvgRowLengthTOP)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableAvgRowLengthTOP {
				ListSchemaTableAvgRowLengthTOP = append(ListSchemaTableAvgRowLengthTOP, o)
			}
		case "ListSchemaCodeObject":
			var oListSchemaCodeObject []*message.SchemaCodeObject
			err = json.Unmarshal(jsonData, &oListSchemaCodeObject)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaCodeObject {
				ListSchemaCodeObject = append(ListSchemaCodeObject, o)
			}
		case "ListSchemaSynonymObject":
			var oListSchemaSynonymObject []*message.SchemaSynonymObject
			err = json.Unmarshal(jsonData, &oListSchemaSynonymObject)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSynonymObject {
				ListSchemaSynonymObject = append(ListSchemaSynonymObject, o)
			}
		case "ListSchemaMaterializedViewObject":
			var oListSchemaMaterializedViewObject []*message.SchemaMaterializedViewObject
			err = json.Unmarshal(jsonData, &oListSchemaMaterializedViewObject)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaMaterializedViewObject {
				ListSchemaMaterializedViewObject = append(ListSchemaMaterializedViewObject, o)
			}
		case "ListSchemaTableNumberTypeEqual0":
			var oListSchemaTableNumberTypeEqual0 []*message.SchemaTableNumberTypeEqual0
			err = json.Unmarshal(jsonData, &oListSchemaTableNumberTypeEqual0)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableNumberTypeEqual0 {
				ListSchemaTableNumberTypeEqual0 = append(ListSchemaTableNumberTypeEqual0, o)
			}

		default:
			log.Infof("v[\"struct_name\"]--unknown: %v\n", v["struct_name"])
		}
	}

	defaultTypes := []string{"PACKAGE BODY", "PROCEDURE", "FUNCTION", "TRIGGER"}
	oracleObjectDefinitions, getErr := models.GetDatasourceReaderWriter().GetOracleObjectDefinitionsBySchemas(ctx, dbS, strings.Split(strings.ReplaceAll(schemaList, "'", ""), ","), defaultTypes)
	if getErr != nil {
		log.Errorf("get oracle source codes error, channelId:%d, taskId:%d, err:%v", channelId, taskId, getErr)
	}

	oracleObjectStatuses, getStatusErr := models.GetDatasourceReaderWriter().GetOracleObjectStatusBySchemas(ctx, dbS, strings.Split(strings.ReplaceAll(schemaList, "'", ""), ","), defaultTypes)
	if getStatusErr != nil {
		log.Errorf("get oracle object status error, channelId:%d, taskId:%d, err:%v", channelId, taskId, getStatusErr)
	}

	listPackageBodyCode := getObjectList(oracleObjectDefinitions, oracleObjectStatuses, "PACKAGE BODY")
	listProcedureCode := getObjectList(oracleObjectDefinitions, oracleObjectStatuses, "PROCEDURE")
	listFunctionCode := getObjectList(oracleObjectDefinitions, oracleObjectStatuses, "FUNCTION")
	listTriggerCode := getObjectList(oracleObjectDefinitions, oracleObjectStatuses, "TRIGGER")

	htmlData := &message.ObjectCompatibleALL{
		Title:                              title,
		ListOracleOverview:                 ListOracleOverview,
		ListObjectCompatibleOverview:       listObjectCompatibleOverview,
		ListObjectCompatibleByObject:       listObjectCompatibleByObject,
		ListObjectCompatibleBySchema:       listObjectCompatibleBySchema,
		ListObjectCompatibleBySchemaObject: listObjectCompatibleBySchemaObject,

		ListSchemaColumnTypeCompatibles:         ListSchemaColumnTypeCompatibles,
		ListSchemaTableTypeCompatibles:          ListSchemaTableTypeCompatibles,
		ListSchemaConstraintTypeCompatibles:     ListSchemaConstraintTypeCompatibles,
		ListSchemaIndexTypeCompatibles:          ListSchemaIndexTypeCompatibles,
		ListSchemaSequenceCompatibles:           ListSchemaSequenceCompatibles,
		ListSchemaDefaultValueCompatibles:       ListSchemaDefaultValueCompatibles,
		ListSchemaViewTypeCompatibles:           ListSchemaViewTypeCompatibles,
		ListSchemaObjectTypeCompatibles:         ListSchemaObjectTypeCompatibles,
		ListLobTypeCompatibles:                  ListLobTypeCompatibles,
		ListSchemaPartitionTypeCompatibles:      ListSchemaPartitionTypeCompatibles,
		ListSchemaSubPartitionTypeCompatibles:   ListSchemaSubPartitionTypeCompatibles,
		ListSchemaTemporaryTableTypeCompatibles: ListSchemaTemporaryTableTypeCompatibles,

		ListSchemaJobCompatibles:               ListSchemaJobCompatibles,
		ListSchemaSynonymCompatibles:           ListSchemaSynonymCompatibles,
		ListSchemaPackageBodyCompatibles:       ListSchemaPackageBodyCompatibles,
		ListSchemaPackageCompatibles:           ListSchemaPackageCompatibles,
		ListSchemaTriggerCompatibles:           ListSchemaTriggerCompatibles,
		ListSchemaProcedureCompatibles:         ListSchemaProcedureCompatibles,
		ListSchemaFunctionCompatibles:          ListSchemaFunctionCompatibles,
		ListSchemaDatabaseLinkCompatibles:      ListSchemaDatabaseLinkCompatibles,
		ListSchemaLobPartitionCompatibles:      ListSchemaLobPartitionCompatibles,
		ListSchemaLobSubpartitionCompatibles:   ListSchemaLobSubpartitionCompatibles,
		ListSchemaIndexSubpartitionCompatibles: ListSchemaIndexSubpartitionCompatibles,
		ListSchemaTypeCompatibles:              ListSchemaTypeCompatibles,

		ListSchemaPartitionTableCountsCheck:  ListSchemaPartitionTableCountsCheck,
		ListSchemaTableRowLengthCheck:        ListSchemaTableRowLengthCheck,
		ListSchemaTableIndexRowLengthCheck:   ListSchemaTableIndexRowLengthCheck,
		ListSchemaTableColumnCountsCheck:     ListSchemaTableColumnCountsCheck,
		ListSchemaTableIndexCountsCheck:      ListSchemaTableIndexCountsCheck,
		ListSchemaTableNameLengthCheck:       ListSchemaTableNameLengthCheck,
		ListSchemaViewNameLengthCheck:        ListSchemaViewNameLengthCheck,
		ListSchemaSequenceNameLengthCheck:    ListSchemaSequenceNameLengthCheck,
		ListSchemaTableIndexNameLengthCheck:  ListSchemaTableIndexNameLengthCheck,
		ListSchemaTableColumnNameLengthCheck: ListSchemaTableColumnNameLengthCheck,
		ListUsernameLengthCheck:              ListUsernameLengthCheck,

		ListSchemaActiveSession:          ListSchemaActiveSession,
		ListSchemaTableSizeData:          ListSchemaTableSizeData,
		ListSchemaTableRowsTOP:           ListSchemaTableRowsTOP,
		ListSchemaTableAvgRowLengthTOP:   ListSchemaTableAvgRowLengthTOP,
		ListSchemaCodeObject:             ListSchemaCodeObject,
		ListSchemaSynonymObject:          ListSchemaSynonymObject,
		ListSchemaMaterializedViewObject: ListSchemaMaterializedViewObject,
		ListSchemaTableNumberTypeEqual0:  ListSchemaTableNumberTypeEqual0,
		ListOraDbaConstraint:             listOraDbaConstraint,
		ListOraDbaIndex:                  listOraDbaIndex,
		ListOraDbaSequence:               listOraDbaSequence,

		ListOraDbaTabColumns:        listOraDbaTabColumns,
		ListDbaTabColumnsDefault:    listDbaTabColumnsDefault,
		ListAllPartTables:           listAllPartTables,
		ListDbaTables:               listDbaTables,
		ListAllObjects:              listAllObjects,
		ListOraLobs:                 listOraLobs,
		ListOraTableTypeCompatibles: listOraTableTypeCompatibles,
		ListOraDbaViews:             listOraDbaViews,
		ListOraSubPartitioning:      listOraSubPartitioning,

		ListJob:               listJob,
		ListSynonym:           listSynonym,
		ListPackageBody:       listPackageBody,
		ListPackage:           listPackage,
		ListTrigger:           listTrigger,
		ListProcedure:         listProcedure,
		ListFunction:          listFunction,
		ListDataBaseLink:      listDataBaseLink,
		ListLobSubpartition:   listLobSubpartition,
		ListType:              listType,
		ListIndexSubpartition: listIndexSubpartition,
		ListLobPartition:      listLobPartition,

		ListPackageBodyCode:   listPackageBodyCode,
		ListFunctionBodyCode:  listFunctionCode,
		ListProcedureBodyCode: listProcedureCode,
		ListTriggerCode:       listTriggerCode,
	}

	return htmlData, nil
}

func getObjectList(oracleObjectDefinitions []datasource.OracleObjectDefinition, oracleObjectStatuses []datasource.OracleObject, objectType string) []*message.OraObjectCode {
	osMap := make(map[string]*message.OraObjectCode)
	for _, o := range oracleObjectDefinitions {
		if strings.EqualFold(objectType, o.ObjectType) {
			osMap[o.Key()] = &message.OraObjectCode{
				OwnerName:  o.OwnerName,
				ObjectType: o.ObjectType,
				ObjectName: o.ObjectName,
				AllText:    o.AllText,
				Status:     "",
			}
		}
	}
	for _, o := range oracleObjectStatuses {
		if os, ok := osMap[o.Key()]; ok {
			os.Status = o.Status
		}
	}
	// 创建一个切片来存储 map 的值
	values := make([]*message.OraObjectCode, 0, len(osMap))

	// 遍历 map 并将值添加到切片中
	for _, value := range osMap {
		values = append(values, value)
	}

	return values
}

func mkdirIfNotExist(reportPath string) error {
	if reportPath != "" {
		_, err := os.Stat(reportPath)
		if err != nil {
			log.Infof("report path not exists, start create it. %v", reportPath)
			if mkdirErr := os.MkdirAll(reportPath, os.ModePerm); mkdirErr != nil {
				log.Errorf("create path failed. err:%s", mkdirErr)
				return mkdirErr
			}
		}
	}
	return nil
}

func buildDataSourceMessageFromModel(model *datasource.Datasource) *message.DataSourceInfo {
	return &message.DataSourceInfo{
		DataSourceID:     model.DatasourceId,
		DataSourceName:   model.DatasourceName,
		DbType:           model.DbType,
		Host:             model.HostIp,
		Port:             model.HostPort,
		DbName:           model.DbName,
		ServiceName:      datasourcepkg.GetServiceName(model),
		Username:         model.UserName,
		Password:         model.PasswordValue,
		ConnectionStatus: model.ConnectionStatus,
	}
}

func doObjectAssessTask(ctx context.Context, timsConfig *config.Config, channelId int, taskId int) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-001:doObjectAssessTask start.channelId:%d, taskId:%d.", channelId, taskId))
	// 获取tims连接以及评估库Oracle信息
	dbT, err := models.OpenMysql(timsConfig.DBConfig.User, timsConfig.DBConfig.Password, timsConfig.DBConfig.Host, timsConfig.DBConfig.Port, timsConfig.DBConfig.Schema)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-001:do assess task failed, open tims db failed. err:%s", err))
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-001:timsConfig:%v", timsConfig))
		return err
	}
	defer dbT.Close()
	UpdateTaskProgress(ctx, taskId, 0.1)

	var (
		sql          string
		assesstask   message.AssessTaskInfo
		oracledbinfo *message.DataSourceInfo
	)
	// 获取task有关的datasource信息
	sql = fmt.Sprintf(`select t1.task_id,t1.task_name,t1.channel_id,
t2.channel_name,t2.channel_type,t2.obj_assessment,
t2.datasource_id_s,t2.datasource_name_s,
t2.datasource_id_t,t2.datasource_name_t 
from tasks t1
inner join channel_informations t2
on t1.channel_id=t2.channel_id
where t1.channel_id=%d
and t1.task_id=%d
limit 1`, channelId, taskId)

	rows := dbT.QueryRow(sql)
	err = rows.Scan(&assesstask.TaskId,
		&assesstask.TaskName,
		&assesstask.ChannelId,
		&assesstask.ChannelName,
		&assesstask.ChannelType,
		&assesstask.ObjAssessment,
		&assesstask.DatasourceIdS,
		&assesstask.DatasourceNameS,
		&assesstask.DatasourceIdT,
		&assesstask.DatasourceNameT,
	)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-002:Query data err: %v", err))
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-002:Query data sql: %v", sql))
		return err
	}
	logtofileanddb(ctx, channelId, taskId, "debug", fmt.Sprintf("t01-002:Query data rows, assesstask: %v", assesstask))

	dataSourceId, err := strconv.Atoi(assesstask.DatasourceIdS)
	if err != nil {
		log.Errorf("convert datasourceid failed, datasource id is %d, error: %v", assesstask.DatasourceIdS, err)
	}
	ds, err := models.GetDatasourceReaderWriter().Get(ctx, dataSourceId)
	if err != nil {
		log.Errorf("get datasource failed, datasource id is %d, error: %v", assesstask.DatasourceIdS, err)
	}
	oracledbinfo = buildDataSourceMessageFromModel(ds)
	logtofileanddb(ctx, channelId, taskId, "debug", fmt.Sprintf("t01-002:Query data rows, oracledbinfo: %v", oracledbinfo))

	// 获取oracle连接
	oraInfo := transdbconfig.OracleConfig{
		Host:        oracledbinfo.Host,
		Port:        oracledbinfo.Port,
		Username:    oracledbinfo.Username,
		Password:    oracledbinfo.Password,
		ServiceName: oracledbinfo.ServiceName,
	}
	oracleDB, err := oracle.NewOracleDBEngine(ctx, oraInfo, "")
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-002:do assess task failed, open oracle db failed. err:%s", err))
		return err
	}
	if _, setModuleErr := oracleDB.OracleDB.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-002:do assess task failed, set oracle module failed. err:%s", err))
		return setModuleErr
	}

	// 获取schema信息
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-002:Get channel schemas, channelId:%d", channelId))
	var channelSchemas string
	var channelSchemasLst []string
	sql = fmt.Sprintf(`select CONCAT('''',GROUP_CONCAT(schema_name_s  separator ''','''),'''') 
	from  channel_schemas cs  
	where channel_id =%d and deleted_at is null`, channelId)
	rows = dbT.QueryRow(sql)
	err = rows.Scan(&channelSchemas)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-002:err: %s", err))
	}
	channelSchemasLst = strings.Split(channelSchemas, ",")

	// 更新task状态
	// 1:未配置 2:未运行 3:运行中 4:运行失败 5:运行完成
	updateTable := "tasks"
	startTime := "'" + time.Now().Format("2006-01-02 15:04:05") + "'"
	endTime := "NULL"
	runStatus := "'" + strconv.Itoa(constants.TASK_STATUS_RUNNING) + "'"
	err = assessTaskStatusUpdate(dbT, updateTable, startTime, endTime, runStatus, taskId)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-002:update task status failed. err: %v", err))
	}
	runStatus = "'" + strconv.Itoa(constants.TASK_STATUS_FINISH) + "'"
	UpdateTaskProgress(ctx, taskId, 0.2)

	progress := 0.2
	// 按照schema执行对象兼容性检查
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:start object check by schema, channelSchemasLst:%v", channelSchemasLst))
	for k, v := range channelSchemasLst {
		logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:start to run assessObjectCheck, channelId:%d, schema:%s", channelId, v))
		err = assessObjectCheck(ctx, dbT, oracleDB, channelId, taskId, v, k)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessObjectCheck err: %v", err))
			runStatus = "'" + strconv.Itoa(constants.TASK_STATUS_FAILED) + "'"
		}
		progress += 0.3 * float64(k/len(channelSchemasLst))
		UpdateTaskProgress(ctx, taskId, progress)

		err = assessObjectDetailCheck(ctx, dbT, oraInfo, channelId, taskId, v, k, ds)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessObjectDetailCheck err: %v", err))
			runStatus = "'" + strconv.Itoa(constants.TASK_STATUS_FAILED) + "'"
		}
		progress += 0.3 * float64(k/len(channelSchemasLst))
		UpdateTaskProgress(ctx, taskId, progress)
	}
	UpdateTaskProgress(ctx, taskId, 0.8)

	// 生成检查结果文件
	//currentTimeStr := time.Now().Format("20060102150405")
	dataDir := fmt.Sprintf("%s/assessment/assessment_channel%d_task%d/", timsConfig.DataDir, channelId, taskId)
	//reportFilename := fmt.Sprintf("%sindex.html", dataDir)
	zipName := fmt.Sprintf("%s/assessment/assessment_channel%d_task%d.zip", timsConfig.DataDir, channelId, taskId)
	title := "compatibility_report"

	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-004:start generate html report,schema list:%s, report name:%v", channelSchemasLst, zipName))

	dbS, err := models.OpenOracle(models.BuildOracleConfigFromTransferDBConf(&oraInfo, ds, ""))
	if err != nil {
		log.Errorf("open oracle failed. err:%s", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:open oracle failed. err:%s", err))
		return err
	}

	htmldata, err := GetAssessTableData(ctx, dbS, dbT, channelId, taskId, channelSchemas, title)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-004:GetAssessTableData err:%s", err))
	}
	err = commonpkg.GenerateReport(ctx, taskId, commonpkg.OBJECT_ASS_REPORT, title, dataDir, zipName, &message.ObjectCompatibleAdapter{Data: htmldata})
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-004:generateAssessReport err: %v", err))
	}
	UpdateTaskProgress(ctx, taskId, 0.9)

	// 按schema生成报告

	// 更新task状态
	startTime = "start_time"
	endTime = "'" + time.Now().Format("2006-01-02 15:04:05") + "'"
	err = assessTaskStatusUpdate(dbT, updateTable, startTime, endTime, runStatus, taskId)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-005:update task status failed. err: %v", err))
		return err
	}

	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-005:ObjectAssessTaskRun finish.channelId:%d, taskId:%d.", channelId, taskId))
	UpdateTaskProgress(ctx, taskId, 1)

	return nil
}

func assessObjectDetailCheck(ctx context.Context, timsDB *sql.DB, oracledbinfo transdbconfig.OracleConfig, channelId int, taskId int, schemaName string, schemaSeq int, ds *datasource.Datasource) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessObjectDetailCheck start, channelId:%d, schemaName:%s", channelId, schemaName))
	dbOra, err := models.OpenOracle(models.BuildOracleConfigFromTransferDBConf(&oracledbinfo, ds, ""))
	if err != nil {
		log.Errorf("open oracle failed. err:%s", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:open oracle failed. err:%s", err))
		return err
	}
	defer dbOra.Close()

	// 获取TiMS的对象兼容性配置
	// sql = "select * from objmap_rules where db_type_s='oracle' and db_type_t='tidb'"
	querySql := fmt.Sprintf(`select t1.db_type_s , t1.db_type_t, t1.object_type, t1.is_compatibility, t1.is_convertible from objmap_rules t1 
	INNER JOIN tmplate_rules t2 ON t1.objmap_rule_id = t2.map_rule_id
	INNER JOIN tmplate_informations t3 ON t3.tmplate_id = t2.tmplate_id
	INNER JOIN tasks t4 ON t3.tmplate_id = t4.objmap_tmplate_id
	WHERE t1.db_type_s='oracle' and t1.db_type_t='tidb' 
	AND t4.task_id=%d`, taskId)
	_, compatibleObjConf, err := oracle.Query(ctx, timsDB, querySql)
	if err != nil {
		// log.Errorf("assessObjectCheck failed. schemaName:%s. err:%s", schemaName, err)
		// log.Errorf("Query Sql:%s", sql)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessObjectCheck failed. schemaName:%s. err:%s", schemaName, err))
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:Query Sql:%s", querySql))
	}
	objAssessCompsMap := make(map[string]ObjectCompatible, 0)
	for _, c := range compatibleObjConf {
		objAssessCompsMap[common.StringUPPER(c["object_type"])] = ObjectCompatible{
			DbTypeS:       c["db_type_s"],
			DbTypeT:       c["db_type_t"],
			ObjectType:    c["object_type"],
			IsCompatible:  c["is_compatibility"],
			IsConvertible: c["is_convertible"],
		}
	}

	// 总体对象
	err = assessDbaObjects(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessDbaObjects failed. err:%s", err))
		return err
	}

	//字段数据类型
	err = assessTabColumns(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessTabColumns failed. err:%s", err))
		return err
	}

	// 表约束
	err = assessDbaConstraints(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessDbaConstraints failed. err:%s", err))
		return err
	}

	//索引
	err = assessDbaIndex(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessDbaIndex failed. err:%s", err))
		return err
	}

	//sequence
	err = assessDbaSequence(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessDbaSequence failed. err:%s", err))
		return err
	}

	//字段默认值
	err = assessTabColumnsDefault(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessTabColumnsDefault failed. err:%s", err))
		return err
	}

	//分区表
	err = assessPartTables(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessPartTables failed. err:%s", err))
		return err
	}

	//临时表
	err = assessDbaTables(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessDbaTables failed. err:%s", err))
		return err
	}

	//其它对象
	err = assessAllObjects(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessAllObjects failed. err:%s", err))
		return err
	}

	//LOB
	err = assessLobs(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessLobs failed. err:%s", err))
		return err
	}

	//表兼容性明细
	err = assessOraTableTypeCompatibles(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessOraTableTypeCompatibles failed. err:%s", err))
		return err
	}

	//视图明细
	err = assessOraDbaViews(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessOraDbaViews failed. err:%s", err))
		return err
	}

	//复合分区表明细
	err = assessOraSubPartitioning(ctx, timsDB, dbOra, channelId, taskId, schemaName, objAssessCompsMap)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:assessOraSubPartitioning failed. err:%s", err))
		return err
	}

	return nil
}

func assessDbaObjects(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessDbaObjects start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select owner,object_name,subobject_name,object_type,status from dba_objects where owner in (%s)`, schemaName)
	DbaObjectList := make([]*assessment.OraDbaObject, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var dbaObj dbaObject
		err = rows.Scan(
			&dbaObj.Owner,
			&dbaObj.ObjectName,
			&dbaObj.SubobjectName,
			&dbaObj.ObjectType,
			&dbaObj.Status,
		)
		if err != nil {
			return err
		}
		DbaObjectList = append(DbaObjectList, &assessment.OraDbaObject{
			ChannelId:     channelId,
			TaskId:        taskId,
			Owner:         dbaObj.Owner,
			ObjectName:    dbaObj.ObjectName,
			SubobjectName: dbaObj.SubobjectName,
			ObjectType:    dbaObj.ObjectType,
			Status:        dbaObj.Status,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DbaObjectList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaObject(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete CreateOraDbaObject failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaObject(ctx, DbaObjectList)
	if err != nil {
		log.Errorf("insert CreateOraDbaObject failed. err:%s", err)
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert CreateOraDbaObject failed. err:%s", err))
		return err
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaObject(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaConstraint failed. err:%s", err))
		}
	}
	return nil
}

func assessDbaConstraints(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessDbaConstraints start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select OWNER,TABLE_NAME,CONSTRAINT_NAME,CONSTRAINT_TYPE from DBA_CONSTRAINTS where owner in (%s) AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DbaConstraintList := make([]*assessment.OraDbaConstraint, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var dbaConstraint dbaConstraint
		err = rows.Scan(
			&dbaConstraint.Owner,
			&dbaConstraint.TableName,
			&dbaConstraint.ConstraintName,
			&dbaConstraint.ConstraintType,
		)
		if err != nil {
			return err
		}
		DbaConstraintList = append(DbaConstraintList, &assessment.OraDbaConstraint{
			ChannelId:      channelId,
			TaskId:         taskId,
			Owner:          dbaConstraint.Owner,
			TableName:      dbaConstraint.TableName,
			ConstraintName: dbaConstraint.ConstraintName,
			ConstraintType: dbaConstraint.ConstraintType,
			IsCompatible:   "N",
			IsConvertible:  "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DbaConstraintList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaConstraint(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaConstraint failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaConstraint(ctx, DbaConstraintList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaConstraint failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaConstraint(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaConstraint failed. err:%s", err))
		}
	}

	return nil
}

/*
*
从oracle系统表dba_indexes按schema查询索引信息，再根据规则数据更新兼容性、可改造
*/
func assessDbaIndex(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessDbaIndex start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select TABLE_OWNER,TABLE_NAME,INDEX_NAME,INDEX_TYPE,PARTITIONED from dba_indexes where owner in (%s) AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DbaIndexList := make([]*assessment.OraDbaIndex, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var dbaIndex dbaIndex
		err = rows.Scan(
			&dbaIndex.TableOwner,
			&dbaIndex.TableName,
			&dbaIndex.IndexName,
			&dbaIndex.IndexType,
			&dbaIndex.Partitioned,
		)
		if err != nil {
			return err
		}
		DbaIndexList = append(DbaIndexList, &assessment.OraDbaIndex{
			ChannelId:     channelId,
			TaskId:        taskId,
			TableOwner:    dbaIndex.TableOwner,
			TableName:     dbaIndex.TableName,
			IndexName:     dbaIndex.IndexName,
			IndexType:     dbaIndex.IndexType,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DbaIndexList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaIndex(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaIndex failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaIndex(ctx, DbaIndexList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaIndex failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaIndex(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraDbaIndex failed. err:%s", err))
		}
	}

	return nil
}

func assessDbaSequence(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessDbaSequence start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select SEQUENCE_OWNER,SEQUENCE_NAME  from DBA_SEQUENCES where SEQUENCE_OWNER in (%s) `, schemaName)
	DbaSequenceList := make([]*assessment.OraDbaSequence, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var dbaSeq structs.SequenceObj
		err = rows.Scan(
			&dbaSeq.SchemaName,
			&dbaSeq.SequenceName,
		)
		if err != nil {
			return err
		}
		DbaSequenceList = append(DbaSequenceList, &assessment.OraDbaSequence{
			ChannelId:     channelId,
			TaskId:        taskId,
			SequenceOwner: dbaSeq.SchemaName,
			SequenceName:  dbaSeq.SequenceName,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DbaSequenceList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaSequence(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaIndex failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaSequence(ctx, DbaSequenceList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaIndex failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaSequence(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraDbaIndex failed. err:%s", err))
		}
	}

	return nil
}

func assessTabColumns(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessTabColumns start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select owner,TABLE_NAME,COLUMN_NAME,CASE 
        WHEN regexp_like(DATA_TYPE, 'TIMESTAMP.*') THEN 'TIMESTAMP'
        ELSE DATA_TYPE
    END AS DATA_TYPE,COLUMN_ID,
        CASE 
        WHEN regexp_like(DATA_TYPE, 'TIMESTAMP.*') THEN DATA_SCALE
        ELSE DATA_LENGTH
    END AS DATA_LENGTH,DATA_PRECISION||'.'||DATA_SCALE as datadesc from dba_tab_columns where owner in (%s) and COLUMN_ID is not null AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DataList := make([]*assessment.OraDbaTabColumns, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data DbaTabColumns
		err = rows.Scan(
			&data.Owner,
			&data.TableName,
			&data.ColumnName,
			&data.DataType,
			&data.ColumnId,
			&data.DataLength,
			&data.DataDesc,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraDbaTabColumns{
			ChannelId:    channelId,
			TaskId:       taskId,
			Owner:        data.Owner,
			TableName:    data.TableName,
			ColumnName:   data.ColumnName,
			DataType:     data.DataType,
			ColumnId:     data.ColumnId,
			DataLength:   data.DataLength,
			DataDesc:     data.DataDesc,
			IsEquivalent: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaTabColumns(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaTabColumns failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaTabColumns(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaTabColumns failed. err:%s", err))
	}

	tabColMap := getTabColMap(ctx, timsDB, channelId, taskId)
	// 更新兼容性配置
	for _, v := range *tabColMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaTabColumns(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ColTypeNameS, v.ColTypeNameT, v.IsEquivalent)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraDbaTabColumns failed. err:%s", err))
		}
	}

	return nil
}

func assessTabColumnsDefault(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessTabColumnsDefault start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select owner,TABLE_NAME,COLUMN_NAME,DATA_TYPE,DATA_DEFAULT from dba_tab_columns where owner in (%s) AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DataList := make([]*assessment.OraDbaTabColumnsDefault, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data DbaTabColumnsDefault
		err = rows.Scan(
			&data.Owner,
			&data.TableName,
			&data.ColumnName,
			&data.DataType,
			&data.DataDefault,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraDbaTabColumnsDefault{
			ChannelId:    channelId,
			TaskId:       taskId,
			Owner:        data.Owner,
			TableName:    data.TableName,
			ColumnName:   data.ColumnName,
			DataType:     data.DataType,
			DataDefault:  data.DataDefault,
			IsEquivalent: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaTabColumnsDefault(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaTabColumnsDefault failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaTabColumnsDefault(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaTabColumnsDefault failed. err:%s", err))
	}
	colDefaultMap := getColDefaultMap(ctx, timsDB, channelId, taskId)
	// 更新兼容性配置
	for _, v := range *colDefaultMap {
		var colTypeDefaultValueS = v.ColTypeDefaultValueS
		var colTypeDefaultValueT = v.ColTypeDefaultValueT
		if colTypeDefaultValueS == "NULL" {
			colTypeDefaultValueS = ""
		}
		if colTypeDefaultValueT == "NULL" {
			colTypeDefaultValueT = ""
		}
		err = models.GetAssessReaderWriter().UpdateOraDbaTabColumnsDefault(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), colTypeDefaultValueS, colTypeDefaultValueT, v.IsEquivalent)

		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraDbaTabColumnsDefault failed. err:%s", err))
		}
	}

	return nil
}

func assessPartTables(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessPartTables start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select OWNER,TABLE_NAME,PARTITIONING_TYPE,SUBPARTITIONING_TYPE,PARTITION_COUNT from ALL_PART_TABLES where owner in (%s) AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DataList := make([]*assessment.OraAllPartTables, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data AllPartTables
		err = rows.Scan(
			&data.Owner,
			&data.TableName,
			&data.PartitioningType,
			&data.SubpartitioningType,
			&data.PartitionCount,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraAllPartTables{
			ChannelId:           channelId,
			TaskId:              taskId,
			Owner:               data.Owner,
			TableName:           data.TableName,
			PartitioningType:    data.PartitioningType,
			SubpartitioningType: data.SubpartitioningType,
			PartitionCount:      data.PartitionCount,
			IsCompatible:        "N",
			IsConvertible:       "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraAllPartTables(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraAllPartTables failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraAllPartTables(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraAllPartTables failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraAllPartTables(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraAllPartTables failed. err:%s", err))
		}
	}

	return nil
}

func assessDbaTables(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessDbaTables start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select OWNER,TABLE_NAME,t.temporary,t.duration from dba_tables t where temporary='Y' and owner in (%s) AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DataList := make([]*assessment.OraDbaTables, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data DbaTables
		err = rows.Scan(
			&data.Owner,
			&data.TableName,
			&data.Temporary,
			&data.Duration,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraDbaTables{
			ChannelId:     channelId,
			TaskId:        taskId,
			Owner:         data.Owner,
			TableName:     data.TableName,
			Temporary:     data.Temporary,
			Duration:      data.Duration,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraDbaTables(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaTables failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaTables(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaTables failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaTables(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraDbaTables failed. err:%s", err))
		}
	}

	return nil
}

/*其它对象明细，不含LOB*/
func assessAllObjects(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessAllObjects start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select owner,object_name,object_type from all_objects t where object_type not in ('TABLE','TABLE PARTITION','INDEX PARTITION','TABLE SUBPARTITION','INDEX','VIEW','LOB') and owner in (%s)`, schemaName)
	DataList := make([]*assessment.OraAllObjects, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data AllObjects
		err = rows.Scan(
			&data.Owner,
			&data.ObjectName,
			&data.ObjectType,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraAllObjects{
			ChannelId:     channelId,
			TaskId:        taskId,
			Owner:         data.Owner,
			ObjectName:    data.ObjectName,
			ObjectType:    data.ObjectType,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))

	err = models.GetAssessReaderWriter().DeleteOraAllObjects(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraAllObjects failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraAllObjects(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraAllObjects failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraAllObjects(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraAllObjects failed. err:%s", err))
		}
	}

	return nil
}

/*LOB明细*/
func assessLobs(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessLob start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`select p.*
	from 
	(
	select m.owner,m.object_name,m.object_type,m.table_name,m.COLUMN_NAME,n.data_type
	from 
	(select t.owner,t.object_name,t.object_type,p.table_name,p.COLUMN_NAME
	from
		(select owner,object_name,object_type 
		from DBA_OBJECTS 
		where object_type in ('LOB') 
		and owner in (%s)
		) t left join dba_lobs p 
	on t.object_name=p.segment_name) m left join dba_tab_columns n
	on m.owner=n.owner and m.table_name=n.table_name and m.column_name=n.column_name
	) p where p.table_name is not null AND p.table_name NOT LIKE 'BIN$%%'`, schemaName)
	DataList := make([]*assessment.OraLobs, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data OraLobs
		err = rows.Scan(
			&data.Owner,
			&data.ObjectName,
			&data.ObjectType,
			&data.TableName,
			&data.ColumnName,
			&data.DataType,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraLobs{
			ChannelId:     channelId,
			TaskId:        taskId,
			Owner:         data.Owner,
			ObjectName:    data.ObjectName,
			ObjectType:    data.ObjectType,
			TableName:     data.TableName,
			ColumnName:    data.ColumnName,
			DataType:      data.DataType,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d lob objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))
	if len(DataList) == 0 {
		return nil
	}
	err = models.GetAssessReaderWriter().DeleteOraLobs(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraLobs failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraLobs(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraLobs failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraLobs(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraLobs failed. err:%s", err))
		}
	}

	return nil
}

/*表兼容性明细*/
func assessOraTableTypeCompatibles(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessOraTableTypeCompatibles start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`SELECT 
	temp.OWNER,
	temp.TABLE_TYPE,
	temp.TABLE_NAME
	FROM
	(SELECT 
	f.OWNER AS OWNER,
		(
		CASE WHEN f.CLUSTER_NAME IS NOT NULL THEN 'CLUSTERED' ELSE
			CASE	WHEN f.IOT_TYPE = 'IOT' THEN
				CASE WHEN t.IOT_TYPE != 'IOT' THEN t.IOT_TYPE ELSE 'IOT' 
					END 
			ELSE
					CASE	
							WHEN f.PARTITIONED = 'YES' THEN 'PARTITIONED' ELSE
						CASE
								WHEN f.TEMPORARY = 'Y' THEN 
								DECODE(f.DURATION,'SYS$SESSION','SESSION TEMPORARY','SYS$TRANSACTION','TRANSACTION TEMPORARY')
								ELSE 'HEAP' 
						END 
					END 
			END 
		END ) TABLE_TYPE,
		ROUND(NVL(f.NUM_ROWS * f.AVG_ROW_LEN / 1024 / 1024 / 1024,0),2) AS OBJECT_SIZE,f.TABLE_NAME
	FROM
	(SELECT tmp.owner,tmp.NUM_ROWS,tmp.AVG_ROW_LEN,tmp.TABLE_NAME,tmp.CLUSTER_NAME,tmp.PARTITIONED,tmp.TEMPORARY,tmp.DURATION,tmp.IOT_TYPE
	FROM DBA_TABLES tmp, DBA_TABLES w
	WHERE tmp.owner=w.owner AND tmp.table_name = w.table_name AND tmp.owner in (%s) AND (w.IOT_TYPE IS NUll OR w.IOT_TYPE='IOT')) f left join (
	select owner,iot_name,iot_type from DBA_TABLES WHERE owner in (%s)) t 
	ON f.owner = t.owner AND f.table_name = t.iot_name) temp`, schemaName, schemaName)
	DataList := make([]*assessment.OraTableTypeCompatibles, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data OraTableTypeCompatibles
		err = rows.Scan(
			&data.Owner,
			&data.TableType,
			&data.TableName,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraTableTypeCompatibles{
			ChannelId:     channelId,
			TaskId:        taskId,
			Owner:         data.Owner,
			TableName:     data.TableName,
			TableType:     data.TableType,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d OraTableTypeCompatibles objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))
	if len(DataList) == 0 {
		return nil
	}
	err = models.GetAssessReaderWriter().DeleteOraTableTypeCompatibles(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraTableTypeCompatibles failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraTableTypeCompatibles(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraTableTypeCompatibles failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraTableTypeCompatibles(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraTableTypeCompatibles failed. err:%s", err))
		}
	}

	return nil
}

/*view明细*/
func assessOraDbaViews(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessOraDbaViews start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`SELECT OWNER,NVL(VIEW_TYPE,'VIEW') VIEW_TYPE,VIEW_TYPE_OWNER ,VIEW_NAME 
	FROM DBA_VIEWS WHERE OWNER IN (%s)`, schemaName)
	DataList := make([]*assessment.OraDbaViews, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data OraDbaViews
		err = rows.Scan(
			&data.Owner,
			&data.ViewType,
			&data.ViewTypeOwner,
			&data.ViewName,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraDbaViews{
			ChannelId:     channelId,
			TaskId:        taskId,
			Owner:         data.Owner,
			ViewType:      data.ViewType,
			ViewTypeOwner: data.ViewTypeOwner,
			ViewName:      data.ViewName,
			IsCompatible:  "N",
			IsConvertible: "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d OraDbaViews objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))
	if len(DataList) == 0 {
		return nil
	}
	err = models.GetAssessReaderWriter().DeleteOraDbaViews(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraDbaViews failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraDbaViews(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraDbaViews failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraDbaViews(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraDbaViews failed. err:%s", err))
		}
	}

	return nil
}

/*复合分区表明细*/
func assessOraSubPartitioning(ctx context.Context, timsDB *sql.DB, oraDB *sql.DB, channelId int, taskId int, schemaName string, objMap map[string]ObjectCompatible) error {
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:assessOraSubPartitioning start, channelId:%d, schemaName:%s", channelId, schemaName))
	querySQL := fmt.Sprintf(`SELECT
	OWNER,
      TABLE_NAME,
	PARTITIONING_TYPE || '-' || SUBPARTITIONING_TYPE AS SUBPARTITIONING_TYPE
FROM
	DBA_PART_TABLES 
WHERE
	OWNER IN (%s)
AND SUBPARTITIONING_TYPE <> 'NONE'  AND table_name NOT LIKE 'BIN$%%'`, schemaName)
	DataList := make([]*assessment.OraSubPartitioning, 0)
	rows, err := oraDB.QueryContext(ctx, querySQL)
	if err != nil {
		return err
	}
	for rows.Next() {
		var data OraSubPartitioning
		err = rows.Scan(
			&data.Owner,
			&data.TableName,
			&data.SubPartitioningType,
		)
		if err != nil {
			return err
		}
		DataList = append(DataList, &assessment.OraSubPartitioning{
			ChannelId:           channelId,
			TaskId:              taskId,
			Owner:               data.Owner,
			TableName:           data.TableName,
			SubPartitioningType: data.SubPartitioningType,
			IsCompatible:        "N",
			IsConvertible:       "N",
		})
	}
	logtofileanddb(ctx, channelId, taskId, "info", fmt.Sprintf("t01-003:get %d OraSubPartitioning objects from oracle, channelId:%d, taskId:%d, schemaName:%s", len(DataList), channelId, taskId, schemaName))
	if len(DataList) == 0 {
		return nil
	}
	err = models.GetAssessReaderWriter().DeleteOraSubPartitioning(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1))
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:delete OraSubPartitioning failed. err:%s", err))
	}
	err = models.GetAssessReaderWriter().CreateOraSubPartitioning(ctx, DataList)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:insert OraSubPartitioning failed. err:%s", err))
	}
	// 更新兼容性配置
	for _, v := range objMap {
		err = models.GetAssessReaderWriter().UpdateOraSubPartitioning(ctx, channelId, taskId, strings.Replace(schemaName, "'", "", -1), v.ObjectType, v.IsCompatible, v.IsConvertible)
		if err != nil {
			logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:update OraSubPartitioning failed. err:%s", err))
		}
	}

	return nil
}

func buildOraDbaConstraintFromModel(model *assessment.OraDbaConstraint) *message.OraDbaConstraint {
	return &message.OraDbaConstraint{
		OraconstraintId: model.OraconstraintId,
		ChannelId:       model.ChannelId,
		TaskId:          model.TaskId,
		Owner:           model.Owner,
		TableName:       model.TableName,
		ConstraintName:  model.ConstraintName,
		ConstraintType:  model.ConstraintType,
		IsCompatible:    model.IsCompatible,
		IsConvertible:   model.IsConvertible,
	}
}

func buildOraDbaIndexFromModel(model *assessment.OraDbaIndex) *message.OraDbaIndex {
	return &message.OraDbaIndex{
		OraIndexId:    model.OraIndexId,
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		TableOwner:    model.TableOwner,
		TableName:     model.TableName,
		IndexName:     model.IndexName,
		IndexType:     model.IndexType,
		Partitioned:   model.Partitioned,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildDbaTabColumnsFromModel(model *assessment.OraDbaTabColumns) *message.OraDbaTabColumns {
	return &message.OraDbaTabColumns{
		ChannelId:    model.ChannelId,
		TaskId:       model.TaskId,
		Owner:        model.Owner,
		TableName:    model.TableName,
		ColumnName:   model.ColumnName,
		DataType:     model.DataType,
		DataTypeT:    model.DataTypeT,
		ColumnId:     model.ColumnId,
		DataLength:   model.DataLength,
		DataDesc:     model.DataDesc,
		IsEquivalent: model.IsEquivalent,
	}
}

func buildDbaTabColumnsDefaultFromModel(model *assessment.OraDbaTabColumnsDefault) *message.OraDbaTabColumnsDefault {
	return &message.OraDbaTabColumnsDefault{
		ChannelId:    model.ChannelId,
		TaskId:       model.TaskId,
		Owner:        model.Owner,
		TableName:    model.TableName,
		ColumnName:   model.ColumnName,
		DataType:     model.DataType,
		DataDefault:  model.DataDefault,
		DataDefaultT: model.DataDefaultT,
		IsEquivalent: model.IsEquivalent,
	}
}

func buildAllPartTablesFromModel(model *assessment.OraAllPartTables) *message.OraAllPartTables {
	return &message.OraAllPartTables{
		ChannelId:           model.ChannelId,
		TaskId:              model.TaskId,
		Owner:               model.Owner,
		TableName:           model.TableName,
		PartitioningType:    model.PartitioningType,
		SubpartitioningType: model.SubpartitioningType,
		PartitionCount:      model.PartitionCount,
		IsCompatible:        model.IsCompatible,
		IsConvertible:       model.IsConvertible,
	}
}

func buildDbaTablesFromModel(model *assessment.OraDbaTables) *message.OraDbaTables {
	return &message.OraDbaTables{
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		Owner:         model.Owner,
		TableName:     model.TableName,
		Temporary:     model.Temporary,
		Duration:      model.Duration,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildAllObjectsFromModel(model *assessment.OraAllObjects) *message.OraAllObjects {
	return &message.OraAllObjects{
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		Owner:         model.Owner,
		ObjectName:    model.ObjectName,
		ObjectType:    model.ObjectType,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildDbaObjectsFromModel(model *assessment.OraDbaObject) *message.OraDbaObjects {
	return &message.OraDbaObjects{
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		Owner:         model.Owner,
		ObjectName:    model.ObjectName,
		ObjectType:    model.ObjectType,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildOraLobsFromModel(model *assessment.OraLobs) *message.OraLobs {
	return &message.OraLobs{
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		Owner:         model.Owner,
		ObjectName:    model.ObjectName,
		ObjectType:    model.ObjectType,
		TableName:     model.TableName,
		ColumnName:    model.ColumnName,
		DataType:      model.DataType,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildOraTableTypeCompatiblesFromModel(model *assessment.OraTableTypeCompatibles) *message.OraTableTypeCompatibles {
	return &message.OraTableTypeCompatibles{
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		Owner:         model.Owner,
		TableName:     model.TableName,
		TableType:     model.TableType,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildOraDbaViewsFromModel(model *assessment.OraDbaViews) *message.OraDbaViews {
	return &message.OraDbaViews{
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		Owner:         model.Owner,
		ViewType:      model.ViewType,
		ViewTypeOwner: model.ViewTypeOwner,
		ViewName:      model.ViewName,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}

func buildOraSubPartitioningFromModel(model *assessment.OraSubPartitioning) *message.OraSubPartitioning {
	return &message.OraSubPartitioning{
		ChannelId:           model.ChannelId,
		TaskId:              model.TaskId,
		Owner:               model.Owner,
		TableName:           model.TableName,
		SubPartitioningType: model.SubPartitioningType,
		IsCompatible:        model.IsCompatible,
		IsConvertible:       model.IsConvertible,
	}
}

func getTabColMap(ctx context.Context, timsDB *sql.DB, channelId int, taskId int) *map[string]message.TabColMapRule {
	sql := fmt.Sprintf(`select t1.* from tabcol_map_rules t1 
	INNER JOIN tmplate_rules t2 ON t1.tabcolmap_rule_id = t2.map_rule_id
	INNER JOIN tmplate_informations t3 ON t3.tmplate_id = t2.tmplate_id
	INNER JOIN tasks t4 ON t3.tmplate_id = t4.tabcolmap_tmplate_id 
	WHERE t1.db_type_s='oracle' and t1.db_type_t='tidb' 
	AND t1.deleted_at is null
	AND t4.task_id=%d`, taskId)
	buildDatatypeMap := make(map[string]message.TabColMapRule)
	_, buildDatatypeRules, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	for _, d := range buildDatatypeRules {
		buildDatatypeMap[common.StringUPPER(d["col_type_name_s"])] = message.TabColMapRule{
			ColTypeNameS: d["col_type_name_s"],
			ColTypeNameT: d["col_type_name_t"],
			IsEquivalent: d["is_equivalent"],
		}
	}
	return &buildDatatypeMap
}

func getColDefaultMap(ctx context.Context, timsDB *sql.DB, channelId int, taskId int) *map[string]message.ColDefaultMapRule {
	sql := fmt.Sprintf(`	select t1.* from coldefault_map_rules t1 
	INNER JOIN tmplate_rules t2 ON t1.coldefaultmap_rule_id = t2.map_rule_id
	INNER JOIN tmplate_informations t3 ON t3.tmplate_id = t2.tmplate_id
	INNER JOIN tasks t4 ON t3.tmplate_id = t4.coldefaultmap_tmplate_id 
	WHERE t1.db_type_s='oracle' and t1.db_type_t='tidb' 
	AND t1.deleted_at is null
	AND t4.task_id=%d`, taskId)
	buildDatatypeMap := make(map[string]message.ColDefaultMapRule)
	_, buildDatatypeRules, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		logtofileanddb(ctx, channelId, taskId, "error", fmt.Sprintf("t01-003:err:%v", err))
	}
	for _, d := range buildDatatypeRules {
		buildDatatypeMap[common.StringUPPER(d["col_type_default_value_s"])] = message.ColDefaultMapRule{
			ColTypeDefaultValueS: d["col_type_default_value_s"],
			ColTypeDefaultValueT: d["col_type_default_value_t"],
			IsEquivalent:         d["is_equivalent"],
		}
	}
	return &buildDatatypeMap
}

// 表兼容性明细
func getListTableTypeCompatibles(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraTableTypeCompatibles {
	var listOraData []*assessment.OraTableTypeCompatibles
	listOraData, err := models.GetAssessReaderWriter().GetOraTableTypeCompatiblesList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraTableTypeCompatiblesList failed, err: %v", err)
	}
	var listData []*message.OraTableTypeCompatibles
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraData {
			if data.IsCompatible == "N" {
				listData = append(listData, buildOraTableTypeCompatiblesFromModel(data))
			}
		}
	} else {
		for _, data := range listOraData {
			listData = append(listData, buildOraTableTypeCompatiblesFromModel(data))
		}
	}
	return listData
}

// 视图明细
func getListOraDbaViews(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaViews {
	var listOraData []*assessment.OraDbaViews
	listOraData, err := models.GetAssessReaderWriter().GetOraDbaViewsList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaViewsList failed, err: %v", err)
	}
	var listData []*message.OraDbaViews
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraData {
			if data.IsCompatible == "N" {
				listData = append(listData, buildOraDbaViewsFromModel(data))
			}
		}
	} else {
		for _, data := range listOraData {
			listData = append(listData, buildOraDbaViewsFromModel(data))
		}
	}

	return listData
}

// 复合分区表明细
func getListOraSubPartitioning(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraSubPartitioning {
	var listOraData []*assessment.OraSubPartitioning
	listOraData, err := models.GetAssessReaderWriter().GetOraSubPartitioningList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraSubPartitioningList failed, err: %v", err)
	}
	var listData []*message.OraSubPartitioning
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraData {
			if data.IsCompatible == "N" {
				listData = append(listData, buildOraSubPartitioningFromModel(data))
			}
		}
	} else {
		for _, data := range listOraData {
			listData = append(listData, buildOraSubPartitioningFromModel(data))
		}
	}
	return listData
}

func getListOraDbaConstraint(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaConstraint {
	//约束明细
	var listOraDbaConstraintData []*assessment.OraDbaConstraint
	listOraDbaConstraintData, err := models.GetAssessReaderWriter().GetOraDbaConstraintList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaConstraintList failed, err: %v", err)
	}
	var listOraDbaConstraint []*message.OraDbaConstraint
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraDbaConstraintData {
			if data.IsCompatible == "N" {
				listOraDbaConstraint = append(listOraDbaConstraint, buildOraDbaConstraintFromModel(data))
			}
		}
	} else {
		for _, data := range listOraDbaConstraintData {
			listOraDbaConstraint = append(listOraDbaConstraint, buildOraDbaConstraintFromModel(data))
		}
	}

	return listOraDbaConstraint
}

func getListOraDbaIndex(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaIndex {
	//索引明细
	var listOraDbaIndexData []*assessment.OraDbaIndex
	listOraDbaIndexData, err := models.GetAssessReaderWriter().GetOraDbaIndexList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaIndexList failed, err: %v", err)
	}
	var listOraDbaIndex []*message.OraDbaIndex
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraDbaIndexData {
			if data.IsCompatible == "N" {
				listOraDbaIndex = append(listOraDbaIndex, buildOraDbaIndexFromModel(data))
			}
		}
	} else {
		for _, data := range listOraDbaIndexData {
			listOraDbaIndex = append(listOraDbaIndex, buildOraDbaIndexFromModel(data))
		}
	}

	return listOraDbaIndex
}

func getListOraDbaTabColumns(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaTabColumns {
	//字段数据类型明细
	var listOraDbaTabColumnsData []*assessment.OraDbaTabColumns
	listOraDbaTabColumnsData, err := models.GetAssessReaderWriter().GetOraDbaTabColumnsList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaTabColumnsList failed, err: %v", err)
	}
	var listOraDbaTabColumns []*message.OraDbaTabColumns
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraDbaTabColumnsData {
			if data.IsEquivalent == "N" {
				listOraDbaTabColumns = append(listOraDbaTabColumns, buildDbaTabColumnsFromModel(data))
			}
		}
	} else {
		for _, data := range listOraDbaTabColumnsData {
			listOraDbaTabColumns = append(listOraDbaTabColumns, buildDbaTabColumnsFromModel(data))
		}
	}

	return listOraDbaTabColumns
}

func getListDbaTabColumnsDefault(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaTabColumnsDefault {
	//字段默认值明细
	var listOraDbaTabColumnsDefaultData []*assessment.OraDbaTabColumnsDefault
	listOraDbaTabColumnsDefaultData, err := models.GetAssessReaderWriter().GetOraDbaTabColumnsDefaultList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaTabColumnsDefaultList failed, err: %v", err)
	}
	var listDbaTabColumnsDefault []*message.OraDbaTabColumnsDefault
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraDbaTabColumnsDefaultData {
			if data.IsEquivalent == "N" {
				if data.DataDefault == "" {
					data.DataDefault = "NULL"
				}
				if data.DataDefaultT == "" {
					data.DataDefaultT = "NULL"
				}
				listDbaTabColumnsDefault = append(listDbaTabColumnsDefault, buildDbaTabColumnsDefaultFromModel(data))
			}
		}
	} else {
		for _, data := range listOraDbaTabColumnsDefaultData {
			if data.DataDefault == "" {
				data.DataDefault = "NULL"
			}
			if data.DataDefaultT == "" {
				data.DataDefaultT = "NULL"
			}
			listDbaTabColumnsDefault = append(listDbaTabColumnsDefault, buildDbaTabColumnsDefaultFromModel(data))
		}
	}

	return listDbaTabColumnsDefault
}

func getListAllPartTables(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraAllPartTables {
	//分区表明细
	var listOraAllPartTablesData []*assessment.OraAllPartTables
	listOraAllPartTablesData, err := models.GetAssessReaderWriter().GetOraAllPartTablesList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraAllPartTablesList failed, err: %v", err)
	}
	var listAllPartTables []*message.OraAllPartTables
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraAllPartTablesData {
			if data.IsCompatible == "N" {
				listAllPartTables = append(listAllPartTables, buildAllPartTablesFromModel(data))
			}
		}
	} else {
		for _, data := range listOraAllPartTablesData {
			listAllPartTables = append(listAllPartTables, buildAllPartTablesFromModel(data))
		}
	}
	return listAllPartTables
}

func getListDbaTables(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaTables {
	//临时表明细
	var listOraDbaTablesData []*assessment.OraDbaTables
	listOraDbaTablesData, err := models.GetAssessReaderWriter().GetOraDbaTablesList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaTablesList failed, err: %v", err)
	}
	var listDbaTables []*message.OraDbaTables
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraDbaTablesData {
			if data.IsCompatible == "N" {
				listDbaTables = append(listDbaTables, buildDbaTablesFromModel(data))
			}
		}
	} else {
		for _, data := range listOraDbaTablesData {
			listDbaTables = append(listDbaTables, buildDbaTablesFromModel(data))
		}
	}

	return listDbaTables
}

func getListAllObjects(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraAllObjects {
	//其它对象明细
	var listOraAllObjectsData []*assessment.OraAllObjects
	listOraAllObjectsData, err := models.GetAssessReaderWriter().GetOraAllObjectsList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraAllObjectsList failed, err: %v", err)
	}
	var listAllObjects []*message.OraAllObjects
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraAllObjectsData {
			if data.IsCompatible == "N" {
				listAllObjects = append(listAllObjects, buildAllObjectsFromModel(data))
			}
		}
	} else {
		for _, data := range listOraAllObjectsData {
			listAllObjects = append(listAllObjects, buildAllObjectsFromModel(data))
		}
	}
	return listAllObjects
}

func getListOthersObjects(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaObjects {
	//其它对象明细
	var listOraAllObjectsData []*assessment.OraDbaObject
	var withoutTypes = []string{"TABLE", "TABLE PARTITION", "INDEX PARTITION", "TABLE SUBPARTITION", "SEQUENCE", "INDEX", "VIEW", "LOB", "JOB", "SYNONYM", "PACKAGE BODY", "TRIGGER", "PACKAGE", "PROCEDURE", "FUNCTION", "DATABASE LINK", "LOB SUBPARTITION", "TYPE", "INDEX SUBPARTITION", "LOB PARTITION"}
	listOraAllObjectsData, err := models.GetAssessReaderWriter().GetOraDbaObjectsListWithoutTypes(ctx, channelId, taskId, schemas, withoutTypes)
	if err != nil {
		log.Errorf("Query GetOraAllObjectsList failed, err: %v", err)
	}
	var listAllObjects []*message.OraDbaObjects
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraAllObjectsData {
			if data.IsCompatible == "N" {
				listAllObjects = append(listAllObjects, buildDbaObjectsFromModel(data))
			}
		}
	} else {
		for _, data := range listOraAllObjectsData {
			listAllObjects = append(listAllObjects, buildDbaObjectsFromModel(data))
		}
	}
	return listAllObjects
}

func getListAllObjectsByObjType(ctx context.Context, channelId int, taskId int, schemas []string, objType string, taskInfo *task.Task) []*message.OraDbaObjects {
	//其它对象明细
	var listOraAllObjectsData []*assessment.OraDbaObject
	listOraAllObjectsData, err := models.GetAssessReaderWriter().GetOraDbaObjectsListByObjType(ctx, channelId, taskId, schemas, objType)
	if err != nil {
		log.Errorf("Query GetOraAllObjectsList failed, err: %v", err)
	}
	var listAllObjects []*message.OraDbaObjects
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraAllObjectsData {
			if data.IsCompatible == "N" {
				listAllObjects = append(listAllObjects, buildDbaObjectsFromModel(data))
			}
		}
	} else {
		for _, data := range listOraAllObjectsData {
			listAllObjects = append(listAllObjects, buildDbaObjectsFromModel(data))
		}
	}
	return listAllObjects
}

func getListOraLobs(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraLobs {
	//LOB明细
	var listOraLobsData []*assessment.OraLobs
	listOraLobsData, err := models.GetAssessReaderWriter().GetOraLobsList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraLobsList failed, err: %v", err)
	}
	var listOraLobs []*message.OraLobs
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listOraLobsData {
			if data.IsCompatible == "N" {
				listOraLobs = append(listOraLobs, buildOraLobsFromModel(data))
			}
		}
	} else {
		for _, data := range listOraLobsData {
			listOraLobs = append(listOraLobs, buildOraLobsFromModel(data))
		}
	}
	return listOraLobs
}

func UpdateTaskProgress(ctx context.Context, taskId int, progress float64) {
	// 检查 progress 是否为 NaN 或 Infinity
	if math.IsNaN(progress) || math.IsInf(progress, 0) {
		log.Errorf(fmt.Sprintf("t01-001:task exec failed, update task id [%v] progress [%g] is NaN or Infinity, skipping update", taskId, progress))
		return
	}

	// 确保 progress 在有效范围内 (0.0 到 100.0)
	if progress < 0.0 {
		progress = 0.0
	} else if progress > 100.0 {
		progress = 100.0
	}

	_, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
		Progress: progress,
	})
	if err != nil {
		log.Errorf(fmt.Sprintf("t01-001:task exec failed, update task id [%v] progress [%g] failed: %v", taskId, progress, err))
	}
}
