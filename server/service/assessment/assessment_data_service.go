package assessment

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/mysql"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/assess/oracle/public"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/assessment"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

func insertAssessDetail(ctx context.Context, channel_id int, task_id int, db_name string, schema_name string, struct_name string, duration string, assess_type string, assess_name string, assess_total int, compatible int, incompatible int, convertible int, inconvertible int, detail string) error {
	var err error
	records, batchGetErr := models.GetAssessReaderWriter().BatchLimitObjectAssess(ctx, &assessment.ObjectAssessDetail{
		ChannelId:  channel_id,
		TaskId:     task_id,
		SchemaName: strings.Trim(schema_name, "'"),
		AssessType: assess_type,
		AssessName: assess_name,
	}, 1)
	if batchGetErr != nil {
		log.Errorf("BatchLimitObjectAssess failed. err:%s", batchGetErr)
	}

	objAssessDetail := &assessment.ObjectAssessDetail{
		ChannelId:     channel_id,
		TaskId:        task_id,
		SchemaName:    strings.Trim(schema_name, "'"),
		AssessType:    assess_type,
		AssessName:    assess_name,
		AssessTotal:   assess_total,
		Compatible:    compatible,
		Incompatible:  incompatible,
		Convertible:   convertible,
		Inconvertible: inconvertible,
		Detail:        detail,
		StructName:    struct_name,
		Duration:      duration,
		DbName:        db_name,
	}

	if len(records) == 0 {
		err = models.GetAssessReaderWriter().CreateObjectAssess(ctx, objAssessDetail)
	} else {
		objAssessDetail.ObjAssessDetailID = records[0].ObjAssessDetailID
		err = models.GetAssessReaderWriter().UpdateObjectAssess(ctx, objAssessDetail)
	}

	if err != nil {
		log.Errorf("insertAssessDetail failed. err:%s", err)
		return err
	}
	return nil
}

func getDefaultValueMap(ctx context.Context, timsDB *sql.DB, taskId int) (map[string]meta.BuildinGlobalDefaultval, error) {
	sql := fmt.Sprintf(`select t1.* from coldefault_map_rules t1 
	INNER JOIN tmplate_rules t2 ON t1.coldefaultmap_rule_id = t2.map_rule_id
	INNER JOIN tmplate_informations t3 ON t3.tmplate_id = t2.tmplate_id
	INNER JOIN tasks t4 ON t3.tmplate_id = t4.coldefaultmap_tmplate_id 
	WHERE t1.db_type_s='oracle' and t1.db_type_t='tidb' 
	AND t4.task_id=%d`, taskId)
	_, buildDatatypeRules, err := mysql.Query(ctx, timsDB, sql)

	buildDatatypeMap := make(map[string]meta.BuildinGlobalDefaultval)
	for _, d := range buildDatatypeRules {
		buildDatatypeMap[common.StringUPPER(d["col_type_default_value_s"])] = meta.BuildinGlobalDefaultval{
			DBTypeS:       d["db_type_s"],
			DBTypeT:       d["db_type_t"],
			DefaultValueS: d["col_type_default_value_s"],
			DefaultValueT: d["col_type_default_value_t"],
		}
		if strings.EqualFold(common.StringUPPER(d["col_type_default_value_s"]), "NULL") {
			buildDatatypeMap[""] = meta.BuildinGlobalDefaultval{
				DBTypeS:       d["db_type_s"],
				DBTypeT:       d["db_type_t"],
				DefaultValueS: "",
				DefaultValueT: d["col_type_default_value_t"],
			}
		}
	}
	return buildDatatypeMap, err
}

func GetListSchemaSequenceCompatibles(ctx context.Context, db *sql.DB, schemaNames []string, objAssessCompsMap map[string]meta.BuildinObjectCompatible) ([]*message.SchemaSequenceCompatibles, public.ReportSummary, error) {
	sequenceList, err := models.GetAssessReaderWriter().GetOraSequenceSummaryList(ctx, db, schemaNames)
	if err != nil {
		log.Errorf("GetOraSequenceList failed. err:%s", err)
		return nil, public.ReportSummary{}, err
	}

	var listData []*message.SchemaSequenceCompatibles
	assessComp := 0
	assessInComp := 0
	assessConvert := 0
	assessInConvert := 0

	for _, ow := range sequenceList {
		if val, ok := objAssessCompsMap[common.StringUPPER("SEQUENCE")]; ok {
			listData = append(listData, &message.SchemaSequenceCompatibles{
				Schema:        ow.SchemaName,
				ObjectCounts:  ow.Count,
				IsCompatible:  val.IsCompatible,
				IsConvertible: val.IsConvertible,
			})
			if strings.EqualFold(val.IsCompatible, common.AssessYesCompatible) {
				assessComp += 1
			}
			if strings.EqualFold(val.IsCompatible, common.AssessNoCompatible) {
				assessInComp += 1
			}
			if strings.EqualFold(val.IsConvertible, common.AssessYesConvertible) {
				assessConvert += 1
			}
			if strings.EqualFold(val.IsConvertible, common.AssessNoConvertible) {
				assessInConvert += 1
			}
		} else {
			listData = append(listData, &message.SchemaSequenceCompatibles{
				Schema:        ow.SchemaName,
				ObjectCounts:  fmt.Sprintf("%d", len(sequenceList)),
				IsCompatible:  common.AssessNoCompatible,
				IsConvertible: common.AssessNoConvertible,
			})
			assessInComp += 1
			assessInConvert += 1
		}
	}

	return listData, public.ReportSummary{
		AssessType:    common.AssessTypeObjectTypeCompatible,
		AssessName:    "SEQUENCE_COMPATIBLE",
		AssessTotal:   len(listData),
		Compatible:    assessComp,
		Incompatible:  assessInComp,
		Convertible:   assessConvert,
		InConvertible: assessInConvert,
	}, nil
}

func GetOraSequenceList(ctx context.Context, db *sql.DB, schemaNames []string) ([]*structs.SequenceObj, error) {
	sequenceList, err := models.GetAssessReaderWriter().GetOraSequenceList(ctx, db, schemaNames)
	if err != nil {
		log.Errorf("GetOraSequenceList failed. err:%s", err)
		return nil, err
	}
	return sequenceList, nil
}

func getListOraDbaSequence(ctx context.Context, channelId int, taskId int, schemas []string, taskInfo *task.Task) []*message.OraDbaSequence {
	//索引明细
	listdata, err := models.GetAssessReaderWriter().GetOraDbaSequenceList(ctx, channelId, taskId, schemas)
	if err != nil {
		log.Errorf("Query GetOraDbaSequenceList failed, err: %v", err)
	}
	var listOraDbaIndex []*message.OraDbaSequence
	if taskInfo.OnlyIncompatibleDetail == 1 {
		for _, data := range listdata {
			if data.IsCompatible == "N" {
				listOraDbaIndex = append(listOraDbaIndex, buildOraDbaSequenceFromModel(data))
			}
		}
	} else {
		for _, data := range listdata {
			listOraDbaIndex = append(listOraDbaIndex, buildOraDbaSequenceFromModel(data))
		}
	}

	return listOraDbaIndex
}

func buildOraDbaSequenceFromModel(model *assessment.OraDbaSequence) *message.OraDbaSequence {
	return &message.OraDbaSequence{
		OraIndexId:    model.OraIndexId,
		ChannelId:     model.ChannelId,
		TaskId:        model.TaskId,
		SequenceOwner: model.SequenceOwner,
		SequenceName:  model.SequenceName,
		IsCompatible:  model.IsCompatible,
		IsConvertible: model.IsConvertible,
	}
}
