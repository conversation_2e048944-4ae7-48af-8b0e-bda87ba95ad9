package assessment

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	tmscommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/assess/oracle/public"
)

type Oracle struct {
	Ctx      context.Context
	OracleDB *sql.DB
}

const AssessNameLobTypeCompatible = "LOB_TYPE_COMPATIBLE"

func GetOracleSchemaObjectTypeCounts(schemaName []string, ctx context.Context, oraDB *sql.DB) ([]map[string]string, error) {

	querySQL := fmt.Sprintf(`SELECT OWNER,OBJECT_TYPE,COUNT(1) COUNTS FROM DBA_OBJECTS WHERE OWNER IN (%s) AND OBJECT_TYPE NOT IN ('TABLE','TABLE PARTITION','INDEX PARTITION','TABLE SUBPARTITION','INDEX','VIEW','LOB','JOB','SYNONYM','PACKAGE BODY','TRIGGER','PACKAGE','PROCEDURE','FUNCTION','DATABASE LINK','LOB SUBPARTITION','SEQUENCE','TYPE','INDEX SUBPARTITION','LOB PARTITION') GROUP BY OWNER,OBJECT_TYPE`, strings.Join(schemaName, ","))

	_, res, err := oracle.Query(ctx, oraDB, querySQL)
	if err != nil {
		return res, err
	}
	return res, nil
}

func GetOracleSchemaObjectTypeCountsByObjectType(schemaName []string, objType string, ctx context.Context, oraDB *sql.DB) ([]map[string]string, error) {

	querySQL := fmt.Sprintf(`SELECT OWNER,OBJECT_TYPE,COUNT(1) COUNTS FROM DBA_OBJECTS WHERE OWNER IN (%s) AND OBJECT_TYPE IN ('%s') GROUP BY OWNER,OBJECT_TYPE`, strings.Join(schemaName, ","), objType)

	_, res, err := oracle.Query(ctx, oraDB, querySQL)
	if err != nil {
		return res, err
	}
	return res, nil
}

func AssessOracleSchemaObjectTypeCompatible(schemaName []string, ctx context.Context, oracleDB *oracle.Oracle, objAssessCompsMap map[string]meta.BuildinObjectCompatible) ([]public.SchemaObjectTypeCompatibles, public.ReportSummary, error) {

	codeInfo, err := GetOracleSchemaObjectTypeCounts(schemaName, ctx, oracleDB.OracleDB)

	if err != nil {
		return nil, public.ReportSummary{}, err
	}

	if len(codeInfo) == 0 {
		return nil, public.ReportSummary{}, nil
	}

	var listData []public.SchemaObjectTypeCompatibles
	assessComp := 0
	assessInComp := 0
	assessConvert := 0
	assessInConvert := 0

	for _, ow := range codeInfo {
		if val, ok := objAssessCompsMap[common.StringUPPER(ow["OBJECT_TYPE"])]; ok {
			listData = append(listData, public.SchemaObjectTypeCompatibles{
				Schema:        ow["OWNER"],
				ObjectType:    ow["OBJECT_TYPE"],
				ObjectCounts:  ow["COUNTS"],
				IsCompatible:  val.IsCompatible,
				IsConvertible: val.IsConvertible,
			})
			if strings.EqualFold(val.IsCompatible, common.AssessYesCompatible) {
				assessComp += 1
			}
			if strings.EqualFold(val.IsCompatible, common.AssessNoCompatible) {
				assessInComp += 1
			}
			if strings.EqualFold(val.IsConvertible, common.AssessYesConvertible) {
				assessConvert += 1
			}
			if strings.EqualFold(val.IsConvertible, common.AssessNoConvertible) {
				assessInConvert += 1
			}
		} else {
			listData = append(listData, public.SchemaObjectTypeCompatibles{
				Schema:        ow["OWNER"],
				ObjectType:    ow["OBJECT_TYPE"],
				ObjectCounts:  ow["COUNTS"],
				IsCompatible:  common.AssessNoCompatible,
				IsConvertible: common.AssessYesConvertible,
			})
			assessInComp += 1
			assessConvert += 1
		}
	}

	return listData, public.ReportSummary{
		AssessType:    common.AssessTypeObjectTypeCompatible,
		AssessName:    common.AssessNameObjectTypeCompatible,
		AssessTotal:   len(listData),
		Compatible:    assessComp,
		Incompatible:  assessInComp,
		Convertible:   assessConvert,
		InConvertible: assessInConvert,
	}, nil
}

func AssessOracleSchemaObjectTypeCompatibleByObjectType(schemaName []string, objType string, ctx context.Context, oracleDB *oracle.Oracle, objAssessCompsMap map[string]meta.BuildinObjectCompatible) ([]public.SchemaObjectTypeCompatibles, public.ReportSummary, error) {

	codeInfo, err := GetOracleSchemaObjectTypeCountsByObjectType(schemaName, objType, ctx, oracleDB.OracleDB)

	if err != nil {
		return nil, public.ReportSummary{}, err
	}

	if len(codeInfo) == 0 {
		return nil, public.ReportSummary{}, nil
	}

	var listData []public.SchemaObjectTypeCompatibles
	assessComp := 0
	assessInComp := 0
	assessConvert := 0
	assessInConvert := 0

	assessTotal := 0
	for _, ow := range codeInfo {
		if val, ok := objAssessCompsMap[common.StringUPPER(ow["OBJECT_TYPE"])]; ok {
			listData = append(listData, public.SchemaObjectTypeCompatibles{
				Schema:        ow["OWNER"],
				ObjectType:    ow["OBJECT_TYPE"],
				ObjectCounts:  ow["COUNTS"],
				IsCompatible:  val.IsCompatible,
				IsConvertible: val.IsConvertible,
			})
			count := 0
			if v, ok := ow["COUNTS"]; ok {
				count, _ = strconv.Atoi(v)
			}
			assessTotal += count
			if strings.EqualFold(val.IsCompatible, common.AssessYesCompatible) {
				assessComp += count
			}
			if strings.EqualFold(val.IsCompatible, common.AssessNoCompatible) {
				assessInComp += count
			}
			if strings.EqualFold(val.IsConvertible, common.AssessYesConvertible) {
				assessConvert += count
			}
			if strings.EqualFold(val.IsConvertible, common.AssessNoConvertible) {
				assessInConvert += count
			}
		} else {
			listData = append(listData, public.SchemaObjectTypeCompatibles{
				Schema:        ow["OWNER"],
				ObjectType:    ow["OBJECT_TYPE"],
				ObjectCounts:  ow["COUNTS"],
				IsCompatible:  common.AssessNoCompatible,
				IsConvertible: common.AssessNoConvertible,
			})
			count := 0
			if v, ok := ow["COUNTS"]; ok {
				count, _ = strconv.Atoi(v)
			}
			assessTotal += count
			assessInComp += count
			assessConvert += count
		}
	}

	assessName := tmscommon.AssessNameObjectTypeCompatible
	if strings.EqualFold(objType, "JOB") {
		assessName = tmscommon.AssessNameJobTypeCompatible
	} else if strings.EqualFold(objType, "SYNONYM") {
		assessName = tmscommon.AssessNameSynonymTypeCompatible
	} else if strings.EqualFold(objType, "BODY") {
		assessName = tmscommon.AssessNameBodyCompatible
	} else if strings.EqualFold(objType, "PACKAGE BODY") {
		assessName = tmscommon.AssessNamePackageBodyCompatible
	} else if strings.EqualFold(objType, "PACKAGE") {
		assessName = tmscommon.AssessNamePackageCompatible
	} else if strings.EqualFold(objType, "TRIGGER") {
		assessName = tmscommon.AssessNameTriggerCompatible
	} else if strings.EqualFold(objType, "PROCEDURE") {
		assessName = tmscommon.AssessNameProcedureCompatible
	} else if strings.EqualFold(objType, "FUNCTION") {
		assessName = tmscommon.AssessNameFunctionCompatible
	} else if strings.EqualFold(objType, "DATABASE LINK") {
		assessName = tmscommon.AssessNameDatabaseLinkCompatible
	} else if strings.EqualFold(objType, "LOB PARTITION") {
		assessName = tmscommon.AssessNameLobPartitionCompatible
	} else if strings.EqualFold(objType, "LOB SUBPARTITION") {
		assessName = tmscommon.AssessNameLobSubpartitionCompatible
	} else if strings.EqualFold(objType, "SEQUENCE") {
		assessName = tmscommon.AssessNameSequenceCompatible
	} else if strings.EqualFold(objType, "TYPE") {
		assessName = tmscommon.AssessNameTypeCompatible
	} else if strings.EqualFold(objType, "INDEX SUBPARTITION") {
		assessName = tmscommon.AssessNameIndexSubpartitionCompatible
	}
	return listData, public.ReportSummary{
		AssessType:    common.AssessTypeObjectTypeCompatible,
		AssessName:    assessName,
		AssessTotal:   assessTotal,
		Compatible:    assessComp,
		Incompatible:  assessInComp,
		Convertible:   assessConvert,
		InConvertible: assessInConvert,
	}, nil
}

func GetOracleLobTypeCounts(schemaName []string, ctx context.Context, oraDB *sql.DB) ([]map[string]string, error) {

	querySQL := fmt.Sprintf(`select p.owner, p.object_type,count(1) COUNTS
	from 
	(
	select m.owner,m.object_name,m.object_type,m.table_name,m.COLUMN_NAME,n.data_type
	from 
	(select t.owner,t.object_name,t.object_type,p.table_name,p.COLUMN_NAME
	from
		(select owner,object_name,object_type 
		from DBA_OBJECTS 
		where object_type in ('LOB') 
		and owner in (%s)
		) t left join dba_lobs p 
	on t.object_name=p.segment_name) m left join dba_tab_columns n
	on m.owner=n.owner and m.table_name=n.table_name and m.column_name=n.column_name
	) p where p.table_name is not NULL
	GROUP BY p.owner ,p.object_type`, strings.Join(schemaName, ","))

	_, res, err := oracle.Query(ctx, oraDB, querySQL)
	if err != nil {
		return res, err
	}
	return res, nil
}

func AssessOracleLobTypeCompatible(schemaName []string, ctx context.Context, oracleDB *oracle.Oracle, objAssessCompsMap map[string]meta.BuildinObjectCompatible) ([]public.SchemaObjectTypeCompatibles, public.ReportSummary, error) {

	codeInfo, err := GetOracleLobTypeCounts(schemaName, ctx, oracleDB.OracleDB)

	if err != nil {
		return nil, public.ReportSummary{}, err
	}

	if len(codeInfo) == 0 {
		return nil, public.ReportSummary{}, nil
	}

	var listData []public.SchemaObjectTypeCompatibles
	assessComp := 0
	assessInComp := 0
	assessConvert := 0
	assessInConvert := 0

	for _, ow := range codeInfo {
		if val, ok := objAssessCompsMap[common.StringUPPER(ow["OBJECT_TYPE"])]; ok {
			listData = append(listData, public.SchemaObjectTypeCompatibles{
				Schema:        ow["OWNER"],
				ObjectType:    ow["OBJECT_TYPE"],
				ObjectCounts:  ow["COUNTS"],
				IsCompatible:  val.IsCompatible,
				IsConvertible: val.IsConvertible,
			})
			if strings.EqualFold(val.IsCompatible, common.AssessYesCompatible) {
				assessComp += 1
			}
			if strings.EqualFold(val.IsCompatible, common.AssessNoCompatible) {
				assessInComp += 1
			}
			if strings.EqualFold(val.IsConvertible, common.AssessYesConvertible) {
				assessConvert += 1
			}
			if strings.EqualFold(val.IsConvertible, common.AssessNoConvertible) {
				assessInConvert += 1
			}
		} else {
			listData = append(listData, public.SchemaObjectTypeCompatibles{
				Schema:        ow["OWNER"],
				ObjectType:    ow["OBJECT_TYPE"],
				ObjectCounts:  ow["COUNTS"],
				IsCompatible:  common.AssessNoCompatible,
				IsConvertible: common.AssessYesConvertible,
			})
			assessInComp += 1
			assessConvert += 1
		}
	}

	return listData, public.ReportSummary{
		AssessType:    common.AssessTypeObjectTypeCompatible,
		AssessName:    AssessNameLobTypeCompatible,
		AssessTotal:   len(listData),
		Compatible:    assessComp,
		Incompatible:  assessInComp,
		Convertible:   assessConvert,
		InConvertible: assessInConvert,
	}, nil
}

func GetOracleDBVersion(ctx context.Context, oracleDB *oracle.Oracle) (string, error) {
	querySQL := `SELECT BANNER FROM v$version`
	_, res, err := oracle.Query(ctx, oracleDB.OracleDB, querySQL)
	if err != nil {
		return "", err
	}
	return res[0]["BANNER"], nil
}
