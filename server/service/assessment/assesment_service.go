package assessment

import (
	"context"
	"fmt"
	"strings"
	"time"

	stringutil "gitee.com/pingcap_enterprise/tms/util/string"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type Service struct {
}

func NewAssessmentService() *Service {
	return &Service{}
}

// var assessmentLog = log.GetAssessmentLoggerEntry()

func ObjectAssessmentRun(ctx context.Context, timsConfig *config.Config, channelId int, taskId int) error {
	// run async
	log.Infof("ObjectAssessmentRun start, channelid:%d, taskid:%d", channelId, taskId)
	go doObjectAssessTask(ctx, timsConfig, channelId, taskId)
	return nil
}

func GetObjectAssessResultSummary(ctx context.Context, inputConfig config.Config, channelId int, taskId int) (*message.GetObjectAssessResultSummaryResp, error) {
	log.Infof("GetObjectAssessResultSummary start, channelid:%d, taskid:%d", channelId, taskId)
	returndata, err := doGetObjectAssessResultSummary(ctx, inputConfig, channelId, taskId)
	if err != nil {
		log.Errorf("O2TObjectAssessSummary run failed, err: %v", err)
		return &message.GetObjectAssessResultSummaryResp{}, err
	}
	return returndata, nil
}

func GetObjectAssessResultSummaryBySchema(ctx context.Context, inputConfig config.Config, channelId int, taskId int) (*message.GetObjectAssessResultSummaryBySchemaResp, error) {
	log.Infof("GetObjectAssessResultSummaryBySchema start, channelid:%d, taskid:%d", channelId, taskId)
	returndata, err := doGetObjectAssessResultSummaryBySchema(ctx, inputConfig, channelId, taskId)
	if err != nil {
		log.Errorf("O2TObjectAssessSummaryBySchema run failed, err: %v", err)
		return &message.GetObjectAssessResultSummaryBySchemaResp{}, err
	}
	return returndata, nil
}

func GetObjectAssessResultDetail(ctx context.Context, inputConfig config.Config, channelId int, taskId int, schema string) (*message.GetObjectAssessResultDetailResp, error) {
	log.Infof("GetObjectAssessResultDetail start, channelid:%d, taskid:%d, schema:%s", channelId, taskId, schema)
	returndata, err := doGetObjectAssessResultDetail(ctx, inputConfig, channelId, taskId, schema)
	if err != nil {
		log.Errorf("GetObjectAssessResultDetail run failed, err: %v", err)
		return &message.GetObjectAssessResultDetailResp{}, err
	}
	return returndata, nil
}

func GetAssessmentProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	AssessmentLogList, err := models.GetTaskReaderWriter().ListTaskLogDetailByTaskIdAndLogTime(ctx, req.TaskId, req.StartTime)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskLogDetailByTaskIdAndLogTime failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}

	lastUpdateTime := req.StartTime
	var progressLog []*message.TaskProgressLogDetail
	for _, v := range AssessmentLogList {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.LogTime,
			LogLevel:   v.LogLevel,
			LogMessage: v.LogMessage,
		})
		if v.UpdatedAt.After(lastUpdateTime) {
			lastUpdateTime = v.UpdatedAt
		}
	}

	AssessProgress := &message.GetTaskProgressResp{
		TaskId:         req.TaskId,
		StartTime:      req.StartTime,
		TotalNums:      1,
		SuccessNums:    1,
		FailedNums:     0,
		RunningNums:    0,
		LastUpdateTime: lastUpdateTime,
		TotalDuration:  lastUpdateTime.Sub(req.StartTime).String(),
		TaskLogFile:    "./data/logs/assessment.log",
		Progress:       stringutil.Decimal(taskInfo.Progress),
	}
	AssessProgress.ProgressLogInfo = &message.TaskProgressLogInfo{
		TaskId:        req.TaskId,
		LogStartTime:  req.StartTime,
		LogCount:      len(progressLog),
		LogDetailList: progressLog,
	}
	if taskInfo.TaskStatus == constants.TASK_STATUS_FAILED {
		AssessProgress.SuccessNums = 0
		AssessProgress.FailedNums = 1
		AssessProgress.RunningNums = 0
	} else if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		AssessProgress.SuccessNums = 0
		AssessProgress.FailedNums = 0
		AssessProgress.RunningNums = 1
	} else if taskInfo.TaskStatus == constants.TASK_STATUS_NOT_RUNNING {
		AssessProgress.SuccessNums = 0
		AssessProgress.FailedNums = 0
		AssessProgress.RunningNums = 0
	}

	return AssessProgress, nil
}

func logtofileanddb(ctx context.Context, channelId int, taskId int, loglevel string, logtxt string) error {
	// log2file
	if strings.ToLower(loglevel) == "info" {
		log.GetAssessmentLoggerEntry().Infof(logtxt)
	} else if strings.ToLower(loglevel) == "error" {
		log.GetAssessmentLoggerEntry().Errorf(logtxt)
	} else if strings.ToLower(loglevel) == "warning" {
		log.GetAssessmentLoggerEntry().Warnf(logtxt)
	} else if strings.ToLower(loglevel) == "debug" {
		log.GetAssessmentLoggerEntry().Debugf(logtxt)
	} else {
		log.GetAssessmentLoggerEntry().Infof(logtxt)
	}
	// log2db
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogLevel:   loglevel,
		LogTime:    time.Now(),
		LogMessage: logtxt,
		LogGroup:   "assessment",
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("CreateTaskLogDetail run failed, err: %v", err)
		return err
	}
	return nil
}

func DownloadObjectAssessResultReportBySchema(ctx context.Context, req *message.DownloadObjectAssessResultReportBySchemaReq, timsConfig *config.Config) (*message.DownloadObjectAssessResultReportBySchemaResp, error) {
	logtofileanddb(ctx, req.ChannelId, req.TaskId, "info", fmt.Sprintf("DownloadObjectAssessResultReportBySchema start.channelId:%d, taskId:%d, schemas:%v", req.ChannelId, req.TaskId, req.Schema))
	// 获取tims连接以及评估库Oracle信息
	dbT, err := models.OpenMysql(timsConfig.DBConfig.User, timsConfig.DBConfig.Password, timsConfig.DBConfig.Host, timsConfig.DBConfig.Port, timsConfig.DBConfig.Schema)
	if err != nil {
		logtofileanddb(ctx, req.ChannelId, req.TaskId, "error", fmt.Sprintf("do assess task failed, open tims db failed. err:%s", err))
		logtofileanddb(ctx, req.ChannelId, req.TaskId, "error", fmt.Sprintf("timsConfig:%v", timsConfig))
		return &message.DownloadObjectAssessResultReportBySchemaResp{}, err
	}
	defer dbT.Close()

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel failed, channelId: %d, taskId: %d, err: %v", req.ChannelId, req.TaskId, getChannelErr)
		return nil, getChannelErr
	}

	// create database conn
	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", getSourceErr)
	}

	dbS, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(sourceDS, ""))
	if err != nil {
		log.Errorf("do assess task failed, open tims db failed. err:%s", err)
		log.Errorf("timsConfig:%v", timsConfig)
		return nil, err
	}
	defer dbS.Close()

	// 生成检查结果文件
	//reportFilename := fmt.Sprintf("%s/assessment/assessment_channelid%d_taskid%d_%s.html", config.GetGlobalConfig().DataDir, req.ChannelId, req.TaskId, strings.Join(req.Schema, "_"))
	//logtofileanddb(ctx, req.ChannelId, req.TaskId, "info", fmt.Sprintf("start generate html, report name:%v", reportFilename))
	channelSchemas := fmt.Sprintf("'%s'", strings.Join(req.Schema, "','"))
	//currentTimeStr := time.Now().Format("20060102150405")
	dataDir := fmt.Sprintf("%s/assessment/assessment_channel%d_task%d/", timsConfig.DataDir, req.ChannelId, req.TaskId)
	//reportFilename := fmt.Sprintf("%sindex.html", dataDir)
	zipName := fmt.Sprintf("%s/assessment/assessment_channel%d_task%d.zip", timsConfig.DataDir, req.ChannelId, req.TaskId)
	title := "compatibility_report"
	logtofileanddb(ctx, req.ChannelId, req.TaskId, "info", fmt.Sprintf("t01-004:start generate html report,schema list:%s, report name:%v", channelSchemas, zipName))

	htmldata, err := GetAssessTableData(ctx, dbS, dbT, req.ChannelId, req.TaskId, channelSchemas, title)
	if err != nil {
		logtofileanddb(ctx, req.ChannelId, req.TaskId, "error", fmt.Sprintf("t01-004:GetAssessTableData err:%s", err))
	}

	err = commonpkg.GenerateReport(ctx, req.TaskId, commonpkg.OBJECT_ASS_REPORT, title, dataDir, zipName, &message.ObjectCompatibleAdapter{Data: htmldata})
	if err != nil {
		logtofileanddb(ctx, req.ChannelId, req.TaskId, "error", fmt.Sprintf("t01-004:generateAssessReport err: %v", err))
	}
	//err = generateAssessReport(ctx, dbT, req.ChannelId, req.TaskId, channelSchemas, reportFilename, req.Title, fmt.Sprintf("%s/assessment/assessment_channel%d_task%d/", timsConfig.DataDir, req.ChannelId, req.TaskId))
	//if err != nil {
	//	logtofileanddb(ctx, req.ChannelId, req.TaskId, "error", fmt.Sprintf("generateAssessReport err: %v", err))
	//}

	return &message.DownloadObjectAssessResultReportBySchemaResp{}, nil
}
