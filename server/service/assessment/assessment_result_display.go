package assessment

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

func DBQueryToMaps(ctx context.Context, db *sql.DB, querySQL string) ([]string, []map[string]string, error) {
	var (
		cols []string
		res  []map[string]string
	)
	rows, err := db.QueryContext(ctx, querySQL)
	if err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query failed: [%v]", querySQL, err.Error())
	}
	defer rows.Close()

	//不确定字段通用查询，自动获取字段名称
	cols, err = rows.Columns()
	if err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query rows.Columns failed: [%v]", querySQL, err.Error())
	}

	values := make([][]byte, len(cols))
	scans := make([]interface{}, len(cols))
	for i := range values {
		scans[i] = &values[i]
	}

	for rows.Next() {
		err = rows.Scan(scans...)
		if err != nil {
			return cols, res, fmt.Errorf("general sql [%v] query rows.Scan failed: [%v]", querySQL, err.Error())
		}

		row := make(map[string]string)
		for k, v := range values {
			// Oracle/Mysql 对于 'NULL' 统一字符 NULL 处理，查询出来转成 NULL,所以需要判断处理
			// 查询字段值 NULL
			// 如果字段值 = NULLABLE 则表示值是 NULL
			// 如果字段值 = "" 则表示值是空字符串
			// 如果字段值 = 'NULL' 则表示值是 NULL 字符串
			// 如果字段值 = 'null' 则表示值是 null 字符串
			if v == nil {
				row[cols[k]] = "NULLABLE"
			} else {
				// 处理空字符串以及其他值情况
				// 数据统一 string 格式显示
				row[cols[k]] = string(v)
			}
		}
		res = append(res, row)
	}

	if err = rows.Err(); err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query rows.Next failed: [%v]", querySQL, err.Error())
	}
	return cols, res, nil
}

func doGetObjectAssessResultSummary(ctx context.Context, timsConfig config.Config, channelId int, taskId int) (*message.GetObjectAssessResultSummaryResp, error) {
	// 获取tims连接以及评估库Oracle信息
	dbT, err := models.OpenMysql(timsConfig.DBConfig.User, timsConfig.DBConfig.Password, timsConfig.DBConfig.Host, timsConfig.DBConfig.Port, timsConfig.DBConfig.Schema)
	if err != nil {
		log.Errorf("do assess task failed, open tims db failed. err:%s", err)
		log.Errorf("timsConfig:%v", timsConfig)
		return &message.GetObjectAssessResultSummaryResp{}, err
	}
	defer dbT.Close()

	// 获取schema信息
	log.Infof("Get channel schemas, channelId:%d", channelId)
	var channelSchemas string
	sql := fmt.Sprintf(`select CONCAT('''',GROUP_CONCAT(schema_name_s  separator ''','''),'''') 
    from  channel_schemas cs  
    where channel_id =%d`, channelId)
	rows := dbT.QueryRow(sql)
	err = rows.Scan(&channelSchemas)
	if err != nil {
		log.Errorf("Get channel schemas failed, err: %v", err)
	}

	// 对象兼容性汇总(1-4)
	sql = fmt.Sprintf(`select sum(assess_total) assess_total,'兼容对象数' compatibletype,sum(compatible) compatiblecount,round(sum(compatible)/sum(assess_total)*100,2) compatiblepct 
	from object_assess_details oad  where channel_id=%d and task_id=%d and schema_name in (%s) and assess_type='OBJECT_TYPE_COMPATIBLE' and assess_total>0 
    union all 
    select sum(assess_total) assess_total,'不兼容对象数' compatibletype,sum(incompatible) compatiblecount,round(sum(incompatible)/sum(assess_total)*100,2) compatiblepct 
    from object_assess_details oad  where channel_id=%d and task_id=%d and schema_name in (%s) and assess_type='OBJECT_TYPE_COMPATIBLE' and assess_total>0 
    union all 
    select sum(assess_total) assess_total,'可转换对象数' compatibletype,sum(convertible) compatiblecount,round(sum(convertible)/sum(assess_total)*100,2) compatiblepct 
    from object_assess_details oad  where channel_id=%d and task_id=%d and schema_name in (%s) and assess_type='OBJECT_TYPE_COMPATIBLE' and assess_total>0 
    union all 
    select sum(assess_total) assess_total,'不可转换对象数' compatibletype,sum(inconvertible) compatiblecount,round(sum(inconvertible)/sum(assess_total)*100,2) compatiblepct 
    from object_assess_details oad  where channel_id=%d and task_id=%d and schema_name in (%s) and assess_type='OBJECT_TYPE_COMPATIBLE' and assess_total>0 
    `, channelId, taskId, channelSchemas, channelId, taskId, channelSchemas, channelId, taskId, channelSchemas, channelId, taskId, channelSchemas)

	_, data, err := DBQueryToMaps(ctx, dbT, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleOverview failed, err: %v", err)
	}
	var listGetObjectCompatibleCountTotal []*message.GetObjectCompatibleCount
	for _, ow := range data {
		compatiblecount_int, _ := strconv.Atoi(ow["compatiblecount"])
		listGetObjectCompatibleCountTotal = append(listGetObjectCompatibleCountTotal, &message.GetObjectCompatibleCount{
			CompatibleType:  ow["compatibletype"],
			CompatibleCount: compatiblecount_int,
			CompatiblePct:   ow["compatiblepct"] + "%",
		})
	}
	log.Debugf("listObjectCompatibleTypeOverview:%v", listGetObjectCompatibleCountTotal)

	sql = fmt.Sprintf(`select db_name,min(updated_at) updated_at,sum(assess_total) assess_total 
	from object_assess_details oad  
	where channel_id=%d 
	and task_id=%d 
	and schema_name in (%s)
	and assess_type='OBJECT_TYPE_COMPATIBLE' 
	and assess_total>0 
	group by db_name`, channelId, taskId, channelSchemas)

	_, data, err = DBQueryToMaps(ctx, dbT, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleOverview failed, err: %v", err)
		return &message.GetObjectAssessResultSummaryResp{}, err
	}
	var listGetObjectAssessResultSummaryResp message.GetObjectAssessResultSummaryResp
	for _, ow := range data {
		assessTotal_int, _ := strconv.Atoi(ow["assess_total"])
		listGetObjectAssessResultSummaryResp = message.GetObjectAssessResultSummaryResp{
			CheckTime:           ow["updated_at"],
			DBName:              ow["db_name"],
			AssessTotal:         assessTotal_int,
			ListCompatibleCount: listGetObjectCompatibleCountTotal,
		}
	}

	return &listGetObjectAssessResultSummaryResp, nil
}

func doGetObjectAssessResultSummaryBySchema(ctx context.Context, timsConfig config.Config, channelId int, taskId int) (*message.GetObjectAssessResultSummaryBySchemaResp, error) {
	// 获取tims连接以及评估库Oracle信息
	dbT, err := models.OpenMysql(timsConfig.DBConfig.User, timsConfig.DBConfig.Password, timsConfig.DBConfig.Host, timsConfig.DBConfig.Port, timsConfig.DBConfig.Schema)
	if err != nil {
		log.Errorf("do assess task failed, open tims db failed. err:%s", err)
		log.Errorf("timsConfig:%v", timsConfig)
		return &message.GetObjectAssessResultSummaryBySchemaResp{}, err
	}
	defer dbT.Close()

	// 获取schema信息
	log.Infof("Get channel schemas, channelId:%d", channelId)
	var channelSchemas string
	sql := fmt.Sprintf(`select CONCAT('''',GROUP_CONCAT(schema_name_s  separator ''','''),'''') 
		from  channel_schemas cs  
		where channel_id =%d`, channelId)
	rows := dbT.QueryRow(sql)
	err = rows.Scan(&channelSchemas)
	if err != nil {
		log.Errorf("Get channel schemas failed, err: %v", err)
	}

	sql = fmt.Sprintf(`select db_name,schema_name,
	min(updated_at) updated_at,
    sum(assess_total) assess_total,
    sum(compatible) compatible,
    sum(incompatible) incompatible, 
    sum(convertible) convertible, 
    sum(inconvertible) inconvertible
    from object_assess_details oad  
    where channel_id =%d 
    and task_id=%d 
    and schema_name in (%s) 
    and assess_type='OBJECT_TYPE_COMPATIBLE'
    and assess_total>0 
    group by db_name,schema_name`, channelId, taskId, channelSchemas)
	_, data, err := DBQueryToMaps(ctx, dbT, sql)
	if err != nil {
		log.Errorf("Query listGetObjectCompatibleCountBySchema failed, err: %v", err)
	}
	var listGetObjectCompatibleCountBySchema []*message.GetObjectCompatibleCountBySchema
	for _, ow := range data {
		listGetObjectCompatibleCountBySchema = append(listGetObjectCompatibleCountBySchema, &message.GetObjectCompatibleCountBySchema{
			CheckTime:     ow["updated_at"],
			DBName:        ow["db_name"],
			Schema:        ow["schema_name"],
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}

	log.Infof("listObjectCompatibleBySchema2:%v", listGetObjectCompatibleCountBySchema)

	ObjectAssessResultSummaryBySchema := message.GetObjectAssessResultSummaryBySchemaResp{
		ListGetObjectCompatibleCountBySchema: listGetObjectCompatibleCountBySchema,
	}

	log.Infof("ObjectAssessResultSummaryBySchema:%v", ObjectAssessResultSummaryBySchema)

	return &ObjectAssessResultSummaryBySchema, nil
}

func doGetObjectAssessResultDetail(ctx context.Context, timsConfig config.Config, channelId int, taskId int, schema string) (*message.GetObjectAssessResultDetailResp, error) {
	// 获取tims连接以及评估库Oracle信息
	dbT, err := models.OpenMysql(timsConfig.DBConfig.User, timsConfig.DBConfig.Password, timsConfig.DBConfig.Host, timsConfig.DBConfig.Port, timsConfig.DBConfig.Schema)
	if err != nil {
		log.Errorf("do assess task failed, open tims db failed. err:%s", err)
		log.Errorf("timsConfig:%v", timsConfig)
		return &message.GetObjectAssessResultDetailResp{}, err
	}
	defer dbT.Close()

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getChannelErr)
		return nil, getChannelErr
	}

	// create database conn
	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", getSourceErr)
	}

	dbS, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(sourceDS, ""))
	if err != nil {
		log.Errorf("do assess task failed, open tims db failed. err:%s", err)
		log.Errorf("timsConfig:%v", timsConfig)
		return &message.GetObjectAssessResultDetailResp{}, err
	}
	defer dbS.Close()

	log.Infof("start run GetAssessTableData schema:%v", "'"+schema+"'")
	htmldata, err := GetAssessTableData(ctx, dbS, dbT, channelId, taskId, "'"+schema+"'", "compatibility_report")
	if err != nil {
		log.Errorf("getAssessTableData err: %v", err)
		return &message.GetObjectAssessResultDetailResp{}, err
	}

	return &message.GetObjectAssessResultDetailResp{ObjectAssessResultDetail: htmldata}, nil
}
