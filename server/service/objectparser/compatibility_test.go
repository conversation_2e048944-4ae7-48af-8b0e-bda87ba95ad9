package objectparser

import (
	"reflect"
	"testing"
)

// TestServiceAPICompatibility verifies that the refactored Service maintains the exact same public API
func TestServiceAPICompatibility(t *testing.T) {
	// Test that we can create a Service instance
	service := &Service{}

	// Verify all expected methods exist
	serviceType := reflect.TypeOf(service)

	expectedMethods := []string{
		"CheckAdaptor",
		"ValidateServices",
		"ParsePLSQLToAST",
		"GetObjectDetail",
		"GetDefinitionsFromMetadata",
		"ListAnalyzeDetail",
		"GetDependencyFromMetadata",
		"GetArchiveData",
		"GetGetArchiveTimes",
		"GetTransactionDataBlocks",
		"GetTransactionLogVolume",
		"GetLogVolumePerSecond",
		"GetTransactionPerSecond",
		"ListBasicIncompatibleFeature",
		"ListTaskIncompatibleFeature",
		"UpdateTaskIncompatibleFeature",
		"UpdateBasicIncompatibleFeature",
		"GetIncompatibleFeatureScoring",
		"ListPrompt",
		"SavePrompt",
		"DeletePrompt",
		"ListTaskObjectPromptRelation",
		"SaveTaskObjectPromptRelation",
		"DeleteTaskObjectPromptRelation",
		"TestAIConnect",
		"GetPLSQLToJavaResults",
		"GetPLSQLToJavaHistoryResults",
		"GetPLSQLToJavaLogs",
		"GetPLSQLToJavaSummary",
		"DownloadJavaCodes",
		"DownloadHistoryJavaCodes",
		"ConvertPLSQLToJava",
		"ConvertPLSQLToJavaInManual",
	}

	t.Logf("Checking %d expected API methods", len(expectedMethods))

	for _, methodName := range expectedMethods {
		method, exists := serviceType.MethodByName(methodName)
		if !exists {
			t.Errorf("Method %s not found on Service", methodName)
			continue
		}

		// Check that method has reasonable signature
		methodType := method.Type
		// CheckAdaptor and ValidateServices don't take context, others do
		if methodName != "CheckAdaptor" && methodName != "ValidateServices" {
			if methodType.NumIn() < 3 {
				t.Errorf("Method %s should have at least 3 parameters (receiver, context, request)", methodName)
			}
		}
		if methodType.NumOut() < 1 {
			t.Errorf("Method %s should have at least 1 return value", methodName)
		}

		t.Logf("✓ Method %s exists with %d params and %d returns", methodName, methodType.NumIn(), methodType.NumOut())
	}

	t.Logf("API compatibility check completed")
}

// TestServiceInstantiation verifies that Service creation works correctly
func TestServiceInstantiation(t *testing.T) {
	// Test creating a service instance directly (without NewObjectParserService which needs config)
	service := &Service{}
	if service == nil {
		t.Error("Service creation returned nil")
	}

	// Test CheckAdaptor method with nil adaptor
	err := service.CheckAdaptor()
	if err == nil {
		t.Error("CheckAdaptor should return error when adaptor is nil")
	} else {
		t.Logf("✓ CheckAdaptor correctly returns error when adaptor is nil: %v", err)
	}

	// Test ValidateServices method
	err = service.ValidateServices()
	if err == nil {
		t.Error("ValidateServices should return error when services are nil")
	} else {
		t.Logf("✓ ValidateServices correctly returns error when services are nil: %v", err)
	}

	t.Log("Service instantiation test completed")
}

// TestServiceStructureFields verifies the Service struct has expected fields
func TestServiceStructureFields(t *testing.T) {
	service := &Service{}
	serviceType := reflect.TypeOf(service).Elem()

	expectedFields := []string{
		"adaptor",
		"parsingService",
		"metadataService",
		"conversionService",
		"archiveService",
		"promptService",
	}

	for _, fieldName := range expectedFields {
		field, exists := serviceType.FieldByName(fieldName)
		if !exists {
			t.Errorf("Field %s not found on Service struct", fieldName)
		} else {
			t.Logf("✓ Field %s exists with type %s", fieldName, field.Type)
		}
	}
}
