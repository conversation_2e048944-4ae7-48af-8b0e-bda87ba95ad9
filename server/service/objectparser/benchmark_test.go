package objectparser

import (
	"context"
	"testing"

	"gitee.com/pingcap_enterprise/tms/server/message"
)

// BenchmarkServiceCreation measures the overhead of service creation
func BenchmarkServiceCreation(b *testing.B) {
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		service := &Service{}
		if service == nil {
			b.Fatal("Service creation failed")
		}
	}
}

// BenchmarkMethodCallOverhead measures the overhead of service method delegation
func BenchmarkMethodCallOverhead(b *testing.B) {
	service := &Service{}
	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// Test CheckAdaptor which has minimal overhead (no service delegation)
		err := service.CheckAdaptor()
		if err == nil {
			b.<PERSON>al("CheckAdaptor should return error when adaptor is nil")
		}
	}
}

// BenchmarkValidateServices measures service validation performance
func BenchmarkValidateServices(b *testing.B) {
	service := &Service{}
	b.ResetTimer()
	b.<PERSON>Allocs()

	for i := 0; i < b.N; i++ {
		err := service.ValidateServices()
		if err == nil {
			b.Fatal("ValidateServices should return error when services are nil")
		}
	}
}

// BenchmarkServiceMemoryFootprint measures memory usage of the refactored service
func BenchmarkServiceMemoryFootprint(b *testing.B) {
	b.ReportAllocs()

	services := make([]*Service, b.N)

	for i := 0; i < b.N; i++ {
		services[i] = &Service{}
	}

	// Prevent compiler optimization
	if len(services) != b.N {
		b.Fatal("Memory allocation test failed")
	}
}

// BenchmarkMethodDelegationPattern compares delegation vs direct implementation
func BenchmarkMethodDelegationPattern(b *testing.B) {
	service := &Service{}
	ctx := context.Background()

	b.Run("DelegationOverhead", func(b *testing.B) {
		b.ReportAllocs()

		for i := 0; i < b.N; i++ {
			// This will panic due to nil service, but we measure the call overhead before panic
			func() {
				defer func() {
					recover() // Catch the expected panic from nil service
				}()
				service.ParsePLSQLToAST(ctx, &message.PlSQLToJSONReq{})
			}()
		}
	})

	b.Run("DirectMethodCall", func(b *testing.B) {
		b.ReportAllocs()

		for i := 0; i < b.N; i++ {
			// Direct method call for comparison (CheckAdaptor doesn't delegate to services)
			err := service.CheckAdaptor()
			_ = err // Use the result to prevent optimization
		}
	})
}

// BenchmarkServiceStructureSize measures the memory footprint of service structure
func BenchmarkServiceStructureSize(b *testing.B) {
	b.ReportAllocs()

	// Test creation of multiple service instances to measure structure overhead
	for i := 0; i < b.N; i++ {
		service := &Service{
			// Initialize with nil values to measure just the structure
			adaptor:           nil,
			parsingService:    nil,
			metadataService:   nil,
			conversionService: nil,
			archiveService:    nil,
			promptService:     nil,
		}

		// Verify structure integrity
		if service == nil {
			b.Fatal("Service creation failed")
		}
	}
}

// BenchmarkValidationMethods measures performance of validation methods
func BenchmarkValidationMethods(b *testing.B) {
	service := &Service{}

	b.Run("CheckAdaptor", func(b *testing.B) {
		b.ReportAllocs()
		for i := 0; i < b.N; i++ {
			err := service.CheckAdaptor()
			_ = err
		}
	})

	b.Run("ValidateServices", func(b *testing.B) {
		b.ReportAllocs()
		for i := 0; i < b.N; i++ {
			err := service.ValidateServices()
			_ = err
		}
	})
}
