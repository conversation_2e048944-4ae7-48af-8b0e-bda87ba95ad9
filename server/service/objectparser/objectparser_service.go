package objectparser

import (
	"context"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/adaptor"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/services"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/config"
)

// Service api interface for ObjectParser service
type Service struct {
	adaptor adaptor.Adaptor

	// Service components (service-based architecture)
	parsingService    services.ParsingService
	metadataService   services.MetadataService
	conversionService services.ConversionService
	archiveService    services.ArchiveService
	promptService     services.PromptService
}

// NewObjectParserService creates a new service with Service-based architecture
func NewObjectParserService() *Service {
	// Initializing ObjectParser Service with Service pattern

	globalConfig := config.GetGlobalConfig()
	if globalConfig == nil {
		// Global config not initialized, returning disabled service
		return &Service{}
	}

	objectParserConfig := globalConfig.ObjectParserConfig
	if objectParserConfig == nil || objectParserConfig.Enable == false {
		// ObjectParser service is not set or not enabled, returning disabled service
		return &Service{}
	}

	// ObjectParser service is enabled, setting up adaptor and services
	objectParserAdaptor, err := adaptor.NewObjectParserAdaptor(objectParserConfig)
	if err != nil {
		// Failed to create ObjectParser adaptor, return disabled service
		return &Service{}
	}

	// Create service factory with ReaderWriter interfaces directly
	// Creating Service factory with direct ReaderWriter dependency injection
	factory := services.NewServiceFactory(
		objectParserAdaptor,
		models.GetObjectParserWriter(),
		models.GetTaskReaderWriter(),
		models.GetDatasourceReaderWriter(),
		models.GetUserReaderWriter(),
	)

	// Initialize all services through factory
	// Initializing all services through factory pattern
	service := &Service{
		adaptor:           objectParserAdaptor,
		parsingService:    factory.CreateParsingService(),
		metadataService:   factory.CreateMetadataService(),
		conversionService: factory.CreateConversionService(),
		archiveService:    factory.CreateArchiveService(),
		promptService:     factory.CreatePromptService(),
	}

	// ObjectParser Service initialized successfully with 5 services
	return service
}

func (i *Service) CheckAdaptor() error {
	if i.adaptor == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "ObjectParser service is not enabled")
	}
	return nil
}

// ValidateServices ensures all services are properly initialized
func (i *Service) ValidateServices() error {
	if i.parsingService == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "ParsingService is not initialized")
	}
	if i.metadataService == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "MetadataService is not initialized")
	}
	if i.conversionService == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "ConversionService is not initialized")
	}
	if i.archiveService == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "ArchiveService is not initialized")
	}
	if i.promptService == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "PromptService is not initialized")
	}
	return nil
}

// ParsingService methods
func (i *Service) ParsePLSQLToAST(ctx context.Context, req *message.PlSQLToJSONReq) (*message.PlSQLToJSONResp, error) {
	return i.parsingService.ParsePLSQLToAST(ctx, req)
}

func (i *Service) GetDependencyFromMetadata(ctx context.Context, req *message.GetDependencyFromMetadataReq) (*message.GetDependencyFromMetadataResp, error) {
	return i.parsingService.GetDependencyFromMetadata(ctx, req)
}

// MetadataService methods
func (i *Service) GetObjectDetail(ctx context.Context, req *message.GetObjectDetailReq) (*message.GetObjectDetailResp, error) {
	return i.metadataService.GetObjectDetail(ctx, req)
}

func (i *Service) GetDefinitionsFromMetadata(ctx context.Context, req *message.GetDefinitionsFromMetadataReq) (*message.GetDefinitionsFromMetadataResp, error) {
	return i.metadataService.GetDefinitionsFromMetadata(ctx, req)
}

func (i *Service) ListAnalyzeDetail(ctx context.Context, req *message.ListAnalyzeDetailReq) (*message.ListAnalyzeDetailResp, *message.Page, error) {
	return i.metadataService.ListAnalyzeDetail(ctx, req)
}

// ArchiveService methods
func (i *Service) GetArchiveData(ctx context.Context, req *message.GetArchiveDataReq) (*message.GetArchiveDataResp, error) {
	return i.archiveService.GetArchiveData(ctx, req)
}

func (i *Service) GetGetArchiveTimes(ctx context.Context, req *message.GetArchiveTimesReq) (*message.GetArchiveTimesResp, error) {
	return i.archiveService.GetArchiveTimes(ctx, req)
}

func (i *Service) GetTransactionDataBlocks(ctx context.Context, req *message.GetTransactionDataBlocksReq) (*message.GetTransactionDataBlocksResp, error) {
	return i.archiveService.GetTransactionDataBlocks(ctx, req)
}

func (i *Service) GetTransactionLogVolume(ctx context.Context, req *message.GetTransactionLogVolumeReq) (*message.GetTransactionLogVolumeResp, error) {
	return i.archiveService.GetTransactionLogVolume(ctx, req)
}

func (i *Service) GetLogVolumePerSecond(ctx context.Context, req *message.GetLogVolumePerSecondReq) (*message.GetLogVolumePerSecondResp, error) {
	return i.archiveService.GetLogVolumePerSecond(ctx, req)
}

func (i *Service) GetTransactionPerSecond(ctx context.Context, req *message.GetTransactionPerSecondReq) (*message.GetTransactionPerSecondResp, error) {
	return i.archiveService.GetTransactionPerSecond(ctx, req)
}

// Compatibility methods (delegated to MetadataService)
func (i *Service) ListBasicIncompatibleFeature(ctx context.Context, req *message.ListBasicIncompatibleFeatureReq) (*message.ListBasicIncompatibleFeatureResp, error) {
	return i.metadataService.ListBasicIncompatibleFeature(ctx, req)
}

func (i *Service) ListTaskIncompatibleFeature(ctx context.Context, req *message.ListTaskIncompatibleFeatureReq) (*message.ListTaskIncompatibleFeatureResp, error) {
	return i.metadataService.ListTaskIncompatibleFeature(ctx, req)
}

func (i *Service) UpdateTaskIncompatibleFeature(ctx context.Context, req *message.UpdateTaskIncompatibleFeatureReq) (*message.UpdateTaskIncompatibleFeatureResp, error) {
	return i.metadataService.UpdateTaskIncompatibleFeature(ctx, req)
}

func (i *Service) UpdateBasicIncompatibleFeature(ctx context.Context, req *message.UpdateBasicIncompatibleFeatureReq) (*message.UpdateBasicIncompatibleFeatureResp, error) {
	return i.metadataService.UpdateBasicIncompatibleFeature(ctx, req)
}

func (i *Service) GetIncompatibleFeatureScoring(ctx context.Context, req *message.GetIncompatibleFeatureScoringReq) (*message.GetIncompatibleFeatureScoringResp, error) {
	return i.metadataService.GetIncompatibleFeatureScoring(ctx, req)
}

// PromptService methods
func (i *Service) ListPrompt(ctx context.Context, req *message.ListPromptReq) (*message.ListPromptResp, error) {
	return i.promptService.ListPrompt(ctx, req)
}

func (i *Service) SavePrompt(ctx context.Context, req *message.SavePromptReq) (*message.SavePromptResp, error) {
	return i.promptService.SavePrompt(ctx, req)
}

func (i *Service) DeletePrompt(ctx context.Context, req *message.DeletePromptReq) (*message.DeletePromptResp, error) {
	return i.promptService.DeletePrompt(ctx, req)
}

func (i *Service) ListTaskObjectPromptRelation(ctx context.Context, req *message.ListTaskObjectPromptRelationReq) (*message.ListTaskObjectPromptRelationResp, *message.Page, error) {
	return i.promptService.ListTaskObjectPromptRelation(ctx, req)
}

func (i *Service) SaveTaskObjectPromptRelation(ctx context.Context, req *message.SaveTaskObjectPromptRelationReq) (*message.SaveTaskObjectPromptRelationResp, error) {
	return i.promptService.SaveTaskObjectPromptRelation(ctx, req)
}

func (i *Service) DeleteTaskObjectPromptRelation(ctx context.Context, req *message.DeleteTaskObjectPromptRelationReq) (*message.DeleteTaskObjectPromptRelationResp, error) {
	return i.promptService.DeleteTaskObjectPromptRelation(ctx, req)
}

// ConversionService methods
func (i *Service) TestAIConnect(ctx context.Context, req *message.TestAIConnectReq) (*message.TestAIConnectResp, error) {
	return i.conversionService.TestAIConnect(ctx, req)
}

func (i *Service) GetPLSQLToJavaResults(ctx context.Context, req *message.GetPLSQLToJavaResultsReq) (*message.GetPLSQLToJavaResultsResp, *message.Page, error) {
	return i.conversionService.GetPLSQLToJavaResults(ctx, req)
}

func (i *Service) GetPLSQLToJavaHistoryResults(ctx context.Context, req *message.GetPLSQLToJavaHistoryResultsReq) (*message.GetPLSQLToJavaHistoryResultsResp, *message.Page, error) {
	return i.conversionService.GetPLSQLToJavaHistoryResults(ctx, req)
}

func (i *Service) GetPLSQLToJavaLogs(ctx context.Context, req *message.GetPLSQLToJavaLogsReq) (*message.GetPLSQLToJavaLogsResp, error) {
	return i.conversionService.GetPLSQLToJavaLogs(ctx, req)
}

func (i *Service) GetPLSQLToJavaSummary(ctx context.Context, req *message.GetPLSQLToJavaSummaryReq) (*message.GetPLSQLToJavaSummaryResp, error) {
	return i.conversionService.GetPLSQLToJavaSummary(ctx, req)
}

func (i *Service) DownloadJavaCodes(ctx context.Context, req *message.DownloadJavaCodesReq) (*message.DownloadJavaCodesResp, error) {
	return i.conversionService.DownloadJavaCodes(ctx, req)
}

func (i *Service) DownloadHistoryJavaCodes(ctx context.Context, req *message.DownloadHistoryJavaCodesReq) (*message.DownloadJavaCodesResp, error) {
	return i.conversionService.DownloadHistoryJavaCodes(ctx, req)
}

func (i *Service) ConvertPLSQLToJava(ctx context.Context, req *message.ConvertPLSQLToJavaReq) (*message.ConvertPLSQLToJavaResp, error) {
	return i.conversionService.ConvertPLSQLToJava(ctx, req)
}

func (i *Service) ConvertPLSQLToJavaInManual(ctx context.Context, req *message.ConvertPLSQLToJavaInManualReq) (*message.ConvertPLSQLToJavaInManualResp, error) {
	return i.conversionService.ConvertPLSQLToJavaInManual(ctx, req)
}
