package users

import (
	"errors"
	"testing"
)

func TestValidateRegisterInput(t *testing.T) {
	tests := []struct {
		name     string
		username string
		password string
		email    string
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid input",
			username: "testuser",
			password: "password123",
			email:    "<EMAIL>",
			wantErr:  false,
		},
		{
			name:     "empty username",
			username: "",
			password: "password123",
			email:    "<EMAIL>",
			wantErr:  true,
			errMsg:   "用户名不能为空",
		},
		{
			name:     "username too short",
			username: "ab",
			password: "password123",
			email:    "<EMAIL>",
			wantErr:  true,
			errMsg:   "用户名长度必须在3-50个字符之间",
		},
		{
			name:     "username too long",
			username: "a" + string(make([]byte, 50)),
			password: "password123",
			email:    "<EMAIL>",
			wantErr:  true,
			errMsg:   "用户名长度必须在3-50个字符之间",
		},
		{
			name:     "empty password",
			username: "testuser",
			password: "",
			email:    "<EMAIL>",
			wantErr:  true,
			errMsg:   "密码不能为空",
		},
		{
			name:     "password too short",
			username: "testuser",
			password: "12345",
			email:    "<EMAIL>",
			wantErr:  true,
			errMsg:   "密码长度不能少于6个字符",
		},
		{
			name:     "empty email",
			username: "testuser",
			password: "password123",
			email:    "",
			wantErr:  true,
			errMsg:   "邮箱不能为空",
		},
		{
			name:     "invalid email format - no @",
			username: "testuser",
			password: "password123",
			email:    "testexample.com",
			wantErr:  true,
			errMsg:   "邮箱格式不正确",
		},
		{
			name:     "invalid email format - no domain",
			username: "testuser",
			password: "password123",
			email:    "test@",
			wantErr:  true,
			errMsg:   "邮箱格式不正确",
		},
		{
			name:     "invalid email format - no local part",
			username: "testuser",
			password: "password123",
			email:    "@example.com",
			wantErr:  true,
			errMsg:   "邮箱格式不正确",
		},
		{
			name:     "invalid email format - no dot in domain",
			username: "testuser",
			password: "password123",
			email:    "test@example",
			wantErr:  true,
			errMsg:   "邮箱格式不正确",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRegisterInput(tt.username, tt.password, tt.email)
			if tt.wantErr {
				if err == nil {
					t.Errorf("validateRegisterInput() expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("validateRegisterInput() error = %v, want %v", err.Error(), tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("validateRegisterInput() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		name  string
		email string
		want  bool
	}{
		{"valid email", "<EMAIL>", true},
		{"valid email with subdomain", "<EMAIL>", true},
		{"valid email with numbers", "<EMAIL>", true},
		{"valid email with dots", "<EMAIL>", true},
		{"valid email with hyphens", "<EMAIL>", true},
		{"valid email with underscores", "<EMAIL>", true},
		{"too short", "a@b", false},
		{"too long", "a" + string(make([]byte, 254)) + "@example.com", false},
		{"no @ symbol", "testexample.com", false},
		{"empty local part", "@example.com", false},
		{"empty domain", "test@", false},
		{"no dot in domain", "test@example", false},
		{"multiple @ symbols", "test@example@com", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidEmail(tt.email); got != tt.want {
				t.Errorf("isValidEmail() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsDuplicateKeyError(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{"mysql duplicate entry", errors.New("Duplicate entry 'test' for key 'idx_users_username'"), true},
		{"sqlite unique constraint", errors.New("UNIQUE constraint failed: users.username"), true},
		{"postgres duplicate key", errors.New("duplicate key value violates unique constraint"), true},
		{"other error", errors.New("some other error"), false},
		{"nil error", nil, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isDuplicateKeyError(tt.err); got != tt.want {
				t.Errorf("isDuplicateKeyError() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsUsernameDuplicateError(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{"username index error", errors.New("Duplicate entry 'test' for key 'idx_users_username'"), true},
		{"username field error", errors.New("duplicate key value violates unique constraint on username"), true},
		{"email index error", errors.New("Duplicate entry '<EMAIL>' for key 'idx_users_email'"), false},
		{"other error", errors.New("some other error"), false},
		{"nil error", nil, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isUsernameDuplicateError(tt.err); got != tt.want {
				t.Errorf("isUsernameDuplicateError() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsEmailDuplicateError(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{"email index error", errors.New("Duplicate entry '<EMAIL>' for key 'idx_users_email'"), true},
		{"email field error", errors.New("duplicate key value violates unique constraint on email"), true},
		{"username index error", errors.New("Duplicate entry 'test' for key 'idx_users_username'"), false},
		{"other error", errors.New("some other error"), false},
		{"nil error", nil, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isEmailDuplicateError(tt.err); got != tt.want {
				t.Errorf("isEmailDuplicateError() = %v, want %v", got, tt.want)
			}
		})
	}
}
