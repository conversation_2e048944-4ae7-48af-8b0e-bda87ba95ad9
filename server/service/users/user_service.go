package users

import (
	"context"
	"errors"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/users"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type Service struct {
}

func NewUserService() *Service {
	return &Service{}
}

func (s *Service) Register(ctx context.Context, username, password, email string) error {
	log.Debugf("Register username: %s, password: %s, email: %s", username, password, email)

	// 验证输入参数
	if err := validateRegisterInput(username, password, email); err != nil {
		return err
	}

	// 检查用户名唯一性
	existingUser, err := models.GetUserReaderWriter().GetUserByUsername(ctx, username)
	if err == nil && existingUser != nil {
		return errors.New("用户名已存在")
	}

	// 检查邮箱唯一性
	existingUser, err = models.GetUserReaderWriter().GetUserByEmail(ctx, email)
	if err == nil && existingUser != nil {
		return errors.New("邮箱已被注册")
	}

	salt, err := users.GenerateSalt()
	if err != nil {
		return err
	}
	passwordHash := users.HashPassword(password, salt)
	user := &users.User{
		Username:     username,
		PasswordHash: passwordHash,
		Salt:         salt,
		Email:        email,
	}

	// 创建用户，如果数据库层面有唯一性约束冲突，会返回相应的错误
	err = models.GetUserReaderWriter().CreateUser(ctx, user)
	if err != nil {
		// 检查是否是唯一性约束错误
		if isDuplicateKeyError(err) {
			if isUsernameDuplicateError(err) {
				return errors.New("用户名已存在")
			}
			if isEmailDuplicateError(err) {
				return errors.New("邮箱已被注册")
			}
		}
		return err
	}

	return nil
}

// isDuplicateKeyError 检查是否是重复键错误
func isDuplicateKeyError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "Duplicate entry") ||
		strings.Contains(errStr, "UNIQUE constraint failed") ||
		strings.Contains(errStr, "duplicate key value")
}

// isUsernameDuplicateError 检查是否是用户名重复错误
func isUsernameDuplicateError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "idx_users_username") ||
		strings.Contains(errStr, "username")
}

// isEmailDuplicateError 检查是否是邮箱重复错误
func isEmailDuplicateError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "idx_users_email") ||
		strings.Contains(errStr, "email")
}

// validateRegisterInput 验证注册输入参数
func validateRegisterInput(username, password, email string) error {
	// 验证用户名
	if username == "" {
		return errors.New("用户名不能为空")
	}
	if len(username) < 3 || len(username) > 50 {
		return errors.New("用户名长度必须在3-50个字符之间")
	}

	// 验证密码
	if password == "" {
		return errors.New("密码不能为空")
	}
	if len(password) < 6 {
		return errors.New("密码长度不能少于6个字符")
	}

	// 验证邮箱格式
	if email == "" {
		return errors.New("邮箱不能为空")
	}
	if !isValidEmail(email) {
		return errors.New("邮箱格式不正确")
	}

	return nil
}

// isValidEmail 验证邮箱格式
func isValidEmail(email string) bool {
	// 简单的邮箱格式验证
	if len(email) < 5 || len(email) > 254 {
		return false
	}

	// 检查是否包含@符号
	if !strings.Contains(email, "@") {
		return false
	}

	// 检查@符号前后是否有内容
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}

	localPart := parts[0]
	domainPart := parts[1]

	// 检查本地部分和域名部分是否为空
	if localPart == "" || domainPart == "" {
		return false
	}

	// 检查域名是否包含点号
	if !strings.Contains(domainPart, ".") {
		return false
	}

	return true
}

func (s *Service) Login(ctx context.Context, username, password string) (*users.User, error) {
	log.Debugf("Login username: %s, password: %s", username, password)

	user, err := models.GetUserReaderWriter().GetUserByUsername(ctx, username)
	if err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	if user.PasswordHash != users.HashPassword(password, user.Salt) {
		return nil, errors.New("用户名或密码错误")
	}
	now := time.Now()
	user.LastLogin = &now
	models.GetUserReaderWriter().UpdateUser(ctx, user)
	return user, nil
}

// 修改密码
func (s *Service) ChangePassword(ctx context.Context, username, oldPassword, newPassword string) error {
	user, err := models.GetUserReaderWriter().GetUserByUsername(ctx, username)
	if err != nil {
		return errors.New("用户不存在")
	}
	if user.PasswordHash != users.HashPassword(oldPassword, user.Salt) {
		return errors.New("旧密码错误")
	}
	salt, err := users.GenerateSalt()
	if err != nil {
		return errors.New("生成新盐失败")
	}
	user.Salt = salt
	user.PasswordHash = users.HashPassword(newPassword, salt)
	return models.GetUserReaderWriter().UpdateUser(ctx, user)
}
