package increment

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"sort"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/dsg"
	incrementpkg "gitee.com/pingcap_enterprise/tms/pkg/increment"
	"gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	modelcommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/increment"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/samber/lo"
)

// Service api interface for increment(dsg) service
type Service struct {
	dsgAdaptor       dsg.Adaptor
	userId           int
	tokenRefreshTime time.Time
}

// NewIncrementService create a new increment(dsg) service
func NewIncrementService() *Service {
	incrementConfig := config.GetGlobalConfig().IncrementConfig
	if incrementConfig == nil || incrementConfig.Enable == false {
		log.Infof("increment(dsg) service is not set or not enabled, skipping setup")
		return &Service{}
	}

	dsgAdaptor, err := dsg.NewDsgAdaptor(incrementConfig)
	if err != nil {
		log.Fatalf("increment(dsg) config is enable,but NewDsgAdaptor failed, err: %v", err)
	}

	return &Service{
		dsgAdaptor:       dsgAdaptor,
		userId:           incrementConfig.DsgUserID,
		tokenRefreshTime: time.Now(),
	}
}

func (i *Service) CheckDSGAdaptor() error {
	if i.dsgAdaptor == nil {
		return errors.NewError(errors.TMS_DSG_ADAPTOR_NOT_EXIST, "increment service is not enabled")
	}
	return nil
}

func (i *Service) RefreshTokenIfNecessary() error {
	// if token refresh time is half day ago, refresh token
	nowTime := time.Now()
	if nowTime.Sub(i.tokenRefreshTime) < time.Hour*6 {
		log.Infof("RefreshTokenIfNecessary, token refresh time is not enough, skip refresh token, last refresh time: %v, now: %v", i.tokenRefreshTime, nowTime)
		return nil
	}
	i.tokenRefreshTime = time.Now()

	getTokenRsp, err := i.dsgAdaptor.GetToken(context.TODO(), dsg.GetTokenRequest{})
	if err != nil {
		return err
	}
	log.Infof("RefreshTokenIfNecessary getTokenRsp: %v", getTokenRsp)
	i.dsgAdaptor.SetToken(getTokenRsp.DataInfo)
	return nil
}

func (i *Service) StartDsgTask(ctx context.Context, req *message.StartDsgTaskReq) (*message.StartDsgTaskResp, error) {
	log.Infof("StartDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("StartDsgTask failed, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	// start task
	startApiReq := dsg.StartTaskRequest{TID: taskInfo.IncrementId}
	log.Infof("StartDsgTask, invoke StartTask, taskId:%d, dsgId:%d, req:%v", taskId, taskInfo.IncrementId, startApiReq)
	startApiResp, startErr := i.dsgAdaptor.StartTask(ctx, startApiReq)
	if startErr != nil {
		log.Errorf("StartDsgTask, invoke StartTask failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, startErr)
		return nil, startErr
	}
	log.Infof("StartDsgTask, invoke StartTask req:%v, rsp:%v", startApiReq, startApiResp)

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)
	dsgTaskTime, getDsgTaskTimeErr := helper.GetDsgTaskTime(ctx, dsgTaskName)
	if getDsgTaskTimeErr != nil {
		log.Errorf("StartDsgTask failed, taskId:%d, err: %v", req.TaskId, getDsgTaskTimeErr)
		return nil, getDsgTaskTimeErr
	}

	if !timeutil.IsTMSNullTime(dsgTaskTime.LastStartMigrateTime) {
		taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
		taskInfo.StartTime = dsgTaskTime.LastStartMigrateTime
		taskInfo.EndTime = timeutil.GetTMSNullTime()
		_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
		if updateTaskErr != nil {
			log.Errorf("StartDsgTask failed, update task info failed, taskId:%d, err: %v", req.TaskId, updateTaskErr)
			return nil, updateTaskErr
		}
	}

	return &message.StartDsgTaskResp{
		BaseDsgResp: buildBaseResp(startApiResp.BaseResponse),
	}, nil
}

func (i *Service) StartSyncDsgTask(ctx context.Context, req *message.StartSyncDsgTaskReq) (*message.StartSyncDsgTaskResp, error) {
	log.Infof("StartSyncDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("StartSyncDsgTask, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("StartSyncDsgTask, get channel info failed, taskId:%d, err: %v", taskId, getChannelErr)
		return nil, getChannelErr
	}

	sourceConns, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if setUpErr != nil {
		log.Errorf("StartSyncDsgTask, set up source database connection failed, taskId:%d, err: %v", taskId, setUpErr)
		return nil, setUpErr
	}

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	var scnValue string
	var timeValue string
	var parseErr error
	if req.SCNType == message.SCNType_TIME {
		scnValue, timeValue, parseErr = helper.ParseTimestampToSCN(ctx, req.SCNValue, sourceConns, taskInfo)
	} else {
		scnValue, timeValue, parseErr = helper.ParseSCNToTimestamp(ctx, req.SCNValue, sourceConns, taskInfo)
	}
	if parseErr != nil {
		log.Errorf("StartSyncDsgTask, parse scn or timestamp failed, taskId:%d, err: %v", taskId, parseErr)
		return nil, parseErr
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)
	incrementTaskInfo := increment.IncrementTask{
		TaskId:            taskId,
		DsgTaskName:       dsgTaskName,
		ScnType:           string(req.SCNType),
		ScnValue:          scnValue,
		TimeValue:         timeValue,
		ScnUserInputValue: req.SCNValue,
	}
	_, createITIErr := models.GetIncrementReaderWriter().CreateOrUpdateIncrementTask(ctx, &incrementTaskInfo)
	if createITIErr != nil {
		log.Errorf("StartSyncDsgTask, create increment task info failed, taskId:%d, err: %v", taskId, createITIErr)
		return nil, errors.NewError(errors.TMS_DSG_CREATE_SYNC_INFO_FAILED, createITIErr.Error())
	}

	log.Infof("StartSyncDsgTask, read source config for update default values, taskId:%d, dsgId:%d", req.TaskId, taskInfo.IncrementId)
	setVmValue := strings.ReplaceAll(dsg.TaskMethodParamIncrementOnlyWithSCN, "${SCN_VALUE}", scnValue)
	updateSourceConfigErr := helper.UpdateDsgSourceConfig(ctx, taskInfo.IncrementId, setVmValue)
	if updateSourceConfigErr != nil {
		log.Errorf("StartSyncDsgTask failed, update source config failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, updateSourceConfigErr)
		return nil, updateSourceConfigErr
	}

	apiReq := dsg.SyncRequest{
		TID: taskInfo.IncrementId,
	}
	log.Infof("StartSyncDsgTask, invoke Sync, taskId:%d, dsgId:%d, req:%v", taskId, taskInfo.IncrementId, apiReq)
	apiResp, err := i.dsgAdaptor.Sync(ctx, apiReq)
	if err != nil {
		log.Errorf("StartSyncDsgTask, invoke Sync failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, err)
		return nil, err
	}
	log.Infof("StartSyncDsgTask, invoke Sync success, taskId:%d, dsgId:%d, req:%v, rsp:%v", taskId, taskInfo.IncrementId, apiReq, apiResp)

	dsgTaskTime, getDsgTaskTimeErr := helper.GetDsgTaskTime(ctx, dsgTaskName)
	if getDsgTaskTimeErr != nil {
		log.Errorf("GetDsgTaskTime failed, taskId:%d, err: %v", req.TaskId, getDsgTaskTimeErr)
		return nil, getDsgTaskTimeErr
	}
	taskInfo.StartTime = dsgTaskTime.LastStartMigrateTime
	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	log.Infof("StartSyncDsgTask, update task info, taskId:%d, dsgId:%d, startTime:%v", taskId, taskInfo.IncrementId, taskInfo.StartTime)
	_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskErr != nil {
		log.Errorf("StartSyncDsgTask failed, update task info failed, taskId:%d, err: %v", req.TaskId, updateTaskErr)
		return nil, updateTaskErr
	}

	return &message.StartSyncDsgTaskResp{
		BaseDsgResp: buildBaseResp(apiResp.BaseResponse),
	}, nil
}

func (i *Service) PauseSyncDsgTask(ctx context.Context, req *message.PauseSyncDsgTaskReq) (*message.PauseSyncDsgTaskResp, error) {
	log.Infof("PauseSyncDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("PauseSyncDsgTask, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)
	dsgTaskTime, getDsgTaskTimeErr := helper.GetDsgTaskTime(ctx, dsgTaskName)
	if getDsgTaskTimeErr != nil {
		log.Errorf("GetDsgTaskTime failed, taskId:%d, err: %v", req.TaskId, getDsgTaskTimeErr)
		return nil, getDsgTaskTimeErr
	}

	log.Infof("PauseSyncDsgTask, invoke PauseSync, taskId:%d, dsgId:%d", taskId, taskInfo.IncrementId)
	stopErr := helper.PauseSync(ctx, taskInfo.IncrementId)
	if stopErr != nil {
		log.Errorf("PauseSyncDsgTask failed, stop sync failed, taskId:%d, err: %v", req.TaskId, stopErr)
		return nil, stopErr
	}

	taskInfo.EndTime = dsgTaskTime.LastStopMigrateTime
	taskInfo.TaskStatus = constants.TASK_STATUS_STOP
	if taskInfo.EndTime.IsZero() || taskInfo.EndTime.Year() == 1970 {
		taskInfo.EndTime = time.Now()
	}
	log.Infof("PauseSyncDsgTask, update task info, taskId:%d, dsgId:%d, endTime:%v", taskId, taskInfo.IncrementId, taskInfo.EndTime)
	_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskErr != nil {
		log.Errorf("PauseSyncDsgTask failed, update task info failed, taskId:%d, err: %v", req.TaskId, updateTaskErr)
		return nil, updateTaskErr
	}

	return &message.PauseSyncDsgTaskResp{}, nil
}

func (i *Service) ResumeSyncDsgTask(ctx context.Context, req *message.ResumeSyncDsgTaskReq) (*message.ResumeSyncDsgTaskResp, error) {
	log.Infof("ResumeSyncDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("ResumeSyncDsgTask, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("ResumeSyncDsgTask, get channel info failed, taskId:%d, err: %v", taskId, getChannelErr)
		return nil, getChannelErr
	}

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	var resumeErr error
	// scn not change, just resume binary process status
	if !req.ChangeSCN {
		log.Infof("ResumeSyncDsgTask, invoke helper ResumeSync, taskId:%d, dsgId:%d", taskId, taskInfo.IncrementId)
		resumeErr = helper.ResumeSync(ctx, taskInfo.IncrementId)
	} else {
		log.Infof("ResumeSyncDsgTask, invoke helper ResumeSyncWithSCN, taskId:%d, dsgId:%d", taskId, taskInfo.IncrementId)
		resumeErr = helper.ResumeSyncWithSCN(ctx, increment.IncrementTask{
			TaskId:            taskId,
			DsgTaskName:       fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID),
			ScnType:           string(req.ScnType),
			ScnUserInputValue: req.ScnInputValue,
		}, taskInfo, channelInfo)
	}
	if resumeErr != nil {
		log.Errorf("ResumeSyncDsgTask failed, resume sync failed, taskId:%d, changeSCN:%v, err: %v", req.TaskId, req.ChangeSCN, resumeErr)
		return nil, resumeErr
	}

	taskInfo.EndTime = timeutil.GetTMSNullTime()
	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	log.Infof("ResumeSyncDsgTask, update task info, taskId:%d, dsgId:%d, endTime:%v", taskId, taskInfo.IncrementId, taskInfo.EndTime)
	_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskErr != nil {
		log.Errorf("ResumeSyncDsgTask failed, update task info failed, taskId:%d, err: %v", req.TaskId, updateTaskErr)
		return nil, updateTaskErr
	}

	return &message.ResumeSyncDsgTaskResp{}, nil
}

func (i *Service) ClearCache(ctx context.Context, req *message.ClearCacheReq) (*message.ClearCacheResp, error) {
	log.Infof("ClearCacheTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("ClearCacheTask, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	apiReq := dsg.ClearCacheRequest{
		TID:    taskInfo.IncrementId,
		Limit:  100,
		Offset: 0,
	}
	log.Infof("ClearCacheTask, invoke ClearCache, taskId:%d, dsgId:%d, req:%v", taskId, taskInfo.IncrementId, apiReq)
	apiResp, err := i.dsgAdaptor.ClearCache(ctx, apiReq)
	if err != nil {
		log.Errorf("ClearCacheTask, invoke ClearCache failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, err)
		return nil, err
	}
	log.Infof("ClearCacheTask, invoke ClearCache success, taskId:%d, dsgId:%d, req:%v, rsp:%v", taskId, taskInfo.IncrementId, apiReq, apiResp)

	deleteErr := models.GetIncrementReaderWriter().DeleteIncrementTask(ctx, taskId)
	if deleteErr != nil {
		log.Errorf("ClearCacheTask, delete increment task info failed, taskId:%d, err: %v", taskId, deleteErr)
		return nil, deleteErr
	}

	return &message.ClearCacheResp{
		BaseDsgResp: buildBaseResp(apiResp.BaseResponse),
	}, nil
}

func (i *Service) StopDsgTask(ctx context.Context, req *message.StopDsgTaskReq) (*message.StopDsgTaskResp, error) {
	log.Infof("StopDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("StopDsgTask, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	stopApiReq := dsg.StopTaskRequest{
		TID: taskInfo.IncrementId,
	}
	log.Infof("StopDsgTask, invoke StopTask, taskId:%d, dsgId:%d, req:%v", taskId, taskInfo.IncrementId, stopApiReq)
	stopRsp, stopErr := i.dsgAdaptor.StopTask(ctx, stopApiReq)
	if stopErr != nil {
		log.Errorf("StopDsgTask, invoke StopTask failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, stopErr)
		return nil, stopErr
	}
	log.Infof("StopDsgTask, invoke StopTask success, taskId:%d, dsgId:%d, req:%v, rsp:%v", taskId, taskInfo.IncrementId, stopApiReq, stopRsp)

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)
	dsgTaskTime, getDsgTaskTimeErr := helper.GetDsgTaskTime(ctx, dsgTaskName)
	if getDsgTaskTimeErr != nil {
		log.Errorf("GetDsgTaskTime failed, taskId:%d, err: %v", req.TaskId, getDsgTaskTimeErr)
		return nil, getDsgTaskTimeErr
	}

	taskInfo.EndTime = dsgTaskTime.LastStopBinaryTime
	taskInfo.TaskStatus = constants.TASK_STATUS_STOP
	if taskInfo.EndTime.IsZero() {
		taskInfo.EndTime = time.Now()
	}
	_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskErr != nil {
		log.Errorf("PauseSyncDsgTask failed, update task info failed, taskId:%d, err: %v", req.TaskId, updateTaskErr)
		return nil, updateTaskErr
	}

	return &message.StopDsgTaskResp{
		BaseDsgResp: buildBaseResp(stopRsp.BaseResponse),
	}, nil
}

func (i *Service) GetDsgTaskPerformanceStat(ctx context.Context, req *message.GetDsgTaskPerformanceStatReq) (*message.GetDsgTaskPerformanceStatResp, *message.Page, error) {
	log.Infof("GetDsgTaskPerformanceStat request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTaskMonitorInfo, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	schemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getTableErr != nil {
		log.Errorf("GetDsgTaskPerformanceStat, get schema tables failed, taskId:%d, err: %v", taskId, getTableErr)
		return nil, nil, getTableErr
	}
	schemaTableMapping := lo.SliceToMap(schemaTables, func(item *channel.ChannelSchemaTable) (structs.SchemaTablePair, bool) {
		return structs.SchemaTablePair{SchemaName: item.SchemaNameS, TableName: item.TableNameS}, true
	})

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	tableSummary, getErr2 := i.dsgAdaptor.GetTableRealInfo(ctx, dsg.GetTableRealInfoRequest{TaskName: dsgTaskName, Limit: 10000})
	if getErr2 != nil {
		log.Errorf("GetDsgTaskPerformanceStat, invoke GetTableRealInfo failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, getErr2)
		return nil, nil, getErr2
	}

	pageNumber := req.Page
	pageSize := req.PageSize

	tableSummaries := buildTableSummaries(tableSummary, schemaTableMapping)
	totalNum := int64(len(tableSummaries))
	tableSummaries = lo.Subset(tableSummaries, (pageNumber-1)*pageSize, uint(pageNumber*pageSize))

	rsp := &message.GetDsgTaskPerformanceStatResp{
		BaseDsgResp:    buildBaseResp(tableSummary.BaseResponse),
		DsgTaskName:    dsgTaskName,
		IncrementId:    taskInfo.IncrementId,
		TableSummaries: tableSummaries,
	}
	return rsp, &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    totalNum,
	}, nil
}

func buildDsgFileStat(realTaskInfo dsg.GetRealTaskInfoResponse) message.DsgFileStat {
	return message.DsgFileStat{
		RealSpeed:  realTaskInfo.DataInfo.RealSpeed,
		QNO:        realTaskInfo.DataInfo.QNO,
		UsedTime:   realTaskInfo.DataInfo.UsedTime,
		NodeName:   realTaskInfo.DataInfo.NodeName,
		NotLoadNum: realTaskInfo.DataInfo.NotLoadNum,
		SizeDsg:    realTaskInfo.DataInfo.SizeDsg,
		FileNo:     realTaskInfo.DataInfo.FileNo,
		DelayTime:  realTaskInfo.DataInfo.DelayTime,
	}
}

func calculateTimeWithDelaySecond(loadTime string, delaySecond float64) string {
	loadTimeValue, err := time.Parse(constants.TIME_FORMAT, loadTime)
	if err != nil {
		return "Parse Time Error: " + loadTime
	}
	return loadTimeValue.Add(time.Duration(-delaySecond) * time.Second).Format(constants.TIME_FORMAT)
}

func buildDsgSyncStatus(realTaskInfo dsg.GetRealTaskInfoResponse, databaseScnTimestamp, analysisScnTimestamp string) message.DsgSyncStat {
	return message.DsgSyncStat{
		SourceDatabaseSCN:       realTaskInfo.DataInfo.DatabaseSCNOra,
		SourceDatabaseTimestamp: databaseScnTimestamp,
		SourceCurrentSCN:        realTaskInfo.DataInfo.AnalysisSCNOra,
		SourceCurrentTimestamp:  analysisScnTimestamp,
		LastTargetLoadTime:      realTaskInfo.DataInfo.LoadTime,
		LastTargetLoadSCNTime:   calculateTimeWithDelaySecond(realTaskInfo.DataInfo.LoadTime, realTaskInfo.DataInfo.DelayTime),
		DelaySecond:             realTaskInfo.DataInfo.DelayTime,

		InitialSyncTime:       realTaskInfo.DataInfo.BeginTime,
		LastSyncTime:          realTaskInfo.DataInfo.LastBeginTime,
		InitialSourceSyncTime: realTaskInfo.DataInfo.BeginTime,
		LastSourceSyncTime:    realTaskInfo.DataInfo.LastBeginTime,
	}
}

func (i *Service) GetDsgTaskPerformanceStatDetail(ctx context.Context, req *message.GetDsgTaskPerformanceStatDetailReq) (*message.GetDsgTaskPerformanceStatDetailResp, error) {
	log.Infof("GetDsgTaskPerformanceStatDetail request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTaskMonitorInfo, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	schemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getTableErr != nil {
		log.Errorf("GetDsgTaskPerformanceStat, get schema tables failed, taskId:%d, err: %v", taskId, getTableErr)
		return nil, getTableErr
	}

	filterSchemaName, filterTableName := req.SchemaName, req.TableName
	schemaTables = lo.Filter(schemaTables, func(item *channel.ChannelSchemaTable, _ int) bool {
		if filterSchemaName != "" && item.SchemaNameS != filterSchemaName {
			return false
		}
		if filterTableName != "" && item.TableNameS != filterTableName {
			return false
		}
		return true
	})

	schemaTableMapping := lo.SliceToMap(schemaTables, func(item *channel.ChannelSchemaTable) (structs.SchemaTablePair, bool) {
		return structs.SchemaTablePair{SchemaName: item.SchemaNameS, TableName: item.TableNameS}, true
	})

	intervalHour := req.IntervalHour
	if intervalHour == 0 {
		intervalHour = 1
	}
	avgMinute := req.AvgMinute
	if avgMinute == 0 {
		avgMinute = 5
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	tableStats, getErr3 := i.dsgAdaptor.GetTableDmlDdlInfo(ctx, dsg.GetTableDmlDdlInfoRequest{
		TaskName: dsgTaskName, AvgMinute: parse.FormatInt(avgMinute), IntervalHour: parse.FormatInt(intervalHour),
	})
	if getErr3 != nil {
		log.Errorf("GetDsgTaskPerformanceStat, invoke GetTableDmlDdlInfo failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, getErr3)
		return nil, getErr3
	}

	tablePerformances := buildTablePerformances(tableStats, schemaTableMapping, intervalHour, avgMinute)

	rsp := &message.GetDsgTaskPerformanceStatDetailResp{
		BaseDsgResp:       buildBaseResp(tableStats.BaseResponse),
		DsgTaskName:       dsgTaskName,
		IncrementId:       taskInfo.IncrementId,
		TablePerformances: tablePerformances,
	}
	return rsp, nil
}

func (i *Service) GetDsgTaskPerformanceStatTop(ctx context.Context, req *message.GetDsgTaskPerformanceStatTopReq) (*message.GetDsgTaskPerformanceStatTopResp, error) {
	log.Infof("GetDsgTaskPerformanceStatTop request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTaskMonitorInfo, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	schemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getTableErr != nil {
		log.Errorf("GetDsgTaskPerformanceStat, get schema tables failed, taskId:%d, err: %v", taskId, getTableErr)
		return nil, getTableErr
	}

	filterSchemaName, filterTableName := req.SchemaName, req.TableName
	schemaTables = lo.Filter(schemaTables, func(item *channel.ChannelSchemaTable, _ int) bool {
		if filterSchemaName != "" && item.SchemaNameS != filterSchemaName {
			return false
		}
		if filterTableName != "" && item.TableNameS != filterTableName {
			return false
		}
		return true
	})

	schemaTableMapping := lo.SliceToMap(schemaTables, func(item *channel.ChannelSchemaTable) (structs.SchemaTablePair, bool) {
		return structs.SchemaTablePair{SchemaName: item.SchemaNameS, TableName: item.TableNameS}, true
	})

	intervalHour := req.IntervalHour
	if intervalHour == 0 {
		intervalHour = 1
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	tableStats, getErr3 := i.dsgAdaptor.GetTableDmlDdlInfo(ctx, dsg.GetTableDmlDdlInfoRequest{
		TaskName: dsgTaskName, AvgMinute: "5", IntervalHour: parse.FormatInt(intervalHour),
	})
	if getErr3 != nil {
		log.Errorf("GetDsgTaskPerformanceStat, invoke GetTableDmlDdlInfo failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, getErr3)
		return nil, getErr3
	}

	tablePerformances := buildTablePerformances(tableStats, schemaTableMapping, intervalHour, 5)
	tableTopPerformances := buildTableTopPerformances(tablePerformances, req.TopNum, req.SortBy)

	rsp := &message.GetDsgTaskPerformanceStatTopResp{
		BaseDsgResp:          buildBaseResp(tableStats.BaseResponse),
		DsgTaskName:          dsgTaskName,
		IncrementId:          taskInfo.IncrementId,
		TableTopPerformances: tableTopPerformances,
	}
	return rsp, nil
}

func buildTableTopPerformances(performances []message.DsgTablePerformance, num int, sortBy string) []message.DsgTableTopPerformance {
	tableTopPerformances := make([]message.DsgTableTopPerformance, 0, len(performances))
	for _, performance := range performances {
		var totalChangeNum int64
		var totalInsertNum int64
		var totalUpdateNum int64
		var totalDeleteNum int64
		var totalDdlNum int64

		for _, item := range performance.Items {
			totalChangeNum += item.TotalChangeNum
			totalInsertNum += item.InsertNum
			totalUpdateNum += item.UpdateNum
			totalDeleteNum += item.DeleteNum
			totalDdlNum += item.DdlNum
		}
		tableTopPerformances = append(tableTopPerformances, message.DsgTableTopPerformance{
			SchemaName: performance.SchemaName,
			TableName:  performance.TableName,
			Item: message.DsgTablePerformanceItem{
				InsertNum:      totalInsertNum,
				UpdateNum:      totalUpdateNum,
				DeleteNum:      totalDeleteNum,
				DdlNum:         totalDdlNum,
				TotalChangeNum: totalChangeNum,
			},
		})
	}
	sort.Slice(tableTopPerformances, func(i, j int) bool {
		switch sortBy {
		case "INSERT":
			return tableTopPerformances[i].Item.InsertNum > tableTopPerformances[j].Item.InsertNum
		case "UPDATE":
			return tableTopPerformances[i].Item.UpdateNum > tableTopPerformances[j].Item.UpdateNum
		case "DELETE":
			return tableTopPerformances[i].Item.DeleteNum > tableTopPerformances[j].Item.DeleteNum
		case "DDL":
			return tableTopPerformances[i].Item.DdlNum > tableTopPerformances[j].Item.DdlNum
		default:
			return tableTopPerformances[i].Item.TotalChangeNum > tableTopPerformances[j].Item.TotalChangeNum
		}
	})

	return lo.Subset(tableTopPerformances, 0, uint(num))
}

func buildTablePerformances(tableStats dsg.GetTableDmlDdlInfoResponse, taskSchemaTables map[structs.SchemaTablePair]bool, intervalHour int, avgMinute int) []message.DsgTablePerformance {

	tablePerformanceMap := map[structs.SchemaTablePair][]message.DsgTablePerformanceItem{}
	intervalMinutes := make(map[string]bool, 0)

	// 1. iterate dsg table stats data, and filter the data by task schema tables
	// 2. save all intervalMinute
	for _, item := range tableStats.DataInfo {
		key := structs.SchemaTablePair{SchemaName: item.SchemaName, TableName: item.TableName}
		if _, ok := taskSchemaTables[key]; !ok {
			continue
		}
		if _, ok := tablePerformanceMap[key]; !ok {
			tablePerformanceMap[key] = []message.DsgTablePerformanceItem{}
		}
		im := strings.ReplaceAll(item.FiveMinuteInterval, "T", " ")
		intervalMinutes[im] = true
		tablePerformanceMap[key] = append(tablePerformanceMap[key], message.DsgTablePerformanceItem{
			IntervalMinute: im,
			DeleteNum:      int64(item.TotalDelete),
			UpdateNum:      int64(item.TotalUpdate),
			InsertNum:      int64(item.TotalInsert),
			DdlNum:         int64(item.TotalDdl),
			TotalChangeNum: int64(item.TotalChanges),
		})
	}

	// 3. fill tablePerformanceMap with missed schemaTable
	for schemaTable := range taskSchemaTables {
		if _, ok := tablePerformanceMap[schemaTable]; !ok {
			tablePerformanceMap[schemaTable] = []message.DsgTablePerformanceItem{}
		}
	}

	// 4. fill intervalMinutes with missed minute by intervalHour and avgMinute
	intervalMinutes = fillIntervalMinutes(intervalMinutes, intervalHour, avgMinute)

	// 5. fill the intervalMinute which is not exist in tablePerformanceMap
	for schemaTablePair, intervalData := range tablePerformanceMap {
		intervalMap := make(map[string]message.DsgTablePerformanceItem, 0)
		for _, item := range intervalData {
			intervalMap[item.IntervalMinute] = item
		}
		for intervalMinute := range intervalMinutes {
			if _, ok := intervalMap[intervalMinute]; !ok {
				tablePerformanceMap[schemaTablePair] = append(tablePerformanceMap[schemaTablePair], message.DsgTablePerformanceItem{
					IntervalMinute: intervalMinute,
				})
			}
		}
	}

	// 6. sort the tablePerformanceMap by intervalMinute
	tablePerformances := make([]message.DsgTablePerformance, 0, len(taskSchemaTables))
	for key, value := range tablePerformanceMap {
		val := value
		sort.Slice(val, func(i, j int) bool {
			return val[i].IntervalMinute < val[j].IntervalMinute
		})
		tablePerformances = append(tablePerformances, message.DsgTablePerformance{
			SchemaName: key.SchemaName,
			TableName:  key.TableName,
			Items:      val,
		})
	}

	// 7. sort the tablePerformances by schemaName and tableName
	sort.Slice(tablePerformances, func(i, j int) bool {
		if tablePerformances[i].SchemaName == tablePerformances[j].SchemaName {
			return tablePerformances[i].TableName < tablePerformances[j].TableName
		}
		return tablePerformances[i].SchemaName < tablePerformances[j].SchemaName
	})
	return tablePerformances
}

func fillIntervalMinutes(minutes map[string]bool, hour int, minute int) map[string]bool {

	nowTime := timeutil.GetNowTime()
	nowTime = nowTime.Add(-time.Second * time.Duration(nowTime.Second()))

	var simpleMinuteVal time.Time

	if len(lo.Keys(minutes)) != 0 {
		simpleMinute := lo.Keys(minutes)[0]
		simpleMinuteVal, _ = time.ParseInLocation(constants.TIME_FORMAT, simpleMinute, time.Local)
	} else {
		simpleMinuteVal = nowTime
	}

	for minuteLag := 0; minuteLag < hour*60; minuteLag++ {
		lagTime := nowTime.Add(-time.Minute * time.Duration(minuteLag))
		if int(simpleMinuteVal.Sub(lagTime).Minutes())%minute == 0 {
			minutes[lagTime.Format(constants.TIME_FORMAT)] = true
		}
	}
	return minutes
}

func buildTableSummaries(tableSummary dsg.GetTableRealInfoResponse, schemaTableMapping map[structs.SchemaTablePair]bool) []message.DsgTableSummary {
	tableSummaries := make([]message.DsgTableSummary, 0, len(schemaTableMapping))

	existTableMap := map[structs.SchemaTablePair]bool{}
	// dts for dsg table summary
	for _, dts := range tableSummary.DataInfo {
		dtsPair := structs.SchemaTablePair{SchemaName: dts.SchemaName, TableName: dts.TableName}
		existTableMap[dtsPair] = true
		tableSummaries = append(tableSummaries, message.DsgTableSummary{
			SchemaName:     dts.SchemaName,
			TableName:      dts.TableName,
			TotalDeleteNum: int64(dts.DeleteNum),
			TotalUpdateNum: int64(dts.UpdateNum),
			TotalInsertNum: int64(dts.InsertNum),
			TotalDdlNum:    int64(dts.DdlNum),
			TotalChangeNum: int64(dts.DeleteNum + dts.UpdateNum + dts.InsertNum + dts.DdlNum),
		})
	}

	for schemaTablePair := range schemaTableMapping {
		if _, ok := existTableMap[schemaTablePair]; !ok {
			tableSummaries = append(tableSummaries, message.DsgTableSummary{
				SchemaName: schemaTablePair.SchemaName,
				TableName:  schemaTablePair.TableName,
			})
		}
	}

	sort.Slice(tableSummaries, func(i, j int) bool {
		if tableSummaries[i].SchemaName == tableSummaries[j].SchemaName {
			return tableSummaries[i].TableName < tableSummaries[j].TableName
		}
		return tableSummaries[i].SchemaName < tableSummaries[j].SchemaName
	})
	return tableSummaries
}

func (i *Service) GetDsgTaskWarningInfo(ctx context.Context, req *message.GetDsgTaskWarningInfoReq) (*message.GetDsgTaskWarningInfoResp, error) {
	log.Infof("GetDsgTaskWarningInfo request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTaskWarningInfo, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	sourceWarningInfo, getSourceErr := i.dsgAdaptor.GetWarningInfo(ctx, dsg.GetWarningInfoRequest{
		TaskName: dsgTaskName, TargetType: dsg.TargetTypeSource,
	})
	if getSourceErr != nil {
		log.Errorf("GetDsgTaskWarningInfo, invoke GetWarningInfo failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, getSourceErr)
		return nil, getSourceErr
	}

	targetWarningInfo, getTargetErr := i.dsgAdaptor.GetWarningInfo(ctx, dsg.GetWarningInfoRequest{
		TaskName: dsgTaskName, TargetType: dsg.TargetTypeTarget,
	})
	if getTargetErr != nil {
		log.Errorf("GetDsgTaskWarningInfo, invoke GetWarningInfo failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, getTargetErr)
		return nil, getTargetErr
	}

	return &message.GetDsgTaskWarningInfoResp{
		BaseDsgResp:  buildBaseResp(targetWarningInfo.BaseResponse),
		DsgTaskName:  dsgTaskName,
		IncrementId:  taskInfo.IncrementId,
		WarningInfos: buildDSGWarningInfo(sourceWarningInfo.DataInfo, targetWarningInfo.DataInfo),
	}, nil

}

func buildDSGWarningInfo(sourceInfos, targetInfos []dsg.WarningInfo) message.WarningInfos {
	warnings := make([]message.Warning, 0, len(sourceInfos)+len(targetInfos))
	for _, info := range sourceInfos {
		warnings = append(warnings, buildWarningInfo(info, "source"))
	}
	for _, info := range targetInfos {
		warnings = append(warnings, buildWarningInfo(info, "target"))
	}
	return message.WarningInfos{
		Warnings: warnings,
		Total:    len(sourceInfos) + len(targetInfos),
	}
}

func buildWarningInfo(info dsg.WarningInfo, datasourceDirection string) message.Warning {
	return message.Warning{
		DataSourceDirection: datasourceDirection,
		SendEmalFlag:        info.SendEmalFlag,
		UpdateTime:          info.UpdateTime,
		ErrorMsg:            info.ErrorMsg,
		TimeStamp:           info.TimeStamp,
		ErrorType:           info.ErrorType,
		NodeName:            info.NodeName,
		TargetType:          info.TargetType,
		ErrorCode:           info.ErrorCode,
		MsgLen:              info.MsgLen,
		ErrorStatus:         info.ErrorStatus,
		MsgMD5:              info.MsgMD5,
		ErrorCount:          info.ErrorCount,
	}
}

func (i *Service) GetDsgTaskOperationLogs(ctx context.Context, req *message.GetDsgTaskOperationLogsReq) (*message.GetDsgTaskOperationLogsResp, *message.Page, error) {
	log.Infof("GetDsgTaskOperationLogs request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTaskMonitorInfo, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	dsTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	logs, total, getLogErr := helper.GetTaskLogs(ctx, dsTaskName, (req.Page-1)*req.PageSize, req.PageSize)
	if getLogErr != nil {
		log.Errorf("GetDsgTaskOperationLogs, invoke GetTaskLogs failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, getLogErr)
		return nil, nil, getLogErr
	}

	return &message.GetDsgTaskOperationLogsResp{Logs: logs}, &message.Page{Page: req.Page, PageSize: req.PageSize, Total: total}, nil
}

func (i *Service) GetDsgTaskMonitorInfo(ctx context.Context, req *message.GetDsgTaskMonitorInfoReq) (*message.GetDsgTaskMonitorInfoResp, error) {
	log.Infof("GetDsgTaskMonitorInfo request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTaskMonitorInfo, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	monitorList, monitorErr := i.dsgAdaptor.GetMonitorList(ctx, dsg.GetMonitorListRequest{
		UserId: i.userId,
		TID:    taskInfo.IncrementId,
		Offset: 0,
		Limit:  100,
	})
	if monitorErr != nil {
		log.Errorf("GetDsgTaskMonitorInfo, invoke GetMonitorList failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, monitorErr)
		return nil, monitorErr
	}

	var syncInfo, syncStatus, resourceInfo string
	var sourceCpu, sourceDisk, sourceMemory string
	var targetCpu, targetDisk, targetMemory string

	if len(monitorList.DataInfo.Rows) != 0 {
		row := monitorList.DataInfo.Rows[0]
		syncInfo = row.SyncInfo
		syncStatus = row.SyncStatus
		resourceInfo = row.ResourcesInfo
		sourceCpu = row.DsCPURate
		sourceDisk = row.DsDiskRate
		sourceMemory = row.DsMemoryRate
		targetCpu = row.DtCPURate
		targetDisk = row.DtDiskRate
		targetMemory = row.DtMemoryRate
	}

	return &message.GetDsgTaskMonitorInfoResp{
		BaseDsgResp:      buildBaseResp(monitorList.BaseResponse),
		DsgTaskName:      fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID),
		IncrementId:      taskInfo.IncrementId,
		SyncInfo:         syncInfo,
		SyncStatus:       syncStatus,
		ResourceInfo:     resourceInfo,
		StartMigrateTime: "not_set",
		StopMigrateTime:  "not_set",
		ResourceInfoRate: message.ResourceInfoRate{
			SourceCPU:    sourceCpu,
			SourceDisk:   sourceDisk,
			SourceMemory: sourceMemory,
			TargetCPU:    targetCpu,
			TargetDisk:   targetDisk,
			TargetMemory: targetMemory,
		},
	}, nil

}

func (i *Service) GetInstallDsgTaskStatus(ctx context.Context, req *message.GetInstallDsgTaskStatusReq) (*message.GetInstallDsgTaskStatusResp, error) {
	log.Infof("GetInstallDsgTaskStatus request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetInstallDsgTaskStatus, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	statusResp, err := i.dsgAdaptor.GetPGMDInstallStatus(ctx, dsg.GetPGMDInstallStatusRequest{
		TaskName: dsgTaskName,
	})
	if err != nil {
		log.Errorf("GetInstallDsgTaskStatus, invoke GetPGMDInstallStatus failed, taskId:%d, dsgId:%d, dsgTaskName:%s, err: %v", req.TaskId, taskInfo.IncrementId, dsgTaskName, err)
		return nil, err
	}

	return &message.GetInstallDsgTaskStatusResp{
		BaseDsgResp: buildBaseResp(statusResp.BaseResponse),
		Success:     statusResp.DataInfo,
	}, nil

}

func (i *Service) InstallDsgTask(ctx context.Context, req *message.InstallDsgTaskReq) (*message.InstallDsgTaskResp, error) {
	log.Infof("InstallDsgTask request: %v", req)
	var (
		dsgTaskName    string
		taskParamBytes []byte
		marshalErr     error
	)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("InstallDsgTask failed, get task info failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("InstallDsgTask failed, get channel info failed, taskId:%d, err: %v", req.TaskId, getChannelErr)
		return nil, getChannelErr
	}
	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		return nil, fmt.Errorf("get task source datasource info failed, taskId:%d, err: %v", req.TaskId, getSourceErr)
	}
	targetDS, getTargetErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if getTargetErr != nil {
		return nil, fmt.Errorf("get task target datasource info failed, taskId:%d, err: %v", req.TaskId, getTargetErr)
	}
	sourceHost, getSourceHostErr := models.GetDatasourceReaderWriter().GetHostByIP(ctx, req.SourceIP)
	if getSourceHostErr != nil {
		return nil, fmt.Errorf("get source host failed, taskId:%d, err: %v", req.TaskId, getSourceHostErr)
	}
	targetHost, getTargetHostErr := models.GetDatasourceReaderWriter().GetHostByIP(ctx, req.TargetIP)
	if getTargetHostErr != nil {
		return nil, fmt.Errorf("get target host failed, taskId:%d, err: %v", req.TaskId, getTargetHostErr)
	}

	dsgTaskName = fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)

	helper := &incrementpkg.DsgTaskHelper{
		UserId:     i.userId,
		Adaptor:    i.dsgAdaptor,
		ChannelId:  taskInfo.ChannelId,
		TaskId:     taskInfo.TaskID,
		SourceHost: sourceHost,
		SourcePath: req.SourcePath,
		TargetHost: targetHost,
		TargetPath: req.TargetPath,
	}

	validateErr := helper.ValidateDataSourceWithSourceIP(ctx, sourceDS, req.SourceIP)
	if validateErr != nil {
		log.Errorf("InstallDsgTask failed, validate source datasource failed, taskId:%d, err: %v", req.TaskId, validateErr)
		return nil, validateErr
	}

	helper.SaveInstallParam(ctx, dsgTaskName)
	helper.BuildParams(ctx)
	helper.BuildMigrationInstallation(ctx)
	helper.BuildSourceDatabaseInformation(ctx, sourceDS, req.SourceIP)
	helper.BuildTargetDatabaseInformation(ctx, targetDS, req.TargetIP)
	helper.BuildDefineConfig(ctx)

	body, getBodyErr := helper.GetRequestBody()
	if getBodyErr != nil {
		log.Errorf("InstallDsgTask failed, build SaveTaskInfo body failed, taskId:%d, err: %v", req.TaskId, getBodyErr)
		return nil, getBodyErr
	}

	taskParamBytes, marshalErr = json.Marshal(body)
	if marshalErr != nil {
		log.Errorf("InstallDsgTask failed, marshal SaveTaskInfo body failed, taskId:%d, err: %v", req.TaskId, marshalErr)
		return nil, marshalErr
	}

	createApiReq := dsg.SaveTaskInfoRequest{
		TaskName:      dsgTaskName,
		InstallStatus: string(dsg.InstallStatusInstall),
		TaskParm:      string(taskParamBytes),
	}
	createApiResp, createErr := i.dsgAdaptor.SaveTaskInfo(ctx, createApiReq)
	if createErr != nil {
		log.Errorf("InstallDsgTask failed, SaveTaskInfo failed, taskId:%d, err: %v", req.TaskId, createErr)
		return nil, createErr
	}

	tasksRsp, getDsgTaskErr := i.dsgAdaptor.SelectAllTask(ctx, dsg.SelectAllTaskRequest{
		Limit:    100 * 100,
		TaskName: dsgTaskName,
		UserID:   i.userId,
	})
	if getDsgTaskErr != nil {
		log.Errorf("InstallDsgTask failed, SaveTaskInfo success but get dsg task failed, taskId:%d, err: %v", req.TaskId, getDsgTaskErr)
		return nil, getDsgTaskErr
	}
	var hitDsgTask bool
	var dsgTaskId int
	for _, t := range tasksRsp.DataInfo.Rows {
		if t.TaskName == dsgTaskName {
			hitDsgTask = true
			dsgTaskId = t.TID
			break
		}
	}
	if !hitDsgTask {
		err := errors.NewError(errors.TMS_DSG_INCREMENT_TASK_NOT_FOUND, "Create DSG task success, but can't find it in DSG")
		log.Errorf("InstallDsgTask failed, SaveTaskInfo success, but can't find dsg task, taskId:%d, err: %v", req.TaskId, err)
		return nil, err
	}
	taskInfo.IncrementId = dsgTaskId

	_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskErr != nil {
		log.Errorf("InstallDsgTask failed, SaveTaskInfo success, update dsg task id to TMS task failed, taskId:%d, dsgTaskId:%d, err: %v", req.TaskId, dsgTaskId, updateTaskErr)
		return nil, updateTaskErr
	}

	for current := 0; current <= 3; current++ {
		time.Sleep(time.Second * time.Duration(3+current))
		installStatusResp, installErr := i.dsgAdaptor.GetPGMDInstallStatus(ctx, dsg.GetPGMDInstallStatusRequest{TaskName: dsgTaskName})
		if installErr != nil {
			log.Errorf("InstallDsgTask failed, get install status failed, taskId:%d, err: %v", req.TaskId, installErr)
			return nil, installErr
		}
		if installStatusResp.BaseResponse.Message == "安装成功" && installStatusResp.BaseResponse.StatFlag == dsg.Success {
			break
		}
	}

	// 1. change taskMethod to Increment
	// 2. fetch source vm config from dsg and verify vm value, based on taskMethod Increment
	// 3. fetch target yloader config from dsg and update some default values

	// 1. change taskMethod to Increment
	changeSyncMethodReq := dsg.OverWriteConfigRequest{TID: taskInfo.IncrementId, TaskMethod: dsg.TaskMethodIncrement}
	log.Infof("InstallDsgTask, invoke OverWriteConfig, taskId:%d, dsgId:%d, req:%v", taskId, taskInfo.IncrementId, changeSyncMethodReq)
	changeSyncMethodResp, changeSyncMethodErr := i.dsgAdaptor.OverWriteConfig(ctx, changeSyncMethodReq)
	if changeSyncMethodErr != nil {
		log.Errorf("InstallDsgTask, invoke OverWriteConfig failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, changeSyncMethodErr)
		return nil, changeSyncMethodErr
	}
	log.Infof("InstallDsgTask, invoke OverWriteConfig req:%v, rsp:%v", changeSyncMethodReq, changeSyncMethodResp)

	// 2. fetch source vm config from dsg and verify vm value, based on taskMethod Increment
	log.Infof("InstallDsgTask, dsg task created, read source config for update default values, taskId:%d, dsgTaskId:%d", req.TaskId, dsgTaskId)
	updateSourceConfigErr := helper.UpdateDsgSourceConfig(ctx, taskInfo.IncrementId, dsg.TaskMethodParamIncrementOnly)
	if updateSourceConfigErr != nil {
		log.Errorf("InstallDsgTask failed, update source config failed, taskId:%d, dsgTaskId:%d, err: %v", req.TaskId, dsgTaskId, updateSourceConfigErr)
		return nil, updateSourceConfigErr
	}

	// 3. fetch target yloader config from dsg and update some default values
	log.Infof("InstallDsgTask, dsg task created, read target config for update default values, taskId:%d, dsgTaskId:%d", req.TaskId, dsgTaskId)
	updateTargetConfigErr := helper.UpdateDsgTargetConfig(ctx, taskInfo.IncrementId)
	if updateTargetConfigErr != nil {
		log.Errorf("InstallDsgTask failed, update target config failed, taskId:%d, dsgTaskId:%d, err: %v", req.TaskId, dsgTaskId, updateTargetConfigErr)
		return nil, updateTargetConfigErr
	}

	return &message.InstallDsgTaskResp{
		BaseDsgResp: buildBaseResp(createApiResp.BaseResponse),
	}, nil
}

func (i *Service) DeleteDsgTask(ctx context.Context, req *message.DeleteDsgTaskReq) (*message.DeleteDsgTaskResp, error) {
	log.Infof("DeleteDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("DeleteDsgTask, get task info failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	apiReq := dsg.DeleteTaskRequest{
		TID: taskInfo.IncrementId,
	}
	log.Infof("DeleteDsgTask, invoke DeleteTask, taskId:%d, dsgId:%d, req:%v", taskId, taskInfo.IncrementId, apiReq)
	apiResp, err := i.dsgAdaptor.DeleteTask(ctx, apiReq)
	if err != nil {
		log.Errorf("DeleteDsgTask, invoke DeleteTask failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, err)
		return nil, err
	}
	log.Infof("DeleteDsgTask, invoke DeleteTask success, taskId:%d, dsgId:%d, req:%v, rsp:%v", taskId, taskInfo.IncrementId, apiReq, apiResp)

	taskInfo.IncrementId = 0

	_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if updateTaskErr != nil {
		log.Errorf("DeleteDsgTask, delete from dsg success, but update task info failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, updateTaskErr)
		return nil, updateTaskErr
	}

	return &message.DeleteDsgTaskResp{
		BaseDsgResp: buildBaseResp(apiResp.BaseResponse),
	}, nil
}

func (i *Service) ListAllDsgTask(ctx context.Context, req *message.ListAllDsgTaskReq) (*message.ListAllDsgTaskResp, error) {
	log.Infof("ListAllDsgTask request: %v", req)
	apiReq := dsg.SelectAllTaskRequest{
		TID:      req.DsgTaskId,
		Offset:   (req.Page - 1) * req.PageSize,
		Limit:    req.PageSize,
		TaskName: req.TaskName,
		UserID:   i.userId,
	}

	apiResp, err := i.dsgAdaptor.SelectAllTask(ctx, apiReq)
	if err != nil {
		log.Errorf("ListAllDsgTask failed, err: %v", err)
		return nil, err
	}

	tasks := lo.Map(apiResp.DataInfo.Rows, buildDsgTaskMessage())
	resp := &message.ListAllDsgTaskResp{
		Total: apiResp.DataInfo.Total,
		Tasks: tasks,
	}
	return resp, nil
}

func (i *Service) VerifyHost(ctx context.Context, req *message.VerifyHostReq) (*message.VerifyHostResp, error) {
	log.Infof("VerifyHost request: %v", req)
	apiReq := dsg.VerifyHostRequest{
		HostIP: req.HostIP,
	}

	apiResp, err := i.dsgAdaptor.VerifyHost(ctx, apiReq)
	if err != nil {
		log.Errorf("VerifyHost failed, err: %v", err)
		return nil, err
	}

	var errMessage string
	if apiResp.Validate() != nil {
		errMessage = apiResp.Validate().Error()
	}

	if errMessage == "" {
		errMessage = "主机Client未启动或异常"
	}

	return &message.VerifyHostResp{
		HostIP:     req.HostIP,
		IsValid:    apiResp.DataInfo,
		ErrMessage: errMessage,
	}, nil
}

func (i *Service) GetDsgTask(ctx context.Context, req *message.GetDsgTaskReq) (*message.GetDsgTaskResp, error) {
	log.Infof("GetDsgTask request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetDsgTask failed, err: %v", getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	selectTaskResp, err := i.dsgAdaptor.SelectAllTask(ctx, dsg.SelectAllTaskRequest{
		TID:    taskInfo.IncrementId,
		Limit:  100,
		Offset: 0,
		UserID: i.userId,
	})
	if err != nil {
		log.Errorf("SelectAllTask failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, err)
		return nil, err
	}

	if selectTaskResp.DataInfo.Total == 0 || len(selectTaskResp.DataInfo.Rows) == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_TASK_NOT_FOUND, "DSG")
	}

	rawDsgTask := selectTaskResp.DataInfo.Rows[0]

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("StartSyncDsgTask, get channel info failed, taskId:%d, err: %v", taskId, getChannelErr)
		return nil, getChannelErr
	}

	sourceConns, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if setUpErr != nil {
		log.Errorf("StartSyncDsgTask, set up source database connection failed, taskId:%d, err: %v", taskId, setUpErr)
		return nil, setUpErr
	}

	log.Infof("GetDsgTaskSyncDetail, get dsg task success, taskId:%d, dsgId:%d, dsgTaskName:%s", req.TaskId, taskInfo.IncrementId, rawDsgTask.TaskName)
	realTaskInfo, getErr1 := i.dsgAdaptor.GetRealTaskInfo(ctx, dsg.GetRealTaskInfoRequest{TaskName: rawDsgTask.TaskName})
	if getErr1 != nil {
		log.Errorf("GetDsgTaskSyncDetail, invoke GetRealTaskInfo failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, getErr1)
	}

	var databaseScnTimestamp, analysisScnTimestamp string
	var parseErr error

	if realTaskInfo.DataInfo.DatabaseSCNOra != "" {
		_, databaseScnTimestamp, parseErr = helper.ParseSCNToTimestamp(ctx, realTaskInfo.DataInfo.DatabaseSCNOra, sourceConns, taskInfo)
		if parseErr != nil {
			log.Errorf("GetDsgTaskSyncDetail, parse database SCN to timestamp failed, taskId:%d, scn:%s, err: %v", req.TaskId, realTaskInfo.DataInfo.DatabaseSCNOra, parseErr)
		}
	}
	if realTaskInfo.DataInfo.AnalysisSCNOra != "" {
		_, analysisScnTimestamp, parseErr = helper.ParseSCNToTimestamp(ctx, realTaskInfo.DataInfo.AnalysisSCNOra, sourceConns, taskInfo)
		if parseErr != nil {
			log.Errorf("GetDsgTaskSyncDetail, parse analysis SCN to timestamp failed, taskId:%d, scn:%s, err: %v", req.TaskId, realTaskInfo.DataInfo.AnalysisSCNOra, parseErr)
		}
	}

	dsgTaskTime, getDsgTaskTimeErr := helper.GetDsgTaskTime(ctx, rawDsgTask.TaskName)
	if getDsgTaskTimeErr != nil {
		log.Errorf("GetDsgTaskTime failed, taskId:%d, err: %v", req.TaskId, getDsgTaskTimeErr)
		return nil, getDsgTaskTimeErr
	}
	dsgTaskTime.InstallBinaryTime = rawDsgTask.CreateTime

	incrementTask, getITErr := models.GetIncrementReaderWriter().GetIncrementTask(ctx, req.TaskId)
	if getITErr != nil {
		log.Errorf("GetDsgTask, get increment task info failed, taskId:%d, err: %v", req.TaskId, getITErr)
	}

	resp := &message.GetDsgTaskResp{
		DsgTask:     buildDsgTaskMessage()(rawDsgTask, 0),
		DsgTaskTime: *dsgTaskTime,
		SyncOption:  buildSyncOption(incrementTask),
		FileStat:    buildDsgFileStat(realTaskInfo),
		SyncStat:    buildDsgSyncStatus(realTaskInfo, databaseScnTimestamp, analysisScnTimestamp),
	}
	return resp, nil
}

func buildSyncOption(incrementTask *increment.IncrementTask) message.IncrementTaskSyncOption {
	var syncOption message.IncrementTaskSyncOption
	if incrementTask != nil {
		syncOption = message.IncrementTaskSyncOption{
			TaskId:            incrementTask.TaskId,
			DsgTaskName:       incrementTask.DsgTaskName,
			ScnType:           incrementTask.ScnType,
			ScnValue:          incrementTask.ScnValue,
			TimeValue:         incrementTask.TimeValue,
			ScnUserInputValue: incrementTask.ScnUserInputValue,
		}
	}
	return syncOption
}

func (i *Service) GetInstallationInfo(ctx context.Context, req *message.GetInstallationInfoReq) (*message.GetInstallationInfoResp, error) {
	log.Infof("GetInstallationInfo request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("GetInstallationInfo, GetTask failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetInstallationInfo, GetChannel failed, taskId:%d, channelId:%d, err: %v", taskId, taskInfo.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		log.Errorf("GetInstallationInfo, Get datasource failed, taskId:%d, datasourceId:%d, err: %v", taskId, channelInfo.DatasourceIdS, getSourceErr)
		return nil, getSourceErr
	}

	incrementInstallParam, getParamErr := models.GetIncrementReaderWriter().GetIncrementInstallParam(ctx, taskId)
	if getParamErr != nil {
		log.Errorf("GetInstallationInfo, GetIncrementInstallParam failed, taskId:%d, err: %v", taskId, getParamErr)
	}

	var sourceIp, targetIp string
	if incrementInstallParam != nil {
		sourceIp = incrementInstallParam.SourceIP
		targetIp = incrementInstallParam.TargetIP
	}
	if sourceIp == "" {
		sourceIp = sourceDS.HostIp
	}
	if targetIp == "" {
		targetIp = sourceDS.HostIp
	}

	incrementConfig := config.GetGlobalConfig().IncrementConfig

	return &message.GetInstallationInfoResp{
		Host:                            incrementConfig.DsgHost,
		Port:                            incrementConfig.DsgPort,
		AutoMaticEngineBootPort:         incrementConfig.DsgAutoMaticEngineBootPort,
		ExternalHost:                    incrementConfig.ExternalDsgHost,
		ExternalPort:                    incrementConfig.ExternalDsgPort,
		ExternalAutoMaticEngineBootPort: incrementConfig.ExternalDsgAutoMaticEngineBootPort,
		SourceIp:                        sourceIp,
		TargetIp:                        targetIp,
	}, nil
}

func (i *Service) GetSourceConfig(ctx context.Context, req *message.GetSourceConfigReq) (*message.GetSourceConfigResp, error) {
	log.Infof("GetSourceConfig request: %v", req)

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetSourceConfig failed, err: %v", getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	sourceIniRsp, getSourceErr := i.dsgAdaptor.ReadSourceDataSourceIni(ctx, dsg.ReadSourceDataSourceIniRequest{FileName: "vm", TID: taskInfo.IncrementId})
	if getSourceErr != nil {
		log.Errorf("GetSourceConfig failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, getSourceErr)
		return nil, getSourceErr
	}

	return &message.GetSourceConfigResp{
		VM: sourceIniRsp.DataInfo.Res,
	}, nil

}

func (i *Service) UpdateTableMapping(ctx context.Context, req *message.UpdateTableMappingReq) (*message.UpdateTableMappingResp, error) {
	log.Infof("GetTableMapping request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("GetSourceConfig failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	channelSchemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getTableErr != nil {
		log.Errorf("GetChannelSchemaTablesByTaskId failed, taskId:%d, err: %v", taskId, getTableErr)
		return nil, getTableErr
	}

	dsgTables := make([]dsg.Table, 0, len(channelSchemaTables))
	for _, table := range channelSchemaTables {
		dsgTables = append(dsgTables, dsg.Table{
			Key:         table.SchemaNameS + "." + table.TableNameS,
			Label:       table.SchemaNameS,
			TableLabel:  table.TableNameS,
			TargetLabel: strings.ToLower(table.SchemaNameT),
			TargetTable: strings.ToLower(table.TableNameT),
		})
	}
	dsgTableBytes, _ := json.Marshal(&dsgTables)

	log.Infof("UpdateTableMapping, taskId:%d, dsgId:%d, dsgTablesLen:%d", taskId, taskInfo.IncrementId, len(dsgTables))
	saveRsp, saveMappingErr := i.dsgAdaptor.SaveTableMappingResult(ctx, dsg.SaveTableMappingResultRequest{
		TID:  taskInfo.IncrementId,
		Rows: string(dsgTableBytes),
	})
	if saveMappingErr != nil {
		log.Errorf("SaveTableMappingResult failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, saveMappingErr)
		return nil, saveMappingErr
	}

	log.Infof("UpdateTableMapping success, taskId:%d, dsgId:%d, dsgTablesLen:%d", taskId, taskInfo.IncrementId, len(dsgTables))
	return &message.UpdateTableMappingResp{
		BaseDsgResp: buildBaseResp(saveRsp.BaseResponse),
	}, nil
}

func (i *Service) GetTableMapping(ctx context.Context, req *message.GetTableMappingReq) (*message.GetTableMappingResp, error) {
	log.Infof("GetTableMapping request: %v", req)

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetSourceConfig failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	defineMappingResponse, sourceErr := i.dsgAdaptor.SelectDefineMapping(ctx, dsg.SelectDefineMappingRequest{TID: taskInfo.IncrementId})
	if sourceErr != nil {
		log.Errorf("GetTableMapping failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, sourceErr)
		return nil, sourceErr
	}

	rsp := &message.GetTableMappingResp{}

	dsgTables := buildDsgTables(defineMappingResponse, req.TaskId)

	rsp.Define.Map = defineMappingResponse.DataInfo.Map
	rsp.Define.Real = defineMappingResponse.DataInfo.Real
	rsp.Define.Full = defineMappingResponse.DataInfo.Full
	rsp.Mapping = dsgTables

	return rsp, nil
}

func (i *Service) VerifyTableMapping(ctx context.Context, req *message.VerifyTableMappingReq) (*message.VerifyTableMappingResp, error) {
	log.Infof("VerifyTableMapping request: %v", req)

	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("GetSourceConfig failed, taskId:%d, err: %v", taskId, getTaskErr)
		return nil, getTaskErr
	}
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetChannel failed, taskId:%d, err: %v", taskId, getChannelErr)
		return nil, getChannelErr
	}
	oracleConns, connErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
	if connErr != nil {
		log.Errorf("GetChannel failed, taskId:%d, err: %v", taskId, connErr)
		return nil, connErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	defineMappingResponse, sourceErr := i.dsgAdaptor.SelectDefineMapping(ctx, dsg.SelectDefineMappingRequest{TID: taskInfo.IncrementId})
	if sourceErr != nil {
		log.Errorf("GetTableMapping failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, sourceErr)
		return nil, sourceErr
	}
	channelSchemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getTableErr != nil {
		log.Errorf("GetChannelSchemaTablesByTaskId failed, taskId:%d, err: %v", taskId, getTableErr)
		return nil, getTableErr
	}

	dsgTables := buildDsgTables(defineMappingResponse, req.TaskId)
	tmsTables := buildTmsTables(channelSchemaTables)

	dsgMissingTables, tmsMissingTables := lo.Difference(tmsTables, dsgTables)

	rsp := &message.VerifyTableMappingResp{
		IsTmsHasNoTables: len(channelSchemaTables) == 0,
		HasMissingTables: len(dsgMissingTables) > 0 || len(tmsMissingTables) > 0,
		DsgMissingTables: dsgMissingTables,
		TmsMissingTables: tmsMissingTables,
	}

	helper := &incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
		Adaptor:   i.dsgAdaptor,
	}

	slv, getVarErr := helper.GetOracleSupplementalLogVariables(ctx, oracleConns)
	if getVarErr != nil {
		log.Errorf("GetOracleSupplementalLogVariables failed, taskId:%d, err: %v", taskId, getVarErr)
		return nil, getVarErr
	}
	log.Debugf("GetOracleSupplementalLogVariables, taskId:%d, SupplementalLogVariables[Min:%t, Pk:%t, Uk:%t, Fk:%t, All:%s,ForceLogging:%t]", taskId, slv.SupplementalLogDataMin, slv.SupplementalLogDataPk, slv.SupplementalLogDataUi, slv.SupplementalLogDataFk, slv.SupplementalLogDataAll, slv.ForceLogging)

	// 如果数据库的SupplementalLogDataAll为YES，那么不需要进行表级别的校验
	if slv.IsSupplementalLogValid() {
		log.Infof("GetOracleSupplementalLogVariables, taskId:%d, SupplementalLogDataAll is YES or PK and Unique is YES, skip table level check", taskId)
	} else {
		log.Infof("GetOracleSupplementalLogVariables, taskId:%d, SupplementalLogDataAll or PK and Unique is not ready, need to check table level", taskId)

		// 如果数据库的SupplementalLogDataAll为NO，那么需要进行表级别的校验
		logGroups, getLogErr := helper.GetOracleLogGroups(ctx, oracleConns)
		if getLogErr != nil {
			log.Errorf("GetOracleLogGroups failed,taskId:%d, err:%v", taskId, getLogErr)
			return nil, getLogErr
		}
		missingOracleSupplementalLogTables := helper.GetMissingOracleSupplementalLogTables(tmsTables, logGroups)

		rsp.HasMissingOracleSupplementalLog = len(missingOracleSupplementalLogTables) > 0
		rsp.MissingOracleSupplementalLogTables = missingOracleSupplementalLogTables
	}

	dbpsView, getViewErr := helper.GetOracleDBPSViews(ctx, oracleConns)
	if getViewErr != nil {
		log.Errorf("GetOracleDBPSViews failed,taskId:%d, err:%v", taskId, getViewErr)
		return nil, getViewErr
	}
	rsp.MissingDBPSView, rsp.HasMissingDBPSView = dbpsView.ValidateMissingView()

	return rsp, nil
}

func buildTmsTables(channelSchemaTables []*channel.ChannelSchemaTable) []message.DsgTable {
	tmsTables := make([]message.DsgTable, 0, len(channelSchemaTables))
	for _, table := range channelSchemaTables {
		tmsTables = append(tmsTables, message.DsgTable{
			SchemaNameS: table.SchemaNameS,
			TableNameS:  table.TableNameS,
			SchemaNameT: strings.ToLower(table.SchemaNameT),
			TableNameT:  strings.ToLower(table.TableNameT),
		})
	}
	return tmsTables
}

func buildDsgTables(defineMappingResponse dsg.SelectDefineMappingResponse, taskId int) []message.DsgTable {
	dsgTables := make([]message.DsgTable, 0)
	for _, mapRow := range strings.Split(defineMappingResponse.DataInfo.Map, "\n") {
		if strings.TrimSpace(mapRow) == "" {
			continue
		}
		rowToken := strings.Split(mapRow, ":")
		if len(rowToken) != 2 {
			log.Warnf("GetTableMapping failed, taskId:%d, invalid map row: %s", taskId, mapRow)
			continue
		}
		sourceInfos := strings.Split(rowToken[0], ".")
		targetInfos := strings.Split(rowToken[1], ".")
		if len(sourceInfos) != 2 || len(targetInfos) != 2 {
			log.Warnf("GetTableMapping failed, taskId:%d, invalid map row: %s", taskId, mapRow)
			continue
		}
		dsgTables = append(dsgTables, message.DsgTable{
			SchemaNameS: strings.TrimSpace(sourceInfos[0]),
			TableNameS:  strings.TrimSpace(sourceInfos[1]),
			SchemaNameT: strings.TrimSpace(targetInfos[0]),
			TableNameT:  strings.TrimSpace(targetInfos[1]),
		})
	}
	return dsgTables
}

func (i *Service) UpdateSourceConfig(ctx context.Context, req *message.UpdateSourceConfigReq) (*message.UpdateSourceConfigResp, error) {
	log.Infof("UpdateSourceConfig request: %v", req)

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("UpdateSourceConfig failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	dsgReq := dsg.WriteSourceDataSourceIniRequest{
		FileName: "vm",
		TID:      taskInfo.IncrementId,
		Content:  req.VM,
	}
	updateRsp, updateSourceErr := i.dsgAdaptor.WriteSourceDataSourceIni(ctx, dsgReq)
	if updateSourceErr != nil {
		log.Errorf("UpdateSourceConfig failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, updateSourceErr)
		return nil, updateSourceErr
	}

	return &message.UpdateSourceConfigResp{
		BaseDsgResp: buildBaseResp(updateRsp.BaseResponse),
	}, nil
}

func (i *Service) UpdateTargetConfig(ctx context.Context, req *message.UpdateTargetConfigReq) (*message.UpdateTargetConfigResp, error) {
	log.Infof("UpdateTargetConfig request: %v", req)

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("UpdateTargetConfig failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	newYloader := buildNewYLoader(req)

	helper := incrementpkg.DsgTaskHelper{
		UserId:    i.userId,
		Adaptor:   i.dsgAdaptor,
		ChannelId: taskInfo.ChannelId,
		TaskId:    taskInfo.TaskID,
	}
	writeRsp, writeErr := helper.WriteYLoader(ctx, taskInfo.IncrementId, req.OldYLoader, newYloader)
	if writeErr != nil {
		log.Errorf("UpdateTargetConfig failed, WriteYLoader failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, writeErr)
		return nil, writeErr
	}

	return &message.UpdateTargetConfigResp{
		BaseDsgResp: buildBaseResp(writeRsp.BaseResponse),
	}, nil
}

func buildNewYLoader(req *message.UpdateTargetConfigReq) message.YLoader {
	newYloader := req.NewYLoader

	wrapper := req.TmsYLoaderWrapper

	var realCopy string
	var realSqlMode string
	var maxBrows string

	maxBrows = fmt.Sprintf("500,%d,%d,%d,0",
		wrapper.TmsRealInsertBindNum,
		wrapper.TmsRealDeleteBindNum,
		wrapper.TmsRealUpdateBindNum)
	switch wrapper.TmsRealInsertMode {
	case constants.INCREMENT_INSERT_COPY_MODE:
		realSqlMode = constants.INCREMENT_NO
		realCopy = constants.INCREMENT_YES
	case constants.INCREMENT_INSERT_SQL_MODE:
		realSqlMode = constants.INCREMENT_YES
		realCopy = constants.INCREMENT_NO
	case constants.INCREMENT_INSERT_BATCH_SQL_MODE:
		realSqlMode = constants.INCREMENT_NO
		realCopy = constants.INCREMENT_NO
	default:
		realSqlMode = constants.INCREMENT_NO
		realCopy = constants.INCREMENT_NO
	}

	newYloader.MaxBrows = maxBrows
	newYloader.RealCopy = realCopy
	newYloader.RealSqlMode = realSqlMode
	return newYloader
}

func (i *Service) GetTargetConfig(ctx context.Context, req *message.GetTargetConfigReq) (*message.GetTargetConfigResp, error) {
	log.Infof("GetTargetConfig request: %v", req)

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetTargetConfig failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	if taskInfo.IncrementId == 0 {
		return nil, errors.NewError(errors.TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY, "DSG")
	}

	targetIniRsp, getTargetErr := i.dsgAdaptor.ReadTargetDataSourceIni(ctx, dsg.ReadTargetDataSourceIniRequest{FileName: "yloader.ini", TID: taskInfo.IncrementId})
	if getTargetErr != nil {
		log.Errorf("GetTargetConfig failed, taskId:%d, dsgId:%d, err: %v", req.TaskId, taskInfo.IncrementId, getTargetErr)
		return nil, getTargetErr
	}

	command := buildTmsYLoaderCommand(targetIniRsp.DataInfo.Form)

	return &message.GetTargetConfigResp{
		YLoader:           buildYLoaderMessage(targetIniRsp.DataInfo.Form),
		YLoaderContent:    targetIniRsp.DataInfo.Res,
		TmsYLoaderWrapper: command,
	}, nil

}

func buildTmsYLoaderCommand(yloader dsg.TargetDataSourceIni) message.YLoaderWrapper {
	var tmsRealInsertBindNum int
	var tmsRealDeleteBindNum int
	var tmsRealUpdateBindNum int
	var tmsRealInsertMode string

	maxBrowTokens := strings.Split(yloader.MaxBrows, ",")

	if len(maxBrowTokens) == 5 {
		tmsRealInsertBindNum, _ = parse.ParseInt(maxBrowTokens[1])
		tmsRealDeleteBindNum, _ = parse.ParseInt(maxBrowTokens[2])
		tmsRealUpdateBindNum, _ = parse.ParseInt(maxBrowTokens[3])
	}

	// 判断bind num并给出默认值
	fixBindNum := func(bindNum int) int {
		if bindNum == 0 {
			return 500
		}
		return bindNum
	}

	// 清空字符串并转换为小写
	trimLower := func(s string) string {
		return strings.ToLower(strings.TrimSpace(s))
	}

	// realSqlMode的优先级比realCopy高
	realSqlMode, realCopy := trimLower(yloader.RealSqlMode), trimLower(yloader.RealCopy)
	if realSqlMode == constants.INCREMENT_YES {
		tmsRealInsertMode = constants.INCREMENT_INSERT_SQL_MODE
	} else {
		if realCopy == constants.INCREMENT_YES {
			tmsRealInsertMode = constants.INCREMENT_INSERT_COPY_MODE
		} else {
			tmsRealInsertMode = constants.INCREMENT_INSERT_BATCH_SQL_MODE
		}
	}

	command := message.YLoaderWrapper{
		TmsRealInsertBindNum: fixBindNum(tmsRealInsertBindNum),
		TmsRealDeleteBindNum: fixBindNum(tmsRealDeleteBindNum),
		TmsRealUpdateBindNum: fixBindNum(tmsRealUpdateBindNum),
		TmsRealInsertMode:    tmsRealInsertMode,
	}

	return command
}

func buildYLoaderMessage(rsp dsg.TargetDataSourceIni) message.YLoader {
	return message.YLoader{
		RealDispFwait:              rsp.RealDispFwait,
		FullRepairRealQuit:         rsp.FullRepairRealQuit,
		UsedMap:                    rsp.UsedMap,
		DelayInterval:              rsp.DelayInterval,
		RealDispSize:               rsp.RealDispSize,
		FullCopyUnusedSize:         rsp.FullCopyUnusedSize,
		UpdatePkukCol:              rsp.UpdatePkukCol,
		WaitFreeSize:               rsp.WaitFreeSize,
		WaitRetry:                  rsp.WaitRetry,
		SQLTrans:                   rsp.SQLTrans,
		RealInsertErrFdelete:       rsp.RealInsertErrFdelete,
		ErrorRetryDdl:              rsp.ErrorRetryDdl,
		TableCreateFullIdxEnd:      rsp.TableCreateFullIdxEnd,
		AddcolRowidInvisible:       rsp.AddcolRowidInvisible,
		ErrorRetry:                 rsp.ErrorRetry,
		FilterString00:             rsp.FilterString00,
		PackForJava:                rsp.PackForJava,
		TableExistsFullDo:          rsp.TableExistsFullDo,
		TableExistsRealDo:          rsp.TableExistsRealDo,
		DbLang:                     rsp.DbLang,
		RealLoadCompleted:          rsp.RealLoadCompleted,
		RealDispFcount:             rsp.RealDispFcount,
		CountsumDetailDb:           rsp.CountsumDetailDb,
		FullInsertErrFdelete:       rsp.FullInsertErrFdelete,
		FullCopy:                   rsp.FullCopy,
		FullSingleParr:             rsp.FullSingleParr,
		CountsumIntervalDb:         rsp.CountsumIntervalDb,
		RealDispMode:               rsp.RealDispMode,
		CtypeIfxDecimal:            rsp.CtypeIfxDecimal,
		FullCindexErrorRetry:       rsp.FullCindexErrorRetry,
		CreateTableFcol:            rsp.CreateTableFcol,
		SesBlen:                    rsp.SesBlen,
		DbName:                     rsp.DbName,
		XsqlRows:                   rsp.XsqlRows,
		FullLoadCompleted:          rsp.FullLoadCompleted,
		MaxBrows:                   rsp.MaxBrows,
		RealDdlFtable:              rsp.RealDdlFtable,
		MapCname:                   rsp.MapCname,
		TableCreateRealConstraint:  rsp.TableCreateRealConstraint,
		RowmapType:                 rsp.RowmapType,
		TableCreateFullIndex:       rsp.TableCreateFullIndex,
		MapTname:                   rsp.MapTname,
		Trans:                      rsp.Trans,
		DbType:                     rsp.DbType,
		SQLErrChange2Bind:          rsp.SQLErrChange2Bind,
		CtypeNumberAdjust:          rsp.CtypeNumberAdjust,
		RealThread:                 rsp.RealThread,
		RealDispRows:               rsp.RealDispRows,
		TableCreateRealIndex:       rsp.TableCreateRealIndex,
		LangGbk2Gb18030:            rsp.LangGbk2Gb18030,
		CountsumTime:               rsp.CountsumTime,
		DbPwd:                      rsp.DbPwd,
		CountsumDetail:             rsp.CountsumDetail,
		TableCreateFullConstraint:  rsp.TableCreateFullConstraint,
		UsedQuotes:                 rsp.UsedQuotes,
		FullInsertFdelete:          rsp.FullInsertFdelete,
		DbHost:                     rsp.DbHost,
		TableCreateFullIndexThread: rsp.TableCreateFullIndexThread,
		Map:                        rsp.Map,
		DataFormat:                 rsp.DataFormat,
		RealDispFi:                 rsp.RealDispFi,
		FullThread:                 rsp.FullThread,
		EncryptPwd:                 rsp.EncryptPwd,
		FullSQLMode:                rsp.FullSQLMode,
		FilterString:               rsp.FilterString,
		TableCreateFullIdxPar:      rsp.TableCreateFullIdxPar,
		UpdateNobefore:             rsp.UpdateNobefore,
		TableExistsCheck:           rsp.TableExistsCheck,
		Home:                       rsp.Home,
		TableCreateReal:            rsp.TableCreateReal,
		QueueName:                  rsp.QueueName,
		OutputLog2Db:               rsp.OutputLog2Db,
		DefineConfig:               rsp.DefineConfig,
		Service:                    rsp.Service,
		FullCopySize:               rsp.FullCopySize,
		DbUser:                     rsp.DbUser,
		TableCreateFull:            rsp.TableCreateFull,
		CfgFilename:                rsp.CfgFilename,
		CreateTableAddcol:          rsp.CreateTableAddcol,
		RealUpdateFobjn:            rsp.RealUpdateFobjn,
		RealInsertFobjn:            rsp.RealInsertFobjn,
		IdbLang:                    rsp.IdbLang,
		OutputRba:                  rsp.OutputRba,
		McSchema:                   rsp.McSchema,
		CountsumTotalDb:            rsp.CountsumTotalDb,
		CountsumFullDb:             rsp.CountsumFullDb,
		UpdateUsedDi:               rsp.UpdateUsedDi,
		RealInsertFdMode:           rsp.RealInsertFdMode,
		RealInsertFdelete:          rsp.RealInsertFdelete,
		UpdateErrUsedDi:            rsp.UpdateErrUsedDi,
		AddcolRowidColumn:          rsp.AddcolRowidColumn,
		AddcolRowid2Uk:             rsp.AddcolRowid2Uk,
		UpdateFilterCol:            rsp.UpdateFilterCol,
		UnusedPkuk:                 rsp.UnusedPkuk,
		FullLangFromForce:          rsp.FullLangFromForce,
		CtypeVarchar2Binary:        rsp.CtypeVarchar2Binary,
		CtypeVarchar2Long:          rsp.CtypeVarchar2Long,
		TableCreateIncPk:           rsp.TableCreateIncPk,
		FullDdlFilter:              rsp.FullDdlFilter,
		RealDdlFobjn:               rsp.RealDdlFobjn,
		RealDeleteFobjn:            rsp.RealDeleteFobjn,
		RealCopy:                   rsp.RealCopy,
		RealSqlMode:                rsp.RealSqlMode,
		FullDdlFtable:              rsp.FullDdlFtable,
		FullInsertFtable:           rsp.FullInsertFtable,
		RealDeleteFtable:           rsp.RealDeleteFtable,
		Ftable:                     rsp.Ftable,
		RealUpdateFtable:           rsp.RealUpdateFtable,
		RealInsertFtable:           rsp.RealInsertFtable,
	}
}

func (i *Service) GetToken(ctx context.Context, req *message.GetTokenReq) (*message.GetTokenResp, error) {
	log.Infof("GetToken request: %v", req)

	var tokenRsp dsg.GetTokenResponse
	var getTokenErr error

	if req.EnableSimulate {
		tokenRsp, getTokenErr = i.dsgAdaptor.GetSimulationToken(ctx, dsg.GetTokenRequest{
			UserAgent:      req.UserAgent,
			AcceptLanguage: req.AcceptLanguage,
			AcceptEncoding: req.AcceptEncoding,
			AcceptCharset:  req.AcceptCharset,
		})
	} else {
		tokenRsp, getTokenErr = i.dsgAdaptor.GetToken(ctx, dsg.GetTokenRequest{})
	}

	if getTokenErr != nil {
		log.Errorf("GetToken failed, err: %v", getTokenErr)
		return nil, getTokenErr
	}
	return &message.GetTokenResp{
		Token: tokenRsp.DataInfo,
	}, nil
}

func (i *Service) ListPhysicalSubSystem(ctx context.Context, req *message.ListPhysicalSubSystemReq) (*message.ListPhysicalSubSystemResp, error) {
	log.Infof("ListPhysicalSubSystemTask request: %v", req)
	apiReq := dsg.QueryPhysicalSubSystemRequest{
		UserID:           i.userId,
		Offset:           (req.Page - 1) * req.PageSize,
		Limit:            req.PageSize,
		GroupMachineName: req.GroupMachineName,
		GroupMachineDesc: req.GroupMachineDesc,
	}

	apiResp, err := i.dsgAdaptor.QueryPhysicalSubSystem(ctx, apiReq)
	if err != nil {
		log.Errorf("ListPhysicalSubSystemTask failed, err: %v", err)
		return nil, err
	}

	systems := lo.Map(apiResp.DataInfo.Rows, buildPhysicalSubSystemMessage())
	resp := &message.ListPhysicalSubSystemResp{
		Total:              apiResp.DataInfo.Total,
		PhysicalSubSystems: systems,
	}
	return resp, nil
}

func (i *Service) ListHost(ctx context.Context, req *message.ListDataSourceHostReq) (*message.ListDataSourceHostResp, *message.Page, error) {
	log.Infof("list datasource host service request: %v", req.PageRequest)
	rw := models.GetDatasourceReaderWriter()
	hosts, total, err := rw.ListHost(ctx, req.Page, req.PageSize, req.HostIP, req.HostName, req.OsName)
	if err != nil {
		log.Errorf("list datasource host failed, error: %v", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}

	hostMessages := lo.Map(hosts, buildDataSourceHostMessageFromModel)
	return &message.ListDataSourceHostResp{Hosts: hostMessages}, page, nil
}

func (i *Service) GetHost(ctx context.Context, req *message.GetHostReq) (*message.OpMachineConf, error) {
	log.Infof("get datasource host service request: %v", req)
	host, err := models.GetDatasourceReaderWriter().GetHost(ctx, req.MacID)
	if err != nil {
		log.Errorf("get host failed, macId is %d, error: %v", req.MacID, err)
		return nil, err
	}
	hostMessage := buildDataSourceHostMessageFromModel(host, 0)
	return hostMessage, nil
}

func (i *Service) GetHostUsedPorts(ctx context.Context, req *message.GetHostUsedPortsReq) (*message.GetHostUsedPortsResp, error) {
	log.Infof("get host usedports service request: %v", req)

	hostPorts, getPortErr := i.dsgAdaptor.GetHostPorts(ctx, dsg.GetHostPortsRequest{
		HostIP: req.HostIP,
		Limit:  10000,
		Offset: 0,
	})
	if getPortErr != nil {
		log.Errorf("get host usedports failed, hostIp is %s, error: %v", req.HostIP, getPortErr)
		return nil, getPortErr
	}

	var usedPortMin, usedPortMax int

	for _, dsgPort := range hostPorts.DataInfo.Rows {
		if usedPortMin == 0 {
			usedPortMin = dsgPort.Port
		}
		if usedPortMax == 0 {
			usedPortMax = dsgPort.Port
		}
		if dsgPort.Port < usedPortMin {
			usedPortMin = dsgPort.Port
		}
		if dsgPort.Port > usedPortMax {
			usedPortMax = dsgPort.Port
		}
	}

	return &message.GetHostUsedPortsResp{
		BaseDsgResp: buildBaseResp(hostPorts.BaseResponse),
		Ports:       buildHostPortMessage(hostPorts.DataInfo.Rows),
		UsedPortMax: usedPortMax,
		UsedPortMin: usedPortMin,
	}, nil
}

func buildHostPortMessage(hostPorts []dsg.HostPort) []message.HostPort {
	ports := make([]message.HostPort, 0, len(hostPorts))
	for _, hostPort := range hostPorts {
		ports = append(ports, message.HostPort{
			Port:     hostPort.Port,
			Describe: hostPort.Describe,
			Notes:    hostPort.Notes,
		})
	}
	return ports
}

func (i *Service) SaveHost(ctx context.Context, req *message.SaveHostReq) (*message.SaveHostResp, error) {
	log.Infof("save datasource host service request: %v", req)

	createTime := req.CreateTime
	if createTime.IsZero() || timeutil.IsTMSNullTime(createTime) {
		createTime = time.Now()
	}

	helper := &incrementpkg.DsgTaskHelper{
		UserId:  i.userId,
		Adaptor: i.dsgAdaptor,
	}
	physicalSubSystemID, getIDErr := helper.MustGetTMSSubSystemID(ctx)
	if getIDErr != nil {
		log.Errorf("get physicalSubSystemID failed, error: %v", getIDErr)
		return nil, getIDErr
	}

	createDsgHostErr := helper.CreateHostIfNotExist(ctx, physicalSubSystemID, req.HostIP, req.HostName, req.OsName, req.BasePath)
	if createDsgHostErr != nil {
		log.Errorf("create dsg host failed, hostIp:%s, hostname:%s, error: %v", req.HostIP, req.HostName, createDsgHostErr)
		return nil, createDsgHostErr
	}
	updateDsgHostPortErr := helper.UpdateHostPorts(ctx, physicalSubSystemID, req.HostIP, req.HostName, req.PortMin, req.PortMax)
	if updateDsgHostPortErr != nil {
		log.Errorf("create dsg host succes, update dsg host ports failed, hostIp:%s, hostname:%s, error: %v", req.HostIP, req.HostName, updateDsgHostPortErr)
		return nil, updateDsgHostPortErr
	}

	host := &datasource.OpMachineConf{
		MacId:       req.MacId,
		HostName:    req.HostName,
		HostIP:      req.HostIP,
		CreateTime:  createTime,
		PortMax:     req.PortMax,
		PortMin:     req.PortMin,
		PortHasUsed: req.PortHasUsed,
		BasePath:    req.BasePath,
		OsName:      req.OsName,
		Allocation:  req.Allocation,
		Entity:      &modelcommon.Entity{Comment: req.Comment},
	}
	_, err := models.GetDatasourceReaderWriter().SaveHost(ctx, host)
	if err != nil {
		log.Errorf("save host in dsg success, but save into tms failed, macId is %d, error: %v", req.MacId, err)
		return nil, err
	}
	return &message.SaveHostResp{}, nil
}

func (i *Service) DeleteHost(ctx context.Context, req *message.DeleteHostReq) (*message.DeleteHostResp, error) {
	log.Infof("delete datasource host service request: %v", req)
	err := models.GetDatasourceReaderWriter().DeleteHost(ctx, req.MacId)
	if err != nil {
		log.Errorf("delete host failed, macId is %d, error: %v", req.MacId, err)
		return nil, err
	}
	return &message.DeleteHostResp{}, nil
}

func (i *Service) DownloadSupplementalLog(ctx context.Context, req *message.DownloadSupplementalLogReq) (*message.DownloadSupplementalLogResp, error) {
	log.Infof("DownloadSupplementalLog service request : %v", req)
	taskId := req.TaskId

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("DownloadSupplementalLog, get task info failed, taskId:%d, err:%s", taskId, getTaskErr)
		return nil, getTaskErr
	}

	channelSchemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
	if getTableErr != nil {
		log.Errorf("DownloadSupplementalLog, get channel schema tables failed, taskId:%d, err:%s", taskId, getTableErr)
		return nil, getTableErr
	}

	workingDir, getWdErr := os.Getwd()
	if getWdErr != nil {
		log.Errorf("DownloadSupplementalLog, get pwd failed, taskId:%d, err:%s", taskId, getWdErr)
		return nil, getWdErr
	}

	timestamp := time.Now().Unix()
	resp := &message.DownloadSupplementalLogResp{}
	exportFileDir := path.Join(workingDir, "data/schema_table_export")
	exportFileName := fmt.Sprintf("task_%d_tables_%d.sql", taskId, timestamp)
	resp.ExportFilePath = path.Join(exportFileDir, exportFileName)

	defaultSQLs := []string{
		`ALTER DATABASE FORCE LOGGING;`, // 必须强制开启日志
		`CREATE OR REPLACE VIEW DBPS_XKCCLE AS SELECT * FROM sys.x$kccle; `,
		`CREATE OR REPLACE VIEW DBPS_XKCCLH AS SELECT * FROM sys.x$kcclh; `,
		`CREATE OR REPLACE VIEW DBPS_XKCCCF AS SELECT * FROM sys.x$kcccf; `,
		`CREATE OR REPLACE VIEW DBPS_XKCCCP AS SELECT * FROM sys.x$kcccp; `,
		`CREATE OR REPLACE VIEW DBPS_XKCCDI AS SELECT * FROM sys.x$kccdi; `,
		`CREATE OR REPLACE VIEW DBPS_XKCRMF AS SELECT * FROM sys.x$kcrmf; `,
		`CREATE OR REPLACE VIEW DBPS_XKTUXE AS SELECT * FROM sys.x$ktuxe; `,
		`CREATE OR REPLACE VIEW DBPS_XLE    AS SELECT * FROM sys.x$le;    `,
		`CREATE OR REPLACE VIEW DBPS_XBH    AS SELECT * FROM sys.x$bh;    `,
	}

	sqls := make([]string, 0, len(channelSchemaTables)+len(defaultSQLs))
	sqls = append(sqls, defaultSQLs...)

	for _, channelSchemaTable := range channelSchemaTables { // PK/UK已经足够，ALL的情况时针对下游是Kafka或其他特殊场景，需要有完整记录
		sqls = append(sqls, fmt.Sprintf("ALTER TABLE %s.%s ADD SUPPLEMENTAL LOG DATA(PRIMARY KEY,UNIQUE) COLUMNS;", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS))
	}

	log.Infof("export tables data to sql file, taskId:%d, sqlNum:%d, fileName:%s, fileDir:%s", taskId, len(sqls), exportFileName, exportFileDir)
	writeErr := file.OpenAndWriteToFile(exportFileDir, exportFileName, sqls)
	if writeErr != nil {
		log.Errorf("export table column data to csv file failed, taskId:%d, error: %v", taskId, writeErr)
		return nil, writeErr
	}

	return resp, nil
}

func buildDataSourceHostMessageFromModel(model *datasource.OpMachineConf, _ int) *message.OpMachineConf {
	return &message.OpMachineConf{
		MacId:       model.MacId,
		HostName:    model.HostName,
		HostIP:      model.HostIP,
		CreateTime:  model.CreateTime,
		PortMax:     model.PortMax,
		PortMin:     model.PortMin,
		PortHasUsed: model.PortHasUsed,
		BasePath:    model.BasePath,
		OsName:      model.OsName,
		BaseFields:  common.BuildBaseFieldsMessageFromModel(model.Entity),
		Allocation:  model.Allocation,
	}
}
