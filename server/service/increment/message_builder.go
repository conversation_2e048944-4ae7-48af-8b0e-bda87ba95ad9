package increment

import (
	"gitee.com/pingcap_enterprise/tms/pkg/dsg"
	"gitee.com/pingcap_enterprise/tms/server/message"
)

func buildDsgTaskMessage() func(row dsg.Task, _ int) message.DsgTask {
	return func(row dsg.Task, _ int) message.DsgTask {
		return message.DsgTask{
			TaskID:        row.TID,
			TaskName:      row.TaskName,
			TaskParam:     row.TaskParm,
			TaskType:      row.TaskType,
			TaskModel:     row.TaskModel,
			TaskMethod:    row.TaskMethod,
			InstallStatus: row.InstallStatus,
			DelStatus:     row.DelStatus,
			TaskStatus:    row.TaskStatus,
			ReceivePort:   row.ReceivePort,
			AnalysisPort:  row.AnalysisPort,
			TarPath:       row.TarPath,
			SourPath:      row.SourPath,
		}
	}
}

func buildPhysicalSubSystemMessage() func(row dsg.PhysicalSubSystem, _ int) message.PhysicalSubSystem {
	return func(row dsg.PhysicalSubSystem, _ int) message.PhysicalSubSystem {
		return message.PhysicalSubSystem{
			CreateTime:       row.CreateTime,
			GroupMachineName: row.GroupMachineName,
			GroupMachineDesc: row.GroupMachineDesc,
			ID:               row.ID,
			GroupType:        row.GroupType,
		}
	}
}

func buildHostMessage() func(row dsg.Host, _ int) message.Host {
	return func(row dsg.Host, _ int) message.Host {
		return message.Host{
			HostIP:           row.HostIP,
			Allocation:       row.Allocation,
			PsName:           row.PsName,
			XcmpFlag:         row.XcmpFlag,
			IsAllocation:     row.IsAllocation,
			MacID:            row.MacID,
			HostType:         row.HostType,
			AffDataCenter:    row.AffDataCenter,
			SecretName:       row.SecretName,
			HostStat:         row.HostStat,
			CreateTime:       row.CreateTime,
			AllocationPort:   row.AllocationPort,
			PhysicalSystemID: row.PsID,
			OSName:           row.OsName,
			HostName:         row.HostName,
		}
	}
}

func buildBaseResp(apiResp dsg.BaseResponse) message.BaseDsgResp {
	return message.BaseDsgResp{
		Message:  apiResp.Message,
		StatFlag: apiResp.StatFlag.Int(),
	}
}
