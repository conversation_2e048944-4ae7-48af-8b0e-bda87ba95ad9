package template

import (
	"context"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/template"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

func (s *Service) CreateTabColMapRule(ctx context.Context, req *message.CreateOrUpdateTabColMapRuleReq) (*message.CreateOrUpdateTabColMapRuleResp, error) {
	log.Infof("CreateTabColMapRule service request: %v", req.TabColMapRule)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		tabColMapRuleModel, createTabColMapRuleModelErr := models.GetTemplateReaderWriter().CreateTablecolMapRule(transactionCtx, buildTabColMapRuleModelFromMessage(req.TabColMapRule))
		if createTabColMapRuleModelErr != nil {
			log.Errorf("create table column map rule failed. data:%v, error: %v", tabColMapRuleModel, createTabColMapRuleModelErr)
			return createTabColMapRuleModelErr
		}
		templateRule := &template.TmplateRule{MapRuleId: tabColMapRuleModel.TabcolmapRuleId, TmplateId: req.TmplateId}
		_, createTemplateRuleErr := models.GetTemplateReaderWriter().CreateTemplateRule(transactionCtx, templateRule)
		if createTemplateRuleErr != nil {
			log.Errorf("when create table column map rule, create template rule failed. data:%v, error: %v", templateRule, createTemplateRuleErr)
			return createTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("create table column map rule failed. templateId:%d, req:%v, error:%v", req.TmplateId, req.TabColMapRule, err)
		return nil, err
	}
	log.Infof("create table column map rule successfully.")
	return &message.CreateOrUpdateTabColMapRuleResp{}, nil

}

func (s *Service) UpdateTabColMapRule(ctx context.Context, req *message.CreateOrUpdateTabColMapRuleReq) (*message.CreateOrUpdateTabColMapRuleResp, error) {
	log.Infof("UpdateTabColMapRule service request: %v", req.TabColMapRule)
	tabColMapRuleModel := buildTabColMapRuleModelFromMessage(req.TabColMapRule)
	_, err := models.GetTemplateReaderWriter().UpdateTablecolMapRule(ctx, tabColMapRuleModel)
	if err != nil {
		log.Errorf("update table column map rule failed. data:%v, error: %v", tabColMapRuleModel, err)
		return nil, err
	}
	log.Infof("update table column map rule successfully.")
	return &message.CreateOrUpdateTabColMapRuleResp{}, nil

}

func (s *Service) DeleteTabColMapRuleById(ctx context.Context, req *message.DeleteTabColMapRuleByIdReq) (*message.DeleteTabColMapRuleByIdResp, error) {
	log.Infof("DeleteTabColMapRuleById service request: %v", req)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		deleteTabColMapRuleErr := models.GetTemplateReaderWriter().DeleteTablecolMapRuleById(ctx, req.TabColMapRuleId)
		if deleteTabColMapRuleErr != nil {
			log.Errorf("delete table column map rule by id failed. id:%d, error: %v", req.TabColMapRuleId, deleteTabColMapRuleErr)
			return deleteTabColMapRuleErr
		}
		deleteTemplateRuleErr := models.GetTemplateReaderWriter().DeleteTemplateRule(transactionCtx, req.TmplateId, req.TabColMapRuleId)
		if deleteTemplateRuleErr != nil {
			log.Errorf("when delete table column map rule, delete template rule failed. TemplateId:%d, ruleId:%d, error: %v", req.TmplateId, req.TabColMapRuleId, deleteTemplateRuleErr)
			return deleteTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("delete table column map rule failed. templateId:%d, ruleId:%v, error:%v", req.TmplateId, req.TabColMapRuleId, err)
		return nil, err
	}
	log.Infof("delete table column map rule by id successfully.")
	return &message.DeleteTabColMapRuleByIdResp{}, nil
}

func (s *Service) GetTabColMapRuleById(ctx context.Context, req *message.GetTabColMapRuleReq) (*message.GetTabColMapRuleResp, error) {
	log.Infof("GetTabColMapRuleById service request: %d", req.TabColMapRuleId)
	rule, err := models.GetTemplateReaderWriter().GetTablecolMapRuleById(ctx, req.TabColMapRuleId)
	if err != nil {
		log.Errorf("get table column map rule failed. id:%d, error: %v", req.TabColMapRuleId, err)
		return nil, err
	}
	log.Infof("get table column map rule successfully.")
	return &message.GetTabColMapRuleResp{TabColMapRule: buildTabColMapRuleMessageFromModel(rule)}, nil
}

func (s *Service) ListTabColMapRules(ctx context.Context, req *message.ListTabColMapRulesReq) (*message.ListTabColMapRulesResp, *message.Page, error) {
	log.Infof("ListTabColMapRules service request received, req:%v", req)
	rules, total, err := models.GetTemplateReaderWriter().ListTabColMapRules(ctx, req.TmplateId, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("list table column map rules failed. err:", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	ruleMessages := make([]*message.TabColMapRule, 0, len(rules))
	for _, rule := range rules {
		ruleMessages = append(ruleMessages, buildTabColMapRuleMessageFromModel(rule))
	}
	log.Infof("list table column map rule size is %v", len(rules))
	return &message.ListTabColMapRulesResp{TabColMapRules: ruleMessages, TmplateId: req.TmplateId}, page, nil
}

func (s *Service) CreateObjMapRule(ctx context.Context, req *message.CreateOrUpdateObjMapRuleReq) (*message.CreateOrUpdateObjMapRuleResp, error) {
	log.Infof("CreateObjMapRule service request: %v", req.ObjMapRule)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		objMapRuleModel, createObjMapRuleErr := models.GetTemplateReaderWriter().CreateObjMapRule(ctx, buildObjMapRuleModelFromMessage(req.ObjMapRule))
		if createObjMapRuleErr != nil {
			log.Errorf("create object map rule failed. data:%v, error: %v", objMapRuleModel, createObjMapRuleErr)
			return createObjMapRuleErr
		}
		templateRule := &template.TmplateRule{MapRuleId: objMapRuleModel.ObjmapRuleId, TmplateId: req.TmplateId}
		_, createTemplateRuleErr := models.GetTemplateReaderWriter().CreateTemplateRule(transactionCtx, templateRule)
		if createTemplateRuleErr != nil {
			log.Errorf("when create object map rule, create template rule failed. data:%v, error: %v", templateRule, createTemplateRuleErr)
			return createTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("create object map rule failed. data:%v, error: %v", req.ObjMapRule, err)
		return nil, err
	}
	log.Infof("create object map rule successfully.")
	return &message.CreateOrUpdateObjMapRuleResp{}, nil
}

func (s *Service) UpdateObjMapRule(ctx context.Context, req *message.CreateOrUpdateObjMapRuleReq) (*message.CreateOrUpdateObjMapRuleResp, error) {
	log.Infof("UpdateObjMapRule service request: %v", req.ObjMapRule)
	objMapRuleModel := buildObjMapRuleModelFromMessage(req.ObjMapRule)
	_, err := models.GetTemplateReaderWriter().UpdateObjMapRule(ctx, objMapRuleModel)
	if err != nil {
		log.Errorf("update object map rule failed. data:%v, error: %v", objMapRuleModel, err)
		return nil, err
	}
	log.Infof("update object map rule successfully.")
	return &message.CreateOrUpdateObjMapRuleResp{}, nil
}

func (s *Service) DeleteObjMapRuleById(ctx context.Context, req *message.DeleteObjMapRuleByIdReq) (*message.DeleteObjMapRuleByIdResp, error) {
	log.Infof("DeleteObjMapRuleById service request: %v", req)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		deleteObjMapRuleErr := models.GetTemplateReaderWriter().DeleteObjMapRuleById(ctx, req.ObjMapRuleId)
		if deleteObjMapRuleErr != nil {
			log.Errorf("delete object map rule by id failed. id:%d, error: %v", req.ObjMapRuleId, deleteObjMapRuleErr)
			return deleteObjMapRuleErr
		}
		deleteTemplateRuleErr := models.GetTemplateReaderWriter().DeleteTemplateRule(transactionCtx, req.TmplateId, req.ObjMapRuleId)
		if deleteTemplateRuleErr != nil {
			log.Errorf("when delete object map rule by id, delete template rule failed. TemplateId:%d, ruleId:%d, error: %v", req.TmplateId, req.ObjMapRuleId, deleteTemplateRuleErr)
			return deleteTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("delete delete object map rule by id failed. templateId:%d, ruleId:%v, error:%v", req.TmplateId, req.ObjMapRuleId, err)
		return nil, err
	}
	log.Infof("delete object map rule by id successfully.")
	return &message.DeleteObjMapRuleByIdResp{}, nil
}

func (s *Service) GetObjMapRuleById(ctx context.Context, req *message.GetObjMapRuleByIdReq) (*message.GetObjMapRuleByIdResp, error) {
	log.Infof("GetObjMapRuleById service request: %v", req.ObjMapRuleId)
	rule, err := models.GetTemplateReaderWriter().GetObjMapRuleById(ctx, req.ObjMapRuleId)
	if err != nil {
		log.Errorf("get object map rule failed. id:%d, error: %v", req.ObjMapRuleId, err)
		return nil, err
	}
	log.Infof("get object map rule successfully.")
	return &message.GetObjMapRuleByIdResp{ObjMapRule: buildObjMapRuleMessageFromModel(rule)}, nil
}

func (s *Service) ListObjMapRules(ctx context.Context, req *message.ListObjMapRulesReq) (*message.ListObjMapRulesResp, *message.Page, error) {
	log.Infof("ListObjMapRules service request received")
	rules, total, err := models.GetTemplateReaderWriter().ListObjMapRules(ctx, req.TmplateId, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("list object map rules failed. err:", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	ruleMessages := make([]*message.ObjMapRule, 0, len(rules))
	for _, rule := range rules {
		ruleMessages = append(ruleMessages, buildObjMapRuleMessageFromModel(rule))
	}
	log.Infof("list object column map rule size is %v", len(rules))
	return &message.ListObjMapRulesResp{ObjMapRules: ruleMessages, TmplateId: req.TmplateId}, page, nil
}

func (s *Service) CreateSqlMapRule(ctx context.Context, req *message.CreateOrUpdateSqlMapRuleReq) (*message.CreateOrUpdateSqlMapRuleResp, error) {
	log.Infof("CreateSqlMapRule service request: %v", req.SqlMapRule)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		sqlMapRuleModel, createSqlMapRuleErr := models.GetTemplateReaderWriter().CreateSqlMapRule(ctx, buildSqlMapRuleModelFromMessage(req.SqlMapRule))
		if createSqlMapRuleErr != nil {
			log.Errorf("create sql map rule failed. data:%v, error: %v", sqlMapRuleModel, createSqlMapRuleErr)
			return createSqlMapRuleErr
		}
		templateRule := &template.TmplateRule{MapRuleId: sqlMapRuleModel.SqlmapRuleId, TmplateId: req.TmplateId}
		_, createTemplateRuleErr := models.GetTemplateReaderWriter().CreateTemplateRule(transactionCtx, templateRule)
		if createTemplateRuleErr != nil {
			log.Errorf("when create object map rule, create template rule failed. data:%v, error: %v", templateRule, createTemplateRuleErr)
			return createTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("create object map rule failed. data:%v, error: %v", req.SqlMapRule, err)
		return nil, err
	}
	log.Infof("create sql map rule successfully.")
	return &message.CreateOrUpdateSqlMapRuleResp{}, nil
}

func (s *Service) UpdateSqlMapRule(ctx context.Context, req *message.CreateOrUpdateSqlMapRuleReq) (*message.CreateOrUpdateSqlMapRuleResp, error) {
	log.Infof("UpdateSqlMapRule service request: %v", req.SqlMapRule)
	sqlMapRuleModel := buildSqlMapRuleModelFromMessage(req.SqlMapRule)
	_, err := models.GetTemplateReaderWriter().UpdateSqlMapRule(ctx, sqlMapRuleModel)
	if err != nil {
		log.Errorf("update sql map rule failed. data:%v, error: %v", sqlMapRuleModel, err)
		return nil, err
	}
	log.Infof("update object map rule successfully.")
	return &message.CreateOrUpdateSqlMapRuleResp{}, nil
}

func (s *Service) DeleteSqlMapRuleById(ctx context.Context, req *message.DeleteSqlMapRuleByIdReq) (*message.DeleteSqlMapRuleByIdResp, error) {
	log.Infof("DeleteSqlMapRuleById service request: %v", req)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		deleteSqlMapRuleErr := models.GetTemplateReaderWriter().DeleteSqlMapRuleById(ctx, req.SqlMapRuleId)
		if deleteSqlMapRuleErr != nil {
			log.Errorf("delete sql map rule failed. id:%d, error: %v", req.SqlMapRuleId, deleteSqlMapRuleErr)
			return deleteSqlMapRuleErr
		}
		deleteTemplateRuleErr := models.GetTemplateReaderWriter().DeleteTemplateRule(transactionCtx, req.TmplateId, req.SqlMapRuleId)
		if deleteTemplateRuleErr != nil {
			log.Errorf("when delete sql map rule, delete template rule failed. TemplateId:%d, ruleId:%d, error: %v", req.TmplateId, req.SqlMapRuleId, deleteTemplateRuleErr)
			return deleteTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("delete sql map rule failed. templateId:%d, ruleId:%v, error:%v", req.TmplateId, req.SqlMapRuleId, err)
		return nil, err
	}
	log.Infof("delete sql map rule successfully.")
	return &message.DeleteSqlMapRuleByIdResp{}, nil
}

func (s *Service) GetSqlMapRuleById(ctx context.Context, req *message.GetSqlMapRuleByIdReq) (*message.GetSqlMapRuleByIdResp, error) {
	log.Infof("GetSqlMapRuleById service request: %v", req.SqlMapRuleId)
	rule, err := models.GetTemplateReaderWriter().GetSqlMapRuleById(ctx, req.SqlMapRuleId)
	if err != nil {
		log.Errorf("get sql map rule failed. id:%d, error: %v", req.SqlMapRuleId, err)
		return nil, err
	}
	log.Infof("get sql map rule successfully.")
	return &message.GetSqlMapRuleByIdResp{SqlMapRule: buildSqlMapRuleMessageFromModel(rule)}, nil
}

func (s *Service) ListSqlMapRules(ctx context.Context, req *message.ListSqlMapRulesReq) (*message.ListSqlMapRulesResp, *message.Page, error) {
	log.Infof("ListSqlMapRules service request received")
	rules, total, err := models.GetTemplateReaderWriter().ListSqlMapRules(ctx, req.TmplateId, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("list sql map rules failed. err:", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	ruleMessages := make([]*message.SqlMapRule, 0, len(rules))
	for _, rule := range rules {
		ruleMessages = append(ruleMessages, buildSqlMapRuleMessageFromModel(rule))
	}
	log.Infof("list sql map rule size is %d", len(rules))
	return &message.ListSqlMapRulesResp{SqlMapRules: ruleMessages, TmplateId: req.TmplateId}, page, nil
}

func (s *Service) CreateColDefaultMapRule(ctx context.Context, req *message.CreateOrUpdateColDefaultMapRuleReq) (*message.CreateOrUpdateColDefaultMapRuleResp, error) {
	log.Infof("CreateColDefaultMapRule service request: %v", req.ColDefaultMapRule)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		colDefaultMapRule, createColDefaultMapErr := models.GetTemplateReaderWriter().CreateColDefaultMapRule(transactionCtx, buildColDefaultMapRuleModelFromMessage(req.ColDefaultMapRule))
		if createColDefaultMapErr != nil {
			log.Errorf("create column default value map rule failed. data:%v, error: %v", colDefaultMapRule, createColDefaultMapErr)
			return createColDefaultMapErr
		}
		templateRule := &template.TmplateRule{MapRuleId: colDefaultMapRule.ColdefaultmapRuleId, TmplateId: req.TmplateId}
		_, createTemplateRuleErr := models.GetTemplateReaderWriter().CreateTemplateRule(transactionCtx, templateRule)
		if createTemplateRuleErr != nil {
			log.Errorf("when create column default value map rule, create template rule failed. data:%v, error: %v", templateRule, createTemplateRuleErr)
			return createTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("create column default value map rule failed. templateId:%d, req:%v, error:%v", req.TmplateId, req.ColDefaultMapRule, err)
		return nil, err
	}
	log.Infof("create column default value map rule successfully.")
	return &message.CreateOrUpdateColDefaultMapRuleResp{}, nil
}

func (s *Service) UpdateColDefaultMapRule(ctx context.Context, req *message.CreateOrUpdateColDefaultMapRuleReq) (*message.CreateOrUpdateColDefaultMapRuleResp, error) {
	log.Infof("UpdateSqlMapRule service request: %v", req.ColDefaultMapRule)
	colDefaultMapRule := buildColDefaultMapRuleModelFromMessage(req.ColDefaultMapRule)
	_, err := models.GetTemplateReaderWriter().UpdateColDefaultMapRule(ctx, colDefaultMapRule)
	if err != nil {
		log.Errorf("update column default value map rule failed. data:%v, error: %v", colDefaultMapRule, err)
		return nil, err
	}
	log.Infof("update column default value map rule successfully.")
	return &message.CreateOrUpdateColDefaultMapRuleResp{}, nil
}

func (s *Service) DeleteColDefaultMapRuleById(ctx context.Context, req *message.DeleteColDefaultMapRuleByIdReq) (*message.DeleteColDefaultMapRuleByIdResp, error) {
	log.Infof("DeleteColDefaultMapRuleById service request: %v", req)
	err := models.Transaction(ctx, func(transactionCtx context.Context) error {
		deleteColDefaultMapRuleErr := models.GetTemplateReaderWriter().DeleteColDefaultMapRuleById(transactionCtx, req.ColDefaultMapRuleId)
		if deleteColDefaultMapRuleErr != nil {
			log.Errorf("delete column default value map rule failed. id:%d, error: %v", req.ColDefaultMapRuleId, deleteColDefaultMapRuleErr)
			return deleteColDefaultMapRuleErr
		}
		deleteTemplateRuleErr := models.GetTemplateReaderWriter().DeleteTemplateRule(transactionCtx, req.TmplateId, req.ColDefaultMapRuleId)
		if deleteTemplateRuleErr != nil {
			log.Errorf("when delete column default value map rule, delete template rule failed. TemplateId:%d, ruleId:%d, error: %v", req.TmplateId, req.ColDefaultMapRuleId, deleteTemplateRuleErr)
			return deleteTemplateRuleErr
		}
		return nil
	})
	if err != nil {
		log.Errorf("delete column default value map rule failed. templateId:%d, ruleId:%v, error:%v", req.TmplateId, req.ColDefaultMapRuleId, err)
		return nil, err
	}
	log.Infof("delete column default value map rule successfully.")
	return &message.DeleteColDefaultMapRuleByIdResp{}, nil
}

func (s *Service) GetColDefaultMapRuleById(ctx context.Context, req *message.GetColDefaultMapRuleByIdReq) (*message.GetColDefaultMapRuleByIdResp, error) {
	log.Infof("GetColDefaultMapRuleById service request: %v", req.ColDefaultMapRuleId)
	rule, err := models.GetTemplateReaderWriter().GetColDefaultMapRuleById(ctx, req.ColDefaultMapRuleId)
	if err != nil {
		log.Errorf("get column default value map rule failed. id:%d, error: %v", req.ColDefaultMapRuleId, err)
		return nil, err
	}
	log.Infof("get column default value map rule successfully.")
	return &message.GetColDefaultMapRuleByIdResp{ColDefaultMapRule: buildColDefaultMapRuleMessageFromModel(rule)}, nil
}

func (s *Service) ListColDefaultMapRulesByTemplateId(ctx context.Context, req *message.ListColDefaultMapRulesReq) (*message.ListColDefaultMapRulesResp, *message.Page, error) {
	log.Infof("ListColDefaultMapRules service request received")
	rules, total, err := models.GetTemplateReaderWriter().ListColDefaultMapRulesByTemplateId(ctx, req.TmplateId, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("list sql map rules failed. err:", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	ruleMessages := make([]*message.ColDefaultMapRule, 0, len(rules))
	for _, rule := range rules {
		ruleMessages = append(ruleMessages, buildColDefaultMapRuleMessageFromModel(rule))
	}
	log.Infof("list column default value map rule size is %d", len(rules))
	return &message.ListColDefaultMapRulesResp{ColDefaultMapRules: ruleMessages, TmplateId: req.TmplateId}, page, nil
}

func buildColDefaultMapRuleModelFromMessage(colDefaultMapRule *message.ColDefaultMapRule) *template.ColdefaultMapRule {
	return &template.ColdefaultMapRule{
		ColdefaultmapRuleId:  colDefaultMapRule.ColDefaultMapRuleId,
		DbTypeS:              colDefaultMapRule.DbTypeS,
		DbTypeT:              colDefaultMapRule.DbTypeT,
		ColTypeDefaultValueS: colDefaultMapRule.ColTypeDefaultValueS,
		ColTypeDefaultValueT: colDefaultMapRule.ColTypeDefaultValueT,
		IsEquivalent:         colDefaultMapRule.IsEquivalent,
		Entity:               common.BuildEntityModelFromMessage(colDefaultMapRule.BaseFields),
	}
}

func buildColDefaultMapRuleMessageFromModel(colDefaultMapRule *template.ColdefaultMapRule) *message.ColDefaultMapRule {
	return &message.ColDefaultMapRule{
		ColDefaultMapRuleId:  colDefaultMapRule.ColdefaultmapRuleId,
		ColTypeDefaultValueS: colDefaultMapRule.ColTypeDefaultValueS,
		ColTypeDefaultValueT: colDefaultMapRule.ColTypeDefaultValueT,
		IsEquivalent:         colDefaultMapRule.IsEquivalent,
		RuleBaseFields: &message.RuleBaseFields{
			DbTypeS:    colDefaultMapRule.DbTypeS,
			DbTypeT:    colDefaultMapRule.DbTypeT,
			BaseFields: common.BuildBaseFieldsMessageFromModel(colDefaultMapRule.Entity),
		},
	}
}

func buildObjMapRuleModelFromMessage(objMapRule *message.ObjMapRule) *template.ObjmapRule {
	return &template.ObjmapRule{
		ObjmapRuleId:    objMapRule.ObjMapRuleId,
		DbTypeS:         objMapRule.DbTypeS,
		DbTypeT:         objMapRule.DbTypeT,
		ObjectType:      objMapRule.ObjectType,
		ObjectTypename:  objMapRule.ObjectTypeName,
		IsCompatibility: objMapRule.IsCompatibility,
		IsConvertible:   objMapRule.IsConvertible,
		Entity:          common.BuildEntityModelFromMessage(objMapRule.BaseFields),
	}
}

func buildObjMapRuleMessageFromModel(objMapRule *template.ObjmapRule) *message.ObjMapRule {
	return &message.ObjMapRule{
		ObjMapRuleId:   objMapRule.ObjmapRuleId,
		ObjectType:     objMapRule.ObjectType,
		ObjectTypeName: objMapRule.ObjectTypename,
		ObjSqlBaseFields: &message.ObjSqlBaseFields{
			IsCompatibility: objMapRule.IsCompatibility,
			IsConvertible:   objMapRule.IsConvertible,
			RuleBaseFields: &message.RuleBaseFields{
				DbTypeS:    objMapRule.DbTypeS,
				DbTypeT:    objMapRule.DbTypeT,
				BaseFields: common.BuildBaseFieldsMessageFromModel(objMapRule.Entity),
			},
		},
	}
}

func buildSqlMapRuleModelFromMessage(sqlMapRule *message.SqlMapRule) *template.SqlmapRule {
	return &template.SqlmapRule{
		SqlmapRuleId:    sqlMapRule.SqlMapRuleId,
		DbTypeS:         sqlMapRule.DbTypeS,
		DbTypeT:         sqlMapRule.DbTypeT,
		Keywords:        sqlMapRule.Keywords,
		IsCompatibility: sqlMapRule.IsCompatibility,
		IsConvertible:   sqlMapRule.IsConvertible,
		Entity:          common.BuildEntityModelFromMessage(sqlMapRule.BaseFields),
	}
}

func buildSqlMapRuleMessageFromModel(sqlMapRule *template.SqlmapRule) *message.SqlMapRule {
	return &message.SqlMapRule{
		SqlMapRuleId: sqlMapRule.SqlmapRuleId,
		Keywords:     sqlMapRule.Keywords,
		ObjSqlBaseFields: &message.ObjSqlBaseFields{
			IsCompatibility: sqlMapRule.IsCompatibility,
			IsConvertible:   sqlMapRule.IsConvertible,
			RuleBaseFields: &message.RuleBaseFields{
				DbTypeS:    sqlMapRule.DbTypeS,
				DbTypeT:    sqlMapRule.DbTypeT,
				BaseFields: common.BuildBaseFieldsMessageFromModel(sqlMapRule.Entity),
			},
		},
	}
}

func buildTabColMapRuleModelFromMessage(messageContent *message.TabColMapRule) *template.TabcolMapRule {
	return &template.TabcolMapRule{
		TabcolmapRuleId: messageContent.TabColMapRuleId,
		DbTypeS:         messageContent.DbTypeS,
		DbTypeT:         messageContent.DbTypeT,
		ColTypeNameS:    messageContent.ColTypeNameS,
		ColTypeNameT:    messageContent.ColTypeNameT,
		IsEquivalent:    messageContent.IsEquivalent,
		Entity:          common.BuildEntityModelFromMessage(messageContent.BaseFields),
	}
}
func buildTabColMapRuleMessageFromModel(model *template.TabcolMapRule) *message.TabColMapRule {
	return &message.TabColMapRule{
		TabColMapRuleId: model.TabcolmapRuleId,
		ColTypeNameS:    model.ColTypeNameS,
		ColTypeNameT:    model.ColTypeNameT,
		IsEquivalent:    model.IsEquivalent,
		RuleBaseFields: &message.RuleBaseFields{
			DbTypeS:    model.DbTypeS,
			DbTypeT:    model.DbTypeT,
			BaseFields: common.BuildBaseFieldsMessageFromModel(model.Entity),
		},
	}
}
