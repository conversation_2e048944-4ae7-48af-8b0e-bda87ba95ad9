package template

import (
	"context"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/template"
	"gitee.com/pingcap_enterprise/tms/server/service/channel"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type Service struct {
}

func NewTemplateService() *Service {
	return &Service{}
}

func (s *Service) CreateTemplateInfo(ctx context.Context, req *message.CreateOrUpdateTemplateInfoReq) (*message.CreateOrUpdateTemplateInfoResp, error) {
	log.Infof("CreateTemplate service request: %v", req.TemplateInfo)
	if req.DefaultTag == constants.TEMPLATE_DEFAULT {
		_, getDefaultTemplateErr := models.GetTemplateReaderWriter().GetDefaultTemplateByType(ctx, req.TemplateType)
		if getDefaultTemplateErr == nil {
			return nil, errors.NewErrorf(errors.TIMS_EXIST_DEFAULT_TEMPLATE, "template type [%s] have default template.", req.TemplateType)
		}
		if msError, ok := getDefaultTemplateErr.(errors.MSError); ok {
			if msError.GetCode() != errors.TIMS_RECORD_NOT_EXIST {
				log.Errorf("check default template failed. template type is %s. err:%v", req.TemplateType, getDefaultTemplateErr)
				return nil, getDefaultTemplateErr
			}
		}
	}

	templateInfo := buildTemplateInfoModelFromMessage(req.TemplateInfo)
	_, err := models.GetTemplateReaderWriter().CreateTemplate(ctx, templateInfo)
	if err != nil {
		log.Errorf("create template info failed. data:%v, error: %v", templateInfo, err)
		return nil, err
	}
	log.Infof("create template successfully")
	return &message.CreateOrUpdateTemplateInfoResp{}, nil

}

func (s *Service) UpdateTemplateInfo(ctx context.Context, req *message.CreateOrUpdateTemplateInfoReq) (*message.CreateOrUpdateTemplateInfoResp, error) {
	log.Infof("UpdateTemplate service request: %v", req.TemplateInfo)
	if req.DefaultTag == constants.TEMPLATE_DEFAULT {
		oldTemplate, getOldTemplateErr := models.GetTemplateReaderWriter().GetTemplate(ctx, req.TemplateId)
		if getOldTemplateErr == nil {
			// change default tag from 'N' to 'Y', so need to determine if there is a default template for this type
			if oldTemplate.DefaultTag == constants.TEMPLATE_NOT_DEFAULT {
				_, getDefaultTemplateErr := models.GetTemplateReaderWriter().GetDefaultTemplateByType(ctx, req.TemplateType)
				if getDefaultTemplateErr == nil {
					return nil, errors.NewErrorf(errors.TIMS_EXIST_DEFAULT_TEMPLATE, "template type [%s] have default template.", req.TemplateType)
				}
				if msError, ok := getDefaultTemplateErr.(errors.MSError); ok {
					if msError.GetCode() != errors.TIMS_RECORD_NOT_EXIST {
						log.Errorf("when update template info, check default template failed. template type is %s. err:%v", req.TemplateType, getDefaultTemplateErr)
						return nil, getDefaultTemplateErr
					}
				}
			}
		} else {
			log.Errorf("when update template info, get old template info failed. req:%v, err:%v", req.TemplateInfo, getOldTemplateErr)
			return nil, getOldTemplateErr
		}
	}
	_, err := models.GetTemplateReaderWriter().UpdateTemplate(ctx, buildTemplateInfoModelFromMessage(req.TemplateInfo))
	if err != nil {
		log.Errorf("update template %d failed. error: %v", req.TemplateId, err)
		return nil, err
	}
	log.Infof("update template successfully")
	return &message.CreateOrUpdateTemplateInfoResp{}, nil
}

func (s *Service) BatchDeleteTemplateInfos(ctx context.Context, req *message.BatchDeleteTemplateInfosReq) (*message.BatchDeleteTemplateInfosResp, error) {
	log.Infof("BatchDeleteTemplates service request: %v", req.TemplateIds)
	if len(req.TemplateIds) > 999 {
		log.Errorf("batch size (%d) too large,greater than 999", len(req.TemplateIds))
		return nil, errors.NewError(errors.TIMS_BATCH_SIZE_TOO_LARGE, "batch size of templateIds too large")
	}
	err := models.GetTemplateReaderWriter().BatchDeleteTemplates(ctx, req.TemplateIds)
	if err != nil {
		log.Errorf("batch delete templates failed, templateIds: %v, error: %v", req.TemplateIds, err)
		return nil, err
	}
	return &message.BatchDeleteTemplateInfosResp{}, nil
}

func (s *Service) DetailTemplateInfo(ctx context.Context, req *message.DetailTemplateInfoReq) (*message.DetailTemplateInfoResp, error) {
	log.Infof("DetailTemplate service request: %d", req.TemplateId)
	templateInfo, err := models.GetTemplateReaderWriter().GetTemplate(ctx, req.TemplateId)
	if err != nil {
		log.Errorf("get template failed, template id is %d, error: %v", req.TemplateId, err)
		return nil, err
	}
	return &message.DetailTemplateInfoResp{TemplateInfo: buildTemplateInfoMessageFromModel(templateInfo)}, nil
}

func (s *Service) ListTemplateInfosByType(ctx context.Context, req *message.ListTemplatesReq) (*message.ListTemplatesResp, *message.Page, error) {
	log.Infof("list templates by type service request: %v", req.PageRequest)
	templateInfos, total, err := models.GetTemplateReaderWriter().ListTemplates(ctx, req.TemplateType, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("list templates failed. err:", err)
		return nil, nil, err
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	log.Infof("templates size is %v", len(templateInfos))
	templatesMessage := make([]*message.TemplateInfo, 0, req.PageSize)
	for _, templateInfo := range templateInfos {
		templatesMessage = append(templatesMessage, buildTemplateInfoMessageFromModel(templateInfo))
	}
	return &message.ListTemplatesResp{Templates: templatesMessage}, page, nil
}

func (s *Service) CreateTaskParamTemplate(ctx context.Context, req *message.CreateOrUpdateTaskParamTemplateReq) (*message.CreateOrUpdateTaskParamTemplateResp, error) {
	log.Infof("CreateTaskParamTemplate service request: %v", req.TaskParamTemplate)
	if req.DefaultTag == constants.TEMPLATE_DEFAULT {
		isExist, err := s.isExistDefaultTaskParamTemplate(ctx, req.TaskType)
		if err != nil {
			log.Errorf("create task param template failed.check whether task type [%d] have default task param template failed.", req.TaskType)
			return nil, err
		}
		if isExist {
			log.Errorf("create task param template failed.task type [%d] exist default task param template. data:%v", req.TaskType)
			return nil, errors.NewErrorf(errors.TIMS_EXIST_DEFAULT_TEMPLATE, "task type [%d] have default task param template.", req.TaskType)
		}
	}
	templateModel := channel.BuildTaskParamTemplateModelFromMessage(req.TaskParamTemplate)
	templateModel, err := models.GetTaskReaderWriter().CreateTaskTemplate(ctx, templateModel)
	if err != nil {
		log.Errorf("create task param template failed. data:%v, error: %v", req.TaskParamTemplate, err)
		return nil, err
	}
	log.Infof("create task param template successfully")
	return &message.CreateOrUpdateTaskParamTemplateResp{TaskParamTemplateID: templateModel.TaskparamTemplateID}, nil
}

func (s *Service) UpdateTaskParamTemplate(ctx context.Context, req *message.CreateOrUpdateTaskParamTemplateReq) (*message.CreateOrUpdateTaskParamTemplateResp, error) {
	log.Infof("UpdateTaskParamTemplate service request: %v", req.TaskParamTemplate)
	if req.DefaultTag == constants.TEMPLATE_DEFAULT {
		oldTaskParamTemplate, getOldTaskParamTemplateErr := models.GetTaskReaderWriter().GetTaskParamTemplate(ctx, req.TaskParamTemplateID)
		if getOldTaskParamTemplateErr == nil {
			if oldTaskParamTemplate.DefaultTag == constants.TEMPLATE_NOT_DEFAULT {
				isExist, err := s.isExistDefaultTaskParamTemplate(ctx, req.TaskType)
				if err != nil {
					log.Errorf("update task param template failed.check whether task type [%d] have default task param template failed.", req.TaskType)
					return nil, err
				}
				if isExist {
					log.Errorf("update task param template failed.task type [%d] exist default task param template. data:%v", req.TaskType)
					return nil, errors.NewErrorf(errors.TIMS_EXIST_DEFAULT_TEMPLATE, "task type [%d] have default task param template.", req.TaskType)
				}
			}
		}
	}
	templateModel := channel.BuildTaskParamTemplateModelFromMessage(req.TaskParamTemplate)
	templateModel, err := models.GetTaskReaderWriter().UpdateTaskParamTemplate(ctx, templateModel)
	if err != nil {
		log.Errorf("update task param template failed. data:%v, error: %v", req.TaskParamTemplate, err)
		return nil, err
	}
	log.Infof("update task param template successfully")
	return &message.CreateOrUpdateTaskParamTemplateResp{TaskParamTemplateID: templateModel.TaskparamTemplateID}, nil
}

func (s *Service) isExistDefaultTaskParamTemplate(ctx context.Context, taskType int) (bool, error) {
	log.Infof("check whether task type [%d] have default task param template", taskType)
	_, err := models.GetTaskReaderWriter().GetDefaultTaskParamTemplateByTaskType(ctx, taskType)
	msError, _ := err.(errors.MSError)
	if err != nil {
		if msError.GetCode() == errors.TIMS_RECORD_NOT_EXIST {
			log.Infof("default task param template not exist. taskType:%d", taskType)
			return false, nil
		}
		// db error
		return false, err
	}
	return true, nil
}

func (s *Service) BatchDeleteTaskParamTemplates(ctx context.Context, req *message.BatchDeleteTaskParamTemplatesReq) (*message.BatchDeleteTaskParamTemplatesResp, error) {
	log.Infof("BatchDeleteTaskParamTemplates service request: %v", req.TaskParamTemplateIDs)
	err := models.GetTaskReaderWriter().BatchDeleteTaskParamTemplates(ctx, req.TaskParamTemplateIDs)
	if err != nil {
		log.Errorf("batch delete task param templates failed. ids:%v, error: %v", req.TaskParamTemplateIDs, err)
		return nil, err
	}
	log.Infof("batch delete task param templates successfully")
	return &message.BatchDeleteTaskParamTemplatesResp{}, nil
}

func (s *Service) ListTaskParamTemplates(ctx context.Context, req *message.ListTaskParamTemplatesReq) (*message.ListTaskParamTemplatesResp, *message.Page, error) {
	log.Infof("ListTaskParamTemplates service request: %v", req.PageRequest)
	taskParamTemplates, total, err := models.GetTaskReaderWriter().ListTaskParamTemplate(ctx, req.ParamTemplateName, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("page list task param templates failed. page info:%v, error: %v", req.PageRequest, err)
		return nil, nil, err
	}
	taskParamTemplatesMessage := make([]*message.TaskParamTemplate, 0, len(taskParamTemplates))
	for _, taskParamTemplate := range taskParamTemplates {
		taskParamTemplatesMessage = append(taskParamTemplatesMessage, channel.BuildTaskParamTemplateMessageFromModel(taskParamTemplate))
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	log.Infof("page list task param templates successfully")
	return &message.ListTaskParamTemplatesResp{TaskParamTemplates: taskParamTemplatesMessage}, page, nil
}

func (s *Service) GetTaskParamTemplate(ctx context.Context, req *message.GetTaskParamTemplateReq) (*message.GetTaskParamTemplateResp, error) {
	log.Infof("GetTaskParamTemplate service request: %v", req.TaskparamTemplateID)
	taskParamTemplates, err := models.GetTaskReaderWriter().GetTaskParamTemplate(ctx, req.TaskparamTemplateID)
	if err != nil {
		log.Errorf("page get task param templates failed. templateId info:%v, error: %v", req.TaskparamTemplateID, err)
		return nil, err
	}
	taskParamTemplatesMessage := channel.BuildTaskParamTemplateMessageFromModel(taskParamTemplates)
	log.Infof("get task param templates successfully")
	return &message.GetTaskParamTemplateResp{TaskParamTemplate: taskParamTemplatesMessage}, nil
}

func buildTemplateInfoMessageFromModel(templateInfo *template.TmplateInformation) *message.TemplateInfo {
	return &message.TemplateInfo{
		TemplateId:   templateInfo.TmplateId,
		TemplateName: templateInfo.TemplateName,
		TemplateType: templateInfo.TemplateType,
		DefaultTag:   templateInfo.DefaultTag,
		BaseFields:   common.BuildBaseFieldsMessageFromModel(templateInfo.Entity),
	}
}

func buildTemplateInfoModelFromMessage(messageContent *message.TemplateInfo) *template.TmplateInformation {
	return &template.TmplateInformation{
		TmplateId:    messageContent.TemplateId,
		TemplateName: messageContent.TemplateName,
		TemplateType: messageContent.TemplateType,
		DefaultTag:   messageContent.DefaultTag,
		Entity:       common.BuildEntityModelFromMessage(messageContent.BaseFields),
	}
}
