package template

import (
	"context"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	common2 "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/models/template"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

func (s *Service) CreateTemplateParamDetail(ctx context.Context, req *message.CreateOrUpdateTemplateParamDetailReq) (*message.CreateOrUpdateTemplateParamDetailResp, error) {
	log.Infof("CreateTemplateParamDetail service request: %v", req.ParamTemplateDetail)
	templateParamInfo := buildTemplateParamModelFromMessage(req.ParamTemplateDetail)
	_, err := models.GetTaskReaderWriter().CreateTemplateParam(ctx, templateParamInfo)
	if err != nil {
		log.Errorf("create template param detail failed. data:%v, error: %v", templateParamInfo, err)
		return nil, err
	}
	log.Infof("create template param detail successfully")
	return &message.CreateOrUpdateTemplateParamDetailResp{}, nil

}

func (s *Service) UpdateTemplateParamDetail(ctx context.Context, req *message.CreateOrUpdateTemplateParamDetailReq) (*message.CreateOrUpdateTemplateParamDetailResp, error) {
	log.Infof("UpdateTemplateParamDetail service request: %v", req.ParamTemplateDetail)

	log.Infof("get old template param detail, templateId:%d", req.TemplateId)
	tempalteParam, getParamErr := models.GetTaskReaderWriter().GetTemplateParam(ctx, req.TemplateId)
	if getParamErr != nil {
		log.Errorf("when update template param detail, get old template param detail failed. templateParamId: %d, error: %v", req.TemplateId, getParamErr)
		return nil, getParamErr
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		_, updateErr := models.GetTaskReaderWriter().UpdateTemplateParam(ctx, buildTemplateParamModelFromMessage(req.ParamTemplateDetail))
		if updateErr != nil {
			log.Errorf("update template param detail %d failed. error: %v", req.TemplateId, updateErr)
			return updateErr
		}
		if tempalteParam.HasSubParams {
			log.Infof("delete old default subparams, paramName:%s", tempalteParam.ParamName)
			deleteErr := models.GetTaskReaderWriter().BatchDeleteTaskSubParamConfigs(ctx, 0, tempalteParam.ParamName)
			if deleteErr != nil {
				log.Errorf("delete old sub param configs failed. templateParamId: %d, error: %v", req.TemplateId, deleteErr)
				return deleteErr
			}
			subConfig := make([]*task.TaskSubParamConfig, 0, len(req.SubParamValuesDefault))
			for _, value := range req.ParamTemplateDetail.SubParamValuesDefault {
				subConfig = append(subConfig, &task.TaskSubParamConfig{
					TaskparamTemplateID: req.TaskparamTemplateId,
					ParamName:           tempalteParam.ParamName,
					ParamValue:          value,
					Entity:              &common2.Entity{Comment: req.Comment},
				})
			}
			if len(subConfig) == 0 {
				log.Infof("create new default subparams, paramName:%s, len is zero, skip", tempalteParam.ParamName)
				return nil
			}
			log.Infof("create new default subparams, paramName:%s, len:%d", tempalteParam.ParamName, len(subConfig))
			_, createErr := models.GetTaskReaderWriter().BatchSaveTaskSubParamConfigs(ctx, subConfig)
			if createErr != nil {
				log.Errorf("create sub param configs failed. templateParamId: %d, taskId: %d, error: %v", req.TemplateId, req.TaskparamTemplateId, createErr)
				return createErr
			}
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("update template param detail %d failed. error: %v", req.TemplateId, trxErr)
		return nil, trxErr
	}

	log.Infof("update template param detail successfully")
	return &message.CreateOrUpdateTemplateParamDetailResp{}, nil
}

func (s *Service) BatchDeleteTemplateParamDetails(ctx context.Context, req *message.BatchDeleteTemplateParamDetailsReq) (*message.BatchDeleteTemplateParamDetailsResp, error) {
	log.Infof("BatchDeleteTemplateParamDetails service request: %v", req.TemplateIds)
	if len(req.TemplateIds) > 999 {
		log.Errorf("batch size (%d) too large,greater than 999", len(req.TemplateIds))
		return nil, errors.NewError(errors.TIMS_BATCH_SIZE_TOO_LARGE, "batch size of templateIds too large")
	}

	paramNames := make([]string, 0, len(req.TemplateIds))
	for _, templateId := range req.TemplateIds {
		paramObj, getErr := models.GetTaskReaderWriter().GetTemplateParam(ctx, templateId)
		if getErr != nil {
			log.Errorf("get template param detail failed, templateId: %v, error: %v", templateId, getErr)
			return nil, getErr
		}
		paramNames = append(paramNames, paramObj.ParamName)
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		batchDeleteErr := models.GetTaskReaderWriter().BatchDeleteTemplateParams(ctx, req.TemplateIds)
		if batchDeleteErr != nil {
			log.Errorf("batch delete templates param detail failed, templateIds: %v, error: %v", req.TemplateIds, batchDeleteErr)
			return batchDeleteErr
		}

		for _, paramName := range paramNames {
			deleteErr := models.GetTaskReaderWriter().BatchDeleteTaskSubParamConfigs(ctx, req.TaskId, paramName)
			if deleteErr != nil {
				log.Errorf("batch delete task sub param configs failed, taskId: %v, paramName: %v, error: %v", req.TaskId, paramName, deleteErr)
				return deleteErr
			}
		}

		return nil
	})
	if trxErr != nil {
		log.Errorf("batch delete templates param detail failed, templateIds: %v, error: %v", req.TemplateIds, trxErr)
		return nil, trxErr
	}
	return &message.BatchDeleteTemplateParamDetailsResp{}, nil
}

func (s *Service) ListTemplateParamDetails(ctx context.Context, req *message.ListTemplateParamDetailsReq) (*message.ListTemplateParamDetailsResp, error) {
	log.Infof("list templates param detail service request: %v", req.TaskparamTemplateId)
	templateParamInfos, err := models.GetTaskReaderWriter().ListTemplateParams(ctx, req.TaskparamTemplateId)
	if err != nil {
		log.Errorf("list templates param detail failed. err:", err)
		return nil, err
	}
	log.Infof("templates param detail size is %v", len(templateParamInfos))

	defaultSubParams, getSubParamsErr := models.GetTaskReaderWriter().BatchGetTaskSubParamConfigs(ctx, 0, 0)
	if getSubParamsErr != nil {
		log.Errorf("get default sub param list by TaskparamTemplateId  failed, err:%v", getSubParamsErr)
		return nil, getSubParamsErr
	}
	defaultSubParamMapping := make(map[string][]*task.TaskSubParamConfig)
	for _, subParam := range defaultSubParams {
		if _, ok := defaultSubParamMapping[subParam.ParamName]; !ok {
			defaultSubParamMapping[subParam.ParamName] = make([]*task.TaskSubParamConfig, 0)
		}
		defaultSubParamMapping[subParam.ParamName] = append(defaultSubParamMapping[subParam.ParamName], subParam)
	}

	templateParamMessage := make([]*message.ParamTemplateDetail, 0, len(templateParamInfos))
	for _, templateInfo := range templateParamInfos {
		if templateInfo.HasSubParams {
			templateParamMessage = append(templateParamMessage, buildTaskSubParamConfigsModelFromMessage(templateInfo, defaultSubParamMapping[templateInfo.ParamName]))
		} else {
			templateParamMessage = append(templateParamMessage, buildTemplateParamMessageFromModel(templateInfo))
		}
	}
	log.Infof("get task param templates successfully")
	return &message.ListTemplateParamDetailsResp{Params: templateParamMessage, Total: len(templateParamInfos)}, nil
}

func buildTemplateParamModelFromMessage(messageContent *message.ParamTemplateDetail) *task.ParamTemplateDetail {
	return &task.ParamTemplateDetail{
		TemplateID:          messageContent.TemplateId,
		TaskparamTemplateID: messageContent.TaskparamTemplateId,
		ParamName:           messageContent.ParamName,
		ParamValueDefault:   messageContent.ParamValueDefault,
		HasSubParams:        messageContent.HasSubParams,
		Entity:              common.BuildEntityModelFromMessage(messageContent.BaseFields),
	}
}

func buildTemplateParamMessageFromModel(model *task.ParamTemplateDetail) *message.ParamTemplateDetail {
	return &message.ParamTemplateDetail{
		TemplateId:          model.TemplateID,
		TaskparamTemplateId: model.TaskparamTemplateID,
		ParamName:           model.ParamName,
		ParamValueDefault:   model.ParamValueDefault,
		HasSubParams:        model.HasSubParams,
		BaseFields:          common.BuildBaseFieldsMessageFromModel(model.Entity),
	}
}

func (s *Service) DeleteTabcolCustMapRules(ctx context.Context, req *message.DeleteTabcolCustMapRulesReq) (*message.CommonResp, error) {
	log.Infof("DeleteTabcolCustMapRules service request: %v", req.TabcolMapruleId)

	err := models.GetTemplateReaderWriter().DeleteTabcolCustMapRules(ctx, &template.TabcolCustMap{TabcolMapruleId: req.TabcolMapruleId})
	if err != nil {
		log.Errorf("DeleteTabcolCustMapRules service failed, TabcolMapruleId: %v, error: %v", req.TabcolMapruleId, err)
		return nil, err
	}
	return &message.CommonResp{}, nil
}

func (s *Service) GetTabcolCustMapRules(ctx context.Context, req *message.GetTabcolCustMapRulesReq) (*message.GetTabcolCustMapRulesResp, error) {
	log.Infof("GetTabcolCustMapRules service request: %v", req)
	if req.RuleType == 3 {
		rules, err := models.GetTemplateReaderWriter().GetDefaultValueCustMapRules(ctx, &template.TabcolCustMap{ChannelId: req.ChannelId, TaskId: req.TaskId})
		if err != nil {
			log.Errorf("GetTabcolCustMapRules service failed, channel_id: %v, error: %v", req.ChannelId, err)
			return nil, err
		}
		ruleList := make([]*message.TabcolCustMapRules, 0, len(rules))
		for _, rule := range rules {
			ruleList = append(ruleList, buildGetTabcolCustMapRulesFormModel(rule))
		}
		return &message.GetTabcolCustMapRulesResp{Rules: ruleList}, nil
	} else if req.RuleType == 4 {
		rules, err := models.GetTemplateReaderWriter().GetColTypeCustMapRules(ctx, &template.TabcolCustMap{ChannelId: req.ChannelId, TaskId: req.TaskId})
		if err != nil {
			log.Errorf("GetTabcolCustMapRules service failed, channel_id: %v, error: %v", req.ChannelId, err)
			return nil, err
		}
		ruleList := make([]*message.TabcolCustMapRules, 0, len(rules))
		for _, rule := range rules {
			ruleList = append(ruleList, buildGetTabcolCustMapRulesFormModel(rule))
		}
		return &message.GetTabcolCustMapRulesResp{Rules: ruleList}, nil
	} else {
		return nil, nil
	}
}

func buildGetTabcolCustMapRulesFormModel(rule *template.TabcolCustMap) *message.TabcolCustMapRules {
	return &message.TabcolCustMapRules{
		TabcolMapruleId:  rule.TabcolMapruleId,
		ChannelId:        rule.ChannelId,
		TaskId:           rule.TaskId,
		SchemaNameS:      rule.SchemaNameS,
		TableNameS:       rule.TableNameS,
		ColNameS:         rule.ColNameS,
		ColNameT:         rule.ColNameT,
		ColStrS:          rule.ColStrS,
		ColStrT:          rule.ColStrT,
		ColDefaultvalueS: rule.ColDefaultvalueS,
		ColDefaultvalueT: rule.ColDefaultvalueT,
	}
}

func (s *Service) CreateTabcolCustMapRules(ctx context.Context, req *message.TabcolCustMapRulesReq) (*message.CommonResp, error) {
	log.Infof("CreateTabcolCustMapRules service request: %v", req.TabcolMapruleId)

	_, err := models.GetTemplateReaderWriter().CreateTabcolCustMap(ctx, &template.TabcolCustMap{
		ChannelId:        req.ChannelId,
		TaskId:           req.TaskId,
		SchemaNameS:      req.SchemaNameS,
		TableNameS:       req.TableNameS,
		ColNameS:         req.ColNameS,
		ColNameT:         req.ColNameT,
		ColStrS:          req.ColStrS,
		ColStrT:          req.ColStrT,
		ColDefaultvalueS: req.ColDefaultvalueS,
		ColDefaultvalueT: req.ColDefaultvalueT,
	})
	if err != nil {
		log.Errorf("CreateTabcolCustMapRules service failed, TabcolMapruleId: %v, error: %v", req.TabcolMapruleId, err)
		return nil, err
	}
	return &message.CommonResp{}, nil
}

func (s *Service) UpdateTabcolCustMapRules(ctx context.Context, req *message.TabcolCustMapRulesReq) (*message.CommonResp, error) {
	log.Infof("UpdateTabcolCustMapRules service request: %v", req.TabcolMapruleId)

	_, err := models.GetTemplateReaderWriter().UpdateTabcolCustMap(ctx, &template.TabcolCustMap{
		TabcolMapruleId:  req.TabcolMapruleId,
		ChannelId:        req.ChannelId,
		TaskId:           req.TaskId,
		SchemaNameS:      req.SchemaNameS,
		TableNameS:       req.TableNameS,
		ColNameS:         req.ColNameS,
		ColNameT:         req.ColNameT,
		ColStrS:          req.ColStrS,
		ColStrT:          req.ColStrT,
		ColDefaultvalueS: req.ColDefaultvalueS,
		ColDefaultvalueT: req.ColDefaultvalueT,
	})
	if err != nil {
		log.Errorf("UpdateTabcolCustMapRules service failed, TabcolMapruleId: %v, error: %v", req.TabcolMapruleId, err)
		return nil, err
	}
	return &message.CommonResp{}, nil
}

func buildTaskSubParamConfigsModelFromMessage(param *task.ParamTemplateDetail, subConfigs []*task.TaskSubParamConfig) *message.ParamTemplateDetail {
	values := lo.Map(subConfigs, func(item *task.TaskSubParamConfig, _ int) string {
		return item.ParamValue
	})
	return &message.ParamTemplateDetail{
		HasSubParams:          true,
		SubParamValuesDefault: values,
		TemplateId:            param.TemplateID,
		TaskparamTemplateId:   param.TaskparamTemplateID,
		ParamName:             param.ParamName,
		BaseFields:            common.BuildBaseFieldsMessageFromModel(param.Entity),
	}
}
