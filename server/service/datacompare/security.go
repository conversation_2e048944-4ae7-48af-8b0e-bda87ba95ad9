package datacompare

import (
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/errors"
)

const (
	// 最大文件大小限制: 1GB
	MaxFileSize = 1 << 30
	// 最大行长度限制: 1MB
	MaxLineLength = 1 << 20
	// 最大并发goroutine数量
	MaxConcurrentGoroutines = 10
	// 最大内存使用限制: 500MB
	MaxMemoryUsage = 500 << 20
)

var (
	// SQL文件名正则表达式: SCHEMA.TABLE.sql
	sqlFileNameRegex = regexp.MustCompile(`^[a-zA-Z0-9_]+\.[a-zA-Z0-9_]+\.sql$`)
	// 安全文件名字符白名单
	safeFileNameRegex = regexp.MustCompile(`^[a-zA-Z0-9_\-\.]+$`)
	// 安全schema/table名称正则
	safeIdentifierRegex = regexp.MustCompile(`^[a-zA-Z0-9_]+$`)
)

// ValidateFileName 验证文件名是否安全
func ValidateFileName(fileName string) error {
	// 检查文件名是否为空
	if fileName == "" {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_name: file name cannot be empty")
	}

	// 检查文件名长度
	if len(fileName) > 255 {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_name: file name too long")
	}

	// 检查是否包含路径分隔符
	if strings.Contains(fileName, "/") || strings.Contains(fileName, "\\") {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_name: file name cannot contain path separators")
	}

	// 检查是否包含父目录引用
	if strings.Contains(fileName, "..") {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_name: file name cannot contain parent directory references")
	}

	// 检查文件名是否符合SQL文件命名规范
	if !sqlFileNameRegex.MatchString(fileName) {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_name: invalid SQL file name format, expected: SCHEMA.TABLE.sql")
	}

	// 检查文件名是否只包含安全字符
	if !safeFileNameRegex.MatchString(fileName) {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_name: file name contains invalid characters")
	}

	return nil
}

// ValidateFilePath 验证文件路径是否安全
func ValidateFilePath(filePath, baseDir string) error {
	// 清理路径
	cleanPath := filepath.Clean(filePath)
	cleanBaseDir := filepath.Clean(baseDir)

	// 解析符号链接
	realPath, err := filepath.EvalSymlinks(cleanPath)
	if err != nil && !os.IsNotExist(err) {
		return errors.WrapError(errors.TIMS_UNRECOGNIZED_ERROR, "failed to resolve file path", err)
	}
	if realPath != "" {
		cleanPath = realPath
	}

	// 确保路径在基础目录内
	if !strings.HasPrefix(cleanPath, cleanBaseDir) {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_path: access denied: path outside allowed directory")
	}

	return nil
}

// ValidateFileSize 验证文件大小是否在限制范围内
func ValidateFileSize(filePath string) error {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "file not found: %s", filepath.Base(filePath))
		}
		return errors.WrapError(errors.TIMS_UNRECOGNIZED_ERROR, "failed to get file info", err)
	}

	if fileInfo.Size() > MaxFileSize {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_size: file too large, max size: %d bytes", MaxFileSize)
	}

	return nil
}

// ValidateOffset 验证offset参数
func ValidateOffset(offset, fileSize int64) error {
	if offset < 0 {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "offset: offset cannot be negative")
	}
	if offset > fileSize {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "offset: offset exceeds file size")
	}
	return nil
}

// ValidateLimit 验证limit参数
func ValidateLimit(limit int) int {
	if limit <= 0 {
		return 20 // 默认值
	}
	if limit > 1000 {
		return 1000 // 最大值
	}
	return limit
}

// SanitizeSearchText 清理搜索文本
func SanitizeSearchText(text string) string {
	// 移除控制字符
	text = strings.TrimSpace(text)

	// 转义正则特殊字符（如果用于正则搜索）
	// 这里简单处理，实际可能需要更复杂的逻辑
	replacer := strings.NewReplacer(
		"\\", "\\\\",
		".", "\\.",
		"*", "\\*",
		"+", "\\+",
		"?", "\\?",
		"[", "\\[",
		"]", "\\]",
		"{", "\\{",
		"}", "\\}",
		"(", "\\(",
		")", "\\)",
		"|", "\\|",
		"^", "\\^",
		"$", "\\$",
	)

	return replacer.Replace(text)
}

// ValidateIdentifier 验证schema或table名称
func ValidateIdentifier(identifier string) error {
	if identifier == "" {
		return nil // 允许为空
	}

	if len(identifier) > 128 {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "identifier: identifier too long")
	}

	if !safeIdentifierRegex.MatchString(identifier) {
		return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "identifier: identifier contains invalid characters")
	}

	return nil
}

// ValidateIdentifiers 批量验证identifiers
func ValidateIdentifiers(identifiers []string, fieldName string) error {
	for _, id := range identifiers {
		if err := ValidateIdentifier(id); err != nil {
			return errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "%s: invalid %s: %s", fieldName, fieldName, id)
		}
	}
	return nil
}

// WrapSafeError 包装错误，隐藏敏感信息
func WrapSafeError(err error, userMessage string) error {
	if err == nil {
		return nil
	}

	// 原始错误已经被包装，不需要额外记录

	// 返回用户友好的错误消息
	return errors.NewError(errors.TIMS_UNRECOGNIZED_ERROR, userMessage)
}
