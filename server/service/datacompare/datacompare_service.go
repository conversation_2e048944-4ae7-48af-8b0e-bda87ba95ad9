package datacompare

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	datacomparepkg "gitee.com/pingcap_enterprise/tms/pkg/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/envdeploy"
	fileutil "gitee.com/pingcap_enterprise/tms/util/file"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/samber/lo"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	compare "gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/sync_diff_inspector"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type Service struct {
}

func NewDataCompareService() *Service {
	return &Service{}
}

func (s *Service) DataCompareReExecution(ctx context.Context, req *message.DataCompareReExecutionReq) (*message.DataCompareReExecutionResp, error) {
	log.Infof("start re-run data compare taskId:%d, channelId:%d, channel schema table ids:%v", req.TaskId, req.ChannelId, req.Ids)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}
	if taskInfo.TaskStatus == constants.TASK_STATUS_NOT_SETUP || taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		return nil, errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "task[%d] status is %d, it must be 2[not running],4[finish],5[failed]", taskInfo.TaskID, taskInfo.TaskStatus)
	}
	taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
	_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		return nil, errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to running failed.", taskInfo.TaskID)
	}
	compare.StartCompare(ctx, req.ChannelId, req.TaskId, config.GetGlobalConfig(), req.Ids)
	return &message.DataCompareReExecutionResp{}, nil
}

func (s *Service) ValidateDataCompareSummaryStatus(ctx context.Context, taskId int, channelSchemaTableIds []int) error {
	log.Infof("validate re-run data compare taskId:%d, channel schema table ids:%v", taskId, channelSchemaTableIds)

	if len(channelSchemaTableIds) == 0 {
		return nil
	}

	_, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		return getTaskErr
	}

	channelSchemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByPks(ctx, channelSchemaTableIds)
	if getTableErr != nil {
		return getTableErr
	}

	summaries, getSummaryErr := models.GetDataCompareReaderWriter().GetDataCompareSummary(ctx, taskId)
	if getSummaryErr != nil {
		return getSummaryErr
	}

	log.Infof("validate re-run data compare taskId:%d, channel schema table ids:%v, channelSchemaTables:%d, summaries:%d", taskId, channelSchemaTableIds, len(channelSchemaTables), len(summaries))

	tableMap := make(map[structs.SchemaTablePair]*channel.ChannelSchemaTable)
	for _, v := range channelSchemaTables {
		tableMap[structs.SchemaTablePair{
			SchemaName: strings.ToLower(v.SchemaNameT),
			TableName:  strings.ToLower(v.TableNameT),
		}] = v
	}

	for _, v := range summaries {
		if v.State != constants.MigrationStatusInvalid && v.State != "invalid" {
			continue
		}
		if _, ok := tableMap[structs.SchemaTablePair{
			SchemaName: strings.ToLower(v.Schema),
			TableName:  strings.ToLower(v.Table),
		}]; !ok {
			return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "task[%d] schema[%s] table[%s] not found in channel_schema_tables", taskId, v.Schema, v.Table)
		}
		return fmt.Errorf("table [%s.%s] may not exist or split chunks failed, please check again", v.Schema, v.Table)
	}

	return nil
}

func (s *Service) GetCompareProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	log.Infof("start GetCompareProgress taskId:%d, channelId:%d, startTM:%v", req.TaskId, req.ChannelId, req.StartTime)
	// total tables GetChannelSchemaTablesCountsByTask
	totalTaskTableCount, err := models.GetChannelReaderWriter().GetChannelSchemaTablesCountsByTask(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("get totalTaskTableCount failed, err:%v", err)
	}
	sumByStateList, err := models.GetDataCompareReaderWriter().GetDataCompareSummaryCountByState(ctx, req.TaskId, req.StartTime)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumByState data failed, err: %v", err))
		return &message.GetTaskProgressResp{}, err
	}
	var (
		succNum     = 0
		failNum     = 0
		runNum      = 0
		totalChunk  = 0
		finishChunk = 0
	)

	runStartTime := req.StartTime
	lastUpdateTime := req.StartTime
	for _, v := range sumByStateList {
		if v.State == "success" {
			succNum += v.Count
		} else if v.State == "failed" {
			failNum += v.Count
		} else {
			runNum += v.Count
			totalChunk += v.ChunkNum
			finishChunk = finishChunk + v.CheckSuccessNum + v.CheckFailedNum + v.CheckIgnoreNum
		}
		if v.StartTime.Before(runStartTime) {
			runStartTime = v.StartTime
		}
		if lastUpdateTime.Before(v.StartTime) {
			lastUpdateTime = v.StartTime
		}
	}

	runChunk := &message.TaskProgressChunkInfo{
		TaskId:             req.TaskId,
		RunningStartTime:   runStartTime,
		LastUpdateTime:     lastUpdateTime,
		RunningTotalChunk:  totalChunk,
		RunningFinishChunk: finishChunk,
	}
	CompareProgress := &message.GetTaskProgressResp{
		TaskId:           req.TaskId,
		StartTime:        req.StartTime,
		LastUpdateTime:   lastUpdateTime,
		TotalNums:        int(totalTaskTableCount),
		SuccessNums:      succNum,
		FailedNums:       failNum,
		RunningNums:      runNum,
		RunningChunkInfo: runChunk,
		TaskLogFile:      fmt.Sprintf("./data/logs/o2t_sync_diff-%d.log", req.TaskId),
	}
	log.Infof("get total %d rows from GetDataCompareSummary", len(sumByStateList))

	compareLogList, err := models.GetDataCompareReaderWriter().GetDataCompareLogsByTime(ctx, req.TaskId, &req.StartTime)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumByState data failed, err: %v", err))
		return CompareProgress, err
	}
	var progressLog []*message.TaskProgressLogDetail
	logStartTime := req.StartTime
	for _, v := range compareLogList {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.CreatedAt,
			LogMessage: v.LogMessage,
			LogLevel:   v.LogLevel,
		})
		if v.CreatedAt.Before(logStartTime) {
			logStartTime = v.CreatedAt
		}
	}

	CompareProgress.ProgressLogInfo = &message.TaskProgressLogInfo{
		TaskId:        req.TaskId,
		LogStartTime:  req.StartTime,
		LogCount:      len(progressLog),
		LogDetailList: progressLog,
	}
	return CompareProgress, nil
}

func (s *Service) GetDataCompareSummaryByTask(ctx context.Context, req *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	taskId, channelId := req.TaskId, req.ChannelId
	log.Infof("start GetCompareProgress taskId:%d, channelId:%d", taskId, channelId)
	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("get task info failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	schemaTables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.TaskId)
	if getTableErr != nil {
		log.Errorf("get channel schema tables failed, taskId:%d, err:%v", req.TaskId, getTableErr)
		return nil, getTableErr
	}

	sumByStateList, getSumErr := models.GetDataCompareReaderWriter().GetDataCompareSummaryCountByState(ctx, req.TaskId, taskInfo.StartTime)
	if getSumErr != nil {
		log.Errorf(fmt.Sprintf("get sumByState data failed, taskId:%d, err: %v", req.TaskId, getSumErr))
		return nil, getSumErr
	}
	sumBySchema, err := models.GetDataCompareReaderWriter().GetDataCompareSummaryCountBySchema(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumBySchema data failed, taskId:%d, err: %v", req.TaskId, err))
		return &message.GetTaskDetailResultResp{}, err
	}
	log.Infof("start GetCompareProgress taskId:%d, sumBySchema:%d", taskId, len(sumBySchema))

	totalTaskTableCount := len(schemaTables)

	schemaTableCounts := make(map[string]int)
	for _, v := range schemaTables {
		schemaName := strings.ToLower(v.SchemaNameT)
		schemaTableCounts[schemaName] = schemaTableCounts[schemaName] + 1
	}

	chartData := buildDataCompareDetailChartData(sumByStateList, totalTaskTableCount)

	taskDetailSchemaData := buildTaskDetailSchemaData(sumBySchema, schemaTableCounts)

	if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
		taskInfo.EndTime = time.Now()
	}

	return &message.GetTaskDetailResultResp{
		TaskId:                  req.TaskId,
		StartTime:               taskInfo.StartTime,
		TotalTables:             totalTaskTableCount,
		TotalDuration:           taskInfo.EndTime.Sub(taskInfo.StartTime).String(),
		TaskDetailChartDataList: chartData,
		TaskDetailSchemaData:    taskDetailSchemaData,
	}, nil
}

func buildDataCompareDetailChartData(sumByStateList []*datacompare.SummaryByState, totalTaskTableCount int) []message.TaskDetailChartData {
	var chartData []message.TaskDetailChartData
	var notNotCheckedNum int

	for _, v := range sumByStateList {
		if v.State == constants.ChunkNotCheckedState {
			continue
		}
		chartData = append(chartData, message.TaskDetailChartData{
			Total: totalTaskTableCount,
			Type:  v.State,
			Count: v.Count,
			Pct:   fmt.Sprintf("%.2f%%", float64(v.Count)/float64(totalTaskTableCount)*100),
		})
		notNotCheckedNum += v.Count
	}
	chartData = append(chartData, message.TaskDetailChartData{
		Total: totalTaskTableCount,
		Type:  constants.ChunkNotCheckedState,
		Count: totalTaskTableCount - notNotCheckedNum, // channel_schema_tables num - notNotChecked table state num
		Pct:   fmt.Sprintf("%.2f%%", float64(totalTaskTableCount-len(chartData))/float64(totalTaskTableCount)*100),
	})
	return chartData
}

// buildTaskDetailSchemaData 通过元数据库的Chunk信息和Schema的表数量统计信息，计算Schema对应的各种不同状态的表的数量
func buildTaskDetailSchemaData(sumBySchema []*datacompare.SummaryBySchema, schemaTableCounts map[string]int) message.TaskDetailSchemaData {
	var schemaData []message.DataCompareSummaryDetail
	for _, v := range sumBySchema {
		schemaTableCount := schemaTableCounts[strings.ToLower(v.Schema)]

		successNums := v.SuccessNums
		failedNums := v.FailedNums
		runningNums := v.RunningNums
		waitingNums := schemaTableCount - successNums - failedNums - runningNums

		schemaData = append(schemaData, message.DataCompareSummaryDetail{
			TaskId:          v.TaskId,
			Schema:          v.Schema,
			TotalNums:       schemaTableCount,
			SuccessNums:     successNums,
			FailedNums:      failedNums,
			RunningNums:     runningNums,
			WaitingNums:     waitingNums,
			SuccessRatio:    fmt.Sprintf("%.2f%%", float64(v.SuccessNums)/float64(schemaTableCount)*100),
			CompareDuration: v.CompareDuration,
			StartTime:       v.StartTime,
			EndTime:         v.EndTime,
		})
	}
	TaskDetailSchemaData := message.TaskDetailSchemaData{
		DataCompare: schemaData,
	}
	return TaskDetailSchemaData
}

func (s *Service) GetDataCompareSummaryBySchema(ctx context.Context, req *message.DataCompareSchemaReq) ([]*message.DataCompareSchemaResp, error) {
	returnData := make([]*message.DataCompareSchemaResp, 0)
	log.Infof("start GetDataCompareSummaryBySchema taskId:%d, schema:%s", req.TaskId, req.Schema)

	schemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get channel schema tables failed, taskId:%d, err:%v", req.TaskId, err)
		return nil, err
	}
	schemaTableCounts := make(map[string]int)
	for _, v := range schemaTables {
		schemaTableCounts[strings.ToLower(v.SchemaNameT)] += 1
	}

	schemaSummaries, err := models.GetDataCompareReaderWriter().GetDataCompareSummaryCountBySchema(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get sumBySchema data failed, err: %v", err))
		return returnData, err
	}
	sumData := &message.DataCompareSchemaResp{}
	for _, schemaSummary := range schemaSummaries {
		if schemaSummary.Schema != req.Schema {
			continue
		}
		totalNums := schemaTableCounts[strings.ToLower(schemaSummary.Schema)]
		sumData.TaskId = schemaSummary.TaskId
		sumData.Schema = schemaSummary.Schema
		sumData.TotalNums = totalNums
		sumData.SuccessNums = schemaSummary.SuccessNums
		sumData.FailedNums = schemaSummary.FailedNums
		sumData.RunningNums = schemaSummary.RunningNums
		sumData.WaitingNums = totalNums - schemaSummary.SuccessNums - schemaSummary.FailedNums - schemaSummary.RunningNums
		sumData.CompareDuration = schemaSummary.CompareDuration
		sumData.StartTime = schemaSummary.StartTime
		sumData.EndTime = schemaSummary.EndTime
		sumData.SuccessRatio = fmt.Sprintf("%.2f%%", float64(schemaSummary.SuccessNums)/float64(totalNums)*100)
		returnData = append(returnData, sumData)
	}

	return returnData, nil
}

func (s *Service) GetDataCompareDetailTableBySchema(ctx context.Context, req *message.DataCompareSchemaStateReq) (*message.DataCompareSchemaStateResp, *message.Page, error) {
	log.Infof("start GetDataCompareDetailTableBySchema taskId:%d, schema:%s", req.TaskId, req.Schema)

	var states []string
	if !stringutil.IsEmpty(req.State) {
		states = append(states, req.State)
	}
	if strings.ToLower(req.State) == "failed" {
		states = append(states, "invalid", "INVALID")
	}
	if strings.ToLower(req.State) == "not_checked" {
		states = append(states, "skipped", "SKIPPED")
	}

	log.Infof("GetDataCompareDetailTableBySchema taskId:%d, schema:%s, state:%s, states:%v", req.TaskId, req.Schema, req.State, states)
	sumBySchemaSate, total, err := models.GetDataCompareReaderWriter().ListDataCompareSummaryByStatusPage(ctx, &datacompare.Summary{
		TaskId: req.TaskId,
		Schema: req.Schema,
	}, states, req.Page, req.PageSize)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListDataCompareSummary data failed, err: %v", err))
		return &message.DataCompareSchemaStateResp{}, nil, err
	}

	taskInfo, getErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getErr != nil {
		log.Errorf("get task info failed, taskId:%d, err: %v", req.TaskId, getErr)
		return &message.DataCompareSchemaStateResp{}, nil, err

	}
	dataCompareParams, getParamErr := datacomparepkg.BuildDataCompareParameter(ctx, taskInfo)
	if getParamErr != nil {
		log.Errorf("get datacompare task params failed, taskId:%d, err: %v", req.TaskId, getParamErr)
		return &message.DataCompareSchemaStateResp{}, nil, err
	}

	modelToMsg := make([]*message.SummaryWithError, 0)
	var tableErrMsg string
	for _, v := range sumBySchemaSate {
		tableErrMsg = ""
		if v.State == "failed" || v.State == "error" || v.State == "invalid" || v.State == "INVALID" {
			detailByTable, err := models.GetDataCompareReaderWriter().ListDataCompareChunk(ctx, &datacompare.Chunk{
				TaskId: v.TaskId,
				Schema: v.Schema,
				Table:  v.Table,
				State:  v.State,
			})
			if err != nil {
				log.Errorf(fmt.Sprintf("get ListDataCompareChunk data failed, table:%s.%s err: %v", v.Schema, v.Table, err))
			}
			for _, sv := range detailByTable {
				tableErrMsg = tableErrMsg + sv.Message + " \n"
			}
		}
		modelToMsg = append(modelToMsg, buildCompareSummaryMessageFromModel(v, tableErrMsg, dataCompareParams.GetChunkSize()))
	}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}
	return &message.DataCompareSchemaStateResp{
		TaskId:      req.TaskId,
		Schema:      req.Schema,
		State:       req.State,
		TableCount:  int(total),
		TableDetail: modelToMsg,
	}, page, nil
}

func (s *Service) GetDataCompareDetailChunkByTable(ctx context.Context, req *message.DataCompareTableChunkReq) (*message.DataCompareTableChunkResp, error) {
	log.Infof("start GetDataCompareDetailChunkByTable taskId:%d, schema:%s, table:%s", req.TaskId, req.Schema, req.Table)
	detailByTable, err := models.GetDataCompareReaderWriter().ListDataCompareChunk(ctx, &datacompare.Chunk{
		TaskId: req.TaskId,
		Schema: req.Schema,
		Table:  req.Table,
		State:  req.State,
	})
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListDataCompareChunk data failed, err: %v", err))
		return &message.DataCompareTableChunkResp{}, err
	}
	modelToMsg := make([]*message.Chunk, 0)
	for _, v := range detailByTable {
		modelToMsg = append(modelToMsg, buildCompareChunkModelFromMessage(v))
	}
	return &message.DataCompareTableChunkResp{
		TaskId:      req.TaskId,
		Schema:      req.Schema,
		Table:       req.Table,
		ChunkCount:  len(detailByTable),
		ChunkDetail: modelToMsg,
	}, nil
}

func buildCompareSummaryMessageFromModel(summary *datacompare.Summary, errMsg string, chunkSize int) *message.SummaryWithError {
	return &message.SummaryWithError{
		ChannelId:         summary.ChannelId,
		TaskId:            summary.TaskId,
		Schema:            summary.Schema,
		Table:             summary.Table,
		ChunkNum:          summary.ChunkNum,
		CheckSuccessNum:   summary.CheckSuccessNum,
		CheckFailedNum:    summary.CheckFailedNum,
		CheckIgnoreNum:    summary.CheckIgnoreNum,
		CheckWaitingNum:   summary.ChunkNum - summary.CheckSuccessNum - summary.CheckFailedNum - summary.CheckIgnoreNum,
		State:             summary.State,
		Duration:          summary.UpdateTime.Sub(summary.StartTime).String(),
		StartTime:         summary.StartTime,
		UpdateTime:        summary.UpdateTime,
		ChannelSchtableId: summary.ChannelSchtableId,
		Message:           errMsg,
		ChunkSize:         chunkSize,
	}
}

func buildCompareChunkModelFromMessage(chunk *datacompare.Chunk) *message.Chunk {
	return &message.Chunk{
		ChannelId:  chunk.ChannelId,
		TaskId:     chunk.TaskId,
		Schema:     chunk.Schema,
		Table:      chunk.Table,
		ChunkId:    chunk.ChunkId,
		Range:      chunk.Range,
		Checksum:   chunk.Checksum,
		ChunkStr:   chunk.ChunkStr,
		State:      chunk.State,
		UpdateTime: chunk.UpdateTime,
		SourceTime: chunk.SourceTime,
		TargetTime: chunk.TargetTime,

		SourceCount: chunk.SourceCount,
		TargetCount: chunk.TargetCount,
		Message:     chunk.Message,
	}
}

func (i Service) UpdateDataCompareEnvDeployTask(ctx context.Context, req *message.UpdateDataCompareEnvDeployTaskReq) (*message.UpdateDataCompareEnvDeployTaskResp, error) {
	deployTask, err := models.GetDataCompareReaderWriter().GetEnvDeployTask(ctx, &datacompare.EnvDeployTask{DataCompareEnvDeployId: req.EnvDeployId, TaskId: req.TaskId})
	if err != nil {
		log.Errorf("GetEnvDeployTask failed, taskId: %d, envDeployId: %d, err: %v", req.TaskId, req.EnvDeployId, err)
		return nil, err
	}
	deployTask.TaskSQL = req.TaskSQL
	deployTask.Comment = req.Comment

	updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, deployTask)
	if updateErr != nil {
		log.Errorf("UpdateEnvDeployTask failed, taskId: %d, envDeployId: %d, err: %v", req.TaskId, req.EnvDeployId, updateErr)
		return nil, updateErr
	}
	return &message.UpdateDataCompareEnvDeployTaskResp{}, nil
}

func (i Service) ListDataCompareEnvDeployTask(ctx context.Context, req *message.ListDataCompareEnvDeployTaskReq) (*message.ListDataCompareEnvDeployTaskResp, error) {

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetTask failed, taskId:%d, err:%v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	query := &datacompare.EnvDeployTask{TaskId: req.TaskId}

	var envDeployTasks []datacompare.EnvDeployTask
	var listErr error

	envDeployTasks, listErr = models.GetDataCompareReaderWriter().ListEnvDeployTask(ctx, query)
	if listErr != nil {
		log.Errorf("ListEnvDeployTask failed, taskId:%d, err:%v", req.TaskId, listErr)
		return nil, listErr
	}

	log.Infof("ListEnvDeployTask, taskId: %d, envDeployTasks size: %d", req.TaskId, len(envDeployTasks))
	if len(envDeployTasks) == 0 {
		log.Infof("ListEnvDeployTask, taskId: %d, no deploy tasks found, create new deploy tasks", req.TaskId)
		deployTasks := buildDataCompareEnvDeployTasks(taskInfo)
		createErr := models.GetDataCompareReaderWriter().BatchCreateEnvDeployTask(ctx, deployTasks)
		if createErr != nil {
			log.Errorf("create data compare env deploy tasks failed, taskId: %d, err: %v", req.TaskId, createErr)
			return nil, createErr
		}
		envDeployTasks, listErr = models.GetDataCompareReaderWriter().ListEnvDeployTask(ctx, query)
		if listErr != nil {
			log.Errorf("ListEnvDeployTask failed, create new deploy tasks success, but list failed, taskId:%d, err:%v", req.TaskId, listErr)
			return nil, listErr
		}
	}

	resp := &message.ListDataCompareEnvDeployTaskResp{}
	for idx := range envDeployTasks {
		resp.Tasks = append(resp.Tasks, message.EnvDeployTask{
			DataCompareEnvDeployId: envDeployTasks[idx].DataCompareEnvDeployId,
			TaskNumber:             envDeployTasks[idx].TaskNumber,
			TaskName:               envDeployTasks[idx].TaskName,
			IsIgnore:               envDeployTasks[idx].IsIgnore,
			TaskStatus:             envDeployTasks[idx].TaskStatus,
			TaskLog:                envDeployTasks[idx].TaskLog,
			TaskSQL:                envDeployTasks[idx].TaskSQL,
			LastRunTime:            envDeployTasks[idx].LastRunTime,
			TaskId:                 envDeployTasks[idx].TaskId,
			Comment:                envDeployTasks[idx].Comment,
			SQLModel:               envDeployTasks[idx].SQLModel,
		})
	}
	return resp, nil
}

func (i Service) ExecuteDataCompareEnvDeployTask(ctx context.Context, req *message.ExecuteDataCompareEnvDeployTaskReq) (*message.ExecuteDataCompareEnvDeployTaskResp, error) {
	validateErr := envdeploy.ValidateRunMode(req.RunMode)
	if validateErr != nil {
		log.Errorf("ValidateRunMode failed, taskId: %d, runMode: %s, err: %v", req.TaskId, req.RunMode, validateErr)
		return nil, validateErr
	}

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("GetTask, execute env deploy task failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}
	log.Infof("execute env deploy task, task id: %d, channel id: %d, envDeployIds: %v", taskInfo.TaskID, taskInfo.ChannelId, req.EnvDeployIds)

	parameter, err := datacomparepkg.BuildDataCompareParameter(ctx, taskInfo)
	if err != nil {
		log.Errorf("BuildDataCompareParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	query := &datacompare.EnvDeployTask{TaskId: req.TaskId}
	envDeployTasks, err := models.GetDataCompareReaderWriter().ListEnvDeployTask(ctx, query)
	if err != nil {
		log.Errorf("ListEnvDeployTask failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}
	defaultAnalyzeTaskIds := lo.Map(envDeployTasks, func(task datacompare.EnvDeployTask, _ int) int {
		return task.DataCompareEnvDeployId
	})

	missingIds, invalidIds := lo.Difference(defaultAnalyzeTaskIds, req.EnvDeployIds)
	if len(invalidIds) != 0 {
		err := fmt.Errorf("invalid env deploy id: %v", invalidIds)
		log.Errorf("execute env deploy task failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}
	log.Debugf("execute env deploy task, taskId: %d, missingIds: %v, invalidIds: %v", req.TaskId, missingIds, invalidIds)
	missingExecIdMap := lo.SliceToMap(missingIds, func(id int) (int, bool) {
		return id, true
	})

	deployTasks := make([]datacompare.EnvDeployTask, 0)
	for idx := range envDeployTasks {
		if missingExecIdMap[envDeployTasks[idx].DataCompareEnvDeployId] {
			envDeployTasks[idx].IsIgnore = "Y"
		} else {
			envDeployTasks[idx].IsIgnore = "N"
			deployTasks = append(deployTasks, envDeployTasks[idx])
		}
	}

	// 如果没有需要执行的任务，直接返回
	if len(deployTasks) == 0 {
		return &message.ExecuteDataCompareEnvDeployTaskResp{}, nil
	}

	// purpose: only update IsIgnore flags
	if updateErr := models.GetDataCompareReaderWriter().BatchUpdateEnvDeployTask(ctx, envDeployTasks); updateErr != nil {
		log.Errorf("BatchUpdateDataCompareTask, execute env deploy task failed, err: %v", updateErr)
		return nil, updateErr
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, fmt.Errorf("get channel info failed: %v", err)
	}

	go func() {
		dbConns, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
		if err != nil {
			log.Errorf("setUpDatabaseConnsAndCollation failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
			return
		}

		for idx := range deployTasks {
			targetEnvDeployTask := deployTasks[idx]
			targetEnvDeployTask.LastRunTime = time.Now()
			targetEnvDeployTask.TaskStatus = constants.TASK_STATUS_RUNNING
			targetEnvDeployTask.TaskLog = "开始执行部署任务"

			if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, &targetEnvDeployTask); updateErr != nil {
				log.Errorf("UpdateDataCompareTask, execute env deploy task failed, err: %v", updateErr)
				return
			}

			clearDeployTaskStatus(ctx, deployTasks[idx+1:])

			deployTask, hit := datacomparepkg.FindDeployTask(&targetEnvDeployTask)
			if !hit {
				err = fmt.Errorf("not found deploy task:" + targetEnvDeployTask.TaskName)
				log.Errorf("FindDeployTask failed, taskId: %d, taskName: %s, err: %v", req.TaskId, targetEnvDeployTask.TaskName, err)
				return
			}

			if deployTask.HasToValidateParam() {
				validateErr := parameter.ValidateParam()
				if validateErr != nil {
					log.Errorf("validate param failed, err:%v", validateErr)
					updateErr := updateEnvDeployTaskValidateFailed(ctx, validateErr, &targetEnvDeployTask)
					if updateErr != nil {
						log.Errorf("validate param failed and update tms meta failed, err:%v", updateErr)
						return
					}
					return
				}
			}

			if deployTask.HasPreCheckSQLs() {
				shouldSkip, preCheckErr := executePreCheckSQLs(ctx, req.RunMode, deployTask, dbConns, &targetEnvDeployTask)
				log.Infof("executePreCheckSQLs result, taskId: %d, taskName: %s, shouldSkip: %v, err: %v", req.TaskId, deployTask.TaskName, shouldSkip, preCheckErr)
				if preCheckErr != nil {
					if shouldSkip {
						log.Infof("execute pre check failed and skip, taskId: %d, taskName: %s, err: %v", req.TaskId, deployTask.TaskName, preCheckErr)
						continue
					} else {
						log.Errorf("execute pre check failed and return, taskId: %d, taskName: %s, err: %v", req.TaskId, deployTask.TaskName, preCheckErr)
						return
					}
				}
			}

			substituteSQLs := datacomparepkg.SubstituteSQL(&targetEnvDeployTask, parameter.OracleHome, deployTask)

			execErr := executeSQLs(ctx, substituteSQLs, &targetEnvDeployTask, deployTask, taskInfo, dbConns)
			if execErr != nil {
				log.Errorf("execute data compare env deploy failed, taskId: %d, taskName: %s, err: %v", req.TaskId, deployTask.TaskName, execErr)
				return
			}
		}

		// 设置channel标识位
		deployTasks, err := models.GetDataCompareReaderWriter().ListEnvDeployTask(ctx, &datacompare.EnvDeployTask{
			TaskId: req.TaskId,
		})
		if err != nil {
			log.Errorf("after deploy, refetch deploy tasks failed, taskId: %d, err: %v", req.TaskId, err)
			return
		}
		var invokeCompareFunctionSuccess bool
		var invokeCompressCompareFunctionSuccess bool
		for _, deployTask := range deployTasks {
			if deployTask.TaskStatus == constants.TASK_STATUS_FINISH && deployTask.TaskName == constants.DataCompareInvokeCompareFunction {
				invokeCompareFunctionSuccess = true
				continue
			}
			if deployTask.TaskStatus == constants.TASK_STATUS_FINISH && deployTask.TaskName == constants.DataCompareInvokeCompressCompareFunction {
				invokeCompressCompareFunctionSuccess = true
				continue
			}
		}
		if invokeCompareFunctionSuccess && invokeCompressCompareFunctionSuccess {
			channelInfo.DataCompareEnvStatus = true
			if _, err := models.GetChannelReaderWriter().UpdateChannel(ctx, channelInfo); err != nil {
				log.Errorf("after deploy, update channel info failed, taskId: %d, err: %v", req.TaskId, err)
				return
			}
		}

	}()
	return &message.ExecuteDataCompareEnvDeployTaskResp{}, nil
}

func executePreCheckSQLs(ctx context.Context, runMode int, defaultAnalyzeTask datacomparepkg.EnvironmentDeployTask, dbConns *oracle.Oracle, targetAnalyzeTask *datacompare.EnvDeployTask) (shouldSkip bool, err error) {
	for _, preCheckStep := range defaultAnalyzeTask.GetDeployPreCheckSteps() {
		targetAnalyzeTask.LastRunTime = time.Now()
		preCheckFunc := preCheckStep.GetPreCheckFunc()

		shouldSkip, err = preCheckFunc(ctx, dbConns.OracleDB, runMode)
		log.Infof("deploy task:%s, shouldSkip:%v, runMode:%v, err:%v", defaultAnalyzeTask.TaskName, shouldSkip, runMode, err)
		if err != nil {
			log.Errorf("call precheck failed, task name: %v, err: %v", defaultAnalyzeTask.TaskName, err)
			targetAnalyzeTask.LastRunTime = time.Now()
			targetAnalyzeTask.TaskLog = err.Error()
			targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
			if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
				log.Errorf("UpdateEnvDeployTask, execute env deploy task failed, err: %v", updateErr)
				return false, updateErr
			}
			if shouldSkip {
				targetAnalyzeTask.TaskLog = preCheckStep.GetPreCheckSkipMessage()
				targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusSuccess)
				if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
					log.Errorf("UpdateEnvDeployTask, execute env deploy task failed, err: %v", updateErr)
					return false, updateErr
				}
				continue
			} else {
				targetAnalyzeTask.TaskLog = preCheckStep.GetPreCheckFailureMessage() + ":" + err.Error()
				targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
				if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
					log.Errorf("UpdateEnvDeployTask, execute env deploy task failed, err: %v", updateErr)
					return false, updateErr
				}
			}
		} else {
			targetAnalyzeTask.TaskLog = preCheckStep.GetPreCheckSuccessMessage()
			targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusSuccess)
			if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
				log.Errorf("UpdateEnvDeployTask, execute env deploy task failed, err: %v", updateErr)
				return false, updateErr
			}
		}
	}
	return shouldSkip, err
}

func executeSQLs(ctx context.Context, substituteSQLs []string, targetAnalyzeTask *datacompare.EnvDeployTask, defaultAnalyzeTask datacomparepkg.EnvironmentDeployTask, taskInfo *task.Task, dbConns *oracle.Oracle) error {
	for _, substituteSQL := range substituteSQLs {
		substituteSQL = strings.Replace(substituteSQL, "\nEND;\n/", "\nEND;", 1)
		_, err := dbConns.OracleDB.ExecContext(ctx, substituteSQL)
		if err != nil {
			log.Errorf("execute sql failed, task name:%s, err: %v, sql:%s", defaultAnalyzeTask.TaskName, err, stringutil.RemoveSpecialLetterForLog(substituteSQL))
			updateErr := updateEnvDeployTaskFailed(ctx, err, targetAnalyzeTask, defaultAnalyzeTask)
			if updateErr != nil {
				return updateErr
			}
			return err
		}
		log.Debugf("execute data compare env deploy task, id: %d, sql: %s", targetAnalyzeTask.DataCompareEnvDeployId, stringutil.RemoveSpecialLetterForLog(substituteSQL))
	}

	targetAnalyzeTask.LastRunTime = time.Now()
	targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusSuccess)
	targetAnalyzeTask.TaskLog = defaultAnalyzeTask.GetSqlSuccessMessage()
	if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
		log.Errorf("UpdateSQLAnalyzeTask, execute data compare env deploy task failed, err: %v", updateErr)
		return updateErr
	}

	if _, updateErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo); updateErr != nil {
		log.Errorf("UpdateTask, execute data compare env deploy task failed, err: %v", updateErr)
		return updateErr
	}
	return nil
}

func updateEnvDeployTaskFailed(ctx context.Context, err error, targetAnalyzeTask *datacompare.EnvDeployTask, defaultAnalyzeTask datacomparepkg.EnvironmentDeployTask) error {
	targetAnalyzeTask.LastRunTime = time.Now()
	targetAnalyzeTask.TaskLog = defaultAnalyzeTask.GetExecSQLFailureMessage() + ", err:" + err.Error()
	targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
	if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
		log.Errorf("UpdateEnvDeployTask failed, err: %v", updateErr)
		return updateErr
	}
	return nil
}

func updateEnvDeployTaskValidateFailed(ctx context.Context, err error, targetAnalyzeTask *datacompare.EnvDeployTask) error {
	targetAnalyzeTask.LastRunTime = time.Now()
	targetAnalyzeTask.TaskLog = "函数创建失败，" + err.Error()
	targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
	if updateErr := models.GetDataCompareReaderWriter().UpdateEnvDeployTask(ctx, targetAnalyzeTask); updateErr != nil {
		log.Errorf("UpdateEnvDeployTask failed, err: %v", updateErr)
		return updateErr
	}
	return nil
}

func clearDeployTaskStatus(ctx context.Context, tasks []datacompare.EnvDeployTask) {
	if len(tasks) == 0 {
		return
	}
	models.GetDataCompareReaderWriter().ClearEnvDeployTask(ctx, tasks)
}

func buildDataCompareEnvDeployTasks(taskInfo *task.Task) []datacompare.EnvDeployTask {
	defaultDeployTasks := datacomparepkg.GetDefaultDataCompareDeployTasks()
	deployTasks := make([]datacompare.EnvDeployTask, 0, len(defaultDeployTasks))
	for _, item := range defaultDeployTasks {
		deployTasks = append(deployTasks, datacompare.EnvDeployTask{
			TaskNumber:  item.TaskNumber,
			TaskName:    item.TaskName,
			IsIgnore:    "N",
			TaskStatus:  int(constants.TASK_STATUS_NOT_RUNNING),
			TaskSQL:     item.GetTaskSQL(),
			SQLModel:    item.SQLModel,
			TaskId:      taskInfo.TaskID, // fill task id
			LastRunTime: timeutil.GetTMSNullTime(),
		})
	}
	return deployTasks
}

func (s *Service) DownloadFixSQL(ctx context.Context, req *message.DownloadFixSQLReq) (*message.DownloadFixSQLResp, error) {
	// 判断是单文件过滤下载还是批量下载
	if req.FilePath != nil && *req.FilePath != "" {
		log.Infof("start DownloadFixSQL single file, taskId:%d, filePath:%s", req.TaskId, *req.FilePath)
		return s.downloadFilteredSingleFile(ctx, req)
	}

	log.Infof("start DownloadFixSQL bulk download, taskId:%d, schemaNameS:%s, table:%s", req.TaskId, strings.Join(req.SchemaNameS, ","), req.TableName)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, err
	}

	tables, getTableErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.TaskId)
	if getTableErr != nil {
		log.Errorf("get channel schema tables failed, taskId:%d, err:%v", req.TaskId, getTableErr)
		return nil, getTableErr
	}

	dataCompareParams, getParamErr := datacomparepkg.BuildDataCompareParameter(ctx, taskInfo)
	if getParamErr != nil {
		log.Errorf("get datacompare task params failed, taskId:%d, err: %v", req.TaskId, getParamErr)
		return nil, err
	}

	taskFixSQLDir := path.Join(
		config.GetGlobalConfig().DataDir,
		dataCompareParams.GetFixSqlDir(),
		fmt.Sprintf("fix-%d-%s", taskInfo.TaskID, taskInfo.StartTime.Format("20060102150405")),
	)
	requestTableList := filterTable(tables, req.SchemaNameS, req.TableName)
	log.Debugf("after filter, schemaNames:%s, tableName:%s, leftTableNum:%d, totalTableNum:%d",
		strings.Join(req.SchemaNameS, ","), req.TableName,
		len(requestTableList), len(tables))

	// 检查实际存在的SQL文件
	existingTables := make([]*channel.ChannelSchemaTable, 0, len(requestTableList))
	for _, table := range requestTableList {
		fileName := fmt.Sprintf("%s.%s.sql", table.SchemaNameS, table.TableNameS)
		filePath := filepath.Join(taskFixSQLDir, fileName)

		if _, err := os.Stat(filePath); err == nil {
			existingTables = append(existingTables, table)
		}
	}

	// 如果没有找到任何SQL文件，返回错误
	if len(existingTables) == 0 {
		log.Warnf("no fix sql files found for download, taskId:%d, totalRequested:%d", req.TaskId, len(requestTableList))
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "没有找到符合条件的Fix SQL文件")
	}

	log.Infof("found %d fix sql files for download, taskId:%d, requested:%d", len(existingTables), req.TaskId, len(requestTableList))

	/**
	data/fixsqldir
	└── fix-7679-2024095175637
	    ├── TMSADMIN.COMPRESS_TEST.sql
	    └── TMSADMIN.PF_FEE_DETAIL_INF.sql
	将existingTables中包含的SCHEMA.TABLE.sql文件，打包成zip文件
	*/
	zipFilePath, zipErr := createZipFromSQLFiles(taskFixSQLDir, taskInfo, existingTables)
	if zipErr != nil {
		log.Errorf("create zip file failed, taskFixSQLDir:%s, taskId:%d, err:%v", taskFixSQLDir, req.TaskId, zipErr)
		return nil, zipErr
	}

	return &message.DownloadFixSQLResp{
		ExportFilePath: zipFilePath,
	}, nil
}

// downloadFilteredSingleFile 下载单个文件的过滤内容
func (s *Service) downloadFilteredSingleFile(ctx context.Context, req *message.DownloadFixSQLReq) (*message.DownloadFixSQLResp, error) {
	// 验证文件路径参数
	if req.FilePath == nil || *req.FilePath == "" {
		return nil, errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_path cannot be empty for filtered download")
	}

	// 构建基础数据目录
	baseDataDir := config.GetGlobalConfig().DataDir

	// 构建完整文件路径
	relativePath := *req.FilePath
	if strings.HasPrefix(relativePath, "data/") {
		relativePath = strings.TrimPrefix(relativePath, "data/")
	}
	sourceFilePath := filepath.Join(baseDataDir, relativePath)

	// 验证文件路径安全性
	if err := ValidateFilePath(sourceFilePath, baseDataDir); err != nil {
		return nil, err
	}

	// 验证文件大小
	if err := ValidateFileSize(sourceFilePath); err != nil {
		return nil, err
	}

	// 检查文件是否存在
	if _, err := os.Stat(sourceFilePath); os.IsNotExist(err) {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "sql file not found: %s", *req.FilePath)
	}

	// 生成输出文件名
	timestamp := time.Now().Format("20060102150405")
	fileName := filepath.Base(*req.FilePath)
	fileName = strings.TrimSuffix(fileName, ".sql")
	outputFileName := fmt.Sprintf("filtered-%s-%s.sql", fileName, timestamp)

	// 创建临时输出目录
	tempDir := filepath.Join(baseDataDir, "temp", fmt.Sprintf("filtered-download-%d", req.TaskId))
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, errors.NewErrorf(errors.TIMS_UNRECOGNIZED_ERROR, "failed to create temp directory: %v", err)
	}

	outputFilePath := filepath.Join(tempDir, outputFileName)

	// 读取并过滤文件内容
	if err := s.writeFilteredContent(sourceFilePath, outputFilePath, req.Filter); err != nil {
		return nil, err
	}

	log.Infof("filtered single file download completed, taskId:%d, filePath:%s, output:%s",
		req.TaskId, *req.FilePath, outputFilePath)

	return &message.DownloadFixSQLResp{
		ExportFilePath: outputFilePath,
	}, nil
}

// writeFilteredContent 将过滤后的内容写入新文件
func (s *Service) writeFilteredContent(sourceFilePath, outputFilePath string, filter *message.SQLContentFilter) error {
	// 打开源文件
	sourceFile, err := os.Open(sourceFilePath)
	if err != nil {
		return WrapSafeError(err, "failed to open source file")
	}
	defer sourceFile.Close()

	// 创建输出文件
	outputFile, err := os.Create(outputFilePath)
	if err != nil {
		return WrapSafeError(err, "failed to create output file")
	}
	defer outputFile.Close()

	// 读取并过滤内容
	scanner := bufio.NewScanner(sourceFile)
	scanner.Buffer(make([]byte, 0, 64*1024), MaxLineLength)

	totalMemoryUsed := 0
	linesWritten := 0

	for scanner.Scan() {
		lineBytes := scanner.Bytes()
		lineLen := len(lineBytes)

		// 检查内存使用限制
		if totalMemoryUsed+lineLen > MaxMemoryUsage {
			log.Warnf("memory usage limit reached during filtered download")
			break
		}

		line := string(lineBytes)
		totalMemoryUsed += lineLen

		// 应用过滤器
		if filter != nil {
			// 清理搜索文本
			if filter.SearchText != "" {
				filter.SearchText = SanitizeSearchText(filter.SearchText)
			}

			if !s.matchSQLFilter(line, filter) {
				continue
			}
		}

		// 写入过滤后的行
		if _, err := outputFile.WriteString(line + "\n"); err != nil {
			return WrapSafeError(err, "failed to write filtered content")
		}
		linesWritten++
	}

	// 检查scanner错误
	if err := scanner.Err(); err != nil {
		return WrapSafeError(err, "failed to read source file content")
	}

	log.Infof("filtered content written successfully, lines:%d", linesWritten)
	return nil
}

func filterTable(tables []*channel.ChannelSchemaTable, schemaNames []string, tableName string) []*channel.ChannelSchemaTable {
	schemaNameMap := make(map[string]bool)
	for _, schemaName := range schemaNames {
		if strings.TrimSpace(schemaName) == "" {
			continue
		}
		schemaNameMap[schemaName] = true
	}

	if len(schemaNameMap) == 0 {
		return tables
	}

	return lo.Filter(tables, func(table *channel.ChannelSchemaTable, _ int) bool {
		if _, ok := schemaNameMap[table.SchemaNameS]; ok {
			if tableName == "" {
				return true
			}
			if strings.ToLower(table.TableNameS) == strings.ToLower(tableName) {
				return true
			}
		}
		return false
	})
}

func createZipFromSQLFiles(taskFixSQLDir string, taskInfo *task.Task, tableList []*channel.ChannelSchemaTable) (string, error) {
	zipFileName := fmt.Sprintf("FIXSQL-%d-%d.zip", taskInfo.TaskID, time.Now().Unix())
	zipFilePath := filepath.Join(taskFixSQLDir, zipFileName)

	sqlFilenames := make([]string, 0, len(tableList))
	for _, table := range tableList {
		sqlFilenames = append(sqlFilenames, table.SchemaNameS+"."+table.TableNameS+".sql")
	}

	// 从ZIP文件名提取根文件夹名
	rootFolderName := strings.TrimSuffix(filepath.Base(zipFilePath), ".zip")
	zipErr := fileutil.ZipFiles(zipFilePath, taskFixSQLDir, sqlFilenames, rootFolderName)
	if zipErr != nil {
		log.Errorf("create zip file failed, taskId:%d, taskFixSQLDir:%s, zipFilePath:%s, err:%v", taskInfo.TaskID, taskFixSQLDir, zipFilePath, zipErr)
		return "", zipErr
	}

	log.Infof("zip file created successfully, taskId:%d, taskFixSQLDir:%s, zippedFileNum:%d, zipFilePath:%s", taskInfo.TaskID, taskFixSQLDir, len(tableList), zipFilePath)
	return zipFilePath, nil
}

func (s *Service) ListFixSQL(ctx context.Context, req *message.ListFixSQLReq) (*message.ListFixSQLResp, *message.Page, error) {
	log.Infof("start list fix sql taskId:%d, schemaNames:%v, tableName:%s, page:%d, pageSize:%d",
		req.TaskId, req.SchemaNameS, req.TableName, req.Page, req.PageSize)

	// 获取任务信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		return nil, nil, err
	}

	// 获取通道schema表信息
	channelSchemaTableInfoS, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, req.TaskId)
	if err != nil {
		return nil, nil, err
	}

	// 构建数据对比参数
	dataCompareParams, err := datacomparepkg.BuildDataCompareParameter(ctx, taskInfo)
	if err != nil {
		return nil, nil, err
	}

	// 构建Fix SQL目录路径
	taskFixSQLDir := path.Join(
		config.GetGlobalConfig().DataDir,
		dataCompareParams.GetFixSqlDir(),
		fmt.Sprintf("fix-%d-%s", taskInfo.TaskID, taskInfo.StartTime.Format("20060102150405")),
	)

	// 过滤表信息
	filteredTables := filterTable(channelSchemaTableInfoS, req.SchemaNameS, req.TableName)

	// 扫描并获取SQL文件信息
	var allSqlFiles []message.FixSQLFileInfo
	for _, table := range filteredTables {
		fileName := fmt.Sprintf("%s.%s.sql", table.SchemaNameS, table.TableNameS)
		filePath := filepath.Join(taskFixSQLDir, fileName)

		// 检查文件是否存在
		if fileInfo, err := os.Stat(filePath); err == nil {
			sqlFileInfo := message.FixSQLFileInfo{
				Schema:   table.SchemaNameS,
				Table:    table.TableNameS,
				FileName: fileName,
				FileSize: fileInfo.Size(),
				ModTime:  fileInfo.ModTime(),
				FilePath: filePath,
			}
			allSqlFiles = append(allSqlFiles, sqlFileInfo)
		}
	}

	// 实现分页逻辑
	totalCount := len(allSqlFiles)

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 计算分页
	startIndex := (req.Page - 1) * req.PageSize
	endIndex := startIndex + req.PageSize

	var pagedSqlFiles []message.FixSQLFileInfo
	if startIndex < totalCount {
		if endIndex > totalCount {
			endIndex = totalCount
		}
		pagedSqlFiles = allSqlFiles[startIndex:endIndex]
	}

	// 构建响应
	resp := &message.ListFixSQLResp{
		TaskId:   req.TaskId,
		SqlFiles: pagedSqlFiles,
	}

	// 构建分页信息
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    int64(totalCount),
	}

	log.Infof("list fix sql completed, taskId:%d, totalCount:%d, pageCount:%d",
		req.TaskId, totalCount, len(pagedSqlFiles))

	return resp, page, nil
}

// GetFixSQLContent 统一的Fix SQL内容获取方法，使用页码分页
func (s *Service) GetFixSQLContent(ctx context.Context, req *message.GetFixSQLContentReq) (*message.GetFixSQLContentResp, *message.Page, error) {
	// 记录请求的基本信息（不记录敏感内容）
	log.Infof("start get fix sql content taskId:%d, filePath:%s", req.TaskId, req.FilePath)

	// 1. 验证文件路径（在任何路径操作之前）
	if req.FilePath == "" {
		return nil, nil, errors.NewErrorf(errors.TIMS_PARAMETER_INVALID, "file_path cannot be empty")
	}

	// 2. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	req.PageSize = ValidateLimit(req.PageSize)

	// 3. 构建基础数据目录
	baseDataDir := config.GetGlobalConfig().DataDir

	// 4. 构建完整文件路径
	// 如果 FilePath 以 "data/" 开头，需要去掉这个前缀，因为 baseDataDir 已经包含了数据目录
	relativePath := req.FilePath
	if strings.HasPrefix(relativePath, "data/") {
		relativePath = strings.TrimPrefix(relativePath, "data/")
	}
	filePath := filepath.Join(baseDataDir, relativePath)

	// 记录构建的完整文件路径用于调试
	log.Infof("constructed file path: %s (baseDataDir: %s)", filePath, baseDataDir)

	// 5. 验证文件路径安全性，确保文件在 DataDir 范围内
	if err := ValidateFilePath(filePath, baseDataDir); err != nil {
		return nil, nil, err
	}

	// 6. 验证文件大小
	if err := ValidateFileSize(filePath); err != nil {
		return nil, nil, err
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "sql file not found: %s", req.FilePath)
		}

		return nil, nil, WrapSafeError(err, "failed to access file")
	}
	defer file.Close()

	fileInfo, err := file.Stat()
	if err != nil {
		return nil, nil, WrapSafeError(err, "failed to get file info")
	}

	// 7. 清理搜索文本和记录过滤条件
	if req.Filter != nil {
		if req.Filter.SearchText != "" {
			req.Filter.SearchText = SanitizeSearchText(req.Filter.SearchText)
		}
		if len(req.Filter.SQLType) > 0 {
			log.Infof("applying SQL type filter: %v", req.Filter.SQLType)
		}
	}

	// 8. 读取文件内容进行分页和过滤
	scanner := bufio.NewScanner(file)
	scanner.Buffer(make([]byte, 0, 64*1024), MaxLineLength)

	var allLines []message.SQLLine
	lineNumber := 0
	sqlCount := make(map[string]int)
	totalMemoryUsed := 0

	// 读取所有符合条件的行
	for scanner.Scan() {
		lineBytes := scanner.Bytes()
		lineLen := len(lineBytes)

		// 检查内存使用限制
		if totalMemoryUsed+lineLen > MaxMemoryUsage {
			log.Warnf("memory usage limit reached for task %d", req.TaskId)
			break
		}

		line := string(lineBytes)
		lineNumber++
		totalMemoryUsed += lineLen

		// 应用过滤器
		if req.Filter != nil {
			if !s.matchSQLFilter(line, req.Filter) {
				continue
			}
		}

		// 解析SQL类型
		sqlType := s.detectSQLType(line)
		tableInfo := s.extractTableInfo(line, sqlType)

		// 统计SQL类型
		sqlCount[sqlType]++

		allLines = append(allLines, message.SQLLine{
			LineNumber: lineNumber,
			Content:    line,
			SQLType:    sqlType,
			TableInfo:  tableInfo,
		})
	}

	// 检查scanner错误
	if err := scanner.Err(); err != nil {
		return nil, nil, WrapSafeError(err, "failed to read file content")
	}

	// 9. 计算分页
	totalCount := len(allLines)
	startIndex := (req.Page - 1) * req.PageSize
	endIndex := startIndex + req.PageSize

	var pagedLines []message.SQLLine
	if startIndex < totalCount {
		if endIndex > totalCount {
			endIndex = totalCount
		}
		pagedLines = allLines[startIndex:endIndex]
	}

	// 构建响应
	resp := &message.GetFixSQLContentResp{
		TaskId:   req.TaskId,
		FilePath: req.FilePath,
		FileSize: fileInfo.Size(),
		Lines:    pagedLines,
		Summary: &message.SQLSummary{
			TotalLines: totalCount,
			SQLCount:   sqlCount,
			HasMore:    endIndex < totalCount,
		},
	}

	// 构建分页信息
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    int64(totalCount),
	}

	// 不记录文件名等敏感信息
	log.Infof("get fix sql content completed, taskId:%d, totalLines:%d, pageLines:%d",
		req.TaskId, totalCount, len(pagedLines))

	return resp, page, nil
}

// 辅助方法：检测SQL类型
func (s *Service) detectSQLType(line string) string {
	upperLine := strings.ToUpper(strings.TrimSpace(line))
	switch {
	case strings.HasPrefix(upperLine, "INSERT"):
		return "INSERT"
	case strings.HasPrefix(upperLine, "DELETE"):
		return "DELETE"
	case strings.HasPrefix(upperLine, "UPDATE"):
		return "UPDATE"
	case strings.HasPrefix(upperLine, "--") || strings.HasPrefix(upperLine, "/*"):
		return "COMMENT"
	default:
		return "OTHER"
	}
}

// 辅助方法：提取表信息
func (s *Service) extractTableInfo(line string, sqlType string) string {
	upperLine := strings.ToUpper(strings.TrimSpace(line))

	switch sqlType {
	case "INSERT":
		// INSERT INTO schema.table ...
		if idx := strings.Index(upperLine, "INTO"); idx != -1 {
			parts := strings.Fields(upperLine[idx+4:])
			if len(parts) > 0 {
				return strings.Trim(parts[0], "(")
			}
		}
	case "DELETE":
		// DELETE FROM schema.table ...
		if idx := strings.Index(upperLine, "FROM"); idx != -1 {
			parts := strings.Fields(upperLine[idx+4:])
			if len(parts) > 0 {
				return parts[0]
			}
		}
	case "UPDATE":
		// UPDATE schema.table SET ...
		parts := strings.Fields(upperLine)
		if len(parts) > 1 {
			return parts[1]
		}
	}
	return ""
}

// 辅助方法：匹配SQL过滤器
func (s *Service) matchSQLFilter(line string, filter *message.SQLContentFilter) bool {
	// SQL类型过滤
	if len(filter.SQLType) > 0 {
		sqlType := s.detectSQLType(line)
		found := false
		for _, t := range filter.SQLType {
			// 确保大小写一致性比较
			if strings.ToUpper(t) == strings.ToUpper(sqlType) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// 文本搜索
	if filter.SearchText != "" {
		if filter.CaseSensitive {
			return strings.Contains(line, filter.SearchText)
		} else {
			return strings.Contains(strings.ToLower(line), strings.ToLower(filter.SearchText))
		}
	}

	return true
}
