package service

import (
	"gitee.com/pingcap_enterprise/tms/server/service/assessment"
	"gitee.com/pingcap_enterprise/tms/server/service/channel"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/server/service/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/service/datasource"
	"gitee.com/pingcap_enterprise/tms/server/service/increment"
	"gitee.com/pingcap_enterprise/tms/server/service/migration"
	"gitee.com/pingcap_enterprise/tms/server/service/mysql"
	"gitee.com/pingcap_enterprise/tms/server/service/objectparser"
	"gitee.com/pingcap_enterprise/tms/server/service/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/server/service/statistics"
	"gitee.com/pingcap_enterprise/tms/server/service/template"
)

var defaultService *service

type service struct {
	DatasourceService        *datasource.Service
	TemplateService          *template.Service
	DataCompareService       *datacompare.Service
	AssessmentService        *assessment.Service
	StructMigrationService   *migration.StructureMigrationService
	FullDataMigrationService *migration.FullDataMigrationService
	CSVMigrationService      *migration.CSVMigrationService
	ChannelService           *channel.Service
	StatisticsService        *statistics.Service
	SQLAnalyzerService       *sqlanalyzer.Service
	IncrementService         *increment.Service
	ObjectParserService      *objectparser.Service
	MySQLService             *mysql.Service
	SystemInfoService        *common.Service
}

func InitService() {
	defaultService = &service{
		DatasourceService:        datasource.NewDataSourceService(),
		TemplateService:          template.NewTemplateService(),
		DataCompareService:       datacompare.NewDataCompareService(),
		AssessmentService:        assessment.NewAssessmentService(),
		StructMigrationService:   migration.NewStructureMigrationService(),
		FullDataMigrationService: migration.NewFullDataMigrationService(),
		CSVMigrationService:      migration.NewCSVMigrationService(),
		ChannelService:           channel.NewChannelService(),
		StatisticsService:        statistics.NewTidbStatsService(),
		SQLAnalyzerService:       sqlanalyzer.NewSqlAnalyzerService(),
		SystemInfoService:        common.NewSystemInfoService(),
		IncrementService:         increment.NewIncrementService(),
		ObjectParserService:      objectparser.NewObjectParserService(),
		MySQLService:             mysql.NewMySQLService(),
	}
}

func GetMySQLService() *mysql.Service {
	return defaultService.MySQLService
}

func GetDatasourceService() *datasource.Service {
	return defaultService.DatasourceService
}

func GetTemplateService() *template.Service {
	return defaultService.TemplateService
}

func GetDataCompareService() *datacompare.Service {
	return defaultService.DataCompareService
}

func GetAssessmentService() *assessment.Service {
	return defaultService.AssessmentService
}

func GetStructMigrationService() *migration.StructureMigrationService {
	return defaultService.StructMigrationService
}

func GetFullDataMigrationService() *migration.FullDataMigrationService {
	return defaultService.FullDataMigrationService
}

func GetChannelService() *channel.Service {
	return defaultService.ChannelService
}

func GetStatisticsService() *statistics.Service {
	return defaultService.StatisticsService
}

func GetCSVMigrationService() *migration.CSVMigrationService {
	return defaultService.CSVMigrationService
}

func GetSQLAnalyzerService() *sqlanalyzer.Service {
	return defaultService.SQLAnalyzerService
}

func GetIncrementService() *increment.Service {
	return defaultService.IncrementService
}

func GetObjectParserService() *objectparser.Service {
	return defaultService.ObjectParserService
}

func GetSystemInfoService() *common.Service {
	return defaultService.SystemInfoService
}
