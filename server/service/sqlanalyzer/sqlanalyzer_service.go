package sqlanalyzer

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"

	sqlcommon "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/deploy"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/tool"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze"
	sqlanalyzepkg "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"

	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/envdeploy"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

// Service api interface for sqlanalyzer service
type Service struct {
}

// NewSqlAnalyzerService create a new sqlanalyzer service
func NewSqlAnalyzerService() *Service {
	return &Service{}
}

func (i Service) ListSQLAnalyzeTask(ctx context.Context, req *message.ListSQLAnalyzeTaskReq) (*message.ListSQLAnalyzeTaskResp, error) {

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		log.Errorf("GetTask, list sql analyze task failed, taskId: %d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	taskMode, getTaskModeErr := getTaskMode(req.TaskModeInt)
	if getTaskModeErr != nil {
		log.Errorf("getTaskMode, list sql analyze task failed, taskId: %d, taskMode: %d, err: %v", req.TaskId, req.TaskModeInt, getTaskModeErr)
		return nil, getTaskModeErr
	}

	query := sqlanalyzer.EnvDeployTask{
		TaskId:   req.TaskId,
		TaskMode: taskMode.String(),
	}

	var taskList []*sqlanalyzer.EnvDeployTask
	var getListErr error

	taskList, getListErr = models.GetSqlAnalyzerReaderWriter().ListSQLAnalyzeTasks(ctx, &query)
	if getListErr != nil {
		log.Errorf("ListSQLAnalyzeTasks, list sql analyze task failed, taskId: %d, err: %v", req.TaskId, getListErr)
		return nil, getListErr
	}

	log.Infof("ListSQLAnalyzeTasks, taskId: %d, taskList size: %d", req.TaskId, len(taskList))
	if len(taskList) == 0 {
		log.Infof("ListSQLAnalyzeTasks, no sql analyze task found, taskId: %d", req.TaskId)
		envDeployTasks := buildSQLAnalyzeEnvDeployTasks(taskInfo)
		createErr := models.GetSqlAnalyzerReaderWriter().BatchCreateEnvDeployTask(ctx, envDeployTasks)
		if createErr != nil {
			log.Errorf("create default sqlanalyze tasks failed, taskId: %d, err: %v", req.TaskId, createErr)
			return nil, createErr
		}

		taskList, getListErr = models.GetSqlAnalyzerReaderWriter().ListSQLAnalyzeTasks(ctx, &query)
		if getListErr != nil {
			log.Errorf("ListSQLAnalyzeTasks, save success but list sql analyze task failed, taskId: %d, err: %v", req.TaskId, getListErr)
			return nil, getListErr
		}
	}

	var resp message.ListSQLAnalyzeTaskResp
	resp.Tasks = lo.Map(taskList, func(task *sqlanalyzer.EnvDeployTask, _ int) message.SQLAnalyzerTask {
		return buildSQLAnalyzerTaskMessage(task)
	})
	return &resp, nil
}

func (i Service) UpdateSQLAnalyzeTask(ctx context.Context, req *message.UpdateSQLAnalyzeTaskReq) (*message.UpdateSQLAnalyzeTaskResp, error) {
	updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTaskSQLById(ctx, req.SQLAnalyzeId, req.TaskSQL)
	if updateErr != nil {
		log.Errorf("UpdateSQLAnalyzeTaskSQLById, update sql analyze task failed, err: %v", updateErr)
		return nil, updateErr
	}
	return &message.UpdateSQLAnalyzeTaskResp{}, nil
}

func clearDeployTaskStatus(ctx context.Context, tasks []*sqlanalyzer.EnvDeployTask) {
	if len(tasks) == 0 {
		return
	}
	models.GetSqlAnalyzerReaderWriter().ClearEnvDeployTask(ctx, tasks)
}

func (i Service) ExecuteSQLAnalyzeEnvDeployTask(ctx context.Context, req *message.ExecuteSQLAnalyzeEnvDeployTaskReq) (*message.ExecuteSQLAnalyzeEnvDeployTaskResp, error) {
	taskMode, err := getTaskMode(req.TaskModeInt)
	if err != nil {
		log.Errorf("getTaskMode, execute sql analyze task failed, taskId: %d, taskMode: %d, err: %v", req.TaskId, req.TaskModeInt, err)
		return nil, err
	}
	validateErr := envdeploy.ValidateRunMode(req.RunMode)
	if validateErr != nil {
		log.Errorf("validateRunMode, execute sql analyze task failed, taskId: %d, runMode: %d, err: %v", req.TaskId, req.RunMode, validateErr)
		return nil, validateErr
	}

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("GetTask, execute sql analyze task failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}
	log.Infof("execute sql analyze task, taskId: %d, channelId: %d, sqlAnalyzeIds: %v", taskInfo.TaskID, taskInfo.ChannelId, req.SQLAnalyzeIds)

	parameter, err := sqlanalyze.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter, execute sql analyze task failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	query := &sqlanalyzer.EnvDeployTask{TaskMode: taskMode.String(), TaskId: req.TaskId}
	sqlAnalyzerDeployTasks, err := models.GetSqlAnalyzerReaderWriter().ListSQLAnalyzeTasks(ctx, query)
	if err != nil {
		log.Errorf("ListSQLAnalyzeTasks, execute sql analyze task failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}
	defaultAnalyzeTaskIds := lo.Map(sqlAnalyzerDeployTasks, func(task *sqlanalyzer.EnvDeployTask, _ int) int {
		return task.SQLAnalyzeId
	})

	// left, right := lo.Difference([]int{0, 1, 2, 3, 4, 5}, []int{0, 2, 6})
	// ==> []int{1, 3, 4, 5}, []int{6}
	missingIds, invalidIds := lo.Difference(defaultAnalyzeTaskIds, req.SQLAnalyzeIds)
	if len(invalidIds) != 0 {
		err := fmt.Errorf("invalid sql analyze id: %v", invalidIds)
		log.Errorf("execute sql analyze task failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}
	log.Debugf("execute sql analyze task, taskId:%d, missing ids: %v, invalid ids: %v", req.TaskId, missingIds, invalidIds)
	missingExecIdMap := lo.SliceToMap(missingIds, func(id int) (int, bool) {
		return id, true
	})

	deployTasks := make([]*sqlanalyzer.EnvDeployTask, 0)
	for idx := range sqlAnalyzerDeployTasks {
		if missingExecIdMap[sqlAnalyzerDeployTasks[idx].SQLAnalyzeId] {
			sqlAnalyzerDeployTasks[idx].IsIgnore = "Y"
		} else {
			sqlAnalyzerDeployTasks[idx].IsIgnore = "N"
			deployTasks = append(deployTasks, sqlAnalyzerDeployTasks[idx])
		}
	}

	// 如果没有需要执行的任务，直接返回
	if len(deployTasks) == 0 {
		return &message.ExecuteSQLAnalyzeEnvDeployTaskResp{}, nil
	}

	// purpose: only update IsIgnore flags
	if updateErr := models.GetSqlAnalyzerReaderWriter().BatchUpdateSQLAnalyzeTask(ctx, sqlAnalyzerDeployTasks); updateErr != nil {
		log.Errorf("BatchUpdateSQLAnalyzeTask, execute sql analyze task failed, taskId: %d, err: %v", req.TaskId, updateErr)
		return nil, updateErr
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		return nil, fmt.Errorf("get channel info failed, taskId: %d, channelId: %d, err: %v", req.TaskId, taskInfo.ChannelId, err)
	}

	go func() {

		dbConns, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
		if err != nil {
			log.Errorf("setUpDatabaseConnsAndCollation failed, taskId:%d, channelId: %d, err: %v", req.TaskId, taskInfo.ChannelId, err)
			return
		}

		for idx := range deployTasks {
			targetAnalyzeTask := deployTasks[idx]
			targetAnalyzeTask.LastRunTime = time.Now()
			targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusRunning)
			targetAnalyzeTask.TaskLog = "开始执行部署任务"

			if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
				log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, taskId: %d, err: %v", req.TaskId, updateErr)
				return
			}

			clearDeployTaskStatus(ctx, deployTasks[idx+1:])

			deployTask, hit := deploy.FindDeployTask(targetAnalyzeTask)
			if !hit {
				err = errors.New("not found deploy task:" + targetAnalyzeTask.TaskName)
				log.Errorf("FindDeployTask failed, taskId:%d, taskName:%s, err:%v", req.TaskId, targetAnalyzeTask.TaskName, err)
				return
			}

			if deployTask.HasToValidateParam() {
				validateErr := parameter.ValidateParam()
				if validateErr != nil {
					log.Errorf("validate param failed, err:%v", validateErr)
					updateErr := updateAnalyzeTaskValidateFailed(ctx, validateErr, targetAnalyzeTask)
					if updateErr != nil {
						log.Errorf("validate param failed and update tms meta failed, taskId:%d, err:%v", req.TaskId, updateErr)
						return
					}
					return
				}
				log.Infof("validate param, taskId:%d, including_schemas is %v", req.TaskId, parameter.SQLFactor.IncludingSchemas)
			}
			log.Infof("not validate param, taskId:%d, including_schemas is %v, deployTask:%v", req.TaskId, parameter.SQLFactor.IncludingSchemas, deployTask)

			if deployTask.HasPreCheckSQLs() {
				shouldSkip, preCheckErr := executePreCheckSQLs(ctx, req.RunMode, deployTask, dbConns, targetAnalyzeTask, parameter)
				log.Infof("executePreCheckSQLs result, taskId:%d, shouldSkip:%v, preCheckErr:%v", req.TaskId, shouldSkip, preCheckErr)
				if preCheckErr != nil {
					if shouldSkip {
						log.Infof("execute pre check failed and skip, taskId:%d, task name:%s, err: %v", req.TaskId, deployTask.TaskName, preCheckErr)
						continue
					} else {
						log.Errorf("execute pre check failed and return, taskId:%d, task name:%s, err: %v", req.TaskId, deployTask.TaskName, preCheckErr)
						return
					}
				}
			}

			substituteSQLs := deploy.SubstituteSQL(targetAnalyzeTask, parameter, deployTask)

			execErr := executeSQLs(ctx, substituteSQLs, targetAnalyzeTask, deployTask, taskInfo, dbConns, parameter)
			if execErr != nil {
				log.Errorf("execute sql failed, taskId:%d, task name:%s, err: %v", req.TaskId, deployTask.TaskName, execErr)
				return
			}
		}

		// 设置channel标识位
		deployTasks, err := models.GetSqlAnalyzerReaderWriter().ListSQLAnalyzeTasks(ctx, &sqlanalyzer.EnvDeployTask{
			TaskMode: taskMode.String(),
			TaskId:   req.TaskId,
		})
		if err != nil {
			log.Errorf("after deploy, refetch deploy tasks failed, taskId:%d, err: %v", req.TaskId, err)
			return
		}
		successNum := 0
		for _, deployTask := range deployTasks {
			if deployTask.TaskNumber > 8 {
				continue
			}
			if deployTask.TaskStatus == int(constants.SQLAnalyzeTaskStatusSuccess) {
				successNum++
			}
		}
		if successNum >= 8 {
			channelInfo.SQLAnalyzerEnvStatus = true
			if _, err := models.GetChannelReaderWriter().UpdateChannel(ctx, channelInfo); err != nil {
				log.Errorf("after deploy, update channel info failed, taskId:%d, channelId:%d, err: %v", req.TaskId, taskInfo.ChannelId, err)
				return
			}
		}

	}()
	return &message.ExecuteSQLAnalyzeEnvDeployTaskResp{}, nil
}

func executeSQLs(ctx context.Context, substituteSQLs []string, targetAnalyzeTask *sqlanalyzer.EnvDeployTask, defaultAnalyzeTask deploy.EnvironmentDeployTask, taskInfo *task.Task, dbConns *oracle.Oracle, parameter *structs.Parameter) error {
	log.Debugf("executing sqls %v", strings.Join(substituteSQLs, ";@;"))
	if targetAnalyzeTask.SQLModel != constants.DeployModeCheckSQL {
		for _, substituteSQL := range substituteSQLs {
			substituteSQL = strings.Replace(substituteSQL, "\nEND;\n/", "\nEND;", 1)
			_, err := dbConns.OracleDB.ExecContext(ctx, substituteSQL)
			if err != nil {
				if defaultAnalyzeTask.ShouldIgnoreError(err) {
					log.Debugf("ignore error, task name:%s, err: %v, sql:%s", defaultAnalyzeTask.TaskName, err, stringutil.RemoveSpecialLetterForLog(substituteSQL))
					continue
				}

				log.Errorf("execute sql failed, task name:%s, err: %v, sql:%s", defaultAnalyzeTask.TaskName, err, stringutil.RemoveSpecialLetterForLog((substituteSQL)))
				updateErr := updateAnalyzeTaskFailed(ctx, err, targetAnalyzeTask, defaultAnalyzeTask)
				if updateErr != nil {
					return updateErr
				}
				return err
			}
			log.Debugf("execute sql analyze task, sql analyze task id: %d, sql: %s", targetAnalyzeTask.SQLAnalyzeId, stringutil.RemoveSpecialLetterForLog(substituteSQL))
		}
	}

	var fetchJobSQL string

	switch targetAnalyzeTask.TaskName {
	case constants.SQLAnalyzeCreateAutoGetCurrentSQLJob, constants.SQLAnalyzeCreateAutoGetHistorySQLJob:
		fetchJobSQL = defaultAnalyzeTask.GetFetchJobSQL(parameter.GetUsername())
	}
	if fetchJobSQL != "" {
		log.Infof("exec fetch job sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(fetchJobSQL), defaultAnalyzeTask.TaskName, targetAnalyzeTask.SQLModel, defaultAnalyzeTask.UsernameCase)
		_, jobRets, err := oracle.Query(ctx, dbConns.OracleDB, fetchJobSQL)
		if err != nil {
			log.Errorf("fetch job id failed, task name:%s, err: %v", defaultAnalyzeTask.TaskName, err)
			updateErr := updateAnalyzeTaskFailed(ctx, err, targetAnalyzeTask, defaultAnalyzeTask)
			if updateErr != nil {
				return updateErr
			}
			return err
		}
		if len(jobRets) == 0 {
			err = errors.New("job id is empty")
			log.Errorf("fetch job id failed, task name:%s, err: %v", defaultAnalyzeTask.TaskName, err)
			updateErr := updateAnalyzeTaskFailed(ctx, err, targetAnalyzeTask, defaultAnalyzeTask)
			if updateErr != nil {
				return updateErr
			}
			return err
		}
		jobIdStr := strings.TrimSpace(jobRets[0]["JOB"])
		targetAnalyzeTask.Comment = jobIdStr

		time.Sleep(time.Second * time.Duration(2))
		brokenSQL := fmt.Sprintf(common.BrokenSQLTemplate, jobIdStr)
		log.Debugf("broken sql: %s", stringutil.RemoveSpecialLetterForLog(brokenSQL))
		_, _, brokenErr := oracle.Query(ctx, dbConns.OracleDB, brokenSQL)
		if brokenErr != nil {
			log.Errorf("broken job failed, task name:%s, jobId:%s, err: %v", defaultAnalyzeTask.TaskName, jobIdStr, brokenErr)
		}
	}

	targetAnalyzeTask.LastRunTime = time.Now()
	targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusSuccess)
	targetAnalyzeTask.TaskLog = defaultAnalyzeTask.GetSqlSuccessMessage()
	if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
		log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
		return updateErr
	}

	if _, updateErr := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo); updateErr != nil {
		log.Errorf("UpdateTask, execute sql analyze task failed, err: %v", updateErr)
		return updateErr
	}
	return nil
}

func updateAnalyzeTaskFailed(ctx context.Context, err error, targetAnalyzeTask *sqlanalyzer.EnvDeployTask, defaultAnalyzeTask deploy.EnvironmentDeployTask) error {
	targetAnalyzeTask.LastRunTime = time.Now()
	targetAnalyzeTask.TaskLog = defaultAnalyzeTask.GetExecSQLFailureMessage() + ", err:" + err.Error()
	targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
	if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
		log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
		return updateErr
	}
	return nil
}

func updateAnalyzeTaskValidateFailed(ctx context.Context, err error, targetAnalyzeTask *sqlanalyzer.EnvDeployTask) error {
	targetAnalyzeTask.LastRunTime = time.Now()
	targetAnalyzeTask.TaskLog = "对象创建失败，" + err.Error()
	targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
	if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
		log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
		return updateErr
	}
	return nil
}

func executePreCheckSQLs(ctx context.Context, runMode int, defaultAnalyzeTask deploy.EnvironmentDeployTask, dbConns *oracle.Oracle, targetAnalyzeTask *sqlanalyzer.EnvDeployTask, parameter *structs.Parameter) (shouldSkip bool, err error) {
	username := strings.TrimSpace(parameter.GetUsername())
	for _, preCheckStep := range defaultAnalyzeTask.GetDeployPreCheckSteps() {
		targetAnalyzeTask.LastRunTime = time.Now()
		preCheckFunc := preCheckStep.GetPreCheckFunc()

		if targetAnalyzeTask.SQLModel == constants.DeployModeCheckSQL {
			shouldSkip, err = preCheckFunc(ctx, dbConns.OracleDB, runMode, username, strings.Split(targetAnalyzeTask.TaskSQL, "\n"), parameter)
		} else {
			shouldSkip, err = preCheckFunc(ctx, dbConns.OracleDB, runMode, username, nil, parameter)
		}
		log.Infof("deploy task:%s, shouldSkip:%v, runMode:%v, err:%v", defaultAnalyzeTask.TaskName, shouldSkip, runMode, err)
		if err != nil {
			log.Errorf("call precheck failed, task name: %v, err: %v", defaultAnalyzeTask.TaskName, err)
			targetAnalyzeTask.LastRunTime = time.Now()
			targetAnalyzeTask.TaskLog = err.Error()
			targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
			if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
				log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
				return false, updateErr
			}
			if shouldSkip {
				targetAnalyzeTask.TaskLog = preCheckStep.GetPreCheckSkipMessage()
				targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusSuccess)
				if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
					log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
					return false, updateErr
				}
				continue
			} else {
				targetAnalyzeTask.TaskLog = err.Error()
				targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusFailed)
				if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
					log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
					return false, updateErr
				}
			}
		} else {
			targetAnalyzeTask.TaskLog = preCheckStep.GetPreCheckSuccessMessage()
			targetAnalyzeTask.TaskStatus = int(constants.SQLAnalyzeTaskStatusSuccess)
			if updateErr := models.GetSqlAnalyzerReaderWriter().UpdateSQLAnalyzeTask(ctx, targetAnalyzeTask); updateErr != nil {
				log.Errorf("UpdateSQLAnalyzeTask, execute sql analyze task failed, err: %v", updateErr)
				return false, updateErr
			}
		}
	}
	return shouldSkip, err
}

func buildSQLAnalyzerTaskMessage(sa *sqlanalyzer.EnvDeployTask) message.SQLAnalyzerTask {
	return message.SQLAnalyzerTask{
		SQLAnalyzeId: sa.SQLAnalyzeId,
		TaskNumber:   sa.TaskNumber,
		TaskName:     sa.TaskName,
		IsIgnore:     sa.IsIgnore,
		TaskStatus:   sa.TaskStatus,
		TaskLog:      sa.TaskLog,
		TaskSQL:      sa.TaskSQL,
		TaskMode:     sa.TaskMode,
		LastRunTime:  sa.LastRunTime,
		TaskId:       sa.TaskId,
		Comment:      sa.Comment,
	}
}

func GetSqlAnalyzerProgress(ctx context.Context, req *message.GetTaskProgressReq) (*message.GetTaskProgressResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get task info failed, taskId:%d, err: %v", req.TaskId, err))
		return nil, err
	}
	taskLogDetails, err := models.GetTaskReaderWriter().ListTaskLogDetailByTaskIdAndLogTime(ctx, req.TaskId, req.StartTime)
	if err != nil {
		log.Errorf(fmt.Sprintf("get ListTaskLogDetailByTaskIdAndLogTime failed, taskId:%d, err: %v", req.TaskId, err))
		return nil, err
	}

	sqlParam, buildErr := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		log.Errorf(fmt.Sprintf("build sql param failed, taskId:%d, err: %v", req.TaskId, buildErr))
		return nil, buildErr
	}

	lastUpdateTime := req.StartTime
	var progressLog []*message.TaskProgressLogDetail
	for _, v := range taskLogDetails {
		progressLog = append(progressLog, &message.TaskProgressLogDetail{
			LogTime:    v.LogTime,
			LogLevel:   v.LogLevel,
			LogMessage: v.LogMessage,
		})
		if v.UpdatedAt.After(lastUpdateTime) {
			lastUpdateTime = v.UpdatedAt
		}
	}

	if sqlParam.IsRunVersionV1() {
		sqlExecSummary, getErr := models.GetSqlAnalyzerReaderWriter().GetSqlExecSummary(ctx, req.TaskId)
		if getErr != nil {
			log.Errorf(fmt.Sprintf("get sql exec summary failed, taskId:%d, err: %v", req.TaskId, getErr))
			return nil, getErr
		}

		progressVal := float64(sqlExecSummary.SuccessNum+
			sqlExecSummary.FailedNum+
			sqlExecSummary.TimeoutNum+
			sqlExecSummary.SchemaNotMatchNum) / float64(sqlExecSummary.TotalNum)
		// Cap progress at 100% to handle edge cases
		if progressVal > 1.0 {
			progressVal = 1.0
		}
		progress := &message.GetTaskProgressResp{
			TaskId:             req.TaskId,
			StartTime:          req.StartTime,
			TotalNums:          sqlExecSummary.TotalNum,
			SuccessNums:        sqlExecSummary.SuccessNum,
			FailedNums:         sqlExecSummary.FailedNum,
			TimeoutNums:        sqlExecSummary.TimeoutNum,
			SchemaNotMatchNums: sqlExecSummary.SchemaNotMatchNum,
			RunningNums:        0,
			Progress:           progressVal,
			LastUpdateTime:     lastUpdateTime,
			TaskLogFile:        config.BuildSQLAnalyzerLogFilePath(taskInfo.ChannelId, taskInfo.TaskID),
		}
		progress.ProgressLogInfo = &message.TaskProgressLogInfo{
			TaskId:        req.TaskId,
			LogStartTime:  req.StartTime,
			LogCount:      len(progressLog),
			LogDetailList: progressLog,
		}
		return progress, nil
	} else {
		oracleConn, getConnErr := tool.NewDatabaseConnector(sqlParam.OracleConnStr, nil).OpenOracle()
		if getConnErr != nil {
			log.Errorf("open oracle db failed, taskId:%d, err:%v", req.TaskId, getConnErr)
			return nil, getConnErr
		}
		defer oracleConn.Close()

		sourceSchemas := sqlParam.GetIncludingSchemasBySQLSource()
		sqlsetName := sqlParam.SQLFactor.GetSQLSetName()
		sqlsetOwner := sqlParam.GetOracleConnStr().GetUpperUser()

		if sqlParam.IsFileMode() {
			sqlsetOwner = sqlParam.AppSQLsFilename
			sourceSchemas = sqlParam.GetIncludingSchemasBySQLSource()
		}

		sqlsetSummaries, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsSummaries(ctx, oracleConn, req.TaskId, sqlsetName, sqlsetOwner, sourceSchemas)
		if getErr != nil {
			log.Errorf("get sqlset summaries failed, taskId:%d, err: %v", req.TaskId, getErr)
			return nil, nil
		}

		// Get filtered SQL count (ignored and resolved)
		filteredStatusList := []string{constants.UserOperateStatusIgnored, constants.UserOperateStatusResolved}
		filteredCount, _ := models.GetSqlAnalyzerReaderWriter().CountSqlStmtUserOperationV2ByTaskIdAndStatus(ctx, req.TaskId, filteredStatusList)

		totalNum := int(sqlsetSummaries.GetTotalTotalNum())
		successNum := int(sqlsetSummaries.GetTotalSuccessNum())
		failedNum := int(sqlsetSummaries.GetTotalFailedNum())
		timeoutNum := int(sqlsetSummaries.GetTotalTimeoutNum())
		filteredNum := int(filteredCount)

		// Calculate progress including filtered SQLs
		var progressVal float64
		if totalNum > 0 {
			progressVal = float64(successNum+failedNum+timeoutNum+filteredNum) / float64(totalNum)
			// Cap progress at 100% to handle edge cases
			if progressVal > 1.0 {
				progressVal = 1.0
			}
		}

		progress := &message.GetTaskProgressResp{
			TaskId:         req.TaskId,
			StartTime:      req.StartTime,
			TotalNums:      totalNum,
			SuccessNums:    successNum,
			FailedNums:     failedNum,
			TimeoutNums:    timeoutNum,
			FilteredNums:   filteredNum,
			Progress:       progressVal,
			LastUpdateTime: lastUpdateTime,
			TaskLogFile:    config.BuildSQLAnalyzerLogFilePath(taskInfo.ChannelId, taskInfo.TaskID),
		}
		progress.ProgressLogInfo = &message.TaskProgressLogInfo{
			TaskId:        req.TaskId,
			LogStartTime:  req.StartTime,
			LogCount:      len(progressLog),
			LogDetailList: progressLog,
		}
		return progress, nil
	}
}

func getTaskMode(taskModeInt int) (constants.SQLAnalyzeTaskMode, error) {
	switch taskModeInt {
	case constants.SQLAnalyzeTaskModeLocalInt:
		return constants.SQLAnalyzeTaskModeLocal, nil
	case constants.SQLAnalyzeTaskModeNotLocalInt:
		return constants.SQLAnalyzeTaskModeNotLocal, nil
	default:
		return "", errors.New("invalid task mode, only allow [LOCAL]0 or [非LOCAL]1")
	}
}

func (i Service) ListSQLAnalyzerTaskJobs(ctx context.Context, req *message.ListSQLAnalyzeTaskJobsReq) (*message.ListSQLAnalyzeTaskJobsResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get task info failed, err: %v", err))
		return nil, err
	}
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get channel info failed, err: %v", err))
		return nil, err
	}
	sourceConn, sourceDS, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("SetUpSourceDatabaseConnFunc failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	query := &sqlanalyzer.EnvDeployTask{TaskId: req.TaskId}
	analyzeTasks, err := models.GetSqlAnalyzerReaderWriter().ListSQLAnalyzeTasks(ctx, query)

	var (
		currentJobId string
		historyJobId string
	)
	resp := &message.ListSQLAnalyzeTaskJobsResp{}

	for _, analyzeTask := range analyzeTasks {
		if analyzeTask.Comment == "" {
			continue
		}
		if analyzeTask.TaskName == constants.SQLAnalyzeCreateAutoGetCurrentSQLJob {
			currentJobId = analyzeTask.Comment
		}
		if analyzeTask.TaskName == constants.SQLAnalyzeCreateAutoGetHistorySQLJob {
			historyJobId = analyzeTask.Comment
		}
	}

	username := strings.TrimSpace(strings.ToLower(sourceDS.UserName))

	resp.CurrentJob.JobId = "未创建"
	resp.CurrentJob.JobNextDate = "未创建"
	resp.HistoryJob.JobId = "未创建"
	resp.HistoryJob.JobNextDate = "未创建"

	errG := errgroup.Group{}
	errG.SetLimit(2) // 仅有两个并发数

	// 查询 采集当前SQL的任务
	errG.Go(func() error {
		if currentJobId != "" {
			currentJobSQL := strings.ReplaceAll(common.QueryCursorJobSQL, sqlcommon.VAR_USERNAME, username)
			_, rets, err := oracle.Query(ctx, sourceConn.OracleDB, currentJobSQL)
			if err != nil {
				log.Errorf("query job status failed, err:%v", err)
				return fmt.Errorf("查询JOB失败, err:%v", err)
			}
			if len(rets) >= 2 {
				resp.CurrentJob.JobNextDate = "存在多个任务"
				resp.CurrentJob.JobId = "存在多个任务"
				return nil
			}
			for _, ret := range rets {
				retJobId := ret["JOB"]
				retBroken := ret["BROKEN"]
				if retJobId == currentJobId {
					resp.CurrentJob.JobId = currentJobId
					resp.CurrentJob.Broken = retBroken
					if retBroken == "N" {
						resp.CurrentJob.JobNextDate = ret["NEXT_DATE"]
					} else {
						resp.CurrentJob.JobNextDate = "任务未启动"
					}
					break
				}
			}
		}
		return nil
	})

	// 查询 采集历史SQL的任务
	errG.Go(func() error {
		if historyJobId != "" {
			historyJobSQL := strings.ReplaceAll(common.QueryAwrJobSQL, sqlcommon.VAR_USERNAME, username)
			_, rets, err := oracle.Query(ctx, sourceConn.OracleDB, historyJobSQL)
			if err != nil {
				log.Errorf("query job status failed, err:%v", err)
				return fmt.Errorf("查询JOB失败, err:%v", err)
			}
			if len(rets) >= 2 {
				resp.HistoryJob.JobNextDate = "存在多个任务"
				resp.HistoryJob.JobId = "存在多个任务"
				return nil
			}
			for _, ret := range rets {
				retJobId := ret["JOB"]
				retBroken := ret["BROKEN"]
				if retJobId == historyJobId {
					resp.HistoryJob.JobId = historyJobId
					resp.HistoryJob.Broken = retBroken
					if retBroken == "N" {
						resp.HistoryJob.JobNextDate = ret["NEXT_DATE"]
					} else {
						resp.HistoryJob.JobNextDate = "任务未启动"
					}
					break
				}
			}
		}
		return nil
	})

	if err := errG.Wait(); err != nil {
		return nil, err
	}

	// 但凡有一个任务未启动，都需要启用开启按钮
	if resp.CurrentJob.Broken == "Y" || resp.HistoryJob.Broken == "Y" {
		resp.StartJob = true
	}
	// 但凡有一个任务已启动，都需要启用关闭按钮
	if resp.CurrentJob.Broken == "N" || resp.HistoryJob.Broken == "N" {
		resp.StopJob = true
	}

	return resp, nil
}

func (i Service) GetTaskDeployStatus(ctx context.Context, req *message.GetTaskDeployStatusReq) (*message.GetTaskDeployStatusResp, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get task info failed, err: %v", err)
		return nil, err
	}
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel info failed, err: %v", err)
		return nil, err
	}
	log.Errorf("需要修改为通过状态位判断是否部署成功")
	return &message.GetTaskDeployStatusResp{IsDeployed: channelInfo.SQLAnalyzerEnvStatus}, nil
}

func (i Service) StopSpaCollect(ctx context.Context, req *message.StopSpaCollectReq) (*message.StopSpaCollectResp, error) {
	// 获取任务信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Failed to retrieve task info for taskId %d, err:%v", req.TaskId, err)
		return nil, fmt.Errorf("get task info: %w", err)
	}

	// 获取渠道信息
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Failed to retrieve channel info for channelId %d, err:%v", taskInfo.ChannelId, err)
		return nil, fmt.Errorf("get channel info: %w", err)
	}

	// 设置数据库连接
	dbConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("Failed to establish database connection for channelId %d, err:%v", taskInfo.ChannelId, err)
		return nil, fmt.Errorf("setup database connection: %w", err)
	}
	defer dbConn.OracleDB.Close() // 确保数据库连接关闭

	// 获取当前和历史Job ID
	currentJobId, historyJobId, err := getCurrentHistoryJobIdsByTaskId(ctx, taskInfo.TaskID)

	if err != nil {
		return nil, fmt.Errorf("get job ids: %w", err)
	}

	// 检查Job ID是否存在
	if currentJobId == "" || historyJobId == "" {
		err := fmt.Errorf("current or historical SQL task not created for taskId %d", taskInfo.TaskID)
		log.Errorf("Failed to stop SPA collection, err:%v, request:%+v", err, req)
		return nil, err
	}

	_, jobStatus, getJobErr := oracle.Query(ctx, dbConn.OracleDB, "SELECT * FROM DBA_JOBS_RUNNING")
	if getJobErr != nil {
		log.Errorf("Failed to get job status, taskId:%d, err:%v", taskInfo.TaskID, getJobErr)
		return nil, fmt.Errorf("get job status: %v", getJobErr)
	}

	for _, job := range jobStatus {
		log.Infof("Job %s this_date:%s, this_sec:%s, last_date:%s, last_sec:%s", job["JOB"],
			job["THIS_DATE"], job["THIS_SEC"], job["LAST_DATE"], job["LAST_SEC"])
		if job["THIS_DATE"] != "NULL" && job["THIS_DATE"] != "NULLABLE" {
			log.Errorf("Job %s is running, please stop it first", job["JOB"])
			return nil, fmt.Errorf("Job %s is running, please stop it first", job["JOB"])
		}
	}

	// 停止当前和历史Job
	if err := breakJob(ctx, dbConn.OracleDB, currentJobId, "current"); err != nil {
		return nil, err
	}
	if err := breakJob(ctx, dbConn.OracleDB, historyJobId, "historical"); err != nil {
		return nil, err
	}

	return &message.StopSpaCollectResp{}, nil
}

// breakJob 封装Job停止逻辑
func breakJob(ctx context.Context, db *sql.DB, jobId, jobType string) error {
	bsql := fmt.Sprintf(common.BrokenSQLTemplate, jobId)
	if _, _, err := oracle.Query(ctx, db, bsql); err != nil {
		log.Errorf("Failed to break %s job with id %s, err:%v", jobType, jobId, err)
		return fmt.Errorf("break %s job: %w", jobType, err)
	}
	return nil
}

func (i Service) StartSQLFileReplay(ctx context.Context, req *message.StartSQLFileReplayReq) (*message.StartSQLFileReplayResp, error) {
	channelId, taskId := req.ChannelId, req.TaskId
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, channelId, taskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, err: %v", err)
		return nil, err
	}

	if initErr := param.InitSQLFileSource(); initErr != nil {
		log.Errorf("doSQLAnalyzerTask failed. InitSQLFileSource failed, channel id is %d, task id is %d, error: %v", channelId, taskId, initErr)
		return nil, initErr
	}

	runErr := sqlanalyzepkg.ExecuteSQLAnalyzer(ctx, req.ChannelId, req.TaskId)
	if runErr != nil {
		log.Errorf("execution ExecuteSQLAnalyzer failed, err:%s", runErr)
		return nil, runErr
	}
	return nil, nil
}

func (i Service) StartSpaCollect(ctx context.Context, req *message.StartSpaCollectReq) (*message.StartSpaCollectResp, error) {
	_ = tool.CreateSQLAnalyzerOperationLog(ctx, req.ChannelId, req.TaskId, log.LogInfo, "启动采集")

	var lastErr error
	defer func() {
		if lastErr != nil {
			_ = tool.CreateSQLAnalyzerOperationLog(ctx, req.ChannelId, req.TaskId, log.LogError, "启动采集失败："+lastErr.Error())
		}
	}()

	// 先尝试获取JobId，如果没有，执行存储过程； 如果仅存在一个job的过程，报错
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		lastErr = err
		log.Errorf(fmt.Sprintf("get task info failed, err: %v", err))
		return nil, err
	}
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		lastErr = err
		log.Errorf(fmt.Sprintf("get channel info failed, err: %v", err))
		return nil, err
	}
	dbConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		lastErr = err
		log.Errorf("SetUpSourceDatabaseConnFunc failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	currentJobId, historyJobId, getErr := getCurrentHistoryJobIdsByTaskId(ctx, taskInfo.TaskID)
	if getErr != nil {
		lastErr = getErr
		return nil, getErr
	}

	if currentJobId != "" && historyJobId == "" {
		lastErr = fmt.Errorf("采集当前SQL任务已创建，采集历史SQL任务未创建")
		return nil, lastErr
	}
	if currentJobId == "" && historyJobId != "" {
		lastErr = fmt.Errorf("采集历史SQL任务已创建，采集当前SQL任务未创建")
		return nil, lastErr
	}

	errG := errgroup.Group{}
	startCurrentJobMsg := "执行采集当前SQL任务成功"
	startHistoryJobMsg := "执行采集历史SQL任务成功"

	errG.Go(func() error {
		log.Infof("enable current sql job")
		disableBrokenSQL := fmt.Sprintf(common.DisableBrokenSQLTemplate, currentJobId)
		_, _, disableBrokenErr := oracle.Query(ctx, dbConn.OracleDB, disableBrokenSQL)
		if disableBrokenErr != nil {
			startCurrentJobMsg = "执行采集当前SQL任务失败"
			log.Errorf("set job broken status to Y failed, err: %v", disableBrokenErr)
			_ = tool.CreateSQLAnalyzerOperationLog(ctx, req.ChannelId, req.TaskId, log.LogError, "启动当前SQL任务失败："+disableBrokenErr.Error())
			return disableBrokenErr
		}
		return nil
	})

	errG.Go(func() error {
		log.Infof("enable history sql job")
		disableBrokenSQL := fmt.Sprintf(common.DisableBrokenSQLTemplate, historyJobId)
		_, _, disableBrokenErr := oracle.Query(ctx, dbConn.OracleDB, disableBrokenSQL)
		if disableBrokenErr != nil {
			startHistoryJobMsg = "执行采集历史SQL任务失败"
			log.Errorf("set job broken status to Y failed, err: %v", disableBrokenErr)
			_ = tool.CreateSQLAnalyzerOperationLog(ctx, req.ChannelId, req.TaskId, log.LogError, "启动历史SQL任务失败："+disableBrokenErr.Error())
			return disableBrokenErr
		}
		return nil
	})

	if waitErr := errG.Wait(); waitErr != nil {
		return nil, waitErr
	}
	log.Infof("stop exec current and history sql job")
	return &message.StartSpaCollectResp{
		Message: startCurrentJobMsg + ";" + startHistoryJobMsg,
	}, nil
}

func getCurrentHistoryJobIdsByTaskId(ctx context.Context, taskId int) (string, string, error) {
	query := &sqlanalyzer.EnvDeployTask{TaskId: taskId}
	analyzeTasks, err := models.GetSqlAnalyzerReaderWriter().ListSQLAnalyzeTasks(ctx, query)
	if err != nil {
		log.Errorf("ListSQLAnalyzeTasks failed, taskId:%d, err:%v", taskId, err)
		return "", "", err
	}

	var (
		currentJobId string
		historyJobId string
	)

	for _, analyzeTask := range analyzeTasks {
		if analyzeTask.Comment == "" {
			continue
		}
		if analyzeTask.TaskName == constants.SQLAnalyzeCreateAutoGetCurrentSQLJob {
			currentJobId = analyzeTask.Comment
		}
		if analyzeTask.TaskName == constants.SQLAnalyzeCreateAutoGetHistorySQLJob {
			historyJobId = analyzeTask.Comment
		}
	}
	return currentJobId, historyJobId, nil
}

func (i Service) GetSQLSets(ctx context.Context, req *message.GetSQLSetsReq) (*message.GetSQLSetsResp, error) {
	// 获取任务和通道信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	// 构建SQL分析参数
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, channelId: %d, err: %v", taskInfo.TaskID, taskInfo.ChannelId, err)
		return nil, err
	}

	// 根据SourceType获取SQL集合
	sqlSets, err := i.fetchSQLSets(ctx, req, taskInfo, channelInfo, param)
	if err != nil {
		return nil, err
	}

	log.Infof("Get sqlsets completed, taskId: %d, sqlSets count: %d", req.TaskId, len(sqlSets))
	return &message.GetSQLSetsResp{SQLSets: sqlSets}, nil
}

// fetchSQLSets 根据SourceType获取SQL集合
func (i Service) fetchSQLSets(ctx context.Context, req *message.GetSQLSetsReq, taskInfo *task.Task, channelInfo *channel.ChannelInformation, param *structs.Parameter) ([]message.SQLSet, error) {
	if req.SourceType == int(sqlcommon.FileSource) {
		return i.fetchSQLSetsFromFile(ctx, taskInfo, param)
	}
	return i.fetchSQLSetsFromDB(ctx, req, taskInfo, channelInfo, param)
}

// fetchSQLSets 根据SourceType获取SQL集合
func (i Service) fetchSQLSetsStatementCount(ctx context.Context, req *message.GetSQLSetsReq, taskInfo *task.Task, channelInfo *channel.ChannelInformation, param *structs.Parameter) ([]message.SQLSetStatementCount, error) {
	if req.SourceType == int(sqlcommon.FileSource) {
		return i.fetchSQLSetsStatementCountFromFile(ctx, taskInfo, param)
	}
	return i.fetchSQLSetsStatementCountFromDB(ctx, req, taskInfo, channelInfo, param)
}

// fetchSQLSetsFromFile 处理文件源SQL集合
func (i Service) fetchSQLSetsFromFile(ctx context.Context, taskInfo *task.Task, param *structs.Parameter) ([]message.SQLSet, error) {
	log.Infof("Fetching sqlsets from file, taskId: %d, sourceType: %d", taskInfo.TaskID, sqlcommon.FileSource)
	if err := param.InitSQLFileSource(); err != nil {
		log.Errorf("InitSQLFileSource failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, err)
		return nil, err
	}
	return []message.SQLSet{{
		DataSourceName: "本地文件",
		SQLSetName:     "*",
		SQLSetOwner:    "*",
		SchemaName:     "*",
		StatementCount: uint64(len(param.GetFileSQLs())),
	}}, nil
}

// fetchSQLSetsFromFile 处理文件源SQL集合
func (i Service) fetchSQLSetsStatementCountFromFile(ctx context.Context, taskInfo *task.Task, param *structs.Parameter) ([]message.SQLSetStatementCount, error) {
	log.Infof("Fetching sqlsets from file, taskId: %d, sourceType: %d", taskInfo.TaskID, sqlcommon.FileSource)
	if err := param.InitSQLFileSource(); err != nil {
		log.Errorf("InitSQLFileSource failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, err)
		return nil, err
	}
	return []message.SQLSetStatementCount{{
		DataSourceName: "本地文件",
		SQLSetName:     "*",
		SQLSetOwner:    "*",
		StatementCount: uint64(len(param.GetFileSQLs())),
	}}, nil
}

// fetchSQLSetsFromDB 处理数据库源SQL集合
func (i Service) fetchSQLSetsFromDB(ctx context.Context, req *message.GetSQLSetsReq, taskInfo *task.Task, channelInfo *channel.ChannelInformation, param *structs.Parameter) ([]message.SQLSet, error) {
	log.Infof("Fetching sqlsets from DB, taskId: %d, sourceType: %d, datasourceId: %d", req.TaskId, req.SourceType, channelInfo.DatasourceIdS)

	// 建立数据库连接
	dbConn, dataSource, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("SetUpSourceDatabaseConnFunc failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	owner := strings.TrimSpace(strings.ToUpper(dataSource.UserName))
	sqlSetName := param.GetSQLFactor().GetSQLSetName()

	// 默认统计SQL
	defaultCountSQL := buildCountSQL(common.CountSQLSetGroupBySchemaSQLDefaultTemplate, sqlSetName, owner, "")
	rets, err := i.querySQLSets(ctx, dbConn, defaultCountSQL, int64(taskInfo.TaskID), dataSource)
	if err != nil {
		return nil, err
	}

	// 转换结果并计算总数
	sqlSets, totalCount := convertQueryResults(rets, channelInfo.DatasourceNameS, sqlSetName, owner)

	// 如果小于行数限制，使用带条件的SQL重新查询
	if totalCount <= uint64(param.GetSQLFactor().GetSQLRowLimit()) {
		log.Infof("Total statement count is less than row limit, query again with more conditions, taskId: %d, total count: %d, row limit: %d",
			taskInfo.TaskID, totalCount, param.GetSQLFactor().GetSQLRowLimit())
		countSQL := buildCountSQL(common.CountSQLSetGroupBySchemaSQLTemplate, sqlSetName, owner, param.BuildAndCondition(""))
		countSQL = strings.ReplaceAll(countSQL, sqlcommon.VAR_COMMAND_TYPE_LIST, strings.Join(param.GetIncludingSQLTypeStringList(), ","))
		rets, err = i.querySQLSets(ctx, dbConn, countSQL, int64(taskInfo.TaskID), dataSource)
		if err != nil {
			return nil, err
		}
		sqlSets, _ = convertQueryResults(rets, channelInfo.DatasourceNameS, sqlSetName, owner)
	} else {
		log.Infof("Total statement count is greater than row limit, taskId: %d, total count: %d, row limit: %d",
			taskInfo.TaskID, totalCount, param.GetSQLFactor().GetSQLRowLimit())
	}

	return sqlSets, nil
}

// fetchSQLSetsFromDB 处理数据库源SQL集合
func (i Service) fetchSQLSetsStatementCountFromDB(ctx context.Context, req *message.GetSQLSetsReq, taskInfo *task.Task, channelInfo *channel.ChannelInformation, param *structs.Parameter) ([]message.SQLSetStatementCount, error) {
	log.Infof("Fetching sqlsets StatementCount from DB, taskId: %d, sourceType: %d, datasourceId: %d", req.TaskId, req.SourceType, channelInfo.DatasourceIdS)

	// 建立数据库连接
	dbConn, dataSource, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("SetUpSourceDatabaseConnFunc failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	owner := strings.TrimSpace(strings.ToUpper(dataSource.UserName))
	sqlSetName := param.GetSQLFactor().GetSQLSetName()

	// 默认统计SQL
	defaultCountSQL := buildCountSQL(common.CountSQLSetStatementCountSQLDefaultTemplate, sqlSetName, owner, "")
	rets, err := i.querySQLSetsStatementCount(ctx, dbConn, defaultCountSQL, int64(taskInfo.TaskID), dataSource)
	if err != nil {
		return nil, err
	}

	// 转换结果并计算总数
	sqlSets := convertSQLSetStatementCount(rets, channelInfo.DatasourceNameS, sqlSetName, owner)

	return sqlSets, nil
}

// buildCountSQL 构建SQL查询语句
func buildCountSQL(template, sqlSetName, owner, condition string) string {
	sql := strings.ReplaceAll(template, sqlcommon.VAR_SQLSET_NAME, sqlSetName)
	sql = strings.ReplaceAll(sql, sqlcommon.VAR_USERNAME, owner)
	if condition != "" {
		sql = strings.ReplaceAll(sql, sqlcommon.VAR_AND_TRUE, condition)
	}
	return sql
}

// querySQLSets 执行SQL查询并记录日志
func (i Service) querySQLSets(ctx context.Context, dbConn *oracle.Oracle, sql string, taskID int64, dataSource *datasource.Datasource) ([]map[string]string, error) {
	log.Infof("Querying sqlsets summary, taskId: %d, countSQL: %s, host: %s, port: %d, sid: %s",
		taskID, stringutil.RemoveSpecialLetterForLog(sql), dataSource.HostIp, dataSource.HostPort, dataSource.ServiceName)
	_, rets, err := oracle.Query(ctx, dbConn.OracleDB, sql)
	if err != nil {
		log.Errorf("Query sqlset failed, taskId: %d, err: %v", taskID, err)
		return nil, fmt.Errorf("query sqlset failed, err: %v", err)
	}
	log.Infof("Query sqlsets completed, taskId: %d, results count: %d", taskID, len(rets))
	return rets, nil
}

// querySQLSets 执行SQL查询并记录日志
func (i Service) querySQLSetsStatementCount(ctx context.Context, dbConn *oracle.Oracle, sql string, taskID int64, dataSource *datasource.Datasource) ([]map[string]string, error) {
	log.Infof("Querying sqlsets statement count, taskId: %d, countSQL: %s, host: %s, port: %d, sid: %s",
		taskID, stringutil.RemoveSpecialLetterForLog(sql), dataSource.HostIp, dataSource.HostPort, dataSource.ServiceName)
	_, rets, err := oracle.Query(ctx, dbConn.OracleDB, sql)
	if err != nil {
		log.Errorf("Query sqlset failed, taskId: %d, err: %v", taskID, err)
		return nil, fmt.Errorf("query sqlset failed, err: %v", err)
	}
	log.Infof("Query sqlsets completed, taskId: %d, results count: %d", taskID, len(rets))
	return rets, nil
}

// convertQueryResults 转换查询结果为SQLSet列表
func convertQueryResults(rets []map[string]string, dataSourceName, sqlSetName, owner string) ([]message.SQLSet, uint64) {
	var totalCount uint64
	sqlSets := lo.Map(rets, func(ret map[string]string, _ int) message.SQLSet {
		count, err := parse.ParseUInt64(ret["COUNT"])
		if err != nil {
			log.Errorf("Convert statement count failed, value: %v, err: %v", ret["COUNT"], err)
		}
		totalCount += count
		return message.SQLSet{
			DataSourceName: dataSourceName,
			SQLSetName:     sqlSetName,
			SQLSetOwner:    owner,
			SchemaName:     ret["PARSING_SCHEMA_NAME"],
			StatementCount: count,
		}
	})
	return sqlSets, totalCount
}

// SQLSetStatementCount 转换查询结果为SQLSet列表
func convertSQLSetStatementCount(rets []map[string]string, dataSourceName, sqlSetName, owner string) []message.SQLSetStatementCount {
	sqlSets := lo.Map(rets, func(ret map[string]string, _ int) message.SQLSetStatementCount {
		count, err := parse.ParseUInt64(ret["STATEMENT_COUNT"])
		if err != nil {
			log.Errorf("Convert statement count failed, value: %v, err: %v", ret["COUNT"], err)
		}
		return message.SQLSetStatementCount{
			DataSourceName: dataSourceName,
			SQLSetName:     sqlSetName,
			SQLSetOwner:    owner,
			StatementCount: count,
		}
	})
	return sqlSets
}

func (i Service) GetSQLSetStatements(ctx context.Context, req *message.GetSQLSetStatementsReq) (*message.GetSQLSetStatementsResp, *message.Page, error) {
	// 获取任务和通道信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, nil, err
	}

	// 构建SQL分析参数
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, channelId: %d, err: %v", taskInfo.TaskID, taskInfo.ChannelId, err)
		return nil, nil, err
	}

	// 计算分页参数
	limit := req.PageSize
	offset := calculateOffset(req.Page, req.PageSize)

	// 获取SQL语句和总数
	statements, total, err := i.fetchSQLSetStatements(ctx, req, taskInfo, channelInfo, param, offset, limit)
	if err != nil {
		return nil, nil, err
	}

	log.Infof("GetSQLSetStatements completed, taskId: %d, statements count: %d, total: %d", req.TaskId, len(statements), total)
	return &message.GetSQLSetStatementsResp{Statements: statements},
		&message.Page{Page: req.Page, PageSize: req.PageSize, Total: int64(total)},
		nil
}

// calculateOffset 计算分页偏移量
func calculateOffset(page, pageSize int) int {
	offset := pageSize * (page - 1)
	if offset < 0 {
		return 0
	}
	return offset
}

// fetchSQLSetStatements 获取SQL语句和总数
func (i Service) fetchSQLSetStatements(ctx context.Context, req *message.GetSQLSetStatementsReq, taskInfo *task.Task, channelInfo *channel.ChannelInformation, param *structs.Parameter, offset, limit int) ([]message.SQLSetStatement, int, error) {
	if req.SourceType == int(sqlcommon.FileSource) {
		return i.fetchStatementsFromFile(ctx, taskInfo, param, offset, limit)
	}
	return i.fetchStatementsFromDB(ctx, req, taskInfo, channelInfo, param, offset, limit)
}

// fetchStatementsFromFile 处理文件源的SQL语句
func (i Service) fetchStatementsFromFile(ctx context.Context, taskInfo *task.Task, param *structs.Parameter, offset, limit int) ([]message.SQLSetStatement, int, error) {
	if err := param.InitSQLFileSource(); err != nil {
		log.Errorf("InitSQLFileSource failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, err)
		return nil, 0, err
	}

	sqls := param.GetFileSQLs()
	total := len(sqls)
	sqlSubsets := lo.Subset(sqls, offset, uint(limit))

	statements := lo.Map(sqlSubsets, func(sql string, idx int) message.SQLSetStatement {
		return message.SQLSetStatement{
			Index:   offset + idx + 1,
			SQLText: sql,
		}
	})
	return statements, total, nil
}

// fetchStatementsFromDB 处理数据库源的SQL语句
func (i Service) fetchStatementsFromDB(ctx context.Context, req *message.GetSQLSetStatementsReq, taskInfo *task.Task, channelInfo *channel.ChannelInformation, param *structs.Parameter, offset, limit int) ([]message.SQLSetStatement, int, error) {
	// 建立数据库连接
	dbConn, dataSource, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("SetUpSourceDatabaseConnFunc failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, 0, err
	}

	owner := strings.TrimSpace(strings.ToUpper(dataSource.UserName))

	// 计算总数并确定是否为小数据集
	total, isSmallSet, err := i.countSQLSetStatements(ctx, dbConn, param, req, int64(taskInfo.TaskID), owner)
	if err != nil {
		return nil, 0, err
	}

	// 查询分页数据
	statements, err := i.querySQLSetStatements(ctx, dbConn, param, req, int64(taskInfo.TaskID), owner, offset, limit, isSmallSet)
	if err != nil {
		return nil, 0, err
	}

	return statements, total, nil
}

// countSQLSetStatements 计算SQL语句总数并判断是否为小数据集
func (i Service) countSQLSetStatements(ctx context.Context, dbConn *oracle.Oracle, param *structs.Parameter, req *message.GetSQLSetStatementsReq, taskID int64, owner string) (int, bool, error) {
	countSQL := buildStatementCountSQL(common.CountSQLSetBySchemaSQLDefaultTemplate, param, req, owner, "")
	countRets, err := i.executeCountQuery(ctx, dbConn, countSQL, taskID)
	if err != nil {
		return 0, false, err
	}

	total, err := strconv.Atoi(countRets[0]["COUNT"])
	if err != nil {
		log.Errorf("Parse count failed, value: %v, err: %v", countRets[0]["COUNT"], err)
		return 0, false, err
	}

	isSmallSet := total < param.GetSQLFactor().GetSQLRowLimit()
	if isSmallSet {
		countSQL = buildStatementCountSQL(common.CountSQLSetBySchemaSQLTemplate, param, req, owner, "")
		countRets, err = i.executeCountQuery(ctx, dbConn, countSQL, taskID)
		if err != nil {
			return 0, false, err
		}
		total, err = strconv.Atoi(countRets[0]["COUNT"])
		if err != nil {
			log.Errorf("Parse count failed, value: %v, err: %v", countRets[0]["COUNT"], err)
			return 0, false, err
		}
	}

	return total, isSmallSet, nil
}

// buildStatementCountSQL 构建计数SQL
func buildStatementCountSQL(template string, param *structs.Parameter, req *message.GetSQLSetStatementsReq, owner, condition string) string {
	sql := template
	sql = strings.ReplaceAll(sql, sqlcommon.VAR_SQLSET_NAME, param.GetSQLFactor().GetSQLSetName())
	sql = strings.ReplaceAll(sql, sqlcommon.VAR_USERNAME, owner)
	sql = strings.ReplaceAll(sql, sqlcommon.VAR_COMMAND_TYPE_LIST, strings.Join(param.GetIncludingSQLTypeStringList(), ","))
	sql = strings.ReplaceAll(sql, sqlcommon.VAR_SCHEMA_NAME, req.SchemaName)
	sql = strings.ReplaceAll(sql, sqlcommon.VAR_AND_TRUE, param.BuildAndCondition(condition))
	return sql
}

// executeCountQuery 执行计数查询
func (i Service) executeCountQuery(ctx context.Context, dbConn *oracle.Oracle, countSQL string, taskID int64) ([]map[string]string, error) {
	log.Infof("Counting sqlset statements, taskId: %d, sql: %s", taskID, stringutil.RemoveSpecialLetterForLog(countSQL))
	_, rets, err := oracle.Query(ctx, dbConn.OracleDB, countSQL)
	if err != nil {
		log.Errorf("Count sqlset failed, taskId: %d, err: %v", taskID, err)
		return nil, fmt.Errorf("query sqlset count failed, err: %v", err)
	}
	if len(rets) == 0 {
		return nil, errors.New("query sqlset count failed, empty result")
	}
	return rets, nil
}

// calculateMaxRows 智能计算最大行数，平衡性能和功能
func calculateMaxRows(offset, limit int) int {
	needed := offset + limit

	// 分级策略，平衡性能和功能
	switch {
	case needed <= 1000:
		// 前10页，使用固定值优化性能
		return 1000
	case needed <= 5000:
		// 中等深度，加适量buffer
		return needed + 200
	case needed <= 20000:
		// 较深分页，减少buffer
		return needed + 100
	default:
		// 极深分页，最小buffer
		return needed + 50
	}
}

// querySQLSetStatements 查询SQL语句详情
func (i Service) querySQLSetStatements(ctx context.Context, dbConn *oracle.Oracle, param *structs.Parameter, req *message.GetSQLSetStatementsReq, taskID int64, owner string, offset, limit int, isSmallSet bool) ([]message.SQLSetStatement, error) {
	template := common.PagedQuerySQLSetStatementSQLDefaultTemplate
	if isSmallSet {
		template = common.PagedQuerySQLSetStatementSQLTemplate
	}

	// 计算最大行数，确保能覆盖需要的数据范围
	maxRows := calculateMaxRows(offset, limit)

	querySQL := template
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_SQLSET_OWNER, req.SQLSetOwner)
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_SQLSET_NAME, req.SQLSetName)
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_COMMAND_TYPE_LIST, strings.Join(param.GetIncludingSQLTypeStringList(), ","))
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_SCHEMA_NAME, req.SchemaName)
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_MAX_ROWS, strconv.Itoa(maxRows)) // 替换MAX_ROWS占位符
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_LIMIT_OFFSET, strconv.Itoa(offset+limit))
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_OFFSET, strconv.Itoa(offset))
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_AND_TRUE, param.BuildAndCondition("a"))

	log.Infof("Querying sqlset statements, taskId: %d, offset: %d, limit: %d, maxRows: %d", taskID, offset, limit, maxRows)
	_, rets, err := oracle.Query(ctx, dbConn.OracleDB, querySQL)
	if err != nil {
		log.Errorf("Query sqlset failed, taskId: %d, err: %v", taskID, err)
		return nil, fmt.Errorf("query sqlset failed, err: %v", err)
	}

	statements := lo.Map(rets, func(ret map[string]string, _ int) message.SQLSetStatement {
		perExecTime, _ := strconv.ParseFloat(ret["PER_EXECTIME"], 64)
		executions, _ := strconv.Atoi(ret["EXECUTIONS"])
		return message.SQLSetStatement{
			SQLText:     ret["SQL_TEXT"],
			SchemaName:  ret["PARSING_SCHEMA_NAME"],
			PerExecTime: perExecTime,
			Executions:  executions,
			SQLId:       ret["SQL_ID"],
		}
	})
	return statements, nil
}

func (i Service) GetConfiguration(ctx context.Context, req *message.GetConfigurationReq) (*message.GetConfigurationResp, error) {
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, req.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId:%d, channelId:%d, err: %v", req.TaskId, req.ChannelId, err)
		return nil, err
	}

	resp := &message.GetConfigurationResp{
		AppSQLsSource:        param.AppSQLsSource.String(),
		AppSQLsFilename:      param.AppSQLsFilename,
		IgnoreSQLIDsFilename: param.IgnoreSQLIDsFilename,
		ExplainOnly:          param.ExplainOnly,
		SQLFactor: message.SQLFactor{
			SQLOrderBy:        param.SQLFactor.SQLOrderBy,
			AnalyzeTimeout:    param.SQLFactor.AnalyzeTimeout,
			IncludingSQLTypes: param.SQLFactor.IncludingSQLTypes,
			ExcludeModules:    param.SQLFactor.ExcludeModules,
			ExcludeSQLTexts:   param.SQLFactor.ExcludeSQLTxts,
			IncludingSchemas:  param.SQLFactor.IncludingSchemas,
		},
	}
	return resp, nil
}

func (i Service) CallSpaCollect(ctx context.Context, req *message.CallSpaCollectReq) (*message.CallSpaCollectResp, error) {
	taskId := req.TaskId
	channelId := req.ChannelId

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf(fmt.Sprintf("get channel info failed, err: %v", err))
		return nil, err
	}
	dbConn, datasource, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("SetUpSourceDatabaseConnFunc failed, channelId: %d, err: %v", channelId, err)
		return nil, err
	}

	username := strings.TrimSpace(strings.ToLower(datasource.UserName))
	var callCursor bool
	var callAwr bool

	if req.CallType == 0 {
		callCursor = true
		callAwr = true
	} else if req.CallType == 1 {
		callCursor = true
	} else if req.CallType == 2 {
		callAwr = true
	}

	if callCursor {
		// 执行存储过程
		startCurrentProcedureSQL := `call ` + username + `.spa_sqlcollect_cursor()`
		log.Infof("call current procedure: %s", startCurrentProcedureSQL)
		_ = tool.CreateSQLAnalyzerOperationLog(ctx, channelId, taskId, log.LogInfo, "发起采集当前SQL任务")
		_, currentRets, err := oracle.Query(ctx, dbConn.OracleDB, startCurrentProcedureSQL)
		if err != nil {
			_ = tool.CreateSQLAnalyzerOperationLog(ctx, channelId, taskId, log.LogInfo, "发起采集当前SQL任务失败："+err.Error())
			log.Errorf("start cursor procedure failed, ret:%v, err: %v", currentRets, err)
			return nil, err
		}
		_ = tool.CreateSQLAnalyzerOperationLog(ctx, channelId, taskId, log.LogInfo, "发起采集当前SQL任务成功")
	}

	if callAwr {
		// 执行存储过程
		startHistoryProcedureSQL := `call ` + username + `.spa_sqlcollect_awr()`
		log.Infof("call history procedure: %s", startHistoryProcedureSQL)
		_ = tool.CreateSQLAnalyzerOperationLog(ctx, channelId, taskId, log.LogInfo, "发起采集历史SQL任务")
		_, historyRets, err := oracle.Query(ctx, dbConn.OracleDB, startHistoryProcedureSQL)
		if err != nil {
			_ = tool.CreateSQLAnalyzerOperationLog(ctx, channelId, taskId, log.LogInfo, "发起采集历史SQL任务失败："+err.Error())
			log.Errorf("start awr procedure failed, ret:%v, err: %v", historyRets, err)
			return nil, err
		}
		_ = tool.CreateSQLAnalyzerOperationLog(ctx, channelId, taskId, log.LogInfo, "发起采集历史SQL任务成功")
	}
	return &message.CallSpaCollectResp{
		Message: "success",
	}, nil
}

func buildSQLAnalyzeEnvDeployTasks(taskInfo *task.Task) []sqlanalyzer.EnvDeployTask {
	defaultDeployTasks := deploy.GetDefaultSQLAnalyzeDeployTasks()
	deployTasks := make([]sqlanalyzer.EnvDeployTask, 0, len(defaultDeployTasks))
	for _, item := range defaultDeployTasks {
		deployTasks = append(deployTasks, sqlanalyzer.EnvDeployTask{
			TaskNumber:  item.TaskNumber,
			TaskName:    item.TaskName,
			IsIgnore:    constants.SQLAnalyzeTaskNotIgnore,
			TaskStatus:  int(constants.SQLAnalyzeTaskStatusNotRunning),
			TaskSQL:     item.GetTaskSQL(),
			SQLModel:    item.SQLModel,
			TaskMode:    item.TaskMode.String(),
			TaskId:      taskInfo.TaskID, // fill task id
			LastRunTime: timeutil.GetTMSNullTime(),
		})
	}
	return deployTasks
}

func (i Service) GetSQLSetsStatementCount(ctx context.Context, req *message.GetSQLSetsReq) (*message.GetSQLSetsStatementCountResp, error) {
	// 获取任务和通道信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	// 构建SQL分析参数
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, channelId: %d, err: %v", taskInfo.TaskID, taskInfo.ChannelId, err)
		return nil, err
	}

	// 根据SourceType获取SQL集合
	sqlSets, err := i.fetchSQLSetsStatementCount(ctx, req, taskInfo, channelInfo, param)
	if err != nil {
		return nil, err
	}

	log.Infof("Get sqlsets completed, taskId: %d, sqlSets count: %d", req.TaskId, len(sqlSets))
	return &message.GetSQLSetsStatementCountResp{SQLSets: sqlSets}, nil
}

// UpdateSqlResultUserOperation updates user operation status for SQL execution results
func (i Service) UpdateSqlResultUserOperation(ctx context.Context, req *message.UpdateSqlResultUserOperationReq) (*message.UpdateSqlResultUserOperationResp, error) {
	log.Infof("UpdateSqlResultUserOperation start, taskId: %d, status: %s, oraSqlIds count: %d",
		req.TaskId, req.UserOperateStatus, len(req.OraSqlIds))

	if len(req.OraSqlIds) == 0 {
		log.Errorf("OraSqlIds is required")
		return nil, fmt.Errorf("oraSqlIds must be provided")
	}

	// Get task info to determine version
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Determine version by checking task parameters
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	if param.IsRunVersionV1() {
		// V1 version - update SqlStmtExecResult table
		log.Infof("Using V1 version for user operation update, taskId: %d", req.TaskId)
		return i.updateSqlResultUserOperationV1(ctx, req)
	} else {
		// V2 version - operate on SqlStmtUserOperationV2 table
		log.Infof("Using V2 version for user operation update, taskId: %d", req.TaskId)
		return i.updateSqlResultUserOperationV2(ctx, req)
	}
}

// ListSqlResultsWithPagination lists SQL execution results with pagination and filtering
func (i Service) ListSqlResultsWithPagination(ctx context.Context, req *message.ListSqlResultsWithPaginationReq) (*message.ListSqlResultsWithPaginationResp, *message.Page, error) {
	log.Infof("ListSqlResultsWithPagination start, taskId: %d, page: %d, pageSize: %d",
		req.TaskId, req.Page, req.PageSize)

	// Get task info to determine channelId
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	// Determine version by checking task parameters
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	if param.IsRunVersionV1() {
		// V1 version - use existing logic
		log.Infof("Using V1 version for taskId: %d", req.TaskId)
		return i.listSqlResultsWithPaginationV1(ctx, req)
	} else {
		// V2 version - use Oracle data source with metadb user operations
		log.Infof("Using V2 version for taskId: %d", req.TaskId)
		return i.listSqlResultsWithPaginationV2(ctx, req)
	}
}

// GetSqlResultStatistics gets statistics for SQL execution results
func (i Service) GetSqlResultStatistics(ctx context.Context, req *message.GetSqlResultStatisticsReq) (*message.GetSqlResultStatisticsResp, error) {
	log.Infof("GetSqlResultStatistics start, taskId: %d", req.TaskId)

	// Get task info to determine channelId
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Determine version by checking task parameters
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	if param.IsRunVersionV1() {
		// V1 version - use existing logic
		log.Infof("Using V1 version for statistics, taskId: %d", req.TaskId)
		return i.getSqlResultStatisticsV1(ctx, req)
	} else {
		// V2 version - calculate statistics from Oracle data with metadb user operations
		log.Infof("Using V2 version for statistics, taskId: %d", req.TaskId)
		return i.getSqlResultStatisticsV2(ctx, req)
	}
}

// GetSqlResultHistory gets the operation history for SQL execution results
func (i Service) GetSqlResultHistory(ctx context.Context, req *message.GetSqlResultHistoryReq) (*message.GetSqlResultHistoryResp, error) {
	log.Infof("GetSqlResultHistory start, taskId: %d, sqlExecId: %d, oraSqlId: %s", req.TaskId, req.SqlExecId, req.OraSqlId)

	// Get task info to determine version
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Determine version by checking task parameters
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	if param.IsRunVersionV1() {
		// V1 version - use existing logic
		log.Infof("Using V1 version for history, taskId: %d", req.TaskId)
		return i.getSqlResultHistoryV1(ctx, req)
	} else {
		// V2 version - use Oracle identifiers
		log.Infof("Using V2 version for history, taskId: %d", req.TaskId)
		// For V2, ensure OraSqlId is provided
		if req.OraSqlId == "" {
			log.Errorf("OraSqlId is required for V2 version")
			return nil, fmt.Errorf("oraSqlId must be provided for V2 version")
		}
		return i.getSqlResultHistoryV2(ctx, req)
	}
}

// listSqlResultsWithPaginationV1 handles V1 version using metadb data
func (i Service) listSqlResultsWithPaginationV1(ctx context.Context, req *message.ListSqlResultsWithPaginationReq) (*message.ListSqlResultsWithPaginationResp, *message.Page, error) {
	results, total, err := models.GetSqlAnalyzerReaderWriter().ListSqlStmtExecResultWithPagination(
		ctx, req.TaskId, req.Schemas, req.StatusList, req.UserOperateStatus, req.OraSqlId, req.OraParsingSchemaName, req.OraSqlText, req.Page, req.PageSize)
	if err != nil {
		log.Errorf("ListSqlStmtExecResultWithPagination failed, err: %v", err)
		return nil, nil, err
	}

	// Convert to response format
	sqlResults := make([]message.SqlResultWithUserOperation, 0, len(results))
	for _, result := range results {
		sqlResults = append(sqlResults, message.SqlResultWithUserOperation{
			SqlExecId:            result.SqlExecId,
			ChannelId:            result.ChannelId,
			TaskId:               result.TaskId,
			OraSqlId:             result.OraSqlId,
			OraSqlText:           result.OraSqlText,
			OraParsingSchemaName: result.OraParsingSchemaName,
			OraModule:            result.OraModule,
			OraExecutions:        result.OraExecutions,
			OraElapsedTimeMs:     result.OraElapsedTimeMs,
			TidbSqlType:          result.TidbSqlType,
			TidbSqlText:          result.TidbSqlText,
			TidbExecStatus:       result.TidbExecStatus,
			TidbExecCode:         result.TidbExecCode,
			TidbExecMsg:          result.TidbExecMsg,
			TidbElapsedTimeMs:    result.TidbElapsedTimeMs,
			UserOperateStatus:    result.UserOperateStatus,
			UserOperateAt:        result.UserOperateAt,
			UserOperateRemark:    result.UserOperateRemark,
		})
	}

	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}

	resp := &message.ListSqlResultsWithPaginationResp{
		SqlResults: sqlResults,
		Page:       *page,
	}

	log.Infof("ListSqlResultsWithPagination V1 completed, returned %d results, total: %d", len(sqlResults), total)
	return resp, page, nil
}

// listSqlResultsWithPaginationV2 handles V2 version by merging Oracle data with metadb user operations
func (i Service) listSqlResultsWithPaginationV2(ctx context.Context, req *message.ListSqlResultsWithPaginationReq) (*message.ListSqlResultsWithPaginationResp, *message.Page, error) {
	// Get task and channel information
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, nil, err
	}

	// Build SQL analyze parameter
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	// Set up Oracle connection
	oracleConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("Set up source database conn failed, taskId: %v, err: %v", req.TaskId, err)
		return nil, nil, fmt.Errorf("failed to set up source database conn: %w", err)
	}

	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := req.Schemas
	if len(schemas) == 0 {
		schemas = param.GetIncludingSchemasBySQLSource()
	}

	// Build status filter for Oracle query (if applicable)
	var oracleStatusFilter string
	if len(req.StatusList) > 0 {
		// For V2, we can only filter by one status at Oracle level, so we'll take the first one
		// If multiple statuses are needed, we'll need to handle it differently
		oracleStatusFilter = req.StatusList[0]
	}

	// Get Oracle data with pagination and sorting at database level
	oracleStatements, total, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsListByReplayStatusWithPagination(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, oracleStatusFilter, schemas, req.Page, req.PageSize, "SQL_ID", req.OraSqlId, req.OraParsingSchemaName, req.OraSqlText)
	if err != nil {
		log.Errorf("Get Oracle SQL statements with pagination failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	// Get user operations from metadb for the paginated results only
	var userOpKeys []sqlanalyzer.SqlStmtUserOperationV2Key
	for _, stmt := range oracleStatements {
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               req.TaskId,
			OraSqlId:             stmt.SQLID,
			OraParsingSchemaName: stmt.ParsingSchemaName,
		}
		userOpKeys = append(userOpKeys, key)
	}

	userOpsMap, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtUserOperationV2ByKeys(ctx, req.TaskId, userOpKeys)
	if err != nil {
		log.Errorf("Get user operations V2 failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, nil, err
	}

	// Merge Oracle data with user operations and apply remaining filters
	var results []message.SqlResultWithUserOperation
	for _, stmt := range oracleStatements {
		// Get user operation for this statement
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               req.TaskId,
			OraSqlId:             stmt.SQLID,
			OraParsingSchemaName: stmt.ParsingSchemaName,
		}

		userOp := userOpsMap[key.String()]
		userOperateStatus := constants.UserOperateStatusNormal // Default
		var userOperateAt *time.Time
		var userOperateRemark string

		if userOp != nil {
			userOperateStatus = userOp.UserOperateStatus
			userOperateAt = userOp.UserOperateAt
			userOperateRemark = userOp.UserOperateRemark
		}

		// Apply user operation status filter
		if len(req.UserOperateStatus) > 0 {
			statusMatch := false
			for _, status := range req.UserOperateStatus {
				if userOperateStatus == status {
					statusMatch = true
					break
				}
			}
			if !statusMatch {
				continue
			}
		}

		// Apply additional status filters if multiple statuses were requested
		if len(req.StatusList) > 1 {
			statusMatch := false
			for _, status := range req.StatusList {
				if stmt.ReplayStatus == status {
					statusMatch = true
					break
				}
			}
			if !statusMatch {
				continue
			}
		}

		// Convert Oracle statement to response format
		result := message.SqlResultWithUserOperation{
			SqlExecId:            0, // V2 doesn't have SqlExecId
			ChannelId:            taskInfo.ChannelId,
			TaskId:               req.TaskId,
			OraSqlId:             stmt.SQLID,
			OraSqlText:           stmt.SQLText,
			OraParsingSchemaName: stmt.ParsingSchemaName,
			OraModule:            stmt.Module,
			OraExecutions:        int(stmt.Executions),
			OraElapsedTimeMs:     float64(stmt.ElapsedTime),
			TidbSqlType:          "",           // May need to derive from Oracle data
			TidbSqlText:          stmt.SQLText, // May need conversion
			TidbExecStatus:       stmt.ReplayStatus,
			TidbExecCode:         stmt.ReplayExecCode,
			TidbExecMsg:          stmt.MessageLog,
			TidbElapsedTimeMs:    0, // Not available in Oracle data
			UserOperateStatus:    userOperateStatus,
			UserOperateAt:        userOperateAt,
			UserOperateRemark:    userOperateRemark,
		}

		results = append(results, result)
	}

	// Note: No need for sorting as Oracle query already sorts by SQL_ID
	// Note: No need for pagination as Oracle query already applies pagination

	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}

	resp := &message.ListSqlResultsWithPaginationResp{
		SqlResults: results,
		Page:       *page,
	}

	log.Infof("ListSqlResultsWithPagination V2 completed, returned %d results, total: %d", len(results), total)
	return resp, page, nil
}

// getSqlResultStatisticsV1 handles V1 version statistics using metadb data
func (i Service) getSqlResultStatisticsV1(ctx context.Context, req *message.GetSqlResultStatisticsReq) (*message.GetSqlResultStatisticsResp, error) {
	stats, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtExecResultStatistics(ctx, req.TaskId, req.Schemas)
	if err != nil {
		log.Errorf("GetSqlStmtExecResultStatistics failed, err: %v", err)
		return nil, err
	}

	log.Infof("GetSqlResultStatistics V1 completed, stats: %+v", stats)
	return &message.GetSqlResultStatisticsResp{Statistics: stats}, nil
}

// getSqlResultStatisticsV2 handles V2 version statistics by combining Oracle data with metadb user operations
func (i Service) getSqlResultStatisticsV2(ctx context.Context, req *message.GetSqlResultStatisticsReq) (*message.GetSqlResultStatisticsResp, error) {
	// Get task and channel information
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	// Build SQL analyze parameter
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Set up Oracle connection
	oracleConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("Set up source database conn failed, taskId: %v, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("failed to set up source database conn: %w", err)
	}

	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := req.Schemas
	if len(schemas) == 0 {
		schemas = param.GetIncludingSchemasBySQLSource()
	}

	if param.IsFileMode() {
		sqlsetOwner = param.AppSQLsFilename
		schemas = param.GetIncludingSchemasBySQLSource()
	}

	// Get summaries from Oracle
	totalSummaries, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsSummaries(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, schemas)
	if err != nil {
		log.Errorf("Get total summaries failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	failedSummaries, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsFailedSummaries(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, schemas)
	if err != nil {
		log.Errorf("Get failed summaries failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	timeoutSummaries, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsTimeoutSummaries(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, schemas)
	if err != nil {
		log.Errorf("Get timeout summaries failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Get all Oracle SQL statements to count user operations
	oracleStatements, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsListByReplayStatus(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, "", schemas)
	if err != nil {
		log.Errorf("Get Oracle SQL statements failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Get user operations from metadb
	var userOpKeys []sqlanalyzer.SqlStmtUserOperationV2Key
	for _, stmt := range oracleStatements {
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               req.TaskId,
			OraSqlId:             stmt.SQLID,
			OraParsingSchemaName: stmt.ParsingSchemaName,
		}
		userOpKeys = append(userOpKeys, key)
	}

	userOpsMap, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtUserOperationV2ByKeys(ctx, req.TaskId, userOpKeys)
	if err != nil {
		log.Errorf("Get user operations V2 failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Calculate statistics
	stats := make(map[string]int64)

	// Execution status statistics from Oracle summaries
	var totalExecCount int64
	var successExecCount int64
	var failedExecCount int64
	var timeoutExecCount int64

	for _, summary := range totalSummaries {
		totalExecCount += int64(summary.TotalNum)
		successExecCount += int64(summary.SuccessNum)
	}

	for _, summary := range failedSummaries {
		failedExecCount += int64(summary.TotalNum)
	}

	for _, summary := range timeoutSummaries {
		timeoutExecCount += int64(summary.TotalNum)
	}

	stats["exec_total"] = totalExecCount
	stats["exec_success"] = successExecCount
	stats["exec_failed"] = failedExecCount
	stats["exec_timeout"] = timeoutExecCount

	// User operation statistics
	var normalCount, ignoredCount, resolvedCount int64

	for _, stmt := range oracleStatements {
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               req.TaskId,
			OraSqlId:             stmt.SQLID,
			OraParsingSchemaName: stmt.ParsingSchemaName,
		}

		userOp := userOpsMap[key.String()]
		if userOp != nil {
			switch userOp.UserOperateStatus {
			case constants.UserOperateStatusIgnored:
				ignoredCount++
			case constants.UserOperateStatusResolved:
				resolvedCount++
			default:
				normalCount++
			}
		} else {
			normalCount++ // Default status
		}
	}

	stats["user_normal"] = normalCount
	stats["user_ignored"] = ignoredCount
	stats["user_resolved"] = resolvedCount

	log.Infof("GetSqlResultStatistics V2 completed, stats: %+v", stats)
	return &message.GetSqlResultStatisticsResp{Statistics: stats}, nil
}

// getSqlResultHistoryV1 handles V1 version history using metadb data
func (i Service) getSqlResultHistoryV1(ctx context.Context, req *message.GetSqlResultHistoryReq) (*message.GetSqlResultHistoryResp, error) {
	historyRecords, err := models.GetSqlAnalyzerReaderWriter().ListSqlStmtExecResultHistory(ctx, req.TaskId, req.SqlExecId)
	if err != nil {
		log.Errorf("ListSqlStmtExecResultHistory failed, err: %v", err)
		return nil, err
	}

	// Convert to response format
	history := make([]message.SqlResultHistoryItem, 0, len(historyRecords))
	for _, record := range historyRecords {
		history = append(history, message.SqlResultHistoryItem{
			Id:             record.Id,
			SqlExecId:      record.SqlExecId,
			TaskId:         record.TaskId,
			OperationType:  record.OperationType,
			PreviousStatus: record.PreviousStatus,
			NewStatus:      record.NewStatus,
			OperateAt:      &record.OperateAt,
			OperateRemark:  record.OperateRemark,
			ClientIP:       record.ClientIP,
		})
	}

	log.Infof("GetSqlResultHistory V1 completed, returned %d history records", len(history))
	return &message.GetSqlResultHistoryResp{History: history}, nil
}

// getSqlResultHistoryV2 handles V2 version history using Oracle identifiers
func (i Service) getSqlResultHistoryV2(ctx context.Context, req *message.GetSqlResultHistoryReq) (*message.GetSqlResultHistoryResp, error) {
	historyRecords, err := models.GetSqlAnalyzerReaderWriter().ListSqlStmtUserOperationV2History(
		ctx, req.TaskId, req.OraSqlId)
	if err != nil {
		log.Errorf("ListSqlStmtUserOperationV2History failed, err: %v", err)
		return nil, err
	}

	// Convert to response format
	history := make([]message.SqlResultHistoryItem, 0, len(historyRecords))
	for _, record := range historyRecords {
		history = append(history, message.SqlResultHistoryItem{
			Id:             record.Id,
			SqlExecId:      0, // V2 doesn't have SqlExecId
			TaskId:         record.TaskId,
			OperationType:  record.OperationType,
			PreviousStatus: record.PreviousStatus,
			NewStatus:      record.NewStatus,
			OperateAt:      &record.OperateAt,
			OperateRemark:  record.OperateRemark,
			ClientIP:       record.ClientIP,
		})
	}

	log.Infof("GetSqlResultHistory V2 completed, returned %d history records", len(history))
	return &message.GetSqlResultHistoryResp{History: history}, nil
}

// updateSqlResultUserOperationV1 handles V1 version user operation updates
// Modified to use SqlStmtUserOperationV2 for centralized user operation management
func (i Service) updateSqlResultUserOperationV1(ctx context.Context, req *message.UpdateSqlResultUserOperationReq) (*message.UpdateSqlResultUserOperationResp, error) {
	// Get task and channel information for Oracle connection
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	// Build SQL analyze parameter to get schema information
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Set up Oracle connection to get detailed SQL information
	oracleConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("Set up source database conn failed, taskId: %v, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("failed to set up source database conn: %w", err)
	}

	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := param.GetIncludingSchemasBySQLSource()

	// Get Oracle SQL statements to map OraSqlId to ParsingSchemaName
	oracleStatements, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsListByReplayStatus(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, "", schemas)
	if err != nil {
		log.Errorf("Get Oracle SQL statements failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Create a map from OraSqlId to ParsingSchemaName
	sqlIdToSchemaMap := make(map[string]string)
	for _, stmt := range oracleStatements {
		sqlIdToSchemaMap[stmt.SQLID] = stmt.ParsingSchemaName
	}

	// Update user operations through V2-compatible method
	updatedCount, err := models.GetSqlAnalyzerReaderWriter().UpdateSqlStmtExecResultUserOperationByOraSqlId(
		ctx, req.TaskId, req.OraSqlIds, req.UserOperateStatus, req.UserOperateRemark, sqlIdToSchemaMap)
	if err != nil {
		log.Errorf("UpdateSqlStmtExecResultUserOperationByOraSqlId failed, err: %v", err)
		return nil, err
	}

	log.Infof("UpdateSqlResultUserOperation V1 completed, updated count: %d", updatedCount)
	return &message.UpdateSqlResultUserOperationResp{UpdatedCount: updatedCount}, nil
}

// updateSqlResultUserOperationV2 handles V2 version user operation updates
func (i Service) updateSqlResultUserOperationV2(ctx context.Context, req *message.UpdateSqlResultUserOperationReq) (*message.UpdateSqlResultUserOperationResp, error) {
	// Get task and channel information for Oracle connection
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("Get task info failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Get channel info failed, channelId: %d, err: %v", taskInfo.ChannelId, err)
		return nil, err
	}

	// Build SQL analyze parameter to get schema information
	param, err := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, req.TaskId)
	if err != nil {
		log.Errorf("BuildSQLAnalyzeParameter failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Set up Oracle connection to get detailed SQL information
	oracleConn, _, err := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if err != nil {
		log.Errorf("Set up source database conn failed, taskId: %v, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("failed to set up source database conn: %w", err)
	}

	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := param.GetIncludingSchemasBySQLSource()

	// Get Oracle SQL statements to map OraSqlId to ParsingSchemaName
	oracleStatements, err := models.GetDatasourceReaderWriter().GetSQLSetStatementsListByReplayStatus(
		ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, "", schemas)
	if err != nil {
		log.Errorf("Get Oracle SQL statements failed, taskId: %d, err: %v", req.TaskId, err)
		return nil, err
	}

	// Create a map from OraSqlId to ParsingSchemaName
	sqlIdToSchemaMap := make(map[string]string)
	for _, stmt := range oracleStatements {
		sqlIdToSchemaMap[stmt.SQLID] = stmt.ParsingSchemaName
	}

	// Prepare V2 user operations
	var userOps []*sqlanalyzer.SqlStmtUserOperationV2
	var histories []*sqlanalyzer.SqlStmtUserOperationV2History
	now := time.Now()

	for _, oraSqlId := range req.OraSqlIds {
		schemaName, exists := sqlIdToSchemaMap[oraSqlId]
		if !exists {
			log.Warnf("OraSqlId %s not found in Oracle data, skipping", oraSqlId)
			continue
		}

		// Create user operation record
		userOp := &sqlanalyzer.SqlStmtUserOperationV2{
			TaskId:               req.TaskId,
			OraSqlId:             oraSqlId,
			OraParsingSchemaName: schemaName,
			UserOperateStatus:    req.UserOperateStatus,
			UserOperateBy:        "system", // TODO: get from context or request
			UserOperateAt:        &now,
			UserOperateRemark:    req.UserOperateRemark,
		}
		userOps = append(userOps, userOp)

		// Create history record
		history := &sqlanalyzer.SqlStmtUserOperationV2History{
			TaskId:               req.TaskId,
			OraSqlId:             oraSqlId,
			OraParsingSchemaName: schemaName,
			OperationType:        "UPDATE",
			PreviousStatus:       constants.UserOperateStatusNormal, // TODO: get actual previous status
			NewStatus:            req.UserOperateStatus,
			OperateBy:            "system", // TODO: get from context or request
			OperateAt:            now,
			OperateRemark:        req.UserOperateRemark,
			ClientIP:             "", // TODO: get from context
		}
		histories = append(histories, history)
	}

	// Batch update user operations
	if len(userOps) > 0 {
		err = models.GetSqlAnalyzerReaderWriter().BatchCreateOrUpdateSqlStmtUserOperationV2(ctx, userOps)
		if err != nil {
			log.Errorf("BatchCreateOrUpdateSqlStmtUserOperationV2 failed, err: %v", err)
			return nil, err
		}
	}

	// Batch create history records
	for _, history := range histories {
		err = models.GetSqlAnalyzerReaderWriter().CreateSqlStmtUserOperationV2History(ctx, history)
		if err != nil {
			log.Errorf("CreateSqlStmtUserOperationV2History failed, err: %v", err)
			// Don't fail the operation if history creation fails, just log the error
		}
	}

	log.Infof("UpdateSqlResultUserOperation V2 completed, updated count: %d", len(userOps))
	return &message.UpdateSqlResultUserOperationResp{UpdatedCount: len(userOps)}, nil
}
