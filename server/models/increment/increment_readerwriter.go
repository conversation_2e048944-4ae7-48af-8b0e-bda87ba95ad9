package increment

import (
	"context"
)

type ReaderWriter interface {
	CreateOrUpdateIncrementTask(ctx context.Context, incrementTask *IncrementTask) (*IncrementTask, error)

	CreateOrUpdateIncrementInstallParam(ctx context.Context, it *IncrementInstallParam) (*IncrementInstallParam, error)

	UpdateIncrementTask(ctx context.Context, incrementTask *IncrementTask) (*IncrementTask, error)

	GetIncrementTask(ctx context.Context, taskId int) (*IncrementTask, error)

	GetIncrementInstallParam(ctx context.Context, taskId int) (*IncrementInstallParam, error)

	DeleteIncrementTask(ctx context.Context, taskId int) error
}
