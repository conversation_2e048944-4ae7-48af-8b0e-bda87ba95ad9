package increment

import (
	"context"
	"errors"

	commonerrors "gitee.com/pingcap_enterprise/tms/common/errors"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type IncrementReadWrite struct {
	dbCommon.GormDB
}

func NewIncrementReadWrite(db *gorm.DB) *IncrementReadWrite {
	m := &IncrementReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw IncrementReadWrite) CreateOrUpdateIncrementTask(ctx context.Context, it *IncrementTask) (*IncrementTask, error) {
	err := rw.DB(ctx).Save(it).Error
	if err != nil {
		log.Errorf("create increment_task to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create increment_task to db successfully, %v", it)
	return it, nil
}

func (rw IncrementReadWrite) UpdateIncrementTask(ctx context.Context, it *IncrementTask) (*IncrementTask, error) {
	err := rw.DB(ctx).Save(it).Error
	if err != nil {
		log.Errorf("update increment_task to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update increment_task to db successfully, %v", it)
	return it, nil
}

func (rw IncrementReadWrite) GetIncrementTask(ctx context.Context, taskId int) (*IncrementTask, error) {
	it := &IncrementTask{}
	err := rw.DB(ctx).First(it, "task_id = ?", taskId).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, commonerrors.NewErrorf(commonerrors.TIMS_RECORD_NOT_EXIST, "query increment_task %d not found", taskId)
	} else if err != nil {
		log.Errorf("get increment_task failed. taskId is %d, %v", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return it, nil
}

func (rw IncrementReadWrite) DeleteIncrementTask(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Delete(&IncrementTask{}, "task_id = ?", taskId).Error
	if err != nil {
		log.Errorf("delete increment_task failed. taskId is %d, %v", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	log.Infof("delete increment_task successfully, taskId is %d", taskId)
	return nil
}

func (rw IncrementReadWrite) CreateOrUpdateIncrementInstallParam(ctx context.Context, it *IncrementInstallParam) (*IncrementInstallParam, error) {
	err := rw.DB(ctx).Save(it).Error
	if err != nil {
		log.Errorf("create increment_install_param to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create increment_install_param to db successfully, %v", it)
	return it, nil
}

func (rw IncrementReadWrite) GetIncrementInstallParam(ctx context.Context, taskId int) (*IncrementInstallParam, error) {
	it := &IncrementInstallParam{}
	err := rw.DB(ctx).First(it, "task_id = ?", taskId).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, commonerrors.NewErrorf(commonerrors.TIMS_RECORD_NOT_EXIST, "query increment_install_param %d not found", taskId)
	} else if err != nil {
		log.Errorf("get increment_install_param failed. taskId is %d, %v", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return it, nil
}
