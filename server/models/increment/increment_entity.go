package increment

type IncrementTask struct {
	TaskId            int    `gorm:"primarykey"`
	DsgTaskName       string `gorm:"not null;type:varchar(100);comment:dsg task name"`
	ScnType           string `gorm:"not null;type:varchar(10);comment:scn type"`
	ScnValue          string `gorm:"not null;type:varchar(100);comment:scn value"`
	TimeValue         string `gorm:"not null;type:varchar(100);comment:time value"`
	ScnUserInputValue string `gorm:"not null;type:varchar(100);comment:scn user input value"`
}

type IncrementInstallParam struct {
	TaskId      int    `gorm:"primarykey"`
	DsgTaskName string `gorm:"not null;type:varchar(100);comment:dsg task name"`
	SourceIP    string `gorm:"not null;type:varchar(100);comment:source ip"`
	TargetIP    string `gorm:"not null;type:varchar(100);comment:target ip"`
}
