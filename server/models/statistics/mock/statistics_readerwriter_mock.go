// Code generated by MockGen. DO NOT EDIT.
// Source: ./statistics/statistics_readerwriter.go

// Package mockstatisticsreaderwriter is a generated GoMock package.
package mockstatisticsreaderwriter

import (
	context "context"
	reflect "reflect"

	statistics "gitee.com/pingcap_enterprise/tms/server/models/statistics"
	gomock "github.com/golang/mock/gomock"
)

// MockStatisticsReaderWriter is a mock of StatisticsReaderWriter interface.
type MockStatisticsReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockStatisticsReaderWriterMockRecorder
}

// MockStatisticsReaderWriterMockRecorder is the mock recorder for MockStatisticsReaderWriter.
type MockStatisticsReaderWriterMockRecorder struct {
	mock *MockStatisticsReaderWriter
}

// NewMockStatisticsReaderWriter creates a new mock instance.
func NewMockStatisticsReaderWriter(ctrl *gomock.Controller) *MockStatisticsReaderWriter {
	mock := &MockStatisticsReaderWriter{ctrl: ctrl}
	mock.recorder = &MockStatisticsReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStatisticsReaderWriter) EXPECT() *MockStatisticsReaderWriterMockRecorder {
	return m.recorder
}

// BatchGetTaskStats mocks base method.
func (m *MockStatisticsReaderWriter) BatchGetTaskStats(ctx context.Context, taskStats *statistics.TaskStatistics) ([]*statistics.TaskStatistics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTaskStats", ctx, taskStats)
	ret0, _ := ret[0].([]*statistics.TaskStatistics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTaskStats indicates an expected call of BatchGetTaskStats.
func (mr *MockStatisticsReaderWriterMockRecorder) BatchGetTaskStats(ctx, taskStats interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTaskStats", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).BatchGetTaskStats), ctx, taskStats)
}

// CreateTaskStats mocks base method.
func (m *MockStatisticsReaderWriter) CreateTaskStats(ctx context.Context, taskStats *statistics.TaskStatistics) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTaskStats", ctx, taskStats)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTaskStats indicates an expected call of CreateTaskStats.
func (mr *MockStatisticsReaderWriterMockRecorder) CreateTaskStats(ctx, taskStats interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTaskStats", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).CreateTaskStats), ctx, taskStats)
}

// DeleteTaskStatsByTaskTable mocks base method.
func (m *MockStatisticsReaderWriter) DeleteTaskStatsByTaskTable(ctx context.Context, taskId int, schemaName, tableName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTaskStatsByTaskTable", ctx, taskId, schemaName, tableName)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTaskStatsByTaskTable indicates an expected call of DeleteTaskStatsByTaskTable.
func (mr *MockStatisticsReaderWriterMockRecorder) DeleteTaskStatsByTaskTable(ctx, taskId, schemaName, tableName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTaskStatsByTaskTable", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).DeleteTaskStatsByTaskTable), ctx, taskId, schemaName, tableName)
}

// GetTaskStatsByTaskId mocks base method.
func (m *MockStatisticsReaderWriter) GetTaskStatsByTaskId(ctx context.Context, taskId int) ([]*statistics.TaskStatistics, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStatsByTaskId", ctx, taskId)
	ret0, _ := ret[0].([]*statistics.TaskStatistics)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStatsByTaskId indicates an expected call of GetTaskStatsByTaskId.
func (mr *MockStatisticsReaderWriterMockRecorder) GetTaskStatsByTaskId(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStatsByTaskId", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).GetTaskStatsByTaskId), ctx, taskId)
}

// GetTaskStatsCountsBySchema mocks base method.
func (m *MockStatisticsReaderWriter) GetTaskStatsCountsBySchema(ctx context.Context, taskId int) ([]*statistics.TaskStatisticsBySchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStatsCountsBySchema", ctx, taskId)
	ret0, _ := ret[0].([]*statistics.TaskStatisticsBySchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStatsCountsBySchema indicates an expected call of GetTaskStatsCountsBySchema.
func (mr *MockStatisticsReaderWriterMockRecorder) GetTaskStatsCountsBySchema(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStatsCountsBySchema", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).GetTaskStatsCountsBySchema), ctx, taskId)
}

// GetTaskStatsCountsByStatusSchema mocks base method.
func (m *MockStatisticsReaderWriter) GetTaskStatsCountsByStatusSchema(ctx context.Context, taskId int) ([]*statistics.TaskStatisticsByStatusSchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStatsCountsByStatusSchema", ctx, taskId)
	ret0, _ := ret[0].([]*statistics.TaskStatisticsByStatusSchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStatsCountsByStatusSchema indicates an expected call of GetTaskStatsCountsByStatusSchema.
func (mr *MockStatisticsReaderWriterMockRecorder) GetTaskStatsCountsByStatusSchema(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStatsCountsByStatusSchema", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).GetTaskStatsCountsByStatusSchema), ctx, taskId)
}

// GetTaskStatsCountsJoinByTask mocks base method.
func (m *MockStatisticsReaderWriter) GetTaskStatsCountsJoinByTask(ctx context.Context, channelId, taskId int) ([]*statistics.TaskStatisticsJoinBySchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStatsCountsJoinByTask", ctx, channelId, taskId)
	ret0, _ := ret[0].([]*statistics.TaskStatisticsJoinBySchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStatsCountsJoinByTask indicates an expected call of GetTaskStatsCountsJoinByTask.
func (mr *MockStatisticsReaderWriterMockRecorder) GetTaskStatsCountsJoinByTask(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStatsCountsJoinByTask", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).GetTaskStatsCountsJoinByTask), ctx, channelId, taskId)
}

// ListTaskStatsConflictTable mocks base method.
func (m *MockStatisticsReaderWriter) ListTaskStatsConflictTable(ctx context.Context, channelId, taskId int, schemaName string, page, pageSize int) ([]*statistics.ChannelTaskSchemaStatisticsDuplicate, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskStatsConflictTable", ctx, channelId, taskId, schemaName, page, pageSize)
	ret0, _ := ret[0].([]*statistics.ChannelTaskSchemaStatisticsDuplicate)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTaskStatsConflictTable indicates an expected call of ListTaskStatsConflictTable.
func (mr *MockStatisticsReaderWriterMockRecorder) ListTaskStatsConflictTable(ctx, channelId, taskId, schemaName, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskStatsConflictTable", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).ListTaskStatsConflictTable), ctx, channelId, taskId, schemaName, page, pageSize)
}

// ListTaskStatsTablesByPage mocks base method.
func (m *MockStatisticsReaderWriter) ListTaskStatsTablesByPage(ctx context.Context, taskStats *statistics.TaskStatistics, page, pageSize int) ([]*statistics.TaskStatistics, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskStatsTablesByPage", ctx, taskStats, page, pageSize)
	ret0, _ := ret[0].([]*statistics.TaskStatistics)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTaskStatsTablesByPage indicates an expected call of ListTaskStatsTablesByPage.
func (mr *MockStatisticsReaderWriterMockRecorder) ListTaskStatsTablesByPage(ctx, taskStats, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskStatsTablesByPage", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).ListTaskStatsTablesByPage), ctx, taskStats, page, pageSize)
}

// ListTaskStatsTablesByTablePage mocks base method.
func (m *MockStatisticsReaderWriter) ListTaskStatsTablesByTablePage(ctx context.Context, taskId int, schemaName, tableName string, page, pageSize int) ([]*statistics.TaskStatistics, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskStatsTablesByTablePage", ctx, taskId, schemaName, tableName, page, pageSize)
	ret0, _ := ret[0].([]*statistics.TaskStatistics)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTaskStatsTablesByTablePage indicates an expected call of ListTaskStatsTablesByTablePage.
func (mr *MockStatisticsReaderWriterMockRecorder) ListTaskStatsTablesByTablePage(ctx, taskId, schemaName, tableName, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskStatsTablesByTablePage", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).ListTaskStatsTablesByTablePage), ctx, taskId, schemaName, tableName, page, pageSize)
}

// ListTaskStatsTablesDuplicate mocks base method.
func (m *MockStatisticsReaderWriter) ListTaskStatsTablesDuplicate(ctx context.Context, channelId int) ([]*statistics.TaskStatisticsTableDuplicate, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskStatsTablesDuplicate", ctx, channelId)
	ret0, _ := ret[0].([]*statistics.TaskStatisticsTableDuplicate)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTaskStatsTablesDuplicate indicates an expected call of ListTaskStatsTablesDuplicate.
func (mr *MockStatisticsReaderWriterMockRecorder) ListTaskStatsTablesDuplicate(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskStatsTablesDuplicate", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).ListTaskStatsTablesDuplicate), ctx, channelId)
}

// SaveTaskStats mocks base method.
func (m *MockStatisticsReaderWriter) SaveTaskStats(ctx context.Context, taskStats []*statistics.TaskStatistics) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveTaskStats", ctx, taskStats)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveTaskStats indicates an expected call of SaveTaskStats.
func (mr *MockStatisticsReaderWriterMockRecorder) SaveTaskStats(ctx, taskStats interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveTaskStats", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).SaveTaskStats), ctx, taskStats)
}

// UpdateTaskStatsRowsByTable mocks base method.
func (m *MockStatisticsReaderWriter) UpdateTaskStatsRowsByTable(ctx context.Context, taskStats *statistics.TaskStatistics) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatsRowsByTable", ctx, taskStats)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatsRowsByTable indicates an expected call of UpdateTaskStatsRowsByTable.
func (mr *MockStatisticsReaderWriterMockRecorder) UpdateTaskStatsRowsByTable(ctx, taskStats interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatsRowsByTable", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).UpdateTaskStatsRowsByTable), ctx, taskStats)
}

// UpdateTaskStatsStatusEndTimeByTable mocks base method.
func (m *MockStatisticsReaderWriter) UpdateTaskStatsStatusEndTimeByTable(ctx context.Context, taskStats *statistics.TaskStatistics) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatsStatusEndTimeByTable", ctx, taskStats)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatsStatusEndTimeByTable indicates an expected call of UpdateTaskStatsStatusEndTimeByTable.
func (mr *MockStatisticsReaderWriterMockRecorder) UpdateTaskStatsStatusEndTimeByTable(ctx, taskStats interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatsStatusEndTimeByTable", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).UpdateTaskStatsStatusEndTimeByTable), ctx, taskStats)
}

// UpdateTaskStatsStatusStartTimeByTable mocks base method.
func (m *MockStatisticsReaderWriter) UpdateTaskStatsStatusStartTimeByTable(ctx context.Context, taskStats *statistics.TaskStatistics) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskStatsStatusStartTimeByTable", ctx, taskStats)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTaskStatsStatusStartTimeByTable indicates an expected call of UpdateTaskStatsStatusStartTimeByTable.
func (mr *MockStatisticsReaderWriterMockRecorder) UpdateTaskStatsStatusStartTimeByTable(ctx, taskStats interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskStatsStatusStartTimeByTable", reflect.TypeOf((*MockStatisticsReaderWriter)(nil).UpdateTaskStatsStatusStartTimeByTable), ctx, taskStats)
}
