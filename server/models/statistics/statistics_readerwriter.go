package statistics

import "context"

type StatisticsReaderWriter interface {
	CreateTaskStats(ctx context.Context, taskStats *TaskStatistics) error
	UpdateTaskStatsRowsByTable(ctx context.Context, taskStats *TaskStatistics) error
	UpdateTaskStatsStatusStartTimeByTable(ctx context.Context, taskStats *TaskStatistics) error
	UpdateTaskStatsStatusEndTimeByTable(ctx context.Context, taskStats *TaskStatistics) error
	SaveTaskStats(ctx context.Context, taskStats []*TaskStatistics) error
	DeleteTaskStatsByTaskTable(ctx context.Context, taskId int, schemaName string, tableName string) error
	BatchGetTaskStats(ctx context.Context, taskStats *TaskStatistics) ([]*TaskStatistics, error)
	GetTaskStatsCountsByStatusSchema(ctx context.Context, taskId int) ([]*TaskStatisticsByStatusSchema, error)
	GetTaskStatsCountsBySchema(ctx context.Context, taskId int) ([]*TaskStatisticsBySchema, error)
	GetTaskStatsCountsJoinByTask(ctx context.Context, channelId int, taskId int) ([]*TaskStatisticsJoinBySchema, error)
	GetTaskStatsByTaskId(ctx context.Context, taskId int) ([]*TaskStatistics, error)
	ListTaskStatsTablesDuplicate(ctx context.Context, channelId int) ([]*TaskStatisticsTableDuplicate, error)
	ListTaskStatsTablesByPage(ctx context.Context, taskStats *TaskStatistics, page int, pageSize int) ([]*TaskStatistics, int64, error)
	ListTaskStatsTablesByTablePage(ctx context.Context, taskId int, schemaName string, tableName string, page int, pageSize int) ([]*TaskStatistics, int64, error)
	ListTaskStatsConflictTable(ctx context.Context, channelId int, taskId int, schemaName string, page int, pageSize int) ([]*ChannelTaskSchemaStatisticsDuplicate, int64, error)
}
