package statistics

import (
	"context"
	"fmt"
	"strings"

	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type RunStatsReadWrite struct {
	dbCommon.GormDB
}

func NewRunStatsReadWrite(db *gorm.DB) *RunStatsReadWrite {
	m := &RunStatsReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw *RunStatsReadWrite) CreateTaskStats(ctx context.Context, taskStats *TaskStatistics) error {
	err := rw.DB(ctx).Create(taskStats).Error
	if err != nil {
		log.Errorf("create table taskStats failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Infof("create table taskStats successfully, %v", taskStats.TableName)
	return nil
}

func (rw *RunStatsReadWrite) UpdateTaskStatsRowsByTable(ctx context.Context, taskStats *TaskStatistics) error {
	err := rw.DB(ctx).Model(&TaskStatistics{}).Where("task_id = ? AND schema_name = ? AND table_name = ?",
		taskStats.TaskId,
		taskStats.SchemaName,
		taskStats.TableName).Updates(map[string]any{
		"last_analyzed_rows": taskStats.TableRows,
		"table_rows":         taskStats.TableRows,
	}).Error
	if err != nil {
		log.Errorf("update table taskStats info failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *RunStatsReadWrite) UpdateTaskStatsStatusStartTimeByTable(ctx context.Context, taskStats *TaskStatistics) error {
	err := rw.DB(ctx).Model(&TaskStatistics{}).Where("task_id = ? AND schema_name = ? AND table_name = ?",
		taskStats.TaskId,
		taskStats.SchemaName,
		taskStats.TableName).Updates(map[string]any{
		"analyze_status":         taskStats.AnalyzeStatus,
		"last_analyze_starttime": taskStats.LastAnalyzeStarttime,
		"message":                taskStats.Message,
	}).Error
	if err != nil {
		log.Errorf("update table taskStats start info failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Debug("update table taskStats start info success. %v", taskStats)
	return nil
}

func (rw *RunStatsReadWrite) UpdateTaskStatsStatusEndTimeByTable(ctx context.Context, taskStats *TaskStatistics) error {
	err := rw.DB(ctx).Model(&TaskStatistics{}).Where("task_id = ? AND schema_name = ? AND table_name = ?",
		taskStats.TaskId,
		taskStats.SchemaName,
		taskStats.TableName).Updates(map[string]any{
		"analyze_status":       taskStats.AnalyzeStatus,
		"last_analyze_endtime": taskStats.LastAnalyzeEndtime,
		"message":              taskStats.Message,
		"last_analyzed_rows":   taskStats.LastAnalyzedRows,
	}).Error
	if err != nil {
		log.Errorf("update table taskStats end info failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Debug("update table taskStats end info success. %v", taskStats)
	return nil
}

func (rw *RunStatsReadWrite) SaveTaskStats(ctx context.Context, taskStats []*TaskStatistics) error {
	err := rw.DB(ctx).Save(taskStats).Error
	if err != nil {
		log.Errorf("save table taskStats info failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *RunStatsReadWrite) DeleteTaskStatsByTaskTable(ctx context.Context, taskId int, schemaName string, tableName string) error {
	err := rw.DB(ctx).Unscoped().Where("task_id = ? and schema_name = ? and table_name=?", taskId, schemaName, tableName).Delete(&TaskStatistics{}).Error
	if err != nil {
		log.Errorf("delete table taskStats failed,taskid:%d, table:%s.%s. err:%s", taskId, schemaName, tableName, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *RunStatsReadWrite) BatchGetTaskStats(ctx context.Context, taskStats *TaskStatistics) ([]*TaskStatistics, error) {
	returnData := make([]*TaskStatistics, 0)
	err := rw.DB(ctx).Model(&TaskStatistics{}).Where(taskStats).Order("priority,schema_name,table_name").Find(&returnData).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query taskStats not found, query cond: %v", taskStats.TableName)
	} else if err != nil {
		log.Errorf("batch query taskStats failed, query cond %d, %v", taskStats.TableName, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return returnData, nil
}

func (rw *RunStatsReadWrite) GetTaskStatsByTaskId(ctx context.Context, taskId int) ([]*TaskStatistics, error) {
	returnData := make([]*TaskStatistics, 0)
	err := rw.DB(ctx).Model(&TaskStatistics{}).Where("task_id = ?", taskId).Find(&returnData).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query taskStats not found, query taskid: %v", taskId)
	} else if err != nil {
		log.Errorf("batch query taskStats failed, query taskid:%d, %v", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return returnData, nil
}

func (rw *RunStatsReadWrite) GetTaskStatsCountsByStatusSchema(ctx context.Context, taskId int) ([]*TaskStatisticsByStatusSchema, error) {
	records := make([]*TaskStatisticsByStatusSchema, 0)
	err := rw.DB(ctx).Model(&TaskStatistics{}).Select(`task_id,schema_name,analyze_status,
	count(1) as count,
	min(last_analyze_starttime) as last_analyze_starttime,
	max(last_analyze_endtime) as last_analyze_endtime
	`).Where("task_id = ? ", taskId).Group("task_id,schema_name,analyze_status").Find(&records).Error
	if err != nil {
		log.Errorf("GetTaskStatsCountsByStatusSchema failed. taskId is %d, startTime:%v, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *RunStatsReadWrite) GetTaskStatsCountsBySchema(ctx context.Context, taskId int) ([]*TaskStatisticsBySchema, error) {
	records := make([]*TaskStatisticsBySchema, 0)
	err := rw.DB(ctx).Model(&TaskStatistics{}).Select(`task_id,schema_name as `+"`schema`"+`,
	count(1) as total_nums,
	sum(case when analyze_status='success' then 1 else 0 end) as success_nums,
	sum(case when analyze_status='failed' then 1 else 0 end) as failed_nums,
	sum(case when analyze_status='running' then 1 else 0 end) as running_nums,
	sum(case when analyze_status='waiting' then 1 else 0 end) as waiting_nums,
	min(last_analyze_starttime) as last_analyze_starttime,
	max(last_analyze_endtime) as last_analyze_endtime
	`).Where("task_id = ? ", taskId).Group("task_id,schema_name").Find(&records).Error
	if err != nil {
		log.Errorf("GetTaskStatsCountsBySchema failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *RunStatsReadWrite) GetTaskStatsCountsJoinByTask(ctx context.Context, channelId int, taskId int) ([]*TaskStatisticsJoinBySchema, error) {
	records := make([]*TaskStatisticsJoinBySchema, 0)
	querySql := fmt.Sprintf(`select
	t2.task_id,
	t2.schema_name as `+"`schema`"+`,
	count(1) as total_nums,
	sum(case when analyze_status = 'success' then 1 else 0 end) as success_nums,
	sum(case when analyze_status = 'failed' then 1 else 0 end) as failed_nums,
	sum(case when analyze_status = 'running' then 1 else 0 end) as running_nums,
	sum(case when analyze_status = 'waiting' then 1 else 0 end) as waiting_nums,
	sum(case when t3.count>0 then 1 else 0 end) as duplicate_nums,
	min(last_analyze_starttime) as last_analyze_starttime,
	max(last_analyze_endtime) as last_analyze_endtime
from
	task_statistics t2
left outer join (
	select
		t1.channel_id,
		t1.schema_name_t as schema_name,
		t1.table_name_t as table_name,
		GROUP_CONCAT(task_id separator ';') taskids,
		count(1) count
	from
		channel_schema_tables t1
	where
		channel_id = %d
		and task_id > 0
		and task_type = 6
	group by
		t1.channel_id,
		t1.schema_name_t,
		t1.table_name_t
	having
		count(1)>1) t3 on
	t2.schema_name = t3.schema_name
	and t2.table_name = t3.table_name
where
	t2.task_id = %d
	and t2.deleted_at is null
group by
	t2.task_id,
	t2.schema_name
`, channelId, taskId)
	err := rw.DB(ctx).Raw(querySql).Find(&records).Error
	if err != nil {
		log.Errorf("GetTaskStatsCountsBySchema failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *RunStatsReadWrite) ListTaskStatsTablesDuplicate(ctx context.Context, channelId int) ([]*TaskStatisticsTableDuplicate, error) {
	records := make([]*TaskStatisticsTableDuplicate, 0)
	querySql := fmt.Sprintf(`select
	t1.channel_id,
        t1.schema_name_t as schema_name,
        t1.table_name_t as table_name,
        GROUP_CONCAT(distinct task_id separator ';') duplicate_tasks,
        count(1) duplicate_count
    from
        channel_schema_tables t1
    where
        channel_id = %d
		and task_id > 0
		and task_type = 6
    group by
        t1.channel_id,
        t1.schema_name_t,
        t1.table_name_t
    having
        count(1)>1
`, channelId)
	err := rw.DB(ctx).Raw(querySql).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query TaskStatisticsTableJoinDuplicate not found, query channel:%d", channelId)
	} else if err != nil {
		log.Errorf("batch query TaskStatisticsTableJoinDuplicate not found, query channel:%d, err:%v", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *RunStatsReadWrite) ListTaskStatsTablesByPage(ctx context.Context, taskStats *TaskStatistics, page int, pageSize int) ([]*TaskStatistics, int64, error) {
	records := make([]*TaskStatistics, 0, pageSize)
	var count int64 = 0
	err := rw.DB(ctx).Model(&TaskStatistics{}).Where(taskStats).Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return nil, count, fmt.Errorf("batch query table summary not found, query cond:%v", taskStats)
	} else if err != nil {
		log.Errorf("batch get table summary failed, query cond:%s, err:%v", taskStats, err)
		return nil, count, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *RunStatsReadWrite) ListTaskStatsTablesByTablePage(ctx context.Context, taskId int, schemaName string, tableName string, page int, pageSize int) ([]*TaskStatistics, int64, error) {
	records := make([]*TaskStatistics, 0, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TaskStatistics{})
	query.Where("task_id = ?", taskId)
	if strings.TrimSpace(schemaName) != "" {
		query.Where(" schema_name like '%" + schemaName + "%'")
	}
	if strings.TrimSpace(tableName) != "" {
		query.Where(" table_name like '%" + tableName + "%'")
	}
	err := query.Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return nil, count, fmt.Errorf("batch query table summary not found, query cond:%d, %s.%s", taskId, schemaName, tableName)
	} else if err != nil {
		log.Errorf("batch get table summary failed, query cond:%d, %s.%s, err:%v", taskId, schemaName, tableName, err)
		return nil, count, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *RunStatsReadWrite) ListTaskStatsConflictTable(ctx context.Context, channelId int, taskId int, schemaName string, page int, pageSize int) ([]*ChannelTaskSchemaStatisticsDuplicate, int64, error) {
	records := make([]*ChannelTaskSchemaStatisticsDuplicate, 0, pageSize)
	var count int64 = 0
	querySql := fmt.Sprintf(`select count(1) as count from channel_schema_tables t1 
	where t1.channel_id = %d
	and t1.task_id = %d
	and t1.schema_name_t = '%s'
	and t1.task_type = 6
	and t1.deleted_at is null`, channelId, taskId, schemaName)
	err := rw.DB(ctx).Raw(querySql).Find(&count).Error
	if err != nil {
		log.Errorf("channel_schema_tables count failed. taskId is %d, err:%s", taskId, err)
		return nil, count, dbCommon.WrapDBError(err)
	}
	querySql = fmt.Sprintf(`select distinct
	t1.channel_schtable_id,
	t1.channel_id,
	t1.task_id,
	t1.schema_name_t as schema_name,
	t1.table_name_t as table_name,
	t2.last_analyze_starttime,
	t2.last_analyze_endtime,
	t2.analyze_status,
	t2.samplerate,
	t2.last_analyzed_rows,
	t2.table_rows,
	t2.priority,
	t2.message,
	t3.duplicate_tasks,
	t3.duplicate_count
  from
	channel_schema_tables t1
  left outer join task_statistics t2 on
	t1.task_id = t2.task_id
	and t1.schema_name_t = t2.schema_name
	and t1.table_name_t = t2.table_name
  inner join (
	select
	  channel_id,
	  schema_name_t as schema_name,
	  table_name_t as table_name,
	  GROUP_CONCAT(distinct task_id separator ';') duplicate_tasks,
	  count(1) duplicate_count
	from
	  channel_schema_tables 
	where
	  channel_id = %d
	  and task_id > 0
	  and task_type = 6
	group by
	  channel_id,
	  schema_name_t,
	  table_name_t
	having
	  count(1)>1) t3 on
	t1.schema_name_t = t3.schema_name
	and t1.table_name_t = t3.table_name
  where
	t1.task_id = %d
	and t1.schema_name_t = '%s'
	and t1.task_type = 6
	and t1.deleted_at is null
  limit %d,%d
  `, channelId, taskId, schemaName, (page-1)*pageSize, pageSize)
	err = rw.DB(ctx).Raw(querySql).Find(&records).Error
	if err != nil {
		log.Errorf("GetTaskStatsCountsBySchema failed. taskId is %d, err:%s", taskId, err)
		return nil, count, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}
