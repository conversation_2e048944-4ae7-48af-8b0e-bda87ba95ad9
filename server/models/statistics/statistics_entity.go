package statistics

import (
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type TableStatisticsSummary struct {
	StatsId        int       `gorm:"type:int;primarykey"`
	ChannelId      int       `gorm:"type:int;"`
	TaskId         int       `gorm:"type:int;index:idx_taskid;"`
	DbNameT        string    `gorm:"type:varchar(100);"`
	SchemaNameT    string    `gorm:"type:varchar(200);index:idx_taskid;"`
	TableNameT     string    `gorm:"type:varchar(200);index:idx_taskid;"`
	TableTypeT     string    `gorm:"type:varchar(100);"`
	Starttime      time.Time `gorm:"type:datetime;"`
	Endtime        time.Time `gorm:"type:datetime;"`
	Status         string    `gorm:"type:varchar(20);comment:run status"`
	Message        string    `gorm:"type:varchar(1000);"`
	TableCount     int       `gorm:"type:int;comment:rows from information_schema.tables"`
	AnalyzeVersion string    `gorm:"type:varchar(200);"`
	Columns        string    `gorm:"type:varchar(500);"`
	Samples        int       `gorm:"type:int;comment:WITH NUM SAMPLES"`
	Samplerate     float64   `gorm:"type:decimal(10,8);comment:WITH FLOATNUM SAMPLERATE"`
	TableKeys      int       `gorm:"type:int;comment:rows from information_schema.table_storage_stats "`
	RowCount       int       `gorm:"type:int;comment:rows from information_schema.stats_meta"`
	Updatetime     time.Time `gorm:"type:datetime;"`
}

type TaskStatistics struct {
	TaskStatsId          int       `gorm:"type:int;primarykey"`
	ChannelId            int       `gorm:"type:int;"`
	TaskId               int       `gorm:"type:int;index:idx_taskid;"`
	SchemaName           string    `gorm:"type:varchar(50);index:idx_taskid;"`
	TableName            string    `gorm:"type:varchar(100);index:idx_taskid;"`
	LastAnalyzeStarttime time.Time `gorm:"type:datetime;"`
	LastAnalyzeEndtime   time.Time `gorm:"type:datetime;"`
	AnalyzeStatus        string    `gorm:"type:varchar(10);comment:run status"`
	Samplerate           float64   `gorm:"type:decimal(10,8);comment:WITH FLOATNUM SAMPLERATE"`
	LastAnalyzedRows     uint64    `gorm:"type:bigint;comment:rows from information_schema.tables"`
	TableRows            uint64    `gorm:"type:bigint;"`
	Priority             int       `gorm:"type:int;"`
	Message              string    `gorm:"type:varchar(1000);"`
	*common.Entity
}

type TaskStatisticsByStatusSchema struct {
	TaskId               int       `json:"taskId"`
	SchemaName           string    `json:"schemaName"`
	AnalyzeStatus        string    `json:"analyzeStatus"`
	Count                int       `json:"count"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
}

type TaskStatisticsBySchema struct {
	TaskId               int       `json:"taskId"`
	Schema               string    `json:"schema"`
	TotalNums            int       `json:"totalNums"`
	SuccessNums          int       `json:"successNums"`
	FailedNums           int       `json:"failedNums"`
	RunningNums          int       `json:"runningNums"`
	WaitingNums          int       `json:"waitingNums"`
	SuccessRatio         string    `json:"successRatio"`
	Duration             string    `json:"compareDuration"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
}

type TaskStatisticsJoinBySchema struct {
	TaskId               int       `json:"taskId"`
	Schema               string    `json:"schema"`
	TotalNums            int       `json:"totalNums"`
	SuccessNums          int       `json:"successNums"`
	FailedNums           int       `json:"failedNums"`
	RunningNums          int       `json:"runningNums"`
	WaitingNums          int       `json:"waitingNums"`
	DuplicateNums        int       `json:"duplicateNums"`
	SuccessRatio         string    `json:"successRatio"`
	Duration             string    `json:"compareDuration"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
}

type TaskStatisticsTableDuplicate struct {
	ChannelId      int    `json:"channelId"`
	SchemaName     string `json:"schemaName"`
	TableName      string `json:"tableName"`
	DuplicateTasks string `json:"duplicateTasks"`
	DuplicateCount int    `json:"duplicateCount"`
}

type ChannelTaskSchemaStatisticsDuplicate struct {
	ChannelSchtableId    int       `json:"channelSchtableId"`
	ChannelId            int       `json:"channelId"`
	TaskId               int       `json:"taskId"`
	SchemaName           string    `json:"schemaName"`
	TableName            string    `json:"tableName"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
	AnalyzeStatus        string    `json:"analyzeStatus"`
	Samplerate           float64   `json:"samplerate"`
	LastAnalyzedRows     int       `json:"lastAnalyzedRows"`
	TableRows            int       `json:"tableRows"`
	Priority             int       `json:"priority"`
	Message              string    `json:"message"`
	DuplicateTasks       string    `json:"duplicateTasks"`
	DuplicateCount       int       `json:"duplicateCount"`
}
