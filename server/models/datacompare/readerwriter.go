package datacompare

import (
	"context"
	"time"
)

type ReaderWriter interface {
	CreateDataCompareLog(ctx context.Context, log *DataCompareLog) (*DataCompareLog, error)
	CreateDataCompareLogs(ctx context.Context, logs []DataCompareLog) error
	GetDataCompareLogsByTime(ctx context.Context, taskId int, startTime *time.Time) ([]*DataCompareLog, error)
	GetDataCompareSummary(ctx context.Context, taskId int) ([]*Summary, error)
	GetDataCompareSummaryCountByState(ctx context.Context, taskId int, startTime time.Time) ([]*SummaryByState, error)
	GetDataCompareSummaryCountBySchemaState(ctx context.Context, taskId int, startTime time.Time) ([]*SummaryBySchemaState, error)
	GetDataCompareSummaryCountBySchema(ctx context.Context, taskId int) ([]*SummaryBySchema, error)
	ListDataCompareSummary(ctx context.Context, chunkInfo *Summary) ([]*Summary, error)
	ListDataCompareChunk(ctx context.Context, chunkInfo *Chunk) ([]*Chunk, error)
	ListDataCompareSummaryByStatusPage(ctx context.Context, chunkInfo *Summary, states []string, page int, pageSize int) ([]*Summary, int64, error)
	SaveDataCompareSummaries(ctx context.Context, summaries []Summary) error
	SaveDataCompareChunks(ctx context.Context, chunks []Chunk) error
	DeleteDataCompareSummaryByChannelIdTaskId(ctx context.Context, channelId, taskId int) error
	DeleteDataCompareChunkByChannelIdTaskId(ctx context.Context, channelId, taskId int) error

	BatchCreateEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error
	BatchUpdateEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error
	GetEnvDeployTask(ctx context.Context, task *EnvDeployTask) (*EnvDeployTask, error)
	UpdateEnvDeployTask(ctx context.Context, task *EnvDeployTask) error
	ListEnvDeployTask(ctx context.Context, query *EnvDeployTask) ([]EnvDeployTask, error)
	ClearEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error
	ResetDataCompareChunks(ctx context.Context, taskId int) error
	ResetCompareSummary(ctx context.Context, taskId int) error
}
