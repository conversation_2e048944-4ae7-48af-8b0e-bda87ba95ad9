package datacompare

import (
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"

	"gorm.io/gorm"
)

type DataCompareLog struct {
	LogId      int       `gorm:"primarykey"`
	ChannelId  int       `gorm:"comment:channel id"`
	TaskId     int       `gorm:"index:idx_task_id_create_time;comment:task id"`
	LogMessage string    `gorm:"type:varchar(1000);comment:message"`
	Comment    string    `gorm:"type:varchar(1000);comment:comment content"`
	LogLevel   string    `gorm:"type:varchar(20);comment:log level"`
	CreatedAt  time.Time `gorm:"<-:create;index:idx_task_id_create_time"`
	UpdatedAt  time.Time
	DeletedAt  gorm.DeletedAt `gorm:"index"`
}

type Summary struct {
	ChannelId         int       `gorm:"type:int" json:"channelId"`
	TaskId            int       `gorm:"type:int;index:idx_task_id" json:"taskId"`
	Schema            string    `gorm:"type:varchar(64)" json:"schema"`
	Table             string    `gorm:"type:varchar(64);index:idx_table" json:"table"`
	ChunkNum          int       `gorm:"type:int" json:"chunkNum"`
	CheckSuccessNum   int       `gorm:"type:int" json:"checkSuccessNum"`
	CheckFailedNum    int       `gorm:"type:int" json:"checkFailedNum"`
	CheckIgnoreNum    int       `gorm:"type:int" json:"checkIgnoreNum"`
	State             string    `gorm:"type:varchar(20)" json:"state"`
	ConfigHash        string    `gorm:"type:varchar(50)" json:"configHash"`
	StartTime         time.Time `gorm:"datetime" json:"startTime"`
	UpdateTime        time.Time `gorm:"datetime" json:"updateTime"`
	ChannelSchtableId int       `gorm:"type:int" json:"channelSchtableId"`
}

type SummaryByState struct {
	ChannelId       int       `json:"channelId"`
	TaskId          int       `json:"taskId"`
	State           string    `json:"state"`
	Count           int       `json:"count"`
	ChunkNum        int       `json:"chunkNum"`
	CheckSuccessNum int       `json:"checkSuccessNum"`
	CheckFailedNum  int       `json:"checkFailedNum"`
	CheckIgnoreNum  int       `json:"checkIgnoreNum"`
	StartTime       time.Time `json:"startTime"`
	EndTime         time.Time `json:"endTime"`
}

type SummaryBySchemaState struct {
	ChannelId       int       `json:"channelId"`
	TaskId          int       `json:"taskId"`
	Schema          string    `json:"schema"`
	State           string    `json:"state"`
	Count           int       `json:"count"`
	ChunkNum        int       `json:"chunkNum"`
	CheckSuccessNum int       `json:"checkSuccessNum"`
	CheckFailedNum  int       `json:"checkFailedNum"`
	CheckIgnoreNum  int       `json:"checkIgnoreNum"`
	StartTime       time.Time `json:"startTime"`
	EndTime         time.Time `json:"endTime"`
}

type SummaryBySchema struct {
	TaskId          int       `json:"taskId"`
	Schema          string    `json:"schema"`
	TotalNums       int       `json:"totalNums"`
	SuccessNums     int       `json:"successNums"`
	FailedNums      int       `json:"failedNums"`
	RunningNums     int       `json:"runningNums"`
	WaitingNums     int       `json:"waitingNums"`
	SuccessRatio    string    `json:"successRatio"`
	CompareDuration string    `json:"compareDuration"`
	StartTime       time.Time `json:"StartTime"`
	EndTime         time.Time `json:"endTime"`
}

type Chunk struct {
	ChunkId     int       `gorm:"type:int;primarykey" json:"chunkId"`
	InstanceId  string    `gorm:"type:varchar(64);primarykey" json:"instanceId"`
	Schema      string    `gorm:"type:varchar(64);primarykey" json:"schema"`
	Table       string    `gorm:"type:varchar(64);primarykey" json:"table"`
	Range       string    `gorm:"type:text" json:"range"`
	Checksum    string    `gorm:"type:varchar(20)" json:"checksum"`
	ChunkStr    string    `gorm:"type:text" json:"chunkStr"`
	State       string    `gorm:"type:varchar(20)" json:"state"`
	UpdateTime  time.Time `gorm:"type:datetime" json:"updateTime"`
	SourceTime  string    `gorm:"type:varchar(64)" json:"sourceTime"`
	TargetTime  string    `gorm:"type:varchar(64)" json:"targetTime"`
	SourceCount int       `gorm:"type:int" json:"sourceCount"`
	TargetCount int       `gorm:"type:int" json:"targetCount"`
	ChannelId   int       `gorm:"type:int;primarykey" json:"channelId"`
	TaskId      int       `gorm:"type:int;primarykey" json:"taskId"`
	Message     string    `gorm:"type:text" json:"message"`
}

type EnvDeployTask struct {
	DataCompareEnvDeployId int       `gorm:"primarykey;column:datacompare_env_deploy_id"`
	TaskNumber             int       `gorm:"type:int" json:"task_number"`
	TaskName               string    `gorm:"type:varchar(100)" json:"task_name"`
	IsIgnore               string    `gorm:"type:varchar(1)" json:"is_ignore"`
	TaskStatus             int       `gorm:"type:int" json:"task_status"`
	TaskLog                string    `gorm:"type:varchar(2000)" json:"task_log"`
	TaskSQL                string    `gorm:"type:longtext" json:"task_sql"`
	LastRunTime            time.Time `gorm:"type:datetime" json:"last_run_time"`
	TaskId                 int       `gorm:"type:int" json:"task_id"`
	Comment                string    `gorm:"type:varchar(1000)" json:"comment"`
	SQLModel               string    `gorm:"type:varchar(20)" json:"sql_model"`

	*common.Entity
}

func (i EnvDeployTask) TableName() string {
	return "data_compare_env_deploy_tasks"
}
