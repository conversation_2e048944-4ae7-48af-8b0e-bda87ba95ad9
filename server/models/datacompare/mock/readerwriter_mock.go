// Code generated by MockGen. DO NOT EDIT.
// Source: ./datacompare/readerwriter.go

// Package mockdatacomparereaderwriter is a generated GoMock package.
package mockdatacomparereaderwriter

import (
	context "context"
	reflect "reflect"
	time "time"

	datacompare "gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	gomock "github.com/golang/mock/gomock"
)

// MockReaderWriter is a mock of ReaderWriter interface.
type MockReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReaderWriterMockRecorder
}

// MockReaderWriterMockRecorder is the mock recorder for MockReaderWriter.
type MockReaderWriterMockRecorder struct {
	mock *MockReaderWriter
}

// NewMockReaderWriter creates a new mock instance.
func NewMockReaderWriter(ctrl *gomock.Controller) *MockReaderWriter {
	mock := &MockReaderWriter{ctrl: ctrl}
	mock.recorder = &MockReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReaderWriter) EXPECT() *MockReaderWriterMockRecorder {
	return m.recorder
}

// CreateDataCompareLog mocks base method.
func (m *MockReaderWriter) CreateDataCompareLog(ctx context.Context, log *datacompare.DataCompareLog) (*datacompare.DataCompareLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDataCompareLog", ctx, log)
	ret0, _ := ret[0].(*datacompare.DataCompareLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataCompareLog indicates an expected call of CreateDataCompareLog.
func (mr *MockReaderWriterMockRecorder) CreateDataCompareLog(ctx, log interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataCompareLog", reflect.TypeOf((*MockReaderWriter)(nil).CreateDataCompareLog), ctx, log)
}

// GetDataCompareLogsByTime mocks base method.
func (m *MockReaderWriter) GetDataCompareLogsByTime(ctx context.Context, taskId int, startTime *time.Time) ([]*datacompare.DataCompareLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataCompareLogsByTime", ctx, taskId, startTime)
	ret0, _ := ret[0].([]*datacompare.DataCompareLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDataCompareLogsByTime indicates an expected call of GetDataCompareLogsByTime.
func (mr *MockReaderWriterMockRecorder) GetDataCompareLogsByTime(ctx, taskId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataCompareLogsByTime", reflect.TypeOf((*MockReaderWriter)(nil).GetDataCompareLogsByTime), ctx, taskId, startTime)
}

// GetDataCompareSummary mocks base method.
func (m *MockReaderWriter) GetDataCompareSummary(ctx context.Context, taskId int, startTime time.Time) ([]*datacompare.Summary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataCompareSummary", ctx, taskId, startTime)
	ret0, _ := ret[0].([]*datacompare.Summary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDataCompareSummary indicates an expected call of GetDataCompareSummary.
func (mr *MockReaderWriterMockRecorder) GetDataCompareSummary(ctx, taskId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataCompareSummary", reflect.TypeOf((*MockReaderWriter)(nil).GetDataCompareSummary), ctx, taskId, startTime)
}

// GetDataCompareSummaryCountBySchema mocks base method.
func (m *MockReaderWriter) GetDataCompareSummaryCountBySchema(ctx context.Context, taskId int) ([]*datacompare.SummaryBySchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataCompareSummaryCountBySchema", ctx, taskId)
	ret0, _ := ret[0].([]*datacompare.SummaryBySchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDataCompareSummaryCountBySchema indicates an expected call of GetDataCompareSummaryCountBySchema.
func (mr *MockReaderWriterMockRecorder) GetDataCompareSummaryCountBySchema(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataCompareSummaryCountBySchema", reflect.TypeOf((*MockReaderWriter)(nil).GetDataCompareSummaryCountBySchema), ctx, taskId)
}

// GetDataCompareSummaryCountBySchemaState mocks base method.
func (m *MockReaderWriter) GetDataCompareSummaryCountBySchemaState(ctx context.Context, taskId int, startTime time.Time) ([]*datacompare.SummaryBySchemaState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataCompareSummaryCountBySchemaState", ctx, taskId, startTime)
	ret0, _ := ret[0].([]*datacompare.SummaryBySchemaState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDataCompareSummaryCountBySchemaState indicates an expected call of GetDataCompareSummaryCountBySchemaState.
func (mr *MockReaderWriterMockRecorder) GetDataCompareSummaryCountBySchemaState(ctx, taskId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataCompareSummaryCountBySchemaState", reflect.TypeOf((*MockReaderWriter)(nil).GetDataCompareSummaryCountBySchemaState), ctx, taskId, startTime)
}

// GetDataCompareSummaryCountByState mocks base method.
func (m *MockReaderWriter) GetDataCompareSummaryCountByState(ctx context.Context, taskId int, startTime time.Time) ([]*datacompare.SummaryByState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataCompareSummaryCountByState", ctx, taskId, startTime)
	ret0, _ := ret[0].([]*datacompare.SummaryByState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDataCompareSummaryCountByState indicates an expected call of GetDataCompareSummaryCountByState.
func (mr *MockReaderWriterMockRecorder) GetDataCompareSummaryCountByState(ctx, taskId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataCompareSummaryCountByState", reflect.TypeOf((*MockReaderWriter)(nil).GetDataCompareSummaryCountByState), ctx, taskId, startTime)
}

// ListDataCompareChunk mocks base method.
func (m *MockReaderWriter) ListDataCompareChunk(ctx context.Context, chunkInfo *datacompare.Chunk) ([]*datacompare.Chunk, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDataCompareChunk", ctx, chunkInfo)
	ret0, _ := ret[0].([]*datacompare.Chunk)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDataCompareChunk indicates an expected call of ListDataCompareChunk.
func (mr *MockReaderWriterMockRecorder) ListDataCompareChunk(ctx, chunkInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDataCompareChunk", reflect.TypeOf((*MockReaderWriter)(nil).ListDataCompareChunk), ctx, chunkInfo)
}

// ListDataCompareSummary mocks base method.
func (m *MockReaderWriter) ListDataCompareSummary(ctx context.Context, chunkInfo *datacompare.Summary) ([]*datacompare.Summary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDataCompareSummary", ctx, chunkInfo)
	ret0, _ := ret[0].([]*datacompare.Summary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListDataCompareSummary indicates an expected call of ListDataCompareSummary.
func (mr *MockReaderWriterMockRecorder) ListDataCompareSummary(ctx, chunkInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDataCompareSummary", reflect.TypeOf((*MockReaderWriter)(nil).ListDataCompareSummary), ctx, chunkInfo)
}

// ListDataCompareSummaryByPage mocks base method.
func (m *MockReaderWriter) ListDataCompareSummaryByPage(ctx context.Context, chunkInfo *datacompare.Summary, page, pageSize int) ([]*datacompare.Summary, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListDataCompareSummaryByStatusPage", ctx, chunkInfo, page, pageSize)
	ret0, _ := ret[0].([]*datacompare.Summary)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListDataCompareSummaryByPage indicates an expected call of ListDataCompareSummaryByPage.
func (mr *MockReaderWriterMockRecorder) ListDataCompareSummaryByPage(ctx, chunkInfo, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListDataCompareSummaryByStatusPage", reflect.TypeOf((*MockReaderWriter)(nil).ListDataCompareSummaryByPage), ctx, chunkInfo, page, pageSize)
}
