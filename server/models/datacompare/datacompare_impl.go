package datacompare

import (
	"context"
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/pkg/errors"

	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type DataCompareReadWrite struct {
	dbCommon.GormDB
}

func NewDataCompareReadWrite(db *gorm.DB) *DataCompareReadWrite {
	m := &DataCompareReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw DataCompareReadWrite) CreateDataCompareLog(ctx context.Context, dataCompareLog *DataCompareLog) (*DataCompareLog, error) {
	err := rw.DB(ctx).Create(dataCompareLog).Error
	if err != nil {
		log.Errorf("create data compare log failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return dataCompareLog, nil
}

func (rw DataCompareReadWrite) GetDataCompareLogsByTime(ctx context.Context, taskId int, startTime *time.Time) ([]*DataCompareLog, error) {
	records := make([]*DataCompareLog, 0)
	err := rw.DB(ctx).Model(&DataCompareLog{}).Where("task_id = ? and created_at >= ?", taskId, startTime).Order("created_at").Find(&records).Error
	if err != nil {
		log.Errorf("get data compare logs by time failed. taskId is %d, startTime:%v, err:%s", taskId, startTime, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) GetDataCompareSummary(ctx context.Context, taskId int) ([]*Summary, error) {
	records := make([]*Summary, 0)
	err := rw.DB(ctx).Model(&Summary{}).Where("task_id = ?", taskId).Find(&records).Error
	if err != nil {
		log.Errorf("get data compare logs by time failed. taskId is %d,  err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) GetDataCompareSummaryCountByState(ctx context.Context, taskId int, startTime time.Time) ([]*SummaryByState, error) {
	records := make([]*SummaryByState, 0)
	err := rw.DB(ctx).Model(&Summary{}).Select(`channel_id,task_id,state,
	count(1) as count,
	sum(chunk_num) as chunk_num,
	sum(check_success_num) as check_success_num,
	sum(check_failed_num) as check_failed_num,
	sum(check_ignore_num) as check_ignore_num,
	min(start_time) as start_time,
	max(update_time) as end_time
	`).Where("task_id = ? ", taskId).Group("channel_id,task_id,state").Find(&records).Error
	if err != nil {
		log.Errorf("GetDataCompareSummaryCountByState failed. taskId is %d, startTime:%v, err:%s", taskId, startTime, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) GetDataCompareSummaryCountBySchemaState(ctx context.Context, taskId int, startTime time.Time) ([]*SummaryBySchemaState, error) {
	records := make([]*SummaryBySchemaState, 0)
	err := rw.DB(ctx).Model(&Summary{}).Select(`channel_id,task_id,`+"`schema`"+`,state,
	count(1) as count,
	sum(chunk_num) as chunk_num,
	sum(check_success_num) as check_success_num,
	sum(check_failed_num) as check_failed_num,
	sum(check_ignore_num) as check_ignore_num,
	min(start_time) as start_time,
	max(update_time) as end_time
	`).Where("task_id = ? ", taskId).Group("channel_id,`schema`,task_id,state").Find(&records).Error
	if err != nil {
		log.Errorf("GetDataCompareSummaryCountByState failed. taskId is %d, startTime:%v, err:%s", taskId, startTime, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) GetDataCompareSummaryCountBySchema(ctx context.Context, taskId int) ([]*SummaryBySchema, error) {
	records := make([]*SummaryBySchema, 0)
	query := rw.DB(ctx).Model(&Summary{}).Select(`task_id,`+"lower(`schema`) AS `schema`"+`,
	count(1) as TotalNums,
	sum(case when state='success' then 1 else  0 end ) as SuccessNums,
	sum(case when state in ('failed','error','invalid') then 1 else  0 end ) as FailedNums,
	sum(case when state='checking' then 1 else  0 end ) as RunningNums,
	sum(case when state='not_checked' then 1 else  0 end ) as WaitingNums,
	sum(case when unix_timestamp(start_time) = 0 then 0 else timestampdiff(second, start_time, update_time) end) as CompareDuration,
	min(case when unix_timestamp(start_time) = 0 then null else start_time end) as StartTime,
	max(update_time) as EndTime
	`).Where("task_id = ? ", taskId).Group("task_id,lower(`schema`)").Order("lower(`schema`) asc")
	err := query.Find(&records).Error
	if err != nil {
		log.Errorf("GetDataCompareSummaryCountByState failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) ListDataCompareChunk(ctx context.Context, chunkInfo *Chunk) ([]*Chunk, error) {
	records := make([]*Chunk, 0)
	var err error
	if chunkInfo.State == "success" {
		err = rw.DB(ctx).Where(chunkInfo).Find(&records).Error
	} else {
		err = rw.DB(ctx).Model(&Chunk{}).Limit(10).Where("task_id=? and `schema`=? and `table`=? and `state`<>'success'", chunkInfo.TaskId, chunkInfo.Schema, chunkInfo.Table).Find(&records).Error
	}
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table Chunk not found, query cond:%v", chunkInfo)
	} else if err != nil {
		log.Errorf("batch get table Chunk failed, query cond:%s, err:%v", chunkInfo, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) ListDataCompareSummary(ctx context.Context, summaryInfo *Summary) ([]*Summary, error) {
	records := make([]*Summary, 0)
	err := rw.DB(ctx).Limit(100).Where(summaryInfo).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table summary not found, query cond:%v", summaryInfo)
	} else if err != nil {
		log.Errorf("batch get table summary failed, query cond:%s, err:%v", summaryInfo, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DataCompareReadWrite) ListDataCompareSummaryByStatusPage(ctx context.Context, summaryInfo *Summary, states []string, page int, pageSize int) ([]*Summary, int64, error) {
	records := make([]*Summary, 0, pageSize)
	var count int64 = 0

	query := rw.DB(ctx).Model(&Summary{}).Where(summaryInfo)
	if len(states) > 0 {
		query = query.Where("state in (?)", states)
	}
	err := query.Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err == gorm.ErrRecordNotFound {
		return nil, count, fmt.Errorf("batch query table summary not found, query cond:%v", summaryInfo)
	} else if err != nil {
		log.Errorf("batch get table summary failed, query cond:%s, err:%v", summaryInfo, err)
		return nil, count, dbCommon.WrapDBError(err)
	}
	return records, count, nil

}

func (rw DataCompareReadWrite) BatchCreateEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error {
	err := rw.DB(ctx).Create(tasks).Error
	if err != nil {
		log.Errorf("batch create env deploy task failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) BatchUpdateEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error {
	err := rw.DB(ctx).Save(tasks).Error
	if err != nil {
		log.Errorf("batch update env deploy task failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) UpdateEnvDeployTask(ctx context.Context, task *EnvDeployTask) error {
	err := rw.DB(ctx).Save(task).Error
	if err != nil {
		log.Errorf("update env deploy task failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) ListEnvDeployTask(ctx context.Context, query *EnvDeployTask) ([]EnvDeployTask, error) {
	var tasks []EnvDeployTask
	err := rw.GormDB.DB(ctx).Where(query).Find(&tasks).Order("task_number asc").Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query env deploy tasks not found, query cond: %v", query)
	} else if err != nil {
		log.Errorf("query env deploy tasks failed, query cond %d, %v", query, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw DataCompareReadWrite) GetEnvDeployTask(ctx context.Context, task *EnvDeployTask) (*EnvDeployTask, error) {
	var t EnvDeployTask
	err := rw.GormDB.DB(ctx).Where(task).First(&t).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query env deploy task not found, query cond: %v", task)
	} else if err != nil {
		log.Errorf("query env deploy task failed, query cond %d, %v", task, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return &t, nil
}

func (rw DataCompareReadWrite) ClearEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error {
	ids := make([]int, 0)
	for _, x := range tasks {
		ids = append(ids, x.DataCompareEnvDeployId)
	}
	return rw.DB(ctx).Model(&EnvDeployTask{}).Where("datacompare_env_deploy_id in (?)", ids).
		Updates(map[string]interface{}{
			"task_status":   constants.TASK_STATUS_NOT_RUNNING,
			"task_log":      "",
			"last_run_time": timeutil.GetTMSNullTime(),
		}).Error

}

func (rw DataCompareReadWrite) SaveDataCompareSummaries(ctx context.Context, summaries []Summary) error {
	err := rw.DB(ctx).Save(summaries).Error
	if err != nil {
		log.Errorf("save data compare summaries failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) DeleteDataCompareSummaryByChannelIdTaskId(ctx context.Context, channelId, taskId int) error {
	err := rw.DB(ctx).Where("channel_id = ? and task_id = ?", channelId, taskId).Delete(&Summary{}).Error
	if err != nil {
		log.Errorf("delete data compare summary failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) SaveDataCompareChunks(ctx context.Context, chunks []Chunk) error {
	err := rw.DB(ctx).Save(chunks).Error
	if err != nil {
		log.Errorf("save data compare chunks failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) DeleteDataCompareChunkByChannelIdTaskId(ctx context.Context, channelId, taskId int) error {
	err := rw.DB(ctx).Where("channel_id = ? and task_id = ?", channelId, taskId).Delete(&Chunk{}).Error
	if err != nil {
		log.Errorf("delete data compare chunk failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) CreateDataCompareLogs(ctx context.Context, logs []DataCompareLog) error {
	err := rw.DB(ctx).Create(logs).Error
	if err != nil {
		log.Errorf("create data compare logs failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) ResetDataCompareChunks(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Model(&Chunk{}).Where("task_id = ? and state = 'checking' ", taskId).Update("state", "failed").Error
	if err != nil {
		log.Errorf("ResetDataCompareChunks failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DataCompareReadWrite) ResetCompareSummary(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Model(&Summary{}).Where("task_id = ? and state = 'checking'", taskId).Update("state", "failed").Error
	if err != nil {
		log.Errorf("ResetCompareSummary failed, taskId:%d, err:%v", taskId, err)
		return err
	}
	return nil
}
