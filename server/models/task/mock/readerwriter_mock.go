// Code generated by MockGen. DO NOT EDIT.
// Source: ./task/readerwriter.go

// Package mocktaskreaderwriter is a generated GoMock package.
package mocktaskreaderwriter

import (
	context "context"
	sql "database/sql"
	reflect "reflect"
	time "time"

	structs "gitee.com/pingcap_enterprise/tms/common/structs"
	task "gitee.com/pingcap_enterprise/tms/server/models/task"
	gomock "github.com/golang/mock/gomock"
)

// MockTaskReaderWriter is a mock of TaskReaderWriter interface.
type MockTaskReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockTaskReaderWriterMockRecorder
}

// MockTaskReaderWriterMockRecorder is the mock recorder for MockTaskReaderWriter.
type MockTaskReaderWriterMockRecorder struct {
	mock *MockTaskReaderWriter
}

// NewMockTaskReaderWriter creates a new mock instance.
func NewMockTaskReaderWriter(ctrl *gomock.Controller) *MockTaskReaderWriter {
	mock := &MockTaskReaderWriter{ctrl: ctrl}
	mock.recorder = &MockTaskReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskReaderWriter) EXPECT() *MockTaskReaderWriterMockRecorder {
	return m.recorder
}

// BatchCreateTask mocks base method.
func (m *MockTaskReaderWriter) BatchCreateTask(ctx context.Context, tasks []*task.Task) ([]*task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateTask", ctx, tasks)
	ret0, _ := ret[0].([]*task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateTask indicates an expected call of BatchCreateTask.
func (mr *MockTaskReaderWriterMockRecorder) BatchCreateTask(ctx, tasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateTask", reflect.TypeOf((*MockTaskReaderWriter)(nil).BatchCreateTask), ctx, tasks)
}

// BatchDeleteTaskParamTemplates mocks base method.
func (m *MockTaskReaderWriter) BatchDeleteTaskParamTemplates(ctx context.Context, taskparamTemplateIDs []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteTaskParamTemplates", ctx, taskparamTemplateIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteTaskParamTemplates indicates an expected call of BatchDeleteTaskParamTemplates.
func (mr *MockTaskReaderWriterMockRecorder) BatchDeleteTaskParamTemplates(ctx, taskparamTemplateIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteTaskParamTemplates", reflect.TypeOf((*MockTaskReaderWriter)(nil).BatchDeleteTaskParamTemplates), ctx, taskparamTemplateIDs)
}

// BatchDeleteTasksByIds mocks base method.
func (m *MockTaskReaderWriter) BatchDeleteTasksByIds(ctx context.Context, taskIds []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteTasksByIds", ctx, taskIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteTasksByIds indicates an expected call of BatchDeleteTasksByIds.
func (mr *MockTaskReaderWriterMockRecorder) BatchDeleteTasksByIds(ctx, taskIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteTasksByIds", reflect.TypeOf((*MockTaskReaderWriter)(nil).BatchDeleteTasksByIds), ctx, taskIds)
}

// BatchDeleteTemplateParams mocks base method.
func (m *MockTaskReaderWriter) BatchDeleteTemplateParams(ctx context.Context, paramTemplateId []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteTemplateParams", ctx, paramTemplateId)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteTemplateParams indicates an expected call of BatchDeleteTemplateParams.
func (mr *MockTaskReaderWriterMockRecorder) BatchDeleteTemplateParams(ctx, paramTemplateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteTemplateParams", reflect.TypeOf((*MockTaskReaderWriter)(nil).BatchDeleteTemplateParams), ctx, paramTemplateId)
}

// BatchGetParamDetail mocks base method.
func (m *MockTaskReaderWriter) BatchGetParamDetail(ctx context.Context, templateID int) ([]*task.ParamTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetParamDetail", ctx, templateID)
	ret0, _ := ret[0].([]*task.ParamTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetParamDetail indicates an expected call of BatchGetParamDetail.
func (mr *MockTaskReaderWriterMockRecorder) BatchGetParamDetail(ctx, templateID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetParamDetail", reflect.TypeOf((*MockTaskReaderWriter)(nil).BatchGetParamDetail), ctx, templateID)
}

// BatchGetTaskParam mocks base method.
func (m *MockTaskReaderWriter) BatchGetTaskParam(ctx context.Context, taskParamCfg *task.TaskParamConfig) ([]*task.TaskParamConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTaskParam", ctx, taskParamCfg)
	ret0, _ := ret[0].([]*task.TaskParamConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTaskParam indicates an expected call of BatchGetTaskParam.
func (mr *MockTaskReaderWriterMockRecorder) BatchGetTaskParam(ctx, taskParamCfg interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTaskParam", reflect.TypeOf((*MockTaskReaderWriter)(nil).BatchGetTaskParam), ctx, taskParamCfg)
}

// CreateParamDetail mocks base method.
func (m *MockTaskReaderWriter) CreateParamDetail(ctx context.Context, taskTemp *task.ParamTemplateDetail) (*task.ParamTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateParamDetail", ctx, taskTemp)
	ret0, _ := ret[0].(*task.ParamTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateParamDetail indicates an expected call of CreateParamDetail.
func (mr *MockTaskReaderWriterMockRecorder) CreateParamDetail(ctx, taskTemp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateParamDetail", reflect.TypeOf((*MockTaskReaderWriter)(nil).CreateParamDetail), ctx, taskTemp)
}

// CreateTask mocks base method.
func (m *MockTaskReaderWriter) CreateTask(ctx context.Context, taskInfo *task.Task) (*task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTask", ctx, taskInfo)
	ret0, _ := ret[0].(*task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTask indicates an expected call of CreateTask.
func (mr *MockTaskReaderWriterMockRecorder) CreateTask(ctx, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTask", reflect.TypeOf((*MockTaskReaderWriter)(nil).CreateTask), ctx, taskInfo)
}

// CreateTaskLogDetail mocks base method.
func (m *MockTaskReaderWriter) CreateTaskLogDetail(ctx context.Context, taskLogDetail *task.TaskLogDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTaskLogDetail", ctx, taskLogDetail)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTaskLogDetail indicates an expected call of CreateTaskLogDetail.
func (mr *MockTaskReaderWriterMockRecorder) CreateTaskLogDetail(ctx, taskLogDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTaskLogDetail", reflect.TypeOf((*MockTaskReaderWriter)(nil).CreateTaskLogDetail), ctx, taskLogDetail)
}

// CreateTaskTableConfigs mocks base method.
func (m *MockTaskReaderWriter) CreateTaskTableConfigs(ctx context.Context, askTableConfigs []*task.TaskTableConfig) ([]*task.TaskTableConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTaskTableConfigs", ctx, askTableConfigs)
	ret0, _ := ret[0].([]*task.TaskTableConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTaskTableConfigs indicates an expected call of CreateTaskTableConfigs.
func (mr *MockTaskReaderWriterMockRecorder) CreateTaskTableConfigs(ctx, askTableConfigs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTaskTableConfigs", reflect.TypeOf((*MockTaskReaderWriter)(nil).CreateTaskTableConfigs), ctx, askTableConfigs)
}

// CreateTaskTemplate mocks base method.
func (m *MockTaskReaderWriter) CreateTaskTemplate(ctx context.Context, taskTemp *task.TaskparamTemplateConfig) (*task.TaskparamTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTaskTemplate", ctx, taskTemp)
	ret0, _ := ret[0].(*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTaskTemplate indicates an expected call of CreateTaskTemplate.
func (mr *MockTaskReaderWriterMockRecorder) CreateTaskTemplate(ctx, taskTemp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTaskTemplate", reflect.TypeOf((*MockTaskReaderWriter)(nil).CreateTaskTemplate), ctx, taskTemp)
}

// CreateTemplateParam mocks base method.
func (m *MockTaskReaderWriter) CreateTemplateParam(ctx context.Context, templateParam *task.ParamTemplateDetail) (*task.ParamTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTemplateParam", ctx, templateParam)
	ret0, _ := ret[0].(*task.ParamTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTemplateParam indicates an expected call of CreateTemplateParam.
func (mr *MockTaskReaderWriterMockRecorder) CreateTemplateParam(ctx, templateParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTemplateParam", reflect.TypeOf((*MockTaskReaderWriter)(nil).CreateTemplateParam), ctx, templateParam)
}

// DeleteTaskTableConfigs mocks base method.
func (m *MockTaskReaderWriter) DeleteTaskTableConfigs(ctx context.Context, ids []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTaskTableConfigs", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTaskTableConfigs indicates an expected call of DeleteTaskTableConfigs.
func (mr *MockTaskReaderWriterMockRecorder) DeleteTaskTableConfigs(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTaskTableConfigs", reflect.TypeOf((*MockTaskReaderWriter)(nil).DeleteTaskTableConfigs), ctx, ids)
}

// GetColumnNamesFromTidb mocks base method.
func (m *MockTaskReaderWriter) GetColumnNamesFromTidb(ctx context.Context, db *sql.DB, schemaNames, tableNames []string) ([]*structs.ColumnName, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetColumnNamesFromTidb", ctx, db, schemaNames, tableNames)
	ret0, _ := ret[0].([]*structs.ColumnName)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetColumnNamesFromTidb indicates an expected call of GetColumnNamesFromTidb.
func (mr *MockTaskReaderWriterMockRecorder) GetColumnNamesFromTidb(ctx, db, schemaNames, tableNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetColumnNamesFromTidb", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetColumnNamesFromTidb), ctx, db, schemaNames, tableNames)
}

// GetDefaultTaskParamTemplateByTaskType mocks base method.
func (m *MockTaskReaderWriter) GetDefaultTaskParamTemplateByTaskType(ctx context.Context, taskType int) (*task.TaskparamTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultTaskParamTemplateByTaskType", ctx, taskType)
	ret0, _ := ret[0].(*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultTaskParamTemplateByTaskType indicates an expected call of GetDefaultTaskParamTemplateByTaskType.
func (mr *MockTaskReaderWriterMockRecorder) GetDefaultTaskParamTemplateByTaskType(ctx, taskType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultTaskParamTemplateByTaskType", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetDefaultTaskParamTemplateByTaskType), ctx, taskType)
}

// GetMaxTaskSeqByChannelId mocks base method.
func (m *MockTaskReaderWriter) GetMaxTaskSeqByChannelId(ctx context.Context, channelId int) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxTaskSeqByChannelId", ctx, channelId)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxTaskSeqByChannelId indicates an expected call of GetMaxTaskSeqByChannelId.
func (mr *MockTaskReaderWriterMockRecorder) GetMaxTaskSeqByChannelId(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxTaskSeqByChannelId", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetMaxTaskSeqByChannelId), ctx, channelId)
}

// GetTask mocks base method.
func (m *MockTaskReaderWriter) GetTask(ctx context.Context, taskID int) (*task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTask", ctx, taskID)
	ret0, _ := ret[0].(*task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTask indicates an expected call of GetTask.
func (mr *MockTaskReaderWriterMockRecorder) GetTask(ctx, taskID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTask", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTask), ctx, taskID)
}

// GetTaskParamTemplate mocks base method.
func (m *MockTaskReaderWriter) GetTaskParamTemplate(ctx context.Context, taskparamTemplateID int) (*task.TaskparamTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskParamTemplate", ctx, taskparamTemplateID)
	ret0, _ := ret[0].(*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskParamTemplate indicates an expected call of GetTaskParamTemplate.
func (mr *MockTaskReaderWriterMockRecorder) GetTaskParamTemplate(ctx, taskparamTemplateID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskParamTemplate", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTaskParamTemplate), ctx, taskparamTemplateID)
}

// GetTaskTableConfigsByTaskIdAndChannelId mocks base method.
func (m *MockTaskReaderWriter) GetTaskTableConfigsByTaskIdAndChannelId(ctx context.Context, channelId, taskId int) ([]*task.TaskTableConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskTableConfigsByTaskIdAndChannelId", ctx, channelId, taskId)
	ret0, _ := ret[0].([]*task.TaskTableConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskTableConfigsByTaskIdAndChannelId indicates an expected call of GetTaskTableConfigsByTaskIdAndChannelId.
func (mr *MockTaskReaderWriterMockRecorder) GetTaskTableConfigsByTaskIdAndChannelId(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskTableConfigsByTaskIdAndChannelId", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTaskTableConfigsByTaskIdAndChannelId), ctx, channelId, taskId)
}

// GetTaskTemplate mocks base method.
func (m *MockTaskReaderWriter) GetTaskTemplate(ctx context.Context, templateID int) (*task.TaskparamTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskTemplate", ctx, templateID)
	ret0, _ := ret[0].(*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskTemplate indicates an expected call of GetTaskTemplate.
func (mr *MockTaskReaderWriterMockRecorder) GetTaskTemplate(ctx, templateID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskTemplate", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTaskTemplate), ctx, templateID)
}

// GetTaskparamTemplateListByTaskType mocks base method.
func (m *MockTaskReaderWriter) GetTaskparamTemplateListByTaskType(ctx context.Context, taskType int) ([]*task.TaskparamTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskparamTemplateListByTaskType", ctx, taskType)
	ret0, _ := ret[0].([]*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskparamTemplateListByTaskType indicates an expected call of GetTaskparamTemplateListByTaskType.
func (mr *MockTaskReaderWriterMockRecorder) GetTaskparamTemplateListByTaskType(ctx, taskType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskparamTemplateListByTaskType", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTaskparamTemplateListByTaskType), ctx, taskType)
}

// GetTasks mocks base method.
func (m *MockTaskReaderWriter) GetTasks(ctx context.Context, channelId int, taskIDs []int) ([]*task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTasks", ctx, channelId, taskIDs)
	ret0, _ := ret[0].([]*task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTasks indicates an expected call of GetTasks.
func (mr *MockTaskReaderWriterMockRecorder) GetTasks(ctx, channelId, taskIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTasks", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTasks), ctx, channelId, taskIDs)
}

// GetTasksByChannelId mocks base method.
func (m *MockTaskReaderWriter) GetTasksByChannelId(ctx context.Context, channelId int) ([]*task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTasksByChannelId", ctx, channelId)
	ret0, _ := ret[0].([]*task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTasksByChannelId indicates an expected call of GetTasksByChannelId.
func (mr *MockTaskReaderWriterMockRecorder) GetTasksByChannelId(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTasksByChannelId", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTasksByChannelId), ctx, channelId)
}

// GetTemplateParam mocks base method.
func (m *MockTaskReaderWriter) GetTemplateParam(ctx context.Context, paramTemplateId int) (*task.ParamTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTemplateParam", ctx, paramTemplateId)
	ret0, _ := ret[0].(*task.ParamTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTemplateParam indicates an expected call of GetTemplateParam.
func (mr *MockTaskReaderWriterMockRecorder) GetTemplateParam(ctx, paramTemplateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplateParam", reflect.TypeOf((*MockTaskReaderWriter)(nil).GetTemplateParam), ctx, paramTemplateId)
}

// ListTaskLogDetailByTaskIdAndLogTime mocks base method.
func (m *MockTaskReaderWriter) ListTaskLogDetailByTaskIdAndLogTime(ctx context.Context, taskId int, startTime time.Time) ([]*task.TaskLogDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskLogDetailByTaskIdAndLogTime", ctx, taskId, startTime)
	ret0, _ := ret[0].([]*task.TaskLogDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTaskLogDetailByTaskIdAndLogTime indicates an expected call of ListTaskLogDetailByTaskIdAndLogTime.
func (mr *MockTaskReaderWriterMockRecorder) ListTaskLogDetailByTaskIdAndLogTime(ctx, taskId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskLogDetailByTaskIdAndLogTime", reflect.TypeOf((*MockTaskReaderWriter)(nil).ListTaskLogDetailByTaskIdAndLogTime), ctx, taskId, startTime)
}

// ListTaskParamTemplate mocks base method.
func (m *MockTaskReaderWriter) ListTaskParamTemplate(ctx context.Context, paramTemplateName string, page, pageSize int) ([]*task.TaskparamTemplateConfig, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskParamTemplate", ctx, paramTemplateName, page, pageSize)
	ret0, _ := ret[0].([]*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTaskParamTemplate indicates an expected call of ListTaskParamTemplate.
func (mr *MockTaskReaderWriterMockRecorder) ListTaskParamTemplate(ctx, paramTemplateName, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskParamTemplate", reflect.TypeOf((*MockTaskReaderWriter)(nil).ListTaskParamTemplate), ctx, paramTemplateName, page, pageSize)
}

// ListTemplateParams mocks base method.
func (m *MockTaskReaderWriter) ListTemplateParams(ctx context.Context, taskparamTemplateId int) ([]*task.ParamTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTemplateParams", ctx, taskparamTemplateId)
	ret0, _ := ret[0].([]*task.ParamTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTemplateParams indicates an expected call of ListTemplateParams.
func (mr *MockTaskReaderWriterMockRecorder) ListTemplateParams(ctx, taskparamTemplateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTemplateParams", reflect.TypeOf((*MockTaskReaderWriter)(nil).ListTemplateParams), ctx, taskparamTemplateId)
}

// SaveTaskParamConfigs mocks base method.
func (m *MockTaskReaderWriter) SaveTaskParamConfigs(ctx context.Context, taskParamCfgs []*task.TaskParamConfig) ([]*task.TaskParamConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveTaskParamConfigs", ctx, taskParamCfgs)
	ret0, _ := ret[0].([]*task.TaskParamConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveTaskParamConfigs indicates an expected call of SaveTaskParamConfigs.
func (mr *MockTaskReaderWriterMockRecorder) SaveTaskParamConfigs(ctx, taskParamCfgs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveTaskParamConfigs", reflect.TypeOf((*MockTaskReaderWriter)(nil).SaveTaskParamConfigs), ctx, taskParamCfgs)
}

// UpdateTask mocks base method.
func (m *MockTaskReaderWriter) UpdateTask(ctx context.Context, taskInfo *task.Task) (*task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTask", ctx, taskInfo)
	ret0, _ := ret[0].(*task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTask indicates an expected call of UpdateTask.
func (mr *MockTaskReaderWriterMockRecorder) UpdateTask(ctx, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTask", reflect.TypeOf((*MockTaskReaderWriter)(nil).UpdateTask), ctx, taskInfo)
}

// UpdateTaskByTaskID mocks base method.
func (m *MockTaskReaderWriter) UpdateTaskByTaskID(ctx context.Context, taskId int, taskInfo task.Task) (task.Task, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskByTaskID", ctx, taskId, taskInfo)
	ret0, _ := ret[0].(task.Task)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskByTaskID indicates an expected call of UpdateTaskByTaskID.
func (mr *MockTaskReaderWriterMockRecorder) UpdateTaskByTaskID(ctx, taskId, taskInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskByTaskID", reflect.TypeOf((*MockTaskReaderWriter)(nil).UpdateTaskByTaskID), ctx, taskId, taskInfo)
}

// UpdateTaskParamTemplate mocks base method.
func (m *MockTaskReaderWriter) UpdateTaskParamTemplate(ctx context.Context, taskParamTemplateConfig *task.TaskparamTemplateConfig) (*task.TaskparamTemplateConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskParamTemplate", ctx, taskParamTemplateConfig)
	ret0, _ := ret[0].(*task.TaskparamTemplateConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskParamTemplate indicates an expected call of UpdateTaskParamTemplate.
func (mr *MockTaskReaderWriterMockRecorder) UpdateTaskParamTemplate(ctx, taskParamTemplateConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskParamTemplate", reflect.TypeOf((*MockTaskReaderWriter)(nil).UpdateTaskParamTemplate), ctx, taskParamTemplateConfig)
}

// UpdateTaskTableConfigs mocks base method.
func (m *MockTaskReaderWriter) UpdateTaskTableConfigs(ctx context.Context, askTableConfigs []*task.TaskTableConfig) ([]*task.TaskTableConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskTableConfigs", ctx, askTableConfigs)
	ret0, _ := ret[0].([]*task.TaskTableConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskTableConfigs indicates an expected call of UpdateTaskTableConfigs.
func (mr *MockTaskReaderWriterMockRecorder) UpdateTaskTableConfigs(ctx, askTableConfigs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskTableConfigs", reflect.TypeOf((*MockTaskReaderWriter)(nil).UpdateTaskTableConfigs), ctx, askTableConfigs)
}

// UpdateTemplateParam mocks base method.
func (m *MockTaskReaderWriter) UpdateTemplateParam(ctx context.Context, templateParam *task.ParamTemplateDetail) (*task.ParamTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTemplateParam", ctx, templateParam)
	ret0, _ := ret[0].(*task.ParamTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTemplateParam indicates an expected call of UpdateTemplateParam.
func (mr *MockTaskReaderWriterMockRecorder) UpdateTemplateParam(ctx, templateParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTemplateParam", reflect.TypeOf((*MockTaskReaderWriter)(nil).UpdateTemplateParam), ctx, templateParam)
}
