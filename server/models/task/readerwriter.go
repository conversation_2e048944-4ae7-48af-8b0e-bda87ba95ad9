package task

import (
	"context"
	"database/sql"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
)

type TaskReaderWriter interface {
	GetTask(ctx context.Context, taskID int) (*Task, error)
	GetTasks(ctx context.Context, channelId int, taskIDs []int) ([]*Task, error)
	CheckSubTaskIdByTaskIdsAndStatus(ctx context.Context, channelId, parentTaskId int, taskIds, statusList []int) ([]int, error)
	GetSubTaskIdsByChannelIdParentTaskIdAndStatus(ctx context.Context, channelId, parentTaskId int, statusList []int) ([]int, error)
	GetSubTaskNumByChannelIdTaskId(ctx context.Context, channelId, parentTaskId int) (int, error)
	CreateTask(ctx context.Context, taskInfo *Task) (*Task, error)
	UpdateTask(ctx context.Context, taskInfo *Task) (*Task, error)
	UpdateTaskStatusAndMessage(ctx context.Context, taskId int, taskStatus int, errorDetail string) error
	UpdateTaskStatusAndRunParam(ctx context.Context, taskId, taskStatus int, runParam string) error
	UpdateTaskCfg(ctx context.Context, taskInfo *Task) (*Task, error)
	BatchUpdateTaskStatus(ctx context.Context, excludeTaskTypes []int) error
	BatchGetRunningTasks(ctx context.Context, excludeTaskTypes []int) ([]*Task, error)
	UpdateTaskByTaskID(ctx context.Context, taskId int, taskInfo Task) (Task, error)
	BatchCreateTask(ctx context.Context, tasks []*Task) ([]*Task, error)
	GetTasksByChannelAndTaskParentId(ctx context.Context, channelId, parentTaskId int) ([]*Task, error)
	ListTasksByChannelParentTaskId(ctx context.Context, page, pageSize int, channelId, parentTaskId int, status int, serverId string) ([]*Task, int64, error)
	SetTaskScnNumber(ctx context.Context, taskId int, scnNumber uint64) error
	ResetTaskScnNumber(ctx context.Context, taskId int) error

	SummarySubTasksByChannelParentTaskId(ctx context.Context, channelId, parentTaskId int) (*structs.SubTaskSummary, error)
	DeleteSubTasksByChannelParentTaskId(ctx context.Context, channelId, parentTaskId int) error
	GetSubTaskIds(ctx context.Context, channelId, parentTaskId int) ([]int, error)
	BatchGetSubTaskIdsByParentTaskId(ctx context.Context, channelId int, parentTaskIds []int) ([]int, error)
	BatchGetSubTasksByParentTaskId(ctx context.Context, channelId int, parentTaskId int) ([]*Task, error)
	GetMaxTaskSeqByChannelId(ctx context.Context, channelId int) (int, error)
	BatchDeleteTasksByIds(ctx context.Context, taskIds []int) error
	BatchUpdateTaskStatusByIds(ctx context.Context, taskIds []int, status int) error
	BatchClearTaskStartEndTimeByIds(ctx context.Context, taskIds []int) error
	ResetTask(ctx context.Context, taskId int) error

	BatchCreateQueuedTask(ctx context.Context, queueTasks []*QueuedTask) error
	CreateQueuedTask(ctx context.Context, queueTask *QueuedTask) (*QueuedTask, error)
	GetTopQueuedTask(ctx context.Context) (*QueuedTask, error)
	GetServerTopQueuedTask(ctx context.Context, serverId string) (*QueuedTask, error)
	CountQueuedTask(ctx context.Context) (int64, error)
	DeleteTopQueuedTask(ctx context.Context, id int) error

	GetTaskTemplate(ctx context.Context, templateID int) (*TaskparamTemplateConfig, error)
	GetTaskparamTemplateListByTaskType(ctx context.Context, taskType int) ([]*TaskparamTemplateConfig, error)
	CreateTaskTemplate(ctx context.Context, taskTemp *TaskparamTemplateConfig) (*TaskparamTemplateConfig, error)
	BatchGetParamDetail(ctx context.Context, templateID int) ([]*ParamTemplateDetail, error)
	GetParamDetail(ctx context.Context, templateID int, paramName string) (*ParamTemplateDetail, error)
	CreateParamDetail(ctx context.Context, taskTemp *ParamTemplateDetail) (*ParamTemplateDetail, error)
	BatchGetTaskParam(ctx context.Context, taskParamCfg *TaskParamConfig) ([]*TaskParamConfig, error)
	SaveTaskParamConfigs(ctx context.Context, taskParamCfgs []*TaskParamConfig) ([]*TaskParamConfig, error)

	GetTaskParamTemplate(ctx context.Context, taskparamTemplateID int) (*TaskparamTemplateConfig, error)
	ListTaskParamTemplate(ctx context.Context, paramTemplateName string, page int, pageSize int) ([]*TaskparamTemplateConfig, int64, error)
	UpdateTaskParamTemplate(ctx context.Context, taskParamTemplateConfig *TaskparamTemplateConfig) (*TaskparamTemplateConfig, error)
	BatchDeleteTaskParamTemplates(ctx context.Context, taskparamTemplateIDs []int) error
	GetDefaultTaskParamTemplateByTaskType(ctx context.Context, taskType int) (*TaskparamTemplateConfig, error)

	CreateTaskTableConfigs(ctx context.Context, askTableConfigs []*TaskTableConfig) ([]*TaskTableConfig, error)
	UpdateTaskTableConfigs(ctx context.Context, askTableConfigs []*TaskTableConfig) ([]*TaskTableConfig, error)
	ToUpperCaseTableNameT(ctx context.Context, channelId int, taskId int) error
	ToLowerCaseTableNameT(ctx context.Context, channelId int, taskId int) error
	ToUpperCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error
	ToLowerCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error
	DeleteTaskTableConfigs(ctx context.Context, ids []int) error
	GetTaskTableConfigsByTaskIdAndChannelId(ctx context.Context, channelId, taskId int) ([]*TaskTableConfig, error)
	GetColumnNamesFromTidb(ctx context.Context, db *sql.DB, schemaNames, tableNames []string) ([]*structs.ColumnName, error)
	GetColumnNamesFromOracle(ctx context.Context, db *sql.DB, schemaNames, tableNames []string) ([]*structs.ColumnName, error)
	GetOracleSchemaTableColumn(ctx context.Context, db *sql.DB, schemaName string, tableName string, oraCollation bool) ([]map[string]string, error)

	CreateTemplateParam(ctx context.Context, templateParam *ParamTemplateDetail) (*ParamTemplateDetail, error)
	UpdateTemplateParam(ctx context.Context, templateParam *ParamTemplateDetail) (*ParamTemplateDetail, error)
	GetTemplateParam(ctx context.Context, paramTemplateId int) (*ParamTemplateDetail, error)
	BatchGetTemplateParam(ctx context.Context, paramTemplateIds []int) ([]*ParamTemplateDetail, error)
	BatchDeleteTemplateParams(ctx context.Context, paramTemplateId []int) error
	ListTemplateParams(ctx context.Context, taskparamTemplateId int) ([]*ParamTemplateDetail, error)

	CreateTaskLogDetail(ctx context.Context, taskLogDetail *TaskLogDetail) error
	BatchCreateTaskLogDetail(ctx context.Context, taskLogDetails []*TaskLogDetail) error
	ListTaskLogDetail(ctx context.Context, taskId int, startTime time.Time) ([]*TaskLogDetail, error)
	DeleteTaskLogs(ctx context.Context, taskId int) error
	ListTaskLogDetailByTaskIdAndLogTime(ctx context.Context, taskId int, startTime time.Time) ([]*TaskLogDetail, error)

	BatchGetTaskSubParamConfigs(ctx context.Context, channelId, taskId int) ([]*TaskSubParamConfig, error)
	BatchSaveTaskSubParamConfigs(ctx context.Context, taskSubParamConfigs []*TaskSubParamConfig) ([]*TaskSubParamConfig, error)
	BatchDeleteTaskSubParamConfigsByTaskIds(ctx context.Context, taskIds []int) error
	BatchDeleteTaskSubParamConfigs(ctx context.Context, taskId int, paramName string) error
	GetTasksByChannelId(ctx context.Context, channelId int) ([]*Task, error)

	GetOneTaskByChannelId(ctx context.Context, channelId int) (*Task, error)
}
