package task

import (
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type Task struct {
	TaskID                 int       `gorm:"primarykey"`
	TaskType               int       `gorm:"comment:task type: 1 object assessment, 2 struct migrate, 3 full data migrate, 4 data compare, 5 sql assessment and performance evaluation"`
	TaskName               string    `gorm:"type:varchar(100);comment:task name"`
	StartTime              time.Time `gorm:"comment:start time"`
	EndTime                time.Time `gorm:"comment:end time"`
	TaskStatus             int       `gorm:"type:varchar(100);default:2;comment:task status,1 not setup,2 not running,3 running,4 finish,5 failed"`
	ServerId               string    `gorm:"type:varchar(100);comment:server id"`
	TaskSeq                int       `gorm:"comment:task sequence"`
	RunParams              string    `gorm:"type:varchar(1000);comment:run params"`
	ChannelId              int       `gorm:"index;comment:channel id"`
	TaskParamTmplateId     int       `gorm:"comment:task param template id"`
	TabcolmapTmplateId     int       `gorm:"comment:table column map template id"`
	ColdefaultmapTmplateId int       `gorm:"comment:table column default map template id"`
	ObjmapTmplateId        int       `gorm:"comment:object map template id"`
	SqlmapTmplateId        int       `gorm:"comment:sql map template id"`
	IncrementId            int       `gorm:"comment:increment id"`
	ErrorDetail            string    `gorm:"type:text;comment:task failed error detail"`
	TaskObjRef             string    `gorm:"type:varchar(100);comment:task object reference type, eg:channel,tasks"`
	TaskReftask            int       `gorm:"type:int;comment:reference task id"`
	TaskWarning            string    `gorm:"type:varchar(100);"`
	OnlyIncompatibleDetail int       `gorm:"type:int;comment:0-show all detail; 1-only show incompatible detail"`
	ParentTaskID           int       `gorm:"type:int;comment:parent task id,0=not child task;default:0"`
	Progress               float64   `gorm:"type:decimal(5,2);"`
	ScnNumber              string    `gorm:"type:varchar(100);comment:scn number"`
	*common.Entity
}

type QueuedTask struct {
	Id        int `gorm:"primarykey"`
	TaskId    int
	ChannelId int
	ServerId  string    `gorm:"index:idx_server_id;type:varchar(100)"`
	CreatedAt time.Time `gorm:"<-:create"`
}

type ServerInfo struct {
	ServerId string `gorm:"primaryKey;comment:server id"`
	HostIp   string `gorm:"type:varchar(15);comment:server ip"`
	HostPort int    `gorm:"comment:server port"`
	*common.Entity
}

type TaskparamTemplateConfig struct {
	TaskparamTemplateID int    `gorm:"primarykey;comment:primary key"`
	ParamTemplateName   string `gorm:"type:varchar(100);comment:param template name"`
	DefaultTag          string `gorm:"index:idx_tag_type;type:char(1);comment:default tag"`
	TaskType            int    `gorm:"index:idx_tag_type;comment:task type: 1 object assessment, 2 struct migrate, 3 full data migrate, 4 data compare, 5 sql assessment and performance evaluation"`
	*common.Entity
}

type ParamTemplateDetail struct {
	TemplateID          int    `gorm:"primarykey;comment:primary key"`
	TaskparamTemplateID int    `gorm:"uniqueIndex:uqi_temp_id_name;comment:task param template id"`
	ParamName           string `gorm:"uniqueIndex:uqi_temp_id_name;type:varchar(100);comment:params name"`
	ParamValueDefault   string `gorm:"type:varchar(500);comment:params default value"`
	HasSubParams        bool   `gorm:"type:tinyint;comment:has sub params"`

	*common.Entity

	// deprecated 废弃字段
	TaskSubType int `gorm:"type:int;default 1;comment:1:normal,2:csv"`
	TaskOther   int `gorm:"type:int;default 0;comment:backup_column"`
}

type TaskParamConfig struct {
	TaskParamID         int    `gorm:"primarykey;comment:primary key"`
	ChannelID           int    `gorm:"index:idx_channel_task_tmp;comment:channel id"`
	TaskID              int    `gorm:"index:idx_channel_task_tmp;comment:task id"`
	ParamName           string `gorm:"type:varchar(100);comment:params name"`
	ParamValueCurrent   string `gorm:"type:varchar(500);comment:params current value"`
	ParamValueDefault   string `gorm:"type:varchar(500);comment:params default value"`
	TaskparamTemplateID int    `gorm:"index:idx_channel_task_tmp;comment:task param template id"`
	HasSubParams        bool   `gorm:"type:tinyint;comment:has sub params"`

	*common.Entity

	// deprecated 废弃字段
	TaskSubType int `gorm:"type:int;default 1;comment:1:normal,2:csv"`
	TaskOther   int `gorm:"type:int;default 0;comment:backup_column"`
}

type TaskSubParamConfig struct {
	TaskSubParamID      int    `gorm:"primarykey;comment:primary key"`
	TaskparamTemplateID int    `gorm:"comment:task param template id"`
	ChannelID           int    `gorm:"comment:channel id"`
	TaskID              int    `gorm:"comment:task id"`
	ParamName           string `gorm:"type:varchar(100);comment:param name"`
	ParamValue          string `gorm:"type:varchar(500);comment:param value"`

	*common.Entity
}

type TaskTableConfig struct {
	TaskTableId        int    `gorm:"primarykey;comment:primary key"`
	ChannelId          int    `gorm:"index:idx_channel_task_tmp;comment:channel id"`
	TaskId             int    `gorm:"index:idx_channel_task_tmp;comment:task id"`
	DbNameS            string `gorm:"type:varchar(200);comment:source database name"`
	SchemaNameS        string `gorm:"type:varchar(200);comment:source schema name"`
	TableNameS         string `gorm:"type:varchar(200);comment:source table name"`
	DbNameT            string `gorm:"type:varchar(200);comment:target database name"`
	SchemaNameT        string `gorm:"type:varchar(200);comment:target schema name"`
	TableNameT         string `gorm:"type:varchar(200);comment:target table name"`
	OperatorTag        string `gorm:"type:varchar(1);comment:include or ignore,Y/N"`
	ColumnslistTidb    string `gorm:"type:varchar(1000);comment:column list in tidb"`
	ColumnslistOracle  string `gorm:"type:varchar(1000);comment:column list in oracle"`
	FilterClauseTidb   string `gorm:"type:varchar(1000);comment:filter clause in tidb"`
	FilterClauseOracle string `gorm:"type:varchar(1000);comment:filter clause in oracle"`
	EnableChunkSplit   string `gorm:"type:varchar(1);comment:enable data chunk split,Y/N"`
	SqlhintTidb        string `gorm:"type:varchar(1000);comment:sql hint in tidb"`
	SqlhintOracle      string `gorm:"type:varchar(1000);comment:sql hint in oracle"`
	ColmapTidb         string `gorm:"type:varchar(1000);comment:column map in tidb"`
	ColmapOracle       string `gorm:"type:varchar(1000);comment:column map in oracle"`
	IndexFieldsTidb    string `gorm:"type:varchar(1000);comment:column list in pk/uk/index in tidb"`
	IndexFieldsOracle  string `gorm:"type:varchar(1000);comment:column list in pk/uk/index in oracle"`
	TablePartition     string `gorm:"type:varchar(1000);comment:table partition"`
	*common.Entity
}

type TaskLogDetail struct {
	TaskLogId  int       `gorm:"type:int;primarykey;comment:primary key"`
	ChannelId  int       `gorm:"type:int;"`
	TaskId     int       `gorm:"type:int;index:idx_taskid;"`
	LogGroup   string    `gorm:"type:varchar(100);"`
	LogTime    time.Time `gorm:"type:datetime;index:idx_logtime;"`
	LogLevel   string    `gorm:"type:varchar(10);"`
	LogMessage string    `gorm:"type:text;"`
	*common.Entity
}
