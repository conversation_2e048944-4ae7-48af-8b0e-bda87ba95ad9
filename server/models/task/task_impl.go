package task

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringUtil "gitee.com/pingcap_enterprise/tms/util/string"
	"gorm.io/gorm"
)

type TaskReadWrite struct {
	dbCommon.GormDB
}

func NewTaskReadWrite(db *gorm.DB) *TaskReadWrite {
	return &TaskReadWrite{GormDB: dbCommon.WrapDB(db)}
}

func (rw *TaskReadWrite) GetTask(ctx context.Context, taskID int) (*Task, error) {
	tempConfig := &Task{}
	err := rw.DB(ctx).First(tempConfig, "task_id = ?", taskID).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query task id %d not found", taskID)
	} else if err != nil {
		log.Errorf("get task info failed. task id is %d, err:%v", taskID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil

}

func (rw *TaskReadWrite) GetTasks(ctx context.Context, channelId int, taskIDs []int) ([]*Task, error) {
	tasks := make([]*Task, 0)
	err := rw.DB(ctx).Model(&Task{}).Where("channel_id = ? AND task_id in ?", channelId, taskIDs).Find(&tasks).Error
	if err != nil {
		log.Errorf("get tasks by taskIds failed. taskIds is %v, err:%s", taskIDs, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw *TaskReadWrite) CreateTask(ctx context.Context, task *Task) (*Task, error) {
	err := rw.DB(ctx).Create(task).Error
	if err != nil {
		log.Errorf("create task info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return task, nil
}

func (rw *TaskReadWrite) BatchCreateTask(ctx context.Context, tasks []*Task) ([]*Task, error) {
	err := rw.DB(ctx).Create(tasks).Error
	if err != nil {
		log.Errorf("batch create task infos to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw *TaskReadWrite) GetTasksByChannelId(ctx context.Context, channelId int) ([]*Task, error) {
	tasks := make([]*Task, 0)
	err := rw.DB(ctx).Model(&Task{}).Where("channel_id = ?", channelId).Order("task_type asc").Order("start_time asc").Find(&tasks).Error
	if err != nil {
		log.Errorf("get tasks by channelId failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw *TaskReadWrite) UpdateTask(ctx context.Context, task *Task) (*Task, error) {
	err := rw.DB(ctx).Save(task).Error
	if err != nil {
		log.Errorf("update task info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update task info to db successfully, taskId:%d, taskStatus: %v", task.TaskID, task.TaskStatus)
	return task, nil
}

func (rw *TaskReadWrite) UpdateTaskCfg(ctx context.Context, task *Task) (*Task, error) {
	updates := map[string]interface{}{
		"task_name":                task.TaskName,
		"task_param_tmplate_id":    task.TaskParamTmplateId,
		"tabcolmap_tmplate_id":     task.TabcolmapTmplateId,
		"coldefaultmap_tmplate_id": task.ColdefaultmapTmplateId,
		"objmap_tmplate_id":        task.ObjmapTmplateId,
		"sqlmap_tmplate_id":        task.SqlmapTmplateId,
		"only_incompatible_detail": task.OnlyIncompatibleDetail,
	}
	err := rw.DB(ctx).Model(&Task{}).Where("task_id = ? ", task.TaskID).Updates(updates).Error
	if err != nil {
		log.Errorf("update task cfg to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update task info to db successfully, taskId:%d, taskStatus: %v", task.TaskID, task.TaskStatus)
	return task, nil
}

func (rw *TaskReadWrite) BatchUpdateTaskStatus(ctx context.Context, excludeTaskTypes []int) error {
	err := rw.DB(ctx).Model(&Task{}).
		Where("task_status = ? ", 3).
		Where("task_type not in (?)", excludeTaskTypes).
		Update("task_status", constants.TASK_STATUS_FAILED).Error
	if err != nil {
		log.Errorf("update task info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Infof("batch update task info to db successfully, ")
	return nil
}

func (rw *TaskReadWrite) BatchGetRunningTasks(ctx context.Context, excludeTaskTypes []int) ([]*Task, error) {
	tasks := make([]*Task, 0)
	err := rw.DB(ctx).Model(&Task{}).Where("task_status = ? ", 3).Where("task_type not in (?)", excludeTaskTypes).Order("task_id asc").Find(&tasks).Error
	if err != nil {
		log.Errorf("BatchGetRunningTasks, err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw *TaskReadWrite) UpdateTaskByTaskID(ctx context.Context, taskId int, task Task) (Task, error) {
	err := rw.DB(ctx).Model(&Task{}).Where("task_id = ? ", taskId).Updates(task).Error
	if err != nil {
		log.Errorf("update task status to db failed. %s", err)
		return task, dbCommon.WrapDBError(err)
	}
	log.Infof("update task status to db successfully, taskId: %v, taskStatus: %v", taskId, task.TaskStatus)
	return task, nil
}

func (rw *TaskReadWrite) GetTaskTemplate(ctx context.Context, templateID int) (*TaskparamTemplateConfig, error) {
	tempConfig := &TaskparamTemplateConfig{}
	err := rw.DB(ctx).First(tempConfig, "taskparam_template_id = ?", templateID).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query template id %d not found", templateID)
	} else if err != nil {
		log.Errorf("get task param template config info failed. templateID is %d, err:%v", templateID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) GetTaskparamTemplateListByTaskType(ctx context.Context, taskType int) ([]*TaskparamTemplateConfig, error) {
	list := make([]*TaskparamTemplateConfig, 0, 0)
	err := rw.DB(ctx).Where("task_type = ?", taskType).Find(&list).Error
	if err != nil {
		log.Errorf("get task param template config list by taskType failed. taskType is %d, err:%v", taskType, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return list, nil
}

func (rw *TaskReadWrite) CreateTaskTemplate(ctx context.Context, taskTemp *TaskparamTemplateConfig) (*TaskparamTemplateConfig, error) {
	err := rw.DB(ctx).Create(taskTemp).Error
	if err != nil {
		log.Errorf("create task param template config info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskTemp, nil
}

func (rw *TaskReadWrite) BatchGetParamDetail(ctx context.Context, templateID int) ([]*ParamTemplateDetail, error) {
	var tempConfig []*ParamTemplateDetail
	err := rw.DB(ctx).Where("taskparam_template_id = ?", templateID).Order("param_name asc").Find(&tempConfig).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query template id %d not found", templateID)
	} else if err != nil {
		log.Errorf("get task param template detail info failed. templateID is %d, err:%v", templateID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) GetParamDetail(ctx context.Context, templateID int, paramName string) (*ParamTemplateDetail, error) {
	tempConfig := &ParamTemplateDetail{}
	err := rw.DB(ctx).First(tempConfig, "taskparam_template_id = ? and param_name = ?", templateID, paramName).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query template id %d not found", templateID)
	} else if err != nil {
		log.Errorf("get task param template detail info failed. templateID is %d, err:%v", templateID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) CreateParamDetail(ctx context.Context, taskTemp *ParamTemplateDetail) (*ParamTemplateDetail, error) {
	err := rw.DB(ctx).Create(taskTemp).Error
	if err != nil {
		log.Errorf("create param template detail info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskTemp, nil
}

func (rw *TaskReadWrite) BatchGetTaskParam(ctx context.Context, taskParamCfg *TaskParamConfig) ([]*TaskParamConfig, error) {
	var tempConfig []*TaskParamConfig
	err := rw.DB(ctx).Where("channel_id = ? AND task_id = ? AND taskparam_template_id = ?",
		taskParamCfg.ChannelID, taskParamCfg.TaskID, taskParamCfg.TaskparamTemplateID).Order("param_name asc").Find(&tempConfig).Error
	if err != nil {
		log.Errorf("get task param  info failed. task param config is %v, err:%v", taskParamCfg.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) SaveTaskParamConfigs(ctx context.Context, taskParamCfgs []*TaskParamConfig) ([]*TaskParamConfig, error) {
	err := rw.DB(ctx).Save(taskParamCfgs).Error
	if err != nil {
		log.Errorf("save task param configs to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskParamCfgs, nil
}

func (rw *TaskParamConfig) String() string {
	marshal, _ := json.Marshal(rw)
	return string(marshal)
}

func (rw *TaskReadWrite) CreateTaskTableConfigs(ctx context.Context, taskTableConfigs []*TaskTableConfig) ([]*TaskTableConfig, error) {
	err := rw.DB(ctx).Create(taskTableConfigs).Error
	if err != nil {
		log.Errorf("create task table configs to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskTableConfigs, nil
}

func (rw *TaskReadWrite) UpdateTaskTableConfigs(ctx context.Context, taskTableConfigs []*TaskTableConfig) ([]*TaskTableConfig, error) {
	err := rw.GormDB.DB(ctx).Save(taskTableConfigs).Error
	if err != nil {
		log.Errorf("update task table configs to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update task table configs to db successfully, taskTableConfigs:%v", taskTableConfigs)
	return taskTableConfigs, nil
}

func (rw *TaskReadWrite) ToUpperCaseTableNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&TaskTableConfig{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("table_name_t", gorm.Expr("upper(table_name_t)")).Error
	if err != nil {
		log.Errorf("TaskTableConfig.ToUpperCaseTableNameT failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) ToLowerCaseTableNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&TaskTableConfig{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("table_name_t", gorm.Expr("lower(table_name_t)")).Error
	if err != nil {
		log.Errorf("TaskTableConfig.ToLowerCaseTableNameT failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) ToUpperCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&TaskTableConfig{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("schema_name_t", gorm.Expr("upper(schema_name_t)")).Error
	if err != nil {
		log.Errorf("TaskTableConfig.ToUpperCaseSchemaNameT failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) ToLowerCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&TaskTableConfig{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("schema_name_t", gorm.Expr("lower(schema_name_t)")).Error
	if err != nil {
		log.Errorf("TaskTableConfig.ToLowerCaseSchemaNameT failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) DeleteTaskTableConfigs(ctx context.Context, ids []int) error {
	err := rw.DB(ctx).Delete(&TaskTableConfig{}, ids).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) GetTaskTableConfigsByTaskIdAndChannelId(ctx context.Context, channelId, taskId int) ([]*TaskTableConfig, error) {
	var taskTableConfigs []*TaskTableConfig
	err := rw.DB(ctx).Where("channel_id = ? AND task_id = ?", channelId, taskId).Order("db_name_s asc,schema_name_s asc, table_name_s asc").Find(&taskTableConfigs).Error
	if err != nil {
		log.Errorf("get task table config list by channelId and taskId failed. channelId:%d, taskId:%d, err:%v", channelId, taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskTableConfigs, nil
}

func (rw *TaskReadWrite) GetColumnNamesFromTidb(ctx context.Context, db *sql.DB, schemaNames, tableNames []string) ([]*structs.ColumnName, error) {
	query := fmt.Sprintf("select TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME from INFORMATION_SCHEMA.COLUMNS where TABLE_SCHEMA in (%s) and TABLE_NAME in (%s) order by TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME",
		stringUtil.GenCommaSeparatedString(schemaNames), stringUtil.GenCommaSeparatedString(tableNames))
	results := make([]*structs.ColumnName, 0, 0)
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	for rows.Next() {
		var schema, table, column sql.NullString
		err = rows.Scan(&schema, &table, &column)
		if err != nil {
			log.Errorf("get column names from tidb failed. err:%v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		results = append(results, &structs.ColumnName{SchemaName: schema.String, TableName: table.String, ColumnName: column.String})
	}
	return results, nil
}

func (rw *TaskReadWrite) GetColumnNamesFromOracle(ctx context.Context, db *sql.DB, schemaNames, tableNames []string) ([]*structs.ColumnName, error) {
	query := fmt.Sprintf("SELECT OWNER, TABLE_NAME, COLUMN_NAME, DATA_TYPE FROM all_tab_columns WHERE  owner IN(%s) AND table_name in (%s) order by COLUMN_ID",
		stringUtil.GenCommaSeparatedString(schemaNames), stringUtil.GenCommaSeparatedString(tableNames))
	results := make([]*structs.ColumnName, 0, 0)
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	for rows.Next() {
		var schema, table, column, dataType sql.NullString
		err = rows.Scan(&schema, &table, &column, &dataType)
		if err != nil {
			log.Errorf("get column names from tidb failed. err:%v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		results = append(results, &structs.ColumnName{SchemaName: schema.String, TableName: table.String, ColumnName: column.String, DataType: dataType.String})
	}
	return results, nil
}

func (rw *TaskReadWrite) GetOracleSchemaTableColumn(ctx context.Context, db *sql.DB, schemaName string, tableName string, oraCollation bool) ([]map[string]string, error) {
	var querySQL string

	/*
			1、dataPrecision 精度范围 ORA-01727: numeric precision specifier is out of range (1 to 38)
			2、dataScale 精度范围 ORA-01728: numeric scale specifier is out of range (-84 to 127)
			3、oracle number 类型，desc tableName 表结构查看
			- number(*,10) -> number(38,10)
			- number(*,0) -> number(38,0)
			- number(*) -> number
			- number -> number
		    - number(x,y) -> number(x,y)
			4、SQL 查询处理
			- number(*,10) -> number(38,10)
			- number(*,0) -> number(38,0)
			- number(*) -> number(38,127)
			- number -> number(38,127)
			- number(x,y) -> number(x,y)
	*/
	if oraCollation {
		querySQL = fmt.Sprintf(`SELECT 
	t.COLUMN_NAME,
	    t.DATA_TYPE,
		 t.CHAR_LENGTH,
		 NVL(t.CHAR_USED, 'UNKNOWN') CHAR_USED,
	    NVL(t.DATA_LENGTH, 0) AS DATA_LENGTH,
	    DECODE(NVL(TO_CHAR(t.DATA_PRECISION), '*'), '*', '38', TO_CHAR(t.DATA_PRECISION)) AS DATA_PRECISION,
	    DECODE(NVL(TO_CHAR(t.DATA_SCALE), '*'), '*', '127', TO_CHAR(t.DATA_SCALE)) AS DATA_SCALE,
		t.NULLABLE,
	    NVL(s.DATA_DEFAULT, 'NULLSTRING') DATA_DEFAULT,
		DECODE(t.COLLATION, 'USING_NLS_COMP',(SELECT VALUE FROM NLS_DATABASE_PARAMETERS WHERE PARAMETER = 'NLS_COMP'), t.COLLATION) COLLATION,
	    c.COMMENTS
FROM
	dba_tab_columns t,
	dba_col_comments c,
	(
	SELECT xs.OWNER,xs.TABLE_NAME,xs.COLUMN_NAME,xs.DATA_DEFAULT FROM
	XMLTABLE (
		'/ROWSET/ROW' PASSING (
	SELECT
		DBMS_XMLGEN.GETXMLTYPE (
				q'[SELECT d.OWNER,d.TABLE_NAME,d.COLUMN_NAME,d.DATA_DEFAULT FROM DBA_TAB_COLUMNS d WHERE upper(d.owner) = upper('%s') AND upper(d.table_name) = upper('%s')]')
	FROM
		DUAL ) COLUMNS OWNER VARCHAR2 (300) PATH 'OWNER', TABLE_NAME VARCHAR2(300) PATH 'TABLE_NAME', COLUMN_NAME VARCHAR2(300) PATH 'COLUMN_NAME', DATA_DEFAULT VARCHAR2(4000) PATH 'DATA_DEFAULT') xs
		) s
WHERE
	t.table_name = c.table_name
	AND t.column_name = c.column_name
	AND t.owner = c.owner
	AND c.owner = s.owner
	AND c.table_name = s.table_name
	AND c.column_name = s.column_name
	AND upper(t.owner) = upper('%s')
	AND upper(t.table_name) = upper('%s')
ORDER BY
	t.COLUMN_ID`, schemaName, tableName, schemaName, tableName)
	} else {
		querySQL = fmt.Sprintf(`SELECT 
	t.COLUMN_NAME,
	    t.DATA_TYPE,
		 t.CHAR_LENGTH,
		 NVL(t.CHAR_USED, 'UNKNOWN') CHAR_USED,
	    NVL(t.DATA_LENGTH, 0) AS DATA_LENGTH,
	    DECODE(NVL(TO_CHAR(t.DATA_PRECISION), '*'), '*', '38', TO_CHAR(t.DATA_PRECISION)) AS DATA_PRECISION,
	    DECODE(NVL(TO_CHAR(t.DATA_SCALE), '*'), '*', '127', TO_CHAR(t.DATA_SCALE)) AS DATA_SCALE,
		t.NULLABLE,
	    NVL(s.DATA_DEFAULT, 'NULLSTRING') DATA_DEFAULT,
	    c.COMMENTS
FROM
	dba_tab_columns t,
	dba_col_comments c,
	(
	SELECT xs.OWNER,xs.TABLE_NAME,xs.COLUMN_NAME,xs.DATA_DEFAULT FROM
	XMLTABLE (
		'/ROWSET/ROW' PASSING (
	SELECT
		DBMS_XMLGEN.GETXMLTYPE (
				q'[SELECT d.OWNER,d.TABLE_NAME,d.COLUMN_NAME,d.DATA_DEFAULT FROM DBA_TAB_COLUMNS d WHERE upper(d.owner) = upper('%s') AND upper(d.table_name) = upper('%s')]')
	FROM
		DUAL ) COLUMNS OWNER VARCHAR2 (300) PATH 'OWNER', TABLE_NAME VARCHAR2(300) PATH 'TABLE_NAME', COLUMN_NAME VARCHAR2(300) PATH 'COLUMN_NAME', DATA_DEFAULT VARCHAR2(4000) PATH 'DATA_DEFAULT') xs
		) s
WHERE
	t.table_name = c.table_name
	AND t.column_name = c.column_name
	AND t.owner = c.owner
	AND c.owner = s.owner
	AND c.table_name = s.table_name
	AND c.column_name = s.column_name
	AND upper(t.owner) = upper('%s')
	AND upper(t.table_name) = upper('%s')
ORDER BY
	t.COLUMN_ID`, schemaName, tableName, schemaName, tableName)
	}

	_, queryRes, err := Query(ctx, db, querySQL)
	if err != nil {
		return queryRes, err
	}
	if len(queryRes) == 0 {
		return queryRes, fmt.Errorf("oracle table [%s.%s] column info cann't be null", schemaName, tableName)
	}

	// check constraints notnull
	// search_condition long datatype
	_, condRes, err := Query(ctx, db, fmt.Sprintf(`SELECT
				col.COLUMN_NAME,
				cons.SEARCH_CONDITION
				FROM
				DBA_CONS_COLUMNS col,
				DBA_CONSTRAINTS cons
				WHERE
				col.OWNER = cons.OWNER
				AND col.TABLE_NAME = cons.TABLE_NAME
				AND col.CONSTRAINT_NAME = cons.CONSTRAINT_NAME
				AND cons.CONSTRAINT_TYPE = 'C'
				AND upper(col.OWNER) = '%s'
				AND upper(col.TABLE_NAME) = '%s'`, strings.ToUpper(schemaName), strings.ToUpper(tableName)))
	if err != nil {
		return queryRes, err
	}

	if len(condRes) == 0 {
		return queryRes, nil
	}

	rep, err := regexp.Compile(`(^.*)(?i:IS NOT NULL)`)
	if err != nil {
		return queryRes, fmt.Errorf("check notnull constraint regexp complile failed: %v", err)
	}
	for _, r := range queryRes {
		for _, c := range condRes {
			if r["COLUMN_NAME"] == c["COLUMN_NAME"] && r["NULLABLE"] == "Y" {
				// 检查约束非空检查
				if rep.MatchString(c["SEARCH_CONDITION"]) {
					r["NULLABLE"] = "N"
				}
			}
		}
	}
	return queryRes, nil
}

func Query(ctx context.Context, db *sql.DB, querySQL string) ([]string, []map[string]string, error) {
	var (
		cols []string
		res  []map[string]string
	)
	rows, err := db.QueryContext(ctx, querySQL)
	if err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query failed: [%v]", querySQL, err.Error())
	}
	defer rows.Close()

	//不确定字段通用查询，自动获取字段名称
	cols, err = rows.Columns()
	if err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query rows.Columns failed: [%v]", querySQL, err.Error())
	}

	values := make([][]byte, len(cols))
	scans := make([]interface{}, len(cols))
	for i := range values {
		scans[i] = &values[i]
	}

	for rows.Next() {
		err = rows.Scan(scans...)
		if err != nil {
			return cols, res, fmt.Errorf("general sql [%v] query rows.Scan failed: [%v]", querySQL, err.Error())
		}

		row := make(map[string]string)
		for k, v := range values {
			// Oracle/Mysql 对于 'NULL' 统一字符 NULL 处理，查询出来转成 NULL,所以需要判断处理
			// 查询字段值 NULL
			// 如果字段值 = NULLABLE 则表示值是 NULL
			// 如果字段值 = "" 则表示值是空字符串
			// 如果字段值 = 'NULL' 则表示值是 NULL 字符串
			// 如果字段值 = 'null' 则表示值是 null 字符串
			if v == nil {
				row[cols[k]] = "NULLABLE"
			} else {
				// 处理空字符串以及其他值情况
				// 数据统一 string 格式显示
				row[cols[k]] = string(v)
			}
		}
		res = append(res, row)
	}

	if err = rows.Err(); err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query rows.Next failed: [%v]", querySQL, err.Error())
	}
	return cols, res, nil
}

func (rw *TaskReadWrite) ListTaskParamTemplate(ctx context.Context, paramTemplateName string, page int, pageSize int) ([]*TaskparamTemplateConfig, int64, error) {
	records := make([]*TaskparamTemplateConfig, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TaskparamTemplateConfig{})
	if strings.TrimSpace(paramTemplateName) != "" {
		query = query.Where("param_template_name like ?", "%"+paramTemplateName+"%")
	}
	err := query.Order("taskparam_template_id asc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("page query task param templates failed. page:%d, pageSize:%d, paramTemplateName:%s", page, pageSize, paramTemplateName)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *TaskReadWrite) GetTaskParamTemplate(ctx context.Context, taskparamTemplateID int) (*TaskparamTemplateConfig, error) {
	template := &TaskparamTemplateConfig{}
	err := rw.DB(ctx).First(template, taskparamTemplateID).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query template %d not found", taskparamTemplateID)
	} else if err != nil {
		log.Errorf("get template failed. templateId is %d, err:%v", taskparamTemplateID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return template, nil
}

func (rw *TaskReadWrite) UpdateTaskParamTemplate(ctx context.Context, taskParamTemplateConfig *TaskparamTemplateConfig) (*TaskparamTemplateConfig, error) {
	err := rw.GormDB.DB(ctx).Save(taskParamTemplateConfig).Error
	if err != nil {
		log.Errorf("update task param template to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update task param template to db successfully, task param Template config id :%d", taskParamTemplateConfig.TaskparamTemplateID)
	return taskParamTemplateConfig, nil
}

func (rw *TaskReadWrite) BatchDeleteTaskParamTemplates(ctx context.Context, taskParamTemplateIDs []int) error {
	err := rw.DB(ctx).Delete(&TaskparamTemplateConfig{}, taskParamTemplateIDs).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) GetDefaultTaskParamTemplateByTaskType(ctx context.Context, taskType int) (*TaskparamTemplateConfig, error) {
	tempConfig := &TaskparamTemplateConfig{}
	err := rw.DB(ctx).First(tempConfig, "task_type = ? and default_tag = 'Y'", taskType).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query default task param template config by taskType %d not found", taskType)
	} else if err != nil {
		log.Errorf("query default task param template config by taskType %d error.err:%v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) GetMaxTaskSeqByChannelId(ctx context.Context, channelId int) (int, error) {
	var max sql.NullInt64
	err := rw.DB(ctx).Model(&Task{}).Select("MAX(task_seq)").Where("channel_id = ?", channelId).First(&max).Error
	if err != nil {
		log.Errorf("get max task seq  by channelId failed. channelId:%d error.err:%v", channelId, err)
		return 0, dbCommon.WrapDBError(err)
	}
	if max.Valid {
		return int(max.Int64), nil
	}
	return 0, nil
}

func (rw *TaskReadWrite) BatchDeleteTasksByIds(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Delete(&Task{}, taskIds).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) CreateTemplateParam(ctx context.Context, templateParam *ParamTemplateDetail) (*ParamTemplateDetail, error) {
	err := rw.DB(ctx).Create(templateParam).Error
	if err != nil {
		log.Errorf("create template param to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create template param to db successfully, %v", templateParam)
	return templateParam, nil
}

func (rw *TaskReadWrite) UpdateTemplateParam(ctx context.Context, templateParam *ParamTemplateDetail) (*ParamTemplateDetail, error) {
	err := rw.DB(ctx).Save(templateParam).Error
	if err != nil {
		log.Errorf("update template param to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update template param to db successfully, %v", templateParam)
	return templateParam, nil
}

func (rw *TaskReadWrite) GetTemplateParam(ctx context.Context, paramTemplateId int) (*ParamTemplateDetail, error) {
	paramTemplate := &ParamTemplateDetail{}
	err := rw.DB(ctx).First(paramTemplate, paramTemplateId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query paramTemplateId %d not found", paramTemplateId)
	} else if err != nil {
		log.Errorf("get template param failed. templateId is %d, err:%v", paramTemplateId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return paramTemplate, nil
}

func (rw *TaskReadWrite) BatchDeleteTemplateParams(ctx context.Context, paramTemplateId []int) error {
	err := rw.DB(ctx).Unscoped().Delete(&ParamTemplateDetail{}, paramTemplateId).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) ListTemplateParams(ctx context.Context, taskparamTemplateId int) ([]*ParamTemplateDetail, error) {
	records := make([]*ParamTemplateDetail, 0)
	err := rw.DB(ctx).Model(&ParamTemplateDetail{}).Where("taskparam_template_id=?", taskparamTemplateId).Order("template_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query templates param details failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TaskReadWrite) CreateTaskLogDetail(ctx context.Context, taskLogDetail *TaskLogDetail) error {
	err := rw.DB(ctx).Create(taskLogDetail).Error
	if err != nil {
		log.Errorf("create taskLogDetail info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) DeleteSubTasksByChannelParentTaskId(ctx context.Context, channelId, parentTaskId int) error {
	err := rw.DB(ctx).Where("channel_id = ?", channelId).Where("parent_task_id = ?", parentTaskId).Delete(&Task{}).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) CheckSubTaskIdByTaskIdsAndStatus(ctx context.Context, channelId, parentTaskId int, taskIds, statusList []int) ([]int, error) {
	recordIds := make([]int, 0)
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("task_id in (?)", taskIds).
		Where("parent_task_id = ?", parentTaskId).
		Where("task_status in (?)", statusList).
		Order("task_id asc").
		Pluck("task_id", &recordIds).Error
	if err != nil {
		log.Errorf("query queueable task ids failed. channelId:%d, task ids:%v, task_status:%v, err:%s", channelId, taskIds, statusList, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return recordIds, nil
}

func (rw *TaskReadWrite) GetSubTaskIdsByChannelIdParentTaskIdAndStatus(ctx context.Context, channelId, parentTaskId int, statusList []int) ([]int, error) {
	recordIds := make([]int, 0)
	query := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id = ?", parentTaskId)
	if len(statusList) > 0 {
		query = query.Where("task_status in (?)", statusList)
	}
	err := query.Order("task_id asc").Pluck("task_id", &recordIds).Error
	if err != nil {
		log.Errorf("query task ids by status failed. channelId:%d, status:%v, err:%s", channelId, statusList, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return recordIds, nil
}

func (rw *TaskReadWrite) GetSubTaskNumByChannelIdTaskId(ctx context.Context, channelId, parentTaskId int) (int, error) {
	var count int64
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id = ?", parentTaskId).
		Count(&count).Error
	if err != nil {
		log.Errorf("query task num failed. channelId:%d, err:%s", channelId, err)
		return 0, dbCommon.WrapDBError(err)
	}
	return int(count), nil
}

func (rw *TaskReadWrite) BatchUpdateTaskStatusByIds(ctx context.Context, taskIds []int, status int) error {
	err := rw.DB(ctx).Model(&Task{}).
		Where("task_id in (?)", taskIds).
		Update("task_status", status).Error
	if err != nil {
		log.Errorf("batch update task status to queueable failed. taskIds:%v, err:%s", taskIds, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) BatchCreateQueuedTask(ctx context.Context, queueTasks []*QueuedTask) error {
	err := rw.DB(ctx).Create(queueTasks).Error
	if err != nil {
		log.Errorf("batch create queued task failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) GetSubTaskIds(ctx context.Context, channelId, parentTaskId int) ([]int, error) {
	taskIds := make([]int, 0)
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id = ?", parentTaskId).
		Pluck("task_id", &taskIds).Error
	if err != nil {
		log.Errorf("get sub task ids failed. channelId:%d, parentTaskId:%d, err:%s", channelId, parentTaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskIds, nil
}

func (rw *TaskReadWrite) BatchGetSubTaskIdsByParentTaskId(ctx context.Context, channelId int, parentTaskIds []int) ([]int, error) {
	taskIds := make([]int, 0)
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id in (?)", parentTaskIds).
		Pluck("task_id", &taskIds).Error
	if err != nil {
		log.Errorf("get sub task ids failed. channelId:%d, parentTaskIds:%v, err:%s", channelId, parentTaskIds, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskIds, nil
}

func (rw *TaskReadWrite) BatchGetSubTasksByParentTaskId(ctx context.Context, channelId int, parentTaskId int) ([]*Task, error) {
	subTasks := make([]*Task, 0)
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id = ?", parentTaskId).
		Find(&subTasks).Error
	if err != nil {
		log.Errorf("get sub task ids failed. channelId:%d, parentTaskId:%d, err:%s", channelId, parentTaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return subTasks, nil
}

func (rw *TaskReadWrite) SummarySubTasksByChannelParentTaskId(ctx context.Context, channelId, parentTaskId int) (*structs.SubTaskSummary, error) {
	rsp := &structs.SubTaskSummary{}

	querySQL := `
SELECT 
    COUNT(*) AS total_num,
    COUNT(if(task_status=1,true,null)) AS not_setup_num,
    COUNT(if(task_status=2,true,null)) AS not_running_num,
    COUNT(if(task_status=3,true,null)) AS running_num,
    COUNT(if(task_status=4,true,null)) AS finish_num,
    COUNT(if(task_status=5,true,null)) AS failed_num,
    COUNT(if(task_status=6,true,null)) AS queued_num,
    COUNT(if(task_status=7,true,null)) AS stop_num
FROM tasks WHERE parent_task_id=? AND channel_id=?;`

	err := rw.DB(ctx).Raw(querySQL, parentTaskId, channelId).Scan(rsp).Error
	return rsp, err
}

func (rw *TaskReadWrite) BatchClearTaskStartEndTimeByIds(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Model(&Task{}).
		Where("task_id in (?)", taskIds).
		Updates(map[string]interface{}{"start_time": timeutil.GetTMSNullTime(), "end_time": timeutil.GetTMSNullTime()}).Error
	if err != nil {
		log.Errorf("batch clear task start and end time failed. taskIds:%v, err:%s", taskIds, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) ListTaskLogDetailByTaskIdAndLogTime(ctx context.Context, taskId int, startTime time.Time) ([]*TaskLogDetail, error) {
	records := make([]*TaskLogDetail, 0)
	err := rw.DB(ctx).Model(&TaskLogDetail{}).Where("task_id=? and log_time>=?", taskId, startTime).Find(&records).Error
	if err != nil {
		log.Errorf("page query task log details failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TaskReadWrite) ResetTask(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Model(&Task{}).
		Where("task_id = ?", taskId).
		Updates(map[string]interface{}{
			"task_status": constants.TASK_STATUS_NOT_RUNNING,
			"start_time":  timeutil.GetTMSNullTime(),
			"end_time":    timeutil.GetTMSNullTime(),
			"server_id":   "",
			"run_params":  "",
		}).Error
	if err != nil {
		log.Errorf("reset task failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) GetTasksByChannelAndTaskParentId(ctx context.Context, channelId, parentTaskId int) ([]*Task, error) {
	tasks := make([]*Task, 0)
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id = ?", parentTaskId).
		Order("field(task_type, 1,2,3,7,4,5,6,7,8,9)").Order("task_id asc").Find(&tasks).Error
	if err != nil {
		log.Errorf("get tasks by channelId failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw *TaskReadWrite) ListTasksByChannelParentTaskId(ctx context.Context, page, pageSize int, channelId, parentTaskId int, status int, serverId string) ([]*Task, int64, error) {
	tasks := make([]*Task, pageSize)
	count := int64(0)
	query := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).
		Where("parent_task_id = ?", parentTaskId)
	if status != 0 {
		query = query.Where("task_status = ?", status)
	}
	if serverId != "" {
		query = query.Where("server_id = ?", serverId)
	}

	query = query.Order("task_id asc").
		Count(&count).
		Limit(pageSize).Offset((page - 1) * pageSize)

	err := query.Find(&tasks).Error
	if err != nil {
		log.Errorf("list tasks by channelId / parentTaskId failed. channelId is %d, parentTaskId is %d, err:%s", channelId, parentTaskId, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return tasks, count, nil
}

func (rw *TaskReadWrite) CreateQueuedTask(ctx context.Context, queueTask *QueuedTask) (*QueuedTask, error) {
	err := rw.DB(ctx).Create(queueTask).Error
	if err != nil {
		log.Errorf("create queue task info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return queueTask, nil
}

func (rw *TaskReadWrite) GetTopQueuedTask(ctx context.Context) (*QueuedTask, error) {
	queuedTask := &QueuedTask{}
	err := rw.DB(ctx).Order("id asc").Limit(1).Find(queuedTask).Error
	if err != nil {
		return nil, err
	}
	return queuedTask, nil
}

func (rw *TaskReadWrite) GetServerTopQueuedTask(ctx context.Context, serverId string) (*QueuedTask, error) {
	queuedTask := &QueuedTask{}
	err := rw.DB(ctx).Where("server_id = '' or server_id = ?", serverId).Order("id asc").Limit(1).Find(queuedTask).Error
	if err != nil {
		return nil, err
	}
	return queuedTask, nil
}

func (rw *TaskReadWrite) CountQueuedTask(ctx context.Context) (int64, error) {
	var count int64
	err := rw.DB(ctx).Model(&QueuedTask{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (rw *TaskReadWrite) DeleteTopQueuedTask(ctx context.Context, id int) error {
	err := rw.DB(ctx).Delete(&QueuedTask{}, id).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) ListTaskLogDetail(ctx context.Context, taskId int, startTime time.Time) ([]*TaskLogDetail, error) {
	records := make([]*TaskLogDetail, 0)
	err := rw.DB(ctx).Model(&TaskLogDetail{}).Where("task_id=? and log_time>=?", taskId, startTime).Find(&records).Error
	if err != nil {
		log.Errorf("page query task log details failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TaskReadWrite) SetTaskScnNumber(ctx context.Context, taskId int, scnNumber uint64) error {
	scnNumberStr := fmt.Sprintf("%d", scnNumber)
	err := rw.DB(ctx).Model(&Task{}).Where("task_id = ?", taskId).Update("scn_number", scnNumberStr).Error
	if err != nil {
		log.Errorf("set task scn number failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) ResetTaskScnNumber(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Model(&Task{}).Where("task_id = ?", taskId).Update("scn_number", "").Error
	if err != nil {
		log.Errorf("clear task scn number failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) BatchCreateTaskLogDetail(ctx context.Context, taskLogDetails []*TaskLogDetail) error {
	err := rw.DB(ctx).Create(taskLogDetails).Error
	if err != nil {
		log.Errorf("create task log detail info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) BatchGetTaskSubParamConfigs(ctx context.Context, channelId, taskId int) ([]*TaskSubParamConfig, error) {
	var tempConfig []*TaskSubParamConfig
	err := rw.DB(ctx).Where("channel_id = ? AND task_id = ?", channelId, taskId).Find(&tempConfig).Error
	if err != nil {
		log.Errorf("get task sub param  info failed. channelId:%d, taskId:%d, err:%s", channelId, taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) BatchSaveTaskSubParamConfigs(ctx context.Context, taskSubParamConfigs []*TaskSubParamConfig) ([]*TaskSubParamConfig, error) {
	err := rw.DB(ctx).Save(taskSubParamConfigs).Error
	if err != nil {
		log.Errorf("save task sub param configs to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskSubParamConfigs, nil
}

func (rw *TaskReadWrite) BatchDeleteTaskSubParamConfigs(ctx context.Context, taskId int, paramName string) error {
	err := rw.DB(ctx).Where("task_id = ? AND param_name = ?", taskId, paramName).Unscoped().Delete(&TaskSubParamConfig{}).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) BatchDeleteTaskSubParamConfigsByTaskIds(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Where("task_id in (?)", taskIds).Unscoped().Delete(&TaskSubParamConfig{}).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TaskReadWrite) BatchGetTemplateParam(ctx context.Context, paramTemplateIds []int) ([]*ParamTemplateDetail, error) {
	var tempConfig []*ParamTemplateDetail
	err := rw.DB(ctx).Where("taskparam_template_id in (?)", paramTemplateIds).Find(&tempConfig).Error
	if err != nil {
		log.Errorf("get task param template detail info failed. templateID is %v, err:%v", paramTemplateIds, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tempConfig, nil
}

func (rw *TaskReadWrite) UpdateTaskStatusAndRunParam(ctx context.Context, taskId, taskStatus int, runParam string) error {
	err := rw.DB(ctx).Exec("update tasks set run_params = ?, task_status = ? where task_id = ?", runParam, taskStatus, taskId).Error
	if err != nil {
		log.Errorf("update task run param failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) UpdateTaskStatusAndMessage(ctx context.Context, taskId int, status int, errorDetail string) error {
	err := rw.DB(ctx).Exec("update tasks set end_time = ?, task_status = ?, error_detail = ? where task_id = ?", time.Now(), status, errorDetail, taskId).Error
	if err != nil {
		log.Errorf("update task status failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) DeleteTaskLogs(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Where("task_id = ?", taskId).Unscoped().Delete(&TaskLogDetail{}).Error
	if err != nil {
		log.Errorf("delete task logs failed. taskId:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *TaskReadWrite) GetOneTaskByChannelId(ctx context.Context, channelId int) (*Task, error) {
	var task Task
	err := rw.DB(ctx).Model(&Task{}).
		Where("channel_id = ?", channelId).Order("task_id asc").First(&task).Error
	if err != nil {
		log.Errorf("get task by channelId failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return &task, nil
}
