package channel

import (
	"context"
)

type ReaderWriter interface {
	CreateChannel(ctx context.Context, channelInfo *ChannelInformation) (*ChannelInformation, error)

	UpdateChannel(ctx context.Context, channelInfo *ChannelInformation) (*ChannelInformation, error)

	UndeleteChannel(ctx context.Context, channel *ChannelInformation) (*ChannelInformation, error)

	GetChannel(ctx context.Context, channelId int) (*ChannelInformation, error)

	DeleteChannel(ctx context.Context, channelId int) error

	ListChannels(ctx context.Context, page int, pageSize int, channelName string, channelType string) ([]*ChannelInformation, int64, error)

	CreateChannelSchemaObjects(ctx context.Context, channelObjects []*ChannelSchemaObject) ([]*ChannelSchemaObject, error)

	CreateChannelSchemaObject(ctx context.Context, channelObject *ChannelSchemaObject) (*ChannelSchemaObject, error)

	SaveChannelSchemaObject(ctx context.Context, channelObject *ChannelSchemaObject) (*ChannelSchemaObject, error)

	GetChannelSchemaObjectByTaskId(ctx context.Context, taskId int) (*ChannelSchemaObject, error)

	CreateChannelSchemas(ctx context.Context, channelSchemas []*ChannelSchema) ([]*ChannelSchema, error)

	UpdateChannelSchemas(ctx context.Context, channelSchemas []*ChannelSchema) ([]*ChannelSchema, error)

	DeleteChannelSchemasByChannelId(ctx context.Context, channelId int) error

	GetChannelSchemas(ctx context.Context, channelId int) ([]*ChannelSchema, error)

	SaveChannelSchemaTables(ctx context.Context, tables []*ChannelSchemaTable) ([]*ChannelSchemaTable, error)

	SaveChannelSchemaTable(ctx context.Context, table *ChannelSchemaTable) (*ChannelSchemaTable, error)

	GetConflictTables(ctx context.Context, taskId int, channelId int, taskType int) ([]*ChannelSchemaTable, error)

	UpdateTargetChannelSchemaNameByPks(ctx context.Context, channelSchtableIds []int, schemaNameT string) error

	BatchDeleteChannelSchemaTables(ctx context.Context, channelSchtableIds []int) error

	GetChannelSchemasByTaskId(ctx context.Context, taskId int) ([]string, error)

	GetChannelSchemaTablesByChannelId(ctx context.Context, channelId int) ([]*ChannelSchemaTable, error)
	GetChannelSchemaTablesByTaskId(ctx context.Context, taskId int) ([]*ChannelSchemaTable, error)

	GetChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, taskType int, channelId int) ([]*ChannelSchemaTable, error)

	GetUnselectedChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, taskType int, channelId int) ([]*ChannelSchemaTable, error)

	GetChannelSchemaTablesByTaskIdAndSourceTableNames(ctx context.Context, taskId int, tableNames []string) ([]*ChannelSchemaTable, error)

	GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, channelId int, taskType int, schemaNames []string, tableNamePrefix string) ([]*ChannelSchemaTable, error)

	GetUnSelectedChannelSchemaTablesByPage(ctx context.Context, channelId int, taskType int, schemaNames []string, tableNamePrefix string, page, pageSize int) ([]*ChannelSchemaTable, int64, error)

	GetSelectedChannelSchemaTablesByTaskIdAndChannelId(ctx context.Context, channelId int, taskId int, schemaNames []string, tableNamePrefix string, clusterTypeT, partitioningTypeS, partitioningTypeT, orderKeys []string, pks, uks, pkt string, page, pageSize int) ([]*ChannelSchemaTable, int64, error)

	UnSelectChannelSchemaTables(ctx context.Context, ids []int) error

	SelectChannelSchemaTables(ctx context.Context, ids []int, taskId int) error

	GetChannelSchemaTablesByPks(ctx context.Context, channelSchtableIds []int) ([]*ChannelSchemaTable, error)

	GetTablesByTableNameAndSourceSchemas(ctx context.Context, channelId, taskId int, tableName string, schemas []string) ([]*ChannelSchemaTable, error)

	SavePreChecks(ctx context.Context, preChecks []*PrecheckInfo) ([]*PrecheckInfo, error)

	GetChannelPreCheckInfosByChannelId(ctx context.Context, channelId int) ([]*PrecheckInfo, error)

	UpdatePreCheckInfo(ctx context.Context, preCheck *PrecheckInfo) (*PrecheckInfo, error)

	GetPreCheckById(ctx context.Context, precheckId int) (*PrecheckInfo, error)

	GetCheckingPreCheckByChannelId(ctx context.Context, channelId int) ([]*PrecheckInfo, error)

	GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx context.Context, channelId, taskId int) ([]string, error)

	GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS(ctx context.Context, channelId, taskId int, schemaS string) (string, error)

	GetChannelSchemaTablesByChannelTaskSchemaS(ctx context.Context, channelId, taskId int, schemaS string) ([]*ChannelSchemaTable, error)
	GetChannelSchemaTableByChannelTaskSchemaSTableS(ctx context.Context, channelId, taskId int, schemaS, tableS string) (*ChannelSchemaTable, error)

	GetChannelSchemaTablesCountsByTask(ctx context.Context, channelId, taskId int) (int64, error)
	GroupChannelSchemaTablesCountsByTask(ctx context.Context, channelId, taskId int) (map[string]int64, error)
	GetChannelTaskIdsByTaskIdAndTableNamePrefix(ctx context.Context, channelId int, tableNamePredix string) ([]int, error)

	AddAllChannelSchemaTables(ctx context.Context, channelId int, taskId int, taskType int) error
	DeleteAllChannelSchemaTables(ctx context.Context, channelId int, taskId int) error
	PhysicalDeleteAllChannelSchemaTables(ctx context.Context, channelId int, taskId int) error
	BatchDeleteChannelSchemas(ctx context.Context, channelSchemaIds []int) error
	DeleteAllChannelSchemaTablesByTaskIds(ctx context.Context, channelId int, taskIds []int) error
	DeleteChannelSchemaTablesByTaskTypeAndChannelIdAndSchemaTableName(ctx context.Context, taskType int, channelId int, schemaName, tableName string) error

	ToUpperCaseChannelSchemaTables(ctx context.Context, channelId int, taskId int) error
	ToLowerCaseChannelSchemaTables(ctx context.Context, channelId int, taskId int) error
	ToUpperCaseTableNameT(ctx context.Context, channelId int, taskId int) error
	ToLowerCaseTableNameT(ctx context.Context, channelId int, taskId int) error
	ToUpperCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error
	ToLowerCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error
	ModifyChannelSchemaTable(ctx context.Context, channelId int, taskId int, modifyTag string, tableNameS []string) error

	ChangePartitioningTypeT(ctx context.Context, channelId int, taskId int, channelSchtableIds []int, partitioningTypeT string) error
	ChangeClusterTypeT(ctx context.Context, channelId int, taskId int, channelSchtableIds []int, clusterTypeT string) error
	CheckChannelSchemaTableByPartitioningTypeT(ctx context.Context, channelId, taskId int, channelSchtableIds []int, partitioningTypeT string) ([]*ChannelSchemaTable, error)
	CheckChannelSchemaTableByCluster(ctx context.Context, channelId, taskId int, channelSchtableIds []int, clusterTypeT string) ([]*ChannelSchemaTable, error)

	UnSelectChannelSchemaTablesByTaskId(ctx context.Context, taskIds []int) error

	SaveTableColumnCustomSummary(ctx context.Context, s *TableColumnCustomSummary) (*TableColumnCustomSummary, error)
	GetTableColumnCustomSummary(ctx context.Context, channelId int) (*TableColumnCustomSummary, error)
	RemoveTableColumnCustomSummary(ctx context.Context, channelId int) (int64, error)

	CountTableColumnCustomMapRule(ctx context.Context, taskId int) (int64, error)
	SaveTableColumnCustomMapRule(ctx context.Context, rules []*TableColumnCustomMapRule) ([]*TableColumnCustomMapRule, int64, error)
	RemoveTableColumnCustomMapRule(ctx context.Context, query *TableColumnCustomMapRule) (int64, error)
	QueryTableColumnCustomMapRuleByChannelTaskAndArgs(ctx context.Context, channelId, taskId int, page, pageSize int, condition string, args []any) ([]*TableColumnCustomMapRule, int64, error)
	QueryTableColumnCustomMapRuleByChannel(ctx context.Context, channelId int) ([]*TableColumnCustomMapRule, error)
	QueryTableColumnCustomMapRuleByChannelAndDigests(ctx context.Context, channelId int, digests []string) ([]*TableColumnCustomMapRule, error)
	CreateChannelDatasource(ctx context.Context, datasource *ChannelDatasource) (*ChannelDatasource, error)

	GetChannelDatasourcesByChannelId(ctx context.Context, channelId int) ([]*ChannelDatasource, error)
	DeleteChannelDatasourceByChannelIdAndDatasourceId(ctx context.Context, channelId int, datasourceId int) (int64, error)
}
