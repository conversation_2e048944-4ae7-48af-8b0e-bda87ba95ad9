package channel

import (
	"context"
	goerrors "errors"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/errors"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type ChannelReadWrite struct {
	dbCommon.GormDB
}

func NewChannelReadWrite(db *gorm.DB) *ChannelReadWrite {
	m := &ChannelReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw ChannelReadWrite) CreateChannel(ctx context.Context, channel *ChannelInformation) (*ChannelInformation, error) {
	err := rw.DB(ctx).Create(channel).Error
	if err != nil {
		log.Errorf("create channel info to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channel, nil
}

func (rw ChannelReadWrite) UpdateChannel(ctx context.Context, channel *ChannelInformation) (*ChannelInformation, error) {
	err := rw.DB(ctx).Save(channel).Error
	if err != nil {
		log.Errorf("update channel info to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channel, nil
}

func (rw ChannelReadWrite) UndeleteChannel(ctx context.Context, channel *ChannelInformation) (*ChannelInformation, error) {
	channel.DeletedAt = gorm.DeletedAt{}
	//err := rw.DB(ctx).Model(&channel).Update("deleted_at", nil).Error
	err := rw.DB(ctx).Unscoped().Save(channel).Error
	if err != nil {
		log.Errorf("undelete channel info to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channel, nil
}

func (rw ChannelReadWrite) GetChannel(ctx context.Context, channelId int) (*ChannelInformation, error) {
	channel := &ChannelInformation{}
	err := rw.DB(ctx).Unscoped().Where("channel_id = ?", channelId).Find(channel).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query channel id %d not found", channelId)
	} else if err != nil {
		log.Errorf("get channel info failed. channelId is %d, %v", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channel, nil
}

func (rw ChannelReadWrite) DeleteChannel(ctx context.Context, channelId int) error {
	err := rw.DB(ctx).Delete(&ChannelInformation{}, channelId).Error
	return dbCommon.WrapDBError(err)
}

func (rw ChannelReadWrite) ListChannels(ctx context.Context, page int, pageSize int, channelName string, channelType string) ([]*ChannelInformation, int64, error) {
	// 定义查询结果结构体
	type ChannelWithCharset struct {
		ChannelInformation
		DatasourceCharsetS string `gorm:"column:datasource_charset_s"`
		DatasourceCharsetT string `gorm:"column:datasource_charset_t"`
	}

	// 构建基础查询SQL
	baseSQL := `
		SELECT 
			c.*,
			ds.charset as datasource_charset_s,
			dt.charset as datasource_charset_t
		FROM channel_informations c
		LEFT JOIN datasources ds ON c.datasource_id_s = ds.datasource_id
		LEFT JOIN datasources dt ON c.datasource_id_t = dt.datasource_id
		WHERE c.deleted_at IS NULL
	`

	// 添加过滤条件
	args := []interface{}{}
	if strings.TrimSpace(channelName) != "" {
		baseSQL += " AND c.channel_name LIKE ?"
		args = append(args, "%"+channelName+"%")
	}
	if strings.TrimSpace(channelType) != "" {
		baseSQL += " AND c.channel_type = ?"
		args = append(args, channelType)
	}

	// 获取总数
	countSQL := "SELECT COUNT(*) FROM (" + baseSQL + ") as count_table"
	var count int64
	err := rw.DB(ctx).Raw(countSQL, args...).Scan(&count).Error
	if err != nil {
		log.Errorf("count query channels failed. page is %d, pageSize is %d, channelName:%s, channelType:%s", page, pageSize, channelName, channelType)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	// 添加排序和分页
	baseSQL += " ORDER BY c.created_at DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, (page-1)*pageSize)

	// 执行查询
	var records []ChannelWithCharset
	err = rw.DB(ctx).Raw(baseSQL, args...).Scan(&records).Error
	if err != nil {
		log.Errorf("page query channels failed. page is %d, pageSize is %d, channelName:%s, channelType:%s", page, pageSize, channelName, channelType)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	// 转换为 ChannelInformation 结构体
	result := make([]*ChannelInformation, len(records))
	for i, record := range records {
		channel := &ChannelInformation{
			ChannelId:            record.ChannelId,
			ChannelName:          record.ChannelName,
			ChannelType:          record.ChannelType,
			DatasourceIdS:        record.DatasourceIdS,
			DatasourceNameS:      record.DatasourceNameS,
			DatasourceIdT:        record.DatasourceIdT,
			DatasourceNameT:      record.DatasourceNameT,
			DatasourceCharsetS:   record.DatasourceCharsetS,
			DatasourceCharsetT:   record.DatasourceCharsetT,
			ObjAssessment:        record.ObjAssessment,
			ObjParser:            record.ObjParser,
			SqlAssessment:        record.SqlAssessment,
			Increment:            record.Increment,
			MigrateStructure:     record.MigrateStructure,
			MigrateFullData:      record.MigrateFullData,
			MigrateCsvData:       record.MigrateCsvData,
			DataCompare:          record.DataCompare,
			TaskCreated:          record.TaskCreated,
			ChannelMode:          record.ChannelMode,
			SQLAnalyzerEnvStatus: record.SQLAnalyzerEnvStatus,
			DataCompareEnvStatus: record.DataCompareEnvStatus,
			Entity:               record.Entity,
		}
		result[i] = channel
	}

	return result, count, nil
}

func (rw ChannelReadWrite) CreateChannelSchemaObjects(ctx context.Context, channelObjects []*ChannelSchemaObject) ([]*ChannelSchemaObject, error) {
	err := rw.DB(ctx).Create(channelObjects).Error
	if err != nil {
		log.Errorf("create channel objects to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channelObjects, nil
}

func (rw ChannelReadWrite) CreateChannelSchemaObject(ctx context.Context, channelObject *ChannelSchemaObject) (*ChannelSchemaObject, error) {
	err := rw.DB(ctx).Create(channelObject).Error
	if err != nil {
		log.Errorf("create channel object to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channelObject, nil
}

func (rw ChannelReadWrite) SaveChannelSchemaObject(ctx context.Context, channelObject *ChannelSchemaObject) (*ChannelSchemaObject, error) {
	err := rw.DB(ctx).Save(channelObject).Error
	if err != nil {
		log.Errorf("update channel schema object to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channelObject, nil
}

func (rw ChannelReadWrite) GetChannelSchemaObjectByTaskId(ctx context.Context, taskId int) (*ChannelSchemaObject, error) {
	cso := &ChannelSchemaObject{}
	err := rw.DB(ctx).First(cso, "task_id = ?", taskId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	} else if err != nil {
		log.Errorf("get channel schema object failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return cso, nil
}

func (rw ChannelReadWrite) CreateChannelSchemas(ctx context.Context, channelSchemas []*ChannelSchema) ([]*ChannelSchema, error) {
	err := rw.DB(ctx).Create(channelSchemas).Error
	if err != nil {
		log.Errorf("create channel schemas to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channelSchemas, nil
}

func (rw ChannelReadWrite) UpdateChannelSchemas(ctx context.Context, channelSchemas []*ChannelSchema) ([]*ChannelSchema, error) {
	err := rw.DB(ctx).Save(channelSchemas).Error
	if err != nil {
		log.Errorf("update channel schemas to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channelSchemas, nil
}

func (rw ChannelReadWrite) DeleteChannelSchemasByChannelId(ctx context.Context, channelId int) error {
	err := rw.DB(ctx).Delete(&ChannelSchema{}, "channel_id = ?", channelId).Error
	if err != nil {
		log.Errorf("delete channel schemas failed by channel_id %d. err:%s", channelId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) BatchDeleteChannelSchemas(ctx context.Context, channelSchemaIds []int) error {
	err := rw.DB(ctx).Delete(&ChannelSchema{}, channelSchemaIds).Error
	if err != nil {
		log.Errorf("BatchDeleteChannelSchemas failed by channel_schema_id %v. err:%s", channelSchemaIds, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) GetChannelSchemas(ctx context.Context, channelId int) ([]*ChannelSchema, error) {
	css := make([]*ChannelSchema, 0)
	err := rw.DB(ctx).Model(&ChannelSchema{}).Where("channel_id = ?", channelId).Order("channel_schema_id asc").Find(&css).Error
	if err != nil {
		log.Errorf("get channel schemas failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return css, nil
}

func (rw ChannelReadWrite) SavePreChecks(ctx context.Context, preChecks []*PrecheckInfo) ([]*PrecheckInfo, error) {
	err := rw.DB(ctx).Save(preChecks).Error
	if err != nil {
		log.Errorf("create channel pre-check infos to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return preChecks, nil
}

func (rw ChannelReadWrite) GetChannelPreCheckInfosByChannelId(ctx context.Context, channelId int) ([]*PrecheckInfo, error) {
	checks := make([]*PrecheckInfo, 0)
	err := rw.DB(ctx).Model(&PrecheckInfo{}).Where("channel_id = ?", channelId).Order("check_object asc").Find(&checks).Error
	if err != nil {
		log.Errorf("get channel pre-check infos failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return checks, nil
}

func (rw ChannelReadWrite) UpdatePreCheckInfo(ctx context.Context, preCheck *PrecheckInfo) (*PrecheckInfo, error) {
	err := rw.DB(ctx).Save(preCheck).Error
	if err != nil {
		log.Errorf("update channel pre-check info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return preCheck, nil
}

func (rw ChannelReadWrite) GetPreCheckById(ctx context.Context, preCheckId int) (*PrecheckInfo, error) {
	check := &PrecheckInfo{}
	err := rw.DB(ctx).First(check, preCheckId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "get channel pre-check info not found, preCheckId:%d", preCheckId)
	} else if err != nil {
		log.Errorf("get channel pre-check info failed. preCheckId:%d, err:%v", preCheckId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return check, nil
}

func (rw ChannelReadWrite) GetCheckingPreCheckByChannelId(ctx context.Context, channelId int) ([]*PrecheckInfo, error) {
	preCheckInfos := make([]*PrecheckInfo, 0, 0)
	err := rw.DB(ctx).Model(&PrecheckInfo{}).Where("channel_id = ? and check_status = ?", channelId, constants.PRE_CHECK_STATUS_CHECKING).Find(&preCheckInfos).Error
	if err != nil {
		log.Errorf("get checking pre-check infos failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return preCheckInfos, nil
}

func (rw ChannelReadWrite) SaveChannelSchemaTables(ctx context.Context, tables []*ChannelSchemaTable) ([]*ChannelSchemaTable, error) {
	// 分离需要插入和更新的记录
	var insertTables []*ChannelSchemaTable
	var updateTables []*ChannelSchemaTable

	for _, table := range tables {
		if table.ChannelSchtableId == 0 {
			// 主键为0，执行插入操作
			insertTables = append(insertTables, table)
		} else {
			// 主键不为0，执行更新操作
			updateTables = append(updateTables, table)
		}
	}

	// 执行插入操作
	if len(insertTables) > 0 {
		err := rw.DB(ctx).CreateInBatches(insertTables, constants.DEFAULT_CREATE_BATCH_SIZE).Error
		if err != nil {
			log.Errorf("insert channel tables to db failed. err:%s", err)
			return nil, dbCommon.WrapDBError(err)
		}
	}

	// 执行批量更新操作
	if len(updateTables) > 0 {
		err := rw.DB(ctx).Save(updateTables).Error
		if err != nil {
			log.Errorf("batch update channel tables to db failed. count:%d, err:%s", len(updateTables), err)
			return nil, dbCommon.WrapDBError(err)
		}
	}

	return tables, nil
}

func (rw ChannelReadWrite) UpdateTargetChannelSchemaNameByPks(ctx context.Context, channelSchtableIds []int, schemaNameT string) error {
	err := rw.DB(ctx).Where("channel_schtable_id in ?", channelSchtableIds).Update("schema_name_t", schemaNameT).Error
	if err != nil {
		log.Errorf("update target channel schema name failed by channel_schtable_id[%d], schemaNameT:%s,  err:%s", channelSchtableIds, schemaNameT, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) BatchDeleteChannelSchemaTables(ctx context.Context, channelSchtableIds []int) error {
	err := rw.DB(ctx).Delete(&ChannelSchemaTable{}, channelSchtableIds).Error
	return dbCommon.WrapDBError(err)
}

func (rw ChannelReadWrite) GetChannelSchemaTablesByTaskId(ctx context.Context, taskId int) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("task_id = ?", taskId).Order("schema_name_s").Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. task_id is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, taskType int, channelId int) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("task_type = ? and channel_id = ? and task_id <> 0 ", taskType, channelId).Order("schema_name_s").Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. task_type is %d, channel_id is %d,err:%s", taskType, channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetUnselectedChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, taskType int, channelId int) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("task_type = ? and channel_id = ? and task_id = 0 ", taskType, channelId).Order("schema_name_s").Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. task_type is %d, channel_id is %d,err:%s", taskType, channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) DeleteChannelSchemaTablesByTaskTypeAndChannelIdAndSchemaTableName(ctx context.Context, taskType int, channelId int, schemaName, tableName string) error {
	err := rw.DB(ctx).Delete(&ChannelSchemaTable{}, "task_type = ? and channel_id = ? and schema_name_s = ? and table_name_s = ?", taskType, channelId, schemaName, tableName).Error
	if err != nil {
		log.Errorf("delete channel schema tables failed. task_type is %d, channel_id is %d, schemaName is %s, tableName is %s, err:%s", taskType, channelId, schemaName, tableName, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) GetConflictTables(ctx context.Context, taskId int, channelId int, taskType int) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).
		Where("task_id != ? and channel_id = ? and task_type = ? and (table_name_s,schema_name_s) in (select table_name_s,schema_name_s from channel_schema_tables where task_id = ?)", taskId, channelId, taskType, taskId).
		Order("task_id, schema_name_s").Find(&tables).Error
	if err != nil {
		log.Errorf("get conflict tables failed. task_id:%d, channelId:%d, taskType:%d, err:%s", taskId, channelId, taskType, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, channelId int, taskType int, schemaNames []string, tableNamePrefix string) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	//query := rw.DB(ctx).Where(&ChannelSchemaTable{ChannelId: channelId, TaskId: 0, TaskType: taskType})
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? and task_id = 0 and task_type = ?", channelId, taskType)
	if len(schemaNames) > 0 {
		query = query.Where("schema_name_s in ?", schemaNames)
	}
	if strings.TrimSpace(tableNamePrefix) != "" {
		query = query.Where("table_name_s like '%" + tableNamePrefix + "%'")
	}
	err := query.Order("schema_name_s,table_name_s").Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. channelId:%d, taskType:%d, schemaNames:%v, tableNamePrefix:%s, err:%s", channelId, taskType, schemaNames, tableNamePrefix, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetUnSelectedChannelSchemaTablesByPage(ctx context.Context, channelId int, taskType int, schemaNames []string, tableNamePrefix string, page, pageSize int) ([]*ChannelSchemaTable, int64, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? and task_id = 0 and task_type = ?", channelId, taskType)
	if len(schemaNames) > 0 {
		query = query.Where("schema_name_s in ?", schemaNames)
	}
	if strings.TrimSpace(tableNamePrefix) != "" {
		query = query.Where("upper(table_name_s) like '%" + strings.ToUpper(tableNamePrefix) + "%'")
	}
	var count int64 = 0
	err := query.Order("schema_name_s,table_name_s").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&tables).Error
	if err != nil {
		log.Errorf("page get channel schema tables failed. channelId:%d, taskType:%d, schemaNames:%v, tableNamePrefix:%s, err:%s", channelId, taskType, schemaNames, tableNamePrefix, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return tables, count, nil
}

func (rw ChannelReadWrite) GetSelectedChannelSchemaTablesByTaskIdAndChannelId(ctx context.Context, channelId int, taskId int, schemaNames []string, tableNamePrefix string, clusterTypeT, partitioningTypeS, partitioningTypeT, orderKeys []string, pks, uks, pkt string, page, pageSize int) ([]*ChannelSchemaTable, int64, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where(&ChannelSchemaTable{ChannelId: channelId, TaskId: taskId})
	if len(schemaNames) > 0 {
		query = query.Where("schema_name_s in ?", schemaNames)
	}
	if len(clusterTypeT) > 0 {
		query = query.Where("cluster_type_t in ?", clusterTypeT)
	}
	if len(partitioningTypeS) > 0 {
		query = query.Where("partitioning_type_s in ?", partitioningTypeS)
	}
	if len(partitioningTypeT) > 0 {
		query = query.Where("partitioning_type_t in ?", partitioningTypeT)
	}
	if strings.TrimSpace(uks) != "" {
		query = query.Where("uk_s = '" + uks + "'")
	}
	if strings.TrimSpace(pks) != "" {
		query = query.Where("pk_s = '" + pks + "'")
	}
	if strings.TrimSpace(pkt) != "" {
		query = query.Where("pk_t = '" + pkt + "'")
	}

	if strings.TrimSpace(tableNamePrefix) != "" {
		query = query.Where("upper(table_name_s) like '%" + strings.ToUpper(tableNamePrefix) + "%'")
	}
	var count int64 = 0
	if len(orderKeys) > 0 {
		for _, key := range orderKeys {
			query = query.Order(key)
		}
	} else {
		query = query.Order("schema_name_s,table_name_s")
	}
	err := query.Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. channelId:%d, taskId:%d, schemaNames:%v, tableNamePrefix:%s, err:%s", channelId, taskId, schemaNames, tableNamePrefix, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return tables, count, nil
}

func (rw ChannelReadWrite) UnSelectChannelSchemaTables(ctx context.Context, ids []int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_schtable_id in ?", ids).Update("task_id", 0).Error
	if err != nil {
		log.Errorf("un-selected channel schema tables failed. ids:%v, err:%s", ids, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) SelectChannelSchemaTables(ctx context.Context, ids []int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_schtable_id in ?", ids).Update("task_id", taskId).Error
	if err != nil {
		log.Errorf("un-selected channel schema tables failed. ids:%v, err:%s", ids, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesByPks(ctx context.Context, channelSchtableIds []int) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Find(&tables, channelSchtableIds).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. pks is %d, err:%s", channelSchtableIds, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetTablesByTableNameAndSourceSchemas(ctx context.Context, channelId, taskId int, tableName string, schemas []string) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Where("channel_id = ? and task_id = ? and schema_name_s in ?", channelId, taskId, schemas).Where("table_name_s like '%" + tableName + "%'").Find(&tables).Error
	if err != nil {
		log.Errorf("get tables by table name and schemas failed. channelId:%d, taskId:%d, schemas:%s, tableName:%s, , err:%s", channelId, taskId, schemas, tableName, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx context.Context, channelId, taskId int) ([]string, error) {
	var schemas []string
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ?", channelId, taskId).Order("schema_name_s asc").Distinct().Pluck("schema_name_s", &schemas).Error
	if err != nil {
		log.Errorf("get channel schema tables distinct schema failed. channel_id is %d, task_id is %d, err:%s", channelId, taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return schemas, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS(ctx context.Context, channelId, taskId int, schemaS string) (string, error) {
	var schemat []string
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND schema_name_s = ?", channelId, taskId, schemaS).Distinct().Pluck("schema_name_t", &schemat).Error
	if err != nil {
		log.Errorf("get channel schema tables distinct schema_name_t failed. channel_id is %d, task_id is %d, schema_name_s is %s, err:%s", channelId, taskId, schemaS, err)
		return "", dbCommon.WrapDBError(err)
	}
	if len(schemat) > 1 {
		return "", fmt.Errorf("get channel schema tables distinct schema_name_t failed. channel_id is %d, task_id is %d, schema_name_s is %s, err: schema_name_t values [%v] are multi", channelId, taskId, schemaS, schemat)
	}
	return schemat[0], nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesByChannelTaskSchemaS(ctx context.Context, channelId, taskId int, schemaS string) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND schema_name_s = ?", channelId, taskId, schemaS).Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. channel_id is %d, task_id is %d, schema_name_s is %s, err:%s", channelId, taskId, schemaS, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTableByChannelTaskSchemaSTableS(ctx context.Context, channelId, taskId int, schemaS, tableS string) (*ChannelSchemaTable, error) {
	table := &ChannelSchemaTable{}
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND schema_name_s = ? AND table_name_s = ?", channelId, taskId, schemaS, tableS).First(table).Error
	if err != nil {
		log.Errorf(" get channel schema table failed. channel_id is %d, task_id is %d, schema_name_s is %s, table_name_s is %s, err:%s", channelId, taskId, schemaS, tableS, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return table, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesCountsByTask(ctx context.Context, channelId, taskId int) (int64, error) {
	var count int64 = 0
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ?", channelId, taskId).Count(&count).Error
	if err != nil {
		log.Errorf("get channel schema tables count failed. channel_id is %d, task_id is %d, err:%s", channelId, taskId, err)
		return count, dbCommon.WrapDBError(err)
	}
	return count, nil
}

func (rw ChannelReadWrite) GroupChannelSchemaTablesCountsByTask(ctx context.Context, channelId, taskId int) (map[string]int64, error) {

	type schemaTableCount struct {
		SchemaName string `gorm:"column:schema_name_s"`
		Count      int64  `gorm:"column:count"`
	}

	counts := make(map[string]int64)
	schemaTableNums := make([]schemaTableCount, 0)

	err := rw.DB(ctx).Raw(`
	SELECT schema_name_s, count(*) as count 
	FROM channel_schema_tables 
	WHERE channel_id = ? 
	  AND task_id = ? 
	GROUP BY schema_name_s`, channelId, taskId).Scan(&schemaTableNums).Error

	if err != nil {
		log.Errorf("group channel schema tables count failed. channel_id is %d, task_id is %d, err:%s", channelId, taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}

	for _, stn := range schemaTableNums {
		counts[stn.SchemaName] = stn.Count
	}
	return counts, nil
}

func (rw ChannelReadWrite) AddAllChannelSchemaTables(ctx context.Context, channelId int, taskId int, taskType int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = 0 AND task_type = ? AND deleted_at is null ", channelId, taskType).Update("task_id", taskId).Error
	if err != nil {
		log.Errorf("AddAllChannelSchemaTables failed. channelId:%d,taskId:%d, err:%s", channelId, taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) DeleteAllChannelSchemaTables(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("task_id", 0).Error
	if err != nil {
		log.Errorf("DeleteAllChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) PhysicalDeleteAllChannelSchemaTables(ctx context.Context, channelId int, taskId int) error {
	request := rw.DB(ctx).Exec("DELETE FROM channel_schema_tables WHERE channel_id = ? AND task_id = ?", channelId, taskId)
	err := request.Error
	if err != nil {
		log.Errorf("PhysicalDeleteAllChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}

	log.Debugf("PhysicalDeleteAllChannelSchemaTables success. affected:%d, channelId:%d, taskId:%d", request.RowsAffected, channelId, taskId)
	return nil
}

func (rw ChannelReadWrite) ToUpperCaseChannelSchemaTables(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("table_name_t", gorm.Expr("upper(table_name_t)")).Error
	if err != nil {
		log.Errorf("ToUpperCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ToLowerCaseChannelSchemaTables(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("table_name_t", gorm.Expr("lower(table_name_t)")).Error
	if err != nil {
		log.Errorf("ToLowerCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ToUpperCaseTableNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("table_name_t", gorm.Expr("upper(table_name_t)")).Error
	if err != nil {
		log.Errorf("ToUpperCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ToLowerCaseTableNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("table_name_t", gorm.Expr("lower(table_name_t)")).Error
	if err != nil {
		log.Errorf("ToLowerCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ToUpperCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("schema_name_t", gorm.Expr("upper(schema_name_t)")).Error
	if err != nil {
		log.Errorf("ToUpperCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ToLowerCaseSchemaNameT(ctx context.Context, channelId int, taskId int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId).Update("schema_name_t", gorm.Expr("lower(schema_name_t)")).Error
	if err != nil {
		log.Errorf("ToLowerCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) UnSelectChannelSchemaTablesByTaskId(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("task_id in ?", taskIds).Update("task_id", 0).Error
	if err != nil {
		log.Errorf("un-selected channel schema tables by task id failed. task_ids:%v, err:%s", taskIds, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ModifyChannelSchemaTable(ctx context.Context, channelId int, taskId int, modifyTag string, tableNameS []string) error {
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id in (select task_id from tasks t where t.task_id =? or t.parent_task_id =?) AND deleted_at is null ", channelId, taskId, taskId)
	if len(tableNameS) > 0 {
		query.Where("table_name_s in ? ", tableNameS)
	}

	var err error
	if strings.EqualFold("schema_upper", modifyTag) {
		err = query.Update("schema_name_t", gorm.Expr("upper(schema_name_t)")).Error
	} else if strings.EqualFold("schema_lower", modifyTag) {
		err = query.Update("schema_name_t", gorm.Expr("lower(schema_name_t)")).Error
	} else if strings.EqualFold("table_upper", modifyTag) {
		err = query.Update("table_name_t", gorm.Expr("upper(table_name_t)")).Error
	} else {
		err = query.Update("table_name_t", gorm.Expr("lower(table_name_t)")).Error
	}

	if err != nil {
		log.Errorf("ToLowerCaseChannelSchemaTables failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) ChangePartitioningTypeT(ctx context.Context, channelId int, taskId int, channelSchtableIds []int, partitioningTypeT string) error {
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId)
	if len(channelSchtableIds) > 0 {
		query.Where("channel_schtable_id in ? ", channelSchtableIds)
	}
	if strings.EqualFold(partitioningTypeT, "N") {
		err := query.Update("partitioning_type_t", partitioningTypeT).Error
		if err != nil {
			log.Errorf("ChangePartitioningT failed. task_id:%d, err:%s", taskId, err)
			return dbCommon.WrapDBError(err)
		}
	} else {
		err := query.Update("partitioning_type_t", gorm.Expr("partitioning_type_s")).Error
		if err != nil {
			log.Errorf("ChangePartitioningT failed. task_id:%d, err:%s", taskId, err)
			return dbCommon.WrapDBError(err)
		}
	}
	return nil
}

func (rw ChannelReadWrite) CheckChannelSchemaTableByPartitioningTypeT(ctx context.Context, channelId, taskId int, channelSchtableIds []int, partitioningTypeT string) ([]*ChannelSchemaTable, error) {
	records := make([]*ChannelSchemaTable, 10)
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId)
	if len(channelSchtableIds) > 0 {
		query.Where("channel_schtable_id in ? ", channelSchtableIds)
	}
	query.Where("partitioning_type_s <> ? ", partitioningTypeT)
	err := query.Order("table_name_s asc").Find(&records).Error
	if err != nil {
		log.Errorf("CheckChannelSchemaTableByPartitioningTypeT failed. channelId:%s, taskId:%s,partitioningTypeT:%S", channelId, taskId, partitioningTypeT)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw ChannelReadWrite) ChangeClusterTypeT(ctx context.Context, channelId int, taskId int, channelSchtableIds []int, clusterTypeT string) error {
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId)
	if len(channelSchtableIds) > 0 {
		query.Where("channel_schtable_id in ? ", channelSchtableIds)
	}
	err := query.Update("cluster_type_t", clusterTypeT).Error
	if err != nil {
		log.Errorf("ChangeClusterT failed. task_id:%d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) CheckChannelSchemaTableByCluster(ctx context.Context, channelId, taskId int, channelSchtableIds []int, clusterTypeT string) ([]*ChannelSchemaTable, error) {
	records := make([]*ChannelSchemaTable, 10)
	query := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null ", channelId, taskId)
	if len(channelSchtableIds) > 0 {
		query.Where("channel_schtable_id in ? ", channelSchtableIds)
	}
	if strings.EqualFold(clusterTypeT, "Y") {
		query.Where("pk_t = 'N' ")
	}

	err := query.Order("table_name_s asc").Find(&records).Error
	if err != nil {
		log.Errorf("CheckChannelSchemaTableByCluster. channelId:%s, taskId:%s,clusterTypeT:%S", channelId, taskId, clusterTypeT)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw ChannelReadWrite) SaveChannelSchemaTable(ctx context.Context, table *ChannelSchemaTable) (*ChannelSchemaTable, error) {
	err := rw.DB(ctx).Save(table).Error
	if err != nil {
		log.Errorf("save channel schema table to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return table, nil
}

func (rw ChannelReadWrite) DeleteAllChannelSchemaTablesByTaskIds(ctx context.Context, channelId int, taskIds []int) error {
	err := rw.DB(ctx).Unscoped().Delete(&ChannelSchemaTable{}, "channel_id = ? AND task_id in (?)", channelId, taskIds).Error
	if err != nil {
		log.Errorf("delete channel schemas failed by channel_id %d and task_ids %v. err:%s", channelId, taskIds, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw ChannelReadWrite) GetChannelSchemasByTaskId(ctx context.Context, taskId int) ([]string, error) {
	var schemaNames []string
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("task_id = ?", taskId).Distinct().Pluck("schema_name_s", &schemaNames).Error
	if err != nil {
		log.Errorf("get channel schema names failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return schemaNames, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesByTaskIdAndSourceTableNames(ctx context.Context, taskId int, tableNames []string) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("task_id = ? AND table_name_s in ?", taskId, tableNames).Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. taskId is %d, tableNames is %v, err:%s", taskId, tableNames, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) SaveTableColumnCustomMapRule(ctx context.Context, rules []*TableColumnCustomMapRule) ([]*TableColumnCustomMapRule, int64, error) {
	saveOp := rw.DB(ctx).Save(rules)
	err := saveOp.Error
	if err != nil {
		log.Errorf("save table column custom map rule failed. err:%s", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return rules, saveOp.RowsAffected, nil
}

func (rw ChannelReadWrite) SaveTableColumnCustomSummary(ctx context.Context, s *TableColumnCustomSummary) (*TableColumnCustomSummary, error) {
	err := rw.DB(ctx).Save(s).Error
	if err != nil {
		log.Errorf("save table column custom summary failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return s, nil
}

func (rw ChannelReadWrite) CountTableColumnCustomMapRule(ctx context.Context, taskId int) (int64, error) {
	var count int64
	err := rw.DB(ctx).Model(&TableColumnCustomMapRule{}).Where("task_id = ?", taskId).Count(&count).Error
	if err != nil {
		log.Errorf("count table column custom map rule failed. taskId is %d, err:%s", taskId, err)
		return 0, dbCommon.WrapDBError(err)
	}
	return count, nil
}

func (rw ChannelReadWrite) QueryTableColumnCustomMapRuleByChannelTaskAndArgs(ctx context.Context, channelId, taskId int, page, pageSize int, condition string, args []any) ([]*TableColumnCustomMapRule, int64, error) {
	var rules []*TableColumnCustomMapRule
	var count int64

	querySQL := `
SELECT r.*
FROM table_column_custom_map_rules r
	JOIN (
		SELECT DISTINCT t.schema_name_s, t.table_name_s
		FROM channel_schema_tables t
		WHERE t.channel_id = ?
		) snstns 
	ON snstns.schema_name_s = r.schema_name_s 
	       AND snstns.table_name_s = r.table_name_s
WHERE r.channel_id = ?
` + condition + ` ORDER BY channel_id,schema_name_s,table_name_s,column_id LIMIT ? OFFSET ?`

	combineArgs := append([]any{channelId, channelId}, args...)

	pagedArgs := append(combineArgs, pageSize, (page-1)*pageSize)

	log.Debugf("query table column custom map rules sql is [%s], args is [%v]", querySQL, pagedArgs)
	err := rw.DB(ctx).Raw(querySQL, pagedArgs...).Scan(&rules).Error
	if err != nil {
		log.Errorf("query table column custom map rules failed. taskId is %d, err:%s", taskId, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	countSQL := `
SELECT COUNT(*)
FROM table_column_custom_map_rules r
	JOIN (
		SELECT DISTINCT t.schema_name_s, t.table_name_s
		FROM channel_schema_tables t
		WHERE t.channel_id = ?
		) snstns 
	ON snstns.schema_name_s = r.schema_name_s 
	       AND snstns.table_name_s = r.table_name_s
WHERE r.channel_id = ?
` + condition
	log.Debugf("count table column custom map rules sql is %s, args is %v", countSQL, combineArgs)
	err = rw.DB(ctx).Raw(countSQL, combineArgs...).Count(&count).Error
	if err != nil {
		log.Errorf("query table column custom map rules count failed. taskId is %d, err:%s", taskId, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	return rules, count, nil
}

func (rw ChannelReadWrite) GetTableColumnCustomSummary(ctx context.Context, channelId int) (*TableColumnCustomSummary, error) {
	summary := &TableColumnCustomSummary{}
	err := rw.DB(ctx).First(summary, "channel_id = ?", channelId).Error
	if goerrors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		log.Errorf("get table column custom summary failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return summary, nil
}

func (rw ChannelReadWrite) RemoveTableColumnCustomMapRule(ctx context.Context, query *TableColumnCustomMapRule) (int64, error) {
	result := rw.DB(ctx).Unscoped().Delete(&TableColumnCustomMapRule{}, query)
	if result.Error != nil {
		log.Errorf("remove table column custom map rule failed. query is %v, err:%s", query, result.Error)
		return 0, dbCommon.WrapDBError(result.Error)
	}
	return result.RowsAffected, nil
}

func (rw ChannelReadWrite) GetChannelTaskIdsByTaskIdAndTableNamePrefix(ctx context.Context, channelId int, tableNamePredix string) ([]int, error) {
	var taskIds []int
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ? AND table_name_s like ?", channelId, tableNamePredix+"%").Distinct().Pluck("task_id", &taskIds).Error
	if err != nil {
		log.Errorf("get channel task ids by task id and table name prefix failed. channelId is %s, tableNamePredix is %s, err:%s", channelId, tableNamePredix, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return taskIds, nil
}

func (rw ChannelReadWrite) RemoveTableColumnCustomSummary(ctx context.Context, channelId int) (int64, error) {
	result := rw.DB(ctx).Unscoped().Delete(&TableColumnCustomSummary{}, "channel_id = ?", channelId)
	if result.Error != nil {
		log.Errorf("remove table column custom summary failed. channelId is %d, err:%s", channelId, result.Error)
		return 0, dbCommon.WrapDBError(result.Error)
	}
	return result.RowsAffected, nil
}

func (rw ChannelReadWrite) GetChannelSchemaTablesByChannelId(ctx context.Context, channelId int) ([]*ChannelSchemaTable, error) {
	tables := make([]*ChannelSchemaTable, 0, 0)
	err := rw.DB(ctx).Model(&ChannelSchemaTable{}).Where("channel_id = ?", channelId).Find(&tables).Error
	if err != nil {
		log.Errorf("get channel schema tables failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tables, nil
}

func (rw ChannelReadWrite) QueryTableColumnCustomMapRuleByChannel(ctx context.Context, channelId int) ([]*TableColumnCustomMapRule, error) {
	var rules []*TableColumnCustomMapRule

	err := rw.DB(ctx).Model(&TableColumnCustomMapRule{}).
		Where("channel_id = ?", channelId).
		Order("schema_name_s,table_name_s,column_id").
		Find(&rules).Error
	if err != nil {
		log.Errorf("query table column custom map rules failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rules, nil
}

func (rw ChannelReadWrite) QueryTableColumnCustomMapRuleByChannelAndDigests(ctx context.Context, channelId int, digests []string) ([]*TableColumnCustomMapRule, error) {
	var rules []*TableColumnCustomMapRule
	err := rw.DB(ctx).Model(&TableColumnCustomMapRule{}).
		Where("channel_id = ? AND column_digest in ?", channelId, digests).
		Order("schema_name_s,table_name_s,column_id").
		Find(&rules).Error
	if err != nil {
		log.Errorf("query table column custom map rules failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rules, nil
}

func (rw ChannelReadWrite) CreateChannelDatasource(ctx context.Context, datasource *ChannelDatasource) (*ChannelDatasource, error) {
	err := rw.DB(ctx).Create(datasource).Error
	if err != nil {
		log.Errorf("create channel datasource to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return datasource, nil
}

func (rw ChannelReadWrite) GetChannelDatasourcesByChannelId(ctx context.Context, channelId int) ([]*ChannelDatasource, error) {
	var datasources []*ChannelDatasource
	err := rw.DB(ctx).Model(&ChannelDatasource{}).Where("channel_id = ?", channelId).Find(&datasources).Error
	if err != nil {
		log.Errorf("get channel datasources failed. channelId is %d, err:%s", channelId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return datasources, nil
}

func (rw ChannelReadWrite) DeleteChannelDatasourceByChannelIdAndDatasourceId(ctx context.Context, channelId int, datasourceId int) (int64, error) {
	result := rw.DB(ctx).Where("channel_id = ? AND data_source_id = ?", channelId, datasourceId).Delete(&ChannelDatasource{})
	if result.Error != nil {
		log.Errorf("delete channel datasource failed. channelId: %d, datasourceId: %d, err: %s", channelId, datasourceId, result.Error)
		return 0, dbCommon.WrapDBError(result.Error)
	}
	return result.RowsAffected, nil
}
