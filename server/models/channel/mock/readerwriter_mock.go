// Code generated by MockGen. DO NOT EDIT.
// Source: ./channel/readerwriter.go

// Package mockchannelreaderwriter is a generated GoMock package.
package mockchannelreaderwriter

import (
	context "context"
	reflect "reflect"

	channel "gitee.com/pingcap_enterprise/tms/server/models/channel"
	gomock "github.com/golang/mock/gomock"
)

// MockReaderWriter is a mock of ReaderWriter interface.
type MockReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReaderWriterMockRecorder
}

// MockReaderWriterMockRecorder is the mock recorder for MockReaderWriter.
type MockReaderWriterMockRecorder struct {
	mock *MockReaderWriter
}

// NewMockReaderWriter creates a new mock instance.
func NewMockReaderWriter(ctrl *gomock.Controller) *MockReaderWriter {
	mock := &MockReaderWriter{ctrl: ctrl}
	mock.recorder = &MockReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReaderWriter) EXPECT() *MockReaderWriterMockRecorder {
	return m.recorder
}

// AddAllChannelSchemaTables mocks base method.
func (m *MockReaderWriter) AddAllChannelSchemaTables(ctx context.Context, channelId, taskId, taskType int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAllChannelSchemaTables", ctx, channelId, taskId, taskType)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAllChannelSchemaTables indicates an expected call of AddAllChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) AddAllChannelSchemaTables(ctx, channelId, taskId, taskType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAllChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).AddAllChannelSchemaTables), ctx, channelId, taskId, taskType)
}

// BatchDeleteChannelSchemaTables mocks base method.
func (m *MockReaderWriter) BatchDeleteChannelSchemaTables(ctx context.Context, channelSchtableIds []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteChannelSchemaTables", ctx, channelSchtableIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteChannelSchemaTables indicates an expected call of BatchDeleteChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) BatchDeleteChannelSchemaTables(ctx, channelSchtableIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).BatchDeleteChannelSchemaTables), ctx, channelSchtableIds)
}

// BatchDeleteChannelSchemas mocks base method.
func (m *MockReaderWriter) BatchDeleteChannelSchemas(ctx context.Context, channelSchemaIds []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteChannelSchemas", ctx, channelSchemaIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteChannelSchemas indicates an expected call of BatchDeleteChannelSchemas.
func (mr *MockReaderWriterMockRecorder) BatchDeleteChannelSchemas(ctx, channelSchemaIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteChannelSchemas", reflect.TypeOf((*MockReaderWriter)(nil).BatchDeleteChannelSchemas), ctx, channelSchemaIds)
}

// CreateChannel mocks base method.
func (m *MockReaderWriter) CreateChannel(ctx context.Context, channelInfo *channel.ChannelInformation) (*channel.ChannelInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannel", ctx, channelInfo)
	ret0, _ := ret[0].(*channel.ChannelInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChannel indicates an expected call of CreateChannel.
func (mr *MockReaderWriterMockRecorder) CreateChannel(ctx, channelInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannel", reflect.TypeOf((*MockReaderWriter)(nil).CreateChannel), ctx, channelInfo)
}

// CreateChannelSchemaObject mocks base method.
func (m *MockReaderWriter) CreateChannelSchemaObject(ctx context.Context, channelObject *channel.ChannelSchemaObject) (*channel.ChannelSchemaObject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannelSchemaObject", ctx, channelObject)
	ret0, _ := ret[0].(*channel.ChannelSchemaObject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChannelSchemaObject indicates an expected call of CreateChannelSchemaObject.
func (mr *MockReaderWriterMockRecorder) CreateChannelSchemaObject(ctx, channelObject interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannelSchemaObject", reflect.TypeOf((*MockReaderWriter)(nil).CreateChannelSchemaObject), ctx, channelObject)
}

// CreateChannelSchemaObjects mocks base method.
func (m *MockReaderWriter) CreateChannelSchemaObjects(ctx context.Context, channelObjects []*channel.ChannelSchemaObject) ([]*channel.ChannelSchemaObject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannelSchemaObjects", ctx, channelObjects)
	ret0, _ := ret[0].([]*channel.ChannelSchemaObject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChannelSchemaObjects indicates an expected call of CreateChannelSchemaObjects.
func (mr *MockReaderWriterMockRecorder) CreateChannelSchemaObjects(ctx, channelObjects interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannelSchemaObjects", reflect.TypeOf((*MockReaderWriter)(nil).CreateChannelSchemaObjects), ctx, channelObjects)
}

// CreateChannelSchemas mocks base method.
func (m *MockReaderWriter) CreateChannelSchemas(ctx context.Context, channelSchemas []*channel.ChannelSchema) ([]*channel.ChannelSchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateChannelSchemas", ctx, channelSchemas)
	ret0, _ := ret[0].([]*channel.ChannelSchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateChannelSchemas indicates an expected call of CreateChannelSchemas.
func (mr *MockReaderWriterMockRecorder) CreateChannelSchemas(ctx, channelSchemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateChannelSchemas", reflect.TypeOf((*MockReaderWriter)(nil).CreateChannelSchemas), ctx, channelSchemas)
}

// DeleteAllChannelSchemaTables mocks base method.
func (m *MockReaderWriter) DeleteAllChannelSchemaTables(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAllChannelSchemaTables", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllChannelSchemaTables indicates an expected call of DeleteAllChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) DeleteAllChannelSchemaTables(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).DeleteAllChannelSchemaTables), ctx, channelId, taskId)
}

// DeleteChannel mocks base method.
func (m *MockReaderWriter) DeleteChannel(ctx context.Context, channelId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChannel", ctx, channelId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChannel indicates an expected call of DeleteChannel.
func (mr *MockReaderWriterMockRecorder) DeleteChannel(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChannel", reflect.TypeOf((*MockReaderWriter)(nil).DeleteChannel), ctx, channelId)
}

// DeleteChannelSchemasByChannelId mocks base method.
func (m *MockReaderWriter) DeleteChannelSchemasByChannelId(ctx context.Context, channelId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteChannelSchemasByChannelId", ctx, channelId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteChannelSchemasByChannelId indicates an expected call of DeleteChannelSchemasByChannelId.
func (mr *MockReaderWriterMockRecorder) DeleteChannelSchemasByChannelId(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteChannelSchemasByChannelId", reflect.TypeOf((*MockReaderWriter)(nil).DeleteChannelSchemasByChannelId), ctx, channelId)
}

// GetChannel mocks base method.
func (m *MockReaderWriter) GetChannel(ctx context.Context, channelId int) (*channel.ChannelInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannel", ctx, channelId)
	ret0, _ := ret[0].(*channel.ChannelInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannel indicates an expected call of GetChannel.
func (mr *MockReaderWriterMockRecorder) GetChannel(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannel", reflect.TypeOf((*MockReaderWriter)(nil).GetChannel), ctx, channelId)
}

// GetChannelPreCheckInfosByChannelId mocks base method.
func (m *MockReaderWriter) GetChannelPreCheckInfosByChannelId(ctx context.Context, channelId int) ([]*channel.PrecheckInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelPreCheckInfosByChannelId", ctx, channelId)
	ret0, _ := ret[0].([]*channel.PrecheckInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelPreCheckInfosByChannelId indicates an expected call of GetChannelPreCheckInfosByChannelId.
func (mr *MockReaderWriterMockRecorder) GetChannelPreCheckInfosByChannelId(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelPreCheckInfosByChannelId", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelPreCheckInfosByChannelId), ctx, channelId)
}

// GetChannelSchemaObjectByTaskId mocks base method.
func (m *MockReaderWriter) GetChannelSchemaObjectByTaskId(ctx context.Context, taskId int) (*channel.ChannelSchemaObject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaObjectByTaskId", ctx, taskId)
	ret0, _ := ret[0].(*channel.ChannelSchemaObject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaObjectByTaskId indicates an expected call of GetChannelSchemaObjectByTaskId.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaObjectByTaskId(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaObjectByTaskId", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaObjectByTaskId), ctx, taskId)
}

// GetChannelSchemaTablesByChannelTaskSchemaS mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesByChannelTaskSchemaS(ctx context.Context, channelId, taskId int, schemaS string) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesByChannelTaskSchemaS", ctx, channelId, taskId, schemaS)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesByChannelTaskSchemaS indicates an expected call of GetChannelSchemaTablesByChannelTaskSchemaS.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesByChannelTaskSchemaS(ctx, channelId, taskId, schemaS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesByChannelTaskSchemaS", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesByChannelTaskSchemaS), ctx, channelId, taskId, schemaS)
}

// GetChannelSchemaTablesByPks mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesByPks(ctx context.Context, channelSchtableIds []int) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesByPks", ctx, channelSchtableIds)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesByPks indicates an expected call of GetChannelSchemaTablesByPks.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesByPks(ctx, channelSchtableIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesByPks", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesByPks), ctx, channelSchtableIds)
}

// GetChannelSchemaTablesByTaskId mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesByTaskId(ctx context.Context, taskId int) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesByTaskId", ctx, taskId)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesByTaskId indicates an expected call of GetChannelSchemaTablesByTaskId.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesByTaskId(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesByTaskId", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesByTaskId), ctx, taskId)
}

// GetChannelSchemaTablesByTaskTypeAndChannelId mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, taskType, channelId int) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesByTaskTypeAndChannelId", ctx, taskType, channelId)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesByTaskTypeAndChannelId indicates an expected call of GetChannelSchemaTablesByTaskTypeAndChannelId.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesByTaskTypeAndChannelId(ctx, taskType, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesByTaskTypeAndChannelId", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesByTaskTypeAndChannelId), ctx, taskType, channelId)
}

// GetChannelSchemaTablesCountsByTask mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesCountsByTask(ctx context.Context, channelId, taskId int) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesCountsByTask", ctx, channelId, taskId)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesCountsByTask indicates an expected call of GetChannelSchemaTablesCountsByTask.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesCountsByTask(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesCountsByTask", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesCountsByTask), ctx, channelId, taskId)
}

// GetChannelSchemaTablesDistinctSchemaSByChannelTaskId mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx context.Context, channelId, taskId int) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesDistinctSchemaSByChannelTaskId", ctx, channelId, taskId)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesDistinctSchemaSByChannelTaskId indicates an expected call of GetChannelSchemaTablesDistinctSchemaSByChannelTaskId.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesDistinctSchemaSByChannelTaskId", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesDistinctSchemaSByChannelTaskId), ctx, channelId, taskId)
}

// GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS mocks base method.
func (m *MockReaderWriter) GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS(ctx context.Context, channelId, taskId int, schemaS string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS", ctx, channelId, taskId, schemaS)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS indicates an expected call of GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS(ctx, channelId, taskId, schemaS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemaTablesDistinctSchemaTByChannelTaskSchemaS), ctx, channelId, taskId, schemaS)
}

// GetChannelSchemas mocks base method.
func (m *MockReaderWriter) GetChannelSchemas(ctx context.Context, channelId int) ([]*channel.ChannelSchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelSchemas", ctx, channelId)
	ret0, _ := ret[0].([]*channel.ChannelSchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelSchemas indicates an expected call of GetChannelSchemas.
func (mr *MockReaderWriterMockRecorder) GetChannelSchemas(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelSchemas", reflect.TypeOf((*MockReaderWriter)(nil).GetChannelSchemas), ctx, channelId)
}

// GetCheckingPreCheckByChannelId mocks base method.
func (m *MockReaderWriter) GetCheckingPreCheckByChannelId(ctx context.Context, channelId int) ([]*channel.PrecheckInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCheckingPreCheckByChannelId", ctx, channelId)
	ret0, _ := ret[0].([]*channel.PrecheckInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCheckingPreCheckByChannelId indicates an expected call of GetCheckingPreCheckByChannelId.
func (mr *MockReaderWriterMockRecorder) GetCheckingPreCheckByChannelId(ctx, channelId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCheckingPreCheckByChannelId", reflect.TypeOf((*MockReaderWriter)(nil).GetCheckingPreCheckByChannelId), ctx, channelId)
}

// GetConflictTables mocks base method.
func (m *MockReaderWriter) GetConflictTables(ctx context.Context, taskId, channelId, taskType int) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConflictTables", ctx, taskId, channelId, taskType)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConflictTables indicates an expected call of GetConflictTables.
func (mr *MockReaderWriterMockRecorder) GetConflictTables(ctx, taskId, channelId, taskType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConflictTables", reflect.TypeOf((*MockReaderWriter)(nil).GetConflictTables), ctx, taskId, channelId, taskType)
}

// GetPreCheckById mocks base method.
func (m *MockReaderWriter) GetPreCheckById(ctx context.Context, precheckId int) (*channel.PrecheckInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreCheckById", ctx, precheckId)
	ret0, _ := ret[0].(*channel.PrecheckInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreCheckById indicates an expected call of GetPreCheckById.
func (mr *MockReaderWriterMockRecorder) GetPreCheckById(ctx, precheckId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreCheckById", reflect.TypeOf((*MockReaderWriter)(nil).GetPreCheckById), ctx, precheckId)
}

// GetSelectedChannelSchemaTablesByTaskIdAndChannelId mocks base method.
func (m *MockReaderWriter) GetSelectedChannelSchemaTablesByTaskIdAndChannelId(ctx context.Context, channelId, taskId int, schemaNames []string, tableNamePrefix string, page, pageSize int) ([]*channel.ChannelSchemaTable, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSelectedChannelSchemaTablesByTaskIdAndChannelId", ctx, channelId, taskId, schemaNames, tableNamePrefix, page, pageSize)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetSelectedChannelSchemaTablesByTaskIdAndChannelId indicates an expected call of GetSelectedChannelSchemaTablesByTaskIdAndChannelId.
func (mr *MockReaderWriterMockRecorder) GetSelectedChannelSchemaTablesByTaskIdAndChannelId(ctx, channelId, taskId, schemaNames, tableNamePrefix, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSelectedChannelSchemaTablesByTaskIdAndChannelId", reflect.TypeOf((*MockReaderWriter)(nil).GetSelectedChannelSchemaTablesByTaskIdAndChannelId), ctx, channelId, taskId, schemaNames, tableNamePrefix, page, pageSize)
}

// GetTablesByTableNameAndSourceSchemas mocks base method.
func (m *MockReaderWriter) GetTablesByTableNameAndSourceSchemas(ctx context.Context, channelId, taskId int, tableName string, schemas []string) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTablesByTableNameAndSourceSchemas", ctx, channelId, taskId, tableName, schemas)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTablesByTableNameAndSourceSchemas indicates an expected call of GetTablesByTableNameAndSourceSchemas.
func (mr *MockReaderWriterMockRecorder) GetTablesByTableNameAndSourceSchemas(ctx, channelId, taskId, tableName, schemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTablesByTableNameAndSourceSchemas", reflect.TypeOf((*MockReaderWriter)(nil).GetTablesByTableNameAndSourceSchemas), ctx, channelId, taskId, tableName, schemas)
}

// GetUnSelectedChannelSchemaTablesByPage mocks base method.
func (m *MockReaderWriter) GetUnSelectedChannelSchemaTablesByPage(ctx context.Context, channelId, taskType int, schemaNames []string, tableNamePrefix string, page, pageSize int) ([]*channel.ChannelSchemaTable, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnSelectedChannelSchemaTablesByPage", ctx, channelId, taskType, schemaNames, tableNamePrefix, page, pageSize)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUnSelectedChannelSchemaTablesByPage indicates an expected call of GetUnSelectedChannelSchemaTablesByPage.
func (mr *MockReaderWriterMockRecorder) GetUnSelectedChannelSchemaTablesByPage(ctx, channelId, taskType, schemaNames, tableNamePrefix, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnSelectedChannelSchemaTablesByPage", reflect.TypeOf((*MockReaderWriter)(nil).GetUnSelectedChannelSchemaTablesByPage), ctx, channelId, taskType, schemaNames, tableNamePrefix, page, pageSize)
}

// GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId mocks base method.
func (m *MockReaderWriter) GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId(ctx context.Context, channelId, taskType int, schemaNames []string, tableNamePrefix string) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId", ctx, channelId, taskType, schemaNames, tableNamePrefix)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId indicates an expected call of GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId.
func (mr *MockReaderWriterMockRecorder) GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId(ctx, channelId, taskType, schemaNames, tableNamePrefix interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId", reflect.TypeOf((*MockReaderWriter)(nil).GetUnSelectedChannelSchemaTablesByTaskTypeAndChannelId), ctx, channelId, taskType, schemaNames, tableNamePrefix)
}

// ListChannels mocks base method.
func (m *MockReaderWriter) ListChannels(ctx context.Context, page, pageSize int, channelName, channelType string) ([]*channel.ChannelInformation, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListChannels", ctx, page, pageSize, channelName, channelType)
	ret0, _ := ret[0].([]*channel.ChannelInformation)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListChannels indicates an expected call of ListChannels.
func (mr *MockReaderWriterMockRecorder) ListChannels(ctx, page, pageSize, channelName, channelType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListChannels", reflect.TypeOf((*MockReaderWriter)(nil).ListChannels), ctx, page, pageSize, channelName, channelType)
}

// SaveChannelSchemaObject mocks base method.
func (m *MockReaderWriter) SaveChannelSchemaObject(ctx context.Context, channelObject *channel.ChannelSchemaObject) (*channel.ChannelSchemaObject, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveChannelSchemaObject", ctx, channelObject)
	ret0, _ := ret[0].(*channel.ChannelSchemaObject)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveChannelSchemaObject indicates an expected call of SaveChannelSchemaObject.
func (mr *MockReaderWriterMockRecorder) SaveChannelSchemaObject(ctx, channelObject interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveChannelSchemaObject", reflect.TypeOf((*MockReaderWriter)(nil).SaveChannelSchemaObject), ctx, channelObject)
}

// SaveChannelSchemaTables mocks base method.
func (m *MockReaderWriter) SaveChannelSchemaTables(ctx context.Context, tables []*channel.ChannelSchemaTable) ([]*channel.ChannelSchemaTable, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveChannelSchemaTables", ctx, tables)
	ret0, _ := ret[0].([]*channel.ChannelSchemaTable)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveChannelSchemaTables indicates an expected call of SaveChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) SaveChannelSchemaTables(ctx, tables interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).SaveChannelSchemaTables), ctx, tables)
}

// SavePreChecks mocks base method.
func (m *MockReaderWriter) SavePreChecks(ctx context.Context, preChecks []*channel.PrecheckInfo) ([]*channel.PrecheckInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SavePreChecks", ctx, preChecks)
	ret0, _ := ret[0].([]*channel.PrecheckInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SavePreChecks indicates an expected call of SavePreChecks.
func (mr *MockReaderWriterMockRecorder) SavePreChecks(ctx, preChecks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePreChecks", reflect.TypeOf((*MockReaderWriter)(nil).SavePreChecks), ctx, preChecks)
}

// ToLowerCaseChannelSchemaTables mocks base method.
func (m *MockReaderWriter) ToLowerCaseChannelSchemaTables(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToLowerCaseChannelSchemaTables", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToLowerCaseChannelSchemaTables indicates an expected call of ToLowerCaseChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) ToLowerCaseChannelSchemaTables(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToLowerCaseChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).ToLowerCaseChannelSchemaTables), ctx, channelId, taskId)
}

// ToLowerCaseSchemaNameT mocks base method.
func (m *MockReaderWriter) ToLowerCaseSchemaNameT(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToLowerCaseSchemaNameT", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToLowerCaseSchemaNameT indicates an expected call of ToLowerCaseSchemaNameT.
func (mr *MockReaderWriterMockRecorder) ToLowerCaseSchemaNameT(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToLowerCaseSchemaNameT", reflect.TypeOf((*MockReaderWriter)(nil).ToLowerCaseSchemaNameT), ctx, channelId, taskId)
}

// ToLowerCaseTableNameT mocks base method.
func (m *MockReaderWriter) ToLowerCaseTableNameT(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToLowerCaseTableNameT", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToLowerCaseTableNameT indicates an expected call of ToLowerCaseTableNameT.
func (mr *MockReaderWriterMockRecorder) ToLowerCaseTableNameT(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToLowerCaseTableNameT", reflect.TypeOf((*MockReaderWriter)(nil).ToLowerCaseTableNameT), ctx, channelId, taskId)
}

// ToUpperCaseChannelSchemaTables mocks base method.
func (m *MockReaderWriter) ToUpperCaseChannelSchemaTables(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToUpperCaseChannelSchemaTables", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToUpperCaseChannelSchemaTables indicates an expected call of ToUpperCaseChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) ToUpperCaseChannelSchemaTables(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToUpperCaseChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).ToUpperCaseChannelSchemaTables), ctx, channelId, taskId)
}

// ToUpperCaseSchemaNameT mocks base method.
func (m *MockReaderWriter) ToUpperCaseSchemaNameT(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToUpperCaseSchemaNameT", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToUpperCaseSchemaNameT indicates an expected call of ToUpperCaseSchemaNameT.
func (mr *MockReaderWriterMockRecorder) ToUpperCaseSchemaNameT(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToUpperCaseSchemaNameT", reflect.TypeOf((*MockReaderWriter)(nil).ToUpperCaseSchemaNameT), ctx, channelId, taskId)
}

// ToUpperCaseTableNameT mocks base method.
func (m *MockReaderWriter) ToUpperCaseTableNameT(ctx context.Context, channelId, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToUpperCaseTableNameT", ctx, channelId, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// ToUpperCaseTableNameT indicates an expected call of ToUpperCaseTableNameT.
func (mr *MockReaderWriterMockRecorder) ToUpperCaseTableNameT(ctx, channelId, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToUpperCaseTableNameT", reflect.TypeOf((*MockReaderWriter)(nil).ToUpperCaseTableNameT), ctx, channelId, taskId)
}

// UnSelectChannelSchemaTables mocks base method.
func (m *MockReaderWriter) UnSelectChannelSchemaTables(ctx context.Context, ids []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnSelectChannelSchemaTables", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnSelectChannelSchemaTables indicates an expected call of UnSelectChannelSchemaTables.
func (mr *MockReaderWriterMockRecorder) UnSelectChannelSchemaTables(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnSelectChannelSchemaTables", reflect.TypeOf((*MockReaderWriter)(nil).UnSelectChannelSchemaTables), ctx, ids)
}

// UnSelectChannelSchemaTablesByTaskId mocks base method.
func (m *MockReaderWriter) UnSelectChannelSchemaTablesByTaskId(ctx context.Context, taskIds []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnSelectChannelSchemaTablesByTaskId", ctx, taskIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnSelectChannelSchemaTablesByTaskId indicates an expected call of UnSelectChannelSchemaTablesByTaskId.
func (mr *MockReaderWriterMockRecorder) UnSelectChannelSchemaTablesByTaskId(ctx, taskIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnSelectChannelSchemaTablesByTaskId", reflect.TypeOf((*MockReaderWriter)(nil).UnSelectChannelSchemaTablesByTaskId), ctx, taskIds)
}

// UpdateChannel mocks base method.
func (m *MockReaderWriter) UpdateChannel(ctx context.Context, channelInfo *channel.ChannelInformation) (*channel.ChannelInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannel", ctx, channelInfo)
	ret0, _ := ret[0].(*channel.ChannelInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChannel indicates an expected call of UpdateChannel.
func (mr *MockReaderWriterMockRecorder) UpdateChannel(ctx, channelInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannel", reflect.TypeOf((*MockReaderWriter)(nil).UpdateChannel), ctx, channelInfo)
}

// UpdateChannelSchemas mocks base method.
func (m *MockReaderWriter) UpdateChannelSchemas(ctx context.Context, channelSchemas []*channel.ChannelSchema) ([]*channel.ChannelSchema, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateChannelSchemas", ctx, channelSchemas)
	ret0, _ := ret[0].([]*channel.ChannelSchema)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateChannelSchemas indicates an expected call of UpdateChannelSchemas.
func (mr *MockReaderWriterMockRecorder) UpdateChannelSchemas(ctx, channelSchemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateChannelSchemas", reflect.TypeOf((*MockReaderWriter)(nil).UpdateChannelSchemas), ctx, channelSchemas)
}

// UpdatePreCheckInfo mocks base method.
func (m *MockReaderWriter) UpdatePreCheckInfo(ctx context.Context, preCheck *channel.PrecheckInfo) (*channel.PrecheckInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePreCheckInfo", ctx, preCheck)
	ret0, _ := ret[0].(*channel.PrecheckInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePreCheckInfo indicates an expected call of UpdatePreCheckInfo.
func (mr *MockReaderWriterMockRecorder) UpdatePreCheckInfo(ctx, preCheck interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePreCheckInfo", reflect.TypeOf((*MockReaderWriter)(nil).UpdatePreCheckInfo), ctx, preCheck)
}

// UpdateTargetChannelSchemaNameByPks mocks base method.
func (m *MockReaderWriter) UpdateTargetChannelSchemaNameByPks(ctx context.Context, channelSchtableIds []int, schemaNameT string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTargetChannelSchemaNameByPks", ctx, channelSchtableIds, schemaNameT)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTargetChannelSchemaNameByPks indicates an expected call of UpdateTargetChannelSchemaNameByPks.
func (mr *MockReaderWriterMockRecorder) UpdateTargetChannelSchemaNameByPks(ctx, channelSchtableIds, schemaNameT interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTargetChannelSchemaNameByPks", reflect.TypeOf((*MockReaderWriter)(nil).UpdateTargetChannelSchemaNameByPks), ctx, channelSchtableIds, schemaNameT)
}
