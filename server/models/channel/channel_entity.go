package channel

import (
	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type ChannelInformation struct {
	ChannelId                   int    `gorm:"primarykey"`
	ChannelName                 string `gorm:"type:varchar(100);comment:channel name"`
	ChannelType                 string `gorm:"type:varchar(10);comment:channel type O2T, M2T, T2T"`
	DatasourceIdS               int    `gorm:"comment:source datasource id"`
	DatasourceNameS             string `gorm:"type:varchar(100);comment:source datasource name"`
	DatasourceIdT               int    `gorm:"comment:target datasource id"`
	DatasourceNameT             string `gorm:"type:varchar(100);comment:target datasource name"`
	DatasourceCharsetS          string `gorm:"-"` // 源数据源字符集，通过关联查询获取
	DatasourceCharsetT          string `gorm:"-"` // 目标数据源字符集，通过关联查询获取
	ObjAssessment               string `gorm:"type:char(1);comment:object assessment Y/N"`
	ObjParser                   string `gorm:"type:char(1);comment:object parser Y/N"`
	SqlAssessment               string `gorm:"type:char(1);comment:application sql assessment Y/N"`
	Increment                   string `gorm:"type:char(1);comment:increment Y/N"`
	MigrateStructure            string `gorm:"type:char(1);comment:migrate structure Y/N"`
	MigrateFullData             string `gorm:"type:char(1);comment:migrate full data Y/N"`
	MigrateCsvData              string `gorm:"type:char(1);comment:migrate csv data Y/N"`
	DataCompare                 string `gorm:"type:char(1);comment:data compare Y/N"`
	FullInitialization          string `gorm:"type:char(1);comment:full initialization Y/N"`
	MigrateFullAndIncrementData string `gorm:"type:char(1);comment:migrate full and increment data Y/N"`
	TaskCreated                 string `gorm:"type:char(1);comment:default task whether created, Y/N"`
	ChannelMode                 string `gorm:"type:varchar(10);comment:channel mode"`
	SQLAnalyzerEnvStatus        bool   `gorm:"not null;comment:sql analyzer env status, Y/N;default:false"`
	DataCompareEnvStatus        bool   `gorm:"not null;comment:sync diff env status, Y/N;default:false"`
	*common.Entity
}

type ChannelSchema struct {
	ChannelSchemaId int    `gorm:"primarykey"`
	ChannelId       int    `gorm:"index;comment:channel id"`
	TaskId          int    `gorm:"comment:task id"`
	DbNameS         string `gorm:"type:varchar(200);comment:source database name"`
	SchemaNameS     string `gorm:"type:varchar(200);comment:source schema name"`
	DbNameT         string `gorm:"type:varchar(200);comment:target database name"`
	SchemaNameT     string `gorm:"type:varchar(200);comment:target schema name"`
	*common.Entity
}

type ChannelDatasource struct {
	ChannelDatasourceId int `gorm:"primarykey"`
	ChannelId           int `gorm:"index;comment:channel id"`
	DataSourceId        int `gorm:"comment:datasource id"`
	*common.Entity
}

type ChannelSchemaObject struct {
	ChannelObjectId   int    `gorm:"primarykey"`
	TaskId            int    `gorm:"comment:task id"`
	ChannelId         int    `gorm:"comment:channel id"`
	OnlyTableandindex string `gorm:"type:char(1);comment:only table and index, Y/N"`
	OnlyTable         string `gorm:"type:char(1);comment:only migrate table structure,ignore index, Y/N"`
	OnlyIndex         string `gorm:"type:char(1);comment:only migrate table index,ignore table structure, Y/N"`
	Partitiontable    string `gorm:"type:char(1);comment:partition table, Y/N"`
	AppendData        string `gorm:"type:char(1);comment:append data, Y/N"`
	ReloadData        string `gorm:"type:char(1);comment:reload data, Y/N"`
	LoadData          string `gorm:"type:char(1);comment:only load data, Y/N"`
	*common.Entity
}

type PrecheckInfo struct {
	PrecheckId      int    `gorm:"primarykey"`
	ChannelId       int    `gorm:"comment:channel id"`
	CheckObject     string `gorm:"type:varchar(1);comment:check object name, A(object compatible check priority),B(object migration priority),C(data migration priority),D(data compare priority),E(no index table),F(no pk/uk table),G(table have dependency relation)"`
	CheckStatus     string `gorm:"type:varchar(1);comment:check status, C:checking,P:pass,N:not pass"`
	CheckIgnore     string `gorm:"type:varchar(1);default:N;comment:is or not ignored, Y/N"`
	CheckResultInfo string `gorm:"type:varchar(1000);comment:check result info"`
	*common.Entity
}

type ChannelSchemaTable struct {
	ChannelSchtableId     int     `gorm:"primarykey"`
	ChannelId             int     `gorm:"index:idx_channel_id_task_type;comment:channel id"`
	TaskId                int     `gorm:"index:idx_task_id;comment:task id"`
	TaskType              int     `gorm:"index:idx_channel_id_task_type;comment:task type"`
	DbNameS               string  `gorm:"type:varchar(200);comment:source database name"`
	SchemaNameS           string  `gorm:"type:varchar(200);comment:source schema name"`
	TableNameS            string  `gorm:"type:varchar(200);comment:source table name"`
	PartitioningTypeS     string  `gorm:"type:varchar(200);comment:source table partition type"`
	PartitioningCountS    int     `gorm:"type:int;comment:source table partition count"`
	SubPartitioningTypeS  string  `gorm:"type:varchar(200);comment:source table sub partition type"`
	SubPartitioningCountS int     `gorm:"type:int;comment:source table sub partition count"`
	TableSizeM            float64 `gorm:"type:int;comment:source table size count(MB)"`
	UkS                   string  `gorm:"type:varchar(200);comment:source table uk"`
	PkS                   string  `gorm:"type:varchar(200);comment:source table pk"`
	PkT                   string  `gorm:"type:varchar(200);comment:target table pk"`
	DbNameT               string  `gorm:"type:varchar(200);comment:target database name"`
	SchemaNameT           string  `gorm:"type:varchar(200);comment:target schema name"`
	TableNameT            string  `gorm:"type:varchar(200);comment:target table name"`
	PartitioningTypeT     string  `gorm:"type:varchar(200);comment:target table partition type"`
	ClusterTypeT          string  `gorm:"type:varchar(200);comment:target table cluster type"`
	*common.Entity
}

type TableColumnCustomMapRule struct {
	ChannelId    int    `gorm:"primarykey;autoIncrement:false;not null;comment:channel id"`
	SchemaNameS  string `gorm:"primarykey;autoIncrement:false;type:varchar(100);not null;comment:source schema name"`
	TableNameS   string `gorm:"primarykey;autoIncrement:false;type:varchar(300);comment:source table name"`
	ColumnNameS  string `gorm:"primarykey;autoIncrement:false;type:varchar(200);comment:source column name"`
	ColumnDigest string `gorm:"primarykey;autoIncrement:false;type:char(32);comment:source column info digest"`

	ColumnID uint `gorm:"type:int;comment:column id"`

	ColumnNameT    string `gorm:"type:varchar(200);comment:target column name"`
	ColumnCommentS string `gorm:"type:varchar(1000);comment:source column comment content"`
	ColumnCommentT string `gorm:"type:varchar(1000);comment:target column comment content"`

	DataDefaultS   string `gorm:"type:varchar(200);comment:source column default value"`
	DataDefaultT   string `gorm:"type:varchar(200);comment:target column default value"`
	DataTypeS      string `gorm:"type:varchar(200);comment:source column data type"`
	DataTypeT      string `gorm:"type:varchar(200);comment:target column data type"`
	DataLengthS    uint   `gorm:"type:int;comment:source column data length"`
	DataLengthT    uint   `gorm:"type:int;comment:target column data length"`
	DataPrecisionS uint   `gorm:"type:int;comment:source column data precision"`
	DataPrecisionT uint   `gorm:"type:int;comment:target column data precision"`
	DataScaleS     uint   `gorm:"type:int;comment:source column data scale"`
	DataScaleT     uint   `gorm:"type:int;comment:target column data scale"`

	NullableS string `gorm:"type:varchar(10);comment:source column nullable,Y/N"`
	NullableT string `gorm:"type:varchar(10);comment:target column nullable,Y/N"`

	*common.Entity
}

func (i *TableColumnCustomMapRule) TableName() string {
	return "table_column_custom_map_rules"
}

func (i *TableColumnCustomSummary) TableName() string {
	return "table_column_custom_summary"
}

type TableColumnCustomSummary struct {
	ID                uint   `gorm:"primarykey"`
	ChannelId         int    `gorm:"not null;uniqueIndex:uqi_channel;comment:channel id"`
	Status            string `gorm:"type:varchar(100);comment:status"`
	TotalTableNum     int64  `gorm:"type:int;comment:total table number"`
	FinishedTableNum  int64  `gorm:"type:int;comment:finished table number"`
	FinishedColumnNum int64  `gorm:"type:int;comment:finished column number"`
	*common.Entity
}
