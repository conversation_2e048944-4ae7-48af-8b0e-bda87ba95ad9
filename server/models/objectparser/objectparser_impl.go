package objectparser

import (
	"context"
	"time"

	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ObjectParserMigrationReadWrite struct {
	dbCommon.GormDB
}

func NewObjectParserMigrationReadWrite(db *gorm.DB) *ObjectParserMigrationReadWrite {
	m := &ObjectParserMigrationReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

// BatchProcessingOptions holds configuration for batch processing operations
type BatchProcessingOptions struct {
	BatchSize      int
	TimeoutSeconds int
	LogProgress    bool
	RetryFailed    bool
	OperationName  string
	TaskId         int
	ChannelId      int
}

// ChunkedDeleteOptions holds configuration for chunked delete operations
type ChunkedDeleteOptions struct {
	ChunkSize           int
	DelayBetweenBatches time.Duration
	MaxRetries          int
	TimeoutSeconds      int
	LogProgress         bool
	OperationName       string
	TaskId              int
	ChannelId           int
}

// getBatchProcessingOptions retrieves batch processing configuration with default values to avoid import cycle
func (rw ObjectParserMigrationReadWrite) getBatchProcessingOptions(operationName string, taskId, channelId int) *BatchProcessingOptions {
	// Use default values to avoid circular dependency with config package
	// These values can be made configurable later through dependency injection
	options := &BatchProcessingOptions{
		BatchSize:      128, // Increased default from 20 to 100 for better performance
		TimeoutSeconds: 300, // 5 minutes default timeout
		LogProgress:    true,
		RetryFailed:    true,
		OperationName:  operationName,
		TaskId:         taskId,
		ChannelId:      channelId,
	}

	return options
}

// getChunkedDeleteOptions retrieves chunked delete configuration with default values
func (rw ObjectParserMigrationReadWrite) getChunkedDeleteOptions(operationName string, taskId, channelId int) *ChunkedDeleteOptions {
	options := &ChunkedDeleteOptions{
		ChunkSize:           1000,                  // Delete 1000 records per batch
		DelayBetweenBatches: 50 * time.Millisecond, // 50ms delay between batches
		MaxRetries:          3,                     // Maximum 3 retries per chunk
		TimeoutSeconds:      600,                   // 10 minutes total timeout
		LogProgress:         true,
		OperationName:       operationName,
		TaskId:              taskId,
		ChannelId:           channelId,
	}

	return options
}

// executeBatchOperation executes batch database operations with enhanced logging and error handling
func (rw ObjectParserMigrationReadWrite) executeBatchOperation(ctx context.Context, data interface{}, options *BatchProcessingOptions) error {
	// Create context with timeout if specified
	if options.TimeoutSeconds > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(options.TimeoutSeconds)*time.Second)
		defer cancel()
	}

	// Get slice length using reflection-like approach through interface{}
	dataLen := getSliceLength(data)
	if dataLen == 0 {
		if options.LogProgress {
			log.Infof("[%s] No data to process, taskId:%d, channelId:%d", options.OperationName, options.TaskId, options.ChannelId)
		}
		return nil
	}

	startTime := time.Now()
	totalBatches := (dataLen + options.BatchSize - 1) / options.BatchSize

	if options.LogProgress {
		log.Infof("[%s] Starting batch operation, taskId:%d, channelId:%d, totalItems:%d, batchSize:%d, totalBatches:%d",
			options.OperationName, options.TaskId, options.ChannelId, dataLen, options.BatchSize, totalBatches)
	}

	// Execute the batch operation
	err := rw.DB(ctx).CreateInBatches(data, options.BatchSize).Error
	if err != nil {
		log.Errorf("[%s] Batch operation failed, taskId:%d, channelId:%d, totalItems:%d, batchSize:%d, error:%v",
			options.OperationName, options.TaskId, options.ChannelId, dataLen, options.BatchSize, err)
		return err
	}

	duration := time.Since(startTime)
	itemsPerSecond := float64(dataLen) / duration.Seconds()

	if options.LogProgress {
		log.Infof("[%s] Batch operation completed successfully, taskId:%d, channelId:%d, totalItems:%d, duration:%v, itemsPerSec:%.2f",
			options.OperationName, options.TaskId, options.ChannelId, dataLen, duration, itemsPerSecond)
	}

	return nil
}

// executeChunkedDelete executes chunked delete operations to handle large datasets without long locks
func (rw ObjectParserMigrationReadWrite) executeChunkedDelete(ctx context.Context, model interface{}, condition string, args []interface{}, options *ChunkedDeleteOptions) error {
	// Create context with timeout if specified
	if options.TimeoutSeconds > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(options.TimeoutSeconds)*time.Second)
		defer cancel()
	}

	startTime := time.Now()
	totalDeleted := int64(0)
	batchCount := 0

	if options.LogProgress {
		log.Infof("[%s] Starting chunked delete operation, taskId:%d, channelId:%d, chunkSize:%d",
			options.OperationName, options.TaskId, options.ChannelId, options.ChunkSize)
	}

	for {
		// Check context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		batchCount++
		var deletedRows int64

		// Execute delete with retry logic
		err := rw.executeDeleteWithRetry(ctx, model, condition, args, options.ChunkSize, options.MaxRetries, &deletedRows)
		if err != nil {
			log.Errorf("[%s] Chunked delete failed, taskId:%d, channelId:%d, batch:%d, error:%v",
				options.OperationName, options.TaskId, options.ChannelId, batchCount, err)
			return err
		}

		totalDeleted += deletedRows

		// If no rows were deleted, we're done
		if deletedRows == 0 {
			break
		}

		// Log progress periodically
		if options.LogProgress && batchCount%10 == 0 {
			duration := time.Since(startTime)
			log.Infof("[%s] Chunked delete progress, taskId:%d, channelId:%d, batch:%d, totalDeleted:%d, duration:%v",
				options.OperationName, options.TaskId, options.ChannelId, batchCount, totalDeleted, duration)
		}

		// Add delay between batches to reduce database load
		if options.DelayBetweenBatches > 0 {
			time.Sleep(options.DelayBetweenBatches)
		}
	}

	duration := time.Since(startTime)
	deletedPerSecond := float64(totalDeleted) / duration.Seconds()

	if options.LogProgress {
		log.Infof("[%s] Chunked delete completed, taskId:%d, channelId:%d, totalDeleted:%d, batches:%d, duration:%v, deletedPerSec:%.2f",
			options.OperationName, options.TaskId, options.ChannelId, totalDeleted, batchCount, duration, deletedPerSecond)
	}

	return nil
}

// executeDeleteWithRetry executes a single delete operation with retry logic
func (rw ObjectParserMigrationReadWrite) executeDeleteWithRetry(ctx context.Context, model interface{}, condition string, args []interface{}, limit int, maxRetries int, deletedRows *int64) error {
	query := rw.DB(ctx).Unscoped().Where(condition, args...).Limit(limit)

	for attempt := 1; attempt <= maxRetries; attempt++ {
		result := query.Delete(model)
		if result.Error == nil {
			*deletedRows = result.RowsAffected
			return nil
		}

		// Log retry attempts
		if attempt < maxRetries {
			log.Warnf("Delete attempt %d failed, retrying: %v", attempt, result.Error)
			time.Sleep(time.Duration(attempt) * 100 * time.Millisecond) // Progressive backoff
		} else {
			return result.Error
		}
	}

	return nil
}

// getSliceLength returns the length of a slice interface{}
func getSliceLength(data interface{}) int {
	switch v := data.(type) {
	case []*OracleObjectDefinition:
		return len(v)
	case []*OracleDependency:
		return len(v)
	case []*OracleObjectDefinitionAnalyzeSummary:
		return len(v)
	case []*OracleObjectDefinitionAnalyzeDetail:
		return len(v)
	case []*OracleObjectDefinitionIncompatibleFeature:
		return len(v)
	case []*OracleTaskIncompatibleFeature:
		return len(v)
	case []*OracleToJavaResult:
		return len(v)
	case []*OracleToJavaHistoryResult:
		return len(v)
	default:
		// Fallback for unknown types
		return 0
	}
}

func (rw ObjectParserMigrationReadWrite) ListBasicOracleIncompatibleFeature(ctx context.Context) ([]*OracleIncompatibleFeature, error) {
	var keywordRules []*OracleIncompatibleFeature
	err := rw.DB(ctx).Find(&keywordRules).Error
	if err != nil {
		return nil, err
	}
	return keywordRules, nil
}

func (rw ObjectParserMigrationReadWrite) CreateOracleIncompatibleFeature(ctx context.Context, keywordRule *OracleIncompatibleFeature) (*OracleIncompatibleFeature, error) {
	err := rw.DB(ctx).Create(keywordRule).Error
	if err != nil {
		return nil, err
	}
	return keywordRule, nil
}

func (rw ObjectParserMigrationReadWrite) CreateOracleObjectDefinition(ctx context.Context, scs []*OracleObjectDefinition) ([]*OracleObjectDefinition, error) {
	if len(scs) == 0 {
		return scs, nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := scs[0].TaskId
	channelId := scs[0].ChannelId

	// Get batch processing options
	options := rw.getBatchProcessingOptions("CreateOracleObjectDefinition", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, scs, options)
	if err != nil {
		return nil, err
	}

	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) CreateOracleOracleDependency(ctx context.Context, scs []*OracleDependency) ([]*OracleDependency, error) {
	if len(scs) == 0 {
		return scs, nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := scs[0].TaskId
	channelId := scs[0].ChannelId

	// Get batch processing options
	options := rw.getBatchProcessingOptions("CreateOracleOracleDependency", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, scs, options)
	if err != nil {
		return nil, err
	}

	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) DeleteOracleObjectDefinitionByTaskId(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("DeleteOracleObjectDefinitionByTaskId", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleObjectDefinition{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) DeleteOracleDependencyByTaskId(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("DeleteOracleDependencyByTaskId", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleDependency{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) DeleteOracleObjectDefinitionAnalyzeDetailByTaskId(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("DeleteOracleObjectDefinitionAnalyzeDetailByTaskId", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleObjectDefinitionAnalyzeDetail{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) DeleteOracleObjectDefinitionAnalyzeSummaryByTaskId(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("DeleteOracleObjectDefinitionAnalyzeSummaryByTaskId", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleObjectDefinitionAnalyzeSummary{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) CreateOracleObjectDefinitionAnalyzeSummary(ctx context.Context, scs []*OracleObjectDefinitionAnalyzeSummary) ([]*OracleObjectDefinitionAnalyzeSummary, error) {
	if len(scs) == 0 {
		return scs, nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := scs[0].TaskId
	channelId := scs[0].ChannelId

	// Get batch processing options
	options := rw.getBatchProcessingOptions("CreateOracleObjectDefinitionAnalyzeSummary", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, scs, options)
	if err != nil {
		return nil, err
	}

	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) CreateOracleObjectDefinitionAnalyzeDetail(ctx context.Context, scs []*OracleObjectDefinitionAnalyzeDetail) ([]*OracleObjectDefinitionAnalyzeDetail, error) {
	if len(scs) == 0 {
		return scs, nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := scs[0].TaskId
	channelId := scs[0].ChannelId

	// Get batch processing options
	options := rw.getBatchProcessingOptions("CreateOracleObjectDefinitionAnalyzeDetail", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, scs, options)
	if err != nil {
		return nil, err
	}

	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) UpdateOracleObjectDefinitionAnalyzeDetail(ctx context.Context, sc *OracleObjectDefinitionAnalyzeDetail) (*OracleObjectDefinitionAnalyzeDetail, error) {
	err := rw.DB(ctx).Save(sc).Error
	if err != nil {
		return nil, err
	}
	return sc, nil
}

func (rw ObjectParserMigrationReadWrite) UpdateOracleObjectDefinitionAnalyzeSummary(ctx context.Context, sc *OracleObjectDefinitionAnalyzeSummary) (*OracleObjectDefinitionAnalyzeSummary, error) {
	err := rw.DB(ctx).Save(sc).Error
	if err != nil {
		return nil, err
	}
	return sc, nil
}

func (rw ObjectParserMigrationReadWrite) CreateOracleObjectDefinitionIncompatibleFeatures(ctx context.Context, scs []*OracleObjectDefinitionIncompatibleFeature) ([]*OracleObjectDefinitionIncompatibleFeature, error) {
	if len(scs) == 0 {
		return nil, nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := scs[0].TaskId
	channelId := scs[0].ChannelId

	// Get batch processing options
	options := rw.getBatchProcessingOptions("CreateOracleObjectDefinitionIncompatibleFeatures", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, scs, options)
	if err != nil {
		return nil, err
	}

	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) DeleteOracleObjectDefinitionIncompatibleFeatureByTaskId(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("DeleteOracleObjectDefinitionIncompatibleFeatureByTaskId", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleObjectDefinitionIncompatibleFeature{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) UpdateOracleObjectDefinitionAnalyzeSummaries(ctx context.Context, scs []*OracleObjectDefinitionAnalyzeSummary) ([]*OracleObjectDefinitionAnalyzeSummary, error) {
	err := rw.DB(ctx).Save(scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleDependencyByTaskId(ctx context.Context, taskId int) ([]*OracleDependency, error) {
	var scs []*OracleDependency
	err := rw.DB(ctx).Where("task_id = ?", taskId).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) GetOracleObjectDefinitionBySchemaObjectKeys(ctx context.Context, taskId int, schemaObjectKeys []string) ([]*OracleObjectDefinition, error) {
	var scs []*OracleObjectDefinition
	err := rw.DB(ctx).Where("task_id = ? AND schema_object_key IN ?", taskId, schemaObjectKeys).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleDependencyByUUIDs(ctx context.Context, taskId int, uuids []string) ([]*OracleDependency, error) {
	var scs []*OracleDependency
	err := rw.DB(ctx).Where("task_id = ? AND uuid IN ?", taskId, uuids).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleObjectDefinitionAnalyzeDetailByTaskId(ctx context.Context, taskId, page, pageSize int, schemaName, objectType, objectName string) ([]*OracleObjectDefinitionAnalyzeDetail, int64, error) {
	arr := make([]*OracleObjectDefinitionAnalyzeDetail, 0)
	total := int64(0)
	
	query := rw.DB(ctx).
		Model(&OracleObjectDefinitionAnalyzeDetail{}).
		Where("task_id = ?", taskId)
	
	// Apply optional filters
	if schemaName != "" {
		query = query.Where("schema_name = ?", schemaName)
	}
	if objectType != "" {
		query = query.Where("object_type = ?", objectType)
	}
	if objectName != "" {
		// Support fuzzy matching for object name with automatic % wrapping
		query = query.Where("object_name LIKE ?", "%"+objectName+"%")
	}
	
	err := query.
		Count(&total).
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Order("schema_name,object_type,object_name").
		Find(&arr).Error
	if err != nil {
		return nil, 0, err
	}
	return arr, total, nil
}

func (rw ObjectParserMigrationReadWrite) FetchHasTableReferenceDetailByTaskId(ctx context.Context, taskId int) ([]*OracleObjectDefinitionAnalyzeDetail, error) {
	var scs []*OracleObjectDefinitionAnalyzeDetail
	err := rw.DB(ctx).Where("task_id = ? AND length(table_reference_list) > 2", taskId).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) GetOracleObjectDefinitionAnalyzeDetailBySchemaObjectKeys(ctx context.Context, taskId int, schemaObjectKeys []string) ([]*OracleObjectDefinitionAnalyzeDetail, error) {
	var scs []*OracleObjectDefinitionAnalyzeDetail
	err := rw.DB(ctx).Where("task_id = ? AND schema_object_key IN ?", taskId, schemaObjectKeys).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) ListTaskOracleIncompatibleFeature(ctx context.Context, taskId int) ([]*OracleTaskIncompatibleFeature, error) {
	var keywordRules []*OracleTaskIncompatibleFeature
	err := rw.DB(ctx).Where("task_id = ?", taskId).Find(&keywordRules).Error
	if err != nil {
		return nil, err
	}
	return keywordRules, nil
}

func (rw ObjectParserMigrationReadWrite) SaveTaskOracleIncompatibleFeature(ctx context.Context, incompatibleFeature *OracleTaskIncompatibleFeature) error {
	err := rw.DB(ctx).Save(incompatibleFeature).Error
	if err != nil {
		return err
	}
	return nil
}

func (rw ObjectParserMigrationReadWrite) CreateTaskOracleIncompatibleFeatures(ctx context.Context, incompatibleFeatures []*OracleTaskIncompatibleFeature) error {
	if len(incompatibleFeatures) == 0 {
		return nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := incompatibleFeatures[0].TaskId
	channelId := 0 // This entity doesn't have ChannelId field, use 0 as default

	// Get batch processing options
	options := rw.getBatchProcessingOptions("CreateTaskOracleIncompatibleFeatures", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, incompatibleFeatures, options)
	if err != nil {
		return err
	}

	return nil
}

func (rw ObjectParserMigrationReadWrite) SaveBasicOracleIncompatibleFeature(ctx context.Context, incompatibleFeature *OracleIncompatibleFeature) error {
	err := rw.DB(ctx).Save(incompatibleFeature).Error
	if err != nil {
		return err
	}
	return nil
}

func (rw ObjectParserMigrationReadWrite) SaveOracleObjectTransformationPrompt(ctx context.Context, sc *OracleObjectTransformationPrompt) (*OracleObjectTransformationPrompt, error) {
	err := rw.DB(ctx).Save(sc).Error
	if err != nil {
		return nil, err
	}
	return sc, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleObjectTransformationPrompt(ctx context.Context, promptId uint) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleObjectTransformationPrompt{}, "id = ?", promptId).Error
	if err != nil {
		return err
	}
	return nil
}

func (rw ObjectParserMigrationReadWrite) SaveOracleObjectTaskObjectPromptRelation(ctx context.Context, sc *OracleObjectTaskObjectPromptRelation) (*OracleObjectTaskObjectPromptRelation, error) {
	// save or update
	err := rw.DB(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "task_id"}, {Name: "dependency_uuid"}},
		UpdateAll: true,
	}).Create(sc).Error
	if err != nil {
		return nil, err
	}
	return sc, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleObjectTaskObjectPromptRelation(ctx context.Context, taskId, relationId uint) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleObjectTaskObjectPromptRelation{}, "task_id = ? AND id = ?", taskId, relationId).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) ListOracleObjectTransformationPrompt(ctx context.Context) ([]*OracleObjectTransformationPrompt, error) {
	var scs []*OracleObjectTransformationPrompt
	err := rw.DB(ctx).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) GetOracleObjectTransformationPrompt(ctx context.Context, promptId uint) (*OracleObjectTransformationPrompt, error) {
	var sc OracleObjectTransformationPrompt
	err := rw.DB(ctx).Where("id = ?", promptId).First(&sc).Error
	if err != nil {
		return nil, err
	}
	return &sc, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleObjectTaskObjectPromptRelationByPromptId(ctx context.Context, promptId uint) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleObjectTaskObjectPromptRelation{}, "task_prompt_id = ?", promptId).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaResults(ctx context.Context, rs []*OracleToJavaResult) error {
	if len(rs) == 0 {
		return nil
	}

	// Extract taskId and channelId from first item for logging
	taskId := rs[0].TaskId
	channelId := rs[0].ChannelId

	// Get batch processing options
	options := rw.getBatchProcessingOptions("SaveOracleToJavaResults", taskId, channelId)

	// Execute batch operation with enhanced logging
	err := rw.executeBatchOperation(ctx, rs, options)
	if err != nil {
		return err
	}

	return nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaResultByTaskId(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("RemoveOracleToJavaResultByTaskId", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleToJavaResult{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaResultByTaskId(ctx context.Context, taskId int, page, pageSize int) ([]*OracleToJavaResult, int64, error) {
	arr := make([]*OracleToJavaResult, 0)
	total := int64(0)
	err := rw.DB(ctx).
		Model(&OracleToJavaResult{}).
		Where("task_id = ?", taskId).
		Count(&total).
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Order("id desc").
		Find(&arr).Error
	if err != nil {
		return nil, 0, err
	}
	return arr, total, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaHistoryResultByTaskId(ctx context.Context, taskId int, page, pageSize int) ([]*OracleToJavaHistoryResult, int64, error) {
	arr := make([]*OracleToJavaHistoryResult, 0)
	total := int64(0)
	err := rw.DB(ctx).
		Model(&OracleToJavaHistoryResult{}).
		Where("task_id = ?", taskId).
		Count(&total).
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Order("id desc").
		Find(&arr).Error
	if err != nil {
		return nil, 0, err
	}
	return arr, total, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleObjectTaskObjectPromptRelation(ctx context.Context, taskId uint, page, pageSize int) ([]*OracleObjectTaskObjectPromptRelation, int64, error) {
	arr := make([]*OracleObjectTaskObjectPromptRelation, 0)
	total := int64(0)
	err := rw.DB(ctx).
		Model(&OracleObjectTaskObjectPromptRelation{}).
		Where("task_id = ?", taskId).
		Count(&total).
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Order("created_at desc").
		Find(&arr).Error
	if err != nil {
		return nil, 0, err
	}
	return arr, total, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaLog(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("RemoveOracleToJavaLog", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleToJavaLog{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaSummary(ctx context.Context, taskId int) error {
	// Get chunked delete options
	options := rw.getChunkedDeleteOptions("RemoveOracleToJavaSummary", taskId, 0)

	// Execute chunked delete
	return rw.executeChunkedDelete(ctx, &OracleToJavaSummary{}, "task_id = ?", []interface{}{taskId}, options)
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaLog(ctx context.Context, log *OracleToJavaLog) (*OracleToJavaLog, error) {
	err := rw.DB(ctx).Create(log).Error
	if err != nil {
		return nil, err
	}
	return log, nil
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaSummary(ctx context.Context, summary *OracleToJavaSummary) (*OracleToJavaSummary, error) {
	err := rw.DB(ctx).Save(summary).Error
	if err != nil {
		return nil, err
	}
	return summary, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaLogByTaskId(ctx context.Context, taskId int) ([]*OracleToJavaLog, error) {
	var scs []*OracleToJavaLog
	err := rw.DB(ctx).Where("task_id = ?", taskId).Find(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) GetOracleToJavaSummary(ctx context.Context, taskId int) (*OracleToJavaSummary, error) {
	var sc OracleToJavaSummary
	err := rw.DB(ctx).Where("task_id = ?", taskId).First(&sc).Error
	if err != nil {
		return nil, err
	}
	return &sc, nil
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaResult(ctx context.Context, r *OracleToJavaResult) (*OracleToJavaResult, error) {
	err := rw.DB(ctx).Create(r).Error
	if err != nil {
		return nil, err
	}
	return r, nil
}

func (rw ObjectParserMigrationReadWrite) GetOracleObjectDefinitionAnalyzeSummary(ctx context.Context, taskId int) (*OracleObjectDefinitionAnalyzeSummary, error) {
	var sc OracleObjectDefinitionAnalyzeSummary
	err := rw.DB(ctx).Where("task_id = ?", taskId).First(&sc).Error
	if err != nil {
		return nil, err
	}
	return &sc, nil
}

func (rw ObjectParserMigrationReadWrite) ListObjectDependenciesByObjectUUID(ctx context.Context, taskId int, uuids []string) ([]*OracleDependencyVO, error) {
	var scs []*OracleDependencyVO
	var sql = `SELECT
	b.uuid AS source_uuid,
	a.*
FROM
	oracle_dependencies a
	JOIN oracle_dependencies b ON a.name = b.name
		AND a.type = b.type
		AND a.task_id = b.task_id
WHERE
	a.type IN('PROCEDURE', 'FUNCTION')
	AND a.referenced_type IN('PROCEDURE', 'FUNCTION')
	AND b.uuid in (?)
	AND b.task_id = ?;`
	err := rw.DB(ctx).Raw(sql, uuids, taskId).Scan(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleObjectFunctionAndProcedures(ctx context.Context, taskId int) ([]*OracleDependency, error) {
	var scs []*OracleDependency
	var sql = `SELECT
	a.*
FROM
	oracle_dependencies a
WHERE
	a.task_id = ?
	AND a.referenced_type IN ("FUNCTION", "PROCEDURE")
	AND (
	    a.type = "PACKAGE BODY" OR 
	     (a.type IN ("FUNCTION", "PROCEDURE") AND a.package_name = "")
	    )
ORDER BY
	a.referenced_package_name,
	a.name;`
	err := rw.DB(ctx).Raw(sql, taskId).Scan(&scs).Error
	if err != nil {
		return nil, err
	}
	return scs, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaHistoryResultByTaskId(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleToJavaHistoryResult{}, "task_id = ?", taskId).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) ArchiveOracleToJavaResultByTaskId(ctx context.Context, taskId int) error {
	arr := make([]*OracleToJavaResult, 0)
	err := rw.DB(ctx).
		Model(&OracleToJavaResult{}).
		Where("task_id = ?", taskId).
		Order("id desc").
		Find(&arr).Error
	if err != nil {
		return err
	}

	// Create history results
	historyArr := lo.Map(arr, func(item *OracleToJavaResult, _ int) *OracleToJavaHistoryResult {
		return &OracleToJavaHistoryResult{
			ChannelId:         item.ChannelId,
			TaskId:            item.TaskId,
			UUID:              item.UUID,
			SchemaName:        item.SchemaName,
			PackageName:       item.PackageName,
			ObjectName:        item.ObjectName,
			ObjectType:        item.ObjectType,
			Depth:             item.Depth,
			CodeFileName:      item.CodeFileName,
			ConvertedCode:     item.ConvertedCode,
			ConvertDuration:   item.ConvertDuration,
			ConvertStatus:     item.ConvertStatus,
			ConvertSQL:        item.ConvertSQL,
			ConvertTime:       item.ConvertTime,
			ConvertErrMessage: item.ConvertErrMessage,
			ConvertPrompts:    item.ConvertPrompts,
			IsMultiFile:       item.IsMultiFile,
			Entity:            item.Entity,
		}
	})

	// Use enhanced batch operation for archiving
	if len(historyArr) > 0 {
		// Extract taskId and channelId from first item
		taskIdForLog := historyArr[0].TaskId
		channelIdForLog := historyArr[0].ChannelId

		// Get batch processing options
		options := rw.getBatchProcessingOptions("ArchiveOracleToJavaResultByTaskId", taskIdForLog, channelIdForLog)

		// Execute batch operation with enhanced logging
		err = rw.executeBatchOperation(ctx, historyArr, options)
		if err != nil {
			return err
		}
	}

	// Archive associated files for multi-file results
	for i, originalResult := range arr {
		if originalResult.IsMultiFile {
			// Get the files for this result
			files, fileErr := rw.ListOracleToJavaFilesByTaskIdAndResultId(ctx, taskId, originalResult.ID)
			if fileErr != nil {
				continue // Skip file archiving for this result but continue with others
			}

			// Create history files mapped to the new history result ID
			historyFiles := lo.Map(files, func(file *OracleToJavaFile, _ int) *OracleToJavaHistoryFile {
				return &OracleToJavaHistoryFile{
					ResultId:    historyArr[i].ID,
					TaskId:      taskId,
					FilePath:    file.FilePath,
					FileName:    file.FileName,
					PackagePath: file.PackagePath,
					FileType:    file.FileType,
					FileContent: file.FileContent,
					FileOrder:   file.FileOrder,
					Entity:      file.Entity,
				}
			})

			// Save history files in batches
			if len(historyFiles) > 0 {
				_ = rw.SaveOracleToJavaHistoryFiles(ctx, historyFiles)
			}
		}
	}

	return nil
}

// Multi-file support implementations

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaFile(ctx context.Context, file *OracleToJavaFile) (*OracleToJavaFile, error) {
	err := rw.DB(ctx).Create(file).Error
	if err != nil {
		return nil, err
	}
	return file, nil
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaFiles(ctx context.Context, files []*OracleToJavaFile) error {
	if len(files) == 0 {
		return nil
	}
	err := rw.DB(ctx).CreateInBatches(files, 20).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaFilesByTaskId(ctx context.Context, taskId int) ([]*OracleToJavaFile, error) {
	var files []*OracleToJavaFile
	err := rw.DB(ctx).Where("task_id = ?", taskId).Order("result_id ASC, file_order ASC, id ASC").Find(&files).Error
	if err != nil {
		return nil, err
	}
	return files, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaFilesByTaskIdAndResultId(ctx context.Context, taskId int, resultId uint) ([]*OracleToJavaFile, error) {
	var files []*OracleToJavaFile
	err := rw.DB(ctx).Where("task_id = ? AND result_id = ?", taskId, resultId).Order("file_order ASC, id ASC").Find(&files).Error
	if err != nil {
		return nil, err
	}
	return files, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaFilesByResultId(ctx context.Context, resultId uint) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleToJavaFile{}, "result_id = ?", resultId).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaFilesByTaskId(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleToJavaFile{}, "task_id = ?", taskId).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaHistoryFile(ctx context.Context, file *OracleToJavaHistoryFile) (*OracleToJavaHistoryFile, error) {
	err := rw.DB(ctx).Create(file).Error
	if err != nil {
		return nil, err
	}
	return file, nil
}

func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaHistoryFiles(ctx context.Context, files []*OracleToJavaHistoryFile) error {
	if len(files) == 0 {
		return nil
	}
	err := rw.DB(ctx).CreateInBatches(files, 20).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaHistoryFilesByTaskId(ctx context.Context, taskId int) ([]*OracleToJavaHistoryFile, error) {
	var files []*OracleToJavaHistoryFile
	err := rw.DB(ctx).Where("task_id = ?", taskId).Order("result_id ASC, file_order ASC, id ASC").Find(&files).Error
	if err != nil {
		return nil, err
	}
	return files, nil
}

func (rw ObjectParserMigrationReadWrite) ListOracleToJavaHistoryFilesByTaskIdAndResultId(ctx context.Context, taskId int, resultId uint) ([]*OracleToJavaHistoryFile, error) {
	var files []*OracleToJavaHistoryFile
	err := rw.DB(ctx).Where("task_id = ? AND result_id = ?", taskId, resultId).Order("file_order ASC, id ASC").Find(&files).Error
	if err != nil {
		return nil, err
	}
	return files, nil
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaHistoryFilesByResultId(ctx context.Context, resultId uint) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleToJavaHistoryFile{}, "result_id = ?", resultId).Error
	return err
}

func (rw ObjectParserMigrationReadWrite) RemoveOracleToJavaHistoryFilesByTaskId(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Delete(&OracleToJavaHistoryFile{}, "task_id = ?", taskId).Error
	return err
}

// ObjectParserCfg 相关方法实现
func (rw ObjectParserMigrationReadWrite) CreateObjectParserCfgs(ctx context.Context, cfgs []*ObjectParserCfg) ([]*ObjectParserCfg, error) {
	if len(cfgs) == 0 {
		return nil, nil
	}
	err := rw.DB(ctx).CreateInBatches(cfgs, 20).Error
	if err != nil {
		return nil, err
	}
	return cfgs, nil
}

func (rw ObjectParserMigrationReadWrite) DeleteObjectParserCfgsByTaskId(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Delete(&ObjectParserCfg{}, "task_id = ?", taskId).Error
	if err != nil {
		return err
	}
	return nil
}

func (rw ObjectParserMigrationReadWrite) ListObjectParserCfgsByTaskId(ctx context.Context, taskId int) ([]*ObjectParserCfg, error) {
	var cfgs []*ObjectParserCfg
	err := rw.DB(ctx).Where("task_id = ?", taskId).Find(&cfgs).Error
	if err != nil {
		return nil, err
	}
	return cfgs, nil
}

func (rw ObjectParserMigrationReadWrite) ListObjectParserCfgWithFilter(ctx context.Context, taskId int, schemaName, objectType, objectName string, page, pageSize int) ([]*ObjectParserCfg, int64, error) {
	var cfgs []*ObjectParserCfg
	var total int64

	query := rw.DB(ctx).Model(&ObjectParserCfg{}).Where("task_id = ?", taskId)

	// Apply filters
	if schemaName != "" {
		// Exact match for schema name
		query = query.Where("schema_name = ?", schemaName)
	}
	if objectType != "" {
		query = query.Where("object_type = ?", objectType)
	}
	if objectName != "" {
		// Fuzzy match for object name
		query = query.Where("object_name LIKE ?", "%"+objectName+"%")
	}

	// Count total records
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination and stable ordering by ID
	offset := (page - 1) * pageSize
	err = query.Order("id ASC").Offset(offset).Limit(pageSize).Find(&cfgs).Error
	if err != nil {
		return nil, 0, err
	}

	return cfgs, total, nil
}

// 按taskId统计各个ObjectType的数量
func (rw ObjectParserMigrationReadWrite) CountObjectParserCfgObjectTypeByTaskId(ctx context.Context, taskId int) (map[string]int, error) {
	results := make(map[string]int)
	type resultRow struct {
		ObjectType string
		Count      int
	}
	var rows []resultRow
	err := rw.DB(ctx).Model(&ObjectParserCfg{}).
		Select("object_type, COUNT(*) as count").
		Where("task_id = ?", taskId).
		Group("object_type").
		Scan(&rows).Error
	if err != nil {
		return nil, err
	}
	for _, row := range rows {
		results[row.ObjectType] = row.Count
	}
	return results, nil
}
