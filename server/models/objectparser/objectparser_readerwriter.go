package objectparser

import (
	"context"
)

type ReaderWriter interface {
	CreateOracleIncompatibleFeature(ctx context.Context, keywordRule *OracleIncompatibleFeature) (*OracleIncompatibleFeature, error)
	ListBasicOracleIncompatibleFeature(ctx context.Context) ([]*OracleIncompatibleFeature, error)
	SaveBasicOracleIncompatibleFeature(ctx context.Context, incompatibleFeature *OracleIncompatibleFeature) error

	ListTaskOracleIncompatibleFeature(ctx context.Context, taskId int) ([]*OracleTaskIncompatibleFeature, error)
	SaveTaskOracleIncompatibleFeature(ctx context.Context, incompatibleFeature *OracleTaskIncompatibleFeature) error
	CreateTaskOracleIncompatibleFeatures(ctx context.Context, incompatibleFeatures []*OracleTaskIncompatibleFeature) error

	GetOracleObjectDefinitionBySchemaObjectKeys(ctx context.Context, taskId int, schemaObjectKeys []string) ([]*OracleObjectDefinition, error)
	CreateOracleObjectDefinition(ctx context.Context, scs []*OracleObjectDefinition) ([]*OracleObjectDefinition, error)
	DeleteOracleObjectDefinitionByTaskId(ctx context.Context, taskId int) error

	CreateOracleOracleDependency(ctx context.Context, scs []*OracleDependency) ([]*OracleDependency, error)
	DeleteOracleDependencyByTaskId(ctx context.Context, taskId int) error
	ListOracleDependencyByTaskId(ctx context.Context, taskId int) ([]*OracleDependency, error)
	ListOracleDependencyByUUIDs(ctx context.Context, taskId int, uuids []string) ([]*OracleDependency, error)
	ListObjectDependenciesByObjectUUID(ctx context.Context, taskId int, uuids []string) ([]*OracleDependencyVO, error)
	ListOracleObjectFunctionAndProcedures(ctx context.Context, taskId int) ([]*OracleDependency, error)

	CreateOracleObjectDefinitionAnalyzeDetail(ctx context.Context, scs []*OracleObjectDefinitionAnalyzeDetail) ([]*OracleObjectDefinitionAnalyzeDetail, error)
	UpdateOracleObjectDefinitionAnalyzeDetail(ctx context.Context, sc *OracleObjectDefinitionAnalyzeDetail) (*OracleObjectDefinitionAnalyzeDetail, error)
	DeleteOracleObjectDefinitionAnalyzeDetailByTaskId(ctx context.Context, taskId int) error
	ListOracleObjectDefinitionAnalyzeDetailByTaskId(ctx context.Context, taskId, page, pageSize int, schemaName, objectType, objectName string) ([]*OracleObjectDefinitionAnalyzeDetail, int64, error)
	FetchHasTableReferenceDetailByTaskId(ctx context.Context, taskId int) ([]*OracleObjectDefinitionAnalyzeDetail, error)
	GetOracleObjectDefinitionAnalyzeDetailBySchemaObjectKeys(ctx context.Context, taskId int, schemaObjectKeys []string) ([]*OracleObjectDefinitionAnalyzeDetail, error)

	CreateOracleObjectDefinitionAnalyzeSummary(ctx context.Context, scs []*OracleObjectDefinitionAnalyzeSummary) ([]*OracleObjectDefinitionAnalyzeSummary, error)
	UpdateOracleObjectDefinitionAnalyzeSummary(ctx context.Context, sc *OracleObjectDefinitionAnalyzeSummary) (*OracleObjectDefinitionAnalyzeSummary, error)
	GetOracleObjectDefinitionAnalyzeSummary(ctx context.Context, taskId int) (*OracleObjectDefinitionAnalyzeSummary, error)
	UpdateOracleObjectDefinitionAnalyzeSummaries(ctx context.Context, scs []*OracleObjectDefinitionAnalyzeSummary) ([]*OracleObjectDefinitionAnalyzeSummary, error)
	DeleteOracleObjectDefinitionAnalyzeSummaryByTaskId(ctx context.Context, taskId int) error

	CreateOracleObjectDefinitionIncompatibleFeatures(ctx context.Context, scs []*OracleObjectDefinitionIncompatibleFeature) ([]*OracleObjectDefinitionIncompatibleFeature, error)
	DeleteOracleObjectDefinitionIncompatibleFeatureByTaskId(ctx context.Context, taskId int) error

	ListOracleObjectTransformationPrompt(ctx context.Context) ([]*OracleObjectTransformationPrompt, error)
	GetOracleObjectTransformationPrompt(ctx context.Context, promptId uint) (*OracleObjectTransformationPrompt, error)
	SaveOracleObjectTransformationPrompt(ctx context.Context, sc *OracleObjectTransformationPrompt) (*OracleObjectTransformationPrompt, error)
	RemoveOracleObjectTransformationPrompt(ctx context.Context, promptId uint) error

	SaveOracleObjectTaskObjectPromptRelation(ctx context.Context, sc *OracleObjectTaskObjectPromptRelation) (*OracleObjectTaskObjectPromptRelation, error)
	RemoveOracleObjectTaskObjectPromptRelation(ctx context.Context, taskId, relationId uint) error
	RemoveOracleObjectTaskObjectPromptRelationByPromptId(ctx context.Context, promptId uint) error
	ListOracleObjectTaskObjectPromptRelation(ctx context.Context, taskId uint, page, pageSize int) ([]*OracleObjectTaskObjectPromptRelation, int64, error)

	SaveOracleToJavaResult(ctx context.Context, r *OracleToJavaResult) (*OracleToJavaResult, error)
	SaveOracleToJavaResults(ctx context.Context, rs []*OracleToJavaResult) error
	RemoveOracleToJavaResultByTaskId(ctx context.Context, taskId int) error
	RemoveOracleToJavaHistoryResultByTaskId(ctx context.Context, taskId int) error
	ArchiveOracleToJavaResultByTaskId(ctx context.Context, taskId int) error
	ListOracleToJavaResultByTaskId(ctx context.Context, taskId int, page, pageSize int) ([]*OracleToJavaResult, int64, error)
	ListOracleToJavaHistoryResultByTaskId(ctx context.Context, taskId int, page, pageSize int) ([]*OracleToJavaHistoryResult, int64, error)

	ListOracleToJavaLogByTaskId(ctx context.Context, taskId int) ([]*OracleToJavaLog, error)
	RemoveOracleToJavaLog(ctx context.Context, taskId int) error
	SaveOracleToJavaLog(ctx context.Context, log *OracleToJavaLog) (*OracleToJavaLog, error)

	RemoveOracleToJavaSummary(ctx context.Context, taskId int) error
	SaveOracleToJavaSummary(ctx context.Context, summary *OracleToJavaSummary) (*OracleToJavaSummary, error)
	GetOracleToJavaSummary(ctx context.Context, taskId int) (*OracleToJavaSummary, error)

	// Multi-file support methods
	SaveOracleToJavaFile(ctx context.Context, file *OracleToJavaFile) (*OracleToJavaFile, error)
	SaveOracleToJavaFiles(ctx context.Context, files []*OracleToJavaFile) error
	ListOracleToJavaFilesByTaskId(ctx context.Context, taskId int) ([]*OracleToJavaFile, error)
	ListOracleToJavaFilesByTaskIdAndResultId(ctx context.Context, taskId int, resultId uint) ([]*OracleToJavaFile, error)
	RemoveOracleToJavaFilesByResultId(ctx context.Context, resultId uint) error
	RemoveOracleToJavaFilesByTaskId(ctx context.Context, taskId int) error

	SaveOracleToJavaHistoryFile(ctx context.Context, file *OracleToJavaHistoryFile) (*OracleToJavaHistoryFile, error)
	SaveOracleToJavaHistoryFiles(ctx context.Context, files []*OracleToJavaHistoryFile) error
	ListOracleToJavaHistoryFilesByTaskId(ctx context.Context, taskId int) ([]*OracleToJavaHistoryFile, error)
	ListOracleToJavaHistoryFilesByTaskIdAndResultId(ctx context.Context, taskId int, resultId uint) ([]*OracleToJavaHistoryFile, error)
	RemoveOracleToJavaHistoryFilesByResultId(ctx context.Context, resultId uint) error
	RemoveOracleToJavaHistoryFilesByTaskId(ctx context.Context, taskId int) error

	// ObjectParserCfg 相关方法
	CreateObjectParserCfgs(ctx context.Context, cfgs []*ObjectParserCfg) ([]*ObjectParserCfg, error)
	DeleteObjectParserCfgsByTaskId(ctx context.Context, taskId int) error
	ListObjectParserCfgsByTaskId(ctx context.Context, taskId int) ([]*ObjectParserCfg, error)
	ListObjectParserCfgWithFilter(ctx context.Context, taskId int, schemaName, objectType, objectName string, page, pageSize int) ([]*ObjectParserCfg, int64, error)
	CountObjectParserCfgObjectTypeByTaskId(ctx context.Context, taskId int) (map[string]int, error)
}
