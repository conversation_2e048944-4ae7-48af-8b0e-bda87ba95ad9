package objectparser

import (
	"encoding/json"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type OracleIncompatibleFeature struct {
	ID           uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	FeatureType  string `gorm:"type:varchar(100);default:'';not null;comment:不兼容类型,DATABASE_LINK/RESERVED_WORD"`
	FeatureKey   string `gorm:"type:varchar(100);default:'';not null;comment:不兼容关键特征"`
	FeatureScore int    `gorm:"type:int;not null;default:0;comment:不兼容分数"`
	FeatureDesc  string `gorm:"default:'';comment:不兼容描述"`
	*common.Entity
}

type OracleTaskIncompatibleFeature struct {
	ID           uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	TaskId       int    `gorm:"not null;default:0;comment:task id;index:uqi_task_feature_type_key,unique"`
	FeatureType  string `gorm:"type:varchar(100);default:'';not null;comment:不兼容类型,DATABASE_LINK/RESERVED_WORD;index:uqi_task_feature_type_key,unique"`
	FeatureKey   string `gorm:"type:varchar(100);default:'';not null;comment:不兼容关键特征;index:uqi_task_feature_type_key,unique"`
	FeatureScore int    `gorm:"type:int;not null;default:0;comment:不兼容分数"`
	FeatureDesc  string `gorm:"default:'';comment:不兼容描述"`
	*common.Entity
}

type OracleObjectDefinition struct {
	ID              uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId       int    `gorm:"not null;default:0;comment:channel id"`
	TaskId          int    `gorm:"not null;default:0;comment:task id;index:uqi_schema_object,unique"`
	SchemaObjectKey string `gorm:"type:varchar(200);default:''not null;comment:schema object unique key;index:uqi_schema_object,unique"`

	SchemaName string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`
	ObjectName string `gorm:"type:varchar(100);default:'';not null;comment:对象名"`
	ObjectType string `gorm:"type:varchar(100);default:'';not null;comment:对象类型"`
	AllText    string `gorm:"type:longtext;comment:源码"`
	*common.Entity
}

type OracleObjectDefinitionAnalyzeSummary struct {
	ID        uint `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int  `gorm:"not null;default:0;comment:channel id"`
	TaskId    int  `gorm:"not null;default:0;comment:task id;index:idx_task"`

	DBName     string `gorm:"type:varchar(100);default:'';not null;comment:数据库名"`
	SchemaName string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`

	Status     string  `gorm:"type:varchar(100);default:'';not null;comment:状态"`
	TotalNum   int     `gorm:"type:int;default:0;comment:总数"`
	SuccessNum int     `gorm:"type:int;default:0;comment:成功数"`
	FailedNum  int     `gorm:"type:int;default:0;comment:失败数"`
	Duration   float64 `gorm:"type:int;comment:run duration, size: seconds"`

	*common.Entity
}
type OracleObjectDefinitionAnalyzeDetail struct {
	ID              uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId       int    `gorm:"not null;default:0;comment:channel id"`
	TaskId          int    `gorm:"not null;default:0;comment:task id;index:uqi_schema_object,unique"`
	SchemaObjectKey string `gorm:"type:varchar(200);default:''not null;comment:schema object unique key;index:uqi_schema_object,unique"`

	SchemaName   string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`
	ObjectName   string `gorm:"type:varchar(100);default:'';not null;comment:对象名"`
	ObjectType   string `gorm:"type:varchar(100);default:'';not null;comment:对象类型"`
	ObjectStatus string `gorm:"type:varchar(100);default:'';not null;comment:对象状态"`

	Status      string `gorm:"type:varchar(100);default:'';not null;comment:状态"`
	ErrorDetail string `gorm:"type:longtext;comment:错误详情"`

	ReservedWordCount               uint   `gorm:"type:int;default:0;comment:不兼容特性数-关键字"`
	DatabaseLinkCount               uint   `gorm:"type:int;default:0;comment:不兼容特性数-数据库链接"`
	IncompatibleFeatureScoreContext string `gorm:"type:text;comment:不兼容特性上下文"`

	MethodInvokeList   string `gorm:"type:longtext;comment:方法调用"`
	IdentifierList     string `gorm:"type:longtext;comment:标识符"`
	TableReferenceList string `gorm:"type:longtext;comment:表引用"`
	ReservedWordList   string `gorm:"type:longtext;comment:保留字"`
	PLSQLSegment       string `gorm:"type:longtext;comment:SQL片段"`
	*common.Entity
}

type OracleObjectTransformationPrompt struct {
	ID uint `gorm:"primary_key;autoIncrement;comment:PK"`

	PromptTitle     string `gorm:"column:prompt_title;type:varchar(100);default:'';not null;comment:提示标题;index:uqi_title,unique"`
	PromptCategory  string `gorm:"column:prompt_category;type:varchar(100);default:'';not null;comment:分类：CODE/TABLE"`
	TargetLanguage  string `gorm:"column:target_language;type:varchar(100);default:'';not null;comment:目标语言"`
	TargetFramework string `gorm:"column:target_framework;type:varchar(100);default:'';not null;comment:目标框架"`
	PromptText      string `gorm:"column:prompt_text;type:longtext;comment:提示内容"`
	IsDefault       bool   `gorm:"column:is_default;type:bool;default:false;comment:是否默认提示"`
	UsageExample    string `gorm:"column:usage_example;type:longtext;comment:使用示例"`

	*common.Entity
}

// TableName specifies the table name for OracleObjectTransformationPrompt
func (OracleObjectTransformationPrompt) TableName() string {
	return "oracle_object_transformation_prompts"
}

type OracleObjectTaskObjectPromptRelation struct {
	ID             uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId      uint   `gorm:"not null;default:0;comment:channel id"`
	TaskId         uint   `gorm:"not null;default:0;comment:task id;index:uqi_task_uuid,unique"`
	DependencyUUID string `gorm:"type:varchar(200);default:''not null;comment:dependency uuid;index:uqi_task_uuid,unique"`
	TaskPromptId   uint   `gorm:"not null;default:0;comment:task prompt id"`
	*common.Entity
}

type OracleObjectDefinitionIncompatibleFeature struct {
	ID        uint `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int  `gorm:"not null;default:0;comment:channel id"`
	TaskId    int  `gorm:"not null;default:0;comment:task id;index:idx_task"`

	DetailId   uint   `gorm:"not null;default:0;comment:detail id;index:idx_detail"`
	SchemaName string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`
	ObjectName string `gorm:"type:varchar(100);default:'';not null;comment:对象名"`
	ObjectType string `gorm:"type:varchar(100);default:'';not null;comment:对象类型"`

	IncompatibleType  string `gorm:"type:varchar(100);default:'';not null;comment:不兼容类型"`
	IncompatibleKey   string `gorm:"type:varchar(100);default:'';not null;comment:不兼容关键特征"`
	IncompatibleValue string `gorm:"type:longtext;comment:实际出现的值"`
	IncompatibleDesc  string `gorm:"type:longtext;comment:不兼容描述"`
	OccurCount        int    `gorm:"type:int;default:0;comment:出现次数"`
	*common.Entity
}

type OracleDependency struct {
	ID        uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int    `gorm:"not null;default:0;comment:channel id"`
	TaskId    int    `gorm:"not null;default:0;comment:task id;index:idx_task"`
	UUID      string `gorm:"type:varchar(100);default:'';not null;comment:UUID;index:uqi_schema_object,unique"`

	SchemaName            string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`
	PackageName           string `gorm:"type:varchar(100);default:'';not null;comment:包名"`
	OwnerName             string `gorm:"type:varchar(100);default:'';not null;comment:OWNER"`
	Name                  string `gorm:"type:varchar(100);default:'';not null;comment:对象名"`
	Type                  string `gorm:"type:varchar(100);default:'';not null;comment:对象类型"`
	Status                string `gorm:"type:varchar(100);default:'';not null;comment:状态"`
	ReferencedPackageName string `gorm:"type:varchar(100);default:'';not null;comment:引用包名"`
	ReferencedOwner       string `gorm:"type:varchar(100);default:'';not null;comment:引用OWNER"`
	ReferencedName        string `gorm:"type:varchar(100);default:'';not null;comment:引用对象名"`
	ReferencedType        string `gorm:"type:varchar(100);default:'';not null;comment:引用对象类型"`
	ReferencedLinkName    string `gorm:"type:varchar(100);default:'';not null;comment:引用链接名"`
	DependencyType        string `gorm:"type:varchar(100);default:'';not null;comment:依赖类型"`

	IsFromPackageBody bool `gorm:"type:bool;default:false;comment:是否来自包体"`

	*common.Entity
}

type OracleDependencyVO struct {
	SourceUUID string `gorm:"type:varchar(100);default:'';not null;comment:源对象UUID"`
	ID         uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId  int    `gorm:"not null;default:0;comment:channel id"`
	TaskId     int    `gorm:"not null;default:0;comment:task id;index:idx_task"`
	UUID       string `gorm:"type:varchar(100);default:'';not null;comment:UUID;index:uqi_schema_object,unique"`

	SchemaName            string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`
	PackageName           string `gorm:"type:varchar(100);default:'';not null;comment:包名"`
	OwnerName             string `gorm:"type:varchar(100);default:'';not null;comment:OWNER"`
	Name                  string `gorm:"type:varchar(100);default:'';not null;comment:对象名"`
	Type                  string `gorm:"type:varchar(100);default:'';not null;comment:对象类型"`
	Status                string `gorm:"type:varchar(100);default:'';not null;comment:状态"`
	ReferencedPackageName string `gorm:"type:varchar(100);default:'';not null;comment:引用包名"`
	ReferencedOwner       string `gorm:"type:varchar(100);default:'';not null;comment:引用OWNER"`
	ReferencedName        string `gorm:"type:varchar(100);default:'';not null;comment:引用对象名"`
	ReferencedType        string `gorm:"type:varchar(100);default:'';not null;comment:引用对象类型"`
	ReferencedLinkName    string `gorm:"type:varchar(100);default:'';not null;comment:引用链接名"`
	DependencyType        string `gorm:"type:varchar(100);default:'';not null;comment:依赖类型"`

	IsFromPackageBody bool `gorm:"type:bool;default:false;comment:是否来自包体"`

	*common.Entity
}

func (i *OracleDependency) String() string {
	bytes, _ := json.Marshal(i)
	return string(bytes)
}

func (i *OracleDependency) UniqueKey() string {
	return i.OwnerName + "." + i.Type + "." + i.Name
}

func (i *OracleDependency) UniqueKeyWithPackageName() string {
	return i.SchemaName + "." + i.PackageName + "." + i.Type + "." + i.Name
}

func (i *OracleDependency) IsReferencedNodeShouldIgnored() bool {
	if i.ReferencedType == "TMS_FLAG" || i.ReferencedName == "TMS_FLAG" || i.ReferencedOwner == "TMS_FLAG" {
		return false
	}
	if i.ReferencedName == "SYS_STUB_FOR_PURITY_ANALYSIS" || i.ReferencedName == "DUAL" || i.ReferencedName == "DBMS_OUTPUT" {
		return true
	}
	if i.ReferencedOwner == "SYS" && i.ReferencedName == "STANDARD" {
		return true
	}
	if i.ReferencedType == "SYNONYM" || i.ReferencedType == "TABLE" {
		return true
	}
	return false
}

func (i *OracleDependency) ReferencedUniqueKey() string {
	return i.ReferencedOwner + "." + i.ReferencedType + "." + i.ReferencedName
}

// ReferencedSchemaAsOwnerUniqueKey 为了适配PACKAGE BODY中调用了普通的存储过程/函数
func (i *OracleDependency) ReferencedSchemaAsOwnerUniqueKey() string {
	return i.SchemaName + "." + i.ReferencedType + "." + i.ReferencedName
}
func (i *OracleDependency) GetSchemaObjectKey() string {
	if i.IsFromPackageBody {
		return i.SchemaName + ".PACKAGE BODY." + i.PackageName
	} else {
		return i.SchemaName + "." + i.Type + "." + i.Name
	}
}
func (i *OracleDependency) GetSchemaName() string {
	return i.SchemaName
}

func (i *OracleDependency) GetPackageName() string {
	return i.PackageName
}

func (i *OracleDependency) GetUUID() string {
	return i.UUID
}

func (i *OracleDependency) GetOwnerName() string {
	return i.OwnerName
}

func (i *OracleDependency) GetName() string {
	return i.Name
}

func (i *OracleDependency) GetType() string {
	return i.Type
}

func (i *OracleDependency) GetDependencyType() string {
	return i.DependencyType
}

func (i *OracleDependency) GetIsFromPackageBody() bool {
	return i.IsFromPackageBody
}

func (i *OracleDependency) GetReferencedPackageName() string {
	return i.ReferencedPackageName
}

func (i *OracleDependency) GetReferencedOwner() string {
	return i.ReferencedOwner
}

func (i *OracleDependency) GetReferencedName() string {
	return i.ReferencedName
}

func (i *OracleDependency) GetReferencedType() string {
	return i.ReferencedType
}

type OracleToJavaLog struct {
	ID        uint `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int  `gorm:"not null;default:0;comment:channel id;index:idx_channel"`
	TaskId    int  `gorm:"not null;default:0;comment:task id;index:idx_task"`

	LogMessage string `gorm:"type:longtext;comment:日志信息"`
	LogLevel   string `gorm:"type:varchar(100);default:'';not null;comment:日志级别"`
	*common.Entity
}

type OracleToJavaSummary struct {
	ID        uint `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int  `gorm:"not null;default:0;comment:channel id;index:idx_channel"`
	TaskId    int  `gorm:"not null;default:0;comment:task id;index:idx_task_uuid,unique"`

	TotalObjectNum   int `gorm:"type:int;default:0;comment:总对象数"`
	SuccessObjectNum int `gorm:"type:int;default:0;comment:成功对象数"`
	FailedObjectNum  int `gorm:"type:int;default:0;comment:失败对象数"`
	*common.Entity
}

type OracleToJavaResult struct {
	ID        uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int    `gorm:"not null;default:0;comment:channel id;index:idx_channel"`
	TaskId    int    `gorm:"not null;default:0;comment:task id;index:idx_task_uuid,unique"`
	UUID      string `gorm:"type:varchar(200);default:'';not null;comment:UUID;index:idx_task_uuid,unique"`

	SchemaName        string    `gorm:"type:varchar(200);default:'';not null;comment:SCHEMA名"`
	PackageName       string    `gorm:"type:varchar(200);default:'';not null;comment:包名"`
	ObjectName        string    `gorm:"type:varchar(200);default:'';not null;comment:对象名"`
	ObjectType        string    `gorm:"type:varchar(200);default:'';not null;comment:对象类型"`
	Depth             uint      `gorm:"type:int;default:0;comment:深度"`
	CodeFileName      string    `gorm:"type:varchar(200);default:'';not null;comment:代码文件名"`
	ConvertedCode     string    `gorm:"type:longtext;comment:转换后的代码"`
	ConvertDuration   float64   `gorm:"type:float;default:0;comment:转换时间"`
	ConvertStatus     string    `gorm:"type:varchar(100);default:'';not null;comment:状态"`
	ConvertSQL        string    `gorm:"type:longtext;;not null;comment:转换的SQL文本"`
	ConvertTime       time.Time `gorm:"not null;comment:转换时间"`
	ConvertErrMessage string    `gorm:"type:longtext;comment:转换错误信息"`
	ConvertPrompts    string    `gorm:"type:longtext;comment:转换提示信息"`
	IsMultiFile       bool      `gorm:"type:tinyint(1);default:0;comment:是否包含多个文件"`
	*common.Entity
}
type OracleToJavaHistoryResult struct {
	ID        uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId int    `gorm:"not null;default:0;comment:channel id;index:idx_channel"`
	TaskId    int    `gorm:"not null;default:0;comment:task id;index:idx_task_uuid,unique"`
	UUID      string `gorm:"type:varchar(200);default:'';not null;comment:UUID;index:idx_task_uuid,unique"`

	SchemaName        string    `gorm:"type:varchar(200);default:'';not null;comment:SCHEMA名"`
	PackageName       string    `gorm:"type:varchar(200);default:'';not null;comment:包名"`
	ObjectName        string    `gorm:"type:varchar(200);default:'';not null;comment:对象名"`
	ObjectType        string    `gorm:"type:varchar(200);default:'';not null;comment:对象类型"`
	Depth             uint      `gorm:"type:int;default:0;comment:深度"`
	CodeFileName      string    `gorm:"type:varchar(200);default:'';not null;comment:代码文件名"`
	ConvertedCode     string    `gorm:"type:longtext;comment:转换后的代码"`
	ConvertDuration   float64   `gorm:"type:float;default:0;comment:转换时间"`
	ConvertStatus     string    `gorm:"type:varchar(100);default:'';not null;comment:状态"`
	ConvertSQL        string    `gorm:"type:longtext;;not null;comment:转换的SQL文本"`
	ConvertTime       time.Time `gorm:"not null;comment:转换时间"`
	ConvertErrMessage string    `gorm:"type:longtext;comment:转换错误信息"`
	ConvertPrompts    string    `gorm:"type:longtext;comment:转换提示信息"`
	IsMultiFile       bool      `gorm:"type:tinyint(1);default:0;comment:是否包含多个文件"`
	*common.Entity
}

// OracleToJavaFile represents a single file within a multi-file conversion result
type OracleToJavaFile struct {
	ID          uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	TaskId      int    `gorm:"not null;default:0;comment:task id;index:idx_result_id"`
	ResultId    uint   `gorm:"not null;comment:关联的转换结果ID;index:idx_result_id"`
	FilePath    string `gorm:"type:varchar(500);not null;comment:文件路径 com/tms/entity/UserEntity.java;index:idx_file_path"`
	FileName    string `gorm:"type:varchar(200);not null;comment:文件名 UserEntity.java"`
	PackagePath string `gorm:"type:varchar(400);comment:包路径 com.tms.entity;index:idx_package_path"`
	FileType    string `gorm:"type:varchar(50);not null;default:'java';comment:文件类型 java xml properties;index:idx_file_type"`
	FileContent string `gorm:"type:longtext;not null;comment:完整文件内容"`
	FileOrder   int    `gorm:"type:int;not null;default:0;comment:文件显示顺序"`
	*common.Entity
}

// OracleToJavaHistoryFile represents a single file within a multi-file history conversion result
type OracleToJavaHistoryFile struct {
	ID          uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	TaskId      int    `gorm:"not null;default:0;comment:task id;index:idx_result_id"`
	ResultId    uint   `gorm:"not null;comment:关联的历史转换结果ID;index:idx_result_id"`
	FilePath    string `gorm:"type:varchar(500);not null;comment:文件路径 com/tms/entity/UserEntity.java;index:idx_file_path"`
	FileName    string `gorm:"type:varchar(200);not null;comment:文件名 UserEntity.java"`
	PackagePath string `gorm:"type:varchar(400);comment:包路径 com.tms.entity;index:idx_package_path"`
	FileType    string `gorm:"type:varchar(50);not null;default:'java';comment:文件类型 java xml properties;index:idx_file_type"`
	FileContent string `gorm:"type:longtext;not null;comment:完整文件内容"`
	FileOrder   int    `gorm:"type:int;not null;default:0;comment:文件显示顺序"`
	*common.Entity
}

// ObjectParserCfg 对象解析器配置表
type ObjectParserCfg struct {
	ID         uint   `gorm:"primary_key;autoIncrement;comment:PK"`
	ChannelId  int    `gorm:"not null;default:0;comment:channel id;index:idx_channel_task"`
	TaskId     int    `gorm:"not null;default:0;comment:task id;index:idx_channel_task"`
	SchemaName string `gorm:"type:varchar(100);default:'';not null;comment:SCHEMA名"`
	ObjectType string `gorm:"type:varchar(100);default:'';not null;comment:对象类型"`
	ObjectName string `gorm:"type:varchar(100);default:'';not null;comment:对象名"`
	*common.Entity
}
