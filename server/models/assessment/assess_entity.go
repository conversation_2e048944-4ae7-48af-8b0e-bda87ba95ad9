/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package assessment

import (
	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type ObjectAssessDetail struct {
	ObjAssessDetailID int    `gorm:"primarykey;comment:object assess detail id"`
	ChannelId         int    `gorm:"index:idx_channel_id;comment:channel id"`
	TaskId            int    `gorm:"type:int;comment:task id"`
	DbName            string `gorm:"type:varchar(100);comment:db name"`
	SchemaName        string `gorm:"type:varchar(200);comment:schema name"`
	AssessType        string `gorm:"type:varchar(100);comment:object type, eg: table/index/partition"`
	AssessName        string `gorm:"type:varchar(100);comment:assess name"`
	AssessTotal       int    `gorm:"type:int;comment:assess total result"`
	Compatible        int    `gorm:"type:int;comment:assess compatible result"`
	Incompatible      int    `gorm:"type:int;comment:assess incompatible result"`
	Convertible       int    `gorm:"type:int;comment:assess convertible result"`
	Inconvertible     int    `gorm:"type:int;comment:assess inconvertible result"`
	Duration          string `gorm:"type:varchar(100);comment:assess duration"`
	Detail            string `gorm:"type:text;comment:assess detail"`
	StructName        string `gorm:"type:text;comment:struct name"`
	*common.Entity
}

type HtmlTemplateDetail struct {
	TemplateId  int    `gorm:"primarykey"`
	HtmlName    string `gorm:"type:varchar(200)"`
	HtmlVersion string `gorm:"type:varchar(20)"`
	HtmlStatus  string `gorm:"type:varchar(20)"`
	Detail      string `gorm:"type:text"`
	*common.Entity
}

type HtmlRptCfg struct {
	RptId         int    `gorm:"primarykey"`
	RptName       string `gorm:"type:varchar(100)"`
	RangeListName string `gorm:"type:varchar(100)"`
	HeaderNames   string `gorm:"type:varchar(1000)"`
	FieldNames    string `gorm:"type:varchar(1000)"`
	FilePath      string `gorm:"type:varchar(100)"`
	FileName      string `gorm:"type:varchar(100)"`
	ParentRptId   int    `gorm:"type:int"`
	DisplayOrder  int    `gorm:"type:int"`
	DisplayType   int    `gorm:"type:int"`
	RptType       int    `gorm:"type:int"`
	*common.Entity
}

type OraDbaObject struct {
	OraobjId      int    `gorm:"type:int;primarykey" json:"oraobjId"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner         string `gorm:"type:varchar(30);" json:"owner"`
	ObjectName    string `gorm:"type:varchar(100);" json:"objectName"`
	SubobjectName string `gorm:"type:varchar(100);" json:"subobjectName"`
	ObjectType    string `gorm:"type:varchar(30);" json:"objectType"`
	Status        string `gorm:"type:varchar(30);" json:"status"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraDbaConstraint struct {
	OraconstraintId int    `gorm:"type:int;primarykey"`
	ChannelId       int    `gorm:"type:int" json:"channelId"`
	TaskId          int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner           string `gorm:"type:varchar(30);" json:"owner"`
	TableName       string `gorm:"type:varchar(100);" json:"tableName"`
	ConstraintName  string `gorm:"type:varchar(100);" json:"constraintName"`
	ConstraintType  string `gorm:"type:varchar(30);" json:"constraintType"`
	IsCompatible    string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible   string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraDbaIndex struct {
	OraIndexId    int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	TableOwner    string `gorm:"type:varchar(30);" json:"tableOwner"`
	TableName     string `gorm:"type:varchar(100);" json:"tableName"`
	IndexName     string `gorm:"type:varchar(100);" json:"indexName"`
	IndexType     string `gorm:"type:varchar(30);" json:"indexType"`
	Partitioned   string `gorm:"type:varchar(10);" json:"partitioned"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraDbaSequence struct {
	OraIndexId    int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	SequenceOwner string `gorm:"type:varchar(30);" json:"sequenceOwner"`
	SequenceName  string `gorm:"type:varchar(100);" json:"sequenceName"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraDbaTabColumns struct {
	DataId       int    `gorm:"type:int;primarykey"`
	ChannelId    int    `gorm:"type:int" json:"channelId"`
	TaskId       int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner        string `gorm:"type:varchar(30);" json:"OwnerName"`
	TableName    string `gorm:"type:varchar(100);" json:"tableName"`
	ColumnName   string `gorm:"type:varchar(100);" json:"columnName"`
	DataType     string `gorm:"type:varchar(100);" json:"dataType"`
	DataTypeT    string `gorm:"type:varchar(100);" json:"dataTypeT"`
	ColumnId     int    `gorm:"type:int;" json:"columnId"`
	DataLength   int    `gorm:"type:int;" json:"dataLength"`
	DataDesc     string `gorm:"type:varchar(100);" json:"dataDesc"`
	IsEquivalent string `gorm:"type:varchar(10);" json:"isEquivalent"`
	*common.Entity
}

type OraDbaTabColumnsDefault struct {
	DataId       int    `gorm:"type:int;primarykey"`
	ChannelId    int    `gorm:"type:int" json:"channelId"`
	TaskId       int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner        string `gorm:"type:varchar(30);" json:"OwnerName"`
	TableName    string `gorm:"type:varchar(100);" json:"tableName"`
	ColumnName   string `gorm:"type:varchar(100);" json:"columnName"`
	DataType     string `gorm:"type:varchar(100);" json:"dataType"`
	DataDefault  string `gorm:"type:varchar(100);" json:"dataDefault"`
	DataDefaultT string `gorm:"type:varchar(100);" json:"dataDefaultT"`
	IsEquivalent string `gorm:"type:varchar(10);" json:"isEquivalent"`
	*common.Entity
}

type OraAllPartTables struct {
	DataId              int    `gorm:"type:int;primarykey"`
	ChannelId           int    `gorm:"type:int" json:"channelId"`
	TaskId              int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner               string `gorm:"type:varchar(30);" json:"owner"`
	TableName           string `gorm:"type:varchar(100);" json:"tableName"`
	PartitioningType    string `gorm:"type:varchar(100);" json:"partitioningType"`
	SubpartitioningType string `gorm:"type:varchar(100);" json:"subpartitioningType"`
	PartitionCount      string `gorm:"type:varchar(100);" json:"partitionCount"`
	IsCompatible        string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible       string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraDbaTables struct {
	DataId        int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner         string `gorm:"type:varchar(30);" json:"owner"`
	TableName     string `gorm:"type:varchar(100);" json:"tableName"`
	Temporary     string `gorm:"type:varchar(100);" json:"temporary"`
	Duration      string `gorm:"type:varchar(100);" json:"duration"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraAllObjects struct {
	DataId        int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner         string `gorm:"type:varchar(30);" json:"owner"`
	ObjectName    string `gorm:"type:varchar(100);" json:"objectName"`
	ObjectType    string `gorm:"type:varchar(100);" json:"objectType"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraLobs struct {
	DataId        int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner         string `gorm:"type:varchar(30);" json:"owner"`
	ObjectName    string `gorm:"type:varchar(100);" json:"objectName"`
	ObjectType    string `gorm:"type:varchar(100);" json:"objectType"`
	TableName     string `gorm:"type:varchar(100);" json:"tableName"`
	ColumnName    string `gorm:"type:varchar(100);" json:"columnName"`
	DataType      string `gorm:"type:varchar(100);" json:"dataType"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraTableTypeCompatibles struct {
	DataId        int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner         string `gorm:"type:varchar(30);" json:"owner"`
	TableName     string `gorm:"type:varchar(100);" json:"tableName"`
	TableType     string `gorm:"type:varchar(100);" json:"tableType"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraDbaViews struct {
	DataId        int    `gorm:"type:int;primarykey"`
	ChannelId     int    `gorm:"type:int" json:"channelId"`
	TaskId        int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner         string `gorm:"type:varchar(30);" json:"owner"`
	ViewType      string `gorm:"type:varchar(100);" json:"viewType"`
	ViewTypeOwner string `gorm:"type:varchar(100);" json:"viewTypeOwner"`
	ViewName      string `gorm:"type:varchar(100);" json:"viewName"`
	IsCompatible  string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}

type OraSubPartitioning struct {
	DataId              int    `gorm:"type:int;primarykey"`
	ChannelId           int    `gorm:"type:int" json:"channelId"`
	TaskId              int    `gorm:"type:int;index:idx_taskid;" json:"taskId"`
	Owner               string `gorm:"type:varchar(30);" json:"owner"`
	TableName           string `gorm:"type:varchar(100);" json:"tableName"`
	SubPartitioningType string `gorm:"type:varchar(100);" json:"subPartitioningType"`
	IsCompatible        string `gorm:"type:varchar(10);" json:"isCompatible"`
	IsConvertible       string `gorm:"type:varchar(10);" json:"isConvertible"`
	*common.Entity
}
