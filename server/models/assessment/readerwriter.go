/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package assessment

import (
	"context"
	"database/sql"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
)

type AssessReaderWriter interface {
	BatchGetObjectAssess(ctx context.Context, channelID int) ([]*ObjectAssessDetail, error)
	BatchQueryObjectAssess(ctx context.Context, objAssessDetail *ObjectAssessDetail) ([]*ObjectAssessDetail, error)
	BatchLimitObjectAssess(ctx context.Context, objAssessDetail *ObjectAssessDetail, limit int) ([]*ObjectAssessDetail, error)
	BatchCreateObjectAssess(ctx context.Context, objAssessDetails []*ObjectAssessDetail) error
	CreateObjectAssess(ctx context.Context, objAssessDetail *ObjectAssessDetail) error
	UpdateObjectAssess(ctx context.Context, objAssessDetails *ObjectAssessDetail) error
	CreateOraDbaObject(ctx context.Context, oraDbaObj []*OraDbaObject) error
	DeleteOraDbaObject(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaObject(ctx context.Context, channelId int, taskId int, schema string, constraintType string, isCompatible string, isConvertible string) error

	CreateOraDbaConstraint(ctx context.Context, oraDbaConstraint []*OraDbaConstraint) error
	DeleteOraDbaConstraint(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaConstraint(ctx context.Context, channelId int, taskId int, schema string, constraintType string, isCompatible string, isConvertible string) error
	GetOraDbaConstraintList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaConstraint, error)

	CreateOraDbaIndex(ctx context.Context, oraDbaIndex []*OraDbaIndex) error
	DeleteOraDbaIndex(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaIndex(ctx context.Context, channelId int, taskId int, schema string, indexType string, isCompatible string, isConvertible string) error
	GetOraDbaIndexList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaIndex, error)

	CreateOraDbaTabColumns(ctx context.Context, data []*OraDbaTabColumns) error
	DeleteOraDbaTabColumns(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaTabColumns(ctx context.Context, channelId int, taskId int, schema string, dataTypeS string, dataTypeT string, isEquivalent string) error
	GetOraDbaTabColumnsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaTabColumns, error)

	CreateOraDbaTabColumnsDefault(ctx context.Context, data []*OraDbaTabColumnsDefault) error
	DeleteOraDbaTabColumnsDefault(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaTabColumnsDefault(ctx context.Context, channelId int, taskId int, schema string, dataDefaultS string, dataDefaultT string, isEquivalent string) error
	GetOraDbaTabColumnsDefaultList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaTabColumnsDefault, error)

	CreateOraAllPartTables(ctx context.Context, data []*OraAllPartTables) error
	DeleteOraAllPartTables(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraAllPartTables(ctx context.Context, channelId int, taskId int, schema string, partitioningType string, isCompatible string, isConvertible string) error
	GetOraAllPartTablesList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraAllPartTables, error)

	CreateOraDbaTables(ctx context.Context, data []*OraDbaTables) error
	DeleteOraDbaTables(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaTables(ctx context.Context, channelId int, taskId int, schema string, tableType string, isCompatible string, isConvertible string) error
	GetOraDbaTablesList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaTables, error)
	GetOraDbaObjectsListWithoutTypes(ctx context.Context, channelId int, taskId int, schemaNames []string, withoutTypes []string) ([]*OraDbaObject, error)
	GetOraDbaObjectsListByObjType(ctx context.Context, channelId int, taskId int, schemaNames []string, objType string) ([]*OraDbaObject, error)

	CreateOraAllObjects(ctx context.Context, data []*OraAllObjects) error
	DeleteOraAllObjects(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraAllObjects(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error
	GetOraAllObjectsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraAllObjects, error)
	//GetOraAllObjectsListWithoutTypes(ctx context.Context, channelId int, taskId int, schemaNames []string, withoutTypes []string) ([]*OraAllObjects, error)
	//GetOraAllObjectsListByObjType(ctx context.Context, channelId int, taskId int, schemaNames []string, objType string) ([]*OraAllObjects, error)

	CreateOraLobs(ctx context.Context, data []*OraLobs) error
	DeleteOraLobs(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraLobs(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error
	GetOraLobsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraLobs, error)

	CreateOraTableTypeCompatibles(ctx context.Context, data []*OraTableTypeCompatibles) error
	DeleteOraTableTypeCompatibles(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraTableTypeCompatibles(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error
	GetOraTableTypeCompatiblesList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraTableTypeCompatibles, error)

	CreateOraDbaViews(ctx context.Context, data []*OraDbaViews) error
	DeleteOraDbaViews(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaViews(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error
	GetOraDbaViewsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaViews, error)

	CreateOraSubPartitioning(ctx context.Context, data []*OraSubPartitioning) error
	DeleteOraSubPartitioning(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraSubPartitioning(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error
	GetOraSubPartitioningList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraSubPartitioning, error)
	GetHtmlTemplateDetail(ctx context.Context, htmlName string) (string, error)
	GetHtmlRptCfg(ctx context.Context, rptType int) ([]*HtmlRptCfg, error)

	GetOraSequenceList(ctx context.Context, db *sql.DB, schemaNames []string) ([]*structs.SequenceObj, error)
	GetOraSequenceSummaryList(ctx context.Context, db *sql.DB, schemaNames []string) ([]*structs.SequenceSummary, error)
	CreateOraDbaSequence(ctx context.Context, oraDbaIndex []*OraDbaSequence) error
	DeleteOraDbaSequence(ctx context.Context, channelId int, taskId int, schema string) error
	UpdateOraDbaSequence(ctx context.Context, channelId int, taskId int, schema string, isCompatible string, isConvertible string) error
	GetOraDbaSequenceList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaSequence, error)

	GetListObjectCompatibleBySchema(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) ([]*message.ObjectCompatibleBySchema, error)
	GetListObjectCompatibleBySchemaObject(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) ([]*message.ObjectCompatibleByObject, error)
	GetListOracleOverview(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) ([]*message.OracleOverview, error)
	GetAssessDetails(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) (*message.ObjectCompatibleALL, error)
}
