// Code generated by MockGen. DO NOT EDIT.
// Source: ./assessment/readerwriter.go

// Package mockassessmentreaderwriter is a generated GoMock package.
package mockassessmentreaderwriter

import (
	context "context"
	reflect "reflect"

	assessment "gitee.com/pingcap_enterprise/tms/server/models/assessment"
	gomock "github.com/golang/mock/gomock"
)

// MockAssessReaderWriter is a mock of AssessReaderWriter interface.
type MockAssessReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockAssessReaderWriterMockRecorder
}

// MockAssessReaderWriterMockRecorder is the mock recorder for MockAssessReaderWriter.
type MockAssessReaderWriterMockRecorder struct {
	mock *MockAssessReaderWriter
}

// NewMockAssessReaderWriter creates a new mock instance.
func NewMockAssessReaderWriter(ctrl *gomock.Controller) *MockAssessReaderWriter {
	mock := &MockAssessReaderWriter{ctrl: ctrl}
	mock.recorder = &MockAssessReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssessReaderWriter) EXPECT() *MockAssessReaderWriterMockRecorder {
	return m.recorder
}

// BatchCreateObjectAssess mocks base method.
func (m *MockAssessReaderWriter) BatchCreateObjectAssess(ctx context.Context, objAssessDetails []*assessment.ObjectAssessDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateObjectAssess", ctx, objAssessDetails)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateObjectAssess indicates an expected call of BatchCreateObjectAssess.
func (mr *MockAssessReaderWriterMockRecorder) BatchCreateObjectAssess(ctx, objAssessDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateObjectAssess", reflect.TypeOf((*MockAssessReaderWriter)(nil).BatchCreateObjectAssess), ctx, objAssessDetails)
}

// BatchGetObjectAssess mocks base method.
func (m *MockAssessReaderWriter) BatchGetObjectAssess(ctx context.Context, channelID int) ([]*assessment.ObjectAssessDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetObjectAssess", ctx, channelID)
	ret0, _ := ret[0].([]*assessment.ObjectAssessDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetObjectAssess indicates an expected call of BatchGetObjectAssess.
func (mr *MockAssessReaderWriterMockRecorder) BatchGetObjectAssess(ctx, channelID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetObjectAssess", reflect.TypeOf((*MockAssessReaderWriter)(nil).BatchGetObjectAssess), ctx, channelID)
}

// BatchLimitObjectAssess mocks base method.
func (m *MockAssessReaderWriter) BatchLimitObjectAssess(ctx context.Context, objAssessDetail *assessment.ObjectAssessDetail, limit int) ([]*assessment.ObjectAssessDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchLimitObjectAssess", ctx, objAssessDetail, limit)
	ret0, _ := ret[0].([]*assessment.ObjectAssessDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchLimitObjectAssess indicates an expected call of BatchLimitObjectAssess.
func (mr *MockAssessReaderWriterMockRecorder) BatchLimitObjectAssess(ctx, objAssessDetail, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchLimitObjectAssess", reflect.TypeOf((*MockAssessReaderWriter)(nil).BatchLimitObjectAssess), ctx, objAssessDetail, limit)
}

// BatchQueryObjectAssess mocks base method.
func (m *MockAssessReaderWriter) BatchQueryObjectAssess(ctx context.Context, objAssessDetail *assessment.ObjectAssessDetail) ([]*assessment.ObjectAssessDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchQueryObjectAssess", ctx, objAssessDetail)
	ret0, _ := ret[0].([]*assessment.ObjectAssessDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchQueryObjectAssess indicates an expected call of BatchQueryObjectAssess.
func (mr *MockAssessReaderWriterMockRecorder) BatchQueryObjectAssess(ctx, objAssessDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchQueryObjectAssess", reflect.TypeOf((*MockAssessReaderWriter)(nil).BatchQueryObjectAssess), ctx, objAssessDetail)
}

// CreateOraAllObjects mocks base method.
func (m *MockAssessReaderWriter) CreateOraAllObjects(ctx context.Context, data []*assessment.OraAllObjects) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraAllObjects", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraAllObjects indicates an expected call of CreateOraAllObjects.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraAllObjects(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraAllObjects", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraAllObjects), ctx, data)
}

// CreateOraAllPartTables mocks base method.
func (m *MockAssessReaderWriter) CreateOraAllPartTables(ctx context.Context, data []*assessment.OraAllPartTables) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraAllPartTables", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraAllPartTables indicates an expected call of CreateOraAllPartTables.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraAllPartTables(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraAllPartTables", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraAllPartTables), ctx, data)
}

// CreateOraDbaConstraint mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaConstraint(ctx context.Context, oraDbaConstraint []*assessment.OraDbaConstraint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaConstraint", ctx, oraDbaConstraint)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaConstraint indicates an expected call of CreateOraDbaConstraint.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaConstraint(ctx, oraDbaConstraint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaConstraint", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaConstraint), ctx, oraDbaConstraint)
}

// CreateOraDbaIndex mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaIndex(ctx context.Context, oraDbaIndex []*assessment.OraDbaIndex) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaIndex", ctx, oraDbaIndex)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaIndex indicates an expected call of CreateOraDbaIndex.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaIndex(ctx, oraDbaIndex interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaIndex", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaIndex), ctx, oraDbaIndex)
}

// CreateOraDbaObject mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaObject(ctx context.Context, oraDbaObj []*assessment.OraDbaObject) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaObject", ctx, oraDbaObj)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaObject indicates an expected call of CreateOraDbaObject.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaObject(ctx, oraDbaObj interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaObject", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaObject), ctx, oraDbaObj)
}

// CreateOraDbaTabColumns mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaTabColumns(ctx context.Context, data []*assessment.OraDbaTabColumns) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaTabColumns", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaTabColumns indicates an expected call of CreateOraDbaTabColumns.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaTabColumns(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaTabColumns", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaTabColumns), ctx, data)
}

// CreateOraDbaTabColumnsDefault mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaTabColumnsDefault(ctx context.Context, data []*assessment.OraDbaTabColumnsDefault) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaTabColumnsDefault", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaTabColumnsDefault indicates an expected call of CreateOraDbaTabColumnsDefault.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaTabColumnsDefault(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaTabColumnsDefault", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaTabColumnsDefault), ctx, data)
}

// CreateOraDbaTables mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaTables(ctx context.Context, data []*assessment.OraDbaTables) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaTables", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaTables indicates an expected call of CreateOraDbaTables.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaTables(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaTables", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaTables), ctx, data)
}

// CreateOraDbaViews mocks base method.
func (m *MockAssessReaderWriter) CreateOraDbaViews(ctx context.Context, data []*assessment.OraDbaViews) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraDbaViews", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraDbaViews indicates an expected call of CreateOraDbaViews.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraDbaViews(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraDbaViews", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraDbaViews), ctx, data)
}

// CreateOraLobs mocks base method.
func (m *MockAssessReaderWriter) CreateOraLobs(ctx context.Context, data []*assessment.OraLobs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraLobs", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraLobs indicates an expected call of CreateOraLobs.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraLobs(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraLobs", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraLobs), ctx, data)
}

// CreateOraSubPartitioning mocks base method.
func (m *MockAssessReaderWriter) CreateOraSubPartitioning(ctx context.Context, data []*assessment.OraSubPartitioning) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraSubPartitioning", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraSubPartitioning indicates an expected call of CreateOraSubPartitioning.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraSubPartitioning(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraSubPartitioning", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraSubPartitioning), ctx, data)
}

// CreateOraTableTypeCompatibles mocks base method.
func (m *MockAssessReaderWriter) CreateOraTableTypeCompatibles(ctx context.Context, data []*assessment.OraTableTypeCompatibles) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOraTableTypeCompatibles", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOraTableTypeCompatibles indicates an expected call of CreateOraTableTypeCompatibles.
func (mr *MockAssessReaderWriterMockRecorder) CreateOraTableTypeCompatibles(ctx, data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOraTableTypeCompatibles", reflect.TypeOf((*MockAssessReaderWriter)(nil).CreateOraTableTypeCompatibles), ctx, data)
}

// DeleteOraAllObjects mocks base method.
func (m *MockAssessReaderWriter) DeleteOraAllObjects(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraAllObjects", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraAllObjects indicates an expected call of DeleteOraAllObjects.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraAllObjects(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraAllObjects", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraAllObjects), ctx, channelId, taskId, schema)
}

// DeleteOraAllPartTables mocks base method.
func (m *MockAssessReaderWriter) DeleteOraAllPartTables(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraAllPartTables", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraAllPartTables indicates an expected call of DeleteOraAllPartTables.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraAllPartTables(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraAllPartTables", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraAllPartTables), ctx, channelId, taskId, schema)
}

// DeleteOraDbaConstraint mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaConstraint(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaConstraint", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaConstraint indicates an expected call of DeleteOraDbaConstraint.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaConstraint(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaConstraint", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaConstraint), ctx, channelId, taskId, schema)
}

// DeleteOraDbaIndex mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaIndex(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaIndex", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaIndex indicates an expected call of DeleteOraDbaIndex.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaIndex(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaIndex", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaIndex), ctx, channelId, taskId, schema)
}

// DeleteOraDbaObject mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaObject(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaObject", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaObject indicates an expected call of DeleteOraDbaObject.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaObject(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaObject", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaObject), ctx, channelId, taskId, schema)
}

// DeleteOraDbaTabColumns mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaTabColumns(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaTabColumns", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaTabColumns indicates an expected call of DeleteOraDbaTabColumns.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaTabColumns(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaTabColumns", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaTabColumns), ctx, channelId, taskId, schema)
}

// DeleteOraDbaTabColumnsDefault mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaTabColumnsDefault(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaTabColumnsDefault", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaTabColumnsDefault indicates an expected call of DeleteOraDbaTabColumnsDefault.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaTabColumnsDefault(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaTabColumnsDefault", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaTabColumnsDefault), ctx, channelId, taskId, schema)
}

// DeleteOraDbaTables mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaTables(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaTables", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaTables indicates an expected call of DeleteOraDbaTables.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaTables(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaTables", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaTables), ctx, channelId, taskId, schema)
}

// DeleteOraDbaViews mocks base method.
func (m *MockAssessReaderWriter) DeleteOraDbaViews(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraDbaViews", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraDbaViews indicates an expected call of DeleteOraDbaViews.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraDbaViews(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraDbaViews", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraDbaViews), ctx, channelId, taskId, schema)
}

// DeleteOraLobs mocks base method.
func (m *MockAssessReaderWriter) DeleteOraLobs(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraLobs", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraLobs indicates an expected call of DeleteOraLobs.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraLobs(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraLobs", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraLobs), ctx, channelId, taskId, schema)
}

// DeleteOraSubPartitioning mocks base method.
func (m *MockAssessReaderWriter) DeleteOraSubPartitioning(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraSubPartitioning", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraSubPartitioning indicates an expected call of DeleteOraSubPartitioning.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraSubPartitioning(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraSubPartitioning", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraSubPartitioning), ctx, channelId, taskId, schema)
}

// DeleteOraTableTypeCompatibles mocks base method.
func (m *MockAssessReaderWriter) DeleteOraTableTypeCompatibles(ctx context.Context, channelId, taskId int, schema string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteOraTableTypeCompatibles", ctx, channelId, taskId, schema)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteOraTableTypeCompatibles indicates an expected call of DeleteOraTableTypeCompatibles.
func (mr *MockAssessReaderWriterMockRecorder) DeleteOraTableTypeCompatibles(ctx, channelId, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteOraTableTypeCompatibles", reflect.TypeOf((*MockAssessReaderWriter)(nil).DeleteOraTableTypeCompatibles), ctx, channelId, taskId, schema)
}

// GetOraAllObjectsList mocks base method.
func (m *MockAssessReaderWriter) GetOraAllObjectsList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraAllObjects, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraAllObjectsList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraAllObjects)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraAllObjectsList indicates an expected call of GetOraAllObjectsList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraAllObjectsList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraAllObjectsList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraAllObjectsList), ctx, channelId, taskId, schemaNames)
}

// GetOraAllPartTablesList mocks base method.
func (m *MockAssessReaderWriter) GetOraAllPartTablesList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraAllPartTables, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraAllPartTablesList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraAllPartTables)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraAllPartTablesList indicates an expected call of GetOraAllPartTablesList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraAllPartTablesList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraAllPartTablesList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraAllPartTablesList), ctx, channelId, taskId, schemaNames)
}

// GetOraDbaConstraintList mocks base method.
func (m *MockAssessReaderWriter) GetOraDbaConstraintList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraDbaConstraint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraDbaConstraintList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraDbaConstraint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraDbaConstraintList indicates an expected call of GetOraDbaConstraintList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraDbaConstraintList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraDbaConstraintList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraDbaConstraintList), ctx, channelId, taskId, schemaNames)
}

// GetOraDbaIndexList mocks base method.
func (m *MockAssessReaderWriter) GetOraDbaIndexList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraDbaIndex, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraDbaIndexList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraDbaIndex)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraDbaIndexList indicates an expected call of GetOraDbaIndexList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraDbaIndexList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraDbaIndexList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraDbaIndexList), ctx, channelId, taskId, schemaNames)
}

// GetOraDbaTabColumnsDefaultList mocks base method.
func (m *MockAssessReaderWriter) GetOraDbaTabColumnsDefaultList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraDbaTabColumnsDefault, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraDbaTabColumnsDefaultList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraDbaTabColumnsDefault)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraDbaTabColumnsDefaultList indicates an expected call of GetOraDbaTabColumnsDefaultList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraDbaTabColumnsDefaultList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraDbaTabColumnsDefaultList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraDbaTabColumnsDefaultList), ctx, channelId, taskId, schemaNames)
}

// GetOraDbaTabColumnsList mocks base method.
func (m *MockAssessReaderWriter) GetOraDbaTabColumnsList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraDbaTabColumns, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraDbaTabColumnsList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraDbaTabColumns)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraDbaTabColumnsList indicates an expected call of GetOraDbaTabColumnsList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraDbaTabColumnsList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraDbaTabColumnsList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraDbaTabColumnsList), ctx, channelId, taskId, schemaNames)
}

// GetOraDbaTablesList mocks base method.
func (m *MockAssessReaderWriter) GetOraDbaTablesList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraDbaTables, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraDbaTablesList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraDbaTables)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraDbaTablesList indicates an expected call of GetOraDbaTablesList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraDbaTablesList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraDbaTablesList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraDbaTablesList), ctx, channelId, taskId, schemaNames)
}

// GetOraDbaViewsList mocks base method.
func (m *MockAssessReaderWriter) GetOraDbaViewsList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraDbaViews, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraDbaViewsList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraDbaViews)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraDbaViewsList indicates an expected call of GetOraDbaViewsList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraDbaViewsList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraDbaViewsList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraDbaViewsList), ctx, channelId, taskId, schemaNames)
}

// GetOraLobsList mocks base method.
func (m *MockAssessReaderWriter) GetOraLobsList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraLobs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraLobsList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraLobs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraLobsList indicates an expected call of GetOraLobsList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraLobsList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraLobsList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraLobsList), ctx, channelId, taskId, schemaNames)
}

// GetOraSubPartitioningList mocks base method.
func (m *MockAssessReaderWriter) GetOraSubPartitioningList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraSubPartitioning, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraSubPartitioningList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraSubPartitioning)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraSubPartitioningList indicates an expected call of GetOraSubPartitioningList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraSubPartitioningList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraSubPartitioningList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraSubPartitioningList), ctx, channelId, taskId, schemaNames)
}

// GetOraTableTypeCompatiblesList mocks base method.
func (m *MockAssessReaderWriter) GetOraTableTypeCompatiblesList(ctx context.Context, channelId, taskId int, schemaNames []string) ([]*assessment.OraTableTypeCompatibles, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOraTableTypeCompatiblesList", ctx, channelId, taskId, schemaNames)
	ret0, _ := ret[0].([]*assessment.OraTableTypeCompatibles)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOraTableTypeCompatiblesList indicates an expected call of GetOraTableTypeCompatiblesList.
func (mr *MockAssessReaderWriterMockRecorder) GetOraTableTypeCompatiblesList(ctx, channelId, taskId, schemaNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOraTableTypeCompatiblesList", reflect.TypeOf((*MockAssessReaderWriter)(nil).GetOraTableTypeCompatiblesList), ctx, channelId, taskId, schemaNames)
}

// UpdateObjectAssess mocks base method.
func (m *MockAssessReaderWriter) UpdateObjectAssess(ctx context.Context, objAssessDetails *assessment.ObjectAssessDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateObjectAssess", ctx, objAssessDetails)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateObjectAssess indicates an expected call of UpdateObjectAssess.
func (mr *MockAssessReaderWriterMockRecorder) UpdateObjectAssess(ctx, objAssessDetails interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateObjectAssess", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateObjectAssess), ctx, objAssessDetails)
}

// UpdateOraAllObjects mocks base method.
func (m *MockAssessReaderWriter) UpdateOraAllObjects(ctx context.Context, channelId, taskId int, schema, objectType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraAllObjects", ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraAllObjects indicates an expected call of UpdateOraAllObjects.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraAllObjects(ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraAllObjects", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraAllObjects), ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
}

// UpdateOraAllPartTables mocks base method.
func (m *MockAssessReaderWriter) UpdateOraAllPartTables(ctx context.Context, channelId, taskId int, schema, partitioningType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraAllPartTables", ctx, channelId, taskId, schema, partitioningType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraAllPartTables indicates an expected call of UpdateOraAllPartTables.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraAllPartTables(ctx, channelId, taskId, schema, partitioningType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraAllPartTables", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraAllPartTables), ctx, channelId, taskId, schema, partitioningType, isCompatible, isConvertible)
}

// UpdateOraDbaConstraint mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaConstraint(ctx context.Context, channelId, taskId int, schema, constraintType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaConstraint", ctx, channelId, taskId, schema, constraintType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaConstraint indicates an expected call of UpdateOraDbaConstraint.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaConstraint(ctx, channelId, taskId, schema, constraintType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaConstraint", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaConstraint), ctx, channelId, taskId, schema, constraintType, isCompatible, isConvertible)
}

// UpdateOraDbaIndex mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaIndex(ctx context.Context, channelId, taskId int, schema, indexType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaIndex", ctx, channelId, taskId, schema, indexType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaIndex indicates an expected call of UpdateOraDbaIndex.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaIndex(ctx, channelId, taskId, schema, indexType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaIndex", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaIndex), ctx, channelId, taskId, schema, indexType, isCompatible, isConvertible)
}

// UpdateOraDbaObject mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaObject(ctx context.Context, channelId, taskId int, schema, constraintType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaObject", ctx, channelId, taskId, schema, constraintType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaObject indicates an expected call of UpdateOraDbaObject.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaObject(ctx, channelId, taskId, schema, constraintType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaObject", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaObject), ctx, channelId, taskId, schema, constraintType, isCompatible, isConvertible)
}

// UpdateOraDbaTabColumns mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaTabColumns(ctx context.Context, channelId, taskId int, schema, dataTypeS, dataTypeT, isEquivalent string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaTabColumns", ctx, channelId, taskId, schema, dataTypeS, dataTypeT, isEquivalent)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaTabColumns indicates an expected call of UpdateOraDbaTabColumns.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaTabColumns(ctx, channelId, taskId, schema, dataTypeS, dataTypeT, isEquivalent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaTabColumns", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaTabColumns), ctx, channelId, taskId, schema, dataTypeS, dataTypeT, isEquivalent)
}

// UpdateOraDbaTabColumnsDefault mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaTabColumnsDefault(ctx context.Context, channelId, taskId int, schema, dataDefaultS, dataDefaultT, isEquivalent string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaTabColumnsDefault", ctx, channelId, taskId, schema, dataDefaultS, dataDefaultT, isEquivalent)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaTabColumnsDefault indicates an expected call of UpdateOraDbaTabColumnsDefault.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaTabColumnsDefault(ctx, channelId, taskId, schema, dataDefaultS, dataDefaultT, isEquivalent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaTabColumnsDefault", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaTabColumnsDefault), ctx, channelId, taskId, schema, dataDefaultS, dataDefaultT, isEquivalent)
}

// UpdateOraDbaTables mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaTables(ctx context.Context, channelId, taskId int, schema, tableType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaTables", ctx, channelId, taskId, schema, tableType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaTables indicates an expected call of UpdateOraDbaTables.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaTables(ctx, channelId, taskId, schema, tableType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaTables", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaTables), ctx, channelId, taskId, schema, tableType, isCompatible, isConvertible)
}

// UpdateOraDbaViews mocks base method.
func (m *MockAssessReaderWriter) UpdateOraDbaViews(ctx context.Context, channelId, taskId int, schema, objectType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraDbaViews", ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraDbaViews indicates an expected call of UpdateOraDbaViews.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraDbaViews(ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraDbaViews", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraDbaViews), ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
}

// UpdateOraLobs mocks base method.
func (m *MockAssessReaderWriter) UpdateOraLobs(ctx context.Context, channelId, taskId int, schema, objectType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraLobs", ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraLobs indicates an expected call of UpdateOraLobs.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraLobs(ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraLobs", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraLobs), ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
}

// UpdateOraSubPartitioning mocks base method.
func (m *MockAssessReaderWriter) UpdateOraSubPartitioning(ctx context.Context, channelId, taskId int, schema, objectType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraSubPartitioning", ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraSubPartitioning indicates an expected call of UpdateOraSubPartitioning.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraSubPartitioning(ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraSubPartitioning", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraSubPartitioning), ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
}

// UpdateOraTableTypeCompatibles mocks base method.
func (m *MockAssessReaderWriter) UpdateOraTableTypeCompatibles(ctx context.Context, channelId, taskId int, schema, objectType, isCompatible, isConvertible string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOraTableTypeCompatibles", ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOraTableTypeCompatibles indicates an expected call of UpdateOraTableTypeCompatibles.
func (mr *MockAssessReaderWriterMockRecorder) UpdateOraTableTypeCompatibles(ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOraTableTypeCompatibles", reflect.TypeOf((*MockAssessReaderWriter)(nil).UpdateOraTableTypeCompatibles), ctx, channelId, taskId, schema, objectType, isCompatible, isConvertible)
}
