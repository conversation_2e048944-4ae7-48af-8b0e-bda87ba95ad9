/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package assessment

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type AssessReadWrite struct {
	dbCommon.GormDB
}

func NewAssessReadWrite(db *gorm.DB) *AssessReadWrite {
	m := &AssessReadWrite{
		GormDB: dbCommon.WrapDB(db),
	}
	return m
}

func (rw *AssessReadWrite) BatchGetObjectAssess(ctx context.Context, channelID int) ([]*ObjectAssessDetail, error) {
	var objDetails []*ObjectAssessDetail
	err := rw.GormDB.DB(ctx).Where("channel_id = ?", channelID).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query channel %d not found", channelID)
	} else if err != nil {
		log.Errorf("get object assess detail failed. channelId is %d, %v", channelID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw *AssessReadWrite) BatchQueryObjectAssess(ctx context.Context, objAssessDetail *ObjectAssessDetail) ([]*ObjectAssessDetail, error) {
	var objDetails []*ObjectAssessDetail
	err := rw.GormDB.DB(ctx).Where(objAssessDetail).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query object assess detail %v not found", objAssessDetail)
	} else if err != nil {
		log.Errorf("query object assess detail failed, detail [%d], error [%v]", objAssessDetail, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw *AssessReadWrite) BatchLimitObjectAssess(ctx context.Context, objAssessDetail *ObjectAssessDetail, limit int) ([]*ObjectAssessDetail, error) {
	var objDetails []*ObjectAssessDetail
	err := rw.GormDB.DB(ctx).Where(objAssessDetail).Find(&objDetails).Limit(limit).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query object assess detail %v not found", objAssessDetail)
	} else if err != nil {
		log.Errorf("query object assess detail failed, detail [%d], error [%v]", objAssessDetail, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw *AssessReadWrite) BatchCreateObjectAssess(ctx context.Context, objAssessDetails []*ObjectAssessDetail) error {
	err := rw.GormDB.DB(ctx).CreateInBatches(objAssessDetails, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create channel to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Infof("create object assess detail to db successfully, %v", objAssessDetails)
	return nil
}

func (rw *AssessReadWrite) CreateObjectAssess(ctx context.Context, objAssessDetail *ObjectAssessDetail) error {
	err := rw.GormDB.DB(ctx).Create(objAssessDetail).Error
	if err != nil {
		log.Errorf("create object assess detail to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Infof("create object assess detail to db successfully, %v", objAssessDetail)
	return nil
}

func (rw *AssessReadWrite) UpdateObjectAssess(ctx context.Context, objAssessDetails *ObjectAssessDetail) error {
	err := rw.GormDB.DB(ctx).Save(objAssessDetails).Error
	if err != nil {
		log.Errorf("update object assess detail info to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	log.Infof("update object assess detail to db successfully, %v", objAssessDetails)
	return nil
}

func (rw *AssessReadWrite) CreateOraDbaObject(ctx context.Context, oraDbaObj []*OraDbaObject) error {
	for i := 0; i < len(oraDbaObj); i += 1000 {
		end := i + 1000
		if end > len(oraDbaObj) {
			end = len(oraDbaObj)
		}
		err := rw.DB(ctx).CreateInBatches(oraDbaObj[i:end], len(oraDbaObj[i:end])).Error
		if err != nil {
			log.Errorf("create OraDbaObject info to db failed. err:%s", err)
			return dbCommon.WrapDBError(err)
		}
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaObject(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraDbaObject{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaObject failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaObject(ctx context.Context, channelId int, taskId int, schema string, constraintType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraDbaObject{}).Where("channel_id = ? and task_id = ? and owner = ? and object_type = ?", channelId, taskId, schema, constraintType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraDbaObject failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraDbaConstraint(ctx context.Context, oraDbaConstraint []*OraDbaConstraint) error {
	err := rw.DB(ctx).CreateInBatches(oraDbaConstraint, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create oraDbaConstraint info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaConstraint(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraDbaConstraint{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaConstraint failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaConstraint(ctx context.Context, channelId int, taskId int, schema string, constraintType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraDbaConstraint{}).Where("channel_id = ? and task_id = ? and owner = ? and constraint_type = ?", channelId, taskId, schema, constraintType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraDbaConstraint failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraDbaIndex(ctx context.Context, oraDbaIndex []*OraDbaIndex) error {
	err := rw.DB(ctx).CreateInBatches(oraDbaIndex, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create oraDbaIndex info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaIndex(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and table_owner = ?", channelId, taskId, schema).Delete(&OraDbaIndex{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaIndex failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaIndex(ctx context.Context, channelId int, taskId int, schema string, indexType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraDbaIndex{}).Where("channel_id = ? and task_id = ? and table_owner = ? and index_type = ?", channelId, taskId, schema, indexType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraDbaConstraint failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) GetOraDbaIndexList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaIndex, error) {
	var records []*OraDbaIndex
	query := rw.DB(ctx).Model(&OraDbaIndex{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("table_owner in ?", schemaNames)
	}

	err := query.Order("ora_index_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaIndex failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraDbaConstraintList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaConstraint, error) {
	var records []*OraDbaConstraint
	query := rw.DB(ctx).Model(&OraDbaConstraint{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("oraconstraint_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaIndex failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) CreateOraDbaTabColumns(ctx context.Context, data []*OraDbaTabColumns) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraDbaTabColumns info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraDbaTabColumnsDefault(ctx context.Context, data []*OraDbaTabColumnsDefault) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraDbaTabColumnsDefault info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraAllPartTables(ctx context.Context, data []*OraAllPartTables) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraAllPartTables info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraDbaTables(ctx context.Context, data []*OraDbaTables) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraDbaTables info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraAllObjects(ctx context.Context, data []*OraAllObjects) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraAllObjects info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraLobs(ctx context.Context, data []*OraLobs) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraLobs info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraTableTypeCompatibles(ctx context.Context, data []*OraTableTypeCompatibles) error {
	err := rw.DB(ctx).CreateInBatches(data, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create OraTableTypeCompatibles info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraDbaViews(ctx context.Context, data []*OraDbaViews) error {
	err := rw.DB(ctx).Create(data).Error
	if err != nil {
		log.Errorf("create OraDbaViews info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) CreateOraSubPartitioning(ctx context.Context, data []*OraSubPartitioning) error {
	err := rw.DB(ctx).Create(data).Error
	if err != nil {
		log.Errorf("create OraSubPartitioning info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaTabColumns(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraDbaTabColumns{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaTabColumns failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaTabColumnsDefault(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraDbaTabColumnsDefault{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaTabColumnsDefault failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraAllPartTables(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraAllPartTables{}).Error
	if err != nil {
		log.Errorf("delete table OraAllPartTables failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaTables(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraDbaTables{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaTables failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraAllObjects(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraAllObjects{}).Error
	if err != nil {
		log.Errorf("delete table OraAllObjects failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraLobs(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraLobs{}).Error
	if err != nil {
		log.Errorf("delete table OraLobs failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraTableTypeCompatibles(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraTableTypeCompatibles{}).Error
	if err != nil {
		log.Errorf("delete table OraTableTypeCompatibles failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaViews(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraDbaViews{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaViews failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraSubPartitioning(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and owner = ?", channelId, taskId, schema).Delete(&OraSubPartitioning{}).Error
	if err != nil {
		log.Errorf("delete table OraSubPartitioning failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaTabColumns(ctx context.Context, channelId int, taskId int, schema string, dataTypeS string, dataTypeT string, isEquivalent string) error {
	err := rw.DB(ctx).Model(&OraDbaTabColumns{}).Where("channel_id = ? and task_id = ? and owner = ? and data_type = ?", channelId, taskId, schema, dataTypeS).
		Updates(map[string]interface{}{"is_equivalent": isEquivalent, "data_type_t": dataTypeT}).Error
	if err != nil {
		log.Errorf("update table OraDbaTabColumns failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaTabColumnsDefault(ctx context.Context, channelId int, taskId int, schema string, dataDefaultS string, dataDefaultT string, isEquivalent string) error {
	err := rw.DB(ctx).Model(&OraDbaTabColumnsDefault{}).Where("channel_id = ? and task_id = ? and owner = ? and data_default = ? ", channelId, taskId, schema, dataDefaultS).
		Updates(map[string]interface{}{"is_equivalent": isEquivalent, "data_default_t": dataDefaultT}).Error
	if err != nil {
		log.Errorf("update table OraDbaTabColumnsDefault failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraAllPartTables(ctx context.Context, channelId int, taskId int, schema string, partitioningType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraAllPartTables{}).Where("channel_id = ? and task_id = ? and owner = ? and partitioning_type = ?", channelId, taskId, schema, partitioningType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraAllPartTables failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaTables(ctx context.Context, channelId int, taskId int, schema string, tableType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraDbaTables{}).Where("channel_id = ? and task_id = ? and owner = ? and temporary = ?", channelId, taskId, schema, tableType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraDbaTables failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraAllObjects(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraAllObjects{}).Where("channel_id = ? and task_id = ? and owner = ? and object_type = ?", channelId, taskId, schema, objectType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraAllObjects failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraLobs(ctx context.Context, channelId int, taskId int, schema string, objectType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraLobs{}).Where("channel_id = ? and task_id = ? and owner = ? and object_type = ?", channelId, taskId, schema, objectType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraLobs failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraTableTypeCompatibles(ctx context.Context, channelId int, taskId int, schema string, tableType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraTableTypeCompatibles{}).Where("channel_id = ? and task_id = ? and owner = ? and table_type = ?", channelId, taskId, schema, tableType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraTableTypeCompatibles failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaViews(ctx context.Context, channelId int, taskId int, schema string, viewType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraDbaViews{}).Where("channel_id = ? and task_id = ? and owner = ? and view_type = ?", channelId, taskId, schema, viewType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraDbaViews failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraSubPartitioning(ctx context.Context, channelId int, taskId int, schema string, subpartitionType string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraSubPartitioning{}).Where("channel_id = ? and task_id = ? and owner = ? and SUB_PARTITIONING_TYPE = ?", channelId, taskId, schema, subpartitionType).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraSubPartitioning failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) GetOraDbaTabColumnsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaTabColumns, error) {
	var records []*OraDbaTabColumns
	query := rw.DB(ctx).Model(&OraDbaTabColumns{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaDbaTabColumns failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraDbaTabColumnsDefaultList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaTabColumnsDefault, error) {
	var records []*OraDbaTabColumnsDefault
	query := rw.DB(ctx).Model(&OraDbaTabColumnsDefault{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaTabColumnsDefault failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraAllPartTablesList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraAllPartTables, error) {
	var records []*OraAllPartTables
	query := rw.DB(ctx).Model(&OraAllPartTables{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraAllPartTables failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraDbaTablesList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaTables, error) {
	var records []*OraDbaTables
	query := rw.DB(ctx).Model(&OraDbaTables{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaTables failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraAllObjectsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraAllObjects, error) {
	var records []*OraAllObjects
	query := rw.DB(ctx).Model(&OraAllObjects{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraAllObjects failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraAllObjectsListWithoutTypes(ctx context.Context, channelId int, taskId int, schemaNames []string, withoutTypes []string) ([]*OraAllObjects, error) {
	var records []*OraAllObjects
	query := rw.DB(ctx).Model(&OraAllObjects{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}
	if len(withoutTypes) > 0 {
		query = query.Where("object_type not in ?", withoutTypes)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraAllObjects failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraDbaObjectsListWithoutTypes(ctx context.Context, channelId int, taskId int, schemaNames []string, withoutTypes []string) ([]*OraDbaObject, error) {
	var records []*OraDbaObject
	query := rw.DB(ctx).Model(&OraDbaObject{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}
	if len(withoutTypes) > 0 {
		query = query.Where("object_type not in ?", withoutTypes)
	}

	err := query.Order("oraobj_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraAllObjects failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraAllObjectsListByObjType(ctx context.Context, channelId int, taskId int, schemaNames []string, objType string) ([]*OraAllObjects, error) {
	var records []*OraAllObjects
	query := rw.DB(ctx).Model(&OraAllObjects{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	query = query.Where("object_type = ?", objType)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraAllObjects failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraDbaObjectsListByObjType(ctx context.Context, channelId int, taskId int, schemaNames []string, objType string) ([]*OraDbaObject, error) {
	var records []*OraDbaObject
	query := rw.DB(ctx).Model(&OraDbaObject{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	query = query.Where("object_type = ?", objType)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("oraobj_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraAllObjects failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraLobsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraLobs, error) {
	var records []*OraLobs
	query := rw.DB(ctx).Model(&OraLobs{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraLobs failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraTableTypeCompatiblesList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraTableTypeCompatibles, error) {
	var records []*OraTableTypeCompatibles
	query := rw.DB(ctx).Model(&OraTableTypeCompatibles{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraTableTypeCompatibles failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraDbaViewsList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaViews, error) {
	var records []*OraDbaViews
	query := rw.DB(ctx).Model(&OraDbaViews{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaViews failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraSubPartitioningList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraSubPartitioning, error) {
	var records []*OraSubPartitioning
	query := rw.DB(ctx).Model(&OraSubPartitioning{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("owner in ?", schemaNames)
	}

	err := query.Order("data_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraSubPartitioning failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetHtmlTemplateDetail(ctx context.Context, htmlName string) (string, error) {
	var records []*HtmlTemplateDetail
	query := rw.DB(ctx).Model(&HtmlTemplateDetail{})
	query = query.Where("html_name = ?", htmlName)
	query = query.Where("html_status = ?", "online")
	query = query.Order("template_id asc")
	query = query.Limit(1)
	err := query.Find(&records).Error
	if err != nil {
		log.Errorf("GetHtmlTemplateDetail err:%s", err)
	}
	if len(records) > 0 {
		return records[0].Detail, nil
	} else {
		return "", nil
	}
}

func (rw *AssessReadWrite) GetHtmlRptCfg(ctx context.Context, rptType int) ([]*HtmlRptCfg, error) {
	var records []*HtmlRptCfg
	query := rw.DB(ctx).Model(&HtmlRptCfg{})
	query = query.Where("rpt_type = ?", rptType)
	query = query.Order("parent_rpt_id").Order("display_order")
	err := query.Find(&records).Error
	if err != nil {
		log.Errorf("GetHtmlRptCfg err:%s", err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetOraSequenceList(ctx context.Context, db *sql.DB, schemaNames []string) ([]*structs.SequenceObj, error) {
	query := fmt.Sprintf("SELECT SEQUENCE_OWNER, SEQUENCE_NAME FROM DBA_SEQUENCES WHERE SEQUENCE_OWNER in (%s)",
		genSchemaPartSql(schemaNames))
	results := make([]*structs.SequenceObj, 0)
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("get sequence from oracle failed. err:%v", err)
		log.Errorf("query is %s", query)
		return nil, dbCommon.WrapDBError(err)
	}
	for rows.Next() {
		var schema, sequence sql.NullString
		err = rows.Scan(&schema, &sequence)
		if err != nil {
			log.Errorf("get sequence from oracle failed. err:%v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		results = append(results, &structs.SequenceObj{SchemaName: schema.String, SequenceName: sequence.String})
	}
	return results, nil
}

func (rw *AssessReadWrite) GetOraSequenceSummaryList(ctx context.Context, db *sql.DB, schemaNames []string) ([]*structs.SequenceSummary, error) {
	query := fmt.Sprintf("SELECT SEQUENCE_OWNER, count(SEQUENCE_NAME) count FROM DBA_SEQUENCES WHERE SEQUENCE_OWNER in (%s) GROUP BY SEQUENCE_OWNER",
		genSchemaPartSql(schemaNames))
	results := make([]*structs.SequenceSummary, 0)
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("get sequence from oracle failed. err:%v", err)
		log.Errorf("query is %s", query)
		return nil, dbCommon.WrapDBError(err)
	}
	for rows.Next() {
		var schema sql.NullString
		var count sql.NullString
		err = rows.Scan(&schema, &count)
		if err != nil {
			log.Errorf("get sequence from oracle failed. err:%v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		results = append(results, &structs.SequenceSummary{SchemaName: schema.String, Count: count.String})
	}
	return results, nil
}

func genSchemaPartSql(schemas []string) string {
	var builder strings.Builder
	for i, schema := range schemas {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString(schema)
	}
	return builder.String()
}

func (rw *AssessReadWrite) CreateOraDbaSequence(ctx context.Context, oraDbaSeq []*OraDbaSequence) error {
	err := rw.DB(ctx).CreateInBatches(oraDbaSeq, constants.DEFAULT_CREATE_BATCH_SIZE).Error
	if err != nil {
		log.Errorf("create oraDbaSequence info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) DeleteOraDbaSequence(ctx context.Context, channelId int, taskId int, schema string) error {
	err := rw.DB(ctx).Unscoped().Where("channel_id = ? and task_id = ? and SEQUENCE_OWNER = ?", channelId, taskId, schema).Delete(&OraDbaSequence{}).Error
	if err != nil {
		log.Errorf("delete table OraDbaSequence failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) UpdateOraDbaSequence(ctx context.Context, channelId int, taskId int, schema string, isCompatible string, isConvertible string) error {
	err := rw.DB(ctx).Model(&OraDbaSequence{}).Where("channel_id = ? and task_id = ? and SEQUENCE_OWNER = ? ", channelId, taskId, schema).
		Updates(map[string]interface{}{"is_compatible": isCompatible, "is_convertible": isConvertible}).Error
	if err != nil {
		log.Errorf("update table OraDbaSequence failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *AssessReadWrite) GetOraDbaSequenceList(ctx context.Context, channelId int, taskId int, schemaNames []string) ([]*OraDbaSequence, error) {
	var records []*OraDbaSequence
	query := rw.DB(ctx).Model(&OraDbaSequence{})
	query = query.Where("channel_id = ?", channelId)
	query = query.Where("task_id = ?", taskId)
	if len(schemaNames) > 0 {
		query = query.Where("SEQUENCE_OWNER in ?", schemaNames)
	}

	err := query.Order("ora_index_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("page query OraDbaSequence failed. channel_id is %d, task_id is %d", channelId, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *AssessReadWrite) GetAssessDetails(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) (*message.ObjectCompatibleALL, error) {
	var ListOracleOverview []*message.OracleOverview
	var ListSchemaTableTypeCompatibles []*message.SchemaTableTypeCompatibles
	var ListSchemaColumnTypeCompatibles []*message.SchemaColumnTypeCompatibles
	var ListSchemaConstraintTypeCompatibles []*message.SchemaConstraintTypeCompatibles
	var ListSchemaIndexTypeCompatibles []*message.SchemaIndexTypeCompatibles
	var ListSchemaSequenceCompatibles []*message.SchemaSequenceCompatibles
	var ListSchemaJobCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaSynonymCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaPackageBodyCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaPackageCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaTriggerCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaProcedureCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaFunctionCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaDatabaseLinkCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaLobPartitionCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaLobSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaIndexSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaTypeCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaDefaultValueCompatibles []*message.SchemaDefaultValueCompatibles
	var ListSchemaViewTypeCompatibles []*message.SchemaViewTypeCompatibles
	var ListSchemaObjectTypeCompatibles []*message.SchemaObjectTypeCompatibles
	var ListLobTypeCompatibles []*message.SchemaObjectTypeCompatibles
	var ListSchemaPartitionTypeCompatibles []*message.SchemaPartitionTypeCompatibles
	var ListSchemaSubPartitionTypeCompatibles []*message.SchemaSubPartitionTypeCompatibles
	var ListSchemaTemporaryTableTypeCompatibles []*message.SchemaTemporaryTableTypeCompatibles
	// TiDB Object Limitation
	var ListSchemaPartitionTableCountsCheck []*message.SchemaPartitionTableCountsCheck
	var ListSchemaTableRowLengthCheck []*message.SchemaTableRowLengthCheck
	var ListSchemaTableIndexRowLengthCheck []*message.SchemaTableIndexRowLengthCheck
	var ListSchemaTableColumnCountsCheck []*message.SchemaTableColumnCountsCheck
	var ListSchemaTableIndexCountsCheck []*message.SchemaTableIndexCountsCheck
	var ListSchemaTableNameLengthCheck []*message.SchemaTableNameLengthCheck
	var ListSchemaViewNameLengthCheck []*message.SchemaViewNameLengthCheck
	var ListSchemaSequenceNameLengthCheck []*message.SchemaSequenceNameLengthCheck
	var ListSchemaTableIndexNameLengthCheck []*message.SchemaTableIndexNameLengthCheck
	var ListSchemaTableColumnNameLengthCheck []*message.SchemaTableColumnNameLengthCheck
	var ListUsernameLengthCheck []*message.UsernameLengthCheck
	// Oracle Key Infomation
	var ListSchemaActiveSession []*message.SchemaActiveSession
	var ListSchemaTableSizeData []*message.SchemaTableSizeData
	var ListSchemaTableRowsTOP []*message.SchemaTableRowsTOP
	var ListSchemaTableAvgRowLengthTOP []*message.SchemaTableAvgRowLengthTOP
	var ListSchemaCodeObject []*message.SchemaCodeObject
	var ListSchemaSynonymObject []*message.SchemaSynonymObject
	var ListSchemaMaterializedViewObject []*message.SchemaMaterializedViewObject
	var ListSchemaTableNumberTypeEqual0 []*message.SchemaTableNumberTypeEqual0

	var schemaList string
	for _, schema := range schemas {
		schemaList += fmt.Sprintf("'%s',", schema.SchemaNameS)
	}
	schemaList = schemaList[:len(schemaList)-1]

	// var timsDB *sql.DB
	sql := fmt.Sprintf(`select channel_id,schema_name,assess_type,assess_name, detail,struct_name 
		from object_assess_details oad 
		where channel_id =%d 
		and task_id=%d 
		and schema_name in (%s) 
		and assess_total>0
		order by assess_type,schema_name`, channelId, taskId, schemaList)
	_, data, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("t01-004:Query object_assess_details failed, err: %v", err)
	}
	for _, v := range data {
		// fmt.Printf("v[struct_name]: %v\n", v["struct_name"])
		jsonData := []byte(v["detail"])
		switch v["struct_name"] {
		case "ListOracleOverview":
			var oListOracleOverview *message.OracleOverview
			err = json.Unmarshal(jsonData, &oListOracleOverview)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			ListOracleOverview = []*message.OracleOverview{}
			ListOracleOverview = append(ListOracleOverview, oListOracleOverview)
		case "ListSchemaColumnTypeCompatibles":
			var oListSchemaColumnTypeCompatibles []*message.SchemaColumnTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaColumnTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaColumnTypeCompatibles {
				ListSchemaColumnTypeCompatibles = append(ListSchemaColumnTypeCompatibles, o)
			}
		case "ListSchemaTableTypeCompatibles":
			var oListSchemaTableTypeCompatibles []*message.SchemaTableTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTableTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableTypeCompatibles {
				ListSchemaTableTypeCompatibles = append(ListSchemaTableTypeCompatibles, o)
			}
		case "ListSchemaConstraintTypeCompatibles":
			var oListSchemaConstraintTypeCompatibles []*message.SchemaConstraintTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaConstraintTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaConstraintTypeCompatibles {
				ListSchemaConstraintTypeCompatibles = append(ListSchemaConstraintTypeCompatibles, o)
			}

		case "ListSchemaIndexTypeCompatibles":
			var oListSchemaIndexTypeCompatibles []*message.SchemaIndexTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaIndexTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaIndexTypeCompatibles {
				ListSchemaIndexTypeCompatibles = append(ListSchemaIndexTypeCompatibles, o)
			}

		case "ListSchemaSequenceCompatibles":
			var oListSchemaSequenceCompatibles []*message.SchemaSequenceCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaSequenceCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSequenceCompatibles {
				ListSchemaSequenceCompatibles = append(ListSchemaSequenceCompatibles, o)
			}

		case "ListSchemaJobCompatibles":
			var oListSchemaJobCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaJobCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaJobCompatibles {
				ListSchemaJobCompatibles = append(ListSchemaJobCompatibles, o)
			}

		case "ListSchemaSynonymCompatibles":
			var oListSchemaSynonymCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaSynonymCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSynonymCompatibles {
				ListSchemaSynonymCompatibles = append(ListSchemaSynonymCompatibles, o)
			}

		case "ListSchemaPackageBodyCompatibles":
			var oListSchemaPackageBodyCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaPackageBodyCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPackageBodyCompatibles {
				ListSchemaPackageBodyCompatibles = append(ListSchemaPackageBodyCompatibles, o)
			}

		case "ListSchemaPackageCompatibles":
			var oListSchemaPackageCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaPackageCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPackageCompatibles {
				ListSchemaPackageCompatibles = append(ListSchemaPackageCompatibles, o)
			}

		case "ListSchemaTriggerCompatibles":
			var oListSchemaTriggerCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTriggerCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTriggerCompatibles {
				ListSchemaTriggerCompatibles = append(ListSchemaTriggerCompatibles, o)
			}

		case "ListSchemaProcedureCompatibles":
			var oListSchemaProcedureCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaProcedureCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaProcedureCompatibles {
				ListSchemaProcedureCompatibles = append(ListSchemaProcedureCompatibles, o)
			}

		case "ListSchemaFunctionCompatibles":
			var oListSchemaFunctionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaFunctionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaFunctionCompatibles {
				ListSchemaFunctionCompatibles = append(ListSchemaFunctionCompatibles, o)
			}

		case "ListSchemaDatabaseLinkCompatibles":
			var oListSchemaDatabaseLinkCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaDatabaseLinkCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaDatabaseLinkCompatibles {
				ListSchemaDatabaseLinkCompatibles = append(ListSchemaDatabaseLinkCompatibles, o)
			}

		case "ListSchemaLobPartitionCompatibles":
			var oListSchemaLobPartitionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaLobPartitionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaLobPartitionCompatibles {
				ListSchemaLobPartitionCompatibles = append(ListSchemaLobPartitionCompatibles, o)
			}

		case "ListSchemaLobSubpartitionCompatibles":
			var oListSchemaLobSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaLobSubpartitionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaLobSubpartitionCompatibles {
				ListSchemaLobSubpartitionCompatibles = append(ListSchemaLobSubpartitionCompatibles, o)
			}

		case "ListSchemaIndexSubpartitionCompatibles":
			var oListSchemaIndexSubpartitionCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaIndexSubpartitionCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaIndexSubpartitionCompatibles {
				ListSchemaIndexSubpartitionCompatibles = append(ListSchemaIndexSubpartitionCompatibles, o)
			}

		case "ListSchemaTypeCompatibles":
			var oListSchemaTypeCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTypeCompatibles {
				ListSchemaTypeCompatibles = append(ListSchemaTypeCompatibles, o)
			}

		case "ListSchemaDefaultValueCompatibles":
			var oListSchemaDefaultValueCompatibles []*message.SchemaDefaultValueCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaDefaultValueCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaDefaultValueCompatibles {
				if o.ColumnDefaultValue == "" {
					o.ColumnDefaultValue = "NULL"
				}
				if o.DefaultValueMap == "" {
					o.DefaultValueMap = "NULL"
				}
				ListSchemaDefaultValueCompatibles = append(ListSchemaDefaultValueCompatibles, o)
			}
		case "ListSchemaViewTypeCompatibles":
			var oListSchemaViewTypeCompatibles []*message.SchemaViewTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaViewTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaViewTypeCompatibles {
				ListSchemaViewTypeCompatibles = append(ListSchemaViewTypeCompatibles, o)
			}
		case "ListSchemaObjectTypeCompatibles":
			var oListSchemaObjectTypeCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaObjectTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaObjectTypeCompatibles {
				ListSchemaObjectTypeCompatibles = append(ListSchemaObjectTypeCompatibles, o)
			}
		case "ListLobTypeCompatibles":
			var oListLobTypeCompatibles []*message.SchemaObjectTypeCompatibles
			err = json.Unmarshal(jsonData, &oListLobTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListLobTypeCompatibles {
				ListLobTypeCompatibles = append(ListLobTypeCompatibles, o)
			}
		case "ListSchemaPartitionTypeCompatibles":
			var oListSchemaPartitionTypeCompatibles []*message.SchemaPartitionTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaPartitionTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPartitionTypeCompatibles {
				ListSchemaPartitionTypeCompatibles = append(ListSchemaPartitionTypeCompatibles, o)
			}
		case "ListSchemaSubPartitionTypeCompatibles":
			var oListSchemaSubPartitionTypeCompatibles []*message.SchemaSubPartitionTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaSubPartitionTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSubPartitionTypeCompatibles {
				ListSchemaSubPartitionTypeCompatibles = append(ListSchemaSubPartitionTypeCompatibles, o)
			}
		case "ListSchemaTemporaryTableTypeCompatibles":
			var oListSchemaTemporaryTableTypeCompatibles []*message.SchemaTemporaryTableTypeCompatibles
			err = json.Unmarshal(jsonData, &oListSchemaTemporaryTableTypeCompatibles)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTemporaryTableTypeCompatibles {
				ListSchemaTemporaryTableTypeCompatibles = append(ListSchemaTemporaryTableTypeCompatibles, o)
			}

		case "ListSchemaPartitionTableCountsCheck":
			var oListSchemaPartitionTableCountsCheck []*message.SchemaPartitionTableCountsCheck
			err = json.Unmarshal(jsonData, &oListSchemaPartitionTableCountsCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaPartitionTableCountsCheck {
				ListSchemaPartitionTableCountsCheck = append(ListSchemaPartitionTableCountsCheck, o)
			}
		case "ListSchemaTableRowLengthCheck":
			var oListSchemaTableRowLengthCheck []*message.SchemaTableRowLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableRowLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableRowLengthCheck {
				ListSchemaTableRowLengthCheck = append(ListSchemaTableRowLengthCheck, o)
			}
		case "ListSchemaTableIndexRowLengthCheck":
			var oListSchemaTableIndexRowLengthCheck []*message.SchemaTableIndexRowLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableIndexRowLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableIndexRowLengthCheck {
				ListSchemaTableIndexRowLengthCheck = append(ListSchemaTableIndexRowLengthCheck, o)
			}
		case "ListSchemaTableColumnCountsCheck":
			var oListSchemaTableColumnCountsCheck []*message.SchemaTableColumnCountsCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableColumnCountsCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableColumnCountsCheck {
				ListSchemaTableColumnCountsCheck = append(ListSchemaTableColumnCountsCheck, o)
			}
		case "ListSchemaTableIndexCountsCheck":
			var oListSchemaTableIndexCountsCheck []*message.SchemaTableIndexCountsCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableIndexCountsCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableIndexCountsCheck {
				ListSchemaTableIndexCountsCheck = append(ListSchemaTableIndexCountsCheck, o)
			}
		case "ListSchemaTableNameLengthCheck":
			var oListSchemaTableNameLengthCheck []*message.SchemaTableNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableNameLengthCheck {
				ListSchemaTableNameLengthCheck = append(ListSchemaTableNameLengthCheck, o)
			}
		case "ListSchemaViewNameLengthCheck":
			var oListSchemaViewNameLengthCheck []*message.SchemaViewNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaViewNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaViewNameLengthCheck {
				ListSchemaViewNameLengthCheck = append(ListSchemaViewNameLengthCheck, o)
			}
		case "ListSchemaSequenceNameLengthCheck":
			var oListSchemaSequenceNameLengthCheck []*message.SchemaSequenceNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaSequenceNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSequenceNameLengthCheck {
				ListSchemaSequenceNameLengthCheck = append(ListSchemaSequenceNameLengthCheck, o)
			}
		case "ListSchemaTableIndexNameLengthCheck":
			var oListSchemaTableIndexNameLengthCheck []*message.SchemaTableIndexNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableIndexNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableIndexNameLengthCheck {
				ListSchemaTableIndexNameLengthCheck = append(ListSchemaTableIndexNameLengthCheck, o)
			}
		case "ListSchemaTableColumnNameLengthCheck":
			var oListSchemaTableColumnNameLengthCheck []*message.SchemaTableColumnNameLengthCheck
			err = json.Unmarshal(jsonData, &oListSchemaTableColumnNameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableColumnNameLengthCheck {
				ListSchemaTableColumnNameLengthCheck = append(ListSchemaTableColumnNameLengthCheck, o)
			}
		case "ListUsernameLengthCheck":
			var oListUsernameLengthCheck []*message.UsernameLengthCheck
			err = json.Unmarshal(jsonData, &oListUsernameLengthCheck)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListUsernameLengthCheck {
				ListUsernameLengthCheck = append(ListUsernameLengthCheck, o)
			}

		case "ListSchemaActiveSession":
			var oListSchemaActiveSession []*message.SchemaActiveSession
			err = json.Unmarshal(jsonData, &oListSchemaActiveSession)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaActiveSession {
				ListSchemaActiveSession = append(ListSchemaActiveSession, o)
			}
		case "ListSchemaTableSizeData":
			var oListSchemaTableSizeData []*message.SchemaTableSizeData
			err = json.Unmarshal(jsonData, &oListSchemaTableSizeData)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableSizeData {
				ListSchemaTableSizeData = append(ListSchemaTableSizeData, o)
			}
		case "ListSchemaTableRowsTOP":
			var oListSchemaTableRowsTOP []*message.SchemaTableRowsTOP
			err = json.Unmarshal(jsonData, &oListSchemaTableRowsTOP)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableRowsTOP {
				ListSchemaTableRowsTOP = append(ListSchemaTableRowsTOP, o)
			}
		case "ListSchemaTableAvgRowLengthTOP":
			var oListSchemaTableAvgRowLengthTOP []*message.SchemaTableAvgRowLengthTOP
			err = json.Unmarshal(jsonData, &oListSchemaTableAvgRowLengthTOP)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableAvgRowLengthTOP {
				ListSchemaTableAvgRowLengthTOP = append(ListSchemaTableAvgRowLengthTOP, o)
			}
		case "ListSchemaCodeObject":
			var oListSchemaCodeObject []*message.SchemaCodeObject
			err = json.Unmarshal(jsonData, &oListSchemaCodeObject)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaCodeObject {
				ListSchemaCodeObject = append(ListSchemaCodeObject, o)
			}
		case "ListSchemaSynonymObject":
			var oListSchemaSynonymObject []*message.SchemaSynonymObject
			err = json.Unmarshal(jsonData, &oListSchemaSynonymObject)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaSynonymObject {
				ListSchemaSynonymObject = append(ListSchemaSynonymObject, o)
			}
		case "ListSchemaMaterializedViewObject":
			var oListSchemaMaterializedViewObject []*message.SchemaMaterializedViewObject
			err = json.Unmarshal(jsonData, &oListSchemaMaterializedViewObject)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaMaterializedViewObject {
				ListSchemaMaterializedViewObject = append(ListSchemaMaterializedViewObject, o)
			}
		case "ListSchemaTableNumberTypeEqual0":
			var oListSchemaTableNumberTypeEqual0 []*message.SchemaTableNumberTypeEqual0
			err = json.Unmarshal(jsonData, &oListSchemaTableNumberTypeEqual0)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			for _, o := range oListSchemaTableNumberTypeEqual0 {
				ListSchemaTableNumberTypeEqual0 = append(ListSchemaTableNumberTypeEqual0, o)
			}

		default:
			log.Infof("v[\"struct_name\"]--unknown: %v\n", v["struct_name"])
		}
	}
	htmlData := &message.ObjectCompatibleALL{
		// Title:                              title,
		ListOracleOverview: ListOracleOverview,
		// ListObjectCompatibleOverview:       listObjectCompatibleOverview,
		// ListObjectCompatibleByObject:       listObjectCompatibleByObject,
		// ListObjectCompatibleBySchema:       listObjectCompatibleBySchema,
		// ListObjectCompatibleBySchemaObject: listObjectCompatibleBySchemaObject,

		ListSchemaColumnTypeCompatibles:         ListSchemaColumnTypeCompatibles,
		ListSchemaTableTypeCompatibles:          ListSchemaTableTypeCompatibles,
		ListSchemaConstraintTypeCompatibles:     ListSchemaConstraintTypeCompatibles,
		ListSchemaIndexTypeCompatibles:          ListSchemaIndexTypeCompatibles,
		ListSchemaSequenceCompatibles:           ListSchemaSequenceCompatibles,
		ListSchemaDefaultValueCompatibles:       ListSchemaDefaultValueCompatibles,
		ListSchemaViewTypeCompatibles:           ListSchemaViewTypeCompatibles,
		ListSchemaObjectTypeCompatibles:         ListSchemaObjectTypeCompatibles,
		ListLobTypeCompatibles:                  ListLobTypeCompatibles,
		ListSchemaPartitionTypeCompatibles:      ListSchemaPartitionTypeCompatibles,
		ListSchemaSubPartitionTypeCompatibles:   ListSchemaSubPartitionTypeCompatibles,
		ListSchemaTemporaryTableTypeCompatibles: ListSchemaTemporaryTableTypeCompatibles,

		ListSchemaJobCompatibles:               ListSchemaJobCompatibles,
		ListSchemaSynonymCompatibles:           ListSchemaSynonymCompatibles,
		ListSchemaPackageBodyCompatibles:       ListSchemaPackageBodyCompatibles,
		ListSchemaPackageCompatibles:           ListSchemaPackageCompatibles,
		ListSchemaTriggerCompatibles:           ListSchemaTriggerCompatibles,
		ListSchemaProcedureCompatibles:         ListSchemaProcedureCompatibles,
		ListSchemaFunctionCompatibles:          ListSchemaFunctionCompatibles,
		ListSchemaDatabaseLinkCompatibles:      ListSchemaDatabaseLinkCompatibles,
		ListSchemaLobPartitionCompatibles:      ListSchemaLobPartitionCompatibles,
		ListSchemaLobSubpartitionCompatibles:   ListSchemaLobSubpartitionCompatibles,
		ListSchemaIndexSubpartitionCompatibles: ListSchemaIndexSubpartitionCompatibles,
		ListSchemaTypeCompatibles:              ListSchemaTypeCompatibles,

		ListSchemaPartitionTableCountsCheck:  ListSchemaPartitionTableCountsCheck,
		ListSchemaTableRowLengthCheck:        ListSchemaTableRowLengthCheck,
		ListSchemaTableIndexRowLengthCheck:   ListSchemaTableIndexRowLengthCheck,
		ListSchemaTableColumnCountsCheck:     ListSchemaTableColumnCountsCheck,
		ListSchemaTableIndexCountsCheck:      ListSchemaTableIndexCountsCheck,
		ListSchemaTableNameLengthCheck:       ListSchemaTableNameLengthCheck,
		ListSchemaViewNameLengthCheck:        ListSchemaViewNameLengthCheck,
		ListSchemaSequenceNameLengthCheck:    ListSchemaSequenceNameLengthCheck,
		ListSchemaTableIndexNameLengthCheck:  ListSchemaTableIndexNameLengthCheck,
		ListSchemaTableColumnNameLengthCheck: ListSchemaTableColumnNameLengthCheck,
		ListUsernameLengthCheck:              ListUsernameLengthCheck,

		ListSchemaActiveSession:          ListSchemaActiveSession,
		ListSchemaTableSizeData:          ListSchemaTableSizeData,
		ListSchemaTableRowsTOP:           ListSchemaTableRowsTOP,
		ListSchemaTableAvgRowLengthTOP:   ListSchemaTableAvgRowLengthTOP,
		ListSchemaCodeObject:             ListSchemaCodeObject,
		ListSchemaSynonymObject:          ListSchemaSynonymObject,
		ListSchemaMaterializedViewObject: ListSchemaMaterializedViewObject,
		ListSchemaTableNumberTypeEqual0:  ListSchemaTableNumberTypeEqual0,
		// ListOraDbaConstraint:             listOraDbaConstraint,
		// ListOraDbaIndex:                  listOraDbaIndex,
		// ListOraDbaSequence:               listOraDbaSequence,

		// ListOraDbaTabColumns:        listOraDbaTabColumns,
		// ListDbaTabColumnsDefault:    listDbaTabColumnsDefault,
		// ListAllPartTables:           listAllPartTables,
		// ListDbaTables:               listDbaTables,
		// ListAllObjects:              listAllObjects,
		// ListOraLobs:                 listOraLobs,
		// ListOraTableTypeCompatibles: listOraTableTypeCompatibles,
		// ListOraDbaViews:             listOraDbaViews,
		// ListOraSubPartitioning:      listOraSubPartitioning,

		// ListJob:               listJob,
		// ListSynonym:           listSynonym,
		// ListPackageBody:       listPackageBody,
		// ListPackage:           listPackage,
		// ListTrigger:           listTrigger,
		// ListProcedure:         listProcedure,
		// ListFunction:          listFunction,
		// ListDataBaseLink:      listDataBaseLink,
		// ListLobSubpartition:   listLobSubpartition,
		// ListType:              listType,
		// ListIndexSubpartition: listIndexSubpartition,
		// ListLobPartition:      listLobPartition,

		// ListPackageBodyCode:   listPackageBodyCode,
		// ListFunctionBodyCode:  listFunctionCode,
		// ListProcedureBodyCode: listProcedureCode,
	}

	return htmlData, nil
}

func (rw *AssessReadWrite) GetListObjectCompatibleBySchema(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) ([]*message.ObjectCompatibleBySchema, error) {
	var schemaList string
	for _, schema := range schemas {
		schemaList += fmt.Sprintf("'%s',", schema.SchemaNameS)
	}
	schemaList = schemaList[:len(schemaList)-1]

	sql := fmt.Sprintf(`select schema_name,
	sum(assess_total) assess_total,
	sum(compatible) compatible,
	sum(incompatible) incompatible, 
	sum(convertible) convertible, 
	sum(inconvertible) inconvertible 
    from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0
	group by schema_name`, channelId, taskId, schemaList)

	_, data, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleBySchema failed, err: %v", err)
	}

	var listObjectCompatibleBySchema []*message.ObjectCompatibleBySchema
	for _, ow := range data {
		listObjectCompatibleBySchema = append(listObjectCompatibleBySchema, &message.ObjectCompatibleBySchema{
			Schema:        ow["schema_name"],
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}

	// 修正返回类型，返回指针类型
	return listObjectCompatibleBySchema, nil
}

func (rw *AssessReadWrite) GetListObjectCompatibleBySchemaObject(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) ([]*message.ObjectCompatibleByObject, error) {
	var schemaList string
	for _, schema := range schemas {
		schemaList += fmt.Sprintf("'%s',", schema.SchemaNameS)
	}
	schemaList = schemaList[:len(schemaList)-1]

	sql := fmt.Sprintf(`select assess_name,
	sum(assess_total) assess_total,
	sum(compatible) compatible,
	sum(incompatible) incompatible, 
	sum(convertible) convertible, 
	sum(inconvertible) inconvertible 
    from object_assess_details oad  
	where channel_id =%d 
	and task_id=%d 
	and schema_name in (%s) 
	and assess_type='OBJECT_TYPE_COMPATIBLE'
	and assess_total>0
	group by assess_name`, channelId, taskId, schemaList)

	_, data, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query listObjectCompatibleByObject failed, err: %v", err)
	}

	var listObjectCompatibleByObject []*message.ObjectCompatibleByObject
	for _, ow := range data {
		var assessNameCh = ""
		switch ow["assess_name"] {
		case "COLUMN_TYPE_COMPATIBLE":
			{
				assessNameCh = "字段数据类型"
			}
		case "TABLE_TYPE_COMPATIBLE":
			{
				assessNameCh = "表兼容性"
			}
		case "CONSTRAINT_TYPE_COMPATIBLE":
			{
				assessNameCh = "约束"
			}
		case "INDEX_TYPE_COMPATIBLE":
			{
				assessNameCh = "索引"
			}
		case "DEFAULT_VALUE_COMPATIBLE":
			{
				assessNameCh = "字段默认值设置"
			}
		case "OBJECT_TYPE_COMPATIBLE":
			{
				assessNameCh = "其它对象"
			}
		case "VIEW_TYPE_COMPATIBLE":
			{
				assessNameCh = "视图"
			}
		case "PARTITION_TYPE_COMPATIBLE":
			{
				assessNameCh = "分区表"
			}
		case "TEMPORARY_TABLE_TYPE_COMPATIBLE":
			{
				assessNameCh = "临时表"
			}
		case "SEQUENCE_COMPATIBLE":
			{
				assessNameCh = "SEQUENCE"
			}
		case "JOB_TYPE_COMPATIBLE":
			{
				assessNameCh = "JOB"
			}
		case "PACKAGE_BODY_COMPATIBLE":
			{
				assessNameCh = "PACKAGE BODY"
			}
		case "PACKAGE_COMPATIBLE":
			{
				assessNameCh = "PACKAGE"
			}
		case "PROCEDURE_COMPATIBLE":
			{
				assessNameCh = "PROCEDURE"
			}
		case "FUNCTION_COMPATIBLE":
			{
				assessNameCh = "FUNCTION"
			}
		case "INDEX_SUBPARTITION_COMPATIBLE":
			{
				assessNameCh = "INDEX SUBPARTITION"
			}
		case "LOB_TYPE_COMPATIBLE":
			{
				assessNameCh = "LOB"
			}
		case "SUBPARTITION_TYPE_COMPATIBLE":
			{
				assessNameCh = "SUBPARTITION"
			}

		case "SYNONYM_TYPE_COMPATIBLE":
			{
				assessNameCh = "SYNONYM"
			}
		case "BODY_COMPATIBLE":
			{
				assessNameCh = "BODY"
			}
		case "TRIGGER_COMPATIBLE":
			{
				assessNameCh = "TRIGGER"
			}
		case "TYPE_COMPATIBLE":
			{
				assessNameCh = "TYPE"
			}
		case "DATABASE_LINK_COMPATIBLE":
			{
				assessNameCh = "DATABASE LINK"
			}
		case "LOB_PARTITION_COMPATIBLE":
			{
				assessNameCh = "LOB PARTITION"
			}
		case "LOB_SUBPARTITION_COMPATIBLE":
			{
				assessNameCh = "LOB SUBPARTITION"
			}
		default:
			{
				assessNameCh = ow["assess_name"]
			}
		}
		listObjectCompatibleByObject = append(listObjectCompatibleByObject, &message.ObjectCompatibleByObject{
			AssessName:    assessNameCh,
			AssessTotal:   ow["assess_total"],
			Compatible:    ow["compatible"],
			InCompatible:  ow["incompatible"],
			Convertible:   ow["convertible"],
			InConvertible: ow["inconvertible"],
		})
	}
	return listObjectCompatibleByObject, nil
}

func (rw *AssessReadWrite) GetListOracleOverview(ctx context.Context, timsDB *sql.DB, channelId int, taskId int, schemas []*channel.ChannelSchema) ([]*message.OracleOverview, error) {
	var ListOracleOverview []*message.OracleOverview

	var schemaList string
	for _, schema := range schemas {
		schemaList += fmt.Sprintf("'%s',", schema.SchemaNameS)
	}
	schemaList = schemaList[:len(schemaList)-1]

	// var timsDB *sql.DB
	sql := fmt.Sprintf(`select channel_id,schema_name,assess_type,assess_name, detail,struct_name 
		from object_assess_details oad 
		where channel_id =%d 
		and task_id=%d 
		and schema_name in (%s) 
		and assess_total>0
		order by assess_type,schema_name`, channelId, taskId, schemaList)
	_, data, err := oracle.Query(ctx, timsDB, sql)
	if err != nil {
		log.Errorf("Query object_assess_details failed, err: %v", err)
	}
	for _, v := range data {
		jsonData := []byte(v["detail"])
		switch v["struct_name"] {
		case "ListOracleOverview":
			var oListOracleOverview *message.OracleOverview
			err = json.Unmarshal(jsonData, &oListOracleOverview)
			if err != nil {
				log.Errorf("err: %v\n", err)
			}
			ListOracleOverview = []*message.OracleOverview{}
			ListOracleOverview = append(ListOracleOverview, oListOracleOverview)

		default:
			log.Infof("v[\"struct_name\"]--unknown: %v\n", v["struct_name"])
		}
	}

	return ListOracleOverview, nil
}
