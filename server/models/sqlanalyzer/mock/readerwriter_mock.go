// Code generated by MockGen. DO NOT EDIT.
// Source: ./sqlanalyzer/readerwriter.go

// Package mocksqlanalyzerreaderwriter is a generated GoMock package.
package mocksqlanalyzerreaderwriter

import (
	context "context"
	reflect "reflect"
	time "time"

	sqlanalyzer "gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	gomock "github.com/golang/mock/gomock"
)

// MockSqlAnalyzerReaderWriter is a mock of SqlAnalyzerReaderWriter interface.
type MockSqlAnalyzerReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockSqlAnalyzerReaderWriterMockRecorder
}

// MockSqlAnalyzerReaderWriterMockRecorder is the mock recorder for MockSqlAnalyzerReaderWriter.
type MockSqlAnalyzerReaderWriterMockRecorder struct {
	mock *MockSqlAnalyzerReaderWriter
}

// NewMockSqlAnalyzerReaderWriter creates a new mock instance.
func NewMockSqlAnalyzerReaderWriter(ctrl *gomock.Controller) *MockSqlAnalyzerReaderWriter {
	mock := &MockSqlAnalyzerReaderWriter{ctrl: ctrl}
	mock.recorder = &MockSqlAnalyzerReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSqlAnalyzerReaderWriter) EXPECT() *MockSqlAnalyzerReaderWriterMockRecorder {
	return m.recorder
}

// BatchCreateSQLAnalyzeTask mocks base method.
func (m *MockSqlAnalyzerReaderWriter) BatchCreateSQLAnalyzeTask(ctx context.Context, tasks []sqlanalyzer.EnvDeployTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateEnvDeployTask", ctx, tasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateSQLAnalyzeTask indicates an expected call of BatchCreateSQLAnalyzeTask.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) BatchCreateSQLAnalyzeTask(ctx, tasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateEnvDeployTask", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).BatchCreateSQLAnalyzeTask), ctx, tasks)
}

// BatchDeleteSQLAnalyzeTasksByTaskIds mocks base method.
func (m *MockSqlAnalyzerReaderWriter) BatchDeleteSQLAnalyzeTasksByTaskIds(ctx context.Context, taskIds []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteSQLAnalyzeTasksByTaskIds", ctx, taskIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteSQLAnalyzeTasksByTaskIds indicates an expected call of BatchDeleteSQLAnalyzeTasksByTaskIds.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) BatchDeleteSQLAnalyzeTasksByTaskIds(ctx, taskIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteSQLAnalyzeTasksByTaskIds", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).BatchDeleteSQLAnalyzeTasksByTaskIds), ctx, taskIds)
}

// BatchUpdateSQLAnalyzeTask mocks base method.
func (m *MockSqlAnalyzerReaderWriter) BatchUpdateSQLAnalyzeTask(ctx context.Context, tasks []*sqlanalyzer.EnvDeployTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateSQLAnalyzeTask", ctx, tasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateSQLAnalyzeTask indicates an expected call of BatchUpdateSQLAnalyzeTask.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) BatchUpdateSQLAnalyzeTask(ctx, tasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateSQLAnalyzeTask", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).BatchUpdateSQLAnalyzeTask), ctx, tasks)
}

// BatchUpdateSQLAnalyzeTaskLastRunTime mocks base method.
func (m *MockSqlAnalyzerReaderWriter) BatchUpdateSQLAnalyzeTaskLastRunTime(ctx context.Context, sqlAnalyzeIds []int, lastExecTime time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateSQLAnalyzeTaskLastRunTime", ctx, sqlAnalyzeIds, lastExecTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateSQLAnalyzeTaskLastRunTime indicates an expected call of BatchUpdateSQLAnalyzeTaskLastRunTime.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) BatchUpdateSQLAnalyzeTaskLastRunTime(ctx, sqlAnalyzeIds, lastExecTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateSQLAnalyzeTaskLastRunTime", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).BatchUpdateSQLAnalyzeTaskLastRunTime), ctx, sqlAnalyzeIds, lastExecTime)
}

// CreateSqlAnalyzerSummary mocks base method.
func (m *MockSqlAnalyzerReaderWriter) CreateSqlAnalyzerSummary(ctx context.Context, summary *sqlanalyzer.SqlResultSummary) (*sqlanalyzer.SqlResultSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSqlAnalyzerSummary", ctx, summary)
	ret0, _ := ret[0].(*sqlanalyzer.SqlResultSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSqlAnalyzerSummary indicates an expected call of CreateSqlAnalyzerSummary.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) CreateSqlAnalyzerSummary(ctx, summary interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSqlAnalyzerSummary", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).CreateSqlAnalyzerSummary), ctx, summary)
}

// CreateSqlExecDetail mocks base method.
func (m *MockSqlAnalyzerReaderWriter) CreateSqlExecDetail(ctx context.Context, sqlExecInfo *sqlanalyzer.O2tSqlsetExecInfo) (*sqlanalyzer.O2tSqlsetExecInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSqlExecDetail", ctx, sqlExecInfo)
	ret0, _ := ret[0].(*sqlanalyzer.O2tSqlsetExecInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSqlExecDetail indicates an expected call of CreateSqlExecDetail.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) CreateSqlExecDetail(ctx, sqlExecInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSqlExecDetail", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).CreateSqlExecDetail), ctx, sqlExecInfo)
}

// CreateSqlStmtExecResult mocks base method.
func (m *MockSqlAnalyzerReaderWriter) CreateSqlStmtExecResult(ctx context.Context, sqlExec *sqlanalyzer.SqlStmtExecResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSqlStmtExecResult", ctx, sqlExec)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSqlStmtExecResult indicates an expected call of CreateSqlStmtExecResult.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) CreateSqlStmtExecResult(ctx, sqlExec interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSqlStmtExecResult", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).CreateSqlStmtExecResult), ctx, sqlExec)
}

// DelSqlExecDetail mocks base method.
func (m *MockSqlAnalyzerReaderWriter) DelSqlExecDetail(ctx context.Context, taskId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelSqlExecDetail", ctx, taskId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelSqlExecDetail indicates an expected call of DelSqlExecDetail.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) DelSqlExecDetail(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSqlExecDetail", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).DelSqlExecDetail), ctx, taskId)
}

// GetHtmlTemplateDetail mocks base method.
func (m *MockSqlAnalyzerReaderWriter) GetHtmlTemplateDetail(ctx context.Context, htmlName string) ([]*sqlanalyzer.HtmlTemplateDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHtmlTemplateDetail", ctx, htmlName)
	ret0, _ := ret[0].([]*sqlanalyzer.HtmlTemplateDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHtmlTemplateDetail indicates an expected call of GetHtmlTemplateDetail.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) GetHtmlTemplateDetail(ctx, htmlName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHtmlTemplateDetail", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).GetHtmlTemplateDetail), ctx, htmlName)
}

// GetSqlAnalyzerSummaryByTaskId mocks base method.
func (m *MockSqlAnalyzerReaderWriter) GetSqlAnalyzerSummaryByTaskId(ctx context.Context, tasklId int, startTime time.Time) ([]*sqlanalyzer.SqlResultSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSqlAnalyzerSummaryByTaskId", ctx, tasklId, startTime)
	ret0, _ := ret[0].([]*sqlanalyzer.SqlResultSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSqlAnalyzerSummaryByTaskId indicates an expected call of GetSqlAnalyzerSummaryByTaskId.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) GetSqlAnalyzerSummaryByTaskId(ctx, tasklId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSqlAnalyzerSummaryByTaskId", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).GetSqlAnalyzerSummaryByTaskId), ctx, tasklId, startTime)
}

// GetSqlExecDetail mocks base method.
func (m *MockSqlAnalyzerReaderWriter) GetSqlExecDetail(ctx context.Context, taskId int, startTime time.Time) ([]*sqlanalyzer.O2tSqlsetExecInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSqlExecDetail", ctx, taskId, startTime)
	ret0, _ := ret[0].([]*sqlanalyzer.O2tSqlsetExecInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSqlExecDetail indicates an expected call of GetSqlExecDetail.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) GetSqlExecDetail(ctx, taskId, startTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSqlExecDetail", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).GetSqlExecDetail), ctx, taskId, startTime)
}

// GetSqlStmtExecResultByErrCode mocks base method.
func (m *MockSqlAnalyzerReaderWriter) GetSqlStmtExecResultByErrCode(ctx context.Context, taskId int, schema string) ([]*sqlanalyzer.SqlStmtExecResultByErrCode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSqlStmtExecResultByErrCode", ctx, taskId, schema)
	ret0, _ := ret[0].([]*sqlanalyzer.SqlStmtExecResultByErrCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSqlStmtExecResultByErrCode indicates an expected call of GetSqlStmtExecResultByErrCode.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) GetSqlStmtExecResultByErrCode(ctx, taskId, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSqlStmtExecResultByErrCode", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).GetSqlStmtExecResultByErrCode), ctx, taskId, schema)
}

// GetSqlStmtExecResultByStatus mocks base method.
func (m *MockSqlAnalyzerReaderWriter) GetSqlStmtExecResultByStatus(ctx context.Context, taskId int, startTime time.Time, schema string) ([]*sqlanalyzer.SqlStmtExecResultByStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSqlStmtExecResultByStatus", ctx, taskId, startTime, schema)
	ret0, _ := ret[0].([]*sqlanalyzer.SqlStmtExecResultByStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSqlStmtExecResultByStatus indicates an expected call of GetSqlStmtExecResultByStatus.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) GetSqlStmtExecResultByStatus(ctx, taskId, startTime, schema interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSqlStmtExecResultByStatus", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).GetSqlStmtExecResultByStatus), ctx, taskId, startTime, schema)
}

// ListSQLAnalyzeTasks mocks base method.
func (m *MockSqlAnalyzerReaderWriter) ListSQLAnalyzeTasks(ctx context.Context, task *sqlanalyzer.EnvDeployTask) ([]*sqlanalyzer.EnvDeployTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSQLAnalyzeTasks", ctx, task)
	ret0, _ := ret[0].([]*sqlanalyzer.EnvDeployTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSQLAnalyzeTasks indicates an expected call of ListSQLAnalyzeTasks.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) ListSQLAnalyzeTasks(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSQLAnalyzeTasks", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).ListSQLAnalyzeTasks), ctx, task)
}

// ListSQLAnalyzeTasksWithTaskNames mocks base method.
func (m *MockSqlAnalyzerReaderWriter) ListSQLAnalyzeTasksWithTaskNames(ctx context.Context, taskId int, taskNames []string) ([]*sqlanalyzer.EnvDeployTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSQLAnalyzeTasksWithTaskNames", ctx, taskId, taskNames)
	ret0, _ := ret[0].([]*sqlanalyzer.EnvDeployTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSQLAnalyzeTasksWithTaskNames indicates an expected call of ListSQLAnalyzeTasksWithTaskNames.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) ListSQLAnalyzeTasksWithTaskNames(ctx, taskId, taskNames interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSQLAnalyzeTasksWithTaskNames", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).ListSQLAnalyzeTasksWithTaskNames), ctx, taskId, taskNames)
}

// ListSqlStmtExecResult mocks base method.
func (m *MockSqlAnalyzerReaderWriter) ListSqlStmtExecResult(ctx context.Context, sqlExec *sqlanalyzer.SqlStmtExecResult) ([]*sqlanalyzer.SqlStmtExecResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSqlStmtExecResult", ctx, sqlExec)
	ret0, _ := ret[0].([]*sqlanalyzer.SqlStmtExecResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSqlStmtExecResult indicates an expected call of ListSqlStmtExecResult.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) ListSqlStmtExecResult(ctx, sqlExec interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSqlStmtExecResult", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).ListSqlStmtExecResult), ctx, sqlExec)
}

// ListSqlStmtExecResultBySchema mocks base method.
func (m *MockSqlAnalyzerReaderWriter) ListSqlStmtExecResultBySchema(ctx context.Context, taskId int, schema, status string) ([]*sqlanalyzer.SqlStmtExecResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSqlStmtExecResultBySchema", ctx, taskId, schema, status)
	ret0, _ := ret[0].([]*sqlanalyzer.SqlStmtExecResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSqlStmtExecResultBySchema indicates an expected call of ListSqlStmtExecResultBySchema.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) ListSqlStmtExecResultBySchema(ctx, taskId, schema, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSqlStmtExecResultBySchema", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).ListSqlStmtExecResultBySchema), ctx, taskId, schema, status)
}

// MergeSqlStmtExecResult mocks base method.
func (m *MockSqlAnalyzerReaderWriter) MergeSqlStmtExecResult(ctx context.Context, sqlExec *sqlanalyzer.SqlStmtExecResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MergeSqlStmtExecResult", ctx, sqlExec)
	ret0, _ := ret[0].(error)
	return ret0
}

// MergeSqlStmtExecResult indicates an expected call of MergeSqlStmtExecResult.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) MergeSqlStmtExecResult(ctx, sqlExec interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MergeSqlStmtExecResult", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).MergeSqlStmtExecResult), ctx, sqlExec)
}

// UpdateSQLAnalyzeTask mocks base method.
func (m *MockSqlAnalyzerReaderWriter) UpdateSQLAnalyzeTask(ctx context.Context, task *sqlanalyzer.EnvDeployTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSQLAnalyzeTask", ctx, task)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSQLAnalyzeTask indicates an expected call of UpdateSQLAnalyzeTask.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) UpdateSQLAnalyzeTask(ctx, task interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSQLAnalyzeTask", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).UpdateSQLAnalyzeTask), ctx, task)
}

// UpdateSQLAnalyzeTaskSQLById mocks base method.
func (m *MockSqlAnalyzerReaderWriter) UpdateSQLAnalyzeTaskSQLById(ctx context.Context, sqlAnalyzeId int, sql string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSQLAnalyzeTaskSQLById", ctx, sqlAnalyzeId, sql)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSQLAnalyzeTaskSQLById indicates an expected call of UpdateSQLAnalyzeTaskSQLById.
func (mr *MockSqlAnalyzerReaderWriterMockRecorder) UpdateSQLAnalyzeTaskSQLById(ctx, sqlAnalyzeId, sql interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSQLAnalyzeTaskSQLById", reflect.TypeOf((*MockSqlAnalyzerReaderWriter)(nil).UpdateSQLAnalyzeTaskSQLById), ctx, sqlAnalyzeId, sql)
}
