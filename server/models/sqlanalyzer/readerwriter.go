package sqlanalyzer

import (
	"context"
	"time"
)

type SqlAnalyzerReaderWriter interface {
	CreateSqlAnalyzerSummary(ctx context.Context, summary *SqlResultSummary) (*SqlResultSummary, error)
	GetSqlAnalyzerSummaryByTaskId(ctx context.Context, tasklId int, startTime time.Time) ([]*SqlResultSummary, error)
	CreateSqlExecDetail(ctx context.Context, sqlExecInfo *O2tSqlsetExecInfo) (*O2tSqlsetExecInfo, error)
	SaveSqlExecSummary(ctx context.Context, summary *SqlStmtExecSummary) (*SqlStmtExecSummary, error)
	RemoveSqlExecSummary(ctx context.Context, taskId int) error
	GetSqlExecSummary(ctx context.Context, taskId int) (*SqlStmtExecSummary, error)
	GetSqlExecDetail(ctx context.Context, taskId int, startTime time.Time) ([]*O2tSqlsetExecInfo, error)
	DelSqlExecDetail(ctx context.Context, taskId int) error
	BatchDeleteSQLAnalyzeTasksByTaskIds(ctx context.Context, taskIds []int) error
	CreateSqlStmtExecResult(ctx context.Context, sqlExec *SqlStmtExecResult) error
	ListSqlStmtExecResult(ctx context.Context, sqlExec *SqlStmtExecResult) ([]*SqlStmtExecResult, error)
	ListSqlStmtExecResultBySchema(ctx context.Context, taskId int, schemas []string, status string) ([]*SqlStmtExecResult, error)
	ListSqlStmtExecResultBySchemaAndTopN(ctx context.Context, taskId int, schemas []string, statusList []string, topn int) ([]*SqlStmtExecResult, error)
	MergeSqlStmtExecResult(ctx context.Context, sqlExec *SqlStmtExecResult) error
	DeleteSqlStmtExecResultByChannelIdTaskId(ctx context.Context, channelId, taskId int) error
	GetSqlStmtExecResultByStatus(ctx context.Context, taskId int, startTime time.Time, schema string) ([]*SqlStmtExecResultByStatus, error)
	GetSqlStmtExecResultByErrCode(ctx context.Context, taskId int, schemas []string) ([]*SqlStmtExecResultByErrCode, error)
	GetHtmlTemplateDetail(ctx context.Context, htmlName string) ([]*HtmlTemplateDetail, error)

	// User operation methods for SQL execution results
	UpdateSqlStmtExecResultUserOperation(ctx context.Context, sqlExecIds []int, userOperateStatus, userOperateBy, userOperateRemark string) error
	UpdateSqlStmtExecResultUserOperationByOraSqlId(ctx context.Context, taskId int, oraSqlIds []string, userOperateStatus, userOperateRemark string, sqlIdToSchemaMap map[string]string) (int, error)
	CreateSqlStmtExecResultHistory(ctx context.Context, history *SqlStmtExecResultHistory) error
	ListSqlStmtExecResultHistory(ctx context.Context, taskId int, sqlExecId int) ([]*SqlStmtExecResultHistory, error)
	ListSqlStmtExecResultWithPagination(ctx context.Context, taskId int, schemas []string, statusList []string, userOperateStatus []string, oraSqlId, oraParsingSchemaName, oraSqlText string, page, pageSize int) ([]*SqlStmtExecResult, int64, error)
	GetSqlStmtExecResultStatistics(ctx context.Context, taskId int, schemas []string) (map[string]int64, error)

	// V2 User operation methods for Oracle data source
	CreateOrUpdateSqlStmtUserOperationV2(ctx context.Context, userOp *SqlStmtUserOperationV2) error
	BatchCreateOrUpdateSqlStmtUserOperationV2(ctx context.Context, userOps []*SqlStmtUserOperationV2) error
	GetSqlStmtUserOperationV2ByKeys(ctx context.Context, taskId int, keys []SqlStmtUserOperationV2Key) (map[string]*SqlStmtUserOperationV2, error)
	GetSqlStmtUserOperationV2ByTaskIdAndStatus(ctx context.Context, taskId int, statusList []string) (map[string]*SqlStmtUserOperationV2, error)
	CountSqlStmtUserOperationV2ByTaskIdAndStatus(ctx context.Context, taskId int, statusList []string) (int64, error)
	CreateSqlStmtUserOperationV2History(ctx context.Context, history *SqlStmtUserOperationV2History) error
	ListSqlStmtUserOperationV2History(ctx context.Context, taskId int, oraSqlId string) ([]*SqlStmtUserOperationV2History, error)

	ListSQLAnalyzeTasksWithTaskNames(ctx context.Context, taskId int, taskNames []string) ([]*EnvDeployTask, error)
	ListSQLAnalyzeTasks(ctx context.Context, task *EnvDeployTask) ([]*EnvDeployTask, error)
	UpdateSQLAnalyzeTask(ctx context.Context, task *EnvDeployTask) error
	UpdateSQLAnalyzeTaskSQLById(ctx context.Context, sqlAnalyzeId int, sql string) error
	BatchUpdateSQLAnalyzeTaskLastRunTime(ctx context.Context, sqlAnalyzeIds []int, lastExecTime time.Time) error
	BatchUpdateSQLAnalyzeTask(ctx context.Context, tasks []*EnvDeployTask) error
	BatchCreateEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error
	ClearEnvDeployTask(ctx context.Context, tasks []*EnvDeployTask) error
}
