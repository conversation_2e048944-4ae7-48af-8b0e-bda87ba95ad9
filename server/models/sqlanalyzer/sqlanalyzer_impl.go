package sqlanalyzer

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"

	tmderrors "gitee.com/pingcap_enterprise/tms/common/errors"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type SqlAnalyzerReadWrite struct {
	dbCommon.GormDB
}

func NewSqlAnalyzerReadWrite(db *gorm.DB) *SqlAnalyzerReadWrite {
	return &SqlAnalyzerReadWrite{GormDB: dbCommon.WrapDB(db)}
}

func (rw *SqlAnalyzerReadWrite) CreateSqlAnalyzerSummary(ctx context.Context, summary *SqlResultSummary) (*SqlResultSummary, error) {
	err := rw.DB(ctx).Create(summary).Error
	if err != nil {
		log.Errorf("create summary info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return summary, nil
}

func (rw *SqlAnalyzerReadWrite) GetSqlAnalyzerSummaryByTaskId(ctx context.Context, taskId int, startTime time.Time) ([]*SqlResultSummary, error) {
	sqlResultSummary := make([]*SqlResultSummary, 0)
	err := rw.DB(ctx).Model(&SqlResultSummary{}).Where("task_id = ? and start_time >= ?", taskId, startTime).Find(&sqlResultSummary).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, tmderrors.NewErrorf(tmderrors.TIMS_RECORD_NOT_EXIST, "query task id %d not found", taskId)
	} else if err != nil {
		log.Errorf("get SqlAnalyzer info failed. task id is %d, err:%v", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return sqlResultSummary, nil
}

func (rw *SqlAnalyzerReadWrite) CreateSqlExecDetail(ctx context.Context, sqlExecInfo *O2tSqlsetExecInfo) (*O2tSqlsetExecInfo, error) {
	err := rw.DB(ctx).Create(sqlExecInfo).Error
	if err != nil {
		log.Errorf("create sqlExecInfo info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return sqlExecInfo, nil
}

func (rw *SqlAnalyzerReadWrite) GetSqlExecDetail(ctx context.Context, taskId int, startTime time.Time) ([]*O2tSqlsetExecInfo, error) {
	records := make([]*O2tSqlsetExecInfo, 0)
	err := rw.DB(ctx).Model(&O2tSqlsetExecInfo{}).Where("task_id = ? and UpdateTime >= ?", taskId, startTime).Find(&records).Error
	if err != nil {
		log.Errorf("GetSqlExecDetail failed. taskId is %d, startTime:%v, err:%s", taskId, startTime, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *SqlAnalyzerReadWrite) DelSqlExecDetail(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Where("task_id = ? ", taskId).Delete(&O2tSqlsetExecInfo{}).Error
	if err != nil {
		log.Errorf("DelSqlExecDetail failed. taskId is %d, err:%s", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) CreateSqlStmtExecResult(ctx context.Context, sqlExec *SqlStmtExecResult) error {
	err := rw.DB(ctx).Create(sqlExec).Error
	if err != nil {
		log.Errorf("create sqlExec info to db failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) ListSqlStmtExecResult(ctx context.Context, sqlExec *SqlStmtExecResult) ([]*SqlStmtExecResult, error) {
	records := make([]*SqlStmtExecResult, 0)
	err := rw.DB(ctx).Model(&SqlStmtExecResult{}).Where(sqlExec).Find(&records).Error
	if err != nil {
		log.Errorf("list sqlExec info failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *SqlAnalyzerReadWrite) ListSqlStmtExecResultBySchema(ctx context.Context, taskId int, schemas []string, status string) ([]*SqlStmtExecResult, error) {
	records := make([]*SqlStmtExecResult, 0)
	query := rw.DB(ctx).Model(&SqlStmtExecResult{}).Where("task_id = ?", taskId)
	if len(schemas) > 0 {
		query = query.Where("ora_parsing_schema_name IN ?", schemas)
	}
	if strings.TrimSpace(status) != "" {
		query = query.Where("tidb_exec_status = ?", status)
	}
	err := query.Order("sql_exec_id asc").Find(&records).Error
	if err != nil {
		log.Errorf("list sqlStmtExec info failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *SqlAnalyzerReadWrite) MergeSqlStmtExecResult(ctx context.Context, sqlExec *SqlStmtExecResult) error {
	records := make([]*SqlStmtExecResult, 0)
	err := rw.DB(ctx).Model(&SqlStmtExecResult{}).Where("task_id=? and ora_sql_id=?", sqlExec.TaskId, sqlExec.OraSqlId).Find(&records).Error
	if err != nil {
		log.Errorf("get SqlStmtExecResult info  failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	if len(records) > 0 {
		err = rw.DB(ctx).Unscoped().Where("task_id=? and ora_sql_id=?", sqlExec.TaskId, sqlExec.OraSqlId).Delete(&SqlStmtExecResult{}).Error
		if err != nil {
			log.Errorf("delete SqlStmtExecResult info failed. err:%s", err)
			return dbCommon.WrapDBError(err)
		}
	}
	err = rw.DB(ctx).Create(sqlExec).Error
	if err != nil {
		log.Errorf("merge SqlStmtExecResult info failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) GetSqlStmtExecResultByStatus(ctx context.Context, taskId int, startTime time.Time, schema string) ([]*SqlStmtExecResultByStatus, error) {
	records := make([]*SqlStmtExecResultByStatus, 0)
	query := rw.DB(ctx).Model(&SqlStmtExecResult{}).Select(`channel_id,task_id,ora_parsing_schema_name,
	count(1) as count,
	sum(case when tidb_exec_status='success' then 1 else 0 end) as successnum,
	sum(case when tidb_exec_status='failed' then 1 else 0 end) as failnum,
	sum(case when tidb_exec_status='timeout' then 1 else 0 end) as timeoutnum,
	min(tidb_exec_time) as starttime,
	max(tidb_exec_time) as endtime
	`).Where("task_id = ? and updated_at >= ?", taskId, startTime).Group("channel_id,task_id,ora_parsing_schema_name")
	if strings.TrimSpace(schema) != "" {
		query = query.Where("ora_parsing_schema_name = '" + schema + "'")
	}
	query = query.Order("ora_parsing_schema_name asc")
	err := query.Find(&records).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("batch query table SqlStmtExecResult not found, query cond:%d, schema:%s, starttime:%s", taskId, schema, startTime)
	} else if err != nil {
		log.Errorf("batch get table SqlStmtExecResult failed, query cond:%d, schema:%s, starttime:%s, err:%v", taskId, schema, startTime, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *SqlAnalyzerReadWrite) GetSqlStmtExecResultByErrCode(ctx context.Context, taskId int, schemas []string) ([]*SqlStmtExecResultByErrCode, error) {
	records := make([]*SqlStmtExecResultByErrCode, 0)
	query := rw.DB(ctx).Model(&SqlStmtExecResult{}).Select(`channel_id,task_id,ora_parsing_schema_name,
	tidb_sql_type,tidb_exec_status,tidb_exec_code,min(tidb_exec_msg) tidb_exec_msg,
	count(1) as count,
	min(tidb_exec_time) as starttime,
	max(tidb_exec_time) as endtime
	`).Where("task_id = ? ", taskId).Group("channel_id,task_id,ora_parsing_schema_name,tidb_sql_type,tidb_exec_status,tidb_exec_code")
	if len(schemas) != 0 {
		query = query.Where("ora_parsing_schema_name in ('" + strings.Join(schemas, "'") + "')")
	}
	err := query.Find(&records).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("batch query table SqlStmtExecResult not found, query cond:%d, schema:%v", taskId, schemas)
	} else if err != nil {
		log.Errorf("batch get table SqlStmtExecResult failed, query cond:%d, schema:%v, err:%v", taskId, schemas, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *SqlAnalyzerReadWrite) GetHtmlTemplateDetail(ctx context.Context, htmlName string) ([]*HtmlTemplateDetail, error) {
	var objDetails []*HtmlTemplateDetail
	err := rw.GormDB.DB(ctx).Where("html_name = ? AND html_status = 'online'", htmlName).Order("html_version desc").Order("template_id asc").Limit(1).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table HtmlTemplateDetail not found, query cond: %v", htmlName)
	} else if err != nil {
		log.Errorf("get table HtmlTemplateDetail failed, query cond %d, %v", htmlName, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw *SqlAnalyzerReadWrite) ListSQLAnalyzeTasks(ctx context.Context, task *EnvDeployTask) ([]*EnvDeployTask, error) {
	var tasks []*EnvDeployTask
	err := rw.GormDB.DB(ctx).Where(task).Find(&tasks).Order("task_number asc").Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table sqlanalyze_tasks not found, query cond: %v", task)
	} else if err != nil {
		log.Errorf("get table EnvDeployTask failed, query cond %d, %v", task, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

// UpdateSQLAnalyzeTaskSQL
func (rw *SqlAnalyzerReadWrite) UpdateSQLAnalyzeTask(ctx context.Context, task *EnvDeployTask) error {
	err := rw.DB(ctx).Model(&EnvDeployTask{}).
		Where("sqlanalyze_id = ?", task.SQLAnalyzeId).
		Save(task).Error
	if err != nil {
		return dbCommon.WrapDBError(fmt.Errorf("update table sqlanalyze task failed. err:%v, val:%v", err, task))
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) BatchUpdateSQLAnalyzeTask(ctx context.Context, tasks []*EnvDeployTask) error {
	err := rw.DB(ctx).Model(&EnvDeployTask{}).
		Save(tasks).Error
	if err != nil {
		return dbCommon.WrapDBError(fmt.Errorf("batch update table sqlanalyze task [%s] record failed: %v", tasks, err))
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) BatchUpdateSQLAnalyzeTaskLastRunTime(ctx context.Context, sqlAnalyzeIds []int, lastExecTime time.Time) error {
	err := rw.DB(ctx).Model(&EnvDeployTask{}).
		Where("sqlanalyze_id in (?)", sqlAnalyzeIds).
		Updates(map[string]interface{}{"last_run_time": lastExecTime}).Error
	if err != nil {
		return dbCommon.WrapDBError(fmt.Errorf("batch update table sqlanalyze task [%s] record failed: %v", sqlAnalyzeIds, err))
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) BatchCreateEnvDeployTask(ctx context.Context, tasks []EnvDeployTask) error {
	err := rw.DB(ctx).Create(tasks).Error
	if err != nil {
		return dbCommon.WrapDBError(fmt.Errorf("batch create table sqlanalyze task [%s] record failed: %v", tasks, err))
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) UpdateSQLAnalyzeTaskSQLById(ctx context.Context, sqlAnalyzeId int, sql string) error {
	err := rw.DB(ctx).Model(&EnvDeployTask{}).
		Where("sqlanalyze_id = ?", sqlAnalyzeId).
		Updates(map[string]interface{}{"task_sql": sql}).Error
	if err != nil {
		return dbCommon.WrapDBError(fmt.Errorf("update table sqlanalyze task failed. err:%v, val:%v", err, sqlAnalyzeId))
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) ListSQLAnalyzeTasksWithTaskNames(ctx context.Context, taskId int, taskNames []string) ([]*EnvDeployTask, error) {
	var tasks []*EnvDeployTask
	err := rw.GormDB.DB(ctx).Where("task_id = ? and task_name in (?)", taskId, taskNames).Find(&tasks).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, dbCommon.WrapDBError(fmt.Errorf("query table sqlanalyze_tasks not found, query cond: %v", taskNames))
	} else if err != nil {
		log.Errorf("get table EnvDeployTask failed, query cond %d, %v", taskNames, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tasks, nil
}

func (rw *SqlAnalyzerReadWrite) BatchDeleteSQLAnalyzeTasksByTaskIds(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Unscoped().Where("task_id in (?)", taskIds).Delete(&EnvDeployTask{}).Error
	if err != nil {
		log.Errorf("delete EnvDeployTask info failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

// BatchDeleteSqlStmtExecResultByChannelIdTaskId 分批删除指定渠道ID和任务ID的SQL执行结果
func (rw *SqlAnalyzerReadWrite) BatchDeleteSqlStmtExecResultByChannelIdTaskId(ctx context.Context, channelId, taskId int, batchSize int) error {
	// 默认批次大小为1000，如果传入的批次大小小于100，则使用默认值
	if batchSize < 100 {
		batchSize = 1000
	}

	// 记录开始时间
	startTime := time.Now()

	// 统计要删除的总记录数
	var totalCount int64
	if err := rw.DB(ctx).Model(&SqlStmtExecResult{}).
		Where("channel_id = ? AND task_id = ?", channelId, taskId).
		Count(&totalCount).Error; err != nil {
		log.Errorf("统计待删除记录数失败: %s", err)
		return dbCommon.WrapDBError(err)
	}

	// 如果没有记录需要删除，直接返回
	if totalCount == 0 {
		log.Infof("没有找到符合条件的记录，渠道ID=%d，任务ID=%d", channelId, taskId)
		return nil
	}

	log.Infof("开始分批删除SQL执行结果，渠道ID=%d，任务ID=%d，预计删除记录数=%d", channelId, taskId, totalCount)

	// 分批删除
	var totalDeleted int64 = 0
	var batchCount int = 0

	for {
		batchCount++

		// 查找一批要删除的记录ID
		var ids []int
		if err := rw.DB(ctx).Model(&SqlStmtExecResult{}).
			Where("channel_id = ? AND task_id = ?", channelId, taskId).
			Limit(batchSize).
			Pluck("sql_exec_id", &ids).Error; err != nil {
			log.Errorf("查询待删除记录ID失败, 批次=%d: %s", batchCount, err)
			return dbCommon.WrapDBError(err)
		}

		// 如果没有找到记录，结束循环
		if len(ids) == 0 {
			break
		}

		// 批次开始时间
		batchStartTime := time.Now()

		// 删除这一批记录
		result := rw.DB(ctx).Unscoped().
			Where("sql_exec_id IN ?", ids).
			Delete(&SqlStmtExecResult{})

		if result.Error != nil {
			log.Errorf("删除SQL执行结果批次失败, 批次=%d: %s", batchCount, result.Error)
			return dbCommon.WrapDBError(result.Error)
		}

		// 更新已删除记录计数
		totalDeleted += result.RowsAffected
		batchDuration := time.Since(batchStartTime)

		// 记录每批次的删除情况
		log.Infof("批次 #%d: 删除了 %d/%d 条记录, 耗时 %.2fs, 总计已删除: %d/%d (%.2f%%)",
			batchCount,
			result.RowsAffected,
			len(ids),
			batchDuration.Seconds(),
			totalDeleted,
			totalCount,
			float64(totalDeleted*100)/float64(totalCount))

		// 如果获取的记录数小于批次大小，说明已经处理完毕
		if len(ids) < batchSize {
			break
		}

		// 添加短暂暂停，减轻数据库压力
		// 根据批次执行时间动态调整暂停时间
		pauseDuration := 100 * time.Millisecond
		if batchDuration > 500*time.Millisecond {
			pauseDuration = 200 * time.Millisecond
		}
		time.Sleep(pauseDuration)
	}

	// 统计总执行时间
	totalDuration := time.Since(startTime)

	log.Infof("分批删除SQL执行结果完成: 渠道ID=%d, 任务ID=%d, 总计删除记录=%d, 批次数=%d, 总耗时=%.2f秒",
		channelId, taskId, totalDeleted, batchCount, totalDuration.Seconds())

	return nil
}

// 兼容原有函数，但内部调用分批删除函数
func (rw *SqlAnalyzerReadWrite) DeleteSqlStmtExecResultByChannelIdTaskId(ctx context.Context, channelId, taskId int) error {
	// 直接调用分批删除函数，使用默认批次大小1000
	return rw.BatchDeleteSqlStmtExecResultByChannelIdTaskId(ctx, channelId, taskId, 1000)
}

func (rw *SqlAnalyzerReadWrite) ClearEnvDeployTask(ctx context.Context, tasks []*EnvDeployTask) error {
	ids := make([]int, 0)
	for _, x := range tasks {
		ids = append(ids, x.SQLAnalyzeId)
	}
	return rw.DB(ctx).Model(&EnvDeployTask{}).Where("sqlanalyze_id in (?)", ids).
		Updates(map[string]interface{}{
			"task_status":   constants.TASK_STATUS_NOT_RUNNING,
			"task_log":      "",
			"last_run_time": timeutil.GetTMSNullTime(),
		}).Error
}

func (rw *SqlAnalyzerReadWrite) SaveSqlExecSummary(ctx context.Context, summary *SqlStmtExecSummary) (*SqlStmtExecSummary, error) {
	err := rw.DB(ctx).Save(summary).Error
	if err != nil {
		log.Errorf("save summary info to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return summary, nil
}

func (rw *SqlAnalyzerReadWrite) GetSqlExecSummary(ctx context.Context, taskId int) (*SqlStmtExecSummary, error) {
	summary := &SqlStmtExecSummary{}
	err := rw.DB(ctx).Where("task_id = ?", taskId).Find(summary).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, tmderrors.NewErrorf(tmderrors.TIMS_RECORD_NOT_EXIST, "query task id %d not found", taskId)
	} else if err != nil {
		log.Errorf("get SqlExecSummary info failed. task id is %d, err:%v", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return summary, nil
}

func (rw *SqlAnalyzerReadWrite) RemoveSqlExecSummary(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Where("task_id = ?", taskId).Delete(&SqlStmtExecSummary{}).Error
	if err != nil {
		log.Errorf("remove SqlExecSummary info failed. task id is %d, err:%v", taskId, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw *SqlAnalyzerReadWrite) ListSqlStmtExecResultBySchemaAndTopN(ctx context.Context, taskId int, schemas []string, statusList []string, topn int) ([]*SqlStmtExecResult, error) {

	var sql = `
WITH ranked_results AS (
    SELECT *,
           ROW_NUMBER() OVER(PARTITION BY tidb_exec_status ORDER BY ora_executions DESC) AS rn
    FROM sql_stmt_exec_results 
	WHERE task_id = ? 
	  AND ora_parsing_schema_name in (?)
	  AND tidb_exec_status in (?)
)
SELECT * FROM ranked_results
WHERE rn <= ?;
`
	records := make([]*SqlStmtExecResult, 0)
	err := rw.DB(ctx).Raw(sql, taskId, schemas, statusList, topn).Scan(&records).Error
	if err != nil {
		log.Errorf("list sqlStmtExec info failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return records, nil
}

// UpdateSqlStmtExecResultUserOperation updates user operation status for multiple SQL execution results
func (rw *SqlAnalyzerReadWrite) UpdateSqlStmtExecResultUserOperation(ctx context.Context, sqlExecIds []int, userOperateStatus, userOperateBy, userOperateRemark string) error {
	if len(sqlExecIds) == 0 {
		return fmt.Errorf("sqlExecIds cannot be empty")
	}

	// Start transaction for consistency
	tx := rw.DB(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get existing records to track previous status
	existingRecords := make([]*SqlStmtExecResult, 0)
	err := tx.Model(&SqlStmtExecResult{}).
		Where("sql_exec_id IN ?", sqlExecIds).
		Find(&existingRecords).Error
	if err != nil {
		tx.Rollback()
		log.Errorf("get existing sql stmt exec results failed. sqlExecIds:%v, err:%s", sqlExecIds, err)
		return dbCommon.WrapDBError(err)
	}

	// Create a map for quick lookup
	existingMap := make(map[int]*SqlStmtExecResult)
	for _, record := range existingRecords {
		existingMap[record.SqlExecId] = record
	}

	now := time.Now()
	updates := map[string]interface{}{
		"user_operate_status": userOperateStatus,
		"user_operate_by":     userOperateBy,
		"user_operate_at":     &now,
	}
	if userOperateRemark != "" {
		updates["user_operate_remark"] = userOperateRemark
	}

	// Update the records
	err = tx.Model(&SqlStmtExecResult{}).
		Where("sql_exec_id IN ?", sqlExecIds).
		Updates(updates).Error
	if err != nil {
		tx.Rollback()
		log.Errorf("update sql stmt exec result user operation failed. sqlExecIds:%v, err:%s", sqlExecIds, err)
		return dbCommon.WrapDBError(err)
	}

	// Create history records for each updated item
	for _, sqlExecId := range sqlExecIds {
		existing, exists := existingMap[sqlExecId]
		if !exists {
			continue
		}

		history := &SqlStmtExecResultHistory{
			SqlExecId:      sqlExecId,
			TaskId:         existing.TaskId,
			OperationType:  "UPDATE",
			PreviousStatus: existing.UserOperateStatus,
			NewStatus:      userOperateStatus,
			OperateBy:      userOperateBy,
			OperateAt:      now,
			OperateRemark:  userOperateRemark,
		}

		err = tx.Create(history).Error
		if err != nil {
			tx.Rollback()
			log.Errorf("create sql stmt exec result history failed. sqlExecId:%d, err:%s", sqlExecId, err)
			return dbCommon.WrapDBError(err)
		}
	}

	return tx.Commit().Error
}

// UpdateSqlStmtExecResultUserOperationByOraSqlId updates user operation status using Oracle SQL IDs
// Modified to use SqlStmtUserOperationV2 for centralized user operation management
func (rw *SqlAnalyzerReadWrite) UpdateSqlStmtExecResultUserOperationByOraSqlId(ctx context.Context, taskId int, oraSqlIds []string, userOperateStatus, userOperateRemark string, sqlIdToSchemaMap map[string]string) (int, error) {
	if len(oraSqlIds) == 0 {
		return 0, fmt.Errorf("oraSqlIds cannot be empty")
	}

	// Start transaction for consistency
	tx := rw.DB(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get existing V2 user operations to track previous status
	keys := make([]SqlStmtUserOperationV2Key, 0)
	validOraSqlIds := make([]string, 0)

	for _, oraSqlId := range oraSqlIds {
		schemaName, exists := sqlIdToSchemaMap[oraSqlId]
		if !exists {
			log.Warnf("OraSqlId %s not found in schema map, skipping", oraSqlId)
			continue
		}
		keys = append(keys, SqlStmtUserOperationV2Key{
			TaskId:               taskId,
			OraSqlId:             oraSqlId,
			OraParsingSchemaName: schemaName,
		})
		validOraSqlIds = append(validOraSqlIds, oraSqlId)
	}

	if len(keys) == 0 {
		return 0, fmt.Errorf("no valid Oracle SQL IDs found with schema mapping")
	}

	// Get existing V2 user operations
	existingUserOps := make([]*SqlStmtUserOperationV2, 0)
	query := tx.Where("task_id = ?", taskId)

	// Build OR conditions for multiple keys
	var conditions []string
	var args []interface{}
	for _, key := range keys {
		conditions = append(conditions, "(ora_sql_id = ? AND ora_parsing_schema_name = ?)")
		args = append(args, key.OraSqlId, key.OraParsingSchemaName)
	}

	if len(conditions) > 0 {
		query = query.Where("("+strings.Join(conditions, " OR ")+")", args...)
	}

	err := query.Find(&existingUserOps).Error
	if err != nil {
		tx.Rollback()
		log.Errorf("get existing sql stmt user operations v2 failed. taskId:%d, oraSqlIds:%v, err:%s", taskId, validOraSqlIds, err)
		return 0, dbCommon.WrapDBError(err)
	}

	// Create a map for quick lookup of existing operations
	existingMap := make(map[string]*SqlStmtUserOperationV2)
	for _, userOp := range existingUserOps {
		key := SqlStmtUserOperationV2Key{
			TaskId:               userOp.TaskId,
			OraSqlId:             userOp.OraSqlId,
			OraParsingSchemaName: userOp.OraParsingSchemaName,
		}
		existingMap[key.String()] = userOp
	}

	now := time.Now()
	var userOps []*SqlStmtUserOperationV2
	var histories []*SqlStmtUserOperationV2History
	updatedCount := 0

	// Process each Oracle SQL ID
	for _, oraSqlId := range validOraSqlIds {
		schemaName := sqlIdToSchemaMap[oraSqlId]
		key := SqlStmtUserOperationV2Key{
			TaskId:               taskId,
			OraSqlId:             oraSqlId,
			OraParsingSchemaName: schemaName,
		}

		// Check if user operation already exists
		existing, exists := existingMap[key.String()]
		previousStatus := constants.UserOperateStatusNormal
		if exists {
			previousStatus = existing.UserOperateStatus
		}

		// Create or update user operation record
		userOp := &SqlStmtUserOperationV2{
			TaskId:               taskId,
			OraSqlId:             oraSqlId,
			OraParsingSchemaName: schemaName,
			UserOperateStatus:    userOperateStatus,
			UserOperateBy:        "system", // TODO: get from context or request
			UserOperateAt:        &now,
			UserOperateRemark:    userOperateRemark,
		}
		userOps = append(userOps, userOp)

		// Create history record
		history := &SqlStmtUserOperationV2History{
			TaskId:               taskId,
			OraSqlId:             oraSqlId,
			OraParsingSchemaName: schemaName,
			OperationType:        "UPDATE",
			PreviousStatus:       previousStatus,
			NewStatus:            userOperateStatus,
			OperateBy:            "system", // TODO: get from context or request
			OperateAt:            now,
			OperateRemark:        userOperateRemark,
			ClientIP:             "", // TODO: get from context
		}
		histories = append(histories, history)
		updatedCount++
	}

	// Batch create or update user operations
	if len(userOps) > 0 {
		for _, userOp := range userOps {
			// Try to update first
			result := tx.Model(&SqlStmtUserOperationV2{}).Where("task_id = ? AND ora_sql_id = ? AND ora_parsing_schema_name = ?",
				userOp.TaskId, userOp.OraSqlId, userOp.OraParsingSchemaName).
				Updates(map[string]interface{}{
					"user_operate_status": userOp.UserOperateStatus,
					"user_operate_by":     userOp.UserOperateBy,
					"user_operate_at":     userOp.UserOperateAt,
					"user_operate_remark": userOp.UserOperateRemark,
				})

			if result.Error != nil {
				tx.Rollback()
				log.Errorf("update sql stmt user operation v2 failed. taskId:%d, oraSqlId:%s, err:%s", taskId, userOp.OraSqlId, result.Error)
				return 0, dbCommon.WrapDBError(result.Error)
			}

			// If no rows affected, create new record
			if result.RowsAffected == 0 {
				if err := tx.Create(userOp).Error; err != nil {
					tx.Rollback()
					log.Errorf("create sql stmt user operation v2 failed. taskId:%d, oraSqlId:%s, err:%s", taskId, userOp.OraSqlId, err)
					return 0, dbCommon.WrapDBError(err)
				}
			}
		}
	}

	// Create history records
	for _, history := range histories {
		err = tx.Create(history).Error
		if err != nil {
			tx.Rollback()
			log.Errorf("create sql stmt user operation v2 history failed. oraSqlId:%s, err:%s", history.OraSqlId, err)
			return 0, dbCommon.WrapDBError(err)
		}
	}

	if err := tx.Commit().Error; err != nil {
		log.Errorf("commit transaction failed. taskId:%d, err:%s", taskId, err)
		return 0, dbCommon.WrapDBError(err)
	}

	return updatedCount, nil
}

// ListSqlStmtExecResultWithPagination retrieves SQL execution results with pagination and filtering
func (rw *SqlAnalyzerReadWrite) ListSqlStmtExecResultWithPagination(ctx context.Context, taskId int, schemas []string, statusList []string, userOperateStatus []string, oraSqlId, oraParsingSchemaName, oraSqlText string, page, pageSize int) ([]*SqlStmtExecResult, int64, error) {
	records := make([]*SqlStmtExecResult, 0)

	query := rw.DB(ctx).Model(&SqlStmtExecResult{}).Where("task_id = ?", taskId)

	if len(schemas) > 0 {
		query = query.Where("ora_parsing_schema_name IN ?", schemas)
	}

	if len(statusList) > 0 {
		query = query.Where("tidb_exec_status IN ?", statusList)
	}

	if len(userOperateStatus) > 0 {
		query = query.Where("user_operate_status IN ?", userOperateStatus)
	}

	if oraSqlId != "" {
		query = query.Where("ora_sql_id LIKE ?", "%"+oraSqlId+"%")
	}

	if oraParsingSchemaName != "" {
		query = query.Where("ora_parsing_schema_name LIKE ?", "%"+oraParsingSchemaName+"%")
	}

	if oraSqlText != "" {
		query = query.Where("ora_sql_text LIKE ?", "%"+oraSqlText+"%")
	}

	// Count total records
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		log.Errorf("count sql stmt exec result failed. taskId:%d, err:%s", taskId, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	// Get paginated records
	offset := (page - 1) * pageSize
	err = query.Order("ora_sql_id").Offset(offset).Limit(pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("list sql stmt exec result with pagination failed. taskId:%d, err:%s", taskId, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	return records, total, nil
}

// CreateSqlStmtExecResultHistory creates a new history record for user operation changes
func (rw *SqlAnalyzerReadWrite) CreateSqlStmtExecResultHistory(ctx context.Context, history *SqlStmtExecResultHistory) error {
	err := rw.DB(ctx).Create(history).Error
	if err != nil {
		log.Errorf("create sql stmt exec result history failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

// ListSqlStmtExecResultHistory retrieves history records for a specific SQL execution result
func (rw *SqlAnalyzerReadWrite) ListSqlStmtExecResultHistory(ctx context.Context, taskId int, sqlExecId int) ([]*SqlStmtExecResultHistory, error) {
	records := make([]*SqlStmtExecResultHistory, 0)
	query := rw.DB(ctx).Model(&SqlStmtExecResultHistory{})

	if taskId > 0 {
		query = query.Where("task_id = ?", taskId)
	}
	if sqlExecId > 0 {
		query = query.Where("sql_exec_id = ?", sqlExecId)
	}

	err := query.Order("operate_at desc").Find(&records).Error
	if err != nil {
		log.Errorf("list sql stmt exec result history failed. taskId:%d, sqlExecId:%d, err:%s", taskId, sqlExecId, err)
		return nil, dbCommon.WrapDBError(err)
	}

	return records, nil
}

// GetSqlStmtExecResultStatistics retrieves statistics for SQL execution results including user operations
func (rw *SqlAnalyzerReadWrite) GetSqlStmtExecResultStatistics(ctx context.Context, taskId int, schemas []string) (map[string]int64, error) {
	stats := make(map[string]int64)

	query := rw.DB(ctx).Model(&SqlStmtExecResult{}).Where("task_id = ?", taskId)
	if len(schemas) > 0 {
		query = query.Where("ora_parsing_schema_name IN ?", schemas)
	}

	// Total count
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		log.Errorf("get total count failed. taskId:%d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	stats["total"] = total

	// Count by execution status
	type StatusCount struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	var execStatusCounts []StatusCount
	err = query.Select("tidb_exec_status as status, count(*) as count").
		Group("tidb_exec_status").Find(&execStatusCounts).Error
	if err != nil {
		log.Errorf("get exec status counts failed. taskId:%d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}

	for _, sc := range execStatusCounts {
		stats["exec_"+sc.Status] = sc.Count
	}

	// Count by user operation status
	var userOpCounts []StatusCount
	err = query.Select("user_operate_status as status, count(*) as count").
		Group("user_operate_status").Find(&userOpCounts).Error
	if err != nil {
		log.Errorf("get user operation counts failed. taskId:%d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}

	for _, sc := range userOpCounts {
		stats["user_"+sc.Status] = sc.Count
	}

	return stats, nil
}

// V2 User operation methods for Oracle data source

// CreateOrUpdateSqlStmtUserOperationV2 creates or updates a user operation record for V2
func (rw *SqlAnalyzerReadWrite) CreateOrUpdateSqlStmtUserOperationV2(ctx context.Context, userOp *SqlStmtUserOperationV2) error {
	// Try to update first
	result := rw.DB(ctx).Model(&SqlStmtUserOperationV2{}).Where("task_id = ? AND ora_sql_id = ? AND ora_parsing_schema_name = ?",
		userOp.TaskId, userOp.OraSqlId, userOp.OraParsingSchemaName).
		Updates(map[string]interface{}{
			"user_operate_status": userOp.UserOperateStatus,
			"user_operate_by":     userOp.UserOperateBy,
			"user_operate_at":     userOp.UserOperateAt,
			"user_operate_remark": userOp.UserOperateRemark,
		})

	if result.Error != nil {
		log.Errorf("update user operation v2 failed. err:%s", result.Error)
		return dbCommon.WrapDBError(result.Error)
	}

	// If no rows affected, create new record
	if result.RowsAffected == 0 {
		err := rw.DB(ctx).Create(userOp).Error
		if err != nil {
			log.Errorf("create user operation v2 failed. err:%s", err)
			return dbCommon.WrapDBError(err)
		}
	}

	return nil
}

// BatchCreateOrUpdateSqlStmtUserOperationV2 batch creates or updates user operation records for V2
func (rw *SqlAnalyzerReadWrite) BatchCreateOrUpdateSqlStmtUserOperationV2(ctx context.Context, userOps []*SqlStmtUserOperationV2) error {
	if len(userOps) == 0 {
		return nil
	}

	// Use transaction for batch operations
	return rw.DB(ctx).Transaction(func(tx *gorm.DB) error {
		for _, userOp := range userOps {
			// Try to update first
			result := tx.Model(&SqlStmtUserOperationV2{}).Where("task_id = ? AND ora_sql_id = ? AND ora_parsing_schema_name = ?",
				userOp.TaskId, userOp.OraSqlId, userOp.OraParsingSchemaName).
				Updates(map[string]interface{}{
					"user_operate_status": userOp.UserOperateStatus,
					"user_operate_by":     userOp.UserOperateBy,
					"user_operate_at":     userOp.UserOperateAt,
					"user_operate_remark": userOp.UserOperateRemark,
				})

			if result.Error != nil {
				return result.Error
			}

			// If no rows affected, create new record
			if result.RowsAffected == 0 {
				if err := tx.Create(userOp).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// GetSqlStmtUserOperationV2ByKeys retrieves user operation records by composite keys
func (rw *SqlAnalyzerReadWrite) GetSqlStmtUserOperationV2ByKeys(ctx context.Context, taskId int, keys []SqlStmtUserOperationV2Key) (map[string]*SqlStmtUserOperationV2, error) {
	if len(keys) == 0 {
		return make(map[string]*SqlStmtUserOperationV2), nil
	}

	var userOps []*SqlStmtUserOperationV2
	query := rw.DB(ctx).Where("task_id = ?", taskId)

	// Build OR conditions for multiple keys
	var conditions []string
	var args []interface{}
	for _, key := range keys {
		conditions = append(conditions, "(ora_sql_id = ? AND ora_parsing_schema_name = ?)")
		args = append(args, key.OraSqlId, key.OraParsingSchemaName)
	}

	if len(conditions) > 0 {
		query = query.Where("("+strings.Join(conditions, " OR ")+")", args...)
	}

	err := query.Find(&userOps).Error
	if err != nil {
		log.Errorf("get user operations v2 by keys failed. taskId:%d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}

	// Convert to map for easy lookup
	result := make(map[string]*SqlStmtUserOperationV2)
	for _, userOp := range userOps {
		key := SqlStmtUserOperationV2Key{
			TaskId:               userOp.TaskId,
			OraSqlId:             userOp.OraSqlId,
			OraParsingSchemaName: userOp.OraParsingSchemaName,
		}
		result[key.String()] = userOp
	}

	return result, nil
}

// CreateSqlStmtUserOperationV2History creates a history record for V2 user operations
func (rw *SqlAnalyzerReadWrite) CreateSqlStmtUserOperationV2History(ctx context.Context, history *SqlStmtUserOperationV2History) error {
	err := rw.DB(ctx).Create(history).Error
	if err != nil {
		log.Errorf("create user operation v2 history failed. err:%s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

// ListSqlStmtUserOperationV2History lists history records for V2 user operations
func (rw *SqlAnalyzerReadWrite) ListSqlStmtUserOperationV2History(ctx context.Context, taskId int, oraSqlId string) ([]*SqlStmtUserOperationV2History, error) {
	var history []*SqlStmtUserOperationV2History

	query := rw.DB(ctx).Where("task_id = ?", taskId)

	if oraSqlId != "" {
		query = query.Where("ora_sql_id = ?", oraSqlId)
	}

	err := query.Order("operate_at DESC").Find(&history).Error
	if err != nil {
		log.Errorf("list user operation v2 history failed. taskId:%d, oraSqlId:%s, err:%s", taskId, oraSqlId, err)
		return nil, dbCommon.WrapDBError(err)
	}

	return history, nil
}

// GetSqlStmtUserOperationV2ByTaskIdAndStatus retrieves user operation records by task ID and status
func (rw *SqlAnalyzerReadWrite) GetSqlStmtUserOperationV2ByTaskIdAndStatus(ctx context.Context, taskId int, statusList []string) (map[string]*SqlStmtUserOperationV2, error) {
	if len(statusList) == 0 {
		return make(map[string]*SqlStmtUserOperationV2), nil
	}

	var userOps []*SqlStmtUserOperationV2
	err := rw.DB(ctx).Where("task_id = ? AND user_operate_status IN ?", taskId, statusList).Find(&userOps).Error
	if err != nil {
		log.Errorf("get user operations v2 by task id and status failed. taskId:%d, statusList:%v, err:%s", taskId, statusList, err)
		return nil, dbCommon.WrapDBError(err)
	}

	// Convert to map for easy lookup using OraSqlId as key
	result := make(map[string]*SqlStmtUserOperationV2)
	for _, userOp := range userOps {
		result[userOp.OraSqlId] = userOp
	}
	return result, nil
}

// CountSqlStmtUserOperationV2ByTaskIdAndStatus counts user operation records by task ID and status
func (rw *SqlAnalyzerReadWrite) CountSqlStmtUserOperationV2ByTaskIdAndStatus(ctx context.Context, taskId int, statusList []string) (int64, error) {
	if len(statusList) == 0 {
		return 0, nil
	}

	var count int64
	err := rw.DB(ctx).Model(&SqlStmtUserOperationV2{}).Where("task_id = ? AND user_operate_status IN ?", taskId, statusList).Count(&count).Error
	if err != nil {
		log.Errorf("count user operations v2 by task id and status failed. taskId:%d, statusList:%v, err:%s", taskId, statusList, err)
		return 0, dbCommon.WrapDBError(err)
	}

	return count, nil
}
