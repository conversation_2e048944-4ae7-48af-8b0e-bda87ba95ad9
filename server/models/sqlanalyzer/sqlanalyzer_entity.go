package sqlanalyzer

import (
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"github.com/shopspring/decimal"
)

type SqlResultSummary struct {
	Id           int       `gorm:"primarykey;comment:primary key"`
	TaskId       int       `gorm:"index:idx_task_id;comment:task id"`
	SchemaName   string    `gorm:"type:varchar(50);comment:schema name"`
	StartTime    time.Time `gorm:"type:datetime;comment:start time"`
	EndTime      time.Time `gorm:"type:datetime;comment:end time"`
	TaskDuration string    `gorm:"type:varchar(50);comment:Duration"`
	TaskStatus   string    `gorm:"type:varchar(100)"`
	LogDetail    string    `gorm:"type:text;comment:log detail"`
	*common.Entity
}

type O2tSqlsetExecInfo struct {
	Id                   int             `gorm:"type:int;primarykey;comment:primary key"`
	Updatetime           time.Time       `gorm:"type:datetime;index:idx_updatetime;"`
	JsonFileName         string          `gorm:"type:varchar(512);"`
	TidbSqlstmt          string          `gorm:"type:text;"`
	TidbExecStatus       string          `gorm:"type:varchar(512);"`
	TidbPlan             string          `gorm:"type:text;"`
	TidbElapsedTimeMs    decimal.Decimal `gorm:"type:decimal(38,20);"`
	TidbEngineTimeMs     decimal.Decimal `gorm:"type:decimal(38,20);"`
	TidbExecTime         time.Time       `gorm:"type:datetime;"`
	TidbDigest           string          `gorm:"type:varchar(64);"`
	TidbUseWhereFunc     string          `gorm:"type:varchar(512);"`
	TidbUseNotExists     string          `gorm:"type:varchar(128);"`
	TidbPlanNotGood      string          `gorm:"type:varchar(128);"`
	OraSqlsetName        string          `gorm:"type:varchar(64);"`
	OraSqlsetOwner       string          `gorm:"type:varchar(64);"`
	OraModule            string          `gorm:"type:varchar(64);"`
	OraSqlId             string          `gorm:"type:varchar(32);"`
	OraSqlText           string          `gorm:"type:text;"`
	OraParsingSchemaName string          `gorm:"type:varchar(32);"`
	OraPlanHashValue     string          `gorm:"type:varchar(64);"`
	OraParameterPosition string          `gorm:"type:varchar(2000);"`
	OraParameterValue    string          `gorm:"type:text;"`
	OraExecutions        int             `gorm:"type:int;"`
	OraElapsedTimeMs     decimal.Decimal `gorm:"type:decimal(38,20);"`
	OraLastExecStartTime string          `gorm:"type:varchar(32);"`
	OraPlanTimestamp     time.Time       `gorm:"type:datetime;"`
	UserOperateFlag      string          `gorm:"type:varchar(128);"`
	TaskId               int             `gorm:"type:int;index:idx_task_id;"`
}

type SqlStmtExecResult struct {
	SqlExecId            int        `gorm:"type:int;primarykey;" json:"sqlExecId"`
	ChannelId            int        `gorm:"type:int;" json:"channelId"`
	TaskId               int        `gorm:"type:int;index:idx_task_id" json:"taskId"`
	TotalSQLNum          int        `gorm:"type:int;" json:"totalSQLNum"`
	OraSqlsetName        string     `gorm:"type:varchar(64);" json:"oraSqlsetName"`
	OraSqlsetOwner       string     `gorm:"type:varchar(64);" json:"oraSqlsetOwner"`
	OraModule            string     `gorm:"type:varchar(256);" json:"oraModule"`
	OraSqlId             string     `gorm:"type:varchar(64);index:idx_task_id;" json:"oraSqlId"`
	OraSqlText           string     `gorm:"type:mediumtext;" json:"oraSqlText"`
	OraParsingSchemaName string     `gorm:"type:varchar(64);" json:"oraParsingSchemaName"`
	OraPlanHashValue     string     `gorm:"type:varchar(64);" json:"oraPlanHashValue"`
	OraParameterPosition string     `gorm:"type:varchar(2000);" json:"oraParameterPosition"`
	OraParameterValue    string     `gorm:"type:text;" json:"oraParameterValue"`
	OraExecutions        int        `gorm:"type:int;" json:"oraExecutions"`
	OraElapsedTimeMs     float64    `gorm:"type:decimal(20,10);" json:"oraElapsedTimeMs"`
	OraLastExecStartTime string     `gorm:"type:varchar(64);" json:"oraLastExecStartTime"`
	OraPlanTimestamp     time.Time  `gorm:"type:datetime;" json:"oraPlanTimestamp"`
	OraCommandType       string     `gorm:"type:varchar(64);" json:"oraCommandType"`
	TidbSqlType          string     `gorm:"type:varchar(32);" json:"tidbSqlType"`
	TidbSqlText          string     `gorm:"type:mediumtext;" json:"tidbSqlText"`
	TidbExecStatus       string     `gorm:"type:varchar(32);" json:"tidbExecStatus"`
	TidbExecCode         string     `gorm:"type:varchar(32);" json:"tidbExecCode"`
	TidbExecMsg          string     `gorm:"type:varchar(2000);" json:"tidbExecMsg"`
	TidbPlan             string     `gorm:"type:varchar(2000);" json:"tidbPlan"`
	TidbElapsedTimeMs    int64      `gorm:"type:int;" json:"tidbElapsedTimeMs"`
	TidbEngineTimeMs     float64    `gorm:"type:decimal(20,10);" json:"tidbEngineTimeMs"`
	TidbExecTime         time.Time  `gorm:"type:datetime;" json:"tidbExecTime"`
	TidbDigest           string     `gorm:"type:varchar(64);" json:"tidbDigest"`
	TidbUseWhereFunc     string     `gorm:"type:varchar(1000);" json:"tidbUseWhereFunc"`
	TidbUseNotExists     string     `gorm:"type:varchar(1000);" json:"tidbUseNotExists"`
	TidbPlanNotGood      string     `gorm:"type:varchar(64);" json:"tidbPlanNotGood"`
	TmsExecMsg           string     `gorm:"type:varchar(64);" json:"tmsExecMsg"`
	UserOperateStatus    string     `gorm:"type:varchar(32);default:'normal'" json:"userOperateStatus"`
	UserOperateBy        string     `gorm:"type:varchar(64);" json:"userOperateBy"`
	UserOperateAt        *time.Time `gorm:"type:datetime;" json:"userOperateAt"`
	UserOperateRemark    string     `gorm:"type:varchar(500);" json:"userOperateRemark"`
	*common.Entity
}

// SqlStmtUserOperationV2 tracks user operations for V2 version (Oracle data source)
// This table bridges Oracle data with metadb user operations
type SqlStmtUserOperationV2 struct {
	Id                   int        `gorm:"type:int;primarykey;autoIncrement" json:"id"`
	TaskId               int        `gorm:"type:int;index:idx_task_id_v2" json:"taskId"`
	OraSqlId             string     `gorm:"type:varchar(64);index:idx_ora_sql_id_v2" json:"oraSqlId"`
	OraParsingSchemaName string     `gorm:"type:varchar(64);index:idx_ora_schema_v2" json:"oraParsingSchemaName"`
	SqlTextHash          string     `gorm:"type:varchar(64);index:idx_sql_text_hash_v2" json:"sqlTextHash"` // SHA256 hash of SQL text
	UserOperateStatus    string     `gorm:"type:varchar(32);default:'normal'" json:"userOperateStatus"`
	UserOperateBy        string     `gorm:"type:varchar(64);" json:"userOperateBy"`
	UserOperateAt        *time.Time `gorm:"type:datetime;" json:"userOperateAt"`
	UserOperateRemark    string     `gorm:"type:varchar(500);" json:"userOperateRemark"`
	*common.Entity
}

func (SqlStmtUserOperationV2) TableName() string {
	return "sql_stmt_user_operation_v2"
}

// SqlStmtUserOperationV2History tracks operation history for V2 version
type SqlStmtUserOperationV2History struct {
	Id                   int       `gorm:"type:int;primarykey;autoIncrement" json:"id"`
	UserOperationId      int       `gorm:"type:int;index:idx_user_operation_id_v2" json:"userOperationId"`
	TaskId               int       `gorm:"type:int;index:idx_task_id_v2" json:"taskId"`
	OraSqlId             string    `gorm:"type:varchar(64);" json:"oraSqlId"`
	OraParsingSchemaName string    `gorm:"type:varchar(64);" json:"oraParsingSchemaName"`
	OperationType        string    `gorm:"type:varchar(32);" json:"operationType"` // 'CREATE', 'UPDATE'
	PreviousStatus       string    `gorm:"type:varchar(32);" json:"previousStatus"`
	NewStatus            string    `gorm:"type:varchar(32);" json:"newStatus"`
	OperateBy            string    `gorm:"type:varchar(64);" json:"operateBy"`
	OperateAt            time.Time `gorm:"type:datetime;index:idx_operate_at_v2" json:"operateAt"`
	OperateRemark        string    `gorm:"type:varchar(500);" json:"operateRemark"`
	ClientIP             string    `gorm:"type:varchar(64);" json:"clientIp"`
	*common.Entity
}

func (SqlStmtUserOperationV2History) TableName() string {
	return "sql_stmt_user_operation_v2_history"
}

// SqlStmtUserOperationV2Key represents the composite key for V2 user operations
type SqlStmtUserOperationV2Key struct {
	TaskId               int    `json:"taskId"`
	OraSqlId             string `json:"oraSqlId"`
	OraParsingSchemaName string `json:"oraParsingSchemaName"`
}

// String returns a string representation of the key for use in maps
func (k SqlStmtUserOperationV2Key) String() string {
	return fmt.Sprintf("%d:%s:%s", k.TaskId, k.OraSqlId, k.OraParsingSchemaName)
}

// SqlStmtExecResultHistory tracks user operation history for audit trail
type SqlStmtExecResultHistory struct {
	Id             int       `gorm:"type:int;primarykey;autoIncrement" json:"id"`
	SqlExecId      int       `gorm:"type:int;index:idx_sql_exec_id" json:"sqlExecId"`
	TaskId         int       `gorm:"type:int;index:idx_task_id" json:"taskId"`
	OperationType  string    `gorm:"type:varchar(32);" json:"operationType"` // 'CREATE', 'UPDATE'
	PreviousStatus string    `gorm:"type:varchar(32);" json:"previousStatus"`
	NewStatus      string    `gorm:"type:varchar(32);" json:"newStatus"`
	OperateBy      string    `gorm:"type:varchar(64);" json:"operateBy"`
	OperateAt      time.Time `gorm:"type:datetime;index:idx_operate_at" json:"operateAt"`
	OperateRemark  string    `gorm:"type:varchar(500);" json:"operateRemark"`
	ClientIP       string    `gorm:"type:varchar(64);" json:"clientIp"`
	*common.Entity
}

func (SqlStmtExecResultHistory) TableName() string {
	return "sql_stmt_exec_result_history"
}

type SqlStmtExecSummary struct {
	ID                int `gorm:"type:int;primarykey;" json:"id"`
	ChannelId         int `gorm:"type:int;" json:"channelId"`
	TaskId            int `gorm:"type:int;uniqueIndex:uqi_task_id;" json:"taskId"`
	TotalNum          int `gorm:"type:int;" json:"totalNum"`
	SuccessNum        int `gorm:"type:int;" json:"successNum"`
	TimeoutNum        int `gorm:"type:int;" json:"timeoutNum"`
	SchemaNotMatchNum int `gorm:"type:int;" json:"schemaNotMatchNum"`
	FailedNum         int `gorm:"type:int;" json:"failedNum"`
	*common.Entity
}

type SqlStmtExecResultByStatus struct {
	ChannelId            int       `gorm:"channel_id" json:"channel_id"`
	TaskId               int       `gorm:"task_id" json:"task_id"`
	OraParsingSchemaName string    `gorm:"ora_parsing_schema_name" json:"ora_parsing_schema_name"`
	Count                int       `gorm:"count" json:"count"`
	Successnum           int       `gorm:"successnum" json:"successnum"`
	Failnum              int       `gorm:"failnum" json:"failnum"`
	Timeoutnum           int       `gorm:"timeoutnum" json:"timeoutnum"`
	Starttime            time.Time `gorm:"starttime" json:"starttime"`
	Endtime              time.Time `gorm:"endtime" json:"endtime"`
}

type SqlStmtExecResultByErrCode struct {
	ChannelId            int       `json:"channelId"`
	TaskId               int       `json:"taskId"`
	OraParsingSchemaName string    `json:"oraParsingSchemaName"`
	TidbSqlType          string    `json:"tidbSqlType"`
	TidbExecStatus       string    `json:"tidbExecStatus"`
	TidbExecCode         string    `json:"tidbExecCode"`
	TidbExecMsg          string    `json:"tidbExecMsg"`
	Count                int       `json:"count"`
	Starttime            time.Time `json:"Starttime"`
	Endtime              time.Time `json:"endtime"`
}

type HtmlTemplateDetail struct {
	TemplateId  int    `gorm:"primarykey"`
	HtmlName    string `gorm:"type:varchar(200)"`
	HtmlVersion string `gorm:"type:varchar(20)"`
	HtmlStatus  string `gorm:"type:varchar(20)"`
	Detail      string `gorm:"type:text"`
	*common.Entity
}

type EnvDeployTask struct {
	SQLAnalyzeId int       `gorm:"primarykey;column:sqlanalyze_id"`
	TaskNumber   int       `gorm:"type:int;" json:"task_number"`
	TaskName     string    `gorm:"type:varchar(100);" json:"task_name"`
	IsIgnore     string    `gorm:"type:varchar(1);" json:"is_ignore"`
	TaskStatus   int       `gorm:"type:int" json:"task_status"`
	TaskLog      string    `gorm:"type:varchar(2000);" json:"task_log"`
	TaskSQL      string    `gorm:"type:longtext;" json:"task_sql"`
	TaskMode     string    `gorm:"type:varchar(10);" json:"task_mode"`
	LastRunTime  time.Time `gorm:"type:datetime;" json:"last_run_time"`
	TaskId       int       `gorm:"type:int;" json:"task_id"`
	Comment      string    `gorm:"type:varchar(1000);" json:"comment"`
	SQLModel     string    `gorm:"type:varchar(20);" json:"sql_model"`

	*common.Entity
}

func (i EnvDeployTask) TableName() string {
	return "sqlanalyze_tasks"
}

func (i EnvDeployTask) String() string {
	return fmt.Sprintf("SQLAnalyzeId:%d,TaskNumber:%d,TaskName:%s,IsIgnore:%s,TaskStatus:%s,TaskLog:%s,taskSQL:%s,TaskMode:%s,LastRunTime:%s,TaskId:%d,Comment:%s",
		i.SQLAnalyzeId, i.TaskNumber, i.TaskName, i.IsIgnore, i.TaskStatus, i.TaskLog, i.TaskSQL, i.TaskMode, i.LastRunTime, i.TaskId, i.Comment)
}
