package migration

import (
	"context"
	"database/sql"

	"gitee.com/pingcap_enterprise/tms/server/message"
)

type FullDataReaderWriter interface {
	CreateTableMigrationSummary(ctx context.Context, tableResult *TableMigrationSummary) (*TableMigrationSummary, error)
	DetailTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary) ([]*TableMigrationSummary, error)
	DetailTableMigrationSummaryPage(ctx context.Context, detailS *TableMigrationSummary, page int, pageSize int) ([]*TableMigrationSummary, int64, error)
	DetailTableMigrationSummaryPageWithStatus(ctx context.Context, detailS *TableMigrationSummary, status []string, page int, pageSize int) ([]*TableMigrationSummary, int64, error)
	UpdateTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary, updates map[string]interface{}) error
	ResetTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary) error
	DeleteTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary) error
	DeleteTableMigrationSummaryByTaskId(ctx context.Context, detailS *TableMigrationSummary) error
	DeleteTableMigrationSummaryByTaskIds(ctx context.Context, taskIds []int) error
	ResetTableMigrationSummaryForImportByTaskId(ctx context.Context, taskId int) error
	DeleteTableMigrationSummaryByTaskIdsAndSchemaTable(ctx context.Context, taskIds []int, schemaName, tableName string) error
	BatchGetTableMigrationSummary(ctx context.Context, taskID int, ids []uint, taskStatus string) ([]*TableMigrationSummary, error)
	BatchGetTableMigrationSummaryByStatus(ctx context.Context, taskID int, taskStatusList []string) ([]*TableMigrationSummary, error)
	BatchGetTableMigrationSummaryByTaskIdSchemaAndStatusList(ctx context.Context, taskID int, schemaName string, statusList []string) ([]*TableMigrationSummary, error)
	BatchGetTableMigrationSummaryByTaskIdAndStatusList(ctx context.Context, taskID int, statusList []string) ([]*TableMigrationSummary, error)
	BatchGetTableMigrationSummaryByTaskIdSummaryIds(ctx context.Context, taskID int, ids []uint) ([]*TableMigrationSummary, error)
	BatchGetTableMigrationSummaryByTaskIds(ctx context.Context, taskIDs []uint) ([]*TableMigrationSummary, error)
	BatchGetTableMigrationSummaryBySummaryIds(ctx context.Context, ids []uint) ([]*TableMigrationSummary, error)
	BatchCreateTableMigrationSummary(ctx context.Context, summaries []*TableMigrationSummary, batchSize int) error
	GetTableMigrationSummaryByInterval(ctx context.Context, detailS *TableMigrationSummary) ([]*TableMigrationSummary, error)
	BatchUpdateTableMigrationSummary(ctx context.Context, summaries []TableMigrationSummary) error

	CountTableMigrationDetail(ctx context.Context, taskId int) (int64, error)
	CountTableMigrationSummary(ctx context.Context, taskId int) (int64, error)
	CreateTableMigrationDetail(ctx context.Context, tableResult *TableMigrationDetail) (*TableMigrationDetail, error)
	DetailTableMigrationDetail(ctx context.Context, detailS *TableMigrationDetail) ([]TableMigrationDetail, error)
	DetailTableMigrationDetailWithStatus(ctx context.Context, detailS *TableMigrationDetail, status []string) ([]TableMigrationDetail, error)
	GetTableMigrationDetailSummary(ctx context.Context, detailS *TableMigrationDetail) (*DetailSummary, error)
	BatchCreateTableMigrationDetail(ctx context.Context, tableResult []*TableMigrationDetail, batchSize int) ([]*TableMigrationDetail, error)
	BatchGetTableMigrationDetail(ctx context.Context, taskID int, schemaName, tableName string, ids []uint, taskStatus string) ([]TableMigrationDetail, error)
	GroupTableMigrationDetailImportStatusCount(ctx context.Context, taskID int, schemaName, tableName string) (*DetailImportStatusCount, error)
	BatchGetTableMigrationDetailByIdsTaskStatus(ctx context.Context, ids []uint, status string) ([]TableMigrationDetail, error)
	BatchGetTableMigrationDetailByStatus(ctx context.Context, taskID int, statusList []string, importStatusList []string) ([]TableMigrationDetail, error)
	BatchGetTableMigrationDetailByIds(ctx context.Context, ids []uint) ([]TableMigrationDetail, error)
	BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(ctx context.Context, taskId int, schemaName, tableName string, statusList []string) ([]TableMigrationDetail, error)
	BatchGetWaitingAndFailedTableMigrationDetail(ctx context.Context, taskId int, schemaName, tableName string) ([]*TableMigrationDetail, error)
	UpdateTableMigrationDetail(ctx context.Context, detailId uint, updates map[string]interface{}) error
	SetTableMigrationDetailFailed(ctx context.Context, id uint, errorDetail string) error
	ResetTableMigrationDetail(ctx context.Context, detailS *TableMigrationDetail) error
	UpdateTableMigrationDetailById(ctx context.Context, detailS *TableMigrationDetail, updates map[string]interface{}) error
	DeleteTableMigrationDetail(ctx context.Context, detailS *TableMigrationDetail) error
	DeleteTableMigrationDetailByTaskId(ctx context.Context, detailS *TableMigrationDetail) error
	DeleteTableMigrationDetailByTaskIds(ctx context.Context, taskIds []int) error
	ResetTableMigrationDetailForImportByTaskId(ctx context.Context, taskId int) error
	DeleteTableMigrationDetailByTaskIdsAndSchemaTable(ctx context.Context, taskIds []int, schemaName, tableName string) error
	GetTableMigrationDetailByInterval(ctx context.Context, detailS *TableMigrationDetail) ([]*TableMigrationDetail, error)
	GetTableMigrationDetailsByTaskId(ctx context.Context, taskId int) ([]*TableMigrationDetail, error)
	GetTableMigrationDetailsByCSVCondition(ctx context.Context, taskId int, schemaNameT, tableNameT string, csvFilter string) ([]*TableMigrationDetail, error)
	BatchUpdateTableMigrationDetail(ctx context.Context, details []*TableMigrationDetail) error
	UpdateTableMigrationDetailImportStatusByIDs(ctx context.Context, taskId int, detailIds []uint, importStatus, importError string) error
	UpdateTableMigrationDetailImportStatusByIDsV2(ctx context.Context, taskId int, detailIds []uint, importStatus string) error

	TruncateTargetSchemaTable(ctx context.Context, db *sql.DB, targetSchema, targetTable string) error
	CountSubTaskMigrationSummaryAndDetailByTaskID(ctx context.Context, taskID int, schemaName, tableName string) (int64, int64, error)

	SaveLightningProgress(ctx context.Context, lightningProgress *LightningProgress) (*LightningProgress, error)
	GetLatestLightningProgressByTaskId(ctx context.Context, taskId int) (*LightningProgress, error)
	GetLatestLightningProgressByTaskIdSchemaSTableS(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*LightningProgress, error)
	GetSchemaSTableSLatestLightningProgressByTaskId(ctx context.Context, taskId int) ([]LightningProgress, error)
	ListLightningProgressesByTaskId(ctx context.Context, taskId int) ([]*LightningProgress, error)
	ListLightningProgressesByTaskIdSchemaSTableS(ctx context.Context, taskId int, schemaNameS, tableNameS string) ([]*LightningProgress, error)
	DeleteLightningProgressByTaskId(ctx context.Context, taskId int) error

	SaveCSVStage(ctx context.Context, stage *CSVStage) (*CSVStage, error)
	BatchSaveCSVStage(ctx context.Context, stages []*CSVStage) error
	GetCSVStageByTaskId(ctx context.Context, taskId int) (*CSVStage, error)
	GetCSVStageByTaskIdSchemaSTableS(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*CSVStage, error)
	UpdateCSVStage(ctx context.Context, channelId, taskId int, updates map[string]interface{}) error
	UpdateCSVStageBySchemaSTableS(ctx context.Context, channelId, taskId int, schemaNameS, tableNameS string, updates map[string]interface{}) error
	BatchDeleteCSVStage(ctx context.Context, channelId, parentTaskId int) error
	BatchDeleteCSVStageByTaskId(ctx context.Context, channelId, taskId int) error
	ResetCSVStageForImportByTaskId(ctx context.Context, channelId, taskId int) error
	DeleteCSVStage(ctx context.Context, taskId int, schemaNameS, tableNameS string) error

	BatchSaveChunkDataAnalyze(ctx context.Context, chunkDataAnalyzer []ChunkDataAnalyze, batchSize int) error
	ListChunkDataAnalyzePage(ctx context.Context, taskId int, schemaNameS, tableNameS string, page int, pageSize int) ([]ChunkDataAnalyze, int64, error)
	ListChunkDataAnalyzePageWithStatus(ctx context.Context, taskId int, schemaNameS, tableNameS, status string, page int, pageSize int) ([]ChunkDataAnalyze, int64, error)
	CountChunkDataAnalyze(ctx context.Context, taskId int, schemaNameS, tableNameS string) (int64, error)
	CountChunkDataAnalyzeByStatus(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*ChunkDataAnalyzeStatusCounts, error)
	FetchChunkDataAnalyzeByIds(ctx context.Context, taskId int, schemaNameS, tableNameS string, ids []uint) ([]ChunkDataAnalyze, error)
	UpdateChunkDataAnalyzeStatusByIds(ctx context.Context, ids []uint, status, errorMessage string) error
	UpdateChunkDataAnalyzeStatus(ctx context.Context, taskId int, schemaNameS, tableNameS, status, errorMessage string) error
	QueryChunkDataAnalyzePageWithStatus(ctx context.Context, msg *message.QueryChunkDataReq) ([]ChunkDataAnalyze, int64, error)
	BatchDeleteChunkDataAnalyze(ctx context.Context, taskId int) error

	SaveChunkDataAnalyzeSummary(ctx context.Context, chunkDataAnalyzeSummary *ChunkDataAnalyzeSummary) (*ChunkDataAnalyzeSummary, error)
	UpdateChunkDataAnalyzeSummary(ctx context.Context, taskId int, schemaNameS, tableNameS string, status string) error
	UpdateChunkDataAnalyzeProgress(ctx context.Context, taskId int, schemaNameS, tableNameS string, totalDetails, processedDetails int64) error
	GetChunkDataAnalyzeSummary(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*ChunkDataAnalyzeSummary, error)
}
