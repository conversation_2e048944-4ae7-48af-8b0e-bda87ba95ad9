package migration

import "context"

type StructureReaderWriter interface {
	CreateTableResultDetail(ctx context.Context, tableResult *TableResultDetail) (*TableResultDetail, error)
	UpdateTableResultDetail(ctx context.Context, tableResult *TableResultDetail) (*TableResultDetail, error)
	BatchGetTableResultDetail(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error)
	BatchGetTableResultDetailWithoutIndex(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error)
	BatchGetTableResultDetailForIndex(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error)
	BatchGetTableResultDetailPage(ctx context.Context, tableResult *TableResultDetail, page int, pageSize int) ([]*TableResultDetail, int64, error)
	BatchGetTableResultDetailPageWithoutIndex(ctx context.Context, tableResult *TableResultDetail, page int, pageSize int) ([]*TableResultDetail, int64, error)
	BatchGetTableResultDetailPageForIndex(ctx context.Context, tableResult *TableResultDetail, page int, pageSize int) ([]*TableResultDetail, int64, error)
	GetTableResultDetail(ctx context.Context, tableResult *TableResultDetail) (*TableResultDetail, error)
	UpdateTableResultDetailStatusFixed(ctx context.Context, tableComp *TableResultDetail) (*TableResultDetail, error)
	BatchGetGroupTableResultDetail(ctx context.Context, tableResult *TableResultDetail) ([]*TableGroupResultDetail, error)
	GetTableResultDetailByInterval(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error)
	BatchCreateTableCompatible(ctx context.Context, tableComp []*TableCompatibleDetail, batchSize int) ([]*TableCompatibleDetail, error)
	BatchCreateTableResultDetail(ctx context.Context, tables []*TableResultDetail, batchSize int) ([]*TableResultDetail, error)
	BatchGetTableCompatibleDetail(ctx context.Context, tableComp *TableCompatibleDetail) ([]TableCompatibleDetail, error)
	BatchGetTableCompatibleDetailPage(ctx context.Context, tableComp *TableCompatibleDetail, page int, pageSize int) ([]TableCompatibleDetail, int64, error)
	BatchGetGroupTableCompatibleDetail(ctx context.Context, tableComp *TableCompatibleDetail) ([]*TableGroupResultDetail, error)
	UpdateTableCompatibleDetailStatusFixed(ctx context.Context, tableComp *TableCompatibleDetail) (*TableCompatibleDetail, error)
	BatchGetTableResultDetailBySchema(ctx context.Context, taskId int, schemas []string) ([]*TableResultDetail, error)
	BatchGetTableResultDetailBySchemaAndStatus(ctx context.Context, taskId int, schemas []string, status int) ([]*TableResultDetail, error)
	BatchGetTableCompatibleDetailBySchema(ctx context.Context, taskId int, schemas []string) ([]*TableCompatibleDetail, error)
	BatchGetTableCompatibleDetailByTable(ctx context.Context, taskId int, schemaNameS, tableNameS string) ([]*TableCompatibleDetail, error)
	ResetTableResultDetail(ctx context.Context, taskId int) error
	GetTableCompatibleDetailCount(ctx context.Context, tableComp *TableCompatibleDetail) (*TableGroupResultDetail, error)
	GetTableResultDetailCount(ctx context.Context, tableResult *TableResultDetail) (*TableGroupResultDetail, error)
	GetTableResultDetailCountForIndex(ctx context.Context, tableResult *TableResultDetail) (*TableGroupResultDetail, error)

	BatchUpdateTableResultDetailToPartialSuccessByFailedIndex(ctx context.Context, taskId int) error
	BatchUpdateTableResultDetailToPartialSuccessByIncompatibalIndex(ctx context.Context, taskId int) error
	SumTableResultDetailDurationBySchema(ctx context.Context, taskId int) ([]*TableResultDetailSumDuration, error)
}
