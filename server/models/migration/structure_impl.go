package migration

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"

	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type StructureMigrationReadWrite struct {
	dbCommon.GormDB
}

func NewStructureMigrationReadWrite(db *gorm.DB) *StructureMigrationReadWrite {
	m := &StructureMigrationReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw StructureMigrationReadWrite) CreateTableResultDetail(ctx context.Context, tableResult *TableResultDetail) (*TableResultDetail, error) {
	// delete history data
	//var count int64 = 0
	//err := rw.GormDB.DB(ctx).Model(&TableResultDetail{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", tableResult.TaskID, tableResult.SchemaNameS, tableResult.TableNameS).Count(&count).Error
	//if err != nil {
	//	log.Errorf("query TableResultDetail failed. task_id: %d, scheme: %s table: %s", tableResult.TaskID, tableResult.SchemaNameS, tableResult.TableNameS)
	//	return tableResult, dbCommon.WrapDBError(err)
	//}
	//if count >= 1 {
	//	log.Infof("delete TableResultDetail history. task_id: %d, scheme: %s table: %s", tableResult.TaskID, tableResult.SchemaNameS, tableResult.TableNameS)
	//	rw.GormDB.DB(ctx).Model(&TableResultDetail{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", tableResult.TaskID, tableResult.SchemaNameS, tableResult.TableNameS).Delete(&TableResultDetail{})
	//}

	err := rw.DB(ctx).Create(tableResult).Error
	if err != nil {
		log.Errorf("create table result detail to db failed. %s", err)
		log.Errorf("create table result detail to db failed, tableResult is :%v", tableResult.String())

		retryTableResult := tableResult
		retryTableResult.ReverseDDL = ""
		retryTableResult.Status = constants.TASK_MIGRATION_STRUCTURE_DDL_FAILED
		retryErr := rw.DB(ctx).Create(retryTableResult).Error
		if retryErr != nil {
			log.Errorf("retry create table result detail to db failed. %s", err)
			log.Errorf("retry create table result detail to db failed, tableResult is :%v", tableResult.String())
			return tableResult, dbCommon.WrapDBError(retryErr)
		}
	}
	log.Infof("create table result detail to db successfully, %v", tableResult.String())
	return tableResult, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetail(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error) {
	var objDetails []*TableResultDetail
	err := rw.GormDB.DB(ctx).Where("schema_name_s = ? AND table_name_s COLLATE utf8mb4_bin = ? AND task_id = ? and deleted_at is null", tableResult.SchemaNameS, tableResult.TableNameS, tableResult.TaskID).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailWithoutIndex(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error) {
	var objDetails []*TableResultDetail
	err := rw.GormDB.DB(ctx).Where(tableResult).Where("table_type_s not in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY') ").Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailForIndex(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error) {
	var objDetails []*TableResultDetail
	err := rw.GormDB.DB(ctx).Where(tableResult).Where("table_type_s in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY') ").Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailPage(ctx context.Context, tableResult *TableResultDetail, page int, pageSize int) ([]*TableResultDetail, int64, error) {
	objDetails := make([]*TableResultDetail, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TableResultDetail{})
	err := query.Order("table_name_s asc").Where(tableResult).Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, 0, fmt.Errorf("batch query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return objDetails, count, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailPageWithoutIndex(ctx context.Context, tableResult *TableResultDetail, page int, pageSize int) ([]*TableResultDetail, int64, error) {
	objDetails := make([]*TableResultDetail, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TableResultDetail{})
	err := query.Order("table_name_s asc").Where(tableResult).Where("table_type_s not in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY') ").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, 0, fmt.Errorf("batch query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return objDetails, count, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailPageForIndex(ctx context.Context, tableResult *TableResultDetail, page int, pageSize int) ([]*TableResultDetail, int64, error) {
	objDetails := make([]*TableResultDetail, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TableResultDetail{})
	err := query.Order("table_name_s asc").Where(tableResult).Where("table_type_s in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY') ").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, 0, fmt.Errorf("batch query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return objDetails, count, nil
}

func (rw StructureMigrationReadWrite) GetTableResultDetail(ctx context.Context, tableResult *TableResultDetail) (*TableResultDetail, error) {
	objDetails := &TableResultDetail{}
	query := rw.DB(ctx).Model(&TableResultDetail{})
	err := query.Where(tableResult).Limit(1).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) GetTableResultDetailCount(ctx context.Context, tableResult *TableResultDetail) (*TableGroupResultDetail, error) {
	objDetails := &TableGroupResultDetail{}
	err := rw.GormDB.DB(ctx).Model(&TableResultDetail{}).Select("count(1) as count, schema_name_s, table_name_s").Where(tableResult).Group("schema_name_s, table_name_s ").Scan(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) GetTableResultDetailCountForIndex(ctx context.Context, tableResult *TableResultDetail) (*TableGroupResultDetail, error) {
	objDetails := &TableGroupResultDetail{}
	err := rw.GormDB.DB(ctx).Model(&TableResultDetail{}).Select("count(1) as count, schema_name_s, table_name_s").Where(tableResult).Where("table_type_s in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY') ").Group("schema_name_s, table_name_s ").Scan(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetGroupTableResultDetail(ctx context.Context, tableResult *TableResultDetail) ([]*TableGroupResultDetail, error) {
	var objDetails []*TableGroupResultDetail
	err := rw.GormDB.DB(ctx).Model(&TableResultDetail{}).Select("service_name_s,schema_name_s,table_type_s,table_type_t,status,count(*) as count").Where(tableResult).Group("service_name_s,schema_name_s,table_type_s,table_type_t,status").Scan(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query group by table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("batch get group by table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

// 只统计表的数量，排除索引
func (rw StructureMigrationReadWrite) GetTableResultDetailByInterval(ctx context.Context, tableResult *TableResultDetail) ([]*TableResultDetail, error) {
	var objDetails []*TableResultDetail
	err := rw.GormDB.DB(ctx).Where("task_id = ? AND created_at >= ? AND object_name_s = '' ", tableResult.TaskID, tableResult.CreatedAt).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table result detail not found, query cond: %v", tableResult.String())
	} else if err != nil {
		log.Errorf("get table result detail failed, query cond %d, %v", tableResult.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) UpdateTableResultDetail(ctx context.Context, tableResult *TableResultDetail) (*TableResultDetail, error) {
	err := rw.DB(ctx).Save(tableResult).Error
	if err != nil {
		log.Errorf("update table result detail info to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tableResult, nil
}

func (rw StructureMigrationReadWrite) UpdateTableResultDetailStatusFixed(ctx context.Context, tableComp *TableResultDetail) (*TableResultDetail, error) {
	err := rw.DB(ctx).Model(&TableResultDetail{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND table_type_s = ? ",
		tableComp.TaskID,
		tableComp.SchemaNameS,
		tableComp.TableNameS,
		tableComp.TableTypeS).Updates(map[string]interface{}{
		"status":    tableComp.Status,
		"fixed_ddl": tableComp.FixedDDL}).Error
	if err != nil {
		log.Errorf("update table result detail info to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tableComp, nil
}

func (rw StructureMigrationReadWrite) BatchCreateTableCompatible(ctx context.Context, tableComp []*TableCompatibleDetail, batchSize int) ([]*TableCompatibleDetail, error) {
	err := rw.DB(ctx).CreateInBatches(tableComp, batchSize).Error
	if err != nil {
		log.Errorf("create table compatible detail to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create table compatible detail to db successfully")
	return tableComp, nil
}

func (rw StructureMigrationReadWrite) BatchCreateTableResultDetail(ctx context.Context, tables []*TableResultDetail, batchSize int) ([]*TableResultDetail, error) {
	err := rw.DB(ctx).CreateInBatches(tables, batchSize).Error
	if err != nil {
		log.Errorf("create table result detail to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create table result detail to db successfully")
	return tables, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableCompatibleDetail(ctx context.Context, tableComp *TableCompatibleDetail) ([]TableCompatibleDetail, error) {
	var objDetails []TableCompatibleDetail
	err := rw.GormDB.DB(ctx).Where(tableComp).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table compatible detail not found, query cond: %v", tableComp.String())
	} else if err != nil {
		log.Errorf("batch get table compatible detail failed, query cond %d, %v", tableComp.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableCompatibleDetailPage(ctx context.Context, tableComp *TableCompatibleDetail, page int, pageSize int) ([]TableCompatibleDetail, int64, error) {
	objDetails := make([]TableCompatibleDetail, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TableCompatibleDetail{})
	err := query.Where(tableComp).Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, 0, fmt.Errorf("batch query table compatible detail not found, query cond: %v", tableComp.String())
	} else if err != nil {
		log.Errorf("batch get table compatible detail failed, query cond %d, %v", tableComp.String(), err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return objDetails, count, nil
}

func (rw StructureMigrationReadWrite) BatchGetGroupTableCompatibleDetail(ctx context.Context, tableComp *TableCompatibleDetail) ([]*TableGroupResultDetail, error) {
	var objDetails []*TableGroupResultDetail
	err := rw.GormDB.DB(ctx).Model(&TableCompatibleDetail{}).Select("service_name_s,schema_name_s,table_type_s,table_type_t,status,count(*) as count").Where(tableComp).Group("service_name_s,schema_name_s,table_type_s,table_type_t,status").Scan(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query group by table compatible detail not found, query cond: %v", tableComp.String())
	} else if err != nil {
		log.Errorf("batch get group by table compatible detail failed, query cond %d, %v", tableComp.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) GetTableCompatibleDetailCount(ctx context.Context, tableComp *TableCompatibleDetail) (*TableGroupResultDetail, error) {
	var objDetails *TableGroupResultDetail
	err := rw.GormDB.DB(ctx).Model(&TableCompatibleDetail{}).Select("count(1) as count, schema_name_s, table_name_s").Where(tableComp).Group("schema_name_s, table_name_s ").Scan(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query group by table compatible detail not found, query cond: %v", tableComp.String())
	} else if err != nil {
		log.Errorf("batch get group by table compatible detail failed, query cond %d, %v", tableComp.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) UpdateTableCompatibleDetailStatusFixed(ctx context.Context, tableComp *TableCompatibleDetail) (*TableCompatibleDetail, error) {
	err := rw.DB(ctx).Model(&TableCompatibleDetail{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND table_type_s = ? ",
		tableComp.TaskID,
		tableComp.SchemaNameS,
		tableComp.TableNameS,
		tableComp.TableTypeS).Updates(map[string]interface{}{
		"status":    tableComp.Status,
		"fixed_ddl": tableComp.FixedDDL}).Error
	if err != nil {
		log.Errorf("update table compatible detail info to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tableComp, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailBySchema(ctx context.Context, taskId int, schemas []string) ([]*TableResultDetail, error) {
	var objDetails []*TableResultDetail
	query := rw.GormDB.DB(ctx).Where("task_id=? ", taskId)
	if len(schemas) > 0 {
		query = query.Where("schema_name_s in ?", schemas)
	}
	err := query.Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table result detail not found, query taskid:%v,schemas: %v", taskId, schemas)
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query taskid:%v,schemas: %v, err: %v", taskId, schemas, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableResultDetailBySchemaAndStatus(ctx context.Context, taskId int, schemas []string, status int) ([]*TableResultDetail, error) {
	var objDetails []*TableResultDetail
	query := rw.GormDB.DB(ctx).Where("task_id=? and  status=?", taskId, status)
	if len(schemas) > 0 {
		query = query.Where("schema_name_s in ?", schemas)
	}
	err := query.Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table result detail not found, query taskid:%v,schemas: %v", taskId, schemas)
	} else if err != nil {
		log.Errorf("batch get table result detail failed, query taskid:%v,schemas: %v, err: %v", taskId, schemas, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableCompatibleDetailBySchema(ctx context.Context, taskId int, schemas []string) ([]*TableCompatibleDetail, error) {
	var objDetails []*TableCompatibleDetail
	query := rw.GormDB.DB(ctx).Where("task_id=? ", taskId)
	if len(schemas) > 0 {
		query = query.Where("schema_name_s in ?", schemas)
	}
	err := query.Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table compatible detail not found, query taskid:%v, schemas: %v", taskId, schemas)
	} else if err != nil {
		log.Errorf("batch get table compatible detail failed, query taskid:%v,schemas: %v, err: %v", taskId, schemas, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) BatchGetTableCompatibleDetailByTable(ctx context.Context, taskId int, schemaNameS, tableNameS string) ([]*TableCompatibleDetail, error) {
	var objDetails []*TableCompatibleDetail
	query := rw.GormDB.DB(ctx).Where("task_id=? ", taskId)
	query = query.Where("schema_name_s = ?", schemaNameS)
	query = query.Where("table_name_s COLLATE utf8mb4_bin = ?", tableNameS)

	err := query.Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("batch query table compatible detail not found, query taskid:%v, schemaNameS: %s, tableNameS: %s", taskId, schemaNameS, tableNameS)
	} else if err != nil {
		log.Errorf("batch get table compatible detail failed, query taskid:%v,schemaNameS: %s, tableNameS:%s, err: %v", taskId, schemaNameS, tableNameS, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw StructureMigrationReadWrite) ResetTableResultDetail(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Model(&TableResultDetail{}).Where("task_id = ? and status = 2 ",
		taskId).Updates(map[string]interface{}{
		"status": 4}).Error
	if err != nil {
		log.Errorf("update table result detail info to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw StructureMigrationReadWrite) BatchUpdateTableResultDetailToPartialSuccessByFailedIndex(ctx context.Context, taskId int) error {
	sql := `update table_result_details t1
join (
		select distinct trd.task_run_id from table_result_details trd , table_result_details trd2
	where trd.task_id = ?
	and trd.schema_name_s = trd2.schema_name_s
	and trd.table_name_s = trd2.table_name_s
	and trd.table_type_s not in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY')
	and trd.status = 3
	and trd2.task_id= ?
	and trd2.table_type_s in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY')
	and trd2.status = 4
) t2 on t1.task_run_id=t2.task_run_id
set status = 7
where t1.task_id =?`
	err := rw.DB(ctx).Exec(sql, taskId, taskId, taskId).Error
	if err != nil {
		log.Errorf("BatchUpdateTableResultDetailToPartialSuccessByFailedIndex failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw StructureMigrationReadWrite) BatchUpdateTableResultDetailToPartialSuccessByIncompatibalIndex(ctx context.Context, taskId int) error {
	sql := `update table_result_details t1
join (
	select distinct trd.task_run_id from table_result_details trd ,table_compatible_details tcd 
	where trd.task_id =? and tcd.task_id=?
	and trd.schema_name_s = tcd.schema_name_s
	and trd.table_name_s = tcd.table_name_s
	and trd.table_type_s not in ('UNIQUE KEYS','UNIQUE INDEX','NORMAL INDEX','PRIMARY KEY')
	and trd.status = 3
) t2 on t1.task_run_id=t2.task_run_id
set status = 7
where t1.task_id =?
`
	err := rw.DB(ctx).Exec(sql, taskId, taskId, taskId).Error
	if err != nil {
		log.Errorf("BatchUpdateTableResultDetailToPartialSuccessByIncompatibalIndex failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw StructureMigrationReadWrite) SumTableResultDetailDurationBySchema(ctx context.Context, taskId int) ([]*TableResultDetailSumDuration, error) {
	var objDetails []*TableResultDetailSumDuration
	err := rw.GormDB.DB(ctx).Model(&TableResultDetail{}).Select("sum(duration) as duration,schema_name_s").Where("task_id = ?", taskId).Group("schema_name_s").Scan(&objDetails).Error
	if err != nil {
		log.Errorf("GetTableResultDetailSumDuration failed, taskId:%d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}
