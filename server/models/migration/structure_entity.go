package migration

import (
	"encoding/json"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type TableResultDetail struct {
	TaskRunID    int     `gorm:"primarykey"`
	ChannelID    int     `gorm:"not null;comment:channel id"`
	TaskID       int     `gorm:"not null;comment:task id"`
	ServiceNameS string  `gorm:"type:varchar(100);comment:service name"`
	DBNameS      string  `gorm:"type:varchar(100);comment:database name"`
	SchemaNameS  string  `gorm:"type:varchar(200);comment:source schema name"`
	TableNameS   string  `gorm:"type:varchar(200);comment:source table name"`
	ObjectNameS  string  `gorm:"type:varchar(200);comment:source index or key name"`
	TableTypeS   string  `gorm:"type:varchar(100);comment:source table type"`
	SchemaNameT  string  `gorm:"type:varchar(200);comment:target schema name"`
	TableNameT   string  `gorm:"type:varchar(200);comment:target table name"`
	TableTypeT   string  `gorm:"type:varchar(100);comment:target table type"`
	Status       int     `gorm:"type:int;comment:run status, 4 finish,5 failed"`
	Duration     float64 `gorm:"type:int;comment:run duration, size: seconds"`
	OriginDDL    string  `gorm:"type:longtext;comment:origin ddl"`
	ReverseDDL   string  `gorm:"type:longtext;comment:reverse ddl"`
	FixedDDL     string  `gorm:"type:longtext;comment:fixed ddl"`
	ErrorDetail  string  `gorm:"type:text;comment:reverse ddl execute error detail"`
	*common.Entity
}

type TableResultDetailSumDuration struct {
	SchemaNameS string  `gorm:"type:varchar(50);comment:source schema name"`
	Duration    float64 `gorm:"type:int;comment:run duration, size: seconds"`
	*common.Entity
}

type TableCompatibleDetail struct {
	TableCompID      int    `gorm:"primarykey"`
	ChannelID        int    `gorm:"not null;comment:channel id"`
	TaskID           int    `gorm:"not null;comment:task id"`
	ServiceNameS     string `gorm:"type:varchar(100);comment:service name"`
	DBNameS          string `gorm:"type:varchar(100);comment:database name"`
	SchemaNameS      string `gorm:"type:varchar(200);comment:source schema name"`
	TableNameS       string `gorm:"type:varchar(200);comment:source table name"`
	ObjectNameS      string `gorm:"type:varchar(200);comment:source index or key name"`
	TableTypeS       string `gorm:"type:varchar(100);comment:source table type"`
	SchemaNameT      string `gorm:"type:varchar(200);comment:target schema name"`
	TableNameT       string `gorm:"type:varchar(200);comment:target table name"`
	TableTypeT       string `gorm:"type:varchar(100);comment:target table type"`
	Status           int    `gorm:"type:int;comment:run status, 4 finish,5 failed"`
	OriginDDL        string `gorm:"type:longtext;comment:origin ddl"`
	CompatibleDDL    string `gorm:"type:longtext;comment:compatible ddl"`
	FixedDDL         string `gorm:"type:longtext;comment:fixed ddl"`
	CompatibleDetail string `gorm:"type:text;comment:compatible detail"`
	*common.Entity
}

type TableGroupResultDetail struct {
	ServiceNameS string `json:"service_name_s"`
	SchemaNameS  string `json:"schema_name_s"`
	TableTypeS   string `json:"table_type_s"`
	TableTypeT   string `json:"table_type_t"`
	Status       int    `json:"status"`
	Count        int    `json:"count"`
}

func (e *TableCompatibleDetail) String() string {
	marshal, _ := json.Marshal(e)
	return string(marshal)
}

func (e *TableResultDetail) String() string {
	marshal, _ := json.Marshal(e)
	return string(marshal)
}
