// Code generated by MockGen. DO NOT EDIT.
// Source: ./migration/structure_readerwriter.go

// Package mockmigrationareaderwriter is a generated GoMock package.
package mockmigrationareaderwriter

import (
	context "context"
	reflect "reflect"

	migration "gitee.com/pingcap_enterprise/tms/server/models/migration"
	gomock "github.com/golang/mock/gomock"
)

// MockStructureReaderWriter is a mock of StructureReaderWriter interface.
type MockStructureReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockStructureReaderWriterMockRecorder
}

// MockStructureReaderWriterMockRecorder is the mock recorder for MockStructureReaderWriter.
type MockStructureReaderWriterMockRecorder struct {
	mock *MockStructureReaderWriter
}

// NewMockStructureReaderWriter creates a new mock instance.
func NewMockStructureReaderWriter(ctrl *gomock.Controller) *MockStructureReaderWriter {
	mock := &MockStructureReaderWriter{ctrl: ctrl}
	mock.recorder = &MockStructureReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStructureReaderWriter) EXPECT() *MockStructureReaderWriterMockRecorder {
	return m.recorder
}

// BatchCreateTableCompatible mocks base method.
func (m *MockStructureReaderWriter) BatchCreateTableCompatible(ctx context.Context, tableComp []*migration.TableCompatibleDetail, batchSize int) ([]*migration.TableCompatibleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateTableCompatible", ctx, tableComp, batchSize)
	ret0, _ := ret[0].([]*migration.TableCompatibleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateTableCompatible indicates an expected call of BatchCreateTableCompatible.
func (mr *MockStructureReaderWriterMockRecorder) BatchCreateTableCompatible(ctx, tableComp, batchSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateTableCompatible", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchCreateTableCompatible), ctx, tableComp, batchSize)
}

// BatchGetGroupTableCompatibleDetail mocks base method.
func (m *MockStructureReaderWriter) BatchGetGroupTableCompatibleDetail(ctx context.Context, tableComp *migration.TableCompatibleDetail) ([]*migration.TableGroupResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupTableCompatibleDetail", ctx, tableComp)
	ret0, _ := ret[0].([]*migration.TableGroupResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupTableCompatibleDetail indicates an expected call of BatchGetGroupTableCompatibleDetail.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetGroupTableCompatibleDetail(ctx, tableComp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupTableCompatibleDetail", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetGroupTableCompatibleDetail), ctx, tableComp)
}

// BatchGetGroupTableResultDetail mocks base method.
func (m *MockStructureReaderWriter) BatchGetGroupTableResultDetail(ctx context.Context, tableResult *migration.TableResultDetail) ([]*migration.TableGroupResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupTableResultDetail", ctx, tableResult)
	ret0, _ := ret[0].([]*migration.TableGroupResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupTableResultDetail indicates an expected call of BatchGetGroupTableResultDetail.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetGroupTableResultDetail(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupTableResultDetail", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetGroupTableResultDetail), ctx, tableResult)
}

// BatchGetTableCompatibleDetail mocks base method.
func (m *MockStructureReaderWriter) BatchGetTableCompatibleDetail(ctx context.Context, tableComp *migration.TableCompatibleDetail) ([]migration.TableCompatibleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableCompatibleDetail", ctx, tableComp)
	ret0, _ := ret[0].([]migration.TableCompatibleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTableCompatibleDetail indicates an expected call of BatchGetTableCompatibleDetail.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetTableCompatibleDetail(ctx, tableComp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableCompatibleDetail", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetTableCompatibleDetail), ctx, tableComp)
}

// BatchGetTableCompatibleDetailBySchema mocks base method.
func (m *MockStructureReaderWriter) BatchGetTableCompatibleDetailBySchema(ctx context.Context, taskId int, schemas []string) ([]*migration.TableCompatibleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableCompatibleDetailBySchema", ctx, taskId, schemas)
	ret0, _ := ret[0].([]*migration.TableCompatibleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTableCompatibleDetailBySchema indicates an expected call of BatchGetTableCompatibleDetailBySchema.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetTableCompatibleDetailBySchema(ctx, taskId, schemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableCompatibleDetailBySchema", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetTableCompatibleDetailBySchema), ctx, taskId, schemas)
}

// BatchGetTableCompatibleDetailPage mocks base method.
func (m *MockStructureReaderWriter) BatchGetTableCompatibleDetailPage(ctx context.Context, tableComp *migration.TableCompatibleDetail, page, pageSize int) ([]migration.TableCompatibleDetail, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableCompatibleDetailPage", ctx, tableComp, page, pageSize)
	ret0, _ := ret[0].([]migration.TableCompatibleDetail)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetTableCompatibleDetailPage indicates an expected call of BatchGetTableCompatibleDetailPage.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetTableCompatibleDetailPage(ctx, tableComp, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableCompatibleDetailPage", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetTableCompatibleDetailPage), ctx, tableComp, page, pageSize)
}

// BatchGetTableResultDetail mocks base method.
func (m *MockStructureReaderWriter) BatchGetTableResultDetail(ctx context.Context, tableResult *migration.TableResultDetail) ([]*migration.TableResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableResultDetail", ctx, tableResult)
	ret0, _ := ret[0].([]*migration.TableResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTableResultDetail indicates an expected call of BatchGetTableResultDetail.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetTableResultDetail(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableResultDetail", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetTableResultDetail), ctx, tableResult)
}

// BatchGetTableResultDetailBySchema mocks base method.
func (m *MockStructureReaderWriter) BatchGetTableResultDetailBySchema(ctx context.Context, taskId int, schemas []string) ([]*migration.TableResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableResultDetailBySchema", ctx, taskId, schemas)
	ret0, _ := ret[0].([]*migration.TableResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTableResultDetailBySchema indicates an expected call of BatchGetTableResultDetailBySchema.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetTableResultDetailBySchema(ctx, taskId, schemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableResultDetailBySchema", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetTableResultDetailBySchema), ctx, taskId, schemas)
}

// BatchGetTableResultDetailPage mocks base method.
func (m *MockStructureReaderWriter) BatchGetTableResultDetailPage(ctx context.Context, tableResult *migration.TableResultDetail, page, pageSize int) ([]*migration.TableResultDetail, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableResultDetailPage", ctx, tableResult, page, pageSize)
	ret0, _ := ret[0].([]*migration.TableResultDetail)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchGetTableResultDetailPage indicates an expected call of BatchGetTableResultDetailPage.
func (mr *MockStructureReaderWriterMockRecorder) BatchGetTableResultDetailPage(ctx, tableResult, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableResultDetailPage", reflect.TypeOf((*MockStructureReaderWriter)(nil).BatchGetTableResultDetailPage), ctx, tableResult, page, pageSize)
}

// CreateTableResultDetail mocks base method.
func (m *MockStructureReaderWriter) CreateTableResultDetail(ctx context.Context, tableResult *migration.TableResultDetail) (*migration.TableResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTableResultDetail", ctx, tableResult)
	ret0, _ := ret[0].(*migration.TableResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTableResultDetail indicates an expected call of CreateTableResultDetail.
func (mr *MockStructureReaderWriterMockRecorder) CreateTableResultDetail(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTableResultDetail", reflect.TypeOf((*MockStructureReaderWriter)(nil).CreateTableResultDetail), ctx, tableResult)
}

// GetTableResultDetailByInterval mocks base method.
func (m *MockStructureReaderWriter) GetTableResultDetailByInterval(ctx context.Context, tableResult *migration.TableResultDetail) ([]*migration.TableResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTableResultDetailByInterval", ctx, tableResult)
	ret0, _ := ret[0].([]*migration.TableResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTableResultDetailByInterval indicates an expected call of GetTableResultDetailByInterval.
func (mr *MockStructureReaderWriterMockRecorder) GetTableResultDetailByInterval(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTableResultDetailByInterval", reflect.TypeOf((*MockStructureReaderWriter)(nil).GetTableResultDetailByInterval), ctx, tableResult)
}

// UpdateTableCompatibleDetailStatusFixed mocks base method.
func (m *MockStructureReaderWriter) UpdateTableCompatibleDetailStatusFixed(ctx context.Context, tableComp *migration.TableCompatibleDetail) (*migration.TableCompatibleDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTableCompatibleDetailStatusFixed", ctx, tableComp)
	ret0, _ := ret[0].(*migration.TableCompatibleDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTableCompatibleDetailStatusFixed indicates an expected call of UpdateTableCompatibleDetailStatusFixed.
func (mr *MockStructureReaderWriterMockRecorder) UpdateTableCompatibleDetailStatusFixed(ctx, tableComp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTableCompatibleDetailStatusFixed", reflect.TypeOf((*MockStructureReaderWriter)(nil).UpdateTableCompatibleDetailStatusFixed), ctx, tableComp)
}

// UpdateTableResultDetail mocks base method.
func (m *MockStructureReaderWriter) UpdateTableResultDetail(ctx context.Context, tableResult *migration.TableResultDetail) (*migration.TableResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTableResultDetail", ctx, tableResult)
	ret0, _ := ret[0].(*migration.TableResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTableResultDetail indicates an expected call of UpdateTableResultDetail.
func (mr *MockStructureReaderWriterMockRecorder) UpdateTableResultDetail(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTableResultDetail", reflect.TypeOf((*MockStructureReaderWriter)(nil).UpdateTableResultDetail), ctx, tableResult)
}

// UpdateTableResultDetailStatusFixed mocks base method.
func (m *MockStructureReaderWriter) UpdateTableResultDetailStatusFixed(ctx context.Context, tableComp *migration.TableResultDetail) (*migration.TableResultDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTableResultDetailStatusFixed", ctx, tableComp)
	ret0, _ := ret[0].(*migration.TableResultDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTableResultDetailStatusFixed indicates an expected call of UpdateTableResultDetailStatusFixed.
func (mr *MockStructureReaderWriterMockRecorder) UpdateTableResultDetailStatusFixed(ctx, tableComp interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTableResultDetailStatusFixed", reflect.TypeOf((*MockStructureReaderWriter)(nil).UpdateTableResultDetailStatusFixed), ctx, tableComp)
}
