// Code generated by MockGen. DO NOT EDIT.
// Source: ./migration/fulldata_readerwriter.go

// Package mockmigrationareaderwriter is a generated GoMock package.
package mockmigrationareaderwriter

import (
	context "context"
	sql "database/sql"
	reflect "reflect"

	migration "gitee.com/pingcap_enterprise/tms/server/models/migration"
	gomock "github.com/golang/mock/gomock"
)

// MockFullDataReaderWriter is a mock of FullDataReaderWriter interface.
type MockFullDataReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockFullDataReaderWriterMockRecorder
}

// MockFullDataReaderWriterMockRecorder is the mock recorder for MockFullDataReaderWriter.
type MockFullDataReaderWriterMockRecorder struct {
	mock *MockFullDataReaderWriter
}

// NewMockFullDataReaderWriter creates a new mock instance.
func NewMockFullDataReaderWriter(ctrl *gomock.Controller) *MockFullDataReaderWriter {
	mock := &MockFullDataReaderWriter{ctrl: ctrl}
	mock.recorder = &MockFullDataReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFullDataReaderWriter) EXPECT() *MockFullDataReaderWriterMockRecorder {
	return m.recorder
}

// BatchCreateTableMigrationDetail mocks base method.
func (m *MockFullDataReaderWriter) BatchCreateTableMigrationDetail(ctx context.Context, tableResult []migration.TableMigrationDetail, batchSize int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateTableMigrationDetail", ctx, tableResult, batchSize)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateTableMigrationDetail indicates an expected call of BatchCreateTableMigrationDetail.
func (mr *MockFullDataReaderWriterMockRecorder) BatchCreateTableMigrationDetail(ctx, tableResult, batchSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateTableMigrationDetail", reflect.TypeOf((*MockFullDataReaderWriter)(nil).BatchCreateTableMigrationDetail), ctx, tableResult, batchSize)
}

// BatchGetTableMigrationDetail mocks base method.
func (m *MockFullDataReaderWriter) BatchGetTableMigrationDetail(ctx context.Context, taskID int, schemaName, tableName string, ids []uint, taskStatus string) ([]migration.TableMigrationDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableMigrationDetail", ctx, taskID, schemaName, tableName, ids, taskStatus)
	ret0, _ := ret[0].([]migration.TableMigrationDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTableMigrationDetail indicates an expected call of BatchGetTableMigrationDetail.
func (mr *MockFullDataReaderWriterMockRecorder) BatchGetTableMigrationDetail(ctx, taskID, schemaName, tableName, ids, taskStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableMigrationDetail", reflect.TypeOf((*MockFullDataReaderWriter)(nil).BatchGetTableMigrationDetail), ctx, taskID, schemaName, tableName, ids, taskStatus)
}

// BatchGetTableMigrationSummary mocks base method.
func (m *MockFullDataReaderWriter) BatchGetTableMigrationSummary(ctx context.Context, taskID int, ids []uint, taskStatus string) ([]migration.TableMigrationSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTableMigrationSummary", ctx, taskID, ids, taskStatus)
	ret0, _ := ret[0].([]migration.TableMigrationSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTableMigrationSummary indicates an expected call of BatchGetTableMigrationSummary.
func (mr *MockFullDataReaderWriterMockRecorder) BatchGetTableMigrationSummary(ctx, taskID, ids, taskStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTableMigrationSummary", reflect.TypeOf((*MockFullDataReaderWriter)(nil).BatchGetTableMigrationSummary), ctx, taskID, ids, taskStatus)
}

// CreateTableMigrationDetail mocks base method.
func (m *MockFullDataReaderWriter) CreateTableMigrationDetail(ctx context.Context, tableResult *migration.TableMigrationDetail) (*migration.TableMigrationDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTableMigrationDetail", ctx, tableResult)
	ret0, _ := ret[0].(*migration.TableMigrationDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTableMigrationDetail indicates an expected call of CreateTableMigrationDetail.
func (mr *MockFullDataReaderWriterMockRecorder) CreateTableMigrationDetail(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTableMigrationDetail", reflect.TypeOf((*MockFullDataReaderWriter)(nil).CreateTableMigrationDetail), ctx, tableResult)
}

// CreateTableMigrationSummary mocks base method.
func (m *MockFullDataReaderWriter) CreateTableMigrationSummary(ctx context.Context, tableResult *migration.TableMigrationSummary) (*migration.TableMigrationSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTableMigrationSummary", ctx, tableResult)
	ret0, _ := ret[0].(*migration.TableMigrationSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTableMigrationSummary indicates an expected call of CreateTableMigrationSummary.
func (mr *MockFullDataReaderWriterMockRecorder) CreateTableMigrationSummary(ctx, tableResult interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTableMigrationSummary", reflect.TypeOf((*MockFullDataReaderWriter)(nil).CreateTableMigrationSummary), ctx, tableResult)
}

// DeleteTableMigrationDetail mocks base method.
func (m *MockFullDataReaderWriter) DeleteTableMigrationDetail(ctx context.Context, detailS *migration.TableMigrationDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTableMigrationDetail", ctx, detailS)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTableMigrationDetail indicates an expected call of DeleteTableMigrationDetail.
func (mr *MockFullDataReaderWriterMockRecorder) DeleteTableMigrationDetail(ctx, detailS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTableMigrationDetail", reflect.TypeOf((*MockFullDataReaderWriter)(nil).DeleteTableMigrationDetail), ctx, detailS)
}

// DeleteTableMigrationSummary mocks base method.
func (m *MockFullDataReaderWriter) DeleteTableMigrationSummary(ctx context.Context, detailS *migration.TableMigrationSummary) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTableMigrationSummary", ctx, detailS)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTableMigrationSummary indicates an expected call of DeleteTableMigrationSummary.
func (mr *MockFullDataReaderWriterMockRecorder) DeleteTableMigrationSummary(ctx, detailS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTableMigrationSummary", reflect.TypeOf((*MockFullDataReaderWriter)(nil).DeleteTableMigrationSummary), ctx, detailS)
}

// DetailTableMigrationDetail mocks base method.
func (m *MockFullDataReaderWriter) DetailTableMigrationDetail(ctx context.Context, detailS *migration.TableMigrationDetail) ([]migration.TableMigrationDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetailTableMigrationDetail", ctx, detailS)
	ret0, _ := ret[0].([]migration.TableMigrationDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DetailTableMigrationDetail indicates an expected call of DetailTableMigrationDetail.
func (mr *MockFullDataReaderWriterMockRecorder) DetailTableMigrationDetail(ctx, detailS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetailTableMigrationDetail", reflect.TypeOf((*MockFullDataReaderWriter)(nil).DetailTableMigrationDetail), ctx, detailS)
}

// DetailTableMigrationSummary mocks base method.
func (m *MockFullDataReaderWriter) DetailTableMigrationSummary(ctx context.Context, detailS *migration.TableMigrationSummary) ([]migration.TableMigrationSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetailTableMigrationSummary", ctx, detailS)
	ret0, _ := ret[0].([]migration.TableMigrationSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DetailTableMigrationSummary indicates an expected call of DetailTableMigrationSummary.
func (mr *MockFullDataReaderWriterMockRecorder) DetailTableMigrationSummary(ctx, detailS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetailTableMigrationSummary", reflect.TypeOf((*MockFullDataReaderWriter)(nil).DetailTableMigrationSummary), ctx, detailS)
}

// DetailTableMigrationSummaryPage mocks base method.
func (m *MockFullDataReaderWriter) DetailTableMigrationSummaryPage(ctx context.Context, detailS *migration.TableMigrationSummary, page, pageSize int) ([]migration.TableMigrationSummary, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetailTableMigrationSummaryPage", ctx, detailS, page, pageSize)
	ret0, _ := ret[0].([]migration.TableMigrationSummary)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// DetailTableMigrationSummaryPage indicates an expected call of DetailTableMigrationSummaryPage.
func (mr *MockFullDataReaderWriterMockRecorder) DetailTableMigrationSummaryPage(ctx, detailS, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetailTableMigrationSummaryPage", reflect.TypeOf((*MockFullDataReaderWriter)(nil).DetailTableMigrationSummaryPage), ctx, detailS, page, pageSize)
}

// GetTableMigrationDetailByInterval mocks base method.
func (m *MockFullDataReaderWriter) GetTableMigrationDetailByInterval(ctx context.Context, detailS *migration.TableMigrationDetail) ([]*migration.TableMigrationDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTableMigrationDetailByInterval", ctx, detailS)
	ret0, _ := ret[0].([]*migration.TableMigrationDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTableMigrationDetailByInterval indicates an expected call of GetTableMigrationDetailByInterval.
func (mr *MockFullDataReaderWriterMockRecorder) GetTableMigrationDetailByInterval(ctx, detailS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTableMigrationDetailByInterval", reflect.TypeOf((*MockFullDataReaderWriter)(nil).GetTableMigrationDetailByInterval), ctx, detailS)
}

// GetTableMigrationSummaryByInterval mocks base method.
func (m *MockFullDataReaderWriter) GetTableMigrationSummaryByInterval(ctx context.Context, detailS *migration.TableMigrationSummary) ([]*migration.TableMigrationSummary, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTableMigrationSummaryByInterval", ctx, detailS)
	ret0, _ := ret[0].([]*migration.TableMigrationSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTableMigrationSummaryByInterval indicates an expected call of GetTableMigrationSummaryByInterval.
func (mr *MockFullDataReaderWriterMockRecorder) GetTableMigrationSummaryByInterval(ctx, detailS interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTableMigrationSummaryByInterval", reflect.TypeOf((*MockFullDataReaderWriter)(nil).GetTableMigrationSummaryByInterval), ctx, detailS)
}

// TruncateTargetSchemaTable mocks base method.
func (m *MockFullDataReaderWriter) TruncateTargetSchemaTable(ctx context.Context, db *sql.DB, targetSchema, targetTable string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TruncateTargetSchemaTable", ctx, db, targetSchema, targetTable)
	ret0, _ := ret[0].(error)
	return ret0
}

// TruncateTargetSchemaTable indicates an expected call of TruncateTargetSchemaTable.
func (mr *MockFullDataReaderWriterMockRecorder) TruncateTargetSchemaTable(ctx, db, targetSchema, targetTable interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TruncateTargetSchemaTable", reflect.TypeOf((*MockFullDataReaderWriter)(nil).TruncateTargetSchemaTable), ctx, db, targetSchema, targetTable)
}

// UpdateTableMigrationDetail mocks base method.
func (m *MockFullDataReaderWriter) UpdateTableMigrationDetail(ctx context.Context, detailS *migration.TableMigrationDetail, updates map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTableMigrationDetail", ctx, detailS, updates)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTableMigrationDetail indicates an expected call of UpdateTableMigrationDetail.
func (mr *MockFullDataReaderWriterMockRecorder) UpdateTableMigrationDetail(ctx, detailS, updates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTableMigrationDetail", reflect.TypeOf((*MockFullDataReaderWriter)(nil).UpdateTableMigrationDetail), ctx, detailS, updates)
}

// UpdateTableMigrationSummary mocks base method.
func (m *MockFullDataReaderWriter) UpdateTableMigrationSummary(ctx context.Context, detailS *migration.TableMigrationSummary, updates map[string]interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTableMigrationSummary", ctx, detailS, updates)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTableMigrationSummary indicates an expected call of UpdateTableMigrationSummary.
func (mr *MockFullDataReaderWriterMockRecorder) UpdateTableMigrationSummary(ctx, detailS, updates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTableMigrationSummary", reflect.TypeOf((*MockFullDataReaderWriter)(nil).UpdateTableMigrationSummary), ctx, detailS, updates)
}
