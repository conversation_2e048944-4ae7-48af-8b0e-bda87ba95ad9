package migration

import (
	"encoding/json"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type TableMigrationSummary struct {
	ID               uint    `gorm:"primary_key;autoIncrement;"`
	TaskID           int     `gorm:"not null;index:idx_dbtype_st_map,unique;comment:task id;index:idx_table_migration_summaries_task_id"`
	ServiceNameS     string  `gorm:"type:varchar(100);comment:service name"`
	DBNameS          string  `gorm:"type:varchar(100);comment:database name"`
	SchemaNameS      string  `gorm:"type:varchar(200);not null;index:idx_dbtype_st_map,unique;comment:source schema"`
	TableNameS       string  `gorm:"type:varchar(200);not null;index:idx_dbtype_st_map,unique;comment:source table name"`
	TableTypeS       string  `gorm:"type:varchar(100);comment:'源端表类型'" json:"table_type_s"`
	SchemaNameT      string  `gorm:"type:varchar(200);not null;comment:'目标端 schema'" json:"schema_name_t"`
	TableNameT       string  `gorm:"type:varchar(200);not null;comment:'目标端表名'" json:"table_name_t"`
	TaskStatus       string  `gorm:"type:varchar(50);not null;comment:'任务状态'" json:"task_status"`
	ChunkTotalNums   int64   `gorm:"comment:'切分 chunk 数'" json:"chunk_total_nums"`
	ChunkSuccessNums int64   `gorm:"comment:'成功 chunk 数'" json:"chunk_success_nums"`
	ChunkFailedNums  int64   `gorm:"comment:'失败 chunk 数'" json:"chunk_failed_nums"`
	Duration         float64 `gorm:"type:int;comment:run duration, size: seconds"`
	ChunkSize        int64   `gorm:"comment:'chunk size'" json:"chunk_size"`
	*common.Entity
}

type TableMigrationDetail struct {
	ID             uint    `gorm:"primary_key;autoIncrement;"`
	TaskID         int     `gorm:"not null;index:idx_dbtype_st_map,unique;comment:task id;index:idx_table_migration_details_task_id"`
	ServiceNameS   string  `gorm:"type:varchar(100);comment:service name"`
	DBNameS        string  `gorm:"type:varchar(100);comment:database name"`
	SchemaNameS    string  `gorm:"type:varchar(200);not null;index:idx_dbtype_st_map,unique;comment:'源端 schema'" json:"schema_name_s"`
	TableNameS     string  `gorm:"type:varchar(200);not null;index:idx_dbtype_st_map,unique;comment:'源端表名'" json:"table_name_s"`
	SchemaNameT    string  `gorm:"type:varchar(200);not null;comment:'目标端 schema'" json:"schema_name_t"`
	TableNameT     string  `gorm:"type:varchar(200);not null;comment:'目标端表名'" json:"table_name_t"`
	ColumnDetailS  string  `gorm:"type:text;comment:'源端查询字段信息'" json:"column_detail_s"`
	SqlHintS       string  `gorm:"type:text;comment:'源端查询 sql hint'" json:"sql_hint_s"`
	SqlHintT       string  `gorm:"type:text;comment:'目标端端查询 sql hint'" json:"sql_hint_t"`
	ChunkDetailS   string  `gorm:"type:varchar(300);not null;index:idx_dbtype_st_map,unique;comment:'表 chunk 切分信息'" json:"chunk_detail_s"`
	TaskStatus     string  `gorm:"type:varchar(50);not null;comment:'任务 chunk 状态'" json:"task_status"`
	ImportStatus   string  `gorm:"type:varchar(30);default:'waiting';not null;comment:import status"`
	ImportError    string  `gorm:"type:longtext;not null;comment:'import error message'"`
	InfoDetail     string  `gorm:"type:longtext;comment:'信息详情'" json:"info_detail"`
	OriginSQL      string  `gorm:"type:longtext;comment:'原始 SQL 详情'" json:"origin_sql"`
	OriginSQLHash  string  `gorm:"type:longtext;comment:'原始 SQL 指纹'" json:"origin_sql_hash"`
	CSVFile        string  `gorm:"type:varchar(400);comment:'CSV File 路径'" json:"csv_file"`
	CSVSize        int64   `gorm:"comment:'CSV 文件大小';default:0" json:"csv_size"`
	ErrorDetail    string  `gorm:"type:longtext;not null;comment:'错误详情'" json:"error_detail"`
	Duration       float64 `gorm:"type:int;comment:run duration, size: seconds"`
	TablePartition string  `gorm:"type:varchar(100);comment:table partition"`
	*common.Entity
}

func (e *TableMigrationSummary) String() string {
	marshal, _ := json.Marshal(e)
	return string(marshal)
}

func (e *TableMigrationDetail) String() string {
	marshal, _ := json.Marshal(e)
	return string(marshal)
}

type LightningProgress struct {
	LogId        int    `gorm:"primarykey"`
	ChannelID    int    `gorm:"not null;comment:channel id"`
	TaskID       int    `gorm:"not null;comment:task id"`
	Time         string `gorm:"type:varchar(30);comment:log time"`
	LogLevel     string `gorm:"type:varchar(30);comment:log level"`
	Total        string `gorm:"type:varchar(100);comment:total"`
	Tables       string `gorm:"type:varchar(100);comment:tables"`
	Chunks       string `gorm:"type:varchar(100);comment:chunks"`
	Engines      string `gorm:"type:varchar(100);comment:engines"`
	RestoreBytes string `gorm:"type:varchar(200);comment:restore bytes"`
	RestoreRows  string `gorm:"type:varchar(200);comment:restore rows"`
	ImportBytes  string `gorm:"type:varchar(200);comment:import bytes"`
	EncodeSpeed  string `gorm:"type:varchar(200);comment:encode speed"`
	State        string `gorm:"type:varchar(100);comment:state"`
	Remaining    string `gorm:"type:varchar(100);comment:remaining"`
	SchemaNameS  string `gorm:"type:varchar(30);comment:source schema"`
	TableNameS   string `gorm:"type:varchar(100);comment:source table name"`
	*common.Entity
}

func (e *LightningProgress) String() string {
	marshal, _ := json.Marshal(e)
	return string(marshal)
}

type CSVStage struct {
	ID           int    `gorm:"primarykey"`
	ChannelId    int    `gorm:"not null;comment:channel id"`
	TaskId       int    `gorm:"not null;uniqueIndex:task_schema_table;comment:task id"`
	ParentTaskId int    `gorm:"not null;comment:parent task id"`
	SchemaNameS  string `gorm:"type:varchar(30);not null;uniqueIndex:task_schema_table;comment:source schema"`
	TableNameS   string `gorm:"type:varchar(100);not null;uniqueIndex:task_schema_table;comment:source table name"`
	ExportToCSV  bool   `gorm:"not null;default:false;comment:export to csv"`
	ImportToDB   bool   `gorm:"not null;default:false;comment:import to db"`
	ErrorMessage string `gorm:"type:text;comment:error message"`
	*common.Entity
}

type DetailSummary struct {
	TotalChunks   int `json:"total_chunks"`
	SuccessChunks int `json:"success_chunks"`
	FailedChunks  int `json:"failed_chunks"`
}

type ChunkDataAnalyze struct {
	ID           uint   `gorm:"primarykey"`
	ChannelId    int    `gorm:"not null;comment:channel id"`
	TaskId       int    `gorm:"not null;uniqueIndex:task_schema_table_rowid;comment:task id"`
	SchemaNameS  string `gorm:"type:varchar(30);uniqueIndex:task_schema_table_rowid;not null;comment:source schema"`
	TableNameS   string `gorm:"type:varchar(100);uniqueIndex:task_schema_table_rowid;not null;comment:source table name"`
	ChunkId      uint   `gorm:"not null;comment:chunk id"`
	RowIDStr     string `gorm:"type:varchar(100);comment:row id"`
	RowIDMD5     string `gorm:"type:varchar(100);uniqueIndex:task_schema_table_rowid;comment:row id md5"`
	Status       string `gorm:"type:varchar(100);comment:status"`
	ErrorMessage string `gorm:"type:text;comment:error message"`
	*common.Entity
}

type ChunkDataAnalyzeSummary struct {
	ID               uint   `gorm:"primarykey"`
	ChannelId        int    `gorm:"not null;comment:channel id"`
	TaskId           int    `gorm:"not null;uniqueIndex:task_schema_table;comment:task id"`
	SchemaNameS      string `gorm:"type:varchar(30);uniqueIndex:task_schema_table;not null;comment:source schema"`
	TableNameS       string `gorm:"type:varchar(100);uniqueIndex:task_schema_table;not null;comment:source table name"`
	Status           string `gorm:"type:varchar(100);comment:status"`
	TotalDetails     int64  `gorm:"default:0;comment:total number of details to process"`
	ProcessedDetails int64  `gorm:"default:0;comment:number of details already processed"`
	RemainingDetails int64  `gorm:"default:0;comment:number of details remaining"`
	*common.Entity
}

type ChunkDataAnalyzeStatusCounts struct {
	TotalCount   int64 `json:"totalCount"`
	SuccessCount int64 `json:"successCount"`
	FailedCount  int64 `json:"failedCount"`
	RunningCount int64 `json:"runningCount"`
	WaitingCount int64 `json:"waitingCount"`
}
