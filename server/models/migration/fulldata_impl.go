package migration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/message"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type FullDataMigrationReadWrite struct {
	dbCommon.GormDB
}

func NewFullDataMigrationReadWrite(db *gorm.DB) *FullDataMigrationReadWrite {
	m := &FullDataMigrationReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw FullDataMigrationReadWrite) CreateTableMigrationSummary(ctx context.Context, tableResult *TableMigrationSummary) (*TableMigrationSummary, error) {
	err := rw.DB(ctx).Create(tableResult).Error
	if err != nil {
		log.Errorf("create table migration summary to db failed. %s", err)
		return tableResult, dbCommon.WrapDBError(err)
	}
	log.Infof("create table migration summary to db successfully, %v", tableResult.String())
	return tableResult, nil
}

func (rw FullDataMigrationReadWrite) DetailTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(detailS).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("detail table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return dsMetas, nil
}

// Deprecated: DetailTableMigrationSummaryPage is deprecated.
func (rw FullDataMigrationReadWrite) DetailTableMigrationSummaryPage(ctx context.Context, detailS *TableMigrationSummary, page int, pageSize int) ([]*TableMigrationSummary, int64, error) {
	// var dsMetas []TableMigrationSummary
	dsMetas := make([]*TableMigrationSummary, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TableMigrationSummary{})
	if err := query.Where(detailS).Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&dsMetas).Error; err != nil {
		return nil, 0, fmt.Errorf("detail table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return dsMetas, count, nil
}

func (rw FullDataMigrationReadWrite) DetailTableMigrationSummaryPageWithStatus(ctx context.Context, detailS *TableMigrationSummary, status []string, page int, pageSize int) ([]*TableMigrationSummary, int64, error) {
	dsMetas := make([]*TableMigrationSummary, pageSize)
	var count int64 = 0
	query := rw.DB(ctx).Model(&TableMigrationSummary{}).Where(detailS)
	if len(status) > 0 {
		query = query.Where("task_status IN (?)", status)
	}
	if err := query.Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&dsMetas).Error; err != nil {
		return nil, 0, fmt.Errorf("detail table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return dsMetas, count, nil
}

func (rw FullDataMigrationReadWrite) UpdateTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary, updates map[string]interface{}) error {
	err := rw.DB(ctx).Model(&TableMigrationSummary{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?",
			detailS.TaskID,
			detailS.SchemaNameS,
			detailS.TableNameS).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("update table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) ResetTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary) error {
	err := rw.DB(ctx).Model(&TableMigrationSummary{}).
		Where("task_id = ? and task_status='RUNNING' and task_status<>'INVALID' ",
			detailS.TaskID).Updates(map[string]interface{}{
		"chunk_failed_nums": gorm.Expr("chunk_total_nums - chunk_success_nums"),
		"task_status":       "FAILED",
	}).Error
	if err != nil {
		return fmt.Errorf("reset table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationSummary(ctx context.Context, detailS *TableMigrationSummary) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?",
			detailS.TaskID,
			detailS.SchemaNameS,
			detailS.TableNameS).
		Delete(&TableMigrationSummary{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationSummaryByTaskIds(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id in (?)", taskIds).
		Delete(&TableMigrationSummary{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration summary [%v] record failed: %v", taskIds, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) ResetTableMigrationSummaryForImportByTaskId(ctx context.Context, taskId int) error {
	// just set task_status='waiting'
	err := rw.DB(ctx).Model(&TableMigrationSummary{}).
		Where("task_id = ?", taskId).
		Update("task_status", "waiting").Error
	if err != nil {
		return fmt.Errorf("reset table migration summary for import by task id [%d] failed: %v", taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationDetailByTaskIds(ctx context.Context, taskIds []int) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id in (?)", taskIds).
		Delete(&TableMigrationDetail{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration detail [%v] record failed: %v", taskIds, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) ResetTableMigrationDetailForImportByTaskId(ctx context.Context, taskId int) error {
	// 通过TaskID设置table_migration_details中的task_status="SUCCESS",error_detail="",import_error="",import_status="waiting"
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("task_id = ?", taskId).
		Updates(map[string]interface{}{
			"task_status":   "SUCCESS",
			"error_detail":  "",
			"import_error":  "",
			"import_status": "WAITING",
		}).Error
	if err != nil {
		return fmt.Errorf("reset table migration detail for import by task id [%d] failed: %v", taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationSummaryByTaskIdsAndSchemaTable(ctx context.Context, taskIds []int, schemaName, tableName string) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id in (?)", taskIds).
		Where("schema_name_s = ?", schemaName).
		Where("table_name_s = ?", tableName).
		Delete(&TableMigrationSummary{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration summary [%v] record failed: %v", taskIds, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationDetailByTaskIdsAndSchemaTable(ctx context.Context, taskIds []int, schemaName, tableName string) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id in (?)", taskIds).
		Where("schema_name_s = ?", schemaName).
		Where("table_name_s = ?", tableName).
		Delete(&TableMigrationDetail{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration detail [%v] record failed: %v", taskIds, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummary(ctx context.Context, taskID int, ids []uint, taskStatus string) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`task_id = ? AND id in (?) `, taskID, ids).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummaryByTaskIdSchemaAndStatusList(ctx context.Context, taskID int, schemaName string, statusList []string) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`task_id = ? AND schema_name_s = ? AND task_status IN (?)`, taskID, schemaName, statusList).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummaryByTaskIdAndStatusList(ctx context.Context, taskID int, statusList []string) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`task_id = ? AND task_status IN (?)`, taskID, statusList).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummaryByTaskIdSummaryIds(ctx context.Context, taskID int, ids []uint) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`task_id = ? AND id in (?)`, taskID, ids).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummaryByTaskIds(ctx context.Context, taskIDs []uint) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`task_id in (?)`, taskIDs).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummaryBySummaryIds(ctx context.Context, ids []uint) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`id in (?)`, ids).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) GetTableMigrationSummaryByInterval(ctx context.Context, detailS *TableMigrationSummary) ([]*TableMigrationSummary, error) {
	var objDetails []*TableMigrationSummary
	err := rw.GormDB.DB(ctx).Where("task_id = ? AND created_at <= ?", detailS.TaskID, detailS.CreatedAt).Or("task_id = ? AND updated_at >= ?", detailS.TaskID, detailS.UpdatedAt).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table migration detail not found, query cond: %v", detailS.String())
	} else if err != nil {
		log.Errorf("get table migration detail failed, query cond %d, %v", detailS.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) CreateTableMigrationDetail(ctx context.Context, tableResult *TableMigrationDetail) (*TableMigrationDetail, error) {
	err := rw.DB(ctx).Create(tableResult).Error
	if err != nil {
		log.Errorf("create table migration detail to db failed. %s", err)
		return tableResult, dbCommon.WrapDBError(err)
	}
	log.Infof("create table migration detail to db successfully, %v", tableResult.String())
	return tableResult, nil
}

func (rw FullDataMigrationReadWrite) BatchCreateTableMigrationDetail(ctx context.Context, tableResult []*TableMigrationDetail, batchSize int) ([]*TableMigrationDetail, error) {
	err := rw.DB(ctx).CreateInBatches(tableResult, batchSize).Error
	if err != nil {
		log.Errorf("batch create table migration detail to db failed. %s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return tableResult, nil
}

func (rw FullDataMigrationReadWrite) BatchCreateTableMigrationSummary(ctx context.Context, summaries []*TableMigrationSummary, batchSize int) error {
	err := rw.DB(ctx).CreateInBatches(summaries, batchSize).Error
	if err != nil {
		log.Errorf("batch create table migration summary to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DetailTableMigrationDetail(ctx context.Context, detailS *TableMigrationDetail) ([]TableMigrationDetail, error) {
	var dsMetas []TableMigrationDetail
	if err := rw.DB(ctx).Where(detailS).Find(&dsMetas).Error; err != nil {
		return dsMetas, fmt.Errorf("detail table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) DetailTableMigrationDetailWithStatus(ctx context.Context, detailS *TableMigrationDetail, status []string) ([]TableMigrationDetail, error) {
	var dsMetas []TableMigrationDetail
	query := rw.DB(ctx).Where(detailS)
	if len(status) > 0 {
		query = query.Where("task_status IN (?)", status)
	}
	if err := query.Find(&dsMetas).Error; err != nil {
		return dsMetas, fmt.Errorf("detail table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) UpdateTableMigrationDetail(ctx context.Context, detailId uint, updates map[string]interface{}) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("id = ?", detailId).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("update table migration detail [%v] record failed, detailId:%d, err:%v", updates, detailId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) UpdateTableMigrationDetailImportResult(ctx context.Context, detailS *TableMigrationDetail, updates map[string]interface{}) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND chunk_detail_s = ? AND id = ?",
			detailS.TaskID,
			detailS.SchemaNameS,
			detailS.TableNameS,
			detailS.ChunkDetailS,
			detailS.ID).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("update table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) UpdateTableMigrationDetailById(ctx context.Context, detailS *TableMigrationDetail, updates map[string]interface{}) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("id = ? ", detailS.ID).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("update table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) ResetTableMigrationDetail(ctx context.Context, detailS *TableMigrationDetail) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("task_id = ? and task_status ='RUNNING' and task_status <>'INVALID' ", detailS.TaskID).Update("task_status", "FAILED").Error
	if err != nil {
		return fmt.Errorf("reset table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationDetail(ctx context.Context, detailS *TableMigrationDetail) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?",
			detailS.TaskID,
			detailS.SchemaNameS,
			detailS.TableNameS).
		Delete(&TableMigrationDetail{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) GetTableMigrationDetailByInterval(ctx context.Context, detailS *TableMigrationDetail) ([]*TableMigrationDetail, error) {
	var objDetails []*TableMigrationDetail
	err := rw.GormDB.DB(ctx).Where("task_id = ? AND created_at <= ?", detailS.TaskID, detailS.CreatedAt).Or("task_id = ? AND updated_at >= ?", detailS.TaskID, detailS.UpdatedAt).Limit(constants.MAX_LOG_SIZE).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table migration detail not found, query cond: %v", detailS.String())
	} else if err != nil {
		log.Errorf("get table migration detail failed, query cond %d, %v", detailS.String(), err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationDetail(ctx context.Context, taskID int, schemaName, tableName string, ids []uint, taskStatus string) ([]TableMigrationDetail, error) {
	var objDetails []TableMigrationDetail
	err := rw.DB(ctx).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND task_status = ? AND id in (?)", taskID, schemaName, tableName, taskStatus, ids).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table migration detail not found, query cond: %v", ids)
	} else if err != nil {
		log.Errorf("get table migration detail failed, query cond %d, %v", ids, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(ctx context.Context, taskId int, schemaName, tableName string, statusList []string) ([]TableMigrationDetail, error) {
	var objDetails []TableMigrationDetail
	err := rw.DB(ctx).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND task_status IN (?)", taskId, schemaName, tableName, statusList).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table migration detail not found, query cond: taskId=%v,schemaName=%s,tableName=%s,statusList=%v", taskId, schemaName, tableName, statusList)
	} else if err != nil {
		log.Errorf("get table migration detail failed, err:%v,query cond: taskId=%v,schemaName=%s,tableName=%s,statusList=%v", err, taskId, schemaName, tableName, statusList)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) BatchGetWaitingAndFailedTableMigrationDetail(ctx context.Context, taskId int, schemaName, tableName string) ([]*TableMigrationDetail, error) {
	var objDetails []*TableMigrationDetail
	err := rw.DB(ctx).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND task_status = ? AND import_status in (?)", taskId, schemaName, tableName, "SUCCESS", []string{"waiting", "WAITING", "failed", "FAILED"}).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table migration detail not found, query cond: taskId=%v,schemaName=%s,tableName=%s", taskId, schemaName, tableName)
	}
	if err != nil {
		log.Errorf("get table migration detail failed, err:%v,query cond: taskId=%v,schemaName=%s,tableName=%s", err, taskId, schemaName, tableName)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) TruncateTargetSchemaTable(ctx context.Context, db *sql.DB, targetSchema, targetTable string) error {
	_, err := db.ExecContext(ctx, fmt.Sprintf("TRUNCATE TABLE %s.%s", targetSchema, targetTable))
	if err != nil {
		log.Errorf("truncate target schema table [%s.%s] failed: %v", targetSchema, targetTable, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationDetailByIdsTaskStatus(ctx context.Context, ids []uint, status string) ([]TableMigrationDetail, error) {
	var objDetails []TableMigrationDetail
	err := rw.DB(ctx).Where("id in (?)", ids).Where("task_status = ?", status).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table migration detail not found, query cond: %v", ids)
	} else if err != nil {
		log.Errorf("get table migration detail failed, query cond %d, %v", ids, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationDetailByIds(ctx context.Context, ids []uint) ([]TableMigrationDetail, error) {
	var objDetails []TableMigrationDetail
	err := rw.DB(ctx).Where("id in (?)", ids).Find(&objDetails).Error
	if err == gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("query table migration detail not found, query cond: %v", ids)
	} else if err != nil {
		log.Errorf("get table migration detail failed, query cond %d, %v", ids, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) CountSubTaskMigrationSummaryAndDetailByTaskID(ctx context.Context, taskID int, schemaName, tableName string) (int64, int64, error) {
	sqlTemplate := `SELECT a.count_summary, b.count_detail
FROM (SELECT count(*) as count_summary
      FROM table_migration_summaries m
               JOIN tasks t ON t.task_id = m.task_id AND t.parent_task_id = ? AND m.schema_name_s = ? AND m.table_name_s = ? ) a,
     (SELECT COUNT(*) as count_detail
      FROM table_migration_details d
               JOIN tasks t ON t.task_id = d.task_id AND t.parent_task_id = ? AND d.schema_name_s = ? AND d.table_name_s = ? ) b;`

	type countStruct struct {
		CountDetail  int64 `gorm:"column:count_detail"`
		CountSummary int64 `gorm:"column:count_summary"`
	}
	var countJob countStruct

	err := rw.DB(ctx).Raw(sqlTemplate, taskID, schemaName, tableName, taskID, schemaName, tableName).Scan(&countJob).Error
	if err != nil {
		log.Errorf("count sub task migration summary and detail by task id [%d] failed: %v", taskID, err)
		return 0, 0, err
	}
	return countJob.CountDetail, countJob.CountSummary, nil
}

func (rw FullDataMigrationReadWrite) SaveLightningProgress(ctx context.Context, lightningProgress *LightningProgress) (*LightningProgress, error) {
	err := rw.DB(ctx).Create(lightningProgress).Error
	if err != nil {
		log.Errorf("create lightning progress to db failed. %s", err)
		return lightningProgress, dbCommon.WrapDBError(err)
	}
	return lightningProgress, nil
}

func (rw FullDataMigrationReadWrite) GetLatestLightningProgressByTaskId(ctx context.Context, taskId int) (*LightningProgress, error) {
	lightningProgress := &LightningProgress{}
	err := rw.DB(ctx).Model(&LightningProgress{}).Where("task_id = ?", taskId).Order("log_id desc").First(lightningProgress).Error
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return lightningProgress, nil
}

func (rw FullDataMigrationReadWrite) GetSchemaSTableSLatestLightningProgressByTaskId(ctx context.Context, taskId int) ([]LightningProgress, error) {
	sqlTemplate := `
	SELECT t1.*
	FROM lightning_progresses t1
	JOIN (
		SELECT task_id, schema_name_s, table_name_s, MAX(created_at) AS max_create_time
		FROM lightning_progresses
		WHERE task_id=%d
		GROUP BY schema_name_s, table_name_s
	) t2
	ON t1.schema_name_s = t2.schema_name_s
	AND t1.table_name_s = t2.table_name_s
	AND t1.created_at = t2.max_create_time
	AND t1.task_id = t2.task_id
	WHERE t2.task_id=%d;

`
	querySQL := fmt.Sprintf(sqlTemplate, taskId, taskId)
	log.Infof("GetSchemaSTableSLatestLightningProgressByTaskId querySQL is %s", querySQL)
	var lightningProgresses []LightningProgress
	err := rw.DB(ctx).Raw(querySQL).Scan(&lightningProgresses).Error
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return lightningProgresses, nil
}

func (rw FullDataMigrationReadWrite) ListLightningProgressesByTaskId(ctx context.Context, taskId int) ([]*LightningProgress, error) {
	lightningProgresses := make([]*LightningProgress, 0)
	err := rw.DB(ctx).Model(&LightningProgress{}).Where("task_id = ?", taskId).Order("log_id").Find(&lightningProgresses).Error
	if err != nil {
		log.Errorf("list lightning progresses by task id failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return lightningProgresses, nil
}

func (rw FullDataMigrationReadWrite) ListLightningProgressesByTaskIdSchemaSTableS(ctx context.Context, taskId int, schemaNameS, tableNameS string) ([]*LightningProgress, error) {
	lightningProgresses := make([]*LightningProgress, 0)
	err := rw.DB(ctx).Model(&LightningProgress{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).Order("log_id").Find(&lightningProgresses).Error
	if err != nil {
		log.Errorf("list lightning progresses by task id failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return lightningProgresses, nil
}

func (rw FullDataMigrationReadWrite) SaveCSVStage(ctx context.Context, stage *CSVStage) (*CSVStage, error) {
	err := rw.DB(ctx).Create(stage).Error
	if err != nil {
		log.Errorf("create csv stage to db failed. %s", err)
		return stage, dbCommon.WrapDBError(err)
	}
	return stage, nil
}

func (rw FullDataMigrationReadWrite) GetCSVStageByTaskId(ctx context.Context, taskId int) (*CSVStage, error) {
	csvStage := &CSVStage{}
	err := rw.DB(ctx).Model(&CSVStage{}).Where("task_id = ?", taskId).First(csvStage).Error
	if err != nil {
		log.Errorf("get csv stage by task id failed. taskId is %d, err:%s", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return csvStage, nil
}

func (rw FullDataMigrationReadWrite) GetCSVStageByTaskIdSchemaSTableS(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*CSVStage, error) {
	csvStage := &CSVStage{}
	err := rw.DB(ctx).Model(&CSVStage{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).First(csvStage).Error
	if err != nil {
		log.Errorf("get csv stage by task id failed. taskId is %d, schemaNameS:%s, tableNameSL%s, err:%s", taskId, schemaNameS, tableNameS, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return csvStage, nil
}

func (rw FullDataMigrationReadWrite) BatchSaveCSVStage(ctx context.Context, stages []*CSVStage) error {
	err := rw.DB(ctx).CreateInBatches(stages, 100).Error
	if err != nil {
		log.Errorf("batch save csv stage to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchGetTableMigrationSummaryByStatus(ctx context.Context, taskID int, taskStatusList []string) ([]*TableMigrationSummary, error) {
	var dsMetas []*TableMigrationSummary
	if err := rw.DB(ctx).Where(`task_id = ? AND task_status IN (?)`, taskID, taskStatusList).Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration summary record failed: %v", err)
	}
	return dsMetas, nil
}

// statusList may be empty, importStatusList may be empty
func (rw FullDataMigrationReadWrite) BatchGetTableMigrationDetailByStatus(ctx context.Context, taskID int, statusList, importStatusList []string) ([]TableMigrationDetail, error) {
	var dsMetas []TableMigrationDetail
	query := rw.DB(ctx).Where("task_id = ?", taskID)
	if len(statusList) > 0 && len(importStatusList) > 0 {
		query = query.Where("( task_status IN (?) OR import_status IN (?))", statusList, importStatusList)
	} else {
		if len(statusList) > 0 {
			query = query.Where("task_status IN (?)", statusList)
		}
		if len(importStatusList) > 0 {
			query = query.Where("import_status IN (?)", importStatusList)
		}
	}
	if err := query.Find(&dsMetas).Error; err != nil {
		return nil, fmt.Errorf("batch get table migration detail record failed: %v", err)
	}
	return dsMetas, nil
}

func (rw FullDataMigrationReadWrite) UpdateCSVStage(ctx context.Context, channelId, taskId int, updates map[string]interface{}) error {
	err := rw.DB(ctx).Model(&CSVStage{}).
		Where("channel_id = ? AND task_id = ?", channelId, taskId).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("update csv stage [%d,%d] record failed: %v", channelId, taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) UpdateCSVStageBySchemaSTableS(ctx context.Context, channelId, taskId int, schemaNameS, tableNameS string, updates map[string]interface{}) error {
	err := rw.DB(ctx).Model(&CSVStage{}).
		Where("channel_id = ? AND task_id = ? AND schema_name_s = ? AND table_name_s = ?", channelId, taskId, schemaNameS, tableNameS).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("update csv stage [%d,%d] record failed: %v", channelId, taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchDeleteCSVStage(ctx context.Context, channelId, parentTaskId int) error {
	err := rw.DB(ctx).Unscoped().
		Where("channel_id = ? AND parent_task_id = ?", channelId, parentTaskId).
		Delete(&CSVStage{}).Error
	if err != nil {
		return fmt.Errorf("delete csv stage [%d,%d] record failed: %v", channelId, parentTaskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchDeleteCSVStageByTaskId(ctx context.Context, channelId, taskId int) error {
	err := rw.DB(ctx).Unscoped().
		Where("channel_id = ? AND task_id = ?", channelId, taskId).
		Delete(&CSVStage{}).Error
	if err != nil {
		return fmt.Errorf("delete csv stage [%d,%d] record failed: %v", channelId, taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) ResetCSVStageForImportByTaskId(ctx context.Context, channelId, taskId int) error {
	// 更新csv_stage中的export_to_csv=True, import_to_db=False
	err := rw.DB(ctx).Model(&CSVStage{}).
		Where("channel_id = ? AND task_id = ?", channelId, taskId).
		Updates(map[string]interface{}{
			"export_to_csv": true,
			"import_to_db":  false,
		}).Error
	if err != nil {
		return fmt.Errorf("reset csv stage for import by task id [%d] failed: %v", taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) GetTableMigrationDetailsByTaskId(ctx context.Context, taskId int) ([]*TableMigrationDetail, error) {
	var objDetails []*TableMigrationDetail
	err := rw.GormDB.DB(ctx).Where("task_id = ?", taskId).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table migration detail not found, query cond: taskId=%v", taskId)
	} else if err != nil {
		log.Errorf("get table migration detail failed, err:%v,query cond: taskId=%v", err, taskId)
		return nil, dbCommon.WrapDBError(err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) BatchUpdateTableMigrationSummary(ctx context.Context, summaries []TableMigrationSummary) error {
	err := rw.DB(ctx).Save(summaries).Error
	if err != nil {
		log.Errorf("batch update table migration summary to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchUpdateTableMigrationDetail(ctx context.Context, details []*TableMigrationDetail) error {
	err := rw.DB(ctx).Save(details).Error
	if err != nil {
		log.Errorf("batch update table migration detail to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationDetailByTaskId(ctx context.Context, detailS *TableMigrationDetail) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id = ? ",
			detailS.TaskID).
		Delete(&TableMigrationDetail{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteTableMigrationSummaryByTaskId(ctx context.Context, detailS *TableMigrationSummary) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id = ? ",
			detailS.TaskID).
		Delete(&TableMigrationSummary{}).Error
	if err != nil {
		return fmt.Errorf("delete table migration summary [%s] record failed: %v", detailS.String(), err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) GetLatestLightningProgressByTaskIdSchemaSTableS(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*LightningProgress, error) {
	lightningProgress := &LightningProgress{}
	err := rw.DB(ctx).Model(&LightningProgress{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).Order("log_id desc").First(lightningProgress).Error
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return lightningProgress, nil
}

func (rw FullDataMigrationReadWrite) DeleteCSVStage(ctx context.Context, taskId int, schemaNameS, tableNameS string) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Delete(&CSVStage{}).Error
	if err != nil {
		return fmt.Errorf("delete csv stage [%s] record failed: %v", schemaNameS, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) DeleteLightningProgressByTaskId(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().
		Where("task_id = ?", taskId).
		Delete(&LightningProgress{}).Error
	if err != nil {
		return fmt.Errorf("delete lightning progress [%d] record failed: %v", taskId, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) SetTableMigrationDetailFailed(ctx context.Context, id uint, errorDetail string) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("id = ? ", id).Updates(map[string]interface{}{
		"task_status":  "FAILED",
		"error_detail": errorDetail,
		"duration":     "1",
	}).Error
	if err != nil {
		return fmt.Errorf("set table migration detail failed [%d] record failed: %v", id, err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) ListChunkDataAnalyzePage(ctx context.Context, taskId int, schemaNameS, tableNameS string, page int, pageSize int) ([]ChunkDataAnalyze, int64, error) {
	var chunkDataAnalyzes []ChunkDataAnalyze
	var count int64 = 0
	query := rw.DB(ctx).Model(&ChunkDataAnalyze{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS)
	queryErr := query.
		Count(&count).
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Order("id").
		Find(&chunkDataAnalyzes).Error
	if queryErr != nil {
		return nil, 0, fmt.Errorf("list chunk data analyze failed: %v", queryErr)
	}
	return chunkDataAnalyzes, count, nil
}

func (rw FullDataMigrationReadWrite) ListChunkDataAnalyzePageWithStatus(ctx context.Context, taskId int, schemaNameS, tableNameS, status string, page int, pageSize int) ([]ChunkDataAnalyze, int64, error) {
	var chunkDataAnalyzes []ChunkDataAnalyze
	var count int64 = 0
	query := rw.DB(ctx).Model(&ChunkDataAnalyze{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? AND status = ?", taskId, schemaNameS, tableNameS, status)
	queryErr := query.
		Count(&count).
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Order("id").
		Find(&chunkDataAnalyzes).Error
	if queryErr != nil {
		return nil, 0, fmt.Errorf("list chunk data analyze failed: %v", queryErr)
	}
	return chunkDataAnalyzes, count, nil
}

func (rw FullDataMigrationReadWrite) BatchSaveChunkDataAnalyze(ctx context.Context, chunkDataAnalyzer []ChunkDataAnalyze, batchSize int) error {
	err := rw.DB(ctx).CreateInBatches(chunkDataAnalyzer, batchSize).Error
	if err != nil {
		log.Errorf("batch save chunk data analyze to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) BatchDeleteChunkDataAnalyze(ctx context.Context, taskId int) error {
	err := rw.DB(ctx).Unscoped().Delete(&ChunkDataAnalyze{}, "task_id = ?", taskId).Error
	if err != nil {
		log.Errorf("batch delete chunk data analyze to db failed. %s", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) CountChunkDataAnalyze(ctx context.Context, taskId int, schemaNameS, tableNameS string) (int64, error) {
	var count int64
	err := rw.DB(ctx).
		Model(&ChunkDataAnalyze{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("count chunk data analyze failed: %v", err)
	}
	return count, nil
}

func (rw FullDataMigrationReadWrite) CountChunkDataAnalyzeByStatus(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*ChunkDataAnalyzeStatusCounts, error) {
	type statusCount struct {
		Status string
		Count  int64
	}

	var results []statusCount
	err := rw.DB(ctx).
		Model(&ChunkDataAnalyze{}).
		Select("status, COUNT(*) as count").
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Group("status").
		Scan(&results).Error

	if err != nil {
		return nil, fmt.Errorf("count chunk data analyze by status failed: %v", err)
	}

	counts := &ChunkDataAnalyzeStatusCounts{}
	for _, result := range results {
		counts.TotalCount += result.Count
		switch result.Status {
		case "SUCCESS":
			counts.SuccessCount = result.Count
		case "FAILED":
			counts.FailedCount = result.Count
		case "RUNNING":
			counts.RunningCount = result.Count
		case "WAITING":
			counts.WaitingCount = result.Count
		}
	}

	return counts, nil
}

func (rw FullDataMigrationReadWrite) FetchChunkDataAnalyzeByIds(ctx context.Context, taskId int, schemaNameS, tableNameS string, ids []uint) ([]ChunkDataAnalyze, error) {
	var chunkDataAnalyzes []ChunkDataAnalyze
	queryErr := rw.DB(ctx).
		Model(&ChunkDataAnalyze{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Where("id in (?)", ids).
		Find(&chunkDataAnalyzes).Error
	if queryErr != nil {
		return nil, fmt.Errorf("fetch chunk data analyze failed: %v", queryErr)
	}
	return chunkDataAnalyzes, nil
}

func (rw FullDataMigrationReadWrite) GetTableMigrationDetailSummary(ctx context.Context, detailS *TableMigrationDetail) (*DetailSummary, error) {
	var dsMetas DetailSummary
	sql := `
SELECT COUNT(*) AS total_chunks, 
	SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) AS failed_chunks,
	SUM(CASE WHEN task_status = 'SUCCESS' THEN 1 ELSE 0 END) AS success_chunks 
FROM table_migration_details
WHERE task_id = ?
  AND schema_name_s = ?
  AND table_name_s = ?
  AND schema_name_t = ?
  AND table_name_t = ?
`

	if err := rw.DB(ctx).Raw(sql, detailS.TaskID, detailS.SchemaNameS, detailS.TableNameS, detailS.SchemaNameT, detailS.TableNameT).Scan(&dsMetas).Error; err != nil {
		return &dsMetas, fmt.Errorf("detail table migration detail [%s] record failed: %v", detailS.String(), err)
	}
	return &dsMetas, nil
}

func (rw FullDataMigrationReadWrite) UpdateChunkDataAnalyzeStatusByIds(ctx context.Context, ids []uint, status, errorMessage string) error {
	err := rw.DB(ctx).Model(&ChunkDataAnalyze{}).
		Where("id in (?)", ids).
		Updates(map[string]interface{}{
			"status":        status,
			"error_message": errorMessage,
			"updated_at":    time.Now(),
		}).Error
	if err != nil {
		return fmt.Errorf("update chunk data analyze status failed: %v", err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) SaveChunkDataAnalyzeSummary(ctx context.Context, chunkDataAnalyzeSummary *ChunkDataAnalyzeSummary) (*ChunkDataAnalyzeSummary, error) {
	err := rw.DB(ctx).Create(chunkDataAnalyzeSummary).Error
	if err != nil {
		log.Errorf("create chunk data analyze summary to db failed. %s", err)
		return chunkDataAnalyzeSummary, dbCommon.WrapDBError(err)
	}
	return chunkDataAnalyzeSummary, nil
}

func (rw FullDataMigrationReadWrite) UpdateChunkDataAnalyzeSummary(ctx context.Context, taskId int, schemaNameS, tableNameS string, status string) error {
	err := rw.DB(ctx).Model(&ChunkDataAnalyzeSummary{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Updates(map[string]interface{}{
			"status": status,
		}).Error
	if err != nil {
		return fmt.Errorf("update chunk data analyze summary failed: %v", err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) GetChunkDataAnalyzeSummary(ctx context.Context, taskId int, schemaNameS, tableNameS string) (*ChunkDataAnalyzeSummary, error) {
	chunkDataAnalyzeSummary := &ChunkDataAnalyzeSummary{}
	err := rw.DB(ctx).Model(&ChunkDataAnalyzeSummary{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).First(chunkDataAnalyzeSummary).Error
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return chunkDataAnalyzeSummary, nil
}

func (rw FullDataMigrationReadWrite) UpdateChunkDataAnalyzeProgress(ctx context.Context, taskId int, schemaNameS, tableNameS string, totalDetails, processedDetails int64) error {
	remainingDetails := totalDetails - processedDetails
	if remainingDetails < 0 {
		remainingDetails = 0
	}
	
	err := rw.DB(ctx).Model(&ChunkDataAnalyzeSummary{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Updates(map[string]interface{}{
			"total_details":     totalDetails,
			"processed_details": processedDetails,
			"remaining_details": remainingDetails,
		}).Error
	if err != nil {
		return fmt.Errorf("update chunk data analyze progress failed: %v", err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) UpdateChunkDataAnalyzeStatus(ctx context.Context, taskId int, schemaNameS, tableNameS, status, errorMessage string) error {
	err := rw.DB(ctx).Model(&ChunkDataAnalyze{}).
		Where("task_id = ? AND schema_name_s = ? AND table_name_s = ?", taskId, schemaNameS, tableNameS).
		Updates(map[string]interface{}{
			"status":        status,
			"error_message": errorMessage,
			"updated_at":    time.Now(),
		}).Error
	if err != nil {
		return fmt.Errorf("update chunk data analyze status failed: %v", err)
	}
	return nil
}

func (rw FullDataMigrationReadWrite) CountTableMigrationDetail(ctx context.Context, taskId int) (int64, error) {
	var count int64
	err := rw.DB(ctx).
		Model(&TableMigrationDetail{}).
		Where("task_id = ?", taskId).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("count table migration detail failed: %v", err)
	}
	return count, nil
}

func (rw FullDataMigrationReadWrite) CountTableMigrationSummary(ctx context.Context, taskId int) (int64, error) {
	var count int64
	err := rw.DB(ctx).
		Model(&TableMigrationSummary{}).
		Where("task_id = ?", taskId).
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("count table migration summary failed: %v", err)
	}
	return count, nil
}

func (rw FullDataMigrationReadWrite) QueryChunkDataAnalyzePageWithStatus(ctx context.Context, msg *message.QueryChunkDataReq) ([]ChunkDataAnalyze, int64, error) {
	var chunkDataAnalyzes []ChunkDataAnalyze
	var count int64 = 0
	query := rw.DB(ctx).Model(&ChunkDataAnalyze{}).Where("task_id = ? AND schema_name_s = ? AND table_name_s = ? ", msg.TaskID, msg.SchemaNameS, msg.TableNameS)
	if msg.Status != "" {
		query = query.Where("status = ?", msg.Status)
	}
	if msg.ErrorMessage != "" {
		query = query.Where("error_message like ?", "%"+msg.ErrorMessage+"%")
	}
	queryErr := query.
		Count(&count).
		Limit(msg.PageSize).
		Offset((msg.Page - 1) * msg.PageSize).
		Order("id").
		Find(&chunkDataAnalyzes).Error
	if queryErr != nil {
		return nil, 0, fmt.Errorf("query chunk data analyze failed: %v", queryErr)
	}
	return chunkDataAnalyzes, count, nil
}

func (rw FullDataMigrationReadWrite) GroupTableMigrationDetailImportStatusCount(ctx context.Context, taskID int, schemaName, tableName string) (*DetailImportStatusCount, error) {
	rsp := &DetailImportStatusCount{}
	querySQL := `
SELECT 
    COUNT(*) AS total_num,
    COUNT(if(import_status='waiting',true,null)) AS waiting_num,
    COUNT(if(import_status='running',true,null)) AS running_num,
    COUNT(if(import_status='success',true,null)) AS success_num,
    COUNT(if(import_status='failed',true,null)) AS failed_num,
    COUNT(if(import_status='ignore',true,null)) AS ignore_num
FROM table_migration_details WHERE task_id = ? AND schema_name_s = ? AND table_name_s = ?;`

	err := rw.DB(ctx).Raw(querySQL, taskID, schemaName, tableName).Scan(rsp).Error
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func (rw FullDataMigrationReadWrite) UpdateTableMigrationDetailImportStatusByIDs(ctx context.Context, taskId int, detailIds []uint, importStatus, importError string) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("task_id = ? AND id in (?)", taskId, detailIds).
		Updates(map[string]interface{}{
			"import_status": importStatus,
			"import_error":  importError,
		}).Error
	if err != nil {
		return fmt.Errorf("update table migration import status by table failed: %v", err)
	}
	return nil
}

/*
*
schemaNameT may empty
tableNameT may empty
csvFilter may empty, if it's not empty, should use like to filter data
*/
func (rw FullDataMigrationReadWrite) GetTableMigrationDetailsByCSVCondition(ctx context.Context, taskId int, schemaNameT, tableNameT string, csvFilter string) ([]*TableMigrationDetail, error) {
	var objDetails []*TableMigrationDetail
	query := rw.DB(ctx).Model(&TableMigrationDetail{}).Where("task_id = ?", taskId)
	if schemaNameT != "" {
		query = query.Where("schema_name_t = ?", schemaNameT)
	}
	if tableNameT != "" {
		query = query.Where("table_name_t = ?", tableNameT)
	}
	if csvFilter != "" {
		query = query.Where("csv_file like ?", "%"+csvFilter+"%")
	}
	err := query.Find(&objDetails).Error
	if err != nil {
		return nil, fmt.Errorf("get table migration detail by csv condition failed: %v", err)
	}
	return objDetails, nil
}

func (rw FullDataMigrationReadWrite) UpdateTableMigrationDetailImportStatusByIDsV2(ctx context.Context, taskId int, detailIds []uint, importStatus string) error {
	err := rw.DB(ctx).Model(&TableMigrationDetail{}).
		Where("task_id = ? AND id in (?)", taskId, detailIds).
		Updates(map[string]interface{}{
			"import_status": importStatus,
		}).Error
	if err != nil {
		return fmt.Errorf("update table migration import status by table failed: %v", err)
	}
	return nil
}
