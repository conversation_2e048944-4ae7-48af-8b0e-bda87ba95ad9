package models

import (
	"context"
	"database/sql"
	"fmt"
	goLog "log"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	transferdbconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/models/assessment"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/increment"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/server/models/statistics"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/models/template"
	"gitee.com/pingcap_enterprise/tms/server/models/users"
	utildatabase "gitee.com/pingcap_enterprise/tms/util/database"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var defaultDb *database
var globalDBRetryConfig *DBRetryConfig

type database struct {
	base                           *gorm.DB
	datasourceReaderWriter         datasource.ReaderWriter
	templateReaderWriter           template.ReaderWriter
	dataCompareReaderWriter        datacompare.ReaderWriter
	structureMigrationReaderWriter migration.StructureReaderWriter
	fullDataMigrationReaderWriter  migration.FullDataReaderWriter
	channelReaderWriter            channel.ReaderWriter
	assessReaderWriter             assessment.AssessReaderWriter
	taskReaderWriter               task.TaskReaderWriter
	progressLogReaderWriter        common.ProgressLogDetailReaderWriter
	sqlAnalyzerReaderWrite         sqlanalyzer.SqlAnalyzerReaderWriter
	statisticsReaderWriter         statistics.StatisticsReaderWriter
	incrementReaderWriter          increment.ReaderWriter
	objectParserReaderWriter       objectparser.ReaderWriter
	systemInfoReaderWriter         common.ReaderWriter
	userReaderWriter               users.ReaderWriter
}

// DBConfig is database configuration.
type DBConfig struct {
	Host string `toml:"host" json:"-"`

	Port int `toml:"port" json:"-"`

	User string `toml:"user" json:"-"`

	Password string `toml:"password" json:"-"`

	Schema string `toml:"schema" json:"-"`

	DBLogLevel string `toml:"db-log-level" json:"db-log-level"`

	DBLogFile string `toml:"db-log-file" json:"db-log-file"`

	//for sqlite
	DBFileDir string `toml:"db-file-dir" json:"db-file-dir"`

	//for auto migrate
	DisableAutoMigrate bool `toml:"disable-auto-migrate" json:"disable-auto-migrate"`

	// Database retry configuration
	RetryConfig *DBRetryConfig `toml:"retry" json:"retry"`
}

// DBRetryConfig defines database operation retry configuration
type DBRetryConfig struct {
	Enabled       bool    `toml:"enabled" json:"enabled"`
	MaxRetries    int     `toml:"max_retries" json:"max_retries"`
	InitialDelay  string  `toml:"initial_delay" json:"initial_delay"`
	MaxDelay      string  `toml:"max_delay" json:"max_delay"`
	BackoffFactor float64 `toml:"backoff_factor" json:"backoff_factor"`
}

var DefaultDBConfig = &DBConfig{
	Host:       "127.0.0.1",
	Port:       3306,
	User:       "tims",
	Password:   "tims",
	DBLogLevel: "info",
	DBLogFile:  constants.LogFileSql,
	RetryConfig: &DBRetryConfig{
		Enabled:       false, // Default disabled for backward compatibility
		MaxRetries:    3,
		InitialDelay:  "100ms",
		MaxDelay:      "5s",
		BackoffFactor: 2.0,
	},
}

// SetGlobalDBRetryConfig sets the global database retry configuration
func SetGlobalDBRetryConfig(retryConfig *DBRetryConfig) {
	globalDBRetryConfig = retryConfig
}

// CreateDatabase create database
func CreateDatabase(cfg *DBConfig) error {
	// Set global retry config from database config
	if cfg != nil {
		SetGlobalDBRetryConfig(cfg.RetryConfig)
	}

	// Build MySQL configuration for unified connection
	dbConfig := utildatabase.MySQLConfig{
		User:     cfg.User,
		Password: cfg.Password,
		Host:     cfg.Host,
		Port:     cfg.Port,
		Database: cfg.Schema,
		Charset:  "utf8mb4",
		Timezone: time.Local,
	}

	// Prepare connection options, including retry configuration
	opts := []utildatabase.Option{
		utildatabase.WithPingTimeout(5),
	}

	// Add retry configuration if provided and enabled
	if cfg.RetryConfig != nil && cfg.RetryConfig.IsEnabled() {
		utilRetryConfig, convErr := cfg.RetryConfig.ToRetryConfig()
		if convErr != nil {
			log.Errorf("failed to convert retry config: %v", convErr)
			return fmt.Errorf("failed to convert retry config: %v", convErr)
		}
		opts = append(opts, utildatabase.WithRetry(utilRetryConfig))
	}

	// Create unified MySQL connection
	connector := utildatabase.NewMySQLConnector(dbConfig)
	mysqlDB, err := utildatabase.OpenMySQL(connector, opts...)
	if err != nil {
		log.Errorf("open mysql failed, database error: %v", err)
		return err
	}

	// Create GORM instance using the unified connection
	db, err := gorm.Open(mysql.New(mysql.Config{
		Conn: mysqlDB, // 使用统一创建的连接
	}), configGorm(cfg.DBLogFile, cfg.DBLogLevel))

	if err != nil || db.Error != nil {
		log.Errorf("open mysql failed, database error: %v, meta database error: %v", err, db.Error)
		return err
	}
	log.Info("create mysql successfully.")

	defaultDb = &database{base: db}

	defaultDb.initReaderWriters()
	if cfg.DisableAutoMigrate {
		log.Infof("disable auto migrate table.")
		return nil
	}

	log.Info("start migrate table.")
	err = defaultDb.migrateTables()
	if err != nil {
		log.Errorf("migrate tables failed. database error: %v", err)
		return err
	}
	log.Info("finish migrate table.")
	return nil
}

func OpenMysql(user, password, host string, port int, schema string) (*sql.DB, error) {
	// Build MySQL configuration
	config := utildatabase.MySQLConfig{
		User:     user,
		Password: password,
		Host:     host,
		Port:     port,
		Database: schema,
		Charset:  "utf8mb4",
		Timezone: time.Local,
	}

	// Prepare base options
	opts := []utildatabase.Option{
		utildatabase.WithPingTimeout(5),
	}

	// Add retry configuration if provided and enabled
	if globalDBRetryConfig != nil && globalDBRetryConfig.IsEnabled() {
		utilRetryConfig, convErr := globalDBRetryConfig.ToRetryConfig()
		if convErr != nil {
			log.Errorf("failed to convert retry config: %v", convErr)
			return nil, errors.NewErrorf(errors.TIMS_OPEN_DATABASE_FAILED, "open mysql db failed.err:%v", convErr)
		}
		opts = append(opts, utildatabase.WithRetry(utilRetryConfig))
	}

	// Create MySQL connector and open connection
	connector := utildatabase.NewMySQLConnector(config)
	db, err := utildatabase.OpenMySQL(connector, opts...)
	if err != nil {
		log.Errorf("open mysql failed, database error: %v", err)
		return nil, errors.NewErrorf(errors.TIMS_OPEN_DATABASE_FAILED, "open mysql db failed.err:%v", err)
	}

	return db, nil
}

// OracleConfig holds configuration parameters for Oracle database connection
type OracleConfig struct {
	User          string
	Password      string
	Host          string
	Port          int
	ServiceName   string
	ConnectString string
	Timezone      *time.Location
}

// Validate checks if the configuration is valid
func (c *OracleConfig) Validate() error {
	if c.User == "" {
		return errors.NewError(errors.TIMS_INVALID_CONFIG, "username is required")
	}
	if c.Password == "" {
		return errors.NewError(errors.TIMS_INVALID_CONFIG, "password is required")
	}
	if c.ConnectString == "" {
		if c.ServiceName == "" {
			return errors.NewError(errors.TIMS_SERVICE_NAME_NOT_EXIST, "service name is required when connect string is empty")
		}
		if c.Host == "" {
			return errors.NewError(errors.TIMS_INVALID_CONFIG, "host is required when connect string is empty")
		}
		if c.Port <= 0 {
			return errors.NewError(errors.TIMS_INVALID_CONFIG, "valid port is required when connect string is empty")
		}
	}
	return nil
}

func BuildOracleConfigFromDatasource(d *datasource.Datasource, connString string) OracleConfig {
	return OracleConfig{
		User:          d.UserName,
		Password:      d.PasswordValue,
		Host:          d.HostIp,
		Port:          d.HostPort,
		ServiceName:   datasourcepkg.GetServiceName(d),
		ConnectString: connString,
	}
}

func BuildOracleConfigFromTransferDBConf(d *transferdbconfig.OracleConfig, ds *datasource.Datasource, connString string) OracleConfig {
	return OracleConfig{
		User:          d.Username,
		Password:      d.Password,
		Host:          d.Host,
		Port:          d.Port,
		ServiceName:   datasourcepkg.GetServiceName(ds),
		ConnectString: connString,
	}
}

func BuildOracleConfigFromSQLAnalyzeConf(d *structs.DBConfigOracle, connString string) OracleConfig {
	return OracleConfig{
		User:          d.User,
		Password:      d.Password,
		Host:          d.Host,
		Port:          d.Port,
		ServiceName:   d.Sid,
		ConnectString: connString,
	}
}

// OpenOracle establishes a connection to an Oracle database with the provided configuration
// Retry configuration is automatically read from global config
func OpenOracle(config OracleConfig) (*sql.DB, error) {
	return openOracleWithRetryConfig(config, globalDBRetryConfig)
}

// openOracleWithRetryConfig is the internal implementation for opening Oracle connections
func openOracleWithRetryConfig(config OracleConfig, retryConfig *DBRetryConfig) (*sql.DB, error) {
	if err := config.Validate(); err != nil {
		return nil, err
	}

	// Set default timezone if not provided
	loc := config.Timezone
	if loc == nil {
		var err error
		loc, err = time.LoadLocation("Local")
		if err != nil {
			return nil, errors.NewErrorf(errors.TIMS_TIMEZONE_ERROR, "failed to load local timezone: %v", err)
		}
	}

	// Build connection string
	connectString := config.ConnectString
	if connectString == "" {
		connectString = fmt.Sprintf(
			"oracle://@%s:%d/%s?connect_timeout=2&standaloneConnection=1",
			config.Host,
			config.Port,
			config.ServiceName,
		)
	}

	// Parse DSN
	oraDSN, err := utildatabase.ParseGoDrorDSN(connectString)
	if err != nil {
		return nil, errors.NewErrorf(errors.TIMS_PARSE_DSN_FAILED, "failed to parse oracle DSN: %v", err)
	}

	// Configure DSN
	oraDSN.Timezone = loc
	oraDSN.Username = config.User
	oraDSN.Password = utildatabase.NewGoDrorPassword(config.Password)

	// Prepare base options
	opts := []utildatabase.Option{
		utildatabase.WithConnMaxLifetime(0),
		utildatabase.WithMaxIdleConns(0),
		utildatabase.WithMaxOpenConns(0),
		utildatabase.WithPingTimeout(5),
		utildatabase.WithSetModuleName(constants.SET_TMS_MODULE_SQL),
	}

	// Add retry configuration if provided and enabled
	if retryConfig != nil && retryConfig.IsEnabled() {
		utilRetryConfig, convErr := retryConfig.ToRetryConfig()
		if convErr != nil {
			return nil, errors.NewErrorf(errors.TMS_SETUP_RETRY_FAILED, "failed to convert retry config: %v", convErr)
		}
		opts = append(opts, utildatabase.WithRetry(utilRetryConfig))
	}

	// Create and configure database connection
	return utildatabase.OpenOracle(utildatabase.NewGoDrorConnector(oraDSN), opts...)
}

// PingOracle open and ping an oracle connection
func PingOracle(user string, password string, host string, port int, serviceName, connectString string, isASM bool) (*sql.DB, error) {
	loc, locateErr := time.LoadLocation("Local")
	if locateErr != nil {
		return nil, locateErr
	}
	pConnectString := ""
	if connectString == "" {
		if serviceName == "" {
			return nil, errors.NewError(errors.TIMS_SERVICE_NAME_NOT_EXIST, "create oracle db,service-name should not be empty")
		}
		pConnectString = fmt.Sprintf("oracle://@%s:%d/%s?connect_timeout=2&standaloneConnection=1", host, port, serviceName)
	} else {
		pConnectString = connectString
	}

	if isASM {
		if strings.Contains(pConnectString, "sysasm=0") {
			pConnectString = strings.ReplaceAll(pConnectString, "sysasm=0", "sysasm=1")
		} else {
			pConnectString = pConnectString + "&sysasm=1"
		}
	}

	log.Info("ping oracle db connect,connect string: ", pConnectString)
	oraDSN, parseErr := utildatabase.ParseGoDrorDSN(pConnectString)
	if parseErr != nil {
		return nil, errors.NewErrorf(errors.TIMS_PARSE_DSN_FAILED, "parse oracle dsn failed.err:%v", parseErr)
	}
	oraDSN.Timezone = loc
	oraDSN.Username, oraDSN.Password = user, utildatabase.NewGoDrorPassword(password)

	return utildatabase.OpenOracle(utildatabase.NewGoDrorConnector(oraDSN),
		utildatabase.WithConnMaxLifetime(0),
		utildatabase.WithMaxIdleConns(0),
		utildatabase.WithMaxOpenConns(0),
		utildatabase.WithSetModuleName(constants.SET_TMS_MODULE_SQL),
		utildatabase.WithPing(),
	)
}

func configGorm(dbLogFile, dbLogLevel string) *gorm.Config {
	sqlFile := dbLogFile
	if strings.TrimSpace(dbLogFile) == "" {
		sqlFile = constants.LogFileSql
	}
	logLevel := logger.Info
	if strings.TrimSpace(dbLogLevel) == "" || strings.TrimSpace(dbLogLevel) == "warn" {
		logLevel = logger.Warn
	} else if strings.TrimSpace(dbLogLevel) == "silent" {
		logLevel = logger.Silent
	} else if strings.TrimSpace(dbLogLevel) == "error" {
		logLevel = logger.Error
	}
	newLogger := logger.New(
		goLog.New(log.GetRootLogger().CreateFileWriter(sqlFile), "\r\n", goLog.LstdFlags),
		logger.Config{
			SlowThreshold:             100 * time.Millisecond,
			LogLevel:                  logLevel,
			IgnoreRecordNotFoundError: false,
			Colorful:                  false,
		},
	)
	return &gorm.Config{
		Logger:                   newLogger,
		DisableNestedTransaction: true,
	}
}

func (p *database) initReaderWriters() {
	defaultDb.datasourceReaderWriter = datasource.NewDatasourceReadWrite(p.base)
	defaultDb.templateReaderWriter = template.NewTemplateReadWrite(p.base)
	defaultDb.dataCompareReaderWriter = datacompare.NewDataCompareReadWrite(p.base)
	defaultDb.structureMigrationReaderWriter = migration.NewStructureMigrationReadWrite(p.base)
	defaultDb.fullDataMigrationReaderWriter = migration.NewFullDataMigrationReadWrite(p.base)
	defaultDb.channelReaderWriter = channel.NewChannelReadWrite(p.base)
	defaultDb.assessReaderWriter = assessment.NewAssessReadWrite(p.base)
	defaultDb.taskReaderWriter = task.NewTaskReadWrite(p.base)
	defaultDb.progressLogReaderWriter = common.NewProgressLogReadWrite(p.base)
	defaultDb.sqlAnalyzerReaderWrite = sqlanalyzer.NewSqlAnalyzerReadWrite(p.base)
	defaultDb.statisticsReaderWriter = statistics.NewRunStatsReadWrite(p.base)
	defaultDb.incrementReaderWriter = increment.NewIncrementReadWrite(p.base)
	defaultDb.objectParserReaderWriter = objectparser.NewObjectParserMigrationReadWrite(p.base)
	defaultDb.systemInfoReaderWriter = common.NewSystemInfoReadWrite(p.base)
	defaultDb.userReaderWriter = users.NewUserReaderWriter(p.base)

}

func (p *database) migrateStream(models ...interface{}) (err error) {
	for _, model := range models {
		err = p.base.Set("gorm:table_options", "CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci").AutoMigrate(model)
		if err != nil {
			return fmt.Errorf("migrate table %T failed: %v", model, err)
		}
	}
	return nil
}

func (p *database) migrateTables() (err error) {
	return p.migrateStream(
		new(datasource.Datasource),
		new(datasource.Datasource),
		new(datasource.OpMachineConf),
		new(channel.ChannelInformation),
		new(channel.ChannelSchemaObject),
		new(channel.ChannelSchema),
		new(channel.PrecheckInfo),
		new(channel.ChannelSchemaTable),
		new(channel.TableColumnCustomMapRule),
		new(channel.TableColumnCustomSummary),
		new(task.Task),
		new(task.ServerInfo),
		new(task.TaskParamConfig),
		new(task.TaskparamTemplateConfig),
		new(task.ParamTemplateDetail),
		new(task.TaskTableConfig),
		new(task.TaskLogDetail),
		new(task.TaskSubParamConfig),
		new(assessment.ObjectAssessDetail),
		new(template.TmplateInformation),
		new(template.TabcolMapRule),
		new(template.ColdefaultMapRule),
		new(template.TabcolCustMap),
		new(template.ObjmapRule),
		new(template.TmplateRule),
		new(template.SqlmapRule),
		new(common.ProgressLogDetail),
		new(migration.TableResultDetail),
		new(migration.TableCompatibleDetail),
		new(migration.TableMigrationSummary),
		new(migration.TableMigrationDetail),
		new(migration.LightningProgress),
		new(migration.CSVStage),
		new(migration.ChunkDataAnalyze),
		new(migration.ChunkDataAnalyzeSummary),
		new(datacompare.DataCompareLog),
		new(datacompare.Summary),
		new(datacompare.Chunk),
		new(datacompare.EnvDeployTask),
		new(sqlanalyzer.SqlResultSummary),
		new(sqlanalyzer.O2tSqlsetExecInfo),
		new(sqlanalyzer.SqlStmtExecResult),
		new(sqlanalyzer.SqlStmtExecSummary),
		new(sqlanalyzer.SqlStmtExecResultHistory),
		new(sqlanalyzer.EnvDeployTask),
		new(sqlanalyzer.SqlStmtUserOperationV2),
		new(sqlanalyzer.SqlStmtUserOperationV2History),
		new(sqlanalyzer.SqlStmtUserOperationV2Key),
		new(statistics.TaskStatistics),
		new(statistics.TableStatisticsSummary),
		new(assessment.HtmlTemplateDetail),
		new(assessment.OraDbaObject),
		new(assessment.OraDbaConstraint),
		new(assessment.OraDbaIndex),
		new(assessment.OraDbaSequence),
		new(assessment.OraDbaTabColumns),
		new(assessment.OraDbaTabColumnsDefault),
		new(assessment.OraAllPartTables),
		new(assessment.OraDbaTables),
		new(assessment.OraAllObjects),
		new(assessment.OraLobs),
		new(assessment.OraTableTypeCompatibles),
		new(assessment.OraDbaViews),
		new(assessment.HtmlRptCfg),
		new(assessment.OraSubPartitioning),
		new(common.SystemInfo),
		new(common.FeatureLicense),
		new(increment.IncrementTask),
		new(increment.IncrementInstallParam),
		new(objectparser.OracleIncompatibleFeature),
		new(objectparser.OracleTaskIncompatibleFeature),
		new(objectparser.OracleObjectDefinition),
		new(objectparser.OracleDependency),
		new(objectparser.OracleToJavaResult),
		new(objectparser.OracleToJavaFile),
		new(objectparser.OracleToJavaHistoryResult),
		new(objectparser.OracleToJavaHistoryFile),
		new(objectparser.OracleToJavaSummary),
		new(objectparser.OracleToJavaLog),
		new(objectparser.OracleObjectDefinitionAnalyzeDetail),
		new(objectparser.OracleObjectDefinitionAnalyzeSummary),
		new(objectparser.OracleObjectDefinitionIncompatibleFeature),
		new(objectparser.OracleObjectTransformationPrompt),
		new(objectparser.OracleObjectTaskObjectPromptRelation),
		new(objectparser.ObjectParserCfg),
		new(datasource.OracleSegmentSizeEntity),
		new(users.User),
		new(channel.ChannelDatasource),
	)
}

// Transaction
// @Description: Transaction for service
// @Parameter ctx
// @Parameter fc
// @return error
func Transaction(ctx context.Context, fc func(transactionCtx context.Context) error) (err error) {
	if defaultDb.base == nil {
		return fc(ctx)
	}
	db := defaultDb.base.WithContext(ctx)

	return db.Transaction(func(tx *gorm.DB) error {
		return fc(common.CtxWithTransaction(ctx, tx))
	})
}

func GetDatasourceReaderWriter() datasource.ReaderWriter {
	return defaultDb.datasourceReaderWriter
}

func GetUserReaderWriter() users.ReaderWriter {
	return defaultDb.userReaderWriter
}

func GetTemplateReaderWriter() template.ReaderWriter {
	return defaultDb.templateReaderWriter
}

func GetDataCompareReaderWriter() datacompare.ReaderWriter {
	return defaultDb.dataCompareReaderWriter
}

func GetStructureMigrationReaderWriter() migration.StructureReaderWriter {
	return defaultDb.structureMigrationReaderWriter
}

func GetFullDataMigrationReaderWriter() migration.FullDataReaderWriter {
	return defaultDb.fullDataMigrationReaderWriter
}

func GetChannelReaderWriter() channel.ReaderWriter {
	return defaultDb.channelReaderWriter
}

func GetAssessReaderWriter() assessment.AssessReaderWriter {
	return defaultDb.assessReaderWriter
}

func GetTaskReaderWriter() task.TaskReaderWriter {
	return defaultDb.taskReaderWriter
}

func GetProgressLogReaderWriter() common.ProgressLogDetailReaderWriter {
	return defaultDb.progressLogReaderWriter
}

func GetSystemInfoReaderWriter() common.ReaderWriter {
	return defaultDb.systemInfoReaderWriter
}

func GetSqlAnalyzerReaderWriter() sqlanalyzer.SqlAnalyzerReaderWriter {
	return defaultDb.sqlAnalyzerReaderWrite
}

func GetStatisticsReaderWriter() statistics.StatisticsReaderWriter {
	return defaultDb.statisticsReaderWriter
}

func GetIncrementReaderWriter() increment.ReaderWriter {
	return defaultDb.incrementReaderWriter
}

func GetObjectParserWriter() objectparser.ReaderWriter {
	return defaultDb.objectParserReaderWriter
}

// ToRetryConfig converts DBRetryConfig to util database RetryConfig
func (drc *DBRetryConfig) ToRetryConfig() (*utildatabase.RetryConfig, error) {
	if drc == nil {
		return nil, nil
	}

	initialDelay, err := time.ParseDuration(drc.InitialDelay)
	if err != nil {
		return nil, fmt.Errorf("failed to parse initial_delay: %v", err)
	}

	maxDelay, err := time.ParseDuration(drc.MaxDelay)
	if err != nil {
		return nil, fmt.Errorf("failed to parse max_delay: %v", err)
	}

	return &utildatabase.RetryConfig{
		MaxRetries:    drc.MaxRetries,
		InitialDelay:  initialDelay,
		MaxDelay:      maxDelay,
		BackoffFactor: drc.BackoffFactor,
	}, nil
}

// IsEnabled returns whether retry is enabled
func (drc *DBRetryConfig) IsEnabled() bool {
	return drc != nil && drc.Enabled
}

// NewDBRetryConfigFromMap creates a DBRetryConfig from a map (to avoid circular imports)
func NewDBRetryConfigFromMap(data interface{}) *DBRetryConfig {
	if data == nil {
		return nil
	}

	m, ok := data.(map[string]interface{})
	if !ok {
		return nil
	}

	config := &DBRetryConfig{}

	if enabled, ok := m["enabled"].(bool); ok {
		config.Enabled = enabled
	}

	if maxRetries, ok := m["max_retries"].(int); ok {
		config.MaxRetries = maxRetries
	}

	if initialDelay, ok := m["initial_delay"].(string); ok {
		config.InitialDelay = initialDelay
	}

	if maxDelay, ok := m["max_delay"].(string); ok {
		config.MaxDelay = maxDelay
	}

	if backoffFactor, ok := m["backoff_factor"].(float64); ok {
		config.BackoffFactor = backoffFactor
	}

	return config
}

// GetDB 返回全局 *gorm.DB
func GetDB() *gorm.DB {
	if defaultDb == nil {
		return nil
	}
	return defaultDb.base
}
