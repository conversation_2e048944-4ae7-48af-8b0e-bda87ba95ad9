package users

import (
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

type User struct {
	UserID       int    `gorm:"primaryKey;autoIncrement;comment:主键ID"`
	Username     string `gorm:"type:varchar(64);not null;uniqueIndex:idx_users_username"`
	PasswordHash string `gorm:"type:varchar(128);not null"`
	Salt         string `gorm:"type:varchar(32);not null"`
	Email        string `gorm:"type:varchar(128);not null;uniqueIndex:idx_users_email"`
	LastLogin    *time.Time
	*common.Entity
}

func (User) TableName() string {
	return "Users"
}
