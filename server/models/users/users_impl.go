package users

import (
	"context"

	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gorm.io/gorm"
)

type UserReaderWriter struct {
	dbCommon.GormDB
}

func NewUserReaderWriter(db *gorm.DB) *UserReaderWriter {
	m := &UserReaderWriter{
		dbCommon.WrapDB(db),
	}
	return m
}

func (u *UserReaderWriter) CreateUser(ctx context.Context, user *User) error {
	return u.DB(ctx).Create(user).Error
}

func (u *UserReaderWriter) UpdateUser(ctx context.Context, user *User) error {
	return u.DB(ctx).Save(user).Error
}

func (u *UserReaderWriter) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	var user User
	if err := u.DB(ctx).Where("username = ?", username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (u *UserReaderWriter) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	var user User
	if err := u.DB(ctx).Where("email = ?", email).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}
