/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package common

import "encoding/json"

type ProgressLogDetail struct {
	ProgressID  int    `gorm:"primarykey"`
	ChannelID   int    `gorm:"not null;comment:channel id"`
	TaskID      int    `gorm:"not null;index:idx_task_id;comment:task id"`
	SchemaNameS string `gorm:"type:varchar(30);comment:source schema name"`
	TableNameS  string `gorm:"type:varchar(100);comment:source table name"`
	Detail      string `gorm:"type:text;comment:detail"`
	LogLevel    string `gorm:"type:varchar(10);comment:log level"`
	*Entity
}

func (e *ProgressLogDetail) String() string {
	marshal, _ := json.Marshal(e)
	return string(marshal)
}
