/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package common

import "time"

type SystemInfo struct {
	RecordId  int    `gorm:"primarykey"`
	MachineId string `gorm:"type:varchar(100);comment:machine id"`
	*Entity
}

// FeatureLicense represents the structure of the feature_licenses table.
type FeatureLicense struct {
	ID                    int       `gorm:"primarykey"`
	FeatureCode           string    `gorm:"type:varchar(100);comment:feature code"`
	FeatureName           string    `gorm:"type:varchar(100);comment:feature name"`
	MaxUsageCount         uint      `gorm:"comment:max usage count"`
	CurrentUsageCount     uint      `gorm:"comment:current usage count"`
	LicenseKey            string    `gorm:"type:varchar(900);comment:license key"`
	VerificationSignature string    `gorm:"type:varchar(100);comment:verification signature"`
	Version               int64     `gorm:"comment:optimistic locking version;default:1"`
	LastModified          time.Time `gorm:"comment:last modification timestamp"`
	ModifiedBy            string    `gorm:"type:varchar(100);comment:machine id that made the modification"`
	*Entity
}
