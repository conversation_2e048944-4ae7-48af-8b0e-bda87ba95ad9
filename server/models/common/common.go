package common

import (
	"context"
	"time"

	"golang.org/x/crypto/bcrypt"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gorm.io/gorm"
)

type Entity struct {
	Comment   string    `gorm:"type:varchar(1000);comment:comment content"`
	CreatedAt time.Time `gorm:"<-:create"`
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

type GormDB struct {
	db *gorm.DB
}

func WrapDB(db *gorm.DB) GormDB {
	return GormDB{db: db}
}

type ctxTransactionKeyStruct struct{}

var ctxTransactionKey = ctxTransactionKeyStruct{}

func CtxWithTransaction(ctx context.Context, db *gorm.DB) context.Context {
	return context.WithValue(ctx, ctxTransactionKey, db)
}

func (m *GormDB) DB(ctx context.Context) *gorm.DB {
	iface := ctx.Value(ctxTransactionKey)

	if iface != nil {
		tx, ok := iface.(*gorm.DB)
		if !ok {
			return nil
		}

		return tx
	}

	return m.db.WithContext(ctx)
}

// WrapDBError
// @Description:
// @Parameter err
// @return error is nil or TiMSError
func WrapDBError(err error) error {
	if err == nil {
		return nil
	}

	switch err.(type) {
	case errors.MSError:
		return err
	default:
		return errors.NewError(errors.TIMS_SQL_ERROR, err.Error())
	}
}

func FinalHash(salt string, password string) ([]byte, error) {
	if password == "" {
		return nil, errors.NewError(errors.TIMS_PARAMETER_INVALID, "password cannot be empty")
	}
	s := salt + password
	finalSalt, err := bcrypt.GenerateFromPassword([]byte(s), bcrypt.DefaultCost)

	return finalSalt, err
}
