/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package common

import (
	"context"
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type SystemInfoReaderWrite struct {
	GormDB
}

func NewSystemInfoReadWrite(db *gorm.DB) *SystemInfoReaderWrite {
	m := &SystemInfoReaderWrite{
		WrapDB(db),
	}
	return m
}

func (rw SystemInfoReaderWrite) CreateSystemInfo(ctx context.Context, systemInfo *SystemInfo) (*SystemInfo, error) {
	err := rw.DB(ctx).Create(systemInfo).Error
	if err != nil {
		log.Errorf("create systemInfo to db failed. %s", err)
		return systemInfo, WrapDBError(err)
	}
	return systemInfo, nil
}

func (rw SystemInfoReaderWrite) UpdateSystemInfo(ctx context.Context, systemInfo *SystemInfo) (*SystemInfo, error) {
	err := rw.DB(ctx).Save(systemInfo).Error
	if err != nil {
		log.Errorf("update systemInfo info to db failed. %s", err)
		return nil, WrapDBError(err)
	}
	return systemInfo, nil
}

func (rw SystemInfoReaderWrite) GetSystemInfo(ctx context.Context, machineId string) (*SystemInfo, error) {
	sysinfo := &SystemInfo{}
	err := rw.DB(ctx).First(sysinfo, "machine_id = ?", machineId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query systemInfo %d not found", machineId)
	} else if err != nil {
		log.Errorf("GetSystemInfo failed. machine_id is %d, %v", machineId, err)
		return nil, WrapDBError(err)
	}
	return sysinfo, nil
}

// GetFeatureLicense retrieves a feature license by feature code and license key using GORM.
func (rw SystemInfoReaderWrite) GetFeatureLicense(ctx context.Context, featureCode, licenseKey string) (*FeatureLicense, error) {
	var fl FeatureLicense
	result := rw.DB(ctx).Where("feature_code = ? AND license_key = ?", featureCode, licenseKey).First(&fl)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil // Not found
		}
		return nil, fmt.Errorf("failed to query feature license: %w", result.Error)
	}
	return &fl, nil
}

// InsertFeatureLicense inserts a new feature license record.
func (rw SystemInfoReaderWrite) InsertFeatureLicense(ctx context.Context, fl *FeatureLicense) (*FeatureLicense, error) {
	// Set initial version and timestamp if not already set
	if fl.Version == 0 {
		fl.Version = 1
	}
	if fl.LastModified.IsZero() {
		fl.LastModified = time.Now()
	}

	err := rw.DB(ctx).Create(fl).Error
	if err != nil {
		return nil, fmt.Errorf("failed to insert feature license: %w", err)
	}
	return fl, nil
}

// UpdateFeatureLicense updates the current usage count and verification signature.
func (rw SystemInfoReaderWrite) UpdateFeatureLicense(ctx context.Context, fl *FeatureLicense) error {
	query := "UPDATE feature_licenses SET current_usage_count = ?, verification_signature = ? WHERE id = ?"
	err := rw.DB(ctx).Exec(query, fl.CurrentUsageCount, fl.VerificationSignature, fl.ID).Error
	if err != nil {
		return fmt.Errorf("failed to update feature license: %w", err)
	}
	return nil
}

// ListFeatureLicense list feature license
func (rw SystemInfoReaderWrite) ListFeatureLicenses(ctx context.Context, licenseKey string) ([]*FeatureLicense, error) {
	var featureLicenses []*FeatureLicense
	err := rw.DB(ctx).Where("license_key = ?", licenseKey).Find(&featureLicenses).Error
	if err != nil {
		log.Errorf("list feature licenses failed. %s", err)
		return nil, WrapDBError(err)
	}
	return featureLicenses, nil
}

// GetFeatureLicenseWithVersion retrieves a feature license with version information for optimistic locking.
func (rw SystemInfoReaderWrite) GetFeatureLicenseWithVersion(ctx context.Context, featureCode, licenseKey string) (*FeatureLicense, error) {
	var fl FeatureLicense
	result := rw.DB(ctx).Where("feature_code = ? AND license_key = ?", featureCode, licenseKey).First(&fl)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, nil // Not found
		}
		return nil, fmt.Errorf("failed to query feature license with version: %w", result.Error)
	}
	return &fl, nil
}

// UpdateFeatureLicenseWithVersion updates a feature license using optimistic locking with version check.
// Returns ErrVersionConflict if the version has changed since the record was read.
func (rw SystemInfoReaderWrite) UpdateFeatureLicenseWithVersion(ctx context.Context, fl *FeatureLicense) error {
	// Update with version check: increment version and update fields only if current version matches
	result := rw.DB(ctx).Model(&FeatureLicense{}).
		Where("id = ? AND version = ?", fl.ID, fl.Version).
		Updates(map[string]interface{}{
			"current_usage_count":    fl.CurrentUsageCount,
			"verification_signature": fl.VerificationSignature,
			"version":                fl.Version + 1,
			"last_modified":          time.Now(),
			"modified_by":            fl.ModifiedBy,
			"updated_at":             time.Now(),
		})

	if result.Error != nil {
		return fmt.Errorf("failed to update feature license with version: %w", result.Error)
	}

	// Check if any rows were affected - if not, it means version conflict
	if result.RowsAffected == 0 {
		log.Warnf("Version conflict detected for feature license ID %d, version %d", fl.ID, fl.Version)
		return ErrVersionConflict
	}

	// Update the version in the passed struct for consistency
	fl.Version = fl.Version + 1
	fl.LastModified = time.Now()

	return nil
}
