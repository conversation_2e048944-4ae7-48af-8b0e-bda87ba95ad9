// Code generated by MockGen. DO NOT EDIT.
// Source: ./common/progresslog_readwriter.go

// Package mockcommonprogresslogreaderwriter is a generated GoMock package.
package mockcommonprogresslogreaderwriter

import (
	context "context"
	reflect "reflect"

	common "gitee.com/pingcap_enterprise/tms/server/models/common"
	gomock "github.com/golang/mock/gomock"
)

// MockProgressLogDetailReaderWriter is a mock of ProgressLogDetailReaderWriter interface.
type MockProgressLogDetailReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockProgressLogDetailReaderWriterMockRecorder
}

// MockProgressLogDetailReaderWriterMockRecorder is the mock recorder for MockProgressLogDetailReaderWriter.
type MockProgressLogDetailReaderWriterMockRecorder struct {
	mock *MockProgressLogDetailReaderWriter
}

// NewMockProgressLogDetailReaderWriter creates a new mock instance.
func NewMockProgressLogDetailReaderWriter(ctrl *gomock.Controller) *MockProgressLogDetailReaderWriter {
	mock := &MockProgressLogDetailReaderWriter{ctrl: ctrl}
	mock.recorder = &MockProgressLogDetailReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProgressLogDetailReaderWriter) EXPECT() *MockProgressLogDetailReaderWriterMockRecorder {
	return m.recorder
}

// BatchGetProgressLogDetail mocks base method.
func (m *MockProgressLogDetailReaderWriter) BatchGetProgressLogDetail(ctx context.Context, logDetail *common.ProgressLogDetail) ([]*common.ProgressLogDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetProgressLogDetail", ctx, logDetail)
	ret0, _ := ret[0].([]*common.ProgressLogDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetProgressLogDetail indicates an expected call of BatchGetProgressLogDetail.
func (mr *MockProgressLogDetailReaderWriterMockRecorder) BatchGetProgressLogDetail(ctx, logDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetProgressLogDetail", reflect.TypeOf((*MockProgressLogDetailReaderWriter)(nil).BatchGetProgressLogDetail), ctx, logDetail)
}

// CreateProgressLogDetail mocks base method.
func (m *MockProgressLogDetailReaderWriter) CreateProgressLogDetail(ctx context.Context, logDetail *common.ProgressLogDetail) (*common.ProgressLogDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateProgressLogDetail", ctx, logDetail)
	ret0, _ := ret[0].(*common.ProgressLogDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateProgressLogDetail indicates an expected call of CreateProgressLogDetail.
func (mr *MockProgressLogDetailReaderWriterMockRecorder) CreateProgressLogDetail(ctx, logDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateProgressLogDetail", reflect.TypeOf((*MockProgressLogDetailReaderWriter)(nil).CreateProgressLogDetail), ctx, logDetail)
}

// GetProcessLogDetailByInterval mocks base method.
func (m *MockProgressLogDetailReaderWriter) GetProcessLogDetailByInterval(ctx context.Context, logDetail *common.ProgressLogDetail) ([]*common.ProgressLogDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProcessLogDetailByInterval", ctx, logDetail)
	ret0, _ := ret[0].([]*common.ProgressLogDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProcessLogDetailByInterval indicates an expected call of GetProcessLogDetailByInterval.
func (mr *MockProgressLogDetailReaderWriterMockRecorder) GetProcessLogDetailByInterval(ctx, logDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProcessLogDetailByInterval", reflect.TypeOf((*MockProgressLogDetailReaderWriter)(nil).GetProcessLogDetailByInterval), ctx, logDetail)
}

// GetProgressLogDetail mocks base method.
func (m *MockProgressLogDetailReaderWriter) GetProgressLogDetail(ctx context.Context, taskID int, sourceSchema string, sourceTables []string) ([]*common.ProgressLogDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProgressLogDetail", ctx, taskID, sourceSchema, sourceTables)
	ret0, _ := ret[0].([]*common.ProgressLogDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProgressLogDetail indicates an expected call of GetProgressLogDetail.
func (mr *MockProgressLogDetailReaderWriterMockRecorder) GetProgressLogDetail(ctx, taskID, sourceSchema, sourceTables interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProgressLogDetail", reflect.TypeOf((*MockProgressLogDetailReaderWriter)(nil).GetProgressLogDetail), ctx, taskID, sourceSchema, sourceTables)
}

// UpdateProgressLogDetail mocks base method.
func (m *MockProgressLogDetailReaderWriter) UpdateProgressLogDetail(ctx context.Context, logDetail *common.ProgressLogDetail) (*common.ProgressLogDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateProgressLogDetail", ctx, logDetail)
	ret0, _ := ret[0].(*common.ProgressLogDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateProgressLogDetail indicates an expected call of UpdateProgressLogDetail.
func (mr *MockProgressLogDetailReaderWriterMockRecorder) UpdateProgressLogDetail(ctx, logDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateProgressLogDetail", reflect.TypeOf((*MockProgressLogDetailReaderWriter)(nil).UpdateProgressLogDetail), ctx, logDetail)
}
