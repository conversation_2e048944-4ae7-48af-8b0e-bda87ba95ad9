/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package common

import (
	"context"
	"errors"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type ProgressLogDetailReadWrite struct {
	GormDB
}

func NewProgressLogReadWrite(db *gorm.DB) *ProgressLogDetailReadWrite {
	m := &ProgressLogDetailReadWrite{
		WrapDB(db),
	}
	return m
}

func (rw ProgressLogDetailReadWrite) CreateProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) (*ProgressLogDetail, error) {
	err := rw.DB(ctx).Create(logDetail).Error
	if err != nil {
		log.Errorf("create progress log detail to db failed. %s", err)
		return logDetail, WrapDBError(err)
	}
	return logDetail, nil
}

func (rw ProgressLogDetailReadWrite) BatchGetProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) ([]*ProgressLogDetail, error) {
	var objDetails []*ProgressLogDetail
	err := rw.GormDB.DB(ctx).Where(logDetail).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("batch query progress log detail not found, query cond: %v", logDetail.String())
	} else if err != nil {
		log.Errorf("batch get progress log detail failed, query cond %v, %v", logDetail.String(), err)
		return nil, WrapDBError(err)
	}
	return objDetails, nil
}

func (rw ProgressLogDetailReadWrite) GetProgressLogDetail(ctx context.Context, taskID int, sourceSchema string, sourceTables []string) ([]*ProgressLogDetail, error) {
	var objDetails []*ProgressLogDetail
	err := rw.GormDB.DB(ctx).Where("task_id = ? AND schema_name_s = ? AND table_name_s  IN (?)", taskID, sourceSchema, sourceTables).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query progress log detail not found, query cond: taskID %v, sourceSchema %v, sourceTables %v", taskID, sourceSchema, sourceTables)
	} else if err != nil {
		log.Errorf("get progress log detail failed, query cond: taskID %v, sourceSchema %v, sourceTables %v, error: %v", taskID, sourceSchema, sourceTables, err)
		return nil, WrapDBError(err)
	}
	return objDetails, nil
}

func (rw ProgressLogDetailReadWrite) GetProcessLogDetailByInterval(ctx context.Context, logDetail *ProgressLogDetail) ([]*ProgressLogDetail, error) {
	var objDetails []*ProgressLogDetail
	err := rw.GormDB.DB(ctx).Where("task_id = ? AND created_at >= ?", logDetail.TaskID, logDetail.CreatedAt).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query table progress log detail by interval not found, query cond: %v", logDetail.String())
	} else if err != nil {
		log.Errorf("get table progress log detail by interval failed, query cond %d, %v", logDetail.String(), err)
		return nil, WrapDBError(err)
	}
	return objDetails, nil
}

func (rw ProgressLogDetailReadWrite) UpdateProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) (*ProgressLogDetail, error) {
	err := rw.DB(ctx).Save(logDetail).Error
	if err != nil {
		log.Errorf("update progress log detail info to db failed. %s", err)
		return nil, WrapDBError(err)
	}
	return logDetail, nil
}

func (rw ProgressLogDetailReadWrite) UpdateProgressLogDetailTimeAndMessageByProgressID(ctx context.Context, logDetail *ProgressLogDetail) error {
	updateSQL := `UPDATE progress_log_details SET created_at = ?, updated_at = ?, detail = ? WHERE progress_id = ?`
	err := rw.DB(ctx).Exec(updateSQL, logDetail.CreatedAt, logDetail.UpdatedAt, logDetail.Detail, logDetail.ProgressID).Error
	if err != nil {
		log.Errorf("update progress log detail info to db failed. %s", err)
		return WrapDBError(err)
	}
	return nil

}

func (rw ProgressLogDetailReadWrite) SaveProgressLogDetails(ctx context.Context, logDetails []*ProgressLogDetail) ([]*ProgressLogDetail, error) {
	err := rw.DB(ctx).Save(logDetails).Error
	if err != nil {
		log.Errorf("save table taskStats info failed. err:%s", err)
		return nil, WrapDBError(err)
	}
	return logDetails, nil
}

func (rw ProgressLogDetailReadWrite) DeleteProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) error {
	err := rw.DB(ctx).Where(logDetail).Delete(&ProgressLogDetail{}).Error
	if err != nil {
		log.Errorf("delete progress log detail info from db failed. %s", err)
		return WrapDBError(err)
	}
	return nil
}

func (rw ProgressLogDetailReadWrite) DeleteProgressLogDetailByChannelIdTaskIds(ctx context.Context, channelId int, taskIds []int) error {
	err := rw.DB(ctx).Where("channel_id = ? AND task_id IN (?)", channelId, taskIds).Delete(&ProgressLogDetail{}).Error
	if err != nil {
		log.Errorf("delete progress log detail info from db failed. %s", err)
		return WrapDBError(err)
	}
	return nil
}

func (rw ProgressLogDetailReadWrite) ListProcessLogDetails(ctx context.Context, taskId int) ([]*ProgressLogDetail, error) {
	var objDetails []*ProgressLogDetail
	err := rw.GormDB.DB(ctx).Where("task_id = ?", taskId).Find(&objDetails).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("query progress log detail not found, query cond: taskId %v", taskId)
	} else if err != nil {
		log.Errorf("get progress log detail failed, query cond: taskId %v, error: %v", taskId, err)
		return nil, WrapDBError(err)
	}
	return objDetails, nil
}
