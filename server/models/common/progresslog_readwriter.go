/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package common

import "context"

type ProgressLogDetailReaderWriter interface {
	CreateProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) (*ProgressLogDetail, error)
	UpdateProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) (*ProgressLogDetail, error)
	UpdateProgressLogDetailTimeAndMessageByProgressID(ctx context.Context, logDetail *ProgressLogDetail) error
	SaveProgressLogDetails(ctx context.Context, logDetails []*ProgressLogDetail) ([]*ProgressLogDetail, error)
	BatchGetProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) ([]*ProgressLogDetail, error)
	GetProgressLogDetail(ctx context.Context, taskID int, sourceSchema string, sourceTables []string) ([]*ProgressLogDetail, error)
	GetProcessLogDetailByInterval(ctx context.Context, logDetail *ProgressLogDetail) ([]*ProgressLogDetail, error)
	ListProcessLogDetails(ctx context.Context, taskId int) ([]*ProgressLogDetail, error)
	DeleteProgressLogDetail(ctx context.Context, logDetail *ProgressLogDetail) error
	DeleteProgressLogDetailByChannelIdTaskIds(ctx context.Context, channelId int, taskIds []int) error
}
