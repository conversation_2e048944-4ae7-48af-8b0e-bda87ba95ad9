/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package common

import (
	"context"
	"errors"
)

// ErrVersionConflict indicates that optimistic locking detected a version conflict
var ErrVersionConflict = errors.New("version conflict detected")

type ReaderWriter interface {
	CreateSystemInfo(ctx context.Context, systemInfo *SystemInfo) (*SystemInfo, error)
	UpdateSystemInfo(ctx context.Context, systemInfo *SystemInfo) (*SystemInfo, error)
	GetSystemInfo(ctx context.Context, machineId string) (*SystemInfo, error)

	InsertFeatureLicense(ctx context.Context, fl *FeatureLicense) (*FeatureLicense, error)
	GetFeatureLicense(ctx context.Context, featureCode, licenseKey string) (*FeatureLicense, error)
	UpdateFeatureLicense(ctx context.Context, fl *FeatureLicense) error
	ListFeatureLicenses(ctx context.Context, licenseKey string) ([]*FeatureLicense, error)

	// Optimistic locking methods
	GetFeatureLicenseWithVersion(ctx context.Context, featureCode, licenseKey string) (*FeatureLicense, error)
	UpdateFeatureLicenseWithVersion(ctx context.Context, fl *FeatureLicense) error
}
