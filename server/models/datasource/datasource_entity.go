package datasource

import (
	"encoding/json"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models/common"
)

// CREATE TABLE `datasources` (
//
//	`datasource_id` bigint NOT NULL AUTO_INCREMENT COMMENT '数据源id',
//	`datasource_name` varchar(100) NOT NULL COMMENT 'datasource instance name数据源别名',
//	`db_type` varchar(10) NOT NULL COMMENT '''type of database, eg.Oracle/Mysql/Tidb/Tidb-proxy/Oracle-adg''',
//	`host_ip` varchar(15) DEFAULT NULL COMMENT 'host ip',
//	`host_port` bigint DEFAULT NULL COMMENT 'host port',
//	`db_name` varchar(100) DEFAULT NULL COMMENT 'db name',
//	`service_name` varchar(100) DEFAULT NULL COMMENT 'oracle service name',
//	`user_name` varchar(100) DEFAULT NULL COMMENT 'user name',
//	`password_value` varchar(100) DEFAULT NULL COMMENT 'user password',
//	`password_encrypt` varchar(1000) DEFAULT NULL COMMENT 'user password',
//	`connect_params` varchar(150) DEFAULT NULL COMMENT 'connect params',
//	`table_option` varchar(150) DEFAULT NULL COMMENT 'table shard row id bit set',
//	`dbcharset` varchar(60) DEFAULT NULL COMMENT 'oracle connect charset',    --->修改名称dbcharset,防止系统保留字冲突
//	`connection_status` char(1) DEFAULT NULL COMMENT 'connection status, eg.Y/N',
//	`proxy_source_id` bigint DEFAULT NULL COMMENT 'refer to proxy data source',
//
//	`db_version` varchar(20) NOT NULL COMMENT '''oracle 11gr2,oracle 12c+''',
//
//	`pdb_flag` varchar(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '是否是pdb',
//	`link_flag` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '连接标记',    ---未知字段，保留
//	`pdb_name` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'pdb服务名称',
//	`pdb_db_name` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'pdb库名',
//
//	`asm_sid` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '添加ASM相关信息：ASM的SID信息',
//	`asm_oracle_home` varchar(150) COLLATE utf8mb4_bin COMMENT '添加ASM相关信息：ASM的ORACLE_HOME信息',
//	`asm_home` varchar(150) COLLATE utf8mb4_bin COMMENT 'ASM_HOME',
//	`asm_db_user` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ASM实例用户名',
//	`asm_db_passwd` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'ASM实例密码',
//
//	`db_connection_mode` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '数据库连接模式 SID/TNS',
//	`sid_name` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'SID名称',
//
//	`logfile_storemode` varchar(1) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '(A)SM/(F)ilesytem',
//	`asm_db_ip` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'asm_ip',
//	`asm_db_port` varchar(10) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'asm_端口',
//	`asm_db_name` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'asm_服务名',
//	`oracle_home` varchar(150) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'oracle_home',
//	`oracle_sid` varchar(30) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'oracle_sid',
//
//	`as_sysdba` int(11) unsigned zerofill DEFAULT NULL COMMENT '是否使用系统管理员身份连接，默认否(0)，是(1)',
//	`host_list` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '负载均衡的主机列表',
//
//	`comment` varchar(1000) DEFAULT NULL COMMENT 'comment content',
//	`created_at` datetime(3) DEFAULT NULL,
//	`updated_at` datetime(3) DEFAULT NULL,
//	`deleted_at` datetime(3) DEFAULT NULL
//
// )
type Datasource struct {
	DatasourceId     int    `gorm:"primarykey"`
	DatasourceName   string `gorm:"not null;type:varchar(100);comment:datasource instance name"`
	DbType           string `gorm:"not null;type:varchar(10);comment:'type of database, eg.Oracle/Mysql/Tidb/Tidb-proxy/Oracle-adg'"`
	HostIp           string `gorm:"type:varchar(15);comment:host ip"`
	HostPort         int    `gorm:"comment:host port"`
	DbName           string `gorm:"type:varchar(100);comment:db name"`
	ServiceName      string `gorm:"type:varchar(100);comment:oracle service name"`
	UserName         string `gorm:"type:varchar(100);comment:user name"`
	PasswordValue    string `gorm:"type:varchar(100);comment:user password"`
	PasswordEncrypt  string `gorm:"type:varchar(1000);comment:user password"`
	ConnectParams    string `gorm:"type:varchar(150);comment:connect params"`
	TableOption      string `gorm:"type:varchar(150);comment:table shard row id bit set"`
	Charset          string `gorm:"type:varchar(60);comment:oracle connect charset"`
	ConnectionStatus string `gorm:"type:char(1);comment:connection status, eg.Y/N"`
	ProxySourceId    int    `gorm:"comment:refer to proxy data source"`

	EnableIncrementSync bool   `gorm:"comment:是否开启增量同步"`
	DBVersion           string `gorm:"type:varchar(100);comment:'oracle 11gr2,oracle 12c+'"`
	PDBFlag             string `gorm:"type:varchar(20);comment:是否是pdb"`
	LinkFlag            string `gorm:"type:varchar(10);comment:连接标记"`
	PDBName             string `gorm:"type:varchar(30);comment:pdb服务名称"`
	PDBDBName           string `gorm:"type:varchar(30);comment:pdb库名"`
	ASMSid              string `gorm:"type:varchar(30);comment:添加ASM相关信息：ASM的SID信息"`
	ASMOracleHome       string `gorm:"type:varchar(150);comment:添加ASM相关信息：ASM的ORACLE_HOME信息"`
	ASMHome             string `gorm:"type:varchar(150);comment:ASM_HOME"`
	ASMDBUser           string `gorm:"type:varchar(30);comment:ASM实例用户名"`
	ASMDBPasswdValue    string `gorm:"type:varchar(30);comment:ASM实例密码"`
	ASMDBPasswdEncrypt  string `gorm:"type:varchar(30);comment:ASM实例密码-加密后"`
	DBConnectionMode    string `gorm:"type:varchar(255);comment:数据库连接模式 SID/TNS"`
	SIDName             string `gorm:"type:varchar(30);comment:SID名称"`
	LogFileStoreMode    string `gorm:"type:varchar(1);comment:'(A)SM/(F)ilesytem'"`
	ASMDBIp             string `gorm:"type:varchar(30);comment:asm_ip"`
	ASMDBPort           int    `gorm:"type:varchar(10);comment:asm_端口"`
	ASMDBName           string `gorm:"type:varchar(30);comment:asm_服务名"`
	OracleHome          string `gorm:"type:varchar(150);comment:oracle_home"`
	OracleSID           string `gorm:"type:varchar(30);comment:oracle_sid"`

	AsSysDBA      int    `gorm:"comment:'是否使用系统管理员身份连接，默认否(0)，是(1)'"`
	HostList      string `gorm:"type:varchar(255);comment:负载均衡的主机列表"`
	PrometheusUrl string `gorm:"type:varchar(255);comment:Prometheus URL"`
	*common.Entity
}

type OpMachineConf struct {
	MacId       int       `gorm:"primarykey"`
	HostName    string    `gorm:"type:varchar(50);comment:主机名称"`
	HostIP      string    `gorm:"type:varchar(32);uniqueIndex:uqi_host_ip;comment:主机IP"`
	CreateTime  time.Time `gorm:"comment:创建时间"`
	PortMax     int       `gorm:"comment:端口范围最大值"`
	PortMin     int       `gorm:"comment:端口范围最小值"`
	PortHasUsed string    `gorm:"type:varchar(100);comment:已使用端口，用逗号分隔"`
	BasePath    string    `gorm:"type:varchar(100);comment:DSG路径"`
	OsName      string    `gorm:"type:varchar(50);comment:操作系统,Linux/Windows"`
	Allocation  bool      `gorm:"comment:是否分配"`
	*common.Entity
}

type SchemaFilter struct {
	SchemaFilterId int    `gorm:"primarykey"`
	DbType         string `gorm:"not null;type:varchar(10);comment:'type of database, eg.oracle/mysql/tidb"`
	SchemaName     string `gorm:"type:varchar(100);comment:schema name"`
}

type Dependency struct {
	SchemaName            string
	PackageName           string
	Owner                 string
	Name                  string
	Type                  string
	ReferencedPackageName string
	ReferencedOwner       string
	ReferencedName        string
	ReferencedType        string
	ReferencedLinkName    string
	DependencyType        string

	IsFromPackageBody bool
}

func (i *Dependency) String() string {
	bytes, _ := json.Marshal(i)
	return string(bytes)
}

func (i *Dependency) UniqueKey() string {
	return i.Owner + "." + i.Type + "." + i.Name
}

func (i *Dependency) IsReferencedNodeShouldIgnored() bool {
	if i.ReferencedType == "TMS_FLAG" || i.ReferencedName == "TMS_FLAG" || i.ReferencedOwner == "TMS_FLAG" {
		return false
	}
	if i.ReferencedName == "SYS_STUB_FOR_PURITY_ANALYSIS" || i.ReferencedName == "DUAL" || i.ReferencedName == "DBMS_OUTPUT" {
		return true
	}
	if i.ReferencedOwner == "SYS" && i.ReferencedName == "STANDARD" {
		return true
	}
	if i.ReferencedType == "SYNONYM" || i.ReferencedType == "TABLE" {
		return true
	}
	return false
}

func (i *Dependency) ReferencedUniqueKey() string {
	return i.ReferencedOwner + "." + i.ReferencedType + "." + i.ReferencedName
}

func (i *Dependency) GetSchemaName() string {
	return i.SchemaName
}

func (i *Dependency) GetPackageName() string {
	return i.PackageName
}

func (i *Dependency) GetOwner() string {
	return i.Owner
}

func (i *Dependency) GetName() string {
	return i.Name
}

func (i *Dependency) GetType() string {
	return i.Type
}

func (i *Dependency) GetDependencyType() string {
	return i.DependencyType
}

func (i *Dependency) GetIsFromPackageBody() bool {
	return i.IsFromPackageBody
}

func (i *Dependency) GetReferencedPackageName() string {
	return i.ReferencedPackageName
}

func (i *Dependency) GetReferencedOwner() string {
	return i.ReferencedOwner
}

func (i *Dependency) GetReferencedName() string {
	return i.ReferencedName
}

func (i *Dependency) GetReferencedType() string {
	return i.ReferencedType
}

// OracleSegmentSizeEntity 定义了Oracle segment size信息的GORM实体模型
type OracleSegmentSizeEntity struct {
	ID          uint      `gorm:"primarykey;autoIncrement;comment:主键ID"`
	TaskId      int       `gorm:"not null;index:idx_task_id;comment:任务ID"`
	Owner       string    `gorm:"type:varchar(100);not null;comment:所有者"`
	SegmentName string    `gorm:"type:varchar(200);not null;comment:段名称"`
	SumT        float64   `gorm:"type:decimal(20,4);default:0;comment:TABLE size in MB"`
	SumTp       float64   `gorm:"type:decimal(20,4);default:0;comment:TABLE PARTITION size in MB"`
	SumTsup     float64   `gorm:"type:decimal(20,4);default:0;comment:TABLE SUBPARTITION size in MB"`
	SumI        float64   `gorm:"type:decimal(20,4);default:0;comment:INDEX size in MB"`
	SumIp       float64   `gorm:"type:decimal(20,4);default:0;comment:INDEX PARTITION size in MB"`
	SumIsp      float64   `gorm:"type:decimal(20,4);default:0;comment:INDEX SUBPARTITION size in MB"`
	CreateTime  time.Time `gorm:"type:datetime;not null;comment:创建时间"`
	*common.Entity
}

// TableName 指定表名
func (OracleSegmentSizeEntity) TableName() string {
	return "TMS_ORACLE_SEGMENT_SIZES"
}

// ClusterHardware 映射 information_schema.cluster_hardware
// CREATE TABLE `CLUSTER_HARDWARE` (
//
//	`TYPE` varchar(64) DEFAULT NULL,
//	`INSTANCE` varchar(64) DEFAULT NULL,
//	`DEVICE_TYPE` varchar(64) DEFAULT NULL,
//	`DEVICE_NAME` varchar(64) DEFAULT NULL,
//	`NAME` varchar(256) DEFAULT NULL,
//	`VALUE` varchar(128) DEFAULT NULL
//
// )
type ClusterHardware struct {
	Type       string `gorm:"column:TYPE" json:"type"`
	Instance   string `gorm:"column:INSTANCE" json:"instance"`
	DeviceType string `gorm:"column:DEVICE_TYPE" json:"device_type"`
	DeviceName string `gorm:"column:DEVICE_NAME" json:"device_name"`
	Name       string `gorm:"column:NAME" json:"name"`
	Value      string `gorm:"column:VALUE" json:"value"`
}

func (ClusterHardware) TableName() string {
	return "information_schema.cluster_hardware"
}
