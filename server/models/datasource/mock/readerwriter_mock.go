// Code generated by MockGen. DO NOT EDIT.
// Source: ./datasource/readerwriter.go

// Package mockdatasourcereaderwriter is a generated GoMock package.
package mockdatasourcereaderwriter

import (
	context "context"
	sql "database/sql"
	reflect "reflect"

	structs "gitee.com/pingcap_enterprise/tms/common/structs"
	datasource "gitee.com/pingcap_enterprise/tms/server/models/datasource"
	gomock "github.com/golang/mock/gomock"
)

// MockReaderWriter is a mock of ReaderWriter interface.
type MockReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReaderWriterMockRecorder
}

// MockReaderWriterMockRecorder is the mock recorder for MockReaderWriter.
type MockReaderWriterMockRecorder struct {
	mock *MockReaderWriter
}

// NewMockReaderWriter creates a new mock instance.
func NewMockReaderWriter(ctrl *gomock.Controller) *MockReaderWriter {
	mock := &MockReaderWriter{ctrl: ctrl}
	mock.recorder = &MockReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReaderWriter) EXPECT() *MockReaderWriterMockRecorder {
	return m.recorder
}

// BatchDelete mocks base method.
func (m *MockReaderWriter) BatchDelete(ctx context.Context, dataSourceIDs []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", ctx, dataSourceIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockReaderWriterMockRecorder) BatchDelete(ctx, dataSourceIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockReaderWriter)(nil).BatchDelete), ctx, dataSourceIDs)
}

// CreateDataSource mocks base method.
func (m *MockReaderWriter) CreateDataSource(ctx context.Context, dataSource *datasource.Datasource) (*datasource.Datasource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDataSource", ctx, dataSource)
	ret0, _ := ret[0].(*datasource.Datasource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataSource indicates an expected call of CreateDataSource.
func (mr *MockReaderWriterMockRecorder) CreateDataSource(ctx, dataSource interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataSource", reflect.TypeOf((*MockReaderWriter)(nil).CreateDataSource), ctx, dataSource)
}

// Get mocks base method.
func (m *MockReaderWriter) Get(ctx context.Context, datasourceId int) (*datasource.Datasource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, datasourceId)
	ret0, _ := ret[0].(*datasource.Datasource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockReaderWriterMockRecorder) Get(ctx, datasourceId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockReaderWriter)(nil).Get), ctx, datasourceId)
}

// GetMysqlSchemas mocks base method.
func (m *MockReaderWriter) GetMysqlSchemas(ctx context.Context, db *sql.DB) ([]*structs.SchemaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMysqlSchemas", ctx, db)
	ret0, _ := ret[0].([]*structs.SchemaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMysqlSchemas indicates an expected call of GetMysqlSchemas.
func (mr *MockReaderWriterMockRecorder) GetMysqlSchemas(ctx, db interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMysqlSchemas", reflect.TypeOf((*MockReaderWriter)(nil).GetMysqlSchemas), ctx, db)
}

// GetMysqlTables mocks base method.
func (m *MockReaderWriter) GetMysqlTables(ctx context.Context, db *sql.DB, schemas []string) ([]*structs.SchemaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMysqlTables", ctx, db, schemas)
	ret0, _ := ret[0].([]*structs.SchemaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMysqlTables indicates an expected call of GetMysqlTables.
func (mr *MockReaderWriterMockRecorder) GetMysqlTables(ctx, db, schemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMysqlTables", reflect.TypeOf((*MockReaderWriter)(nil).GetMysqlTables), ctx, db, schemas)
}

// GetOracleSchemaTableOriginDDL mocks base method.
func (m *MockReaderWriter) GetOracleSchemaTableOriginDDL(ctx context.Context, tableType, schemaName, tableName string, db *sql.DB) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOracleSchemaTableOriginDDL", ctx, tableType, schemaName, tableName, db)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOracleSchemaTableOriginDDL indicates an expected call of GetOracleSchemaTableOriginDDL.
func (mr *MockReaderWriterMockRecorder) GetOracleSchemaTableOriginDDL(ctx, tableType, schemaName, tableName, db interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOracleSchemaTableOriginDDL", reflect.TypeOf((*MockReaderWriter)(nil).GetOracleSchemaTableOriginDDL), ctx, tableType, schemaName, tableName, db)
}

// GetOracleSchemas mocks base method.
func (m *MockReaderWriter) GetOracleSchemas(ctx context.Context, db *sql.DB) ([]*structs.SchemaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOracleSchemas", ctx, db)
	ret0, _ := ret[0].([]*structs.SchemaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOracleSchemas indicates an expected call of GetOracleSchemas.
func (mr *MockReaderWriterMockRecorder) GetOracleSchemas(ctx, db interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOracleSchemas", reflect.TypeOf((*MockReaderWriter)(nil).GetOracleSchemas), ctx, db)
}

// GetOracleTables mocks base method.
func (m *MockReaderWriter) GetOracleTables(ctx context.Context, db *sql.DB, schemas []string) ([]*structs.SchemaInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOracleTables", ctx, db, schemas)
	ret0, _ := ret[0].([]*structs.SchemaInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOracleTables indicates an expected call of GetOracleTables.
func (mr *MockReaderWriterMockRecorder) GetOracleTables(ctx, db, schemas interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOracleTables", reflect.TypeOf((*MockReaderWriter)(nil).GetOracleTables), ctx, db, schemas)
}

// List mocks base method.
func (m *MockReaderWriter) List(ctx context.Context, page, pageSize int, dataSourceName, dbType, connectionStatus string) ([]*datasource.Datasource, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, page, pageSize, dataSourceName, dbType, connectionStatus)
	ret0, _ := ret[0].([]*datasource.Datasource)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// List indicates an expected call of List.
func (mr *MockReaderWriterMockRecorder) List(ctx, page, pageSize, dataSourceName, dbType, connectionStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockReaderWriter)(nil).List), ctx, page, pageSize, dataSourceName, dbType, connectionStatus)
}

// UpdateDataSource mocks base method.
func (m *MockReaderWriter) UpdateDataSource(ctx context.Context, dataSource *datasource.Datasource) (*datasource.Datasource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDataSource", ctx, dataSource)
	ret0, _ := ret[0].(*datasource.Datasource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateDataSource indicates an expected call of UpdateDataSource.
func (mr *MockReaderWriterMockRecorder) UpdateDataSource(ctx, dataSource interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDataSource", reflect.TypeOf((*MockReaderWriter)(nil).UpdateDataSource), ctx, dataSource)
}
