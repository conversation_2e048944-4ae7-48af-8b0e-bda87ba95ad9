package datasource

import (
	"bytes"
	"reflect"
	"strconv"
	"strings"
	"time"
)

type OracleObject struct {
	OwnerName  string
	ObjectType string
	ObjectName string
	Status     string
}

func (i OracleObject) Key() string {
	return i.OwnerName + "." + i.ObjectType + "." + i.ObjectName
}

func (i OracleObject) IsValid() bool {
	return i.Status == "VALID" || i.Status == "valid"
}

type OraclePackageDefinition struct {
	SchemaName    string
	PackageName   string
	Text          string
	HighlightLine string
}

func (i OraclePackageDefinition) GetDefinition() string {
	if strings.HasPrefix(i.Text, "create") || strings.HasPrefix(i.Text, "CREATE") {
		return i.Text
	} else {
		return "CREATE " + i.Text
	}
}

func (i OraclePackageDefinition) GetHighlightLine() string {
	return i.HighlightLine
}

func (i OraclePackageDefinition) GetSchemaName() string {
	return i.SchemaName
}

func (i OraclePackageDefinition) GetPackageName() string {
	return i.PackageName
}

type OracleObjectDefinition struct {
	OwnerName  string `json:"ownerName"`
	ObjectName string `json:"objectName"`
	ObjectType string `json:"objectType"`
	AllText    string `json:"allText"`
}

func (i OracleObjectDefinition) Key() string {
	return i.OwnerName + "." + i.ObjectType + "." + i.ObjectName
}

func (s OracleObjectDefinition) GetOwnerName() string {
	return s.OwnerName
}

func (s OracleObjectDefinition) GetObjectName() string {
	return s.ObjectName
}

func (s OracleObjectDefinition) GetObjectType() string {
	return s.ObjectType
}

func (s OracleObjectDefinition) GetAllText() string {
	return s.AllText
}

// OracleObjectMeta represents lightweight object metadata for batch processing optimization
type OracleObjectMeta struct {
	OwnerName  string `json:"owner_name"`
	ObjectName string `json:"object_name"`
	ObjectType string `json:"object_type"`
}

func (o OracleObjectMeta) Key() string {
	return o.OwnerName + "." + o.ObjectType + "." + o.ObjectName
}

// ObjectBatchGroup represents a group of objects for batch processing
// Groups objects by SCHEMA+TYPE for optimal query performance
type ObjectBatchGroup struct {
	SchemaName   string
	ObjectType   string
	Objects      []OracleObjectMeta
	BatchIndex   int
	TotalBatches int
}

// BatchResult represents the result of a batch processing operation
type BatchResult struct {
	BatchIndex  int
	SchemaName  string
	ObjectType  string
	Definitions []OracleObjectDefinition
	Error       error
	ProcessTime time.Duration
}

type SQLSet struct {
	ID             uint64    `json:"id"`
	TaskId         int       `json:"taskId"`
	Name           string    `json:"name"`
	Owner          string    `json:"owner"`
	Description    string    `json:"description"`
	Created        time.Time `json:"created"`
	LastModified   time.Time `json:"lastModified"`
	StatementCount uint64    `json:"statementCount"`
}

type SQLSetStatement struct {
	TaskID            int64     `db:"task_id"`             // NUMBER not null
	SQLSetName        string    `db:"sqlset_name"`         // VARCHAR2(128) not null
	SQLSetOwner       string    `db:"sqlset_owner"`        // VARCHAR2(128)
	SQLSetID          int64     `db:"sqlset_id"`           // NUMBER not null
	DigestID          string    `db:"digest_id"`           // VARCHAR2(64)
	SQLID             string    `db:"sql_id"`              // VARCHAR2(13) not null
	SQLText           string    `db:"sql_text"`            // CLOB
	ParsingSchemaName string    `db:"parsing_schema_name"` // VARCHAR2(128)
	PlanHashValue     int64     `db:"plan_hash_value"`     // NUMBER not null
	BindData          []byte    `db:"bind_data"`           // RAW(2000)
	CommandType       int64     `db:"command_type"`        // NUMBER not null
	Module            string    `db:"module"`              // VARCHAR2(64)
	Action            string    `db:"action"`              // VARCHAR2(64)
	ElapsedTime       int64     `db:"elapsed_time"`        // NUMBER
	Executions        int64     `db:"executions"`          // NUMBER
	Replay            string    `db:"replay"`              // CHAR(1)
	ReplayStatus      string    `db:"replay_status"`       // VARCHAR2(10)
	ReplayFinishTime  time.Time `db:"replay_finish_time"`  // DATE
	ReplayExecCode    string    `db:"replay_exec_code"`    // VARCHAR2(20)
	RewriteSQLText    string    `db:"rewrite_sql_text"`    // CLOB
	RewritePrompts    string    `db:"rewrite_prompts"`     // VARCHAR2(2000)
	MessageLog        string    `db:"message_log"`         // VARCHAR2(2000)
	LastCopyTime      time.Time `db:"last_copy_time"`      // DATE

	// User operation fields (from metadb)
	UserOperateStatus string     `json:"userOperateStatus,omitempty"`
	UserOperateBy     string     `json:"userOperateBy,omitempty"`
	UserOperateAt     *time.Time `json:"userOperateAt,omitempty"`
	UserOperateRemark string     `json:"userOperateRemark,omitempty"`
}

func (i *SQLSet) String() string {
	sb := bytes.Buffer{}
	sb.WriteString("SQLSet{")
	sb.WriteString("ID: ")
	sb.WriteString(strconv.FormatUint(i.ID, 10))
	sb.WriteString(", Name: ")
	sb.WriteString(i.Name)
	sb.WriteString(", Owner: ")
	sb.WriteString(i.Owner)
	sb.WriteString(", Description: ")
	sb.WriteString(i.Description)
	sb.WriteString(", Created: ")
	sb.WriteString(i.Created.Format(time.RFC3339))
	sb.WriteString(", LastModified: ")
	sb.WriteString(i.LastModified.Format(time.RFC3339))
	sb.WriteString(", StatementCount: ")
	sb.WriteString(strconv.FormatUint(i.StatementCount, 10))
	sb.WriteString("}")
	return sb.String()
}

func (i *SQLSet) Equals(other *SQLSet) bool {
	if i == nil || other == nil {
		return false
	}
	return i.Name == other.Name &&
		i.Owner == other.Owner &&
		i.Created.Unix() == other.Created.Unix() &&
		i.LastModified.Unix() == other.LastModified.Unix() &&
		i.StatementCount == other.StatementCount
}

func (i *SQLSet) IsCreatedModified(other *SQLSet) bool {
	if i == nil || other == nil {
		return false
	}
	if !(i.Name == other.Name && i.Owner == other.Owner) {
		return false
	}
	return i.Created.Unix() != other.Created.Unix()
}

// HasIncrementalData 如果创建时间相等，但是LAST_MODIFIED或者STATEMENT_COUNT变化，表示有增量
func (i *SQLSet) HasIncrementalData(other *SQLSet) bool {
	if i == nil || other == nil {
		return false
	}
	if !(i.Name == other.Name &&
		i.Created.Unix() == other.Created.Unix() &&
		i.Owner == other.Owner) {
		return false
	}

	if i.StatementCount != other.StatementCount {
		return true
	}
	if i.LastModified.Unix() != other.LastModified.Unix() {
		return true
	}

	return false
}

type SQLSetStatementsSummaries []SQLSetStatementsSummary

type SQLSetStatements []SQLSetStatement

func (i SQLSetStatementsSummaries) GetTotalTotalNum() uint64 {
	total := uint64(0)
	for _, summary := range i {
		total += summary.TotalNum
	}
	return total
}

func (i SQLSetStatementsSummaries) GetTotalFailedNum() uint64 {
	total := uint64(0)
	for _, summary := range i {
		total += summary.FailedNum
	}
	return total
}

func (i SQLSetStatementsSummaries) GetTotalSuccessNum() uint64 {
	total := uint64(0)
	for _, summary := range i {
		total += summary.SuccessNum
	}
	return total
}

func (i SQLSetStatementsSummaries) GetTotalTimeoutNum() uint64 {
	total := uint64(0)
	for _, summary := range i {
		total += summary.TimeoutNum
	}
	return total
}

func (i SQLSetStatementsSummaries) GetTotalProgress() float64 {
	total := i.GetTotalTotalNum()
	if total == 0 {
		return 0
	}
	failedNum := i.GetTotalFailedNum()
	successNum := i.GetTotalSuccessNum()
	timeoutNum := i.GetTotalTimeoutNum()
	return float64(successNum+failedNum+timeoutNum) / float64(total)
}

func (i SQLSetStatementsSummaries) GetTotal() *SQLSetStatementsSummary {
	total := &SQLSetStatementsSummary{}
	for _, summary := range i {
		total.TotalNum += summary.TotalNum
		total.FailedNum += summary.FailedNum
		total.SuccessNum += summary.SuccessNum
		total.TimeoutNum += summary.TimeoutNum
		total.NotReplayNum += summary.NotReplayNum
		total.ReplayedNum += summary.ReplayedNum
		total.NotDigestNum += summary.NotDigestNum
		total.DigestedNum += summary.DigestedNum
	}
	return total
}

type SQLSetStatementsSummary struct {
	SQLSetOwner  string
	SQLSetName   string
	TotalNum     uint64
	FailedNum    uint64
	SuccessNum   uint64
	TimeoutNum   uint64
	NotReplayNum uint64
	ReplayedNum  uint64
	NotDigestNum uint64
	DigestedNum  uint64
	IgnoredNum   uint64
	ResolvedNum  uint64
	Pct          string
}

type SQLSetStatementsFailedSummary struct {
	ReplayExecCode string
	TotalNum       uint64
	IgnoredNum     uint64
	ResolvedNum    uint64
	MessageLog     string   // 保留以保持兼容性，存储第一个错误信息
	MessageLogs    []string // 新增：存储所有不同的错误信息
}

type SQLSetStatementsFailedSummaries []SQLSetStatementsFailedSummary

type SQLSetStatementsTimeoutSummary struct {
	TotalNum           uint64
	IgnoredTimeoutNum  uint64
	ResolvedTimeoutNum uint64
}

type SQLSetStatementsTimeoutSummaries []SQLSetStatementsTimeoutSummary

type SqlAnalyzerReportV2 struct {
	Title string

	TotalSummaries   SQLSetStatementsSummaries
	FailedSummaries  SQLSetStatementsFailedSummaries
	TimeoutSummaries SQLSetStatementsTimeoutSummaries

	// Normal (not ignored/resolved) details
	ErrorDetails     SQLSetStatements
	SuccessDetails   SQLSetStatements
	TimeoutDetails   SQLSetStatements
	NotReplayDetails SQLSetStatements
	AiFixDetails     SQLSetStatements

	// Ignored details (user operation status = 'ignored')
	IgnoredErrorDetails     SQLSetStatements
	IgnoredSuccessDetails   SQLSetStatements
	IgnoredTimeoutDetails   SQLSetStatements
	IgnoredNotReplayDetails SQLSetStatements

	// Resolved details (user operation status = 'resolved')
	ResolvedErrorDetails     SQLSetStatements
	ResolvedSuccessDetails   SQLSetStatements
	ResolvedTimeoutDetails   SQLSetStatements
	ResolvedNotReplayDetails SQLSetStatements
}

type SqlAnalyzerReportAdapterV2 struct {
	Data *SqlAnalyzerReportV2
}

func (o *SqlAnalyzerReportAdapterV2) GetFieldByName(name string) reflect.Value {
	if o == nil || o.Data == nil {
		return reflect.Value{} // Return invalid reflect.Value
	}
	v := reflect.ValueOf(o.Data)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return reflect.Value{}
	}
	return v.Elem().FieldByName(name)
}

// Oracle 活跃会话信息
type OracleActiveTmsSession struct {
	Sid          string
	Serial       string
	Username     string
	Status       string
	SqlID        string
	SqlExecStart string
	Event        string
	Machine      string
	Program      string
}

// Oracle 会话进度信息
type OracleTmsSessionProgress struct {
	Username             string
	Target               string
	Sid                  string
	Serial               string
	Opname               string
	Progress             string
	TimeRemaining        int64
	TimeRemainingMinutes float64
	TimeRemainingHours   float64
	SqlText              string
	StartTime            string
}

// Oracle 物理文件IO统计
type TmsUserStats struct {
	TmsPct    string
	TotalUser string
	TmsUser   string
}

// Oracle 物理文件IO统计
type OraclePhysicalFileIOStats struct {
	Fn     string
	Ts     string
	Reads  string
	Writes string
	Br     string
	Bw     string
	RTimes string
	WTimes string
}

// Oracle 热点文件信息
type OracleHotFileInfo struct {
	FileName       string
	TablespaceName string
	CS             float64
	ReadTimeS      float64
	WriteTimeS     float64
}

// Oracle 表空间统计
type OracleTableSpaceStats struct {
	TablespaceName string
	TotGrootteMb   string
	UsedSpace      string
	UsedRatio      string
	TotalBytes     string
	MaxBytes       string
}

type OracleObjectStats struct {
	SchemaName  string
	Tables      string
	Views       string
	Sequences   string
	Indexes     string
	Procedures  string
	Packages    string
	Constraints string
	Triggers    string
	Functions   string
}

type TiDBRowCount struct {
	SchemaName string
	TableName  string
	TableRows  string
}

type OracleTableNumberRows struct {
	Owner        string
	TableName    string
	NumRows      string
	LastAnalyzed string
	Rn           string
}

type OracleObjectList struct {
	Owner      string
	ObjectName string
	ObjectType string
	Status     string
	Rn         string
}

// OracleSegmentSize 定义了Oracle segment size信息结构体
type OracleSegmentSize struct {
	TaskId      int       `json:"taskId" db:"task_id"`
	Owner       string    `json:"owner" db:"owner"`
	SegmentName string    `json:"segmentName" db:"segment_name"`
	SumT        float64   `json:"sumT" db:"sum_t"`       // TABLE size in MB
	SumTp       float64   `json:"sumTp" db:"sum_tp"`     // TABLE PARTITION size in MB
	SumTsup     float64   `json:"sumTsup" db:"sum_tsup"` // TABLE SUBPARTITION size in MB
	SumI        float64   `json:"sumI" db:"sum_i"`       // INDEX size in MB
	SumIp       float64   `json:"sumIp" db:"sum_ip"`     // INDEX PARTITION size in MB
	SumIsp      float64   `json:"sumIsp" db:"sum_isp"`   // INDEX SUBPARTITION size in MB
	CreateTime  time.Time `json:"createTime" db:"create_time"`
	TotalSize   float64   `json:"totalSize" db:"total_size"` // 新增字段，用于SQL聚合
}

type OracleSegmentSizes []OracleSegmentSize
