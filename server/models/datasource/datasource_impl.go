package datasource

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/server/crypto"
	"gitee.com/pingcap_enterprise/tms/util/database"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/panjf2000/ants/v2"
	"github.com/samber/lo"

	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gorm.io/gorm"
)

type DatasourceReadWrite struct {
	dbCommon.GormDB
}

func NewDatasourceReadWrite(db *gorm.DB) *DatasourceReadWrite {
	m := &DatasourceReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}

func (rw DatasourceReadWrite) CreateDataSource(ctx context.Context, dataSource *Datasource) (*Datasource, error) {
	if strings.EqualFold(dataSource.PasswordEncrypt, "") {
		dataSource.PasswordEncrypt = crypto.AesDefaultEncrypt(dataSource.PasswordValue, crypto.DefaultKey)
	}
	dataSource.PasswordValue = ""
	err := rw.DB(ctx).Create(dataSource).Error
	if err != nil {
		log.Errorf("create datasource to db failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create datasource to db successfully, %v", dataSource)
	return dataSource, nil
}

func (rw DatasourceReadWrite) UpdateDataSource(ctx context.Context, dataSource *Datasource) (*Datasource, error) {
	if strings.EqualFold(dataSource.PasswordEncrypt, "") {
		dataSource.PasswordEncrypt = crypto.AesDefaultEncrypt(dataSource.PasswordValue, crypto.DefaultKey)
	}
	dataSource.PasswordValue = ""
	err := rw.DB(ctx).Save(dataSource).Error
	if err != nil {
		log.Errorf("update datasource to db failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update datasource to db successfully, %v", dataSource)
	return dataSource, nil
}

func (rw DatasourceReadWrite) Get(ctx context.Context, dataSourceID int) (*Datasource, error) {
	ds := &Datasource{}
	err := rw.DB(ctx).First(ds, "datasource_id = ?", dataSourceID).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query datasource %d not found", dataSourceID)
	} else if err != nil {
		log.Errorf("get datasource failed. dataSourceID is %d, %v", dataSourceID, err)
		return nil, dbCommon.WrapDBError(err)
	}
	if strings.EqualFold(ds.PasswordValue, "") {
		ds.PasswordValue = crypto.AesDefaultDecrypt(ds.PasswordEncrypt, crypto.DefaultKey)
	} else if strings.EqualFold(ds.PasswordEncrypt, "") {
		ds.PasswordEncrypt = crypto.AesDefaultEncrypt(ds.PasswordValue, crypto.DefaultKey)
	}

	if strings.EqualFold(ds.ASMDBPasswdValue, "") {
		ds.ASMDBPasswdValue = crypto.AesDefaultDecrypt(ds.ASMDBPasswdEncrypt, crypto.DefaultKey)
	} else if strings.EqualFold(ds.ASMDBPasswdEncrypt, "") {
		ds.ASMDBPasswdEncrypt = crypto.AesDefaultEncrypt(ds.ASMDBPasswdValue, crypto.DefaultKey)
	}
	return ds, nil
}

func (rw DatasourceReadWrite) BatchDelete(ctx context.Context, dataSourceIDs []int) error {
	err := rw.DB(ctx).Delete(&Datasource{}, dataSourceIDs).Error
	return dbCommon.WrapDBError(err)
}

func (rw DatasourceReadWrite) List(ctx context.Context, page int, pageSize int, dataSourceName string, dbType string, connectionStatus string) ([]*Datasource, int64, error) {
	records := make([]*Datasource, pageSize)
	query := rw.DB(ctx).Model(&Datasource{})
	if strings.TrimSpace(dataSourceName) != "" {
		query = query.Where("datasource_name like '%" + dataSourceName + "%'")
	}
	if strings.TrimSpace(dbType) != "" {
		query = query.Where("db_type = ?", dbType)
	}
	if strings.TrimSpace(connectionStatus) != "" {
		query = query.Where("connection_status = ?", connectionStatus)
	}
	var count int64 = 0
	err := query.Order("datasource_id desc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("page query dataSources failed. page is %d, pageSize is %d", page, pageSize)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw DatasourceReadWrite) GetOracleSchemas(ctx context.Context, db *sql.DB) ([]*structs.SchemaInfo, error) {
	getSchemaSql := rw.genSelectSchemasSql(ctx, constants.DB_TYPE_ORACLE)
	if getSchemaSql == "" {
		return nil, errors.NewError(errors.TIMS_GET_FILTER_SCHEMAS_FAILED, "get oracle filter schema failed.")
	}
	log.Debugf("get oracle schema sql:%s", stringutil.RemoveSpecialLetterForLog(getSchemaSql))
	rows, err := db.QueryContext(ctx, getSchemaSql)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	schemas, err := processSchemaRows(rows, err)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return schemas, nil
}

func (rw DatasourceReadWrite) SearchOracleSchemas(ctx context.Context, db *sql.DB, schemaNameS string) ([]*structs.SchemaInfo, error) {
	getSchemaSql := rw.genSearchSchemasSql(ctx, constants.DB_TYPE_ORACLE, schemaNameS)
	if getSchemaSql == "" {
		return nil, errors.NewError(errors.TIMS_GET_FILTER_SCHEMAS_FAILED, "get oracle filter schema failed.")
	}
	log.Debugf("get oracle schema sql:%s", stringutil.RemoveSpecialLetterForLog(getSchemaSql))
	rows, err := db.QueryContext(ctx, getSchemaSql)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	schemas, err := processSchemaRows(rows, err)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return schemas, nil
}

func (rw DatasourceReadWrite) GetFilterSchemas(ctx context.Context, dbType string) ([]*SchemaFilter, error) {
	records := make([]*SchemaFilter, 4)
	err := rw.DB(ctx).Where("db_type = ?", dbType).Find(&records).Error
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw DatasourceReadWrite) GetOracleTables(ctx context.Context, db *sql.DB, schemas []string) ([]*structs.SchemaInfo, error) {
	sqlSchema := strings.ToUpper(genSchemaPartSql(schemas))
	sql := fmt.Sprintf(oracleObjectSql, sqlSchema, sqlSchema, sqlSchema, sqlSchema)
	rows, err := db.QueryContext(ctx, sql)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()
	objects, err := processObjectRows(rows, err)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return objects, nil
}

func (rw DatasourceReadWrite) GetOracleTablesByCSV(ctx context.Context, db *sql.DB, schemasTables []*structs.CsvSchemaTables) ([]*structs.SchemaInfo, error) {
	var buffer bytes.Buffer

	//构造一个schemaName为key，tableNameList为value的map
	schemasTablesMap := make(map[string][]string)
	for _, schemaTable := range schemasTables {
		schemasTablesMap[schemaTable.SchemaNameS] = append(schemasTablesMap[schemaTable.SchemaNameS], schemaTable.TableNameS)
	}

	count := 0
	//遍历map，构造sql
	for schemaName, tableNames := range schemasTablesMap {
		if count > 0 {
			buffer.WriteString("\nUNION ALL\n")
		}
		buffer.WriteString(buildSQL(schemaName, tableNames))
		count++
	}

	log.Debugf("GetOracleTablesByCSV.sql: %s", buffer.String())
	rows, err := db.QueryContext(ctx, buffer.String())
	if err != nil {
		log.Errorf("GetOracleTablesByCSV.db.QueryContext err: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()
	objects, err := processCSVObjectRows(rows, err)
	if err != nil {
		log.Errorf("GetOracleTablesByCSV.processCSVObjectRows err: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return objects, nil
}

// 组装 SQL 的函数
func buildSQL(schemaName string, tableNames []string) string {
	const maxInClauseSize = 1000
	var sqlParts []string

	// 每次获取不超过 maxInClauseSize 的表名分组
	for i := 0; i < len(tableNames); i += maxInClauseSize {
		end := i + maxInClauseSize
		if end > len(tableNames) {
			end = len(tableNames)
		}
		// 取当前分组
		group := tableNames[i:end]
		// 组装表名的字符串
		tableNamesInClause := "'" + strings.Join(group, "', '") + "'"
		// 拼接 SQL 语句的两部分
		part := fmt.Sprintf(`
			SELECT owner, table_name, 'TABLE' AS object_type 
			FROM dba_tables 
			WHERE owner = '%s' AND table_name IN (%s)
			UNION ALL
			SELECT owner, table_name, 'TABLE' AS object_type 
			FROM dba_part_tables 
			WHERE owner = '%s' AND table_name IN (%s)`, schemaName, tableNamesInClause, schemaName, tableNamesInClause)
		sqlParts = append(sqlParts, part)
	}

	// 最后将所有 SQL 语句片段用换行符连接
	return strings.Join(sqlParts, "\nUNION ALL\n")
}

func (rw DatasourceReadWrite) GetOracleSchemaCollation(ctx context.Context, db *sql.DB, schemaName string) (string, error) {
	querySQL := fmt.Sprintf(`SELECT DECODE(DEFAULT_COLLATION,
'USING_NLS_COMP',(SELECT VALUE from NLS_DATABASE_PARAMETERS WHERE PARAMETER = 'NLS_COMP'),DEFAULT_COLLATION) DEFAULT_COLLATION FROM DBA_USERS WHERE USERNAME = '%s'`, schemaName)
	_, res, err := Query(ctx, db, querySQL)
	if err != nil {
		return "", err
	}
	return res[0]["DEFAULT_COLLATION"], nil
}

func (rw DatasourceReadWrite) GetOracleSchemaTableType(ctx context.Context, db *sql.DB, schemaName string) (map[string]string, error) {
	tableMap := make(map[string]string)

	_, res, err := Query(ctx, db, fmt.Sprintf(`SELECT 
	f.TABLE_NAME TABLE_NAME,
	(
	CASE WHEN f.CLUSTER_NAME IS NOT NULL THEN 'CLUSTERED' ELSE
		CASE	WHEN f.IOT_TYPE = 'IOT' THEN
			CASE WHEN t.IOT_TYPE != 'IOT' THEN t.IOT_TYPE ELSE 'IOT' 
				END 
		ELSE
				CASE	
						WHEN f.PARTITIONED = 'YES' THEN 'PARTITIONED' ELSE
					CASE
							WHEN f.TEMPORARY = 'Y' THEN 
							DECODE(f.DURATION,'SYS$SESSION','SESSION TEMPORARY','SYS$TRANSACTION','TRANSACTION TEMPORARY')
							ELSE 'HEAP' 
					END 
				END 
		END 
	END ) TABLE_TYPE 
FROM
(SELECT tmp.owner,tmp.TABLE_NAME,tmp.CLUSTER_NAME,tmp.PARTITIONED,tmp.TEMPORARY,tmp.DURATION,tmp.IOT_TYPE
FROM
	DBA_TABLES tmp, DBA_TABLES w
WHERE tmp.owner=w.owner AND tmp.table_name = w.table_name AND tmp.owner  = '%s' AND (w.IOT_TYPE IS NUll OR w.IOT_TYPE='IOT')) f left join (
select owner,iot_name,iot_type from DBA_TABLES WHERE owner  = '%s')t 
ON f.owner = t.owner AND f.table_name = t.iot_name`, schemaName, schemaName))
	if err != nil {
		return tableMap, err
	}
	if len(res) == 0 {
		return tableMap, fmt.Errorf("oracle schema [%s] table type can't be null", schemaName)
	}

	for _, r := range res {
		if len(r) > 2 || len(r) == 0 || len(r) == 1 {
			return tableMap, fmt.Errorf("oracle schema [%s] table type values should be 2, result: %v", schemaName, r)
		}
		tableMap[r["TABLE_NAME"]] = r["TABLE_TYPE"]
	}
	return tableMap, nil
}

func Query(ctx context.Context, db *sql.DB, querySQL string) ([]string, []map[string]string, error) {
	var (
		cols []string
		res  []map[string]string
	)
	rows, err := db.QueryContext(ctx, querySQL)
	if err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query failed: [%v]", querySQL, err.Error())
	}
	defer rows.Close()

	//不确定字段通用查询，自动获取字段名称
	cols, err = rows.Columns()
	if err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query rows.Columns failed: [%v]", querySQL, err.Error())
	}

	values := make([][]byte, len(cols))
	scans := make([]interface{}, len(cols))
	for i := range values {
		scans[i] = &values[i]
	}

	for rows.Next() {
		err = rows.Scan(scans...)
		if err != nil {
			return cols, res, fmt.Errorf("general sql [%v] query rows.Scan failed: [%v]", querySQL, err.Error())
		}

		row := make(map[string]string)
		for k, v := range values {
			// Oracle/Mysql 对于 'NULL' 统一字符 NULL 处理，查询出来转成 NULL,所以需要判断处理
			// 查询字段值 NULL
			// 如果字段值 = NULLABLE 则表示值是 NULL
			// 如果字段值 = "" 则表示值是空字符串
			// 如果字段值 = 'NULL' 则表示值是 NULL 字符串
			// 如果字段值 = 'null' 则表示值是 null 字符串
			if v == nil {
				row[cols[k]] = "NULLABLE"
			} else {
				// 处理空字符串以及其他值情况
				// 数据统一 string 格式显示
				row[cols[k]] = string(v)
			}
		}
		res = append(res, row)
	}

	if err = rows.Err(); err != nil {
		return cols, res, fmt.Errorf("general sql [%v] query rows.Next failed: [%v]", querySQL, err.Error())
	}
	return cols, res, nil
}

func (rw DatasourceReadWrite) GetMysqlTables(ctx context.Context, db *sql.DB, schemas []string) ([]*structs.SchemaInfo, error) {
	sqlSchema := genSchemaPartSql(schemas)
	sql := fmt.Sprintf(mysqlTableSql, sqlSchema)
	rows, err := db.QueryContext(ctx, sql)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()
	objects, err := processMySQLObjectRows(rows, err)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return objects, nil
}

var oracleSchemasSql = "SELECT username owner, account_status FROM dba_users where username not in (%s)  and username in (SELECT DISTINCT(OWNER) FROM all_objects ) ORDER BY username"
var mysqlSchemasSql = "select schema_name owner, 'OPEN' account_status from INFORMATION_SCHEMA.SCHEMATA where schema_name not in (%s) order by schema_name"

var oracleSchemasSearchSql = "SELECT username owner, account_status FROM dba_users where username not in (%s)  and username in (SELECT DISTINCT(OWNER) FROM all_objects ) and upper(username) like %s ORDER BY username"

// var oracleObjectSql = "select a.owner,a.object_type, a.object_name from all_objects a where a.owner in (%s) and a.object_type in (%s) order by a.owner,a.object_type,a.object_name"
var oracleObjectSql = `select /*+ rule parallel(8) */ 
m.owner,'TABLE' AS object_type,m.table_name,m.UK,m.PK,n.partitioning_type,n.partition_count,n.subpartitioning_type,n.subpartition_count,0 AS table_sizeM
from 
(
select distinct a.owner,
        a.table_name,
        case
         when exists (select c.OWNER, c.TABLE_NAME, c.UNIQUENESS
             from dba_indexes c
             where UPPER(c.OWNER) in (%s)
              and c.OWNER = a.owner
              and c.table_name = a.table_name
              and c.UNIQUENESS = 'UNIQUE') then
          'Y'
         else
          'N'
        end as "UK",
        case
         when exists
          (select e.OWNER, e.TABLE_NAME, e.CONSTRAINT_TYPE
             from DBA_cons_columns d, DBA_constraints e
             where d.constraint_name = e.constraint_name
              and d.table_name = e.table_name
              and d.OWNER = e.owner
              and UPPER(d.OWNER) in (%s)
              and d.table_name = a.table_name and e.CONSTRAINT_TYPE = 'P') then
          'Y'
         else
          'N'
        end as "PK"
 from dba_tables a
 where UPPER(a.owner) in (%s)
) m left join 
(
select distinct a.owner,
        a.table_name ,
        a.partitioning_type,
        a.partition_count,
        a.subpartitioning_type,
        0 as subpartition_count
 from dba_part_tables a, dba_tab_partitions b
 where a.owner = b.table_owner
  and a.table_name = b.table_name
  and UPPER(a.owner) in (%s)
) n on m.owner=n.owner and m.table_name=n.table_name 
and m.table_name not like 'BIN$%%'
and m.table_name not like 'MLOG$_%%'`
var mysqlTableSql = "select p.table_schema, 'table' as obj_type, p.table_name from information_schema.tables p where p.table_schema in (%s) order by p.table_schema, p.table_name"

var oracleSchemaTableDDLSql = "select replace(dbms_metadata.get_ddl('%s','%s','%s'),'\"') from dual"

func (rw DatasourceReadWrite) genSelectSchemasSql(ctx context.Context, dbType string) string {
	schemaFilters, err := rw.GetFilterSchemas(ctx, dbType)
	if err != nil {
		log.Errorf("get filter schemas failed. dbType: %s, err:%v", dbType, err)
		return ""
	}
	var builder strings.Builder
	for i, schema := range schemaFilters {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString("'")
		builder.WriteString(schema.SchemaName)
		builder.WriteString("'")
	}
	if dbType == constants.DB_TYPE_ORACLE || dbType == constants.DB_TYPE_ORACLE_ADG {
		return fmt.Sprintf(oracleSchemasSql, builder.String())
	} else if dbType == constants.DB_TYPE_MYSQL {
		return fmt.Sprintf(mysqlSchemasSql, builder.String())
	} else {
		log.Errorf("unknown data base type %s", dbType)
		return ""
	}
}

func (rw DatasourceReadWrite) genSearchSchemasSql(ctx context.Context, dbType string, schemaNameS string) string {
	schemaFilters, err := rw.GetFilterSchemas(ctx, dbType)
	if err != nil {
		log.Errorf("get filter schemas failed. dbType: %s, err:%v", dbType, err)
		return ""
	}
	var builder strings.Builder
	for i, schema := range schemaFilters {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString("'")
		builder.WriteString(schema.SchemaName)
		builder.WriteString("'")
	}
	searchSchema := fmt.Sprintf("'%%%s%%'", schemaNameS)
	if dbType == constants.DB_TYPE_ORACLE || dbType == constants.DB_TYPE_ORACLE_ADG {
		return fmt.Sprintf(oracleSchemasSearchSql, builder.String(), searchSchema)
	} else if dbType == constants.DB_TYPE_MYSQL {
		return fmt.Sprintf(mysqlSchemasSql, builder.String())
	} else {
		log.Errorf("unknown data base type %s", dbType)
		return ""
	}
}

func (rw DatasourceReadWrite) GetOracleSchemaTableOriginDDL(ctx context.Context, tableType, schemaName, tableName string, db *sql.DB) (string, error) {
	ddlSql := fmt.Sprintf(oracleSchemaTableDDLSql, tableType, tableName, schemaName)
	log.Debugf("GetOracleSchemaTableOriginDDL.ddlSql: %s", ddlSql)
	rows, err := db.QueryContext(ctx, ddlSql)
	if err != nil {
		log.Errorf("GetOracleSchemaTableOriginDDL.db.QueryContext err: %v", err)
		return "", err
	}
	defer rows.Close()

	var ddl string
	for rows.Next() {
		if err = rows.Scan(&ddl); err != nil {
			return ddl, fmt.Errorf("get oracle schema table origin ddl scan failed: %v", err)
		}
	}
	log.Debugf("GetOracleSchemaTableOriginDDL.ddl: %s", ddl)

	if err = rows.Close(); err != nil {
		return ddl, fmt.Errorf("get oracle schema table origin ddl close failed: %v, ddlSql:%v", err, ddlSql)
	}
	if err = rows.Err(); err != nil {
		return ddl, fmt.Errorf("get oracle schema table origin ddl Err failed: %v, ddlSql:%v", err, ddlSql)
	}
	return ddl, nil
}

func (rw DatasourceReadWrite) GetOracleDDL(ctx context.Context, schemaName, objectName, objectType string, db *sql.DB) (string, error) {
	ddlSql := fmt.Sprintf("select replace(dbms_metadata.get_ddl('%s','%s','%s'),'\"') from dual", objectType, objectName, schemaName)
	log.Debugf("GetOracleDDL.ddlSql: %s", ddlSql)
	rows, err := db.QueryContext(ctx, ddlSql)
	if err != nil {
		log.Errorf("GetOracleSchemaTableOriginDDL.db.QueryContext err: %v", err)
		return "", err
	}
	defer rows.Close()

	var ddl string
	for rows.Next() {
		if err = rows.Scan(&ddl); err != nil {
			return ddl, fmt.Errorf("get oracle ddl scan failed: %v", err)
		}
	}
	log.Debugf("GetOracleDDL.ddl: %s", ddl)

	if err = rows.Close(); err != nil {
		return ddl, fmt.Errorf("get oracle ddl close failed: %v, ddlSql:%v", err, ddlSql)
	}
	if err = rows.Err(); err != nil {
		return ddl, fmt.Errorf("get oracle ddl Err failed: %v, ddlSql:%v", err, ddlSql)
	}
	return ddl, nil
}

func (rw DatasourceReadWrite) GetMysqlSchemas(ctx context.Context, db *sql.DB) ([]*structs.SchemaInfo, error) {
	getSchemaSql := rw.genSelectSchemasSql(ctx, constants.DB_TYPE_MYSQL)
	if getSchemaSql == "" {
		return nil, errors.NewError(errors.TIMS_GET_FILTER_SCHEMAS_FAILED, "get mysql filter schema failed.")
	}
	log.Debugf("get mysql schema sql:%s", stringutil.RemoveSpecialLetterForLog(getSchemaSql))
	rows, err := db.QueryContext(ctx, getSchemaSql)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	schemas, err := processSchemaRows(rows, err)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return schemas, nil
}

func (rw DatasourceReadWrite) ListHost(ctx context.Context, page int, pageSize int, hostIp, hostName, osName string) ([]*OpMachineConf, int64, error) {
	records := make([]*OpMachineConf, pageSize)
	query := rw.DB(ctx).Model(&OpMachineConf{})
	if strings.TrimSpace(hostIp) != "" {
		query = query.Where("host_ip like '%" + hostIp + "%'")
	}
	if strings.TrimSpace(hostName) != "" {
		query = query.Where("host_name like '%" + hostName + "%'")
	}
	if strings.TrimSpace(osName) != "" {
		query = query.Where("os_name like '%" + osName + "%'")
	}
	var count int64 = 0
	err := query.Order("mac_id desc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("page query hosts failed. page is %d, pageSize is %d", page, pageSize)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw DatasourceReadWrite) SaveHost(ctx context.Context, host *OpMachineConf) (*OpMachineConf, error) {
	err := rw.DB(ctx).Save(host).Error
	if err != nil {
		log.Errorf("save host to db failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("save host to db successfully, %v", host)
	return host, nil
}

func (rw DatasourceReadWrite) DeleteHost(ctx context.Context, macId int) error {
	err := rw.DB(ctx).Unscoped().Delete(&OpMachineConf{}, macId).Error
	return dbCommon.WrapDBError(err)
}

func (rw DatasourceReadWrite) GetHost(ctx context.Context, macId int) (*OpMachineConf, error) {
	host := &OpMachineConf{}
	err := rw.DB(ctx).First(host, "mac_id = ?", macId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query host %d not found", macId)
	} else if err != nil {
		log.Errorf("get host failed. macId is %d, %v", macId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return host, nil
}
func (rw DatasourceReadWrite) GetHostByIP(ctx context.Context, ip string) (*OpMachineConf, error) {
	host := &OpMachineConf{}
	err := rw.DB(ctx).First(host, "host_ip = ?", ip).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query host %s not found", ip)
	} else if err != nil {
		log.Errorf("get host failed. ip is %s, %v", ip, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return host, nil
}

func processSchemaRows(rows *sql.Rows, err error) ([]*structs.SchemaInfo, error) {
	schemas := make([]*structs.SchemaInfo, 0, 0)
	preSchemaName := ""
	var schema *structs.SchemaInfo
	for rows.Next() {
		var owner, accountStatus, objectType sql.NullString
		var count sql.NullInt64
		err = rows.Scan(&owner, &accountStatus)
		if err != nil {
			return nil, dbCommon.WrapDBError(err)
		}
		if preSchemaName != owner.String {
			schema = &structs.SchemaInfo{SchemaName: owner.String, AccountStatus: accountStatus.String, ObjectTypes: make([]*structs.ObjectType, 0, 0)}
			schemas = append(schemas, schema)
			preSchemaName = owner.String
		}
		schema.ObjectTypes = append(schema.ObjectTypes, &structs.ObjectType{TypeName: objectType.String, ObjectCount: count.Int64})
	}
	return schemas, nil
}

func processCSVObjectRows(rows *sql.Rows, err error) ([]*structs.SchemaInfo, error) {
	objects := make([]*structs.SchemaInfo, 0, 0)
	preSchemaName := ""
	preObjectType := ""
	var schema *structs.SchemaInfo
	var objectType *structs.ObjectType
	for rows.Next() {
		var owner, pObjectType, objectName sql.NullString
		err = rows.Scan(&owner, &objectName, &pObjectType)
		if err != nil {
			return nil, dbCommon.WrapDBError(err)
		}
		if preSchemaName != owner.String {
			schema = &structs.SchemaInfo{SchemaName: owner.String, ObjectTypes: make([]*structs.ObjectType, 0, 0)}
			objects = append(objects, schema)
			preSchemaName = owner.String
			preObjectType = ""
		}
		if preObjectType != pObjectType.String {
			objectType = &structs.ObjectType{TypeName: pObjectType.String, ObjectNames: make([]string, 0, 0), Tables: make([]structs.TableInfo, 0, 0)}
			schema.ObjectTypes = append(schema.ObjectTypes, objectType)
			preObjectType = pObjectType.String
		}
		objectType.ObjectNames = append(objectType.ObjectNames, objectName.String)
		objectType.Tables = append(objectType.Tables, structs.TableInfo{
			TableName: objectName.String,
		})
	}
	return objects, nil
}

func processObjectRows(rows *sql.Rows, err error) ([]*structs.SchemaInfo, error) {
	objects := make([]*structs.SchemaInfo, 0, 0)
	preSchemaName := ""
	preObjectType := ""
	var schema *structs.SchemaInfo
	var objectType *structs.ObjectType
	for rows.Next() {
		var owner, pObjectType, objectName, partitioningType, subpartitioningType, uk, pk sql.NullString
		var partitionCount, subpartitionCount sql.NullInt64
		var tableSizeM sql.NullFloat64
		err = rows.Scan(&owner, &pObjectType, &objectName, &uk, &pk, &partitioningType, &partitionCount, &subpartitioningType, &subpartitionCount, &tableSizeM)
		if err != nil {
			return nil, dbCommon.WrapDBError(err)
		}
		if preSchemaName != owner.String {
			schema = &structs.SchemaInfo{SchemaName: owner.String, ObjectTypes: make([]*structs.ObjectType, 0, 0)}
			objects = append(objects, schema)
			preSchemaName = owner.String
			preObjectType = ""
		}
		if preObjectType != pObjectType.String {
			objectType = &structs.ObjectType{TypeName: pObjectType.String, ObjectNames: make([]string, 0, 0), Tables: make([]structs.TableInfo, 0, 0)}
			schema.ObjectTypes = append(schema.ObjectTypes, objectType)
			preObjectType = pObjectType.String
		}
		objectType.ObjectNames = append(objectType.ObjectNames, objectName.String)
		objectType.Tables = append(objectType.Tables, structs.TableInfo{
			TableName:           objectName.String,
			PartitioningType:    partitioningType.String,
			PartitionCount:      int(partitionCount.Int64),
			SubPartitioningType: subpartitioningType.String,
			SubPartitionCount:   int(subpartitionCount.Int64),
			Pk:                  pk.String,
			Uk:                  uk.String,
			TableSizeM:          tableSizeM.Float64,
		})
	}
	return objects, nil
}

func processMySQLObjectRows(rows *sql.Rows, err error) ([]*structs.SchemaInfo, error) {
	objects := make([]*structs.SchemaInfo, 0, 0)
	preSchemaName := ""
	preObjectType := ""
	var schema *structs.SchemaInfo
	var objectType *structs.ObjectType
	for rows.Next() {
		var owner, pObjectType, objectName, partitioningType, subpartitioningType, uk, pk sql.NullString
		var partitionCount, subpartitionCount sql.NullInt64
		var tableSizeM sql.NullFloat64
		err = rows.Scan(&owner, &pObjectType, &objectName)
		if err != nil {
			return nil, dbCommon.WrapDBError(err)
		}
		if preSchemaName != owner.String {
			schema = &structs.SchemaInfo{SchemaName: owner.String, ObjectTypes: make([]*structs.ObjectType, 0, 0)}
			objects = append(objects, schema)
			preSchemaName = owner.String
			preObjectType = ""
		}
		if preObjectType != pObjectType.String {
			objectType = &structs.ObjectType{TypeName: pObjectType.String, ObjectNames: make([]string, 0, 0), Tables: make([]structs.TableInfo, 0, 0)}
			schema.ObjectTypes = append(schema.ObjectTypes, objectType)
			preObjectType = pObjectType.String
		}
		objectType.ObjectNames = append(objectType.ObjectNames, objectName.String)
		objectType.Tables = append(objectType.Tables, structs.TableInfo{
			TableName:           objectName.String,
			PartitioningType:    partitioningType.String,
			PartitionCount:      int(partitionCount.Int64),
			SubPartitioningType: subpartitioningType.String,
			SubPartitionCount:   int(subpartitionCount.Int64),
			Pk:                  pk.String,
			Uk:                  uk.String,
			TableSizeM:          tableSizeM.Float64,
		})
	}
	return objects, nil
}

func genSchemaPartSql(schemas []string) string {
	var builder strings.Builder
	for i, schema := range schemas {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString("'")
		builder.WriteString(schema)
		builder.WriteString("'")
	}
	return builder.String()
}

func (rw DatasourceReadWrite) GetOracleTablePartitions(ctx context.Context, db *sql.DB, schemaName, tableName string) ([]*structs.TablePartitions, error) {
	sql := fmt.Sprintf(`select a.table_name, a.partition_name from dba_tab_partitions a where a.table_name='%s' and a.table_owner='%s' order by a.partition_position`, tableName, schemaName)

	log.Infof("GetOracleTablePartitions.sql: %s", sql)
	rows, err := db.QueryContext(ctx, sql)
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}

	defer rows.Close()

	objects := make([]*structs.TablePartitions, 0, 0)

	for rows.Next() {
		var mTableName string
		var mPartitionName string
		err = rows.Scan(&mTableName, &mPartitionName)
		if err != nil {
			return nil, dbCommon.WrapDBError(err)
		}
		objects = append(objects, &structs.TablePartitions{TableName: mTableName, PartitionName: mPartitionName})
	}
	if err != nil {
		return nil, dbCommon.WrapDBError(err)
	}
	return objects, nil
}

func (rw DatasourceReadWrite) GetOracleDependencies(ctx context.Context, db *sql.DB) ([]*Dependency, error) {
	// DBA_DEPENDENCIES 依旧只是一个数据字典视图，底层表（SYS.DEP$ 等）在数据字典区都带有 NOPARALLEL 属性，所以给视图本身下 /*+ PARALLEL */ 提示通常不会被采纳
	queryDependenciesSQL := `SELECT *
				FROM dba_dependencies a
				WHERE a.name NOT LIKE 'BIN$%'`
	startTime := time.Now()
	log.Infof("query oracle package/procedure/function/trigger dependency sql, startTime:%v, sql:%s", startTime, queryDependenciesSQL)
	_, res, queryErr := oracle.Query(ctx, db, queryDependenciesSQL)
	if queryErr != nil {
		return nil, queryErr
	}
	finishTime := time.Now()
	log.Infof("query oracle package/procedure/function/trigger dependency sql, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))

	dependencies := make([]*Dependency, 0, len(res))
	for _, item := range res {
		referencedName := item["REFERENCED_NAME"]
		referencedOwner := item["REFERENCED_OWNER"]
		referencedType := item["REFERENCED_TYPE"]

		dependencies = append(dependencies, &Dependency{
			SchemaName:         item["OWNER"],
			Owner:              item["OWNER"],
			Name:               item["NAME"],
			Type:               item["TYPE"],
			ReferencedOwner:    referencedOwner,
			ReferencedName:     referencedName,
			ReferencedType:     referencedType,
			ReferencedLinkName: item["REFERENCED_LINK_NAME"],
			DependencyType:     item["DEPENDENCY_TYPE"],
		})
	}
	return dependencies, nil
}

func (rw DatasourceReadWrite) GetOraclePackageBodies(ctx context.Context, db *sql.DB, targetSchemas []string) ([]OraclePackageDefinition, error) {
	queryDependenciesSQL := `SELECT OWNER, NAME AS PACKAGE_NAME, LINE, TEXT
FROM ALL_SOURCE
WHERE TYPE = 'PACKAGE BODY'
  AND OWNER IN (${SCHAMES})
ORDER BY OWNER, NAME, LINE`

	queryDependenciesSQL = strings.ReplaceAll(queryDependenciesSQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(targetSchemas, "','")))

	startTime := time.Now()
	log.Infof("query oracle package body sql, startTime:%v, sql:%s", startTime, queryDependenciesSQL)
	_, res, queryErr := oracle.Query(ctx, db, queryDependenciesSQL)
	if queryErr != nil {
		return nil, queryErr
	}
	finishTime := time.Now()
	log.Infof("query oracle package body sql, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))

	packageDefinitionMap := make(map[structs.OracleObjectKey]*bytes.Buffer)
	packageDefinitions := make([]OraclePackageDefinition, 0)

	for _, item := range res {
		schemaName := item["OWNER"]
		packageName := item["PACKAGE_NAME"]
		text := item["TEXT"]

		oraclePackage := structs.OracleObjectKey{
			SchemaName: schemaName,
			ObjectName: packageName,
		}
		if _, ok := packageDefinitionMap[oraclePackage]; !ok {
			packageDefinitionMap[oraclePackage] = &bytes.Buffer{}
		}
		packageDefinitionMap[oraclePackage].WriteString(text)
	}
	for oraclePackage, definition := range packageDefinitionMap {
		packageDefinitions = append(packageDefinitions, OraclePackageDefinition{
			SchemaName:  oraclePackage.SchemaName,
			PackageName: oraclePackage.ObjectName,
			Text:        definition.String(),
		})
	}

	packageNames := lo.Map(packageDefinitions, func(item OraclePackageDefinition, _ int) string {
		return item.SchemaName + "." + item.PackageName
	})

	log.Infof("get oracle package bodies, targetSchemas:%v, packageDefinitionsLen:%d, packageNames:%v", strings.Join(targetSchemas, ","), len(packageDefinitions),
		strings.Join(packageNames, ","))

	return packageDefinitions, nil
}

func (rw DatasourceReadWrite) GetOracleDefinition(ctx context.Context, db *sql.DB, schemaName, objectType, objectName string, objectPackageName string) (*OraclePackageDefinition, error) {

	var highlightLine string

	if objectPackageName != "" {
		highlightLine = objectType + " " + objectName
		objectName = objectPackageName
		objectType = "PACKAGE BODY"
	}

	querySQL := `SELECT OWNER, NAME, LINE, TEXT
FROM ALL_SOURCE
WHERE OWNER = '${SCHAME_NAME}'
  AND TYPE = '${OBJECT_TYPE}'
  AND NAME = '${OBJECT_NAME}'
ORDER BY OWNER, NAME, LINE`

	querySQL = strings.ReplaceAll(querySQL, "${SCHAME_NAME}", schemaName)
	querySQL = strings.ReplaceAll(querySQL, "${OBJECT_TYPE}", objectType)
	querySQL = strings.ReplaceAll(querySQL, "${OBJECT_NAME}", objectName)

	startTime := time.Now()
	log.Infof("query oracle object definition sql, startTime:%v, sql:%s", startTime, querySQL)
	_, res, queryErr := oracle.Query(ctx, db, querySQL)
	if queryErr != nil {
		return nil, queryErr
	}
	finishTime := time.Now()
	log.Infof("query oracle object body definition, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))

	definitionMap := make(map[structs.OracleObjectKey]*bytes.Buffer)
	definitions := make([]*OraclePackageDefinition, 0)

	for _, item := range res {
		owner := item["OWNER"]
		name := item["NAME"]
		text := item["TEXT"]

		oraclePackage := structs.OracleObjectKey{
			SchemaName: owner,
			ObjectName: name,
		}
		if _, ok := definitionMap[oraclePackage]; !ok {
			definitionMap[oraclePackage] = &bytes.Buffer{}
		}
		definitionMap[oraclePackage].WriteString(text)
	}
	for oraclePackage, definition := range definitionMap {
		definitions = append(definitions, &OraclePackageDefinition{
			SchemaName:  oraclePackage.SchemaName,
			PackageName: oraclePackage.ObjectName,
			Text:        definition.String(),
		})
	}

	if len(definitions) <= 0 || len(definitions) >= 2 {
		return nil, errors.NewError(errors.TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED, "definition not found or more than one: "+schemaName+"."+objectName)
	}

	ret := definitions[0]
	ret.HighlightLine = highlightLine

	return ret, nil
}

func (rw DatasourceReadWrite) GetOracleObjectStatus(ctx context.Context, db *sql.DB) ([]OracleObject, error) {
	startTime := time.Now()
	log.Infof("query oracle object status sql, startTime:%v", startTime)
	queryInvalidObjectSQL := `SELECT OWNER,OBJECT_NAME,OBJECT_TYPE,STATUS FROM DBA_OBJECTS`
	_, res, queryErr := oracle.Query(ctx, db, queryInvalidObjectSQL)
	finishTime := time.Now()
	log.Infof("query oracle object status sql, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))
	if queryErr != nil {
		return nil, queryErr
	}

	oracleObjects := make([]OracleObject, 0, len(res))
	for _, item := range res {
		oracleObjects = append(oracleObjects, OracleObject{
			OwnerName:  item["OWNER"],
			ObjectName: item["OBJECT_NAME"],
			ObjectType: item["OBJECT_TYPE"],
			Status:     item["STATUS"],
		})
	}
	return oracleObjects, nil
}

func (rw DatasourceReadWrite) GetOracleObjectStatusBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObject, error) {
	startTime := time.Now()
	log.Infof("query oracle object status sql, startTime:%v", startTime)
	queryInvalidObjectSQL := `SELECT OWNER,OBJECT_NAME,OBJECT_TYPE,STATUS FROM DBA_OBJECTS WHERE OWNER IN (${SCHAMES}) AND OBJECT_TYPE IN (${TYPES})`
	queryInvalidObjectSQL = strings.ReplaceAll(queryInvalidObjectSQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemas, "','")))
	queryInvalidObjectSQL = strings.ReplaceAll(queryInvalidObjectSQL, "${TYPES}", fmt.Sprintf("'%s'", strings.Join(types, "','")))
	_, res, queryErr := oracle.Query(ctx, db, queryInvalidObjectSQL)
	finishTime := time.Now()
	log.Infof("query oracle object status sql, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))
	if queryErr != nil {
		return nil, queryErr
	}

	oracleObjects := make([]OracleObject, 0, len(res))
	for _, item := range res {
		oracleObjects = append(oracleObjects, OracleObject{
			OwnerName:  item["OWNER"],
			ObjectName: item["OBJECT_NAME"],
			ObjectType: item["OBJECT_TYPE"],
			Status:     item["STATUS"],
		})
	}
	return oracleObjects, nil
}

func (rw DatasourceReadWrite) GetOracleObjectDefinitionsBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObjectDefinition, error) {
	// Step 1: Get lightweight object list first to check dataset size
	objectMetas, err := rw.GetOracleObjectListBySchemas(ctx, db, schemas, types)
	if err != nil {
		log.Errorf("Failed to get object list for optimization: %v, falling back to original method", err)
		return nil, err
	}

	// For small datasets, use original method
	if len(objectMetas) <= 50 {
		log.Infof("Small dataset (%d objects), using original method", len(objectMetas))
		return rw.getOracleObjectDefinitionsOriginal(ctx, db, schemas, types)
	}

	log.Infof("Large dataset (%d objects), using optimized batch processing", len(objectMetas))

	// Step 2: Use optimized batch processing with default configuration
	return rw.fetchObjectDefinitionsWithPool(ctx, db, objectMetas, 5, 32, true) // poolSize=5, batchSize=20, enableProgress=true
}

// getOracleObjectDefinitionsOriginal implements the original non-optimized query method
func (rw DatasourceReadWrite) getOracleObjectDefinitionsOriginal(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObjectDefinition, error) {
	querySQL := `SELECT /*+ parallel 4 */ OWNER, TYPE, NAME, LINE, TEXT
	FROM DBA_SOURCE
	WHERE OWNER IN (${SCHAMES})
	  AND TYPE IN (${TYPES})
	ORDER BY OWNER, TYPE,NAME, LINE`

	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${TYPES}", fmt.Sprintf("'%s'", strings.Join(types, "','")))

	startTime := time.Now()
	log.Infof("query oracle source code sql (original method), startTime:%v, sql:%s", startTime, querySQL)
	_, res, queryErr := oracle.Query(ctx, db, querySQL)
	if queryErr != nil {
		return nil, queryErr
	}
	finishTime := time.Now()
	log.Infof("query oracle source code sql (original method), endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))

	sourceDefinitionMap := make(map[structs.OracleObjectKey]*bytes.Buffer)
	sourceDefinitions := make([]OracleObjectDefinition, 0)

	for _, item := range res {
		schemaName := item["OWNER"]
		objectName := item["NAME"]
		objectType := item["TYPE"]
		text := item["TEXT"]

		objectKey := structs.OracleObjectKey{
			SchemaName: schemaName,
			ObjectName: objectName,
			ObjectType: objectType,
		}
		if _, ok := sourceDefinitionMap[objectKey]; !ok {
			sourceDefinitionMap[objectKey] = &bytes.Buffer{}
		}
		sourceDefinitionMap[objectKey].WriteString(text)
	}

	for objectKey, definition := range sourceDefinitionMap {
		sourceDefinitions = append(sourceDefinitions, OracleObjectDefinition{
			OwnerName:  objectKey.SchemaName,
			ObjectName: objectKey.ObjectName,
			ObjectType: objectKey.ObjectType,
			AllText:    prettyCreateDefinition(definition.String()),
		})
	}

	return sourceDefinitions, nil
}

// GetOracleObjectListBySchemas retrieves lightweight object metadata for batch processing optimization
// This method queries DBA_OBJECTS for object names and types only, avoiding the heavy ALL_SOURCE query
func (rw DatasourceReadWrite) GetOracleObjectListBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObjectMeta, error) {
	querySQL := `
	SELECT /*+ parallel 8 */
		OWNER, OBJECT_NAME, OBJECT_TYPE 
	FROM DBA_OBJECTS
	WHERE OWNER IN (${SCHEMAS}) 
	  AND OBJECT_TYPE IN (${TYPES})
	  AND STATUS = 'VALID'
	ORDER BY OWNER, OBJECT_TYPE, OBJECT_NAME`

	querySQL = strings.ReplaceAll(querySQL, "${SCHEMAS}", fmt.Sprintf("'%s'", strings.Join(schemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${TYPES}", fmt.Sprintf("'%s'", strings.Join(types, "','")))

	startTime := time.Now()
	log.Infof("query oracle object list sql, startTime:%v, sql:%s", startTime, querySQL)
	_, res, queryErr := oracle.Query(ctx, db, querySQL)
	if queryErr != nil {
		return nil, queryErr
	}
	finishTime := time.Now()
	log.Infof("query oracle object list sql, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))

	objectMetas := make([]OracleObjectMeta, 0, len(res))
	for _, item := range res {
		objectMetas = append(objectMetas, OracleObjectMeta{
			OwnerName:  item["OWNER"],
			ObjectName: item["OBJECT_NAME"],
			ObjectType: item["OBJECT_TYPE"],
		})
	}

	log.Infof("Found %d objects across %d schemas for batch processing", len(objectMetas), len(schemas))
	return objectMetas, nil
}

// GetOracleObjectDefinitionsBatch retrieves source code for a specific batch of objects
// Optimized for SCHEMA+TYPE+NAMES pattern to leverage Oracle indexes efficiently
func (rw DatasourceReadWrite) GetOracleObjectDefinitionsBatch(ctx context.Context, db *sql.DB, schema, objectType string, objectNames []string) ([]OracleObjectDefinition, error) {
	if len(objectNames) == 0 {
		return []OracleObjectDefinition{}, nil
	}

	// Build optimized query for single schema+type with multiple names
	namePlaceholders := make([]string, len(objectNames))
	for i, name := range objectNames {
		namePlaceholders[i] = fmt.Sprintf("'%s'", name)
	}

	querySQL := fmt.Sprintf(`
	SELECT /*+ parallel 4 */ 
		OWNER, TYPE, NAME, LINE, TEXT
	FROM ALL_SOURCE
	WHERE OWNER = '%s' 
	  AND TYPE = '%s' 
	  AND NAME IN (%s)
	ORDER BY NAME, LINE`, schema, objectType, strings.Join(namePlaceholders, ","))

	startTime := time.Now()
	log.Debugf("query oracle batch definitions sql, schema:%s, type:%s, objects:%d, sql:%s", schema, objectType, len(objectNames), querySQL)
	_, res, queryErr := oracle.Query(ctx, db, querySQL)
	if queryErr != nil {
		return nil, queryErr
	}
	finishTime := time.Now()
	log.Debugf("query oracle batch definitions sql completed, costTime:%v, resultRows:%d", finishTime.Sub(startTime), len(res))

	// Assemble the results similar to the original method
	sourceDefinitionMap := make(map[structs.OracleObjectKey]*bytes.Buffer)
	sourceDefinitions := make([]OracleObjectDefinition, 0)

	for _, item := range res {
		schemaName := item["OWNER"]
		objectName := item["NAME"]
		objectType := item["TYPE"]
		text := item["TEXT"]

		objectKey := structs.OracleObjectKey{
			SchemaName: schemaName,
			ObjectName: objectName,
			ObjectType: objectType,
		}
		if _, ok := sourceDefinitionMap[objectKey]; !ok {
			sourceDefinitionMap[objectKey] = &bytes.Buffer{}
		}
		sourceDefinitionMap[objectKey].WriteString(text)
	}

	for objectKey, definition := range sourceDefinitionMap {
		sourceDefinitions = append(sourceDefinitions, OracleObjectDefinition{
			OwnerName:  objectKey.SchemaName,
			ObjectName: objectKey.ObjectName,
			ObjectType: objectKey.ObjectType,
			AllText:    prettyCreateDefinition(definition.String()),
		})
	}

	return sourceDefinitions, nil
}

// fetchObjectDefinitionsWithPool processes object definitions using goroutine pool for concurrent batch processing
func (rw DatasourceReadWrite) fetchObjectDefinitionsWithPool(ctx context.Context, db *sql.DB, objectMetas []OracleObjectMeta, poolSize, batchSize int, enableProgress bool) ([]OracleObjectDefinition, error) {

	// Step 1: Group objects by SCHEMA+TYPE+NUM_LIMIT
	batches := rw.groupObjectsBySchemaType(objectMetas, batchSize)

	if enableProgress {
		log.Infof("Created %d batches for processing %d objects (pool size: %d, batch size: %d)",
			len(batches), len(objectMetas), poolSize, batchSize)
	}

	if len(batches) == 0 {
		return []OracleObjectDefinition{}, nil
	}

	// Step 2: Create ants goroutine pool
	results := make(chan BatchResult, len(batches))
	pool, err := ants.NewPoolWithFunc(poolSize, func(payload interface{}) {
		batch := payload.(*ObjectBatchGroup)
		rw.processBatch(ctx, db, batch, results, enableProgress)
	})
	if err != nil {
		log.Errorf("Failed to create goroutine pool: %v", err)
		return nil, err
	}
	defer pool.Release()

	// Step 3: Submit all batches to pool
	for i := range batches {
		err := pool.Invoke(&batches[i])
		if err != nil {
			log.Errorf("Failed to submit batch %d to pool: %v", batches[i].BatchIndex, err)
		}
	}

	// Step 4: Collect results
	return rw.collectBatchResults(results, len(batches), enableProgress)
}

// groupObjectsBySchemaType groups objects using SCHEMA+TYPE+NUM_LIMIT strategy for optimal batch processing
func (rw DatasourceReadWrite) groupObjectsBySchemaType(objectMetas []OracleObjectMeta, batchSize int) []ObjectBatchGroup {
	// Critical fix: Prevent infinite loop and invalid batch sizes
	if batchSize <= 0 {
		batchSize = 1
	}

	// Handle empty input
	if len(objectMetas) == 0 {
		return []ObjectBatchGroup{}
	}

	// Use safer separator to avoid conflicts with user data containing "|"
	const separator = "\x00" // null character as separator

	// Group by SCHEMA+TYPE first
	groupMap := make(map[string][]OracleObjectMeta)
	for _, obj := range objectMetas {
		// Input validation: skip objects with empty schema or type names
		if strings.TrimSpace(obj.OwnerName) == "" || strings.TrimSpace(obj.ObjectType) == "" {
			continue
		}

		key := obj.OwnerName + separator + obj.ObjectType
		groupMap[key] = append(groupMap[key], obj)
	}

	// Split each group into batches of batchSize
	var batches []ObjectBatchGroup
	batchIndex := 0

	for key, objects := range groupMap {
		parts := strings.Split(key, separator)
		// Safe string splitting: ensure we have exactly 2 parts
		if len(parts) != 2 {
			continue
		}
		schema, objType := parts[0], parts[1]

		// Split objects of same SCHEMA+TYPE into batches
		// This loop is now safe from infinite loops due to batchSize validation above
		for i := 0; i < len(objects); i += batchSize {
			end := i + batchSize
			if end > len(objects) {
				end = len(objects)
			}

			batchIndex++
			batches = append(batches, ObjectBatchGroup{
				SchemaName: schema,
				ObjectType: objType,
				Objects:    objects[i:end], // Safe slice operation due to boundary checks
				BatchIndex: batchIndex,
			})
		}
	}

	// Set total batch count
	for i := range batches {
		batches[i].TotalBatches = len(batches)
	}

	return batches
}

// processBatch processes a single batch using the optimized query method
func (rw DatasourceReadWrite) processBatch(ctx context.Context, db *sql.DB, batch *ObjectBatchGroup, results chan<- BatchResult, enableProgress bool) {
	startTime := time.Now()

	if enableProgress {
		log.Infof("[Batch %d/%d] Processing %s.%s: %d objects",
			batch.BatchIndex, batch.TotalBatches,
			batch.SchemaName, batch.ObjectType, len(batch.Objects))
	}

	// Extract object names for the batch query
	objectNames := make([]string, len(batch.Objects))
	for i, obj := range batch.Objects {
		objectNames[i] = obj.ObjectName
	}

	// Execute optimized batch query
	definitions, err := rw.GetOracleObjectDefinitionsBatch(ctx, db, batch.SchemaName, batch.ObjectType, objectNames)

	duration := time.Since(startTime)
	if err != nil {
		log.Errorf("[Batch %d/%d] Failed %s.%s after %v: %v",
			batch.BatchIndex, batch.TotalBatches,
			batch.SchemaName, batch.ObjectType, duration, err)
	} else if enableProgress {
		log.Infof("[Batch %d/%d] Completed %s.%s: %d objects in %v",
			batch.BatchIndex, batch.TotalBatches,
			batch.SchemaName, batch.ObjectType, len(definitions), duration)
	}

	// Send result to channel
	results <- BatchResult{
		BatchIndex:  batch.BatchIndex,
		SchemaName:  batch.SchemaName,
		ObjectType:  batch.ObjectType,
		Definitions: definitions,
		Error:       err,
		ProcessTime: duration,
	}
}

// collectBatchResults collects and consolidates results from all batches
func (rw DatasourceReadWrite) collectBatchResults(resultChan <-chan BatchResult, totalBatches int, enableProgress bool) ([]OracleObjectDefinition, error) {
	var allDefinitions []OracleObjectDefinition
	var errors []error
	completedBatches := 0

	for completedBatches < totalBatches {
		select {
		case result := <-resultChan:
			completedBatches++

			if result.Error != nil {
				errors = append(errors, fmt.Errorf("batch %d (%s.%s) failed: %v",
					result.BatchIndex, result.SchemaName, result.ObjectType, result.Error))
			} else {
				allDefinitions = append(allDefinitions, result.Definitions...)
			}

			if enableProgress {
				log.Infof("Overall progress: %d/%d batches completed, %d objects collected",
					completedBatches, totalBatches, len(allDefinitions))
			}
		}
	}

	if len(errors) > 0 {
		log.Warnf("Some batches failed (%d/%d), continuing with successful results: %v",
			len(errors), totalBatches, errors)
	}

	if enableProgress {
		log.Infof("All batches completed: %d objects total, %d failed batches",
			len(allDefinitions), len(errors))
	}

	return allDefinitions, nil
}

func (rw DatasourceReadWrite) GetOracleInvalidObjectBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObject, error) {
	startTime := time.Now()
	log.Infof("query oracle invalid object sql, startTime:%v", startTime)
	queryInvalidObjectSQL := `SELECT OWNER,OBJECT_NAME,OBJECT_TYPE,STATUS FROM DBA_OBJECTS WHERE OWNER IN (${SCHAMES}) AND OBJECT_TYPE IN (${TYPES}) AND STATUS = 'INVALID'`
	queryInvalidObjectSQL = strings.ReplaceAll(queryInvalidObjectSQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemas, "','")))
	queryInvalidObjectSQL = strings.ReplaceAll(queryInvalidObjectSQL, "${TYPES}", fmt.Sprintf("'%s'", strings.Join(types, "','")))
	_, res, queryErr := oracle.Query(ctx, db, queryInvalidObjectSQL)
	finishTime := time.Now()
	log.Infof("query oracle invalid object sql, endTime:%v, costTime:%v", finishTime, finishTime.Sub(startTime))
	if queryErr != nil {
		return nil, queryErr
	}

	oracleObjects := make([]OracleObject, 0, len(res))
	for _, item := range res {
		oracleObjects = append(oracleObjects, OracleObject{
			OwnerName:  item["OWNER"],
			ObjectName: item["OBJECT_NAME"],
			ObjectType: item["OBJECT_TYPE"],
			Status:     item["STATUS"],
		})
	}
	return oracleObjects, nil
}

func (rw DatasourceReadWrite) TruncateTMSSQLSetBak(ctx context.Context, db *sql.DB, taskId int) error {
	s := `DELETE FROM TMS_SQLSET_BAK WHERE TASK_ID = :1`
	_, err := db.ExecContext(ctx, s, taskId)
	if err != nil {
		log.Errorf("truncate tms_sqlset_bak failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) TruncateTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, taskId int) error {
	s := `DELETE FROM TMS_SQLSET_STATEMENTS_BAK WHERE TASK_ID = :1`
	_, err := db.ExecContext(ctx, s, taskId)
	if err != nil {
		log.Errorf("truncate tms_sqlset_statements_bak failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) GetDBASQLSets(ctx context.Context, db *sql.DB, sqlsetName string, sourceSchemas []string) ([]SQLSet, error) {
	querySQL := `
SELECT
    ID,
    NAME,
    OWNER,
    DESCRIPTION,
    FLOOR((CREATED - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400) AS CREATED_UNIXTIME,
    FLOOR((LAST_MODIFIED - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400) AS LAST_MODIFIED_UNIXTIME,
    STATEMENT_COUNT
FROM DBA_SQLSET
WHERE OWNER IN (${SCHAMES}) 
	AND NAME = ${NAME}`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	log.Debugf("GetDBASQLSets.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("query dba_sqlset failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	sqlSets := make([]SQLSet, 0, len(res))
	for _, item := range res {
		var (
			id                   uint64
			statementCount       uint64
			createdUnixTime      uint64
			lastModifiedUnixTime uint64
			created              time.Time
			lastModified         time.Time
		)

		id, _ = strconv.ParseUint(item["ID"], 10, 64)
		statementCount, _ = strconv.ParseUint(item["STATEMENT_COUNT"], 10, 64)
		createdUnixTime, _ = strconv.ParseUint(item["CREATED_UNIXTIME"], 10, 64)
		lastModifiedUnixTime, _ = strconv.ParseUint(item["LAST_MODIFIED_UNIXTIME"], 10, 64)
		created = convertUnixTimeToUTC8(createdUnixTime)
		lastModified = convertUnixTimeToUTC8(lastModifiedUnixTime)
		sqlSets = append(sqlSets, SQLSet{
			ID:             id,
			Name:           item["NAME"],
			Owner:          item["OWNER"],
			Description:    item["DESCRIPTION"],
			Created:        created,
			LastModified:   lastModified,
			StatementCount: statementCount,
		})
	}
	return sqlSets, nil
}

func (rw DatasourceReadWrite) GetSQLSetStatementsSummaries(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) (SQLSetStatementsSummaries, error) {
	querySQL := `
SELECT /*+parallel(8)*/
    SQLSET_NAME,
    PARSING_SCHEMA_NAME,
    COUNT(*) AS total_num,
    SUM(CASE WHEN (REPLAY_STATUS = 'failed') THEN 1 ELSE 0 END) AS failed_num,
    SUM(CASE WHEN (REPLAY_STATUS = 'success') THEN 1 ELSE 0 END) AS success_num,
    SUM(CASE WHEN (REPLAY_STATUS = 'timeout') THEN 1 ELSE 0 END) AS timeout_num,
	SUM(CASE WHEN (REPLAY != 'Y' OR REPLAY IS NULL) THEN 1 ELSE 0 END) AS not_replay_num,
    SUM(CASE WHEN (REPLAY = 'Y') THEN 1 ELSE 0 END) AS replayed_num,
    SUM(CASE WHEN (DIGEST_ID IS NULL) THEN 1 ELSE 0 END) AS not_digest_num,
    SUM(CASE WHEN (DIGEST_ID IS NOT NULL) THEN 1 ELSE 0 END) AS digested_num,
    ROUND(SUM(CASE WHEN (REPLAY_STATUS = 'success') THEN 1 ELSE 0 END) / COUNT(*) * 100, 2) AS pct
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}
GROUP BY SQLSET_NAME, PARSING_SCHEMA_NAME`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	log.Debugf("GetSQLSetStatementsSummaries.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("query sqlset statements summary failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	summaries := make(SQLSetStatementsSummaries, 0, len(res))
	for _, item := range res {
		var (
			schemaName   string
			totalNum     uint64
			failedNum    uint64
			successNum   uint64
			timeoutNum   uint64
			notReplayNum uint64
			replayedNum  uint64
			notDigestNum uint64
			digestedNum  uint64
			pct          string
		)
		schemaName = item["PARSING_SCHEMA_NAME"]
		totalNum, _ = strconv.ParseUint(item["TOTAL_NUM"], 10, 64)
		failedNum, _ = strconv.ParseUint(item["FAILED_NUM"], 10, 64)
		successNum, _ = strconv.ParseUint(item["SUCCESS_NUM"], 10, 64)
		timeoutNum, _ = strconv.ParseUint(item["TIMEOUT_NUM"], 10, 64)
		notReplayNum, _ = strconv.ParseUint(item["NOT_REPLAY_NUM"], 10, 64)
		replayedNum, _ = strconv.ParseUint(item["REPLAYED_NUM"], 10, 64)
		notDigestNum, _ = strconv.ParseUint(item["NOT_DIGEST_NUM"], 10, 64)
		digestedNum, _ = strconv.ParseUint(item["DIGESTED_NUM"], 10, 64)
		pct = fmt.Sprintf("%s%%", item["PCT"])
		summaries = append(summaries, SQLSetStatementsSummary{
			SQLSetOwner:  schemaName,
			SQLSetName:   sqlsetName,
			TotalNum:     totalNum,
			FailedNum:    failedNum,
			SuccessNum:   successNum,
			TimeoutNum:   timeoutNum,
			NotReplayNum: notReplayNum,
			ReplayedNum:  replayedNum,
			NotDigestNum: notDigestNum,
			DigestedNum:  digestedNum,
			Pct:          pct,
		})
	}
	return summaries, nil
}

func (rw DatasourceReadWrite) GetSQLSetStatementsFailedSummaries(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatementsFailedSummary, error) {
	// 先获取错误码统计（不使用LISTAGG避免长度限制）
	querySQL := `
SELECT /*+parallel(8)*/
    REPLAY_EXEC_CODE,
    COUNT(*) AS TOTAL_NUM,
    min(MESSAGE_LOG) AS MESSAGE_LOG
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}
  AND REPLAY_STATUS = 'failed'
GROUP BY REPLAY_EXEC_CODE`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	log.Debugf("GetSQLSetStatementsFailedSummaries.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsFailedSummaries failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	summaries := make(SQLSetStatementsFailedSummaries, 0, len(res))
	
	// 第一步：处理主查询结果
	for _, item := range res {
		var (
			ReplayExecCode string
			TotalNum       uint64
			MessageLog     string
		)

		ReplayExecCode = item["REPLAY_EXEC_CODE"]
		TotalNum, _ = strconv.ParseUint(item["TOTAL_NUM"], 10, 64)
		MessageLog, _ = item["MESSAGE_LOG"]
		
		// 暂时先用单个MessageLog
		MessageLogs := []string{}
		if MessageLog != "" {
			MessageLogs = []string{MessageLog}
		}
		
		summaries = append(summaries, SQLSetStatementsFailedSummary{
			ReplayExecCode: ReplayExecCode,
			TotalNum:       TotalNum,
			MessageLog:     MessageLog,
			MessageLogs:    MessageLogs,
		})
	}
	
	// 第二步：为每个错误码查询所有不同的错误信息（最多100条）
	for i, summary := range summaries {
		detailSQL := `
		SELECT * FROM (
			SELECT DISTINCT MESSAGE_LOG
			FROM TMS_SQLSET_STATEMENTS
			WHERE PARSING_SCHEMA_NAME IN (${SCHAMES})
			  AND SQLSET_OWNER = ${OWNER}
			  AND SQLSET_NAME = ${NAME}
			  AND TASK_ID = ${TASK_ID}
			  AND REPLAY_STATUS = 'failed'
			  AND REPLAY_EXEC_CODE = '${ERROR_CODE}'
			  AND MESSAGE_LOG IS NOT NULL
		) WHERE ROWNUM <= 100`  // 限制最多100个不同的错误信息
		
		detailSQL = strings.ReplaceAll(detailSQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
		detailSQL = strings.ReplaceAll(detailSQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
		detailSQL = strings.ReplaceAll(detailSQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
		detailSQL = strings.ReplaceAll(detailSQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
		detailSQL = strings.ReplaceAll(detailSQL, "${ERROR_CODE}", summary.ReplayExecCode)
		
		log.Debugf("GetSQLSetStatementsFailedSummaries detail query for error code %s: %s", summary.ReplayExecCode, detailSQL)
		
		_, detailRes, detailErr := oracle.Query(ctx, db, detailSQL)
		if detailErr != nil {
			log.Warnf("Failed to get detail messages for error code %s: %v", summary.ReplayExecCode, detailErr)
			continue
		}
		
		if len(detailRes) > 0 {
			var messageLogs []string
			for _, detail := range detailRes {
				if msg, ok := detail["MESSAGE_LOG"]; ok && msg != "" {
					messageLogs = append(messageLogs, msg)
				}
			}
			if len(messageLogs) > 0 {
				summaries[i].MessageLogs = messageLogs
				// 如果MessageLog为空，使用第一个错误信息
				if summaries[i].MessageLog == "" {
					summaries[i].MessageLog = messageLogs[0]
				}
			}
		}
	}
	return summaries, nil
}

func (rw DatasourceReadWrite) GetSQLSetStatementsTimeoutSummaries(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatementsFailedSummary, error) {
	querySQL := `
SELECT /*+parallel(8)*/
    COUNT(*) AS total_num
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}
  AND REPLAY_STATUS = 'timeout'`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	log.Debugf("GetSQLSetStatementsTimeoutSummaries.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsTimeoutSummaries failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	summaries := make(SQLSetStatementsFailedSummaries, 0, len(res))
	for _, item := range res {
		var (
			totalNum uint64
		)

		totalNum, _ = strconv.ParseUint(item["TOTAL_NUM"], 10, 64)
		summaries = append(summaries, SQLSetStatementsFailedSummary{
			TotalNum: totalNum,
		})
	}
	return summaries, nil
}

func (rw DatasourceReadWrite) GetSQLSetStatementsList(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatement, error) {
	querySQL := `
SELECT /*+parallel(8)*/
    *
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	log.Debugf("GetSQLSetStatementsList.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsList failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	results := make(SQLSetStatements, 0, len(res))
	for _, item := range res {
		var (
			TaskID            int64
			SQLSetName        string
			SQLSetOwner       string
			SQLSetID          int64
			DigestID          string
			SQLID             string
			SQLText           string
			ParsingSchemaName string
			PlanHashValue     int64
			BindData          []byte
			CommandType       int64
			Module            string
			Action            string
			ElapsedTime       int64
			Executions        int64
			Replay            string
			ReplayStatus      string
			ReplayFinishTime  time.Time
			ReplayExecCode    string
			RewriteSQLText    string
			RewritePrompts    string
			MessageLog        string
			LastCopyTime      time.Time
		)
		if val, ok := item["TASK_ID"]; ok && val != "" {
			TaskID, _ = strconv.ParseInt(val, 10, 64)
		}
		if val, ok := item["SQLSET_NAME"]; ok {
			SQLSetName = val
		}
		if val, ok := item["SQLSET_OWNER"]; ok {
			SQLSetOwner = val
		}
		if val, ok := item["SQLSET_ID"]; ok && val != "" {
			SQLSetID, _ = strconv.ParseInt(val, 10, 64)
		}
		if val, ok := item["DIGEST_ID"]; ok {
			DigestID = val
		}
		if val, ok := item["SQL_ID"]; ok {
			SQLID = val
		}
		if val, ok := item["SQL_TEXT"]; ok {
			SQLText = val
		}
		if val, ok := item["PARSING_SCHEMA_NAME"]; ok {
			ParsingSchemaName = val
		}
		if val, ok := item["PLAN_HASH_VALUE"]; ok && val != "" {
			PlanHashValue, _ = strconv.ParseInt(val, 10, 64)
		}
		// BIND_DATA would need special handling if it's a binary field
		if val, ok := item["COMMAND_TYPE"]; ok && val != "" {
			CommandType, _ = strconv.ParseInt(val, 10, 64)
		}
		if val, ok := item["MODULE"]; ok {
			Module = val
		}
		if val, ok := item["ACTION"]; ok {
			Action = val
		}
		if val, ok := item["ELAPSED_TIME"]; ok && val != "" {
			ElapsedTime, _ = strconv.ParseInt(val, 10, 64)
		}
		if val, ok := item["EXECUTIONS"]; ok && val != "" {
			Executions, _ = strconv.ParseInt(val, 10, 64)
		}
		if val, ok := item["REPLAY"]; ok {
			Replay = val
		}
		if val, ok := item["REPLAY_STATUS"]; ok {
			ReplayStatus = val
		}
		if val, ok := item["REPLAY_FINISH_TIME"]; ok && val != "" {
			ReplayFinishTime, _ = time.Parse("2006-01-02 15:04:05", val)
		}
		if val, ok := item["REPLAY_EXEC_CODE"]; ok {
			ReplayExecCode = val
		}
		if val, ok := item["REWRITE_SQL_TEXT"]; ok {
			RewriteSQLText = val
		}
		if val, ok := item["REWRITE_PROMPTS"]; ok {
			RewritePrompts = val
		}
		if val, ok := item["MESSAGE_LOG"]; ok {
			MessageLog = val
		}
		if val, ok := item["LAST_COPY_TIME"]; ok && val != "" {
			LastCopyTime, _ = time.Parse("2006-01-02 15:04:05", val)
		}
		sqlSetStatement := SQLSetStatement{
			TaskID:            TaskID,
			SQLSetName:        SQLSetName,
			SQLSetOwner:       SQLSetOwner,
			SQLSetID:          SQLSetID,
			DigestID:          DigestID,
			SQLID:             SQLID,
			SQLText:           SQLText,
			ParsingSchemaName: ParsingSchemaName,
			PlanHashValue:     PlanHashValue,
			BindData:          BindData,
			CommandType:       CommandType,
			Module:            Module,
			Action:            Action,
			ElapsedTime:       ElapsedTime,
			Executions:        Executions,
			Replay:            Replay,
			ReplayStatus:      ReplayStatus,
			ReplayFinishTime:  ReplayFinishTime,
			ReplayExecCode:    ReplayExecCode,
			RewriteSQLText:    RewriteSQLText,
			RewritePrompts:    RewritePrompts,
			MessageLog:        MessageLog,
			LastCopyTime:      LastCopyTime,
		}
		results = append(results, sqlSetStatement)
	}
	return results, nil
}

func (rw DatasourceReadWrite) GetSQLSetStatementsListByReplayStatus(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, replayStatus string, sourceSchemas []string) ([]SQLSetStatement, error) {
	querySQL := `
SELECT /*+parallel(8)*/
    *
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}`

	if strings.TrimSpace(replayStatus) != "" && replayStatus != "''" {
		querySQL += `
  AND REPLAY_STATUS = ${REPLAY_STATUS}`
	}

	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))

	if strings.TrimSpace(replayStatus) != "" && replayStatus != "''" {
		querySQL = strings.ReplaceAll(querySQL, "${REPLAY_STATUS}", fmt.Sprintf("'%s'", replayStatus))
	}
	log.Debugf("GetSQLSetStatementsListByReplayStatus.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsListByReplayStatus failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	results := make(SQLSetStatements, 0, len(res))
	for _, item := range res {
		var (
			TaskID            int64
			SQLSetName        string
			SQLSetOwner       string
			SQLSetID          int64
			DigestID          string
			SQLID             string
			SQLText           string
			ParsingSchemaName string
			PlanHashValue     int64
			BindData          []byte
			CommandType       int64
			Module            string
			Action            string
			ElapsedTime       int64
			Executions        int64
			Replay            string
			ReplayStatus      string
			ReplayFinishTime  time.Time
			ReplayExecCode    string
			RewriteSQLText    string
			RewritePrompts    string
			MessageLog        string
			LastCopyTime      time.Time
		)

		TaskID, _ = strconv.ParseInt(item["TASK_ID"], 10, 64)
		SQLSetName = item["SQLSET_NAME"]
		SQLSetOwner = item["SQLSET_OWNER"]
		SQLSetID, _ = strconv.ParseInt(item["SQLSET_ID"], 10, 64)
		DigestID = item["DIGEST_ID"]
		SQLID = item["SQL_ID"]
		SQLText = item["SQL_TEXT"]
		ParsingSchemaName = item["PARSING_SCHEMA_NAME"]
		PlanHashValue, _ = strconv.ParseInt(item["PLAN_HASH_VALUE"], 10, 64)
		BindData = []byte(item["BIND_DATA"])
		CommandType, _ = strconv.ParseInt(item["COMMAND_TYPE"], 10, 64)
		Module = item["MODULE"]
		Action = item["ACTION"]
		ElapsedTime, _ = strconv.ParseInt(item["ELAPSED_TIME"], 10, 64)
		Executions, _ = strconv.ParseInt(item["EXECUTIONS"], 10, 64)
		Replay = item["REPLAY"]
		ReplayStatus = item["REPLAY_STATUS"]
		mReplayFinishTime, _ := strconv.ParseInt(item["REPLAY_FINISH_TIME"], 10, 64)
		ReplayFinishTime = time.Unix(0, mReplayFinishTime*int64(time.Millisecond))
		ReplayExecCode = item["REPLAY_EXEC_CODE"]
		RewriteSQLText = item["REWRITE_SQL_TEXT"]
		RewritePrompts = item["REWRITE_PROMPTS"]
		MessageLog = item["MESSAGE_LOG"]
		mLastCopyTime, _ := strconv.ParseInt(item["LAST_COPY_TIME"], 10, 64)
		LastCopyTime = time.Unix(0, mLastCopyTime*int64(time.Millisecond))

		results = append(results, SQLSetStatement{
			TaskID:            TaskID,
			SQLSetName:        SQLSetName,
			SQLSetOwner:       SQLSetOwner,
			SQLSetID:          SQLSetID,
			DigestID:          DigestID,
			SQLID:             SQLID,
			SQLText:           SQLText,
			ParsingSchemaName: ParsingSchemaName,
			PlanHashValue:     PlanHashValue,
			BindData:          BindData,
			CommandType:       CommandType,
			Module:            Module,
			Action:            Action,
			ElapsedTime:       ElapsedTime,
			Executions:        Executions,
			Replay:            Replay,
			ReplayStatus:      ReplayStatus,
			ReplayFinishTime:  ReplayFinishTime,
			ReplayExecCode:    ReplayExecCode,
			RewriteSQLText:    RewriteSQLText,
			RewritePrompts:    RewritePrompts,
			MessageLog:        MessageLog,
			LastCopyTime:      LastCopyTime,
		})
	}
	return results, nil
}

func (rw DatasourceReadWrite) GetNotReplaySQLSetStatementsList(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatement, error) {
	querySQL := `
SELECT /*+parallel(8)*/
    *
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}
  AND REPLAY != 'Y' OR REPLAY IS NULL`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	log.Debugf("GetSQLSetStatementsListByReplayStatus.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsListByReplayStatus failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	results := make(SQLSetStatements, 0, len(res))
	for _, item := range res {
		var (
			TaskID            int64
			SQLSetName        string
			SQLSetOwner       string
			SQLSetID          int64
			DigestID          string
			SQLID             string
			SQLText           string
			ParsingSchemaName string
			PlanHashValue     int64
			BindData          []byte
			CommandType       int64
			Module            string
			Action            string
			ElapsedTime       int64
			Executions        int64
			Replay            string
			ReplayStatus      string
			ReplayFinishTime  time.Time
			ReplayExecCode    string
			RewriteSQLText    string
			RewritePrompts    string
			MessageLog        string
			LastCopyTime      time.Time
		)

		TaskID, _ = strconv.ParseInt(item["TASK_ID"], 10, 64)
		SQLSetName = item["SQLSET_NAME"]
		SQLSetOwner = item["SQLSET_OWNER"]
		SQLSetID, _ = strconv.ParseInt(item["SQLSET_ID"], 10, 64)
		DigestID = item["DIGEST_ID"]
		SQLID = item["SQL_ID"]
		SQLText = item["SQL_TEXT"]
		ParsingSchemaName = item["PARSING_SCHEMA_NAME"]
		PlanHashValue, _ = strconv.ParseInt(item["PLAN_HASH_VALUE"], 10, 64)
		BindData = []byte(item["BIND_DATA"])
		CommandType, _ = strconv.ParseInt(item["COMMAND_TYPE"], 10, 64)
		Module = item["MODULE"]
		Action = item["ACTION"]
		ElapsedTime, _ = strconv.ParseInt(item["ELAPSED_TIME"], 10, 64)
		Executions, _ = strconv.ParseInt(item["EXECUTIONS"], 10, 64)
		Replay = item["REPLAY"]
		ReplayStatus = item["REPLAY_STATUS"]
		mReplayFinishTime, _ := strconv.ParseInt(item["REPLAY_FINISH_TIME"], 10, 64)
		ReplayFinishTime = time.Unix(0, mReplayFinishTime*int64(time.Millisecond))
		ReplayExecCode = item["REPLAY_EXEC_CODE"]
		RewriteSQLText = item["REWRITE_SQL_TEXT"]
		RewritePrompts = item["REWRITE_PROMPTS"]
		MessageLog = item["MESSAGE_LOG"]
		mLastCopyTime, _ := strconv.ParseInt(item["LAST_COPY_TIME"], 10, 64)
		LastCopyTime = time.Unix(0, mLastCopyTime*int64(time.Millisecond))

		results = append(results, SQLSetStatement{
			TaskID:            TaskID,
			SQLSetName:        SQLSetName,
			SQLSetOwner:       SQLSetOwner,
			SQLSetID:          SQLSetID,
			DigestID:          DigestID,
			SQLID:             SQLID,
			SQLText:           SQLText,
			ParsingSchemaName: ParsingSchemaName,
			PlanHashValue:     PlanHashValue,
			BindData:          BindData,
			CommandType:       CommandType,
			Module:            Module,
			Action:            Action,
			ElapsedTime:       ElapsedTime,
			Executions:        Executions,
			Replay:            Replay,
			ReplayStatus:      ReplayStatus,
			ReplayFinishTime:  ReplayFinishTime,
			ReplayExecCode:    ReplayExecCode,
			RewriteSQLText:    RewriteSQLText,
			RewritePrompts:    RewritePrompts,
			MessageLog:        MessageLog,
			LastCopyTime:      LastCopyTime,
		})
	}
	return results, nil
}

// GetSQLSetStatementsListByReplayStatusWithPagination retrieves SQL statements with database-level pagination and sorting
func (rw DatasourceReadWrite) GetSQLSetStatementsListByReplayStatusWithPagination(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, replayStatus string, sourceSchemas []string, page, pageSize int, orderBy string, oraSqlId, oraParsingSchemaName, oraSqlText string) ([]SQLSetStatement, int64, error) {
	// Set default orderBy if not provided
	if strings.TrimSpace(orderBy) == "" {
		orderBy = "SQL_ID"
	}

	// First, get the total count
	countSQL := `
SELECT /*+parallel(8)*/
    COUNT(*)
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}`

	if strings.TrimSpace(replayStatus) != "" && replayStatus != "''" {
		countSQL += `
  AND REPLAY_STATUS = ${REPLAY_STATUS}`
	}
	if strings.TrimSpace(oraSqlId) != "" {
		countSQL += `
  AND SQL_ID LIKE ${ORA_SQL_ID}`
	}
	if strings.TrimSpace(oraParsingSchemaName) != "" {
		countSQL += `
  AND PARSING_SCHEMA_NAME LIKE ${ORA_PARSING_SCHEMA_NAME}`
	}
	if strings.TrimSpace(oraSqlText) != "" {
		countSQL += `
  AND SQL_TEXT LIKE ${ORA_SQL_TEXT}`
	}

	countSQL = strings.ReplaceAll(countSQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	countSQL = strings.ReplaceAll(countSQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	countSQL = strings.ReplaceAll(countSQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	countSQL = strings.ReplaceAll(countSQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))

	if strings.TrimSpace(replayStatus) != "" && replayStatus != "''" {
		countSQL = strings.ReplaceAll(countSQL, "${REPLAY_STATUS}", fmt.Sprintf("'%s'", replayStatus))
	}
	if strings.TrimSpace(oraSqlId) != "" {
		countSQL = strings.ReplaceAll(countSQL, "${ORA_SQL_ID}", fmt.Sprintf("'%%%s%%'", oraSqlId))
	}
	if strings.TrimSpace(oraParsingSchemaName) != "" {
		countSQL = strings.ReplaceAll(countSQL, "${ORA_PARSING_SCHEMA_NAME}", fmt.Sprintf("'%%%s%%'", oraParsingSchemaName))
	}
	if strings.TrimSpace(oraSqlText) != "" {
		countSQL = strings.ReplaceAll(countSQL, "${ORA_SQL_TEXT}", fmt.Sprintf("'%%%s%%'", oraSqlText))
	}

	log.Debugf("GetSQLSetStatementsListByReplayStatusWithPagination.countSQL: %s", countSQL)
	_, countRes, err := oracle.Query(ctx, db, countSQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsListByReplayStatusWithPagination count failed. %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	var total int64 = 0
	if len(countRes) > 0 {
		if count, parseErr := strconv.ParseInt(countRes[0]["COUNT(*)"], 10, 64); parseErr == nil {
			total = count
		}
	}

	// Calculate offset for pagination
	offset := (page - 1) * pageSize

	// Build the paginated query using ROW_NUMBER() for consistent pagination
	querySQL := `
SELECT * FROM (
    SELECT /*+parallel(8)*/
        a.*,
        ROW_NUMBER() OVER (ORDER BY ${ORDER_BY}) as rn
    FROM TMS_SQLSET_STATEMENTS a
    WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
      AND SQLSET_OWNER = ${OWNER}
      AND SQLSET_NAME = ${NAME}
      AND TASK_ID = ${TASK_ID}`

	if strings.TrimSpace(replayStatus) != "" && replayStatus != "''" {
		querySQL += `
      AND REPLAY_STATUS = ${REPLAY_STATUS}`
	}
	if strings.TrimSpace(oraSqlId) != "" {
		querySQL += `
      AND SQL_ID LIKE ${ORA_SQL_ID}`
	}
	if strings.TrimSpace(oraParsingSchemaName) != "" {
		querySQL += `
      AND PARSING_SCHEMA_NAME LIKE ${ORA_PARSING_SCHEMA_NAME}`
	}
	if strings.TrimSpace(oraSqlText) != "" {
		querySQL += `
      AND SQL_TEXT LIKE ${ORA_SQL_TEXT}`
	}

	querySQL += `
) WHERE rn > ${OFFSET} AND rn <= ${LIMIT}`

	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	querySQL = strings.ReplaceAll(querySQL, "${ORDER_BY}", orderBy)
	querySQL = strings.ReplaceAll(querySQL, "${OFFSET}", fmt.Sprintf("%d", offset))
	querySQL = strings.ReplaceAll(querySQL, "${LIMIT}", fmt.Sprintf("%d", offset+pageSize))

	if strings.TrimSpace(replayStatus) != "" && replayStatus != "''" {
		querySQL = strings.ReplaceAll(querySQL, "${REPLAY_STATUS}", fmt.Sprintf("'%s'", replayStatus))
	}
	if strings.TrimSpace(oraSqlId) != "" {
		querySQL = strings.ReplaceAll(querySQL, "${ORA_SQL_ID}", fmt.Sprintf("'%%%s%%'", oraSqlId))
	}
	if strings.TrimSpace(oraParsingSchemaName) != "" {
		querySQL = strings.ReplaceAll(querySQL, "${ORA_PARSING_SCHEMA_NAME}", fmt.Sprintf("'%%%s%%'", oraParsingSchemaName))
	}
	if strings.TrimSpace(oraSqlText) != "" {
		querySQL = strings.ReplaceAll(querySQL, "${ORA_SQL_TEXT}", fmt.Sprintf("'%%%s%%'", oraSqlText))
	}

	log.Debugf("GetSQLSetStatementsListByReplayStatusWithPagination.querySQL: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsListByReplayStatusWithPagination failed. %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	results := make(SQLSetStatements, 0, len(res))
	for _, item := range res {
		var (
			TaskID            int64
			SQLSetName        string
			SQLSetOwner       string
			SQLSetID          int64
			DigestID          string
			SQLID             string
			SQLText           string
			ParsingSchemaName string
			PlanHashValue     int64
			BindData          []byte
			CommandType       int64
			Module            string
			Action            string
			ElapsedTime       int64
			Executions        int64
			Replay            string
			ReplayStatus      string
			ReplayFinishTime  time.Time
			ReplayExecCode    string
			RewriteSQLText    string
			RewritePrompts    string
			MessageLog        string
			LastCopyTime      time.Time
		)

		TaskID, _ = strconv.ParseInt(item["TASK_ID"], 10, 64)
		SQLSetName = item["SQLSET_NAME"]
		SQLSetOwner = item["SQLSET_OWNER"]
		SQLSetID, _ = strconv.ParseInt(item["SQLSET_ID"], 10, 64)
		DigestID = item["DIGEST_ID"]
		SQLID = item["SQL_ID"]
		SQLText = item["SQL_TEXT"]
		ParsingSchemaName = item["PARSING_SCHEMA_NAME"]
		PlanHashValue, _ = strconv.ParseInt(item["PLAN_HASH_VALUE"], 10, 64)
		BindData = []byte(item["BIND_DATA"])
		CommandType, _ = strconv.ParseInt(item["COMMAND_TYPE"], 10, 64)
		Module = item["MODULE"]
		Action = item["ACTION"]
		ElapsedTime, _ = strconv.ParseInt(item["ELAPSED_TIME"], 10, 64)
		Executions, _ = strconv.ParseInt(item["EXECUTIONS"], 10, 64)
		Replay = item["REPLAY"]
		ReplayStatus = item["REPLAY_STATUS"]
		mReplayFinishTime, _ := strconv.ParseInt(item["REPLAY_FINISH_TIME"], 10, 64)
		ReplayFinishTime = time.Unix(0, mReplayFinishTime*int64(time.Millisecond))
		ReplayExecCode = item["REPLAY_EXEC_CODE"]
		RewriteSQLText = item["REWRITE_SQL_TEXT"]
		RewritePrompts = item["REWRITE_PROMPTS"]
		MessageLog = item["MESSAGE_LOG"]
		mLastCopyTime, _ := strconv.ParseInt(item["LAST_COPY_TIME"], 10, 64)
		LastCopyTime = time.Unix(0, mLastCopyTime*int64(time.Millisecond))

		results = append(results, SQLSetStatement{
			TaskID:            TaskID,
			SQLSetName:        SQLSetName,
			SQLSetOwner:       SQLSetOwner,
			SQLSetID:          SQLSetID,
			DigestID:          DigestID,
			SQLID:             SQLID,
			SQLText:           SQLText,
			ParsingSchemaName: ParsingSchemaName,
			PlanHashValue:     PlanHashValue,
			BindData:          BindData,
			CommandType:       CommandType,
			Module:            Module,
			Action:            Action,
			ElapsedTime:       ElapsedTime,
			Executions:        Executions,
			Replay:            Replay,
			ReplayStatus:      ReplayStatus,
			ReplayFinishTime:  ReplayFinishTime,
			ReplayExecCode:    ReplayExecCode,
			RewriteSQLText:    RewriteSQLText,
			RewritePrompts:    RewritePrompts,
			MessageLog:        MessageLog,
			LastCopyTime:      LastCopyTime,
		})
	}

	log.Debugf("GetSQLSetStatementsListByReplayStatusWithPagination completed, returned %d records, total: %d", len(results), total)
	return results, total, nil
}

func (rw DatasourceReadWrite) GetAiFixSQLSetStatementsListByReplayStatus(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, replayStatus string, sourceSchemas []string) ([]SQLSetStatement, error) {
	querySQL := `
SELECT /*+parallel(8)*/
    *
FROM TMS_SQLSET_STATEMENTS
WHERE PARSING_SCHEMA_NAME IN (${SCHAMES}) 
  AND SQLSET_OWNER = ${OWNER}
  AND SQLSET_NAME = ${NAME}
  AND TASK_ID = ${TASK_ID}
  AND REPLAY_STATUS = ${REPLAY_STATUS}
  AND REWRITE_SQL_TEXT IS NOT NULL `
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	querySQL = strings.ReplaceAll(querySQL, "${OWNER}", fmt.Sprintf("'%s'", sqlsetOwner))
	querySQL = strings.ReplaceAll(querySQL, "${REPLAY_STATUS}", fmt.Sprintf("'%s'", replayStatus))
	log.Debugf("GetAiFixSQLSetStatementsListByReplayStatus.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("GetAiFixSQLSetStatementsListByReplayStatus failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	results := make(SQLSetStatements, 0, len(res))
	for _, item := range res {
		var (
			TaskID            int64
			SQLSetName        string
			SQLSetOwner       string
			SQLSetID          int64
			DigestID          string
			SQLID             string
			SQLText           string
			ParsingSchemaName string
			PlanHashValue     int64
			BindData          []byte
			CommandType       int64
			Module            string
			Action            string
			ElapsedTime       int64
			Executions        int64
			Replay            string
			ReplayStatus      string
			ReplayFinishTime  time.Time
			ReplayExecCode    string
			RewriteSQLText    string
			RewritePrompts    string
			MessageLog        string
			LastCopyTime      time.Time
		)

		TaskID, _ = strconv.ParseInt(item["TASK_ID"], 10, 64)
		SQLSetName = item["SQLSET_NAME"]
		SQLSetOwner = item["SQLSET_OWNER"]
		SQLSetID, _ = strconv.ParseInt(item["SQLSET_ID"], 10, 64)
		DigestID = item["DIGEST_ID"]
		SQLID = item["SQL_ID"]
		SQLText = item["SQL_TEXT"]
		ParsingSchemaName = item["PARSING_SCHEMA_NAME"]
		PlanHashValue, _ = strconv.ParseInt(item["PLAN_HASH_VALUE"], 10, 64)
		BindData = []byte(item["BIND_DATA"])
		CommandType, _ = strconv.ParseInt(item["COMMAND_TYPE"], 10, 64)
		Module = item["MODULE"]
		Action = item["ACTION"]
		ElapsedTime, _ = strconv.ParseInt(item["ELAPSED_TIME"], 10, 64)
		Executions, _ = strconv.ParseInt(item["EXECUTIONS"], 10, 64)
		Replay = item["REPLAY"]
		ReplayStatus = item["REPLAY_STATUS"]
		mReplayFinishTime, _ := strconv.ParseInt(item["REPLAY_FINISH_TIME"], 10, 64)
		ReplayFinishTime = time.Unix(0, mReplayFinishTime*int64(time.Millisecond))
		ReplayExecCode = item["REPLAY_EXEC_CODE"]
		RewriteSQLText = item["REWRITE_SQL_TEXT"]
		RewritePrompts = item["REWRITE_PROMPTS"]
		MessageLog = item["MESSAGE_LOG"]
		mLastCopyTime, _ := strconv.ParseInt(item["LAST_COPY_TIME"], 10, 64)
		LastCopyTime = time.Unix(0, mLastCopyTime*int64(time.Millisecond))

		results = append(results, SQLSetStatement{
			TaskID:            TaskID,
			SQLSetName:        SQLSetName,
			SQLSetOwner:       SQLSetOwner,
			SQLSetID:          SQLSetID,
			DigestID:          DigestID,
			SQLID:             SQLID,
			SQLText:           SQLText,
			ParsingSchemaName: ParsingSchemaName,
			PlanHashValue:     PlanHashValue,
			BindData:          BindData,
			CommandType:       CommandType,
			Module:            Module,
			Action:            Action,
			ElapsedTime:       ElapsedTime,
			Executions:        Executions,
			Replay:            Replay,
			ReplayStatus:      ReplayStatus,
			ReplayFinishTime:  ReplayFinishTime,
			ReplayExecCode:    ReplayExecCode,
			RewriteSQLText:    RewriteSQLText,
			RewritePrompts:    RewritePrompts,
			MessageLog:        MessageLog,
			LastCopyTime:      LastCopyTime,
		})
	}
	return results, nil
}

func (rw DatasourceReadWrite) GetTMSSQLSets(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sourceSchemas []string) ([]SQLSet, error) {
	querySQL := `
SELECT
    TASK_ID,
    NAME,
    OWNER,
    DESCRIPTION,
    FLOOR((CREATED - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400) AS CREATED_UNIXTIME,
    FLOOR((LAST_MODIFIED - TO_DATE('1970-01-01', 'YYYY-MM-DD')) * 86400) AS LAST_MODIFIED_UNIXTIME,
    STATEMENT_COUNT
FROM TMS_SQLSET
WHERE OWNER IN (${SCHAMES}) 
	AND NAME = ${NAME}
	AND TASK_ID = ${TASK_ID}`
	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(sourceSchemas, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${NAME}", fmt.Sprintf("'%s'", sqlsetName))
	querySQL = strings.ReplaceAll(querySQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	log.Debugf("GetTMSSQLSets.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("query tms_sqlset failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	sqlSets := make([]SQLSet, 0, len(res))
	for _, item := range res {
		var (
			id                   uint64
			iTaskId              uint64
			statementCount       uint64
			createdUnixTime      uint64
			lastModifiedUnixTime uint64
			created              time.Time
			lastModified         time.Time
		)

		id, _ = strconv.ParseUint(item["ID"], 10, 64)
		iTaskId, _ = strconv.ParseUint(item["TASK_ID"], 10, 64)
		statementCount, _ = strconv.ParseUint(item["STATEMENT_COUNT"], 10, 64)
		createdUnixTime, _ = strconv.ParseUint(item["CREATED_UNIXTIME"], 10, 64)
		lastModifiedUnixTime, _ = strconv.ParseUint(item["LAST_MODIFIED_UNIXTIME"], 10, 64)
		created = convertUnixTimeToUTC8(createdUnixTime)
		lastModified = convertUnixTimeToUTC8(lastModifiedUnixTime)
		sqlSets = append(sqlSets, SQLSet{
			ID:             id,
			TaskId:         int(iTaskId),
			Name:           item["NAME"],
			Owner:          item["OWNER"],
			Description:    item["DESCRIPTION"],
			Created:        created,
			LastModified:   lastModified,
			StatementCount: statementCount,
		})
	}
	return sqlSets, nil
}

func convertUnixTimeToUTC8(lastModifiedUnixTime uint64) time.Time {
	return time.Unix(int64(lastModifiedUnixTime)-3600*8, 0)
}

func (rw DatasourceReadWrite) InsertIntoTMSSQLSet(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	insertSQL := `
INSERT INTO TMS_SQLSET (TASK_ID,NAME,OWNER,DESCRIPTION,CREATED,LAST_MODIFIED,STATEMENT_COUNT)
SELECT ${TASK_ID},NAME,OWNER,DESCRIPTION,CREATED,LAST_MODIFIED,STATEMENT_COUNT 
FROM DBA_SQLSET
WHERE OWNER=:1 AND NAME=:2`
	insertSQL = strings.ReplaceAll(insertSQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	_, err := db.ExecContext(ctx, insertSQL, sourceSchema, sqlsetName)
	if err != nil {
		log.Errorf("insert into tms_sqlset failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) InsertIntoTMSSQLSetStatements(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	insertSQL := `
INSERT INTO /*+append parallel(8)*/ TMS_SQLSET_STATEMENTS
(TASK_ID,SQLSET_NAME,SQLSET_OWNER,SQLSET_ID,SQL_ID,SQL_TEXT,PARSING_SCHEMA_NAME,PLAN_HASH_VALUE,BIND_DATA,COMMAND_TYPE,MODULE,ACTION,ELAPSED_TIME,EXECUTIONS)
SELECT /*+parallel(8)*/ 
${TASK_ID},SQLSET_NAME,SQLSET_OWNER,SQLSET_ID,SQL_ID,SQL_TEXT,PARSING_SCHEMA_NAME,PLAN_HASH_VALUE,BIND_DATA,COMMAND_TYPE,MODULE,ACTION,ELAPSED_TIME,EXECUTIONS
FROM DBA_SQLSET_STATEMENTS
WHERE SQLSET_OWNER=:1 AND SQLSET_NAME=:2
`
	insertSQL = strings.ReplaceAll(insertSQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	_, err := db.ExecContext(ctx, insertSQL, sourceSchema, sqlsetName)
	if err != nil {
		log.Errorf("insert into tms_sqlset_statements failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) InsertIntoTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	insertSQL := `
INSERT INTO /*+append parallel(8)*/ TMS_SQLSET_STATEMENTS_BAK
(TASK_ID,SQLSET_NAME,SQLSET_OWNER,SQLSET_ID,SQL_ID,SQL_TEXT,PARSING_SCHEMA_NAME,PLAN_HASH_VALUE,BIND_DATA,COMMAND_TYPE,MODULE,ACTION,ELAPSED_TIME,EXECUTIONS)
SELECT /*+parallel(8)*/ 
${TASK_ID},SQLSET_NAME,SQLSET_OWNER,SQLSET_ID,SQL_ID,SQL_TEXT,PARSING_SCHEMA_NAME,PLAN_HASH_VALUE,BIND_DATA,COMMAND_TYPE,MODULE,ACTION,ELAPSED_TIME,EXECUTIONS
FROM DBA_SQLSET_STATEMENTS
WHERE SQLSET_OWNER=:1 AND SQLSET_NAME=:2
`
	insertSQL = strings.ReplaceAll(insertSQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	_, err := db.ExecContext(ctx, insertSQL, sourceSchema, sqlsetName)
	if err != nil {
		log.Errorf("insert into tms_sqlset_statements_bak failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) InsertIntoTMSSQLSetBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	insertSQL := `
INSERT INTO TMS_SQLSET_BAK (TASK_ID,NAME,OWNER,DESCRIPTION,CREATED,LAST_MODIFIED,STATEMENT_COUNT)
SELECT ${TASK_ID},NAME,OWNER,DESCRIPTION,CREATED,LAST_MODIFIED,STATEMENT_COUNT 
FROM DBA_SQLSET
WHERE OWNER=:1 AND NAME=:2
`
	insertSQL = strings.ReplaceAll(insertSQL, "${TASK_ID}", fmt.Sprintf("%d", taskId))
	_, err := db.ExecContext(ctx, insertSQL, sourceSchema, sqlsetName)
	if err != nil {
		log.Errorf("insert into tms_sqlset_bak failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	deleteSQL := `DELETE FROM TMS_SQLSET_STATEMENTS_BAK WHERE SQLSET_OWNER=:1 AND SQLSET_NAME=:2 AND TASK_ID=:3`
	_, err := db.ExecContext(ctx, deleteSQL, sourceSchema, sqlsetName, taskId)
	if err != nil {
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatements(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	deleteSQL := `DELETE FROM TMS_SQLSET_STATEMENTS WHERE SQLSET_OWNER=:1 AND SQLSET_NAME=:2 AND TASK_ID=:3`
	_, err := db.ExecContext(ctx, deleteSQL, sourceSchema, sqlsetName, taskId)
	if err != nil {
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) UpdateTMSSQLSetStatementsLastCopyTime(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	updateSQL := `
UPDATE TMS_SQLSET_STATEMENTS
SET last_copy_time=(SELECT LAST_MODIFIED FROM TMS_SQLSET WHERE OWNER=:1 AND NAME=:2 AND rownum<2)
WHERE SQLSET_OWNER=:3 AND SQLSET_NAME=:4 AND TASK_ID=:5
`
	_, err := db.ExecContext(ctx, updateSQL, sourceSchema, sqlsetName, sourceSchema, sqlsetName, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset_statements last_copy_time failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) UpdateTMSSQLSetStatementsBakLastCopyTime(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	updateSQL := `
UPDATE TMS_SQLSET_STATEMENTS_BAK
SET last_copy_time=(SELECT LAST_MODIFIED FROM TMS_SQLSET_BAK where SQLSET_OWNER=:1 AND SQLSET_NAME=:2 AND rownum<2)
WHERE SQLSET_OWNER=:3 AND SQLSET_NAME=:4 AND TASK_ID=:5
`
	_, err := db.ExecContext(ctx, updateSQL, sourceSchema, sqlsetName, sourceSchema, sqlsetName, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset_statements_bak last_copy_time failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSet(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	deleteSQL := `DELETE FROM TMS_SQLSET where OWNER=:1 AND NAME=:2 AND TASK_ID=:3`
	_, err := db.ExecContext(ctx, deleteSQL, sourceSchema, sqlsetName, taskId)
	if err != nil {
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	deleteSQL := `DELETE FROM TMS_SQLSET_BAK WHERE OWNER=:1 AND NAME=:2 AND TASK_ID=:3`
	_, err := db.ExecContext(ctx, deleteSQL, sourceSchema, sqlsetName, taskId)
	if err != nil {
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func prettyCreateDefinition(definition string) string {
	codeTokens := strings.Split(definition, " ")
	firstToken := codeTokens[0]
	// if firstToken is all lower case, then append 'create', else append 'CREATE'
	if strings.ToLower(firstToken) == firstToken {
		return "create " + definition
	}
	return "CREATE " + definition
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsByPlanHashValue(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `
BEGIN
	FOR v IN (
		SELECT plan_hash_value, COUNT(*) cnt 
		FROM TMS_SQLSET_STATEMENTS 
		WHERE TASK_ID = :1
		GROUP BY plan_hash_value) 
	LOOP 
		WHILE (v.cnt > 1) 
			LOOP
				DELETE 
				FROM TMS_SQLSET_STATEMENTS a 
				WHERE a.plan_hash_value = v.plan_hash_value 
				  AND a.TASK_ID = :1 
				  AND rownum = 1;
				v.cnt := v.cnt - 1;
			END LOOP;
		COMMIT;
	END LOOP;
END;`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements by plan_hash_value failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsByDigestID(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `
BEGIN
    FOR v IN (
		SELECT DIGEST_ID, COUNT(*) cnt 
		FROM TMS_SQLSET_STATEMENTS 
		WHERE TASK_ID = :1 
		GROUP BY DIGEST_ID)
	LOOP
		WHILE(v.cnt > 1)
			LOOP
				DELETE 
				FROM TMS_SQLSET_STATEMENTS a 
				WHERE a.DIGEST_ID = v.DIGEST_ID 
				  AND a.TASK_ID = :1 
				  AND rownum = 1;
				v.cnt := v.cnt - 1;
			END LOOP;
		COMMIT;
	END LOOP;
END;`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements by digest_id failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsBakByDigestID(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `
BEGIN
    FOR v IN (
		SELECT DIGEST_ID, COUNT(*) cnt 
		FROM TMS_SQLSET_STATEMENTS_BAK 
		WHERE TASK_ID = :1 
		GROUP BY DIGEST_ID)
	LOOP
		WHILE(v.cnt > 1)
			LOOP
				DELETE 
				FROM TMS_SQLSET_STATEMENTS_BAK a 
				WHERE a.DIGEST_ID = v.DIGEST_ID 
				  AND a.TASK_ID = :1 
				  AND rownum = 1;
				v.cnt := v.cnt - 1;
			END LOOP;
		COMMIT;
	END LOOP;
END;`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements_bak by digest_id failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsBakByPlanHashValue(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `
BEGIN
    FOR v IN (
		SELECT plan_hash_value, COUNT(*) cnt
		FROM TMS_SQLSET_STATEMENTS_BAK
		WHERE TASK_ID = :1
		GROUP BY plan_hash_value)
	LOOP
		WHILE(v.cnt > 1)
			LOOP
				DELETE
				FROM TMS_SQLSET_STATEMENTS_BAK a
				WHERE a.plan_hash_value = v.plan_hash_value
				  AND a.TASK_ID = :1
				  AND rownum = 1;
				v.cnt := v.cnt - 1;
			END LOOP;
		COMMIT;
	END LOOP;
END;`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements_bak by plan_hash_value failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsBySQLID(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `
BEGIN
    FOR v IN (
		SELECT sql_id, COUNT(*) cnt 
		FROM TMS_SQLSET_STATEMENTS 
		WHERE TASK_ID = :1 
		GROUP BY sql_id)
	LOOP
		WHILE(v.cnt > 1)
			LOOP
				DELETE 
				FROM TMS_SQLSET_STATEMENTS a 
				WHERE a.sql_id = v.sql_id 
				  AND a.TASK_ID = :1
				  AND rownum = 1;
				v.cnt := v.cnt - 1;
			END LOOP;
		COMMIT;
	END LOOP;
END;`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements by sql_id failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsBakBySQLID(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `
BEGIN
    FOR v IN (
		SELECT sql_id, COUNT(*) cnt 
		FROM TMS_SQLSET_STATEMENTS_BAK
		WHERE TASK_ID = :1
		GROUP BY sql_id)
	LOOP
		WHILE(v.cnt > 1)
			LOOP
				DELETE 
				FROM TMS_SQLSET_STATEMENTS_BAK a 
				WHERE a.sql_id = v.sql_id 
				  AND a.TASK_ID = :1
				  AND rownum = 1;
				v.cnt := v.cnt - 1;
			END LOOP;
		COMMIT;
	END LOOP;
END;`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements_bak by sql_id failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) UpdateTMSSQLSetStatementsDigestID(ctx context.Context, db *sql.DB, taskId int, sqlId string, digestID string) error {
	updateSQL := `
	UPDATE TMS_SQLSET_STATEMENTS 
	SET DIGEST_ID = :1 
	WHERE SQL_ID = :2 
	    AND TASK_ID = :3
	    AND DIGEST_ID IS NULL`
	_, err := db.ExecContext(ctx, updateSQL, digestID, sqlId, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset_statements digest_id failed, sqlId:%s, digestID:%s, err:%v", sqlId, digestID, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) UpdateTMSSQLSetStatementsBakDigestID(ctx context.Context, db *sql.DB, taskId int, sqlId string, digestID string) error {
	updateSQL := `
	UPDATE TMS_SQLSET_STATEMENTS_BAK 
	SET DIGEST_ID = :1 
	WHERE SQL_ID = :2 
	    AND TASK_ID = :3
	    AND DIGEST_ID IS NULL`
	_, err := db.ExecContext(ctx, updateSQL, digestID, sqlId, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset_statements_bak digest_id failed, sqlId:%s, digestID:%s, err:%v", sqlId, digestID, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBySchemas(ctx context.Context, db *sql.DB, taskId int, sourceSchemas []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(sourceSchemas) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(sourceSchemas))
	for i := range sourceSchemas {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS 
	WHERE TASK_ID = :1
	AND PARSING_SCHEMA_NAME NOT IN (%s)`, strings.Join(placeholders, ", "))

	// 将 sourceSchemas 展开为参数
	args := make([]interface{}, 0, len(sourceSchemas)+1)
	args = append(args, taskId)
	for _, v := range sourceSchemas {
		args = append(args, v)
	}

	// 执行 SQL
	log.Infof("delete tms_sqlset_statements by schemas, sql:%s, args:%v", deleteSQL, args)
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBakBySchemas(ctx context.Context, db *sql.DB, taskId int, sourceSchemas []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(sourceSchemas) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(sourceSchemas))
	for i := range sourceSchemas {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS_BAK 
	WHERE TASK_ID = :1 
	AND PARSING_SCHEMA_NAME NOT IN (%s)`, strings.Join(placeholders, ", "))

	// 将 sourceSchemas 展开为参数
	args := make([]interface{}, 0, len(sourceSchemas)+1)
	args = append(args, taskId)
	for _, v := range sourceSchemas {
		args = append(args, v)
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBySQLTypes(ctx context.Context, db *sql.DB, taskId int, includingSqlTypes []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(includingSqlTypes) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(includingSqlTypes))
	for i := range includingSqlTypes {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS 
	WHERE TASK_ID = :1 
	AND COMMAND_TYPE NOT IN (%s)`, strings.Join(placeholders, ", "))

	// 将 includingSqlTypes 展开为参数
	args := make([]interface{}, 0, len(includingSqlTypes)+1)
	args = append(args, taskId)
	for _, v := range includingSqlTypes {
		args = append(args, v)
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBakBySQLTypes(ctx context.Context, db *sql.DB, taskId int, includingSqlTypes []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(includingSqlTypes) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(includingSqlTypes))
	for i := range includingSqlTypes {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS_BAK 
	WHERE TASK_ID = :1 
	AND COMMAND_TYPE NOT IN (%s)`, strings.Join(placeholders, ", "))

	// 将 includingSqlTypes 展开为参数
	args := make([]interface{}, 0, len(includingSqlTypes)+1)
	args = append(args, taskId)
	for _, v := range includingSqlTypes {
		args = append(args, v)
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBySQLIDs(ctx context.Context, db *sql.DB, taskId int, excludingSQLIds []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(excludingSQLIds) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(excludingSQLIds))
	for i := range excludingSQLIds {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS 
	WHERE TASK_ID = :1 
	AND SQL_ID IN (%s)`, strings.Join(placeholders, ", "))

	// 将 excludingSQLIds 展开为参数
	args := make([]interface{}, 0, len(excludingSQLIds)+1)
	args = append(args, taskId)
	for i, v := range excludingSQLIds {
		args[i+1] = v
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBakBySQLIDs(ctx context.Context, db *sql.DB, taskId int, excludingSQLIds []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(excludingSQLIds) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(excludingSQLIds))
	for i := range excludingSQLIds {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS_BAK
	WHERE TASK_ID = :1 
	AND SQL_ID IN (%s)`, strings.Join(placeholders, ", "))

	// 将 excludingSQLIds 展开为参数
	args := make([]interface{}, 0, len(excludingSQLIds)+1)
	args = append(args, taskId)
	for i, v := range excludingSQLIds {
		args[i+1] = v
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBySQLModules(ctx context.Context, db *sql.DB, taskId int, excludingSQLModules []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(excludingSQLModules) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(excludingSQLModules))
	for i := range excludingSQLModules {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS 
	WHERE TASK_ID = :1 
	AND MODULE IN (%s)`, strings.Join(placeholders, ", "))

	// 将 excludingSQLModules 展开为参数
	args := make([]interface{}, 0, len(excludingSQLModules)+1)
	args = append(args, taskId)
	for _, v := range excludingSQLModules {
		args = append(args, v)
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBakBySQLModules(ctx context.Context, db *sql.DB, taskId int, excludingSQLModules []any) (int64, error) {
	// 如果切片为空，直接返回，不执行删除
	if len(excludingSQLModules) == 0 {
		return 0, nil
	}

	// 动态生成占位符，例如 ":1, :2, :3"
	placeholders := make([]string, len(excludingSQLModules))
	for i := range excludingSQLModules {
		placeholders[i] = fmt.Sprintf(":%d", i+2)
	}
	deleteSQL := fmt.Sprintf(`
	DELETE 
	FROM TMS_SQLSET_STATEMENTS_BAK
	WHERE TASK_ID = :1 
	AND MODULE IN (%s)`, strings.Join(placeholders, ", "))

	// 将 excludingSQLModules 展开为参数
	args := make([]interface{}, 0, len(excludingSQLModules)+1)
	args = append(args, taskId)
	for _, v := range excludingSQLModules {
		args = append(args, v)
	}

	// 执行 SQL
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

// DeleteTMSSQLSetStatementsBySQLTexts deletes SQL statements from TMS_SQLSET_STATEMENTS table
func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBySQLTexts(
	ctx context.Context,
	db *sql.DB,
	taskId int,
	sqlTexts []any,
) (int64, error) {
	var patterns []string
	for _, v := range sqlTexts {
		if v == nil {
			continue
		}
		// 添加通配符以支持模糊匹配
		pattern := "%" + fmt.Sprint(v) + "%"
		patterns = append(patterns, pattern)
	}

	if len(patterns) == 0 {
		return 0, nil
	}

	// 使用 Oracle 占位符 :1, :2, 等
	var conditions []string
	for i := range patterns {
		conditions = append(conditions, fmt.Sprintf("UPPER(SQL_TEXT) LIKE :%d", i+2))
	}

	conditionString := strings.Join(conditions, " OR ")
	deleteSQL := "DELETE FROM TMS_SQLSET_STATEMENTS WHERE TASK_ID = :1 AND " + conditionString

	// 将 patterns 转换为 interface{} 切片并展开
	args := make([]interface{}, 0, len(patterns)+1)
	args = append(args, taskId)
	for _, v := range patterns {
		args = append(args, v)
	}

	log.Infof("delete tms_sqlset_statements by sqltexts sql: %s, args: %v", deleteSQL, args)
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

// DeleteTMSSQLSetStatementsBakBySQLTexts deletes SQL statements from TMS_SQLSET_STATEMENTS table
func (rw DatasourceReadWrite) DeleteTMSSQLSetStatementsBakBySQLTexts(
	ctx context.Context,
	db *sql.DB,
	taskId int,
	sqlTexts []any,
) (int64, error) {
	var patterns []string
	for _, v := range sqlTexts {
		if v == nil {
			continue
		}
		// 添加通配符以支持模糊匹配
		pattern := "%" + fmt.Sprint(v) + "%"
		patterns = append(patterns, pattern)
	}

	if len(patterns) == 0 {
		return 0, nil
	}

	// 使用 Oracle 占位符 :1, :2, 等
	var conditions []string
	for i := range patterns {
		conditions = append(conditions, fmt.Sprintf("UPPER(SQL_TEXT) LIKE :%d", i+2))
	}

	conditionString := strings.Join(conditions, " OR ")
	deleteSQL := "DELETE FROM TMS_SQLSET_STATEMENTS_BAK WHERE TASK_ID = :1 AND " + conditionString

	// 将 patterns 转换为 interface{} 切片并展开
	args := make([]interface{}, 0, len(patterns)+1)
	args = append(args, taskId)
	for _, v := range patterns {
		args = append(args, v)
	}

	log.Infof("delete tms_sqlset_statements_bak by sqltexts sql: %s, args: %v", deleteSQL, args)
	result, err := db.ExecContext(ctx, deleteSQL, args...)
	if err != nil {
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := result.RowsAffected()
	return affected, nil
}

func (rw DatasourceReadWrite) MergeTMSSQLSetStatements(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error {
	mergeSQL := `
MERGE INTO TMS_SQLSET_STATEMENTS t
USING (
    SELECT TASK_ID, SQLSET_NAME, SQLSET_OWNER, SQLSET_ID, SQL_ID, SQL_TEXT, PARSING_SCHEMA_NAME, PLAN_HASH_VALUE,
           BIND_DATA, COMMAND_TYPE, MODULE, ACTION, ELAPSED_TIME, EXECUTIONS, last_copy_time
    FROM TMS_SQLSET_STATEMENTS_BAK
    WHERE SQLSET_OWNER = :1 AND SQLSET_NAME = :2 AND TASK_ID = :3
) s
ON (
    t.SQLSET_NAME = s.SQLSET_NAME
    AND t.SQLSET_OWNER = s.SQLSET_OWNER
    AND t.SQLSET_ID = s.SQLSET_ID
    AND t.SQL_ID = s.SQL_ID
	AND t.TASK_ID = s.TASK_ID
)
WHEN NOT MATCHED THEN
    INSERT (
        TASK_ID, SQLSET_NAME, SQLSET_OWNER, SQLSET_ID, SQL_ID, SQL_TEXT, PARSING_SCHEMA_NAME, PLAN_HASH_VALUE,
        BIND_DATA, COMMAND_TYPE, MODULE, ACTION, ELAPSED_TIME, EXECUTIONS, LAST_COPY_TIME
    )
    VALUES (
        s.TASK_ID, s.SQLSET_NAME, s.SQLSET_OWNER, s.SQLSET_ID, s.SQL_ID, s.SQL_TEXT, s.PARSING_SCHEMA_NAME, s.PLAN_HASH_VALUE,
        s.BIND_DATA, s.COMMAND_TYPE, s.MODULE, s.ACTION, s.ELAPSED_TIME, s.EXECUTIONS, s.last_copy_time
    )`
	_, err := db.ExecContext(ctx, mergeSQL, sourceSchema, sqlsetName, taskId)
	if err != nil {
		log.Errorf("merge tms_sqlset_statements failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) UpdateTMSSQLSetStatementsReplayStatus(ctx context.Context, db *sql.DB, taskId int, sqlId string, replayStatus, replayExecCode string, replayFinishTime time.Time, messageLog string) error {
	updateSQL := `UPDATE TMS_SQLSET_STATEMENTS 
SET REPLAY = :1, 
REPLAY_STATUS = :2, 
REPLAY_EXEC_CODE = :3, 
REPLAY_FINISH_TIME = :4, 
MESSAGE_LOG = :5 
WHERE SQL_ID = :6 AND TASK_ID = :7`
	_, err := db.ExecContext(ctx, updateSQL, "Y", replayStatus, replayExecCode, replayFinishTime, messageLog, sqlId, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset_statements replay status failed, sqlId:%s, replay:%v, replayStatus:%s, replayFinishTime:%v, messageLog:%s, err:%v",
			sqlId, "Y", replayStatus, replayFinishTime, messageLog, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsBakByExecutions(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `DELETE FROM TMS_SQLSET_STATEMENTS_BAK WHERE EXECUTIONS <=0 AND TASK_ID = :1`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements_bak by executions failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) PruneTMSSQLSetStatementsByExecutions(ctx context.Context, db *sql.DB, taskId int) error {
	pruneSQL := `DELETE FROM TMS_SQLSET_STATEMENTS WHERE EXECUTIONS <=0 AND TASK_ID = :1`
	_, err := db.ExecContext(ctx, pruneSQL, taskId)
	if err != nil {
		log.Errorf("prune tms_sqlset_statements by executions failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) SaveTMSSQLSet(ctx context.Context, db *sql.DB, sqlset SQLSet) error {
	insertSQL := `
INSERT INTO TMS_SQLSET (TASK_ID,NAME,OWNER,DESCRIPTION,CREATED,LAST_MODIFIED,STATEMENT_COUNT)
VALUES (:1,:2,:3,:4,:5,:6,:7)`
	_, err := db.ExecContext(ctx, insertSQL, sqlset.TaskId, sqlset.Name, sqlset.Owner, sqlset.Description,
		sqlset.Created, sqlset.LastModified, sqlset.StatementCount)
	if err != nil {
		log.Errorf("insert into tms_sqlset failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) SaveTMSSQLSetBak(ctx context.Context, db *sql.DB, sqlset SQLSet) error {
	insertSQL := `
INSERT INTO TMS_SQLSET_BAK (TASK_ID,NAME,OWNER,DESCRIPTION,CREATED,LAST_MODIFIED,STATEMENT_COUNT)
VALUES (:1,:2,:3,:4,:5,:6,:7)`
	_, err := db.ExecContext(ctx, insertSQL, sqlset.TaskId, sqlset.Name, sqlset.Owner, sqlset.Description,
		sqlset.Created, sqlset.LastModified, sqlset.StatementCount)
	if err != nil {
		log.Errorf("insert into tms_sqlset_bak failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) SaveTMSSQLSetStatements(ctx context.Context, db *sql.DB, statements []SQLSetStatement) error {
	if len(statements) == 0 {
		return nil
	}

	insertSQL := `
INSERT INTO TMS_SQLSET_STATEMENTS (
	   							   TASK_ID,
                                   SQLSET_NAME,
                                   SQLSET_OWNER,
                                   SQLSET_ID,
                                   DIGEST_ID,
                                   SQL_ID,
                                   
                                   SQL_TEXT,
                                   PARSING_SCHEMA_NAME,
                                   PLAN_HASH_VALUE,
                                   BIND_DATA,
                                   
                                   COMMAND_TYPE,
                                   MODULE,
                                   ACTION,
                                   ELAPSED_TIME,
                                   EXECUTIONS)
VALUES (:1,:2,:3,:4,:5,:6,:7,:8,:9,:10,:11,:12,:13,:14,:15)`
	// 使用事务来批量插入数据
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		log.Errorf("begin transaction failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	stmt, err := tx.PrepareContext(ctx, insertSQL)
	if err != nil {
		log.Errorf("prepare statement failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	defer stmt.Close()
	for _, statement := range statements {
		_, err = stmt.ExecContext(ctx,
			statement.TaskID,
			statement.SQLSetName,
			statement.SQLSetOwner,
			statement.SQLSetID,
			statement.DigestID,
			statement.SQLID,

			statement.SQLText,
			statement.ParsingSchemaName,
			statement.PlanHashValue,
			statement.BindData,

			statement.CommandType,
			statement.Module,
			statement.Action,
			statement.ElapsedTime,
			statement.Executions)
		if err != nil {
			log.Errorf("insert into tms_sqlset_statements failed. %v", err)
			return dbCommon.WrapDBError(err)
		}
	}
	// 提交事务
	err = tx.Commit()
	if err != nil {
		log.Errorf("commit transaction failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) SaveTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, statements []SQLSetStatement) error {
	if len(statements) == 0 {
		return nil
	}

	insertSQL := `
INSERT INTO TMS_SQLSET_STATEMENTS_BAK (
	   							   TASK_ID,
                                   SQLSET_NAME,
                                   SQLSET_OWNER,
                                   SQLSET_ID,
                                   DIGEST_ID,
                                   SQL_ID,
                                   
                                   SQL_TEXT,
								   PARSING_SCHEMA_NAME,
                                   PLAN_HASH_VALUE,
                                   BIND_DATA,
                                   
                                   COMMAND_TYPE,
                                   MODULE,
                                   ACTION,
                                   ELAPSED_TIME,
                                   EXECUTIONS)
VALUES (:1,:2,:3,:4,:5,:6,:7,:8,:9,:10,:11,:12,:13,:14,:15)`
	// 使用事务来批量插入数据
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		log.Errorf("begin transaction failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	stmt, err := tx.PrepareContext(ctx, insertSQL)
	if err != nil {
		log.Errorf("prepare statement failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	defer stmt.Close()
	for _, statement := range statements {
		_, err = stmt.ExecContext(ctx,
			statement.TaskID,
			statement.SQLSetName,
			statement.SQLSetOwner,
			statement.SQLSetID,
			statement.DigestID,
			statement.SQLID,

			statement.SQLText,
			statement.ParsingSchemaName,
			statement.PlanHashValue,
			statement.BindData,

			statement.CommandType,
			statement.Module,
			statement.Action,
			statement.ElapsedTime,
			statement.Executions)
		if err != nil {
			log.Errorf("insert into tms_sqlset_statements_bak failed. %v", err)
			return dbCommon.WrapDBError(err)
		}
	}
	// 提交事务
	err = tx.Commit()
	if err != nil {
		log.Errorf("commit transaction failed. %v", err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

func (rw DatasourceReadWrite) UpdateTMSSQLSet(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string, created time.Time, lastModified time.Time, statementCount uint64) (int64, error) {
	updateSQL := `
UPDATE TMS_SQLSET
SET created = :1, last_modified = :2, statement_count = :3
WHERE owner = :4 AND name = :5 AND task_id = :6`
	ret, err := db.ExecContext(ctx, updateSQL,
		created, lastModified, statementCount, sourceSchema, sqlsetName, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset failed. %v", err)
		return 0, dbCommon.WrapDBError(err)
	}
	affected, _ := ret.RowsAffected()
	return affected, err
}

func (rw DatasourceReadWrite) UpdateTMSSQLSetStatementsRewriteInfo(ctx context.Context, db *sql.DB, taskId int, sqlId string, rewriteSQLText, rewritePrompts string) error {
	updateSQL := `
UPDATE TMS_SQLSET_STATEMENTS
SET REWRITE_SQL_TEXT = :1, REWRITE_PROMPTS = :2
WHERE SQL_ID = :3 AND TASK_ID = :4`
	_, err := db.ExecContext(ctx, updateSQL, rewriteSQLText, rewritePrompts, sqlId, taskId)
	if err != nil {
		log.Errorf("update tms_sqlset_statements rewrite info failed, sqlId:%s, rewriteSQLText:%s, err:%v", sqlId, rewriteSQLText, err)
		return dbCommon.WrapDBError(err)
	}
	return nil
}

// Oracle performance monitoring methods

func (rw DatasourceReadWrite) GetOracleActiveTmsSessions(ctx context.Context, db *sql.DB) ([]OracleActiveTmsSession, error) {
	/*
		SQL 查询说明：
		- 查询 TMS 会话的进度信息。
		- 字段说明：
			- USERNAME: 用户名
			- TARGET: 目标
			- SID: 会话 ID
			- SERIAL#: 会话序列号
			- OPNAME: 操作名称
			- 执行进度: 当前进度百分比
			- 剩余秒数: 剩余时间（秒）
			- 剩余分钟: 剩余时间（分钟）
			- 剩余小时: 剩余时间（小时）
			- SQL_TEXT: 执行 SQL
			- LAST_ACTIVE_TIME: 开始时间
	*/
	query := `
		SELECT a.SID, a.SERIAL#, a.USERNAME, a.STATUS, a.SQL_ID, to_char(a.SQL_EXEC_START,'YYYY-MM-DD HH24:MI:SS') as SQL_EXEC_START, a.EVENT, a.MACHINE, a.PROGRAM
		FROM v$session a
		WHERE ( a.MODULE = 'TMS' or a.MODULE LIKE 'tms-server%' )
		ORDER BY to_char(a.SQL_EXEC_START,'YYYY-MM-DD HH24:MI:SS')
	`
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query active tms sessions: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	activeTmsSessions := make([]OracleActiveTmsSession, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to scan active tms sessions: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		activeTmsSessions = append(activeTmsSessions, OracleActiveTmsSession{
			Sid:          rowData["SID"].String(),
			Serial:       rowData["SERIAL#"].String(),
			Username:     rowData["USERNAME"].String(),
			Status:       rowData["STATUS"].String(),
			SqlID:        rowData["SQL_ID"].String(),
			SqlExecStart: rowData["SQL_EXEC_START"].String(),
			Event:        rowData["EVENT"].String(),
			Machine:      rowData["MACHINE"].String(),
			Program:      rowData["PROGRAM"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during active tms sessions row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return activeTmsSessions, nil
}

func (rw DatasourceReadWrite) GetOracleTmsSessionProgress(ctx context.Context, db *sql.DB, username string) ([]OracleTmsSessionProgress, error) {
	/*
		SQL 查询说明：
		- 查询 TMS 会话的进度信息。
		- 字段说明：
			- USERNAME: 用户名
			- TARGET: 目标
			- SID: 会话 ID
			- SERIAL#: 会话序列号
			- OPNAME: 操作名称
			- 执行进度: 当前进度百分比
			- 剩余秒数: 剩余时间（秒）
			- 剩余分钟: 剩余时间（分钟）
			- 剩余小时: 剩余时间（小时）
			- SQL_TEXT: 执行 SQL
			- LAST_ACTIVE_TIME: 开始时间
	*/
	query := `
		SELECT A.USERNAME, A.TARGET, A.SID, a.SERIAL#, A.OPNAME,
		       ROUND(A.SOFAR * 100 / A.TOTALWORK, 0) || '%' AS "执行进度",
		       A.TIME_REMAINING "剩余秒数",
		       TRUNC(A.TIME_REMAINING / 60, 2) "剩余分钟",
		       TRUNC(A.TIME_REMAINING / 60/60, 2) "剩余小时",
		       B.SQL_TEXT "执行SQL",
		       B.LAST_ACTIVE_TIME "开始时间"
		FROM V$SESSION_LONGOPS A, V$SQLAREA B
		WHERE A.TIME_REMAINING <> 0
		AND A.SQL_ADDRESS = B.ADDRESS
		AND A.SQL_HASH_VALUE = B.HASH_VALUE
		AND A.USERNAME = :1
	`
	rows, err := db.QueryContext(ctx, query, strings.ToUpper(username))
	if err != nil {
		log.Errorf("failed to query tms session progress: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	tmsSessionProgress := make([]OracleTmsSessionProgress, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to scan tms session progress: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}

		// Parse numeric values from ColumnData
		timeRemaining, timeRemainingErr := rowData["剩余秒数"].Int64()
		if timeRemainingErr != nil {
			log.Warnf("failed to parse remainingSeconds '%s', using 0: %v", rowData["剩余秒数"].String(), timeRemainingErr)
			timeRemaining = 0
		}

		timeRemainingMin, timeRemainingMinErr := rowData["剩余分钟"].Float64()
		if timeRemainingMinErr != nil {
			log.Warnf("failed to parse remainingMinutes '%s', using 0: %v", rowData["剩余分钟"].String(), timeRemainingMinErr)
			timeRemainingMin = 0
		}

		timeRemainingHr, timeRemainingHrErr := rowData["剩余小时"].Float64()
		if timeRemainingHrErr != nil {
			log.Warnf("failed to parse remainingHours '%s', using 0: %v", rowData["剩余小时"].String(), timeRemainingHrErr)
			timeRemainingHr = 0
		}

		tmsSessionProgress = append(tmsSessionProgress, OracleTmsSessionProgress{
			Username:             rowData["USERNAME"].String(),
			Target:               rowData["TARGET"].String(),
			Sid:                  rowData["SID"].String(),
			Serial:               rowData["SERIAL#"].String(),
			Opname:               rowData["OPNAME"].String(),
			Progress:             rowData["执行进度"].String(),
			TimeRemaining:        timeRemaining,
			TimeRemainingMinutes: timeRemainingMin,
			TimeRemainingHours:   timeRemainingHr,
			SqlText:              rowData["执行SQL"].String(),
			StartTime:            rowData["开始时间"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during tms session progress row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return tmsSessionProgress, nil
}

func (rw DatasourceReadWrite) GetOracleTmsSessionRatio(ctx context.Context, db *sql.DB) ([]TmsUserStats, error) {
	query := `
		/*
			SQL 查询说明：
			- 查询 TMS 会话的占比。
			- 字段说明：
				- TMSPCT: TMS 会话的占比
		*/
		select
		round((select count(*)
		from v$session
		where MODULE like '%tms-server%')/
		(Select count(*)
		from v$session
		where  TYPE<>'BACKGROUND'),2)*100 as tmspct,
		(Select count(*)
		from v$session
		where  TYPE<>'BACKGROUND') as totaluser,
		(select count(*)
		from v$session
		where MODULE like '%tms-server%') as tmsuser
		from dual
	`
	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query tms session ratio: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	tmsUserStats := make([]TmsUserStats, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to scan tms session ratio: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}

		tmsUserStats = append(tmsUserStats, TmsUserStats{
			TmsPct:    rowData["TMSPCT"].String(),
			TotalUser: rowData["TOTALUSER"].String(),
			TmsUser:   rowData["TMSUSER"].String(),
		})
	}

	return tmsUserStats, nil
}

func (rw DatasourceReadWrite) GetOraclePhysicalFileIOStats(ctx context.Context, db *sql.DB) ([]OraclePhysicalFileIOStats, error) {
	query := `
		/*
			SQL 查询说明：
			- 查询 Oracle 数据库中最热的前 10 个数据文件（按单块读时间排序）。
			- 字段说明：
				- FILE_NAME: 文件名
				- TABLESPACE_NAME: 表空间名
				- CS: 单块读平均耗时（毫秒）
				- READTIME_S: 读操作总耗时（秒）
				- WIRTETIME_S: 写操作总耗时（秒）
		*/
	SELECT * FROM (
		SELECT ts.name AS ts, df.name as fn, round(fs.phyrds/1000) "Reads", round(fs.phywrts/1000) "Writes", round(fs.phyblkrd*df.BLOCK_SIZE/1024/1024) AS "br", round(fs.phyblkwrt*df.BLOCK_SIZE/1024/1024) AS "bw", fs.readtim/100 "RTimes", fs.writetim/100 "WTimes"
		FROM v$tablespace ts, v$datafile df, v$filestat fs
		WHERE ts.ts# = df.ts#
		AND df.file# = fs.file#
		UNION
		SELECT ts.name AS ts, tf.name as fn, round(ts.phyrds/1000) "Reads", round(ts.phywrts/1000) "Writes", round(ts.phyblkrd*tf.BLOCK_SIZE/1024/1024) AS "br", round(ts.phyblkwrt*tf.BLOCK_SIZE/1024/1024) AS "bw", ts.readtim /100 "RTimes", ts.writetim/100 "WTimes"
		FROM v$tablespace ts, v$tempfile tf, v$tempstat ts
		WHERE ts.ts# = tf.ts#
		AND tf.file# = ts.file#
		order by 5 desc,6 desc
	)WHERE ROWNUM <= 20
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query physical file io stats: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	physicalFileIOStats := make([]OraclePhysicalFileIOStats, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to scan physical file io stats: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		physicalFileIOStats = append(physicalFileIOStats, OraclePhysicalFileIOStats{
			Fn:     rowData["FN"].String(),
			Ts:     rowData["TS"].String(),
			Reads:  rowData["READS"].String(),
			Writes: rowData["WRITES"].String(),
			Br:     rowData["BR"].String(),
			Bw:     rowData["BW"].String(),
			RTimes: rowData["RTIMES"].String(),
			WTimes: rowData["WTIMES"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during physical file io stats row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return physicalFileIOStats, nil
}

func (rw DatasourceReadWrite) GetOracleHotFileInformation(ctx context.Context, db *sql.DB) ([]OracleHotFileInfo, error) {
	query := `
		/*
			SQL 查询说明：
			- 查询 Oracle 数据库中最热的前 10 个数据文件（按单块读时间排序）。
			- 字段说明：
				- FILE_NAME: 文件名
				- TABLESPACE_NAME: 表空间名
				- CS: 单块读平均耗时（毫秒）
				- READTIME_S: 读操作总耗时（秒）
				- WIRTETIME_S: 写操作总耗时（秒）
		*/
    SELECT T.FILE_NAME,
       T.TABLESPACE_NAME,
       ROUND(S.SINGLEBLKRDTIM / S.SINGLEBLKRDS, 2)/10 AS CS,    --厘秒/10=毫秒
       S.READTIM/10 READTIME_S, --厘秒/10=毫秒
       S.WRITETIM/10 WIRTETIME_S --厘秒/10=毫秒
	FROM V$FILESTAT S, DBA_DATA_FILES T
	WHERE S.FILE# = T.FILE_ID
		AND ROWNUM <= 20
		ORDER BY CS DESC
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query hot file information: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	hotFiles := make([]OracleHotFileInfo, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to scan hot file information: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}

		cs, err := rowData["CS"].Float64()
		if err != nil {
			log.Errorf("failed to parse CS value: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}

		readTimeS, err := rowData["READTIME_S"].Float64()
		if err != nil {
			log.Errorf("failed to parse READTIME_S value: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}

		writeTimeS, err := rowData["WIRTETIME_S"].Float64()
		if err != nil {
			log.Errorf("failed to parse WIRTETIME_S value: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}

		hotFiles = append(hotFiles, OracleHotFileInfo{
			FileName:       rowData["FILE_NAME"].String(),
			TablespaceName: rowData["TABLESPACE_NAME"].String(),
			CS:             cs,
			ReadTimeS:      readTimeS,
			WriteTimeS:     writeTimeS,
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during hot file information row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return hotFiles, nil
}

func (rw DatasourceReadWrite) GetOracleTableSpaceStats(ctx context.Context, db *sql.DB) ([]OracleTableSpaceStats, error) {
	query := `
		SELECT
		UPPER( F.TABLESPACE_NAME ) "TABLESPACE_NAME",
		D.TOT_GROOTTE_MB "TOT_GROOTTE_MB",
		D.TOT_GROOTTE_MB - F.TOTAL_BYTES "USED_SPACE",
		TO_CHAR( ROUND(( D.TOT_GROOTTE_MB - F.TOTAL_BYTES ) / D.TOT_GROOTTE_MB * 100, 2 ), '990.99' ) || '%' "USED_RATIO",
		F.TOTAL_BYTES "TOTAL_BYTES",
		F.MAX_BYTES "MAX_BYTES"
		FROM
		(
		SELECT
		TABLESPACE_NAME,
		ROUND( SUM( BYTES ) / ( 1024 * 1024 ), 2 ) TOTAL_BYTES,
		ROUND( MAX( BYTES ) / ( 1024 * 1024 ), 2 ) MAX_BYTES
		FROM
		SYS.DBA_FREE_SPACE
		GROUP BY
		TABLESPACE_NAME
		) F,
		(
		SELECT
		DD.TABLESPACE_NAME,ROUND( SUM( DD.BYTES ) / ( 1024 * 1024 ), 2 ) TOT_GROOTTE_MB
		FROM
		SYS.DBA_DATA_FILES DD
		GROUP BY
		DD.TABLESPACE_NAME
		) D
		WHERE
		D.TABLESPACE_NAME = F.TABLESPACE_NAME
		ORDER BY 1
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query table space stats: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleTableSpaceStats := make([]OracleTableSpaceStats, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query table space stats: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		oracleTableSpaceStats = append(oracleTableSpaceStats, OracleTableSpaceStats{
			TablespaceName: rowData["TABLESPACE_NAME"].String(),
			TotGrootteMb:   rowData["TOT_GROOTTE_MB"].String(),
			UsedSpace:      rowData["USED_SPACE"].String(),
			UsedRatio:      rowData["USED_RATIO"].String(),
			TotalBytes:     rowData["TOTAL_BYTES"].String(),
			MaxBytes:       rowData["MAX_BYTES"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query table space stats row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return oracleTableSpaceStats, nil
}

func (rw DatasourceReadWrite) GetOracleObjectStats(ctx context.Context, db *sql.DB) ([]OracleObjectStats, error) {
	query := `
		select 
		(select count(*) from user_tables) as TABLES,
		(select count(*) from user_views) as VIEWS,
		(select count(*) from user_sequences) as SEQUENCES,
		(select count(*) from user_indexes) as INDEXES,
		(select count(*) from user_objects where object_type='PROCEDURE') as PROCEDURES,
		(select count(*) from user_objects where object_type='PACKAGE') as PACKAGES,
		(select count(*) from user_constraints) as CONSTRAINTS,
		(select count(*) from user_objects where object_type='TRIGGER') as TRIGGERS,
		(select count(*) from user_objects where object_type='FUNCTION') as FUNCTIONS
		from dual
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query object stats: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleObjectStats := make([]OracleObjectStats, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query object stats: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		oracleObjectStats = append(oracleObjectStats, OracleObjectStats{
			Tables:      rowData["TABLES"].String(),
			Views:       rowData["VIEWS"].String(),
			Sequences:   rowData["SEQUENCES"].String(),
			Indexes:     rowData["INDEXES"].String(),
			Procedures:  rowData["PROCEDURES"].String(),
			Packages:    rowData["PACKAGES"].String(),
			Constraints: rowData["CONSTRAINTS"].String(),
			Triggers:    rowData["TRIGGERS"].String(),
			Functions:   rowData["FUNCTIONS"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query object stats row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return oracleObjectStats, nil
}

func (rw DatasourceReadWrite) GetOracleObjectStatsBySchema(ctx context.Context, db *sql.DB, schemas []*channel.ChannelSchema) ([]OracleObjectStats, error) {
	schemaNames := make([]string, 0, len(schemas))
	for _, schema := range schemas {
		schemaNames = append(schemaNames, schema.SchemaNameS)
	}

	querySql := `
		SELECT
    owner AS SCHEMA_NAME,
    COUNT(CASE WHEN object_type = 'TABLE' THEN 1 END) AS TABLES,
    COUNT(CASE WHEN object_type = 'VIEW' THEN 1 END) AS VIEWS,
    COUNT(CASE WHEN object_type = 'SEQUENCE' THEN 1 END) AS SEQUENCES,
    COUNT(CASE WHEN object_type = 'INDEX' THEN 1 END) AS INDEXES,
    COUNT(CASE WHEN object_type = 'PROCEDURE' THEN 1 END) AS PROCEDURES,
    COUNT(CASE WHEN object_type = 'PACKAGE' THEN 1 END) AS PACKAGES,
    COUNT(CASE WHEN object_type = 'CONSTRAINT' THEN 1 END) AS CONSTRAINTS,
    COUNT(CASE WHEN object_type = 'TRIGGER' THEN 1 END) AS TRIGGERS,
    COUNT(CASE WHEN object_type = 'FUNCTION' THEN 1 END) AS FUNCTIONS
	FROM
		all_objects
	WHERE
		object_type IN ('TABLE', 'VIEW', 'SEQUENCE', 'INDEX', 'PROCEDURE', 'PACKAGE', 'TRIGGER', 'FUNCTION')
		and owner in(${SCHAMES})
	GROUP BY
		owner
	ORDER BY
		owner
	`

	querySql = strings.ReplaceAll(querySql, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemaNames, "','")))

	rows, err := db.QueryContext(ctx, querySql)
	if err != nil {
		log.Errorf("failed to query object stats: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleObjectStats := make([]OracleObjectStats, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query object stats: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		oracleObjectStats = append(oracleObjectStats, OracleObjectStats{
			SchemaName:  rowData["SCHEMA_NAME"].String(),
			Tables:      rowData["TABLES"].String(),
			Views:       rowData["VIEWS"].String(),
			Sequences:   rowData["SEQUENCES"].String(),
			Indexes:     rowData["INDEXES"].String(),
			Procedures:  rowData["PROCEDURES"].String(),
			Packages:    rowData["PACKAGES"].String(),
			Constraints: rowData["CONSTRAINTS"].String(),
			Triggers:    rowData["TRIGGERS"].String(),
			Functions:   rowData["FUNCTIONS"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query object stats row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return oracleObjectStats, nil
}

func (rw DatasourceReadWrite) GetOracleCpuNumber(ctx context.Context, db *sql.DB) (int64, error) {
	query := `
		select a.VALUE
		from v$osstat a where a.STAT_NAME in ('NUM_CPUS')
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query oracle cpu number: %v", err)
		return 0, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleCpuNumber := int64(0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query oracle cpu number: %v", err)
			return 0, dbCommon.WrapDBError(err)
		}

		oracleCpuNumber, err = rowData["VALUE"].Int64()
		if err != nil {
			log.Errorf("failed to parse VALUE value: %v", err)
			return 0, dbCommon.WrapDBError(err)
		}
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query oracle cpu number row iteration: %v", err)
		return 0, dbCommon.WrapDBError(err)
	}

	return oracleCpuNumber, nil
}

func (rw DatasourceReadWrite) GetOracleMemory(ctx context.Context, db *sql.DB) (int64, error) {
	query := `
		select
		round(a.VALUE/1024/1024/1000) as VALUE
		from v$osstat a where a.STAT_NAME in ('PHYSICAL_MEMORY_BYTES')
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query oracle memory: %v", err)
		return 0, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleMemory := int64(0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query oracle memory: %v", err)
			return 0, dbCommon.WrapDBError(err)
		}

		oracleMemory, err = rowData["VALUE"].Int64()
		if err != nil {
			log.Errorf("failed to parse VALUE value: %v", err)
			return 0, dbCommon.WrapDBError(err)
		}
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query oracle memory row iteration: %v", err)
		return 0, dbCommon.WrapDBError(err)
	}

	return oracleMemory, nil
}

func (rw DatasourceReadWrite) GetOracleTableNumberRowsBySchema(ctx context.Context, db *sql.DB, schemas []*channel.ChannelSchema, page, pageSize int) ([]OracleTableNumberRows, int64, error) {
	// 遍历schemas，提取SchemaNameS字段
	schemaNames := make([]string, 0, len(schemas))
	for _, schema := range schemas {
		schemaNames = append(schemaNames, schema.SchemaNameS)
	}

	// First, get the total count
	countSQL := `SELECT COUNT(*)
               FROM all_tables
               WHERE owner in ${SCHAMES}`

	countSQL = strings.ReplaceAll(countSQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemaNames, "','")))

	_, countRes, err := oracle.Query(ctx, db, countSQL)
	if err != nil {
		log.Errorf("GetSQLSetStatementsListByReplayStatusWithPagination count failed. %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	var total int64 = 0
	if len(countRes) > 0 {
		if count, parseErr := strconv.ParseInt(countRes[0]["COUNT(*)"], 10, 64); parseErr == nil {
			total = count
		}
	}

	querySQL := `
	select * from (
		SELECT owner, table_name, num_rows, last_analyzed
			, ROW_NUMBER() OVER (ORDER BY NVL(num_rows, 0) desc) as rn
		FROM
			all_tables
		WHERE
			owner in (${SCHAMES})
		ORDER BY NVL(num_rows, 0) desc
	) where rn > ${OFFSET} AND rn <= ${LIMIT}
	`
	offset := (page - 1) * pageSize

	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemaNames, "','")))
	querySQL = strings.ReplaceAll(querySQL, "${OFFSET}", fmt.Sprintf("%d", offset))
	querySQL = strings.ReplaceAll(querySQL, "${LIMIT}", fmt.Sprintf("%d", offset+pageSize))

	rows, err := db.QueryContext(ctx, querySQL)
	if err != nil {
		log.Errorf("failed to query object stats: %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleObjectStats := make([]OracleTableNumberRows, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query object stats: %v", err)
			return nil, 0, dbCommon.WrapDBError(err)
		}
		oracleObjectStats = append(oracleObjectStats, OracleTableNumberRows{
			Owner:        rowData["OWNER"].String(),
			TableName:    rowData["TABLE_NAME"].String(),
			NumRows:      rowData["NUM_ROWS"].String(),
			LastAnalyzed: rowData["LAST_ANALYZED"].String(),
			Rn:           rowData["RN"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query object stats row iteration: %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	return oracleObjectStats, total, nil
}

func (rw DatasourceReadWrite) GetTiDBRowCount(ctx context.Context, db *sql.DB, schemaTableMap map[structs.SchemaTablePair]*channel.ChannelSchemaTable) ([]TiDBRowCount, error) {
	baseQuery := `
		select table_schema,table_name,table_rows
		FROM
			information_schema.TABLES
		WHERE
			UPPER(table_schema) = '${SCHEMA}'
			and UPPER(table_name) = '${TABLE}'
	`
	query := baseQuery
	index := 0

	for schemaTable, _ := range schemaTableMap {
		if index > 0 {
			query += " union all "
			query += baseQuery
		}
		query = strings.ReplaceAll(query, "${SCHEMA}", schemaTableMap[schemaTable].SchemaNameT)
		query = strings.ReplaceAll(query, "${TABLE}", schemaTableMap[schemaTable].TableNameT)
		index++
	}

	log.Debugf("GetTiDBRowCount query: %s", query)

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		log.Errorf("failed to query object stats: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	tiDBRowCount := make([]TiDBRowCount, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query object stats: %v", err)
			return nil, dbCommon.WrapDBError(err)
		}
		tiDBRowCount = append(tiDBRowCount, TiDBRowCount{
			SchemaName: rowData["TABLE_SCHEMA"].String(),
			TableName:  rowData["TABLE_NAME"].String(),
			TableRows:  rowData["TABLE_ROWS"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query object stats row iteration: %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	return tiDBRowCount, nil
}

func (rw DatasourceReadWrite) GetOracleObjectList(ctx context.Context, db *sql.DB, schemaName string, objectType string, page, pageSize int) ([]OracleObjectList, int64, error) {

	// First, get the total count
	countSQL := `SELECT COUNT(*)
               FROM all_objects
               WHERE object_type = '${OBJECT_TYPE}'
               and owner = '${SCHEMA_NAME}'`

	countSQL = strings.ReplaceAll(countSQL, "${OBJECT_TYPE}", objectType)
	countSQL = strings.ReplaceAll(countSQL, "${SCHEMA_NAME}", schemaName)

	_, countRes, err := oracle.Query(ctx, db, countSQL)
	if err != nil {
		log.Errorf("GetOracleObjectList count failed. %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	var total int64 = 0
	if len(countRes) > 0 {
		if count, parseErr := strconv.ParseInt(countRes[0]["COUNT(*)"], 10, 64); parseErr == nil {
			total = count
		}
	}

	querySQL := `
	select * from (SELECT OWNER, OBJECT_NAME, object_type, STATUS, ROW_NUMBER() OVER (ORDER BY OBJECT_NAME) as rn
               FROM all_objects
               WHERE object_type = '${OBJECT_TYPE}'
                 and owner = '${SCHEMA_NAME}'
               order by OBJECT_NAME)
	where  rn > ${OFFSET} and rn <= ${LIMIT}
	`
	offset := (page - 1) * pageSize

	querySQL = strings.ReplaceAll(querySQL, "${OBJECT_TYPE}", objectType)
	querySQL = strings.ReplaceAll(querySQL, "${SCHEMA_NAME}", schemaName)
	querySQL = strings.ReplaceAll(querySQL, "${OFFSET}", fmt.Sprintf("%d", offset))
	querySQL = strings.ReplaceAll(querySQL, "${LIMIT}", fmt.Sprintf("%d", offset+pageSize))

	rows, err := db.QueryContext(ctx, querySQL)
	if err != nil {
		log.Errorf("failed to query object list: %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	oracleObjectList := make([]OracleObjectList, 0)
	for rows.Next() {
		rowData, err := database.ScanRowWithUppercaseKeys(rows)
		if err != nil {
			log.Errorf("failed to query object stats: %v", err)
			return nil, 0, dbCommon.WrapDBError(err)
		}
		oracleObjectList = append(oracleObjectList, OracleObjectList{
			Owner:      rowData["OWNER"].String(),
			ObjectName: rowData["OBJECT_NAME"].String(),
			ObjectType: rowData["OBJECT_TYPE"].String(),
			Status:     rowData["STATUS"].String(),
			Rn:         rowData["RN"].String(),
		})
	}

	if err = rows.Err(); err != nil {
		log.Errorf("error occurred during query object stats row iteration: %v", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}

	return oracleObjectList, total, nil
}

// GetOracleSegmentSizes 从Oracle查询segment size信息
func (rw DatasourceReadWrite) GetOracleSegmentSizes(ctx context.Context, db *sql.DB, schemas []string) ([]OracleSegmentSize, error) {
	querySQL := `
SELECT
	m.owner,
    m.segment_name,
    SUM(CASE WHEN m.segment_type = 'TABLE' THEN m.bytes ELSE 0 END) / 1024 / 1024 AS sum_t,
    SUM(CASE WHEN m.segment_type = 'TABLE PARTITION' THEN m.bytes ELSE 0 END) / 1024 / 1024 AS sum_tp,
    SUM(CASE WHEN m.segment_type = 'TABLE SUBPARTITION' THEN m.bytes ELSE 0 END) / 1024 / 1024 AS sum_tsup,
    SUM(CASE WHEN m.segment_type = 'INDEX' THEN m.bytes ELSE 0 END) / 1024 / 1024 AS sum_i,
    SUM(CASE WHEN m.segment_type = 'INDEX PARTITION' THEN m.bytes ELSE 0 END) / 1024 / 1024 AS sum_ip,
    SUM(CASE WHEN m.segment_type = 'INDEX SUBPARTITION' THEN m.bytes ELSE 0 END) / 1024 / 1024 AS sum_isp
FROM
    dba_segments m
WHERE
    m.owner in (${SCHAMES})
    AND m.segment_name NOT LIKE 'BIN$%' 
GROUP BY
    m.owner, m.segment_name
ORDER BY
    m.owner, m.segment_name`

	querySQL = strings.ReplaceAll(querySQL, "${SCHAMES}", fmt.Sprintf("'%s'", strings.Join(schemas, "','")))

	log.Debugf("GetOracleSegmentSizes.sql: %s", querySQL)
	_, res, err := oracle.Query(ctx, db, querySQL)
	if err != nil {
		log.Errorf("query oracle segment sizes failed. %v", err)
		return nil, dbCommon.WrapDBError(err)
	}

	segmentSizes := make([]OracleSegmentSize, 0, len(res))
	for _, item := range res {
		var (
			owner       string
			segmentName string
			sumT        float64
			sumTp       float64
			sumTsup     float64
			sumI        float64
			sumIp       float64
			sumIsp      float64
		)

		owner = item["OWNER"]
		segmentName = item["SEGMENT_NAME"]
		sumT, _ = strconv.ParseFloat(item["SUM_T"], 64)
		sumTp, _ = strconv.ParseFloat(item["SUM_TP"], 64)
		sumTsup, _ = strconv.ParseFloat(item["SUM_TSUP"], 64)
		sumI, _ = strconv.ParseFloat(item["SUM_I"], 64)
		sumIp, _ = strconv.ParseFloat(item["SUM_IP"], 64)
		sumIsp, _ = strconv.ParseFloat(item["SUM_ISP"], 64)

		segmentSizes = append(segmentSizes, OracleSegmentSize{
			Owner:       owner,
			SegmentName: segmentName,
			SumT:        sumT,
			SumTp:       sumTp,
			SumTsup:     sumTsup,
			SumI:        sumI,
			SumIp:       sumIp,
			SumIsp:      sumIsp,
		})
	}

	return segmentSizes, nil
}

// SaveOracleSegmentSizesToTiDB 使用GORM将Oracle segment size信息保存到TiDB
func (rw DatasourceReadWrite) SaveOracleSegmentSizesToTiDB(ctx context.Context, segmentSizes []*OracleSegmentSizeEntity) error {
	if len(segmentSizes) == 0 {
		return nil
	}

	// 使用GORM批量插入数据
	err := rw.DB(ctx).CreateInBatches(segmentSizes, 1000).Error
	if err != nil {
		log.Errorf("save oracle segment sizes with gorm failed. %v", err)
		return dbCommon.WrapDBError(err)
	}

	log.Infof("save oracle segment sizes with gorm successfully, count: %d", len(segmentSizes))
	return nil
}

// DeleteOracleSegmentSizesByTaskId 按taskId物理删除Oracle segment size信息
func (rw DatasourceReadWrite) DeleteOracleSegmentSizesByTaskId(ctx context.Context, taskId int) error {
	// 使用GORM物理删除指定taskId的所有记录（绕过逻辑删除）
	result := rw.DB(ctx).Unscoped().Where("task_id = ?", taskId).Delete(&OracleSegmentSizeEntity{})
	if result.Error != nil {
		log.Errorf("delete oracle segment sizes by taskId failed. taskId:%d, err:%v", taskId, result.Error)
		return dbCommon.WrapDBError(result.Error)
	}

	log.Infof("delete oracle segment sizes by taskId successfully. taskId:%d, affected rows:%d", taskId, result.RowsAffected)
	return nil
}

func (rw DatasourceReadWrite) GetOracleSegmentSizesFromTiDB(ctx context.Context, taskId int) ([]OracleSegmentSize, error) {
	// 由于SQL已内置Top10+Other聚合，page/pageSize参数不再生效
	query := `WITH ranked_sizes AS (
  SELECT
    owner, segment_name, 
    COALESCE(sum_t, 0) as sum_t, 
    COALESCE(sum_tp, 0) as sum_tp, 
    COALESCE(sum_tsup, 0) as sum_tsup, 
    COALESCE(sum_i, 0) as sum_i, 
    COALESCE(sum_ip, 0) as sum_ip, 
    COALESCE(sum_isp, 0) as sum_isp,
    COALESCE(sum_t, 0) + COALESCE(sum_tp, 0) + COALESCE(sum_tsup, 0) + COALESCE(sum_i, 0) + COALESCE(sum_ip, 0) + COALESCE(sum_isp, 0) AS total_size,
    ROW_NUMBER() OVER(
      ORDER BY
        (
          COALESCE(sum_t, 0) + COALESCE(sum_tp, 0) + COALESCE(sum_tsup, 0) + COALESCE(sum_i, 0) + COALESCE(sum_ip, 0) + COALESCE(sum_isp, 0)
        ) DESC
    ) as rn
  FROM
    tms_oracle_segment_sizes ss
  inner join channel_schema_tables  ct 
  		on ss.owner=ct.schema_name_s 
		and ss.segment_name COLLATE utf8mb4_bin = ct.table_name_s COLLATE utf8mb4_bin
		and ss.task_id=ct.task_id
        where ss.task_id=${TASK_ID}
) 
SELECT
  owner, segment_name, sum_t, sum_tp, sum_tsup, sum_i, sum_ip, sum_isp, total_size
FROM
  ranked_sizes
WHERE
  rn <= 10
UNION ALL
SELECT
  'OTHER' AS owner,
  'Remaining records summed' AS segment_name,
  COALESCE(SUM(sum_t), 0),
  COALESCE(SUM(sum_tp), 0),
  COALESCE(SUM(sum_tsup), 0),
  COALESCE(SUM(sum_i), 0),
  COALESCE(SUM(sum_ip), 0),
  COALESCE(SUM(sum_isp), 0),
  COALESCE(SUM(total_size), 0)
FROM
  ranked_sizes
WHERE
  rn > 10`

	query = strings.ReplaceAll(query, "${TASK_ID}", fmt.Sprintf("%d", taskId))

	db := rw.DB(ctx)
	rows, err := db.Raw(query).Rows()
	if err != nil {
		log.Errorf("get oracle segment sizes from TiDB failed. taskId:%d, err:%v", taskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	defer rows.Close()

	segmentSizes := make([]OracleSegmentSize, 0)
	for rows.Next() {
		var seg OracleSegmentSize
		// 结构体字段: Owner, SegmentName, SumT, SumTp, SumTsup, SumI, SumIp, SumIsp, TotalSize
		err := rows.Scan(
			&seg.Owner,
			&seg.SegmentName,
			&seg.SumT,
			&seg.SumTp,
			&seg.SumTsup,
			&seg.SumI,
			&seg.SumIp,
			&seg.SumIsp,
			&seg.TotalSize,
		)
		if err != nil {
			log.Errorf("scan oracle segment sizes from TiDB failed. taskId:%d, err:%v", taskId, err)
			return nil, dbCommon.WrapDBError(err)
		}
		segmentSizes = append(segmentSizes, seg)
	}

	return segmentSizes, nil
}

func (rw DatasourceReadWrite) GetOracleSegmentSumSizesFromTiDB(ctx context.Context, taskId int) (float64, float64, error) {
	query := `select sum(sum_t)+sum(sum_tp)+sum(sum_tsup) as sum_table, sum(sum_i)+sum(sum_ip)+sum(sum_isp) as sum_index from tms_oracle_segment_sizes where task_id=?`
	db := rw.DB(ctx)
	row := db.Raw(query, taskId).Row()
	var sumTable, sumIndex float64
	err := row.Scan(&sumTable, &sumIndex)
	if err != nil {
		log.Errorf("get oracle segment sum sizes from TiDB failed. taskId:%d, err:%v", taskId, err)
		return 0, 0, dbCommon.WrapDBError(err)
	}
	return sumTable, sumIndex, nil
}

// QueryClusterHardware 查询cluster_hardware表
func (rw DatasourceReadWrite) QueryClusterHardware(ctx context.Context, db *sql.DB, typ, deviceType, deviceName, name string) ([]*ClusterHardware, error) {
	var wheres []string
	var args []interface{}
	if typ != "" {
		wheres = append(wheres, "TYPE = ?")
		args = append(args, typ)
	}
	if deviceType != "" {
		wheres = append(wheres, "DEVICE_TYPE = ?")
		args = append(args, deviceType)
	}
	if deviceName != "" {
		wheres = append(wheres, "DEVICE_NAME = ?")
		args = append(args, deviceName)
	}
	if name != "" {
		wheres = append(wheres, "NAME = ?")
		args = append(args, name)
	}
	query := "SELECT * FROM information_schema.cluster_hardware"
	if len(wheres) > 0 {
		query += " WHERE " + strings.Join(wheres, " AND ")
	}
	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var result []*ClusterHardware
	for rows.Next() {
		var ch ClusterHardware
		err := rows.Scan(&ch.Type, &ch.Instance, &ch.DeviceType, &ch.DeviceName, &ch.Name, &ch.Value)
		if err != nil {
			return nil, err
		}
		result = append(result, &ch)
	}
	return result, nil
}

// QueryClusterHardwareDistinctInstance 查询distinct instance
func (rw DatasourceReadWrite) QueryClusterHardwareDistinctInstance(ctx context.Context, db *sql.DB, typ string) ([]string, error) {
	query := "SELECT DISTINCT INSTANCE FROM information_schema.cluster_hardware WHERE TYPE = ?"
	rows, err := db.QueryContext(ctx, query, typ)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var result []string
	for rows.Next() {
		var instance string
		if err := rows.Scan(&instance); err != nil {
			return nil, err
		}
		result = append(result, instance)
	}
	return result, nil
}
