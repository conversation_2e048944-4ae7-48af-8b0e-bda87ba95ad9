package datasource

import (
	"context"
	"database/sql"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
)

type ReaderWriter interface {
	CreateDataSource(ctx context.Context, dataSource *Datasource) (*Datasource, error)

	UpdateDataSource(ctx context.Context, dataSource *Datasource) (*Datasource, error)

	Get(ctx context.Context, datasourceId int) (*Datasource, error)

	BatchDelete(ctx context.Context, dataSourceIDs []int) error

	List(ctx context.Context, page int, pageSize int, dataSourceName string, dbType string, connectionStatus string) ([]*Datasource, int64, error)

	ListHost(ctx context.Context, page int, pageSize int, hostIp, hostName, osName string) ([]*OpMachineConf, int64, error)

	SaveHost(ctx context.Context, host *OpMachineConf) (*OpMachineConf, error)

	GetHost(ctx context.Context, macId int) (*OpMachineConf, error)

	GetHostByIP(ctx context.Context, ip string) (*OpMachineConf, error)

	DeleteHost(ctx context.Context, macId int) error

	GetOracleSchemas(ctx context.Context, db *sql.DB) ([]*structs.SchemaInfo, error)

	SearchOracleSchemas(ctx context.Context, db *sql.DB, schemaNameS string) ([]*structs.SchemaInfo, error)

	GetMysqlSchemas(ctx context.Context, db *sql.DB) ([]*structs.SchemaInfo, error)

	GetOracleTables(ctx context.Context, db *sql.DB, schemas []string) ([]*structs.SchemaInfo, error)
	GetOracleTablesByCSV(ctx context.Context, db *sql.DB, schemasTables []*structs.CsvSchemaTables) ([]*structs.SchemaInfo, error)

	GetOracleSchemaCollation(ctx context.Context, db *sql.DB, schemaName string) (string, error)

	GetOracleSchemaTableType(ctx context.Context, db *sql.DB, schemaName string) (map[string]string, error)

	GetMysqlTables(ctx context.Context, db *sql.DB, schemas []string) ([]*structs.SchemaInfo, error)

	GetOracleSchemaTableOriginDDL(ctx context.Context, tableType, schemaName, tableName string, db *sql.DB) (string, error)

	GetOracleDDL(ctx context.Context, schemaName, objectName, objectType string, db *sql.DB) (string, error)

	GetOracleTablePartitions(ctx context.Context, db *sql.DB, schemaName, tableName string) ([]*structs.TablePartitions, error)

	GetOracleDependencies(ctx context.Context, db *sql.DB) ([]*Dependency, error)

	GetOraclePackageBodies(ctx context.Context, db *sql.DB, targetSchemas []string) ([]OraclePackageDefinition, error)

	GetOracleDefinition(ctx context.Context, db *sql.DB, schemaName, objectType, objectName string, objectPackageName string) (*OraclePackageDefinition, error)

	GetOracleObjectStatus(ctx context.Context, db *sql.DB) ([]OracleObject, error)

	GetOracleObjectStatusBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObject, error)

	GetOracleInvalidObjectBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObject, error)

	GetOracleObjectDefinitionsBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObjectDefinition, error)

	// New optimized methods for batch processing
	GetOracleObjectListBySchemas(ctx context.Context, db *sql.DB, schemas []string, types []string) ([]OracleObjectMeta, error)
	GetOracleObjectDefinitionsBatch(ctx context.Context, db *sql.DB, schema, objectType string, objectNames []string) ([]OracleObjectDefinition, error)

	TruncateTMSSQLSetBak(ctx context.Context, db *sql.DB, taskId int) error
	TruncateTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, taskId int) error

	DeleteTMSSQLSet(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	DeleteTMSSQLSetBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	DeleteTMSSQLSetStatements(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	DeleteTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error

	GetDBASQLSets(ctx context.Context, db *sql.DB, sqlsetName string, sourceSchemas []string) ([]SQLSet, error)
	GetTMSSQLSets(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sourceSchemas []string) ([]SQLSet, error)

	GetSQLSetStatementsSummaries(ctx context.Context, db *sql.DB, taskId int, sqlsetName, sqlsetOwner string, sourceSchemas []string) (SQLSetStatementsSummaries, error)
	GetSQLSetStatementsFailedSummaries(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatementsFailedSummary, error)
	GetSQLSetStatementsTimeoutSummaries(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatementsFailedSummary, error)
	GetSQLSetStatementsList(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatement, error)
	GetSQLSetStatementsListByReplayStatus(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, replayStatus string, sourceSchemas []string) ([]SQLSetStatement, error)
	GetSQLSetStatementsListByReplayStatusWithPagination(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, replayStatus string, sourceSchemas []string, page, pageSize int, orderBy string, oraSqlId, oraParsingSchemaName, oraSqlText string) ([]SQLSetStatement, int64, error)
	GetNotReplaySQLSetStatementsList(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, sourceSchemas []string) ([]SQLSetStatement, error)
	GetAiFixSQLSetStatementsListByReplayStatus(ctx context.Context, db *sql.DB, taskId int, sqlsetName string, sqlsetOwner string, replayStatus string, sourceSchemas []string) ([]SQLSetStatement, error)

	InsertIntoTMSSQLSet(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	InsertIntoTMSSQLSetStatements(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	InsertIntoTMSSQLSetBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	InsertIntoTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	SaveTMSSQLSet(ctx context.Context, db *sql.DB, sqlset SQLSet) error
	SaveTMSSQLSetStatements(ctx context.Context, db *sql.DB, statements []SQLSetStatement) error
	SaveTMSSQLSetBak(ctx context.Context, db *sql.DB, sqlset SQLSet) error
	SaveTMSSQLSetStatementsBak(ctx context.Context, db *sql.DB, statements []SQLSetStatement) error

	UpdateTMSSQLSetStatementsLastCopyTime(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	UpdateTMSSQLSetStatementsBakLastCopyTime(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
	UpdateTMSSQLSet(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string, created time.Time, lastModified time.Time, statementCount uint64) (int64, error)

	PruneTMSSQLSetStatementsByPlanHashValue(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsBakByPlanHashValue(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsBySQLID(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsBakBySQLID(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsByDigestID(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsBakByDigestID(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsByExecutions(ctx context.Context, db *sql.DB, taskId int) error
	PruneTMSSQLSetStatementsBakByExecutions(ctx context.Context, db *sql.DB, taskId int) error

	DeleteTMSSQLSetStatementsBySchemas(ctx context.Context, db *sql.DB, taskId int, sourceSchemas []any) (int64, error)
	DeleteTMSSQLSetStatementsBySQLTypes(ctx context.Context, db *sql.DB, taskId int, sqlTypes []any) (int64, error)
	DeleteTMSSQLSetStatementsBySQLIDs(ctx context.Context, db *sql.DB, taskId int, sqlIDs []any) (int64, error)
	DeleteTMSSQLSetStatementsBySQLTexts(ctx context.Context, db *sql.DB, taskId int, sqlTexts []any) (int64, error)
	DeleteTMSSQLSetStatementsBySQLModules(ctx context.Context, db *sql.DB, taskId int, sqlModules []any) (int64, error)
	DeleteTMSSQLSetStatementsBakBySchemas(ctx context.Context, db *sql.DB, taskId int, sourceSchemas []any) (int64, error)
	DeleteTMSSQLSetStatementsBakBySQLTypes(ctx context.Context, db *sql.DB, taskId int, sqlTypes []any) (int64, error)
	DeleteTMSSQLSetStatementsBakBySQLIDs(ctx context.Context, db *sql.DB, taskId int, sqlIDs []any) (int64, error)
	DeleteTMSSQLSetStatementsBakBySQLTexts(ctx context.Context, db *sql.DB, taskId int, sqlTexts []any) (int64, error)
	DeleteTMSSQLSetStatementsBakBySQLModules(ctx context.Context, db *sql.DB, taskId int, sqlModules []any) (int64, error)

	UpdateTMSSQLSetStatementsDigestID(ctx context.Context, db *sql.DB, taskId int, sqlId string, digestID string) error
	UpdateTMSSQLSetStatementsRewriteInfo(ctx context.Context, db *sql.DB, taskId int, sqlId string, rewriteSQLText, rewritePrompts string) error
	UpdateTMSSQLSetStatementsBakDigestID(ctx context.Context, db *sql.DB, taskId int, sqlId string, digestID string) error

	UpdateTMSSQLSetStatementsReplayStatus(ctx context.Context, db *sql.DB, taskId int, sqlId string, replayStatus, replayExecCode string, replayFinishTime time.Time, messageLog string) error

	MergeTMSSQLSetStatements(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error

	// Oracle performance monitoring methods
	GetOracleActiveTmsSessions(ctx context.Context, db *sql.DB) ([]OracleActiveTmsSession, error)
	GetOracleTmsSessionProgress(ctx context.Context, db *sql.DB, username string) ([]OracleTmsSessionProgress, error)
	GetOracleTmsSessionRatio(ctx context.Context, db *sql.DB) ([]TmsUserStats, error)
	GetOraclePhysicalFileIOStats(ctx context.Context, db *sql.DB) ([]OraclePhysicalFileIOStats, error)
	GetOracleHotFileInformation(ctx context.Context, db *sql.DB) ([]OracleHotFileInfo, error)
	GetOracleTableSpaceStats(ctx context.Context, db *sql.DB) ([]OracleTableSpaceStats, error)
	GetOracleObjectStats(ctx context.Context, db *sql.DB) ([]OracleObjectStats, error)
	GetOracleObjectStatsBySchema(ctx context.Context, db *sql.DB, schemas []*channel.ChannelSchema) ([]OracleObjectStats, error)
	GetOracleCpuNumber(ctx context.Context, db *sql.DB) (int64, error)
	GetOracleMemory(ctx context.Context, db *sql.DB) (int64, error)
	GetOracleTableNumberRowsBySchema(ctx context.Context, db *sql.DB, schemas []*channel.ChannelSchema, page, pageSize int) ([]OracleTableNumberRows, int64, error)
	GetTiDBRowCount(ctx context.Context, db *sql.DB, schemaTableMap map[structs.SchemaTablePair]*channel.ChannelSchemaTable) ([]TiDBRowCount, error)
	GetOracleObjectList(ctx context.Context, db *sql.DB, schemaName string, objectType string, page, pageSize int) ([]OracleObjectList, int64, error)

	// Oracle segment size methods
	GetOracleSegmentSizes(ctx context.Context, db *sql.DB, schemas []string) ([]OracleSegmentSize, error)
	SaveOracleSegmentSizesToTiDB(ctx context.Context, segmentSizes []*OracleSegmentSizeEntity) error
	DeleteOracleSegmentSizesByTaskId(ctx context.Context, taskId int) error
	GetOracleSegmentSizesFromTiDB(ctx context.Context, taskId int) ([]OracleSegmentSize, error)
	GetOracleSegmentSumSizesFromTiDB(ctx context.Context, taskId int) (float64, float64, error)

	// ClusterHardware相关
	QueryClusterHardware(ctx context.Context, db *sql.DB, typ, deviceType, deviceName, name string) ([]*ClusterHardware, error)
	QueryClusterHardwareDistinctInstance(ctx context.Context, db *sql.DB, typ string) ([]string, error)
}
