package datasource

import (
	"testing"
	"time"
)

func TestSQLSet_HasIncrementalData(t *testing.T) {

	location, _ := time.LoadLocation("Asia/Tokyo")

	type fields struct {
		ID             uint64
		Name           string
		Owner          string
		Description    string
		Created        time.Time
		LastModified   time.Time
		StatementCount uint64
	}
	type args struct {
		other *SQLSet
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "test1",
			fields: fields{
				Name:           "Z",
				Owner:          "X",
				Description:    "C",
				Created:        time.Date(2024, time.April, 10, 12, 17, 4, 0, location),
				LastModified:   time.Date(2025, time.April, 10, 12, 17, 4, 0, location),
				StatementCount: 10,
			},
			args: args{
				other: &SQLSet{
					Name:           "Z",
					Owner:          "X",
					Description:    "C",
					Created:        time.Date(2024, time.April, 10, 12, 17, 4, 0, location),
					LastModified:   time.Date(2025, time.April, 10, 12, 17, 4, 0, location),
					StatementCount: 11,
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &SQLSet{
				ID:             tt.fields.ID,
				Name:           tt.fields.Name,
				Owner:          tt.fields.Owner,
				Description:    tt.fields.Description,
				Created:        tt.fields.Created,
				LastModified:   tt.fields.LastModified,
				StatementCount: tt.fields.StatementCount,
			}
			if got := i.HasIncrementalData(tt.args.other); got != tt.want {
				t.Errorf("HasIncrementalData() = %v, want %v", got, tt.want)
			}
		})
	}
}
