package template

import "gitee.com/pingcap_enterprise/tms/server/models/common"

type TmplateInformation struct {
	TmplateId    int    `gorm:"primarykey"`
	TemplateName string `gorm:"type:varchar(100);comment:template name"`
	TemplateType string `gorm:"type:varchar(100);comment:template type"`
	DefaultTag   string `gorm:"type:char(1);comment:default tag"`
	*common.Entity
}

type TmplateRule struct {
	TmplateRuleId int    `gorm:"primarykey"`
	TmplateId     int    `gorm:"type:int;index:idx_map_rule_id_tmplate_id;comment:template id"`
	MapRuleId     int    `gorm:"type:int;index:idx_map_rule_id_tmplate_id;comment:map rule id"`
	MapRuleType   string `gorm:"type:varchar(100);comment:map rule type"`
	*common.Entity
}

type TabcolMapRule struct {
	TabcolmapRuleId int    `gorm:"primarykey"`
	DbTypeS         string `gorm:"type:varchar(10);comment:source db type"`
	DbTypeT         string `gorm:"type:varchar(10);comment:target db type"`
	ColTypeNameS    string `gorm:"type:varchar(100);comment:source column type name"`
	ColTypeNameT    string `gorm:"type:varchar(100);comment:target column type name"`
	IsEquivalent    string `gorm:"type:varchar(1);comment:equivalent flag,its value is Y,N"`
	*common.Entity
}

type ColdefaultMapRule struct {
	ColdefaultmapRuleId  int    `gorm:"primarykey"`
	DbTypeS              string `gorm:"type:varchar(10);comment:source db type"`
	DbTypeT              string `gorm:"type:varchar(10);comment:target db type"`
	ColTypeDefaultValueS string `gorm:"type:varchar(100);comment:source column type default value"`
	ColTypeDefaultValueT string `gorm:"type:varchar(100);comment:target column type default value"`
	IsEquivalent         string `gorm:"type:varchar(1);comment:equivalent flag,its value is Y,N"`
	*common.Entity
}

type TabcolCustMap struct {
	TabcolMapruleId  int    `gorm:"primarykey"`
	ChannelId        int    `gorm:"type:int;comment:channel id"`
	TaskId           int    `gorm:"type:int;comment:task id"`
	SchemaNameS      string `gorm:"type:varchar(30);comment:source schema name"`
	TableNameS       string `gorm:"type:varchar(100);comment:source schema table name"`
	ColNameS         string `gorm:"type:varchar(100);comment:source column name"`
	ColNameT         string `gorm:"type:varchar(100);comment:target column name"`
	ColStrS          string `gorm:"type:varchar(100);comment:source column type"`
	ColStrT          string `gorm:"type:varchar(100);comment:target column type"`
	ColDefaultvalueS string `gorm:"type:varchar(100);comment:source column default value"`
	ColDefaultvalueT string `gorm:"type:varchar(100);comment:target column default value"`
	*common.Entity
}

type ObjmapRule struct {
	ObjmapRuleId    int    `gorm:"primarykey"`
	DbTypeS         string `gorm:"type:varchar(10);comment:source db type"`
	DbTypeT         string `gorm:"type:varchar(10);comment:target db type"`
	ObjectType      string `gorm:"type:varchar(100);comment:object type"`
	ObjectTypename  string `gorm:"type:varchar(100);comment:object type name"`
	IsCompatibility string `gorm:"type:varchar(1);comment:is compatibility.its value is Y,N"`
	IsConvertible   string `gorm:"type:varchar(1);comment:is convertible.its value is Y,N"`
	*common.Entity
}

type SqlmapRule struct {
	SqlmapRuleId    int    `gorm:"primarykey"`
	DbTypeS         string `gorm:"type:varchar(10);comment:source db type"`
	DbTypeT         string `gorm:"type:varchar(10);comment:target db type"`
	Keywords        string `gorm:"type:varchar(20);comment:Keywords"`
	IsCompatibility string `gorm:"type:varchar(1);comment:is compatibility.its value is Y,N"`
	IsConvertible   string `gorm:"type:varchar(1);comment:is convertible.its value is Y,N"`
	*common.Entity
}
