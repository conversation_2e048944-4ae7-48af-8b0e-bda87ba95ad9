package template

import "context"

type ReaderWriter interface {
	CreateTemplate(ctx context.Context, templateInfo *TmplateInformation) (*TmplateInformation, error)

	UpdateTemplate(ctx context.Context, templateInfo *TmplateInformation) (*TmplateInformation, error)

	GetTemplate(ctx context.Context, templateId int) (*TmplateInformation, error)

	BatchDeleteTemplates(ctx context.Context, templateIds []int) error

	ListTemplates(ctx context.Context, templateType string, page int, pageSize int) ([]*TmplateInformation, int64, error)

	GetDefaultTemplateByType(ctx context.Context, templateType string) (*TmplateInformation, error)

	GetAllDefaultTemplates(ctx context.Context) ([]*TmplateInformation, error)

	GetTabColMapRules(ctx context.Context, dbTypeS, dbTypeT string, ruleIds []string) ([]*TabcolMapRule, error)

	GetObjMapRules(ctx context.Context, dbTypeS, dbTypeT string) ([]*ObjmapRule, error)

	GetSqlMapRules(ctx context.Context, dbTypeS, dbTypeT string) ([]*SqlmapRule, error)

	GetTabcolCustMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error)

	GetDefaultValueCustMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error)

	GetColTypeCustMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error)

	GetColDefaultMapRules(ctx context.Context, dbTypeS, dbTypeT string, ruleIds []string) ([]*ColdefaultMapRule, error)

	GetTemplateRuleID(ctx context.Context, templateId int) ([]*TmplateRule, error)

	CreateTemplateRule(ctx context.Context, templateRule *TmplateRule) (*TmplateRule, error)

	DeleteTemplateRule(ctx context.Context, templateId int, mapRuleId int) error

	CreateTablecolMapRule(ctx context.Context, rule *TabcolMapRule) (*TabcolMapRule, error)

	UpdateTablecolMapRule(ctx context.Context, rule *TabcolMapRule) (*TabcolMapRule, error)

	DeleteTablecolMapRuleById(ctx context.Context, ruleId int) error

	GetTablecolMapRuleById(ctx context.Context, ruleId int) (*TabcolMapRule, error)

	ListTabColMapRules(ctx context.Context, templateId int, page int, pageSize int) ([]*TabcolMapRule, int64, error)

	CreateObjMapRule(ctx context.Context, rule *ObjmapRule) (*ObjmapRule, error)
	UpdateObjMapRule(ctx context.Context, rule *ObjmapRule) (*ObjmapRule, error)
	DeleteObjMapRuleById(ctx context.Context, ruleId int) error
	GetObjMapRuleById(ctx context.Context, ruleId int) (*ObjmapRule, error)
	ListObjMapRules(ctx context.Context, templateId int, page int, pageSize int) ([]*ObjmapRule, int64, error)

	CreateSqlMapRule(ctx context.Context, rule *SqlmapRule) (*SqlmapRule, error)
	UpdateSqlMapRule(ctx context.Context, rule *SqlmapRule) (*SqlmapRule, error)
	DeleteSqlMapRuleById(ctx context.Context, ruleId int) error
	GetSqlMapRuleById(ctx context.Context, ruleId int) (*SqlmapRule, error)
	ListSqlMapRules(ctx context.Context, templateId int, page int, pageSize int) ([]*SqlmapRule, int64, error)

	CreateColDefaultMapRule(ctx context.Context, rule *ColdefaultMapRule) (*ColdefaultMapRule, error)
	UpdateColDefaultMapRule(ctx context.Context, rule *ColdefaultMapRule) (*ColdefaultMapRule, error)
	DeleteColDefaultMapRuleById(ctx context.Context, ruleId int) error
	GetColDefaultMapRuleById(ctx context.Context, ruleId int) (*ColdefaultMapRule, error)
	ListColDefaultMapRulesByTemplateId(ctx context.Context, templateId int, page int, pageSize int) ([]*ColdefaultMapRule, int64, error)

	CreateTabcolCustMap(ctx context.Context, rule *TabcolCustMap) (*TabcolCustMap, error)
	UpdateTabcolCustMap(ctx context.Context, rule *TabcolCustMap) (*TabcolCustMap, error)
	DeleteTabcolCustMapRules(ctx context.Context, tabCol *TabcolCustMap) error
}
