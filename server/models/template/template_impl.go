package template

import (
	"context"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	dbCommon "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gorm.io/gorm"
)

type TemplateReadWrite struct {
	dbCommon.GormDB
}

func NewTemplateReadWrite(db *gorm.DB) *TemplateReadWrite {
	m := &TemplateReadWrite{
		dbCommon.WrapDB(db),
	}
	return m
}
func (rw *TemplateReadWrite) CreateTemplate(ctx context.Context, template *TmplateInformation) (*TmplateInformation, error) {
	err := rw.DB(ctx).Create(template).Error
	if err != nil {
		log.Errorf("create template to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create template to db successfully, %v", template)
	return template, nil
}

func (rw *TemplateReadWrite) UpdateTemplate(ctx context.Context, template *TmplateInformation) (*TmplateInformation, error) {
	err := rw.DB(ctx).Save(template).Error
	if err != nil {
		log.Errorf("update template to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update template to db successfully, %v", template)
	return template, nil
}

func (rw *TemplateReadWrite) GetTemplate(ctx context.Context, templateId int) (*TmplateInformation, error) {
	channel := &TmplateInformation{}
	err := rw.DB(ctx).First(channel, templateId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query template %d not found", templateId)
	} else if err != nil {
		log.Errorf("get template failed. templateId is %d, err:%v", templateId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channel, nil
}

func (rw *TemplateReadWrite) BatchDeleteTemplates(ctx context.Context, templateIds []int) error {
	err := rw.DB(ctx).Delete(&TmplateInformation{}, templateIds).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) ListTemplates(ctx context.Context, templateType string, page int, pageSize int) ([]*TmplateInformation, int64, error) {
	records := make([]*TmplateInformation, 0, pageSize)
	var count int64 = 0
	err := rw.DB(ctx).Model(&TmplateInformation{}).Where("template_type = ?", templateType).
		Order("tmplate_id desc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("page query templates by type failed. templateType:%s, page:%d, pageSize:%d,err:%s", templateType, page, pageSize, err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *TemplateReadWrite) GetDefaultTemplateByType(ctx context.Context, templateType string) (*TmplateInformation, error) {
	channel := &TmplateInformation{}
	err := rw.DB(ctx).Where(&TmplateInformation{TemplateType: templateType, DefaultTag: "Y"}).First(channel).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "query default template of template type[%s] %d not found", templateType)
	} else if err != nil {
		log.Errorf("get default template by template type failed. template type is %s, err:%v", templateType, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return channel, nil
}

func (rw *TemplateReadWrite) GetTabColMapRules(ctx context.Context, dbTypeS, dbTypeT string, ruleIds []string) ([]*TabcolMapRule, error) {
	records := make([]*TabcolMapRule, 10)
	err := rw.DB(ctx).Model(&TabcolMapRule{}).Where("UPPER(db_type_s) = ? AND UPPER(db_type_t) = ? AND tabcolmap_rule_id IN (?)",
		strings.ToUpper(dbTypeS),
		strings.ToUpper(dbTypeT),
		ruleIds).Order("tabcolmap_rule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get TabColMapRules failed. dbTypeS is %d, dbTypeT is %s,err:%s", dbTypeS, dbTypeT, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetObjMapRules(ctx context.Context, dbTypeS, dbTypeT string) ([]*ObjmapRule, error) {
	records := make([]*ObjmapRule, 10)
	err := rw.DB(ctx).Model(&ObjmapRule{}).Order("objmap_rule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get ObjMapRules failed. dbTypeS is %d, dbTypeT is %s,err:%s", dbTypeS, dbTypeT, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetSqlMapRules(ctx context.Context, dbTypeS, dbTypeT string) ([]*SqlmapRule, error) {
	records := make([]*SqlmapRule, 10)
	err := rw.DB(ctx).Model(&SqlmapRule{}).Order("sqlmap_rule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get SqlMapRules failed. dbTypeS is %d, dbTypeT is %s, err:%s", dbTypeS, dbTypeT, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetTabcolCustMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error) {
	var records []*TabcolCustMap
	err := rw.DB(ctx).Model(&TabcolCustMap{}).Where(&tabCol).Order("tabcol_maprule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get TabcolCustMap failed. channel_id is %d, task_id is %s, err:%s", tabCol.ChannelId, tabCol.TaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetDefaultValueCustMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error) {
	var records []*TabcolCustMap
	err := rw.DB(ctx).Model(&TabcolCustMap{}).Where(&tabCol).Where("col_defaultvalue_s is not null and col_defaultvalue_t is not null and col_defaultvalue_s <> '' and col_defaultvalue_t <> '' ").Order("tabcol_maprule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get TabcolCustMap failed. channel_id is %d, task_id is %s, err:%s", tabCol.ChannelId, tabCol.TaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetColTypeCustMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error) {
	var records []*TabcolCustMap
	err := rw.DB(ctx).Model(&TabcolCustMap{}).Where(&tabCol).Order("tabcol_maprule_id desc").Where("col_str_s is not null and col_str_t is not null and col_str_s <> '' and col_str_t <> '' ").Find(&records).Error
	if err != nil {
		log.Errorf("get TabcolCustMap failed. channel_id is %d, task_id is %s, err:%s", tabCol.ChannelId, tabCol.TaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetTabcolCustDefaultValueMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error) {
	var records []*TabcolCustMap
	err := rw.DB(ctx).Model(&TabcolCustMap{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null AND col_defaultvalue_s is not null ", tabCol.ChannelId, tabCol.TaskId).Order("tabcol_maprule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get TabcolCustMap failed. channel_id is %d, task_id is %s, err:%s", tabCol.ChannelId, tabCol.TaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetTabcolCustColMapRules(ctx context.Context, tabCol *TabcolCustMap) ([]*TabcolCustMap, error) {
	var records []*TabcolCustMap
	err := rw.DB(ctx).Model(&TabcolCustMap{}).Where("channel_id = ? AND task_id = ? AND deleted_at is null AND col_str_s is not null AND col_str_t is not null ", tabCol.ChannelId, tabCol.TaskId).Order("tabcol_maprule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get TabcolCustMap failed. channel_id is %d, task_id is %s, err:%s", tabCol.ChannelId, tabCol.TaskId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) DeleteTabcolCustMapRules(ctx context.Context, tabCol *TabcolCustMap) error {
	err := rw.DB(ctx).Unscoped().Where("tabcol_maprule_id = ? ", tabCol.TabcolMapruleId).Delete(&TabcolCustMap{}).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) GetColDefaultMapRules(ctx context.Context, dbTypeS, dbTypeT string, ruleIds []string) ([]*ColdefaultMapRule, error) {
	var records []*ColdefaultMapRule
	err := rw.DB(ctx).Model(&ColdefaultMapRule{}).Where("UPPER(db_type_s) = ? AND UPPER(db_type_t) = ? AND coldefaultmap_rule_id IN (?)",
		strings.ToUpper(dbTypeS),
		strings.ToUpper(dbTypeT),
		ruleIds).Order("coldefaultmap_rule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get ColDefaultMapRules failed. dbTypeS is %d, dbTypeT is %s, err:%s", dbTypeS, dbTypeT, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) GetTemplateRuleID(ctx context.Context, templateId int) ([]*TmplateRule, error) {
	var records []*TmplateRule
	err := rw.DB(ctx).Model(&TmplateRule{}).Where("tmplate_id = ?", templateId).Order("tmplate_rule_id desc").Find(&records).Error
	if err != nil {
		log.Errorf("get TmplateRule failed. templateId is %d, err:%s", templateId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}

func (rw *TemplateReadWrite) CreateTemplateRule(ctx context.Context, template *TmplateRule) (*TmplateRule, error) {
	err := rw.DB(ctx).Create(template).Error
	if err != nil {
		log.Errorf("create template rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create template rule to db successfully, %v", template)
	return template, nil
}

func (rw *TemplateReadWrite) DeleteTemplateRule(ctx context.Context, templateId int, mapRuleId int) error {
	err := rw.DB(ctx).Where("tmplate_id = ? and map_rule_id = ?", templateId, mapRuleId).Delete(&TmplateRule{}).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) CreateTablecolMapRule(ctx context.Context, rule *TabcolMapRule) (*TabcolMapRule, error) {
	err := rw.DB(ctx).Create(rule).Error
	if err != nil {
		log.Errorf("create table column map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create table column map rule to db successfully, %v", rule)
	return rule, nil
}

func (rw *TemplateReadWrite) UpdateTablecolMapRule(ctx context.Context, rule *TabcolMapRule) (*TabcolMapRule, error) {
	err := rw.DB(ctx).Save(rule).Error
	if err != nil {
		log.Errorf("update table column map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) DeleteTablecolMapRuleById(ctx context.Context, ruleId int) error {
	err := rw.DB(ctx).Delete(&TabcolMapRule{}, ruleId).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) GetTablecolMapRuleById(ctx context.Context, ruleId int) (*TabcolMapRule, error) {
	rule := &TabcolMapRule{}
	err := rw.DB(ctx).First(rule, ruleId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "get TabcolMapRule id %d not found", ruleId)
	} else if err != nil {
		log.Errorf("get table column map rule failed. ruleId is %d, err:%v", ruleId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) ListTabColMapRules(ctx context.Context, templateId int, page int, pageSize int) ([]*TabcolMapRule, int64, error) {
	records := make([]*TabcolMapRule, 0, 0)
	var count int64 = 0
	err := rw.DB(ctx).Model(&TabcolMapRule{}).Where("tabcolmap_rule_id in (select map_rule_id from tmplate_rules where tmplate_id = ?)", templateId).
		Order("tabcolmap_rule_id asc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("list table column map rules failed.err:%s", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *TemplateReadWrite) CreateColDefaultMapRule(ctx context.Context, rule *ColdefaultMapRule) (*ColdefaultMapRule, error) {
	err := rw.DB(ctx).Create(rule).Error
	if err != nil {
		log.Errorf("create table column default value map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create table column default value map rule to db successfully, %v", rule)
	return rule, nil
}

func (rw *TemplateReadWrite) UpdateColdefaultMapRule(ctx context.Context, rule *TabcolMapRule) (*TabcolMapRule, error) {
	err := rw.DB(ctx).Save(rule).Error
	if err != nil {
		log.Errorf("update ColdefaultMapRule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) CreateObjMapRule(ctx context.Context, rule *ObjmapRule) (*ObjmapRule, error) {
	err := rw.DB(ctx).Create(rule).Error
	if err != nil {
		log.Errorf("create object map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create object map rule to db successfully.")
	return rule, nil
}

func (rw *TemplateReadWrite) UpdateObjMapRule(ctx context.Context, rule *ObjmapRule) (*ObjmapRule, error) {
	err := rw.DB(ctx).Save(rule).Error
	if err != nil {
		log.Errorf("update object map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) DeleteObjMapRuleById(ctx context.Context, ruleId int) error {
	err := rw.DB(ctx).Delete(&ObjmapRule{}, ruleId).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) GetObjMapRuleById(ctx context.Context, ruleId int) (*ObjmapRule, error) {
	rule := &ObjmapRule{}
	err := rw.DB(ctx).First(rule, ruleId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "get object map rule id %d not found", ruleId)
	} else if err != nil {
		log.Errorf("get object map rule failed. ruleId is %d, err:%v", ruleId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) ListObjMapRules(ctx context.Context, templateId int, page int, pageSize int) ([]*ObjmapRule, int64, error) {
	records := make([]*ObjmapRule, 0, 0)
	var count int64 = 0
	err := rw.DB(ctx).Model(&ObjmapRule{}).Where("objmap_rule_id in (select map_rule_id from tmplate_rules where tmplate_id = ?)", templateId).
		Order("objmap_rule_id asc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("list object map rules failed.err:%s", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *TemplateReadWrite) CreateSqlMapRule(ctx context.Context, rule *SqlmapRule) (*SqlmapRule, error) {
	err := rw.DB(ctx).Create(rule).Error
	if err != nil {
		log.Errorf("create sql map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create sql map rule to db successfully.")
	return rule, nil
}

func (rw *TemplateReadWrite) UpdateSqlMapRule(ctx context.Context, rule *SqlmapRule) (*SqlmapRule, error) {
	err := rw.DB(ctx).Save(rule).Error
	if err != nil {
		log.Errorf("update sql map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) DeleteSqlMapRuleById(ctx context.Context, ruleId int) error {
	err := rw.DB(ctx).Delete(&SqlmapRule{}, ruleId).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) GetSqlMapRuleById(ctx context.Context, ruleId int) (*SqlmapRule, error) {
	rule := &SqlmapRule{}
	err := rw.DB(ctx).First(rule, ruleId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "get SqlmapRule id %d not found", ruleId)
	} else if err != nil {
		log.Errorf("get sql map rule failed. ruleId is %d, err:%v", ruleId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) ListSqlMapRules(ctx context.Context, templateId int, page int, pageSize int) ([]*SqlmapRule, int64, error) {
	records := make([]*SqlmapRule, 0, 0)
	var count int64 = 0
	err := rw.DB(ctx).Model(&SqlmapRule{}).Where("sqlmap_rule_id in (select map_rule_id from tmplate_rules where tmplate_id = ?)", templateId).
		Order("sqlmap_rule_id asc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("list sql map rules failed.err:%s", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *TemplateReadWrite) CreateTabcolCustMap(ctx context.Context, rule *TabcolCustMap) (*TabcolCustMap, error) {
	err := rw.DB(ctx).Create(rule).Error
	if err != nil {
		log.Errorf("create table column custom map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("create table column custom map rule to db successfully, %v", rule)
	return rule, nil
}

func (rw *TemplateReadWrite) UpdateTabcolCustMap(ctx context.Context, rule *TabcolCustMap) (*TabcolCustMap, error) {
	err := rw.DB(ctx).Save(rule).Error
	if err != nil {
		log.Errorf("update table column custom map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	log.Infof("update table column custom map rule to db successfully, %v", rule)
	return rule, nil
}

func (rw *TemplateReadWrite) UpdateColDefaultMapRule(ctx context.Context, rule *ColdefaultMapRule) (*ColdefaultMapRule, error) {
	err := rw.DB(ctx).Save(rule).Error
	if err != nil {
		log.Errorf("update column default map rule to db failed. err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) DeleteColDefaultMapRuleById(ctx context.Context, ruleId int) error {
	err := rw.DB(ctx).Delete(&ColdefaultMapRule{}, ruleId).Error
	return dbCommon.WrapDBError(err)
}

func (rw *TemplateReadWrite) GetColDefaultMapRuleById(ctx context.Context, ruleId int) (*ColdefaultMapRule, error) {
	rule := &ColdefaultMapRule{}
	err := rw.DB(ctx).First(rule, ruleId).Error
	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewErrorf(errors.TIMS_RECORD_NOT_EXIST, "get column default map rule id %d not found", ruleId)
	} else if err != nil {
		log.Errorf("get column default map rule failed. ruleId is %d, err:%v", ruleId, err)
		return nil, dbCommon.WrapDBError(err)
	}
	return rule, nil
}

func (rw *TemplateReadWrite) ListColDefaultMapRulesByTemplateId(ctx context.Context, templateId int, page int, pageSize int) ([]*ColdefaultMapRule, int64, error) {
	records := make([]*ColdefaultMapRule, 0, pageSize)
	var count int64 = 0
	err := rw.DB(ctx).Model(&ColdefaultMapRule{}).Where("coldefaultmap_rule_id in (select map_rule_id from tmplate_rules where tmplate_id = ?)", templateId).
		Order("coldefaultmap_rule_id asc").Count(&count).Limit(pageSize).Offset((page - 1) * pageSize).Find(&records).Error
	if err != nil {
		log.Errorf("list column default map rules failed.err:%s", err)
		return nil, 0, dbCommon.WrapDBError(err)
	}
	return records, count, nil
}

func (rw *TemplateReadWrite) GetAllDefaultTemplates(ctx context.Context) ([]*TmplateInformation, error) {
	records := make([]*TmplateInformation, 0, 0)
	err := rw.DB(ctx).Where(&TmplateInformation{DefaultTag: "Y"}).Find(&records).Error
	if err != nil {
		log.Errorf("get all default templates failed, err:%s", err)
		return nil, dbCommon.WrapDBError(err)
	}
	return records, nil
}
