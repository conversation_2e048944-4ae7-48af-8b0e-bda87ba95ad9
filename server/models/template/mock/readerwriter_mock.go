// Code generated by MockGen. DO NOT EDIT.
// Source: ./template/readerwriter.go

// Package mocktemplatereaderwriter is a generated GoMock package.
package mocktemplatereaderwriter

import (
	context "context"
	reflect "reflect"

	template "gitee.com/pingcap_enterprise/tms/server/models/template"
	gomock "github.com/golang/mock/gomock"
)

// MockReaderWriter is a mock of ReaderWriter interface.
type MockReaderWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReaderWriterMockRecorder
}

// MockReaderWriterMockRecorder is the mock recorder for MockReaderWriter.
type MockReaderWriterMockRecorder struct {
	mock *MockReaderWriter
}

// NewMockReaderWriter creates a new mock instance.
func NewMockReaderWriter(ctrl *gomock.Controller) *MockReaderWriter {
	mock := &MockReaderWriter{ctrl: ctrl}
	mock.recorder = &MockReaderWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReaderWriter) EXPECT() *MockReaderWriterMockRecorder {
	return m.recorder
}

// BatchDeleteTemplates mocks base method.
func (m *MockReaderWriter) BatchDeleteTemplates(ctx context.Context, templateIds []int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeleteTemplates", ctx, templateIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDeleteTemplates indicates an expected call of BatchDeleteTemplates.
func (mr *MockReaderWriterMockRecorder) BatchDeleteTemplates(ctx, templateIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeleteTemplates", reflect.TypeOf((*MockReaderWriter)(nil).BatchDeleteTemplates), ctx, templateIds)
}

// CreateColDefaultMapRule mocks base method.
func (m *MockReaderWriter) CreateColDefaultMapRule(ctx context.Context, rule *template.ColdefaultMapRule) (*template.ColdefaultMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateColDefaultMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.ColdefaultMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateColDefaultMapRule indicates an expected call of CreateColDefaultMapRule.
func (mr *MockReaderWriterMockRecorder) CreateColDefaultMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateColDefaultMapRule", reflect.TypeOf((*MockReaderWriter)(nil).CreateColDefaultMapRule), ctx, rule)
}

// CreateObjMapRule mocks base method.
func (m *MockReaderWriter) CreateObjMapRule(ctx context.Context, rule *template.ObjmapRule) (*template.ObjmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateObjMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.ObjmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateObjMapRule indicates an expected call of CreateObjMapRule.
func (mr *MockReaderWriterMockRecorder) CreateObjMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateObjMapRule", reflect.TypeOf((*MockReaderWriter)(nil).CreateObjMapRule), ctx, rule)
}

// CreateSqlMapRule mocks base method.
func (m *MockReaderWriter) CreateSqlMapRule(ctx context.Context, rule *template.SqlmapRule) (*template.SqlmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSqlMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.SqlmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSqlMapRule indicates an expected call of CreateSqlMapRule.
func (mr *MockReaderWriterMockRecorder) CreateSqlMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSqlMapRule", reflect.TypeOf((*MockReaderWriter)(nil).CreateSqlMapRule), ctx, rule)
}

// CreateTabcolCustMap mocks base method.
func (m *MockReaderWriter) CreateTabcolCustMap(ctx context.Context, rule *template.TabcolCustMap) (*template.TabcolCustMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTabcolCustMap", ctx, rule)
	ret0, _ := ret[0].(*template.TabcolCustMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTabcolCustMap indicates an expected call of CreateTabcolCustMap.
func (mr *MockReaderWriterMockRecorder) CreateTabcolCustMap(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTabcolCustMap", reflect.TypeOf((*MockReaderWriter)(nil).CreateTabcolCustMap), ctx, rule)
}

// CreateTablecolMapRule mocks base method.
func (m *MockReaderWriter) CreateTablecolMapRule(ctx context.Context, rule *template.TabcolMapRule) (*template.TabcolMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTablecolMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.TabcolMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTablecolMapRule indicates an expected call of CreateTablecolMapRule.
func (mr *MockReaderWriterMockRecorder) CreateTablecolMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTablecolMapRule", reflect.TypeOf((*MockReaderWriter)(nil).CreateTablecolMapRule), ctx, rule)
}

// CreateTemplate mocks base method.
func (m *MockReaderWriter) CreateTemplate(ctx context.Context, templateInfo *template.TmplateInformation) (*template.TmplateInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTemplate", ctx, templateInfo)
	ret0, _ := ret[0].(*template.TmplateInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTemplate indicates an expected call of CreateTemplate.
func (mr *MockReaderWriterMockRecorder) CreateTemplate(ctx, templateInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTemplate", reflect.TypeOf((*MockReaderWriter)(nil).CreateTemplate), ctx, templateInfo)
}

// CreateTemplateRule mocks base method.
func (m *MockReaderWriter) CreateTemplateRule(ctx context.Context, templateRule *template.TmplateRule) (*template.TmplateRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTemplateRule", ctx, templateRule)
	ret0, _ := ret[0].(*template.TmplateRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTemplateRule indicates an expected call of CreateTemplateRule.
func (mr *MockReaderWriterMockRecorder) CreateTemplateRule(ctx, templateRule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTemplateRule", reflect.TypeOf((*MockReaderWriter)(nil).CreateTemplateRule), ctx, templateRule)
}

// DeleteColDefaultMapRuleById mocks base method.
func (m *MockReaderWriter) DeleteColDefaultMapRuleById(ctx context.Context, ruleId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteColDefaultMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteColDefaultMapRuleById indicates an expected call of DeleteColDefaultMapRuleById.
func (mr *MockReaderWriterMockRecorder) DeleteColDefaultMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteColDefaultMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).DeleteColDefaultMapRuleById), ctx, ruleId)
}

// DeleteObjMapRuleById mocks base method.
func (m *MockReaderWriter) DeleteObjMapRuleById(ctx context.Context, ruleId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteObjMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteObjMapRuleById indicates an expected call of DeleteObjMapRuleById.
func (mr *MockReaderWriterMockRecorder) DeleteObjMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteObjMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).DeleteObjMapRuleById), ctx, ruleId)
}

// DeleteSqlMapRuleById mocks base method.
func (m *MockReaderWriter) DeleteSqlMapRuleById(ctx context.Context, ruleId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSqlMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSqlMapRuleById indicates an expected call of DeleteSqlMapRuleById.
func (mr *MockReaderWriterMockRecorder) DeleteSqlMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSqlMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).DeleteSqlMapRuleById), ctx, ruleId)
}

// DeleteTabcolCustMapRules mocks base method.
func (m *MockReaderWriter) DeleteTabcolCustMapRules(ctx context.Context, tabCol *template.TabcolCustMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTabcolCustMapRules", ctx, tabCol)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTabcolCustMapRules indicates an expected call of DeleteTabcolCustMapRules.
func (mr *MockReaderWriterMockRecorder) DeleteTabcolCustMapRules(ctx, tabCol interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTabcolCustMapRules", reflect.TypeOf((*MockReaderWriter)(nil).DeleteTabcolCustMapRules), ctx, tabCol)
}

// DeleteTablecolMapRuleById mocks base method.
func (m *MockReaderWriter) DeleteTablecolMapRuleById(ctx context.Context, ruleId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTablecolMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTablecolMapRuleById indicates an expected call of DeleteTablecolMapRuleById.
func (mr *MockReaderWriterMockRecorder) DeleteTablecolMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTablecolMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).DeleteTablecolMapRuleById), ctx, ruleId)
}

// DeleteTemplateRule mocks base method.
func (m *MockReaderWriter) DeleteTemplateRule(ctx context.Context, templateId, mapRuleId int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTemplateRule", ctx, templateId, mapRuleId)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTemplateRule indicates an expected call of DeleteTemplateRule.
func (mr *MockReaderWriterMockRecorder) DeleteTemplateRule(ctx, templateId, mapRuleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTemplateRule", reflect.TypeOf((*MockReaderWriter)(nil).DeleteTemplateRule), ctx, templateId, mapRuleId)
}

// GetAllDefaultTemplates mocks base method.
func (m *MockReaderWriter) GetAllDefaultTemplates(ctx context.Context) ([]*template.TmplateInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllDefaultTemplates", ctx)
	ret0, _ := ret[0].([]*template.TmplateInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllDefaultTemplates indicates an expected call of GetAllDefaultTemplates.
func (mr *MockReaderWriterMockRecorder) GetAllDefaultTemplates(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllDefaultTemplates", reflect.TypeOf((*MockReaderWriter)(nil).GetAllDefaultTemplates), ctx)
}

// GetColDefaultMapRuleById mocks base method.
func (m *MockReaderWriter) GetColDefaultMapRuleById(ctx context.Context, ruleId int) (*template.ColdefaultMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetColDefaultMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(*template.ColdefaultMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetColDefaultMapRuleById indicates an expected call of GetColDefaultMapRuleById.
func (mr *MockReaderWriterMockRecorder) GetColDefaultMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetColDefaultMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).GetColDefaultMapRuleById), ctx, ruleId)
}

// GetColDefaultMapRules mocks base method.
func (m *MockReaderWriter) GetColDefaultMapRules(ctx context.Context, dbTypeS, dbTypeT string, ruleIds []string) ([]*template.ColdefaultMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetColDefaultMapRules", ctx, dbTypeS, dbTypeT, ruleIds)
	ret0, _ := ret[0].([]*template.ColdefaultMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetColDefaultMapRules indicates an expected call of GetColDefaultMapRules.
func (mr *MockReaderWriterMockRecorder) GetColDefaultMapRules(ctx, dbTypeS, dbTypeT, ruleIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetColDefaultMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetColDefaultMapRules), ctx, dbTypeS, dbTypeT, ruleIds)
}

// GetColTypeCustMapRules mocks base method.
func (m *MockReaderWriter) GetColTypeCustMapRules(ctx context.Context, tabCol *template.TabcolCustMap) ([]*template.TabcolCustMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetColTypeCustMapRules", ctx, tabCol)
	ret0, _ := ret[0].([]*template.TabcolCustMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetColTypeCustMapRules indicates an expected call of GetColTypeCustMapRules.
func (mr *MockReaderWriterMockRecorder) GetColTypeCustMapRules(ctx, tabCol interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetColTypeCustMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetColTypeCustMapRules), ctx, tabCol)
}

// GetDefaultTemplateByType mocks base method.
func (m *MockReaderWriter) GetDefaultTemplateByType(ctx context.Context, templateType string) (*template.TmplateInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultTemplateByType", ctx, templateType)
	ret0, _ := ret[0].(*template.TmplateInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultTemplateByType indicates an expected call of GetDefaultTemplateByType.
func (mr *MockReaderWriterMockRecorder) GetDefaultTemplateByType(ctx, templateType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultTemplateByType", reflect.TypeOf((*MockReaderWriter)(nil).GetDefaultTemplateByType), ctx, templateType)
}

// GetDefaultValueCustMapRules mocks base method.
func (m *MockReaderWriter) GetDefaultValueCustMapRules(ctx context.Context, tabCol *template.TabcolCustMap) ([]*template.TabcolCustMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultValueCustMapRules", ctx, tabCol)
	ret0, _ := ret[0].([]*template.TabcolCustMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultValueCustMapRules indicates an expected call of GetDefaultValueCustMapRules.
func (mr *MockReaderWriterMockRecorder) GetDefaultValueCustMapRules(ctx, tabCol interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultValueCustMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetDefaultValueCustMapRules), ctx, tabCol)
}

// GetObjMapRuleById mocks base method.
func (m *MockReaderWriter) GetObjMapRuleById(ctx context.Context, ruleId int) (*template.ObjmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(*template.ObjmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjMapRuleById indicates an expected call of GetObjMapRuleById.
func (mr *MockReaderWriterMockRecorder) GetObjMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).GetObjMapRuleById), ctx, ruleId)
}

// GetObjMapRules mocks base method.
func (m *MockReaderWriter) GetObjMapRules(ctx context.Context, dbTypeS, dbTypeT string) ([]*template.ObjmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjMapRules", ctx, dbTypeS, dbTypeT)
	ret0, _ := ret[0].([]*template.ObjmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjMapRules indicates an expected call of GetObjMapRules.
func (mr *MockReaderWriterMockRecorder) GetObjMapRules(ctx, dbTypeS, dbTypeT interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetObjMapRules), ctx, dbTypeS, dbTypeT)
}

// GetSqlMapRuleById mocks base method.
func (m *MockReaderWriter) GetSqlMapRuleById(ctx context.Context, ruleId int) (*template.SqlmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSqlMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(*template.SqlmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSqlMapRuleById indicates an expected call of GetSqlMapRuleById.
func (mr *MockReaderWriterMockRecorder) GetSqlMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSqlMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).GetSqlMapRuleById), ctx, ruleId)
}

// GetSqlMapRules mocks base method.
func (m *MockReaderWriter) GetSqlMapRules(ctx context.Context, dbTypeS, dbTypeT string) ([]*template.SqlmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSqlMapRules", ctx, dbTypeS, dbTypeT)
	ret0, _ := ret[0].([]*template.SqlmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSqlMapRules indicates an expected call of GetSqlMapRules.
func (mr *MockReaderWriterMockRecorder) GetSqlMapRules(ctx, dbTypeS, dbTypeT interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSqlMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetSqlMapRules), ctx, dbTypeS, dbTypeT)
}

// GetTabColMapRules mocks base method.
func (m *MockReaderWriter) GetTabColMapRules(ctx context.Context, dbTypeS, dbTypeT string, ruleIds []string) ([]*template.TabcolMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabColMapRules", ctx, dbTypeS, dbTypeT, ruleIds)
	ret0, _ := ret[0].([]*template.TabcolMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabColMapRules indicates an expected call of GetTabColMapRules.
func (mr *MockReaderWriterMockRecorder) GetTabColMapRules(ctx, dbTypeS, dbTypeT, ruleIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabColMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetTabColMapRules), ctx, dbTypeS, dbTypeT, ruleIds)
}

// GetTabcolCustMapRules mocks base method.
func (m *MockReaderWriter) GetTabcolCustMapRules(ctx context.Context, tabCol *template.TabcolCustMap) ([]*template.TabcolCustMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTabcolCustMapRules", ctx, tabCol)
	ret0, _ := ret[0].([]*template.TabcolCustMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTabcolCustMapRules indicates an expected call of GetTabcolCustMapRules.
func (mr *MockReaderWriterMockRecorder) GetTabcolCustMapRules(ctx, tabCol interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTabcolCustMapRules", reflect.TypeOf((*MockReaderWriter)(nil).GetTabcolCustMapRules), ctx, tabCol)
}

// GetTablecolMapRuleById mocks base method.
func (m *MockReaderWriter) GetTablecolMapRuleById(ctx context.Context, ruleId int) (*template.TabcolMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTablecolMapRuleById", ctx, ruleId)
	ret0, _ := ret[0].(*template.TabcolMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTablecolMapRuleById indicates an expected call of GetTablecolMapRuleById.
func (mr *MockReaderWriterMockRecorder) GetTablecolMapRuleById(ctx, ruleId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTablecolMapRuleById", reflect.TypeOf((*MockReaderWriter)(nil).GetTablecolMapRuleById), ctx, ruleId)
}

// GetTemplate mocks base method.
func (m *MockReaderWriter) GetTemplate(ctx context.Context, templateId int) (*template.TmplateInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTemplate", ctx, templateId)
	ret0, _ := ret[0].(*template.TmplateInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTemplate indicates an expected call of GetTemplate.
func (mr *MockReaderWriterMockRecorder) GetTemplate(ctx, templateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplate", reflect.TypeOf((*MockReaderWriter)(nil).GetTemplate), ctx, templateId)
}

// GetTemplateRuleID mocks base method.
func (m *MockReaderWriter) GetTemplateRuleID(ctx context.Context, templateId int) ([]*template.TmplateRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTemplateRuleID", ctx, templateId)
	ret0, _ := ret[0].([]*template.TmplateRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTemplateRuleID indicates an expected call of GetTemplateRuleID.
func (mr *MockReaderWriterMockRecorder) GetTemplateRuleID(ctx, templateId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTemplateRuleID", reflect.TypeOf((*MockReaderWriter)(nil).GetTemplateRuleID), ctx, templateId)
}

// ListColDefaultMapRulesByTemplateId mocks base method.
func (m *MockReaderWriter) ListColDefaultMapRulesByTemplateId(ctx context.Context, templateId, page, pageSize int) ([]*template.ColdefaultMapRule, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListColDefaultMapRulesByTemplateId", ctx, templateId, page, pageSize)
	ret0, _ := ret[0].([]*template.ColdefaultMapRule)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListColDefaultMapRulesByTemplateId indicates an expected call of ListColDefaultMapRulesByTemplateId.
func (mr *MockReaderWriterMockRecorder) ListColDefaultMapRulesByTemplateId(ctx, templateId, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListColDefaultMapRulesByTemplateId", reflect.TypeOf((*MockReaderWriter)(nil).ListColDefaultMapRulesByTemplateId), ctx, templateId, page, pageSize)
}

// ListObjMapRules mocks base method.
func (m *MockReaderWriter) ListObjMapRules(ctx context.Context, templateId, page, pageSize int) ([]*template.ObjmapRule, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjMapRules", ctx, templateId, page, pageSize)
	ret0, _ := ret[0].([]*template.ObjmapRule)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListObjMapRules indicates an expected call of ListObjMapRules.
func (mr *MockReaderWriterMockRecorder) ListObjMapRules(ctx, templateId, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjMapRules", reflect.TypeOf((*MockReaderWriter)(nil).ListObjMapRules), ctx, templateId, page, pageSize)
}

// ListSqlMapRules mocks base method.
func (m *MockReaderWriter) ListSqlMapRules(ctx context.Context, templateId, page, pageSize int) ([]*template.SqlmapRule, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSqlMapRules", ctx, templateId, page, pageSize)
	ret0, _ := ret[0].([]*template.SqlmapRule)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListSqlMapRules indicates an expected call of ListSqlMapRules.
func (mr *MockReaderWriterMockRecorder) ListSqlMapRules(ctx, templateId, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSqlMapRules", reflect.TypeOf((*MockReaderWriter)(nil).ListSqlMapRules), ctx, templateId, page, pageSize)
}

// ListTabColMapRules mocks base method.
func (m *MockReaderWriter) ListTabColMapRules(ctx context.Context, templateId, page, pageSize int) ([]*template.TabcolMapRule, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTabColMapRules", ctx, templateId, page, pageSize)
	ret0, _ := ret[0].([]*template.TabcolMapRule)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTabColMapRules indicates an expected call of ListTabColMapRules.
func (mr *MockReaderWriterMockRecorder) ListTabColMapRules(ctx, templateId, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTabColMapRules", reflect.TypeOf((*MockReaderWriter)(nil).ListTabColMapRules), ctx, templateId, page, pageSize)
}

// ListTemplates mocks base method.
func (m *MockReaderWriter) ListTemplates(ctx context.Context, templateType string, page, pageSize int) ([]*template.TmplateInformation, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTemplates", ctx, templateType, page, pageSize)
	ret0, _ := ret[0].([]*template.TmplateInformation)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTemplates indicates an expected call of ListTemplates.
func (mr *MockReaderWriterMockRecorder) ListTemplates(ctx, templateType, page, pageSize interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTemplates", reflect.TypeOf((*MockReaderWriter)(nil).ListTemplates), ctx, templateType, page, pageSize)
}

// UpdateColDefaultMapRule mocks base method.
func (m *MockReaderWriter) UpdateColDefaultMapRule(ctx context.Context, rule *template.ColdefaultMapRule) (*template.ColdefaultMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateColDefaultMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.ColdefaultMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateColDefaultMapRule indicates an expected call of UpdateColDefaultMapRule.
func (mr *MockReaderWriterMockRecorder) UpdateColDefaultMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateColDefaultMapRule", reflect.TypeOf((*MockReaderWriter)(nil).UpdateColDefaultMapRule), ctx, rule)
}

// UpdateObjMapRule mocks base method.
func (m *MockReaderWriter) UpdateObjMapRule(ctx context.Context, rule *template.ObjmapRule) (*template.ObjmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateObjMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.ObjmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateObjMapRule indicates an expected call of UpdateObjMapRule.
func (mr *MockReaderWriterMockRecorder) UpdateObjMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateObjMapRule", reflect.TypeOf((*MockReaderWriter)(nil).UpdateObjMapRule), ctx, rule)
}

// UpdateSqlMapRule mocks base method.
func (m *MockReaderWriter) UpdateSqlMapRule(ctx context.Context, rule *template.SqlmapRule) (*template.SqlmapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSqlMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.SqlmapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSqlMapRule indicates an expected call of UpdateSqlMapRule.
func (mr *MockReaderWriterMockRecorder) UpdateSqlMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSqlMapRule", reflect.TypeOf((*MockReaderWriter)(nil).UpdateSqlMapRule), ctx, rule)
}

// UpdateTabcolCustMap mocks base method.
func (m *MockReaderWriter) UpdateTabcolCustMap(ctx context.Context, rule *template.TabcolCustMap) (*template.TabcolCustMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTabcolCustMap", ctx, rule)
	ret0, _ := ret[0].(*template.TabcolCustMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTabcolCustMap indicates an expected call of UpdateTabcolCustMap.
func (mr *MockReaderWriterMockRecorder) UpdateTabcolCustMap(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTabcolCustMap", reflect.TypeOf((*MockReaderWriter)(nil).UpdateTabcolCustMap), ctx, rule)
}

// UpdateTablecolMapRule mocks base method.
func (m *MockReaderWriter) UpdateTablecolMapRule(ctx context.Context, rule *template.TabcolMapRule) (*template.TabcolMapRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTablecolMapRule", ctx, rule)
	ret0, _ := ret[0].(*template.TabcolMapRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTablecolMapRule indicates an expected call of UpdateTablecolMapRule.
func (mr *MockReaderWriterMockRecorder) UpdateTablecolMapRule(ctx, rule interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTablecolMapRule", reflect.TypeOf((*MockReaderWriter)(nil).UpdateTablecolMapRule), ctx, rule)
}

// UpdateTemplate mocks base method.
func (m *MockReaderWriter) UpdateTemplate(ctx context.Context, templateInfo *template.TmplateInformation) (*template.TmplateInformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTemplate", ctx, templateInfo)
	ret0, _ := ret[0].(*template.TmplateInformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTemplate indicates an expected call of UpdateTemplate.
func (mr *MockReaderWriterMockRecorder) UpdateTemplate(ctx, templateInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTemplate", reflect.TypeOf((*MockReaderWriter)(nil).UpdateTemplate), ctx, templateInfo)
}
