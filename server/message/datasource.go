package message

import (
	"gitee.com/pingcap_enterprise/tms/common/structs"
)

type TryConnectionType int

const TryConnectionTypeDefault = TryConnectionType(0)
const TryConnectionTypeASM = TryConnectionType(1)
const TryConnectionTypeDefaultAndASM = TryConnectionType(2)

func (i TryConnectionType) String() string {
	switch i {
	case TryConnectionTypeDefault:
		return "DEFAULT"
	case TryConnectionTypeASM:
		return "ASM"
	case TryConnectionTypeDefaultAndASM:
		return "DEFAULTANDASM"
	}
	return "UNKNOWN"
}

// CreateOrUpdateDataSourceReq create dataSource http message
type CreateOrUpdateDataSourceReq struct {
	*DataSource
}

// TryDataSourceReq try dataSource http message
type TryDataSourceReq struct {
	*DataSource

	TryConnectionType TryConnectionType `json:"tryConnectionType"`
}

type CreateOrUpdateDataSourceResp struct {
}

type GetDataSourceVersionResp struct {
	Version string `json:"version"`
}

type GetDataSourceCharacterSetResp struct {
	CharacterSet string `json:"characterSet"`
}

type BatchDeleteDataSourceReq struct {
	DataSourceIDs []int `json:"dataSourceIds" validate:"required,gt=0,lt=1000"`
}

type BatchDeleteDataSourceResp struct {
}

type DetailDataSourceReq struct {
	DataSourceID int `json:"dataSourceId"`
}

type DetailDataSourceResp struct {
	*DataSource `json:"dataSource"`
}

type TestConnectionReq struct {
	DataSourceID int `json:"dataSourceId"`
}

type TestConnectionResp struct {
}

type ListDataSourcesReq struct {
	PageRequest
	DataSourceName   string `json:"dataSourceName" validate:"omitempty,min=1,max=100"`
	DbType           string `json:"dbType" validate:"omitempty,oneof=oracle mysql tidb tidb-proxy oracle-adg csv" enums:"oracle,mysql,tidb,tidb-proxy,oracle-adg csv"`
	ConnectionStatus string `json:"connectionStatus" validate:"omitempty,oneof=Y N S" enums:"Y(success),N(failed),S(not test)"`
}

type ListDataSourcesResp struct {
	DataSources []*DataSource
}

type DataSource struct {
	DataSourceID     int    `json:"dataSourceId"`
	DataSourceName   string `json:"dataSourceName" validate:"required,min=1,max=100"`
	DbType           string `json:"dbType" validate:"required,oneof=oracle mysql tidb tidb-proxy oracle-adg csv" enums:"oracle,mysql,tidb,tidb-proxy,oracle-adg csv"`
	HostIp           string `json:"hostIp" validate:"required"`
	HostPort         int    `json:"hostPort" validate:"required"`
	DbName           string `json:"dbName" validate:"max=100"`
	ServiceName      string `json:"serviceName" validate:"max=100"`
	User             string `json:"user" validate:"required,max=100"`
	Password         string `json:"password" validate:"max=100"`
	PasswordEncrypt  string `json:"passwordEncrypt" validate:"max=1000"`
	ConnectParams    string `json:"connectParams" validate:"max=100"`
	TableOption      string `json:"tableOption" validate:"max=100"`
	Charset          string `json:"charset" validate:"max=100"`
	ConnectionStatus string `json:"connectionStatus" validate:"required,oneof=Y N S" enums:"Y(success),N(failed),S(not test)"`
	ProxySourceId    int    `json:"proxySourceId"`

	EnableIncrementSync bool   `json:"enableIncrementSync"`
	DBVersion           string `json:"dbVersion"`
	PDBFlag             string `json:"pdbFlag"`
	LinkFlag            string `json:"linkFlag"`
	PDBName             string `json:"pdbName"`
	PDBDBName           string `json:"pdbDBName"`
	ASMSid              string `json:"asmSid"`
	ASMOracleHome       string `json:"asmOracleHome"`
	ASMHome             string `json:"asmHome"`
	ASMDBUser           string `json:"asmDBUser"`
	ASMDBPasswd         string `json:"asmDBPasswd"`
	ASMDBPasswdEncrypt  string `json:"asmDBPasswdEncrypt"`
	DBConnectionMode    string `json:"dbConnectionMode"`
	SIDName             string `json:"sidName"`
	LogFileStoreMode    string `json:"logFileStoreMode"`
	ASMDBIp             string `json:"asmDBIp"`
	ASMDBPort           int    `json:"asmDBPort"`
	ASMDBName           string `json:"asmDBName"`
	OracleHome          string `json:"oracleHome"`
	OracleSID           string `json:"oracleSID"`
	AsSysDBA            int    `json:"asSysDBA"`
	HostList            string `json:"hostList"`
	PrometheusUrl       string `json:"prometheusUrl"`

	*BaseFields
}

type GetDataSourceSchemasReq struct {
	DataSourceID int `json:"dataSourceId" form:"dataSourceId"`
}

type GetDataSourceSchemasResp struct {
	SchemaInfos []*structs.SchemaInfo `json:"children" form:"children"`
}

type GetDataSourceSchemaTablesReq struct {
	DataSourceID int      `json:"dataSourceId" form:"dataSourceId"`
	Schemas      []string `json:"schemas" form:"schemas"`
}

type GetDataSourceSchemaTablesResp struct {
	SchemaInfos []*structs.SchemaInfo `json:"children" form:"children"`
}

type GetUnselectedDataSourceSchemasReq struct {
	DataSourceID int `json:"dataSourceId" form:"dataSourceId"`
	ChannelID    int `json:"channelId" form:"channelId"`
}

type SearchUnselectedDataSourceSchemasReq struct {
	DataSourceID int    `json:"dataSourceId"`
	ChannelID    int    `json:"channelId"`
	SchemaNameS  string `json:"schemaNameS"`
}

type GetUnselectedDataSourceSchemasResp struct {
	SchemaInfos []*structs.SchemaInfo `json:"children"`
}

type GetTablePartitionReq struct {
	DataSourceID int    `json:"dataSourceId"`
	SchemaName   string `json:"schemaName"`
	TableName    string `json:"tableName"`
}
