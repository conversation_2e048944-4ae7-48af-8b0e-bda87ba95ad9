package message

// CommonWorkerResponseDTO represents a single worker's response in source operations
type CommonWorkerResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
	Source string `json:"source"`
	Worker string `json:"worker"`
}

// OperateSourceResponseDTO is the message DTO for dm.OperateSourceResponseDTO
type OperateSourceResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// StartTaskResponseDTO is the message DTO for dm.StartTaskResponseDTO
type StartTaskResponseDTO struct {
	Result      bool                       `json:"result"`
	Msg         string                     `json:"msg"`
	Sources     []*CommonWorkerResponseDTO `json:"sources"`
	CheckResult string                     `json:"check_result"`
}

// OperateTaskResponseDTO is the message DTO for dm.OperateTaskResponseDTO
type OperateTaskResponseDTO struct {
	Op      string                     `json:"op"`
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// UpdateTaskResponseDTO is the message DTO for dm.UpdateTaskResponseDTO
type UpdateTaskResponseDTO struct {
	Result      bool                       `json:"result"`
	Msg         string                     `json:"msg"`
	Sources     []*CommonWorkerResponseDTO `json:"sources"`
	CheckResult string                     `json:"check_result"`
}

// QueryStatusResponseDTO is the message DTO for dm.QueryStatusResponseDTO
type QueryStatusResponseDTO struct {
	Result  bool                 `json:"result"`
	Msg     string               `json:"msg"`
	Sources []*QuerySourceStatus `json:"sources"`
}

// QuerySourceStatus represents the status of a single source in query-status response
type QuerySourceStatus struct {
	Result        bool             `json:"result"`
	Msg           string           `json:"msg,omitempty"`
	SourceStatus  *SourceStatus    `json:"source_status,omitempty"`
	SubTaskStatus []*SubTaskStatus `json:"sub_task_status,omitempty"`
}

// SourceStatus represents source information in query-status
type SourceStatus struct {
	Source string `json:"source"`
	Worker string `json:"worker"`
}

// SubTaskStatus represents subtask status information
type SubTaskStatus struct {
	Name       string             `json:"name"`
	Stage      int                `json:"stage"`
	Unit       int                `json:"unit"`
	Result     *SubTaskResult     `json:"result,omitempty"`
	Status     *SubTaskStatusInfo `json:"status,omitempty"`
	Validation *ValidationStatus  `json:"validation,omitempty"`
}

// SubTaskResult represents the result information of a subtask
type SubTaskResult struct {
	IsCanceled bool            `json:"is_canceled,omitempty"`
	Errors     []*SubTaskError `json:"errors,omitempty"`
}

// SubTaskError represents error information in subtask result
type SubTaskError struct {
	ErrCode    int    `json:"err_code"`
	ErrClass   string `json:"err_class"`
	ErrScope   string `json:"err_scope"`
	ErrLevel   string `json:"err_level"`
	Message    string `json:"message"`
	Workaround string `json:"workaround,omitempty"`
}

// SubTaskStatusInfo represents the status information of a subtask
type SubTaskStatusInfo struct {
	Sync *SyncStatus `json:"sync,omitempty"`
}

// SyncStatus represents sync status information
type SyncStatus struct {
	TotalEvents      int64  `json:"total_events"`
	TotalTps         int64  `json:"total_tps,omitempty"`
	MasterBinlog     string `json:"master_binlog"`
	MasterBinlogGtid string `json:"master_binlog_gtid"`
	SyncerBinlog     string `json:"syncer_binlog"`
	SyncerBinlogGtid string `json:"syncer_binlog_gtid"`
	Synced           bool   `json:"synced,omitempty"`
	BinlogType       string `json:"binlog_type"`
	TotalRows        int64  `json:"total_rows"`
	TotalRps         int64  `json:"total_rps,omitempty"`
}

// ValidationStatus represents validation status information
type ValidationStatus struct {
	Task                string `json:"task"`
	Source              string `json:"source"`
	Mode                string `json:"mode"`
	Stage               int    `json:"stage"`
	ValidatorBinlog     string `json:"validator_binlog,omitempty"`
	ValidatorBinlogGtid string `json:"validator_binlog_gtid,omitempty"`
	ProcessedRowsStatus string `json:"processed_rows_status"`
	PendingRowsStatus   string `json:"pending_rows_status"`
	ErrorRowsStatus     string `json:"error_rows_status"`
}

// TaskConfigDTO represents task configuration
type TaskConfigDTO struct {
	Name                       string                          `json:"name"`
	TaskMode                   string                          `json:"task_mode"`
	IsSharding                 bool                            `json:"is_sharding"`
	ShardMode                  string                          `json:"shard_mode"`
	StrictOptimisticShardMode  bool                            `json:"strict_optimistic_shard_mode"`
	IgnoreCheckingItems        []string                        `json:"ignore_checking_items"`
	MetaSchema                 string                          `json:"meta_schema"`
	EnableHeartbeat            bool                            `json:"enable_heartbeat"`
	HeartbeatUpdateInterval    int                             `json:"heartbeat_update_interval"`
	HeartbeatReportInterval    int                             `json:"heartbeat_report_interval"`
	Timezone                   string                          `json:"timezone"`
	CaseSensitive              bool                            `json:"case_sensitive"`
	CollationCompatible        string                          `json:"collation_compatible"`
	TargetDatabase             *TaskTargetDatabase             `json:"target_database"`
	MySQLInstances             []*MySQLInstance                `json:"mysql_instances"`
	OnlineDDL                  bool                            `json:"online_ddl"`
	ShadowTableRules           []string                        `json:"shadow_table_rules"`
	TrashTableRules            []string                        `json:"trash_table_rules"`
	OnlineDDLScheme            string                          `json:"online_ddl_scheme"`
	Routes                     map[string]*RouteRule           `json:"routes"`
	Filters                    map[string]*BinlogEventRule     `json:"filters"`
	ColumnMappings             map[string]*ColumnRule          `json:"column_mappings"`
	ExpressionFilter           map[string]*ExpressionFilter    `json:"expression_filter"`
	BlockAllowList             map[string]*BlockAllowListRule  `json:"block_allow_list"`
	Mydumpers                  map[string]*MydumperConfig      `json:"mydumpers"`
	Loaders                    map[string]*LoaderConfig        `json:"loaders"`
	Syncers                    map[string]*SyncerConfig        `json:"syncers"`
	Validators                 map[string]*ValidatorConfig     `json:"validators"`
	CleanDumpFile              bool                            `json:"clean_dump_file"`
	AnsiQuotes                 bool                            `json:"ansi_quotes"`
	RemoveMeta                 bool                            `json:"remove_meta"`
	Experimental               *ExperimentalConfig             `json:"experimental"`
}

// TaskTargetDatabase represents target database configuration
type TaskTargetDatabase struct {
	Host             string             `json:"host"`
	Port             int                `json:"port"`
	User             string             `json:"user"`
	Password         string             `json:"password"`
	MaxAllowedPacket interface{}        `json:"max_allowed_packet"`
	Session          map[string]string  `json:"session"`
	Security         *SecurityConfig    `json:"security"`
}

// MySQLInstance represents MySQL instance configuration
type MySQLInstance struct {
	SourceID            string              `json:"source_id"`
	Meta                *Meta               `json:"meta"`
	FilterRules         []string            `json:"filter_rules"`
	ColumnMappingRules  []string            `json:"column_mapping_rules"`
	RouteRules          []string            `json:"route_rules"`
	ExpressionFilters   []string            `json:"expression_filters"`
	BlockAllowList      string              `json:"block_allow_list"`
	MydumperConfigName  string              `json:"mydumper_config_name"`
	Mydumper            *MydumperConfig     `json:"mydumper"`
	MydumperThread      int                 `json:"mydumper_thread"`
	LoaderConfigName    string              `json:"loader_config_name"`
	Loader              *LoaderConfig       `json:"loader"`
	LoaderThread        int                 `json:"loader_thread"`
	SyncerConfigName    string              `json:"syncer_config_name"`
	Syncer              *SyncerConfig       `json:"syncer"`
	SyncerThread        int                 `json:"syncer_thread"`
	ValidatorConfigName string              `json:"validator_config_name"`
}

// RouteRule represents routing rules
type RouteRule struct {
	SchemaPattern string `json:"schema_pattern"`
	TablePattern  string `json:"table_pattern"`
	TargetSchema  string `json:"target_schema"`
	TargetTable   string `json:"target_table"`
}

// BinlogEventRule represents binlog event filtering rules
type BinlogEventRule struct {
	SchemaPattern string   `json:"schema_pattern"`
	TablePattern  string   `json:"table_pattern"`
	Events        []string `json:"events"`
	SQLPattern    []string `json:"sql_pattern"`
	Action        string   `json:"action"`
}

// ColumnRule represents column mapping rules
type ColumnRule struct {
	SchemaPattern string   `json:"schema_pattern"`
	TablePattern  string   `json:"table_pattern"`
	SourceColumn  string   `json:"source_column"`
	TargetColumn  string   `json:"target_column"`
	Expression    string   `json:"expression"`
	Arguments     []string `json:"arguments"`
}

// BlockAllowListRule represents block/allow list rules
type BlockAllowListRule struct {
	DoTables     []*TableRef `json:"do_tables"`
	DoDbs        []string    `json:"do_dbs"`
	IgnoreTables []*TableRef `json:"ignore_tables"`
	IgnoreDbs    []string    `json:"ignore_dbs"`
}

// TableRef represents table reference
type TableRef struct {
	DbName  string `json:"db_name"`
	TblName string `json:"tbl_name"`
}

// ExpressionFilter represents expression filter
type ExpressionFilter struct {
	Schema             string `json:"schema"`
	Table              string `json:"table"`
	InsertValueExpr    string `json:"insert_value_expr"`
	UpdateOldValueExpr string `json:"update_old_value_expr"`
	UpdateNewValueExpr string `json:"update_new_value_expr"`
	DeleteValueExpr    string `json:"delete_value_expr"`
}

// WorkerConfigDTO represents worker node configuration
type WorkerConfigDTO struct {
	Name              string `json:"name"`
	LogLevel          string `json:"log_level"`
	LogFile           string `json:"log_file"`
	LogFormat         string `json:"log_format"`
	LogRotate         string `json:"log_rotate"`
	Join              string `json:"join"`
	WorkerAddr        string `json:"worker_addr"`
	AdvertiseAddr     string `json:"advertise_addr"`
	ConfigFile        string `json:"config_file"`
	KeepaliveTTL      int64  `json:"keepalive_ttl"`
	RelayKeepaliveTTL int64  `json:"relay_keepalive_ttl"`
	RelayDir          string `json:"relay_dir"`
	SSLCA             string `json:"ssl_ca"`
	SSLCert           string `json:"ssl_cert"`
	SSLKey            string `json:"ssl_key"`
	SSLCABase64       string `json:"ssl_ca_base64"`
	SSLKeyBase64      string `json:"ssl_key_base64"`
	SSLCertBase64     string `json:"ssl_cert_base64"`
}

// MasterConfigDTO represents master node configuration
type MasterConfigDTO struct {
	LogLevel                string                 `json:"log_level"`
	LogFile                 string                 `json:"log_file"`
	LogFormat               string                 `json:"log_format"`
	LogRotate               string                 `json:"log_rotate"`
	RPCTimeout              string                 `json:"rpc_timeout"`
	RPCRateLimit            float64                `json:"rpc_rate_limit"`
	RPCRateBurst            int                    `json:"rpc_rate_burst"`
	MasterAddr              string                 `json:"master_addr"`
	AdvertiseAddr           string                 `json:"advertise_addr"`
	ConfigFile              string                 `json:"config_file"`
	Name                    string                 `json:"name"`
	DataDir                 string                 `json:"data_dir"`
	PeerURLs                string                 `json:"peer_urls"`
	AdvertisePeerURLs       string                 `json:"advertise_peer_urls"`
	InitialCluster          string                 `json:"initial_cluster"`
	InitialClusterState     string                 `json:"initial_cluster_state"`
	Join                    string                 `json:"join"`
	MaxTxnOps               int64                  `json:"max_txn_ops"`
	MaxRequestBytes         int64                  `json:"max_request_bytes"`
	AutoCompactionMode      string                 `json:"auto_compaction_mode"`
	AutoCompactionRetention string                 `json:"auto_compaction_retention"`
	QuotaBackendBytes       int64                  `json:"quota_backend_bytes"`
	OpenAPI                 bool                   `json:"openapi"`
	V1SourcesPath           string                 `json:"v1_sources_path"`
	SSLCA                   string                 `json:"ssl_ca"`
	SSLCert                 string                 `json:"ssl_cert"`
	SSLKey                  string                 `json:"ssl_key"`
	SSLCABase64             string                 `json:"ssl_ca_base64"`
	SSLKeyBase64            string                 `json:"ssl_key_base64"`
	SSLCertBase64           string                 `json:"ssl_cert_base64"`
	SecretKeyPath           string                 `json:"secret_key_path"`
	Experimental            map[string]interface{} `json:"experimental"`
}

// OperateWorkerRelayResponseDTO is the message DTO for dm.OperateWorkerRelayResponseDTO
type OperateWorkerRelayResponseDTO struct {
	Op      string                     `json:"op"`
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// PurgeWorkerRelayResponseDTO is the message DTO for dm.PurgeWorkerRelayResponseDTO
type PurgeWorkerRelayResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// ShowDDLLocksResponseDTO is the message DTO for dm.ShowDDLLocksResponseDTO
type ShowDDLLocksResponseDTO struct {
	Result bool       `json:"result"`
	Msg    string     `json:"msg"`
	Locks  []*DDLLock `json:"locks"`
}

// DDLLock represents a DDL lock
type DDLLock struct {
	ID       string   `json:"id"`
	Task     string   `json:"task"`
	Mode     string   `json:"mode"`
	Owner    string   `json:"owner"`
	DDLs     []string `json:"DDLs"`
	Synced   []string `json:"synced"`
	Unsynced []string `json:"unsynced"`
}

// UnlockDDLLockResponseDTO is the message DTO for dm.UnlockDDLLockResponseDTO
type UnlockDDLLockResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// CheckTaskResponseDTO is the message DTO for dm.CheckTaskResponseDTO
type CheckTaskResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// GetConfigResponseDTO is the message DTO for dm.GetConfigResponseDTO
type GetConfigResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
	Config string `json:"config"`
}

// TransferSourceResponseDTO is the message DTO for dm.TransferSourceResponseDTO
type TransferSourceResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// StartValidationResponseDTO is the message DTO for dm.StartValidationResponseDTO
type StartValidationResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// StopValidationResponseDTO is the message DTO for dm.StopValidationResponseDTO
type StopValidationResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// OperateLeaderResponseDTO is the message DTO for dm.OperateLeaderResponseDTO
type OperateLeaderResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// OfflineMemberResponseDTO is the message DTO for dm.OfflineMemberResponseDTO
type OfflineMemberResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// ValidationStatusDetail represents detailed validation status
type ValidationStatusDetail struct {
	Validators    []*ValidationStatus      `json:"validators"`
	TableStatuses []*ValidationTableStatus `json:"table_statuses"`
}

// ValidationTableStatus represents validation status for a table
type ValidationTableStatus struct {
	Source        string `json:"source"`
	SchemaName    string `json:"schema_name"`
	TableName     string `json:"table_name"`
	Stage         string `json:"stage"`
	Message       string `json:"message"`
	ProcessedRows int64  `json:"processed_rows"`
	PendingRows   int64  `json:"pending_rows"`
	FailedRows    int64  `json:"failed_rows"`
}

// ValidationError represents a validation error
type ValidationError struct {
	ID         string `json:"id"`
	Source     string `json:"source"`
	SchemaName string `json:"schema_name"`
	TableName  string `json:"table_name"`
	Stage      string `json:"stage"`
	Message    string `json:"message"`
	Timestamp  int64  `json:"timestamp"`
}

// Meta represents binlog position metadata
type Meta struct {
	BinLogName string `json:"binlog_name"`
	BinLogPos  uint32 `json:"binlog_pos"`
	BinLogGTID string `json:"binlog_gtid"`
}

// SecurityConfig represents SSL/TLS security configuration
type SecurityConfig struct {
	SSLCa   string `json:"ssl_ca"`
	SSLCert string `json:"ssl_cert"`
	SSLKey  string `json:"ssl_key"`
}

// ExperimentalConfig represents experimental feature configuration
type ExperimentalConfig struct {
	AsyncCheckpointFlush bool `json:"async_checkpoint_flush"`
}

// MydumperConfig represents Mydumper configuration
type MydumperConfig struct {
	MydumperPath  string `json:"mydumper_path"`
	Threads       int    `json:"threads"`
	ChunkFilesize string `json:"chunk_filesize"`
	StatementSize int    `json:"statement_size"`
	Rows          int    `json:"rows"`
	Where         string `json:"where"`
	SkipTzUtc     bool   `json:"skip_tz_utc"`
	ExtraArgs     string `json:"extra_args"`
}

// LoaderConfig represents Loader configuration
type LoaderConfig struct {
	PoolSize            int    `json:"pool_size"`
	Dir                 string `json:"dir"`
	SortingDirPhysical  string `json:"sorting_dir_physical"`
	ImportMode          string `json:"import_mode"`
	OnDuplicate         string `json:"on_duplicate"`
	OnDuplicateLogical  string `json:"on_duplicate_logical"`
	OnDuplicatePhysical string `json:"on_duplicate_physical"`
	DiskQuotaPhysical   int64  `json:"disk_quota_physical"`
	ChecksumPhysical    string `json:"checksum_physical"`
	Analyze             string `json:"analyze"`
	RangeConcurrency    int    `json:"range_concurrency"`
	CompressKVPairs     string `json:"compress_kv_pairs"`
	PdAddr              string `json:"pd_addr"`
}

// SyncerConfig represents Syncer configuration
type SyncerConfig struct {
	MetaFile                string `json:"meta_file"`
	WorkerCount             int    `json:"worker_count"`
	Batch                   int    `json:"batch"`
	QueueSize               int    `json:"queue_size"`
	CheckpointFlushInterval int    `json:"checkpoint_flush_interval"`
	Compact                 bool   `json:"compact"`
	MultipleRows            bool   `json:"multiple_rows"`
	MaxRetry                int    `json:"max_retry"`
	AutoFixGtid             bool   `json:"auto_fix_gtid"`
	EnableGtid              bool   `json:"enable_gtid"`
	DisableDetect           bool   `json:"disable_detect"`
	SafeMode                bool   `json:"safe_mode"`
	SafeModeDuration        string `json:"safe_mode_duration"`
	EnableAnsiQuotes        bool   `json:"enable_ansi_quotes"`
}

// ValidatorConfig represents Validator configuration
type ValidatorConfig struct {
	Mode               string `json:"mode"`
	WorkerCount        int    `json:"worker_count"`
	ValidateInterval   string `json:"validate_interval"`
	CheckInterval      string `json:"check_interval"`
	RowErrorDelay      string `json:"row_error_delay"`
	MetaFlushInterval  string `json:"meta_flush_interval"`
	BatchQuerySize     int    `json:"batch_query_size"`
	MaxPendingRowSize  string `json:"max_pending_row_size"`
	MaxPendingRowCount int64  `json:"max_pending_row_count"`
}