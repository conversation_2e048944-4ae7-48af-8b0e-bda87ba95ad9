/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package message

import (
	"time"
)

type MigrationDataSchemaReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	QueryStatus string `json:"query_status" enums:"SUCCESS,FAILED,WAITING"`
}

type MigrationDataSchemaPageReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	QueryStatus string `json:"query_status" enums:"SUCCESS,FAILED,WAITING"`
	PageRequest
}

type MigrationDataBatchRetryReq struct {
	TaskID      int    `json:"task_id"`
	TableID     []uint `json:"table_id"` // 其实这里是table_detail_summaries的PK ID
	SchemaNameS string `json:"schema_name_s"`
	QueryStatus string `json:"query_status" enums:"SUCCESS,FAILED,WAITING"`
}

type MigrationDataChunkFailedAndWaitingReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"`
}

type MigrationDataChunkFailedAndWaitingResp struct {
	MigrationDataFailedAndWaitingChunkDetail []MigrationDataTableFailedAndWaitingChunkDetail `json:"migration_data_failed_and_waiting_chunk_detail"`
}

type MigrationDataChunkErrorReq struct {
	TaskID      int    `json:"task_id"`
	ID          uint   `json:"id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"`
}

type MigrationDataChunkErrorResp struct {
	MigrationDataFailedChunkErrorDetail []MigrationDataTableFailedAndWaitingChunkDetail `json:"migration_data_failed_chunk_error_detail"`
}

type MigrationDataFailedChunkRetryReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"`
	ChunkID     []uint `json:"chunk_id"`
}

type MigrationDataFailedChunkBatchRetryReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"`
}

type FetchChunkDataReq struct {
	TaskID      int    `json:"taskId" validate:"required"`
	SchemaNameS string `json:"schemaNameS" validate:"required"`
	TableNameS  string `json:"tableNameS" validate:"required"`
}

type ListChunkDataReq struct {
	TaskID      int    `json:"taskId" form:"taskId" validate:"required"`
	SchemaNameS string `json:"schemaNameS" form:"schemaNameS" validate:"required"`
	TableNameS  string `json:"tableNameS" form:"tableNameS" validate:"required"`

	RowExecStatus string `json:"rowExecStatus" form:"rowExecStatus"`
	PageRequest
}

type GetChunkDataSummaryReq struct {
	TaskID      int    `json:"taskId" form:"taskId" validate:"required"`
	SchemaNameS string `json:"schemaNameS" form:"schemaNameS" validate:"required"`
	TableNameS  string `json:"tableNameS" form:"tableNameS" validate:"required"`
}

type GetChunkDataSummaryResp struct {
	TaskID           int    `json:"taskID"`
	SchemaNameS      string `json:"schemaNameS"`
	TableNameS       string `json:"tableNameS"`
	Status           string `json:"status"`
	RowIdCount       int64  `json:"rowIdCount"`
	TotalDetails     int64  `json:"totalDetails"`
	ProcessedDetails int64  `json:"processedDetails"`
	RemainingDetails int64  `json:"remainingDetails"`
	
	// ChunkDataAnalyze 统计信息
	TotalCount       int64  `json:"totalCount"`       // ChunkDataAnalyze总数
	SuccessCount     int64  `json:"successCount"`     // SUCCESS状态数量
	FailedCount      int64  `json:"failedCount"`      // FAILED状态数量  
	RunningCount     int64  `json:"runningCount"`     // RUNNING状态数量
	WaitingCount     int64  `json:"waitingCount"`     // WAITING状态数量
}

type ReplayChunkDataReq struct {
	TaskId      int    `json:"taskId" validate:"required"`
	ChannelId   int    `json:"channelId" validate:"required"`
	SchemaNameS string `json:"schemaNameS" validate:"required"`
	TableNameS  string `json:"tableNameS" validate:"required"`
	BatchSize   int    `json:"batchSize" validate:"required,min=1,max=1000"`

	ExecuteIds []uint `json:"executeIds"`
	ExecuteAll bool   `json:"executeAll"`
}

type QueryChunkDataReq struct {
	TaskID       int    `json:"taskId" validate:"required"`
	SchemaNameS  string `json:"schemaNameS" validate:"required"`
	TableNameS   string `json:"tableNameS" validate:"required"`
	Status       string `json:"status"`
	ErrorMessage string `json:"errorMessage"`
	PageRequest
}

type FetchChunkDataResp struct {
}

type ListChunkDataResp struct {
	Data     []ChunkDataAnalyze    `json:"data"`
	Progress *ChunkReplayProgress  `json:"progress,omitempty"`
}

type ChunkReplayProgress struct {
	Total      int64      `json:"total"`
	Success    int64      `json:"success"`
	Failed     int64      `json:"failed"`
	Processing int64      `json:"processing"`
	StartTime  time.Time  `json:"startTime"`
	EndTime    *time.Time `json:"endTime,omitempty"`
}

type ChunkDataAnalyze struct {
	ID           uint
	ChannelId    int
	TaskId       int
	SchemaNameS  string
	TableNameS   string
	ChunkId      uint
	RowIDStr     string
	RowIDMD5     string
	Status       string
	ErrorMessage string

	Comment   string
	CreatedAt time.Time
	UpdatedAt time.Time
}

type ReplayChunkDataResp struct {
}

type MigrationDataTaskSummaryResp struct {
	TaskID               int                              `json:"task_id"`
	TotalTableNums       int64                            `json:"total_table_nums"`
	TotalMigrationNums   int                              `json:"total_migration_nums"`
	SuccessMigrationNums int                              `json:"success_migration_nums"`
	RunningMigrationNums int                              `json:"running_migration_nums"`
	FailedMigrationNums  int                              `json:"failed_migration_nums"`
	WaitingMigrationNums int                              `json:"waiting_migration_nums"`
	MigrationDetails     []MigrationDataTaskSummaryDetail `json:"migration_details"`
}

type MigrationDataSchemaDetailResp struct {
	MigrationDataSchemaDetail []MigrationDataSchemaDetail `json:"migration_data_schema_detail"`
}

type MigrationDataSchemaSummaryResp struct {
	MigrationSchemaSummary []MigrationDataTaskSummaryDetail `json:"migration_schema_summary"`
}

type MigrationDataProgressResp struct {
	TaskID int `json:"task_id"`

	TotalMigrationNums   int `json:"total_migration_nums"`
	SuccessMigrationNums int `json:"success_migration_nums"`
	RunningMigrationNums int `json:"running_migration_nums"`
	FailedMigrationNums  int `json:"failed_migration_nums"`

	TotalChunkNums   int `json:"total_chunk_nums"`
	SuccessChunkNums int `json:"success_chunk_nums"`
	RunningChunkNums int `json:"running_chunk_nums"`
	FailedChunkNums  int `json:"failed_chunk_nums"`

	TotalTaskNums   int `json:"total_task_nums"`
	SuccessTaskNums int `json:"success_task_nums"`
	RunningTaskNums int `json:"running_task_nums"`
	FailedTaskNums  int `json:"failed_task_nums"`

	ProgressLog       []*MigrationLogDetail `json:"progress_log"`
	ProgressStartTime time.Time             `json:"progress_start_time"`
	LastUpdateTime    time.Time             `json:"lastUpdateTime"`
}

type MigrationDataTableFailedAndWaitingChunkDetail struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"`
	ChunkDetail string `json:"chunk_detail"`
	TaskStatus  string `json:"task_status"`
	ErrorDetail string `json:"error_detail"`
}

type MigrationDataTaskSummaryDetail struct {
	ServiceName       string `json:"service_name"`
	SchemaName        string `json:"schema_name"`
	TotalTableNums    int64  `json:"total_table_nums"`
	MigrationNums     int    `json:"migration_nums"`
	SuccessNums       int    `json:"success_nums"`
	RunningNums       int    `json:"running_nums"`
	WaitingNums       int    `json:"waiting_nums"`
	FailedNums        int    `json:"failed_nums"`
	SuccessRatio      string `json:"success_ratio"`
	MigrationResult   string `json:"migration_result"`
	MigrationDuration string `json:"migration_duration"`
}

type MigrationDataSchemaDetail struct {
	ID                uint   `json:"id"`
	ServiceName       string `json:"service_name"`
	SchemaNameS       string `json:"schema_name_s"`
	TableNameS        string `json:"table_name_s"`
	TableTypeS        string `json:"table_type_s"`
	TotalChunks       int64  `json:"total_chunks"`
	ChunkSize         int64  `json:"chunk_size"`
	SuccessChunks     int64  `json:"success_chunks"`
	FailedChunks      int64  `json:"failed_chunks"`
	RunningChunks     int64  `json:"running_chunks"`
	WaitingChunks     int64  `json:"waiting_chunks"`
	MigrationResult   string `json:"migration_result"`
	MigrationDuration string `json:"migration_duration"`
	MigrationMessage  string `json:"migration_message"`

	IsCSVImportFinished     bool    `json:"is_csv_import_finished"`
	IsCSVExportFinished     bool    `json:"is_csv_export_finished"`
	LightningImportProgress float64 `json:"lightning_import_progress"`
	Result                  string  `json:"result"`
}

type CSVFileInfo struct {
	FileName string `json:"file_name"`
	FilePath string `json:"file_path"`
	FileSize int64  `json:"file_size"`

	IsValid       bool              `json:"is_valid"`
	ErrMessage    string            `json:"err_message"`
	TableProperty *CSVTableProperty `json:"table_property,omitempty"`
}

type CSVTableProperty struct {
	SchemaNameT string `json:"schema_name_t"`
	TableNameT  string `json:"table_name_t"`

	CSVFileNum int    `json:"csv_file_num"`
	IsValid    bool   `json:"is_valid"`
	ErrMessage string `json:"err_message"`
}

func (i CSVTableProperty) String() string {
	return "SchemaNameT: " + i.SchemaNameT + ", TableNameT: " + i.TableNameT
}

func (i CSVTableProperty) GetSchemaNameT() string {
	return i.SchemaNameT
}

func (i CSVTableProperty) GetTableNameT() string {
	return i.TableNameT
}

func (i CSVTableProperty) GetCSVFileNum() int64 {
	return int64(i.CSVFileNum)
}

type WalkCSVDirResp struct {
	WalkPaths []string `json:"walk_paths"`

	CSVFiles        []CSVFileInfo      `json:"csv_files"`
	Tables          []CSVTableProperty `json:"tables"`
	TotalCSVFileNum int64              `json:"total_csv_file_num"`
	ValidCSVFileNum int64              `json:"valid_csv_file_num"`
	ErrorCSVFileNum int64              `json:"error_csv_file_num"`
	TotalTableNum   int64              `json:"total_table_num"`
}

type SaveMigrationDataResp struct {
	SavedChannelSchemaNum      int `json:"saved_channel_schema_num"`
	SavedChannelSchemaTableNum int `json:"saved_channel_schema_table_num"`
	SavedMigrationDetailNum    int `json:"saved_migration_detail_num"`
	SavedMigrationSummaryNum   int `json:"saved_migration_summary_num"`
	SavedTableNum              int `json:"saved_table_num"`
}

type QueryMigrationDetailsResp struct {
	Data []*CSVTableMigrationDetail `json:"data"`
}

type UpdateMigrationDetailResp struct {
}

type SchemaTableWithCSV struct {
	SchemaNameT string `json:"schema_name_t"`
	TableNameT  string `json:"table_name_t"`
	CSVFileNum  int64  `json:"csv_file_num"`
}

type MigrationDataStatisticResp struct {
	Data []*SchemaTableWithCSV `json:"data"`
}

type CSVTableMigrationDetail struct {
	ID           uint   `json:"id"`
	TaskID       int    `json:"task_id"`
	SchemaNameS  string `json:"schema_name_s"`
	TableNameS   string `json:"table_name_s"`
	SchemaNameT  string `json:"schema_name_t"`
	TableNameT   string `json:"table_name_t"`
	TaskStatus   string `json:"task_status"`
	ImportStatus string `json:"import_status"`
	ImportError  string `json:"import_error"`

	FileName  string    `json:"file_name"`
	FilePath  string    `json:"file_path"`
	FileSize  int64     `json:"file_size"`
	CreatedAt time.Time `gorm:"<-:create"`
}
