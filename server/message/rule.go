package message

type RuleBaseFields struct {
	DbTypeS string `json:"dbTypeS" validate:"required,oneof=oracle mysql" enums:"oracle,mysql"`
	DbTypeT string `json:"dbTypeT" validate:"required,oneof=tidb" enums:"tidb"`
	*BaseFields
}

type ObjSqlBaseFields struct {
	*RuleBaseFields
	IsCompatibility string `json:"isCompatibility" validate:"required,oneof=Y N" enums:"Y,N"`
	IsConvertible   string `json:"isConvertible" validate:"required,oneof=Y N" enums:"Y,N"`
}

// TabColMapRule table column map rule
type TabColMapRule struct {
	TabColMapRuleId int `json:"tabColMapRuleId"`
	*RuleBaseFields
	ColTypeNameS string `json:"colTypeNameS" validate:"required,min=1,max=100"`
	ColTypeNameT string `json:"colTypeNameT" validate:"required,min=1,max=100"`
	IsEquivalent string `json:"isEquivalent" validate:"required,oneof=Y N" enums:"Y,N"`
}

type CreateOrUpdateTabColMapRuleReq struct {
	TmplateId int `json:"templateId"`
	*TabColMapRule
}
type CreateOrUpdateTabColMapRuleResp struct {
}

type DeleteTabColMapRuleByIdReq struct {
	TmplateId       int `json:"templateId"`
	TabColMapRuleId int `json:"tabColMapRuleId"`
}
type DeleteTabColMapRuleByIdResp struct {
}

type GetTabColMapRuleReq struct {
	TabColMapRuleId int `json:"tabColMapRuleId"`
}
type GetTabColMapRuleResp struct {
	*TabColMapRule
}

type ListTabColMapRulesReq struct {
	TmplateId int `json:"templateId" validate:"required"`
	PageRequest
}
type ListTabColMapRulesResp struct {
	TmplateId      int `json:"templateId"`
	TabColMapRules []*TabColMapRule
}

// ObjMapRule object map rule
type ObjMapRule struct {
	ObjMapRuleId   int    `json:"objMapRuleId"`
	ObjectType     string `json:"objectType" validate:"required,min=1,max=100"`
	ObjectTypeName string `json:"objectTypeName" validate:"required,min=1,max=100"`
	*ObjSqlBaseFields
}

type CreateOrUpdateObjMapRuleReq struct {
	TmplateId int `json:"templateId"`
	*ObjMapRule
}
type CreateOrUpdateObjMapRuleResp struct {
}

type DeleteObjMapRuleByIdReq struct {
	TmplateId    int `json:"templateId"`
	ObjMapRuleId int `json:"objMapRuleId"`
}
type DeleteObjMapRuleByIdResp struct {
}

type GetObjMapRuleByIdReq struct {
	ObjMapRuleId int `json:"objMapRuleId"`
}
type GetObjMapRuleByIdResp struct {
	*ObjMapRule
}

type ListObjMapRulesReq struct {
	TmplateId int `json:"templateId" validate:"required"`
	PageRequest
}
type ListObjMapRulesResp struct {
	TmplateId   int `json:"templateId"`
	ObjMapRules []*ObjMapRule
}

// SqlMapRule sql map rule
type SqlMapRule struct {
	SqlMapRuleId int `json:"sqlMapRuleId"`
	*ObjSqlBaseFields
	Keywords string `json:"keywords" validate:"required,min=1,max=20"`
}

type CreateOrUpdateSqlMapRuleReq struct {
	TmplateId int `json:"templateId"`
	*SqlMapRule
}
type CreateOrUpdateSqlMapRuleResp struct {
}

type DeleteSqlMapRuleByIdReq struct {
	TmplateId    int `json:"templateId"`
	SqlMapRuleId int `json:"sqlMapRuleId"`
}
type DeleteSqlMapRuleByIdResp struct {
}

type GetSqlMapRuleByIdReq struct {
	SqlMapRuleId int `json:"sqlMapRuleId"`
}
type GetSqlMapRuleByIdResp struct {
	*SqlMapRule
}

type ListSqlMapRulesReq struct {
	TmplateId int `json:"templateId" validate:"required"`
	PageRequest
}
type ListSqlMapRulesResp struct {
	TmplateId   int `json:"templateId"`
	SqlMapRules []*SqlMapRule
}

type ColDefaultMapRule struct {
	ColDefaultMapRuleId  int    `json:"colDefaultMapRuleId"`
	ColTypeDefaultValueS string `json:"colTypeDefaultValueS" validate:"min=0,max=100"`
	ColTypeDefaultValueT string `json:"colTypeDefaultValueT" validate:"min=0,max=100"`
	IsEquivalent         string `json:"isEquivalent" validate:"required,oneof=Y N" enums:"Y,N"`
	*RuleBaseFields
}
type CreateOrUpdateColDefaultMapRuleReq struct {
	TmplateId int `json:"templateId"`
	*ColDefaultMapRule
}
type CreateOrUpdateColDefaultMapRuleResp struct {
}

type DeleteColDefaultMapRuleByIdReq struct {
	TmplateId           int `json:"templateId"`
	ColDefaultMapRuleId int `json:"colDefaultMapRuleId"`
}
type DeleteColDefaultMapRuleByIdResp struct {
}

type GetColDefaultMapRuleByIdReq struct {
	ColDefaultMapRuleId int `json:"colDefaultMapRuleId" validate:"required"`
}
type GetColDefaultMapRuleByIdResp struct {
	*ColDefaultMapRule
}

type ListColDefaultMapRulesReq struct {
	TmplateId int `json:"templateId" validate:"required"`
	PageRequest
}
type ListColDefaultMapRulesResp struct {
	TmplateId          int `json:"templateId"`
	ColDefaultMapRules []*ColDefaultMapRule
}
