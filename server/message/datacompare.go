package message

import (
	"time"
)

type DataCompareReExecutionReq struct {
	ChannelId int
	TaskId    int
	Ids       []int
}
type DataCompareReExecutionResp struct {
}

type DataCompareSummaryDetail struct {
	TaskId          int       `json:"taskId"`
	Schema          string    `json:"schema"`
	TotalNums       int       `json:"totalNums"`
	SuccessNums     int       `json:"successNums"`
	FailedNums      int       `json:"failedNums"`
	RunningNums     int       `json:"runningNums"`
	WaitingNums     int       `json:"waitingNums"`
	SuccessRatio    string    `json:"successRatio"`
	CompareDuration string    `json:"compareDuration"`
	StartTime       time.Time `json:"StartTime"`
	EndTime         time.Time `json:"endTime"`
}

type DataCompareSchemaReq struct {
	TaskId int    `json:"taskId"`
	Schema string `json:"schema"`
}

type DataCompareSchemaResp struct {
	TaskId          int       `json:"taskId"`
	Schema          string    `json:"schema"`
	TotalNums       int       `json:"totalNums"`
	SuccessNums     int       `json:"successNums"`
	FailedNums      int       `json:"failedNums"`
	RunningNums     int       `json:"runningNums"`
	WaitingNums     int       `json:"waitingNums"`
	SuccessRatio    string    `json:"successRatio"`
	CompareDuration string    `json:"compareDuration"`
	StartTime       time.Time `json:"StartTime"`
	EndTime         time.Time `json:"endTime"`
}

type DataCompareSchemaStateReq struct {
	TaskId   int    `json:"taskId"`
	Schema   string `json:"schema"`
	State    string `json:"state" enums:"success,failed,error,checking,not_checked,ignore,invalid"`
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
}

type DataCompareSchemaStateResp struct {
	TaskId      int                 `json:"taskId"`
	Schema      string              `json:"schema"`
	State       string              `json:"state"`
	TableCount  int                 `json:"tablecount"`
	TableDetail []*SummaryWithError `json:"tableDetail"`
}

type Summary struct {
	ChannelId         int       `json:"channelId"`
	TaskId            int       `json:"taskId"`
	Schema            string    `json:"schema"`
	Table             string    `json:"table"`
	ChunkSize         int       `json:"chunkSize"`
	ChunkNum          int       `json:"chunkNum"`
	CheckSuccessNum   int       `json:"checkSuccessNum"`
	CheckFailedNum    int       `json:"checkFailedNum"`
	CheckIgnoreNum    int       `json:"checkIgnoreNum"`
	CheckWaitingNum   int       `json:"checkWaitingNum"`
	State             string    `json:"state"`
	Duration          string    `json:"duration"`
	ConfigHash        string    `json:"configHash"`
	StartTime         time.Time `json:"startTime"`
	UpdateTime        time.Time `json:"updateTime"`
	ChannelSchtableId int       `json:"channelSchtableId"`
}

type SummaryWithError struct {
	ChannelId         int       `json:"channelId"`
	TaskId            int       `json:"taskId"`
	Schema            string    `json:"schema"`
	Table             string    `json:"table"`
	ChunkSize         int       `json:"chunkSize"`
	ChunkNum          int       `json:"chunkNum"`
	CheckSuccessNum   int       `json:"checkSuccessNum"`
	CheckFailedNum    int       `json:"checkFailedNum"`
	CheckIgnoreNum    int       `json:"checkIgnoreNum"`
	CheckWaitingNum   int       `json:"checkWaitingNum"`
	State             string    `json:"state"`
	Duration          string    `json:"duration"`
	ConfigHash        string    `json:"configHash"`
	StartTime         time.Time `json:"startTime"`
	UpdateTime        time.Time `json:"updateTime"`
	ChannelSchtableId int       `json:"channelSchtableId"`
	Message           string    `json:"message"`
}
type DataCompareTableChunkReq struct {
	TaskId int    `json:"taskId"`
	Schema string `json:"schema"`
	Table  string `json:"table"`
	State  string `json:"state"`
}

type DataCompareTableChunkResp struct {
	TaskId      int      `json:"taskId"`
	Schema      string   `json:"schema"`
	Table       string   `json:"table"`
	State       string   `json:"state"`
	ChunkCount  int      `json:"chunkCount"`
	ChunkDetail []*Chunk `json:"chunkDetail"`
}

type Chunk struct {
	ChunkId     int       `json:"chunkId"`
	InstanceId  string    `json:"instanceId"`
	Schema      string    `json:"schema"`
	Table       string    `json:"table"`
	Range       string    `json:"range"`
	Checksum    string    `json:"checksum"`
	ChunkStr    string    `json:"chunkStr"`
	State       string    `json:"state"`
	UpdateTime  time.Time `json:"updateTime"`
	SourceTime  string    `json:"sourceTime"`
	TargetTime  string    `json:"targetTime"`
	SourceCount int       `json:"sourceCount"`
	TargetCount int       `json:"targetCount"`
	ChannelId   int       `json:"channelId"`
	TaskId      int       `json:"taskId"`
	Message     string    `json:"message"`
}

type EnvDeployTask struct {
	DataCompareEnvDeployId int       `json:"dataCompareEnvDeployId"`
	TaskNumber             int       `json:"taskNumber"`
	TaskName               string    `json:"taskName"`
	IsIgnore               string    `json:"isIgnore"`
	TaskStatus             int       `json:"taskStatus"`
	TaskLog                string    `json:"taskLog"`
	TaskSQL                string    `json:"taskSQL"`
	LastRunTime            time.Time `json:"lastRunTime"`
	TaskId                 int       `json:"taskId"`
	Comment                string    `json:"comment"`
	SQLModel               string    `json:"SQLModel"`
}

type ExecuteDataCompareEnvDeployTaskReq struct {
	TaskId       int   `json:"-"`
	EnvDeployIds []int `json:"envDeployIds"`
	RunMode      int   `json:"runMode"`
}

type ExecuteDataCompareEnvDeployTaskResp struct {
}

type ListDataCompareEnvDeployTaskReq struct {
	TaskId int `json:"-"`
}

type ListDataCompareEnvDeployTaskResp struct {
	Tasks []EnvDeployTask `json:"tasks"`
}

type UpdateDataCompareEnvDeployTaskReq struct {
	EnvDeployId int    `json:"envDeployId"`
	TaskSQL     string `json:"taskSQL"`
	TaskId      int    `json:"taskId"`
	Comment     string `json:"comment"`
}

type UpdateDataCompareEnvDeployTaskResp struct {
}

type DownloadFixSQLReq struct {
	TaskId      int               `json:"task_id"`
	SchemaNameS []string          `json:"schema_name_s"`
	TableName   string            `json:"table_name"`
	FilePath    *string           `json:"file_path,omitempty"` // 可选：指定单个文件路径进行过滤下载
	Filter      *SQLContentFilter `json:"filter,omitempty"`    // 可选：内容过滤条件
}

type DownloadFixSQLResp struct {
	ExportFilePath string `json:"exportFilePath"`
}

func (i DownloadFixSQLResp) GetFilePath() string {
	return i.ExportFilePath
}

type ListFixSQLReq struct {
	TaskId      int      `json:"task_id"`       // 任务ID
	SchemaNameS []string `json:"schema_name_s"` // 过滤的Schema列表
	TableName   string   `json:"table_name"`    // 过滤的表名
	Page        int      `json:"page"`          // 页码
	PageSize    int      `json:"pageSize"`      // 每页大小
}

type ListFixSQLResp struct {
	TaskId   int              `json:"task_id"`
	SqlFiles []FixSQLFileInfo `json:"sql_files"`
}

type FixSQLFileInfo struct {
	Schema   string    `json:"schema"`    // Schema名称
	Table    string    `json:"table"`     // 表名
	FileName string    `json:"filename"`  // 文件名
	FileSize int64     `json:"file_size"` // 文件大小
	ModTime  time.Time `json:"mod_time"`  // 修改时间
	FilePath string    `json:"file_path"` // 文件路径
}

type SQLContentFilter struct {
	SQLType       []string `json:"sql_types"`      // ["INSERT", "DELETE", "UPDATE"]
	SearchText    string   `json:"search_text"`    // 搜索关键字
	CaseSensitive bool     `json:"case_sensitive"` // 是否大小写敏感
}

type SQLLine struct {
	LineNumber int    `json:"line_number"`
	Content    string `json:"content"`
	SQLType    string `json:"sql_type"`   // INSERT/DELETE/UPDATE/COMMENT/OTHER
	TableInfo  string `json:"table_info"` // 提取的表信息
}

type SQLSummary struct {
	TotalLines int            `json:"total_lines"`
	SQLCount   map[string]int `json:"sql_count"` // 各类SQL语句数量
	HasMore    bool           `json:"has_more"`  // 是否还有更多内容
}

// GetFixSQLContent 统一的Fix SQL内容获取API相关结构体
type GetFixSQLContentReq struct {
	TaskId   int               `json:"task_id" binding:"required"`
	FilePath string            `json:"file_path" binding:"required"` // 相对路径，如: SCHEMA.TABLE.sql 或 subdir/SCHEMA.TABLE.sql
	Page     int               `json:"page"`                         // 页码（默认1）
	PageSize int               `json:"page_size"`                    // 每页行数（默认1000，最大10000）
	Filter   *SQLContentFilter `json:"filter"`                       // 可选的内容过滤器
}

type GetFixSQLContentResp struct {
	TaskId   int         `json:"task_id"`
	FilePath string      `json:"file_path"`
	FileSize int64       `json:"file_size"`
	Lines    []SQLLine   `json:"lines"`   // 当前页的内容
	Summary  *SQLSummary `json:"summary"` // 文件统计摘要
}
