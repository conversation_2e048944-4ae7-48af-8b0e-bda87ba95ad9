package message

import "gitee.com/pingcap_enterprise/tms/pkg/mysql/dm"

type Leader dm.LeaderInfo
type Master dm.MasterInfo
type Worker dm.WorkerInfo

// ListMembersReq 列出成员请求结构体
type ListMembersReq struct {
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

// ListMembersResp 列出成员响应结构体
type ListMembersResp struct {
	Leader  *Leader   `json:"leader"`
	Masters []*Master `json:"masters"`
	Workers []*Worker `json:"workers"`
	Msg     string    `json:"msg"`
	Result  bool      `json:"result"`
}

// ListSourcesReq 列出数据源请求结构体
type ListSourcesReq struct {
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

// PreviewSourcesReq 根据ChannelId对数据源进行预览，输出为DM格式
type PreviewSourcesReq struct {
	ChannelId int `json:"channelId" form:"channelId" validate:"required"`
}
type PreviewConfigurationReq struct {
	ChannelId  int    `json:"channelId" form:"channelId" validate:"required"`
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

type PreviewSourcesResp struct {
	Configs []CustomSourceConfigDTO `json:"configs"`
}

type PreviewConfigurationResp struct {
	Config TaskConfigDTO `json:"config"`
}

// ListSourcesResp 列出数据源响应结构体
type ListSourcesResp struct {
	Sources []*Source `json:"sources"`
	Msg     string    `json:"msg"`
	Result  bool      `json:"result"`
}

// Source 数据源信息结构体
type Source struct {
	Source string                 `json:"source"`
	Worker string                 `json:"worker"`
	Result bool                   `json:"result"`
	Msg    string                 `json:"msg"`
	Config *CustomSourceConfigDTO `json:"config,omitempty"`
}

// CustomSourceConfigDTO 自定义数据源配置 DTO 结构，增加密码加密字段
type CustomSourceConfigDTO struct {
	SourceID string `json:"source_id"`

	EnableGTID bool `json:"enable_gtid"`

	EnableRelay     bool   `json:"enable_relay"`
	RelayBinlogName string `json:"relay_binlog_name"`
	RelayBinlogGTID string `json:"relay_binlog_gtid"`

	From CustomDatabaseConfig `json:"from"`

	Purge *dm.PurgeConfig `json:"purge,omitempty"`

	Checker *dm.CheckerConfig `json:"checker,omitempty"`

	CaseSensitive bool             `json:"case_sensitive"`
	Filters       []dm.EventFilter `json:"filters,omitempty"`

	RelayDir string `json:"relay_dir"`
}

// CustomDatabaseConfig 自定义数据库连接配置，增加密码加密字段
type CustomDatabaseConfig struct {
	Host            string             `json:"host"`
	Port            int                `json:"port"`
	User            string             `json:"user"`
	Password        string             `json:"password"`
	PasswordEncrypt string             `json:"password_encrypt"`
	Security        *dm.SecurityConfig `json:"security,omitempty"`
}

// PreviewAllReq 预览所有配置请求结构体
type PreviewAllReq struct {
	ChannelId  int    `json:"channelId" form:"channelId" validate:"required"`
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"`
}

// PreviewAllResp 预览所有配置响应结构体
type PreviewAllResp struct {
	Tree *DMConfigTreeNode `json:"tree"`
}

// DMConfigTreeNode DM配置树节点结构体
type DMConfigTreeNode struct {
	Key      string              `json:"key"`                // 唯一标识符
	Title    string              `json:"title"`              // 显示名称（文件名或节点名）
	Context  string              `json:"context,omitempty"`  // YAML格式的配置内容
	Children []*DMConfigTreeNode `json:"children,omitempty"` // 子节点
	IsLeaf   bool                `json:"isLeaf"`             // 是否为叶子节点
}

// TriggerCheckReq 触发检查请求结构体
type TriggerCheckReq struct {
	ChannelId  int    `json:"channelId" form:"channelId" validate:"required"`   // 通道ID
	MasterAddr string `json:"masterAddr" form:"masterAddr" validate:"required"` // DM Master地址
}

// TriggerCheckResp 触发检查响应结构体
type TriggerCheckResp struct {
	RequestID string `json:"requestId"` // 检查请求唯一标识符
}

// GetCheckStatusReq 获取检查状态请求结构体
type GetCheckStatusReq struct {
	RequestID string `json:"requestId" form:"requestId" validate:"required"` // 检查请求唯一标识符
}

// CheckItem 检查项结构体
type CheckItem struct {
	Name       string            `json:"name"`       // 检查项名称
	Status     string            `json:"status"`     // 检查状态：pending, running, success, failed
	Progress   int               `json:"progress"`   // 进度百分比 (0-100)
	Details    []CheckItemDetail `json:"details"`    // 检查详情
	Suggestion string            `json:"suggestion"` // 修复建议
	ErrorMsg   string            `json:"errorMsg"`   // 错误信息
}

// CheckItemDetail 检查项详情结构体
type CheckItemDetail struct {
	Type       string      `json:"type"`       // 详情类型：info, warning, error
	Message    string      `json:"message"`    // 详情消息
	Data       interface{} `json:"data"`       // 相关数据
	Suggestion string      `json:"suggestion"` // 修复建议
}

// GetCheckStatusResp 获取检查状态响应结构体
type GetCheckStatusResp struct {
	RequestID       string      `json:"requestId"`       // 检查请求唯一标识符
	OverallStatus   string      `json:"overallStatus"`   // 整体状态：pending, running, completed, failed
	OverallProgress int         `json:"overallProgress"` // 整体进度百分比 (0-100)
	CheckItems      []CheckItem `json:"checkItems"`      // 检查项列表
	StartTime       string      `json:"startTime"`       // 开始时间
	EndTime         string      `json:"endTime"`         // 结束时间（如果已完成）
}

// TableInfo 表信息结构体，用于检查表一致性
type TableInfo struct {
	SchemaName string   `json:"schemaName"` // 数据库名
	TableName  string   `json:"tableName"`  // 表名
	Columns    []string `json:"columns"`    // 列名列表
}

// WorkerBindingInfo Worker绑定信息结构体
type WorkerBindingInfo struct {
	WorkerName string `json:"workerName"` // Worker名称
	SourceName string `json:"sourceName"` // 绑定的数据源名称（空表示未绑定）
	Status     string `json:"status"`     // 绑定状态：bound, idle
}

// SourcePermissionInfo 数据源权限信息结构体
type SourcePermissionInfo struct {
	SourceName        string   `json:"sourceName"`        // 数据源名称
	HasPermission     bool     `json:"hasPermission"`     // 是否有足够权限
	MissingPrivileges []string `json:"missingPrivileges"` // 缺失的权限列表
}
