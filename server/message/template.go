package message

type TemplateInfo struct {
	TemplateId   int    `json:"templateId"`
	TemplateName string `json:"templateName" validate:"required,min=1,max=100"`
	TemplateType string `json:"templateType" validate:"required,oneof=object_map sql_map col_default_map tab_col_map" enums:"object_map,sql_map,col_default_map,tab_col_map"`
	DefaultTag   string `json:"defaultTag" validate:"required,oneof=Y N" enums:"Y,N"`
	*BaseFields
}

type CreateOrUpdateTemplateInfoReq struct {
	*TemplateInfo
}

type CreateOrUpdateTemplateInfoResp struct {
}

type BatchDeleteTemplateInfosReq struct {
	TemplateIds []int `json:"templateIds"`
}

type BatchDeleteTemplateInfosResp struct {
}

type DetailTemplateInfoReq struct {
	TemplateId int `json:"templateId"`
}

type DetailTemplateInfoResp struct {
	*TemplateInfo
}

type ListTemplatesReq struct {
	TemplateType string `json:"templateType" validate:"required,oneof=object_map sql_map col_default_map tab_col_map" enums:"object_map,sql_map,col_default_map,tab_col_map"`
	PageRequest
}

type ListTemplatesResp struct {
	Templates []*TemplateInfo
}

type TemplateRule struct {
	TemplateRuleId int `json:"templateRuleId"`
	TemplateId     int `json:"templateId" validate:"required"`
	//MapRuleType    string `json:"mapRuleType" validate:"required,max=20" enums:"column,object,sql"`
	MapRuleId int `json:"mapRuleId" validate:"required"`
	*BaseFields
}

type AddTemplateRulesReq struct {
	TemplateRules []*TemplateRule
}
type AddTemplateRulesResp struct {
}

type DeleteTemplateRulesReq struct {
	TemplateRuleIds []int `json:"templateRuleIds"`
}
type DeleteTemplateRulesResp struct {
}

type ListTemplateRulesByTemplateIdReq struct {
	TemplateId int `json:"templateRuleIds"`
	PageRequest
}
type ListTemplateRulesByTemplateIdResp struct {
	TemplateRules []*TemplateRule
}
