package message

import "reflect"

type AssessTaskInfo struct {
	TaskId          string
	TaskName        string
	ChannelId       string
	ChannelName     string
	ChannelType     string
	ObjAssessment   string
	DatasourceIdS   string
	DatasourceNameS string
	DatasourceIdT   string
	DatasourceNameT string
}

type DataSourceInfo struct {
	DataSourceID     int
	DataSourceName   string
	DbType           string
	DbName           string
	Host             string
	Port             int
	Username         string
	Password         string
	ServiceName      string
	ConnectionStatus string
}

type ObjectCompatibleOverview struct {
	DBName        string `json:"dbName"`
	AssessTotal   string `json:"assessTotal"`
	Compatible    string `json:"compatible"`
	InCompatible  string `json:"inCompatible"`
	Convertible   string `json:"convertible"`
	InConvertible string `json:"inConvertible"`
}

type ObjectCompatibleByObject struct {
	DBName        string `json:"dbName"`
	AssessName    string `json:"assessName"`
	AssessTotal   string `json:"assessTotal"`
	Compatible    string `json:"compatible"`
	InCompatible  string `json:"inCompatible"`
	Convertible   string `json:"convertible"`
	InConvertible string `json:"inConvertible"`
}

type ObjectCompatibleBySchema struct {
	DBName        string `json:"dbName"`
	Schema        string `json:"schema"`
	AssessTotal   string `json:"assessTotal"`
	Compatible    string `json:"compatible"`
	InCompatible  string `json:"inCompatible"`
	Convertible   string `json:"convertible"`
	InConvertible string `json:"inConvertible"`
}

type ObjectCompatibleBySchemaObject struct {
	DBName        string `json:"dbName"`
	Schema        string `json:"schema"`
	AssessType    string `json:"assessType"`
	AssessName    string `json:"assessName"`
	AssessTotal   string `json:"assessTotal"`
	Compatible    string `json:"compatible"`
	InCompatible  string `json:"inCompatible"`
	Convertible   string `json:"convertible"`
	InConvertible string `json:"inConvertible"`
}

type OracleOverview struct {
	CheckTime         string `json:"check_time"`
	ReportName        string `json:"report_name"`
	ReportUser        string `json:"report_user"`
	HostName          string `json:"host_name"`
	PlatformName      string `json:"platform_name"`
	DBName            string `json:"db_name"`
	DBVersion         string `json:"db_version"`
	GlobalDBName      string `json:"global_db_name"`
	ClusterDB         string `json:"cluster_db"`
	ClusterDBInstance string `json:"cluster_db_instance"`
	InstanceName      string `json:"instance_name"`
	InstanceNumber    string `json:"instance_number"`
	ThreadNumber      string `json:"thread_number"`
	BlockSize         string `json:"block_size"`
	TotalUsedSize     string `json:"total_used_size"`
	HostCPUS          string `json:"host_cpus"`
	HostMem           string `json:"host_mem"`
	CharacterSet      string `json:"character_set"`
}

type ReportOracleOverview struct {
	ReportName        string `json:"report_name"`
	ReportUser        string `json:"report_user"`
	HostName          string `json:"host_name"`
	PlatformName      string `json:"platform_name"`
	DBName            string `json:"db_name"`
	GlobalDBName      string `json:"global_db_name"`
	ClusterDB         string `json:"cluster_db"`
	ClusterDBInstance string `json:"cluster_db_instance"`
	InstanceName      string `json:"instance_name"`
	InstanceNumber    string `json:"instance_number"`
	ThreadNumber      string `json:"thread_number"`
	BlockSize         string `json:"block_size"`
	TotalUsedSize     string `json:"total_used_size"`
	HostCPUS          string `json:"host_cpus"`
	HostMem           string `json:"host_mem"`
	CharacterSet      string `json:"character_set"`
}

type ReportSummary struct {
	AssessType    string `json:"assess_type"`
	AssessName    string `json:"assess_name"`
	AssessTotal   int    `json:"assess_total"`
	Compatible    int    `json:"compatible"`
	Incompatible  int    `json:"incompatible"`
	Convertible   int    `json:"convertible"`
	InConvertible int    `json:"inconvertible"`
}

type SchemaTableTypeCompatibles struct {
	Schema        string `json:"schema"`
	TableType     string `json:"table_type"`
	ObjectCounts  string `json:"object_counts"`
	ObjectSize    string `json:"object_size"`
	IsCompatible  string `json:"is_compatible"`
	IsConvertible string `json:"is_convertible"`
}

type SchemaColumnTypeCompatibles struct {
	Schema        string `json:"schema"`
	ColumnType    string `json:"column_type"`
	ObjectCounts  string `json:"object_counts"`
	MaxDataLength string `json:"max_data_length"`
	ColumnTypeMap string `json:"column_type_map"`
	IsEquivalent  string `json:"is_equivalent"`
}

type SchemaConstraintTypeCompatibles struct {
	Schema         string `json:"schema"`
	ConstraintType string `json:"constraint_type"`
	ObjectCounts   string `json:"object_counts"`
	IsCompatible   string `json:"is_compatible"`
	IsConvertible  string `json:"is_convertible"`
}

type SchemaIndexTypeCompatibles struct {
	Schema        string `json:"schema"`
	IndexType     string `json:"index_type"`
	ObjectCounts  string `json:"object_counts"`
	IsCompatible  string `json:"is_compatible"`
	IsConvertible string `json:"is_convertible"`
}

type SchemaSequenceCompatibles struct {
	Schema        string `json:"schema"`
	ObjectCounts  string `json:"object_counts"`
	IsCompatible  string `json:"is_compatible"`
	IsConvertible string `json:"is_convertible"`
}

type SchemaDefaultValueCompatibles struct {
	Schema             string `json:"schema"`
	ColumnDefaultValue string `json:"column_default_value"`
	ObjectCounts       string `json:"object_counts"`
	DefaultValueMap    string `json:"default_value_map"`
	IsCompatible       string `json:"is_compatible"`
	IsConvertible      string `json:"is_convertible"`
}

type SchemaViewTypeCompatibles struct {
	Schema        string `json:"schema"`
	ViewType      string `json:"view_type"`
	ViewTypeOwner string `json:"view_type_owner"`
	ObjectCounts  string `json:"object_counts"`
	IsCompatible  string `json:"is_compatible"`
	IsConvertible string `json:"is_convertible"`
}

type SchemaObjectTypeCompatibles struct {
	Schema        string `json:"schema"`
	ObjectType    string `json:"object_type"`
	ObjectCounts  string `json:"object_counts"`
	IsCompatible  string `json:"is_compatible"`
	IsConvertible string `json:"is_convertible"`
}

type SchemaPartitionTypeCompatibles struct {
	Schema        string `json:"schema"`
	PartitionType string `json:"partition_type"`
	ObjectCounts  string `json:"object_counts"`
	IsCompatible  string `json:"is_compatible"`
	IsConvertible string `json:"is_convertible"`
}

type SchemaSubPartitionTypeCompatibles struct {
	Schema           string `json:"schema"`
	SubPartitionType string `json:"sub_partition_type"`
	ObjectCounts     string `json:"object_counts"`
	IsCompatible     string `json:"is_compatible"`
	IsConvertible    string `json:"is_convertible"`
}

type SchemaTemporaryTableTypeCompatibles struct {
	Schema             string `json:"schema"`
	TemporaryTableType string `json:"temporary_table_type"`
	ObjectCounts       string `json:"object_counts"`
	IsCompatible       string `json:"is_compatible"`
	IsConvertible      string `json:"is_convertible"`
}

type SchemaPartitionTableCountsCheck struct {
	Schema          string `json:"schema"`
	TableName       string `json:"table_name"`
	PartitionCounts string `json:"partition_counts"`
}

type SchemaTableRowLengthCheck struct {
	Schema       string `json:"schema"`
	TableName    string `json:"table_name"`
	AvgRowLength string `json:"avg_row_length"`
}

type SchemaTableIndexRowLengthCheck struct {
	Schema       string `json:"schema"`
	TableName    string `json:"table_name"`
	IndexName    string `json:"index_name"`
	ColumnLength string `json:"column_length"`
}

type SchemaTableColumnCountsCheck struct {
	Schema       string `json:"schema"`
	TableName    string `json:"table_name"`
	ColumnCounts string `json:"column_counts"`
}

type SchemaTableIndexCountsCheck struct {
	Schema      string `json:"schema"`
	TableName   string `json:"table_name"`
	IndexCounts string `json:"index_counts"`
}

type UsernameLengthCheck struct {
	Schema        string `json:"schema"`
	AccountStatus string `json:"account_status"`
	Created       string `json:"created"`
	Length        string `json:"length"`
}

type SchemaTableNameLengthCheck struct {
	Schema    string `json:"schema"`
	TableName string `json:"table_name"`
	Length    string `json:"length"`
}

type SchemaTableColumnNameLengthCheck struct {
	Schema     string `json:"schema"`
	TableName  string `json:"table_name"`
	ColumnName string `json:"column_name"`
	Length     string `json:"length"`
}

type SchemaTableIndexNameLengthCheck struct {
	Schema    string `json:"schema"`
	TableName string `json:"table_name"`
	IndexName string `json:"index_name"`
	Length    string `json:"length"`
}

type SchemaViewNameLengthCheck struct {
	Schema   string `json:"schema"`
	ViewName string `json:"view_name"`
	ReadOnly string `json:"read_only"`
	Length   string `json:"length"`
}

type SchemaSequenceNameLengthCheck struct {
	Schema       string `json:"schema"`
	SequenceName string `json:"sequence_name"`
	OrderFlag    string `json:"order_flag"`
	Length       string `json:"length"`
}

type SchemaActiveSession struct {
	Rownum         string `json:"rownum"`
	DBID           string `json:"dbid"`
	InstanceNumber string `json:"instance_number"`
	SampleID       string `json:"sample_id"`
	SampleTime     string `json:"sample_time"`
	SessionCounts  string `json:"session_counts"`
}

type SchemaTableSizeData struct {
	Schema        string `json:"schema"`
	TableSize     string `json:"table_size"`
	IndexSize     string `json:"index_size"`
	LobTableSize  string `json:"lob_table_size"`
	LobIndexSize  string `json:"lob_index_size"`
	AllTablesRows string `json:"all_tables_rows"`
}

type SchemaTableRowsTOP struct {
	Schema    string `json:"schema"`
	TableName string `json:"table_name"`
	TableType string `json:"table_type"`
	TableSize string `json:"table_size"`
}

type SchemaCodeObject struct {
	Schema     string `json:"schema"`
	ObjectName string `json:"object_name"`
	ObjectType string `json:"object_type"`
	Lines      string `json:"lines"`
}

type SchemaSynonymObject struct {
	Schema      string `json:"schema"`
	SynonymName string `json:"synonym_name"`
	TableOwner  string `json:"table_owner"`
	TableName   string `json:"table_name"`
}

type SchemaMaterializedViewObject struct {
	Schema            string `json:"schema"`
	MviewName         string `json:"mview_name"`
	RewriteCapability string `json:"rewrite_capability"`
	RefreshMode       string `json:"refresh_mode"`
	RefreshMethod     string `json:"refresh_method"`
	FastRefreshable   string `json:"fast_refreshable"`
}

type SchemaTableAvgRowLengthTOP struct {
	Schema       string `json:"schema"`
	TableName    string `json:"table_name"`
	AvgRowLength string `json:"avg_row_length"`
}

type SchemaTableNumberTypeEqual0 struct {
	Schema        string `json:"schema"`
	TableName     string `json:"table_name"`
	ColumnName    string `json:"column_name"`
	DataPrecision string `json:"data_precision"`
	DataScale     string `json:"data_scale"`
}

type OraDbaConstraint struct {
	OraconstraintId int    `json:"oraconstraintId"`
	ChannelId       int    `json:"channelId"`
	TaskId          int    `json:"taskId"`
	Owner           string `json:"owner"`
	TableName       string `json:"tableName"`
	ConstraintName  string `json:"constraintName"`
	ConstraintType  string `json:"constraintType"`
	IsCompatible    string `json:"isCompatible"`
	IsConvertible   string `json:"isConvertible"`
}

type OraDbaIndex struct {
	OraIndexId    int    `json:"oraIndexId"`
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	TableOwner    string `json:"tableOwner"`
	TableName     string `json:"tableName"`
	IndexName     string `json:"indexName"`
	IndexType     string `json:"indexType"`
	Partitioned   string `json:"partitioned"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraDbaSequence struct {
	OraIndexId    int    `json:"oraIndexId"`
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	SequenceOwner string `json:"sequenceOwner"`
	SequenceName  string `json:"sequenceName"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraDbaTabColumns struct {
	DataId       int    `json:"dataId"`
	ChannelId    int    `json:"channelId"`
	TaskId       int    `json:"taskId"`
	Owner        string `json:"OwnerName"`
	TableName    string `json:"tableName"`
	ColumnName   string `json:"columnName"`
	DataType     string `json:"dataType"`
	DataTypeT    string `json:"dataTypeT"`
	ColumnId     int    `json:"columnId"`
	DataLength   int    `json:"dataLength"`
	DataDesc     string `json:"dataDesc"`
	IsEquivalent string `json:"isEquivalent"`
}

type OraDbaTabColumnsDefault struct {
	DataId       int    `json:"dataId"`
	ChannelId    int    `json:"channelId"`
	TaskId       int    `json:"taskId"`
	Owner        string `json:"OwnerName"`
	TableName    string `json:"tableName"`
	ColumnName   string `json:"columnName"`
	DataType     string `json:"dataType"`
	DataDefault  string `json:"dataDefault"`
	DataDefaultT string `json:"dataDefaultT"`
	IsEquivalent string `json:"isEquivalent"`
}

type OraAllPartTables struct {
	DataId              int    `json:"dataId"`
	ChannelId           int    `json:"channelId"`
	TaskId              int    `json:"taskId"`
	Owner               string `json:"owner"`
	TableName           string `json:"tableName"`
	PartitioningType    string `json:"partitioningType"`
	SubpartitioningType string `json:"subpartitioningType"`
	PartitionCount      string `json:"partitionCount"`
	IsCompatible        string `json:"isCompatible"`
	IsConvertible       string `json:"isConvertible"`
}

type OraDbaTables struct {
	DataId        int    `json:"dataId"`
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	TableName     string `json:"tableName"`
	Temporary     string `json:"temporary"`
	Duration      string `json:"duration"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraAllObjects struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ObjectName    string `json:"objectName"`
	ObjectType    string `json:"objectType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraDbaObjects struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ObjectName    string `json:"objectName"`
	ObjectType    string `json:"objectType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraObjectCode struct {
	OwnerName  string `json:"ownerName"`
	ObjectType string `json:"objectType"`
	ObjectName string `json:"objectName"`
	Status     string `json:"status"`
	AllText    string `json:"allText"`
}

type OraLobs struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ObjectName    string `json:"objectName"`
	ObjectType    string `json:"objectType"`
	TableName     string `json:"tableName"`
	ColumnName    string `json:"columnName"`
	DataType      string `json:"dataType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraTableTypeCompatibles struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	TableName     string `json:"tableName"`
	TableType     string `json:"tableType"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraDbaViews struct {
	ChannelId     int    `json:"channelId"`
	TaskId        int    `json:"taskId"`
	Owner         string `json:"owner"`
	ViewType      string `json:"viewType"`
	ViewTypeOwner string `json:"viewTypeOwner"`
	ViewName      string `json:"viewName"`
	IsCompatible  string `json:"isCompatible"`
	IsConvertible string `json:"isConvertible"`
}

type OraSubPartitioning struct {
	ChannelId           int    `json:"channelId"`
	TaskId              int    `json:"taskId"`
	Owner               string `json:"owner"`
	TableName           string `json:"tableName"`
	SubPartitioningType string `json:"subPartitioningType"`
	IsCompatible        string `json:"isCompatible"`
	IsConvertible       string `json:"isConvertible"`
}

type ObjectCompatibleALL struct {
	Title                               string
	ListOracleOverview                  []*OracleOverview
	ListObjectCompatibleOverview        []*ObjectCompatibleOverview
	ListObjectCompatibleByObject        []*ObjectCompatibleByObject
	ListObjectCompatibleBySchema        []*ObjectCompatibleBySchema
	ListObjectCompatibleBySchemaObject  []*ObjectCompatibleBySchemaObject
	ListSchemaTableTypeCompatibles      []*SchemaTableTypeCompatibles
	ListSchemaColumnTypeCompatibles     []*SchemaColumnTypeCompatibles
	ListSchemaConstraintTypeCompatibles []*SchemaConstraintTypeCompatibles
	ListSchemaIndexTypeCompatibles      []*SchemaIndexTypeCompatibles
	ListSchemaSequenceCompatibles       []*SchemaSequenceCompatibles
	ListSchemaDefaultValueCompatibles   []*SchemaDefaultValueCompatibles
	ListSchemaViewTypeCompatibles       []*SchemaViewTypeCompatibles
	ListSchemaObjectTypeCompatibles     []*SchemaObjectTypeCompatibles
	ListLobTypeCompatibles              []*SchemaObjectTypeCompatibles

	ListSchemaJobCompatibles               []*SchemaObjectTypeCompatibles
	ListSchemaSynonymCompatibles           []*SchemaObjectTypeCompatibles
	ListSchemaPackageBodyCompatibles       []*SchemaObjectTypeCompatibles
	ListSchemaPackageCompatibles           []*SchemaObjectTypeCompatibles
	ListSchemaTriggerCompatibles           []*SchemaObjectTypeCompatibles
	ListSchemaProcedureCompatibles         []*SchemaObjectTypeCompatibles
	ListSchemaFunctionCompatibles          []*SchemaObjectTypeCompatibles
	ListSchemaDatabaseLinkCompatibles      []*SchemaObjectTypeCompatibles
	ListSchemaLobPartitionCompatibles      []*SchemaObjectTypeCompatibles
	ListSchemaLobSubpartitionCompatibles   []*SchemaObjectTypeCompatibles
	ListSchemaIndexSubpartitionCompatibles []*SchemaObjectTypeCompatibles
	ListSchemaTypeCompatibles              []*SchemaObjectTypeCompatibles

	ListSchemaPartitionTypeCompatibles      []*SchemaPartitionTypeCompatibles
	ListSchemaSubPartitionTypeCompatibles   []*SchemaSubPartitionTypeCompatibles
	ListSchemaTemporaryTableTypeCompatibles []*SchemaTemporaryTableTypeCompatibles
	ListSchemaPartitionTableCountsCheck     []*SchemaPartitionTableCountsCheck
	ListSchemaTableRowLengthCheck           []*SchemaTableRowLengthCheck
	ListSchemaTableIndexRowLengthCheck      []*SchemaTableIndexRowLengthCheck
	ListSchemaTableColumnCountsCheck        []*SchemaTableColumnCountsCheck
	ListSchemaTableIndexCountsCheck         []*SchemaTableIndexCountsCheck
	ListSchemaTableNameLengthCheck          []*SchemaTableNameLengthCheck
	ListSchemaViewNameLengthCheck           []*SchemaViewNameLengthCheck
	ListSchemaSequenceNameLengthCheck       []*SchemaSequenceNameLengthCheck
	ListSchemaTableIndexNameLengthCheck     []*SchemaTableIndexNameLengthCheck
	ListSchemaTableColumnNameLengthCheck    []*SchemaTableColumnNameLengthCheck
	ListUsernameLengthCheck                 []*UsernameLengthCheck
	ListSchemaActiveSession                 []*SchemaActiveSession
	ListSchemaTableSizeData                 []*SchemaTableSizeData
	ListSchemaTableRowsTOP                  []*SchemaTableRowsTOP
	ListSchemaTableAvgRowLengthTOP          []*SchemaTableAvgRowLengthTOP
	ListSchemaCodeObject                    []*SchemaCodeObject
	ListSchemaSynonymObject                 []*SchemaSynonymObject
	ListSchemaMaterializedViewObject        []*SchemaMaterializedViewObject
	ListSchemaTableNumberTypeEqual0         []*SchemaTableNumberTypeEqual0
	ListOraDbaConstraint                    []*OraDbaConstraint
	ListOraDbaIndex                         []*OraDbaIndex
	ListOraDbaSequence                      []*OraDbaSequence

	ListOraDbaTabColumns        []*OraDbaTabColumns
	ListDbaTabColumnsDefault    []*OraDbaTabColumnsDefault
	ListAllPartTables           []*OraAllPartTables
	ListDbaTables               []*OraDbaTables
	ListAllObjects              []*OraDbaObjects
	ListOraLobs                 []*OraLobs
	ListOraTableTypeCompatibles []*OraTableTypeCompatibles
	ListOraDbaViews             []*OraDbaViews
	ListOraSubPartitioning      []*OraSubPartitioning

	ListJob               []*OraDbaObjects
	ListSynonym           []*OraDbaObjects
	ListPackageBody       []*OraDbaObjects
	ListPackage           []*OraDbaObjects
	ListTrigger           []*OraDbaObjects
	ListProcedure         []*OraDbaObjects
	ListFunction          []*OraDbaObjects
	ListDataBaseLink      []*OraDbaObjects
	ListLobSubpartition   []*OraDbaObjects
	ListType              []*OraDbaObjects
	ListIndexSubpartition []*OraDbaObjects
	ListLobPartition      []*OraDbaObjects

	ListPackageBodyCode   []*OraObjectCode
	ListFunctionBodyCode  []*OraObjectCode
	ListProcedureBodyCode []*OraObjectCode
	ListTriggerCode       []*OraObjectCode
}

// GetFieldByName 实现 ReportData 接口
//func (o *ObjectCompatibleALL) GetFieldByName(name string) reflect.Value {
//	return reflect.ValueOf(o).Elem().FieldByName(name)
//}

func (o *ObjectCompatibleAdapter) GetFieldByName(name string) reflect.Value {
	if o == nil || o.Data == nil {
		return reflect.Value{} // Return invalid reflect.Value
	}
	v := reflect.ValueOf(o.Data)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return reflect.Value{}
	}
	return v.Elem().FieldByName(name)
}

type ObjectCompatibleAdapter struct {
	Data *ObjectCompatibleALL
}
