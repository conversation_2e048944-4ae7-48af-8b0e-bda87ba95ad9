package message

import (
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"github.com/samber/lo"
)

type AnalyzeSQLReq struct {
	PLSQL     string `json:"plsql" validate:"required"`
	Comment   string `json:"comment"`
	IsEncoded bool   `json:"isEncoded"`
}

type MethodInvoke struct {
	OwnerName             string      `json:"ownerName"`
	FuncName              string      `json:"funcName"`
	Count                 int         `json:"count"`
	IsOwnerInReservedWord bool        `json:"isOwnerInReservedWord"`
	Parents               []SQLParent `json:"parents,omitempty"`
}

type SQLParent struct {
	ParentType string `json:"parentType"`
	ParentName string `json:"parentName"`
}

type TableReference struct {
	StatementType  string `json:"statementType"`
	StatementName  string `json:"statementName"`
	TableName      string `json:"tableName"`
	Count          int    `json:"count"`
	IsDatabaseLink bool   `json:"isDatabaseLink,omitempty"`
}

type ReservedWord struct {
	Value             string         `json:"value"`
	Count             int            `json:"count"`
	Highlight         []ReservedWord `json:"highlight,omitempty"`
	HasNonAlphaPrefix bool           `json:"hasNonAlphaPrefix,omitempty"`
}

type ReservedWordItem struct {
	Value string `json:"value"`
	Count int    `json:"count"`
}

type AnalyzeSQLResp struct {
	MethodInvokeList   []MethodInvoke   `json:"methodInvokeList"`
	IdentifierList     []MethodInvoke   `json:"identifierList"`
	TableReferenceList []TableReference `json:"tableReferenceList"`
	ReservedWordList   []ReservedWord   `json:"reservedWordList"`
}

type PlSQLToJSONReq struct {
	ChannelId      int    `json:"channelId" validate:"required"`
	TaskId         int    `json:"taskId" validate:"required"`
	PLSQL          string `json:"plsql"`
	DependencyUUID string `json:"dependencyUUID"`
	Comment        string `json:"comment"`
}

type GetDependencyReq struct {
	ChannelId            int      `json:"channelId" validate:"required"`
	TaskId               int      `json:"taskId" validate:"required"`
	Schemas              []string `json:"schemas"`
	OrderType            int      `json:"orderType"`
	ExcludeAmbiguityWord bool     `json:"excludeAmbiguityWord"`
	Fields               []int    `json:"fields"`

	ObjectFilters []ObjectProperty `json:"objectFilters"`
}

type GetDependencyFromMetadataReq struct {
	ChannelId int      `json:"channelId" validate:"required"`
	TaskId    int      `json:"taskId" validate:"required"`
	OrderType int      `json:"orderType"`
	Schemas   []string `json:"schemas"`
	Fields    []int    `json:"fields"`

	ObjectFilters         []ObjectProperty `json:"objectFilters"`
	ObjectUUIDFilters     []string         `json:"objectUUIDFilters"`
	ObjectKeyFilters      []string         `json:"objectKeyFilters"`
	IncludePromptRelation bool             `json:"includePromptRelation"`
	ExcludeInvalidObject  bool             `json:"excludeInvalidObject"`
}

type ObjectProperty struct {
	Schema  string `json:"schema"`
	Package string `json:"package"`
	Title   string `json:"title"`
	Type    string `json:"type"`
}

type GetDefinitionsReq struct {
	ChannelId int                         `json:"channelId" validate:"required"`
	TaskId    int                         `json:"taskId" validate:"required"`
	Objects   []GetOracleObjectDefinition `json:"objects" validate:"required"`
}

type GetObjectDetailReq struct {
	ChannelId     int                         `json:"channelId" validate:"required"`
	TaskId        int                         `json:"taskId" validate:"required"`
	ShouldEncode  bool                        `json:"shouldEncode"`
	ShouldCorrupt bool                        `json:"shouldCorrupt"`
	Objects       []GetOracleObjectDefinition `json:"objects" validate:"required"`
}

type GetDefinitionsFromMetadataReq struct {
	ChannelId       int                         `json:"channelId" validate:"required"`
	TaskId          int                         `json:"taskId" validate:"required"`
	Objects         []GetOracleObjectDefinition `json:"objects"`
	DependencyUUIDs []string                    `json:"dependencyUUIDs"`
}

type GetArchiveTimesReq struct {
	ChannelId int `json:"channelId" validate:"required"`
	TaskId    int `json:"taskId" validate:"required"`
}

type GetArchiveDataReq struct {
	ChannelId int `json:"channelId" validate:"required"`
	TaskId    int `json:"taskId" validate:"required"`
}

type ListAnalyzeDetailReq struct {
	ChannelId int `json:"channelId" validate:"required"`
	TaskId    int `json:"taskId" validate:"required"`
	PageRequest
	// Filter conditions
	SchemaName string `json:"schemaName,omitempty"` // Exact match filter for schema name
	ObjectType string `json:"objectType,omitempty"` // Exact match filter for object type
	ObjectName string `json:"objectName,omitempty"` // Fuzzy match filter for object name (automatically wrapped with %)
}

type ListAnalyzeDetailResp struct {
	Data []*OracleObjectDefinitionAnalyzeDetail `json:"data"`
}

type OracleObjectDefinitionAnalyzeDetail struct {
	ID                              uint             `json:"id,omitempty"`
	ChannelId                       int              `json:"channelId,omitempty"`
	TaskId                          int              `json:"taskId,omitempty"`
	SchemaObjectKey                 string           `json:"schemaObjectKey,omitempty"`
	SchemaName                      string           `json:"schemaName"`
	ObjectType                      string           `json:"objectType"`
	ObjectName                      string           `json:"objectName"`
	ObjectStatus                    string           `json:"objectStatus,omitempty"`
	Status                          string           `json:"status,omitempty"`
	ErrorDetail                     string           `json:"errorDetail"`
	ReservedWordCount               uint             `json:"reservedWordCount"`
	DatabaseLinkCount               uint             `json:"databaseLinkCount"`
	IncompatibleFeatureScoreContext EvaluateContext  `json:"incompatibleFeatureScoreContext"`
	MethodInvokeList                []MethodInvoke   `json:"methodInvokeList"`   //属于函数调用
	IdentifierList                  []MethodInvoke   `json:"identifierList"`     //属于函数调用
	TableReferenceList              []TableReference `json:"tableReferenceList"` //被使用的表，其中，'@' in tableName，表示dblink
	ReservedWordList                []ReservedWord   `json:"reservedWordList"`   //关键字
	PLSQLSegment                    PLSQLSegment     `json:"plsqlSegment"`       //sql片段
}

type Segment struct {
	Name     string `json:"name"`
	StartPos int    `json:"startPos"`
	EndPos   int    `json:"endPos"`
	SQL      string `json:"sql"`
}

type Position struct {
	BP        int    `json:"bp"`
	StartPos  int    `json:"startPos"`
	SP        int    `json:"sp"`
	NP        int    `json:"np"`
	CH        string `json:"ch"`
	Token     string `json:"token"`
	StringVal string `json:"stringVal"`
}

type PLSQLSegment struct {
	SegmentPrefix Segment `json:"segmentPrefix"`
	SegmentSuffix Segment `json:"segmentSuffix"`

	Declares   []Segment `json:"declares"`
	Functions  []Segment `json:"functions"`
	Procedures []Segment `json:"procedures"`

	LeftStartPos  int        `json:"leftStartPos"`
	RightStartPos int        `json:"rightStartPos"`
	AllPositions  []Position `json:"allPositions"`
}

// CombineSegmentedSQL combines the SQL segments into a single SQL string
func (i PLSQLSegment) CombineSegmentedSQL(objectType string, objectName string) (string, bool) {
	stringBuilder := &strings.Builder{}
	stringBuilder.WriteString(i.SegmentPrefix.SQL)

	for _, declare := range i.Declares {
		stringBuilder.WriteString(declare.SQL)
	}
	wf := i.writeFunctionSQL(objectType, objectName, stringBuilder)
	wp := i.writeProcedureSQL(objectType, objectName, stringBuilder)

	stringBuilder.WriteString(i.SegmentSuffix.SQL)
	return stringBuilder.String(), wf || wp
}

// writeProcedureSQL writes the SQL for the specified procedure
func (i PLSQLSegment) writeProcedureSQL(objectType string, objectName string, stringBuilder *strings.Builder) bool {
	if strings.ToUpper(objectType) != "PROCEDURE" {
		return false
	}

	for _, procedure := range i.Procedures {
		if !strings.EqualFold(procedure.Name, objectName) {
			continue
		}
		stringBuilder.WriteString(procedure.SQL)
		return true
	}
	return false
}

// writeFunctionSQL writes the SQL for the specified function
func (i PLSQLSegment) writeFunctionSQL(objectType string, objectName string, stringBuilder *strings.Builder) bool {
	if strings.ToUpper(objectType) != "FUNCTION" {
		return false
	}
	for _, function := range i.Functions {
		if !strings.EqualFold(function.Name, objectName) {
			continue
		}
		stringBuilder.WriteString(function.SQL)
		return true
	}
	return false
}

type EvaluateContextItem struct {
	FeatureType string `json:"featureType"`
	FeatureKey  string `json:"featureKey"`
	Score       int    `json:"score,omitempty"`
}

type EvaluateContext []EvaluateContextItem

type GetLogVolumePerSecondReq struct {
	ChannelId int     `json:"channelId" validate:"required"`
	TaskId    int     `json:"taskId" validate:"required"`
	DivideBy  float64 `json:"divideBy"`
}

type GetTransactionLogVolumeReq struct {
	ChannelId int     `json:"channelId" validate:"required"`
	TaskId    int     `json:"taskId" validate:"required"`
	DivideBy  float64 `json:"divideBy"`
}

type GetTransactionDataBlocksReq struct {
	ChannelId int     `json:"channelId" validate:"required"`
	TaskId    int     `json:"taskId" validate:"required"`
	DivideBy  float64 `json:"divideBy"`
}

type GetTransactionPerSecondReq struct {
	ChannelId int `json:"channelId" validate:"required"`
	TaskId    int `json:"taskId" validate:"required"`
}

type ListPromptReq struct {
}

type ListTaskObjectPromptRelationReq struct {
	ChannelId int  `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
	PageRequest
}

type ListBasicIncompatibleFeatureReq struct {
}

type ListTaskIncompatibleFeatureReq struct {
	ChannelId int `json:"channelId" validate:"required"`
	TaskId    int `json:"taskId" validate:"required"`
}

type UpdateTaskIncompatibleFeatureReq struct {
	ChannelId int                 `json:"channelId" validate:"required"`
	TaskId    int                 `json:"taskId" validate:"required"`
	Data      IncompatibleFeature `json:"data" validate:"required"`
}

type UpdateBasicIncompatibleFeatureReq struct {
	Data IncompatibleFeature `json:"data" validate:"required"`
}

type UpdateBasicPromptReq struct {
	ID              uint   `json:"id"`
	PromptTitle     string `json:"promptTitle" validate:"required"`
	TargetLanguage  string `json:"targetLanguage" validate:"required"`
	TargetFramework string `json:"targetFramework" validate:"required"`
	PromptCategory  string `json:"promptCategory" validate:"required"`
	PromptText      string `json:"promptText" validate:"required"`
	IsDefault       bool   `json:"isDefault"`
	UsageExample    string `json:"usageExample,omitempty"`
	*BaseFields
}

type SavePromptReq struct {
	ID              uint   `json:"id"`
	PromptTitle     string `json:"promptTitle" validate:"required"`
	TargetLanguage  string `json:"targetLanguage" validate:"required"`
	TargetFramework string `json:"targetFramework" validate:"required"`
	PromptCategory  string `json:"promptCategory" validate:"required"`
	PromptText      string `json:"promptText" validate:"required"`
	IsDefault       bool   `json:"isDefault"`
	UsageExample    string `json:"usageExample,omitempty"`
	*BaseFields
}

type DeletePromptReq struct {
	PromptId uint `json:"promptID" validate:"required"`
}

type SaveTaskObjectPromptRelationReq struct {
	RelationId uint `json:"relationId"`

	ChannelId      uint   `json:"channelId" validate:"required"`
	TaskId         uint   `json:"taskId" validate:"required"`
	TaskPromptId   uint   `json:"taskPromptId" validate:"required"`
	DependencyUUID string `json:"dependencyUUID" validate:"required"`
}

type DeleteTaskObjectPromptRelationReq struct {
	TaskId     uint `json:"taskId" validate:"required"`
	RelationId uint `json:"relationId" validate:"required"`
}

type SaveTaskObjectPromptRelationResp struct {
	ChannelId    uint `json:"channelId"`
	TaskId       uint `json:"taskId"`
	TaskPromptId uint `json:"taskPromptId"`
	RelationId   uint `json:"relationId"`
}

type DeleteTaskObjectPromptRelationResp struct {
}

type GetIncompatibleFeatureScoringReq struct {
	ChannelId        int              `json:"channelId" validate:"required"`
	TaskId           int              `json:"taskId" validate:"required"`
	HistogramOptions HistogramOptions `json:"histogramOptions" validate:"required"`
}

type TestAIConnectReq struct {
	ChannelId uint `json:"channelId"`
	TaskId    uint `json:"taskId"`

	TemplateId uint `json:"templateId"`
}

type ConvertPLSQLToJavaReq struct {
	ChannelId      uint            `json:"channelId" validate:"required"`
	TaskId         uint            `json:"taskId" validate:"required"`
	ConvertObjects []ConvertObject `json:"convertObjects" validate:"required"`
	Prompt         string          `json:"prompt"`
}

type ConvertPLSQLToJavaInManualResp struct {
	JavaCode string   `json:"javaCode"`
	Prompts  []string `json:"prompts"`
}

type ConvertPLSQLToJavaInManualReq struct {
	TaskId      uint   `json:"taskId" validate:"required"`
	SQL         string `json:"sql" validate:"required"`
	SchemaName  string `json:"schemaName"`
	PackageName string `json:"packageName"`
	ObjectType  string `json:"objectType"`
	ObjectName  string `json:"objectName"`
	Prompt      string `json:"prompt"`
}

type GetPLSQLToJavaResultsReq struct {
	ChannelId uint `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
	PageRequest
}

type GetPLSQLToJavaHistoryResultsReq struct {
	ChannelId uint `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
	PageRequest
}

type GetPLSQLToJavaLogsReq struct {
	ChannelId uint `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
}

type GetPLSQLToJavaSummaryReq struct {
	ChannelId uint `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
}

type DownloadHistoryJavaCodesReq struct {
	ChannelId uint `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
}

type DownloadJavaCodesReq struct {
	ChannelId uint `json:"channelId" validate:"required"`
	TaskId    uint `json:"taskId" validate:"required"`
}

func (i ConvertPLSQLToJavaReq) GetObjectUUIDs() []string {
	return lo.Map(i.ConvertObjects, func(item ConvertObject, _ int) string {
		return item.ObjectUUID
	})
}

type ConvertObject struct {
	ObjectUUID string `json:"objectUUID" validate:"required"`
	Depth      uint   `json:"depth" validate:"required"`
}

type HistogramOptions struct {
	ArchiveTimes          bool `json:"archiveTimes"`
	ArchiveData           bool `json:"archiveData"`
	TransactionDataBlocks bool `json:"transactionDataBlocks"`
	TransactionLogVolume  bool `json:"transactionLogVolume"`
	LogVolumePerSecond    bool `json:"logVolumePerSecond"`
	TransactionPerSecond  bool `json:"transactionPerSecond"`
}

type GetIncompatibleFeatureScoringResp struct {
	Total               WeightedScoring `json:"total"`
	IncompatibleFeature WeightedScoring `json:"incompatibleFeature"`
	Histogram           WeightedScoring `json:"histogram"`
}

type TestAIConnectResp struct {
	Success        bool   `json:"success"`
	SuccessMessage string `json:"successMessage"`
	ErrMessage     string `json:"errMessage"`
}

type ConvertPLSQLToJavaResp struct {
}

type JavaCodeTreeNode struct {
	Key            string              `json:"key"`
	Title          string              `json:"title"`
	FileName       string              `json:"fileName"`
	FilePackage    string              `json:"filePackage"`
	FileContent    string              `json:"fileContent"`
	ObjectUUID     string              `json:"objectUUID"`
	ConvertPrompts []string            `json:"convertPrompts"`
	Children       []*JavaCodeTreeNode `json:"children,omitempty"`
	IsLeaf         bool                `json:"isLeaf,omitempty"`
}

type GetPLSQLToJavaResultsResp struct {
	CodeTree  []*JavaCodeTreeNode  `json:"codeTree"`
}

type GetPLSQLToJavaHistoryResultsResp struct {
	JavaCodes []OracleToJavaResult `json:"javaCode"`
	CodeTree  []*JavaCodeTreeNode  `json:"codeTree"`
}

type GetPLSQLToJavaLogsResp struct {
	Logs []OracleToJavaLog `json:"logs"`
}

type GetPLSQLToJavaSummaryResp struct {
	ID               uint `json:"id"`
	ChannelId        int  `json:"channelId"`
	TaskId           int  `json:"taskId"`
	TotalObjectNum   int  `json:"totalObjectNum"`
	SuccessObjectNum int  `json:"successObjectNum"`
	FailedObjectNum  int  `json:"failedObjectNum"`
}

type OracleToJavaLog struct {
	ID         uint      `json:"id"`
	ChannelId  int       `json:"channelId"`
	TaskId     int       `json:"taskId"`
	LogMessage string    `json:"logMessage"`
	LogLevel   string    `json:"logLevel"`
	CreatedAt  time.Time `json:"createAt"`
}

type OracleToJavaResult struct {
	ID                uint      `json:"id"`
	ChannelId         int       `json:"channelId"`
	TaskId            int       `json:"taskId"`
	ObjectUUID        string    `json:"objectUUID"`
	SchemaName        string    `json:"schemaName"`
	PackageName       string    `json:"packageName"`
	ObjectName        string    `json:"objectName"`
	ObjectType        string    `json:"objectType"`
	Depth             uint      `json:"depth"`
	FileName          string    `json:"fileName"`
	FilePackage       string    `json:"filePackage"`
	FileContent       string    `json:"fileContent"`
	ConvertDuration   float64   `json:"convertDuration"`
	ConvertStatus     string    `json:"convertStatus"`
	ConvertTime       time.Time `json:"convertTime"`
	ConvertErrMessage string    `json:"convertErrMessage"`
	ConvertSQL        string    `json:"convertSQL"`
	ConvertPrompts    []string  `json:"convertPrompts"`
}

type JavaCode struct {
	FileName    string    `json:"fileName"`
	FilePackage string    `json:"filePackage"`
	FileContent string    `json:"fileContent"`
	ObjectUUID  string    `json:"objectUUID"`
	Depth       uint      `json:"level"`
	Prompts     []string  `json:"prompt"`
	CreateTime  time.Time `json:"createTime"`
}

type WeightedScoring struct {
	FullScoring     float64 `json:"fullScoring"`
	Scoring         float64 `json:"scoring"`
	Weight          float64 `json:"weight,omitempty"`
	WeightedScoring float64 `json:"weightedScoring,omitempty"`
}

type UpdateTaskIncompatibleFeatureResp struct {
}

type UpdateBasicIncompatibleFeatureResp struct {
}

type UpdateBasicPromptResp struct {
}

type SavePromptResp struct {
}

type DeletePromptResp struct {
}

func (i *GetOracleObjectDefinition) GetObjectSchema() string {
	return strings.TrimSpace(i.ObjectSchema)
}

func (i *GetOracleObjectDefinition) GetObjectName() string {
	return strings.TrimSpace(i.ObjectName)
}

func (i *GetOracleObjectDefinition) GetObjectType() string {
	return strings.TrimSpace(i.ObjectType)
}

func (i *GetOracleObjectDefinition) GetObjectPackageName() string {
	return strings.TrimSpace(i.ObjectPackageName)
}

func (i *GetOracleObjectDefinition) GetObjectText() string {
	return strings.TrimSpace(i.ObjectText)
}

func (i *GetOracleObjectDefinition) GetObjectTextHighlightLine() string {
	return strings.TrimSpace(i.ObjectTextHighlightLine)
}

type AbstractSyntaxTreeNode struct {
	StmtType  string `json:"stmtType"`
	StmtName  string `json:"stmtName"`
	StmtValue string `json:"stmtValue"`

	Key      string                    `json:"key"`
	Children []*AbstractSyntaxTreeNode `json:"children"`
}

type PlSQLToJSONResp struct {
	Root *AbstractSyntaxTreeNode `json:"root"`
}

type GetDependencyFromMetadataResp struct {
	Dependencies []*structs.DependencyTreeNode `json:"dependencies,omitempty"`
	DisplayTree  []*structs.DependencyTreeVO   `json:"displayTree,omitempty"`
	DisplayGraph *structs.DependencyGraphVO    `json:"displayGraph,omitempty"`
}

type GetDefinitionsResp struct {
	Definitions []*GetOracleObjectDefinition `json:"definitions"`
}

type GetOracleObjectDefinition struct {
	ObjectSchema string `json:"objectSchema" validate:"required"`
	ObjectType   string `json:"objectType" validate:"required"`
	ObjectName   string `json:"objectName" validate:"required"`

	ObjectPackageName       string `json:"objectPackageName,omitempty"`
	ObjectText              string `json:"objectText,omitempty"`
	ObjectTextHighlightLine string `json:"objectTextHighlightLine,omitempty"`

	DependencyObjectKey string `json:"dependencyObjectKey,omitempty"`
}

type GetDefinitionsFromMetadataResp struct {
	Data []DefinitionAndDetail `json:"data"`
}

type GetObjectDetailResp struct {
	Data []*DefinitionAndDetail `json:"data"`
}

type DefinitionAndDetail struct {
	Definition *GetOracleObjectDefinition           `json:"definition"`
	Detail     *OracleObjectDefinitionAnalyzeDetail `json:"detail"`
}

func (r *GetDependencyFromMetadataResp) ApplyOutputFields(fields []int) {
	if len(fields) == 0 {
		return
	}

	hasDependencies, hasTree, hasGraph := false, false, false
	for _, field := range fields {
		switch field {
		case 1:
			hasDependencies = true
		case 2:
			hasTree = true
		case 3:
			hasGraph = true
		}
	}

	if !hasDependencies {
		r.Dependencies = nil
	}
	if !hasTree {
		r.DisplayTree = nil
	}
	if !hasGraph {
		r.DisplayGraph = nil
	}
}

type GetArchiveTimesResp struct {
	HourData []Datum `json:"hourData"`
	DayData  []Datum `json:"dayData"`

	GraphData *GraphData `json:"graphData"`
}

type GraphData struct {
	Values [][]float64 `json:"values"`
	Times  []string    `json:"times"`
	Titles []string    `json:"titles"`
}

type Datum struct {
	Time  string  `json:"time"`
	Value float64 `json:"value"`
}

type ArchiveData struct {
	Day string `json:"day"`

	HourZero        string `json:"hourZero"`
	HourOne         string `json:"hourOne"`
	HourTwo         string `json:"hourTwo"`
	HourThree       string `json:"hourThree"`
	HourFour        string `json:"hourFour"`
	HourFive        string `json:"hourFive"`
	HourSix         string `json:"hourSix"`
	HourSeven       string `json:"hourSeven"`
	HourEight       string `json:"hourEight"`
	HourNine        string `json:"hourNine"`
	HourTen         string `json:"hourTen"`
	HourEleven      string `json:"hourEleven"`
	HourTwelve      string `json:"hourTwelve"`
	HourThirteen    string `json:"hourThirteen"`
	HourFourteen    string `json:"hourFourteen"`
	HourFifteen     string `json:"hourFifteen"`
	HourSixteen     string `json:"hourSixteen"`
	HourSeventeen   string `json:"hourSeventeen"`
	HourEighteen    string `json:"hourEighteen"`
	HourNineteen    string `json:"hourNineteen"`
	HourTwenty      string `json:"hourTwenty"`
	HourTwentyOne   string `json:"hourTwentyOne"`
	HourTwentyTwo   string `json:"hourTwentyTwo"`
	HourTwentyThree string `json:"hourTwentyThree"`

	Total string `json:"total"`
}

type GetArchiveDataResp struct {
	DayData   []Datum    `json:"dayData"`
	HourData  []Datum    `json:"hourData"`
	GraphData *GraphData `json:"graphData"`
}

type GetLogVolumePerSecondResp struct {
	GraphData *GraphData             `json:"graphData"`
	Data      []TransactionDataBlock `json:"data"`
}

type GetTransactionLogVolumeResp struct {
	GraphData *GraphData             `json:"graphData"`
	Data      []TransactionDataBlock `json:"data"`
}

type GetTransactionDataBlocksResp struct {
	GraphData *GraphData             `json:"graphData"`
	Data      []TransactionDataBlock `json:"data"`
}

type GetTransactionPerSecondResp struct {
	GraphData *GraphData             `json:"graphData"`
	Data      []TransactionDataBlock `json:"data"`
}

type TransactionDataBlock struct {
	SnapshotID     string  `json:"snapshotID"`
	SnapshotTime   string  `json:"snapshotTime"`
	PerSecondValue float64 `json:"perSecondValue"`
}

type ListBasicIncompatibleFeatureResp struct {
	Features []*IncompatibleFeature `json:"features"`
}

type PromptRelation struct {
	ID             uint   `json:"id"`
	TaskId         uint   `json:"taskId,omitempty"`
	ChannelId      uint   `json:"channelId,omitempty"`
	PromptId       uint   `json:"promptId,omitempty"`
	DependencyUUID string `json:"dependencyUUID" validate:"required"`
}

type ListTaskObjectPromptRelationResp struct {
	Data []*PromptRelation `json:"data"`
}

type ListPromptResp struct {
	Prompts []*Prompt `json:"prompts"`
}

type Prompt struct {
	ID              uint   `json:"id"`
	PromptTitle     string `json:"promptTitle" validate:"required"`
	TargetLanguage  string `json:"targetLanguage" validate:"required"`
	TargetFramework string `json:"targetFramework" validate:"required"`
	PromptCategory  string `json:"promptCategory" validate:"required"`
	PromptText      string `json:"promptText" validate:"required"`
	IsDefault       bool   `json:"isDefault"`
	UsageExample    string `json:"usageExample,omitempty"`
	*BaseFields
}
type IncompatibleFeature struct {
	ID           uint   `json:"id"`
	TaskId       int    `json:"taskId,omitempty"`
	FeatureType  string `json:"featureType"`
	FeatureKey   string `json:"featureKey"`
	FeatureScore int    `json:"featureScore"`
	FeatureDesc  string `json:"featureDesc"`
	*BaseFields
}

type ListTaskIncompatibleFeatureResp struct {
	Features []*IncompatibleFeature `json:"features"`
}

type DownloadJavaCodesResp struct {
	FilePath string
}

func (i DownloadJavaCodesResp) GetFilePath() string {
	return i.FilePath
}
