package message

// LoginReq login
type LoginReq struct {
	Username  string `json:"username"`
	Password  string `json:"password"`
	AutoLogin bool   `json:"autoLogin"`
	Type      string `json:"type"`
}

// RegisterReq 用户注册请求
type RegisterReq struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Email    string `json:"email"`
}

type LoginResp struct {
	Status           string `json:"status"`
	Type             string `json:"type"`
	CurrentAuthority string `json:"currentAuthority"`
}

type GetUserInfo struct {
	Success  bool `json:"success"`
	UserInfo `json:"data"`
}

type UserInfo struct {
	Name      string `json:"name"`
	Avatar    string `json:"avatar"`
	Userid    string `json:"userid"`
	Email     string `json:"email"`
	Signature string `json:"signature"`
	Title     string `json:"title"`
	Group     string `json:"group"`
	Tags      []struct {
		Key   string `json:"key"`
		Label string `json:"label"`
	} `json:"tags"`
	NotifyCount int    `json:"notifyCount"`
	UnreadCount int    `json:"unreadCount"`
	Country     string `json:"country"`
	Access      string `json:"access"`
	Geographic  struct {
		Province struct {
			Label string `json:"label"`
			Key   string `json:"key"`
		} `json:"province"`
		City struct {
			Label string `json:"label"`
			Key   string `json:"key"`
		} `json:"city"`
	} `json:"geographic"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
}

// 修改密码请求
// ChangePasswordReq 修改密码
// @Description 修改密码
// @Param username string 用户名
// @Param oldPassword string 旧密码
// @Param newPassword string 新密码
type ChangePasswordReq struct {
	Username    string `json:"username"`
	OldPassword string `json:"oldPassword"`
	NewPassword string `json:"newPassword"`
}

// ChangePasswordResp 修改密码响应
type ChangePasswordResp struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
}
