package message

import (
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
)

type Task struct {
	TaskID                  int       `json:"taskId"`
	TaskType                int       `json:"taskType" validate:"required,oneof=1 2 3 4 5 6 7 8 9" enums:"1,2,3,4,5,6,7,8,9"`
	TaskName                string    `json:"taskName" validate:"required,min=1,max=100"`
	StartTime               time.Time `json:"startTime"`
	EndTime                 time.Time `json:"endTime"`
	TaskStatus              int       `json:"taskStatus"  validate:"required,oneof=1 2 3 4 5" enums:"1,2,3,4,5"`
	ChannelId               int       `json:"channelId" validate:"required"`
	RunParams               string    `json:"runParams"`
	TaskParamTemplateId     int       `json:"taskParamTemplateId"`
	TabcolmapTemplateId     int       `json:"tabcolmapTemplateId"`
	ObjmapTemplateId        int       `json:"objmapTemplateId"`
	SqlmapTemplateId        int       `json:"sqlmapTemplateId"`
	ColDefaultMapTemplateId int       `json:"colDefaultMapTemplateId"`
	IncrementId             int       `json:"incrementId"`
	TaskSeq                 int       `json:"taskSeq"`
	ServerId                string    `json:"serverId"`
	ErrorDetail             string    `json:"errorDetail"`
	TaskObjRef              string    `json:"taskObjRef" validate:"omitempty,oneof=channel tasks" enums:"channel,tasks"`
	TaskReftask             int       `json:"taskReftask"`
	TaskWarning             string    `json:"taskWarning"`
	OnlyIncompatibleDetail  int       `json:"onlyIncompatibleDetail"`
	ParentTaskId            int       `json:"parentTaskId"`
	Progress                float64   `json:"progress"`
	ScnNumber               string    `json:"scnNumber"`
	*BaseFields
}
type CreateDefaultTasksReq struct {
	ChannelId int `json:"-"`
}
type CreateDefaultTasksResp struct {
}

type CreateDefaultTasksForM2TReq struct {
	ChannelId                   int    `json:"channelId"`
	DatasourceIdS               int    `json:"datasourceIdS" validate:"required"`
	DatasourceIdT               int    `json:"datasourceIdT" validate:"required"`
	DataCompare                 string `json:"dataCompare" validate:"required,oneof=Y N" enums:"Y,N"`
	Increment                   string `json:"increment" validate:"required,oneof=Y N" enums:"Y,N"`
	FullInitialization          string `json:"fullInitialization" validate:"required,oneof=Y N" enums:"Y,N"`
	MigrateFullAndIncrementData string `json:"migrateFullAndIncrementData" validate:"required,oneof=Y N" enums:"Y,N"`
}
type CreateDefaultTasksForM2TResp struct {
}

type CreateTaskReq struct {
	*Task
}
type CreateTaskResp struct {
}

type BatchDeleteTasksReq struct {
	ChannelId int   `json:"-"`
	TaskIDs   []int `json:"taskIDs"`
}
type BatchDeleteTasksResp struct {
}

type ListTasksByChannelIdReq struct {
	ChannelId    int    `json:"-"`
	ParentTaskId int    `json:"parentTaskId" form:"parentTaskId"`
	ObjectQuery  string `json:"objectQuery" form:"objectQuery"`
}
type ListTasksByChannelIdResp struct {
	Tasks []*Task `json:"tasks"`
}

type GetOneTaskByChannelIdReq struct {
	ChannelId int `json:"channelId"`
}
type GetOneTaskByChannelIdResp struct {
	Task *Task `json:"task"`
}

type UpdateTaskAndChannelSchemaObjectByIdReq struct {
	Task                *Task                `json:"task"`
	ChannelSchemaObject *ChannelSchemaObject `json:"channelSchemaObject"`
	TaskParams          []*TaskParam         `json:"taskParams"`
}
type UpdateTaskAndChannelSchemaObjectByIdResp struct {
}

type GetTaskInfoAndChannelSchemaObjectByIdReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}
type GetTaskInfoAndChannelSchemaObjectByIdResp struct {
	Task                *Task                `json:"task"`
	ChannelSchemaObject *ChannelSchemaObject `json:"channelSchemaObject"`
}

type TaskParamTemplate struct {
	TaskParamTemplateID int    `json:"taskParamTemplateID"`
	ParamTemplateName   string `json:"paramTemplateName" validate:"required,min=1,max=100"`
	DefaultTag          string `json:"defaultTag" validate:"required,oneof=Y N" enums:"Y,N"`
	TaskType            int    `json:"taskType" validate:"required,oneof=1 2 3 4 5 6 7 8 9" enums:"1,2,3,4,5,6,7,8,9"`
	TemplateType        int    `json:"templateType"`
	*BaseFields
}

type GetTaskParamTemplateListByTaskTypeReq struct {
	TaskType int `json:"taskType" validate:"required,oneof=1 2 3 4 5 6 7 8 9" enums:"1,2,3,4,5,6,7,8,9"`
}
type GetTaskParamTemplateListByTaskTypeResp struct {
	TaskParamTemplateList []*TaskParamTemplate `json:"taskParamTemplateList"`
}

type CreateOrUpdateTaskParamTemplateReq struct {
	*TaskParamTemplate
}
type CreateOrUpdateTaskParamTemplateResp struct {
	TaskParamTemplateID int `json:"taskParamTemplateID"`
}
type BatchDeleteTaskParamTemplatesReq struct {
	TaskId               int
	TaskParamTemplateIDs []int
}
type BatchDeleteTaskParamTemplatesResp struct {
}
type ListTaskParamTemplatesReq struct {
	ParamTemplateName string `json:"paramTemplateName"`
	PageRequest
}
type ListTaskParamTemplatesResp struct {
	TaskParamTemplates []*TaskParamTemplate
}

type GetTaskParamTemplateReq struct {
	TaskparamTemplateID int `json:"taskparamTemplateID"`
}
type GetTaskParamTemplateResp struct {
	*TaskParamTemplate
}

type TaskExecutionReq struct {
	ChannelId int
	TaskId    int
}
type TaskExecutionResp struct {
}

type TaskStopReq struct {
	ChannelId int
	TaskId    int
}
type TaskStopResp struct {
}

type TaskExecutionPreCheckReq struct {
	ChannelId int
	TaskId    int
}
type TaskExecutionPreCheckResp struct {
	PreCheckPass    string `json:"preCheckPass"`
	PreCheckMessage string `json:"preCheckMessage"`
}

type GetObjectCompatibleCount struct {
	CompatibleType  string `json:"compatibleType"`
	CompatibleCount int    `json:"compatibleCount"`
	CompatiblePct   string `json:"compatiblePct"`
}
type GetObjectAssessResultSummaryReq struct {
	ChannelId int
	TaskId    int
}
type GetObjectAssessResultSummaryResp struct {
	CheckTime           string                      `json:"checkTime"`
	DBName              string                      `json:"dbName"`
	AssessTotal         int                         `json:"assessTotal"`
	ListCompatibleCount []*GetObjectCompatibleCount `json:"listCompatibleCount"`
}
type GetObjectAssessResultSummaryBySchemaReq struct {
	ChannelId int
	TaskId    int
}

type GetObjectCompatibleCountBySchema struct {
	CheckTime     string `json:"checkTime"`
	DBName        string `json:"dbName"`
	Schema        string `json:"schema"`
	AssessTotal   string `json:"assessTotal"`
	Compatible    string `json:"compatible"`
	InCompatible  string `json:"inCompatible"`
	Convertible   string `json:"convertible"`
	InConvertible string `json:"inConvertible"`
	InValid       string `json:"inValid"`
}
type GetObjectAssessResultSummaryBySchemaResp struct {
	ListGetObjectCompatibleCountBySchema []*GetObjectCompatibleCountBySchema `json:"listGetObjectCompatibleCountBySchema"`
}

type GetObjectAssessResultDetailReq struct {
	ChannelId int
	TaskId    int
	Schema    string
}
type GetObjectAssessResultDetailResp struct {
	ObjectAssessResultDetail *ObjectCompatibleALL `json:"objectAssessResultDetail"`
}

type DownloadObjectAssessResultReportReq struct {
	ChannelId int
	TaskId    int
}
type DownloadObjectAssessResultReportResp struct {
}

type DownloadObjectAssessResultReportBySchemaReq struct {
	ChannelId int      `json:"channelId"`
	TaskId    int      `json:"taskId"`
	Schema    []string `json:"schema"`
	Title     string   `json:"title"`
}
type DownloadObjectAssessResultReportBySchemaResp struct {
}

type TaskParam struct {
	TaskParamID           int      `json:"taskParamID"`
	ChannelID             int      `json:"channelID" validate:"required"`
	TaskID                int      `json:"taskID" validate:"required"`
	ParamName             string   `json:"paramName" validate:"required,min=1,max=100"`
	ParamValueCurrent     string   `json:"paramValueCurrent" validate:"required,min=1,max=500"`
	ParamValueDefault     string   `json:"paramValueDefault" validate:"required,min=1,max=500"`
	TaskparamTemplateID   int      `json:"taskparamTemplateID" validate:"required"`
	TaskRefTask           int      `json:"taskRefTask"`
	IncrementId           int      `json:"incrementId"`
	HasSubParams          bool     `json:"hasSubParams"`
	SubParamValuesCurrent []string `json:"subParamValuesCurrent"`
	SubParamValuesDefault []string `json:"subParamValuesDefault"`
	*BaseFields
}

type GetMergedTaskParamListByTaskparamTemplateIdReq struct {
	ChannelId           int `json:"channelId"`
	TaskId              int `json:"taskId"`
	TaskparamTemplateId int `json:"taskparamTemplateId"`
}
type GetMergedTaskParamListByTaskparamTemplateIdResp struct {
	TaskParams []*TaskParam `json:"taskParams"`
}

type SaveTaskParamsReq struct {
	ChannelId           int          `json:"-"`
	TaskId              int          `json:"-"`
	TaskparamTemplateId int          `json:"-"`
	TaskParams          []*TaskParam `json:"taskParams"`
}
type SaveTaskParamsResp struct {
}

type TaskTableConfig struct {
	TaskTableId        int    `json:"taskTableId"`
	ChannelId          int    `json:"channelId"`
	TaskId             int    `json:"taskId"`
	DbNameS            string `json:"dbNameS" validate:"required,min=1,max=200"`
	SchemaNameS        string `json:"schemaNameS" validate:"required,min=1,max=200"`
	TableNameS         string `json:"tableNameS" validate:"required,min=1,max=200"`
	DbNameT            string `json:"dbNameT" validate:"required,min=1,max=200"`
	SchemaNameT        string `json:"schemaNameT" validate:"required,min=1,max=200"`
	TableNameT         string `json:"tableNameT" validate:"required,min=1,max=200"`
	OperatorTag        string `json:"operatorTag" validate:"min=1,max=1"`
	ColumnslistTidb    string `json:"columnslistTidb" validate:"min=1,max=1000" example:"column1,column2,column3"`
	ColumnslistOracle  string `json:"columnslistOracle" validate:"min=1,max=1000" example:"column1,column2,column3"`
	FilterClauseTidb   string `json:"filterClauseTidb" validate:"min=1,max=1000" example:"where a>1"`
	FilterClauseOracle string `json:"filterClauseOracle" validate:"min=1,max=1000" example:"where a>1"`
	SqlhintTidb        string `json:"sqlhintTidb" validate:"min=1,max=1000"`
	SqlhintOracle      string `json:"sqlhintOracle" validate:"min=1,max=1000"`
	ColmapTidb         string `json:"colmapTidb" validate:"min=1,max=1000"`
	ColmapOracle       string `json:"colmapOracle" validate:"min=1,max=1000"`
	ChunkSize          int64  `json:"chunkSize" validate:"min=1,max=1000"`
	EnableChunkSplit   string `json:"enableChunkSplit"`
	TablePartition     string `json:"tablePartition"`
	*BaseFields
}
type CreateTaskTableConfigsReq struct {
	ChannelId        int                `json:"-"`
	TaskId           int                `json:"-"`
	TaskTableConfigs []*TaskTableConfig `json:"taskTableConfigs"`
}
type CreateTaskTableConfigsResp struct {
}

type UpdateTaskTableConfigsReq struct {
	ChannelId        int                `json:"-"`
	TaskId           int                `json:"-"`
	TaskTableConfigs []*TaskTableConfig `json:"taskTableConfigs"`
}
type UpdateTaskTableConfigsResp struct {
}

type DeleteTaskTableConfigsReq struct {
	ChannelId int   `json:"-"`
	TaskId    int   `json:"-"`
	Ids       []int `json:"ids"`
}
type DeleteTaskTableConfigsResp struct {
}

type GetTaskTableConfigsByTaskIdAndChannelIdReq struct {
	ChannelId int `json:"-"`
	TaskId    int `json:"-"`
}
type GetTaskTableConfigsByTaskIdAndChannelIdResp struct {
	TaskTableConfigs []*TaskTableConfig `json:"taskTableConfigs"`
}

type GetColumnNamesReq struct {
	ChannelId   int      `json:"-"`
	SchemaNames []string `json:"schemaNames"`
	TableNames  []string `json:"tableNames"`
}
type GetColumnNamesResp struct {
	ColumnNames []*structs.ColumnName `json:"columnNames"`
}

type TaskDetailChartData struct {
	Total int    `json:"total"`
	Type  string `json:"type"`
	Count int    `json:"count"`
	Pct   string `json:"pct"`
}

type TaskDetailSchemaData struct {
	ObjectAssessment  []string                              `json:"objectAssessment"`
	DdlMigration      []MigrationStructureTaskSummaryDetail `json:"ddlMigration"`
	FullDataMigration []MigrationDataTaskSummaryDetail      `json:"fullDataMigration"`
	DataCompare       []DataCompareSummaryDetail            `json:"dataCompare"`
	SqlAnalyzer       []SqlAnalyzerSummaryDetail            `json:"sqlAnalyzer"`
	Statistics        []StatisticsSummaryDetail             `json:"statistics"`
}

type GetTaskDetailResultResp struct {
	TaskId                  int                   `json:"taskId"`
	StartTime               time.Time             `json:"startTime"`
	DBName                  string                `json:"dbName"`
	TotalTables             int                   `json:"totalTables"`
	TotalDuration           string                `json:"totalDuration"`
	TaskDetailChartDataList []TaskDetailChartData `json:"taskDetailChartDataLst"`
	TaskDetailSchemaData    TaskDetailSchemaData  `json:"taskDetailSchemaData"`
}

type GetTaskDetailResultReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type GetTaskProgressReq struct {
	ChannelId int       `json:"channelId"`
	TaskId    int       `json:"taskId"`
	StartTime time.Time `json:"startTime"`
}

type GetTaskProgressResp struct {
	TaskId         int       `json:"taskId"`
	StartTime      time.Time `json:"startTime"`
	LastUpdateTime time.Time `json:"lastUpdateTime"`
	TotalDuration  string    `json:"totalDuration"`

	TotalNums          int `json:"totalNums"`
	SuccessNums        int `json:"successNums"`
	FailedNums         int `json:"failedNums"`
	IncompatibleNums   int `json:"incompatibleNums"`
	PartialSuccessNums int `json:"partialSuccessNums"`

	RunningNums        int `json:"runningNums"`
	TimeoutNums        int `json:"timeoutNums"`
	SchemaNotMatchNums int `json:"schemaNotMatchNums"`
	FilteredNums       int `json:"filteredNums"`

	TotalChunkNums   int `json:"totalChunkNums"`
	SuccessChunkNums int `json:"successChunkNums"`
	RunningChunkNums int `json:"runningChunkNums"`

	RunningChunkInfo *TaskProgressChunkInfo `json:"runningChunkInfo"`
	TaskLogFile      string                 `json:"taskLogFile"`
	OtherFiles       map[string]string      `json:"otherFiles"`
	ProgressLogInfo  *TaskProgressLogInfo   `json:"progressLog"`
	Progress         float64                `json:"progress"`
}

type TaskProgressChunkInfo struct {
	TaskId             int       `json:"taskId"`
	RunningStartTime   time.Time `json:"runningStartTime"`
	LastUpdateTime     time.Time `json:"lastUpdateTime"`
	RunningTotalChunk  int       `json:"runningTotalChunk"`
	RunningFinishChunk int       `json:"runningFinishChunk"`
}

type TaskProgressLogInfo struct {
	TaskId        int                      `json:"taskId"`
	LogStartTime  time.Time                `json:"logStartTime"`
	LogCount      int                      `json:"logCount"`
	LogDetailList []*TaskProgressLogDetail `json:"logDetailList"`
}

type TaskProgressLogDetail struct {
	LogTime    time.Time `json:"logTime"`
	LogLevel   string    `json:"logLevel"`
	LogMessage string    `json:"logMessage"`
}

type OperAllChannelSchemaTablesReq struct {
	ChannelId          int    `json:"channelId"`
	TaskId             int    `json:"taskId"`
	ChannelSchtableIds []int  `json:"channelSchtableIds"`
	PartitionTypeT     string `json:"partitionTypeT"`
	ClusterTypeT       string `json:"clusterTypeT"`
}

type OperChannelSchemaTablesReq struct {
	ChannelId  int      `json:"channelId"`
	TaskId     int      `json:"taskId"`
	ModifyTag  string   `json:"modifyTag"`
	TableNameS []string `json:"tableNameS"`
}

type OperAllChannelSchemaTablesResp struct {
}

type CreateTaskByCsvReq struct {
	ChannelId int    `json:"channelId"`
	TaskName  string `json:"taskName" validate:"required,min=1,max=100"`
	TaskType  int    `json:"taskType" validate:"required,oneof=1 2 3 4 5 6 7 8 9" enums:"1,2,3,4,5,6,7,8,9"`
	FileName  string `json:"fileName" validate:"required"`
}

type CreateTaskByRefTaskReq struct {
	ChannelId   int    `json:"channelId"`
	TaskName    string `json:"taskName" validate:"required,min=1,max=100"`
	TaskType    int    `json:"taskType" validate:"required,oneof=1 2 3 4 5 6 7 8 9" enums:"1,2,3,4,5,6,7,8,9"`
	TaskRefTask int    `json:"taskRefTask" validate:"required"`
}

type ListSubTasksByParentIdReq struct {
	ChannelId    int    `json:"-"`
	ParentTaskId int    `json:"-"`
	SubTaskId    int    `json:"subTaskId" form:"subTaskId"`
	TaskStatus   int    `json:"taskStatus" form:"taskStatus"`
	ServerId     string `json:"serverId" form:"serverId"`
	PageRequest
}

type GetSubTaskSummaryByParentIdReq struct {
	ChannelId    int `json:"-"`
	ParentTaskId int `json:"-"`
}

type GetSubTaskSummaryByParentIdResp struct {
	Summary                       structs.SubTaskSummary `json:"summary"`
	SchemaTableChangeCheckRetCode int                    `json:"schemaTableChangeCheckRetCode" enums:"1,2,3"`
}

type ListSubTasksByParentIdResp struct {
	SubTasks []*Task `json:"subtasks"`
}

type BatchDeleteSubTasksByIdReq struct {
	ChannelId    int `json:"-"`
	ParentTaskId int `json:"-"`
}
type BatchDeleteSubTasksByIdResp struct {
}

type SubTasksExecutionReq struct {
	ChannelId    int    `json:"-"`
	ParentTaskId int    `json:"-"`
	SubTaskIds   []int  `json:"subtaskIds"`
	AllSubTasks  bool   `json:"allSubTasks"`
	ServerId     string `json:"serverId"`
}
type SubTasksExecutionResp struct {
	Message string `json:"message"`
}

type CreateSubTasksReq struct {
	ChannelId    int `json:"-"`
	ParentTaskId int `json:"-"`
}
type CreateSubTasksResp struct {
}

type CreateTaskTabCfgByCsvReq struct {
	ChannelId int    `json:"channelId"`
	TaskId    int    `json:"taskId"`
	FileName  string `json:"fileName" validate:"required"`
}

type FixTaskStatusReq struct {
	ChannelId int   `json:"channelId"`
	TaskIdS   []int `json:"taskIdS"`
}

type SplitTaskReq struct {
	ChannelId int   `json:"channelId"`
	TaskIds   []int `json:"taskIds"`
	SplitNum  int   `json:"splitNum" validate:"required,min=1,max=100"`
}

type VerifySplitTaskReq struct {
	ChannelId int   `json:"channelId"`
	TaskIds   []int `json:"taskIds"`
	PageRequest
}

type SplitTaskResp struct {
	IsFailed bool   `json:"isFailed"`
	Message  string `json:"message"`
}

type VerifySplitTaskResp struct {
	IsFailed   bool               `json:"isFailed"`
	Message    string             `json:"message"`
	DiffTables []SplitSchemaTable `json:"diffTables"`
}

type SplitSchemaTable struct {
	SchemaName string `json:"schemaName"`
	TableName  string `json:"tableName"`

	TaskId     int     `json:"taskId"`
	TaskName   string  `json:"taskName"`
	TaskType   int     `json:"taskType"`
	TableSizeM float64 `json:"tableSizeM"`
}

type SyncSourceTableColumnsReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type BatchUpdateSourceTableColumnsReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`

	Data []*TableColumnCustomMapRule `json:"tableColumnMapRules"`
}

type BatchUpdateSourceTableColumnsResp struct {
	Affected int `json:"affected"`
}

type DeleteSourceTableColumnsReq struct {
	ChannelId int `json:"channelId"`

	SchemaName string `json:"schemaName"`
	TableName  string `json:"tableName"`
	ColumnName string `json:"columnName"`
}

type DeleteSourceTableColumnsResp struct {
	AffectedColumns int64 `json:"affectedColumns"`
	AffectedSummary int64 `json:"affectedSummary"`
}

type GetSyncSourceTableColumnsStatusReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type GetSyncSourceTableColumnsStatusResp struct {
	ChannelId         int     `json:"channelId"`
	Status            string  `json:"status"`
	TotalTableNum     int64   `json:"totalTableNum"`
	FinishedColumnNum int64   `json:"finishedColumnNum"`
	FinishedTableNum  int64   `json:"finishedTableNum"`
	TableProgress     float64 `json:"tableProgress"`
}

type GetSourceTableColumnsReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`

	// filter condition
	FilterQueries string `json:"filterQueries" form:"filterQueries"`

	SchemaNames   string `json:"schemaNames" form:"schemaNames"`
	TableName     string `json:"tableName" form:"tableName"`
	ColumnName    string `json:"columnName" form:"columnName"`
	DataType      string `json:"dataType" form:"dataType"`
	DataDefault   string `json:"dataDefault" form:"dataDefault"`
	DataLength    uint   `json:"dataLength" form:"dataLength"`
	DataPrecision uint   `json:"dataPrecision" form:"dataPrecision"`
	DataScale     uint   `json:"dataScale" form:"dataScale"`
	Nullable      string `json:"nullable" form:"nullable"`
	ColumnComment string `json:"columnComment" form:"columnComment"`
	PageRequest
}

type GetSourceTableColumnsResp struct {
	Data []*TableColumnCustomMapRule `json:"data"`
}

type SyncSourceTableColumnsResp struct {
	Affected int64 `json:"affected"`
}

type TableColumnCustomMapRule struct {
	ChannelId    int    `json:"channelId"`
	SchemaNameS  string `json:"schemaNameS"`
	TableNameS   string `json:"tableNameS"`
	ColumnDigest string `json:"columnDigest" validate:"required"`

	ColumnID       uint   `json:"columnID"`
	ColumnNameS    string `json:"columnNameS"`
	ColumnNameT    string `json:"columnNameT"`
	ColumnCommentS string `json:"columnCommentS"`
	ColumnCommentT string `json:"columnCommentT"`
	DataDefaultS   string `json:"dataDefaultS"`
	DataDefaultT   string `json:"dataDefaultT"`
	DataTypeS      string `json:"dataTypeS"`
	DataTypeT      string `json:"dataTypeT"`
	DataLengthS    uint   `json:"dataLengthS"`
	DataLengthT    uint   `json:"dataLengthT"`
	DataPrecisionS uint   `json:"dataPrecisionS"`
	DataPrecisionT uint   `json:"dataPrecisionT"`
	DataScaleS     uint   `json:"dataScaleS"`
	DataScaleT     uint   `json:"dataScaleT"`
	NullableS      string `json:"nullableS"`
	NullableT      string `json:"nullableT"`
}

type CreateChunkReq struct {
	ChannelId int
	TaskId    int
}
type CreateChunkResp struct {
}

type ObjectParserCfg struct {
	TaskTableId int    `json:"taskTableId"`
	ChannelId   int    `json:"channelId"`
	TaskId      int    `json:"taskId"`
	SchemaName  string `json:"schemaName"`
	ObjectType  string `json:"objectType"`
	ObjectName  string `json:"objectName"`
	*BaseFields
}

type CreateObjectParserCfgReq struct {
	ChannelId int    `json:"channelId"`
	TaskId    int    `json:"taskId"`
	FileName  string `json:"fileName"`
}

type CreateObjectParserCfgDataReq struct {
	ChannelId        int                `json:"channelId"`
	TaskId           int                `json:"taskId"`
	ObjectParserCfgs []*ObjectParserCfg `json:"objectParserCfgs"`
}

type CreateObjectParserCfgResp struct {
	ObjectTypeCount map[string]int `json:"objectTypeCount"`
}

type ListObjectParserCfgReq struct {
	ChannelId  int    `json:"-"`
	TaskId     int    `json:"taskId" validate:"required"`
	SchemaName string `json:"schemaName"` // Exact match
	ObjectType string `json:"objectType"`
	ObjectName string `json:"objectName"` // Fuzzy match
	PageRequest
}

type ListObjectParserCfgResp struct {
	TaskId   int               `json:"taskId"`
	CfgItems []ObjectParserCfg `json:"cfgItems"`
}
