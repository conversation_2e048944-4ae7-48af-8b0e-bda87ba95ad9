package message

import (
	"time"
)

type SqlAnalyzerSummaryDetail struct {
	TaskId       int       `json:"taskId"`
	Schema       string    `json:"schema"`
	TotalNums    int       `json:"totalNums"`
	SuccessNums  int       `json:"successNums"`
	FailedNums   int       `json:"failedNums"`
	RunningNums  int       `json:"runningNums"`
	WaitingNums  int       `json:"waitingNums"`
	SuccessRatio string    `json:"successRatio"`
	Duration     string    `json:"duration"`
	StartTime    time.Time `json:"StartTime"`
	EndTime      time.Time `json:"endTime"`
}

type SqlAnalyzerReportDownloadReq struct {
	TaskID     int    `json:"taskId"`
	ReportType string `json:"reportType"`
}

type SqlAnalyzerReportDownloadResp struct {
}

type DownloadSqlReportBySchemaReq struct {
	ChannelId  int      `json:"channelId" validate:"required"`
	TaskId     int      `json:"taskId" validate:"required"`
	ReportType string   `json:"reportType" validate:"required,oneof=compatibility performance compatibility_fingerprint compatibility_v2"`
	Schema     []string `json:"schema" validate:"required"`
	Title      string   `json:"title"`

	ReportFilePath string `json:"-"`
}
type DownloadSqlReportBySchemaResp struct {
	ExportFilePath string `json:"exportFilePath"`
}

func (i DownloadSqlReportBySchemaResp) GetFilePath() string {
	return i.ExportFilePath
}

type ListSQLAnalyzeTaskReq struct {
	TaskId      int `json:"-"`
	TaskModeInt int `json:"taskModeInt" form:"taskModeInt" `
}

type UpdateSQLAnalyzeTaskReq struct {
	SQLAnalyzeId int    `json:"sqlAnalyzeId"`
	TaskSQL      string `json:"taskSQL"`
	TaskId       int    `json:"taskId"`
	Comment      string `json:"comment"`
}

type ExecuteSQLAnalyzeEnvDeployTaskReq struct {
	TaskId        int   `json:"-"`
	TaskModeInt   int   `json:"taskModeInt" form:"taskModeInt" `
	SQLAnalyzeIds []int `json:"sqlAnalyzeIds"`
	RunMode       int   `json:"runMode"`
}

type UpdateSQLAnalyzeTaskResp struct {
}

type ExecuteSQLAnalyzeEnvDeployTaskResp struct {
}

type ListSQLAnalyzeTaskResp struct {
	Tasks []SQLAnalyzerTask `json:"tasks"`
}

type SQLAnalyzerTask struct {
	SQLAnalyzeId int       `json:"sqlAnalyzeId"`
	TaskNumber   int       `json:"taskNumber"`
	TaskName     string    `json:"taskName"`
	IsIgnore     string    `json:"isIgnore"`
	TaskStatus   int       `json:"taskStatus"`
	TaskLog      string    `json:"taskLog"`
	TaskSQL      string    `json:"taskSQL"`
	TaskMode     string    `json:"taskMode"`
	LastRunTime  time.Time `json:"lastRunTime"`
	TaskId       int       `json:"taskId"`
	Comment      string    `json:"comment"`
}

type StartSpaCollectReq struct {
	TaskId    int `json:"-"`
	ChannelId int `json:"-"`
}

type StartSQLFileReplayReq struct {
	TaskId    int `json:"-"`
	ChannelId int `json:"-"`
}

type StartSQLFileReplayResp struct {
}

type CallSpaCollectReq struct {
	ChannelId int `json:"-"`
	TaskId    int `json:"-"`
	CallType  int `json:"callType" validate:"required,oneof=0 1 2" enums:"0,1,2"`
}

type GetConfigurationReq struct {
	ChannelId int `json:"-"`
	TaskId    int `json:"-"`
}

type GetConfigurationResp struct {
	AppSQLsSource        string    `json:"appSQLsSource"`
	AppSQLsFilename      string    `json:"appSQLsFilename"`
	SqlFileType          string    `json:"sqlFileType"`
	IgnoreSQLIDsFilename string    `json:"ignoreSQLIDsFilename"`
	ExplainOnly          string    `json:"explainOnly"`
	SQLFactor            SQLFactor `json:"SQLFactor"`
}

type SQLFactor struct {
	SQLOrderBy     string `json:"sql_order_by"`
	AnalyzeTimeout int    `json:"analyze_timeout"`

	IncludingSQLTypes []string `json:"including_sql_types"`
	IncludingSchemas  []string `json:"including_schemas"`
	ExcludeModules    []string `json:"exclude_modules"`
	ExcludeSQLTexts   []string `json:"exclude_sqltxts"`
	ExcludeSQLIDs     []string `json:"exclude_sqlids"`
}

type StopSpaCollectReq struct {
	TaskId    int `json:"-"`
	ChannelId int `json:"-"`
}

type ListSQLAnalyzeTaskJobsReq struct {
	TaskId int `json:"-"`
}

type GetTaskDeployStatusReq struct {
	TaskId int `json:"-"`
}

type GetSQLSetsReq struct {
	TaskId     int `json:"-"`
	ChannelId  int `json:"-"`
	SourceType int `json:"sourceType" form:"sourceType"`
}

type GetSQLSetStatementsReq struct {
	TaskId      int    `json:"-"`
	SQLSetName  string `json:"sqlSetName" form:"sqlSetName"`
	SchemaName  string `json:"schemaName" form:"schemaName"`
	SQLSetOwner string `json:"sqlSetOwner" form:"sqlSetOwner"`
	SourceType  int    `json:"sourceType" form:"sourceType"`
	PageRequest
}

type StartSpaCollectResp struct {
	Message string `json:"message"`
}
type CallSpaCollectResp struct {
	Message string `json:"message"`
}

type SQLSet struct {
	DataSourceName string `json:"dataSourceName"`
	SQLSetName     string `json:"sqlSetName"`
	SQLSetOwner    string `json:"sqlSetOwner"`
	SchemaName     string `json:"schemaName"`
	StatementCount uint64 `json:"sqlCount"`
}

type SQLSetStatementCount struct {
	DataSourceName string `json:"dataSourceName"`
	SQLSetName     string `json:"sqlSetName"`
	SQLSetOwner    string `json:"sqlSetOwner"`
	StatementCount uint64 `json:"sqlCount"`
}

type GetSQLSetsResp struct {
	SQLSets []SQLSet `json:"sqlSets"`
}

type GetSQLSetsStatementCountResp struct {
	SQLSets []SQLSetStatementCount `json:"sqlSets"`
}

type SQLSetStatement struct {
	Index       int     `json:"index"`
	SQLText     string  `json:"sqlText"`
	SQLId       string  `json:"sqlId"`
	PerExecTime float64 `json:"perExecTime"`
	Executions  int     `json:"executions"`
	SchemaName  string  `json:"schemaName"`
}

type GetSQLSetStatementsResp struct {
	Statements []SQLSetStatement `json:"statements"`
}

type StopSpaCollectResp struct {
	Message string `json:"message"`
}

type GetTaskDeployStatusResp struct {
	IsDeployed bool `json:"isDeployed"`
}

type ListSQLAnalyzeTaskJobsResp struct {
	CurrentJob TaskDbmsJobStatus `json:"currentJob"`
	HistoryJob TaskDbmsJobStatus `json:"historyJob"`

	StartJob bool `json:"startJob"`
	StopJob  bool `json:"stopJob"`
}

type TaskDbmsJobStatus struct {
	JobId       string `json:"jobId"`
	JobNextDate string `json:"jobNextDate"`
	Broken      string `json:"broken"`
}

// User operation requests and responses
type UpdateSqlResultUserOperationReq struct {
	TaskId            int      `json:"taskId" validate:"required"`
	OraSqlIds         []string `json:"oraSqlIds" validate:"required"` // Unified for both V1 and V2
	UserOperateStatus string   `json:"userOperateStatus" validate:"required,oneof=normal ignored resolved"`
	UserOperateRemark string   `json:"userOperateRemark"`
}

type UpdateSqlResultUserOperationResp struct {
	UpdatedCount int `json:"updatedCount"`
}

type ListSqlResultsWithPaginationReq struct {
	TaskId               int      `json:"taskId"`
	Schemas              []string `json:"schemas"`
	StatusList           []string `json:"statusList"`
	UserOperateStatus    []string `json:"userOperateStatus"`
	OraSqlId             string   `json:"oraSqlId"`
	OraParsingSchemaName string   `json:"oraParsingSchemaName"`
	OraSqlText           string   `json:"oraSqlText"`
	PageRequest
}

type SqlResultWithUserOperation struct {
	SqlExecId            int        `json:"sqlExecId"`
	ChannelId            int        `json:"channelId"`
	TaskId               int        `json:"taskId"`
	OraSqlId             string     `json:"oraSqlId"`
	OraSqlText           string     `json:"oraSqlText"`
	OraParsingSchemaName string     `json:"oraParsingSchemaName"`
	OraModule            string     `json:"oraModule"`
	OraExecutions        int        `json:"oraExecutions"`
	OraElapsedTimeMs     float64    `json:"oraElapsedTimeMs"`
	TidbSqlType          string     `json:"tidbSqlType"`
	TidbSqlText          string     `json:"tidbSqlText"`
	TidbExecStatus       string     `json:"tidbExecStatus"`
	TidbExecCode         string     `json:"tidbExecCode"`
	TidbExecMsg          string     `json:"tidbExecMsg"`
	TidbElapsedTimeMs    int64      `json:"tidbElapsedTimeMs"`
	UserOperateStatus    string     `json:"userOperateStatus"`
	UserOperateAt        *time.Time `json:"userOperateAt"`
	UserOperateRemark    string     `json:"userOperateRemark"`
}

type ListSqlResultsWithPaginationResp struct {
	SqlResults []SqlResultWithUserOperation `json:"sqlResults"`
	Page
}

type GetSqlResultStatisticsReq struct {
	TaskId  int      `json:"taskId" validate:"required"`
	Schemas []string `json:"schemas"`
}

type GetSqlResultStatisticsResp struct {
	Statistics map[string]int64 `json:"statistics"`
}

// History tracking requests and responses
type GetSqlResultHistoryReq struct {
	TaskId    int    `json:"taskId"`
	SqlExecId int    `json:"sqlExecId"` // Optional: for backwards compatibility
	OraSqlId  string `json:"oraSqlId"`  // Primary identifier
}

type SqlResultHistoryItem struct {
	Id             int        `json:"id"`
	SqlExecId      int        `json:"sqlExecId"`
	TaskId         int        `json:"taskId"`
	OperationType  string     `json:"operationType"`
	PreviousStatus string     `json:"previousStatus"`
	NewStatus      string     `json:"newStatus"`
	OperateAt      *time.Time `json:"operateAt"`
	OperateRemark  string     `json:"operateRemark"`
	ClientIP       string     `json:"clientIp"`
}

type GetSqlResultHistoryResp struct {
	History []SqlResultHistoryItem `json:"history"`
}
