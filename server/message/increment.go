package message

import "time"

type ListAllDsgTaskReq struct {
	DsgTaskId int    `json:"dsgTaskId"`
	TaskName  string `json:"taskName"`
	PageRequest
}

type SCNType string

const (
	SCNType_SCN  SCNType = "scn"
	SCNType_TIME SCNType = "time"
)

type GetInstallationInfoReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type GetSourceConfigReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type GetTableMappingReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type VerifyTableMappingReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type UpdateTableMappingReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type UpdateSourceConfigReq struct {
	TaskId int    `json:"taskId" form:"taskId" validate:"required"`
	VM     string `json:"vm" form:"vm" validate:"required"`
}

type UpdateTargetConfigReq struct {
	TaskId     int     `json:"taskId" form:"taskId" validate:"required"`
	OldYLoader YLoader `json:"oldYLoader" form:"oldYLoader" validate:"required"`
	NewYLoader YLoader `json:"newYLoader" form:"newYLoader" validate:"required"`

	TmsYLoaderWrapper YLoaderWrapper `json:"tmsYLoaderWrapper" form:"tmsYLoaderWrapper"`
}

type YLoaderWrapper struct {
	TmsRealInsertBindNum int    `json:"tms_real_insert_bind_num"  validate:"required,min=1,max=1000"`
	TmsRealDeleteBindNum int    `json:"tms_real_delete_bind_num"  validate:"required,min=1,max=1000"`
	TmsRealUpdateBindNum int    `json:"tms_real_update_bind_num"  validate:"required,min=1,max=1000"`
	TmsRealInsertMode    string `json:"tms_real_insert_mode"  validate:"required,oneof=batch_sql_mode sql_mode copy_mode" enums:"batch_sql_mode,sql_mode,copy_mode"`
}

type GetTargetConfigReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type GetTokenReq struct {
	EnableSimulate bool   `json:"enableSimulate" form:"enableSimulate"`
	UserAgent      string `json:"userAgent" form:"userAgent"`
	AcceptLanguage string `json:"acceptLanguage" form:"acceptLanguage"`
	AcceptEncoding string `json:"acceptEncoding" form:"acceptEncoding"`
	AcceptCharset  string `json:"acceptCharset" form:"acceptCharset"`
}

type ListPhysicalSubSystemReq struct {
	GroupMachineName string `json:"groupMachineName"`
	GroupMachineDesc string `json:"groupMachineDesc"`
	PageRequest
}

type ListPhysicalSubSystemResp struct {
	PhysicalSubSystems []PhysicalSubSystem `json:"physicalSubSystems"`
	Total              int                 `json:"total"`
}

type GetTokenResp struct {
	Token string `json:"token"`
}

type PhysicalSubSystem struct {
	CreateTime       string `json:"createTime"`
	GroupMachineName string `json:"groupMachineName"`
	GroupMachineDesc string `json:"groupMachineDesc"`
	ID               int    `json:"id"`
	GroupType        string `json:"groupType"`
}

type Host struct {
	HostIP           string `json:"hostIp"`
	Allocation       string `json:"allocation"`
	PsName           string `json:"psName"`
	XcmpFlag         int    `json:"xcmpFlag"`
	IsAllocation     string `json:"isAllocation"`
	MacID            int    `json:"macId"`
	HostType         string `json:"hostType"`
	AffDataCenter    string `json:"affDataCenter"`
	SecretName       string `json:"secretName"`
	HostStat         int    `json:"hostStat"`
	CreateTime       string `json:"createTime"`
	AllocationPort   string `json:"allocationPort"`
	PhysicalSystemID int    `json:"physicalSystemId"`
	OSName           string `json:"osName"`
	HostName         string `json:"hostName"`
}

type ListHostResp struct {
	Hosts []Host `json:"hosts"`
	Total int    `json:"total"`
}

type ListDataSourceResp struct {
	DataSources []DataSource `json:"dataSource"`
	Total       int          `json:"total"`
}

type ListHostReq struct {
	PhysicalSystemId int `json:"physicalSystemId"`

	SortOrder     string `json:"sortOrder"`
	UserID        int    `json:"userId"`
	AffDataCenter string `json:"affDataCenter"`
	HostIP        string `json:"hostIp"`
	HostName      string `json:"hostName"`
	UserName      string `json:"userName"`
	HostPort      string `json:"hostPort"`
	OsName        string `json:"osName"`
	SecretName    string `json:"secretName"`
	Allocation    string `json:"allocation"`

	PageRequest
}

type ListDataSourceReq struct {
	PhysicalSystemId int    `json:"physicalSystemId"`
	UserID           int    `json:"userId"`
	MachineIp        string `json:"machineIp"`
	PageRequest
}

type BaseDsgResp struct {
	Message  string `json:"message"`
	StatFlag int    `json:"statFlag"`
}

type StartDsgTaskResp struct {
	BaseDsgResp
}

type StartSyncDsgTaskResp struct {
	BaseDsgResp
}

type PauseSyncDsgTaskResp struct {
	BaseDsgResp
}

type ResumeSyncDsgTaskResp struct {
	BaseDsgResp
}

type ClearCacheResp struct {
	BaseDsgResp
}

type DeleteDsgTaskResp struct {
	BaseDsgResp
}

type StopDsgTaskResp struct {
	BaseDsgResp
}

type GetInstallDsgTaskStatusResp struct {
	BaseDsgResp
	Success string `json:"success"`
}

type GetHostUsedPortsResp struct {
	BaseDsgResp
	UsedPortMin int        `json:"usedPortMin"`
	UsedPortMax int        `json:"usedPortMax"`
	Ports       []HostPort `json:"ports"`
}

type GetDsgTaskOperationLogsResp struct {
	Logs DsgOperationLogs `json:"logs"`
}

type GetDsgTaskMonitorInfoResp struct {
	BaseDsgResp
	DsgTaskName      string `json:"dsgTaskName"`
	IncrementId      int    `json:"incrementId"`
	SyncInfo         string `json:"syncInfo"`
	SyncStatus       string `json:"syncStatus"`
	ResourceInfo     string `json:"resourceInfo"`
	StartMigrateTime string `json:"startMigrateTime"`
	StopMigrateTime  string `json:"stopMigrateTime"`

	ResourceInfoRate ResourceInfoRate `json:"resourceInfoRate"`
}

type GetDsgTaskWarningInfoResp struct {
	BaseDsgResp
	DsgTaskName string `json:"dsgTaskName"`
	IncrementId int    `json:"incrementId"`

	WarningInfos WarningInfos `json:"warningInfos"`
}

type WarningInfos struct {
	Warnings []Warning `json:"warnings"`
	Total    int       `json:"total"`
}

type Warning struct {
	DataSourceDirection string `json:"data_source_direction"`
	SendEmalFlag        int    `json:"send_emal_flag"`
	UpdateTime          string `json:"update_time"`
	ErrorMsg            string `json:"error_msg"`
	TimeStamp           string `json:"time_stamp"`
	ErrorType           string `json:"error_type"`
	NodeName            string `json:"node_name"`
	TargetType          string `json:"target_type"`
	ErrorCode           int    `json:"error_code"`
	MsgLen              int    `json:"msg_len"`
	ErrorStatus         int    `json:"error_status"`
	MsgMD5              string `json:"msg_md5"`
	ErrorCount          int    `json:"error_count"`
}

type GetDsgTaskPerformanceStatResp struct {
	BaseDsgResp
	DsgTaskName string `json:"dsgTaskName"`
	IncrementId int    `json:"incrementId"`

	TableSummaries []DsgTableSummary `json:"tableSummaries"`
}

type GetDsgTaskPerformanceStatDetailResp struct {
	BaseDsgResp
	DsgTaskName string `json:"dsgTaskName"`
	IncrementId int    `json:"incrementId"`

	TablePerformances []DsgTablePerformance `json:"tablePerformances"`
}

type GetDsgTaskPerformanceStatTopResp struct {
	BaseDsgResp
	DsgTaskName string `json:"dsgTaskName"`
	IncrementId int    `json:"incrementId"`

	TableTopPerformances []DsgTableTopPerformance `json:"tableTopPerformances"`
}

type DsgTablePerformance struct {
	SchemaName string                    `json:"schemaName"`
	TableName  string                    `json:"tableName"`
	Items      []DsgTablePerformanceItem `json:"items"`
}

type DsgTableTopPerformance struct {
	SchemaName string                  `json:"schemaName"`
	TableName  string                  `json:"tableName"`
	Item       DsgTablePerformanceItem `json:"item"`
}

type DsgTablePerformanceItem struct {
	InsertNum      int64  `json:"insertNum"`
	UpdateNum      int64  `json:"updateNum"`
	DeleteNum      int64  `json:"deleteNum"`
	DdlNum         int64  `json:"ddlNum"`
	TotalChangeNum int64  `json:"totalChangeNum"`
	IntervalMinute string `json:"intervalMinute,omitempty"`
}

type DsgTableSummary struct {
	SchemaName     string `json:"schemaName"`
	TableName      string `json:"tableName"`
	TotalInsertNum int64  `json:"totalInsertNum"`
	TotalUpdateNum int64  `json:"totalUpdateNum"`
	TotalDeleteNum int64  `json:"totalDeleteNum"`
	TotalDdlNum    int64  `json:"totalDdlNum"`
	TotalChangeNum int64  `json:"totalChangeNum"`
}

type DsgFileStat struct {
	RealSpeed  float64 `json:"realSpeed"`
	QNO        int     `json:"qno"`
	UsedTime   float64 `json:"usedTime"`
	NodeName   string  `json:"nodeName"`
	NotLoadNum int     `json:"notLoadNum"`
	SizeDsg    int     `json:"sizeDsg"`
	FileNo     int     `json:"fileNo"`
	DelayTime  float64 `json:"delayTime"`
}

type DsgSyncStat struct {
	SourceDatabaseSCN       string `json:"sourceDatabaseScn"`
	SourceDatabaseTimestamp string `json:"sourceDatabaseTimestamp"`
	SourceCurrentSCN        string `json:"sourceCurrentScn"`
	SourceCurrentTimestamp  string `json:"sourceCurrentTimestamp"`

	InitialSourceSyncTime string  `json:"initialSourceSyncTime"`
	LastSourceSyncTime    string  `json:"lastSourceSyncTime"`
	LastTargetLoadTime    string  `json:"lastTargetLoadTime"`
	LastTargetLoadSCNTime string  `json:"lastTargetLoadSCNTime"`
	DelaySecond           float64 `json:"delaySecond"`

	InitialSyncTime string `json:"initialSyncTime"`
	LastSyncTime    string `json:"lastSyncTime"`
}

type ResourceInfoRate struct {
	SourceCPU    string `json:"sourceCPU"`
	SourceDisk   string `json:"sourceDisk"`
	SourceMemory string `json:"sourceMemory"`
	TargetCPU    string `json:"targetCPU"`
	TargetDisk   string `json:"targetDisk"`
	TargetMemory string `json:"targetMemory"`
}

type InstallDsgTaskResp struct {
	BaseDsgResp
}

type StopDsgTaskReq struct {
	TaskId int `json:"taskId" validate:"required"`
}

type GetInstallDsgTaskStatusReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type GetDsgTaskMonitorInfoReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type GetDsgTaskOperationLogsReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
	PageRequest
}

type GetDsgTaskWarningInfoReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type GetDsgTaskPerformanceStatReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
	PageRequest
}

type GetDsgTaskPerformanceStatDetailReq struct {
	TaskId     int    `json:"taskId" form:"taskId" validate:"required"`
	SchemaName string `json:"schemaName" form:"schemaName"`
	TableName  string `json:"tableName" form:"tableName"`

	AvgMinute    int `json:"avgMinute" form:"avgMinute" validate:"required,min=1,max=1440"`
	IntervalHour int `json:"intervalHour" form:"intervalHour" validate:"required,min=1,max=168"`
}

type GetDsgTaskPerformanceStatTopReq struct {
	TaskId     int    `json:"taskId" form:"taskId" validate:"required"`
	SchemaName string `json:"schemaName" form:"schemaName"`
	TableName  string `json:"tableName" form:"tableName"`
	SortBy     string `json:"sortBy" form:"sortBy" validate:"required,oneof=INSERT UPDATE DELETE DDL TOTAL" enums:"INSERT,UPDATE,DELETE,DDL,TOTAL"`
	TopNum     int    `json:"topNum" form:"topNum" validate:"required,min=1,max=200"`

	IntervalHour int `json:"intervalHour" form:"intervalHour" validate:"required,min=1,max=168"`
}

type InstallDsgTaskReq struct {
	TaskId int `json:"taskId" validate:"required"`

	SourceIP   string `json:"sourceIp" validate:"required"`
	SourcePath string `json:"sourcePath" validate:"required"`
	TargetIP   string `json:"targetIp" validate:"required"`
	TargetPath string `json:"targetPath" validate:"required"`
}

type StartDsgTaskReq struct {
	TaskId int `json:"taskId" validate:"required"`
}

type GetDsgTaskReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required,gt=0"`
}

type GetDsgTaskSyncDetailReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required,gt=0"`
}

type DeleteDsgTaskReq struct {
	TaskId int `json:"taskId" validate:"required"`
}

type StartSyncDsgTaskReq struct {
	TaskId   int     `json:"taskId" validate:"required"`
	SCNType  SCNType `json:"scnType" validate:"oneof=scn time" enums:"scn,time"`
	SCNValue string  `json:"scnValue" validate:"required"`
}

type PauseSyncDsgTaskReq struct {
	TaskId int `json:"taskId" validate:"required"`
}

type ResumeSyncDsgTaskReq struct {
	TaskId int `json:"taskId" validate:"required"`

	ChangeSCN     bool    `json:"changeScn"`
	ScnInputValue string  `json:"scnInputValue"`
	ScnType       SCNType `json:"scnType"`
}

type ClearCacheReq struct {
	TaskId int `json:"taskId" validate:"required"`
}

type DownloadSupplementalLogReq struct {
	TaskId int `json:"taskId" form:"taskId" validate:"required"`
}

type ListAllDsgTaskResp struct {
	Tasks []DsgTask `json:"tasks"`
	Total int       `json:"total"`
}

type GetDsgTaskResp struct {
	DsgTask     DsgTask                 `json:"dsgTask"`
	DsgTaskTime DsgTaskTime             `json:"taskTime"`
	SyncOption  IncrementTaskSyncOption `json:"syncOption"`
	SyncStat    DsgSyncStat             `json:"syncStat"`
	FileStat    DsgFileStat             `json:"fileStat"`
}

type GetSourceConfigResp struct {
	VM string `json:"vm"`
}

type DSGTableDefine struct {
	Real string `json:"real"`
	Map  string `json:"map"`
	Full string `json:"full"`
}

type DsgTable struct {
	SchemaNameS string `json:"schemaNameS"`
	TableNameS  string `json:"tableNameS"`
	SchemaNameT string `json:"schemaNameT"`
	TableNameT  string `json:"tableNameT"`
}

type GetTableMappingResp struct {
	Define  DSGTableDefine `json:"define"`
	Mapping []DsgTable     `json:"mappings"`
}

type VerifyTableMappingResp struct {
	IsTmsHasNoTables bool       `json:"isTmsHasNoTables"`
	HasMissingTables bool       `json:"hasMissingTables"`
	DsgMissingTables []DsgTable `json:"dsgMissingTables"`
	TmsMissingTables []DsgTable `json:"tmsMissingTables"`

	HasMissingOracleSupplementalLog    bool       `json:"hasMissingOracleSupplementalLog"`
	MissingOracleSupplementalLogTables []DsgTable `json:"missingOracleSupplementalLogTables"`

	HasMissingDBPSView bool     `json:"hasMissingDBPSView"`
	MissingDBPSView    []string `json:"missingDBPSView"`
}

type UpdateSourceConfigResp struct {
	BaseDsgResp
}

type UpdateTableMappingResp struct {
	BaseDsgResp
}

type UpdateTargetConfigResp struct {
	BaseDsgResp
}

type GetTargetConfigResp struct {
	YLoader           YLoader        `json:"yloader"`
	YLoaderContent    string         `json:"yloader_content"`
	TmsYLoaderWrapper YLoaderWrapper `json:"tmsYLoaderWrapper" form:"tmsYLoaderWrapper"`
}

type YLoader struct {
	RealDispFwait              string `json:"real_disp_fwait"`
	FullRepairRealQuit         string `json:"full_repair_real_quit"`
	UsedMap                    string `json:"used_map"`
	DelayInterval              string `json:"delay_interval"`
	RealDispSize               string `json:"real_disp_size"`
	FullCopyUnusedSize         string `json:"full_copy_unused_size"`
	UpdatePkukCol              string `json:"update_pkuk_col"`
	WaitFreeSize               string `json:"wait_free_size"`
	WaitRetry                  string `json:"wait_retry"`
	SQLTrans                   string `json:"sql_trans"`
	RealInsertErrFdelete       string `json:"real_insert_err_fdelete"`
	ErrorRetryDdl              string `json:"error_retry_ddl"`
	TableCreateFullIdxEnd      string `json:"table_create_full_idx_end"`
	AddcolRowidInvisible       string `json:"addcol_rowid_invisible"`
	ErrorRetry                 string `json:"error_retry"`
	FilterString00             string `json:"filter_string_00"`
	PackForJava                string `json:"pack_for_java"`
	TableExistsFullDo          string `json:"table_exists_full_do"`
	TableExistsRealDo          string `json:"table_exists_real_do"`
	DbLang                     string `json:"db_lang"`
	RealLoadCompleted          string `json:"real_load_completed"`
	RealDispFcount             string `json:"real_disp_fcount"`
	CountsumDetailDb           string `json:"countsum_detail_db"`
	FullInsertErrFdelete       string `json:"full_insert_err_fdelete"`
	FullCopy                   string `json:"full_copy"`
	FullSingleParr             string `json:"full_single_parr"`
	CountsumIntervalDb         string `json:"countsum_interval_db"`
	RealDispMode               string `json:"real_disp_mode"`
	CtypeIfxDecimal            string `json:"ctype_ifx_decimal"`
	FullCindexErrorRetry       string `json:"full_cindex_error_retry"`
	CreateTableFcol            string `json:"create_table_fcol"`
	SesBlen                    string `json:"ses_blen"`
	DbName                     string `json:"db_name"`
	XsqlRows                   string `json:"xsql_rows"`
	FullLoadCompleted          string `json:"full_load_completed"`
	MaxBrows                   string `json:"max_brows"`
	RealDdlFtable              string `json:"real_ddl_ftable"`
	MapCname                   string `json:"map_cname"`
	TableCreateRealConstraint  string `json:"table_create_real_constraint"`
	RowmapType                 string `json:"rowmap_type"`
	TableCreateFullIndex       string `json:"table_create_full_index"`
	MapTname                   string `json:"map_tname"`
	Trans                      string `json:"trans"`
	DbType                     string `json:"db_type"`
	SQLErrChange2Bind          string `json:"sql_err_change2bind"`
	CtypeNumberAdjust          string `json:"ctype_number_adjust"`
	RealThread                 string `json:"real_thread"`
	RealDispRows               string `json:"real_disp_rows"`
	TableCreateRealIndex       string `json:"table_create_real_index"`
	LangGbk2Gb18030            string `json:"lang_gbk2gb18030"`
	CountsumTime               string `json:"countsum_time"`
	DbPwd                      string `json:"db_pwd"`
	CountsumDetail             string `json:"countsum_detail"`
	TableCreateFullConstraint  string `json:"table_create_full_constraint"`
	UsedQuotes                 string `json:"used_quotes"`
	FullInsertFdelete          string `json:"full_insert_fdelete"`
	DbHost                     string `json:"db_host"`
	TableCreateFullIndexThread string `json:"table_create_full_index_thread"`
	Map                        string `json:"map"`
	DataFormat                 string `json:"data_format"`
	RealDispFi                 string `json:"real_disp_fi"`
	FullThread                 string `json:"full_thread"`
	EncryptPwd                 string `json:"encrypt_pwd"`
	FullSQLMode                string `json:"full_sql_mode"`
	FilterString               string `json:"filter_string"`
	TableCreateFullIdxPar      string `json:"table_create_full_idx_par"`
	UpdateNobefore             string `json:"update_nobefore"`
	TableExistsCheck           string `json:"table_exists_check"`
	Home                       string `json:"home"`
	TableCreateReal            string `json:"table_create_real"`
	QueueName                  string `json:"queue_name"`
	OutputLog2Db               string `json:"output_log2db"`
	DefineConfig               string `json:"defineConfig"`
	Service                    string `json:"service"`
	FullCopySize               string `json:"full_copy_size"`
	DbUser                     string `json:"db_user"`
	TableCreateFull            string `json:"table_create_full"`
	CfgFilename                string `json:"cfg_filename"`
	CreateTableAddcol          string `json:"create_table_addcol"`
	RealUpdateFobjn            string `json:"real_update_fobjn"`
	RealInsertFobjn            string `json:"real_insert_fobjn"`
	IdbLang                    string `json:"idb_lang"`
	OutputRba                  string `json:"output_rba"`
	McSchema                   string `json:"mc_schema"`
	CountsumTotalDb            string `json:"countsum_total_db"`
	CountsumFullDb             string `json:"countsum_full_db"`
	UpdateUsedDi               string `json:"update_used_di"`
	RealInsertFdMode           string `json:"real_insert_fd_mode"`
	RealInsertFdelete          string `json:"real_insert_fdelete"`
	UpdateErrUsedDi            string `json:"update_err_used_di"`
	AddcolRowidColumn          string `json:"addcol_rowid_column"`
	AddcolRowid2Uk             string `json:"addcol_rowid2uk"`
	UpdateFilterCol            string `json:"update_filter_col"`
	UnusedPkuk                 string `json:"unused_pkuk"`
	FullLangFromForce          string `json:"full_lang_from_force"`
	CtypeVarchar2Binary        string `json:"ctype_varchar2binary"`
	CtypeVarchar2Long          string `json:"ctype_varchar2long"`
	TableCreateIncPk           string `json:"table_create_inc_pk"`
	FullDdlFilter              string `json:"full_ddl_filter"`
	RealDdlFobjn               string `json:"real_ddl_fobjn"`
	RealDeleteFobjn            string `json:"real_delete_fobjn"`
	RealCopy                   string `json:"real_copy"`
	RealSqlMode                string `json:"real_sql_mode"`
	FullDdlFtable              string `json:"full_ddl_ftable"`
	FullInsertFtable           string `json:"full_insert_ftable"`
	RealDeleteFtable           string `json:"real_delete_ftable"`
	Ftable                     string `json:"ftable"`
	RealUpdateFtable           string `json:"real_update_ftable"`
	RealInsertFtable           string `json:"real_insert_ftable"`
}

type GetInstallationInfoResp struct {
	Host                            string `json:"host"`
	Port                            int    `json:"port"`
	AutoMaticEngineBootPort         int    `json:"autoMaticEngineBootPort"`
	ExternalHost                    string `json:"externalHost"`
	ExternalPort                    int    `json:"externalPort"`
	ExternalAutoMaticEngineBootPort int    `json:"externalAutoMaticEngineBootPort"`
	SourceIp                        string `json:"sourceIp"`
	TargetIp                        string `json:"targetIp"`
}

type IncrementTaskSyncOption struct {
	TaskId            int    `json:"taskId"`
	DsgTaskName       string `json:"dsgTaskName"`
	ScnType           string `json:"scnType"`
	ScnValue          string `json:"scnValue"`
	TimeValue         string `json:"timeValue"`
	ScnUserInputValue string `json:"scnUserInputValue"`
}

type DsgTask struct {
	TaskID        int    `json:"taskId"`
	TaskName      string `json:"taskName"`
	TaskParam     string `json:"taskParam"`
	TaskType      string `json:"taskType"`
	TaskModel     string `json:"taskModel"`
	TaskMethod    string `json:"taskMethod"`
	InstallStatus string `json:"installStatus"`
	DelStatus     string `json:"delStatus"`
	TaskStatus    string `json:"taskStatus"`

	ReceivePort  string `json:"receivePort"`
	AnalysisPort string `json:"analysisPort"`
	TarPath      string `json:"tarPath"`
	SourPath     string `json:"sourPath"`
}

type DsgOperationLogs []DsgOperationLog

type DsgOperationLog struct {
	ID                int    `json:"id"`
	TmsOperationTitle string `json:"tmsOperationTitle"`
	OperationTitle    string `json:"operationTitle"`

	BusinessType    int    `json:"businessType"`
	OperationMethod string `json:"operationMethod"`
	RequestMethod   string `json:"requestMethod"`
	OperationID     int    `json:"operationId"`
	OperationName   string `json:"operationName"`
	RoleName        string `json:"roleName"`
	UsergroupName   string `json:"usergroupName"`
	OperationURL    string `json:"operationUrl"`
	OperationIP     string `json:"operationIp"`
	OperationSource string `json:"operationSource"`
	OperationParam  string `json:"operationParam"`
	JSONResult      string `json:"jsonResult"`
	OperationStatus int    `json:"operationStatus"`
	ErrorMsg        any    `json:"errorMsg"`
	OperationTime   string `json:"operationTime"`
	BeginTime       any    `json:"beginTime"`
	EndTime         any    `json:"endTime"`
	Limit           int    `json:"limit"`
	Offset          int    `json:"offset"`
	SortName        any    `json:"sortName"`
	UserID          any    `json:"userId"`
}

type DsgTaskTime struct {
	InstallBinaryTime string `json:"installBinaryTime"`
	StartBinaryTime   string `json:"startBinaryTime"`
	StopBinaryTime    string `json:"stopBinaryTime"`
	StartMigrateTime  string `json:"startMigrateTime"`
	StopMigrateTime   string `json:"stopMigrateTime"`
	PauseMigrateTime  string `json:"pauseMigrateTime"`
	ResumeMigrateTime string `json:"resumeMigrateTime"`

	// 不输出到前端
	LastStartBinaryTime  time.Time `json:"-"`
	LastStopBinaryTime   time.Time `json:"-"`
	LastStartMigrateTime time.Time `json:"-"`
	LastStopMigrateTime  time.Time `json:"-"`
}

type ListDataSourceHostReq struct {
	HostName string `json:"hostName" form:"hostName" `
	HostIP   string `json:"hostIp" form:"hostIp"`
	OsName   string `json:"osName" form:"osName"`
	PageRequest
}

type VerifyHostReq struct {
	HostIP string `json:"hostIp" form:"hostIp" validate:"required"`
}

type GetHostReq struct {
	MacID int `json:"macId" form:"macId"`
}

type GetHostUsedPortsReq struct {
	HostIP string `json:"hostIp" form:"hostIp" validate:"required"`
}

type DeleteHostReq struct {
	MacId int `json:"macId"`
}

type SaveHostReq struct {
	MacId       int       `json:"macId"`
	HostName    string    `json:"hostName"`
	HostIP      string    `json:"hostIp"`
	CreateTime  time.Time `json:"createTime"`
	PortMax     int       `json:"portMax"`
	PortMin     int       `json:"portMin"`
	PortHasUsed string    `json:"portHasUsed"`
	BasePath    string    `json:"basePath"`
	OsName      string    `json:"osName"`
	Allocation  bool      `json:"allocation"`
	Comment     string    `json:"comment"`
}

type ListDataSourceHostResp struct {
	Hosts []*OpMachineConf `json:"hosts"`
}

type SaveHostResp struct {
}

type DeleteHostResp struct {
}

type VerifyHostResp struct {
	HostIP     string `json:"hostIp"`
	IsValid    bool   `json:"isValid"`
	ErrMessage string `json:"errMessage"`
}

type OpMachineConf struct {
	MacId       int       `json:"macId"`
	HostName    string    `json:"hostName"`
	HostIP      string    `json:"hostIp"`
	CreateTime  time.Time `json:"createTime"`
	PortMax     int       `json:"portMax"`
	PortMin     int       `json:"portMin"`
	PortHasUsed string    `json:"portHasUsed"`
	BasePath    string    `json:"basePath"`
	OsName      string    `json:"osName"`
	Allocation  bool      `json:"allocation"`
	*BaseFields
}

type HostPort struct {
	Port     int    `json:"port"`
	Describe string `json:"describe"`
	Notes    string `json:"notes"`
}

type DownloadSupplementalLogResp struct {
	ExportFilePath string `json:"exportFilePath"`
}

func (i DownloadSupplementalLogResp) GetFilePath() string {
	return i.ExportFilePath
}
