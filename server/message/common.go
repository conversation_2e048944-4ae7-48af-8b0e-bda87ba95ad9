package message

import "time"

type Page struct {
	Page     int   `json:"current"`
	PageSize int   `json:"pageSize"`
	Total    int64 `json:"total"`
}

var DefaultPageRequest = PageRequest{
	1,
	20,
}

type PageRequest struct {
	Page     int `json:"page" validate:"required,min=1" form:"page"`
	PageSize int `json:"pageSize" validate:"required,min=1" form:"pageSize"`
}

type ResultMark struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type CommonResult struct {
	ResultMark
	Data interface{} `json:"data"`
}

type ResultWithPage struct {
	ResultMark
	Data interface{} `json:"data"`
	Page
}

type BaseFields struct {
	Comment   string    `json:"comment" validate:"max=1000"`
	CreatedAt time.Time `json:"createdAt" example:"RFC3339,2022-06-10T09:09:51.123+08:00"`
	UpdatedAt time.Time `json:"updatedAt" example:"RFC3339,2022-06-10T09:09:51.123+08:00"`
}

type TreeNode struct {
	Data     string      `json:"key"`
	Children []*TreeNode `json:"children"`
}

func (t *TreeNode) AddChild(node *TreeNode) {
	t.Children = append(t.Children, node)
}
