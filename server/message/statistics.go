package message

import (
	"time"
)

type StatisticsSummaryDetail struct {
	TaskId        int       `json:"taskId"`
	Schema        string    `json:"schema"`
	TotalNums     int       `json:"totalNums"`
	SuccessNums   int       `json:"successNums"`
	FailedNums    int       `json:"failedNums"`
	RunningNums   int       `json:"runningNums"`
	WaitingNums   int       `json:"waitingNums"`
	DuplicateNums int       `json:"duplicateNums"`
	SuccessRatio  string    `json:"successRatio"`
	Duration      string    `json:"compareDuration"`
	StartTime     time.Time `json:"StartTime"`
	EndTime       time.Time `json:"endTime"`
}

type StatisticsSummarySchemaReq struct {
	ChannelId int    `json:"channelId"`
	TaskId    int    `json:"taskId"`
	Schema    string `json:"schema"`
}

type StatisticsSummarySchemaResp struct {
	StatsSchemaSummary []*StatisticsSummaryDetail `json:"statsSchemaSummary"`
}

type StatisticsTablesReq struct {
	ChannelId int    `json:"channelId"`
	TaskId    int    `json:"taskId"`
	Schema    string `json:"schema"`
	Table     string `json:"table"`
	Page      int    `json:"page"`
	PageSize  int    `json:"pageSize"`
}

type StatisticsTablesResp struct {
	ChannelId   int                    `json:"channelId"`
	TaskId      int                    `json:"taskId"`
	Schema      string                 `json:"schema"`
	TableCount  int                    `json:"tablecount"`
	TableDetail []*TaskStatisticsTable `json:"tableDetail"`
}

type StatisticsSchemaStateReq struct {
	ChannelId int    `json:"channelId"`
	TaskId    int    `json:"taskId"`
	Schema    string `json:"schema"`
	State     string `json:"state" enums:"success,failed,waiting,running"`
	Page      int    `json:"page"`
	PageSize  int    `json:"pageSize"`
}

type StatisticsSchemaStateResp struct {
	ChannelId   int                            `json:"channelId"`
	TaskId      int                            `json:"taskId"`
	Schema      string                         `json:"schema"`
	State       string                         `json:"state"`
	TableCount  int                            `json:"tablecount"`
	TableDetail []*TaskStatisticsJoinDuplicate `json:"tableDetail"`
}

type TaskStatisticsTable struct {
	TaskStatsId          int       `json:"taskStatsId"`
	ChannelId            int       `json:"channelId"`
	TaskId               int       `json:"taskId"`
	SchemaName           string    `json:"schemaName"`
	TableName            string    `json:"tableName"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
	AnalyzeStatus        string    `json:"analyzeStatus"`
	Samplerate           float64   `json:"samplerate"`
	LastAnalyzedRows     uint64    `json:"lastAnalyzedRows"`
	AnalyzeDuration      string    `json:"analyzeDuration"`
	TableRows            uint64    `json:"tableRows"`
	Priority             int       `json:"priority"`
	Message              string    `json:"message"`
	*BaseFields
}

type TaskStatisticsJoinDuplicate struct {
	TaskStatsId          int       `json:"taskStatsId"`
	ChannelId            int       `json:"channelId"`
	TaskId               int       `json:"taskId"`
	SchemaName           string    `json:"schemaName"`
	TableName            string    `json:"tableName"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
	AnalyzeStatus        string    `json:"analyzeStatus"`
	AnalyzeDuration      string    `json:"analyzeDuration"`
	Samplerate           float64   `json:"samplerate"`
	LastAnalyzedRows     uint64    `json:"lastAnalyzedRows"`
	TableRows            uint64    `json:"tableRows"`
	Priority             int       `json:"priority"`
	Message              string    `json:"message"`
	DuplicateTasks       string    `json:"duplicateTasks"`
	DuplicateCount       int       `json:"duplicateCount"`
}

type InitTidbStatisticsReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type InitTidbStatisticsResp struct {
}

type UpdateTidbStatisticsReq struct {
	ChannelId   int                    `json:"channelId"`
	TaskId      int                    `json:"taskId"`
	StatsTables []*TaskStatisticsTable `json:"statsTables"`
}

type UpdateTidbStatisticsResp struct {
}

type StatisticsTaskTableDuplReq struct {
	ChannelId int    `json:"channelId"`
	TaskId    int    `json:"taskId"`
	Schema    string `json:"schema"`
	Page      int    `json:"page"`
	PageSize  int    `json:"pageSize"`
}

type StatisticsTaskTableDuplResp struct {
	ChannelId   int                                     `json:"channelId"`
	TaskId      int                                     `json:"taskId"`
	Schema      string                                  `json:"schema"`
	TableCount  int                                     `json:"tablecount"`
	TableDetail []*ChannelTaskSchemaStatisticsDuplicate `json:"tableDetail"`
}

type ChannelTaskSchemaStatisticsDuplicate struct {
	ChannelSchtableId    int       `json:"channelSchtableId"`
	ChannelId            int       `json:"channelId"`
	TaskId               int       `json:"taskId"`
	SchemaName           string    `json:"schemaName"`
	TableName            string    `json:"tableName"`
	LastAnalyzeStarttime time.Time `json:"lastAnalyzeStarttime"`
	LastAnalyzeEndtime   time.Time `json:"lastAnalyzeEndtime"`
	AnalyzeStatus        string    `json:"analyzeStatus"`
	Samplerate           float64   `json:"samplerate"`
	LastAnalyzedRows     int       `json:"lastAnalyzedRows"`
	AnalyzeDuration      string    `json:"analyzeDuration"`
	TableRows            int       `json:"tableRows"`
	Priority             int       `json:"priority"`
	Message              string    `json:"message"`
	DuplicateTasks       string    `json:"duplicateTasks"`
	DuplicateCount       int       `json:"duplicateCount"`
}

type DeleteStatsTaskConflictTablesReq struct {
	TaskId      int           `json:"taskId"`
	StatsTables []*StatsTable `json:"statsTables"`
}

type DeleteStatsTaskConflictTablesResp struct {
}

type StatsTable struct {
	SchemaName string `json:"schemaName"`
	TableName  string `json:"tableName"`
}

type GetTidbStatsTableCountReq struct {
	ChannelId   int           `json:"channelId"`
	TaskId      int           `json:"taskId"`
	StatsTables []*StatsTable `json:"statsTables"`
}

type GetTidbStatsTableCountResp struct {
}
