package message

type GetLicenseInfoResp struct {
	ExpiredTime   int64 `json:"expiredTime"`
	RemainingDays int64 `json:"remainingDays"`
}

type GenKeyResp struct {
	TimsKey string `json:"timsKey"`
}

type VersionResp struct {
	Version string `json:"version"`
}

type ListLicenseFeaturesReq struct {
}

type UpdateLicenseFeaturesReq struct {
	IncreaseOpProcedureNum bool `json:"increaseOpProcedureNum"`
	IncreaseOpFunctionNum  bool `json:"increaseOpFunctionNum"`
	IncreaseOpTriggerNum   bool `json:"increaseOpTriggerNum"`
	IncreaseOpPackageNum   bool `json:"increaseOpPackageNum"`
}

type UpdateLicenseFeaturesResp struct {
	BeforeLicenseFeatures []LicenseFeature `json:"beforeLicenseFeatures"`
	AfterLicenseFeatures  []LicenseFeature `json:"afterLicenseFeatures"`
}

type LicenseFeature struct {
	FeatureCode           string `json:"featureCode"`
	FeatureName           string `json:"featureName"`
	MaxUsageCount         uint   `json:"maxUsageCount"`
	CurrentUsageCount     uint   `json:"currentUsageCount"`
	LicenseKey            string `json:"-"`
	VerificationSignature string `json:"-"`
}
type ListLicenseFeaturesResp struct {
	LicenseFeatures []LicenseFeature `json:"licenseFeatures"`
}
