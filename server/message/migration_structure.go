/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package message

import (
	"time"
)

type MigrationTaskReq struct {
	TaskID int `json:"task_id"`
}

type MigrationSchemaReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"` // request used for get ddl detail page
	TableTypeS  string `json:"table_type_s"` // request used for get ddl detail page
}

type MigrationSchemaPageReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"` // request used for get ddl detail page
	TableTypeS  string `json:"table_type_s"` // request used for get ddl detail page
	Status      int    `json:"status"`
	PageRequest
}

type GetOriginDDLReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	TableNameS  string `json:"table_name_s"` // request used for get ddl detail page
}

type GetOracleDDLReq struct {
	TaskID      int    `json:"task_id"`
	SchemaNameS string `json:"schema_name_s"`
	ObjectNameS string `json:"object_name_s"` // request used for get ddl detail page
	ObjectTypeS string `json:"object_type_s"` // request used for get ddl detail page
}

type MigrationProgressReq struct {
	ChannelID         int       `json:"channel_id"`
	TaskID            int       `json:"task_id"`
	ProgressStartTime time.Time `json:"progress_start_time"` // request used for reverse progress bar and log
}

type WalkCSVDirReq struct {
	WalkPaths  []string `json:"walk_paths" validate:"required"`
	SchemaName string   `json:"schema_name"`
	TableName  string   `json:"table_name"`
	PageRequest
}

type MigrationDataStatisticReq struct {
	ChannelId uint `json:"channel_id" validate:"required"`
	TaskId    uint `json:"task_id" validate:"required"`
	PageRequest
}

type QueryMigrationDetailsReq struct {
	ChannelId  uint   `json:"channel_id" validate:"required"`
	TaskId     uint   `json:"task_id" validate:"required"`
	PathFilter string `json:"path_filter"`
	SchemaName string `json:"schema_name"`
	TableName  string `json:"table_name"`
	PageRequest
}

type UpdateMigrationDetailReq struct {
	ChannelId    int    `json:"channel_id" validate:"required"`
	TaskId       int    `json:"task_id" validate:"required"`
	ChunkIds     []uint `json:"chunk_ids" validate:"required"`
	ImportStatus string `json:"import_status" validate:"oneof=WAITING RUNNING SUCCESS FAILED IGNORE" enums:"WAITING,RUNNING,SUCCESS,FAILED,IGNORE"`
}

type SaveMigrationDataReq struct {
	TaskID          int      `json:"task_id" validate:"required"`
	WalkPaths       []string `json:"walk_paths" validate:"required"`
	TotalCSVFileNum int      `json:"total_csv_file_num" validate:"required"`

	ResetChannelSchemaTable bool `json:"reset_channel_schema_table"`
	ResetMigrationMeta      bool `json:"reset_migration_meta"`
}

type MigrationDDLReRunReq struct {
	TaskRunId    int    `json:"task_run_id"`
	TaskID       int    `json:"task_id"`
	SchemaNameS  string `json:"schema_name_s"`
	TableNameS   string `json:"table_name_s"` // request used for get ddl detail page
	TableTypeS   string `json:"table_type_s"`
	IsCompatible string `json:"is_compatible"`
	ReverseDDL   string `json:"reverse_ddl"` // request used for manual adjust ddl execute
	FixedDDL     string `json:"fixed_ddl"`   // request used for manual adjust ddl execute
}

type MigrationStructureTaskSummaryResp struct {
	TaskID               int                                   `json:"task_id"`
	TotalMigrationNums   int                                   `json:"total_migration_nums"`
	SuccessMigrationNums int                                   `json:"success_migration_nums"`
	FailedMigrationNums  int                                   `json:"failed_migration_nums"`
	RunningMigrationNums int                                   `json:"running_migration_nums"`
	WaitingMigrationNums int                                   `json:"waiting_migration_nums"`
	CompatibleNums       int                                   `json:"compatible_nums"`
	MigrationDetails     []MigrationStructureTaskSummaryDetail `json:"migration_details"`
}

type MigrationStructureDDLDetailResp struct {
	SchemaDDLS []MigrationStructureDDLDetail `json:"schema_ddls"`
}

type MigrationStructureSchemaSummaryResp struct {
	MigrationSchemaSummary []MigrationStructureSchemaSummary `json:"migration_schema_summary"`
}

type MigrationStructureDDLProgressResp struct {
	TaskID                      int                   `json:"task_id"`
	TotalMigrationNums          int                   `json:"total_migration_nums"`
	SuccessMigrationNums        int                   `json:"success_migration_nums"`
	PartialSuccessMigrationNums int                   `json:"partial_success_migration_nums"`
	FailedMigrationNums         int                   `json:"failed_migration_nums"`
	IncompatibleMigrationNums   int                   `json:"incompatible_migration_nums"`
	RunningMigrationNums        int                   `json:"running_migration_nums"`
	WaitingMigrationNums        int                   `json:"waiting_migration_nums"`
	ProgressLog                 []*MigrationLogDetail `json:"progress_log"`
	ProgressStartTime           time.Time             `json:"progress_start_time"`
	LastUpdateTime              time.Time             `json:"lastUpdateTime"`
}

type GetOriginDDLResp struct {
	OriginDDL string `json:"OriginDDL"`
}

type MigrationStructureTableDetailResp struct {
	MigrationStructureTableDetail []*MigrationStructureTableDetail `json:"migration_structure_table_detail"`
}

type MigrationStructureDDLReRunResp struct {
	TaskName string `json:"task_name"`
	FixedDDL string `json:"fixed_ddl"`
}

type MigrationStructureDDLDetail struct {
	SchemaNameS     string `json:"schema_name_s"`
	ReverseDDL      string `json:"reverse_ddl"`
	IncompatibleDDL string `json:"incompatible_ddl"`
}

type MigrationStructureTableDetail struct {
	TaskRunID         int       `json:"task_run_id"`
	ServiceNameS      string    `json:"service_name_s"`
	SchemaNameS       string    `json:"schema_name_s"`
	SchemaNameT       string    `json:"schema_name_t"`
	TableTypeS        string    `json:"table_type_s"`
	TableNameS        string    `json:"table_name_s"`
	ObjectNameS       string    `json:"object_name_s"`
	TableTypeT        string    `json:"table_type_t"`
	TableNameT        string    `json:"table_name_t"`
	Status            string    `json:"status"`
	OriginSQL         string    `json:"origin_sql"`
	ReverseSQL        string    `json:"reverse_sql"`
	FixedDDL          string    `json:"fixed_ddl"`
	ErrorDetail       string    `json:"error_detail"`
	Comment           string    `json:"comment"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	IncompatibleCount int       `json:"incompatible_count"`
	SuccessCount      int       `json:"success_count"`
	FailedCount       int       `json:"failed_count"`
}

type MigrationStructureSchemaSummary struct {
	ServiceName                 string `json:"service_name"`
	SchemaNameS                 string `json:"schema_name_s"`
	TableTypeS                  string `json:"table_type_s"`
	TableTypeT                  string `json:"table_type_t"`
	TotalMigrationNums          int    `json:"total_migration_nums"`
	SuccessMigrationNums        int    `json:"success_migration_nums"`
	FailedMigrationNums         int    `json:"failed_migration_nums"`
	RunningMigrationNums        int    `json:"running_migration_nums"`
	WaitingMigrationNums        int    `json:"waiting_migration_nums"`
	CompatibleNums              int    `json:"compatible_nums"`
	PartialSuccessMigrationNums int    `json:"partial_success_migration_nums"`
}

type MigrationStructureTaskSummaryDetail struct {
	ServiceName        string `json:"service_name"`
	SchemaName         string `json:"schema_name"`
	MigrationNums      int    `json:"migration_nums"`
	SuccessNums        int    `json:"success_nums"`
	PartialSuccessNums int    `json:"partial_success_nums"`
	FailedNums         int    `json:"failed_nums"`
	RunningNums        int    `json:"running_nums"`
	WaitingNums        int    `json:"waiting_nums"`
	CompatibleNums     int    `json:"compatible_nums"`
	SuccessRatio       string `json:"success_ratio"`
	MigrationDuration  string `json:"migration_duration"`
}

type MigrationLogDetail struct {
	ProgressID  int       `json:"progress_id"`
	ChannelID   int       `json:"channel_id"`
	TaskID      int       `json:"task_id"`
	SchemaNameS string    `json:"schema_name_s"`
	TableNameS  string    `json:"table_name_s"`
	Detail      string    `json:"detail"`
	Comment     string    `json:"comment"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	LogLevel    string    `json:"log_level"`
}

type MigrationDDLDownloadReq struct {
	TaskID      int      `json:"task_id"`
	SchemaNameS []string `json:"schema_name_s"`
}
