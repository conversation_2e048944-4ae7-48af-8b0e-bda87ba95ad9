package message

import "gitee.com/pingcap_enterprise/tms/common/structs"

type ParamTemplateDetail struct {
	TemplateId          int    `json:"templateId"`
	TaskparamTemplateId int    `json:"taskparamTemplateId" validate:"required,min=1"`
	ParamName           string `json:"paramName" validate:"required"`
	ParamValueDefault   string `json:"paramValueDefault"`

	HasSubParams          bool     `json:"hasSubParams"`
	SubParamValuesDefault []string `json:"subParamValuesDefault"`
	*BaseFields
}

type CreateOrUpdateTemplateParamDetailReq struct {
	*ParamTemplateDetail
}

type CreateOrUpdateTemplateParamDetailResp struct {
}

type ListTemplateParamDetailsReq struct {
	TaskparamTemplateId int `json:"taskparamTemplateId"`
}

type ListTemplateParamDetailsResp struct {
	Total  int                    `json:"total"`
	Params []*ParamTemplateDetail `json:"params"`
}

type BatchDeleteTemplateParamDetailsReq struct {
	TaskId      int   `json:"taskId" required:"true" validate:"required,min=1"`
	TemplateIds []int `json:"templateIds"`
}

type BatchDeleteTemplateParamDetailsResp struct {
}

type DeleteTabcolCustMapRulesReq struct {
	TabcolMapruleId int `json:"tabcolMapruleId"`
}

type TabcolCustMapRulesReq struct {
	TabcolMapruleId  int    `json:"tabcolMapruleId"`
	ChannelId        int    `json:"channelId"`
	TaskId           int    `json:"taskId"`
	SchemaNameS      string `json:"schemaNameS"`
	TableNameS       string `json:"tableNameS"`
	ColNameS         string `json:"colNameS"`
	ColNameT         string `json:"colNameT"`
	ColStrS          string `json:"colStrS"`
	ColStrT          string `json:"colStrT"`
	ColDefaultvalueS string `json:"colDefaultvalueS"`
	ColDefaultvalueT string `json:"colDefaultvalueT"`
}

type ParsingCSVResp struct {
	CsvSchemaTables []*structs.CsvSchemaTables `json:"CsvSchemaTables"`
}

type CommonResp struct {
}

type GetTabcolCustMapRulesReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
	RuleType  int `json:"ruleType"`
}

type TabcolCustMapRules struct {
	TabcolMapruleId  int    `json:"tabcolMapruleId"`
	ChannelId        int    `json:"channelId"`
	TaskId           int    `json:"taskId"`
	SchemaNameS      string `json:"schemaNameS"`
	TableNameS       string `json:"tableNameS"`
	ColNameS         string `json:"colNameS"`
	ColNameT         string `json:"colNameT"`
	ColStrS          string `json:"colStrS"`
	ColStrT          string `json:"colStrT"`
	ColDefaultvalueS string `json:"colDefaultvalueS"`
	ColDefaultvalueT string `json:"colDefaultvalueT"`
}

type GetTabcolCustMapRulesResp struct {
	Rules []*TabcolCustMapRules
}
