package message

import (
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
)

// ClusterHardware结构体复用models/datasource.ClusterHardware
type ClusterHardware = datasource.ClusterHardware

// 查询cluster_hardware表的响应结构
// 适用于多种查询类型，rows为通用结果，instances为distinct instance结果
// 只会返回其中一个字段，另一个为空
// 例如：{"rows": [...], "instances": null} 或 {"rows": null, "instances": ["xxx", ...]}
type GetClusterHardwareResp struct {
	Rows      []*ClusterHardware `json:"rows,omitempty"`
	Instances []string           `json:"instances,omitempty"`
}

type Channel struct {
	ChannelId          int    `json:"channelId"`
	ChannelName        string `json:"channelName" validate:"required,max=100"`
	ChannelType        string `json:"channelType" validate:"required,oneof=O2T M2T C2T O2M" enums:"O2T,M2T,C2T,O2M"`
	DatasourceIdS      int    `json:"datasourceIdS"`
	DatasourceNameS    string `json:"datasourceNameS" validate:"max=100"`
	DatasourceIdT      int    `json:"datasourceIdT"`
	DatasourceNameT    string `json:"datasourceNameT" validate:"max=100"`
	DatasourceCharsetS string `json:"datasourceCharsetS"` // 源数据源字符集
	DatasourceCharsetT string `json:"datasourceCharsetT"` // 目标数据源字符集
	ObjAssessment      string `json:"objAssessment" validate:"required,oneof=Y N" enums:"Y,N"`
	ObjParser          string `json:"objParser" `
	//ObjParser            string `json:"objParser" validate:"required,oneof=Y N" enums:"Y,N"`
	SqlAssessment               string `json:"sqlAssessment" validate:"required,oneof=Y N" enums:"Y,N"`
	MigrateStructure            string `json:"migrateStructure" validate:"required,oneof=Y N" enums:"Y,N"`
	MigrateFullData             string `json:"migrateFullData" validate:"required,oneof=Y N" enums:"Y,N"`
	MigrateCsvData              string `json:"migrateCsvData" validate:"required,oneof=Y N" enums:"Y,N"`
	DataCompare                 string `json:"dataCompare" validate:"required,oneof=Y N" enums:"Y,N"`
	Increment                   string `json:"increment" validate:"required,oneof=Y N" enums:"Y,N"`
	FullInitialization          string `json:"fullInitialization" enums:"Y,N"`
	MigrateFullAndIncrementData string `json:"migrateFullAndIncrementData" enums:"Y,N"`
	TaskCreated                 string `json:"taskCreated" validate:"max=1" enums:"Y,N"`
	ChannelMode                 string `json:"channelMode" validate:"max=10"`
	SQLAnalyzerEnvStatus        string `json:"sqlAnalyzerEnvStatus" validate:"max=10"`
	DataCompareEnvStatus        string `json:"dataCompareEnvStatus" validate:"max=10"`
	*BaseFields
}

type ChannelSchema struct {
	ChannelSchemaId int    `json:"channelSchemaId"`
	ChannelId       int    `json:"channelId"`
	TaskId          int    `json:"taskId"`
	DbNameS         string `json:"dbNameS" validate:"max=100"`
	SchemaNameS     string `json:"schemaNameS" validate:"required,min=1,max=200"`
	DbNameT         string `json:"dbNameT" validate:"max=200"`
	SchemaNameT     string `json:"schemaNameT" validate:"required,min=1,max=200"`
	*BaseFields
}

type ChannelSchemaObject struct {
	ChannelObjectId   int    `json:"channelObjectId"`
	TaskId            int    `json:"taskId" validate:"required"`
	ChannelId         int    `json:"channelId" validate:"required"`
	OnlyTableandindex string `json:"onlyTableandindex" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	OnlyTable         string `json:"onlyTable" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	OnlyIndex         string `json:"onlyIndex" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	Partitiontable    string `json:"partitiontable" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	AppendData        string `json:"appendData" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	ReloadData        string `json:"reloadData" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	LoadData          string `json:"loadData" validate:"omitempty,oneof=Y N" enums:"Y,N"`
	*BaseFields
}

type CreateOrUpdateChannelReq struct {
	*Channel
}
type CreateOrUpdateChannelResp struct {
	ChannelId int `json:"channelId"`
}

type DeleteChannelReq struct {
	ChannelId int `json:"channelId"`
}

type DeleteChannelResp struct {
}

type GetChannelReq struct {
	ChannelId int `json:"channelId"`
}

type GetChannelResp struct {
	*Channel
}

type ListChannelsReq struct {
	PageRequest
	ChannelName string `json:"channelName" validate:"omitempty,min=1,max=100"`
	ChannelType string `json:"channelType" validate:"omitempty,oneof=O2T M2T C2T O2M" enums:"O2T,M2T,C2T,O2M"`
}

type ListChannelsResp struct {
	Channels []*Channel
}

type CreateOrUpdateChannelObjectsReq struct {
	ChannelId      int `json:"-"`
	ChannelObjects []*ChannelSchemaObject
}
type CreateOrUpdateChannelObjectsResp struct {
}

type GetChannelObjectsReq struct {
	ChannelId int `json:"channelId"`
}
type GetChannelObjectsResp struct {
	ChannelObjects []*ChannelSchemaObject
}

type CreateOrUpdateChannelSchemasReq struct {
	ChannelId      int              `json:"-"`
	DatasourceIdS  int              `json:"datasourceIdS"`
	ChannelSchemas []*ChannelSchema `json:"channelSchemas"`
}
type CreateOrUpdateChannelSchemasResp struct {
}

type BatchDeleteChannelSchemasReq struct {
	ChannelSchemaIds []int `json:"channelSchemaIds" validate:"required,gt=0,lt=1000"`
	// DataSourceIDs    []int `json:"dataSourceIds" validate:"required,gt=0,lt=1000"`
}
type BatchDeleteChannelSchemasResp struct {
}

type GetChannelSchemasReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}
type GetChannelSchemasResp struct {
	ChannelSchemas []*ChannelSchema
}

type PrecheckInfo struct {
	PreCheckId      int    `json:"precheckId"`
	ChannelId       int    `json:"channelId" validate:"required"`
	CheckObject     string `json:"checkObject" validate:"required,oneof=A B C D E F G H" enums:"A(object compatible check priority),B(object migration priority),C(data migration priority),D(data compare priority),E(target privilege check),F(no index table),G(no pk/uk table),H(table have dependency relation)"`
	CheckStatus     string `json:"checkStatus" validate:"required,oneof=C P N" enums:"C(checking),P(pass),N(not pass)"`
	CheckIgnore     string `json:"checkIgnore" validate:"required,oneof=Y N" enums:"Y(ignored),N(not ignored)"`
	CheckResultInfo string `json:"checkResultInfor" validate:"max=1000"`
	*BaseFields
}
type ExecutionPreCheckReq struct {
	ChannelId      int             `json:"-"`
	PrecheckInfors []*PrecheckInfo `json:"precheckInfors" validate:"required,gt=0,lt=500"`
}
type ExecutionPreCheckResp struct {
}

type GetPreCheckInfosByChannelIdReq struct {
	ChannelId int `json:"channelId"`
}
type GetPreCheckInfosByChannelIdResp struct {
	PrecheckInfors []*PrecheckInfo `json:"precheckInfors"`
}

type UpdatePreCheckInfoReq struct {
	*PrecheckInfo
}
type UpdatePreCheckInfoResp struct {
}

type GetPreCheckInfoByIdReq struct {
	PreCheckId int `json:"preCheckId"`
}
type GetPreCheckInfoByIdResp struct {
	*PrecheckInfo
}

type ChannelSchemaTable struct {
	ChannelSchtableId     int     `json:"channelSchtableId"`
	ChannelId             int     `json:"channelId" validate:"required"`
	TaskId                int     `json:"taskId" validate:"required"`
	DbNameS               string  `json:"dbNameS" validate:"required,min=1,max=200"`
	SchemaNameS           string  `json:"schemaNameS" validate:"required,min=1,max=200"`
	TableNameS            string  `json:"tableNameS" validate:"required,min=1,max=200"`
	DbNameT               string  `json:"dbNameT" validate:"required,min=1,max=200"`
	SchemaNameT           string  `json:"schemaNameT" validate:"required,min=1,max=200"`
	TableNameT            string  `json:"tableNameT" validate:"required,min=1,max=200"`
	TaskType              int     `json:"taskType" validate:"required"`
	PartitioningTypeS     string  `json:"partitioningTypeS"`
	PartitioningCountS    int     `json:"partitioningCountS"`
	SubPartitioningTypeS  string  `json:"subPartitioningTypeS"`
	SubPartitioningCountS int     `json:"subPartitioningCountS"`
	UkS                   string  `json:"ukS"`
	PkS                   string  `json:"pkS"`
	PkT                   string  `json:"pkT"`
	PartitioningTypeT     string  `json:"partitioningTypeT"`
	ClusterTypeT          string  `json:"clusterTypeT"`
	TableSizeM            float64 `json:"tableSizeM"`
	*BaseFields
}
type SubmitTaskSchemaTablesReq struct {
	ChannelId     int                   `json:"channelId"`
	TaskId        int                   `json:"taskId"`
	SaveTables    []*ChannelSchemaTable `json:"saveTables"`
	DeleteTables  []int                 `json:"deleteTables"`
	SaveSchemas   []*ChannelSchemaTable `json:"saveSchemas"`
	DeleteSchemas []int                 `json:"deleteSchemas"`
}
type SubmitTaskSchemaTablesResp struct {
	IsConflict      bool   `json:"isConflict"`
	ConflictMessage string `json:"conflictMessage"`
}

type GetUnSelectedTaskSchemaTablesReq struct {
	ChannelId       int      `json:"channelId"`
	TaskType        int      `json:"taskType" validate:"required"`
	SchemaNames     []string `json:"schemaNames"`
	TableNamePrefix string   `json:"tableNamePrefix"`
	PageRequest
}
type GetUnSelectedTaskSchemaTablesResp struct {
	Tables []*ChannelSchemaTable `json:"tables"`
}

type GetSelectedTaskSchemaTablesReq struct {
	ChannelId         int      `json:"channelId"`
	TaskId            int      `json:"taskId"`
	SchemaNames       []string `json:"schemaNames"`
	TableNamePrefix   string   `json:"tableNamePrefix"`
	ClusterTypeT      []string `json:"clusterTypeT"`
	PartitioningTypeS []string `json:"partitioningTypeS"`
	PartitioningTypeT []string `json:"partitioningTypeT"`
	PkS               string   `json:"pks"`
	UkS               string   `json:"uks"`
	PkT               string   `json:"pkt"`
	OrderKeys         []string `json:"orderKeys"`
	DatasourceIdS     int      `json:"datasourceIdS"`
	PageRequest
}
type GetSelectedTaskSchemaTablesResp struct {
	Tables []*ChannelSchemaTable `json:"tables"`
}

type ReferenceTablesReq struct {
	ChannelId       int    `json:"channelId"`
	TaskId          int    `json:"taskId"`
	ReferenceType   string `json:"referenceType" validate:"oneof=channel tasks" enums:"channel,tasks"`
	ReferenceTaskId int    `json:"referenceTaskId"`
}
type ReferenceTablesResp struct {
	IsConflict      bool   `json:"isConflict"`
	ConflictMessage string `json:"conflictMessage"`
}

type GetSchemaTableByTaskIdReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}
type GetSchemaTableByTaskIdResp struct {
	Tables []*ChannelSchemaTable `json:"tables"`
}

type GetTablesByTableNameAndSourceSchemasReq struct {
	ChannelId     int      `json:"channelId"`
	TaskId        int      `json:"taskId"`
	TableName     string   `json:"tableName"`
	SourceSchemas []string `json:"sourceSchemas"`
}
type GetTablesByTableNameAndSourceSchemasResp struct {
	Tables []*ChannelSchemaTable `json:"tables"`
}

type DownloadTemplateReq struct {
	TemplateType int `json:"templateType"`
}

type ExportSourceTableColumnsToCSVReq struct {
	ChannelId int `json:"channelId"`
}

type ExportSourceTableColumnsToCSVResp struct {
	ExportCSVPath string `json:"exportCSVPath"`
}

func (i ExportSourceTableColumnsToCSVResp) GetFilePath() string {
	return i.ExportCSVPath
}

type ImportSourceTableColumnByCSVReq struct {
	ChannelId int    `json:"channelId"`
	FileName  string `json:"fileName"`
	FileDir   string `json:"fileDir"`
}

type UploadSourceTableColumnCSVReq struct {
	ChannelId int `json:"channelId"`
	FileName  string
	FileDir   string
}

type ImportSourceTableColumnByCSVResp struct {
	TableNum         int64 `json:"tableNum"`
	ColumnNum        int64 `json:"columnNum"`
	UpdatedColumnNum int64 `json:"updatedColumnNum"`
}

type UploadSourceTableColumnCSVResp struct {
	ChannelId int    `json:"channelId"`
	FileName  string `json:"fileName"`
	FileDir   string `json:"fileDir"`
}

type FileResponseInterface interface {
	GetFilePath() string
}

type GetActiveTmsSessionsReq struct {
	ChannelId int `json:"channelId"`
}

type GetTmsSessionProgressReq struct {
	ChannelId int `json:"channelId"`
}
type GetTmsSessionProgressResp struct {
	Progress []TmsSessionProgress `json:"progress"`
}

type TmsSessionProgress struct {
	// 执行进度，表示当前会话SQL操作的完成百分比，例如"50%"
	Progress string `json:"progress"` // 执行进度
	// 剩余秒数，表示该操作预计还需多少秒完成
	TimeRemaining int64 `json:"timeRemaining"` // 剩余秒数
	// 剩余分钟，保留两位小数，便于前端展示
	TimeRemainingMinutes float64 `json:"timeRemainingMinutes"` // 剩余分钟
	// 剩余小时，保留两位小数，便于前端展示
	TimeRemainingHours float64 `json:"timeRemainingHours"`
	// 执行SQL，当前会话正在执行的SQL语句
	SqlText string `json:"sqlText"` // 执行SQL
	// 开始时间，表示该SQL操作的开始时间
	StartTime string `json:"startTime"`
	Username  string `json:"username"` // 用户名
	Target    string `json:"target"`   // 目标
	Sid       string `json:"sid"`      // 会话 ID
	Serial    string `json:"serial"`   // 会话序列号
	Opname    string `json:"opname"`   // 操作名称
}

type GetTmsSessionRatioReq struct {
	ChannelId int `json:"channelId"`
}

type TmsUserStats struct {
	TmsPct    string `json:"tmsPct"`
	TotalUser string `json:"totalUser"`
	TmsUser   string `json:"tmsUser"`
}

type GetTmsSessionRatioResp struct {
	TmsUserStats []TmsUserStats `json:"tmsUserStats"`
}

type PhysicalFileIOStats struct {
	Fn     string `json:"fn"`
	Ts     string `json:"ts"`
	Reads  string `json:"reads"`
	Writes string `json:"writes"`
	Br     string `json:"br"`
	Bw     string `json:"bw"`
	RTimes string `json:"rTimes"`
	WTimes string `json:"wTimes"`
}

type GetPhysicalFileIOStatsReq struct {
	ChannelId int `json:"channelId"`
}

type GetPhysicalFileIOStatsResp struct {
	Stats []PhysicalFileIOStats `json:"stats"`
}

type GetHotFileInformationReq struct {
	ChannelId int `json:"channelId"`
}

type GetTableSpaceStatsReq struct {
	ChannelId int `json:"channelId"`
}

type OracleTableSpaceStats struct {
	TablespaceName string `json:"tablespaceName"`
	TotGrootteMb   string `json:"totGrootteMb"`
	UsedSpace      string `json:"usedSpace"`
	UsedRatio      string `json:"usedRatio"`
	TotalBytes     string `json:"totalBytes"`
	MaxBytes       string `json:"maxBytes"`
}

type OracleObjectStats struct {
	SchemaName  string `json:"schemaName"`
	Tables      string `json:"tables"`
	Views       string `json:"views"`
	Sequences   string `json:"sequences"`
	Indexes     string `json:"indexes"`
	Procedures  string `json:"procedures"`
	Packages    string `json:"packages"`
	Constraints string `json:"constraints"`
	Triggers    string `json:"triggers"`
	Functions   string `json:"functions"`
}

type OracleTableNumberRows struct {
	SchemaNameS   string `json:"schemaNameS"`
	TableNameS    string `json:"tableNameS"`
	TableRowsS    string `json:"tableRowsS"`
	LastAnalyzedS string `json:"lastAnalyzedS"`
	RnS           string `json:"rnS"`
	SchemaNameT   string `json:"schemaNameT"`
	TableNameT    string `json:"tableNameT"`
	TableRowsT    string `json:"tableRowsT"`
	LastAnalyzedT string `json:"lastAnalyzedT"`
}

type GetOracleObjectStatsReq struct {
	ChannelId int `json:"channelId"`
}

type GetOracleObjectStatsResp struct {
	Stats []OracleObjectStats `json:"stats"`
}

type GetOracleTableNumberRowsBySchemaReq struct {
	PageRequest
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId" form:"taskId"`
}

type GetOracleTableNumberRowsBySchemaResp struct {
	Stats []OracleTableNumberRows `json:"stats"`
	Page
}

type GetOracleMetaReq struct {
	ChannelId int    `json:"channelId"`
	ReqType   string `json:"reqType"`
}

type GetOracleMetaResp struct {
	MetaValue int64 `json:"metaValue"`
}

type GetTableSpaceStatsResp struct {
	Stats []OracleTableSpaceStats `json:"stats"`
}

// HotFileInfo 定义了最热数据文件的信息结构体
type HotFileInfo struct {
	// 文件名
	FileName string `json:"fileName"`
	// 表空间名
	TablespaceName string `json:"tablespaceName"`
	// 单块读平均耗时（毫秒）
	CS float64 `json:"cs"`
	// 读操作总耗时（秒）
	ReadTimeS float64 `json:"readTimeS"`
	// 写操作总耗时（秒）
	WriteTimeS float64 `json:"writeTimeS"`
}

// GetHotFileInformationResp 定义了获取最热数据文件信息的响应结构体
type GetHotFileInformationResp struct {
	// 热文件信息列表
	Files []HotFileInfo `json:"files"`
}

type GetActiveTmsSessionsResp struct {
	Sessions []ActiveTmsSession `json:"sessions"`
}

// ActiveTmsSession 结构体用于表示活跃的 TMS 会话信息
type ActiveTmsSession struct {
	Status       string `json:"status"`       // 会话状态
	SqlID        string `json:"sqlId"`        // 当前 SQL 的 SQL_ID
	SqlExecStart string `json:"sqlExecStart"` // SQL 执行开始时间
	Event        string `json:"event"`        // 当前等待事件
	Machine      string `json:"machine"`      // 机器名
	Program      string `json:"program"`      // 程序名
	Sid          string `json:"sid"`          // 会话 ID
	Serial       string `json:"serial"`       // 会话序列号
	Username     string `json:"username"`     // 用户名
}

type GetTiDBPerformanceReq struct {
	ReqType   string `json:"reqType"`
	ChannelId int    `json:"channelId"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Step      string `json:"step"`
}

type GetTiDBPerformanceResp struct {
	Performance []TiDBPerformance   `json:"performance"`
	Rows        []map[string]string `json:"rows"` // 新增，通用SQL结果
}

type TiDBPerformance struct {
	Instance  string  `json:"instance"`
	Value     float64 `json:"value"`
	Timestamp string  `json:"timestamp"`
}

type QueryRangeResponse struct {
	Data struct {
		ResultType string `json:"resultType"`
		Result     []struct {
			Metric map[string]string `json:"metric"`
			Values [][]interface{}   `json:"values"`
		} `json:"result"`
	} `json:"data"`
	Error string `json:"error"`
}

type GetObjectAssessOverviewReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type GetObjectAssessOverviewResp struct {
	OracleOverview           []*OracleOverview           `json:"oracleOverview"`
	ObjectCompatibleBySchema []*ObjectCompatibleBySchema `json:"objectCompatibleBySchema"`
	ObjectCompatibleByObject []*ObjectCompatibleByObject `json:"objectCompatibleByObject"`
}

type GetOracleObjectListReq struct {
	ChannelId  int    `json:"channelId"`
	TaskId     int    `json:"taskId" form:"taskId"`
	SchemaName string `json:"schemaName" form:"schemaName"`
	ObjectType string `json:"objectType" form:"objectType"`
	PageRequest
}

type GetOracleObjectListResp struct {
	Stats []OracleObjectList `json:"stats"`
	Page
}

type OracleObjectList struct {
	Owner        string
	ObjectName   string
	ObjectType   string
	Status       string
	LastAnalyzed string
	Rn           string
}

type GetOracleSegmentSizesReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type GetOracleSegmentSizesResp struct {
}

type QueryOracleSegmentSizesReq struct {
	ChannelId int `json:"channelId"`
	TaskId    int `json:"taskId"`
}

type QueryOracleSegmentSizesResp struct {
	Stats []*datasource.OracleSegmentSize `json:"stats"`
}

type QueryOracleSegmentSumSizesResp struct {
	SumTable float64 `json:"sumTable"`
	SumIndex float64 `json:"sumIndex"`
}

type GetChannelDatasourceListResp struct {
	DataSources []*DataSource `json:"dataSources"`
}
