package message

import "encoding/json"

type TaskEtcdValue struct {
	TaskId    int    `json:"taskId"`
	ChannelId int    `json:"channelId"`
	TaskType  int    `json:"taskType"`
	TaskName  string `json:"taskName"`
}

type RunningTask struct {
	ChannelId   int    `json:"channelId"`
	ChannelName string `json:"channelName"`

	TaskId   int    `json:"taskId"`
	TaskType int    `json:"taskType"`
	TaskName string `json:"taskName"`
}

type WorkerNode struct {
	ServerId         string `json:"serverId"`
	RegistrationTime string `json:"registrationTime"`
}

type GetWorkerNodesReq struct {
}

type GetWorkerNodesResp struct {
	Nodes []*WorkerNode `json:"nodes"`
}

type GetRunningTasksByServerIdReq struct {
	ServerId string `json:"serverId"`
}

type GetRunningTasksResp struct {
	Tasks []*RunningTask `json:"tasks"`
}

// Marshal using json.Marshal.
func (t *TaskEtcdValue) Marshal() ([]byte, error) {
	data, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// Unmarshal from binary data.
func (t *TaskEtcdValue) Unmarshal(data []byte) error {
	err := json.Unmarshal(data, t)
	if err != nil {
		return err
	}
	return nil
}

type GetServerModeReq struct {
}

type GetServerModeResp struct {
	ServerMode string `json:"serverMode"`
	ClusterId  string `json:"clusterId"`
}

type EtcdNotification struct {
	Type string `json:"type"`
	Body string `json:"body"`
}

type TaskStopBody struct {
	Tasks []TaskEtcdValue `json:"tasks"`
}
