# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TMS (Table Migration System) is PingCAP's enterprise data migration platform for migrating from Oracle databases to TiDB/MySQL. The system features AI-powered code conversion, comprehensive schema migration, and distributed processing capabilities.

## Build and Development Commands

### Building the Application
```bash
# Build the main TMS server binary
make build

# Build with Swagger documentation generation
make swag build

# Package with attachments (SQL scripts, JAR files)
make package

# Clean build artifacts
make clean
```

### Development Workflow
```bash
# Format code
make fmt

# Run development self-check (formatting, linting, static analysis)
make devselfcheck

# Run tests with coverage
make test

# Generate Swagger documentation
swag init -g cmd/main.go --exclude ./lib
```

### Testing
```bash
# Run unit tests
go test ./...

# Run tests with coverage report
make test

# Run specific package tests
go test ./pkg/ai/...
go test ./server/service/...

# Run tests with race detection
go test -race ./...
```

## Code Architecture

### Core Architecture Pattern
The system follows a layered architecture with clear separation of concerns:

- **`cmd/`**: Application entry points and main server initialization
- **`pkg/`**: Core business logic modules (domain layer)
- **`server/`**: Web application layer (presentation/API layer)
  - `models/`: Data access layer with GORM entities
  - `service/`: Business logic services 
  - `web/`: HTTP handlers and routing
- **`common/`**: Shared constants, structs, and error definitions
- **`util/`**: Cross-cutting utility libraries

### Key Business Modules (pkg/)

#### Migration Pipeline
- **`migration/`**: Orchestrates data migration workflows (full, incremental, CSV)
- **`sqlanalyze/`**: Analyzes Oracle SQL compatibility with TiDB
- **`objectparser/`**: Parses database objects and analyzes dependencies
- **`datacompare/`**: Validates migrated data integrity
  - Fast-fail thresholds for early termination:
    - `missing-tables-threshold`: Stop when missing tables percentage exceeds threshold (0-100, default 100)
    - `table-chunk-fail-threshold`: Stop when any table's chunk failure rate exceeds threshold (0-100, default 100)
    - `overall-table-error-threshold`: Stop when overall table error percentage exceeds threshold (0-100, default 100)
  - Any threshold being exceeded triggers task failure
- **`increment/`**: Handles incremental data synchronization via DSG

#### AI Integration
- **`ai/`**: Multi-provider AI service (OpenAI, Azure, Claude, Gemini)
  - Converts Oracle PL/SQL procedures to Java code
  - Context-aware prompt generation with dependency analysis
  - Supports different AI providers via factory pattern

### Database Layer Patterns
- Uses Repository pattern with interfaces for testability
- GORM entities in `server/models/*/entity.go`
- Service implementations in `server/models/*/impl.go`  
- Data access interfaces in `server/models/*/readerwriter.go`
- Mock generation via `golang/mock` framework

### Unified Database Connection Architecture
- **`util/database/`**: Centralized database connection management
  - `OpenOracle()`: Unified Oracle connection interface with retry and pooling
  - `OpenMySQL()`: Unified MySQL/TiDB connection interface with retry and pooling
  - `MySQLConfig` and `OracleConfig`: Standardized configuration structures
  - Automatic retry mechanisms for transient failures
  - Connection pool management with configurable options
  - All database connections throughout the project use these unified interfaces

### Configuration Management
- TOML-based configuration files in `data/` directory
- Environment-specific configs: `config-cluster.toml`, `config-single-main.toml`
- Role-based configuration (web server vs worker nodes)
- Configuration parsing handled by `util/config/config.go`

## Development Guidelines

### Code Organization
- Business logic belongs in `pkg/` modules
- Web layer code goes in `server/web/`
- Database entities and DAOs in `server/models/`
- Shared utilities in `util/`
- Constants and common structures in `common/`

### Testing Conventions
- Unit tests follow `*_test.go` naming
- Mock interfaces generated using `server/models/genmock.sh`
- Test data and fixtures organized per module
- Integration tests for database operations

### AI Module Development
- AI providers implement common interface in `pkg/ai/provider/`
- Use factory pattern for provider instantiation
- Context and dependency information passed via structured prompts
- Support for different AI models and temperature settings

### Migration Module Development  
- Migration tasks follow state machine pattern
- Progress tracking via database entities
- Chunk-based processing for large datasets
- Error handling with retry mechanisms

### API Development
- REST APIs follow standard patterns in `server/web/`
- Swagger documentation generated from code annotations
- DTOs defined in `server/message/`
- Input validation and error handling standardized

## Key Dependencies and Integrations

### Database Drivers
- Oracle: Custom `godror` driver with Oracle client
- MySQL/TiDB: Standard `go-sql-driver/mysql`
- SQLite: For local metadata storage

### TiDB Ecosystem
- TiDB Lightning for high-performance data loading
- TiKV client for cluster operations
- PD client for cluster metadata

### External Services
- Multiple AI provider APIs (OpenAI, Azure, Claude, Gemini)
- ETCD for distributed coordination in cluster mode
- Kafka integration for event streaming (via Shopify/sarama)

## Running the Application

### Local Development
```bash
# Start the server (default port 8082)
./bin/tms-server

# With custom config
./bin/tms-server -config ./data/config-single-main.toml

# Generate license key
./bin/tms-server -gen-key

# Encrypt configuration values
./bin/tms-server -encrypt "your-secret-value"
```

### Configuration Files
- Main config: `config.toml` (server port, data directory, database settings)
- Cluster config: `data/config-cluster.toml`
- Development config: `data/config-worker-dev.toml`

### Important Build Requirements
- Go 1.21+
- TIMS_EDITION environment variable must be set to "Enterprise"
- Oracle client libraries required for godror driver
- Swagger CLI for documentation generation

## License and Versioning
- Enterprise edition with license key validation
- Version information embedded during build via LDFLAGS
- Git commit hash and build timestamp tracked in binaries