# IDE and Editor files
*/*.iml
*/.idea
.idea/
.vscode/
.cursor/
.DS_Store

# Build and runtime directories
cmd/data
cmd/dist
dist/
tmp/
bin/
data/

# Configuration files
cmd/config.toml
.air.toml
fix*.sql

# Test files
cmd/config-penwu.toml
cmd/config-penwu.toml-migrate
SqlAnalyzer.py

# Data files
ai_source.json
oracle.json
transform.json
transform.json.cursor/

# IDE specific
.run/agents/
worktrees/
.kiro
scripts/temp_init.sql

# dev server
build-dev.sh
dist.zip
ps.sh
reload.sh
start.sh
stop.sh
tms-server-dev-output.log
tms-server-dev.pid
tms-server-sql-output.log
tms-server-sql.pid