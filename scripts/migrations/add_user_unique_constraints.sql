-- 为users表添加唯一性约束
-- 执行前请确保没有重复的用户名和邮箱

-- 检查是否有重复的用户名
SELECT username, COUNT(*) as count 
FROM users 
WHERE deleted_at IS NULL 
GROUP BY username 
HAVING COUNT(*) > 1;

-- 检查是否有重复的邮箱
SELECT email, COUNT(*) as count 
FROM users 
WHERE deleted_at IS NULL 
GROUP BY email 
HAVING COUNT(*) > 1;

-- 添加唯一性约束（如果上面的查询没有返回结果）
ALTER TABLE users ADD UNIQUE KEY idx_users_username (username);
ALTER TABLE users ADD UNIQUE KEY idx_users_email (email); 