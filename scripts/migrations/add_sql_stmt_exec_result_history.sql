-- Create history table for SQL execution result user operations
CREATE TABLE IF NOT EXISTS `sql_stmt_exec_result_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sql_exec_id` int NOT NULL COMMENT 'Reference to sql_stmt_exec_results',
  `task_id` int NOT NULL COMMENT 'Task ID for quick filtering',
  `operation_type` varchar(32) NOT NULL COMMENT 'Operation type: CREATE, UPDATE',
  `previous_status` varchar(32) DEFAULT NULL COMMENT 'Previous user operation status',
  `new_status` varchar(32) NOT NULL COMMENT 'New user operation status',
  `operate_by` varchar(64) NOT NULL COMMENT 'User who performed the operation',
  `operate_at` datetime NOT NULL COMMENT 'When the operation was performed',
  `operate_remark` varchar(500) DEFAULT NULL COMMENT 'Optional remark for the operation',
  `client_ip` varchar(64) DEFAULT NULL COMMENT 'Client IP address',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(32) NOT NULL DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `idx_sql_exec_id` (`sql_exec_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_operate_at` (`operate_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='History tracking for SQL execution result user operations';

-- Add indexes for better query performance
ALTER TABLE `sql_stmt_exec_result_history` ADD INDEX `idx_task_sql_exec` (`task_id`, `sql_exec_id`);
ALTER TABLE `sql_stmt_exec_result_history` ADD INDEX `idx_operate_by` (`operate_by`);