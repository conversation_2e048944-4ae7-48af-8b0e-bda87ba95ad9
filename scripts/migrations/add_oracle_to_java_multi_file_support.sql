-- Add multi-file support for Oracle to Java conversion results
-- This migration adds a new table to store multiple Java files per conversion result
-- and extends the existing result table with multi-file indicators.

-- Create the multi-file storage table
CREATE TABLE IF NOT EXISTS `oracle_to_java_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `result_id` bigint NOT NULL COMMENT 'Reference to oracle_to_java_results.id',
  `file_path` varchar(500) NOT NULL COMMENT 'Full file path: com/tms/entity/UserEntity.java',
  `file_name` varchar(200) NOT NULL COMMENT 'File name: UserEntity.java',
  `package_path` varchar(400) DEFAULT NULL COMMENT 'Package path: com.tms.entity',
  `file_type` varchar(50) NOT NULL DEFAULT 'java' COMMENT 'File type: java, xml, properties',
  `file_content` longtext NOT NULL COMMENT 'Complete file content',
  `file_order` int NOT NULL DEFAULT 0 COMMENT 'Display order for files',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update timestamp',
  `deleted_at` datetime DEFAULT NULL COMMENT 'Soft delete timestamp',
  PRIMARY KEY (`id`),
  KEY `idx_result_id` (`result_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_package_path` (`package_path`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_oracle_to_java_files_result` FOREIGN KEY (`result_id`) REFERENCES `oracle_to_java_results` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Multi-file storage for Oracle to Java conversion results';

-- Add multi-file indicator to existing oracle_to_java_results table
ALTER TABLE `oracle_to_java_results` 
ADD COLUMN `is_multi_file` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this result contains multiple files' AFTER `convert_prompts`;

-- Create similar table for history results
CREATE TABLE IF NOT EXISTS `oracle_to_java_history_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `result_id` bigint NOT NULL COMMENT 'Reference to oracle_to_java_history_results.id',
  `file_path` varchar(500) NOT NULL COMMENT 'Full file path: com/tms/entity/UserEntity.java',
  `file_name` varchar(200) NOT NULL COMMENT 'File name: UserEntity.java',
  `package_path` varchar(400) DEFAULT NULL COMMENT 'Package path: com.tms.entity',
  `file_type` varchar(50) NOT NULL DEFAULT 'java' COMMENT 'File type: java, xml, properties',
  `file_content` longtext NOT NULL COMMENT 'Complete file content',
  `file_order` int NOT NULL DEFAULT 0 COMMENT 'Display order for files',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update timestamp',
  `deleted_at` datetime DEFAULT NULL COMMENT 'Soft delete timestamp',
  PRIMARY KEY (`id`),
  KEY `idx_result_id` (`result_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_package_path` (`package_path`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_oracle_to_java_history_files_result` FOREIGN KEY (`result_id`) REFERENCES `oracle_to_java_history_results` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Multi-file storage for Oracle to Java history conversion results';

-- Add multi-file indicator to existing oracle_to_java_history_results table
ALTER TABLE `oracle_to_java_history_results` 
ADD COLUMN `is_multi_file` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this result contains multiple files' AFTER `convert_prompts`;

-- Create indexes for better query performance
ALTER TABLE `oracle_to_java_files` ADD INDEX `idx_result_file_type` (`result_id`, `file_type`);
ALTER TABLE `oracle_to_java_files` ADD INDEX `idx_file_path` (`file_path`);
ALTER TABLE `oracle_to_java_history_files` ADD INDEX `idx_result_file_type` (`result_id`, `file_type`);
ALTER TABLE `oracle_to_java_history_files` ADD INDEX `idx_file_path` (`file_path`);