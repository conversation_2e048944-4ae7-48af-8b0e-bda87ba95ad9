INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (1, 'mysql', 'information_schema');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (2, 'mysql', 'mysql');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (3, 'mysql', 'performance_schema');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (4, 'mysql', 'sys');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (5, 'oracle', 'OWBSYS_AUDIT');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (6, 'oracle', 'MDSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (7, 'oracle', 'PUBLIC');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (8, 'oracle', 'OUTLN');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (9, 'oracle', 'CTXSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (10, 'oracle', 'OLAPSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (11, 'oracle', 'FLOWS_FILES');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (12, 'oracle', 'OWBSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (13, 'oracle', 'HR');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (14, 'oracle', 'SYSTEM');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (15, 'oracle', 'ORACLE_OCM');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (16, 'oracle', 'EXFSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (17, 'oracle', 'APEX_030200');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (18, 'oracle', 'SCOTT');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (19, 'oracle', 'SH');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (20, 'oracle', 'PM');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (21, 'oracle', 'DBSNMP');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (22, 'oracle', 'ORDSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (23, 'oracle', 'ORDPLUGINS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (24, 'oracle', 'SYSMAN');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (25, 'oracle', 'OE');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (26, 'oracle', 'IX');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (27, 'oracle', 'APPQOSSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (28, 'oracle', 'XDB');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (29, 'oracle', 'ORDDATA');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (30, 'oracle', 'BI');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (31, 'oracle', 'SYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (32, 'oracle', 'WMSYS');
INSERT INTO  schema_filters (schema_filter_id, db_type, schema_name) VALUES (33, 'oracle', 'SI_INFORMTN_SCHEMA');