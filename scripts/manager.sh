#!/bin/bash

# 设置变量
APP_NAME="tms-server"
CONFIG_FILE="config.toml"
LOG_FILE="manager.log"
TAIL_LOG_FILE="data/logs/tims.log"
PID_FILE="$(pwd)/.tms.pid"
CMD="$(pwd)/${APP_NAME} -config $(pwd)/${CONFIG_FILE}"

# 启动函数
start() {
    if [ -f $PID_FILE ] && kill -0 $(cat $PID_FILE) 2>/dev/null; then
        echo "$APP_NAME is already running."
        exit 1
    fi

    # 清空日志文件
    > $LOG_FILE

    echo "Starting $APP_NAME..."
    nohup $CMD > $LOG_FILE 2>&1 &
    echo $! > $PID_FILE

    # 等待一秒钟，以确保进程有时间启动
    sleep 1

    # 检查进程是否启动成功
    if ! kill -0 $(cat $PID_FILE) 2>/dev/null; then
        echo "Failed to start $APP_NAME. Check the logs below:"
        cat $LOG_FILE
        rm -f $PID_FILE
        exit 1
    else
        echo "$APP_NAME started with PID $(cat $PID_FILE)."
    fi
}

# 停止函数
stop() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)

        # 检查PID是否匹配正确的命令路径
        if ps -p $PID -o args= | grep -q "$CMD"; then
            echo "Stopping $APP_NAME..."
            kill $PID
            rm -f $PID_FILE
            echo "$APP_NAME stopped."
        else
            echo "No matching process found for PID $PID. Not stopping."
        fi
    else
        echo "$APP_NAME is not running."
    fi
}

# 状态函数
status() {
    if [ -f $PID_FILE ] && kill -0 $(cat $PID_FILE) 2>/dev/null; then
        echo "$APP_NAME is running with PID $(cat $PID_FILE)."
    else
        echo "$APP_NAME is not running with saved PID."

        # 检查是否有相同命令的进程在运行
        RUNNING_PID=$(pgrep -f "$CMD")
        if [ -n "$RUNNING_PID" ]; then
            echo "$APP_NAME is running with PID $RUNNING_PID, but no PID file found."
        else
            echo "$APP_NAME is not running."
        fi
    fi
}

# 重启函数
restart() {
    stop
    start
}

# tail命令函数
tail_log() {
    tail -f $TAIL_LOG_FILE
}

# 脚本使用说明
usage() {
    echo "Usage: $0 {start|stop|status|restart|tail}"
    exit 1
}

# 主体逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    status)
        status
        ;;
    restart)
        restart
        ;;
    tail)
        tail_log
        ;;
    *)
        usage
        ;;
esac

exit 0
