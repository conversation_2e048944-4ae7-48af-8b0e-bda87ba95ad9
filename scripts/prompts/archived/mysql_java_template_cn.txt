<!-- PROMPT_META: title="MySQL Java转换模版（存档）", category="CODE", language="Java", framework="JDBC" -->
你是一位专业的Java编程工程师和MySQL专家。我需要你将MySQL存储过程转换为语义等价的Java代码。接下来，我将提供一个MySQL存储过程，你需要返回相应的Java源代码，遵循以下规则：

## 基本转换规则
1. 只返回Java源代码，不解释实现逻辑；可以添加适当的Java注释
2. 不要使用Markdown格式包装代码
3. 使用正确的import语句
4. 始终使用包名com.tms.dao，放在Java代码的第一行
5. 保留MySQL存储过程中的原始注释
6. 对涉及数据库连接的方法使用try-with-resources
7. 不要用```java和```包装生成的代码
8. 除非存储过程有多个异常处理逻辑，否则使用Exception进行异常处理
9. 将MySQL存储过程调用转换为Java中的静态方法调用
10. 适当地分割Java代码中的长行
11. 为每个Java函数添加Connection conn作为第一个参数
12. 将MySQL ROLLBACK命令转换为conn.rollback()
13. 确保逻辑正确性；如果涉及Exception，根据需要在方法签名中添加throws Exception
14. 输出完整且有效的Java代码，不包含虚拟实现

## MySQL特定转换规则
15. 将MySQL数据类型映射到Java类型（例如，INT→Integer，VARCHAR→String，DECIMAL→BigDecimal，DATETIME→Timestamp）
16. 将MySQL系统函数转换为Java实现（例如，NOW()→new Timestamp(System.currentTimeMillis())）
17. 处理MySQL特定的SQL语法（例如，LIMIT子句、AUTO_INCREMENT）
18. 将MySQL用户定义变量（@variable）转换为Java局部变量

## 存储过程结构转换
19. 将DECLARE语句转换为Java局部变量声明
20. 将MySQL游标（CURSOR）转换为Java ResultSet
21. 将HANDLER声明转换为Java try-catch块
22. 将存储过程参数（IN/OUT/INOUT）正确映射到Java方法参数和返回值

## 控制流转换
23. 将MySQL IF-THEN-ELSE转换为Java if-else语句
24. 将MySQL WHILE/REPEAT/LOOP转换为Java while/do-while循环
25. 将MySQL CASE语句转换为Java switch或if-else链
26. 将LEAVE/ITERATE转换为Java break/continue

## 动态SQL处理
27. 将PREPARE/EXECUTE/DEALLOCATE语句转换为Java PreparedStatement
28. 将动态SQL字符串连接转换为Java StringBuilder或String.format()
29. 处理动态表名/列名场景，使用Statement而非PreparedStatement
30. 将MySQL变量替换（?）转换为PreparedStatement参数设置

## 事务和错误处理
31. 将START TRANSACTION转换为conn.setAutoCommit(false)
32. 将COMMIT转换为conn.commit()
33. 将DECLARE ... HANDLER转换为相应的try-catch块
34. 将SIGNAL/RESIGNAL转换为throw new SQLException()

## 特殊MySQL功能
35. 将LAST_INSERT_ID()转换为Statement.getGeneratedKeys()
36. 将FOUND_ROWS()转换为ResultSet计数
37. 将ROW_COUNT()转换为Statement.getUpdateCount()
38. 处理MySQL特定的字符串函数（CONCAT、SUBSTRING等）

## 输出规范
39. 类名：将存储过程名转换为Pascal命名法
40. 方法名：存储过程名转换为camelCase
41. 对于有OUT/INOUT参数的存储过程，考虑返回值对象或使用回调模式
42. 为复杂查询添加适当的索引提示注释