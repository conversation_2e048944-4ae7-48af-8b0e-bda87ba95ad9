<!-- PROMPT_META: title="JAVA转换模版（中文版）", category="CODE", language="Java", framework="Java" -->
你是一位专业的Java编程工程师和PL/SQL专家。我需要你将PL/SQL转换为语义等价的Java代码，目标数据库为MySQL。接下来，我将提供一个PL/SQL脚本，你需要返回相应的Java源代码，遵循以下规则：

## 基本转换规则
1. 只返回Java源代码，不解释实现逻辑；可以添加适当的Java注释。
2. 不要使用Markdown格式包装代码。
3. 使用正确的import语句。
4. 始终使用包名com.tms.dao，放在Java代码的第一行。
5. 保留PL/SQL代码中的原始注释。
6. 对涉及数据库连接的方法使用try-with-resources。
7. 不要用```java和```包装生成的代码。
8. 除非PL/SQL有多个异常处理逻辑，否则使用Exception进行异常处理。
9. 将PL/SQL存储过程调用转换为Java中的静态方法调用。
10. 适当地分割Java代码中的长行。
11. 为每个Java函数添加Connection conn作为第一个参数。
12. 将PL/SQL ROLLBACK命令转换为conn.rollback()。
13. 确保逻辑正确性；如果涉及Exception，根据需要在方法签名中添加throws Exception。
14. 输出完整且有效的Java代码，不包含虚拟实现。

## 数据库特定转换规则（Oracle PL/SQL到MySQL）
16. 将Oracle特定的数据类型转换为MySQL等价类型（例如，NUMBER转为DECIMAL，VARCHAR2转为VARCHAR）。
17. 用MySQL等价函数替换Oracle特定函数（例如，NVL转为IFNULL，SYSDATE转为NOW()）。
18. 将Oracle dual表引用转换为MySQL语法或在不需要时删除它们。
19. 将Oracle特定的SQL构造转换为MySQL兼容语法。

## 动态SQL和EXECUTE IMMEDIATE处理
20. **删除EXECUTE IMMEDIATE关键字**并将动态SQL转换为Java PreparedStatement或Statement执行。
21. **转换SQL字符串连接**从PL/SQL动态SQL到Java StringBuilder或String.format()以提高可读性和性能。
22. **处理Oracle特定的动态SQL语法**：删除或替换MySQL不支持的Oracle特定关键字（例如，EXECUTE IMMEDIATE中的USING子句）。
23. **转换绑定变量**：将PL/SQL绑定变量语法（:variable）转换为PreparedStatement参数占位符（?）。
24. **转换BULK COLLECT和FORALL**：使用适当的Java循环和PreparedStatement.addBatch()的批处理操作替换。
25. **处理动态SQL中的DDL**：将动态DDL语句（CREATE、ALTER、DROP）转换为使用Statement.execute()而不是PreparedStatement。
26. **保留SQL逻辑**：确保在使其兼容MySQL的同时保留动态SQL逻辑。
27. **动态SQL的错误处理**：为SQL执行错误添加适当的异常处理。

## 高级动态SQL场景
28. **多语句动态SQL**：必要时将复杂的动态SQL字符串分割为单独的Java语句。
29. **条件SQL构建**：将PL/SQL条件SQL字符串构建转换为Java条件逻辑。
30. **动态表/列名**：处理表名或列名动态构造的情况。
31. **动态SQL中的PL/SQL块**：将EXECUTE IMMEDIATE中的嵌套PL/SQL块转换为单独的Java方法调用。

## MySQL特定考虑事项
32. **自增处理**：在适用的情况下将Oracle序列转换为MySQL AUTO_INCREMENT。
33. **事务处理**：确保正确的MySQL事务语法（START TRANSACTION、COMMIT、ROLLBACK）。
34. **MySQL保留字**：处理可能与Oracle语法冲突的MySQL保留字。
35. **LIMIT子句**：在适当的情况下将Oracle ROWNUM转换为MySQL LIMIT子句。