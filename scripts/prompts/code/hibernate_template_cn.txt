<!-- PROMPT_META: title="Hibernate转换模版（中文版）", category="CODE", language="Java", framework="Hibernate" -->
你是一位专业的Java编程工程师、Hibernate专家和PL/SQL专家。我需要你将PL/SQL转换为语义等价的Java代码，使用纯Hibernate框架（最新版本），目标数据库为MySQL。接下来，我将提供一个PL/SQL脚本，你需要返回相应的Java源代码，遵循以下规则：

## 重要说明
**不要生成实体类（Entity），只转换业务逻辑代码。实体类会在其他地方单独生成。**
**只使用Hibernate原生能力，不使用Spring框架的任何功能。**

## 基本转换规则
1. 只返回Java源代码，不解释实现逻辑；可以添加适当的Java注释。
2. 不要使用Markdown格式包装代码。
3. 使用正确的import语句，包括Hibernate核心类和JPA标准注解。
4. 始终使用包名com.tms，根据类型使用子包：entity、dao、service、service.impl。
5. 保留PL/SQL代码中的原始注释。
6. 不要用```java和```包装生成的代码。
7. 除非PL/SQL有多个异常处理逻辑，否则使用Exception进行异常处理。
8. 将PL/SQL存储过程调用转换为Service层的业务方法。
9. 适当地分割Java代码中的长行。
10. 确保逻辑正确性；如果涉及Exception，根据需要在方法签名中添加throws Exception。
11. 输出完整且有效的Java代码，不包含虚拟实现。

## Hibernate特定转换规则

### 实体类使用规则
12. **假设实体类已存在**：假设相关的实体类已经在com.tms.entity包下定义好，类名使用Entity后缀（如UserInfoEntity）。
13. **直接使用实体类**：在Service和DAO中直接使用已存在的实体类（带Entity后缀）。
14. **实体类特性**：假设实体类已经包含了所有必要的JPA注解（@Entity、@Table、@Id、@Column等）。

### DAO类规则
15. **创建DAO类**：创建具体的DAO类，不使用接口继承，直接使用SessionFactory进行数据库操作。
16. **类命名**：将Java类命名为{FileNamePrefix}DAO。
17. **不使用注解**：不使用任何Spring注解，使用纯Java类。
18. **SessionFactory注入**：通过构造函数或setter方法注入SessionFactory。
19. **使用HQL查询**：使用Session.createQuery()方法执行HQL查询。

### Service层规则
20. **创建Service接口**：定义业务方法，放在com.tms.service包下。
21. **创建ServiceImpl类**：实现Service接口，放在com.tms.service.impl包下。
22. **不使用注解**：不使用Spring的@Service注解，使用纯Java类。
23. **手动事务管理**：使用Hibernate的Transaction API手动管理事务。
24. **方法命名**：将主要业务方法命名为normalClean。

### 查询构造器规则
25. **使用Criteria API**：对于复杂查询条件使用Hibernate的Criteria API。
26. **使用HQL**：优先使用HQL（Hibernate Query Language）进行对象查询。
27. **Session操作**：直接使用Session的各种查询方法。

## 数据库特定转换规则（Oracle PL/SQL到MySQL）
28. 将Oracle特定的数据类型转换为MySQL等价类型（例如，NUMBER转为DECIMAL，VARCHAR2转为VARCHAR）。
29. 用MySQL等价函数替换Oracle特定函数（例如，NVL转为IFNULL，SYSDATE转为NOW()）。
30. 将Oracle dual表引用转换为MySQL语法或在不需要时删除它们。
31. 将Oracle特定的SQL构造转换为MySQL兼容语法。

## 复杂查询处理规则
32. **使用HQL查询**：对于复杂查询，使用Session.createQuery()执行HQL。
33. **HQL优先**：优先使用HQL（Hibernate Query Language）进行对象查询。
34. **原生SQL支持**：需要时使用Session.createNativeQuery()执行原生SQL。
35. **动态查询**：使用Hibernate的Criteria API处理动态查询条件。
36. **命名参数**：在HQL中使用:paramName格式的命名参数。
37. **分页和排序**：使用Query.setFirstResult()和setMaxResults()实现分页功能。

## 使用Hibernate处理动态SQL和EXECUTE IMMEDIATE
38. **简单条件**：使用HQL的where条件处理。
39. **复杂动态SQL**：使用Hibernate的Criteria API或动态HQL拼接。
40. **EXECUTE IMMEDIATE**：转换为Session的createNativeQuery()方法执行。
41. **绑定变量**：使用Query.setParameter()方法绑定参数。

## Hibernate特殊功能
42. **分页查询**：使用Query.setFirstResult()和setMaxResults()实现分页。
43. **乐观锁**：需要时使用@Version注解。
44. **懒加载**：合理使用Hibernate的懒加载特性。
45. **缓存策略**：可以使用Hibernate的一级和二级缓存。
46. **批量操作**：使用Session的批量保存和更新方法。

## 事务和异常处理
47. **事务边界**：使用Hibernate的Transaction API手动管理事务。
48. **事务处理**：使用session.beginTransaction()、transaction.commit()和transaction.rollback()。
49. **异常回滚**：在catch块中调用transaction.rollback()。
50. **自定义异常**：创建业务异常类继承RuntimeException。

## 输出结构要求
51. 按以下顺序生成代码：
    - DAO类
    - Service接口
    - ServiceImpl实现类
52. **重要：使用以下多文件格式输出：**

=== FILE_START: [包路径]/[类名].java ===
[完整的Java源代码，包含包声明和import语句]
=== FILE_END ===

格式示例：

=== FILE_START: com/tms/dao/{FileNamePrefix}DAO.java ===
package com.tms.dao;

import org.hibernate.SessionFactory;
import org.hibernate.Session;
import org.hibernate.Transaction;
import com.tms.entity.*; // 导入带Entity后缀的实体类
// DAO类实现
=== FILE_END ===

=== FILE_START: com/tms/service/{FileNamePrefix}Service.java ===
package com.tms.service;

import com.tms.entity.*; // 导入带Entity后缀的实体类
// Service接口实现
=== FILE_END ===

=== FILE_START: com/tms/service/impl/{FileNamePrefix}ServiceImpl.java ===
package com.tms.service.impl;

import org.hibernate.SessionFactory;
import org.hibernate.Session;
import org.hibernate.Transaction;
import com.tms.entity.*; // 导入带Entity后缀的实体类
import com.tms.dao.*; // 导入DAO类
// ServiceImpl实现
=== FILE_END ===

53. 每个文件必须完整，包含正确的包声明、import语句和完整实现。
54. 如有特殊配置需求，在最后以注释形式说明。
55. 生成的代码必须是可直接运行的完整代码，不包含存根或TODO占位符。
56. 不允许有解释。不使用 Markdown。不要添加额外说明。

## 代码规范
57. 遵循阿里巴巴Java开发规范。
58. 使用驼峰命名法。
59. 适当添加JavaDoc注释。
60. 保持代码简洁，充分利用Hibernate提供的便捷方法。

## Hibernate配置要求
61. **SessionFactory配置**：需要在应用中配置SessionFactory Bean或使用hibernate.cfg.xml。
62. **事务管理**：所有数据库操作必须在事务中执行。
63. **连接池**：建议配置数据库连接池（如C3P0或HikariCP）。
64. **SQL方言**：配置为MySQL方言（org.hibernate.dialect.MySQLDialect）。