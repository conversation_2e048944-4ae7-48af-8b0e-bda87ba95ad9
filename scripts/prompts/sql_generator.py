#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SQL Generator Script - Read local txt files and generate INSERT statements
Compatible with Python 3.x
"""

import sys
import os
import re
from datetime import datetime

def parse_header_metadata(first_line):
    """
    Parse header metadata from first line
    Expected format: <!-- PROMPT_META: title="...", category="...", language="...", framework="..." -->
    Returns: (title, category, language, framework) or None if parsing fails
    """
    if not first_line.strip().startswith('<!-- PROMPT_META:'):
        return None
    
    try:
        # Extract content between <!-- PROMPT_META: and -->
        match = re.search(r'<!-- PROMPT_META:\s*(.+?)\s*-->', first_line.strip())
        if not match:
            return None
        
        content = match.group(1)
        
        # Parse key="value" pairs
        fields = {}
        pattern = r'(\w+)="([^"]*?)"'
        matches = re.findall(pattern, content)
        
        for key, value in matches:
            fields[key] = value
        
        # Check if all required fields are present
        required_fields = ['title', 'category', 'language', 'framework']
        if all(field in fields for field in required_fields):
            return fields['title'], fields['category'], fields['language'], fields['framework']
        else:
            missing = [f for f in required_fields if f not in fields]
            print(f"Warning: Header missing required fields: {missing}")
            return None
    
    except Exception as e:
        print(f"Error parsing header: {e}")
        return None


def escape_sql_string(text):
    """
    Escape special characters in SQL string
    """
    # Handle backslashes first (must be first)
    text = text.replace('\\', '\\\\')

    # Handle single quotes
    text = text.replace("'", "\\'")

    # Handle double quotes
    text = text.replace('"', '\\"')

    # Handle newlines
    text = text.replace('\n', '\\n')

    # Handle carriage returns
    text = text.replace('\r', '\\r')

    # Handle tabs
    text = text.replace('\t', '\\t')

    # Handle null characters
    text = text.replace('\0', '\\0')

    return text

def generate_insert_sql(prompt_text, prompt_id, prompt_title, file_path, content, table_name='oracle_object_transformation_prompts'):
    """
    Generate INSERT SQL statement
    """
    # Remove header line from prompt_text
    lines = prompt_text.split('\n')
    if lines and lines[0].strip().startswith('<!-- PROMPT_META:'):
        # Remove the header line and join the rest
        prompt_text_without_header = '\n'.join(lines[1:]).strip()
    else:
        prompt_text_without_header = prompt_text
    
    # Escape text
    escaped_text = escape_sql_string(prompt_text_without_header)
    escaped_title = escape_sql_string(prompt_title)

    # Determine if this is the default template
    is_default = 1 if is_default_template(prompt_title) else 0

    # Extract category, language and framework from header (strict validation)
    prompt_category, target_language, target_framework = extract_metadata_strict(prompt_title, file_path, content)
    usage_example = escape_sql_string(get_usage_example(prompt_category, target_framework))

    # Generate SQL statement
    sql_template = """INSERT IGNORE INTO `{table}`
(`id`, `prompt_title`, `prompt_category`, `target_language`, `target_framework`, `prompt_text`, `is_default`, `created_at`, `updated_at`, `deleted_at`)
VALUES (
    null,
    '{title}',
    '{prompt_category}',
    '{target_language}',
    '{target_framework}',
    '{prompt_text}',
    {is_default},
    NOW(),
    NOW(),
    NULL
);

"""

    return sql_template.format(
        table=table_name,
        id=prompt_id,
        title=escaped_title,
        prompt_category=prompt_category,
        target_language=target_language,
        target_framework=target_framework,
        prompt_text=escaped_text,
        is_default=is_default,
        usage_example=usage_example
    )

def read_file_content(file_path):
    """
    Read file content with UTF-8 encoding support
    """
    encodings = ['utf-8', 'utf-8-sig', 'gbk', 'cp1252', 'latin-1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read().strip()
                print(f"Successfully read file {file_path} with encoding: {encoding}")
                return content
        except (UnicodeDecodeError, FileNotFoundError) as e:
            if encoding == encodings[-1]:  # Last encoding attempt
                print(f"Error: Cannot read file {file_path}")
                print(f"Details: {e}")
                return None
            continue

    return None

def write_sql_file(sql_content, output_path):
    """
    Write SQL content to file
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"SQL file generated: {output_path}")
    except IOError as e:
        print(f"Error: Cannot write file {output_path}")
        print(f"Details: {e}")

def get_txt_files(directory):
    """
    Get all .txt files in directory and subdirectories (table, code, trigger)
    """
    txt_files = []
    try:
        # Check for subdirectories first (table, code, trigger)
        subdirs = ['table', 'code', 'trigger']
        for subdir in subdirs:
            subdir_path = os.path.join(directory, subdir)
            if os.path.isdir(subdir_path):
                for filename in os.listdir(subdir_path):
                    if filename.lower().endswith('.txt'):
                        txt_files.append(os.path.join(subdir_path, filename))
        
        # Also check root directory for any remaining files
        for filename in os.listdir(directory):
            if filename.lower().endswith('.txt'):
                txt_files.append(os.path.join(directory, filename))
        
        return sorted(txt_files)
    except OSError as e:
        print(f"Error: Cannot read directory {directory}")
        print(f"Details: {e}")
        return []



def is_default_template(prompt_name):
    """
    Check if this template should be set as default
    """
    return prompt_name == 'JAVA转换模版（英文版）' or prompt_name == 'Java触发器转换模版'

def extract_metadata_strict(prompt_title, file_path, content):
    """
    Extract metadata with strict header validation - no fallback
    Returns: (prompt_category, target_language, target_framework)
    Raises SystemExit if header is missing or invalid
    """
    if not content:
        print(f"ERROR: File {file_path} is empty")
        sys.exit(1)
    
    lines = content.split('\n')
    if not lines:
        print(f"ERROR: File {file_path} has no content")
        sys.exit(1)
    
    header_data = parse_header_metadata(lines[0])
    if not header_data:
        print(f"ERROR: File {file_path} is missing required PROMPT_META header")
        print(f"Expected format: <!-- PROMPT_META: title=\"...\", category=\"...\", language=\"...\", framework=\"...\" -->")
        sys.exit(1)
    
    title, category, language, framework = header_data
    print(f"Using header metadata: category={category}, language={language}, framework={framework}")
    return category, language, framework

def get_usage_example(prompt_category, target_framework):
    """
    Get usage example based on prompt category and framework
    """
    if prompt_category == 'CODE':
        return """Input: Oracle PL/SQL procedure or function
Output: Java code with appropriate framework integration
Example: Convert Oracle stored procedure to Java DAO method"""
    elif prompt_category == 'TABLE':
        if target_framework == 'Hibernate':
            return """Input: Oracle table name(s)
Output: Hibernate entity class with JPA annotations
Example: USER_INFO -> UserInfoEntity.java"""
        elif target_framework == 'MyBatis':
            return """Input: Oracle table name(s)
Output: MyBatis entity class with mapper XML
Example: USER_INFO -> UserInfo.java + UserInfoMapper.xml"""
        elif target_framework == 'MyBatisPlus':
            return """Input: Oracle table name(s)
Output: MyBatis Plus entity class with annotations
Example: USER_INFO -> UserInfo.java with @TableName annotation"""
    elif prompt_category == 'TRIGGER':
        if target_framework == 'Hibernate':
            return """Input: Oracle table DDL and trigger definitions
Output: Hibernate entity with @EntityListeners and EntityListener class
Example: EMPLOYEE table + triggers -> EmployeeEntity.java + EmployeeEntityListener.java"""
        elif target_framework == 'MyBatis':
            return """Input: Oracle trigger definitions
Output: MyBatis interceptor implementing trigger logic
Example: BEFORE INSERT trigger -> TriggerInterceptor.java"""
        elif target_framework == 'MyBatisPlus':
            return """Input: Oracle trigger definitions
Output: MyBatis-Plus MetaObjectHandler and Interceptor
Example: Triggers -> TriggerMetaObjectHandler.java + AfterTriggerInterceptor.java"""
        else:  # Java/JDBC
            return """Input: Oracle trigger definitions
Output: Java TriggerManager and Trigger implementations
Example: Triggers -> TriggerManager.java + trigger implementation classes"""
    return ""


def sort_files_by_metadata(txt_files):
    """
    Sort files by prompt_category, target_language, target_framework
    """
    file_metadata = []
    
    for file_path in txt_files:
        # Read file content to extract metadata
        content = read_file_content(file_path)
        if content is None:
            continue
            
        lines = content.split('\n')
        if not lines:
            continue
            
        header_data = parse_header_metadata(lines[0])
        if not header_data:
            continue
            
        title, category, language, framework = header_data
        file_metadata.append((file_path, title, category, language, framework))
    
    # Sort by: prompt_title, prompt_category, target_language, target_framework
    file_metadata.sort(key=lambda x: (x[2], x[3], x[4]))
    
    return [item[0] for item in file_metadata]

def validate_headers_and_check_duplicates(txt_files):
    """
    Validate all headers and check for duplicates before processing
    """
    seen_titles = set()
    seen_headers = set()
    
    print("Validating headers and checking for duplicates...")
    
    for file_path in txt_files:
        # Skip archive files - they don't need validation
        if '/archive/' in file_path:
            print(f"Skipping archive file: {os.path.basename(file_path)}")
            continue
            
        # Read file content
        content = read_file_content(file_path)
        if content is None:
            print(f"ERROR: Cannot read file {file_path}")
            sys.exit(1)
        
        # Validate header exists
        lines = content.split('\n')
        if not lines or not lines[0].strip():
            print(f"ERROR: File {file_path} is missing header")
            sys.exit(1)
        
        header_data = parse_header_metadata(lines[0])
        if not header_data:
            print(f"ERROR: File {file_path} is missing required PROMPT_META header")
            print(f"Expected format: <!-- PROMPT_META: title=\"...\", category=\"...\", language=\"...\", framework=\"...\" -->")
            sys.exit(1)
        
        title, category, language, framework = header_data
        
        # Check for duplicate titles
        if title in seen_titles:
            print(f"ERROR: Duplicate title '{title}' found in file {file_path}")
            sys.exit(1)
        seen_titles.add(title)
        
        # Check for duplicate header combinations - CODE category can have duplicates, others cannot
        if category != 'CODE':
            header_combo = (category, language, framework)
            if header_combo in seen_headers:
                print(f"ERROR: Duplicate header combination (category={category}, language={language}, framework={framework}) found in file {file_path}")
                print(f"Note: Only CODE category allows duplicates, TABLE and TRIGGER must be unique")
                sys.exit(1)
            seen_headers.add(header_combo)
    
    print("Header validation completed successfully")

def process_files(input_dir, output_file, table_name='oracle_object_transformation_prompts'):
    """
    Process all txt files in directory and generate SQL
    """
    print(f"Scanning directory: {input_dir}")

    # Get all txt files
    txt_files = get_txt_files(input_dir)

    if not txt_files:
        print("No .txt files found")
        return
    
    # Validate headers and check duplicates before processing
    validate_headers_and_check_duplicates(txt_files)
    
    # Filter out archive files for processing
    processing_files = [f for f in txt_files if '/archive/' not in f]

    # Sort files by metadata fields
    sorted_txt_files = sort_files_by_metadata(processing_files)

    print(f"Found {len(txt_files)} txt files ({len(processing_files)} for processing, {len(txt_files) - len(processing_files)} archived)")
    print("Processing order (sorted by title, category, language, framework):")
    for i, file_path in enumerate(sorted_txt_files, 1):
        basename = os.path.basename(file_path)
        # Extract metadata for display
        content = read_file_content(file_path)
        if content:
            lines = content.split('\n')
            if lines:
                header_data = parse_header_metadata(lines[0])
                if header_data:
                    title, category, language, framework = header_data
                    default_marker = " (DEFAULT)" if is_default_template(title) else ""
                    print(f"  {i}. {title} [{category}/{language}/{framework}]{default_marker}")
    print()


    # Build order string from sorted files metadata
    order_items = []
    for file_path in sorted_txt_files:
        content = read_file_content(file_path)
        if content:
            lines = content.split('\n')
            if lines:
                header_data = parse_header_metadata(lines[0])
                if header_data:
                    title, category, language, framework = header_data
                    order_items.append(f"{title}[{category}/{language}/{framework}]")
    order_str = ",".join(order_items)
    # Generate all SQL statements
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    all_sql = f"-- Auto-generated SQL script\n"
    all_sql += f"-- Generated at: {current_time}\n"
    all_sql += f"-- File count: {len(txt_files)}\n"
    all_sql += f"-- Processing order: {order_str}）\n\n"
    all_sql += f"-- Truncate Table Before Init.\nTRUNCATE TABLE `oracle_object_transformation_prompts`;\n\n"

    successful_count = 0

    for i, file_path in enumerate(sorted_txt_files, 1):
        print(f"Processing file {i}/{len(processing_files)}: {os.path.basename(file_path)}")

        # Read file content
        content = read_file_content(file_path)
        if content is None:
            print(f"Skipping file: {os.path.basename(file_path)}")
            continue

        # Extract metadata from header
        lines = content.split('\n')
        header_data = parse_header_metadata(lines[0])
        if not header_data:
            print(f"ERROR: File {file_path} missing header during processing")
            continue
        title, _, _, _ = header_data

        # Generate SQL
        sql = generate_insert_sql(
            prompt_text=content,
            prompt_id=i,
            prompt_title=title,
            file_path=file_path,
            content=content,
            table_name=table_name
        )

        all_sql += f"-- File: {os.path.basename(file_path)}\n"
        all_sql += sql
        all_sql += "\n"

        successful_count += 1

    # Write SQL file
    if successful_count > 0:
        write_sql_file(all_sql, output_file)
        print(f"\nProcessing completed! Successfully processed {successful_count} files")
        print(f"Archive files skipped: {len(txt_files) - len(processing_files)}")
    else:
        print("\nNo files were successfully processed")

def print_usage():
    """
    Print usage instructions
    """
    script_name = os.path.basename(sys.argv[0])
    print("Usage:")
    print(f"  python {script_name} <txt_files_directory> [output_sql_file] [table_name]")
    print("\nExamples:")
    print(f"  python {script_name} ./prompts")
    print(f"  python {script_name} ./prompts output.sql")
    print(f"  python {script_name} ./prompts output.sql my_prompts_table")

def main():
    """
    Main function
    """
    if len(sys.argv) < 2:
        print_usage()
        return

    # Parse arguments
    input_dir = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'generated_prompts.sql'
    table_name = sys.argv[3] if len(sys.argv) > 3 else 'oracle_object_transformation_prompts'

    # Check input directory
    if not os.path.isdir(input_dir):
        print(f"Error: Directory does not exist - {input_dir}")
        return

    # Process files
    process_files(input_dir, output_file, table_name)

if __name__ == '__main__':
    main()