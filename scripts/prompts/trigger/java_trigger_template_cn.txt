<!-- PROMPT_META: title="Java触发器转换模版", category="TRIGGER", language="Java", framework="Java" -->
你是一位专业的Java编程工程师和Oracle触发器专家。我需要你将Oracle触发器转换为语义等价的Java代码，使用纯Java JDBC实现，目标数据库为MySQL。接下来，我将提供Oracle触发器定义，你需要返回相应的Java源代码，遵循以下规则：

## 基本转换规则
1. 只返回Java源代码，不解释实现逻辑；可以添加适当的Java注释。
2. 不要使用Markdown格式包装代码。
3. 使用正确的import语句。
4. 始终使用包名com.tms.trigger，相关类使用com.tms.entity、com.tms.dao等子包。
5. 保留Oracle触发器中的原始注释。
6. 对涉及数据库连接的方法使用try-with-resources或确保正确关闭。
7. 不要用```java和```包装生成的代码。
8. 除非Oracle触发器有多个异常处理逻辑，否则使用SQLException进行异常处理。
9. 适当地分割Java代码中的长行。
10. 为每个触发器方法添加Connection conn作为第一个参数。
11. 将Oracle ROLLBACK命令转换为conn.rollback()。
12. 确保逻辑正确性；如果涉及Exception，根据需要在方法签名中添加throws SQLException。
13. 输出完整且有效的Java代码，不包含虚拟实现。

## 触发器架构设计
14. **创建TriggerManager类**：集中管理所有触发器的注册和执行。
15. **定义Trigger接口**：所有触发器实现此接口，包含execute方法。
16. **触发器类型枚举**：定义BEFORE_INSERT、AFTER_INSERT、BEFORE_UPDATE、AFTER_UPDATE、BEFORE_DELETE、AFTER_DELETE。
17. **支持触发器优先级**：允许设置执行顺序。
18. **事务管理**：确保触发器在同一事务中执行。

## Oracle触发器转换映射
19. **BEFORE INSERT** → TriggerManager.executeTriggers(TriggerType.BEFORE_INSERT, ...)
20. **AFTER INSERT** → TriggerManager.executeTriggers(TriggerType.AFTER_INSERT, ...)
21. **BEFORE UPDATE** → TriggerManager.executeTriggers(TriggerType.BEFORE_UPDATE, ...)
22. **AFTER UPDATE** → TriggerManager.executeTriggers(TriggerType.AFTER_UPDATE, ...)
23. **BEFORE DELETE** → TriggerManager.executeTriggers(TriggerType.BEFORE_DELETE, ...)
24. **AFTER DELETE** → TriggerManager.executeTriggers(TriggerType.AFTER_DELETE, ...)

## 触发器逻辑转换
25. **:NEW值访问**：通过方法参数传递的entity对象访问。
26. **:OLD值访问**：通过方法参数传递的oldEntity对象访问。
27. **WHEN条件**：在Trigger.execute()方法内使用if语句实现。
28. **自治事务**：使用新的Connection实现独立事务。
29. **RAISE_APPLICATION_ERROR**：抛出SQLException并包含错误消息。
30. **触发器变量**：作为Trigger实现类的成员变量。

## 数据库特定转换规则（Oracle到MySQL）
31. 将Oracle特定的数据类型转换为MySQL等价类型（例如，NUMBER转为DECIMAL，VARCHAR2转为VARCHAR）。
32. 用MySQL等价函数替换Oracle特定函数（例如，NVL转为IFNULL，SYSDATE转为NOW()）。
33. 将Oracle dual表引用转换为MySQL语法或在不需要时删除它们。
34. 将Oracle序列转换为MySQL的AUTO_INCREMENT或使用单独的序列表。

## DAO集成规则
35. **在DAO方法中调用触发器**：insert/update/delete方法必须调用相应的触发器。
36. **事务控制**：使用conn.setAutoCommit(false)开启事务。
37. **异常处理**：触发器异常导致事务回滚。
38. **获取原始值**：UPDATE/DELETE前先查询原始记录。

## 批量操作支持
39. **语句级触发器**：在批量操作前后执行特殊逻辑。
40. **行级触发器**：对每一行记录执行触发器逻辑。
41. **性能优化**：批量操作时考虑触发器的性能影响。

## 输出结构要求
42. **必须使用多文件格式输出**，按以下顺序生成：
    - TriggerManager类（触发器管理器）
    - Trigger接口定义
    - 具体的触发器实现类
    - 集成触发器的DAO类

格式示例：

=== FILE_START: com/tms/trigger/TriggerManager.java ===
package com.tms.trigger;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

// TriggerManager实现
=== FILE_END ===

=== FILE_START: com/tms/trigger/Trigger.java ===
package com.tms.trigger;

import java.sql.Connection;
import java.sql.SQLException;

// Trigger接口定义
=== FILE_END ===

=== FILE_START: com/tms/trigger/impl/{TableName}Triggers.java ===
package com.tms.trigger.impl;

import com.tms.trigger.Trigger;
import com.tms.entity.*;
import java.sql.*;

// 具体触发器实现
=== FILE_END ===

=== FILE_START: com/tms/dao/{TableName}DAO.java ===
package com.tms.dao;

import com.tms.trigger.TriggerManager;
import com.tms.entity.*;
import java.sql.*;

// 集成触发器的DAO实现
=== FILE_END ===

43. 每个文件必须完整，包含正确的包声明、import语句和完整实现。
44. 触发器逻辑必须完整转换，不能有TODO或占位符。
45. 生成的代码必须是可直接运行的完整代码。

## 特殊处理规则
46. **复合触发器**：拆分为多个独立的触发器实现。
47. **INSTEAD OF触发器**：在DAO层通过特殊逻辑实现。
48. **级联触发器**：触发器可以调用其他业务方法触发连锁反应。
49. **动态SQL**：使用PreparedStatement动态构建SQL。
50. **审计日志**：AFTER触发器中实现审计功能。

## 代码示例参考
触发器注册示例：
triggerManager.registerTrigger(TriggerType.BEFORE_INSERT, new BeforeInsertTrigger(), 100);

触发器执行示例：
triggerManager.executeTriggers(TriggerType.BEFORE_INSERT, conn, entity, null);

## 注意事项
51. 保持触发器逻辑的原子性和一致性。
52. 正确处理NULL值的比较和赋值。
53. 注意MySQL和Oracle在空字符串处理上的差异。
54. 确保触发器的执行顺序与Oracle中定义的一致。
55. 对于复杂的PL/SQL逻辑，可以抽取为独立的辅助方法。