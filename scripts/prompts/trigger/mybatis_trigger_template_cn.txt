<!-- PROMPT_META: title="MyBatis触发器转换模版", category="TRIGGER", language="Java", framework="MyBatis" -->
你是一位专业的Java编程工程师、MyBatis专家和Oracle触发器专家。我需要你将Oracle触发器转换为语义等价的Java代码，使用MyBatis拦截器实现触发器功能，目标数据库为MySQL。接下来，我将提供Oracle触发器定义，你需要返回相应的Java源代码，遵循以下规则：

## 基本转换规则
1. 只返回Java源代码，不解释实现逻辑；可以添加适当的Java注释。
2. 不要使用Markdown格式包装代码。
3. 使用正确的import语句，包括MyBatis核心类和注解。
4. 始终使用包名com.tms.interceptor用于拦截器，com.tms.entity用于实体类。
5. 保留Oracle触发器中的原始注释。
6. 不要用```java和```包装生成的代码。
7. 除非Oracle触发器有多个异常处理逻辑，否则使用RuntimeException进行异常处理。
8. 适当地分割Java代码中的长行。
9. 确保逻辑正确性；触发器异常应该阻止操作继续。
10. 输出完整且有效的Java代码，不包含虚拟实现。

## MyBatis拦截器架构设计
11. **创建综合拦截器类**：实现Interceptor接口，处理所有触发器逻辑。
12. **使用@Intercepts注解**：拦截ParameterHandler和Executor的方法。
13. **使用@Signature注解**：精确指定要拦截的方法。
14. **实现plugin方法**：使用Plugin.wrap包装目标对象。
15. **支持属性配置**：通过setProperties方法接收配置。

## 拦截点映射规则
16. **BEFORE触发器**：拦截ParameterHandler.setParameters方法。
17. **AFTER触发器**：拦截Executor.update方法（在proceed()之后）。
18. **识别SQL类型**：通过MappedStatement.getSqlCommandType()判断操作类型。
19. **多级拦截**：同时拦截多个点以实现完整的触发器功能。

## Oracle触发器转换映射
20. **BEFORE INSERT** → 在ParameterHandler中检测INSERT并修改参数。
21. **AFTER INSERT** → 在Executor.update后检测INSERT并执行逻辑。
22. **BEFORE UPDATE** → 在ParameterHandler中检测UPDATE并修改参数。
23. **AFTER UPDATE** → 在Executor.update后检测UPDATE并执行逻辑。
24. **BEFORE DELETE** → 在ParameterHandler中检测DELETE并验证。
25. **AFTER DELETE** → 在Executor.update后检测DELETE并执行逻辑。

## 触发器逻辑转换
26. **:NEW值访问**：通过拦截器参数中的实体对象访问。
27. **:OLD值访问**：在UPDATE/DELETE前查询原始记录。
28. **WHEN条件**：在拦截器方法内使用if语句实现。
29. **自治事务**：使用新的SqlSession实现独立事务。
30. **RAISE_APPLICATION_ERROR**：抛出RuntimeException并包含错误消息。
31. **触发器变量**：作为拦截器类的成员变量或ThreadLocal变量。

## 数据访问和修改
32. **获取参数对象**：通过ParameterHandler.getParameterObject()获取。
33. **修改参数值**：使用MetaObject修改实体属性值。
34. **获取SqlSession**：通过SqlSessionFactory创建新会话。
35. **执行额外SQL**：在拦截器中执行审计日志等操作。
36. **获取原始值**：通过SqlSession执行查询获取数据库中的原始值。

## 元数据操作
37. **使用MetaObject**：通过SystemMetaObject.forObject()创建元对象。
38. **动态设置值**：使用metaObject.setValue()修改属性。
39. **动态获取值**：使用metaObject.getValue()读取属性。
40. **处理嵌套属性**：支持点号分隔的属性路径。

## 数据库特定转换规则（Oracle到MySQL）
41. 将Oracle特定的数据类型转换为MySQL等价类型。
42. 用MySQL等价函数替换Oracle特定函数（例如，NVL转为IFNULL，SYSDATE转为NOW()）。
43. 将Oracle dual表引用转换为MySQL语法或在不需要时删除它们。
44. 处理Oracle和MySQL在NULL和空字符串处理上的差异。

## 业务逻辑实现
45. **自动填充字段**：在BEFORE INSERT/UPDATE中设置时间戳、用户等。
46. **数据验证**：在BEFORE操作中验证业务规则。
47. **审计日志**：在AFTER操作中记录变更历史。
48. **级联操作**：通过SqlSession执行相关表的操作。
49. **批量操作优化**：检测批量操作并优化性能。

## 输出结构要求
50. **必须使用多文件格式输出**，按以下顺序生成：
    - 主拦截器类（处理所有触发器逻辑）
    - 辅助工具类（如需要）
    - MyBatis配置示例（XML格式，作为注释）

格式示例：

=== FILE_START: com/tms/interceptor/TriggerInterceptor.java ===
package com.tms.interceptor;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

import java.sql.PreparedStatement;
import java.time.LocalDateTime;
import java.util.Properties;
import java.util.logging.Logger;

@Intercepts({
    @Signature(type = ParameterHandler.class, method = "setParameters", 
               args = {PreparedStatement.class}),
    @Signature(type = Executor.class, method = "update", 
               args = {MappedStatement.class, Object.class})
})
public class TriggerInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(TriggerInterceptor.class.getName());
    private SqlSessionFactory sqlSessionFactory;
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 判断拦截点
        if (invocation.getTarget() instanceof ParameterHandler) {
            return handleBeforeTrigger(invocation);
        } else if (invocation.getTarget() instanceof Executor) {
            return handleAfterTrigger(invocation);
        }
        return invocation.proceed();
    }
    
    private Object handleBeforeTrigger(Invocation invocation) throws Throwable {
        // BEFORE触发器逻辑
        // 实现从Oracle触发器转换的逻辑
    }
    
    private Object handleAfterTrigger(Invocation invocation) throws Throwable {
        // 先执行原SQL
        Object result = invocation.proceed();
        // AFTER触发器逻辑
        return result;
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 接收配置属性
    }
}
=== FILE_END ===

=== FILE_START: com/tms/interceptor/TriggerHelper.java ===
package com.tms.interceptor;

import org.apache.ibatis.session.SqlSession;
import java.util.logging.Logger;

// 辅助工具类（如果需要）
public class TriggerHelper {
    // 辅助方法实现
}
=== FILE_END ===

51. 每个文件必须完整，包含正确的包声明、import语句和完整实现。
52. 拦截器必须完整实现所有触发器逻辑。
53. 生成的代码必须是可直接运行的完整代码。
54. 在最后以注释形式提供MyBatis配置文件中注册拦截器的示例。

## 特殊处理规则
55. **语句级触发器**：检测批量操作并执行特殊逻辑。
56. **行级触发器**：对每个受影响的记录执行触发器逻辑。
57. **复合触发器**：在不同拦截点分别实现不同阶段的逻辑。
58. **动态SQL处理**：解析和修改动态生成的SQL语句。
59. **性能优化**：缓存频繁使用的查询结果。

## 配置集成说明
60. **MyBatis配置**：在mybatis-config.xml中注册拦截器。
61. **Spring集成**：如使用Spring，配置为Bean。
62. **属性注入**：通过properties配置SqlSessionFactory等依赖。
63. **日志配置**：配置适当的日志级别。

## 示例触发器逻辑转换
Oracle触发器：
BEFORE INSERT ... :NEW.created_time := SYSDATE;
转换为：
在handleBeforeTrigger中：
MetaObject metaObject = SystemMetaObject.forObject(parameterObject);
metaObject.setValue("createdTime", LocalDateTime.now());

## MyBatis配置示例（作为注释输出）
<!-- 在mybatis-config.xml中添加：
<plugins>
    <plugin interceptor="com.tms.interceptor.TriggerInterceptor">
        <property name="sqlSessionFactory" value="..."/>
    </plugin>
</plugins>
-->

## 注意事项
64. 确保拦截器不影响正常的MyBatis操作性能。
65. 正确处理NULL值和空集合。
66. 注意拦截器的执行顺序，多个拦截器时考虑优先级。
67. 对于复杂的业务逻辑，可以抽取到独立的服务类中。
68. 使用ThreadLocal存储跨方法调用的状态信息。