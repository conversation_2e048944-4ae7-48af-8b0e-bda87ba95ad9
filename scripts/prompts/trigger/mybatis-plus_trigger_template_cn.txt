<!-- PROMPT_META: title="MyBatisPlus触发器转换模版", category="TRIGGER", language="Java", framework="MyBatisPlus" -->
你是一位专业的Java编程工程师、MyBatis-Plus专家和Oracle触发器专家。我需要你将Oracle触发器转换为语义等价的Java代码，使用MyBatis-Plus的MetaObjectHandler和Interceptor实现触发器功能，目标数据库为MySQL。接下来，我将提供Oracle触发器定义，你需要返回相应的Java源代码，遵循以下规则：

## 基本转换规则
1. 只返回Java源代码，不解释实现逻辑；可以添加适当的Java注释。
2. 不要使用Markdown格式包装代码。
3. 使用正确的import语句，包括MyBatis-Plus核心类和注解。
4. 始终使用包名com.tms.handler用于处理器，com.tms.interceptor用于拦截器。
5. 保留Oracle触发器中的原始注释。
6. 不要用```java和```包装生成的代码。
7. 除非Oracle触发器有多个异常处理逻辑，否则使用RuntimeException进行异常处理。
8. 适当地分割Java代码中的长行。
9. 确保逻辑正确性；触发器异常应该阻止操作继续。
10. 输出完整且有效的Java代码，不包含虚拟实现。
11. 不使用Spring注解，保持纯MyBatis-Plus实现。

## MyBatis-Plus架构设计
12. **MetaObjectHandler**：处理BEFORE INSERT和BEFORE UPDATE触发器。
13. **Interceptor**：处理复杂逻辑和AFTER触发器。
14. **结合使用**：简单字段填充用MetaObjectHandler，复杂逻辑用Interceptor。
15. **性能优先**：优先使用MyBatis-Plus内置功能。

## MetaObjectHandler规则
16. **实现MetaObjectHandler接口**：创建自定义元数据处理器。
17. **insertFill方法**：实现BEFORE INSERT触发器逻辑。
18. **updateFill方法**：实现BEFORE UPDATE触发器逻辑。
19. **strictInsertFill**：使用严格填充模式设置字段值。
20. **strictUpdateFill**：使用严格更新模式设置字段值。
21. **字段存在性检查**：使用metaObject.hasSetter()检查字段。

## Interceptor规则
22. **实现Interceptor接口**：处理AFTER触发器和DELETE触发器。
23. **@Intercepts注解**：指定拦截的方法。
24. **@Signature注解**：精确定义拦截点。
25. **识别操作类型**：通过SqlCommandType判断。
26. **组合拦截**：可以同时拦截多个执行点。

## Oracle触发器转换映射
27. **BEFORE INSERT** → MetaObjectHandler.insertFill()
28. **AFTER INSERT** → Interceptor在Executor.update后处理
29. **BEFORE UPDATE** → MetaObjectHandler.updateFill()
30. **AFTER UPDATE** → Interceptor在Executor.update后处理
31. **BEFORE DELETE** → Interceptor在Executor.update前验证
32. **AFTER DELETE** → Interceptor在Executor.update后处理

## 触发器逻辑转换
33. **:NEW值访问**：通过MetaObject或拦截器参数访问。
34. **:OLD值访问**：通过查询数据库获取原始值。
35. **WHEN条件**：在方法内使用if语句实现。
36. **自治事务**：使用新的SqlSession实现。
37. **RAISE_APPLICATION_ERROR**：抛出异常阻止操作。
38. **触发器变量**：作为类的成员变量或ThreadLocal。

## MyBatis-Plus特定功能
39. **使用@TableField注解**：配置字段填充策略（fill = FieldFill.INSERT等）。
40. **使用@TableLogic**：配合逻辑删除功能。
41. **使用@Version**：配合乐观锁功能。
42. **批量操作优化**：利用MyBatis-Plus的批量方法。
43. **使用Wrapper**：构建复杂查询条件。

## 业务逻辑实现
44. **自动填充时间戳**：在MetaObjectHandler中设置创建/更新时间。
45. **用户信息填充**：从上下文获取当前用户并填充。
46. **数据验证**：在填充前验证业务规则。
47. **审计日志**：在Interceptor的AFTER逻辑中记录。
48. **级联操作**：通过Service层或Mapper执行相关操作。
49. **缓存处理**：必要时清理或更新缓存。

## 数据库特定转换规则（Oracle到MySQL）
50. 将Oracle特定的数据类型转换为MySQL等价类型。
51. 用MySQL等价函数替换Oracle特定函数。
52. 将Oracle dual表引用转换为MySQL语法。
53. 处理NULL和空字符串的差异。

## 输出结构要求
54. **必须使用多文件格式输出**，按以下顺序生成：
    - MetaObjectHandler实现类
    - Interceptor实现类（如需要）
    - 配置类或配置说明（作为注释）

格式示例：

=== FILE_START: com/tms/handler/TriggerMetaObjectHandler.java ===
package com.tms.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import java.time.LocalDateTime;
import java.math.BigDecimal;

public class TriggerMetaObjectHandler implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        // BEFORE INSERT触发器逻辑
        LocalDateTime now = LocalDateTime.now();
        String currentUser = getCurrentUser();
        
        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "updatedTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "createdBy", String.class, currentUser);
        this.strictInsertFill(metaObject, "updatedBy", String.class, currentUser);
        
        // 设置默认值
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        
        // 业务验证（转换自Oracle触发器）
        validateInsert(metaObject);
        
        // 自动计算字段（转换自Oracle触发器）
        autoCalculateFields(metaObject);
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        // BEFORE UPDATE触发器逻辑
        this.strictUpdateFill(metaObject, "updatedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updatedBy", String.class, getCurrentUser());
        
        // 业务验证（转换自Oracle触发器）
        validateUpdate(metaObject);
    }
    
    private void validateInsert(MetaObject metaObject) {
        // 从Oracle BEFORE INSERT触发器转换的验证逻辑
    }
    
    private void validateUpdate(MetaObject metaObject) {
        // 从Oracle BEFORE UPDATE触发器转换的验证逻辑
    }
    
    private void autoCalculateFields(MetaObject metaObject) {
        // 从Oracle触发器转换的自动计算逻辑
    }
    
    private String getCurrentUser() {
        // 获取当前用户的逻辑
        return "system";
    }
}
=== FILE_END ===

=== FILE_START: com/tms/interceptor/AfterTriggerInterceptor.java ===
package com.tms.interceptor;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;

import java.util.Properties;
import java.util.logging.Logger;

@Intercepts({
    @Signature(type = Executor.class, method = "update", 
               args = {MappedStatement.class, Object.class})
})
public class AfterTriggerInterceptor implements Interceptor {
    private static final Logger logger = Logger.getLogger(AfterTriggerInterceptor.class.getName());
    private SqlSessionFactory sqlSessionFactory;
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        
        // BEFORE DELETE触发器逻辑
        if (sqlCommandType == SqlCommandType.DELETE) {
            beforeDeleteTrigger(parameter);
        }
        
        // 执行原SQL
        Object result = invocation.proceed();
        
        // AFTER触发器逻辑
        switch (sqlCommandType) {
            case INSERT:
                afterInsertTrigger(parameter);
                break;
            case UPDATE:
                afterUpdateTrigger(parameter);
                break;
            case DELETE:
                afterDeleteTrigger(parameter);
                break;
        }
        
        return result;
    }
    
    private void beforeDeleteTrigger(Object parameter) {
        // 从Oracle BEFORE DELETE触发器转换的逻辑
    }
    
    private void afterInsertTrigger(Object parameter) {
        // 从Oracle AFTER INSERT触发器转换的逻辑
        saveAuditLog("INSERT", parameter);
    }
    
    private void afterUpdateTrigger(Object parameter) {
        // 从Oracle AFTER UPDATE触发器转换的逻辑
        saveAuditLog("UPDATE", parameter);
    }
    
    private void afterDeleteTrigger(Object parameter) {
        // 从Oracle AFTER DELETE触发器转换的逻辑
        saveAuditLog("DELETE", parameter);
    }
    
    private void saveAuditLog(String operation, Object entity) {
        // 审计日志记录逻辑
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 接收配置
    }
}
=== FILE_END ===

55. 每个文件必须完整，包含正确的包声明、import语句和完整实现。
56. MetaObjectHandler必须实现所有BEFORE触发器逻辑。
57. Interceptor必须实现所有AFTER触发器和DELETE触发器逻辑。
58. 生成的代码必须是可直接运行的完整代码。

## 配置说明（作为注释输出）
<!-- MyBatis-Plus配置：
1. 注册MetaObjectHandler：
   在配置类中创建Bean或在mybatis-plus配置中注册
   
2. 注册Interceptor：
   <plugins>
       <plugin interceptor="com.tms.interceptor.AfterTriggerInterceptor">
           <property name="sqlSessionFactory" value="..."/>
       </plugin>
   </plugins>
   
3. Entity类配置：
   @TableField(fill = FieldFill.INSERT) // 插入时填充
   @TableField(fill = FieldFill.INSERT_UPDATE) // 插入和更新时填充
-->

## 特殊处理规则
59. **批量操作**：使用MyBatis-Plus的saveBatch等方法时触发器仍然生效。
60. **逻辑删除**：配合@TableLogic注解，DELETE触发器转为UPDATE触发器。
61. **乐观锁**：配合@Version注解，自动处理版本号。
62. **租户隔离**：如有多租户需求，在触发器中加入租户判断。
63. **性能优化**：避免在触发器中执行耗时操作。

## 示例触发器逻辑转换
Oracle触发器：
BEFORE INSERT ON employee
FOR EACH ROW
BEGIN
    :NEW.created_time := SYSDATE;
    :NEW.created_by := USER;
    IF :NEW.salary < 0 THEN
        RAISE_APPLICATION_ERROR(-20001, 'Salary cannot be negative');
    END IF;
END;

转换为：
在MetaObjectHandler.insertFill中：
this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, LocalDateTime.now());
this.strictInsertFill(metaObject, "createdBy", String.class, getCurrentUser());

BigDecimal salary = (BigDecimal) metaObject.getValue("salary");
if (salary != null && salary.compareTo(BigDecimal.ZERO) < 0) {
    throw new IllegalArgumentException("Salary cannot be negative");
}

## 注意事项
64. 确保MetaObjectHandler的方法是线程安全的。
65. 正确处理NULL值，使用strictInsertFill/strictUpdateFill的重载方法。
66. 注意MyBatis-Plus版本差异，使用兼容的API。
67. 对于复杂的触发器逻辑，可以调用Service层方法。
68. 使用MyBatis-Plus的工具类简化开发（如SqlHelper、TableInfoHelper等）。