package main

import (
	"context"
	"errors"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"gitee.com/pingcap_enterprise/tms/server/crypto"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/file"

	_ "net/http/pprof"

	_ "gitee.com/pingcap_enterprise/tms/docs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/service"
	"gitee.com/pingcap_enterprise/tms/server/web/route"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/versioninfo"
	"github.com/gin-gonic/contrib/static"
	"github.com/gin-gonic/gin"
)

// @title TiMS UI API
// @version 1.0
// @description TiMS UI API

// @contact.name carter
// @contact.email <EMAIL>

// @host localhost:8082
// @BasePath /api/v1/

func main() {
	// Check for hidden commands before normal config parsing
	for _, arg := range os.Args[1:] {
		if arg == "-check-lock" || arg == "--check-lock" {
			license.ProcessCheckLockCommand(os.Args[1:])
			os.Exit(0)
		}
	}

	ctx := context.Background()
	config := config.NewConfig(constants.WEB_ROLE)
	if err := config.Parse(os.Args[1:]); err != nil {
		fmt.Println(fmt.Sprintf("Start server failed. error is [%s], Use '--help' for help.", err))
		return
	}
	if err := createDataDir(config.DataDir); err != nil {
		fmt.Println(fmt.Sprintf("Start server failed. create data-dir failed.error:%s", err))
		return
	}
	initLog(config.LogConfig)
	log.Info(fmt.Sprintf("config is %s", config))
	if config.GenKey {
		processKey()
		os.Exit(0)
	}

	if config.Encrypt {
		processEncrypt(os.Args)
		os.Exit(0)
	}

	if config.EnablePprof {
		fmt.Println(fmt.Sprintf("Start pprof, listen port %d", config.PprofPort))
		go initPprof(config)
	}

	initService(config)
	updateSystemInfo(ctx)
	checkLicense(ctx, config)
	batchUpdateTaskStatus(ctx)
	initGinEngine(config)
}

func initPprof(config *config.Config) {
	func() {
		listenAddr := "0.0.0.0:7999"
		if config.PprofPort != 0 {
			listenAddr = fmt.Sprintf("0.0.0.0:%d", config.PprofPort)
		}
		http.ListenAndServe(listenAddr, nil)
	}()
}

func initGinEngine(config *config.Config) error {
	gin.SetMode(gin.ReleaseMode)
	gin.DisableConsoleColor()
	gin.DefaultWriter = io.MultiWriter(log.GetRootLogger().GetRootWriters()...)
	gin.DefaultErrorWriter = gin.DefaultWriter
	g := gin.New()
	route.Route(g)
	g.Use(static.Serve("/", static.LocalFile(config.StaticFilePath, true)))
	g.NoRoute(func(ctx *gin.Context) {
		indexHTML, err := ioutil.ReadFile(fmt.Sprintf("%s/index.html", config.StaticFilePath))
		if err != nil {
			ctx.Status(http.StatusNotFound)
			return
		}
		ctx.Header("Content-Type", "text/html; charset=utf-8")
		ctx.String(http.StatusOK, string(indexHTML))
	})

	addr := fmt.Sprintf(":%d", config.ServerPort)

	if err := g.Run(addr); err != nil {
		log.Error("start server failed.", err)
		panic(err)
	}

	return nil
}

func initService(config *config.Config) error {
	err := models.CreateDatabase(config.DBConfig)
	if err != nil {
		panic(err)
	}
	service.InitService()
	return nil
}

func updateSystemInfo(ctx context.Context) {
	service.GetSystemInfoService().InsertOrUpdateSystemInfo(ctx)
}

func initLog(conf *log.LogConfig) {
	log.NewRootLoggerFromConfig(conf)
	log.NewAssessmentLogger()
	log.NewTidbStatsLogLogger()
	versioninfo.PrintVersionInfo()
}

func createDataDir(dataDir string) error {
	if err := file.CreateIfNotExist(dataDir); err != nil {
		return err
	}
	return nil
}

func processKey() {
	ls := license.NewLicenseService()
	if key, err := ls.GenerateKey(); err != nil {
		fmt.Println(fmt.Sprintf("generate key failed. err:%v", err))
	} else {
		fmt.Println(fmt.Sprintf("key:%s", key))
	}
}

func processEncrypt(args []string) {
	if len(args) <= 2 {
		fmt.Println("Please enter the string to be encrypted")
		os.Exit(1)
	}
	encryptVal := crypto.AesDefaultEncrypt(args[2], crypto.DefaultKey)
	fmt.Println(fmt.Sprintf("enc_%s", encryptVal))
}

func checkLicense(ctx context.Context, cfg *config.Config) error {
	if strings.TrimSpace(cfg.License) == "" {
		return errors.New("license is empty")
	}
	return license.StartCheckLicenseWorker(ctx, cfg.License)
}

func batchUpdateTaskStatus(ctx context.Context) {
	service.GetChannelService().BatchUpdateTaskStatus(ctx)
}
