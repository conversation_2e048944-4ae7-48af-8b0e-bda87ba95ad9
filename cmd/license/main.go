package main

import (
	"flag"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"os"
	"strings"
)

func main() {
	options := flag.NewFlagSet("license generation tool", flag.ContinueOnError)
	options.Usage = func() {
		fmt.Fprintln(os.Stderr, "Usage of license generation tool:")
		options.PrintDefaults()
	}
	inputKey := options.String("key", "", "input key")
	days := options.Int("days", 3, "number of day")
	opProcedureNum := options.Uint(constants.OpProcedureNum.String(), 8, "number of operation procedures")
	opFunctionNum := options.Uint(constants.OpFunctionNum.String(), 8, "number of operation functions")
	opTriggerNum := options.Uint(constants.OpTriggerNum.String(), 8, "number of operation triggers")
	opPackageNum := options.Uint(constants.OpPackageNum.String(), 4, "number of operation packages")

	err := options.Parse(os.Args[1:])
	switch err {
	case nil:
	case flag.ErrHelp:
		os.Exit(0)
	default:
		os.Exit(2)
	}

	if strings.EqualFold(strings.TrimSpace(*inputKey), "") {
		options.Usage()
		return
	}

	log.InitTestLogger()

	opt := license.GenerateOpt{
		SignedToken:    *inputKey,
		NumberOfDate:   *days,
		OpProcedureNum: *opProcedureNum,
		OpFunctionNum:  *opFunctionNum,
		OpPackageNum:   *opPackageNum,
		OpTriggerNum:   *opTriggerNum,
	}

	ls := license.NewLicenseService()
	token, getTokenErr := ls.GenerateLicenseFromKey(opt)
	if getTokenErr != nil {
		fmt.Println("generate license from key error:", getTokenErr)
		return
	}
	fmt.Println(fmt.Sprintf("license info:\n%s", token))
}
