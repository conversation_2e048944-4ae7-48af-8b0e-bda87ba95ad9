package structs

import "fmt"

type StructureParam struct {
	LowerCaseFieldNames    uint
	LowerCaseTableNames    uint
	MigrationIndexParallel uint
	MigrationTableParallel uint
	SyncMetaBatchSize      uint
	SyncMetaSQLHint        string
	PunctReplacement       string
	CreatePkWithTable      string
	RunVersion             string
}

const MigrationIndexParallelDefault = 8
const MigrationTableParallelDefault = 8
const SyncMetaBatchSizeDefault = 512

func (i *StructureParam) String() string {
	return fmt.Sprintf("<StructureParam> LowerCaseFieldNames: %d, LowerCaseTableNames: %d, MigrationIndexParallel: %d, MigrationTableParallel: %d, SyncMetaBatchSize: %d, SyncMetaSQLHint: %s",
		i.LowerCaseFieldNames, i.LowerCaseTableNames, i.MigrationIndexParallel, i.MigrationTableParallel, i.SyncMetaBatchSize, i.SyncMetaSQLHint)
}

func (i *StructureParam) Adjust() {
	if i.MigrationIndexParallel <= 0 {
		i.MigrationIndexParallel = MigrationIndexParallelDefault
	}
	if i.MigrationTableParallel <= 0 {
		i.MigrationTableParallel = MigrationTableParallelDefault
	}
	if i.SyncMetaBatchSize <= 0 {
		i.SyncMetaBatchSize = SyncMetaBatchSizeDefault
	}
}

func (i *StructureParam) GetLowerCaseFieldNames() uint {
	return i.LowerCaseFieldNames
}
func (i *StructureParam) GetLowerCaseTableNames() uint {
	return i.LowerCaseTableNames
}
func (i *StructureParam) GetMigrationIndexParallel() uint {
	return i.MigrationIndexParallel
}
func (i *StructureParam) GetMigrationTableParallel() uint {
	return i.MigrationTableParallel
}
func (i *StructureParam) GetSyncMetaBatchSize() uint {
	return i.SyncMetaBatchSize
}
func (i *StructureParam) GetSyncMetaSQLHint() string {
	return i.SyncMetaSQLHint
}
func (i *StructureParam) GetPunctReplacement() string {
	return i.PunctReplacement
}
