package structs

import "strings"

type ChannelSchemaMapper struct {
	m map[string]string
}

func NewChannelSchemaMapper() *ChannelSchemaMapper {
	return &ChannelSchemaMapper{
		m: make(map[string]string),
	}
}

func (i *ChannelSchemaMapper) SetSchemaMapping(sourceSchema, targetSchema string) {
	if i.m == nil {
		i.m = make(map[string]string)
	}
	i.m[strings.ToLower(sourceSchema)] = strings.ToLower(targetSchema)
}

func (i *ChannelSchemaMapper) GetTargetSchema(parsingSchema, sourceSchema string) string {
	parsingSchema = strings.ToLower(parsingSchema)
	sourceSchema = strings.ToLower(sourceSchema)

	if parsingSchema != "" && i.m[parsingSchema] != "" {
		return i.m[parsingSchema]
	}
	if sourceSchema != "" && i.m[sourceSchema] != "" {
		return i.m[sourceSchema]
	}
	return parsingSchema
}

func (i *ChannelSchemaMapper) GetTargetSchemaForFile(sourceSchema string) string {
	sourceSchema = strings.ToLower(sourceSchema)
	if i.m[sourceSchema] != "" {
		return i.m[sourceSchema]
	}
	for k := range i.m {
		return i.m[k]
	}
	return ""
}
