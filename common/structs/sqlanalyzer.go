package structs

import (
	"errors"
	"fmt"
	"io"
	"os"
	"reflect"
	"strings"
	"syscall"
	"time"

	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type DBConfigOracle struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Sid      string `json:"sid"`
}

func (i DBConfigOracle) GetUpperUser() string {
	if i.User == "" {
		return i.User
	}
	return strings.ToUpper(i.User)
}

func (i DBConfigOracle) String() string {
	return fmt.Sprintf("<DBConfigOracle Host: %s, Port: %d, User: %s, Password: %s, Sid: %s >", i.<PERSON>, i.<PERSON>, i.User, i.Password, i.Sid)
}

func (i DBConfigTiDB) String() string {
	return fmt.Sprintf("<DBConfigTiDB Host: %s, Port: %d, User: %s, Password: %s, Database: %s >", i.Host, i.Port, i.User, i.Password, i.Database)
}

type DBConfigTiDB struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	Database string `json:"database"`
}

type SQLFactor struct {
	IncludingSchemas     []string `json:"including_schemas"`
	IncludingSQLTypes    []string `json:"including_sqltypes"`
	ExcludeModules       []string `json:"exclude_modules"`
	ExcludeSQLTxts       []string `json:"exclude_sqltxts"`
	ExcludeSQLIDs        []string `json:"exclude_sqlids"`
	TiDBExecStatusFilter []string `json:"tidb_exec_status_filter"`

	SQLOrderBy     string `json:"sql_orderby"`
	AnalyzeTimeout int    `json:"analyze_timeout"`

	SQLRowLimit   int    `json:"sql_row_limit"`
	SQLSetName    string `json:"sqlset_name"`
	AnalyzeThread int    `json:"analyze_thread"`
	TopNPerStatus int    `json:"topn_per_status"`

	includingSchemaMap   map[string]bool
	includingSQLTypeList []int
	excludeSQLIdsMap     map[string]bool
	excludeSQLList       []string
	excludeModulesList   []string
}

type Parameter struct {
	LLMProviderAPIConfig

	TaskID               int               `json:"taskId"`
	ChannelID            int               `json:"channelId"`
	RunVersion           string            `json:"runVersion"`
	AppSQLsSource        common.SourceType `json:"sqlsSource"`
	AppSQLsFilename      string            `json:"sqlfilename"`
	IgnoreSQLIDsFilename string            `json:"ignoresqlfile"`
	ExplainOnly          string            `json:"explainonly"`
	TidbConnStr          *DBConfigTiDB     `json:"tidb_conn_str"`
	OracleConnStr        *DBConfigOracle   `json:"oracle_conn_str"`
	SQLFactor            *SQLFactor        `json:"sql_factor"`
	EnableSQLRewrite     bool              `json:"enable_openai_suggestion"`

	sqlFileContent *FileContent
}

func (i *Parameter) GetSQLFileContent() *FileContent {
	return i.sqlFileContent
}

func (i *Parameter) GetAppSQLsSource() common.SourceType {
	return i.AppSQLsSource
}

func (i *Parameter) IsRunVersionV1() bool {
	return i.RunVersion == "v1"
}

func (i *Parameter) SetIgnoreSQLIDsFilename(filename string) {
	i.IgnoreSQLIDsFilename = filename
}

func (i *SQLFactor) SetIncludingSchemas(schemas []string) {
	i.IncludingSchemas = schemas
}

func (i *SQLFactor) SetIncludingSQLTypes(sqlTypes []string) {
	i.IncludingSQLTypes = sqlTypes
}

func (i *SQLFactor) SetExecStatusFilter(statusFilter []string) {
	i.TiDBExecStatusFilter = statusFilter
}

func (i *SQLFactor) SetExcludeModules(modules []string) {
	modules = append(modules, "TMS")
	modules = lo.Uniq(modules)
	i.ExcludeModules = modules
}

func (i *SQLFactor) SetExcludeSQLTxts(sqlTxts []string) {
	i.ExcludeSQLTxts = sqlTxts
}

func (i *SQLFactor) GetTiDBExecStatusFilter() []string {
	return i.TiDBExecStatusFilter
}

func (i *SQLFactor) SetExcludeSQLIDs(sqlIds []string) {
	i.ExcludeSQLIDs = sqlIds
}

func (i *SQLFactor) SetSQLOrderBy(orderBy string) {
	i.SQLOrderBy = orderBy
}

func (i *SQLFactor) SetAnalyzeTimeout(timeout int) {
	i.AnalyzeTimeout = timeout
}

func (i *SQLFactor) SetSQLRowLimit(rowLimit int) {
	i.SQLRowLimit = rowLimit
}

func (i *SQLFactor) SetSQLSetName(setName string) {
	if setName == "" {
		setName = "sqlanalyzeset"
	}
	i.SQLSetName = setName
}

func (i *SQLFactor) SetAnalyzeThread(thread int) {
	i.AnalyzeThread = thread
}

func (i *SQLFactor) SetTopNPerStatus(topn int) {
	i.TopNPerStatus = topn
}

func (i *SQLFactor) GetIncludingSQLTypes() []string {
	return i.IncludingSQLTypes
}
func (i *SQLFactor) GetExcludeModules() []string {
	return i.ExcludeModules
}
func (i *SQLFactor) GetExcludeSQLTxts() []string {
	return i.ExcludeSQLTxts
}
func (i *SQLFactor) GetExcludeSQLIDs() []string {
	return i.ExcludeSQLIDs
}
func (i *SQLFactor) GetSQLOrderBy() string {
	return i.SQLOrderBy
}
func (i *SQLFactor) GetAnalyzeTimeout() int {
	if i.AnalyzeTimeout <= 0 {
		return 5
	}
	return i.AnalyzeTimeout
}
func (i *SQLFactor) GetSQLRowLimit() int {
	return i.SQLRowLimit
}
func (i *SQLFactor) GetSQLSetName() string {
	return i.SQLSetName
}

func (i *SQLFactor) GetAnalyzeThread() int {
	if i.AnalyzeThread <= 0 {
		return 8
	}
	return i.AnalyzeThread
}
func (i *SQLFactor) GetTopNPerStatus() int {
	return i.TopNPerStatus
}
func (i *SQLFactor) GetIncludingSchemaMap() map[string]bool {
	return i.includingSchemaMap
}
func (i *SQLFactor) GetIncludingSQLTypeList() []int {
	return i.includingSQLTypeList
}
func (i *SQLFactor) GetExcludeSQLIdsMap() map[string]bool {
	return i.excludeSQLIdsMap
}
func (i *SQLFactor) GetExcludeSQLIds() []string {
	return i.ExcludeSQLIDs
}
func (i *SQLFactor) GetExcludeSQLList() []string {
	return i.excludeSQLList
}
func (i *SQLFactor) GetExcludeModulesList() []string {
	return i.excludeModulesList
}

func (i *SQLFactor) SetIncludingSchemaMap(schemaMap map[string]bool) {
	i.includingSchemaMap = schemaMap
}

func (i *SQLFactor) SetExcludeSQLIdsMap(sqlIdsMap map[string]bool) {
	i.excludeSQLIdsMap = sqlIdsMap
}

func (i *SQLFactor) SetExcludeSQLList(sqlList []string) {
	i.excludeSQLList = sqlList
}

func (i *SQLFactor) SetExcludeModulesList(modulesList []string) {
	i.excludeModulesList = modulesList
}

func (i *SQLFactor) SetIncludingSQLTypeList(includingSQLTypeList []int) {
	i.includingSQLTypeList = includingSQLTypeList
}

func (i *Parameter) GetRunVersion() string {
	return i.RunVersion
}

func (i *Parameter) SetRunVersion(runVersion string) {
	switch runVersion {
	case "v2", "V2":
		runVersion = "v2"
	default:
		runVersion = "v1"
	}
	i.RunVersion = runVersion
}

func (i *Parameter) GetUsername() string {
	return i.OracleConnStr.User
}

func (i *Parameter) HasFileSQLs() bool {
	if i.sqlFileContent != nil && len(i.sqlFileContent.Contents) > 0 {
		return true
	}
	return false
}

func (i *Parameter) GetFileSQLs() []string {
	if i.sqlFileContent != nil {
		return i.sqlFileContent.Contents
	}
	return nil
}

func (i *Parameter) GetTidbConnStr() *DBConfigTiDB {
	return i.TidbConnStr
}

func (i *Parameter) HasOrderBy() bool {
	return strings.TrimSpace(i.SQLFactor.SQLOrderBy) != ""
}

func (i *Parameter) GetOracleConnStr() *DBConfigOracle {
	return i.OracleConnStr
}
func (i *Parameter) SetEnableSQLRewrite(val bool) {
	i.EnableSQLRewrite = val
}

func (i *Parameter) HasIncludingSchemas() bool {
	return len(i.SQLFactor.IncludingSchemas) > 0
}

func (i *Parameter) HasExcludeModulesList() bool {
	return len(i.SQLFactor.excludeModulesList) > 0
}

func (i *Parameter) HasExcludeSQLIDs() bool {
	return len(i.SQLFactor.ExcludeSQLIDs) > 0
}

func (i *Parameter) GetExcludeModulesList() []string {
	return i.SQLFactor.excludeModulesList
}

func (i *Parameter) GetExcludeSQLIDs() []string {
	return i.SQLFactor.ExcludeSQLIDs
}

func (i *Parameter) HasIncludingSQLTypeList() bool {
	return len(i.SQLFactor.includingSQLTypeList) > 0
}

func (i *Parameter) SetAppSQLsSource(sqlSource string) {
	switch strings.ToUpper(strings.TrimSpace(sqlSource)) {
	case "FILE":
		i.AppSQLsSource = common.FileSource
	default:
		i.AppSQLsSource = common.DBSource
	}
}

func (i *Parameter) SetAppSQLsFilename(sqlFilename string) {
	i.AppSQLsFilename = sqlFilename
}

func (i *Parameter) SetExplainOnly(explainOnly string) {
	i.ExplainOnly = explainOnly
}

func (i *Parameter) GetIncludingSQLTypeStringList() []string {
	if len(i.SQLFactor.includingSQLTypeList) == 0 {
		return []string{"3", "2", "7", "6"}
	}
	return lo.Map(i.SQLFactor.includingSQLTypeList, func(item int, _ int) string {
		return fmt.Sprintf("%d", item)
	})
}

func (i *Parameter) HasExcludeSQLTxtList() bool {
	return len(i.SQLFactor.excludeSQLList) > 0
}

func (i *Parameter) BuildAndCondition(tableAliasName string) string {
	sb := strings.Builder{}

	// 辅助函数：处理表别名
	prefix := func(field string) string {
		if tableAliasName == "" {
			return field
		}
		return tableAliasName + "." + field
	}

	// 处理 ExcludeSQLTxtList
	if i.HasExcludeSQLTxtList() {
		for _, excludeSQL := range i.GetExcludeSQLTxtList() {
			sb.WriteString(` AND upper(`)
			sb.WriteString(prefix("sql_text"))
			sb.WriteString(`) NOT LIKE '%`)
			sb.WriteString(excludeSQL)
			sb.WriteString(`%'`)
		}
	}

	// 处理 ExcludeModulesList
	if i.HasExcludeModulesList() {
		sb.WriteString(` AND `)
		sb.WriteString(prefix("module"))
		sb.WriteString(` NOT IN ('`)
		sb.WriteString(strings.Join(i.GetExcludeModulesList(), `','`))
		sb.WriteString(`')`)
	}

	// 处理 ExcludeSQLIDs
	if i.HasExcludeSQLIDs() {
		sb.WriteString(` AND `)
		sb.WriteString(prefix("sql_id"))
		sb.WriteString(` NOT IN ('`)
		sb.WriteString(strings.Join(i.GetExcludeSQLIDs(), `','`))
		sb.WriteString(`')`)
	}

	// 处理 IncludingSQLTypeList
	if i.HasIncludingSQLTypeList() {
		sb.WriteString(` AND `)
		sb.WriteString(prefix("command_type"))
		sb.WriteString(` IN (`)
		sb.WriteString(strings.Join(i.GetIncludingSQLTypeStringList(), ","))
		sb.WriteString(`)`)
	}

	return sb.String()
}

func (i *Parameter) GetExcludeSQLTxtList() []string {
	return i.SQLFactor.excludeSQLList
}

func (i *Parameter) GetSQLFactor() *SQLFactor {
	return i.SQLFactor
}

func (i *Parameter) GetIncludingSchemasBySQLSource() []string {
	if i.AppSQLsSource == common.DBSource {
		return i.SQLFactor.IncludingSchemas
	}
	return []string{i.AppSQLsFilename}
}

func (i *Parameter) MatchAtLeastOneSchema(schemaNames []string) bool {
	if len(schemaNames) == 0 {
		return true
	}

	hitOneSchema := false
	for _, schemaName := range schemaNames {
		if i.SQLFactor.includingSchemaMap[strings.ToUpper(schemaName)] {
			hitOneSchema = true
			break
		}
	}
	return hitOneSchema
}

func (i *Parameter) MatchIgnoreSQLId(sqlid string) bool {
	if len(i.SQLFactor.excludeSQLIdsMap) == 0 {
		return false
	}
	return i.SQLFactor.excludeSQLIdsMap[sqlid]
}

func (i *Parameter) MatchIgnoreSQLPattern(sqlText string) bool {
	upperSQLText := strings.ToUpper(sqlText)
	if len(i.SQLFactor.excludeSQLList) == 0 {
		return false
	}
	for _, excludeSQL := range i.SQLFactor.excludeSQLList {
		if strings.Contains(upperSQLText, excludeSQL) {
			return true
		}
	}
	return false
}

func (i *Parameter) IsFileMode() bool {
	return i.AppSQLsFilename != "" && i.AppSQLsSource == common.FileSource
}

// normalizeText 规范化SQL文本，去除多余空白
func (i *Parameter) normalizeText(sqlText string) string {
	text := strings.ToUpper(sqlText)
	text = strings.ReplaceAll(text, "\n", " ")
	text = strings.ReplaceAll(text, "\t", " ")
	for strings.Contains(text, "  ") {
		text = strings.ReplaceAll(text, "  ", " ")
	}
	return text
}

// MatchFromDual 检查SQL是否包含"FROM DUAL"
func (i *Parameter) MatchFromDual(sqlText string) bool {
	return strings.Contains(i.normalizeText(sqlText), " FROM DUAL")
}

// MatchSysSchema 检查SQL是否访问SYS模式
func (i *Parameter) MatchSysSchema(sqlText string) bool {
	return strings.Contains(i.normalizeText(sqlText), " FROM SYS.")
}

func (i *Parameter) InitSQLFileSource() error {
	if i.AppSQLsFilename != "" {
		// read sqls from file
		sqls, err := ReadContentFromFile(i.AppSQLsFilename)
		if err != nil {
			return err
		}
		i.sqlFileContent = sqls
	} else {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "没有正确配置该任务的参数app_sqls_filename")
	}
	return nil
}

func (i *Parameter) InitParameter() error {
	if i.IgnoreSQLIDsFilename != "" {
		// read exclude sql ids from file
		excludeSQLIDs, err := ReadContentFromFile(i.IgnoreSQLIDsFilename)
		if err != nil {
			return err
		}
		excludeSQLIdsMap := make(map[string]bool)
		for _, ignoreSQLId := range excludeSQLIDs.Contents {
			excludeSQLIdsMap[ignoreSQLId] = true
		}
		i.SQLFactor.excludeSQLIdsMap = excludeSQLIdsMap
	}

	if len(i.SQLFactor.IncludingSchemas) != 0 {
		schemaList := lo.Map(i.SQLFactor.IncludingSchemas, func(s string, _ int) string {
			return strings.ToUpper(strings.TrimSpace(s))
		})
		schemaList = lo.Filter(schemaList, func(s string, _ int) bool {
			return s != "" && s != constants.FakeParamValue
		})
		i.SQLFactor.includingSchemaMap = make(map[string]bool)
		for _, schema := range schemaList {
			i.SQLFactor.includingSchemaMap[schema] = true
		}
	}

	if len(i.SQLFactor.TiDBExecStatusFilter) != 0 {
		statusList := lo.Map(i.SQLFactor.TiDBExecStatusFilter, func(s string, _ int) string {
			return strings.ToLower(strings.TrimSpace(s))
		})
		statusList = lo.Filter(statusList, func(s string, _ int) bool {
			return s != "" && s != constants.FakeParamValue
		})
		i.SQLFactor.TiDBExecStatusFilter = statusList
	}

	if len(i.SQLFactor.ExcludeSQLTxts) != 0 {
		sqlList := lo.Map(i.SQLFactor.ExcludeSQLTxts, func(s string, _ int) string {
			return strings.ToUpper(strings.TrimSpace(s))
		})
		sqlList = lo.Filter(sqlList, func(s string, _ int) bool {
			return s != "" && s != constants.FakeParamValue
		})
		i.SQLFactor.SetExcludeSQLList(sqlList)
	}

	if len(i.SQLFactor.IncludingSQLTypes) != 0 {
		m := make([]int, 0, 10)
		m = append(m, -1) // for file sql command type
		for _, sqlType := range i.SQLFactor.IncludingSQLTypes {
			if sqlType == constants.FakeParamValue {
				continue
			}
			upperSQLType := strings.ToUpper(strings.TrimSpace(sqlType))
			commandType, ok := constants.OracleCommandTypeMapping[upperSQLType]
			if !ok {
				return fmt.Errorf("not support oracle sql type: %s", upperSQLType)
			}
			m = append(m, commandType)
		}
		i.SQLFactor.SetIncludingSQLTypeList(m)
	}

	if len(i.SQLFactor.ExcludeModules) != 0 {
		arr := make([]string, 0)
		for _, em := range i.SQLFactor.ExcludeModules {
			em = strings.TrimSpace(em)
			if em == constants.FakeParamValue {
				continue
			}
			arr = append(arr, em)
		}
		i.SQLFactor.SetExcludeModulesList(arr)
	}
	return nil
}

func (i *Parameter) ValidateParam() error {
	if len(i.SQLFactor.IncludingSchemas) == 0 {
		return errors.New("没有正确配置该任务的参数including_schema")
	}
	return nil
}

type FileContent struct {
	FileName   string
	Contents   []string
	ModifyTime time.Time
	Inode      uint64
}

func (i *FileContent) InodeAsTime() time.Time {
	if i.Inode == 0 {
		return time.Time{}
	}
	return time.Unix(int64(i.Inode), 0)
}

// read file and split every line to int
func ReadContentFromFile(filename string) (*FileContent, error) {
	// read file
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	fileStat, statErr := file.Stat()
	if statErr != nil {
		return nil, statErr
	}

	// read file content
	content, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}
	// split every line
	lines := strings.Split(string(content), "\n")
	// convert string to int
	var ignoreSQLIds []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		ignoreSQLIds = append(ignoreSQLIds, line)
	}

	var inode uint64
	if stat, ok := fileStat.Sys().(*syscall.Stat_t); ok {
		inode = stat.Ino
	}

	return &FileContent{
		FileName:   filename,
		Contents:   ignoreSQLIds,
		ModifyTime: fileStat.ModTime(),
		Inode:      inode,
	}, nil
}

type ExplainResult struct {
	Stmt       *SpaSqlStmt
	FinishTime time.Time
	IsTimeout  bool
	Err        error
}

func (i *ExplainResult) GetReplayStatus() string {
	return i.Stmt.TidbExecStatus
}

func (i *ExplainResult) GetReplayExecCode() string {
	return i.Stmt.TidbExecCode
}

func (i *ExplainResult) GetFinishTime() time.Time {
	return i.FinishTime
}

func (i *ExplainResult) GetErrorMessage() string {
	if i.Err != nil {
		em := i.Err.Error()
		if len(em) > 2000 {
			em = em[:2000]
		}
		return em
	}
	return ""
}

func (i *Parameter) String() string {
	return fmt.Sprintf(
		"<Parameter TaskId: %d, ChannelId:%d, AppSQLsSource: %s, AppSQLsFilename: %s, "+
			"IgnoreSQLIDsFilename: %s, ExplainOnly: %s, TidbConnStr: %s, OracleConnStr: %s, "+
			"RunVersion: %s, SQLFactor: %s>",
		i.TaskID, i.ChannelID, i.AppSQLsSource, i.AppSQLsFilename,
		i.IgnoreSQLIDsFilename, i.ExplainOnly, i.TidbConnStr, i.OracleConnStr,
		i.RunVersion, i.SQLFactor)
}

func (i *SQLFactor) String() string {
	sb := strings.Builder{}
	sb.WriteString("<SQLFactor")

	// 辅助函数：拼接切片元素
	writeSlice := func(name string, slice []string) {
		if len(slice) == 0 {
			sb.WriteString(fmt.Sprintf(" %s: [],", name))
			return
		}
		sb.WriteString(fmt.Sprintf(" %s: [", name))
		for j, item := range slice {
			sb.WriteString(item)
			if j < len(slice)-1 {
				sb.WriteString(",")
			}
		}
		sb.WriteString("],")
	}

	// 依次处理各个切片字段
	writeSlice("IncludingSchemas", i.IncludingSchemas)
	writeSlice("IncludingSQLTypes", i.IncludingSQLTypes)
	writeSlice("ExcludeModules", i.ExcludeModules)
	writeSlice("ExcludeSQLTxts", i.ExcludeSQLTxts)
	writeSlice("ExcludeSQLIDs", i.ExcludeSQLIDs)
	writeSlice("TiDBExecStatusFilter", i.TiDBExecStatusFilter)

	// 处理其他字段
	fmt.Fprintf(&sb, " SQLOrderBy: %s,", i.SQLOrderBy)
	fmt.Fprintf(&sb, " SQLSetName: %s,", i.SQLSetName)
	fmt.Fprintf(&sb, " AnalyzeTimeout: %d,", i.AnalyzeTimeout)
	fmt.Fprintf(&sb, " TopNPerStatus: %d,", i.TopNPerStatus)
	fmt.Fprintf(&sb, " SQLRowLimit: %d,", i.SQLRowLimit)
	fmt.Fprintf(&sb, " AnalyzeThread: %d>", i.AnalyzeThread)

	return sb.String()
}

type O2tSqlsetExecInfo struct {
	Id                   int             `json:"id"`
	Updatetime           time.Time       `json:"updatetime"`
	JsonFileName         string          `json:"jsonFileName"`
	TidbSqlstmt          string          `json:"tidbSqlstmt"`
	TidbExecStatus       string          `json:"tidbExecStatus"`
	TidbPlan             string          `json:"tidbPlan"`
	TidbElapsedTimeMs    decimal.Decimal `json:"tidbElapsedTimeMs"`
	TidbEngineTimeMs     decimal.Decimal `json:"tidbEngineTimeMs"`
	TidbExecTime         time.Time       `json:"tidbExecTime"`
	TidbDigest           string          `json:"tidbDigest"`
	TidbUseWhereFunc     string          `json:"tidbUseWhereFunc"`
	TidbUseNotExists     string          `json:"tidbUseNotExists"`
	TidbPlanNotGood      string          `json:"tidbPlanNotGood"`
	OraSqlsetName        string          `json:"oraSqlsetName"`
	OraSqlsetOwner       string          `json:"oraSqlsetOwner"`
	OraModule            string          `json:"oraModule"`
	OraSqlId             string          `json:"oraSqlId"`
	OraSqlText           string          `json:"oraSqlText"`
	OraParsingSchemaName string          `json:"oraParsingSchemaName"`
	OraPlanHashValue     string          `json:"oraPlanHashValue"`
	OraParameterPosition string          `json:"oraParameterPosition"`
	OraParameterValue    string          `json:"oraParameterValue"`
	OraExecutions        int             `json:"oraExecutions"`
	OraElapsedTimeMs     decimal.Decimal `json:"oraElapsedTimeMs"`
	OraLastExecStartTime string          `json:"oraLastExecStartTime"`
	OraPlanTimestamp     time.Time       `json:"oraPlanTimestamp"`
	UserOperateFlag      string          `json:"userOperateFlag"`
}

type SpaSqlStmt struct {
	OraSqlsetName        string    `json:"oraSqlsetName"`
	OraSqlsetOwner       string    `json:"oraSqlsetOwner"`
	OraModule            string    `json:"oraModule"`
	OraSqlId             string    `json:"oraSqlId"`
	OraDigestId          string    `json:"oraDigestId"`
	OraSqlText           string    `json:"oraSqlText"`
	OraParsingSchemaName string    `json:"oraParsingSchemaName"`
	OraPlanHashValue     string    `json:"oraPlanHashValue"`
	OraParameterPosition string    `json:"oraParameterPosition"`
	OraParameterValue    string    `json:"oraParameterValue"`
	OraExecutions        int       `json:"oraExecutions"`
	OraElapsedTimeMs     float64   `json:"oraElapsedTimeMs"`
	OraLastExecStartTime string    `json:"oraLastExecStartTime"`
	OraPlanTimestamp     time.Time `json:"oraPlanTimestamp"`
	OraCommandType       string    `json:"oraCommandType"`
	TidbSqlText          string    `json:"tidbSqlText"`
	TidbSqlType          string    `json:"tidbSqlType"`
	TidbExecStatus       string    `json:"tidbExecStatus"`
	TidbExecCode         string    `json:"tidbExecCode"`
	TidbExecMsg          string    `json:"tidbExecMsg"`
	TidbPlan             string    `json:"tidbPlan"`
	TidbElapsedTimeMs    int64     `json:"tidbElapsedTimeMs"`
	TidbEngineTimeMs     float64   `json:"tidbEngineTimeMs"`
	TidbExecTime         time.Time `json:"tidbExecTime"`
	TidbDigest           string    `json:"tidbDigest"`
	TidbUseWhereFunc     string    `json:"tidbUseWhereFunc"`
	TidbUseNotExists     string    `json:"tidbUseNotExists"`
	TidbPlanNotGood      string    `json:"tidbPlanNotGood"`
	TmsExecMsg           string    `json:"tmsExecMsg"`
	RewriteSQLText       string    `json:"rewriteSqlText"`
	RewritePrompts       string    `json:"rewritePrompts"`
}

type CompatibleOverview struct {
	Type         string `json:"type"`
	TotalNums    int    `json:"totalNums"`
	SuccNums     int    `json:"succNums"`
	FailNums     int    `json:"failNums"`
	TimeoutNums  int    `json:"timeoutNums"`
	IgnoredNums  int    `json:"ignoredNums"`
	ResolvedNums int    `json:"resolvedNums"`
	Pct          string `json:"pct"`
}

type IncompatibleError struct {
	ErrCode   string `json:"errCode"`
	TotalNums int    `json:"totalNums"`
	ErrMsg    string `json:"errMsg"`
}

type TimeoutError struct {
	TotalNums int `json:"totalNums"`
}

type SqlIncompatibleDetail struct {
	OraSqlId             string  `json:"oraSqlId"`
	TidbExecCode         string  `json:"tidbExecCode"`
	OraSqlText           string  `json:"oraSqlText"`
	OraModule            string  `json:"oraModule"`
	OraExecutions        int     `json:"oraExecutions"`
	OraElapsedTimeMs     float64 `json:"oraElapsedTimeMs"`
	OraLastExecStartTime string  `json:"oraLastExecStartTime"`
}

type CompatibleDetail struct {
	OraSqlId             string  `json:"oraSqlId"`
	OraParsingSchemaName string  `json:"oraParsingSchemaName"`
	TidbExecCode         string  `json:"tidbExecCode"`
	TidbSqlText          string  `json:"tidbSqlText"`
	OraModule            string  `json:"oraModule"`
	TidbElapsedTimeMs    int64   `json:"tidbElapsedTimeMs"`
	TidbEngineTimeMs     float64 `json:"tidbEngineTimeMs"`
	OraExecutions        int     `json:"oraExecutions"`
	OraElapsedTimeMs     float64 `json:"oraElapsedTimeMs"`
	OraLastExecStartTime string  `json:"oraLastExecStartTime"`
	OraSqlText           string  `json:"oraSqlText"`
}

//type SqlCompatibleReport struct {
//	SqlCompatibleOverview []*CompatibleOverview `json:"sqlCompatibleOverview"`
//	SqlIncompatibleError  []*IncompatibleError  `json:"sqlIncompatibleError"`
//	SqlFailDetail         []*CompatibleDetail   `json:"sqlFailedDetail"`
//	SqlSuccDetail         []*CompatibleDetail   `json:"SuccessDetail"`
//	SqlOtherDetail        []*CompatibleDetail   `json:"sqlOtherDetail"`
//}

type PerformanceOverview struct {
	Type       string `json:"type"`
	WorseNums  int    `json:"worseNums"`
	BetterNums int    `json:"betterNums"`
}

type PerformanceWorse struct {
	OraSqlId           string  `json:"oraSqlId"`
	OraModule          string  `json:"oraModule"`
	OraExecutions      int     `json:"oraExecutions"`
	OraElapsedTimeMs   float64 `json:"oraElapsedTimeMs"`
	TidbEngineTimeMs   float64 `json:"tidbEngineTimeMs"`
	TidbElapsedTimeMs  int64   `json:"tidbElapsedTimeMs"`
	ElapsedTimeDiffMs  float64 `json:"elapsedTimeDiffMs"`
	ElapsedTimeDiffPct string  `json:"elapsedTimeDiffPct"`
	OraSqlText         string  `json:"oraSqlText"`
	TidbPlan           string  `json:"tidbPlan"`
}

type PerformanceBetter struct {
	OraSqlId           string  `json:"oraSqlId"`
	OraModule          string  `json:"oraModule"`
	OraExecutions      int     `json:"oraExecutions"`
	OraElapsedTimeMs   float64 `json:"oraElapsedTimeMs"`
	TidbEngineTimeMs   float64 `json:"tidbEngineTimeMs"`
	TidbElapsedTimeMs  int64   `json:"tidbElapsedTimeMs"`
	ElapsedTimeDiffMs  float64 `json:"elapsedTimeDiffMs"`
	ElapsedTimeDiffPct string  `json:"elapsedTimeDiffPct"`
	OraSqlText         string  `json:"oraSqlText"`
	TidbPlan           string  `json:"tidbPlan"`
}

type SQLPerformanceWorseDetail struct {
	OraSqlId          string  `json:"oraSqlId"`
	OraSqlText        string  `json:"oraSqlText"`
	OraModule         string  `json:"oraModule"`
	OraExecutions     int     `json:"oraExecutions"`
	OraElapsedTimeMs  float64 `json:"oraElapsedTimeMs"`
	TidbEngineTimeMs  float64 `json:"tidbEngineTimeMs"`
	TidbElapsedTimeMs int64   `json:"tidbElapsedTimeMs"`
	TidbPlan          string  `json:"tidbPlan"`
}

type SQLPerformanceBetterDetail struct {
	OraSqlId          string  `json:"oraSqlId"`
	OraSqlText        string  `json:"oraSqlText"`
	OraModule         string  `json:"oraModule"`
	OraExecutions     int     `json:"oraExecutions"`
	OraElapsedTimeMs  float64 `json:"oraElapsedTimeMs"`
	TidbEngineTimeMs  float64 `json:"tidbEngineTimeMs"`
	TidbElapsedTimeMs int64   `json:"tidbElapsedTimeMs"`
	TidbPlan          string  `json:"tidbPlan"`
}

type SqlPerformanceReport struct {
	PerformanceOverview     []*PerformanceOverview
	PerformanceWorse        []*PerformanceWorse
	PerformanceBetter       []*PerformanceBetter
	PerformanceWorseDetail  []*SQLPerformanceWorseDetail
	PerformanceBetterDetail []*SQLPerformanceBetterDetail
}

type SqlAnalyzerReport struct {
	Title string

	CompatibleOverview []*CompatibleOverview
	IncompatibleError  []*IncompatibleError
	TimeoutError       []*TimeoutError

	ErrorDetail   []*CompatibleDetail
	SuccessDetail []*CompatibleDetail
	TimeoutDetail []*CompatibleDetail
	OtherDetail   []*CompatibleDetail

	// User operation sections
	IgnoredErrorDetail   []*CompatibleDetail
	IgnoredSuccessDetail []*CompatibleDetail
	IgnoredTimeoutDetail []*CompatibleDetail
	IgnoredOtherDetail   []*CompatibleDetail

	ResolvedErrorDetail   []*CompatibleDetail
	ResolvedSuccessDetail []*CompatibleDetail
	ResolvedTimeoutDetail []*CompatibleDetail
	ResolvedOtherDetail   []*CompatibleDetail

	PerformanceOverview []*PerformanceOverview
	PerformanceWorse    []*PerformanceWorse
	PerformanceBetter   []*PerformanceBetter

	// User operation performance sections
	IgnoredPerformanceWorse   []*PerformanceWorse
	IgnoredPerformanceBetter  []*PerformanceBetter
	ResolvedPerformanceWorse  []*PerformanceWorse
	ResolvedPerformanceBetter []*PerformanceBetter
}

func (o *SqlAnalyzerReportAdapter) GetFieldByName(name string) reflect.Value {
	if o == nil || o.Data == nil {
		return reflect.Value{} // Return invalid reflect.Value
	}
	v := reflect.ValueOf(o.Data)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return reflect.Value{}
	}
	return v.Elem().FieldByName(name)
}

type SqlAnalyzerReportAdapter struct {
	Data *SqlAnalyzerReport
}
