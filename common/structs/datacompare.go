package structs

import (
	"fmt"

	"github.com/pkg/errors"
)

func (i DataCompareTaskParam) String() string {
	return fmt.Sprintf("<DataCompareTaskParam taskId:%d, oracle_home:%s>", i.TaskId, i.OracleHome)
}

func (i DataCompareTaskParam) ValidateParam() error {
	oracleHome := i.OracleHome
	if oracleHome == "" || oracleHome == "/path/to/oracle/home" ||
		oracleHome == "${ORACLE_HOME}" || oracleHome == "${ORACLE_HOME}/lib" ||
		oracleHome == "$ORACLE_HOME" || oracleHome == "$ORACLE_HOME/lib" {
		return errors.New("没有正确配置该任务的参数oracle_home")
	}
	return nil
}

type DataCompareTaskParam struct {
	TaskId int

	SourceSnapshot             string  // source snapshot
	TargetSnapshot             string  // target snapshot
	OracleScn                  string  // oracle scn
	SourceCharset              string  // source charset
	TargetCharset              string  // target charset
	ChunkSize                  int     // chunk size, default is 10000
	CheckThreadCount           int     // check thread count, default is 4
	UseChecksum                bool    // use checksum, default is true
	OnlyUseChecksum            bool    // only use checksum, default is false
	FixTarget                  string  // fix target is oracle/tidb, default is tidb
	EmptyStringNullCompare     bool    // empty string and null whether equal, default is false
	Ascii0Switch               bool    // whether replace ascii 0, default is false
	IgnoreStructCheck          bool    // whether ignore struct compare, default is true
	IgnoreStats                bool    // whether ignore stats, default is false
	IgnoreDataCheck            bool    // whether ignore data check, default is false
	UseCheckpoint              bool    // whether use checkpoint, default is false
	DoubleTruncPrecision       int     // double truncate precision, 0<=value<=9, default is 9
	FloatTruncPrecision        int     // float truncate precision, 0<=value<=6, default is 6
	OracleHome                 string  // oracle home for yzbt_crc32.so
	LowCaseSchemaName          int     // schema names letter case(0:source reference;1:lower;2:upper), default is 1
	LowCaseTableName           int     // table names letter case(0:source reference;1:lower;2:upper), default is 1
	CompressMode               bool    // enable compress mode to compare data, default is false
	FixSqlDir                  string  // fix sql directory, default is fixsqldir
	MissingTablesThreshold     float64 // missing tables percentage threshold (0-100), default is 100 (no limit)
	TableChunkFailThreshold    float64 // single table chunk failure percentage threshold (0-100), default is 100 (no limit)
	OverallTableErrorThreshold float64 // overall table error percentage threshold (0-100), default is 100 (no limit)
	TableChunkFailMinCount     int     // minimum failed chunks count before triggering table chunk fail threshold, default is 5
}

// Getters
func (c *DataCompareTaskParam) GetSourceSnapshot() string {
	return c.SourceSnapshot
}

func (c *DataCompareTaskParam) GetTargetSnapshot() string {
	return c.TargetSnapshot
}

func (c *DataCompareTaskParam) GetOracleScn() string {
	return c.OracleScn
}

func (c *DataCompareTaskParam) GetSourceCharset() string {
	return c.SourceCharset
}

func (c *DataCompareTaskParam) GetTargetCharset() string {
	return c.TargetCharset
}

func (c *DataCompareTaskParam) GetChunkSize() int {
	return c.ChunkSize
}

func (c *DataCompareTaskParam) GetCheckThreadCount() int {
	return c.CheckThreadCount
}

func (c *DataCompareTaskParam) GetUseChecksum() bool {
	return c.UseChecksum
}

func (c *DataCompareTaskParam) GetOnlyUseChecksum() bool {
	return c.OnlyUseChecksum
}

func (c *DataCompareTaskParam) GetFixTarget() string {
	return c.FixTarget
}

func (c *DataCompareTaskParam) GetEmptyStringNullCompare() bool {
	return c.EmptyStringNullCompare
}

func (c *DataCompareTaskParam) GetAscii0Switch() bool {
	return c.Ascii0Switch
}

func (c *DataCompareTaskParam) GetIgnoreStructCheck() bool {
	return c.IgnoreStructCheck
}

func (c *DataCompareTaskParam) GetIgnoreStats() bool {
	return c.IgnoreStats
}

func (c *DataCompareTaskParam) GetIgnoreDataCheck() bool {
	return c.IgnoreDataCheck
}

func (c *DataCompareTaskParam) GetUseCheckpoint() bool {
	return c.UseCheckpoint
}

func (c *DataCompareTaskParam) GetDoubleTruncPrecision() int {
	return c.DoubleTruncPrecision
}

func (c *DataCompareTaskParam) GetFloatTruncPrecision() int {
	return c.FloatTruncPrecision
}

func (c *DataCompareTaskParam) GetOracleHome() string {
	return c.OracleHome
}

func (c *DataCompareTaskParam) GetLowCaseSchemaName() int {
	return c.LowCaseSchemaName
}

func (c *DataCompareTaskParam) GetLowCaseTableName() int {
	return c.LowCaseTableName
}

func (c *DataCompareTaskParam) GetCompressMode() bool {
	return c.CompressMode
}

func (c *DataCompareTaskParam) GetFixSqlDir() string {
	return c.FixSqlDir
}

func (c *DataCompareTaskParam) GetMissingTablesThreshold() float64 {
	return c.MissingTablesThreshold
}

func (c *DataCompareTaskParam) GetTableChunkFailThreshold() float64 {
	return c.TableChunkFailThreshold
}

func (c *DataCompareTaskParam) GetOverallTableErrorThreshold() float64 {
	return c.OverallTableErrorThreshold
}

func (c *DataCompareTaskParam) GetTableChunkFailMinCount() int {
	return c.TableChunkFailMinCount
}

// Setters
func (c *DataCompareTaskParam) SetSourceSnapshot(value string) {
	c.SourceSnapshot = value
}

func (c *DataCompareTaskParam) SetTargetSnapshot(value string) {
	c.TargetSnapshot = value
}

func (c *DataCompareTaskParam) SetOracleScn(value string) {
	c.OracleScn = value
}

func (c *DataCompareTaskParam) SetSourceCharset(value string) {
	c.SourceCharset = value
}

func (c *DataCompareTaskParam) SetTargetCharset(value string) {
	c.TargetCharset = value
}

func (c *DataCompareTaskParam) SetChunkSize(value int) {
	c.ChunkSize = value
}

func (c *DataCompareTaskParam) SetCheckThreadCount(value int) {
	c.CheckThreadCount = value
}

func (c *DataCompareTaskParam) SetUseChecksum(value bool) {
	c.UseChecksum = value
}

func (c *DataCompareTaskParam) SetOnlyUseChecksum(value bool) {
	c.OnlyUseChecksum = value
}

func (c *DataCompareTaskParam) SetFixTarget(value string) {
	c.FixTarget = value
}

func (c *DataCompareTaskParam) SetEmptyStringNullCompare(value bool) {
	c.EmptyStringNullCompare = value
}

func (c *DataCompareTaskParam) SetAscii0Switch(value bool) {
	c.Ascii0Switch = value
}

func (c *DataCompareTaskParam) SetIgnoreStructCheck(value bool) {
	c.IgnoreStructCheck = value
}

func (c *DataCompareTaskParam) SetIgnoreStats(value bool) {
	c.IgnoreStats = value
}

func (c *DataCompareTaskParam) SetIgnoreDataCheck(value bool) {
	c.IgnoreDataCheck = value
}

func (c *DataCompareTaskParam) SetUseCheckpoint(value bool) {
	c.UseCheckpoint = value
}

func (c *DataCompareTaskParam) SetDoubleTruncPrecision(value int) {
	c.DoubleTruncPrecision = value
}

func (c *DataCompareTaskParam) SetFloatTruncPrecision(value int) {
	c.FloatTruncPrecision = value
}

func (c *DataCompareTaskParam) SetOracleHome(value string) {
	c.OracleHome = value
}

func (c *DataCompareTaskParam) SetLowCaseSchemaName(value int) {
	c.LowCaseSchemaName = value
}

func (c *DataCompareTaskParam) SetLowCaseTableName(value int) {
	c.LowCaseTableName = value
}

func (c *DataCompareTaskParam) SetCompressMode(value bool) {
	c.CompressMode = value
}

func (c *DataCompareTaskParam) SetFixSqlDir(value string) {
	c.FixSqlDir = value
}

func (c *DataCompareTaskParam) SetMissingTablesThreshold(value float64) {
	c.MissingTablesThreshold = value
}

func (c *DataCompareTaskParam) SetTableChunkFailThreshold(value float64) {
	c.TableChunkFailThreshold = value
}

func (c *DataCompareTaskParam) SetOverallTableErrorThreshold(value float64) {
	c.OverallTableErrorThreshold = value
}

func (c *DataCompareTaskParam) SetTableChunkFailMinCount(value int) {
	c.TableChunkFailMinCount = value
}
