package structs

import "sync"

type ConcurrentMap struct {
	sync.RWMutex
	internal map[string]interface{}
}

func NewConcurrentMap() *ConcurrentMap {
	return &ConcurrentMap{
		internal: make(map[string]interface{}),
	}
}

func (cm *ConcurrentMap) Set(key string, value interface{}) {
	cm.Lock()
	cm.internal[key] = value
	cm.Unlock()
}

func (cm *ConcurrentMap) Get(key string) (interface{}, bool) {
	cm.RLock()
	val, ok := cm.internal[key]
	cm.RUnlock()
	return val, ok
}

func (cm *ConcurrentMap) Delete(key string) {
	cm.Lock()
	delete(cm.internal, key)
	cm.Unlock()
}

func (cm *ConcurrentMap) GetInternal() map[string]interface{} {
	return cm.internal
}
