package structs

type SubTaskSummary struct {
	TotalNum      int `gorm:"total_num" json:"totalNum"`
	NotSetupNum   int `gorm:"not_setup_num" json:"notSetupNum"`
	NotRunningNum int `gorm:"not_running_num" json:"notRunningNum"`
	RunningNum    int `gorm:"running_num" json:"runningNum"`
	FinishNum     int `gorm:"finish_num" json:"finishNum"`
	FailedNum     int `gorm:"failed_num" json:"failedNum"`
	QueuedNum     int `gorm:"queued_num" json:"queuedNum"`
	StopNum       int `gorm:"stop_num" json:"stopNum"`
}

func (i SubTaskSummary) HasRunningOrQueued() bool {
	return i.RunningNum > 0 || i.QueuedNum > 0
}

func (i SubTaskSummary) GetSchemaTableChangeCheckRetCode() int {
	if i.TotalNum == 0 {
		return 1
	}
	if i.HasRunningOrQueued() {
		return 2
	}
	return 3
}
