package structs

type StatsConfigParams struct {
	TaskThread                     int
	MaxExecutionTime               string
	TiDBMemQuotaQuery              string
	TiDBDistSQLScanConcurrency     string
	TiDBIndexSerialScanConcurrency string
	TiDBBuildStatsConcurrency      string
}

func (i StatsConfigParams) GetMaxExecutionTimeSQL() string {
	return "set session max_execution_time = " + i.MaxExecutionTime
}

func (i StatsConfigParams) GetTiDBMemQuotaQuerySQL() string {
	return "set session tidb_mem_quota_query = " + i.TiDBMemQuotaQuery
}

func (i StatsConfigParams) GetTiDBDistSQLScanConcurrencySQL() string {
	return "set session tidb_distsql_scan_concurrency = " + i.TiDBDistSQLScanConcurrency
}

func (i StatsConfigParams) GetTiDBIndexSerialScanConcurrencySQL() string {
	return "set session tidb_index_serial_scan_concurrency = " + i.TiDBIndexSerialScanConcurrency
}

func (i StatsConfigParams) GetTiDBBuildStatsConcurrencySQL() string {
	return "set session tidb_build_stats_concurrency = " + i.TiDBBuildStatsConcurrency
}

func (i StatsConfigParams) GetStatsThread() uint {
	if i.TaskThread <= 0 {
		return 1
	}
	return uint(i.TaskThread)
}

type SchemaTablePair struct {
	SchemaName string
	TableName  string
}

func (i SchemaTablePair) String() string {
	return "<" + i.SchemaName + "." + i.TableName + ">"
}

type SplitSchemaTable struct {
	SchemaName string
	TableName  string

	TaskId     int
	TaskName   string
	TaskType   int
	TableSizeM float64
}

type SchemaTableMapping map[SchemaTablePair]bool

func (i SchemaTableMapping) String() string {
	var result string
	for k := range i {
		result += k.String() + " "
	}
	return result
}
