package structs

import (
	"fmt"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"github.com/google/uuid"
	"strconv"
	"strings"
)

type OracleAllSource struct {
	Owner string
	Name  string
	Line  string
	Text  string
}

type OracleObjectKey struct {
	SchemaName string
	ObjectName string
	ObjectType string
}

type DependencyTreeNode struct {
	SchemaName  string `json:"-"`
	PackageName string `json:"packageName,omitempty"`
	UUID        string `json:"-"`
	Owner       string `json:"owner,omitempty"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Status      string `json:"status"`
	IsCycle     bool   `json:"isCycle,omitempty"`

	//ChildrenUniqueKeys []string              `json:"childrenUniqueKeys,omitempty"`
	ChildrenUniqueKeys []string              `json:"-"`
	Children           []*DependencyTreeNode `json:"children,omitempty"`

	IsFromPackageBody bool `json:"-"`
}

func (i *DependencyTreeNode) GetIsCycle() bool {
	return i.IsCycle
}
func (i *DependencyTreeNode) GetStatus() string {
	return i.Status
}

func (i *DependencyTreeNode) GetChildren() []*DependencyTreeNode {
	return i.Children
}

func (i *DependencyTreeNode) GetChildrenUniqueKeys() []string {
	return i.ChildrenUniqueKeys
}

func (i *DependencyTreeNode) GetSchemaName() string {
	return i.SchemaName
}

func (i *DependencyTreeNode) GetPackageName() string {
	return i.PackageName
}

func (i *DependencyTreeNode) GetOwner() string {
	return i.Owner
}

func (i *DependencyTreeNode) GetName() string {
	return i.Name
}

func (i *DependencyTreeNode) GetType() string {
	return i.Type
}

func (i *DependencyTreeNode) GetIsFromPackageBody() bool {
	return i.IsFromPackageBody
}

func (i *DependencyTreeNode) HasPackageName() bool {
	return i.PackageName != ""
}

func (i *DependencyTreeNode) GenerateUUIDIfNecessary() string {
	uid := i.UUID
	if uid == "" {
		uid = uuid.NewString()
	}
	if i.Type == "SCHEMA" {
		uid = "SCHEMA:" + i.Name
	}
	return uid
}

func (i *DependencyTreeNode) String() string {
	return i.SchemaName + "." + i.GetType() + "." + i.GetName()
}

func (i *DependencyTreeNode) FullString() string {
	return "<SchemaName:" + i.SchemaName + ",PackageName:" + i.PackageName + ",OwnerName:" + i.Owner + ",Name:" + i.Name + ",Type:" + i.Type + ",Status:" + i.Status + ">"
}

type OracleObjectPrompt struct {
	RelationId  uint   `json:"relationId"`
	PromptId    uint   `json:"promptId"`
	PromptTitle string `json:"promptTitle"`
	PromptText  string `json:"promptText"`
}

type DependencyTreeVO struct {
	Schema       string              `json:"schema"`
	Package      string              `json:"package"`
	Title        string              `json:"title"`
	Type         string              `json:"type"`
	Status       string              `json:"status"`
	Key          string              `json:"key"`
	UUID         string              `json:"uuid"`
	Depth        uint                `json:"depth"`
	IsCycle      bool                `json:"isCycle,omitempty"`
	IsReferenced bool                `json:"isReferenced,omitempty"`
	HasDbLink    bool                `json:"hasDbLink,omitempty"`
	Prompt       *OracleObjectPrompt `json:"prompt,omitempty"`
	Children     []*DependencyTreeVO `json:"children,omitempty"`

	Level uint   `json:"-"`
	Owner string `json:"-"`
}

type DependencyGraphVO struct {
	Nodes []DependencyGraphNode `json:"nodes"`
	Edges []DependencyGraphEdge `json:"edges"`
}

func (i *DependencyGraphVO) AddGraphNode(node DependencyGraphNode) {
	i.Nodes = append(i.Nodes, node)
}
func (i *DependencyGraphVO) AddGraphEdge(edge DependencyGraphEdge) {
	i.Edges = append(i.Edges, edge)
}

type DependencyGraphEdge struct {
	SourceID string `json:"source"`
	TargetID string `json:"target"`
}

type DependencyGraphNode struct {
	ID           string `json:"id"`
	Label        string `json:"label"`
	Status       string `json:"status"`
	Type         string `json:"type"`
	HasDbLink    bool   `json:"hasDbLink,omitempty"`
	IsReferenced bool   `json:"isReferenced,omitempty"`
}

func (i *DependencyTreeNode) UniqueKey() string {
	return i.Owner + "." + i.Type + "." + i.Name
}

func (i *DependencyTreeNode) UniqueKeyWithSchemaPackage() string {
	return i.SchemaName + "." + i.PackageName + "." + i.Owner + "." + i.Type + "." + i.Name
}

func (i *DependencyTreeNode) UniqueKeyWithCycle() string {
	return i.Owner + "." + i.Type + "." + i.Name + "." + strconv.FormatBool(i.IsCycle)
}

type IncompatibleFeatureScoringMode int

func (i IncompatibleFeatureScoringMode) String() string {
	switch i {
	case DEDUCT_ONCE:
		return "DEDUCT_ONCE"
	case DEDUCT_PER_OCCURRENCE:
		return "DEDUCT_PER_OCCURRENCE"
	default:
		return "DEDUCT_ONCE"
	}
}

const (
	DEDUCT_ONCE           IncompatibleFeatureScoringMode = 0
	DEDUCT_PER_OCCURRENCE IncompatibleFeatureScoringMode = 1
)

type LLMProviderAPIConfig struct {
	Endpoint string
	APIKey   string
	Source   string
	Model    string
	Timeout  int

	MaxTokensName string
	MaxTokens     int
	Stream        bool
	Temperature   float32
}

func (i *LLMProviderAPIConfig) GetTemperature() float32 {
	return i.Temperature
}

type ObjectParserParam struct {
	LLMProviderAPIConfig

	ReservedWordScoringMode    IncompatibleFeatureScoringMode
	DatabaseLinkScoringMode    IncompatibleFeatureScoringMode
	HistogramScoreWeightFactor float64
	ConvertJavaThread          int

	// Concurrent analysis configuration
	AnalyzeWorkerPoolSize int // Number of concurrent workers for analysis, default CPU * 2
	AnalyzeBatchSize      int // Number of objects per batch, default 100
}

func (i *LLMProviderAPIConfig) GetMaxTokens(defaultMaxTokens int) int {
	if i.MaxTokens > 0 {
		return i.MaxTokens
	}
	return defaultMaxTokens
}

func (i *LLMProviderAPIConfig) IsStream() bool {
	return i.Stream
}

func (i *LLMProviderAPIConfig) GetModel() string {
	return i.Model
}
func (i *LLMProviderAPIConfig) GetEndpoint() string {
	return i.Endpoint
}
func (i *LLMProviderAPIConfig) GetAPIKey() string {
	return i.APIKey
}
func (i *LLMProviderAPIConfig) GetSource() string {
	return i.Source
}

func (i *LLMProviderAPIConfig) ValidateOpenAIConfig() error {
	if i.Model == "" {
		return tmserrors.NewErrorf(tmserrors.TMS_LLM_MODEL, "Model is empty")
	}
	if i.Endpoint == "" {
		return tmserrors.NewErrorf(tmserrors.TMS_LLM_ENDPOINT, "Endpoint is empty")
	}
	if i.APIKey == "" {
		return tmserrors.NewErrorf(tmserrors.TMS_LLM_KEY, "APIKey is empty")
	}
	if i.Source == "" {
		return tmserrors.NewErrorf(tmserrors.TMS_LLM_SOURCE, "Source is empty")
	}
	return nil
}

// maskAPIKey masks the API key for logging purposes
func maskAPIKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return "****"
	}
	return apiKey[:4] + "***" + apiKey[len(apiKey)-4:]
}

func (i *ObjectParserParam) String() string {
	return "<ObjectParserParam> ReservedWordScoringMode:" + i.ReservedWordScoringMode.String() +
		", DatabaseLinkScoringMode:" + i.DatabaseLinkScoringMode.String() +
		", HistogramScoreWeightFactor:" + strconv.FormatFloat(i.HistogramScoreWeightFactor, 'f', -1, 64) +
		", Source:" + i.Source +
		", Model:" + i.Model +
		", Endpoint:" + i.Endpoint +
		", APIKey:" + maskAPIKey(i.APIKey) +
		", MaxTokens:" + fmt.Sprint(i.MaxTokens) +
		", MaxTokensName:" + i.MaxTokensName +
		", Stream:" + fmt.Sprint(i.Stream) +
		", Temperature:" + fmt.Sprint(i.Temperature)
}

func (i *ObjectParserParam) SetReservedWordScoringMode(value int) {
	switch value {
	case 0:
		i.ReservedWordScoringMode = DEDUCT_ONCE
	case 1:
		i.ReservedWordScoringMode = DEDUCT_PER_OCCURRENCE
	default:
		i.ReservedWordScoringMode = DEDUCT_ONCE
	}
}

func (i *ObjectParserParam) GetReservedWordScoringMode() IncompatibleFeatureScoringMode {
	return i.ReservedWordScoringMode
}

func (i *ObjectParserParam) GetDatabaseLinkScoringMode() IncompatibleFeatureScoringMode {
	return i.DatabaseLinkScoringMode
}

func (i *ObjectParserParam) SetDatabaseLinkScoringMode(value int) {
	switch value {
	case 0:
		i.DatabaseLinkScoringMode = DEDUCT_ONCE
	case 1:
		i.DatabaseLinkScoringMode = DEDUCT_PER_OCCURRENCE
	default:
		i.DatabaseLinkScoringMode = DEDUCT_ONCE
	}
}

func (i *ObjectParserParam) SetHistogramScoreWeightFactor(value float64) {
	if value <= 0 {
		value = 1.5
	}
	i.HistogramScoreWeightFactor = value
}
func (i *LLMProviderAPIConfig) SetModel(value string) {
	i.Model = value
}
func (i *LLMProviderAPIConfig) SetEndpoint(value string) {
	i.Endpoint = value
}
func (i *LLMProviderAPIConfig) SetAPIKey(value string) {
	i.APIKey = value
}
func (i *LLMProviderAPIConfig) SetSource(value string) {
	i.Source = value
}

func (i *ObjectParserParam) SetConvertJavaThread(value int) {
	if value <= 0 {
		value = 2
	}
	i.ConvertJavaThread = value
}

func (i *ObjectParserParam) SetAnalyzeWorkerPoolSize(value int) {
	if value <= 0 {
		value = 8 // Default to a reasonable value for concurrent processing
	}
	i.AnalyzeWorkerPoolSize = value
}

func (i *ObjectParserParam) SetAnalyzeBatchSize(value int) {
	if value <= 0 {
		value = 100
	}
	i.AnalyzeBatchSize = value
}

func (i *LLMProviderAPIConfig) SetStream(value bool) {
	i.Stream = value
}

func (i *LLMProviderAPIConfig) SetTimeout(value int) {
	if value <= 0 {
		value = 300
	}
	i.Timeout = value
}

func (i *LLMProviderAPIConfig) SetMaxTokens(value int) {
	if value <= 0 {
		value = 4096
	}
	i.MaxTokens = value
}

func (i *LLMProviderAPIConfig) SetMaxTokensName(value string) {
	i.MaxTokensName = strings.ToLower(strings.TrimSpace(value))
	if i.MaxTokensName == "" {
		i.MaxTokensName = "max_tokens"
	}
}

func (i *LLMProviderAPIConfig) SetTemperature(value float32) {
	if value <= 0 || value > 1 {
		value = 0.7
	}
	i.Temperature = value
}

type FeatureItem struct {
	FeatureType string
	FeatureKey  string
}

func (i *FeatureItem) GetUniqueKey() string {
	return i.FeatureType + "." + i.FeatureKey
}
