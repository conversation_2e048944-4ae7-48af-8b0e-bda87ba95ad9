package structs

type SchemaInfo struct {
	SchemaName    string        `json:"key"`
	AccountStatus string        `json:"accountStatus"`
	ObjectTypes   []*ObjectType `json:"children"`
	SchemaNameT   string        `json:"schemaNameT"`
}

type ObjectType struct {
	TypeName    string      `json:"key"`
	ObjectCount int64       `json:"objectCount"`
	ObjectNames []string    `json:"children"`
	Tables      []TableInfo `json:"tables"`
}

type TableInfo struct {
	TableName           string  `json:"tableName"`
	PartitioningType    string  `json:"partitioningType"`
	PartitionCount      int     `json:"partitionCount"`
	SubPartitioningType string  `json:"subPartitioningType"`
	SubPartitionCount   int     `json:"subPartitionCount"`
	Pk                  string  `json:"pk"`
	Uk                  string  `json:"uk"`
	TableSizeM          float64 `json:"tableSizeM"`
}

type ColumnName struct {
	SchemaName string `json:"schemaName"`
	TableName  string `json:"tableName"`
	ColumnName string `json:"columnName"`
	DataType   string `json:"dataType"`
}

type SequenceObj struct {
	SchemaName   string `json:"schemaName"`
	SequenceName string `json:"sequenceName"`
}

type SequenceSummary struct {
	SchemaName string `json:"schemaName"`
	Count      string `json:"count"`
}

type CsvSchemaTables struct {
	DbNameS     string `json:"dbNameS"`
	SchemaNameS string `json:"schemaNameS"`
	TableNameS  string `json:"tableNameS"`
	DbNameT     string `json:"dbNameT"`
	SchemaNameT string `json:"schemaNameT"`
	TableNameT  string `json:"tableNameT"`
}

type Privilege struct {
	Grantee      string
	Privilege    string
	PrivilegeVal string
	AdminOption  string
	Result       string
	Host         string
	TmsTask      string
}

type TablePartitions struct {
	TableName     string `json:"tableName"`
	PartitionName string `json:"partitionName"`
}
