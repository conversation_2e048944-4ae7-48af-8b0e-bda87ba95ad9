package structs

import (
	"fmt"
	"path"
	"strings"

	"github.com/pelletier/go-toml/v2"
	"github.com/pingcap/errors"
)

type FullMigrationConfigParam struct {
	ApplyThreads int

	ChunkSize           int64
	InsertBatchSize     int
	TaskThreads         int
	TableThreads        int
	SqlThreads          int
	TaskCountPerTable   int
	CallTimeout         int
	ConsistentRead      bool
	RemoveSplitTask     bool
	SQLMigrateStatement int
	SQLRetryStatement   int
}

type SQLStatementType int

const (
	SQLStatementInsert SQLStatementType = iota
	SQLStatementReplace
	SQLStatementInsertIgnore
)

func (i SQLStatementType) String() string {
	switch i {
	case SQLStatementInsert:
		return "INSERT"
	case SQLStatementReplace:
		return "REPLACE"
	case SQLStatementInsertIgnore:
		return "INSERT IGNORE"
	default:
		return "INSERT"
	}
}

func (i FullMigrationConfigParam) GetSQLStatementType(isRetry bool) SQLStatementType {
	if isRetry {
		if i.SQLRetryStatement == 2 {
			return SQLStatementInsertIgnore
		}
		return SQLStatementReplace
	}

	if i.SQLMigrateStatement == 2 {
		return SQLStatementInsertIgnore
	}

	return SQLStatementInsert
}

func (i FullMigrationConfigParam) GetConsistentRead() bool {
	return i.ConsistentRead
}

type CSVMigrationConfigParam struct {
	CSVParam       CSVParam
	LightningParam LightningParam
}

func (csvParam CSVMigrationConfigParam) Validate() error {
	if csvParam.GetTiDBPDAddr() == "" || csvParam.GetTiDBPDAddr() == "0.0.0.0:2379" {
		return errors.New("没有正确配置该任务的参数tidb.pd_addr")
	}
	if csvParam.GetLightningBinaryPath() == "" || csvParam.GetLightningBinaryPath() == "/path/to/tidb-lightning" {
		return errors.New("没有正确配置该任务的参数csv.lightning_binary_path")
	}
	if csvParam.GetOutputDirPrefix() == "" || csvParam.GetOutputDirPrefix() == "/path/to/csv" {
		return errors.New("没有正确配置该任务的参数csv.output_dir")
	}
	if csvParam.GetSortedKvDirPrefix() == "" || csvParam.GetSortedKvDirPrefix() == "/path/to/sorted_kv" {
		return errors.New("没有正确配置该任务的参数csv.sorted_kv_dir")
	}
	return nil
}

type LightningParam struct {
	RemoveCSV           bool
	LightningBinaryPath string
	BatchImportSize     int

	LightningLogLevel          string
	LightningMetaSchemaName    string
	LightningCheckRequirements bool
	LightningIndexConcurrency  int
	LightningTableConcurrency  int

	PostRestoreAnalyze  string
	PostRestoreChecksum string

	TiDBLogLevel   string
	TiDBPDAddr     string
	TiDBStatusPort int

	TiKVImporterDiskQuota           string
	TiKVImporterIncrementalImport   bool
	TiKVImporterDuplicateResolution string
	TiKVImporterBackend             string
	TiKVImporterOnDuplicate         string
	TiKVImporterStoreWriteBWLimit   string
	TiKVImporterRangeConcurrency    int

	CheckpointDriver string
	CheckpointEnable bool

	MydumperCsvTrimLastSeparator   bool
	MydumperCsvNull                string
	MydumperCsvNotNull             bool
	MydumperCsvHeaderSchemaMatch   bool
	MydumperFilter                 []string
	MydumperStrictFormat           bool
	MydumperDataInvalidCharReplace string
	MydumperCharacterSet           string
	MydumperBatchImportRatio       float64
	MydumperReadBlockSize          string

	CronSwitchMode string
}

type CSVParam struct {
	Header          bool
	Separator       string
	Terminator      string
	Charset         string
	Delimiter       string
	EscapeBackslash bool
	OutputDir       string
	SortedKVDir     string

	ChunkSize         int64
	InsertBatchSize   int
	TaskThreads       int
	TableThreads      int
	SqlThreads        int
	TaskCountPerTable int
	CallTimeout       int
	ConsistentRead    bool
	RemoveSplitTask   bool
}

func (i FullMigrationConfigParam) String() string {
	return fmt.Sprintf("<FullMigrationConfigParam> ApplyThreads:%v, ChunkSize:%v, InsertBatchSize:%v, SqlThreads:%v, TableThreads:%v, TaskThreads:%v, TaskCountPerTable:%v, CallTimeout:%v, ConsistentRead:%v, RemoveSplitTask:%v, SQLMigrateStatement:%v, SQLRetryStatement:%v",
		i.ApplyThreads, i.ChunkSize, i.InsertBatchSize, i.SqlThreads, i.TableThreads, i.TaskThreads, i.TaskCountPerTable,
		i.CallTimeout, i.ConsistentRead, i.RemoveSplitTask, i.SQLMigrateStatement, i.SQLRetryStatement)
}

type InvokeMigrationOption struct {
	DoSplit   bool
	DoMigrate bool
}

func (i InvokeMigrationOption) String() string {
	return fmt.Sprintf("<InvokeMigrationOption> DoSplit:%v, DoMigrate:%v", i.DoSplit, i.DoMigrate)
}

func (i *CSVMigrationConfigParam) GetHeader() bool {
	return i.CSVParam.Header
}

func (i *CSVMigrationConfigParam) SetHeader(h bool) {
	i.CSVParam.Header = h
}

func (i *CSVMigrationConfigParam) SetSeparator(s string) {
	i.CSVParam.Separator = s
}

func (i *CSVMigrationConfigParam) SetTerminator(t string) {
	i.CSVParam.Terminator = t
}

func (i *CSVMigrationConfigParam) SetCharset(c string) {
	i.CSVParam.Charset = c
}

func (i *CSVMigrationConfigParam) SetDelimiter(d string) {
	i.CSVParam.Delimiter = d
}

func (i *CSVMigrationConfigParam) SetEscapeBackslash(e bool) {
	i.CSVParam.EscapeBackslash = e
}

func (i *CSVMigrationConfigParam) SetChunkSize(c int64) {
	i.CSVParam.ChunkSize = c
}

func (i *CSVMigrationConfigParam) SetCallTimeout(c int) {
	i.CSVParam.CallTimeout = c
}

func (i *CSVMigrationConfigParam) SetConsistentRead(c bool) {
	i.CSVParam.ConsistentRead = c
}

func (i *CSVMigrationConfigParam) SetRemoveSplitTask(c bool) {
	i.CSVParam.RemoveSplitTask = c
}

func (i *CSVMigrationConfigParam) SetInsertBatchSize(b int) {
	i.CSVParam.InsertBatchSize = b
}

func (i *CSVMigrationConfigParam) SetTaskThreads(t int) {
	i.CSVParam.TaskThreads = t
}

func (i *CSVMigrationConfigParam) SetTableThreads(t int) {
	i.CSVParam.TableThreads = t
}

func (i *CSVMigrationConfigParam) SetSqlThreads(s int) {
	i.CSVParam.SqlThreads = s
}

func (i *CSVMigrationConfigParam) SetTaskCountPerTable(t int) {
	i.CSVParam.TaskCountPerTable = t
}

func (i *CSVMigrationConfigParam) SetOutputDir(o string) {
	i.CSVParam.OutputDir = o
}

func (i *CSVMigrationConfigParam) SetSortedKVDir(o string) {
	i.CSVParam.SortedKVDir = o
}

func (i *CSVMigrationConfigParam) SetBatchImportSize(e int) {
	i.LightningParam.BatchImportSize = e
}

func (i *CSVMigrationConfigParam) GetBatchImportSize() int {
	return i.LightningParam.BatchImportSize
}

func (i *CSVMigrationConfigParam) SetLightningBinaryPath(l string) {
	i.LightningParam.LightningBinaryPath = l
}

func (i *CSVMigrationConfigParam) SetLightningRemoveCSV(l bool) {
	i.LightningParam.RemoveCSV = l
}

func (i *CSVMigrationConfigParam) SetMydumperCsvTrimLastSeparator(m bool) {
	i.LightningParam.MydumperCsvTrimLastSeparator = m
}

func (i *CSVMigrationConfigParam) SetMydumperCsvNull(m string) {
	i.LightningParam.MydumperCsvNull = m
}

func (i *CSVMigrationConfigParam) SetMydumperCsvNotNull(m bool) {
	i.LightningParam.MydumperCsvNotNull = m
}

func (i *CSVMigrationConfigParam) SetMydumperCsvHeaderSchemaMatch(m bool) {
	i.LightningParam.MydumperCsvHeaderSchemaMatch = m
}

func (i *CSVMigrationConfigParam) SetMydumperFilter(m []string) {
	i.LightningParam.MydumperFilter = m
}

func (i *CSVMigrationConfigParam) SetMydumperStrictFormat(m bool) {
	i.LightningParam.MydumperStrictFormat = m
}

func (i *CSVMigrationConfigParam) SetMydumperDataInvalidCharReplace(m string) {
	i.LightningParam.MydumperDataInvalidCharReplace = m
}

func (i *CSVMigrationConfigParam) SetMydumperCharacterSet(m string) {
	i.LightningParam.MydumperCharacterSet = m
}

func (i *CSVMigrationConfigParam) SetMydumperBatchImportRatio(m float64) {
	i.LightningParam.MydumperBatchImportRatio = m
}

func (i *CSVMigrationConfigParam) SetMydumperReadBlockSize(m string) {
	i.LightningParam.MydumperReadBlockSize = m
}

func (i *CSVMigrationConfigParam) SetLightningLogLevel(l string) {
	i.LightningParam.LightningLogLevel = l
}

func (i *CSVMigrationConfigParam) SetLightningMetaSchemaName(l string) {
	i.LightningParam.LightningMetaSchemaName = l
}

func (i *CSVMigrationConfigParam) SetPostRestoreAnalyze(p string) {
	i.LightningParam.PostRestoreAnalyze = p
}

func (i *CSVMigrationConfigParam) SetPostRestoreChecksum(p string) {
	i.LightningParam.PostRestoreChecksum = p
}

func (i *CSVMigrationConfigParam) SetTiDBLogLevel(t string) {
	i.LightningParam.TiDBLogLevel = t
}

func (i *CSVMigrationConfigParam) SetLightningIndexConcurrency(t int) {
	i.LightningParam.LightningIndexConcurrency = t
}

func (i *CSVMigrationConfigParam) SetLightningTableConcurrency(t int) {
	i.LightningParam.LightningTableConcurrency = t
}

func (i *CSVMigrationConfigParam) SetLightningCheckRequirements(t bool) {
	i.LightningParam.LightningCheckRequirements = t
}

func (i *CSVMigrationConfigParam) SetTiDBPDAddr(t string) {
	i.LightningParam.TiDBPDAddr = strings.TrimSpace(t)
}

func (i *CSVMigrationConfigParam) SetTiDBStatusPort(t int) {
	i.LightningParam.TiDBStatusPort = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterDiskQuota(t string) {
	i.LightningParam.TiKVImporterDiskQuota = t
}

func (i *CSVMigrationConfigParam) SetCronSwitchMode(t string) {
	i.LightningParam.CronSwitchMode = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterRangeConcurrency(t int) {
	if t < 0 {
		t = 0
	}
	i.LightningParam.TiKVImporterRangeConcurrency = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterStoreWriteBWLimit(t string) {
	tt := strings.ToUpper(t)
	if tt == "0" || tt == "0MIB" || tt == "0MB" {
		return
	}
	i.LightningParam.TiKVImporterStoreWriteBWLimit = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterIncrementalImport(t bool) {
	i.LightningParam.TiKVImporterIncrementalImport = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterDuplicateResolution(t string) {
	i.LightningParam.TiKVImporterDuplicateResolution = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterBackend(t string) {
	i.LightningParam.TiKVImporterBackend = t
}

func (i *CSVMigrationConfigParam) SetTiKVImporterOnDuplicate(t string) {
	i.LightningParam.TiKVImporterOnDuplicate = t
}

func (i *CSVMigrationConfigParam) SetCheckpointDriver(c string) {
	i.LightningParam.CheckpointDriver = c
}

func (i *CSVMigrationConfigParam) SetCheckpointEnable(c bool) {
	i.LightningParam.CheckpointEnable = c
}

func (i *CSVMigrationConfigParam) GetLightningBinaryPath() string {
	return i.LightningParam.LightningBinaryPath
}

func (i *CSVMigrationConfigParam) GetSeparator() string {
	return i.CSVParam.Separator
}

func (i *CSVMigrationConfigParam) GetConsistentRead() bool {
	return i.CSVParam.ConsistentRead
}

func (i *CSVMigrationConfigParam) GetTerminator() string {
	return i.CSVParam.Terminator
}

func (i *CSVMigrationConfigParam) GetCharset() string {
	return i.CSVParam.Charset
}

func (i *CSVMigrationConfigParam) GetLightningLogLevel() string {
	return i.LightningParam.LightningLogLevel
}

func (i *CSVMigrationConfigParam) GetLightningIndexConcurrency() int {
	return i.LightningParam.LightningIndexConcurrency
}

func (i *CSVMigrationConfigParam) GetLightningTableConcurrency() int {
	return i.LightningParam.LightningTableConcurrency
}

func (i *CSVMigrationConfigParam) GetLightningCheckRequirements() bool {
	return i.LightningParam.LightningCheckRequirements
}

func (i *CSVMigrationConfigParam) GetLightningMetaSchemaName() string {
	return i.LightningParam.LightningMetaSchemaName
}

func (i *CSVMigrationConfigParam) GetPostRestoreAnalyze() string {
	return i.LightningParam.PostRestoreAnalyze
}

func (i *CSVMigrationConfigParam) GetPostRestoreChecksum() string {
	return i.LightningParam.PostRestoreChecksum
}

func (i *CSVMigrationConfigParam) GetTiDBLogLevel() string {
	return i.LightningParam.TiDBLogLevel
}

func (i *CSVMigrationConfigParam) GetTiKVImporterDiskQuota() string {
	return i.LightningParam.TiKVImporterDiskQuota
}

func (i *CSVMigrationConfigParam) GetTiKVImporterIncrementalImport() bool {
	return i.LightningParam.TiKVImporterIncrementalImport
}

func (i *CSVMigrationConfigParam) GetTiKVImporterStoreWriteBWLimit() string {
	return i.LightningParam.TiKVImporterStoreWriteBWLimit
}

func (i *CSVMigrationConfigParam) GetTiKVImporterRangeConcurrency() int {
	return i.LightningParam.TiKVImporterRangeConcurrency
}

func (i *CSVMigrationConfigParam) GetTiKVImporterDuplicateResolution() string {
	return i.LightningParam.TiKVImporterDuplicateResolution
}

func (i *CSVMigrationConfigParam) GetTiKVImporterBackend() string {
	return i.LightningParam.TiKVImporterBackend
}

func (i *CSVMigrationConfigParam) GetTiKVImporterOnDuplicate() string {
	return i.LightningParam.TiKVImporterOnDuplicate
}

func (i *CSVMigrationConfigParam) GetCheckpointDriver() string {
	return i.LightningParam.CheckpointDriver
}

func (i *CSVMigrationConfigParam) GetCheckpointEnable() bool {

	return i.LightningParam.CheckpointEnable
}

func (i *CSVMigrationConfigParam) ShouldRemoveCSV(dbType string) bool {
	if dbType == "csv" {
		return false
	}
	return i.LightningParam.RemoveCSV
}

func (i *CSVMigrationConfigParam) GetOutputDirPrefix() string {
	return i.CSVParam.OutputDir
}

func (i *CSVMigrationConfigParam) GetSortedKvDirPrefix() string {
	return i.CSVParam.SortedKVDir
}

func (i *CSVMigrationConfigParam) GetMydumperCSVTrimLastSeparator() bool {
	return i.LightningParam.MydumperCsvTrimLastSeparator
}

func (i *CSVMigrationConfigParam) GetMydumperCSVNull() string {
	return i.LightningParam.MydumperCsvNull
}

func (i *CSVMigrationConfigParam) GetMydumperCSVNotNull() bool {
	return i.LightningParam.MydumperCsvNotNull
}

func (i *CSVMigrationConfigParam) GetMydumperCSVHeaderSchemaMatch() bool {
	return i.LightningParam.MydumperCsvHeaderSchemaMatch
}

func (i *CSVMigrationConfigParam) GetMydumperFilter() []string {
	return i.LightningParam.MydumperFilter
}

func (i *CSVMigrationConfigParam) GetMydumperStrictFormat() bool {
	return i.LightningParam.MydumperStrictFormat
}

func (i *CSVMigrationConfigParam) GetMydumperDataInvalidCharReplace() string {
	return i.LightningParam.MydumperDataInvalidCharReplace
}

func (i *CSVMigrationConfigParam) GetMydumperCharacterSet() string {
	return i.LightningParam.MydumperCharacterSet
}

func (i *CSVMigrationConfigParam) GetMydumperBatchImportRatio() float64 {
	return i.LightningParam.MydumperBatchImportRatio
}

func (i *CSVMigrationConfigParam) GetMydumperReadBlockSize() string {
	return i.LightningParam.MydumperReadBlockSize
}

func (i *CSVMigrationConfigParam) GetCronSwitchMode() string {
	return i.LightningParam.CronSwitchMode
}

func (i *CSVMigrationConfigParam) GetTiDBPDAddr() string {
	return i.LightningParam.TiDBPDAddr
}

func (i *CSVMigrationConfigParam) GetTiDBStatusPort() int {
	return i.LightningParam.TiDBStatusPort
}

func (i *CSVMigrationConfigParam) GetDelimiter() string {
	return i.CSVParam.Delimiter
}

func (i *CSVMigrationConfigParam) GetEscapeBackslash() bool {
	return i.CSVParam.EscapeBackslash
}

func (i *CSVMigrationConfigParam) GetOutputDataDir(channelId, taskId int) string {
	return path.Join(i.GetOutputDirPrefix(), i.GetOutputBaseDir(channelId, taskId), "data")
}

func (i *CSVMigrationConfigParam) GetOutputDataRootDir(channelId, taskId int) string {
	return path.Join(i.GetOutputDirPrefix(), i.GetOutputBaseDir(channelId, taskId))
}

func (i *CSVMigrationConfigParam) GetOutputSortedKVDir(channelId, taskId int) string {
	return path.Join(i.GetSortedKvDirPrefix(), i.GetOutputBaseDir(channelId, taskId))
}

func (i *CSVMigrationConfigParam) GetOutputBaseDir(channelId, taskId int) string {
	return fmt.Sprintf("export_data-%d-%d", channelId, taskId)
}

func (i *CSVMigrationConfigParam) String() string {
	return fmt.Sprintf("<CSVMigrationConfigParam> CSVParam:%v, LightningParam:%v", i.CSVParam.String(), i.LightningParam.String())
}

func (i CSVParam) String() string {
	return fmt.Sprintf("<CSVParam> Header:%v, Separator:%v, Terminator:%v, Charset:%v, Delimiter:%v, "+
		"EscapeBackslash:%v, OutputDir:%v, SortedKVDir:%v, ChunkSize:%v, InsertBatchSize:%v, SqlThreads:%v, "+
		"TableThreads:%v, TaskThreads:%v, TaskCountPerTable:%v, CallTimeout:%v, ConsistentRead:%v, RemoveSplitTask:%v",
		i.Header, i.Separator, i.Terminator, i.Charset, i.Delimiter,
		i.EscapeBackslash, i.OutputDir, i.SortedKVDir, i.ChunkSize, i.InsertBatchSize, i.SqlThreads,
		i.TableThreads, i.TaskThreads, i.TaskCountPerTable, i.CallTimeout, i.ConsistentRead, i.RemoveSplitTask)
}

func (i LightningParam) String() string {
	return fmt.Sprintf("<LightningParam> RemoveCSV:%v, LightningBinaryPath:%v, LightningLogLevel:%v, "+
		"LightningMetaSchemaName:%v, PostRestoreAnalyze:%v, PostRestoreChecksum:%v, TiDBLogLevel:%v, "+
		"TiDBPDAddr:%v, TiDBStatusPort:%v, TiKVImporterDiskQuota:%v, TiKVImporterIncrementalImport:%v, "+
		"TiKVImporterDuplicateResolution:%v, TiKVImporterBackend:%v, TiKVImporterOnDuplicate:%v, "+
		"TiKVImporterRangeConcurrency:%v, TiKVImporterStoreWriteBWLimit:%v, CheckpointDriver:%v, "+
		"CheckpointEnable:%v, MydumperCsvTrimLastSeparator:%v, MydumperCsvNull:%v, MydumperCsvNotNull:%v, "+
		"MydumperCsvHeaderSchemaMatch:%v, MydumperFilter:%v, MydumperStrictFormat:%v, MydumperDataInvalidCharReplace:%v, "+
		"MydumperCharacterSet:%v, MydumperBatchImportRatio:%v, MydumperReadBlockSize:%v, CronSwitchMode:%v, "+
		"BatchImportSize:%v",
		i.RemoveCSV, i.LightningBinaryPath, i.LightningLogLevel,
		i.LightningMetaSchemaName, i.PostRestoreAnalyze, i.PostRestoreChecksum, i.TiDBLogLevel,
		i.TiDBPDAddr, i.TiDBStatusPort, i.TiKVImporterDiskQuota, i.TiKVImporterIncrementalImport,
		i.TiKVImporterDuplicateResolution, i.TiKVImporterBackend, i.TiKVImporterOnDuplicate,
		i.TiKVImporterRangeConcurrency, i.TiKVImporterStoreWriteBWLimit, i.CheckpointDriver,
		i.CheckpointEnable, i.MydumperCsvTrimLastSeparator, i.MydumperCsvNull, i.MydumperCsvNotNull,
		i.MydumperCsvHeaderSchemaMatch, i.MydumperFilter, i.MydumperStrictFormat, i.MydumperDataInvalidCharReplace,
		i.MydumperCharacterSet, i.MydumperBatchImportRatio, i.MydumperReadBlockSize, i.CronSwitchMode,
		i.BatchImportSize)
}

type CommonParam interface {
	GetChunkSize() int64
	GetInsertBatchSize() int
	GetTaskThreads() int
	GetTableThreads() int
	GetSqlThreads() int
	GetTaskCountPerTable() int
}

func (i FullMigrationConfigParam) GetCallTimeout() int {
	return i.CallTimeout
}

func (i FullMigrationConfigParam) GetRemoveSplitTask() bool {
	return i.RemoveSplitTask
}

func (i FullMigrationConfigParam) GetChunkSize() int64 {
	return i.ChunkSize
}

func (i FullMigrationConfigParam) GetInsertBatchSize() int {
	return i.InsertBatchSize
}

func (i FullMigrationConfigParam) GetTaskThreads() int {
	return i.TaskThreads
}

func (i FullMigrationConfigParam) GetTableThreads() int {
	return i.TableThreads
}

func (i FullMigrationConfigParam) GetSqlThreads() int {
	return i.SqlThreads
}

func (i FullMigrationConfigParam) GetTaskCountPerTable() int {
	return i.TaskCountPerTable
}

func (i FullMigrationConfigParam) GetApplyThreads() int {
	return i.ApplyThreads
}

func (i *CSVMigrationConfigParam) GetChunkSize() int64 {
	return i.CSVParam.ChunkSize
}

func (i *CSVMigrationConfigParam) GetRemoveSplitTask() bool {
	return i.CSVParam.RemoveSplitTask
}
func (i *CSVMigrationConfigParam) GetCallTimeout() int {
	if i.CSVParam.CallTimeout == 0 {
		return 10
	}
	return i.CSVParam.CallTimeout
}

func (i *CSVMigrationConfigParam) GetInsertBatchSize() int {
	return i.CSVParam.InsertBatchSize
}

func (i *CSVMigrationConfigParam) GetTaskThreads() int {
	return i.CSVParam.TaskThreads
}

func (i *CSVMigrationConfigParam) GetTableThreads() int {
	return i.CSVParam.TableThreads
}

func (i *CSVMigrationConfigParam) GetSqlThreads() int {
	return i.CSVParam.SqlThreads
}

func (i *CSVMigrationConfigParam) GetTaskCountPerTable() int {
	return i.CSVParam.TaskCountPerTable
}

type LightningProgress struct {
	Time         string
	LogLevel     string
	Total        string
	Tables       string
	Chunks       string
	Engines      string
	RestoreBytes string
	RestoreRows  string
	ImportBytes  string
	EncodeSpeed  string
	State        string
	Remaining    string
}

type LightningConfiguration struct {
	Lightning    Lightning    `toml:"lightning"`
	TiKVImporter TiKVImporter `toml:"tikv-importer"`
	MyDumper     MyDumper     `toml:"mydumper"`
	Checkpoint   Checkpoint   `toml:"checkpoint"`
	Tidb         Tidb         `toml:"tidb"`
	PostRestore  PostRestore  `toml:"post-restore"`
	Cron         Cron         `toml:"cron"`
}

type OptionLightningConfiguration func(*LightningConfiguration)

func OptionLightningLogFile(fileName string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Lightning.File = fileName
	}
}

func OptionMydumperFiles(files []MyDumperFile) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.Files = files
	}
}

func OptionDataSourceDir(dir string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.DataSourceDir = dir
	}
}

func OptionMyDumperFile(f MyDumperFile) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.Files = []MyDumperFile{f}
	}
}

func OptionCheckpointDSN(dsn string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Checkpoint.Dsn = dsn
	}
}

func OptionSortedKVDir(kvDir string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.SortedKvDir = kvDir
	}
}

func OptionTiDBHost(host string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.Host = host
	}
}

func OptionTiDBPort(port int) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.Port = port
	}
}

func OptionTiDBUser(user string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.User = user
	}
}

func OptionTiDBPassword(password string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.Password = password
	}
}

func OptionTiDBStatusPort(port int) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.StatusPort = port
	}
}

func OptionTiDBPDAddr(pdAddr string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.PdAddr = pdAddr
	}
}

func OptionTiDBLogLevel(level string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Tidb.LogLevel = level
	}
}

func OptionCSVSeparator(separator string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.Separator = separator
	}
}

func OptionCSVHeader(header bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.Header = header
	}
}

func OptionCSVBackslashEscape(backslashEscape bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.BackslashEscape = backslashEscape
	}
}

func OptionCSVLightningLogLevel(level string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Lightning.Level = level
	}
}

func OptionCSVLightningCheckRequirements(check bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Lightning.CheckRequirements = check
	}
}

func OptionCSVLightningIndexConcurrency(concurrency int) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Lightning.IndexConcurrency = concurrency
	}
}

func OptionCSVLightningTableConcurrency(concurrency int) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Lightning.TableConcurrency = concurrency
	}
}

func OptionCSVLightningMetaSchemaName(name string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Lightning.MetaSchemaName = name
	}
}

func OptionCSVPostRestoreAnalyze(analyze string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.PostRestore.Analyze = analyze
	}
}

func OptionCSVTiKVImporterDiskQuota(diskQuota string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.DiskQuota = diskQuota
	}
}

func OptionCSVTiKVImporterIncrementalImport(incrementalImport bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.IncrementalImport = incrementalImport
	}
}

func OptionTiKVImporterStoreWriteBWLimit(bwLimit string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.StoreWriteBWLimit = bwLimit
	}
}

func OptionTiKVImporterRangeConcurrency(rc int) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.RangeConcurrency = rc
	}
}

func OptionCSVTiKVImporterDuplicateResolution(duplicateResolution string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.DuplicateResolution = duplicateResolution
	}
}

func OptionCSVTiKVImporterBackend(backend string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.Backend = backend
	}
}

func OptionCSVTiKVImporterOnDuplicate(value string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.TiKVImporter.OnDuplicate = value
	}
}

func OptionCSVCheckpointDriver(driver string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Checkpoint.Driver = driver
	}
}

func OptionCSVCheckpointEnable(enable bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Checkpoint.Enable = enable
	}
}

func OptionCSVMydumperCSVTrimLastSeparator(trimLastSeparator bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.TrimLastSeparator = trimLastSeparator
	}
}

func OptionCSVMydumperCSVNull(null string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.Null = null
	}
}

func OptionCSVCronSwitchMode(switchMode string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Cron.SwitchMode = switchMode
	}
}

func OptionCSVMydumperCSVNotNull(notNull bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.NotNull = notNull
	}
}

func OptionCSVMydumperCSVHeaderSchemaMatch(headerSchemaMatch bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.HeaderSchemaMatch = headerSchemaMatch
	}
}

func OptionCSVMydumperFilter(filter []string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.Filter = filter
	}
}

func OptionCSVMydumperStrictFormat(strictFormat bool) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.StrictFormat = strictFormat
	}
}

func OptionCSVMydumperDataCharacterSet(dataCharacterSet string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.DataCharacterSet = dataCharacterSet
	}
}

func OptionCSVMydumperCharacterSet(characterSet string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CharacterSet = characterSet
	}
}

func OptionCSVMydumperBatchImportRatio(batchImportRatio float64) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.BatchImportRatio = batchImportRatio
	}
}

func OptionCSVMydumperReadBlockSize(readBlockSize string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.ReadBlockSizeSize = readBlockSize
	}
}

func OptionCSVMydumperDataInvalidCharReplace(dataInvalidCharReplace string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.DataInvalidCharReplace = dataInvalidCharReplace
	}
}

func OptionCSVPostRestoreChecksum(checksum string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.PostRestore.Checksum = checksum
	}
}

func OptionCSVTerminator(terminator string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.Terminator = terminator
	}
}

func OptionCSVDelimiter(delimiter string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.MyDumper.CSV.Delimiter = delimiter
	}
}

func OptionCronLogInterval(interval string) func(*LightningConfiguration) {
	return func(l *LightningConfiguration) {
		l.Cron.LogProgress = interval
	}
}

func (i *LightningConfiguration) MarshalTOML() (string, error) {
	bytes, err := toml.Marshal(*i)
	if err != nil {
		return "", err
	}

	content := string(bytes)
	content = strings.Replace(content, "separator = '\\u001e'", `separator = "\u001e"`, -1)
	return content, nil
}

type Lightning struct {
	Level             string `toml:"level"`
	File              string `toml:"file"`
	MetaSchemaName    string `toml:"meta-schema-name"`
	CheckRequirements bool   `toml:"check-requirements"`
	IndexConcurrency  int    `toml:"index-concurrency"`
	TableConcurrency  int    `toml:"table-concurrency"`
}

type TiKVImporter struct {
	Backend             string `toml:"backend"`
	IncrementalImport   bool   `toml:"incremental-import"`
	SortedKvDir         string `toml:"sorted-kv-dir"`
	DuplicateResolution string `toml:"duplicate-resolution"`
	DiskQuota           string `toml:"disk-quota"`
	OnDuplicate         string `toml:"on-duplicate,omitempty"`
	RangeConcurrency    int    `toml:"range-concurrency,omitempty"`
	StoreWriteBWLimit   string `toml:"store-write-bwlimit,omitempty"`
}

type MyDumperFile struct {
	Pattern string `toml:"pattern"`
	Schema  string `toml:"schema"`
	Table   string `toml:"table"`
	Type    string `toml:"type"`
}

type MyDumperCSV struct {
	Separator         string `toml:"separator"`
	Delimiter         string `toml:"delimiter"`
	Terminator        string `toml:"terminator"`
	Header            bool   `toml:"header"`
	HeaderSchemaMatch bool   `toml:"header-schema-match"`
	NotNull           bool   `toml:"not-null"`
	Null              string `toml:"null"`
	BackslashEscape   bool   `toml:"backslash-escape"`
	TrimLastSeparator bool   `toml:"trim-last-separator"`
}

type MyDumper struct {
	ReadBlockSizeSize      string  `toml:"read-block-size"`
	BatchImportRatio       float64 `toml:"batch-import-ratio"`
	DataSourceDir          string  `toml:"data-source-dir"`
	CharacterSet           string  `toml:"character-set"`
	DataCharacterSet       string  `toml:"data-character-set"`
	DataInvalidCharReplace string  `toml:"data-invalid-char-replace"`
	StrictFormat           bool    `toml:"strict-format"`

	Filter []string       `toml:"filter,omitempty"`
	Files  []MyDumperFile `toml:"files,omitempty"`
	CSV    MyDumperCSV    `toml:"csv"`
}

type Tidb struct {
	Host       string `toml:"host"`
	Port       int    `toml:"port"`
	User       string `toml:"user"`
	Password   string `toml:"password"`
	StatusPort int    `toml:"status-port"`
	PdAddr     string `toml:"pd-addr"`
	LogLevel   string `toml:"log-level"`
}

type Checkpoint struct {
	Enable bool   `toml:"enable"`
	Driver string `toml:"driver"`
	Dsn    string `toml:"dsn,omitempty"`
}

type Cron struct {
	SwitchMode     string `toml:"switch-mode"`
	LogProgress    string `toml:"log-progress"`
	CheckDiskQuota string `toml:"check-disk-quota"`
}

type PostRestore struct {
	Checksum       string `toml:"checksum"`
	ChecksumViaSQL bool   `toml:"checksum-via-sql"`
	Analyze        string `toml:"analyze"`
}

var SingleModeMigrationInvokeOption = InvokeMigrationOption{
	DoSplit:   true,
	DoMigrate: true,
}

var ClusterModeMigrationDoSplit = InvokeMigrationOption{
	DoSplit: true,
}

var ClusterModeMigrationDoMigrate = InvokeMigrationOption{
	DoMigrate: true,
}
