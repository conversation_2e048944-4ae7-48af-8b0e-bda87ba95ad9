package errors

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestError(t *testing.T) {
	err := Error(TIMS_MARSHAL_ERROR)
	assert.Error(t, err)
	assert.Equal(t, TIMS_MARSHAL_ERROR, err.code)
	assert.Equal(t, TIMS_MARSHAL_ERROR.Explain(), err.GetMsg())
	assert.Contains(t, err.<PERSON>r(), TIMS_MARSHAL_ERROR.Explain())
	assert.Contains(t, err.<PERSON>rror(), "[10004]")
	fmt.Print(err.Error())
}

func TestNewError(t *testing.T) {
	err := NewError(TIMS_MARSHAL_ERROR, "mymessage")
	assert.Error(t, err)
	assert.Equal(t, TIMS_MARSHAL_ERROR, err.code)
	assert.Equal(t, "mymessage", err.GetMsg())
	assert.Contains(t, err.<PERSON><PERSON><PERSON>(), TIM<PERSON>_MARSHAL_ERROR.Explain())
	assert.Contains(t, err.<PERSON><PERSON>r(), "[10004]")
	assert.Contains(t, err.<PERSON>r(), "mymessage")
	fmt.Print(err.Error())
}

func TestNewEMErrorf(t *testing.T) {
	err := NewErrorf(TIMS_MARSHAL_ERROR, "mymessage %d", 3333)
	assert.Error(t, err)
	assert.Equal(t, TIMS_MARSHAL_ERROR, err.code)
	assert.Equal(t, "mymessage 3333", err.GetMsg())
	assert.Contains(t, err.Error(), TIMS_MARSHAL_ERROR.Explain())
	assert.Contains(t, err.Error(), "[10004]")
	assert.Contains(t, err.Error(), "mymessage")
	assert.Contains(t, err.Error(), "3333")

	fmt.Print(err.Error())
}

func TestWrapError(t *testing.T) {
	err := WrapError(TIMS_MARSHAL_ERROR, "mymessage", Error(TIMS_UNRECOGNIZED_ERROR))
	assert.Error(t, err)
	assert.Equal(t, TIMS_MARSHAL_ERROR, err.code)
	assert.Equal(t, "mymessage", err.GetMsg())
	assert.Contains(t, err.Error(), TIMS_MARSHAL_ERROR.Explain())
	assert.Contains(t, err.Error(), "[10004]")
	assert.Contains(t, err.Error(), "mymessage")
	assert.Contains(t, err.Error(), "[10000]")
	assert.Contains(t, err.Error(), TIMS_UNRECOGNIZED_ERROR.Explain())
	fmt.Print(err.Error())
}
