package errors

type MS_ERROR_CODE int32

const (
	TIMS_PANIC   MS_ERROR_CODE = -1
	TIMS_SUCCESS MS_ERROR_CODE = 0

	TIMS_UNRECOGNIZED_ERROR        MS_ERROR_CODE = 10000
	TIMS_PARAMETER_INVALID         MS_ERROR_CODE = 10001
	TIMS_SQL_ERROR                 MS_ERROR_CODE = 10002
	TIMS_MARSHAL_ERROR             MS_ERROR_CODE = 10003
	TIMS_UNMARSHAL_ERROR           MS_ERROR_CODE = 10004
	TIMS_BATCH_SIZE_TOO_LARGE      MS_ERROR_CODE = 10005
	TIMS_RECORD_NOT_EXIST          MS_ERROR_CODE = 10006
	TIMS_OPEN_DATABASE_FAILED      MS_ERROR_CODE = 10007
	TIMS_PING_DATABASE_FAILED      MS_ERROR_CODE = 10008
	TIMS_UNKNOWN_DATABASE_TYPE     MS_ERROR_CODE = 10009
	TIMS_NOT_ORACLE_DATASOURCE     MS_ERROR_CODE = 10010
	TIMS_LICENSE_EXPIRED           MS_ERROR_CODE = 10011
	TIMS_UPLOAD_CSV_FAILED         MS_ERROR_CODE = 10012
	TIMS_CREATE_TASK_FAILED        MS_ERROR_CODE = 10013
	TIMS_UPDATE_TASK_FAILED        MS_ERROR_CODE = 10014
	TIMS_PARSE_DSN_FAILED          MS_ERROR_CODE = 10015
	TIMS_SET_DATABASE_PARAM_FAILED MS_ERROR_CODE = 10016
	TIMS_TIMEZONE_ERROR            MS_ERROR_CODE = 10017
	TIMS_INVALID_CONFIG            MS_ERROR_CODE = 10018
	TMS_LICENSE_FEATURE_EXCEED     MS_ERROR_CODE = 11000

	//data source
	TIMS_DATASOURCE_CONNECT_FAILED             MS_ERROR_CODE = 20000
	TIMS_DATASOURCE_SAVE_CONNECT_STATUS_FAILED MS_ERROR_CODE = 20001
	TIMS_SERVICE_NAME_NOT_EXIST                MS_ERROR_CODE = 20002
	TIMS_GET_FILTER_SCHEMAS_FAILED             MS_ERROR_CODE = 20003
	TMS_SETUP_RETRY_FAILED                     MS_ERROR_CODE = 20004

	//channel
	TIMS_EXIST_CHECKING_STATUS_PRECHECK MS_ERROR_CODE = 30000

	//task
	TIMS_TASK_EXCUTION_FAILED            MS_ERROR_CODE = 40000
	TIMS_UNKOWN_TASK_TYPE                MS_ERROR_CODE = 40001
	TIMS_NO_UN_SELECTED_TABLES           MS_ERROR_CODE = 40002
	TIMS_NO_COMPARE_TABLES               MS_ERROR_CODE = 40003
	TIMS_UNKNOWN_REFERENCE_TABLES_TYPE   MS_ERROR_CODE = 40004
	TIMS_CONFLICT_TABLES                 MS_ERROR_CODE = 40005
	TIMS_CHANGE_PARTITIONING_TYPE_FAILED MS_ERROR_CODE = 40006
	TIMS_CHANGE_CLUSTER_TYPE_FAILED      MS_ERROR_CODE = 40007
	TIMS_SPLIT_TASK_FAILED               MS_ERROR_CODE = 40008
	TIMS_TASK_NOT_FOUND                  MS_ERROR_CODE = 40009
	TIMS_DM_CONFIG_BUILD_FAILED          MS_ERROR_CODE = 40010
	TIMS_DM_TARGET_DB_NOT_FOUND          MS_ERROR_CODE = 40011
	TIMS_DM_SOURCE_DB_NOT_FOUND          MS_ERROR_CODE = 40012

	//template
	TIMS_EXIST_DEFAULT_TEMPLATE MS_ERROR_CODE = 50000

	//sqlanalyze
	TMS_SQLANALYZE_BUILD_PARAM_FAILED         = 60000
	TMS_SQLANALYZE_INIT_PARAM_FAILED          = 60001
	TMS_SQLANALYZE_GET_ORACLE_SQL_FAILED      = 60002
	TMS_SQLANALYZE_EXPLAIN_SQL_IN_TIDB_FAILED = 60003
	TMS_FETCH_STATEMENT_FAILED                = 60011
	TMS_UNKOWN_REPORT_TYPE                    = 60020

	// migration
	TMS_MIGRATION_CHUNK_NOT_FOUND               = 70000
	TMS_MIGRATION_CHUNK_ANALYZE_EXIST           = 70001
	TMS_MIGRATION_CHUNK_ANALYZE_NOT_FOUND       = 70002
	TMS_MIGRATION_LIGHTNING_INVALID             = 70003
	TMS_MIGRATION_CSV_WALK_DIR_FAILED           = 70004
	TMS_MIGRATION_CSV_FILE_NUM_NOT_MATCH        = 70005
	TMS_MIGRATION_CSV_FILE_EXTRACT_TABLE_FAILED = 70006
	TMS_MIGRATION_CSV_METADATA_EXIST            = 70007
	TMS_MIGRATION_CSV_TABLE_NOT_MATCH           = 70008

	// column data
	TMS_UPDATE_COLUMN_DIGEST_NODATA       MS_ERROR_CODE = 80000
	TMS_UPDATE_COLUMN_DIGEST_MISSED       MS_ERROR_CODE = 80001
	TMS_UPDATE_COLUMN_DIGEST_NOT_EQUAL                  = 80002
	TMS_UPDATE_COLUMN_DIGEST_NOT_FOUND                  = 80003
	TMS_COLUMN_DATA_CSV_OPEN_FILE_FAILED                = 80010
	TMS_COLUMN_DATA_CSV_HEADER_NOT_MATCH                = 80011
	TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED               = 80012
	TMS_COLUMN_DATA_CSV_DATA_INVALID                    = 80013

	// increment - dsg
	TMS_DSG_ADAPTOR_NOT_EXIST                MS_ERROR_CODE = 90000
	TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY  MS_ERROR_CODE = 90001
	TMS_DSG_INCREMENT_TASK_NOT_FOUND         MS_ERROR_CODE = 90002
	TMS_DSG_CREATE_PHYSICAL_SUBSYSTEM_FAILED MS_ERROR_CODE = 90003
	TMS_DSG_CREATE_DATASOURCE_FAILED         MS_ERROR_CODE = 90004
	TMS_DSG_GET_DATASOURCE_FAILED            MS_ERROR_CODE = 90005
	TMS_DSG_DATASOURCE_NOT_ENABLE_INCREMENT  MS_ERROR_CODE = 90006
	TMS_DSG_DATASOURCE_HAS_NO_LOG_STORE_MODE MS_ERROR_CODE = 90007
	TMS_DSG_HOST_NOT_EXIST                   MS_ERROR_CODE = 90008
	TMS_DSG_CREATE_SYNC_INFO_FAILED          MS_ERROR_CODE = 90009
	TMS_DSG_VALIDATE_DATASOURCE_FAILED       MS_ERROR_CODE = 90010
	TMS_DSG_UPDATE_ASM_DATASOURCE_FAILED     MS_ERROR_CODE = 90011
	TMS_DSG_SOURCE_IP_NOT_MATCH              MS_ERROR_CODE = 90012

	// object-parser
	TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST                MS_ERROR_CODE = 91000
	TMS_OBJECT_PARSER_GET_DEPENDENCY_FAILED            MS_ERROR_CODE = 91001
	TMS_OBJECT_PARSER_GET_PACKAGE_BODY_FAILED          MS_ERROR_CODE = 91002
	TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED           MS_ERROR_CODE = 91003
	TMS_OBJECT_PARSER_MAP_SOURCE_CODE_TO_DETAIL_FAILED MS_ERROR_CODE = 91004
	TMS_OBJECT_PARSER_DUPLICATE_UUID                   MS_ERROR_CODE = 91005
	TMS_OBJECT_PARSER_PARSE_OBJECT_FAILED              MS_ERROR_CODE = 91006
	TMS_OBJECT_PARSER_INVALID_OBJECT                   MS_ERROR_CODE = 91007
	TMS_OBJECT_PARSER_PROMPT_NOT_FOUND                 MS_ERROR_CODE = 91008
	TMS_OBJECT_PARSER_PROMPT_SAVE_FAILED               MS_ERROR_CODE = 91009
	TMS_OBJECT_PARSER_PROMPT_RELATION_SAVE_FAILED      MS_ERROR_CODE = 91010
	TMS_OBJECT_PARSER_PROMPT_RELATION_DELETE_FAILED    MS_ERROR_CODE = 91011
	TMS_OBJECT_PARSER_FEATURE_UPDATE_FAILED            MS_ERROR_CODE = 91012
	TMS_OBJECT_PARSER_BUILD_PARAM_FAILED               MS_ERROR_CODE = 91013
	TMS_OBJECT_PARSER_PROMPT_DELETE_FAILED             MS_ERROR_CODE = 91014
	TMS_OBJECT_PARSER_PROMPT_NO_DEFAULT                MS_ERROR_CODE = 91015
	TMS_OBJECT_PARSER_PROMPT_HAS_DEFAULT               MS_ERROR_CODE = 91016
	TMS_OBJECT_PARSER_ENTITY_CONVERSION_FAILED         MS_ERROR_CODE = 91017
	TMS_OBJECT_PARSER_ENTITY_SAVE_FAILED               MS_ERROR_CODE = 91018
	TMS_OBJECT_PARSER_PROMPT_DELETE_LAST_ONE           MS_ERROR_CODE = 91019

	TMS_LLM_SOURCE                  MS_ERROR_CODE = 91020
	TMS_LLM_ENDPOINT                MS_ERROR_CODE = 91021
	TMS_LLM_MODEL                   MS_ERROR_CODE = 91022
	TMS_LLM_KEY                     MS_ERROR_CODE = 91023
	TMS_LLM_INIT_CLIENT             MS_ERROR_CODE = 91024
	TMS_LLM_SEND_REQUEST            MS_ERROR_CODE = 91025
	TMS_LLM_RECEIVE_RESPONSE        MS_ERROR_CODE = 91026
	TMS_LLM_READ_RESPONSE           MS_ERROR_CODE = 91027
	TMS_LLM_RECEIVE_STREAM_RESPONSE MS_ERROR_CODE = 91028
	TMS_LLM_PARSE_RESPONSE          MS_ERROR_CODE = 91029
	TMS_LLM_CONNECTION_TEST         MS_ERROR_CODE = 91030
	
	TMS_OBJECT_PARSER_PROMPT_INVALID_CATEGORY MS_ERROR_CODE = 91031
)

type ErrorCodeExplanation struct {
	explanation string
	httpCode    int
}

func (t MS_ERROR_CODE) GetHttpCode() int {
	return explanationContainer[t].httpCode
}

func (t MS_ERROR_CODE) Equal(code int32) bool {
	return code == int32(t)
}

func (t MS_ERROR_CODE) Explain() string {
	return explanationContainer[t].explanation
}

var explanationContainer = map[MS_ERROR_CODE]ErrorCodeExplanation{
	TIMS_SUCCESS: {"succeed", 200},
	TIMS_PANIC:   {"panic", 500},

	TIMS_PARAMETER_INVALID:         {"parameter is invalid", 400},
	TIMS_SQL_ERROR:                 {"failed to execute SQL", 500},
	TIMS_MARSHAL_ERROR:             {"marshal error", 500},
	TIMS_UNMARSHAL_ERROR:           {"unmarshal error", 500},
	TIMS_BATCH_SIZE_TOO_LARGE:      {"batch size too large error", 400},
	TIMS_RECORD_NOT_EXIST:          {"record not exist", 404},
	TIMS_PING_DATABASE_FAILED:      {"ping database connection failed", 500},
	TIMS_PARSE_DSN_FAILED:          {"parse dsn failed", 400},
	TIMS_SET_DATABASE_PARAM_FAILED: {"set database param failed", 400},
	TIMS_TIMEZONE_ERROR:            {"timezone error", 400},
	TIMS_INVALID_CONFIG:            {"invalid config", 400},
	TIMS_OPEN_DATABASE_FAILED:      {"open mysql failed", 500},
	TIMS_UNKNOWN_DATABASE_TYPE:     {"open mysql failed", 400},
	TIMS_NOT_ORACLE_DATASOURCE:     {"not oracle datasource", 400},
	TIMS_LICENSE_EXPIRED:           {"license expired", 401},
	TIMS_UPLOAD_CSV_FAILED:         {"upload csv failed", 400},
	TIMS_CREATE_TASK_FAILED:        {"create task failed", 400},
	TIMS_UPDATE_TASK_FAILED:        {"update task failed", 400},
	TMS_LICENSE_FEATURE_EXCEED:     {"license feature exceed", 400},

	TIMS_DATASOURCE_CONNECT_FAILED:             {"datasource connect failed", 500},
	TIMS_DATASOURCE_SAVE_CONNECT_STATUS_FAILED: {"connection status save failed", 500},
	TIMS_SERVICE_NAME_NOT_EXIST:                {"oracle service name should not empty", 400},
	TIMS_GET_FILTER_SCHEMAS_FAILED:             {"get filter schemas failed", 500},
	TMS_SETUP_RETRY_FAILED:                     {"setup database with retry failed", 400},

	//channel
	TIMS_EXIST_CHECKING_STATUS_PRECHECK: {"exist checking status pre-check item", 400},
	TIMS_TASK_EXCUTION_FAILED:           {"starting to execute task failed.", 400},
	TIMS_UNKOWN_TASK_TYPE:               {"task type unknown", 400},
	TIMS_NO_UN_SELECTED_TABLES:          {"no un-selected tables", 400},
	TIMS_NO_COMPARE_TABLES:              {"no compare tables", 400},
	TIMS_UNKNOWN_REFERENCE_TABLES_TYPE:  {"reference tables type unknown", 400},
	TIMS_CONFLICT_TABLES:                {"tables conflict with tables in other tasks on same channel", 400},
	TIMS_SPLIT_TASK_FAILED:              {"tables has diff channel_table_tables", 400},
	TIMS_TASK_NOT_FOUND:                 {"task not found", 400},

	//template
	TIMS_EXIST_DEFAULT_TEMPLATE: {"exist default template", 400},

	// sqlanalyze
	TMS_SQLANALYZE_BUILD_PARAM_FAILED:         {"build parameter failed", 400},
	TMS_SQLANALYZE_INIT_PARAM_FAILED:          {"init parameter failed", 400},
	TMS_SQLANALYZE_GET_ORACLE_SQL_FAILED:      {"get oracle sqls failed", 500},
	TMS_SQLANALYZE_EXPLAIN_SQL_IN_TIDB_FAILED: {"explain sql in tidb failed", 500},
	TMS_FETCH_STATEMENT_FAILED:                {"fetch statement failed", 400},
	TMS_UNKOWN_REPORT_TYPE:                    {"unknown report type", 400},

	// migration
	TMS_MIGRATION_CHUNK_NOT_FOUND:               {"migration chunk not found", 400},
	TMS_MIGRATION_CHUNK_ANALYZE_EXIST:           {"chunk analyze data already exist", 400},
	TMS_MIGRATION_CHUNK_ANALYZE_NOT_FOUND:       {"chunk analyze data not found", 400},
	TMS_MIGRATION_LIGHTNING_INVALID:             {"lightning invalid", 400},
	TMS_MIGRATION_CSV_WALK_DIR_FAILED:           {"walk csv dir failed", 400},
	TMS_MIGRATION_CSV_FILE_NUM_NOT_MATCH:        {"csv file num not match", 400},
	TMS_MIGRATION_CSV_FILE_EXTRACT_TABLE_FAILED: {"extract table from csv file name failed", 400},
	TMS_MIGRATION_CSV_METADATA_EXIST:            {"csv metadata already exist", 400},
	TMS_MIGRATION_CSV_TABLE_NOT_MATCH:           {"csv table not match", 400},

	// column data management
	TMS_UPDATE_COLUMN_DIGEST_MISSED:       {"column digest is required", 400},
	TMS_UPDATE_COLUMN_DIGEST_NOT_EQUAL:    {"column digest is not equals", 400},
	TMS_UPDATE_COLUMN_DIGEST_NOT_FOUND:    {"column digest not found in db", 400},
	TMS_COLUMN_DATA_CSV_OPEN_FILE_FAILED:  {"open csv file failed", 400},
	TMS_COLUMN_DATA_CSV_HEADER_NOT_MATCH:  {"csv header not match", 400},
	TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED: {"csv data parse failed", 400},
	TMS_COLUMN_DATA_CSV_DATA_INVALID:      {"csv data invalid", 400},

	// increment - dsg
	TMS_DSG_ADAPTOR_NOT_EXIST:                {"dsg adaptor is not exist", 400},
	TMS_DSG_INCREMENT_ENVIRONMENT_NOT_READY:  {"increment environment not ready", 400},
	TMS_DSG_INCREMENT_TASK_NOT_FOUND:         {"increment task not found", 400},
	TMS_DSG_CREATE_PHYSICAL_SUBSYSTEM_FAILED: {"create physical subsystem failed", 400},
	TMS_DSG_CREATE_DATASOURCE_FAILED:         {"create datasource failed", 400},
	TMS_DSG_GET_DATASOURCE_FAILED:            {"get datasource failed", 400},
	TMS_DSG_DATASOURCE_NOT_ENABLE_INCREMENT:  {"tms datasource not enable increment", 400},
	TMS_DSG_DATASOURCE_HAS_NO_LOG_STORE_MODE: {"tms datasource has no log store mode", 400},
	TMS_DSG_HOST_NOT_EXIST:                   {"dsg host not found", 400},
	TMS_DSG_CREATE_SYNC_INFO_FAILED:          {"create dsg sync info failed", 400},
	TMS_DSG_VALIDATE_DATASOURCE_FAILED:       {"validate datasource failed", 500},
	TMS_DSG_UPDATE_ASM_DATASOURCE_FAILED:     {"update asm datasource failed", 500},
	TMS_DSG_SOURCE_IP_NOT_MATCH:              {"When the datasource log storage mode is set to filesystem, the deployment IP must match the datasource IP", 400},

	TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST:                {"object parser adaptor is not exist", 400},
	TMS_OBJECT_PARSER_GET_DEPENDENCY_FAILED:            {"get dependency failed", 400},
	TMS_OBJECT_PARSER_GET_PACKAGE_BODY_FAILED:          {"get package body failed", 400},
	TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED:           {"get definition failed", 400},
	TMS_OBJECT_PARSER_MAP_SOURCE_CODE_TO_DETAIL_FAILED: {"map source code to detail failed", 400},
	TMS_OBJECT_PARSER_DUPLICATE_UUID:                   {"duplicate uuid", 400},
	TMS_OBJECT_PARSER_PARSE_OBJECT_FAILED:              {"parse object sql failed", 500},
	TMS_OBJECT_PARSER_INVALID_OBJECT:                   {"invalid object", 500},
	TMS_OBJECT_PARSER_PROMPT_NOT_FOUND:                 {"prompt not found", 400},
	TMS_OBJECT_PARSER_PROMPT_SAVE_FAILED:               {"prompt save failed", 400},
	TMS_OBJECT_PARSER_PROMPT_RELATION_SAVE_FAILED:      {"prompt relation save failed", 400},
	TMS_OBJECT_PARSER_PROMPT_RELATION_DELETE_FAILED:    {"prompt relation delete failed", 400},
	TMS_OBJECT_PARSER_FEATURE_UPDATE_FAILED:            {"update feature failed", 400},
	TMS_OBJECT_PARSER_BUILD_PARAM_FAILED:               {"build param failed", 400},
	TMS_OBJECT_PARSER_PROMPT_DELETE_FAILED:             {"prompt delete failed", 400},
	TMS_OBJECT_PARSER_PROMPT_NO_DEFAULT:                {"no default prompt", 400},
	TMS_OBJECT_PARSER_PROMPT_HAS_DEFAULT:               {"default prompt already exist", 400},
	TMS_OBJECT_PARSER_ENTITY_CONVERSION_FAILED:         {"entity conversion failed", 400},
	TMS_OBJECT_PARSER_ENTITY_SAVE_FAILED:               {"entity save failed", 400},
	TMS_OBJECT_PARSER_PROMPT_DELETE_LAST_ONE:           {"cannot delete the last prompt for this combination", 400},

	TMS_LLM_SOURCE:                  {"llm source error", 400},
	TMS_LLM_ENDPOINT:                {"llm provider api endpoint error", 400},
	TMS_LLM_MODEL:                   {"llm provider model error", 400},
	TMS_LLM_KEY:                     {"llm provider api key error", 400},
	TMS_LLM_INIT_CLIENT:             {"llm provider api init client error", 400},
	TMS_LLM_SEND_REQUEST:            {"llm send request error", 400},
	TMS_LLM_RECEIVE_RESPONSE:        {"llm receive response error", 400},
	TMS_LLM_READ_RESPONSE:           {"llm read response error", 400},
	TMS_LLM_RECEIVE_STREAM_RESPONSE: {"llm receive stream response error", 400},
	TMS_LLM_PARSE_RESPONSE:          {"llm parse response error", 400},
	TMS_LLM_CONNECTION_TEST:         {"llm connection test error", 400},
	TMS_OBJECT_PARSER_PROMPT_INVALID_CATEGORY: {"invalid prompt category or configuration", 400},
}
