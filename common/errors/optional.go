package errors

type Optional struct {
	last      error
	allErrors []error
	broken    bool
}

func OfNullable(err error) *Optional {
	optional := &Optional{
		broken: err != nil,
	}
	optional.addError(err)
	return optional
}

func (p *Optional) addError(err error) {
	if err == nil {
		return
	}
	p.last = err
	if len(p.allErrors) == 0 {
		p.allErrors = make([]error, 0)
	}

	p.allErrors = append(p.allErrors, err)
}

func (p *Optional) BreakIf(executor func() error) *Optional {
	if p.broken {
		return p
	}
	if err := executor(); err != nil {
		p.broken = true
		p.addError(err)
	}

	return p
}

func (p *Optional) ContinueIf(executor func() error) *Optional {
	if p.broken {
		return p
	}
	if err := executor(); err != nil {
		p.addError(err)
	}
	return p
}

func (p *Optional) If(handle func(err error)) *Optional {
	if p.last != nil && handle != nil {
		handle(p.last)
	}
	return p
}

func (p *Optional) Map(handle func(err error) error) *Optional {
	if p.last != nil && handle != nil {
		p.addError(handle(p.last))
	}
	return p
}

func (p *Optional) Else(handle func()) *Optional {
	if p.last == nil && handle != nil {
		handle()
	}
	return p
}

func (p *Optional) IfElse(errHandler func(err error), nilHandler func()) *Optional {
	return p.If(errHandler).Else(nilHandler)
}

func (p *Optional) Present() error {
	return p.last
}

func (p *Optional) IfPresent() bool {
	return p.last != nil
}

func (p *Optional) IfNil() bool {
	return p.last == nil
}
