package errors

import (
	"fmt"
)

// MSError
// @Description: MS business error
// Always get MSError from MSErrorBuilder.build(), limited to Error, NewMSError, NewErrorf, WrapError, ErrorBuilder().build()
type MSError struct {
	code  MS_ERROR_CODE
	msg   string
	cause error
}

func Error(code MS_ERROR_CODE) MSError {
	return ErrorBuilder().Code(code).Build()
}

func NewError(code MS_ERROR_CODE, msg string) MSError {
	return ErrorBuilder().Code(code).Message(msg).Build()
}

func NewErrorf(code MS_ERROR_CODE, format string, a ...interface{}) MSError {
	return ErrorBuilder().Code(code).Message(fmt.Sprintf(format, a...)).Build()
}

func WrapError(code MS_ERROR_CODE, msg string, err error) MSError {
	return ErrorBuilder().Code(code).Message(msg).Cause(err).Build()
}

func (e MSError) Is(err error) bool {
	if e == err {
		return true
	}

	if e.cause == nil {
		return false
	}

	if cause, ok := e.cause.(MSError); ok {
		return cause.Is(err)
	} else {
		return e.cause == err
	}

}

func (e MSError) Error() string {
	errInfo := fmt.Sprintf("[%d]%s", e.GetCode(), e.GetCodeText())
	if len(e.msg) > 0 {
		errInfo = fmt.Sprintf("%s, %s", errInfo, e.msg)
	}

	if e.cause != nil {
		errInfo = fmt.Sprintf("%s, cause: %s", errInfo, e.cause.Error())
	}
	return errInfo
}

type EMErrorBuilder struct {
	template MSError
}

// ErrorBuilder
// @return EMErrorBuilder
func ErrorBuilder() EMErrorBuilder {
	return EMErrorBuilder{
		template: MSError{},
	}
}

func (t EMErrorBuilder) Code(code MS_ERROR_CODE) EMErrorBuilder {
	t.template.code = code
	return t
}

func (t EMErrorBuilder) Message(msg string) EMErrorBuilder {
	t.template.msg = msg
	return t
}

func (t EMErrorBuilder) Format(format string, args ...interface{}) EMErrorBuilder {
	t.template.msg = fmt.Sprintf(format, args...)
	return t
}

func (t EMErrorBuilder) Cause(cause error) EMErrorBuilder {
	t.template.cause = cause
	return t
}

// Build
// @Description: the only way to get a efficient MSError
// @Receiver t
// @return MSError
func (t EMErrorBuilder) Build() MSError {
	return t.template
}

func (e MSError) GetCode() MS_ERROR_CODE {
	return e.code
}

func (e MSError) GetMsg() string {
	if len(e.msg) == 0 {
		return e.GetCodeText()
	}
	return e.msg
}

func (e MSError) GetCodeText() string {
	return e.code.Explain()
}

func (e MSError) GetCause() error {
	return e.cause
}

func (e MSError) Unwrap() error {
	return e.cause
}
