package constants

const (
	//1 not setup,2 not running,3 running,4 finish,5 failed
	TASK_STATUS_NOT_SETUP   = 1
	TASK_STATUS_NOT_RUNNING = 2
	TASK_STATUS_RUNNING     = 3
	TASK_STATUS_FINISH      = 4
	TASK_STATUS_FAILED      = 5
	TASK_STATUS_QUEUED      = 6
	TASK_STATUS_STOP        = 7
)

var TASK_STATUS_CAN_EXECUTE_ARR = []int{TASK_STATUS_NOT_RUNNING, TASK_STATUS_FINISH, TASK_STATUS_FAILED, TASK_STATUS_STOP}

const (
	TASK_TYPE_CHECKED    = "Y"
	TASK_TYPE_UN_CHECKED = "N"
)

const (
	TASK_TYPE_OBJ_ASSESSMENT                  = 1
	TASK_TYPE_MIGRATE_STRUCTURE               = 2
	TASK_TYPE_MIGRATE_FULL_DATA               = 3
	TASK_TYPE_DATA_COMPARE                    = 4
	TASK_TYPE_SQL_ASSESSMENT                  = 5
	TASK_TYPE_STATISTICS                      = 6
	TASK_TYPE_MIGRATE_CSV_DATA                = 7
	TASK_TYPE_INCREMENT                       = 8
	TASK_TYPE_OBJ_PARSER                      = 9
	TASK_TYPE_FULL_INITIALIZATION             = 10
	TASK_TYPE_MIGRATE_FULL_AND_INCREMENT_DATA = 11
)

const (
	ONLY_INCOMPATIBLE_DETAIL_DEFAULT = 0
)

const (
	TASK_MIGRATION_STRUCTURE_DDL_NOT_RUNNING           = 1
	TASK_MIGRATION_STRUCTURE_DDL_RUNNING               = 2
	TASK_MIGRATION_STRUCTURE_DDL_SUCCESS               = 3
	TASK_MIGRATION_STRUCTURE_DDL_FAILED                = 4
	TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_INCOMPATIBLE = 5
	TASK_MIGRATION_STRUCTURE_DDL_FUNCTION_COMPATIBLE   = 6
	TASK_MIGRATION_STRUCTURE_DDL_PARTIAL_SUCCESS       = 7
)

const (
	TIME_FORMAT = "2006-01-02 15:04:05"
)

const (
	MAX_LOG_SIZE              = 200
	DEFAULT_CREATE_BATCH_SIZE = 200
)

const (
	REFERENCE_TABLE_TYPE_CHANNEL = "channel"
	REFERENCE_TABLE_TYPE_TASKS   = "tasks"
)

const (
	TASK_WARNING_TYPE_CONFLICT     = "C"
	TASK_WARNING_TYPE_EXCEPTION    = "Y"
	TASK_WARNING_TYPE_NO_EXCEPTION = "N"
)
