package constants

const (
	DC_PARAM_SOURCE_SNAPSHOT                  = "source-snapshot"
	DC_PARAM_TARGET_SNAPSHOT                  = "target-snapshot"
	DC_PARAM_ORACLE_SCN                       = "oracle-scn"
	DC_PARAM_SOURCE_CHARSET                   = "source-charset"
	DC_PARAM_TARGET_CHARSET                   = "target-charset"
	DC_PARAM_CHUNK_SIZE                       = "chunk-size"
	DC_PARAM_CHECK_THREAD_COUNT               = "check-thread-count"
	DC_PARAM_USE_CHECKSUM                     = "use-checksum"
	DC_PARAM_ONLY_USE_CHECKSUM                = "only-use-checksum"
	DC_PARAM_FIX_SQL_DIR                      = "fix-sql-dir"
	DC_PARAM_FIX_TARGET                       = "fix-target"
	DC_PARAM_EMPTY_STRING_NULL_COMPARE_SWITCH = "empty-string-null-compare-switch"
	DC_PARAM_ASCII_0_SWITCH                   = "ascii-0-switch"
	DC_PARAM_IGNORE_STRUCT_CHECK              = "ignore-struct-check"
	DC_PARAM_IGNORE_STATS                     = "ignore-stats"
	DC_PARAM_IGNORE_DATA_CHECK                = "ignore-data-check"
	DC_PARAM_USE_CHECKPOINT                   = "use-checkpoint"
	DC_FLOAT_TRUNC_PRECISION                  = "float-trunc-precision"
	DC_DOUBLE_TRUNC_PRECISION                 = "double-trunc-precision"
	DC_ORACLE_HOME                            = "oracle-home"
	DC_LOW_CASE_SCHEMA_NAME                   = "low-case-schema-name"
	DC_LOW_CASE_TABLE_NAME                    = "low-case-table-name"
	DC_COMPRESS_MODE                          = "compress-mode"
	DC_PARAM_MISSING_TABLES_THRESHOLD         = "missing-tables-threshold"
	DC_PARAM_TABLE_CHUNK_FAIL_THRESHOLD       = "table-chunk-fail-threshold"
	DC_PARAM_OVERALL_TABLE_ERROR_THRESHOLD    = "overall-table-error-threshold"
	DC_PARAM_TABLE_CHUNK_FAIL_COUNT           = "table-chunk-fail-count"
)

const (
	DataCompareCreateLibrary                 = "创建数据校验库文件"
	DataCompareCreateModuleLibFunction       = "创建数据校验模块库函数"
	DataCompareCreateCompareFunction         = "创建数据校验函数"
	DataCompareCreateCompressCompareFunction = "创建数据压缩校验函数"
	DataCompareInvokeCompareFunction         = "验证数据校验"
	DataCompareInvokeCompressCompareFunction = "验证数据压缩校验"
)

const FIX_SQL_PATH = "fix-sql"

const (
	ChunkSuccessState    = "success"
	ChunkFailedState     = "failed"
	ChunkErrorState      = "error"
	ChunkNotCheckedState = "not_checked"
	ChunkCheckingState   = "checking"
	ChunkIgnoreState     = "ignore"
)
