package constants

const (
	PRE_CHECK_STATUS_CHECKING string = "C"
	PRE_CHECK_STATUS_PASS     string = "P"
	PRE_CHECK_STATUS_NOT_PASS string = "N"
)

const (
	PRE_CHECK_OBJECT_COMPATIBLE_PRIORITY string = "A"
	PRE_CHECK_OBJECT_MIGRATION_PRIORITY  string = "B"
	PRE_CHECK_DATA_MIGRATION_PRIORITY    string = "C"
	PRE_CHECK_DATA_COMPARE_PRIORITY      string = "D"
	PRE_CHECK_NO_INDEX_TABLE             string = "E"
	PRE_CHECK_NO_PK_UK_TABLE             string = "F"
	PRE_CHECK_DEPENDENCY_RELATION        string = "G"
)

const (
	CHANNEL_TYPE_O2T = "O2T"
	CHANNEL_TYPE_T2T = "T2T"
	CHANNEL_TYPE_M2T = "M2T"
)

const (
	ColumnDataStatusNoData   = "NODATA"
	ColumnDataStatusFetching = "FETCHING"
	ColumnDataStatusFetched  = "FETCHED"
	ColumnDataStatusFailed   = "FAILED"
)

type FilterQueryType string

const (
	FilterSchemaNames   FilterQueryType = "SCHEMA_NAMES"
	FilterTableName     FilterQueryType = "TABLE_NAME"
	FilterColumnName    FilterQueryType = "COLUMN_NAME"
	FilterDataType      FilterQueryType = "DATA_TYPE"
	FilterDataDefault   FilterQueryType = "DATA_DEFAULT"
	FilterDataLength    FilterQueryType = "DATA_LENGTH"
	FilterDataPrecision FilterQueryType = "DATA_PRECISION"
	FilterDataScale     FilterQueryType = "DATA_SCALE"
	FilterNullable      FilterQueryType = "NULLABLE"
	FilterColumnComment FilterQueryType = "COLUMN_COMMENT"
)
