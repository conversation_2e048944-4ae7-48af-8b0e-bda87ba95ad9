package constants

const OPLogType string = "T08"

const OPStepInit string = "001"
const OPStepFetchingObjectData string = "002"
const OPStepSavingObjectData string = "003"
const OPStepParsePackageBody string = "004"
const OPStepSavingMetaData string = "005"
const OPStepParseAST string = "006"
const OPStepUpdateMetadata string = "009"

const IncompatibleFeatureTypeDatabaseLink = "DATABASE_LINK"
const IncompatibleFeatureTypeReservedWord = "RESERVED_WORD"

var ValidObjectTypes = []string{
	OracleObjectTypePackageBody,
	OracleObjectTypeProcedure,
	OracleObjectTypeFunction,
	OracleObjectTypeTrigger,
}

const OracleObjectTypePackageBody = "PACKAGE BODY"
const OracleObjectTypePackage = "PACKAGE"
const OracleObjectTypeProcedure = "PROCEDURE"
const OracleObjectTypeFunction = "FUNCTION"
const OracleObjectTypeTrigger = "TRIGGER"
const OracleObjectTypeTable = "TABLE"
const OracleObjectTypeView = "VIEW"
const OracleObjectTypeIndex = "INDEX"
const OracleObjectTypeSequence = "SEQUENCE"
const OracleObjectTypeType = "TYPE"

const OPTargetLanguageJava = "Java"
const OPTargetLanguageGo = "Go"
const OPTargetLanguagePython = "Python"

const OPTargetFrameworkHibernate string = "hibernate"
const OPTargetFrameworkMyBatis string = "mybatis"
const OPTargetFrameworkMyBatisPlus string = "mybatis-plus"
const OPTargetFrameworkJava string = "java"

var OPTargetFrameworkPriorities = []string{
	OPTargetFrameworkHibernate,
	OPTargetFrameworkMyBatis,
	OPTargetFrameworkMyBatisPlus,
	OPTargetFrameworkJava,
}
