/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package constants

const (
	ParamsMigrationStructTableParallel       = "migration_table_parallel"
	ParamsMigrationStructIndexParallel       = "migration_index_parallel"
	ParamsMigrationStructLowerCaseFieldNames = "lower_case_field_names" // 1为统一小写;2为统一大写
	ParamsMigrationStructLowerCaseTableNames = "lower_case_table_names" // 1为统一小写;2为统一大写
	ParamsMigrationStructSyncMetaBatchSize   = "sync_meta_batch_size"
	ParamsMigrationStructSyncMetaSQLHint     = "sync_meta_sql_hint"
	ParamsMigrationStructPunctReplacement    = "punct_replacement"
	ParamsMigrationStructCreatePkWithTable   = "create_pk_with_table"
	ParamsMigrationStructRunVersion          = "run_version"

	ParamsFullMigrationChunkSize           = "chunk_size"
	ParamsFullMigrationInsertBatchSize     = "insert_batch_size"
	ParamsFullMigrationTaskThreads         = "task_threads"
	ParamsFullMigrationTableThreads        = "table_threads"
	ParamsFullMigrationSqlThreads          = "sql_threads"
	ParamsFullMigrationApplyThreads        = "apply_threads"
	ParamsFullMigrationTaskCountPerTable   = "task_count_per_table"
	ParamsFullMigrationCallTimeout         = "call_timeout"
	ParamsFullMigrationConsistentRead      = "consistent_read"
	ParamsFullMigrationSQLMigrateStatement = "sql_migrate_statement"
	ParamsFullMigrationSQLRetryStatement   = "sql_retry_statement"
	ParamsFullRemoveSplitTask              = "remove_split_task"

	// for csv
	ParamsCsvHeader            = "csv.header"
	ParamsCsvChunkSize         = "csv.chunk_size"
	ParamsCsvEscapeBackslash   = "csv.escape_backslash"
	ParamsCsvTerminator        = "csv.terminator"
	ParamsCsvTaskCountPerTable = "csv.task_count_per_table"
	ParamsCsvInsertBatchSize   = "csv.insert_batch_size"
	ParamsCsvDelimiter         = "csv.delimiter"
	ParamsCsvCharset           = "csv.charset"
	ParamsCsvSeparator         = "csv.separator"
	ParamsCsvSqlThreads        = "csv.sql_threads"
	ParamsCsvTableThreads      = "csv.table_threads"
	ParamsCsvTaskThreads       = "csv.task_threads"
	ParamsCsvOutputDir         = "csv.output_dir"
	ParamsCsvSortedKVDir       = "csv.sorted_kv_dir"
	ParamsCsvCallTimeout       = "csv.call_timeout"
	ParamsCsvConsistentRead    = "csv.consistent_read"
	ParamsCsvRemoveSplitTask   = "csv.remove_split_task"

	// for csv + lightning
	ParamsCsvRemoveCSV           = "csv.remove_csv"
	ParamsCsvLightningBinaryPath = "csv.lightning_binary_path"
	ParamsCsvBatchImportSize     = "csv.batch_import_size"

	ParamsCsvLightningMydumperCsvTrimLastSeparator   = "mydumper.csv.trim-last-separator"
	ParamsCsvLightningMydumperCsvNull                = "mydumper.csv.null"
	ParamsCsvLightningMydumperCsvNotNull             = "mydumper.csv.not-null"
	ParamsCsvLightningMydumperCsvHeaderSchemaMatch   = "mydumper.csv.header-schema-match"
	ParamsCsvLightningMydumperFilter                 = "mydumper.filter"
	ParamsCsvLightningMydumperStrictFormat           = "mydumper.strict-format"
	ParamsCsvLightningMydumperDataInvalidCharReplace = "mydumper.data-invalid-char-replace"
	ParamsCsvLightningMydumperCharacterSet           = "mydumper.character-set"
	ParamsCsvLightningMydumperBatchImportRatio       = "mydumper.batch-import-ratio"
	ParamsCsvLightningMydumperReadBlockSize          = "mydumper.read-block-size"
	ParamsCsvPostRestoreAnalyze                      = "post-restore.analyze"
	ParamsCsvPostRestoreChecksum                     = "post-restore.checksum"
	ParamsCsvTiDBLogLevel                            = "tidb.log-level"
	ParamsCsvTiDBPdAddr                              = "tidb.pd-addr"
	ParamsCsvTiDBStatusPort                          = "tidb.status-port"
	ParamsCsvTiKVImporterDiskQuota                   = "tikv-importer.disk-quota"
	ParamsCsvTiKVImporterIncrementalImport           = "tikv-importer.incremental-import"
	ParamsCsvTiKVImporterDuplicateResolution         = "tikv-importer.duplicate-resolution"
	ParamsCsvTiKVImporterBackend                     = "tikv-importer.backend"
	ParamsCsvTiKVImporterOnDuplicate                 = "tikv-importer.on-duplicate"
	ParamsCsvTiKVImporterStoreWriteBWLimit           = "tikv-importer.store-write-bwlimit"
	ParamsCsvTiKVImporterRangeConcurrency            = "tikv-importer.range-concurrency"
	ParamsCsvCheckpointDriver                        = "checkpoint.driver"
	ParamsCsvCheckpointEnable                        = "checkpoint.enable"
	ParamsCsvLightningLevel                          = "lightning.level"
	ParamsCsvLightningCheckRequirements              = "lightning.check-requirements"
	ParamsCsvLightningIndexConcurrency               = "lightning.index-concurrency"
	ParamsCsvLightningTableConcurrency               = "lightning.table-concurrency"
	ParamsCsvLightningMetaSchemaName                 = "lightning.meta-schema-name"
	ParamsCsvCronSwitchMode                          = "cron.switch-mode"

	// for object parser
	ParamsObjectParserOpenAISource   = "openai_source"
	ParamsObjectParserOpenAIModel    = "openai_model"
	ParamsObjectParserOpenAIEndpoint = "openai_endpoint"
	ParamsObjectParserOpenAIKey      = "openai_key"

	ParamsObjectParserLLMSource        = "llm_source"
	ParamsObjectParserLLMModel         = "llm_model"
	ParamsObjectParserLLMEndpoint      = "llm_endpoint"
	ParamsObjectParserLLMAPIKey        = "llm_api_key"
	ParamsObjectParserLLMMaxTokens     = "llm_max_tokens"
	ParamsObjectParserLLMTemperature   = "llm_temperature"
	ParamsObjectParserLLMStream        = "llm_stream"
	ParamsObjectParserLLMTimeout       = "llm_timeout"
	ParamsObjectParserLLMMaxTokensName = "llm_max_tokens_name"

	ParamsObjectParserReservedWordScoringMode    = "reserved_word_scoring_mode"
	ParamsObjectParserDatabaseLinkScoringMode    = "database_link_scoring_mode"
	ParamsObjectParserHistogramScoreWeightFactor = "histogram_score_weight_factor"
	ParamsObjectParserConvertJavaThread          = "convert_java_thread"
	ParamsObjectParserAnalyzeWorkerPoolSize      = "analyze_worker_pool_size"
	ParamsObjectParserAnalyzeBatchSize           = "analyze_batch_size"

	// for sql analyze
	ParamsSQLAnalyzeSQLSetName             = "sqlset_name"
	ParamsSQLAnalyzeSQLRowLimit            = "sql_row_limit"
	ParamsSQLAnalyzeAnalyzeThread          = "analyze_thread"
	ParamsSQLAnalyzeExcludeSQLText         = "exclude_sqltxt"
	ParamsSQLAnalyzeExcludeSQLId           = "exclude_sqlid"
	ParamsSQLAnalyzeExcludeMode            = "exclude_module"
	ParamsSQLAnalyzeIncludingSchema        = "including_schema"
	ParamsSQLAnalyzeIncludingSQLType       = "including_sqltype"
	ParamsSQLAnalyzeExplainTimeout         = "explain_timeout"
	ParamsSQLAnalyzeAnalyzeTimeout         = "analyze_timeout"
	ParamsSQLAnalyzeAppSQLsSource          = "app_sqls_source"
	ParamsSQLAnalyzeAppSQLsFilename        = "app_sqls_filename"
	ParamsSQLAnalyzeIgnoreSQLIdsFilename   = "ignore_sqlids_filename"
	ParamsSQLAnalyzeExplainOnly            = "explain_only"
	ParamsSQLAnalyzeSQLOrderBy             = "sql_orderby"
	ParamsSQLAnalyzeTiDBExecStatusFilter   = "tidb_exec_status_filter"
	ParamsSQLAnalyzeTopNPerStatus          = "topn_per_status"
	ParamsSQLAnalyzeRunVersion             = "run_version"
	ParamsSQLAnalyzeEnableOpenAISuggestion = "enable_openai_suggestion"
	ParamsSQLAnalyzeEnableSQLRewrite       = "enable_sql_rewrite"
	ParamsSQLAnalyzeOpenAISource           = "openai_source"
	ParamsSQLAnalyzeOpenAIModel            = "openai_model"
	ParamsSQLAnalyzeOpenAIEndpoint         = "openai_endpoint"
	ParamsSQLAnalyzeOpenAIKey              = "openai_key"
)

const (
	ParamsMigrationStructLowerCaseTableNamesLower = "1"
	ParamsMigrationStructLowerCaseTableNamesUpper = "2"
)
