package constants

const (
	ServerPort int    = 8080
	DataDir    string = "./data"
)

// TiMS log-related constants
const (
	LogFileSql    string = "sql"
	LogFileAccess string = "access"
	LogFileAudit  string = "audit"
	LogDirPrefix  string = "/logs/"
)
const (
	// OutputConsole log console output
	OutputConsole = "console"
	// OutputFile log file output
	OutputFile = "file"
)

// TiMS database constants
const (
	DBDirPrefix      string = "/"
	DatabaseFileName string = "ms.db"
)

// Server role type
const (
	WEB_ROLE    string = "web"
	WORKER_ROLE string = "worker"
)

// Mode type
const (
	CLUSTER_MODE string = "cluster"
	SINGLE_MODE  string = "single"
)

const (
	APP_NAME string = "tims-server"
)

const (
	SET_TMS_MODULE_SQL = `call DBMS_APPLICATION_INFO.SET_MODULE('TMS','TMS Server')`
)

const (
	TMS_MD5_SALT = "!TMS!@#"
)
