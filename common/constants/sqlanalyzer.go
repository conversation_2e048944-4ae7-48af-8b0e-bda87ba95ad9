package constants

import "fmt"

const FakeParamValue = "FAKE_PARAM_VALUE"

type SQLAnalyzeTaskMode string
type SQLAnalyzeTaskStatus int

type SQLSetStatus int

// 定义四种枚举值
const (
	SQLSetEmpty       SQLSetStatus = iota // 空状态
	SQLSetBreakpoint                      // 三个属性都不变,断点回放
	SQLSetIncremental                     // LAST_MODIFIED 或 STATEMENT_COUNT 变化，表示有增量
	SQLSetFullReset                       // 清空数据，从头开始（CREATED 变化）
)

// 可选：添加 String 方法，便于调试和输出
func (s SQLSetStatus) String() string {
	switch s {
	case SQLSetEmpty:
		return "Empty"
	case SQLSetBreakpoint:
		return "Breakpoint"
	case SQLSetIncremental:
		return "Incremental"
	case SQLSetFullReset:
		return "FullReset"
	default:
		return fmt.Sprintf("Unknown(%d)", s)
	}
}

func (i SQLAnalyzeTaskMode) String() string {
	return string(i)
}

func (i SQLAnalyzeTaskStatus) String() string {
	switch i {
	case SQLAnalyzeTaskStatusSuccess:
		return "成功"
	case SQLAnalyzeTaskStatusFailed:
		return "失败"
	case SQLAnalyzeTaskStatusRunning:
		return "运行中"
	case SQLAnalyzeTaskStatusNotRunning:
		return "未运行"
	default:
		return "UNKNOWN"
	}
}

const ExecuteRunModeRun = 1
const ExecuteRunModeReRun = 2

const TiDBExplainSuccess = "success"
const TiDBExplainFailed = "failed"
const TiDBExplainTimeout = "timeout"
const TiDBExplainExcluded = "excluded"

const SQLAnalyzeTaskModeLocalInt = 0
const SQLAnalyzeTaskModeNotLocalInt = 1

const SQLAnalyzeTaskModeLocal SQLAnalyzeTaskMode = "LOCAL"
const SQLAnalyzeTaskModeNotLocal SQLAnalyzeTaskMode = "非LOCAL"

const SQLAnalyzeTaskStatusSuccess SQLAnalyzeTaskStatus = 4
const SQLAnalyzeTaskStatusFailed SQLAnalyzeTaskStatus = 5
const SQLAnalyzeTaskStatusRunning SQLAnalyzeTaskStatus = 3
const SQLAnalyzeTaskStatusNotRunning SQLAnalyzeTaskStatus = 2

const SQLAnalyzeTaskIgnore = "Y"
const SQLAnalyzeTaskNotIgnore = "N"

// User operation status for SQL execution results
const UserOperateStatusNormal = "normal"
const UserOperateStatusIgnored = "ignored"
const UserOperateStatusResolved = "resolved"

// SQL兼容性相关
const SQLAnalyzeCheckSourceDataAccessPermission = "检查访问源数据权限"
const SQLAnalyzeCreateSQLSet = "创建SQLSet"
const SQLAnalyzeCreateGetCurrentSQLProgram = "创建获取当前SQL程序"
const SQLAnalyzeCreateGetHistorySQLProgram = "创建获取历史SQL程序"
const SQLAnalyzeCreateAutoGetCurrentSQLJob = "创建自动获取当前SQL采集作业"
const SQLAnalyzeCreateAutoGetHistorySQLJob = "创建自动获取历史SQL采集作业"
const SQLAnalyzeCreateUserFunction = "创建自定义函数"
const SQLAnalyzeCreateDataDictionary = "创建TMS数据字典表"

const SQLAnalyzeCheckTargetDataAccessPermission = "检查访问目标数据权限"

const DeployModeSQL = "sql"             // SQL可能会有多条，按\n分隔以后单独执行
const DeployModeSQLs = "sqls"           // SQL可能会有多条，按;分隔以后单独执行
const DeployModeCheckSQL = "check-sql"  // taskSQL走check逻辑
const DeployModeProcedure = "procedure" // SQL直接执行，不分隔

const UsernameDefaultCase = 0
const UsernameUpperCase = 1
const UsernameLowerCase = 2

var OracleCommandTypeMapping = map[string]int{
	"CREATE TABLE":   1,
	"INSERT":         2,
	"SELECT":         3,
	"DELETE":         6,
	"UPDATE":         7,
	"CREATE INDEX":   9,
	"ALTER INDEX":    11,
	"LOCK TABLE":     26,
	"COMMIT":         44,
	"ROLLBACK":       45,
	"SAVEPOINT":      46,
	"PL/SQL EXECUTE": 47,
	"EXPLAIN":        50,
	"CALL":           170,
}

const SQLAnalyzeTaskLogType = "T05"

const SQLAnalyzeTaskLogStepExplainStart = "001"
const SQLAnalyzeTaskLogStepExplainPreparing = "002"
const SQLAnalyzeTaskLogStepExplainRunning = "003"
const SQLAnalyzeTaskLogStepExplainFinish = "004"
const SQLAnalyzeTaskLogStepExplainFailed = "005"
const SQLAnalyzeTaskLogStepExplainOperation = "006"
