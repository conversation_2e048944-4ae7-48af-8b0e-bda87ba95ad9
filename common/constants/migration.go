package constants

type MigrationType int

const MigrationTypeFullData MigrationType = 1
const MigrationTypeCSV MigrationType = 2
const MigrationTypeLightning MigrationType = 3

const MigrationLogTypeFullData string = "T03"
const MigrationLogTypeCSV string = "T07"

const MigrationStepMigrateTableBegin string = "001"       // XX表数据迁移开始
const MigrationStepMigrateTableChunkBegin string = "002"  // XX表Chunk数据迁移开始
const MigrationStepMigrateTableRunning string = "003"     // XX表Chunk数据迁移开始
const MigrationStepMigrateTableChunkFinish string = "004" // XX表Chunk数据迁移结束
const MigrationStepMigrateTableFinish string = "005"      // XX表数据迁移结束
const MigrationStepMigrateTableSkip string = "006"        // XX库没有chunk，跳过

const MigrationStepLightningImportBegin string = "007"
const MigrationStepLightningImportRunning string = "008"
const MigrationStepLightningImportFinish string = "009"
const MigrationStepLightningImportSkip string = "010"
const MigrationStepLightningImportFailed string = "011"

func BuildProgressLog(logType, stepNum, logMessage string) string {
	return "[" + logType + "-" + stepNum + "]: " + logMessage
}

const ChunkDataStatusWaiting = "WAITING"
const ChunkDataStatusFetching = "FETCHING"
const ChunkDataStatusFetched = "FETCHED"
const ChunkDataStatusReplaying = "REPLAYING"
const ChunkDataStatusReplayed = "REPLAYED"

const MigrationStatusInvalid = "INVALID"
