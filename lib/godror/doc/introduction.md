# Go DRiver for ORacle User Guide

## Godror Introduction

[godror](https://godoc.org/pkg/github.com/godror/godror) is a package which is a
[database/sql/driver.Driver](http://golang.org/pkg/database/sql/driver/#Driver)
for connecting to Oracle Database.  It uses <PERSON>'s excellent Oracle
Call Interface (OCI) wrapper, [ODPI-C](https://www.github.com/oracle/odpi).

The source code is at [github.com/godror](https://github.com/godror/godror).

Questions can be asked [here](https://github.com/godror/godror/issues).

Useful links:

- [API Documentation](https://pkg.go.dev/github.com/godror/godror?tab=doc)
- [User Guide contents](contents.md)
