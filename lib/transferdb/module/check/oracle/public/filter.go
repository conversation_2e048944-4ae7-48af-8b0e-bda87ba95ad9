/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package public

import (
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/filter"
	"go.uber.org/zap"
	"time"
)

func FilterCFGTable(cfg *config.Config, oracle *oracle.Oracle) ([]string, error) {
	startTime := time.Now()
	var (
		exporterTableSlice []string
		excludeTables      []string
		err                error
	)

	// 获取 oracle 所有 schema
	allOraSchemas, err := oracle.GetOracleSchemas()
	if err != nil {
		return nil, err
	}

	if !common.IsContainString(allOraSchemas, common.StringUPPER(cfg.SchemaConfig.SourceSchema)) {
		return nil, fmt.Errorf("oracle schema [%s] isn't exist in the database", cfg.SchemaConfig.SourceSchema)
	}

	// 获取 oracle 所有数据表
	allTables, err := oracle.GetOracleSchemaTable(common.StringUPPER(cfg.SchemaConfig.SourceSchema))
	if err != nil {
		return exporterTableSlice, err
	}

	switch {
	case len(cfg.SchemaConfig.SourceIncludeTable) != 0 && len(cfg.SchemaConfig.SourceExcludeTable) == 0:
		// 过滤规则加载
		f, err := filter.Parse(cfg.SchemaConfig.SourceIncludeTable)
		if err != nil {
			panic(err)
		}

		for _, t := range allTables {
			if f.MatchTable(t) {
				exporterTableSlice = append(exporterTableSlice, t)
			}
		}
	case len(cfg.SchemaConfig.SourceIncludeTable) == 0 && len(cfg.SchemaConfig.SourceExcludeTable) != 0:
		// 过滤规则加载
		f, err := filter.Parse(cfg.SchemaConfig.SourceExcludeTable)
		if err != nil {
			panic(err)
		}

		for _, t := range allTables {
			if f.MatchTable(t) {
				excludeTables = append(excludeTables, t)
			}
		}
		exporterTableSlice = common.FilterDifferenceStringItems(allTables, excludeTables)

	case len(cfg.SchemaConfig.SourceIncludeTable) == 0 && len(cfg.SchemaConfig.SourceExcludeTable) == 0:
		exporterTableSlice = allTables

	default:
		return exporterTableSlice, fmt.Errorf("source config params include-table/exclude-table cannot exist at the same time")
	}

	if len(exporterTableSlice) == 0 {
		return exporterTableSlice, fmt.Errorf("exporter tables aren't exist, please check config params include-table/exclude-table")
	}

	endTime := time.Now()
	zap.L().Info("get oracle to mysql all tables",
		zap.String("schema", cfg.SchemaConfig.SourceSchema),
		zap.Strings("exporter tables list", exporterTableSlice),
		zap.Int("include table counts", len(exporterTableSlice)),
		zap.Int("exclude table counts", len(excludeTables)),
		zap.Int("all table counts", len(allTables)),
		zap.String("cost", endTime.Sub(startTime).String()))

	return exporterTableSlice, nil
}
