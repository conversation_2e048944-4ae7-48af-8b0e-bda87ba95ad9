{{ define "report_overview" }}
<a name="report_overview"></a>
<center><font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>REPORT OVERVIEW</b></font><hr align="center" width="460">
</center>
<table width="90%" border="1">
    <tr>
        <th align="left" width="20%">REPORT NAME</th>
        <td width="80%"><tt>{{.ReportName}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">REPORT RUN USER</th>
        <td width="80%"><tt>{{.ReportUser}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">HOST NAME</th>
        <td width="80%"><tt>{{.HostName}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">PLATFORM NAME / ID</th>
        <td width="80%"><tt>{{.PlatformName}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">DATABASE NAME</th>
        <td width="80%"><tt>{{.DBName}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">GLOBAL DATABASE NAME</th>
        <td width="80%"><tt>{{.GlobalDBName}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">CLUSTERED DATABASE</th>
        <td width="80%"><tt>{{.ClusterDB}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">CLUSTERED DATABASE INSTANCES</th>
        <td width="80%"><tt>{{.ClusterDBInstance}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">INSTANCE NAME</th>
        <td width="80%"><tt>{{.InstanceName}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">INSTANCE NUMBER</th>
        <td width="80%"><tt>{{.InstanceNumber}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">THREAD NUMBER</th>
        <td width="80%"><tt>{{.ThreadNumber}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">DATABASE BLOCK SIZE(KB)</th>
        <td width="80%"><tt>{{.BlockSize}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">DATABASE TOTAL USED SIZE(GB)</th>
        <td width="80%"><tt>{{.TotalUsedSize}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">HOST CPUS(Single)</th>
        <td width="80%"><tt>{{.HostCPUS}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">HOST MEM(GB)</th>
        <td width="80%"><tt>{{.HostMem}}</tt></td>
    </tr>
    <tr><th align="left" width="20%">CHARACTER SET</th>
        <td width="80%"><tt>{{.CharacterSet}}</tt></td>
    </tr>
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>
&nbsp;
{{ end }}