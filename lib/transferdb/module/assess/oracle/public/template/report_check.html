{{ define "report_check" }}
<center><font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>Report Check</b></font><hr align="center" width="460">
</center>
<a name="schema_table_partition_counts_over1024"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_table_partition_counts_over1024</b>
</font><hr align="left" width="260">

<li class="comment">
    The database schema partition table counts overview.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">PARTITION COUNTS</th>
    </tr>
    {{ range .ListSchemaPartitionTableCountsCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .PartitionCounts }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_table_avg_row_length_over6M"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_table_avg_row_length_over6M</b>
</font><hr align="left" width="260">

<li class="comment">
    The database schema table avg row length over 6M overview.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">AVG ROW LENGTH/MB</th>
    </tr>
    {{ range .ListSchemaTableRowLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .AvgRowLength }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_table_index_column_over3072"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_table_index_column_over3072</b>
</font><hr align="left" width="260">

<li class="comment">
    The database schema table index column length over 3072 bytes overview.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">INDEX NAME</th>
        <th class="noLink">COLUMN LENGTH/bytes</th>
    </tr>
    {{ range .ListSchemaTableIndexRowLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .IndexName }}</td>
        <td class="noLink" align="center">{{ .ColumnLength }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_table_column_counts_over512"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_table_column_counts_over512</b>
</font><hr align="left" width="260">

<li class="comment">
    The database schema table column counts over 512 overview.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">COLUMN COUNTS</th>
    </tr>
    {{ range .ListSchemaTableColumnCountsCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .ColumnCounts }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_table_index_counts_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_table_index_counts_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The database schema table index counts over 64 overview.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">INDEX COUNTS</th>
    </tr>
    {{ range .ListSchemaIndexCountsCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .IndexCounts }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_username_length_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_username_length_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The length of the database username length is greater than 64.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">ACCOUNT STATUS</th>
        <th class="noLink">CREATED</th>
        <th class="noLink">LENGTH</th>
    </tr>
    {{ range .ListUsernameLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .AccountStatus }}</td>
        <td class="noLink" align="center">{{ .Created }}</td>
        <td class="noLink" align="center">{{ .Length }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_tablename_length_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_tablename_length_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The length of the database tablename length is greater than 64.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">LENGTH</th>
    </tr>
    {{ range .ListSchemaTableNameLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .Length }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_columnname_length_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_columnname_length_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The length of the database table column name length is greater than 64.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">COLUMN NAME</th>
        <th class="noLink">LENGTH</th>
    </tr>
    {{ range .ListSchemaTableColumnNameLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .ColumnName }}</td>
        <td class="noLink" align="center">{{ .Length }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_indexname_length_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_indexname_length_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The length of the database table index name length is greater than 64.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">TABLE NAME</th>
        <th class="noLink">INDEX NAME</th>
        <th class="noLink">LENGTH</th>
    </tr>
    {{ range .ListSchemaTableIndexNameLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .TableName }}</td>
        <td class="noLink" align="center">{{ .IndexName }}</td>
        <td class="noLink" align="center">{{ .Length }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_viewname_length_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_viewname_length_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The length of the database table view name length is greater than 64.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">VIEW NAME</th>
        <th class="noLink">READ ONLY</th>
        <th class="noLink">LENGTH</th>
    </tr>
    {{ range .ListSchemaViewNameLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .ViewName }}</td>
        <td class="noLink" align="center">{{ .ReadOnly }}</td>
        <td class="noLink" align="center">{{ .Length }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>

<a name="schema_sequencename_length_over64"></a>
<font size="+2" face="Arial,Helvetica,Geneva,sans-serif" color="#336699">
    <b>schema_sequencename_length_over64</b>
</font><hr align="left" width="260">

<li class="comment">
    The length of the database table sequence name length is greater than 64.
</li>
<table width="90%" border="1">
    <tr>
        <th class="noLink">SCHEMA</th>
        <th class="noLink">SEQUENCE NAME</th>
        <th class="noLink">ORDER FLAG</th>
        <th class="noLink">LENGTH</th>
    </tr>
    {{ range .ListSchemaSequenceNameLengthCheck }}
    <tr>
        <td class="noLink" align="center" >{{ .Schema }}</td>
        <td class="noLink" align="center">{{ .SequenceName }}</td>
        <td class="noLink" align="center">{{ .OrderFlag }}</td>
        <td class="noLink" align="center">{{ .Length }}</td>
    </tr>
    {{ end }}
</table>
&nbsp;
<center>[<a class="noLink" href="#top">Top</a>]</center>
&nbsp;
&nbsp;
{{ end }}