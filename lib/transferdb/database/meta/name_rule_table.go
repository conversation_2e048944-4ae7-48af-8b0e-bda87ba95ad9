/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package meta

import (
	"context"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gorm.io/gorm"
)

// 上下游数据表名字映射规则
type TableNameRule struct {
	ID          uint   `gorm:"primary_key;autoIncrement;comment:'自增编号'" json:"id"`
	DBTypeS     string `gorm:"type:varchar(30);index:idx_dbtype_st_map,unique;index:idx_dbtype_st_table,unique;comment:'源数据库类型'" json:"db_type_s"`
	DBTypeT     string `gorm:"type:varchar(30);index:idx_dbtype_st_map,unique;index:idx_dbtype_st_table,unique;comment:'目标数据库类型'" json:"db_type_t"`
	SchemaNameS string `gorm:"type:varchar(100);not null;index:idx_dbtype_st_map,unique;index:idx_dbtype_st_table,unique;comment:'源端库 schema'" json:"schema_name_s"`
	TableNameS  string `gorm:"type:varchar(100);not null;index:idx_dbtype_st_map,unique;index:idx_dbtype_st_table,unique;comment:'源端表名'" json:"table_name_s"`
	SchemaNameT string `gorm:"type:varchar(100);not null;index:idx_dbtype_st_map,unique;comment:'目标库 schema'" json:"schema_name_t"`
	TableNameT  string `gorm:"type:varchar(100);not null;comment:'目标表名'" json:"table_name_t"`
	*BaseModel
}

func NewTableNameRuleModel(m *Meta) *TableNameRule {
	return &TableNameRule{BaseModel: &BaseModel{
		Meta: m,
	}}
}

func (rw *TableNameRule) ParseSchemaTable() (string, error) {
	stmt := &gorm.Statement{DB: rw.GormDB}
	err := stmt.Parse(rw)
	if err != nil {
		return "", fmt.Errorf("parse struct [TableNameRule] get table_name failed: %v", err)
	}
	return stmt.Schema.Table, nil
}

func (rw *TableNameRule) DetailTableNameRule(ctx context.Context, detailS *TableNameRule) ([]TableNameRule, error) {
	var tableRuleMap []TableNameRule

	table, err := rw.ParseSchemaTable()
	if err != nil {
		return nil, err
	}

	if err = rw.DB(ctx).Where("UPPER(db_type_s) = ? AND UPPER(db_type_t) = ? AND UPPER(schema_name_s) = ? AND UPPER(schema_name_t) = ?",
		common.StringUPPER(detailS.DBTypeS),
		common.StringUPPER(detailS.DBTypeT),
		common.StringUPPER(detailS.SchemaNameS),
		common.StringUPPER(detailS.SchemaNameT)).Find(&tableRuleMap).Error; err != nil {
		return tableRuleMap, fmt.Errorf("detail table [%s] record failed: %v", table, err)
	}
	return tableRuleMap, nil
}
