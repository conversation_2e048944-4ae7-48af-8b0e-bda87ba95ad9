/*
Copyright © 2020 Marvin

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package meta

import (
	"context"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/logger"
	utildatabase "gitee.com/pingcap_enterprise/tms/util/database"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"time"
)

type Meta struct {
	GormDB *gorm.DB
}

func NewMetaDBEngine(ctx context.Context, mysqlCfg config.MetaConfig, slowThreshold int) (*Meta, error) {
	// 创建元数据库 - using unified connection
	dbConfig := utildatabase.MySQLConfig{
		User:     mysqlCfg.Username,
		Password: mysqlCfg.Password,
		Host:     mysqlCfg.Host,
		Port:     mysqlCfg.Port,
		Database: "", // Empty for root connection
		Charset:  "utf8mb4",
		Timezone: time.Local,
	}

	// Create connection for database creation
	connector := utildatabase.NewMySQLConnector(dbConfig)
	mysqlDB, err := utildatabase.OpenMySQL(connector, utildatabase.WithPingTimeout(5))
	if err != nil {
		return &Meta{}, fmt.Errorf("error on open general database connection [%v]: %v", mysqlCfg.MetaSchema, err)
	}

	createSchema := fmt.Sprintf(`CREATE DATABASE IF NOT EXISTS %s`, mysqlCfg.MetaSchema)
	_, err = mysqlDB.ExecContext(ctx, createSchema)
	if err != nil {
		return &Meta{}, fmt.Errorf("error on exec meta database sql [%v]: %v", createSchema, err)
	}
	err = mysqlDB.Close()
	if err != nil {
		return &Meta{}, fmt.Errorf("error on close general database sql [%v]: %v", createSchema, err)
	}

	// 初始化 MetaDB - using unified connection for GORM
	// 创建连接到 MetaSchema 的统一数据库连接
	metaDBConfig := utildatabase.MySQLConfig{
		User:     mysqlCfg.Username,
		Password: mysqlCfg.Password,
		Host:     mysqlCfg.Host,
		Port:     mysqlCfg.Port,
		Database: mysqlCfg.MetaSchema, // 直接连接到 MetaSchema
		Charset:  "utf8mb4",
		Timezone: time.Local,
	}

	// 使用统一连接接口创建数据库连接
	metaConnector := utildatabase.NewMySQLConnector(metaDBConfig)
	metaDB, err := utildatabase.OpenMySQL(metaConnector, utildatabase.WithPingTimeout(5))
	if err != nil {
		return &Meta{}, fmt.Errorf("error on open meta database connection: %v", err)
	}

	// 初始化 gorm 日志记录器
	l := logger.NewGormLogger(zap.L(), slowThreshold)
	l.SetAsDefault()
	
	// 使用统一创建的连接初始化 GORM
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: metaDB, // 使用统一创建的连接
	}), &gorm.Config{
		// 禁用外键（指定外键时不会在 mysql 创建真实的外键约束）
		DisableForeignKeyConstraintWhenMigrating: true,
		PrepareStmt:                              true,
		Logger:                                   l,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
	})

	if err != nil {
		return nil, fmt.Errorf("error on open meta database connection: %v", err)
	}

	return &Meta{GormDB: gormDB}, nil
}

func WrapGormDB(gormDB *gorm.DB) *Meta {
	return &Meta{GormDB: gormDB}
}

type ctxTxnKeyStruct struct{}

var ctxTxnKey = ctxTxnKeyStruct{}

func (m *Meta) DB(ctx context.Context) *gorm.DB {
	iface := ctx.Value(ctxTxnKey)
	if iface != nil {
		tx, ok := iface.(*gorm.DB)
		if !ok {
			return nil
		}
		return tx
	}
	return m.GormDB.WithContext(ctx)
}

func (m *Meta) MigrateTables() (err error) {
	return m.migrateStream(
		new(ColumnDatatypeRule),
		new(TableDatatypeRule),
		new(SchemaDatatypeRule),
		new(DataCompareMeta),
		new(WaitSyncMeta),
		new(FullSyncMeta),
		new(IncrSyncMeta),
		new(ErrorLogDetail),
		new(BuildinGlobalDefaultval),
		new(BuildinColumnDefaultval),
		new(BuildinObjectCompatible),
		new(BuildinDatatypeRule),
		new(TableNameRule),
		new(ChunkErrorDetail),
	)
}

func (m *Meta) InitDefaultValue(ctx context.Context) error {
	err := NewBuildinGlobalDefaultvalModel(m).InitO2MTBuildinGlobalDefaultValue(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinGlobalDefaultvalModel(m).InitMT2OBuildinGlobalDefaultValue(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinObjectCompatibleModel(m).InitO2MBuildinObjectCompatible(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinObjectCompatibleModel(m).InitO2TBuildinObjectCompatible(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinDatatypeRuleModel(m).InitO2MBuildinDatatypeRule(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinDatatypeRuleModel(m).InitO2TBuildinDatatypeRule(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinDatatypeRuleModel(m).InitM2OBuildinDatatypeRule(ctx)
	if err != nil {
		return err
	}
	err = NewBuildinDatatypeRuleModel(m).InitT2OBuildinDatatypeRule(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (m *Meta) migrateStream(models ...interface{}) (err error) {
	for _, model := range models {
		err = m.GormDB.AutoMigrate(model)
		if err != nil {
			return fmt.Errorf("error on migrate stream: %v", err)
		}
	}
	return nil
}

func ArrayStructGroupsOf[T any](fsm []T, num int64) [][]T {
	max := int64(len(fsm))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]T{fsm}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]T, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, fsm[start:end])
		} else {
			segments = append(segments, fsm[start:])
		}
		start = i * num
	}
	return segments
}
