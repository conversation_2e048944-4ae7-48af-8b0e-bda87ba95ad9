# Diff Configuration. This config file shows how to check data for D<PERSON>'s task.

######################### Global config #########################

log-level = "info"

# for example, the whole data is [1...100]
# we can split these data to [1...10], [11...20], ..., [91...100]
# the [1...10] is a chunk, and it's chunk size is 10
# size of the split chunk
chunk-size = 1000

# how many goroutines are created to check data
check-thread-count = 4

# sampling check percent, for example 10 means only check 10% data
sample-percent = 100

# calculate the data's checksum, and compare data by checksum.
# set false if want to comapre the data directly
use-checksum = true

# set true if just want compare data by checksum, will skip select data when checksum is not equal. 
only-use-checksum = false

# set true will continue check from the latest checkpoint
use-checkpoint = true

# ignore check table's data
ignore-data-check = false

# ignore check table's struct
ignore-struct-check = false

# the name of the file which saves sqls used to fix different data.
fix-sql-file = "fix.sql"

# dm-master's address, the format should like "http://127.0.0.1:8261"
dm-addr = "http://127.0.0.1:8261"

# the DM's task name which is willing to check data
dm-task = "test"
