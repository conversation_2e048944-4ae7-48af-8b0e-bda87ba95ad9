// Copyright 2018 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

package compare

import (
	"context"
	"fmt"
	"path"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/dbutil"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/diff"
	datacomparepkg "gitee.com/pingcap_enterprise/tms/pkg/datacompare"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/file"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/samber/lo"

	timslog "gitee.com/pingcap_enterprise/tms/util/log"
	_ "github.com/go-sql-driver/mysql"
	"github.com/pingcap/log"
	"go.uber.org/zap"
)

type FastFailThresholds struct {
	MissingTablesThreshold     float64
	TableChunkFailThreshold    float64
	OverallTableErrorThreshold float64
	TableChunkFailMinCount     int
}

type ErrorTracker struct {
	TotalTables      int
	MissingTables    int
	TablesWithErrors int
	TableChunkErrors map[string]int
	TableChunkTotals map[string]int
	Thresholds       *FastFailThresholds
}

func NewErrorTracker(thresholds *FastFailThresholds) *ErrorTracker {
	return &ErrorTracker{
		TableChunkErrors: make(map[string]int),
		TableChunkTotals: make(map[string]int),
		Thresholds:       thresholds,
	}
}

func (et *ErrorTracker) CheckMissingTablesThreshold() bool {
	if et.TotalTables == 0 {
		return false
	}
	missingPercentage := float64(et.MissingTables) / float64(et.TotalTables) * 100
	return missingPercentage > et.Thresholds.MissingTablesThreshold
}

func (et *ErrorTracker) CheckTableChunkFailThreshold(tableName string) (bool, float64, string) {
	chunkErrors, ok := et.TableChunkErrors[tableName]
	if !ok {
		return false, 0, ""
	}
	chunkTotals, ok := et.TableChunkTotals[tableName]
	if !ok || chunkTotals == 0 {
		return false, 0, ""
	}

	errorPercentage := float64(chunkErrors) / float64(chunkTotals) * 100

	// Check if minimum failed chunks count is reached (OR condition)
	if et.Thresholds.TableChunkFailMinCount > 0 && chunkErrors >= et.Thresholds.TableChunkFailMinCount {
		return true, errorPercentage, "table-chunk-fail-count"
	}

	// Check if error percentage threshold is exceeded (OR condition)
	if errorPercentage > et.Thresholds.TableChunkFailThreshold {
		return true, errorPercentage, "table-chunk-fail-threshold"
	}

	return false, errorPercentage, ""
}

// CheckAnyTableChunkFailThreshold 检查是否有任何表的块失败率超过阈值。
// 如果超过阈值，返回 true、表名、失败百分比和触发原因。
func (et *ErrorTracker) CheckAnyTableChunkFailThreshold() (bool, string, float64, string) {
	// 遍历所有记录了块错误的表
	for tableName := range et.TableChunkErrors {
		exceeded, percentage, reason := et.CheckTableChunkFailThreshold(tableName)
		if exceeded {
			return true, tableName, percentage, reason
		}
	}
	return false, "", 0, ""
}

func (et *ErrorTracker) CheckOverallTableErrorThreshold() (bool, float64) {
	if et.TotalTables == 0 {
		return false, 0
	}
	errorPercentage := float64(et.TablesWithErrors) / float64(et.TotalTables) * 100
	exceeded := errorPercentage > et.Thresholds.OverallTableErrorThreshold

	// If threshold is exceeded, return the current table that triggered it
	if exceeded {
		return true, errorPercentage
	}

	return false, errorPercentage
}

func (et *ErrorTracker) AddTableChunkError(tableName string) {
	et.TableChunkErrors[tableName]++
}

func (et *ErrorTracker) AddTableError() {
	et.TablesWithErrors++
}

//func main() {
//	cfg := NewConfig()
//	err := cfg.Parse(os.Args[1:])
//	switch errors.Cause(err) {
//	case nil:
//	case flag.ErrHelp:
//		os.Exit(0)
//	default:
//		log.Error("parse cmd flags", zap.Error(err))
//		os.Exit(2)
//	}
//
//	if cfg.PrintVersion {
//		fmt.Printf("version: \n%s", utils.GetRawInfo("o2t_sync_diff"))
//		return
//	}
//
//	l := zap.NewAtomicLevel()
//	if err := l.UnmarshalText([]byte(cfg.LogLevel)); err != nil {
//		log.Error("invalid log level", zap.String("log level", cfg.LogLevel))
//		return
//	}
//	log.SetLevel(l.Depth())
//
//	utils.PrintInfo("o2t_sync_diff")
//
//	ok := cfg.checkConfig()
//	if !ok {
//		log.Error("there is something wrong with your config, please check it!")
//		return
//	}
//
//	log.Info("", zap.Stringer("config", cfg))
//
//	ctx := context.Background()
//	if !checkSyncState(ctx, cfg) {
//		log.Warn("check failed!!!")
//		os.Exit(1)
//	}
//	log.Info("check pass!!!")
//}

func initLogger(taskId int) error {
	cfg := &log.Config{
		Level: timslog.GetRootLogger().LogLevel,
		File: log.FileLogConfig{
			Filename: timslog.GetRootLogger().LogFileRoot + fmt.Sprintf("o2t_sync_diff-%d.log", taskId),
			// default rotate by size 300M in pingcap/log never delete old files
			// MaxSize:
			// MaxDays:
			// MaxBackups:
		},
	}

	var lg *zap.Logger
	var err error
	var globalP *log.ZapProperties
	lg, globalP, err = log.InitLogger(cfg)
	if err != nil {
		return err
	}

	// Do not log stack traces at all, as we'll get the stack trace from the
	// error itself.
	lg = lg.WithOptions(zap.AddStacktrace(zap.DPanicLevel))

	log.ReplaceGlobals(lg, globalP)

	return nil
}

//func checkSyncState(ctx context.Context, cfg *Config, taskInfo *task.Task) bool {
//	beginTime := time.Now()
//	defer func() {
//		log.Info("check data finished", zap.Duration("cost", time.Since(beginTime)))
//	}()
//
//	d, err := NewDiff(ctx, cfg)
//	if err != nil {
//		taskInfo.ErrorDetail = fmt.Sprintf("initialization process failed. %s", err)
//		log.Error("fail to initialize diff process", zap.Error(err))
//		return false
//	}
//
//	err = d.Equal()
//	if err != nil {
//		taskInfo.ErrorDetail = fmt.Sprintf("check data difference failed. %s", err)
//		log.Error("check data difference failed", zap.Error(err))
//		return false
//	}
//
//	d.report.Print()
//
//	return d.report.Result == Pass
//}

func checkSyncState(ctx context.Context, cfg *Config, taskInfo *task.Task, errorTracker *ErrorTracker) bool {
	beginTime := time.Now()
	defer func() {
		log.Info("check data finished", zap.Duration("cost", time.Since(beginTime)))
	}()

	d, err := NewDiff(ctx, cfg)
	if err != nil {
		taskInfo.ErrorDetail = fmt.Sprintf("initialization process failed. %s", err)
		log.Error("fail to initialize diff process", zap.Error(err))
		return false
	}

	result := d.Equal(ctx, errorTracker)
	if result.FastFailTriggered {
		// Handle fast-fail condition
		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		taskInfo.ErrorDetail = result.TriggerReason
		log.Error("Fast-fail threshold exceeded", zap.String("reason", result.TriggerReason))

		// Handle table state based on which table triggered the error
		if result.TriggerTable != "" {
			log.Error("Fast-fail triggered by table", zap.String("triggerTable", result.TriggerTable))
		} else {
			log.Info("Fast-fail triggered but unable to identify trigger table")
		}

		// Create Summary records for remaining unprocessed tables
		createSummaryForSkippedTablesWithState(ctx, cfg, taskInfo, d, result.TriggerTable, errorTracker)
		return false
	} else if result.Err != nil {
		// Handle normal error
		taskInfo.ErrorDetail = fmt.Sprintf("check data difference failed. %s", result.Err)
		log.Error("check data difference failed", zap.Error(result.Err))
		return false
	}

	d.report.Print()

	return d.report.Result == Pass
}

func StartCompare(ctx context.Context, channelId int, taskId int, tmsConfig *config.Config, channelSchemaTableIds []int) {
	initLogger(taskId)
	if err := createFixSqlDir(tmsConfig); err != nil {
		log.Error("tims can not start data compare, it can not create fix sql path.")
		return
	}
	log.Info("start compare task.", zap.Int("channelId", channelId), zap.Int("taskId", taskId))
	go compare(ctx, taskId, tmsConfig, channelSchemaTableIds)

}

func createFixSqlDir(timsConfig *config.Config) error {
	p := path.Join(timsConfig.DataDir, constants.FIX_SQL_PATH)
	if err := file.CreateIfNotExist(p); err != nil {
		log.Error("create fix sql dir failed.", zap.String("path", p), zap.Error(err))
		return err
	}
	return nil
}

func compare(ctx context.Context, taskId int, timsConfig *config.Config, channelSchemaTableIds []int) {
	taskInfo, getTaskInfoErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskInfoErr != nil {
		log.Error("get task info failed.", zap.Int("taskId", taskId), zap.Error(getTaskInfoErr))
		return
	}

	_, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
		StartTime:  time.Now(),
		TaskStatus: constants.TASK_STATUS_RUNNING,
	})
	if err != nil {
		log.Error("update task status failed.", zap.Int("taskId", taskId), zap.Error(err))
		return
	}

	channelInfo, getChannelInfoErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelInfoErr != nil {
		log.Error("get channel info failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Error(getChannelInfoErr))
		return
	}
	datasourceS, getDatasourceSErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getDatasourceSErr != nil {
		log.Error("get datasourceS failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Error(getDatasourceSErr))
		return
	}
	datasourceT, getDatasourceTErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if getDatasourceTErr != nil {
		log.Error("get datasourceT failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Error(getDatasourceTErr))
		return
	}
	dataCompareParams, getErr := datacomparepkg.BuildDataCompareParameter(ctx, taskInfo)
	if getErr != nil {
		log.Error("get merged task params failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId), zap.Error(getErr))
		return
	}

	userCheckpoint := dataCompareParams.GetUseCheckpoint()
	if !userCheckpoint {
		//delete summary and chunk if checkpoint is false
		deleteSummaryErr := models.GetDataCompareReaderWriter().DeleteDataCompareSummaryByChannelIdTaskId(ctx, taskInfo.ChannelId, taskInfo.TaskID)
		if deleteSummaryErr != nil {
			log.Error("delete data compare summary from db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId))
			return
		}
		deleteChunkErr := models.GetDataCompareReaderWriter().DeleteDataCompareChunkByChannelIdTaskId(ctx, taskInfo.ChannelId, taskInfo.TaskID)
		if deleteChunkErr != nil {
			log.Error("delete data compare chunk from db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId))
			return
		}
	}

	taskInfo.StartTime = time.Now()
	dcLog := &datacompare.DataCompareLog{ChannelId: taskInfo.ChannelId, TaskId: taskInfo.TaskID, LogMessage: fmt.Sprintf("t04:data compare task[%d]start", taskId)}
	_, createDcLogErr := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog)
	if createDcLogErr != nil {
		log.Error("task start compare. create data compare log to db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId))
	}

	thresholds := &FastFailThresholds{
		MissingTablesThreshold:     dataCompareParams.GetMissingTablesThreshold(),
		TableChunkFailThreshold:    dataCompareParams.GetTableChunkFailThreshold(),
		OverallTableErrorThreshold: dataCompareParams.GetOverallTableErrorThreshold(),
		TableChunkFailMinCount:     dataCompareParams.GetTableChunkFailMinCount(),
	}
	errorTracker := NewErrorTracker(thresholds)

	existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, err :=
		getTableStatusFromDataSource(ctx, channelSchemaTableIds, taskInfo, datasourceS, datasourceT, dataCompareParams)
	if err != nil {
		log.Error("get table status from data source failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId), zap.Error(err))
		return
	}

	totalTables := len(existTables) + len(oracleNotExistTables) + len(tidbNotExistTables)
	missingTables := len(oracleNotExistTables) + len(tidbNotExistTables)
	errorTracker.TotalTables = totalTables
	errorTracker.MissingTables = missingTables

	log.Info("start compare data.",
		zap.Int("task_id", taskInfo.TaskID), zap.Int("channel_id", taskInfo.ChannelId),
		zap.Int("oracleNotExistTables", len(oracleNotExistTables)),
		zap.Int("tidbNotExistTables", len(tidbNotExistTables)),
		zap.Int("existTables", len(existTables)),
		zap.Float64("missingTablesThreshold", thresholds.MissingTablesThreshold),
		zap.Float64("tableChunkFailThreshold", thresholds.TableChunkFailThreshold),
		zap.Float64("overallTableErrorThreshold", thresholds.OverallTableErrorThreshold))

	if errorTracker.CheckMissingTablesThreshold() {
		missingPercentage := float64(missingTables) / float64(totalTables) * 100
		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		taskInfo.ErrorDetail = fmt.Sprintf("Fast-fail triggered: Missing tables percentage (%.2f%%) exceeds threshold (%.2f%%)",
			missingPercentage, thresholds.MissingTablesThreshold)
		log.Error("Fast-fail triggered due to missing tables threshold",
			zap.Float64("missingPercentage", missingPercentage),
			zap.Float64("threshold", thresholds.MissingTablesThreshold),
			zap.Int("taskId", taskInfo.TaskID))

		saveNotExistTablesToSummaryAndChunks(ctx, taskInfo, oracleNotExistTables, tidbNotExistTables)
		goto updateTask
	}

	saveNotExistTablesToSummaryAndChunks(ctx, taskInfo, oracleNotExistTables, tidbNotExistTables)

	if len(existTables) != 0 {
		doCompare(ctx, timsConfig, taskInfo, dataCompareParams, existTablesIds, errorTracker)
	}

	if len(oracleNotExistTables) != 0 || len(tidbNotExistTables) != 0 {
		if taskInfo.TaskStatus != constants.TASK_STATUS_FAILED {
			taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
			if taskInfo.ErrorDetail != "" {
				taskInfo.ErrorDetail = fmt.Sprintf("%s, %s", taskInfo.ErrorDetail, "compare finished, but contains some table names that do not match.")
			} else {
				taskInfo.ErrorDetail = "compare finished, but contains some table names that do not match."
			}
		}
	}

updateTask:
	taskInfo.EndTime = time.Now()
	_, err = models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		log.Error("update taskInfo to db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Error(err))
	}
	dcLog = &datacompare.DataCompareLog{ChannelId: taskInfo.ChannelId, TaskId: taskInfo.TaskID, LogMessage: fmt.Sprintf("t05:data compare task[%d]finish", taskInfo.TaskID)}
	_, createDcLogErr = models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog)
	if createDcLogErr != nil {
		log.Error("task finish compare. create data compare log to db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId))
	}
}

func getUserCheckpointFlag(mergedTaskParamsMap map[string]*task.TaskParamConfig) bool {
	var useCheckpoint bool
	if useCheckpointConfig, ok := mergedTaskParamsMap[constants.DC_PARAM_USE_CHECKPOINT]; ok {
		useCheckpoint, _ = strconv.ParseBool(useCheckpointConfig.ParamValueCurrent)
	} else {
		useCheckpoint = false
	}
	return useCheckpoint
}

func getTableStatusFromDataSource(ctx context.Context, channelSchemaTableIds []int, taskInfo *task.Task, datasourceS *datasource.Datasource, datasourceT *datasource.Datasource, dataCompareParams *structs.DataCompareTaskParam) ([]*channel.ChannelSchemaTable, []int, []*channel.ChannelSchemaTable, []*channel.ChannelSchemaTable, error) {

	var channelSchemaTables []*channel.ChannelSchemaTable
	var schemaSArray, schemaTArray []string
	var schemaLowercase, tableLowercase int
	var schemaCaseFunc, tableCaseFunc func(string) string
	var err error

	schemaLowercase = dataCompareParams.GetLowCaseSchemaName()
	tableLowercase = dataCompareParams.GetLowCaseTableName()

	// handle target table schema/table names with case
	schemaCaseFunc = stringutil.GetCaseFunc(schemaLowercase)
	tableCaseFunc = stringutil.GetCaseFunc(tableLowercase)
	log.Info("get tables from data source.",
		zap.Int("task_id", taskInfo.TaskID),
		zap.Int("channel_id", taskInfo.ChannelId),
		zap.Any("schemaLowercase", schemaLowercase),
		zap.Any("tableLowercase", tableLowercase))

	if len(channelSchemaTableIds) == 0 {
		channelSchemaTables, err = models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
		if err != nil {
			log.Error("get channel schema tables failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(err))
			return nil, nil, nil, nil, err
		}
	} else {
		channelSchemaTables, err = models.GetChannelReaderWriter().GetChannelSchemaTablesByPks(ctx, channelSchemaTableIds)
		if err != nil {
			log.Error("get channel schema tables failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(err))
			return nil, nil, nil, nil, err
		}
	}
	for _, channelSchemaTable := range channelSchemaTables {
		schemaSArray = append(schemaSArray, channelSchemaTable.SchemaNameS)
		schemaTArray = append(schemaTArray, channelSchemaTable.SchemaNameT)
		schemaTArray = append(schemaTArray, schemaCaseFunc(channelSchemaTable.SchemaNameT))
	}
	schemaSArray = lo.Uniq(schemaSArray)
	schemaTArray = lo.Uniq(schemaTArray)

	oracleConn, err := models.OpenOracle(models.BuildOracleConfigFromDatasource(datasourceS, ""))
	if err != nil {
		log.Error("open oracle db failed.", zap.Error(err), zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID))
		return nil, nil, nil, nil, err
	}
	log.Info("open oracle db success, get oracle tables.", zap.Int("task_id", taskInfo.TaskID), zap.Int("channel_id", taskInfo.ChannelId), zap.Any("schemaSArray", schemaSArray))
	oracleTables, err := models.GetDatasourceReaderWriter().GetOracleTables(ctx, oracleConn, schemaSArray)
	if err != nil {
		log.Error("get oracle tables failed.", zap.Error(err), zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID))
		return nil, nil, nil, nil, err
	}

	tidbConn, err := models.OpenMysql(datasourceT.UserName, datasourceT.PasswordValue, datasourceT.HostIp, datasourceT.HostPort, "")
	if err != nil {
		log.Error("open tidb db failed.", zap.Error(err), zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID))
		return nil, nil, nil, nil, err
	}
	log.Info("open tidb db success, get tidb tables.", zap.Int("task_id", taskInfo.TaskID), zap.Int("channel_id", taskInfo.ChannelId), zap.Any("schemaTArray", schemaTArray))
	tidbTables, err := models.GetDatasourceReaderWriter().GetMysqlTables(ctx, tidbConn, schemaTArray)
	if err != nil {
		log.Error("get tidb tables failed.", zap.Error(err), zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID))
		return nil, nil, nil, nil, err
	}

	oracleTableMap := make(structs.SchemaTableMapping)
	tidbTableMap := make(structs.SchemaTableMapping)
	for _, oracleSchema := range oracleTables {
		for _, oracleObj := range oracleSchema.ObjectTypes {
			for _, oracleTable := range oracleObj.Tables {
				oracleTableMap[structs.SchemaTablePair{SchemaName: oracleSchema.SchemaName, TableName: oracleTable.TableName}] = true
			}
		}
	}
	for _, tidbSchema := range tidbTables {
		for _, tidbObj := range tidbSchema.ObjectTypes {
			for _, tidbTable := range tidbObj.Tables {
				tidbTableMap[structs.SchemaTablePair{SchemaName: schemaCaseFunc(tidbSchema.SchemaName), TableName: tableCaseFunc(tidbTable.TableName)}] = true
			}
		}
	}

	log.Debug("mapping", zap.Any("oracleTableMap", oracleTableMap.String()), zap.Any("tidbTableMap", tidbTableMap.String()), zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID))
	existTables := make([]*channel.ChannelSchemaTable, 0)
	oracleNotExistTables := make([]*channel.ChannelSchemaTable, 0)
	tidbNotExistTables := make([]*channel.ChannelSchemaTable, 0)
	for _, channelSchemaTable := range channelSchemaTables {
		pairS := structs.SchemaTablePair{SchemaName: channelSchemaTable.SchemaNameS, TableName: channelSchemaTable.TableNameS}
		pairT := structs.SchemaTablePair{SchemaName: schemaCaseFunc(channelSchemaTable.SchemaNameT), TableName: tableCaseFunc(channelSchemaTable.TableNameT)}
		log.Debug("check table exist",
			zap.Any("sourceTable", pairS), zap.Any("targetTable", pairT),
			zap.Any("tableInOracle", oracleTableMap[pairS]),
			zap.Any("tableInTiDB", tidbTableMap[pairT]),
			zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID))
		if _, ok := oracleTableMap[pairS]; !ok {
			oracleNotExistTables = append(oracleNotExistTables, channelSchemaTable)
			continue
		}
		if _, ok := tidbTableMap[pairT]; !ok {
			tidbNotExistTables = append(tidbNotExistTables, channelSchemaTable)
			continue
		}
		existTables = append(existTables, channelSchemaTable)
	}
	existTablesIds := lo.Map(existTables, func(v *channel.ChannelSchemaTable, _ int) int {
		return v.ChannelSchtableId
	})
	return existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, nil
}

func saveNotExistTablesToSummaryAndChunks(ctx context.Context, taskInfo *task.Task, oracleNotExistTables []*channel.ChannelSchemaTable, tidbNotExistTables []*channel.ChannelSchemaTable) {
	totalLen := len(oracleNotExistTables) + len(tidbNotExistTables)
	if totalLen == 0 {
		return
	}
	summaries := make([]datacompare.Summary, 0)
	chunks := make([]datacompare.Chunk, 0)
	nowTime := time.Now()
	dcLogs := make([]datacompare.DataCompareLog, 0, totalLen)

	for _, notExistTable := range oracleNotExistTables {
		summary := datacompare.Summary{
			ChannelId:         taskInfo.ChannelId,
			TaskId:            taskInfo.TaskID,
			Schema:            strings.ToLower(notExistTable.SchemaNameT),
			Table:             notExistTable.TableNameT,
			ChunkNum:          1,
			CheckFailedNum:    1,
			State:             "invalid",
			StartTime:         nowTime,
			UpdateTime:        nowTime,
			ChannelSchtableId: notExistTable.ChannelSchtableId,
		}
		message := fmt.Sprintf("table [%s.%s] not exist in oracle", notExistTable.SchemaNameS, notExistTable.TableNameS)
		chunk := datacompare.Chunk{
			InstanceId: "target-1",
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			Schema:     strings.ToLower(notExistTable.SchemaNameT),
			Table:      notExistTable.TableNameT,
			State:      "invalid",
			UpdateTime: nowTime,
			Message:    message,
		}
		dcLog := datacompare.DataCompareLog{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			LogLevel:   "error",
			LogMessage: fmt.Sprintf("t04-000:%s", message),
		}
		dcLogs = append(dcLogs, dcLog)
		summaries = append(summaries, summary)
		chunks = append(chunks, chunk)
	}
	for _, notExistTable := range tidbNotExistTables {
		summary := datacompare.Summary{
			ChannelId:         taskInfo.ChannelId,
			TaskId:            taskInfo.TaskID,
			Schema:            notExistTable.SchemaNameT,
			Table:             notExistTable.TableNameT,
			ChunkNum:          1,
			CheckFailedNum:    1,
			State:             "failed",
			StartTime:         nowTime,
			UpdateTime:        nowTime,
			ChannelSchtableId: notExistTable.ChannelSchtableId,
		}
		message := fmt.Sprintf("table [%s.%s] not exist in tidb", notExistTable.SchemaNameT, notExistTable.TableNameT)
		chunk := datacompare.Chunk{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			Schema:     notExistTable.SchemaNameT,
			Table:      notExistTable.TableNameT,
			State:      "failed",
			UpdateTime: nowTime,
			Message:    message,
		}
		dcLog := datacompare.DataCompareLog{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			LogLevel:   "error",
			LogMessage: fmt.Sprintf("t04-000:%s", message),
		}
		dcLogs = append(dcLogs, dcLog)
		summaries = append(summaries, summary)
		chunks = append(chunks, chunk)
	}

	createErr := models.GetDataCompareReaderWriter().CreateDataCompareLogs(ctx, dcLogs)
	if createErr != nil {
		log.Error("create data compare logs to db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId), zap.Error(createErr))
	}

	saveSummaryErr := models.GetDataCompareReaderWriter().SaveDataCompareSummaries(ctx, summaries)
	if saveSummaryErr != nil {
		log.Error("save data compare summaries to db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId), zap.Error(saveSummaryErr))
	}
	saveChunksErr := models.GetDataCompareReaderWriter().SaveDataCompareChunks(ctx, chunks)
	if saveChunksErr != nil {
		log.Error("save data compare chunks to db failed.", zap.Int("taskId", taskInfo.TaskID), zap.Int("channelId", taskInfo.ChannelId), zap.Error(saveChunksErr))
	}
}

//func doCompare(ctx context.Context, timsConfig *config.Config, taskInfo *task.Task, dataCompareParams *structs.DataCompareTaskParam, channelSchemaTableIds []int) {
//	config, err := buildConfig(ctx, taskInfo, timsConfig, dataCompareParams, channelSchemaTableIds)
//	if err != nil {
//		log.Error("build config from tims config failed.", zap.Error(err))
//		taskInfo.ErrorDetail = "build config from tims config failed."
//		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
//		return
//	}
//	ok := config.checkConfig()
//	if !ok {
//		log.Error("there is something wrong with your config, please check it!")
//		taskInfo.ErrorDetail = fmt.Sprintf("there is something wrong with your config, please check it!. %s", err)
//		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
//		return
//	}
//
//	//change default schema to schema same to tims db schema
//	diff.SetCheckpointSchemaName(timsConfig.DBConfig.Schema)
//
//	if !checkSyncState(ctx, config, taskInfo) {
//		log.Warn("check failed")
//		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
//	} else {
//		log.Info("check pass")
//		taskInfo.ErrorDetail = "check pass"
//		taskInfo.TaskStatus = constants.TASK_STATUS_FINISH
//	}
//}

func doCompare(ctx context.Context, timsConfig *config.Config, taskInfo *task.Task, dataCompareParams *structs.DataCompareTaskParam, channelSchemaTableIds []int, errorTracker *ErrorTracker) {
	config, err := buildConfig(ctx, taskInfo, timsConfig, dataCompareParams, channelSchemaTableIds)
	if err != nil {
		log.Error("build config from tims config failed.", zap.Error(err))
		taskInfo.ErrorDetail = "build config from tims config failed."
		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		return
	}
	ok := config.checkConfig()
	if !ok {
		log.Error("there is something wrong with your config, please check it!")
		taskInfo.ErrorDetail = fmt.Sprintf("there is something wrong with your config, please check it!. %s", err)
		taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		return
	}

	diff.SetCheckpointSchemaName(timsConfig.DBConfig.Schema)

	if !checkSyncState(ctx, config, taskInfo, errorTracker) {
		log.Warn("check failed")
		// Only set status to FAILED if it wasn't already set by fast-fail
		if taskInfo.TaskStatus != constants.TASK_STATUS_FAILED {
			taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		}
		// Don't override the error detail if it was already set (e.g., by fast-fail)
		// Only set a generic message if no error detail exists
		if taskInfo.ErrorDetail == "" {
			taskInfo.ErrorDetail = "data comparison check failed"
		}
	} else {
		log.Info("check pass")
		taskInfo.ErrorDetail = "check pass"
		taskInfo.TaskStatus = constants.TASK_STATUS_FINISH
	}
}

func buildConfig(ctx context.Context, taskInfo *task.Task, tmsConfig *config.Config, dataCompareParams *structs.DataCompareTaskParam, channelSchemaTableIds []int) (*Config, error) {
	var tables []*channel.ChannelSchemaTable
	var err error

	defer func() {
		if err != nil {
			log.Error("build config failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(err))
		} else {
			log.Info("build config finished.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Int("tableCount", len(tables)))
		}
	}()

	if len(channelSchemaTableIds) == 0 {
		tables, err = models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
		if err != nil {
			log.Error("get channel schema tables failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(err))
			return nil, err
		}
	} else {
		tables, err = models.GetChannelReaderWriter().GetChannelSchemaTablesByPks(ctx, channelSchemaTableIds)
		if err != nil {
			log.Error("get channel schema tables failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(err))
			return nil, err
		}
	}
	if len(tables) == 0 {
		return nil, errors.NewErrorf(errors.TIMS_NO_COMPARE_TABLES, "task[%d] have not config table to compare.", taskInfo.TaskID)
	}

	schemaLowercase := dataCompareParams.GetLowCaseSchemaName()
	tableLowercase := dataCompareParams.GetLowCaseTableName()
	if schemaLowercase != 0 && tableLowercase != 0 {
		for _, table := range tables {
			if schemaLowercase == 1 {
				table.SchemaNameT = strings.ToLower(table.SchemaNameT)
			} else if schemaLowercase == 2 {
				table.SchemaNameT = strings.ToUpper(table.SchemaNameT)
			} else if schemaLowercase == 0 {
				table.SchemaNameT = table.SchemaNameS
			}
			if tableLowercase == 1 {
				table.TableNameT = strings.ToLower(table.TableNameT)
			} else if tableLowercase == 2 {
				table.TableNameT = strings.ToUpper(table.TableNameT)
			} else if tableLowercase == 0 {
				table.TableNameT = table.TableNameS
			}
		}
	}

	//task table config
	taskTableConfigs, getTaskTableConfigsErr := models.GetTaskReaderWriter().GetTaskTableConfigsByTaskIdAndChannelId(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if getTaskTableConfigsErr != nil {
		log.Error("get param template details failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(getTaskTableConfigsErr))
		return nil, getTaskTableConfigsErr
	}
	if schemaLowercase != 0 && tableLowercase != 0 {
		for _, taskTableConfig := range taskTableConfigs {
			if schemaLowercase == 1 {
				taskTableConfig.SchemaNameT = strings.ToLower(taskTableConfig.SchemaNameT)
			} else if schemaLowercase == 2 {
				taskTableConfig.SchemaNameT = strings.ToUpper(taskTableConfig.SchemaNameT)
			} else if schemaLowercase == 0 {
				taskTableConfig.SchemaNameT = taskTableConfig.SchemaNameS
			}
			if tableLowercase == 1 {
				taskTableConfig.TableNameT = strings.ToLower(taskTableConfig.TableNameT)
			} else if tableLowercase == 2 {
				taskTableConfig.TableNameT = strings.ToUpper(taskTableConfig.TableNameT)
			} else if tableLowercase == 0 {
				taskTableConfig.TableNameT = taskTableConfig.TableNameS
			}
		}
	}
	taskTableConfigsMap := buildTaskTableConfigsMap(taskTableConfigs)

	config := &Config{}
	config.FromTims = true
	config.ChannelId = taskInfo.ChannelId
	config.TaskId = taskInfo.TaskID
	config.TimsDBConfig = buildTimsDBConfig(tmsConfig.DBConfig)
	channelInfo, getChannelInfoErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelInfoErr != nil {
		log.Error("get channel info error.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(getChannelInfoErr))
		return nil, getChannelInfoErr
	}
	sourceDBCfg, buildSourceDbCfgErr := buildSourceDbCfg(ctx, channelInfo, dataCompareParams)
	if buildSourceDbCfgErr != nil {
		log.Error("build source db config failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(buildSourceDbCfgErr))
		return nil, buildSourceDbCfgErr
	}
	config.SourceDBCfg = sourceDBCfg

	targetDBCfg, buildTargetDBCfgErr := buildTargetDBCfg(ctx, channelInfo, dataCompareParams)
	if buildTargetDBCfgErr != nil {
		log.Error("build target db config failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Error(buildTargetDBCfgErr))
		return nil, buildTargetDBCfgErr
	}
	config.TargetDBCfg = targetDBCfg

	config.Sample = 100
	config.ChunkSize = dataCompareParams.GetChunkSize()
	config.CheckThreadCount = dataCompareParams.GetCheckThreadCount()
	config.UseChecksum = dataCompareParams.GetUseChecksum()
	config.OnlyUseChecksum = dataCompareParams.GetOnlyUseChecksum()
	config.FixTarget = dataCompareParams.GetFixTarget()
	config.EmptyStringNullCompareSwitch = dataCompareParams.GetEmptyStringNullCompare()
	config.Ascii0Switch = dataCompareParams.GetAscii0Switch()
	config.IgnoreStructCheck = dataCompareParams.GetIgnoreStructCheck()
	config.IgnoreStats = dataCompareParams.GetIgnoreStats()
	config.IgnoreDataCheck = dataCompareParams.GetIgnoreDataCheck()
	config.UseCheckpoint = dataCompareParams.GetUseCheckpoint()
	config.FloatTruncPrecision = dataCompareParams.GetFloatTruncPrecision()
	config.DoubleTruncPrecision = dataCompareParams.GetDoubleTruncPrecision()
	config.LowCaseSchemaName = dataCompareParams.GetLowCaseSchemaName()
	config.LowCaseTableName = dataCompareParams.GetLowCaseTableName()
	config.CompressMode = dataCompareParams.GetCompressMode()

	config.FixSQLDir = path.Join(
		tmsConfig.DataDir,
		dataCompareParams.GetFixSqlDir(),
		fmt.Sprintf("fix-%d-%s", taskInfo.TaskID, taskInfo.StartTime.Format("20060102150405")),
	)

	config.Tables, config.TableCfgs, config.ChannelSchemaTablePKMap, config.SourceTableNameMap = buildCheckTablesAndTableConfigs(tables, config.SourceDBCfg[0], config.TargetDBCfg, taskTableConfigsMap)

	return config, nil

}

func buildTimsDBConfig(dbConfig *models.DBConfig) *dbutil.DBConfig {
	return &dbutil.DBConfig{
		Host: dbConfig.Host,

		Port: dbConfig.Port,

		User: dbConfig.User,

		Password: dbConfig.Password,
	}
}

func buildTableConfig(table *channel.ChannelSchemaTable, sourceDBConfig, targetDBConfig DBConfig, taskTableConfig *task.TaskTableConfig) *TableConfig {
	tableConfig := &TableConfig{}
	tableConfig.TableInstance = TableInstance{InstanceID: targetDBConfig.InstanceID, Schema: table.SchemaNameT, Table: table.TableNameT}
	if taskTableConfig.OperatorTag == "Y" {
		tableConfig.OnlyCheckColumns = strings.Split(taskTableConfig.ColumnslistTidb, ",")
	} else if taskTableConfig.OperatorTag == "N" {
		tableConfig.IgnoreColumns = strings.Split(taskTableConfig.ColumnslistTidb, ",")
	}
	tableConfig.Fields = taskTableConfig.IndexFieldsTidb
	tableConfig.Range = taskTableConfig.FilterClauseTidb
	tableConfig.OracleRange = taskTableConfig.FilterClauseOracle
	tableConfig.OracleHint = taskTableConfig.SqlhintOracle
	tableConfig.TidbHint = taskTableConfig.SqlhintTidb
	tableConfig.IsSharding = false
	tableConfig.SourceTables = []*TableInstance{{InstanceID: sourceDBConfig.InstanceID, Schema: table.SchemaNameS, Table: table.TableNameS}}
	tableConfig.Collation = ""
	return tableConfig
}

func buildCheckTablesAndTableConfigs(tables []*channel.ChannelSchemaTable, sourceDBConfig, targetDBConfig DBConfig, taskTableConfigsMap map[string]*task.TaskTableConfig) ([]*CheckTables, []*TableConfig, map[string]int, map[string]string) {
	checkTablesResults := make([]*CheckTables, 0, 0)
	tableConfigResults := make([]*TableConfig, 0, 0)
	channelSchemaTablePKMap := make(map[string]int)
	sourceTableNameMap := make(map[string]string)

	//preSchema := ""
	//var checkTables *CheckTables
	//for _, table := range tables {
	// if preSchema != table.SchemaNameT {
	//    checkTables = &CheckTables{Schema: table.SchemaNameT, OracleSchema: table.SchemaNameS, Tables: make([]string, 0, 0)}
	//    checkTablesResults = append(checkTablesResults, checkTables)
	//    preSchema = table.SchemaNameT
	// }
	// checkTables.Tables = append(checkTables.Tables, table.TableNameT)
	// if taskTableConfig, ok := taskTableConfigsMap[table.TableNameT]; ok {
	//    tableConfigResults = append(tableConfigResults, buildTableConfig(table, sourceDBConfig, targetDBConfig, taskTableConfig))
	// }
	// channelSchemaTablePKMap[table.SchemaNameT+"."+table.TableNameT] = table.ChannelSchtableId
	// sourceTableNameMap[table.SchemaNameT+"."+table.TableNameT] = table.TableNameS
	//}

	checkTableResultsMap := make(map[string]*CheckTables)
	for _, table := range tables {
		schemaName := table.SchemaNameT
		if _, ok := checkTableResultsMap[schemaName]; !ok {
			checkTableResultsMap[schemaName] = &CheckTables{Schema: schemaName, OracleSchema: table.SchemaNameS, Tables: make([]string, 0, 0)}
		}
		checkTableResultsMap[schemaName].Tables = append(checkTableResultsMap[schemaName].Tables, table.TableNameT)
		if taskTableConfig, ok := taskTableConfigsMap[table.TableNameT]; ok {
			tableConfigResults = append(tableConfigResults, buildTableConfig(table, sourceDBConfig, targetDBConfig, taskTableConfig))
		}
		channelSchemaTablePKMap[table.SchemaNameT+"."+table.TableNameT] = table.ChannelSchtableId
		sourceTableNameMap[table.SchemaNameT+"."+table.TableNameT] = table.TableNameS
	}
	checkTablesResults = lo.Values(checkTableResultsMap)

	log.Info("build check tables and table configs success.",
		zap.Int("channelSchemaTableCount", len(tables)),
		zap.Int("checkTablesResults", len(checkTablesResults)),
		zap.Int("tableConfigResults", len(tableConfigResults)),
		zap.Int("channelSchemaTablePKMap", len(channelSchemaTablePKMap)),
		zap.Int("sourceTableNameMap", len(sourceTableNameMap)),
	)
	for _, checkSchema := range checkTablesResults {
		log.Info("prepare to check schema and tables", zap.String("schema", checkSchema.Schema), zap.Strings("tables", checkSchema.Tables))
	}
	return checkTablesResults, tableConfigResults, channelSchemaTablePKMap, sourceTableNameMap
}

func buildTargetDBCfg(ctx context.Context, channelInfo *channel.ChannelInformation, dataCompareParams *structs.DataCompareTaskParam) (DBConfig, error) {
	datasourceT, datasourceTErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if datasourceTErr != nil {
		return DBConfig{}, datasourceTErr
	}
	dbConfig := DBConfig{}
	dbConfig.Host = datasourceT.HostIp
	dbConfig.Port = datasourceT.HostPort
	dbConfig.User = datasourceT.UserName
	dbConfig.Password = datasourceT.PasswordValue

	dbConfig.Type = constants.DB_TYPE_TIDB
	dbConfig.InstanceID = "target-1"

	dbConfig.Snapshot = dataCompareParams.GetTargetSnapshot()
	dbConfig.Charset = dataCompareParams.GetTargetCharset()
	return dbConfig, nil
}

func buildSourceDbCfg(ctx context.Context, channelInfo *channel.ChannelInformation, dataCompareParams *structs.DataCompareTaskParam) ([]DBConfig, error) {
	datasourceS, datasourceSErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if datasourceSErr != nil {
		return nil, datasourceSErr
	}
	datasourceT, datasourceTErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if datasourceTErr != nil {
		return nil, datasourceTErr
	}
	dbConfigs := make([]DBConfig, 0, 1)
	dbConfig := DBConfig{}
	dbConfig.Host = datasourceS.HostIp
	dbConfig.Port = datasourceS.HostPort
	dbConfig.User = datasourceS.UserName
	dbConfig.Password = datasourceS.PasswordValue
	dbConfig.ConnectString = fmt.Sprintf("oracle://@%s:%d/%s?connect_timeout=2&standaloneConnection=1&", dbConfig.Host, dbConfig.Port, datasourcepkg.GetServiceName(datasourceS))
	dbConfig.Type = constants.DB_TYPE_ORACLE
	dbConfig.InstanceID = "source-1"

	dbConfig.Snapshot = dataCompareParams.GetSourceSnapshot()
	dbConfig.OracleSCN = dataCompareParams.GetOracleScn()
	dbConfig.Charset = dataCompareParams.GetSourceCharset()

	if strings.TrimSpace(dbConfig.Charset) == "" {
		if (datasourceS.Charset == constants.DATASOURCE_S_CHARSET_ZHS16GBK || datasourceS.Charset == constants.DATASOURCE_S_CHARSET_ZHS32GB18030) &&
			(datasourceT.Charset == constants.DATASOURCE_T_CHARSET_UTF8 || datasourceT.Charset == constants.DATASOURCE_T_CHARSET_UTF8MB4) {
			dbConfig.Charset = datasourceS.Charset
		} else {
			dbConfig.Charset = ""
		}
	}
	dbConfigs = append(dbConfigs, dbConfig)
	return dbConfigs, nil
}

func buildTaskTableConfigsMap(tableConfigs []*task.TaskTableConfig) map[string]*task.TaskTableConfig {
	result := make(map[string]*task.TaskTableConfig)
	for _, tableConfig := range tableConfigs {
		result[tableConfig.TableNameT] = tableConfig
	}
	return result
}

func createSummaryForSkippedTablesWithState(ctx context.Context, cfg *Config, taskInfo *task.Task, d *Diff, errorTriggerTable string, errorTracker *ErrorTracker) {
	// Get all tables that should have been processed
	allTables := make(map[structs.SchemaTablePair]bool)
	for schema, tables := range d.tables {
		for table := range tables {
			tableKey := structs.SchemaTablePair{SchemaName: schema, TableName: table}
			allTables[tableKey] = true
		}
	}

	// Get processed tables from the report
	processedTables := make(map[structs.SchemaTablePair]bool)
	for schema, tables := range d.report.TableResults {
		for table := range tables {
			tableKey := structs.SchemaTablePair{SchemaName: schema, TableName: table}
			processedTables[tableKey] = true
		}
	}

	// Find unprocessed tables
	skippedTables := make([]structs.SchemaTablePair, 0)
	for tableKey := range allTables {
		if !processedTables[tableKey] {
			skippedTables = append(skippedTables, tableKey)
		}
	}

	if len(skippedTables) == 0 {
		return
	}

	// Create Summary records for skipped tables with appropriate states
	summaries := make([]datacompare.Summary, 0)
	nowTime := time.Now()

	for _, tableKey := range skippedTables {
		// Find channel schema table ID
		channelSchemaTablePk := 0
		if pk, ok := cfg.ChannelSchemaTablePKMap[tableKey.SchemaName+"."+tableKey.TableName]; ok {
			channelSchemaTablePk = pk
		}

		var state string
		var logMessage string
		var logLevel string

		// This table was skipped due to fast-fail triggered by another table
		state = "skipped"
		logMessage = fmt.Sprintf("t04:table[%s.%s] skipped due to fast-fail threshold exceeded by table %s", tableKey.SchemaName, tableKey.TableName, errorTriggerTable)
		logLevel = "warn"

		// Get actual chunk information from ErrorTracker
		tableName := tableKey.SchemaName + "." + tableKey.TableName
		chunkTotal := 0
		chunkErrors := 0
		if errorTracker != nil {
			if total, ok := errorTracker.TableChunkTotals[tableName]; ok {
				chunkTotal = total
			}
			if errors, ok := errorTracker.TableChunkErrors[tableName]; ok {
				chunkErrors = errors
			}
		}

		summary := datacompare.Summary{
			ChannelId:         taskInfo.ChannelId,
			TaskId:            taskInfo.TaskID,
			Schema:            tableKey.SchemaName,
			Table:             tableKey.TableName,
			ChunkNum:          chunkTotal,
			CheckFailedNum:    chunkErrors,
			State:             state,
			StartTime:         nowTime,
			UpdateTime:        nowTime,
			ChannelSchtableId: channelSchemaTablePk,
		}
		summaries = append(summaries, summary)

		// Create log entry
		dcLog := datacompare.DataCompareLog{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			LogLevel:   logLevel,
			LogMessage: logMessage,
		}
		_, createDcLogErr := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, &dcLog)
		if createDcLogErr != nil {
			log.Error("create data compare log for table failed.",
				zap.String("schemaName", tableKey.SchemaName),
				zap.String("tableName", tableKey.TableName),
				zap.String("state", state),
				zap.Int("taskId", taskInfo.TaskID),
				zap.Int("channelId", taskInfo.ChannelId))
		}
	}

	if len(summaries) > 0 {
		saveSummaryErr := models.GetDataCompareReaderWriter().SaveDataCompareSummaries(ctx, summaries)
		if saveSummaryErr != nil {
			log.Error("save data compare summaries for tables failed.",
				zap.Int("taskId", taskInfo.TaskID),
				zap.Int("channelId", taskInfo.ChannelId),
				zap.Error(saveSummaryErr))
		}
	}
}
