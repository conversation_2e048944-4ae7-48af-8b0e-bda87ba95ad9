// Copyright 2018 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// See the License for the specific language governing permissions and
// limitations under the License.

package compare

import (
	"context"
	"database/sql"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/dbutil"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/diff"
	router "gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/table-router"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/utils"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	"github.com/pingcap/errors"
	"github.com/pingcap/log"
	tidbconfig "github.com/pingcap/tidb/pkg/config"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"os"
	"regexp"
	"strings"
	"time"
)

var fixsqlWarning = "请注意！！！本程序会将空字符串 '' 与 NULL 识别为不一致的数据并进行修复，当被修复库是 TiDB 时，请再三和应用方确认是否将 TiDB 中的空字符串 '' 修复为 NULL，若无需修复，请找到这些修复语句并删除它们。（搜索关键字 NULL 确认，请注意大小写）\n请人工确认修复sql的正确性"

// Diff contains two sql DB, used for comparing.
type Diff struct {
	sourceDBs         map[string]DBConfig
	targetDB          DBConfig
	chunkSize         int
	sample            int
	checkThreadCount  int
	useChecksum       bool
	useCheckpoint     bool
	onlyUseChecksum   bool
	ignoreDataCheck   bool
	ignoreStructCheck bool
	ignoreStats       bool
	tables            map[string]map[string]*TableConfig
	//fixSQLFile                   *os.File
	fixSQLWriter                 *FixSQLWriter
	haveOutputWarning            bool
	fixTarget                    string
	EmptyStringNullCompareSwitch bool
	Ascii0Switch                 bool
	FloatTruncPrecision          int
	DoubleTruncPrecision         int
	LowCaseSchemaName            int
	LowCaseTableName             int
	CompressMode                 bool

	schemaCaseFunc func(string) string
	tableCaseFunc  func(string) string

	report         *Report
	tidbInstanceID string
	tableRouter    *router.Table
	cpDB           *sql.DB

	// DM's subtask config
	//subTaskCfgs []*config.SubTaskConfig

	ctx context.Context

	OracleSchema string
	OracleSCN    string

	//for tims
	FromTims                bool
	ChannelId               int
	TaskId                  int
	TimsDBConfig            *dbutil.DBConfig
	ChannelSchemaTablePKMap map[string]int
}

// NewDiff returns a Diff instance.
func NewDiff(ctx context.Context, cfg *Config) (diff *Diff, err error) {
	diff = &Diff{
		sourceDBs:                    make(map[string]DBConfig),
		chunkSize:                    cfg.ChunkSize,
		sample:                       cfg.Sample,
		checkThreadCount:             cfg.CheckThreadCount,
		useChecksum:                  cfg.UseChecksum,
		useCheckpoint:                cfg.UseCheckpoint,
		onlyUseChecksum:              cfg.OnlyUseChecksum,
		ignoreDataCheck:              cfg.IgnoreDataCheck,
		ignoreStructCheck:            cfg.IgnoreStructCheck,
		ignoreStats:                  cfg.IgnoreStats,
		tables:                       make(map[string]map[string]*TableConfig),
		report:                       NewReport(),
		ctx:                          ctx,
		OracleSchema:                 cfg.Tables[0].OracleSchema,
		fixTarget:                    cfg.FixTarget,
		EmptyStringNullCompareSwitch: cfg.EmptyStringNullCompareSwitch,
		Ascii0Switch:                 cfg.Ascii0Switch,
		OracleSCN:                    cfg.SourceDBCfg[0].OracleSCN,
		haveOutputWarning:            false,
		FloatTruncPrecision:          cfg.FloatTruncPrecision,
		DoubleTruncPrecision:         cfg.DoubleTruncPrecision,
		LowCaseSchemaName:            cfg.LowCaseSchemaName,
		LowCaseTableName:             cfg.LowCaseTableName,
		CompressMode:                 cfg.CompressMode,
		TimsDBConfig:                 cfg.TimsDBConfig,
		FromTims:                     cfg.FromTims,
		ChannelId:                    cfg.ChannelId,
		TaskId:                       cfg.TaskId,
		ChannelSchemaTablePKMap:      cfg.ChannelSchemaTablePKMap,
		fixSQLWriter:                 NewFixSQLWriter(cfg.FixSQLDir),
	}

	if err = diff.init(cfg); err != nil {
		diff.Close()
		return nil, errors.Trace(err)
	}

	return diff, nil
}

func (df *Diff) init(cfg *Config) (err error) {
	setTiDBCfg()

	df.SetUpInternalFunc()

	// create connection for source.
	if err = df.CreateDBConn(cfg); err != nil {
		return errors.Trace(err)
	}

	if err = df.AdjustTableConfig(cfg); err != nil {
		return errors.Trace(err)
	}

	err = os.MkdirAll(cfg.FixSQLDir, 0755)
	if err != nil {
		return errors.Trace(err)
	}

	return nil
}

// CreateDBConn creates db connections for source and target.
func (df *Diff) CreateDBConn(cfg *Config) (err error) {
	// create connection for target.
	cfg.TargetDBCfg.Conn, err = diff.CreateDB(df.ctx, cfg.TargetDBCfg.DBConfig, nil, cfg.CheckThreadCount*2)
	if err != nil {
		return errors.Errorf("create target db %s error %v", cfg.TargetDBCfg.DBConfig.String(), err)
	}
	df.targetDB = cfg.TargetDBCfg

	targetTZOffset, err := dbutil.GetTimeZoneOffset(df.ctx, cfg.TargetDBCfg.Conn)
	if err != nil {
		return errors.Annotatef(err, "fetch target db %s time zone offset failed", cfg.TargetDBCfg.DBConfig.String())
	}
	vars := map[string]string{
		"time_zone": dbutil.FormatTimeZoneOffset(targetTZOffset),
	}

	for _, source := range cfg.SourceDBCfg {
		// connect source db with target db time_zone
		source.Conn, err = diff.CreateDB(df.ctx, source.DBConfig, vars, cfg.CheckThreadCount*2)
		if err != nil {
			return errors.Annotatef(err, "create source db %s failed", source.DBConfig.String())
		}
		df.sourceDBs[source.InstanceID] = source
	}
	if df.TimsDBConfig == nil {
		df.cpDB, err = diff.CreateDBForCP(df.ctx, cfg.TargetDBCfg.DBConfig)
	} else {
		// for tims
		df.cpDB, err = diff.CreateDBForCP(df.ctx, *df.TimsDBConfig)
	}
	if err != nil {
		return errors.Errorf("create checkpoint db %s error %v", cfg.TargetDBCfg.DBConfig.String(), err)
	}

	return nil
}

// AdjustTableConfig adjusts the table's config by check-tables and table-config.
func (df *Diff) AdjustTableConfig(cfg *Config) (err error) {

	df.tableRouter, err = router.NewTableRouter(false, cfg.TableRules)
	if err != nil {
		return errors.Trace(err)
	}

	allTablesMap, err := df.GetAllTables(cfg)
	if err != nil {
		return errors.Trace(err)
	}

	// get all source table's matched target table
	// target database name => target table name => all matched source table instance
	sourceTablesMap := make(map[string]map[string][]*TableInstance)
	for instanceID, allSchemas := range allTablesMap {
		if instanceID == df.targetDB.InstanceID {
			continue
		}

		for schema, allTables := range allSchemas {
			for table := range allTables {
				targetSchema, targetTable, err := df.tableRouter.Route(schema, table)
				if err != nil {
					return errors.Errorf("get route result for %s.%s.%s failed, error %v", instanceID, schema, table, err)
				}

				if _, ok := sourceTablesMap[targetSchema]; !ok {
					sourceTablesMap[targetSchema] = make(map[string][]*TableInstance)
				}

				if _, ok := sourceTablesMap[targetSchema][targetTable]; !ok {
					sourceTablesMap[targetSchema][targetTable] = make([]*TableInstance, 0, 1)
				}
				sourceTablesMap[targetSchema][targetTable] = append(sourceTablesMap[targetSchema][targetTable], &TableInstance{
					InstanceID: instanceID,
					Schema:     schema,
					Table:      table,
				})
			}
		}
	}

	// fill the table information.
	// will add default source information, don't worry, we will use table config's info replace this later.
	for _, schemaTables := range cfg.Tables {
		log.Info("adjust table config", zap.String("schema", schemaTables.Schema), zap.Strings("tables", schemaTables.Tables), zap.Strings("exclude-tables", schemaTables.ExcludeTables))
		df.tables[schemaTables.Schema] = make(map[string]*TableConfig)
		tables := make([]string, 0, len(schemaTables.Tables))
		allTables, ok := allTablesMap[df.targetDB.InstanceID][schemaTables.Schema]
		if !ok {
			return errors.NotFoundf("schema %s.%s", df.targetDB.InstanceID, schemaTables.Schema)
		}

		for _, table := range schemaTables.Tables {
			matchedTables, err := df.GetMatchTable(df.targetDB, schemaTables.Schema, table, allTables)
			if err != nil {
				return errors.Trace(err)
			}

			//exclude those in "exclude-tables"
			for _, t := range matchedTables {
				if df.InExcludeTables(schemaTables.ExcludeTables, t) {
					continue
				} else {
					tables = append(tables, t)
				}
			}
		}
		log.Info("get matched tables", zap.String("schema", schemaTables.Schema), zap.Strings("matchedTables", tables))
		for _, tableName := range tables {
			tableInfo, err := dbutil.GetTableInfo(df.ctx, df.targetDB.Conn, schemaTables.Schema, tableName)
			if err != nil {
				return errors.Errorf("get table %s.%s's information error %s", schemaTables.Schema, tableName, errors.ErrorStack(err))
			}

			if _, ok := df.tables[schemaTables.Schema][tableName]; ok {
				log.Error("duplicate config for one table", zap.String("table", dbutil.TableName(schemaTables.Schema, tableName)))
				continue
			}

			sourceTables := make([]*TableInstance, 0, 1)
			if _, ok := sourceTablesMap[schemaTables.Schema][tableName]; ok {
				log.Info("find matched source tables", zap.Reflect("source tables", sourceTablesMap[schemaTables.Schema][tableName]), zap.String("target schema", schemaTables.Schema), zap.String("table", tableName))
				sourceTables = sourceTablesMap[schemaTables.Schema][tableName]
			} else {
				sourceTables = append(sourceTables, &TableInstance{
					InstanceID: cfg.SourceDBCfg[0].InstanceID,
					Schema:     schemaTables.OracleSchema,
					Table:      tableName,
				})
			}

			for _, sourceTable := range sourceTables {
				// 避免没有正确配置或读取table_name，导致结果为空
				sourceTableName := cfg.SourceTableNameMap[schemaTables.Schema+"."+tableName]
				if sourceTableName == "" {
					sourceTableName = sourceTable.Table
				}
				log.Info("replace source table info",
					zap.String("oldSchema", sourceTable.Schema),
					zap.String("newSchema", schemaTables.OracleSchema),
					zap.String("oldTable", sourceTable.Table),
					zap.String("newTable", sourceTableName),
				)
				sourceTable.Schema = schemaTables.OracleSchema
				sourceTable.Table = sourceTableName
			}

			df.tables[schemaTables.Schema][tableName] = &TableConfig{
				TableInstance: TableInstance{
					Schema: schemaTables.Schema,
					Table:  tableName,
				},
				TargetTableInfo:      tableInfo,
				Range:                "1=1",
				OracleRange:          "1=1",
				FloatTruncPrecision:  df.FloatTruncPrecision,
				DoubleTruncPrecision: df.DoubleTruncPrecision,
				CompressMode:         df.CompressMode,
				SourceTables:         sourceTables,
			}
		}
	}

	for _, table := range cfg.TableCfgs {
		if _, ok := df.tables[table.Schema]; !ok {
			return errors.NotFoundf("schema %s in check tables", table.Schema)
		}
		if _, ok := df.tables[table.Schema][table.Table]; !ok {
			return errors.NotFoundf("table %s.%s in check tables", table.Schema, table.Table)
		}

		sourceTables := make([]*TableInstance, 0, len(table.SourceTables))
		for _, sourceTable := range table.SourceTables {
			if _, ok := df.sourceDBs[sourceTable.InstanceID]; !ok {
				return errors.Errorf("unknown database instance id %s", sourceTable.InstanceID)
			}

			sourceTables = append(sourceTables, &TableInstance{
				InstanceID: sourceTable.InstanceID,
				Schema:     sourceTable.Schema,
				Table:      sourceTable.Table,
			})
			log.Info("modify matched source tables", zap.Reflect("source tables", sourceTables), zap.String("target schema", table.Schema), zap.String("table", table.Table))
		}

		if len(sourceTables) != 0 {
			df.tables[table.Schema][table.Table].SourceTables = sourceTables
		}
		if table.Range != "" {
			df.tables[table.Schema][table.Table].Range = table.Range
		}
		if table.OracleRange != "" {
			df.tables[table.Schema][table.Table].OracleRange = table.OracleRange
		}
		df.tables[table.Schema][table.Table].OracleHint = table.OracleHint
		if table.TidbHint != "" {
			df.tables[table.Schema][table.Table].TidbHint = table.TidbHint
		}
		if table.FloatTruncPrecision <= 0 {
			df.tables[table.Schema][table.Table].FloatTruncPrecision = df.FloatTruncPrecision
		} else {
			df.tables[table.Schema][table.Table].FloatTruncPrecision = table.FloatTruncPrecision
		}
		if table.DoubleTruncPrecision <= 0 {
			df.tables[table.Schema][table.Table].DoubleTruncPrecision = df.DoubleTruncPrecision
		} else {
			df.tables[table.Schema][table.Table].DoubleTruncPrecision = table.DoubleTruncPrecision
		}
		df.tables[table.Schema][table.Table].IgnoreColumns = table.IgnoreColumns
		df.tables[table.Schema][table.Table].OnlyCheckColumns = table.OnlyCheckColumns
		df.tables[table.Schema][table.Table].Fields = table.Fields
		df.tables[table.Schema][table.Table].Collation = table.Collation
		df.tables[table.Schema][table.Table].ChannelSchemaTablePk = table.ChannelSchemaTablePk
	}

	// we need to increase max open connections for upstream, because one chunk needs accessing N shard tables in one
	// upstream, and there are `CheckThreadCount` processing chunks. At most we need N*`CheckThreadCount` connections
	// for an upstream
	// instanceID -> max number of upstream shard tables every target table
	maxNumShardTablesOneRun := map[string]int{}
	for _, targetTables := range df.tables {
		for _, sourceCfg := range targetTables {
			upstreamCount := map[string]int{}
			for _, sourceTables := range sourceCfg.SourceTables {
				upstreamCount[sourceTables.InstanceID]++
			}
			for id, count := range upstreamCount {
				if count > maxNumShardTablesOneRun[id] {
					maxNumShardTablesOneRun[id] = count
				}
			}
		}
	}

	for instanceId, count := range maxNumShardTablesOneRun {
		db := df.sourceDBs[instanceId].Conn
		if db == nil {
			return errors.Errorf("didn't found sourceDB for instance %s", instanceId)
		}
		log.Info("will increase connection configurations for DB of instance",
			zap.String("instance id", instanceId),
			zap.Int("connection limit", count*df.checkThreadCount))
		db.SetMaxOpenConns(count * df.checkThreadCount * 2)
		db.SetMaxIdleConns(count * df.checkThreadCount * 2)
	}

	return nil
}

// GetAllTables get all tables in all databases.
func (df *Diff) GetAllTables(cfg *Config) (map[string]map[string]map[string]interface{}, error) {

	schemaCaseFunc := df.GetSchemaCaseFunc()
	tableCaseFunc := df.GetTableCaseFunc()

	log.Info("get all tables case func", zap.Int("schemaCase", df.LowCaseSchemaName), zap.Int("tableCase", df.LowCaseTableName))

	// instanceID => schema => table
	allTablesMap := make(map[string]map[string]map[string]interface{})

	allTablesMap[df.targetDB.InstanceID] = make(map[string]map[string]interface{})
	targetSchemas, err := dbutil.GetSchemas(df.ctx, df.targetDB.Conn)
	if err != nil {
		return nil, errors.Annotatef(err, "get schemas from %s", df.targetDB.InstanceID)
	}
	for _, schema := range targetSchemas {
		schema = schemaCaseFunc(schema)
		allTables, err := dbutil.GetTables(df.ctx, df.targetDB.Conn, schema)
		if err != nil {
			return nil, errors.Annotatef(err, "get tables from %s.%s", df.targetDB.InstanceID, schema)
		}
		allTables = lo.Map(allTables, func(item string, _ int) string {
			return tableCaseFunc(item)
		})
		allTablesMap[df.targetDB.InstanceID][schema] = utils.SliceToMap(allTables)
	}

	//for Oracle db source, it do not support table-rules.
	//it have no multi sources; so one target schema correspond to  one source schema
	if cfg.SourceDBCfg[0].Type == dbutil.TypeOracle {
		for _, source := range df.sourceDBs {
			allTablesMap[source.InstanceID] = make(map[string]map[string]interface{})
			for _, schema := range targetSchemas {
				schema = schemaCaseFunc(schema)
				allTablesMap[source.InstanceID][schema] = allTablesMap[df.targetDB.InstanceID][schema]
			}
		}
		return allTablesMap, nil
	}

	//for TiDB
	for _, source := range df.sourceDBs {
		allTablesMap[source.InstanceID] = make(map[string]map[string]interface{})
		sourceSchemas, err := dbutil.GetSchemas(df.ctx, source.Conn)
		if err != nil {
			return nil, errors.Annotatef(err, "get schemas from %s", source.InstanceID)
		}

		for _, schema := range sourceSchemas {
			schema = schemaCaseFunc(schema)
			allTables, err := dbutil.GetTables(df.ctx, source.Conn, schema)
			if err != nil {
				return nil, errors.Annotatef(err, "get tables from %s.%s", source.InstanceID, schema)
			}
			allTables = lo.Map(allTables, func(item string, _ int) string {
				return tableCaseFunc(item)
			})
			allTablesMap[source.InstanceID][schema] = utils.SliceToMap(allTables)
		}
	}

	return allTablesMap, nil
}

// GetMatchTable returns all the matched table.
func (df *Diff) GetMatchTable(db DBConfig, schema, table string, allTables map[string]interface{}) ([]string, error) {
	tableNames := make([]string, 0, 1)

	if table[0] == '~' {
		tableRegex := regexp.MustCompile(fmt.Sprintf("(?i)%s", table[1:]))
		for tableName := range allTables {
			if !tableRegex.MatchString(tableName) {
				continue
			}
			tableNames = append(tableNames, tableName)
		}
	} else {
		if _, ok := allTables[table]; ok {
			tableNames = append(tableNames, table)
		} else {
			return nil, errors.Errorf("%s.%s not found in %s", schema, table, db.InstanceID)
		}
	}

	return tableNames, nil
}

// Close closes file and database connection.
func (df *Diff) Close() {
	if df.fixSQLWriter != nil {
		df.fixSQLWriter.Close()
	}

	for _, db := range df.sourceDBs {
		if db.Conn != nil {
			db.Conn.Close()
		}
	}

	if df.targetDB.Conn != nil {
		df.targetDB.Conn.Close()
	}

	if df.cpDB != nil {
		df.cpDB.Close()
	}
}

// Equal tests whether two database have same data and schema.
//func (df *Diff) Equal() (err error) {
//	defer df.Close()
//
//	for schemaName, schema := range df.tables {
//
//		var tables []string
//		for _, sourceTable := range schema {
//			tables = append(tables, sourceTable.Table)
//		}
//
//		log.Info("check target schema", zap.String("schemaName", schemaName), zap.Reflect("tables", tables))
//		for _, table := range schema {
//			sourceTables := make([]*diff.TableInstance, 0, len(table.SourceTables))
//			for _, sourceTable := range table.SourceTables {
//				sourceTableInstance := &diff.TableInstance{
//					Conn:       df.sourceDBs[sourceTable.InstanceID].Conn,
//					Schema:     sourceTable.Schema,
//					Table:      sourceTable.Table,
//					InstanceID: sourceTable.InstanceID,
//					DBType:     df.sourceDBs[sourceTable.InstanceID].Type,
//					Charset:    df.sourceDBs[sourceTable.InstanceID].Charset,
//				}
//				sourceTables = append(sourceTables, sourceTableInstance)
//			}
//
//			targetTableInstance := &diff.TableInstance{
//				Conn:       df.targetDB.Conn,
//				Schema:     table.Schema,
//				Table:      table.Table,
//				InstanceID: df.targetDB.InstanceID,
//				DBType:     df.targetDB.DBConfig.Type,
//				Charset:    df.targetDB.DBConfig.Charset,
//			}
//
//			// find tidb instance for getting statistical information to split chunk
//			var tidbStatsSource *diff.TableInstance
//			//ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
//			//defer cancel()
//			ctx := context.Background()
//
//			if !df.ignoreStats {
//				log.Info("use tidb stats to split chunks", zap.String("table", targetTableInstance.Table))
//				isTiDB, err := dbutil.IsTiDB(ctx, targetTableInstance.Conn)
//				if err != nil {
//					log.Warn("judge instance is tidb failed", zap.Error(err), zap.String("table", targetTableInstance.Table))
//				} else if isTiDB {
//					tidbStatsSource = targetTableInstance
//				} else if len(sourceTables) == 1 {
//					isTiDB, err := dbutil.IsTiDB(ctx, sourceTables[0].Conn)
//					if err != nil {
//						log.Warn("judge instance is tidb failed", zap.Error(err), zap.String("table", targetTableInstance.Table))
//					} else if isTiDB {
//						tidbStatsSource = sourceTables[0]
//					}
//				}
//			} else {
//				log.Info("ignore tidb stats because of user setting", zap.String("table", targetTableInstance.Table))
//			}
//
//			td := &diff.TableDiff{
//				SourceTables: sourceTables,
//				TargetTable:  targetTableInstance,
//
//				IgnoreColumns:    table.IgnoreColumns,
//				OnlyCheckColumns: table.OnlyCheckColumns,
//
//				Fields:                       table.Fields,
//				Range:                        table.Range,
//				OracleRange:                  table.OracleRange,
//				OracleHint:                   table.GetOracleHint(),
//				TidbHint:                     table.TidbHint,
//				Collation:                    table.Collation,
//				FloatTruncPrecision:          table.FloatTruncPrecision,
//				DoubleTruncPrecision:         table.DoubleTruncPrecision,
//				CompressMode:                 table.CompressMode,
//				ChunkSize:                    df.chunkSize,
//				Sample:                       df.sample,
//				CheckThreadCount:             df.checkThreadCount,
//				UseChecksum:                  df.useChecksum,
//				UseCheckpoint:                df.useCheckpoint,
//				OnlyUseChecksum:              df.onlyUseChecksum,
//				IgnoreStructCheck:            df.ignoreStructCheck,
//				IgnoreDataCheck:              df.ignoreDataCheck,
//				TiDBStatsSource:              tidbStatsSource,
//				CpDB:                         df.cpDB,
//				FixTarget:                    df.fixTarget,
//				EmptyStringNullCompareSwitch: df.EmptyStringNullCompareSwitch,
//				Ascii0Switch:                 df.Ascii0Switch,
//				OracleSCN:                    df.OracleSCN,
//				FromTims:                     df.FromTims,
//				ChannelId:                    df.ChannelId,
//				TaskId:                       df.TaskId,
//			}
//
//			if pk, ok := df.ChannelSchemaTablePKMap[targetTableInstance.Schema+"."+targetTableInstance.Table]; ok {
//				td.ChannelSchemaTablePk = pk
//			} else {
//				return errors.Trace(errors.New(fmt.Sprintf("table[%s] have not channel schema table id.", targetTableInstance.Table))), false
//			}
//			log.Info("check target table", zap.Reflect("table ", targetTableInstance.Table),
//				zap.Reflect("connection pool stat", sourceTables[0].Conn.Stats()))
//			if df.FromTims {
//				dcLog := &datacompare.DataCompareLog{ChannelId: td.ChannelId, TaskId: td.TaskId, LogMessage: fmt.Sprintf("t04:comparing table[%s.%s]", targetTableInstance.Schema, targetTableInstance.Table)}
//				_, createDcLogErr := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog)
//				if createDcLogErr != nil {
//					log.Error("start compare, create data compare log to db failed.", zap.String("table", targetTableInstance.Table), zap.Int("taskId", td.TaskId), zap.Int("channelId", td.ChannelId))
//				}
//			}
//			structEqual, dataEqual, err := td.Equal(df.ctx, func(dml diff.FixSQL) error {
//				var err error
//				if df.haveOutputWarning {
//					err = df.fixSQLWriter.WriteToString(dml, "")
//				} else {
//					err = df.fixSQLWriter.WriteToString(dml, fixsqlWarning)
//					df.haveOutputWarning = true
//				}
//				return errors.Trace(err)
//			})
//			log.Info("check target table finish", zap.String("table", dbutil.TableName(table.Schema, table.Table)),
//				zap.Bool("struct equal", structEqual), zap.Bool("data equal", dataEqual), zap.Error(err))
//
//			if err != nil {
//				log.Error("check failed", zap.String("table", dbutil.TableName(table.Schema, table.Table)), zap.Error(err))
//				df.report.SetTableMeetError(table.Schema, table.Table, err)
//				df.report.FailedNum++
//				if df.FromTims {
//					dcLog := &datacompare.DataCompareLog{ChannelId: td.ChannelId, TaskId: td.TaskId, LogMessage: fmt.Sprintf("t04:compare table[%s.%s] failed.", targetTableInstance.Schema, targetTableInstance.Table)}
//					_, createDcLogErr := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog)
//					if createDcLogErr != nil {
//						log.Error("check table failed. create data compare log to db failed.", zap.String("table", targetTableInstance.Table), zap.Int("taskId", td.TaskId), zap.Int("channelId", td.ChannelId))
//					}
//				}
//				continue
//			}
//			if df.FromTims {
//				dcLog := &datacompare.DataCompareLog{ChannelId: td.ChannelId, TaskId: td.TaskId, LogMessage: fmt.Sprintf("t04:compare table[%s.%s] finish", targetTableInstance.Schema, targetTableInstance.Table)}
//				_, createDcLogErr := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog)
//				if createDcLogErr != nil {
//					log.Error("check table finish. create data compare log to db failed.", zap.String("table", targetTableInstance.Table), zap.Int("taskId", td.TaskId), zap.Int("channelId", td.ChannelId))
//				}
//			}
//			df.report.SetTableStructCheckResult(table.Schema, table.Table, structEqual)
//			df.report.SetTableDataCheckResult(table.Schema, table.Table, dataEqual)
//
//			if structEqual && dataEqual {
//				df.report.PassNum++
//			} else {
//				df.report.FailedNum++
//			}
//		}
//	}
//
//	return
//}

// checkOverallTableErrorThreshold checks if the overall table error threshold has been exceeded
// and returns an appropriate error if it has.
func checkOverallTableErrorThreshold(errorTracker *ErrorTracker, tableName string) (error, string) {
	if exceeded, errorPercentage := errorTracker.CheckOverallTableErrorThreshold(); exceeded {
		log.Error("Fast-fail triggered because the current table's error pushed the overall error percentage above the threshold",
			zap.String("table", tableName),
			zap.Float64("errorPercentage", errorPercentage),
			zap.Float64("threshold", errorTracker.Thresholds.OverallTableErrorThreshold))
		return errors.Errorf("Fast-fail triggered: The error from table %s caused the overall table error percentage (%.2f%%) to exceed the threshold (%.2f%%)",
			tableName, errorPercentage, errorTracker.Thresholds.OverallTableErrorThreshold), tableName
	}
	return nil, ""
}

// checkTableChunkFailThreshold checks if any table chunk fail threshold has been exceeded
// and returns an appropriate error if it has.
func checkTableChunkFailThreshold(errorTracker *ErrorTracker) (error, string, string) {
	if exceeded, tableName, errorPercentage, reason := errorTracker.CheckAnyTableChunkFailThreshold(); exceeded {
		log.Error("Table chunk fail threshold exceeded, marking table as failed",
			zap.String("table", tableName),
			zap.Float64("errorPercentage", errorPercentage),
			zap.String("reason", reason))
		
		var errorMsg string
		if reason == "table-chunk-fail-count" {
			errorMsg = fmt.Sprintf("Table %s failed due to table-chunk-fail-count (%d chunks failed)", 
				tableName, errorTracker.TableChunkErrors[tableName])
		} else {
			errorMsg = fmt.Sprintf("Table %s failed due to table-chunk-fail-threshold (%.2f%% > %.2f%%)",
				tableName, errorPercentage, errorTracker.Thresholds.TableChunkFailThreshold)
		}
		
		return errors.New(errorMsg), tableName, reason
	}
	return nil, "", ""
}

// createSourceTableInstances creates source table instances from table configuration
func (df *Diff) createSourceTableInstances(table *TableConfig) []*diff.TableInstance {
	sourceTables := make([]*diff.TableInstance, 0, len(table.SourceTables))
	for _, sourceTable := range table.SourceTables {
		sourceTableInstance := &diff.TableInstance{
			Conn:       df.sourceDBs[sourceTable.InstanceID].Conn,
			Schema:     sourceTable.Schema,
			Table:      sourceTable.Table,
			InstanceID: sourceTable.InstanceID,
			DBType:     df.sourceDBs[sourceTable.InstanceID].Type,
			Charset:    df.sourceDBs[sourceTable.InstanceID].Charset,
		}
		sourceTables = append(sourceTables, sourceTableInstance)
	}
	return sourceTables
}

// createTargetTableInstance creates target table instance from table configuration
func (df *Diff) createTargetTableInstance(table *TableConfig) *diff.TableInstance {
	return &diff.TableInstance{
		Conn:       df.targetDB.Conn,
		Schema:     table.Schema,
		Table:      table.Table,
		InstanceID: df.targetDB.InstanceID,
		DBType:     df.targetDB.DBConfig.Type,
		Charset:    df.targetDB.DBConfig.Charset,
	}
}

// determineTiDBStatsSource determines which table instance to use for TiDB statistics
func (df *Diff) determineTiDBStatsSource(ctx context.Context, targetTableInstance *diff.TableInstance, sourceTables []*diff.TableInstance) *diff.TableInstance {
	if df.ignoreStats {
		log.Info("ignore tidb stats because of user setting", zap.String("table", targetTableInstance.Table))
		return nil
	}

	log.Info("use tidb stats to split chunks", zap.String("table", targetTableInstance.Table))

	// Check if target is TiDB
	if isTiDB, err := dbutil.IsTiDB(ctx, targetTableInstance.Conn); err != nil {
		log.Warn("judge instance is tidb failed", zap.Error(err), zap.String("table", targetTableInstance.Table))
	} else if isTiDB {
		return targetTableInstance
	}

	// Check if single source is TiDB
	if len(sourceTables) == 1 {
		if isTiDB, err := dbutil.IsTiDB(ctx, sourceTables[0].Conn); err != nil {
			log.Warn("judge instance is tidb failed", zap.Error(err), zap.String("table", targetTableInstance.Table))
		} else if isTiDB {
			return sourceTables[0]
		}
	}

	return nil
}

// buildTableDiffConfig creates and configures a TableDiff instance
func (df *Diff) buildTableDiffConfig(table *TableConfig, sourceTables []*diff.TableInstance, targetTableInstance *diff.TableInstance, tidbStatsSource *diff.TableInstance, errorTracker *ErrorTracker) (*diff.TableDiff, error) {
	td := &diff.TableDiff{
		SourceTables: sourceTables,
		TargetTable:  targetTableInstance,

		IgnoreColumns:    table.IgnoreColumns,
		OnlyCheckColumns: table.OnlyCheckColumns,

		Fields:                       table.Fields,
		Range:                        table.Range,
		OracleRange:                  table.OracleRange,
		OracleHint:                   table.GetOracleHint(),
		TidbHint:                     table.TidbHint,
		Collation:                    table.Collation,
		FloatTruncPrecision:          table.FloatTruncPrecision,
		DoubleTruncPrecision:         table.DoubleTruncPrecision,
		CompressMode:                 table.CompressMode,
		ChunkSize:                    df.chunkSize,
		Sample:                       df.sample,
		CheckThreadCount:             df.checkThreadCount,
		UseChecksum:                  df.useChecksum,
		UseCheckpoint:                df.useCheckpoint,
		OnlyUseChecksum:              df.onlyUseChecksum,
		IgnoreStructCheck:            df.ignoreStructCheck,
		IgnoreDataCheck:              df.ignoreDataCheck,
		TiDBStatsSource:              tidbStatsSource,
		CpDB:                         df.cpDB,
		FixTarget:                    df.fixTarget,
		EmptyStringNullCompareSwitch: df.EmptyStringNullCompareSwitch,
		Ascii0Switch:                 df.Ascii0Switch,
		OracleSCN:                    df.OracleSCN,
		FromTims:                     df.FromTims,
		ChannelId:                    df.ChannelId,
		TaskId:                       df.TaskId,
		ErrorTracker:                 toDiffErrorTracker(errorTracker),
	}

	// Set channel schema table PK
	if pk, ok := df.ChannelSchemaTablePKMap[targetTableInstance.Schema+"."+targetTableInstance.Table]; ok {
		td.ChannelSchemaTablePk = pk
	} else {
		return nil, errors.Trace(errors.New(fmt.Sprintf("table[%s] have not channel schema table id.", targetTableInstance.Table)))
	}

	return td, nil
}

// logTIMSTableStart logs the start of table comparison for TIMS
func (df *Diff) logTIMSTableStart(ctx context.Context, td *diff.TableDiff, targetTableInstance *diff.TableInstance) {
	if !df.FromTims {
		return
	}
	dcLog := &datacompare.DataCompareLog{
		ChannelId:  td.ChannelId,
		TaskId:     td.TaskId,
		LogLevel:   "info",
		LogMessage: fmt.Sprintf("t04:comparing table[%s.%s]", targetTableInstance.Schema, targetTableInstance.Table),
	}
	if _, err := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog); err != nil {
		log.Error("start compare, create data compare log to db failed.",
			zap.String("table", targetTableInstance.Table),
			zap.Int("taskId", td.TaskId),
			zap.Int("channelId", td.ChannelId))
	}
}

// logTIMSTableError logs table comparison error for TIMS
func (df *Diff) logTIMSTableError(ctx context.Context, td *diff.TableDiff, targetTableInstance *diff.TableInstance) {
	if !df.FromTims {
		return
	}
	dcLog := &datacompare.DataCompareLog{
		ChannelId:  td.ChannelId,
		TaskId:     td.TaskId,
		LogLevel:   "info",
		LogMessage: fmt.Sprintf("t04:compare table[%s.%s] failed.", targetTableInstance.Schema, targetTableInstance.Table),
	}
	if _, err := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog); err != nil {
		log.Error("check table failed. create data compare log to db failed.",
			zap.String("table", targetTableInstance.Table),
			zap.Int("taskId", td.TaskId),
			zap.Int("channelId", td.ChannelId))
	}
}

// logTIMSTableFinish logs the completion of table comparison for TIMS
func (df *Diff) logTIMSTableFinish(ctx context.Context, td *diff.TableDiff, targetTableInstance *diff.TableInstance) {
	if !df.FromTims {
		return
	}
	dcLog := &datacompare.DataCompareLog{
		ChannelId:  td.ChannelId,
		TaskId:     td.TaskId,
		LogLevel:   "info",
		LogMessage: fmt.Sprintf("t04:compare table[%s.%s] finish", targetTableInstance.Schema, targetTableInstance.Table),
	}
	if _, err := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog); err != nil {
		log.Error("check table finish. create data compare log to db failed.",
			zap.String("table", targetTableInstance.Table),
			zap.Int("taskId", td.TaskId),
			zap.Int("channelId", td.ChannelId))
	}
}

// logTIMSTableFastFailTriggered logs when fast-fail is triggered for TIMS
func (df *Diff) logTIMSTableFastFailTriggered(ctx context.Context, td *diff.TableDiff, targetTableInstance *diff.TableInstance, triggerErr error) {
	if !df.FromTims {
		return
	}
	dcLog := &datacompare.DataCompareLog{
		ChannelId:  td.ChannelId,
		TaskId:     td.TaskId,
		LogLevel:   "error",
		LogMessage: fmt.Sprintf("t05:table[%s.%s], %v", targetTableInstance.Schema, targetTableInstance.Table, triggerErr),
	}
	if _, err := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog); err != nil {
		log.Error("table fast-fail triggered. create data compare log to db failed.",
			zap.String("table", targetTableInstance.Table),
			zap.Int("taskId", td.TaskId),
			zap.Int("channelId", td.ChannelId))
	}
}

// syncChunkErrorsFromTableDiff synchronizes chunk error statistics from TableDiff to ErrorTracker
func syncChunkErrorsFromTableDiff(td *diff.TableDiff, errorTracker *ErrorTracker) {
	if td.ErrorTracker == nil {
		return
	}
	errors, totals := td.ErrorTracker.GetTableChunkStats()
	for tableName, errorCount := range errors {
		errorTracker.TableChunkErrors[tableName] = errorCount
	}
	for tableName, totalCount := range totals {
		errorTracker.TableChunkTotals[tableName] = totalCount
	}
}

// ensureTableSummaryRecord ensures that a table has a Summary record before processing starts
func ensureTableSummaryRecord(ctx context.Context, df *Diff, table *TableConfig) {
	// Find channel schema table ID
	channelSchemaTablePk := 0
	if pk, ok := df.ChannelSchemaTablePKMap[table.Schema+"."+table.Table]; ok {
		channelSchemaTablePk = pk
	}

	// Create or update Summary record with initial state
	summary := datacompare.Summary{
		ChannelId:         df.ChannelId,
		TaskId:            df.TaskId,
		Schema:            table.Schema,
		Table:             table.Table,
		ChunkNum:          0, // Will be updated when chunks are split
		CheckFailedNum:    0,
		State:             "not_checked",
		StartTime:         time.Now(),
		UpdateTime:        time.Now(),
		ChannelSchtableId: channelSchemaTablePk,
	}

	// Use SaveDataCompareSummaries to ensure record exists
	summaries := []datacompare.Summary{summary}
	err := models.GetDataCompareReaderWriter().SaveDataCompareSummaries(ctx, summaries)
	if err != nil {
		log.Warn("failed to ensure table summary record",
			zap.String("schema", table.Schema),
			zap.String("table", table.Table),
			zap.Error(err))
	}
}

// updateTableSummaryForError updates the Summary record when a table encounters an error
func updateTableSummaryForError(ctx context.Context, df *Diff, table *TableConfig, tableErr error, errorTracker *ErrorTracker) {
	// Get chunk information from ErrorTracker if available
	tableName := table.Schema + "." + table.Table
	// The sync_diff_inspector ErrorTracker doesn't have GetTableChunkInfo method
	// So we'll set default values for early error cases
	chunkErrors := 0
	chunkTotals := 0
	if errorTracker != nil {
		if errors, ok := errorTracker.TableChunkErrors[tableName]; ok {
			chunkErrors = errors
		}
		if totals, ok := errorTracker.TableChunkTotals[tableName]; ok {
			chunkTotals = totals
		}
	}

	// Create error Summary record
	summary := datacompare.Summary{
		ChannelId:         df.ChannelId,
		TaskId:            df.TaskId,
		Schema:            table.Schema,
		Table:             table.Table,
		ChunkNum:          chunkTotals, // Use actual chunk total or 0 if not set
		CheckFailedNum:    chunkErrors, // Use actual error count
		State:             "error",
		StartTime:         time.Now(),
		UpdateTime:        time.Now(),
		ChannelSchtableId: df.ChannelSchemaTablePKMap[table.Schema+"."+table.Table],
		// Note: Summary struct doesn't have Message field, error info logged separately
	}

	// Update Summary record
	summaries := []datacompare.Summary{summary}
	err := models.GetDataCompareReaderWriter().SaveDataCompareSummaries(ctx, summaries)
	if err != nil {
		log.Warn("failed to update table summary for error",
			zap.String("schema", table.Schema),
			zap.String("table", table.Table),
			zap.Error(err))
	}

	log.Info("updated summary for error table",
		zap.String("schema", table.Schema),
		zap.String("table", table.Table),
		zap.Int("chunkTotals", chunkTotals),
		zap.Int("chunkErrors", chunkErrors),
		zap.String("errorMessage", tableErr.Error()))
}

// Equal tests whether two database have same data and schema with fast-fail support.
func (df *Diff) Equal(ctx context.Context, errorTracker *ErrorTracker) *diff.DiffResult {
	defer df.Close()

	for schemaName, schema := range df.tables {
		var tables []string
		for _, sourceTable := range schema {
			tables = append(tables, sourceTable.Table)
		}

		log.Info("check target schema", zap.String("schemaName", schemaName), zap.Reflect("tables", tables))
		for _, table := range schema {
			// Ensure table has Summary record at the beginning
			ensureTableSummaryRecord(ctx, df, table)

			currentTable := table.Schema + "." + table.Table

			// Create table instances
			sourceTables := df.createSourceTableInstances(table)
			targetTableInstance := df.createTargetTableInstance(table)
			tidbStatsSource := df.determineTiDBStatsSource(ctx, targetTableInstance, sourceTables)

			// Build TableDiff configuration
			td, err := df.buildTableDiffConfig(table, sourceTables, targetTableInstance, tidbStatsSource, errorTracker)
			if err != nil {
				return &diff.DiffResult{
					Err:               err,
					FastFailTriggered: false,
					TriggerTable:      "",
					TriggerReason:     "",
				}
			}
			log.Info("check target table", zap.Reflect("table ", targetTableInstance.Table),
				zap.Reflect("connection pool stat", sourceTables[0].Conn.Stats()))

			// Log table comparison start for TIMS
			df.logTIMSTableStart(ctx, td, targetTableInstance)

			var updateSQLFunc = func(dml diff.FixSQL) error {
				var err error
				if df.haveOutputWarning {
					err = df.fixSQLWriter.WriteToString(dml, "")
				} else {
					err = df.fixSQLWriter.WriteToString(dml, fixsqlWarning)
					df.haveOutputWarning = true
				}
				return errors.Trace(err)
			}

			// 核心代码，校验数据是否一致
			structEqual, dataEqual, err := td.Equal(df.ctx, updateSQLFunc)
			log.Info("check target table finish", zap.String("table", currentTable),
				zap.Bool("struct equal", structEqual), zap.Bool("data equal", dataEqual), zap.Error(err))

			if err != nil {
				log.Error("check failed", zap.String("table", currentTable), zap.Error(err))
				df.report.SetTableMeetError(table.Schema, table.Table, err)
				df.report.FailedNum++

				errorTracker.AddTableError()
				df.logTIMSTableError(ctx, td, targetTableInstance)

				// Update Summary record for the failed table
				updateTableSummaryForError(ctx, df, table, err, errorTracker)

				// Check if the overall table error threshold has been exceeded
				if triggerErr, triggerTable := checkOverallTableErrorThreshold(errorTracker, currentTable); triggerErr != nil {
					df.logTIMSTableFastFailTriggered(ctx, td, targetTableInstance, triggerErr)
					return &diff.DiffResult{
						Err:               nil,
						FastFailTriggered: true,
						TriggerTable:      triggerTable,
						TriggerReason:     triggerErr.Error(),
					}
				}
				continue
			}

			// Log table comparison completion for TIMS
			df.logTIMSTableFinish(ctx, td, targetTableInstance)
			df.report.SetTableStructCheckResult(table.Schema, table.Table, structEqual)
			df.report.SetTableDataCheckResult(table.Schema, table.Table, dataEqual)

			if structEqual && dataEqual {
				df.report.PassNum++
			} else {
				df.report.FailedNum++
			}

			// Sync back chunk errors from diffErrorTracker to errorTracker BEFORE any fast-fail checks
			syncChunkErrorsFromTableDiff(td, errorTracker)

			if !structEqual || !dataEqual {
				errorTracker.AddTableError()
			}

			// Check if current table chunk fail threshold has been exceeded
			// Note: We no longer trigger fast-fail for table chunk failures, just log and continue
			if err, triggerTable, reason := checkTableChunkFailThreshold(errorTracker); err != nil {
				log.Info("Table chunk fail threshold exceeded, but continuing with next table",
					zap.String("triggerTable", triggerTable),
					zap.String("reason", reason),
					zap.String("errorMessage", err.Error()))
				
				// Create detailed log entry for the failed table
				dcLog := &datacompare.DataCompareLog{
					ChannelId:  df.ChannelId,
					TaskId:     df.TaskId,
					LogLevel:   "error",
					LogMessage: fmt.Sprintf("t04:%s", err.Error()),
				}
				_, createDcLogErr := models.GetDataCompareReaderWriter().CreateDataCompareLog(ctx, dcLog)
				if createDcLogErr != nil {
					log.Error("create data compare log for table chunk failure failed.",
						zap.String("triggerTable", triggerTable),
						zap.Int("taskId", df.TaskId),
						zap.Int("channelId", df.ChannelId))
				}
				// Continue processing next table instead of triggering fast-fail
			}

			// Check if the overall table error threshold has been exceeded
			if triggerErr, triggerTable := checkOverallTableErrorThreshold(errorTracker, currentTable); triggerErr != nil {
				df.logTIMSTableFastFailTriggered(ctx, td, targetTableInstance, triggerErr)
				return &diff.DiffResult{
					Err:               nil,
					FastFailTriggered: true,
					TriggerTable:      triggerTable,
					TriggerReason:     triggerErr.Error(),
				}
			}
		}
	}

	return &diff.DiffResult{
		Err:               nil,
		FastFailTriggered: false,
		TriggerTable:      "",
		TriggerReason:     "",
	}
}

// toDiffErrorTracker 将 compare.ErrorTracker 转换为 diff.ErrorTracker。
// 这个辅助函数封装了转换逻辑，使代码更清晰、更易于维护。
func toDiffErrorTracker(tracker *ErrorTracker) *diff.ErrorTracker {
	if tracker == nil {
		return nil
	}

	// 初始化并复制 tableChunkErrors
	chunkErrors := make(map[string]int, len(tracker.TableChunkErrors))
	for k, v := range tracker.TableChunkErrors {
		chunkErrors[k] = v
	}

	// 初始化并复制 tableChunkTotals
	chunkTotals := make(map[string]int, len(tracker.TableChunkTotals))
	for k, v := range tracker.TableChunkTotals {
		chunkTotals[k] = v
	}

	return &diff.ErrorTracker{
		TotalTables:      tracker.TotalTables,
		MissingTables:    tracker.MissingTables,
		TablesWithErrors: tracker.TablesWithErrors,
		TableChunkErrors: chunkErrors,
		TableChunkTotals: chunkTotals,
		Thresholds: &diff.FastFailThresholds{
			MissingTablesThreshold:     tracker.Thresholds.MissingTablesThreshold,
			TableChunkFailThreshold:    tracker.Thresholds.TableChunkFailThreshold,
			OverallTableErrorThreshold: tracker.Thresholds.OverallTableErrorThreshold,
			TableChunkFailMinCount:     tracker.Thresholds.TableChunkFailMinCount,
		},
	}
}

// Judge if a table is in "exclude-tables" list
func (df *Diff) InExcludeTables(exclude_tables []string, table string) bool {
	for _, exclude_table := range exclude_tables {
		if strings.EqualFold(exclude_table, table) {
			return true
		}
	}
	return false
}

func (df *Diff) SetUpInternalFunc() {
	switch df.LowCaseTableName {
	case 1:
		df.tableCaseFunc = strings.ToLower
	case 2:
		df.tableCaseFunc = strings.ToUpper
	default:
		df.tableCaseFunc = func(s string) string { return s }
	}

	switch df.LowCaseSchemaName {
	case 1:
		df.schemaCaseFunc = strings.ToLower
	case 2:
		df.schemaCaseFunc = strings.ToUpper
	default:
		df.schemaCaseFunc = func(s string) string { return s }
	}
}

func (df *Diff) GetSchemaCaseFunc() func(string) string {
	if df.schemaCaseFunc == nil {
		return strings.ToLower
	}
	return df.schemaCaseFunc
}

func (df *Diff) GetTableCaseFunc() func(string) string {
	if df.tableCaseFunc == nil {
		return strings.ToLower
	}
	return df.tableCaseFunc
}

func setTiDBCfg() {
	// to support long index key in TiDB
	tidbCfg := tidbconfig.GetGlobalConfig()
	// 3027 * 4 is the max value the MaxIndexLength can be set
	tidbCfg.MaxIndexLength = tidbconfig.DefMaxOfMaxIndexLength
	tidbCfg.Experimental.AllowsExpressionIndex = true
	tidbconfig.StoreGlobalConfig(tidbCfg)

	log.Info("set tidb cfg")
}
