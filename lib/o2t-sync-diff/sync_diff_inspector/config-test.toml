# Diff Configuration.

######################### Global config #########################

log-level = "debug"
#log-level = "info"

# for example, the whole data is [1...100]
# we can split these data to [1...10], [11...20], ..., [91...100]
# the [1...10] is a chunk, and it's chunk size is 10
# size of the split chunk
chunk-size = 1

# how many goroutines are created to check data
check-thread-count = 1

# sampling check percent, for example 10 means only check 10% data
sample-percent = 100

# calculate the data's checksum, and compare data by checksum.
# set false if want to comapre the data directly
use-checksum = true

# set true if just want compare data by checksum, will skip select data when checksum is not equal.
only-use-checksum = false

# set true will continue check from the latest checkpoint
use-checkpoint = true

# ignore check table's data
ignore-data-check = false

# ignore check table's struct, for Oracle db source ,it must be true
ignore-struct-check = true

# the name of the file which saves sqls used to fix different data.
fix-sql-file = "fix-tidb.sql"

##fix db type, surpport Oracle, Tidb. default value is Tidb
#fix-target = "Tidb"

######################### Tables config #########################

# uncomment this if comparing data with different database name or table name
# for oracle db, it should not be setup
#[[table-rules]]
#schema-pattern = "test_*"
#table-pattern = "t_*"
#target-schema = "test"
#target-table = "t"


# tables need to check.
[[check-tables]]
# schema name in target database.
schema = "sl-dev"
oracle-schema = "devuser"
# table list which need check in target database.
#    tables = ["user_demo", "user_demo_1", "user_demo_2", "user_demo_3", "user_demo_4", "user_demo_6","user_demo_7", "user_demo_8","user_demo_9", "user_demo_10"]
#    tables = ["user_demo_10"]
#    tables = ["T6"]
#    tables = ["t_scn"]
#    tables = ["t1"]
tables = ["t11"]
# support regular expression, must start with '~'.
# for example, this config will check tables with prefix 'test'.
#         tables = ["~^user_demo*"]

# tables that should be exclude when checked
#    exclude-tables = ["a_table", "should_not_compare"]

# schema and table in table-config must be contained in check-tables.
# a example for comparing table with same schema and table name.
#[[table-config]]
#    # schema name.
#    schema = "LJSUSER"
#
#    # table name.
#    table = "user_demo"
#
#    # field should be the primary key, unique key or field with index.
#    # if comment this, diff will find a suitable field.
#    #    index-fields = "id"
#
#    # can set multiple fields split by ','
#    # index-fields = "id,age"
#
#    # check data's range.
#    #    range = "id > 0 AND id < 10"
#    #    oracle-range = "id > 0 AND id < 10"
#    # set true if comparing sharding tables with target table
##    is-sharding = false
#
#    # collation config in mysql/tidb, should corresponding to charset.
#    #     collation = "latin1_bin"
#
#[[table-config]]
#    #    # schema name.
#    schema = "LJSUSER"
#    #
#    #    # table name.
#    table = "user_demo_1"
#    #
#    #    # field should be the primary key, unique key or field with index.
#    #    # if comment this, diff will find a suitable field.
#    ##    index-fields = "id"
#    #
#    #    # can set multiple fields split by ','
#    #    # index-fields = "id,age"
#    #
#    #    # check data's range.
#    #    range = "birthday >= '2000-10-01 11:00:00' AND birthday <= '2000-10-01 12:00:00'"
#    #    oracle-range = "birthday >= to_date('2000-10-01 11:00:00','yyyy-mm-dd hh24:mi:ss') AND birthday <= to_date('2000-10-01 12:00:00','yyyy-mm-dd hh24:mi:ss')"
#    #
#    #    # set true if comparing sharding tables with target table
##    is-sharding = false
#
#    # collation config in mysql/tidb, should corresponding to charset.
#    #     collation = "latin1_bin"
#
#[[table-config]]
#    # schema name.
#    schema = "LJSUSER"
#
#    # table name.
#    table = "user_demo_2"
#    # set true if comparing sharding tables with target table
##    is-sharding = false
#
#[[table-config]]
#    # schema name.
#    schema = "LJSUSER"
#
#    # table name.
#    table = "user_demo_3"
#    # set true if comparing sharding tables with target table
##    is-sharding = false
#[[table-config]]
#     ##schema name.
#    schema = "LJSUSER"
#
#    ##table name.
#    table = "user_demo_10"
##    ignore-columns = ["telephone", "address", "id", "name", "balance"]
#    ignore-columns = ["c2"]
##    ignore-columns = ["c1","c2"]
##    only-check-columns = ["c1","c2"]
##    only-check-columns = ["a"]
##     set true if comparing sharding tables with target table
##    is-sharding = false



######################### Databases config #########################

[[source-db]]
type = "Oracle"
user = "devuser"
password = "devuser"
connectString = "************:1521/utf8?connect_timeout=2"
instance-id = "source-1"
#oracle-scn = "2062847460"

[target-db]
#如果不配置type缺省是Tidb
type = "Tidb"
host = "************"
port = 4900
user = "tidb"
password = "tidb@1234"
instance-id = "target-1"
#snapshot = "432155286607495170"
