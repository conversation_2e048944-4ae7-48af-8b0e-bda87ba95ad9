package compare

import (
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/diff"
	"github.com/pingcap/log"
	"go.uber.org/zap"
	"os"
	"path/filepath"
	"sync"
)

type SchemaTablePair struct {
	SchemaName string
	TableName  string
}

func (i SchemaTablePair) String() string {
	return i.SchemaName + "." + i.TableName
}

func NewFixSQLWriter(fixSQLDir string) *FixSQLWriter {
	return &FixSQLWriter{
		FixSQLDir: fixSQLDir,
		files:     make(map[SchemaTablePair]*os.File),
		onceMap:   make(map[SchemaTablePair]*sync.Once),
	}
}

type FixSQLWriter struct {
	FixSQLDir string

	mu      sync.Mutex
	files   map[SchemaTablePair]*os.File
	onceMap map[SchemaTablePair]*sync.Once
}

func (i *FixSQLWriter) WriteToString(fixSQL diff.FixSQL, warning string) error {
	i.mu.Lock()
	// Generate file key
	schemaTableKey := SchemaTablePair{SchemaName: fixSQL.SchemaName, TableName: fixSQL.TableName}
	fixSQLFile := fmt.Sprintf("%s.%s.sql", fixSQL.SchemaName, fixSQL.TableName)

	// Ensure sync.Once exists for this file
	if _, exists := i.onceMap[schemaTableKey]; !exists {
		i.onceMap[schemaTableKey] = &sync.Once{}
	}

	// Open file if not already opened
	i.onceMap[schemaTableKey].Do(func() {
		filePath := filepath.Join(i.FixSQLDir, fixSQLFile)
		file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			log.Error("failed to open file", zap.String("filePath", filePath), zap.Error(err))
			return
		}
		i.files[schemaTableKey] = file
	})

	// Get the file handle
	file, ok := i.files[schemaTableKey]
	i.mu.Unlock()

	// If file handle is found, write the SQL
	if ok {
		i.mu.Lock()
		defer i.mu.Unlock()

		var writeErr error
		if warning == "" {
			_, writeErr = file.WriteString(fmt.Sprintf("%s\n\n", fixSQL.SQL))
		} else {
			_, writeErr = file.WriteString(fmt.Sprintf("%s\n\n%s\n\n", warning, fixSQL.SQL))
		}
		if writeErr != nil {
			return fmt.Errorf("failed to write to file: %w", writeErr)
		}

	} else {
		return fmt.Errorf("file handler for %s not found", schemaTableKey.String())
	}

	return nil
}

func (i *FixSQLWriter) Close() error {
	i.mu.Lock()
	defer i.mu.Unlock()

	var closeErr error

	// Iterate through all open files and close them
	for key, file := range i.files {
		if err := file.Close(); err != nil {
			fmt.Printf("Failed to close file %s: %v\n", key, err)
			// Store the first encountered error
			if closeErr == nil {
				closeErr = err
			}
		}
		delete(i.files, key) // Remove the file from the map after closing
	}

	return closeErr
}
