# diff Configuration.

######################### Global config #########################

log-level = "info"

# for example, the whole data is [1...100]
# we can split these data to [1...10], [11...20], ..., [91...100]
# the [1...10] is a chunk, and it's chunk size is 10
# size of the split chunk
chunk-size = 1000

# how many goroutines are created to check data
check-thread-count = 4

# sampling check percent, for example 10 means only check 10% data
sample-percent = 100

# calculate the data's checksum, and compare data by checksum.
use-checksum = true

# the name of the file which saves sqls used to fix different data
fix-sql-file = "fix.sql"

######################### Tables config #########################

# tables need to check.
[[check-tables]]
    # schema name in target database.
    schema = "test"

    # table list which need check in target database. 
    # in sharding mode, you must set config for every table in table-config, otherwise will not check the table.
    tables = ["test"]


# schema and table in check-tables must be contained in check-tables.
# a example for sharding tables.
[[table-config]]
    # target schema name.
    schema = "test"

    # target table name.
    table = "test"

    # field should be the primary key, unique key or field with index. 
    # if comment this, diff will find a suitable field.
    # index-field = "id"

    # check data's range.
    # range = "age > 10 AND age < 20"

    # set true if comparing sharding tables with target table
    is-sharding = true

    # source tables.
    [[table-config.source-tables]]
        instance-id = "source-1"
        schema = "test"
        table  = "test1"

    [[table-config.source-tables]]
        instance-id = "source-1"
        schema = "test"
        table  = "test2"

    [[table-config.source-tables]]
        instance-id = "source-2"
        schema = "test"
        table  = "test3"

######################### Databases config #########################

[[source-db]]
    host = "127.0.0.1"
    port = 3306
    user = "root"
    password = ""
    instance-id = "source-1"
    # remove comment if use tidb's snapshot data
    # snapshot = "2016-10-08 16:45:26"

[[source-db]]
    host = "*********"
    port = 3306
    user = "root"
    password = ""
    instance-id = "source-2"
    # remove comment if use tidb's snapshot data
    # snapshot = "2016-10-08 16:45:26"

[target-db]
    host = "127.0.0.1"
    port = 4000
    user = "root"
    password = ""
    # remove comment if use tidb's snapshot data
    # snapshot = "2016-10-08 16:45:26"