# Diff Configuration.

######################### Global config #########################

log-level = "info"

# for example, the whole data is [1...100]
# we can split these data to [1...10], [11...20], ..., [91...100]
# the [1...10] is a chunk, and it's chunk size is 10
# size of the split chunk
chunk-size = 1000

# how many goroutines are created to check data
check-thread-count = 4

# sampling check percent, for example 10 means only check 10% data
sample-percent = 100

# calculate the data's checksum, and compare data by checksum.
# set false if want to comapre the data directly
use-checksum = true

# set true if just want compare data by checksum, will skip select data when checksum is not equal.
only-use-checksum = false

# set true will continue check from the latest checkpoint
use-checkpoint = true

# ignore check table's data
ignore-data-check = false

# ignore check table's struct
ignore-struct-check = false

# the name of the file which saves sqls used to fix different data.
fix-sql-file = "fix.sql"


######################### Tables config #########################

# uncomment this if comparing data with different database name or table name
#[[table-rules]]
    #schema-pattern = "test_*"
    #table-pattern = "t_*"
    #target-schema = "test"
    #target-table = "t"


# tables need to check.
[[check-tables]]
    # schema name in target database.
    schema = "test"

    # table list which need check in target database.
    tables = ["test1", "test2", "test3"]

    # support regular expression, must start with '~'.
    # for example, this config will check tables with prefix 'test'.
    # tables = ["~^test.*"]

    # tables that should be exclude when checked
    exclude-tables = ["a_table", "should_not_compare"]

# schema and table in table-config must be contained in check-tables.
# a example for comparing table with same schema and table name.
[[table-config]]
    # schema name.
    schema = "test"

    # table name.
    table = "test3"

    # field should be the primary key, unique key or field with index.
    # if comment this, diff will find a suitable field.
    index-fields = "id"

    # can set multiple fields split by ','
    # index-fields = "id,age"

    # check data's range.
    range = "age > 10 AND age < 20"

    # set true if comparing sharding tables with target table
    is-sharding = false

    # collation config in mysql/tidb, should corresponding to charset.
    # collation = "latin1_bin"

# a example for comparing table with different name.
[[table-config]]
    # target schema name.
    schema = "test"

    # target table name.
    table = "test2"

    # set true if comparing sharding tables with target table
    is-sharding = false

    # columns be ignored, will not check this column's data
    # ignore-columns = ["name"]

    # source table.
    [[table-config.source-tables]]
        instance-id = "source-1"
        schema = "test"
        table  = "test1"

######################### Databases config #########################

[[source-db]]
    host = "127.0.0.1"
    port = 3306
    user = "root"
    password = ""
    instance-id = "source-1"
    # remove comment if use tidb's snapshot data
    # snapshot = "2016-10-08 16:45:26"

[target-db]
    host = "127.0.0.1"
    port = 4000
    user = "root"
    password = ""
    instance-id = "target-1"
    # remove comment if use tidb's snapshot data
    # snapshot = "2016-10-08 16:45:26"
