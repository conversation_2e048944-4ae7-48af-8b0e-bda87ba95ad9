```mermaid
graph TD
    A["🚀 Start Data Compare Task"] --> B["📋 Build Data Compare Parameters"];
    B --> C["⚙️ Initialize ErrorTracker<br/>四个快速失败阈值:<br/>• missing-tables-threshold (default 100%)<br/>• table-chunk-fail-threshold (default 100%)<br/>• overall-table-error-threshold (default 100%)<br/>• table-chunk-fail-count (default 0)"];
    C --> D["📊 Get Table Status from DataSource<br/>分类为: Exist, OracleNotExist, TiDBNotExist"];
    D --> E["📈 Calculate Missing Tables Statistics<br/>errorTracker.TotalTables = len(allTables)<br/>errorTracker.MissingTables = len(missingTables)"];
    E --> E1{"🔍 Missing Tables % > missing-tables-threshold?"};
    E1 -- "Yes: 快速失败" --> E2["💾 Save Missing Tables to Summary & Chunks<br/>State: 'invalid' (Oracle不存在)<br/>State: 'failed' (TiDB不存在)"];
    E2 --> E3["📝 Create DataCompareLog: Missing Tables Fast-Fail"];
    E3 --> F["❌ Task Status: FAILED (Fast-Fail)"];
    E1 -- "No: 继续执行" --> E4["💾 Save Missing Tables to Summary & Chunks<br/>State: 'invalid'/'failed' 同上"];
    E4 --> G["🔄 Iterate Through Existing Tables"];
    
    G --> G1["📝 Ensure Table Summary Record<br/>State: 'not_checked'<br/>ChunkNum: 0, CheckFailedNum: 0"];
    G1 --> G2["🏗️ Create Table Instances & Build TableDiff Config"];
    G2 --> G3["📝 Log TIMS Table Start"];
    G3 --> I["🔍 Equal() - Compare Table Data & Structure"];
    
    I --> I0["🔄 Start Chunk Processing"];
    I0 --> I0a{"🔍 Check table-chunk-fail thresholds<br/>before each chunk submission?"};
    I0a -- "Yes: 快速失败" --> I0b["⏹️ Stop chunk submission & processing"];
    I0b --> I3["💾 Update Table Summary<br/>State: 'failed'<br/>ChunkNum: processed chunks<br/>CheckFailedNum: failed chunks"];
    I0a -- "No: 继续处理" --> I0c["🔄 Process chunks concurrently"];
    I0c --> I0d{"🔍 Check table-chunk-fail thresholds<br/>after each chunk result?"};
    I0d -- "Yes: 快速失败" --> I0e["⏹️ Stop chunk processing early"];
    I0e --> I3;
    I0d -- "No: 继续处理" --> I1{"❌ Table Comparison Error?"};
    I1 -- "Yes: 表比较出错" --> I2["📊 errorTracker.AddTableError()<br/>Increment TablesWithErrors"];
    I2 --> I3["💾 Update Table Summary<br/>State: 'error'<br/>ChunkNum: actual chunks<br/>CheckFailedNum: actual errors"];
    I3 --> I4["📝 Log TIMS Table Error"];
    I4 --> I5{"📈 Overall Table Error % > overall-table-error-threshold?"};
    I5 -- "Yes: 快速失败" --> I6["📝 Log TIMS Fast-Fail Triggered"];
    I6 --> I7["💾 Create Summary for Skipped Tables<br/>State: 'skipped'"];
    I7 --> F;
    I5 -- "No: 继续下一个表" --> G;
    
    I1 -- "No: 表比较成功" --> I8["📝 Log TIMS Table Finish"];
    I8 --> I9["📊 Set Report Results (struct/data equal)"];
    I9 --> I10["🔄 Sync Chunk Errors from TableDiff to ErrorTracker"];
    I10 --> I11{"📊 Table Failed (struct!=equal OR data!=equal)?"};
    I11 -- "Yes" --> I12["📊 errorTracker.AddTableError()"];
    I12 --> I13{"📈 Any Table Chunk Fail % > table-chunk-fail-threshold<br/>OR Chunk Errors >= table-chunk-fail-count?"};
    I11 -- "No" --> I13;
    I13 -- "Yes: 快速失败" --> I14["💾 Create Summary for Skipped Tables<br/>State: 'skipped'"];
    I14 --> F;
    I13 -- "No: 继续" --> I15{"📈 Overall Table Error % > overall-table-error-threshold?"};
    I15 -- "Yes: 快速失败" --> I16["📝 Log TIMS Fast-Fail Triggered"];
    I16 --> I17["💾 Create Summary for Skipped Tables<br/>State: 'skipped'"];
    I17 --> F;
    I15 -- "No: 继续下一个表" --> G;
    
    G -- "✅ All Tables Processed" --> O["📊 Final Task Status Update"];
    O --> P["🏁 End Data Compare Task"];
    F --> P;
    
    style A fill:#e1f5fe
    style F fill:#ffebee
    style P fill:#f3e5f5
    style C fill:#fff3e0
    style E2 fill:#e8f5e8
    style E4 fill:#e8f5e8
    style G1 fill:#e8f5e8
    style I3 fill:#e8f5e8
    style I7 fill:#e8f5e8
    style I14 fill:#e8f5e8
    style I17 fill:#e8f5e8
```
