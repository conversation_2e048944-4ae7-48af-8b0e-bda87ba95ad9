package dbutil

import (
	"database/sql"
	"gitee.com/pingcap_enterprise/tms/util/database"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"strings"

	"github.com/pingcap/errors"
)

// ScanRowsToInterfaces scans rows to interface arrary.
func ScanRowsToInterfaces(rows *sql.Rows) ([][]any, error) {
	var rowsData [][]any
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	for rows.Next() {
		colVals := make([]any, len(cols))

		err = rows.Scan(colVals...)
		if err != nil {
			return nil, errors.Trace(err)
		}
		rowsData = append(rowsData, colVals)
	}

	return rowsData, nil
}

// ColumnData saves column's data.
type ColumnData struct {
	Data   []byte
	IsNull bool
}

// ScanRow scans rows into a map.
func ScanRow(rows *sql.Rows) (map[string]*ColumnData, error) {
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	colVals := make([][]byte, len(cols))
	colValsI := make([]any, len(colVals))
	for i := range colValsI {
		colValsI[i] = &colVals[i]
	}

	err = rows.Scan(colValsI...)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result := make(map[string]*ColumnData)
	for i := range colVals {
		data := &ColumnData{
			Data:   colVals[i],
			IsNull: colVals[i] == nil,
		}
		result[cols[i]] = data
	}

	return result, nil
}

// UpperCaseKeyScanRow scan rows into a map, key is upper case.
func UpperCaseKeyScanRow(rows *sql.Rows) (map[string]*ColumnData, error) {
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	columnTypes, getErr := rows.ColumnTypes()
	if getErr != nil {
		return nil, errors.Trace(getErr)
	}

	specialColumnResult := database.ExtractSpecialColumnTypes(columnTypes)

	colVals := make([][]byte, len(cols))
	anyVals := make([]any, len(cols))

	colValsI := make([]any, len(colVals))
	for i := range colValsI {
		if specialColumnResult.IsGoDrorJSON(i) {
			colValsI[i] = &anyVals[i]
		} else {
			colValsI[i] = &colVals[i]
		}
	}

	err = rows.Scan(colValsI...)
	if err != nil {
		return nil, errors.Trace(err)
	}

	result := make(map[string]*ColumnData)
	for i := range colVals {
		var data *ColumnData
		if specialColumnResult.IsGoDrorJSON(i) {
			byteData, strErr := stringutil.GetStringFromAny(anyVals[i])
			if strErr != nil {
				return nil, errors.Trace(strErr)
			}
			data = &ColumnData{
				Data:   []byte(byteData),
				IsNull: anyVals[i] == nil,
			}
		} else {
			data = &ColumnData{
				Data:   colVals[i],
				IsNull: colVals[i] == nil,
			}
		}
		//oracle return rows by upper case column names, but tidb/mysql is lower case.
		// so we need unified format
		result[strings.ToUpper(cols[i])] = data
	}

	return result, nil
}
