package dbutil

import (
	"github.com/pingcap/tidb/pkg/meta/model"
	"github.com/pingcap/tidb/pkg/parser/mysql"
)

// IsNumberType returns true if tp is number type
func IsNumberType(tp byte) bool {
	switch tp {
	case mysql.TypeTiny, mysql.TypeShort, mysql.TypeLong, mysql.TypeLonglong, mysql.TypeInt24, mysql.TypeYear:
		return true
	}

	return false
}

// IsFloatOrDoubleOrDecimalType returns true if tp is float type or double type or decimal type
func IsFloatOrDoubleOrDecimalType(tp byte) bool {
	switch tp {
	case mysql.TypeFloat, mysql.TypeDouble, mysql.TypeNewDecimal:
		return true
	}

	return false
}

func IsDecimalType(tp byte) bool {
	switch tp {
	case mysql.TypeNewDecimal:
		return true
	}

	return false
}

func IsFloatOrDoubleType(tp byte) bool {
	switch tp {
	case mysql.TypeFloat, mysql.TypeDouble:
		return true
	}

	return false
}

func IsFloatType(tp byte) bool {
	switch tp {
	case mysql.TypeFloat:
		return true
	}
	return false
}

func IsDoubleType(tp byte) bool {
	switch tp {
	case mysql.TypeDouble:
		return true
	}
	return false
}

// IsTimeTypeAndNeedDecode returns true if tp is time type and encoded in tidb buckets.
func IsTimeTypeAndNeedDecode(tp byte) bool {
	if tp == mysql.TypeDatetime || tp == mysql.TypeTimestamp || tp == mysql.TypeDate {
		return true
	}
	return false
}

func IsTimeType(tp byte) bool {
	if tp == mysql.TypeDatetime || tp == mysql.TypeTimestamp || tp == mysql.TypeDate || tp == mysql.TypeYear || tp == mysql.TypeNewDate {
		return true
	}
	return false
}

func IsDateTimeOrTimestampType(tp byte) bool {
	if tp == mysql.TypeDatetime || tp == mysql.TypeTimestamp {
		return true
	}
	return false
}

func IsCharType(tp byte) bool {
	if tp == mysql.TypeString {
		return true
	}
	return false
}

func IsBiggerThan128(v int) bool {
	return v > 128
}

func IsNumberOrFloatType(tp byte) bool {
	return IsNumberType(tp) || IsFloatOrDoubleOrDecimalType(tp)
}

func IsStringType(tp byte) bool {
	if tp == mysql.TypeVarchar || tp == mysql.TypeVarString || tp == mysql.TypeString {
		return true
	}
	return false
}

func IsJSONType(tp byte) bool {
	if tp == mysql.TypeJSON {
		return true
	}
	return false
}

func IsBlobType(tp byte) bool {
	if tp == mysql.TypeTinyBlob || tp == mysql.TypeMediumBlob || tp == mysql.TypeLongBlob || tp == mysql.TypeBlob {
		return true
	}
	return false
}

func IsBinaryBlobType(col *model.ColumnInfo) bool {
	tp := col.GetType()
	if !IsBlobType(tp) {
		return false
	}
	return col.GetCharset() == "binary"
}
