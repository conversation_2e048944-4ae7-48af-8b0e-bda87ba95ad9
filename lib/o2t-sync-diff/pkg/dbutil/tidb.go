package dbutil

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/pingcap/errors"
)

// GetTidbTableEstimateRows returns the estimate number of rows in the table.
func GetTidbTableEstimateRows(ctx context.Context, db *sql.DB, schemaName, tableName string) (uint64, error) {
	var rows uint64
	scanErr := db.QueryRowContext(ctx, "select table_rows from information_schema.tables t where table_schema=? and table_name=?;", schemaName, tableName).Scan(&rows)
	if scanErr != nil {
		return 0, errors.Trace(scanErr)
	}
	return rows, nil
}

// GetTidbTableActualRows returns the actual number of rows in the table.
func GetTidbTableActualRows(ctx context.Context, db *sql.DB, schemaName, tableName string) (uint64, error) {
	var rows uint64
	scanErr := db.QueryRowContext(ctx, fmt.Sprintf("select count(1) from %s.%s", schemaName, tableName)).Scan(&rows)
	if scanErr != nil {
		return 0, errors.Trace(scanErr)
	}
	return rows, nil
}
