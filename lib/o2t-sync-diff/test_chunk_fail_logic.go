package main

import (
	"fmt"
	compare "gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/sync_diff_inspector"
)

func main() {
	// Test the new OR logic for table chunk fail thresholds
	thresholds := &compare.FastFailThresholds{
		TableChunkFailThreshold: 50.0, // 50% threshold
		TableChunkFailMinCount:  5,    // 5 chunks minimum
	}
	
	errorTracker := compare.NewErrorTracker(thresholds)
	
	// Test Case 1: Min count reached but percentage not (should trigger with OR logic)
	errorTracker.TableChunkErrors["table1"] = 5  // exactly min count
	errorTracker.TableChunkTotals["table1"] = 20 // 25% error rate (below threshold)
	
	exceeded, percentage, reason := errorTracker.CheckTableChunkFailThreshold("table1")
	fmt.Printf("Test Case 1 - Min count trigger:\n")
	fmt.Printf("  Exceeded: %t, Percentage: %.2f%%, Reason: %s\n", exceeded, percentage, reason)
	fmt.Printf("  Expected: true, 25.00%%, table-chunk-fail-count\n\n")
	
	// Test Case 2: Percentage reached but min count not (should trigger with OR logic)
	errorTracker.TableChunkErrors["table2"] = 3  // below min count
	errorTracker.TableChunkTotals["table2"] = 5  // 60% error rate (above threshold)
	
	exceeded, percentage, reason = errorTracker.CheckTableChunkFailThreshold("table2")
	fmt.Printf("Test Case 2 - Percentage threshold trigger:\n")
	fmt.Printf("  Exceeded: %t, Percentage: %.2f%%, Reason: %s\n", exceeded, percentage, reason)
	fmt.Printf("  Expected: true, 60.00%%, table-chunk-fail-threshold\n\n")
	
	// Test Case 3: Neither condition met (should not trigger)
	errorTracker.TableChunkErrors["table3"] = 3  // below min count
	errorTracker.TableChunkTotals["table3"] = 20 // 15% error rate (below threshold)
	
	exceeded, percentage, reason = errorTracker.CheckTableChunkFailThreshold("table3")
	fmt.Printf("Test Case 3 - No trigger:\n")
	fmt.Printf("  Exceeded: %t, Percentage: %.2f%%, Reason: %s\n", exceeded, percentage, reason)
	fmt.Printf("  Expected: false, 15.00%%, (empty)\n\n")
	
	// Test Case 4: Both conditions met (should trigger)
	errorTracker.TableChunkErrors["table4"] = 6  // above min count
	errorTracker.TableChunkTotals["table4"] = 10 // 60% error rate (above threshold)
	
	exceeded, percentage, reason = errorTracker.CheckTableChunkFailThreshold("table4")
	fmt.Printf("Test Case 4 - Both conditions met:\n")
	fmt.Printf("  Exceeded: %t, Percentage: %.2f%%, Reason: %s\n", exceeded, percentage, reason)
	fmt.Printf("  Expected: true, 60.00%%, table-chunk-fail-count (min count checked first)\n")
}