# TMS Data Compare Fast-Fail 机制详细文档

## 概述

TMS (Table Migration System) 数据比较模块实现了完整的快速失败（Fast-Fail）机制，通过四个可配置的阈值参数，在数据比较过程中及早检测到致命错误时立即终止任务，避免资源浪费并提供快速反馈。

## 快速失败参数

### 1. missing-tables-threshold (缺失表阈值)
- **类型**: `float64` (百分比: 0-100)
- **默认值**: `100` (禁用快速失败)
- **功能**: 当缺失表的百分比超过此阈值时触发快速失败
- **计算公式**: `(缺失表数量 / 总表数量) × 100`
- **触发时机**: 在表状态检查阶段，获取到 Oracle 和 TiDB 中的表信息后立即检查

**缺失表分类**:
- **Oracle 不存在**: 配置的表在 Oracle 中不存在
- **TiDB 不存在**: 配置的表在 TiDB 中不存在

### 2. table-chunk-fail-threshold (表块失败阈值)
- **类型**: `float64` (百分比: 0-100)
- **默认值**: `100` (禁用快速失败)
- **功能**: 当任意表的块失败率超过此阈值时触发快速失败
- **计算公式**: `(表的失败块数量 / 表的总块数量) × 100`
- **触发时机**: 每个表比较完成后检查

### 3. overall-table-error-threshold (整体表错误阈值)
- **类型**: `float64` (百分比: 0-100)
- **默认值**: `100` (禁用快速失败)
- **功能**: 当整体表错误率超过此阈值时触发快速失败
- **计算公式**: `(错误表数量 / 总表数量) × 100`
- **触发时机**: 每个表比较出错后或比较完成后检查

### 4. table-chunk-fail-count (表块失败计数)
- **类型**: `int`
- **默认值**: `0` (禁用)
- **功能**: 当任意表的失败块数量达到此数值时触发快速失败
- **触发条件**: 与table-chunk-fail-threshold参数是OR关系，任一条件满足即触发
- **作用**: 提供基于绝对数量的fast-fail机制，适用于对失败块数量有严格要求的场景

## 数据库状态管理

### Summary 表状态
Summary 表记录每个表的比较状态和统计信息：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `ChannelId` | int | 通道ID |
| `TaskId` | int | 任务ID |
| `Schema` | string | 模式名 |
| `Table` | string | 表名 |
| `ChunkNum` | int | 总块数量 |
| `CheckSuccessNum` | int | 成功检查的块数量 |
| `CheckFailedNum` | int | 失败检查的块数量 |
| `CheckIgnoreNum` | int | 忽略检查的块数量 |
| `State` | string | 表状态 |
| `StartTime` | datetime | 开始时间 |
| `UpdateTime` | datetime | 更新时间 |
| `ChannelSchtableId` | int | 通道表配置ID |

**State 字段可能的值**:
- `not_checked`: 初始状态，表已创建但尚未开始比较
- `invalid`: 表在 Oracle 中不存在
- `failed`: 表在 TiDB 中不存在或比较过程中出错
- `skipped`: 由于快速失败被跳过
- `success`: 比较成功
- `error`: 比较过程中出现错误

### Chunk 表状态
Chunk 表记录每个数据块的比较详情：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `ChunkId` | int | 块ID |
| `InstanceId` | string | 实例ID |
| `Schema` | string | 模式名 |
| `Table` | string | 表名 |
| `Range` | text | 数据范围条件 |
| `State` | string | 块状态 |
| `Message` | text | 错误或状态消息 |
| `ChannelId` | int | 通道ID |
| `TaskId` | int | 任务ID |
| `UpdateTime` | datetime | 更新时间 |

**State 字段可能的值**:
- `invalid`: 表不存在
- `failed`: 数据比较失败
- `success`: 数据比较成功

## 快速失败流程

### 1. 初始化阶段
```go
// 从配置参数构建快速失败阈值
thresholds := &FastFailThresholds{
    MissingTablesThreshold:     dataCompareParams.GetMissingTablesThreshold(),
    TableChunkFailThreshold:    dataCompareParams.GetTableChunkFailThreshold(),
    OverallTableErrorThreshold: dataCompareParams.GetOverallTableErrorThreshold(),
    TableChunkFailCount:        dataCompareParams.GetTableChunkFailCount(),
}

// 创建错误跟踪器
errorTracker := NewErrorTracker(thresholds)
```

### 2. 缺失表检查
```go
// 计算总表数和缺失表数
totalTables := len(existTables) + len(oracleNotExistTables) + len(tidbNotExistTables)
missingTables := len(oracleNotExistTables) + len(tidbNotExistTables)
errorTracker.TotalTables = totalTables
errorTracker.MissingTables = missingTables

// 检查缺失表阈值
if errorTracker.CheckMissingTablesThreshold() {
    // 触发快速失败
    taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
    taskInfo.ErrorDetail = fmt.Sprintf("Fast-fail triggered: Missing tables percentage (%.2f%%) exceeds threshold (%.2f%%)",
        missingPercentage, thresholds.MissingTablesThreshold)
    
    // 保存缺失表到 Summary 和 Chunk 表
    saveNotExistTablesToSummaryAndChunks(ctx, taskInfo, oracleNotExistTables, tidbNotExistTables)
    return
}
```

### 3. 表比较过程中的检查

#### 表错误处理
```go
if err != nil {
    // 记录表错误
    errorTracker.AddTableError()
    
    // 更新 Summary 记录
    updateTableSummaryForError(ctx, df, table, err, errorTracker)
    
    // 检查整体表错误阈值
    if triggerErr, triggerTable := checkOverallTableErrorThreshold(errorTracker, currentTable); triggerErr != nil {
        // 触发快速失败
        return &diff.DiffResult{
            FastFailTriggered: true,
            TriggerTable:      triggerTable,
            TriggerReason:     triggerErr.Error(),
        }
    }
}
```

#### 表块失败检查 (增强版)
```go
// 同步块错误统计
syncChunkErrorsFromTableDiff(td, errorTracker)

// 检查表块失败阈值 (在表比较完成后)
if err, triggerTable := checkTableChunkFailThreshold(errorTracker); err != nil {
    return &diff.DiffResult{
        FastFailTriggered: true,
        TriggerTable:      triggerTable,
        TriggerReason:     err.Error(),
    }
}
```

#### Chunk级别的Fast-Fail检查 (新增)
```go
// 在chunk提交循环中检查fast-fail
for i, chunk := range chunks {
    // 检查是否已超过阈值，停止提交新的chunk
    if exceeded, errorPercentage, reason := t.ErrorTracker.CheckTableChunkFailThreshold(tableName); exceeded {
        log.Warn("table chunk fail threshold exceeded, stopping chunk submission",
            zap.String("table", tableName),
            zap.Float64("errorPercentage", errorPercentage),
            zap.String("reason", reason))
        return // 停止提交新的chunk
    }
    
    // 提交chunk进行处理
    pool.Apply(func() { t.newCheckChunksDataEqual(ctx, chunk, resultCh) })
}

// 在chunk结果处理循环中检查fast-fail
for {
    select {
    case eq := <-checkResultCh:
        // 处理chunk结果后检查是否超过阈值
        if exceeded, errorPercentage, reason := t.ErrorTracker.CheckTableChunkFailThreshold(tableName); exceeded {
            log.Warn("table chunk fail threshold exceeded, stopping chunk processing",
                zap.String("table", tableName),
                zap.Float64("errorPercentage", errorPercentage),
                zap.String("reason", reason))
            break CheckResult // 立即退出处理循环
        }
    }
}
```

### 4. 跳过表处理
当快速失败触发时，系统会为所有未处理的表创建 Summary 记录：

```go
func createSummaryForSkippedTablesWithState(ctx context.Context, cfg *Config, taskInfo *task.Task, d *Diff, errorTriggerTable string, errorTracker *ErrorTracker) {
    // 找出所有未处理的表
    skippedTables := findUnprocessedTables(allTables, processedTables)
    
    // 为每个跳过的表创建 Summary 记录
    for _, tableKey := range skippedTables {
        summary := datacompare.Summary{
            State:             "skipped",
            ChannelId:         taskInfo.ChannelId,
            TaskId:            taskInfo.TaskID,
            Schema:            tableKey.SchemaName,
            Table:             tableKey.TableName,
            ChunkNum:          chunkTotal,
            CheckFailedNum:    chunkErrors,
            StartTime:         nowTime,
            UpdateTime:        nowTime,
            ChannelSchtableId: channelSchemaTablePk,
        }
        summaries = append(summaries, summary)
    }
    
    // 批量保存 Summary 记录
    models.GetDataCompareReaderWriter().SaveDataCompareSummaries(ctx, summaries)
}
```

## 错误跟踪器实现

### ErrorTracker 结构
```go
type ErrorTracker struct {
    TotalTables      int                    // 总表数量
    MissingTables    int                    // 缺失表数量
    TablesWithErrors int                    // 有错误的表数量
    TableChunkErrors map[string]int         // 每个表的块错误数量
    TableChunkTotals map[string]int         // 每个表的总块数量
    Thresholds       *FastFailThresholds    // 阈值配置
}
```

### 关键方法
```go
// 检查缺失表阈值
func (et *ErrorTracker) CheckMissingTablesThreshold() bool {
    if et.TotalTables == 0 {
        return false
    }
    missingPercentage := float64(et.MissingTables) / float64(et.TotalTables) * 100
    return missingPercentage > et.Thresholds.MissingTablesThreshold
}

// 检查表块失败阈值 (OR逻辑)
func (et *ErrorTracker) CheckTableChunkFailThreshold(tableName string) (bool, float64, string) {
    chunkErrors := et.TableChunkErrors[tableName]
    chunkTotals := et.TableChunkTotals[tableName]
    
    // 检查失败块数量阈值 (OR条件1)
    if et.Thresholds.TableChunkFailCount > 0 && chunkErrors >= et.Thresholds.TableChunkFailCount {
        return true, float64(chunkErrors)/float64(chunkTotals)*100, "table-chunk-fail-count"
    }
    
    if chunkTotals == 0 {
        return false, 0, ""
    }
    
    // 检查失败块百分比阈值 (OR条件2)
    errorPercentage := float64(chunkErrors) / float64(chunkTotals) * 100
    if errorPercentage > et.Thresholds.TableChunkFailThreshold {
        return true, errorPercentage, "table-chunk-fail-threshold"
    }
    
    return false, errorPercentage, ""
}

// 检查整体表错误阈值
func (et *ErrorTracker) CheckOverallTableErrorThreshold() (bool, float64) {
    if et.TotalTables == 0 {
        return false, 0
    }
    errorPercentage := float64(et.TablesWithErrors) / float64(et.TotalTables) * 100
    return errorPercentage > et.Thresholds.OverallTableErrorThreshold, errorPercentage
}
```

## 日志记录

### DataCompareLog 表
快速失败机制会在关键节点记录日志：

```go
type DataCompareLog struct {
    LogId      int       `gorm:"primarykey"`
    ChannelId  int       `gorm:"comment:channel id"`
    TaskId     int       `gorm:"comment:task id"`
    LogMessage string    `gorm:"type:varchar(1000);comment:message"`
    LogLevel   string    `gorm:"type:varchar(20);comment:log level"`
    CreatedAt  time.Time `gorm:"<-:create"`
}
```

### 日志级别和消息格式
- **info**: 正常流程日志（表开始比较、完成比较）
- **warn**: 警告日志（表被跳过）
- **error**: 错误日志（表比较失败、快速失败触发）

**消息格式示例**:
- `t04:comparing table[schema.table]` - 开始比较表
- `t04:compare table[schema.table] finish` - 完成比较表
- `t04:compare table[schema.table] failed.` - 表比较失败
- `t05:table[schema.table], Fast-fail triggered: ...` - 快速失败触发
- `t04:table[schema.table] skipped due to fast-fail threshold exceeded by table trigger_table` - 表被跳过

## 配置示例

### 启用所有快速失败检查
```toml
[data-compare-params]
missing-tables-threshold = 10.0        # 缺失表超过10%时失败
table-chunk-fail-threshold = 50.0      # 任意表块失败率超过50%时失败
overall-table-error-threshold = 20.0   # 整体表错误率超过20%时失败
table-chunk-fail-count = 5             # 任意表失败块数量达到5个时失败 (OR条件)
```

### 仅启用缺失表检查
```toml
[data-compare-params]
missing-tables-threshold = 5.0         # 缺失表超过5%时失败
table-chunk-fail-threshold = 100.0     # 禁用表块失败检查
overall-table-error-threshold = 100.0  # 禁用整体错误检查
table-chunk-fail-count = 0             # 禁用块计数检查
```

### 禁用所有快速失败检查
```toml
[data-compare-params]
missing-tables-threshold = 100.0       # 禁用
table-chunk-fail-threshold = 100.0     # 禁用
overall-table-error-threshold = 100.0  # 禁用
table-chunk-fail-count = 0             # 禁用
```

## 最佳实践

### 1. 阈值设置建议
- **开发环境**: 设置较低的阈值（5-10%）以快速发现配置问题
- **测试环境**: 设置中等阈值（10-20%）平衡检测效果和完整性
- **生产环境**: 设置较高阈值（20-30%）或禁用，确保完整的比较结果

### 2. 监控建议
- 监控 DataCompareLog 表中的快速失败事件
- 跟踪 Summary 表中的 `skipped` 状态记录
- 分析块级别的失败模式以优化阈值设置

### 3. 调试建议
- 使用 `table-chunk-fail-count` 设置绝对失败块数量阈值，适用于对错误块数量有严格要求的场景
- 灵活运用OR逻辑：可以单独使用百分比阈值或绝对数量阈值，也可以同时使用获得更严格的控制
- 逐步降低阈值以找到合适的敏感度设置
- 结合日志分析确定快速失败的根本原因

## 技术实现要点

### 1. 线程安全
ErrorTracker 在单线程环境下操作，无需额外的并发控制。

### 2. 状态同步
通过 `syncChunkErrorsFromTableDiff()` 函数确保块级错误统计的准确性。

### 3. 数据一致性
所有数据库操作使用事务或批量操作确保一致性。

### 4. 错误处理
快速失败机制不会影响正常的错误处理流程，而是在其基础上增加早期终止逻辑。

## 限制和注意事项

1. **阈值计算精度**: 使用浮点数计算百分比，可能存在精度问题
2. **块统计延迟**: 块错误统计需要等到表比较完成后才能获取
3. **跳过表处理**: 被跳过的表不会进行实际的数据比较，可能遗漏数据问题
4. **配置验证**: 系统不会验证阈值配置的合理性，需要用户确保配置正确

这个快速失败机制为 TMS 数据比较提供了强大的早期错误检测能力，帮助用户快速识别和解决大规模数据迁移中的问题。