package uuidutil

import (
	"fmt"
	"strings"
	"testing"

	"github.com/alecthomas/assert"
)

func TestGenerateID(t *testing.T) {
	got := GenerateID()
	if got == "" {
		t.<PERSON><PERSON><PERSON>("GenerateID() empty, got = %v", got)
	}

	if len(got) != ENTITY_UUID_LENGTH {
		t.<PERSON><PERSON>("GenerateID() want len = %d, got = %v", ENTITY_UUID_LENGTH, len(got))
	}

}

func TestGenerateIDReplace(t *testing.T) {
	time := 0
	for time < 1000 {
		got := GenerateID()
		if strings.Contains(got, "/") {
			t.<PERSON>("GenerateID() got /")
		}
		if strings.Contains(got, "+") {
			t.<PERSON>("GenerateID() got +")
		}
		if strings.HasPrefix(got, "-") {
			t.<PERSON>("GenerateID() has prefix -")
		}
		fmt.Println(got)
		time++
	}
}

func TestShortId(t *testing.T) {
	tests := []struct {
		name string
		want string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for i := 0; i < 1000; i++ {
				id := ShortId()
				fmt.Println(id)
				assert.NotContains(t, id, "_")
			}
		})
	}
}
