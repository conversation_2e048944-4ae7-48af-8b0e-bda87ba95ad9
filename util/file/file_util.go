package file

import (
	"archive/zip"
	"io"
	"os"
	"path/filepath"
)

// Exists returns whether the given file or directory exists
func Exists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func CreateIfNotExist(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		err = os.MkdirAll(path, 0755)
		return err
	}
	return nil
}

func RemoveIfExist(path string) error {
	if _, err := os.Stat(path); err == nil {
		err = os.RemoveAll(path)
		return err
	}
	return nil
}

// CreateOrReplaceFile creates or replaces a file with the given content
func CreateOrReplaceFile(fileName, fileContent string) error {
	f, err := os.OpenFile(fileName, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	defer f.Close()
	_, err = f.WriteString(fileContent)
	return err
}

func RemoveFile(filepath string) error {
	err := os.RemoveAll(filepath)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func MkdirIfNotExist(filePath string) error {
	if filePath != "" {
		_, err := os.Stat(filePath)
		if err != nil {
			if mkdirErr := os.MkdirAll(filePath, os.ModePerm); mkdirErr != nil {
				return mkdirErr
			}
		}
	}
	return nil
}

func ZipPathWithFolders(sourcePath, zipName string) error {
	zipFile, err := os.Create(zipName)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 检查 sourcePath 是否是目录
	fileInfo, err := os.Stat(sourcePath)
	if err == nil && fileInfo.IsDir() {
		// 如果是目录，遍历目录下的文件和子目录
		err = filepath.Walk(sourcePath, func(filePath string, fileInfo os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 获取相对路径
			relativePath, err := filepath.Rel(sourcePath, filePath)
			if err != nil {
				return err
			}

			// 替换斜杠为正斜杠
			relativePath = filepath.ToSlash(relativePath)

			// 创建压缩文件头
			header, err := zip.FileInfoHeader(fileInfo)
			if err != nil {
				return err
			}
			header.Name = relativePath

			if fileInfo.IsDir() {
				header.Name += "/"
			} else {
				header.Method = zip.Deflate
			}

			// 写入文件头到压缩包
			writer, err := zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}

			if !fileInfo.IsDir() {
				// 打开源文件
				file, err := os.Open(filePath)
				if err != nil {
					return err
				}
				defer file.Close()

				// 写入文件内容到压缩包
				_, err = io.Copy(writer, file)
				if err != nil {
					return err
				}
			}

			return nil
		})
	} else {
		// 如果是文件，直接将文件添加到压缩包中
		relativePath := filepath.Base(sourcePath)

		// 创建压缩文件头
		header, err := zip.FileInfoHeader(fileInfo)
		if err != nil {
			return err
		}
		header.Name = relativePath
		header.Method = zip.Deflate

		// 写入文件头到压缩包
		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			return err
		}

		// 打开源文件
		file, err := os.Open(sourcePath)
		if err != nil {
			return err
		}
		defer file.Close()

		// 写入文件内容到压缩包
		_, err = io.Copy(writer, file)
		if err != nil {
			return err
		}
	}

	return nil
}

func CreateAndWriteFile(fileName string, fileContent string) error {
	file, err := os.Create(fileName)
	if err != nil {
		return err
	}
	_, err = file.WriteString(fileContent)
	if err != nil {
		return err
	}
	err = file.Close()
	if err != nil {
		return err
	}
	return nil
}
