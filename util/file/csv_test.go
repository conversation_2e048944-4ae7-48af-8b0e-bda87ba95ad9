package file

import (
	"reflect"
	"testing"

	"gitee.com/pingcap_enterprise/tms/server/message"
)

func TestExtractSchemaAndTable(t *testing.T) {
	type args struct {
		filePath string
	}
	tests := []struct {
		name    string
		args    args
		want    *message.CSVTableProperty
		wantErr bool
	}{
		{
			name: "1",
			args: args{filePath: "schema2/table1/schema2_xx.table2.123.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema2",
				TableNameS:  "table1",
				SchemaNameT: "schema2_xx",
				TableNameT:  "table2",
			},
			wantErr: false,
		},
		{
			name: "2",
			args: args{filePath: "schema2.table2.123.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema2",
				TableNameS:  "table2",
				SchemaNameT: "schema2",
				TableNameT:  "table2",
			},
			wantErr: false,
		},
		{
			name: "3",
			args: args{filePath: "schema4.table5.789.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema4",
				TableNameS:  "table5",
				SchemaNameT: "schema4",
				TableNameT:  "table5",
			},
			wantErr: false,
		},
		{
			name: "4",
			args: args{filePath: "/data/schema1/table1/schema2.table2.123.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema2",
				TableNameS:  "table2",
				SchemaNameT: "schema2",
				TableNameT:  "table2",
			},
			wantErr: false,
		},
		{
			name: "5",
			args: args{filePath: "/data/schema2.table2.123.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema2",
				SchemaNameT: "schema2",
				TableNameS:  "table2",
				TableNameT:  "table2",
			},
			wantErr: false,
		},
		{
			name: "6",
			args: args{filePath: "/data/schema4.table5.789.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema4",
				TableNameS:  "table5",
				SchemaNameT: "schema4",
				TableNameT:  "table5",
			},
			wantErr: false,
		},
		{
			name: "7",
			args: args{filePath: "/data/schema4.table5.aa789.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema4",
				TableNameS:  "table5",
				SchemaNameT: "schema4",
				TableNameT:  "table5",
			},
			wantErr: false,
		},
		{
			name: "8",
			args: args{filePath: "/data/schema4.table5.cc.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema4",
				TableNameS:  "table5",
				SchemaNameT: "schema4",
				TableNameT:  "table5",
			},
			wantErr: false,
		},
		{
			name: "9",
			args: args{filePath: "/data/schema4.table5.cc__aa.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "schema4",
				TableNameS:  "table5",
				SchemaNameT: "schema4",
				TableNameT:  "table5",
			},
			wantErr: false,
		},
		{
			name: "10",
			args: args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/SCHEMA3.TABLE4.08.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "SCHEMA3",
				TableNameS:  "TABLE4",
				SchemaNameT: "SCHEMA3",
				TableNameT:  "TABLE4",
			},
			wantErr: false,
		},
		{
			name: "11",
			args: args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/SCHEMA3.TABLE4.08.part_ap.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "SCHEMA3",
				TableNameS:  "TABLE4",
				SchemaNameT: "SCHEMA3",
				TableNameT:  "TABLE4",
			},
			wantErr: false,
		},
		{
			name: "12",
			args: args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/FINDPTXXX.TABLE4.08.part_ap.csv"},
			want: &message.CSVTableProperty{
				SchemaNameS: "FINDPT",
				TableNameS:  "TABLE2",
				SchemaNameT: "FINDPTXXX",
				TableNameT:  "TABLE4",
			},
			wantErr: false,
		},
		{
			name:    "schema_only_number",
			args:    args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/08.part_ap.csv"},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "table_only_number",
			args:    args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/x08.8877.csv"},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "no_schema_name",
			args:    args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/rrrr.csv"},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "no_table_name",
			args:    args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/eeee.csv"},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "number",
			args:    args{filePath: "/data/csv/export_data-534-7673/data/FINDPT/TABLE2/1223.csv"},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractSchemaAndTableFromFileName(tt.args.filePath)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractSchemaAndTableFromFileName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExtractSchemaAndTableFromFileName() got = %v, want %v", got, tt.want)
			}
		})
	}
}
