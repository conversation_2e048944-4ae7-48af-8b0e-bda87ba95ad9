package file

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"sort"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/pingcap/errors"
)

func OpenAndWriteToCSVFile(filepath, filename string, record [][]string) error {
	_, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			err := os.MkdirAll(filepath, 0766)
			if err != nil {
				log.Errorf("os.MkdirAll err:%v", err)
				return err
			}
		}
	}

	file, err := os.Create(fmt.Sprintf("%s/%s", filepath, filename))
	if err != nil {
		log.Errorf("创建文件失败:%v", err)
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	//writer.UseCRLF = true

	err = writer.WriteAll(record)
	if err != nil {
		log.Errorf("写入记录失败:%v", err)
		return err
	}
	return nil
}

// OpenAndWriteToFile open and write to file, if fileDir not exist, create it, if filename exist, overwrite it
func OpenAndWriteToFile(fileDir, filename string, records []string) error {
	_, err := os.Stat(fileDir)
	if err != nil {
		if os.IsNotExist(err) {
			err := os.MkdirAll(fileDir, 0766)
			if err != nil {
				log.Errorf("os.MkdirAll err:%v", err)
				return err
			}
		}
	}

	file, err := os.Create(path.Join(fileDir, filename))
	if err != nil {
		log.Errorf("创建文件失败:%v", err)
		return err
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	for _, record := range records {
		_, writeErr := writer.WriteString(record + "\n")
		if writeErr != nil {
			log.Errorf("写入记录失败:%v", writeErr)
			return writeErr
		}
	}

	return nil
}

func WalkForCSVFiles(walkPaths []string) ([]message.CSVFileInfo, error) {
	infos := make([]message.CSVFileInfo, 0)
	for _, walkPath := range walkPaths {
		csvFiles, err := WalkForCSVFile(walkPath)
		if err != nil {
			return nil, err
		}
		infos = append(infos, csvFiles...)
	}

	// 按照 FilePath 进行排序
	sort.Slice(infos, func(i, j int) bool {
		if infos[i].IsValid != infos[j].IsValid {
			return !infos[i].IsValid
		}
		// 如果 IsValid 相同，按 FilePath 排序
		return infos[i].FilePath < infos[j].FilePath
	})

	return infos, nil
}

func WalkForCSVFile(walkPath string) ([]message.CSVFileInfo, error) {
	var csvFiles []message.CSVFileInfo
	walkErr := filepath.Walk(walkPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return errors.Trace(err)
		}

		// 只处理 .csv 文件
		if !info.IsDir() && strings.HasSuffix(strings.ToLower(info.Name()), ".csv") {
			absPath, absErr := filepath.Abs(path)
			if absErr != nil {
				return absErr
			}

			var (
				tableProperty *message.CSVTableProperty
				extractErr    error
			)

			tableProperty, extractErr = ExtractSchemaAndTableFromFileName(absPath)
			if extractErr != nil {
				tableProperty = &message.CSVTableProperty{
					IsValid:    false,
					ErrMessage: extractErr.Error(),
				}
			} else {
				tableProperty.IsValid = true
				tableProperty.ErrMessage = ""
			}

			csvFiles = append(csvFiles, message.CSVFileInfo{
				FileName:      info.Name(),
				FilePath:      absPath,
				FileSize:      info.Size(),
				IsValid:       tableProperty.IsValid,
				ErrMessage:    tableProperty.ErrMessage,
				TableProperty: tableProperty,
			})
		}
		return nil
	})

	if walkErr != nil {
		return nil, walkErr
	}

	return csvFiles, nil
}

// ExtractSchemaAndTableFromFileName 提取文件名中的 target_schema 和 target_table
func ExtractSchemaAndTableFromFileName(filePath string) (*message.CSVTableProperty, error) {
	// 1. 先从文件名中提取 target_schema 和 target_table
	fileName := filepath.Base(filePath)
	targetSchema, targetTable, err := extractTargetSchemaAndTable(fileName)
	if err != nil {
		return nil, err
	}

	// 2. 从路径中提取 source_schema 和 source_table
	//dirPath := filepath.Dir(filePath)
	//sourceSchema, sourceTable := extractSourceSchemaAndTable(dirPath)

	// 3. 判断是否提取到 source_schema 和 source_table
	//lowerTargetSchema := strings.ToLower(targetSchema)
	//lowerSourceSchema := strings.ToLower(sourceSchema)
	//if lowerSourceSchema == "" || !strings.Contains(lowerTargetSchema, lowerSourceSchema) {
	//	sourceSchema = targetSchema
	//	sourceTable = targetTable
	//}

	return &message.CSVTableProperty{
		SchemaNameT: targetSchema,
		TableNameT:  targetTable,
	}, nil
}

// 从文件名中提取 target_schema 和 target_table
func extractTargetSchemaAndTable(fileName string) (string, string, error) {
	// 正则表达式匹配文件名中的 target_schema 和 target_table，兼容 CSV 和 csv
	regex := regexp.MustCompile(`^([a-zA-Z0-9_]+)\.([a-zA-Z0-9_]+)(?:\..*)?\.(?i:csv)$`)

	matches := regex.FindStringSubmatch(fileName)
	if len(matches) < 3 {
		return "", "", fmt.Errorf("invalid file name format: %s", fileName)
	}

	// 提取并清理匹配结果
	targetSchema := strings.TrimSpace(matches[1])
	targetTable := strings.TrimSpace(matches[2])

	// 检查 schema 和 table 是否仅包含数字（假设 containOnlyNumber 是检查器函数）
	if err := containOnlyNumber(targetSchema); err != nil {
		return "", "", fmt.Errorf("invalid target schema: %s, error: %v", targetSchema, err)
	}
	if err := containOnlyNumber(targetTable); err != nil {
		return "", "", fmt.Errorf("invalid target table: %s, error: %v", targetTable, err)
	}

	return targetSchema, targetTable, nil
}
func containOnlyNumber(val string) error {
	for _, v := range val {
		if v < '0' || v > '9' {
			return nil
		}
	}
	return fmt.Errorf("invalid value, just contain number, %s", val)
}

// 从路径中提取 source_schema 和 source_table
func extractSourceSchemaAndTable(dirPath string) (string, string) {
	// 将路径分割为各部分
	pathParts := strings.Split(filepath.ToSlash(dirPath), "/")

	// 确保路径中至少有两个部分来提取 source_schema 和 source_table
	if len(pathParts) < 2 {
		return "", ""
	}

	sourceSchema := pathParts[len(pathParts)-2]
	sourceTable := pathParts[len(pathParts)-1]

	return sourceSchema, sourceTable
}
