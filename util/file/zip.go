package file

import (
	"archive/zip"
	"io"
	"os"
	"path/filepath"
)

// ZipFiles 将指定的文件打包为zip文件
func ZipFiles(zipFileName, dirToZip string, filePaths []string, rootFolderName string) error {
	// 创建zip文件
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	filePathMap := make(map[string]bool)
	for _, filePath := range filePaths {
		filePathMap[filePath] = true
	}

	// 创建zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 遍历目录中的文件
	err = filepath.Walk(dirToZip, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录本身
		if info.IsDir() {
			return nil
		}

		// 获取相对路径
		relPath, err := filepath.Rel(dirToZip, filePath)
		if err != nil {
			return err
		}

		// 判断文件是否在指定的filePaths列表中
		if !isInFilePaths(relPath, filePathMap) {
			return nil
		}

		// 打开文件
		file, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer file.Close()

		// 创建zip中的文件，添加根文件夹路径
		zipEntryPath := relPath
		if rootFolderName != "" {
			zipEntryPath = filepath.Join(rootFolderName, relPath)
		}
		zipFileWriter, err := zipWriter.Create(zipEntryPath)
		if err != nil {
			return err
		}

		// 将文件内容写入zip
		_, err = io.Copy(zipFileWriter, file)
		return err
	})

	return err
}

// isInFilePaths 判断文件是否在filePaths列表中
func isInFilePaths(file string, filePathMap map[string]bool) bool {
	_, ok := filePathMap[file]
	return ok
}
