package stringutil

import (
	"testing"

	"github.com/alecthomas/assert"
)

func TestEncodeAndDecodeBookPackage(t *testing.T) {
	// Test encoding
	encoded := EncodeSQL(TEST_BOOK_PACKAGE)
	assert.NotEmpty(t, encoded, "EncodeSQL() returned empty string for TEST_BOOK_PACKAGE")

	// Test decoding
	decoded, err := DecodeSQL(encoded)
	assert.NoError(t, err, "DecodeSQL() error")

	// Verify round-trip
	assert.Equal(t, TEST_BOOK_PACKAGE, decoded, "DecodeSQL(EncodeSQL(TEST_BOOK_PACKAGE)) mismatch")

	// Log the results for debugging
	t.Logf("Original: %s", TEST_BOOK_PACKAGE)
	t.Logf("Encoded: %s", encoded)
	t.Logf("Decoded: %s", decoded)
}

func TestEncodeSQL(t *testing.T) {
	type args struct {
		originStr string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "1",
			args: args{
				originStr: TEST_TAIPING_SUFFIX_PACKAGE,
			},
			want: "",
		},
		{
			name: "2",
			args: args{
				originStr: "CREATE PACKAGE BODY DEMO AS\n   PROCEDURE D IS\n      v_id NUMBER := TO_NUMBER(TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS'));\n   BEGIN\n      BEGIN\n         INSERT INTO log_d (id, message)\n         VALUES (v_id, 'D executed at ' || TO_CHAR(SYSDATE, 'HH24:MI:SS'));\n         COMMIT;\n         DBMS_OUTPUT.PUT_LINE('D committed with id=' || v_id);\n      EXCEPTION\n         WHEN DUP_VAL_ON_INDEX THEN\n            ROLLBACK;\n            DBMS_OUTPUT.PUT_LINE('D rolled back due to duplicate id=' || v_id);\n         WHEN OTHERS THEN\n            ROLLBACK;\n            DBMS_OUTPUT.PUT_LINE('D rolled back due to error: ' || SQLERRM);\n            RAISE_APPLICATION_ERROR(-20001, 'Error in D: ' || SQLERRM);\n      END;\n   END D;\n\n   END DEMO;",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EncodeSQL(tt.args.originStr); got != tt.want {
				println(got)
			}
		})
	}
}

func TestDecodeSQL(t *testing.T) {
	type args struct {
		originStr string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name:    "restore",
			args:    args{originStr: "Q1JFQVRFIFBBQ0tBR0UgQk9EWSBERU1PIEFTCiAgIFBST0NFRFVSRSBEIElTCiAgICAgIHZfaWQgTlVNQkVSIDo9IFRPX05VTUJFUihUT19DSEFSKFNZU0RBVEUsICdZWVlZTU1EREhIMjRNSVNTJykpOwogICBCRUdJTgogICAgICBCRUdJTgogICAgICAgICBJTlNFUlQgSU5UTyBsb2dfZCAoaWQsIG1lc3NhZ2UpCiAgICAgICAgIFZBTFVFUyAodl9pZCwgJ0QgZXhlY3V0ZWQgYXQgJyB8fCBUT19DSEFSKFNZU0RBVEUsICdISDI0Ok1JOlNTJykpOwogICAgICAgICBDT01NSVQ7CiAgICAgICAgIERCTVNfT1VUUFVULlBVVF9MSU5FKCdEIGNvbW1pdHRlZCB3aXRoIGlkPScgfHwgdl9pZCk7CiAgICAgIEVYQ0VQVElPTgogICAgICAgICBXSEVOIERVUF9WQUxfT05fSU5ERVggVEhFTgogICAgICAgICAgICBST0xMQkFDSzsKICAgICAgICAgICAgREJNU19PVVRQVVQuUFVUX0xJTkUoJ0Qgcm9sbGVkIGJhY2sgZHVlIHRvIGR1cGxpY2F0ZSBpZD0nIHx8IHZfaWQpOwogICAgICAgICBXSEVOIE9USEVSUyBUSEVOCiAgICAgICAgICAgIFJPTExCQUNLOwogICAgICAgICAgICBEQk1TX09VVFBVVC5QVVRfTElORSgnRCByb2xsZWQgYmFjayBkdWUgdG8gZXJyb3I6ICcgfHwgU1FMRVJSTSk7CiAgICAgICAgICAgIFJBSVNFX0FQUExJQ0FUSU9OX0VSUk9SKC0yMDAwMSwgJ0Vycm9yIGluIEQ6ICcgfHwgU1FMRVJSTSk7CiAgICAgIEVORDsKICAgRU5EIEQ7CgogICBFTkQgREVNTzs%3D"},
			want:    "",
			wantErr: false,
		},
		{
			name:    "restore2",
			args:    args{originStr: "Q1JFQVRFIFBBQ0tBR0UgQk9EWSBERU1PIEFTCiAgIFBST0NFRFVSRSBEIElTCiAgICAgIHZfaWQgTlVNQkVSIDo9IFRPX05VTUJFUihUT19DSEFSKFNZU0RBVEUsICdZWVlZTU1EREhIMjRNSVNTJykpOwogICBCRUdJTgogICAgICBCRUdJTgogICAgICAgICBJTlNFUlQgSU5UTyBsb2dfZCAoaWQsIG1lc3NhZ2UpCiAgICAgICAgIFZBTFVFUyAodl9pZCwgJ0QgZXhlY3V0ZWQgYXQgJyB8fCBUT19DSEFSKFNZU0RBVEUsICdISDI0Ok1JOlNTJykpOwogICAgICAgICBDT01NSVQ7CiAgICAgICAgIERCTVNfT1VUUFVULlBVVF9MSU5FKCdEIGNvbW1pdHRlZCB3aXRoIGlkPScgfHwgdl9pZCk7CiAgICAgIEVYQ0VQVElPTgogICAgICAgICBXSEVOIERVUF9WQUxfT05fSU5ERVggVEhFTgogICAgICAgICAgICBST0xMQkFDSzsKICAgICAgICAgICAgREJNU19PVVRQVVQuUFVUX0xJTkUoJ0Qgcm9sbGVkIGJhY2sgZHVlIHRvIGR1cGxpY2F0ZSBpZD0nIHx8IHZfaWQpOwogICAgICAgICBXSEVOIE9USEVSUyBUSEVOCiAgICAgICAgICAgIFJPTExCQUNLOwogICAgICAgICAgICBEQk1TX09VVFBVVC5QVVRfTElORSgnRCByb2xsZWQgYmFjayBkdWUgdG8gZXJyb3I6ICcgfHwgU1FMRVJSTSk7CiAgICAgICAgICAgIFJBSVNFX0FQUExJQ0FUSU9OX0VSUk9SKC0yMDAwMSwgJ0Vycm9yIGluIEQ6ICcgfHwgU1FMRVJSTSk7CiAgICAgIEVORDsKICAgRU5EIEQ7CgogICBFTkQgREVNTzs%3D"},
			want:    "",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := DecodeSQL(tt.args.originStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("DecodeSQL() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("DecodeSQL() got = %v, want %v", got, tt.want)
			}
		})
	}
}
