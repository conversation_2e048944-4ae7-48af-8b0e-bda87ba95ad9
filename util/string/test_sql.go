package stringutil

const TEST_TAIPING_SUFFIX_PACKAGE = `-- 包头部（规范）
CREATE OR REPLACE PACKAGE pkg_account_check IS
  PROCEDURE checkAccountStatus(
    o_returnType OUT VARCHAR2,
    o_returnMsg  OUT VARCHAR2
  );
END pkg_account_check;
/

-- 包体（实现）
CREATE OR REPLACE PACKAGE BODY pkg_account_check AS
  PROCEDURE checkAccountStatus(
    o_returnType OUT VARCHAR2,
    o_returnMsg  OUT VARCHAR2
  ) IS
    v_counter NUMBER := 0;

    -- 定义游标
    CURSOR cur_account IS
      SELECT ACCOUNT_ID, ACCOUNT_STAT FROM ACCOUNT;

  BEGIN
    -- 游标循环
    FOR rec IN cur_account LOOP
      -- 在循环内执行SELECT查询（示例）
      SELECT COUNT(*) INTO v_counter
      FROM ACCOUNT
      WHERE ACCOUNT_STAT = rec.ACCOUNT_STAT;

      IF rec.ACCOUNT_STAT = 'PENDING' THEN
        IF rec.ACCOUNT_ID IS NOT NULL THEN
          IF o_returnMsg IS NOT NULL THEN
            o_returnType := '3';
            o_returnMsg  := '该单底下标的序号为';
          END IF;

          -- 在第一个 END IF 后添加逻辑
          IF v_counter IS NOT NULL THEN
            v_counter := v_counter + 1;
            o_returnMsg := o_returnMsg || rec.ACCOUNT_ID || ',';
          END IF;

        END IF;
      END IF;
    END LOOP;
  END checkAccountStatus;
END ​pkg_account_check;
/`

const TEST_BOOK_PACKAGE = "create PACKAGE BODY test_book AS\n" +
	"\n" +
	"   overall_success BOOLEAN := TRUE;\n" +
	"   l_verbose BOOLEAN := TRUE;\n" +
	"   dup_val_sqlcode CONSTANT VARCHAR2(12) := '-1';\n" +
	"   val_err_sqlcode CONSTANT VARCHAR2(12) := '-6502';\n" +
	"   okay_sqlcode CONSTANT VARCHAR2(12) := '0';\n" +
	"   bad_fk_sqlcode CONSTANT VARCHAR2(12) := '-2291';\n" +
	"   no_data_found_sqlcode CONSTANT VARCHAR2(12) := '100';\n" +
	"\n" +
	"\n" +
	"   /* convenient set of column values to use in various tests */\n" +
	"\n" +
	"   l_isbn books.isbn%TYPE := '1-56592-335-9';\n" +
	"   l_title books.title%TYPE := 'Oracle PL/SQL Programming';\n" +
	"   l_summary books.summary%TYPE := 'Reference for PL/SQL developers, ' ||\n" +
	"       'including examples and best practice recommendations.';\n" +
	"   l_author books.author%TYPE := 'Feuerstein, Steven, and Bill Pribyl';\n" +
	"   l_date_published books.date_published%TYPE :=\n" +
	"      TO_DATE('01-SEP-1997', 'DD-MON-YYYY');\n" +
	"   l_page_count books.page_count%TYPE := 987;\n" +
	"   l_barcode_id book_copies.barcode_id%TYPE := '100000001';\n" +
	"\n" +
	"\n" +
	"   /* ======================================================================\n" +
	"   || Utility routines.  First some generic stuff that could\n" +
	"   || actually be moved elsewhere.\n" +
	"   */\n" +
	"\n" +
	"   PROCEDURE pl(msg IN VARCHAR2, newline IN BOOLEAN DEFAULT TRUE) IS\n" +
	"   BEGIN\n" +
	"      IF l_verbose\n" +
	"      THEN\n" +
	"         IF newline THEN\n" +
	"            RAISE_APPLICATION_ERROR(1,'ERROR!');\n" +
	"            DBMS_OUTPUT.PUT_LINE(SQLERRM(-1403));\n" +
	"         ELSE\n" +
	"            DBMS_OUTPUT.PUT(msg);\n" +
	"            RAISE NO_DATA_FOUND;\n" +
	"         END IF;\n" +
	"      END IF;\n" +
	"   EXCEPTION\n" +
	"     WHEN OTHERS\n" +
	"       THEN NULL;\n" +
	"   END pl;\n" +
	"\n" +
	"   PROCEDURE reporteq (description IN VARCHAR2, expected_value IN VARCHAR2,\n" +
	"      actual_value IN VARCHAR2) IS\n" +
	"   BEGIN\n" +
	"      pl('...' || description || ': ', newline => FALSE);\n" +
	"      IF expected_value = actual_value\n" +
	"         OR (expected_value IS NULL AND actual_value IS NULL)\n" +
	"      THEN\n" +
	"         pl('PASSED');\n" +
	"      ELSE\n" +
	"         overall_success := FALSE;\n" +
	"         pl('FAILED.  Expected ' || expected_value || '; got ' || actual_value);\n" +
	"      END IF;\n" +
	"   END;\n" +
	"\n" +
	"\n" +
	"   FUNCTION my_to_char (is_true IN BOOLEAN) RETURN VARCHAR2 IS\n" +
	"   BEGIN\n" +
	"      IF is_true\n" +
	"      THEN\n" +
	"         RETURN 'TRUE';\n" +
	"      ELSIF NOT is_true\n" +
	"      THEN\n" +
	"         RETURN 'FALSE';\n" +
	"      ELSE\n" +
	"         RETURN TO_CHAR(NULL);\n" +
	"      END IF;\n" +
	"   END;\n" +
	"\n" +
	"\n" +
	"   PROCEDURE reporteq (description IN VARCHAR2, expected_value IN BOOLEAN,\n" +
	"      actual_value IN BOOLEAN) IS\n" +
	"   BEGIN\n" +
	"      reporteq(description, my_to_char(expected_value),\n" +
	"         my_to_char(actual_value));\n" +
	"   END reporteq;\n" +
	"\n" +
	"\n" +
	"   /* ======================================================================\n" +
	"   || Now some more private routines that are unique to books.\n" +
	"   */\n" +
	"\n" +
	"   FUNCTION book_count RETURN NUMBER IS\n" +
	"      how_many NUMBER;\n" +
	"   BEGIN\n" +
	"      SELECT COUNT(*) INTO how_many FROM books;\n" +
	"      RETURN how_many;\n" +
	"   END;\n" +
	"\n" +
	"   FUNCTION book_copy_count RETURN NUMBER IS\n" +
	"      how_many NUMBER;\n" +
	"   BEGIN\n" +
	"      SELECT COUNT(*) INTO how_many FROM book_copies;\n" +
	"      RETURN how_many;\n" +
	"   END;\n" +
	"\n" +
	"   PROCEDURE add_one_book IS\n" +
	"   BEGIN\n" +
	"      book.add(isbn_in => l_isbn, barcode_id_in => l_barcode_id,\n" +
	"         title_in => l_title, summary_in => l_summary, author_in => l_author,\n" +
	"         date_published_in => l_date_published, page_count_in => l_page_count);\n" +
	"   END;\n" +
	"\n" +
	"\n" +
	"   /* ======================================================================\n" +
	"   || A \"driver\" that calls all the other tests\n" +
	"   */\n" +
	"\n" +
	"   PROCEDURE run(verbose IN BOOLEAN) \n" +
	"   IS\n" +
	"      TYPE book_data_t1 is REF CURSOR RETURN books%ROWTYPE;\n" +
	"      book_curs_vara book_data_t1;\n" +
	"      TYPE book_data_t2 is REF CURSOR;\n" +
	"      book_curs_varb SYS_REFCURSOR;\n" +
	"      TYPE book_data_t3 is TABLE OF books%ROWTYPE INDEX BY BINARY_INTEGER;\n" +
	"      d2s INTERVAL DAY(3) TO SECOND(0);\n" +
	"   BEGIN\n" +
	"      l_verbose := verbose;\n" +
	"      IF l_verbose\n" +
	"      THEN\n" +
	"         DBMS_OUTPUT.PUT_LINE('Testing book package...');\n" +
	"      END IF;\n" +
	"      add;\n" +
	"      add_copy;\n" +
	"      book_copy_qty;\n" +
	"      change;\n" +
	"      remove_copy;\n" +
	"      IF overall_success\n" +
	"      THEN\n" +
	"         DBMS_OUTPUT.PUT_LINE('book package: PASSED');\n" +
	"      ELSE\n" +
	"         DBMS_OUTPUT.PUT_LINE('book package: FAILED');\n" +
	"      END IF;\n" +
	"   EXCEPTION\n" +
	"     WHEN NO_DATA_FOUND\n" +
	"     THEN\n" +
	"        RAISE;\n" +
	"     WHEN TOO_MANY_ROWS\n" +
	"     THEN\n" +
	"        RAISE;\n" +
	"     WHEN ROWTYPE_MISMATCH\n" +
	"     THEN\n" +
	"        RAISE;\n" +
	"   END run;\n" +
	"\n" +
	"\n" +
	"   /* ======================================================================\n" +
	"   || Unit tests for each routine in the package we're testing\n" +
	"   */\n" +
	"\n" +
	"   PROCEDURE add IS\n" +
	"\n" +
	"      CURSOR bookcur IS\n" +
	"         SELECT COUNT(*) FROM books\n" +
	"          WHERE isbn = l_isbn\n" +
	"            AND title = l_title\n" +
	"            AND summary = l_summary\n" +
	"            AND author = l_author\n" +
	"            AND date_published = l_date_published\n" +
	"            AND page_count = l_page_count;\n" +
	"\n" +
	"      CURSOR copiescur IS\n" +
	"         SELECT COUNT(*) FROM book_copies\n" +
	"          WHERE isbn = l_isbn\n" +
	"            AND barcode_id = l_barcode_id;\n" +
	"\n" +
	"      how_many NUMBER;\n" +
	"      l_sqlcode NUMBER;\n" +
	"\n" +
	"      CURSOR copiesbook IS\n" +
	"         SELECT * FROM book_copies\n" +
	"          WHERE isbn = l_isbn\n" +
	"            AND barcode_id = l_barcode_id;\n" +
	"\n" +
	"      copiesbook_rec copiesbook%ROWTYPE;\n" +
	"\n" +
	"      TYPE author_title_rt IS RECORD (\n" +
	"      author books.author%TYPE,\n" +
	"      title books.title%TYPE);\n" +
	"      \n" +
	"      l_book_info author_title_rt;\n" +
	"      \n" +
	"      TYPE list_of_names_t IS TABLE OF hr.employees.first_name%TYPE INDEX BY PLS_INTEGER;\n" +
	"      happyfamily list_of_names_t;\n" +
	"      l_row PLS_INTEGER;\n" +
	"      \n" +
	"      v_rowid UROWID;\n" +
	"      v_rows number;\n" +
	"   BEGIN\n" +
	"      happyfamily(1) :='first test';\n" +
	"      happyfamily(2) :='second test';\n" +
	"      l_row:=happyfamily.First;\n" +
	"      WHILE (l_row IS NOT NULL)\n" +
	"      LOOP\n" +
	"        DBMS_OUTPUT.put_line(happyfamily(l_row));\n" +
	"        l_row:=happyfamily.NEXT(l_row);\n" +
	"      END LOOP;      \n" +
	"      DELETE book_copies;\n" +
	"      DELETE books;\n" +
	"\n" +
	"      add_one_book;\n" +
	"\n" +
	"      BEGIN\n" +
	"         book.add(isbn_in => NULL, barcode_id_in => 'foo',\n" +
	"            title_in => 'foo', summary_in => 'foo', author_in => 'foo',\n" +
	"            date_published_in => SYSDATE, page_count_in => 0);\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      EXCEPTION\n" +
	"      WHEN OTHERS THEN\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"\n" +
	"      reporteq('add procedure, detection of NULL input', val_err_sqlcode,\n" +
	"                TO_CHAR(l_sqlcode));\n" +
	"      reporteq('add procedure, book_record count', '1', TO_CHAR(book_count()));\n" +
	"      reporteq('add procedure, book_copy record count',\n" +
	"               '1', TO_CHAR(book_copy_count()));\n" +
	"\n" +
	"      OPEN bookcur; FETCH bookcur INTO how_many;\n" +
	"      reporteq('add procedure, book fetch matches insert',\n" +
	"               TRUE, bookcur%FOUND);\n" +
	"      \n" +
	"      CLOSE bookcur;\n" +
	"\n" +
	"      OPEN copiesbook; FETCH copiesbook INTO copiesbook_rec;\n" +
	"      v_rows:=SQL%ROWCOUNT;\n" +
	"      IF copiesbook%ROWCOUNT >10 THEN\n" +
	"         DBMS_OUTPUT.put_line(copiesbook_rec.barcode_id);\n" +
	"      END IF;\n" +
	"      CLOSE copiesbook;\n" +
	"\n" +
	"      OPEN copiescur; FETCH copiescur INTO how_many;\n" +
	"      reporteq('add procedure, book copy fetch matches insert', TRUE,\n" +
	"                 copiescur%FOUND);\n" +
	"      CLOSE copiescur;\n" +
	"\n" +
	"      /* Confirm that attempting to add same isbn a second time will raise an\n" +
	"      || exception.  Yes I know this is really a test of the database design\n" +
	"      || but we might as well test it somewhere.\n" +
	"      */\n" +
	"\n" +
	"      BEGIN\n" +
	"         add_one_book;\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      EXCEPTION\n" +
	"         WHEN OTHERS THEN\n" +
	"            l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('add procedure, detection of duplicate isbn', dup_val_sqlcode,\n" +
	"               l_sqlcode);\n" +
	"\n" +
	"   END add;\n" +
	"\n" +
	"   /* ------------------------------------------------------------------- */\n" +
	"   PROCEDURE add_copy IS\n" +
	"      l_sqlcode NUMBER := 0;\n" +
	"   BEGIN\n" +
	"      DELETE book_copies;\n" +
	"      DELETE books;\n" +
	"      add_one_book;\n" +
	"      DELETE book_copies;\n" +
	"      book.add_copy(isbn_in => l_isbn, barcode_id_in => l_barcode_id);\n" +
	"      reporteq('add_copy procedure, nominal case, first book',\n" +
	"               '1', TO_CHAR(book_copy_count()));\n" +
	"\n" +
	"      book.add_copy(isbn_in => l_isbn, barcode_id_in => '0101010101');\n" +
	"      reporteq('add_copy procedure, nominal case, second book',\n" +
	"               '2', TO_CHAR(book_copy_count()));\n" +
	"\n" +
	"      BEGIN\n" +
	"         book.add_copy(isbn_in => l_isbn, barcode_id_in => l_barcode_id);\n" +
	"      EXCEPTION\n" +
	"      WHEN OTHERS THEN\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('add_copy procedure, ignore duplicates',\n" +
	"               okay_sqlcode, TO_CHAR(l_sqlcode));\n" +
	"\n" +
	"      BEGIN\n" +
	"         book.add_copy(isbn_in => '1234567890', barcode_id_in => '0202020202');\n" +
	"      EXCEPTION\n" +
	"      WHEN OTHERS THEN\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('add_copy procedure, bad isbn detection',\n" +
	"               bad_fk_sqlcode, TO_CHAR(l_sqlcode));\n" +
	"\n" +
	"\n" +
	"      BEGIN\n" +
	"         book.add_copy(isbn_in => NULL, barcode_id_in => '0303030303');\n" +
	"      EXCEPTION\n" +
	"      WHEN OTHERS THEN\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('add_copy procedure, NULL isbn detection',\n" +
	"               val_err_sqlcode, TO_CHAR(l_sqlcode));\n" +
	"\n" +
	"      BEGIN\n" +
	"         book.add_copy(isbn_in => 'anything', barcode_id_in => NULL);\n" +
	"      EXCEPTION\n" +
	"      WHEN OTHERS THEN\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('add_copy procedure, NULL barcode_id detection',\n" +
	"               val_err_sqlcode, TO_CHAR(l_sqlcode));\n" +
	"   END add_copy;\n" +
	"\n" +
	"   /* ------------------------------------------------------------------- */\n" +
	"   PROCEDURE book_copy_qty IS\n" +
	"   BEGIN\n" +
	"      DELETE book_copies;\n" +
	"      DELETE books;\n" +
	"      add_one_book;\n" +
	"      DELETE book_copies;\n" +
	"      reporteq('book_copy_qty function, zero count', '0',\n" +
	"         TO_CHAR(book.book_copy_qty(l_isbn)));\n" +
	"      book.add_copy(isbn_in => l_isbn, barcode_id_in => l_barcode_id);\n" +
	"      book.add_copy(isbn_in => l_isbn, barcode_id_in => '0101010101');\n" +
	"      reporteq('book_copy_qty function, non-zero count', '2',\n" +
	"         TO_CHAR(book.book_copy_qty(l_isbn)));\n" +
	"   END;\n" +
	"\n" +
	"   /* ------------------------------------------------------------------- */\n" +
	"   PROCEDURE change IS\n" +
	"      l_new_summary books.summary%TYPE := 'A long and boring book about PL/SQL';\n" +
	"      CURSOR chgcur IS\n" +
	"         SELECT COUNT(*) FROM books\n" +
	"          WHERE isbn = l_isbn\n" +
	"            AND title = l_title\n" +
	"            AND summary = l_new_summary\n" +
	"            AND author = l_author\n" +
	"            AND date_published = l_date_published\n" +
	"            AND page_count = l_page_count;\n" +
	"\n" +
	"      how_many NUMBER;\n" +
	"      l_sqlcode NUMBER;\n" +
	"   BEGIN\n" +
	"      DELETE book_copies;\n" +
	"      DELETE books;\n" +
	"      add_one_book;\n" +
	"\n" +
	"      /* Now change only the summary */\n" +
	"      book.change(isbn_in => l_isbn,\n" +
	"         new_summary => l_new_summary, new_title => l_title,\n" +
	"         new_author => l_author, new_date_published => l_date_published,\n" +
	"         new_page_count => l_page_count);\n" +
	"      OPEN chgcur; FETCH chgcur INTO how_many; CLOSE chgcur;\n" +
	"      reporteq('change procedure, single field test', '1', TO_CHAR(how_many));\n" +
	"\n" +
	"      /* NULL isbn test */\n" +
	"      BEGIN\n" +
	"         book.change(isbn_in => NULL,\n" +
	"         new_title => l_title, new_summary => l_summary, new_author => l_author,\n" +
	"         new_date_published => l_date_published,\n" +
	"            new_page_count => l_page_count);\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"         IF l_sqlcode= 0 THEN\n" +
	"           GOTO END_CHANGE;\n" +
	"         END IF;\n" +
	"      EXCEPTION\n" +
	"      WHEN OTHERS THEN\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      <<END_CHANGE>>\n" +
	"      reporteq('change procedure, NULL barcode_id detection',\n" +
	"         val_err_sqlcode, TO_CHAR(l_sqlcode));\n" +
	"\n" +
	"   END change;\n" +
	"\n" +
	"   /* ------------------------------------------------------------------- */\n" +
	"   PROCEDURE remove_copy IS\n" +
	"      l_sqlcode NUMBER;\n" +
	"   BEGIN\n" +
	"      DELETE book_copies;\n" +
	"      DELETE books;\n" +
	"      add_one_book;\n" +
	"      book.remove_copy(barcode_id_in => l_barcode_id);\n" +
	"      reporteq('remove_copy procedure, book count normal',\n" +
	"               '1', TO_CHAR(book_count()));\n" +
	"      reporteq('remove_copy procedure, book copy count normal',\n" +
	"               '0', TO_CHAR(book_copy_count()));\n" +
	"\n" +
	"      /* If we delete it again, there should be no error. */\n" +
	"      BEGIN\n" +
	"         book.remove_copy(barcode_id_in => l_barcode_id);\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      EXCEPTION\n" +
	"         WHEN OTHERS THEN\n" +
	"            l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('remove_copy procedure, superfluous invocation', okay_sqlcode,\n" +
	"         TO_CHAR(l_sqlcode));\n" +
	"\n" +
	"   END remove_copy;\n" +
	"\n" +
	"   /* ------------------------------------------------------------------- */\n" +
	"   PROCEDURE weed IS\n" +
	"      l_sqlcode NUMBER;\n" +
	"   BEGIN\n" +
	"      DELETE book_copies;\n" +
	"      DELETE books;\n" +
	"      add_one_book;\n" +
	"      book.weed(l_isbn);\n" +
	"      reporteq('weed procedure, book count normal', '0', TO_CHAR(book_count()));\n" +
	"      reporteq('weed procedure, book copy count normal',\n" +
	"               '0', TO_CHAR(book_copy_count()));\n" +
	"\n" +
	"      /* If we weed it again, there should a NO_DATA_FOUND error. */\n" +
	"      BEGIN\n" +
	"         book.weed(l_isbn);\n" +
	"         l_sqlcode := SQLCODE;\n" +
	"      EXCEPTION\n" +
	"         WHEN OTHERS THEN\n" +
	"            l_sqlcode := SQLCODE;\n" +
	"      END;\n" +
	"      reporteq('weed procedure, superfluous invocation',\n" +
	"               no_data_found_sqlcode, TO_CHAR(l_sqlcode));\n" +
	"\n" +
	"   END weed;\n" +
	"\n" +
	"END test_book;\n" +
	"\n" +
	"\n" +
	"/\n" +
	"\n"
const HZBAK_PACKAGE = `create package body PKG_DATA_CLEANER IS

  -- 取得业务日期
  FUNCTION GetCurrentBusiDate RETURN DATE AS
    v_BusiDate DATE;
  BEGIN

    --v_BusiDate := to_date('2012-10-10','YYYY-MM-DD');
    --SELECT busi_date INTO v_BusiDate FROM dual;
    --v_BusiDate := SYSDATE;
    select BUSINESS_DATE into v_BusiDate from PM_SPMA_BANK_PARA;
    RETURN v_BusiDate;
  END;

  -- 重建索引
  -- ,o_Result OUT VARCHAR2
  PROCEDURE rebuild_index(i_TableName VARCHAR2) AS
    CURSOR c_Ind IS
      SELECT * FROM user_indexes WHERE table_name = i_TableName;
    r_Ind c_Ind%ROWTYPE;

  BEGIN

    FOR r_Ind IN c_Ind LOOP

      BEGIN
        EXECUTE IMMEDIATE 'alter index ' || r_Ind.Index_Name || ' rebuild online';
      EXCEPTION
        WHEN OTHERS THEN
          --o_Result := '重建索引失败：' || substr(SQLERRM,1,100);

          EXIT;
      END;
    END LOOP;

  END;

  -- 收集统计信息
  -- ,o_Result OUT VARCHAR2
  PROCEDURE gather_stats(i_TableName VARCHAR2) AS
  BEGIN

    BEGIN
      dbms_stats.gather_table_stats(USER,
                                    i_TableName,
                                    method_opt  => 'FOR ALL INDEXED COLUMNS SIZE 1',
                                    cascade     => TRUE,
                                    degree      => 8);
    EXCEPTION
      WHEN OTHERS THEN
        --o_Result := '收集统计信息失败：' || substr(SQLERRM,1,100);
        NULL;
    END;

  END;

  -- 普通清理
  PROCEDURE normal_clean(i_TableName      VARCHAR2,
                         i_DocTableName   VARCHAR2,
                         i_WhereClause    VARCHAR2,
                         i_CleanDate      VARCHAR2,
                         i_KeepMonthBegin VARCHAR2,
                         i_KeepMonthEnd   VARCHAR2,
                         i_SplitMonth     VARCHAR2,
                         i_HisTablespace  VARCHAR2,
                         o_RowCount       OUT NUMBER,
                         o_Result         OUT VARCHAR2) AS
    v_Sql          VARCHAR2(1024);
    v_DocCnt       NUMBER := 0;
    v_Cnt          NUMBER := 0;
    v_DocTableName VARCHAR2(40);
  BEGIN

    -- 2013-09-26
    -- 如果设定历史表按照月份建一张表
    -- 检查历史表是否存在如果不存在则建立历史表
    -- 表名为i_DocTableName||'_YYYYMM'
    IF i_SplitMonth = '1' THEN

      -- 取得历史表名
      IF length(i_DocTableName) > 23 THEN
        v_DocTableName := substr(i_DocTableName, 1, 23) || '_' || Replace(SUBSTR(i_CleanDate, 1, 7), '-', '');
      ELSE
        v_DocTableName := i_DocTableName || '_' || Replace(SUBSTR(i_CleanDate, 1, 7), '-', '');
      END IF;

      -- 判断历史表是否存在
      SELECT COUNT(1)
        INTO v_Cnt
        FROM user_tables
       WHERE table_name = v_DocTableName;
      IF SQL%NOTFOUND OR v_Cnt = 0 THEN

        IF i_HisTablespace IS NOT NULL AND length(i_HisTablespace) > 0 THEN
          v_Sql := 'CREATE TABLE ' || v_DocTableName || ' TABLESPACE "' ||  i_HisTablespace || '" AS SELECT * FROM ' || i_TableName || ' WHERE 1=2';

        ELSE
          v_Sql := 'CREATE TABLE ' || v_DocTableName || ' AS SELECT * FROM ' || i_TableName || ' WHERE 1=2';

        END IF;

        BEGIN
          EXECUTE IMMEDIATE v_Sql;
        EXCEPTION
          WHEN OTHERS THEN
            o_Result := '创建历史表[' || v_DocTableName || ']错：' ||
                        substr(SQLERRM, 1, 100);
            ROLLBACK;
            RETURN;
        END;
      END IF;

    ELSE
      v_DocTableName := i_DocTableName;
    END IF;

    -- 备份待清理的记录到历史表
    v_Sql := 'BEGIN INSERT INTO ' || v_DocTableName || ' SELECT * FROM ' || i_TableName || ' WHERE ' || i_WhereClause || '; END;';

    BEGIN
      EXECUTE IMMEDIATE v_Sql
        USING IN i_CleanDate;
    EXCEPTION
      WHEN OTHERS THEN
        o_Result := '备份数据到历史表错：' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        RETURN;
    END;

    -- 取出历史表记录
    v_Sql := 'BEGIN  SELECT count(1) into :2 FROM ' || v_DocTableName ||' WHERE ' || i_WhereClause || '; END;';

    BEGIN
      EXECUTE IMMEDIATE v_Sql
        USING OUT v_DocCnt, IN i_CleanDate;
    EXCEPTION
      WHEN OTHERS THEN
        o_Result := '计算历史表记录条数错：' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        RETURN;
    END;

    -- 取得当前表记录
    v_Sql := 'BEGIN  SELECT count(1) into :2 FROM ' || i_TableName || ' WHERE ' || i_WhereClause || '; END;';

    BEGIN
      EXECUTE IMMEDIATE v_Sql
        USING OUT v_Cnt, IN i_CleanDate;
    EXCEPTION
      WHEN OTHERS THEN
        o_Result := substr(SQLERRM, 1, 100);
        ROLLBACK;
        RETURN;
    END;

    o_RowCount := v_Cnt;

    IF v_Cnt != v_DocCnt THEN
      -- 判断历史表和当前表记录数是否相等，如果不相等则回滚并退出
      o_Result := 'ORA-9999:历史表记录数和当前表记录数不相等!';
      ROLLBACK;
      RETURN;
    ELSIF i_KeepMonthBegin = '1' AND substr(i_CleanDate, 9, 10) = '01' OR
          i_KeepMonthEnd = '1' AND substr(to_char((to_date(i_CleanDate, 'YYYY-MM-DD') + 1), 'YYYY-MM-DD'), 9, 10) = '01'
    THEN
      -- 保留月初或者月末的数据
      NULL;
    ELSE

      -- 删除当前表记录
      v_Sql := 'BEGIN DELETE FROM ' || i_TableName || ' WHERE ' || i_WhereClause || '; END;';
      BEGIN
        EXECUTE IMMEDIATE v_Sql
          USING IN i_CleanDate;
      EXCEPTION
        WHEN OTHERS THEN
          o_Result := '删除当前表记录错：' || substr(SQLERRM, 1, 100);
          ROLLBACK;
          RETURN;
      END;
    END IF;

    -- 提交
    COMMIT;

  END;

  -- 自定义脚本清理
  -- 脚本参数: tablename in, doc_table_name in, clean_date in, rowcount out, result out
  PROCEDURE user_defined_clean(i_TableName    VARCHAR2,
                               i_HisTableName VARCHAR2,
                               i_CleanSql     VARCHAR2,
                               i_CleanDate    VARCHAR2,
                               o_RowCount     OUT NUMBER,
                               o_Result       OUT VARCHAR2) AS
    v_Sql VARCHAR2(800);
  BEGIN

    v_Sql := 'BEGIN ' || substr(i_CleanSql, 2) || ' END;';
    BEGIN
      EXECUTE IMMEDIATE v_Sql
        USING IN i_TableName, i_HisTableName, i_CleanDate, OUT o_RowCount, OUT o_Result;

    EXCEPTION
      WHEN OTHERS THEN
        o_Result := '执行自定义清理脚本错误:' || substr(SQLERRM, 1, 100);
        -- 回滚
        ROLLBACK;
    END;

    -- 提交
    COMMIT;

  END;

  -- 执行清理
  PROCEDURE execute_clean (i_GroupNo IN NUMBER) AS
    CURSOR c_Tab IS
      SELECT *
        FROM data_clean_table
       WHERE valid_flag = '1'
         AND clean_type = CLEAN_TYPE_BATCH
         AND group_no=i_GroupNo
       ORDER BY order_no;

    r_Tab       c_Tab%ROWTYPE;
    v_BeginTime data_clean_log.begin_time%TYPE;
    v_EndTime   data_clean_log.end_time%TYPE;
    v_Result    data_clean_log.result%TYPE;
    v_RowCount  data_clean_log.Row_Count%TYPE;
    v_CleanDate r_Tab.Clean_Date%TYPE;
  BEGIN

    FOR r_Tab IN c_Tab LOOP

      -- 赋值要清理的日期
      v_CleanDate := r_Tab.Clean_Date;
      v_BeginTime := SYSDATE;
      v_RowCount  := 0;
      v_Result    := '';

      --判断是否超过保留日期
      IF GetCurrentBusiDate - to_date(r_Tab.Clean_Date, 'YYYY-MM-DD') <= r_Tab.Retention_Days THEN

        -- 小于等于保留日期不清理
        v_Result := '到达保留天数，无需清理';

      ELSE

        IF substr(r_Tab.clean_sql, 1, 1) <> '@' THEN

          -- 普通清理,指定where条件
          normal_clean(r_Tab.Table_Name,
                       r_Tab.His_Table_Name,
                       r_Tab.Clean_Sql,
                       r_Tab.Clean_Date,
                       r_Tab.Keep_Month_Begin,
                       r_Tab.Keep_Month_End,
                       r_Tab.Split_Month,
                       r_Tab.His_Tablespace,
                       v_RowCount,
                       v_Result);

        ELSIF substr(r_Tab.clean_sql, 1, 1) = '@' THEN

          -- 通过自定义脚本执行清理
          user_defined_clean(r_Tab.Table_Name,
                             r_Tab.His_Table_Name,
                             r_Tab.Clean_Sql,
                             r_Tab.Clean_Date,
                             v_RowCount,
                             v_Result);

        ELSE
          -- 错误的清理类型
          v_Result := '错误的处理类型';
        END IF;

      END IF;

      IF v_Result IS NULL OR v_Result = '' THEN

        -- 清理成功
        v_CleanDate := TO_CHAR(TO_DATE(r_Tab.Clean_Date, 'YYYY-MM-DD') + 1, 'YYYY-MM-DD');
        v_Result    := '清理成功';

        -- 更新清理结果信息
        UPDATE data_clean_table
           SET clean_date = v_CleanDate
         WHERE table_name = r_Tab.table_name;

        IF r_Tab.rebuild_index = '1' THEN

          -- 重建索引
          rebuild_index(r_Tab.Table_Name);

        END IF;

        IF r_Tab.gather_stats = '1' THEN

          -- 收集统计信息
          gather_stats(r_Tab.Table_Name);

        END IF;

      END IF;

      -- 登记清理日志
      v_EndTime := SYSDATE;

      INSERT INTO data_clean_log
        (table_name,
         his_table_name,
         clean_date,
         begin_time,
         end_time,
         row_count,
         RESULT)
      VALUES
        (r_Tab.Table_Name,
         r_Tab.His_Table_Name,
         r_Tab.Clean_Date,
         v_BeginTime,
         v_EndTime,
         v_RowCount,
         v_Result);

      -- 提交
      COMMIT;

    END LOOP;

  END;

  -- 执行清理
  --    CURSOR c_Tab IS
  --         SELECT * FROM data_clean_table WHERE table_name=i_TableName and valid_flag='1';
  --    r_Tab c_Tab%ROWTYPE;
  PROCEDURE execute_clean_one(i_TableName IN VARCHAR2) AS
    r_Tab       data_clean_table%ROWTYPE;
    v_BeginTime data_clean_log.begin_time%TYPE;
    v_EndTime   data_clean_log.end_time%TYPE;
    v_Result    data_clean_log.result%TYPE;
    v_RowCount  data_clean_log.Row_Count%TYPE;
    v_CleanDate r_Tab.Clean_Date%TYPE;
  BEGIN

    --FOR r_Tab IN (SELECT * FROM data_clean_table WHERE table_name=i_TableName and valid_flag='1' )

    SELECT *
      into r_Tab
      FROM data_clean_table
     WHERE table_name = i_TableName
       AND clean_type = CLEAN_TYPE_BATCH;

    --LOOP

    -- 赋值要清理的日期
    v_CleanDate := r_Tab.Clean_Date;
    v_BeginTime := SYSDATE;
    v_RowCount  := 0;
    v_Result    := '';

    --判断是否超过保留日期
    IF GetCurrentBusiDate - to_date(r_Tab.Clean_Date, 'YYYY-MM-DD') <=
       r_Tab.Retention_Days THEN

      -- 小于等于保留日期不清理
      v_Result := '到达保留天数，无需清理';

    ELSE

      IF substr(r_Tab.clean_sql, 1, 1) <> '@' THEN

        -- 普通清理,指定where条件
        normal_clean(r_Tab.Table_Name,
                     r_Tab.His_Table_Name,
                     r_Tab.Clean_Sql,
                     r_Tab.Clean_Date,
                     r_Tab.Keep_Month_Begin,
                     r_Tab.Keep_Month_End,
                     r_Tab.Split_Month,
                     r_Tab.His_Tablespace,
                     v_RowCount,
                     v_Result);

      ELSIF substr(r_Tab.clean_sql, 1, 1) = '@' THEN

        -- 通过自定义脚本执行清理
        user_defined_clean(r_Tab.Table_Name,
                           r_Tab.His_Table_Name,
                           r_Tab.Clean_Sql,
                           r_Tab.Clean_Date,
                           v_RowCount,
                           v_Result);

      ELSE
        -- 错误的清理类型
        v_Result := '错误的处理类型';
      END IF;

    END IF;

    IF v_Result IS NULL OR v_Result = '' THEN

      -- 清理成功
      v_CleanDate := TO_CHAR(TO_DATE(r_Tab.Clean_Date, 'YYYY-MM-DD') + 1,
                             'YYYY-MM-DD');
      v_Result    := '清理成功';

      -- 更新清理结果信息
      UPDATE data_clean_table
         SET clean_date = v_CleanDate
       WHERE table_name = r_Tab.table_name;

      IF r_Tab.rebuild_index = '1' THEN

        -- 重建索引
        rebuild_index(r_Tab.Table_Name);

      END IF;

      IF r_Tab.gather_stats = '1' THEN

        -- 收集统计信息
        gather_stats(r_Tab.Table_Name);

      END IF;

    END IF;

    -- 登记清理日志
    v_EndTime := SYSDATE;

    INSERT INTO data_clean_log
      (table_name,
       his_table_name,
       clean_date,
       begin_time,
       end_time,
       row_count,
       RESULT)
    VALUES
      (r_Tab.Table_Name,
       r_Tab.His_Table_Name,
       r_Tab.Clean_Date,
       v_BeginTime,
       v_EndTime,
       v_RowCount,
       v_Result);

    -- 提交
    COMMIT;

    --END LOOP;

  END;

  -- 登记日志函数
  PROCEDURE add_log(i_Table_Name   VARCHAR2,
                    i_DocTableName VARCHAR2,
                    i_CleanDate    VARCHAR2,
                    i_BeginTime    DATE,
                    i_EndTime      DATE,
                    i_RowCount     NUMBER,
                    i_Result       VARCHAR2) AS
    pragma autonomous_transaction;
  BEGIN
    INSERT INTO data_clean_log
      (table_name,
       his_table_name,
       clean_date,
       begin_time,
       end_time,
       row_count,
       result)
    VALUES
      (i_Table_Name,
       i_DocTableName,
       i_CleanDate,
       i_BeginTime,
       i_EndTime,
       i_RowCount,
       i_Result);
    COMMIT;

  END;

  -- 执行分批清理
  --    CURSOR c_Tab IS
  --         SELECT * FROM data_clean_table WHERE table_name=i_TableName and valid_flag='1';
  --    r_Tab c_Tab%ROWTYPE;
  PROCEDURE execute_clean_step(i_TableName IN VARCHAR2) IS
    r_Tab          data_clean_table%ROWTYPE;
    v_BeginTime    data_clean_log.begin_time%TYPE;
    v_EndTime      data_clean_log.end_time%TYPE;
    v_Result       data_clean_log.result%TYPE;
    v_RowCount     data_clean_log.Row_Count%TYPE;
    v_DocTableName r_Tab.his_table_name%TYPE;
    --v_LastRowNo r_Tab.Last_Serial_No%TYPE;
    --v_StepRows r_Tab.Step_Count%TYPE;
    v_Sql         VARCHAR2(800);
    v_DocCnt      NUMBER := 0;
    v_Cnt         NUMBER := 0;
    v_MinSerialNo NUMBER := 0;
    v_BusiDate    VARCHAR2(10);
  BEGIN

    v_BeginTime := SYSDATE;

    --------------------------------------- 初始判断 ------------------------------------------
    SELECT *
      INTO r_Tab
      FROM data_clean_table
     WHERE table_name = i_TableName
       AND clean_type = CLEAN_TYPE_STEP
       FOR UPDATE;

    IF SQL%NOTFOUND THEN
      -- 找不到要清理的表，退出程序
      v_Result := '找不到要清理的表[' || i_TableName || '] ，退出程序';
      GOTO logging;
    END IF;

    IF r_Tab.Valid_Flag IS NULL OR r_Tab.Valid_Flag != '1' THEN
      v_Result := '此表已置于失效状态，不运行';
      GOTO logging;
    END IF;

    IF r_Tab.Min_Date_Sql IS NULL OR TRIM(r_Tab.Min_Date_Sql) = '' THEN
      -- 输入参数条件不对
      v_Result := 'Min_Date_Sql为空，退出程序';
      GOTO logging;
    END IF;

    IF r_Tab.Last_Serial_No IS NULL THEN
      -- 输入参数条件不对
      v_Result := 'Last_Serial_No为空，退出程序';
      GOTO logging;
    END IF;

    IF r_Tab.Step_Count IS NULL THEN
      -- 输入参数条件不对
      v_Result := 'Step_Count为空，退出程序';
      GOTO logging;
    END IF;

    --判断是否超过保留日期
    IF GetCurrentBusiDate - to_date(r_Tab.clean_date, 'YYYY-MM-DD') <= r_Tab.Retention_Days THEN

      -- 小于等于保留日期不清理
      v_Result := '到达保留天数，无需清理';
      GOTO logging;

    END IF;

    --------------------------------------- 创建历史表 --------------------------------------------
    -- 如果设定历史表按照月份建一张表
    -- 检查历史表是否存在如果不存在则建立历史表
    -- 表名为i_DocTableName||'_YYYYMM'
    IF r_Tab.Split_Month = '1' THEN

      -- 取得历史表名
      IF length(r_Tab.his_table_name) > 23 THEN
        v_DocTableName := substr(r_Tab.his_table_name, 1, 23) || '_' ||
                          Replace(SUBSTR(r_Tab.clean_date, 1, 7), '-', '');

      ELSE
        v_DocTableName := r_Tab.his_table_name || '_' ||
                          Replace(SUBSTR(r_Tab.clean_date, 1, 7), '-', '');

      END IF;

      -- 判断历史表是否存在
      SELECT COUNT(1)
        INTO v_Cnt
        FROM user_tables
       WHERE table_name = v_DocTableName;
      IF SQL%NOTFOUND OR v_Cnt = 0 THEN

        IF r_Tab.his_tablespace IS NOT NULL AND
           length(r_Tab.his_tablespace) > 0 THEN
          v_Sql := 'CREATE TABLE ' || v_DocTableName || ' TABLESPACE "' ||
                   r_Tab.his_tablespace || '" AS SELECT * FROM ' ||
                   i_TableName || ' WHERE 1=2';

        ELSE
          v_Sql := 'CREATE TABLE ' || v_DocTableName ||
                   ' AS SELECT * FROM ' || i_TableName || ' WHERE 1=2';

        END IF;

        BEGIN
          EXECUTE IMMEDIATE v_Sql;
        EXCEPTION
          WHEN OTHERS THEN
            v_Result := '创建历史表[' || v_DocTableName || ']错：' ||
                        substr(SQLERRM, 1, 100);
            ROLLBACK;
            GOTO logging;
        END;
      END IF;

    ELSE
      v_DocTableName := r_Tab.his_table_name;
    END IF;

    --------------------------------------- 开始清理数据 ----------------------------------------

    -- 备份待清理的记录到历史表
    --v_Sql := 'BEGIN INSERT INTO '||v_DocTableName||' SELECT * FROM '||i_TableName ||' WHERE ' || r_Tab.clean_sql ||'; END;';

    v_Sql := 'INSERT INTO ' || v_DocTableName || ' SELECT * FROM ' ||
             i_TableName || ' WHERE ' || r_Tab.clean_sql;

    BEGIN
      EXECUTE IMMEDIATE v_Sql
        USING IN r_Tab.clean_date, r_Tab.last_serial_no, r_Tab.last_serial_no, r_Tab.step_count;

      v_DocCnt := SQL%ROWCOUNT;
    EXCEPTION
      WHEN OTHERS THEN
        v_Result := '备份数据到历史表错：' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
    END;

    -- 删除当前表记录
    --v_Sql := 'BEGIN DELETE FROM '||i_TableName|| ' WHERE ' || r_Tab.clean_sql ||'; END;';
    v_Sql := 'DELETE FROM ' || i_TableName || ' WHERE ' || r_Tab.clean_sql;
    BEGIN
      EXECUTE IMMEDIATE v_Sql
        USING IN r_Tab.clean_date, r_Tab.last_serial_no, r_Tab.last_serial_no, r_Tab.step_count;

      v_Cnt := SQL%ROWCOUNT;
    EXCEPTION
      WHEN OTHERS THEN
        v_Result := '删除当前表记录错：' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
    END;

    IF v_Cnt != v_DocCnt THEN
      -- 判断历史表和当前表记录数是否相等，如果不相等则回滚并退出
      v_Result := '历史表记录数' || v_DocCnt || '和当前表记录数' || v_Cnt || '不相等!';
      ROLLBACK;
      GOTO logging;
    END IF;

    v_RowCount := v_Cnt;

    --------------------------------------- 计算下一次清理条件 ----------------------------------------

    --取得表最小的序列号，以及业务日期
    BEGIN
      EXECUTE IMMEDIATE r_Tab.Min_Date_Sql
        INTO v_MinSerialNo, v_BusiDate;
      IF SQL%NOTFOUND THEN
        v_Result := '执行MinDateSql操作没有返回记录' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
      END IF;

      IF v_BusiDate IS NULL OR TRIM(v_BusiDate) = '' THEN
        v_Result := '执行MinDateSql操作返回busi_date为空' ||
                    substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
      END IF;

      IF v_MinSerialNo IS NULL THEN
        v_Result := '执行MinDateSql操作返回v_MinSerialNo为空' ||
                    substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
      END IF;

    EXCEPTION
      WHEN OTHERS THEN
        v_Result := '执行MinDateSql操作错误：' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
    END;

    BEGIN
      -- 更新清理日期
      UPDATE data_clean_table
         SET clean_date = v_BusiDate, last_serial_no = v_MinSerialNo
       WHERE table_name = r_Tab.table_name;

      IF SQL%NOTFOUND THEN
        v_Result := '执行更新清理日期操作没有返回记录' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
      END IF;

    EXCEPTION
      WHEN OTHERS THEN
        v_Result := '更新清理日期错误：' || substr(SQLERRM, 1, 100);
        ROLLBACK;
        GOTO logging;
    END;

    v_Result := 'OK';

  <<logging>>
  -- 登记清理日志
    v_EndTime := SYSDATE;
    add_log(r_Tab.Table_Name,
            r_Tab.His_Table_Name,
            r_Tab.Clean_Date,
            v_BeginTime,
            v_EndTime,
            v_RowCount,
            v_Result);

    -- 提交
    IF v_Result = 'OK' THEN
      COMMIT;
    ELSE
      ROLLBACK;
    END IF;
  END;

 --收集表变化量
 PROCEDURE ANALYZE_ROWS(i_date IN VARCHAR2) AS
  -- Local variables here
   D_DATE     VARCHAR2(1024);
   I_COUNT    NUMBER;
   str_err    VARCHAR2(1024);
 BEGIN
    I_COUNT :=0;
    D_DATE := TO_CHAR(SYSDATE,'YYYY-MM-DD');


    SELECT COUNT(1) INTO I_COUNT FROM TABLE_ROWS A WHERE A.ANALYZE_DATE=D_DATE;
    IF I_COUNT=0 THEN

      INSERT INTO TABLE_ROWS (SELECT A.TABLE_NAME,A.NUM_ROWS,D_DATE FROM DBA_TABLES A );
      COMMIT;
    END IF;

    EXCEPTION
      WHEN OTHERS THEN
        str_err := '收集表行数error：' || substr(SQLERRM,1,100);
        dbms_output.put_line(str_err);
        NULL;

  END;

end PKG_DATA_CLEANER;`
