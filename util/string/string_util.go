package stringutil

import (
	"crypto/md5"
	"database/sql"
	"encoding/base64"
	"errors"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/godror"
	"io"
	"net/url"
	"strconv"
	"strings"
)

func GenCommaSeparatedString(arrayString []string) string {
	var builder strings.Builder
	for i, item := range arrayString {
		if i > 0 {
			builder.WriteString(",")
		}
		builder.WriteString("'")
		builder.WriteString(item)
		builder.WriteString("'")
	}
	return builder.String()
}

func ExtractKeySuffix(key string) (string, error) {
	subs := strings.Split(key, "/")
	if len(subs) < 2 {
		return "", errors.New(fmt.Sprintf("invalidate key:%s", key))
	}
	return subs[len(subs)-1], nil
}

type MD5Option struct {
	Upper bool
	Salt  string
}

type MD5OptionFunc func(*MD5Option)

func WithMD5Upper() MD5OptionFunc {
	return func(option *MD5Option) {
		option.Upper = true
	}
}

func WithMD5Salt(salt string) MD5OptionFunc {
	return func(option *MD5Option) {
		option.Salt = salt
	}
}

func MD5(data string, opts ...MD5OptionFunc) string {
	option := MD5Option{}
	for _, o := range opts {
		o(&option)
	}

	if option.Salt != "" {
		data = data + "@TMS@" + option.Salt
	}

	stringCaseFunc := strings.ToLower
	if option.Upper {
		stringCaseFunc = strings.ToUpper
	}

	t := md5.New()
	io.WriteString(t, data)

	md5Str := fmt.Sprintf("%x", t.Sum(nil))
	return stringCaseFunc(md5Str)
}

func Decimal(value float64) float64 {
	value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return value
}

func FormatSingleSQLForOracle(sql string) string {
	sql = strings.TrimSpace(sql)
	if strings.HasSuffix(sql, ";") {
		return strings.TrimSuffix(sql, ";")
	} else {
		return sql
	}
}

func BoolToYesOrNo(b bool) string {
	if b {
		return "YES"
	}
	return "NO"
}

func BoolToTrueOrFalse(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

func GetCaseFunc(caseVal int) func(string) string {
	switch caseVal {
	case 1:
		return strings.ToLower
	case 2:
		return strings.ToUpper
	}
	return func(s string) string {
		return s
	}
}

func RemoveSpecialLetterForLog(querySQL string) string {
	return strings.ReplaceAll(strings.ReplaceAll(querySQL, "\t", " "), "\n", " ")
}

func DecodeSQL(originStr string) (string, error) {
	decodedString, err := url.QueryUnescape(originStr)
	if err != nil {
		return "", err
	}

	decodedBytes, err := base64.StdEncoding.DecodeString(decodedString)
	if err != nil {
		return "", err
	}

	return string(decodedBytes), nil
}

func EncodeSQL(originStr string) string {
	encodedBytes := base64.StdEncoding.EncodeToString([]byte(originStr))
	encodedString := url.QueryEscape(encodedBytes)

	return encodedString
}

func GetStringFromAny(jsonData any) (string, error) {
	if jsonData == nil {
		return "", nil
	}
	switch d := jsonData.(type) {
	case godror.JSON:
		return d.GetJSONString()
	case sql.NullString:
		return d.String, nil
	default:
		return "", fmt.Errorf("unsupported type %T", d)
	}
}

func IsEmpty(state string) bool {
	return state == "" || strings.TrimSpace(state) == ""
}
