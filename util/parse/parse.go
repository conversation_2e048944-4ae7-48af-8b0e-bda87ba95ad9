package parse

import (
	"encoding/binary"
	"fmt"
	"math"
	"net"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/structs"
)

const (
	base    = 10
	bitSize = 64
)

// Int 泛型
type Int interface {
	int | int8 | int16 | int32 | int64
}

// Uint 泛型
type Uint interface {
	uint | uint8 | uint16 | uint32 | uint64
}

// FormatInt Int类型转换为十进制字符串
func FormatInt[T Int](i T) string {
	return strconv.FormatInt(int64(i), base)
}

func FormatFloat(f float64, prec int) string {
	return strconv.FormatFloat(f, 'f', prec, 64)
}

// FormatUint Uint类型转换为十进制字符串
func FormatUint[T Uint](i T) string {
	return strconv.FormatUint(uint64(i), base)
}

// ParseInt64 字符串转换为int64
func ParseInt64(s string) (int64, error) {
	return strconv.ParseInt(strings.TrimSpace(s), base, bitSize)
}

// ParseInt 字符串转换为int
func ParseInt(s string) (int, error) {
	return strconv.Atoi(strings.TrimSpace(s))
}

// ParseAnyInt any to int
func ParseAnyInt(val any) (int, error) {
	return ParseInt(fmt.Sprintf("%v", val))
}

// ParseInt8 字符串转换为int
func ParseInt8(s string) (int8, error) {
	x, err := strconv.Atoi(strings.TrimSpace(s))
	if err != nil {
		return 0, err
	}
	return int8(x), nil
}

// ParseInt32 字符串转换为int
func ParseInt32(s string) (int32, error) {
	x, err := strconv.Atoi(strings.TrimSpace(s))
	if err != nil {
		return 0, err
	}
	return int32(x), nil
}

// ParseUInt 字符串转换为uint
func ParseUInt(s string) (uint, error) {
	value, err := strconv.Atoi(strings.TrimSpace(s))
	if err != nil {
		return 0, err
	}
	return uint(value), nil
}

// ParseUInt64 字符串转换为uint64
func ParseUInt64(s string) (uint64, error) {
	return strconv.ParseUint(strings.TrimSpace(s), base, bitSize)
}

// ParseUInt32 字符串转换为uint32
func ParseUInt32(s string) (uint32, error) {
	x, err := strconv.ParseUint(strings.TrimSpace(s), base, bitSize)
	if err != nil {
		return 0, err
	}
	return uint32(x), nil
}

// ParseFloat64 字符串转换为float64
func ParseFloat64(s string) (float64, error) {
	return strconv.ParseFloat(strings.TrimSpace(s), bitSize)
}

// ParseFloat32 字符串转换为float64
func ParseFloat32(s string) (float32, error) {
	v, err := strconv.ParseFloat(strings.TrimSpace(s), bitSize)
	return float32(v), err
}

// IntToIP int ip to string
func IntToIP(nn uint32) string {
	obj := make(net.IP, 4)
	binary.BigEndian.PutUint32(obj, nn)
	return obj.String()
}

func CountDigits(num int) int {
	strNum := strconv.Itoa(num)
	return len(strNum)
}

func ZeroPad(num int, desiredDigits int) string {
	strNum := strconv.Itoa(num)
	currentDigits := len(strNum)
	if currentDigits >= desiredDigits {
		return strNum
	}
	zeroesNeeded := desiredDigits - currentDigits
	zeroes := "0"
	paddedNum := strNum
	for i := 0; i < zeroesNeeded; i++ {
		paddedNum = zeroes + paddedNum
	}
	return paddedNum
}

// raw format is :
// [2023/10/10 16:54:15.549 +08:00] [INFO] [import.go:1372] [progress] [total=14.9%] [tables="0/1 (0.0%)"] [chunks="16/65 (24.6%)"] [engines="0/2 (0.0%)"] [restore-bytes=875.4MiB/2.864GiB] [restore-rows=1915312/6416018(estimated)] [import-bytes=0B/2.806GiB(estimated)] ["encode speed(MiB/s)"=88.17730924791287] [state=writing] [remaining=57s]
// [2023/10/10 16:54:15.549 +08:00] [INFO] [import.go:1372] [progress] [total=14.9%] [tables="0/1 (0.0%)"] [chunks="16/65 (24.6%)"] [engines="0/2 (0.0%)"] [restore-bytes=875.4MiB/2.864GiB] [restore-rows=1915312/6416018(estimated)] [import-bytes=0B/2.806GiB(estimated)] ["encode speed(MiB/s)"=88.17730924791287] [state=writing] []
func ParseLightningProgressLog(logLine string) *structs.LightningProgress {

	logLine = strings.TrimSpace(logLine)
	logLine = strings.TrimPrefix(logLine, "[")
	logLine = strings.TrimSuffix(logLine, "]")
	logLine = strings.TrimSpace(logLine)

	logTokens := strings.Split(logLine, "] [")

	var p structs.LightningProgress

	for idx, token := range logTokens {
		token := strings.TrimSpace(token)
		if token == "" {
			continue
		}
		switch idx {
		case 0:
			p.Time = token
		case 1:
			p.LogLevel = token
		case 4:
			p.Total = strings.TrimPrefix(token, "total=")
		case 5:
			value := strings.TrimPrefix(token, "tables=")
			value = strings.TrimPrefix(value, "\"")
			value = strings.TrimSuffix(value, "\"")
			p.Tables = value
		case 6:
			value := strings.TrimPrefix(token, "chunks=")
			value = strings.TrimPrefix(value, "\"")
			value = strings.TrimSuffix(value, "\"")
			p.Chunks = value
		case 7:
			value := strings.TrimPrefix(token, "engines=")
			value = strings.TrimPrefix(value, "\"")
			value = strings.TrimSuffix(value, "\"")
			p.Engines = value
		case 8:
			p.RestoreBytes = strings.TrimPrefix(token, "restore-bytes=")
		case 9:
			p.RestoreRows = strings.TrimPrefix(token, "restore-rows=")
		case 10:
			p.ImportBytes = strings.TrimPrefix(token, "import-bytes=")
		case 11:
			p.EncodeSpeed = strings.TrimPrefix(token, `"encode speed(MiB/s)"=`)
		case 12:
			p.State = strings.TrimPrefix(token, "state=")
		case 13:
			p.Remaining = strings.TrimPrefix(token, "remaining=")

		}
	}

	// 因为有些日志中没有remaining字段，所以这里需要做一下兼容
	if p.Remaining == "" {
		p.Remaining = "0s"
	}
	return &p
}

func TruncateFloat(num float64, precision int) float64 {
	multiplier := math.Pow(10, float64(precision))
	return math.Floor(num*multiplier) / multiplier
}

func ParseBoolWithDefault(val string, defaultVal bool) bool {
	val = strings.TrimSpace(val)
	if val == "" {
		return defaultVal
	}
	pVal, err := strconv.ParseBool(val)
	if err == nil {
		return pVal
	}
	return defaultVal
}

func ParsePositiveIntWithDefault(val string, defaultVal int) int {
	val = strings.TrimSpace(val)
	if val == "" {
		return defaultVal
	}
	pVal, err := strconv.Atoi(val)
	if err == nil {
		if pVal > 0 {
			return pVal
		}
	}
	return defaultVal
}
