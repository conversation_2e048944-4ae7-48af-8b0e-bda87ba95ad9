package parse

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
)

func TestParseInt64(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		{
			name: "fail",
			args: args{
				s: "a",
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				s: "1",
			},
			want:    1,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseInt64(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseInt64() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ParseInt64() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestParseFloat64(t *testing.T) {
	convey.Convey("success", t, func() {
		got, err := ParseFloat64(" 1.2")
		assert.NoError(t, err)
		convey.So(got, convey.ShouldEqual, 1.2)
	})
	convey.Convey("success", t, func() {
		got, err := ParseFloat64(" 1.x")
		assert.Error(t, err)
		convey.So(got, convey.ShouldEqual, 0)
	})
}

func TestParseInt(t *testing.T) {
	convey.Convey("success", t, func() {
		got, err := ParseInt(" 1")
		assert.NoError(t, err)
		convey.So(got, convey.ShouldEqual, 1)
	})
}
