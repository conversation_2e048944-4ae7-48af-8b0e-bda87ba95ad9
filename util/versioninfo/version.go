// Copyright 2020 PingCAP, Inc. Licensed under Apache-2.0.
package versioninfo

import (
	"context"
	"database/sql"
	"regexp"
	"strings"

	"gitee.com/pingcap_enterprise/tms/util/log"

	"github.com/coreos/go-semver/semver"
	"github.com/pingcap/errors"
	"go.uber.org/zap"
)

var (
	versionHash = regexp.MustCompile("-[0-9]+-g[0-9a-f]{7,}")
)

// ServerInfo is the combination of ServerType and ServerInfo
type ServerInfo struct {
	ServerType    ServerType
	ServerVersion *semver.Version
	HasTiKV       bool
}

// removeVAndHash sanitizes a version string.
func removeVAndHash(v string) string {
	v = versionHash.ReplaceAllLiteralString(v, "")
	v = strings.TrimSuffix(v, "-dirty")
	return strings.TrimPrefix(v, "v")
}

// FetchVersion gets the version information from the database server
//
// NOTE: the executed query will be:
// - `select tidb_version()` if target db is tidb
// - `select version()` if target db is not tidb
func FetchVersion(ctx context.Context, db *sql.DB) (string, error) {
	var versionInfo string
	const queryTiDB = "SELECT tidb_version();"
	tidbRow := db.QueryRowContext(ctx, queryTiDB)
	err := tidbRow.Scan(&versionInfo)
	if err == nil && tidbReleaseVersionFullRegex.FindString(versionInfo) != "" {
		return versionInfo, nil
	}
	log.Warn("select tidb_version() failed, will fallback to 'select version();'", zap.Error(err))
	const query = "SELECT version();"
	row := db.QueryRowContext(ctx, query)
	err = row.Scan(&versionInfo)
	if err != nil {
		return "", errors.Annotatef(err, "sql: %s", query)
	}
	return versionInfo, nil
}

type ServerType int

const (
	// ServerTypeUnknown represents unknown server type
	ServerTypeUnknown = iota
	// ServerTypeMySQL represents MySQL server type
	ServerTypeMySQL
	// ServerTypeMariaDB represents MariaDB server type
	ServerTypeMariaDB
	// ServerTypeTiDB represents TiDB server type
	ServerTypeTiDB

	// ServerTypeAll represents All server types
	ServerTypeAll
)

var serverTypeString = []string{
	ServerTypeUnknown: "Unknown",
	ServerTypeMySQL:   "MySQL",
	ServerTypeMariaDB: "MariaDB",
	ServerTypeTiDB:    "TiDB",
}

// String implements Stringer.String
func (s ServerType) String() string {
	if s >= ServerTypeAll {
		return ""
	}
	return serverTypeString[s]
}

var (
	mysqlVersionRegex = regexp.MustCompile(`^\d+\.\d+\.\d+([0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?`)
	// `select version()` result
	tidbVersionRegex = regexp.MustCompile(`-[v]?\d+\.\d+\.\d+([0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?`)
	// `select tidb_version()` result
	tidbReleaseVersionRegex = regexp.MustCompile(`v\d+\.\d+\.\d+([0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?`)
	// `select tidb_version()` result with full release version
	tidbReleaseVersionFullRegex = regexp.MustCompile(`Release Version:\s*v\d+\.\d+\.\d+([0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?`)
)

// ParseServerInfo parses exported server type and version info from version string
func ParseServerInfo(src string) ServerInfo {
	lowerCase := strings.ToLower(src)
	serverInfo := ServerInfo{}
	isReleaseVersion := false
	switch {
	case strings.Contains(lowerCase, "release version:"):
		// this version string is tidb release version
		serverInfo.ServerType = ServerTypeTiDB
		isReleaseVersion = true
	case strings.Contains(lowerCase, "tidb"):
		serverInfo.ServerType = ServerTypeTiDB
	case strings.Contains(lowerCase, "mariadb"):
		serverInfo.ServerType = ServerTypeMariaDB
	case mysqlVersionRegex.MatchString(lowerCase):
		serverInfo.ServerType = ServerTypeMySQL
	default:
		serverInfo.ServerType = ServerTypeUnknown
	}

	var versionStr string
	if serverInfo.ServerType == ServerTypeTiDB {
		if isReleaseVersion {
			versionStr = tidbReleaseVersionRegex.FindString(src)
		} else {
			versionStr = tidbVersionRegex.FindString(src)
			versionStr = strings.TrimPrefix(versionStr, "-")
		}
		versionStr = strings.TrimPrefix(versionStr, "v")
	} else {
		versionStr = mysqlVersionRegex.FindString(src)
	}

	var err error
	serverInfo.ServerVersion, err = semver.NewVersion(versionStr)
	if err != nil {
		log.Warn("fail to parse version, fallback to 0.0.0",
			zap.String("version", versionStr))
		serverInfo.ServerVersion = semver.New("0.0.0")
	}

	log.Debug("detect server version",
		zap.String("type", serverInfo.ServerType.String()),
		zap.String("version", serverInfo.ServerVersion.String()))

	return serverInfo
}
