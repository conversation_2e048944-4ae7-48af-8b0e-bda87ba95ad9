package versioninfo

import (
	"fmt"
	"runtime"

	"gitee.com/pingcap_enterprise/tms/util/log"
)

// Version information.
var (
	// GitHash will be set during make
	GitHash = "Not provided (use make build instead of go build)"
	//GitBranch will be set during make
	GitBranch = "Not provided (use make build instead of go build)"
	// BuildTS and BuildTS will be set during make
	BuildTS = "Not provided (use make build instead of go build)"
	// ReleaseVersion will be set during make, default value is v1.0
	ReleaseVersion = "Not provided (use make build instead of go build)"
)

// GetRawVersionInfo do what its name tells
func GetRawVersionInfo() string {
	var info string
	info += fmt.Sprintf("Release Version: %s\n", ReleaseVersion)
	info += fmt.Sprintf("Git Commit Hash: %s\n", GitHash)
	info += fmt.Sprintf("Git Branch: %s\n", GitBranch)
	info += fmt.Sprintf("Build TS: %s\n", BuildTS)
	info += fmt.Sprintf("Go Version: %s\n", runtime.Version())
	info += fmt.Sprintf("Go OS/Arch: %s/%s\n", runtime.GOOS, runtime.GOARCH)
	return info
}

func PrintVersionInfo() {
	log.Info("Welcome to TiMS ",
		fmt.Sprintf("[Release Version: %s]", ReleaseVersion),
		fmt.Sprintf("[Git Commit Hash: %s]", GitHash),
		fmt.Sprintf("[Git Branch: %s]", GitBranch),
		fmt.Sprintf("[Build TS: %s]", BuildTS),
		fmt.Sprintf("[Go Version: %s]", runtime.Version()),
		fmt.Sprintf("[Go OS/Arch: %s]", runtime.GOOS+"/"+runtime.GOARCH),
	)
}
