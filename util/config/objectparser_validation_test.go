package config

import (
	"gitee.com/pingcap_enterprise/tms/util/log"
	"testing"
)

func TestValidateObjectParserConfig(t *testing.T) {
	// Initialize log for testing - use a minimal setup
	_ = log.DefaultLogConfig

	tests := []struct {
		name         string
		config       *ObjectParserConfig
		expectedHost string
		shouldLog    bool
	}{
		{
			name: "Valid IP address",
			config: &ObjectParserConfig{
				Enable: true,
				Host:   "***********",
				Port:   8080,
			},
			expectedHost: "***********",
			shouldLog:    false,
		},
		{
			name: "Valid domain name",
			config: &ObjectParserConfig{
				Enable: true,
				Host:   "example.com",
				Port:   8080,
			},
			expectedHost: "example.com",
			shouldLog:    false,
		},
		{
			name: "Host with http prefix - should be cleaned",
			config: &ObjectParserConfig{
				Enable: true,
				Host:   "http://example.com",
				Port:   8080,
			},
			expectedHost: "example.com",
			shouldLog:    true,
		},
		{
			name: "Host with https prefix - should be cleaned",
			config: &ObjectParserConfig{
				Enable: true,
				Host:   "https://***********",
				Port:   8080,
			},
			expectedHost: "***********",
			shouldLog:    true,
		},
		{
			name: "Host with whitespace - should be trimmed",
			config: &ObjectParserConfig{
				Enable: true,
				Host:   "  localhost  ",
				Port:   8080,
			},
			expectedHost: "localhost",
			shouldLog:    false,
		},
		{
			name: "Disabled config - should not validate",
			config: &ObjectParserConfig{
				Enable: false,
				Host:   "http://invalid",
				Port:   8080,
			},
			expectedHost: "http://invalid", // Should remain unchanged since validation is skipped
			shouldLog:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := &Config{
				ObjectParserConfig: tt.config,
			}

			// Call validation
			cfg.validateObjectParserConfig()

			// Check if host was updated correctly
			if tt.config.Host != tt.expectedHost {
				t.Errorf("Expected host to be '%s', but got '%s'", tt.expectedHost, tt.config.Host)
			}
		})
	}
}

func TestIsValidDomainName(t *testing.T) {
	tests := []struct {
		domain string
		valid  bool
	}{
		{"example.com", true},
		{"sub.example.com", true},
		{"localhost", true},
		{"test-server", true},
		{"", false},
		{"example..com", false},
		{"-example.com", false},
		{"example-.com", false},
		{"example.com-", false},
		{"very-long-domain-name-that-exceeds-the-maximum-allowed-length-for-a-single-label-which-is-sixty-three-characters.com", false},
	}

	for _, tt := range tests {
		t.Run(tt.domain, func(t *testing.T) {
			result := isValidDomainName(tt.domain)
			if result != tt.valid {
				t.Errorf("isValidDomainName(%s) = %v, want %v", tt.domain, result, tt.valid)
			}
		})
	}
}
