package config

import (
	"fmt"
	"path"

	"gitee.com/pingcap_enterprise/tms/common/constants"
)

func getLogFileRootPattern() string {
	if GetGlobalConfig().IsClusterMode() {
		return "{data_dir}/logs"
	} else {
		return path.Join(GetGlobalConfig().DataDir, "logs")
	}
}

func GetDataRootPattern() string {
	if GetGlobalConfig().IsClusterMode() {
		return "{data_dir}"
	} else {
		return GetGlobalConfig().DataDir
	}
}

func BuildAssessmentLogFilePath(channelId, taskId int) string {
	logFile := fmt.Sprintf("assessment-%d-%d.log", channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildStatisticLogFilePath(channelId, taskId int) string {
	logFile := fmt.Sprintf("tidbstats-%d-%d.log", channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildSQLAnalyzerLogFilePath(channelId, taskId int) string {
	logFile := fmt.Sprintf("sqlanalyzer-%d-%d.log", channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildO2TSyncDiffLogFilePath(channelId, taskId int) string {
	logFile := fmt.Sprintf("o2t_sync_diff-%d-%d.log", channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildMigrationFullDataLogFilePath(channelId, taskId int) string {
	logFile := BuildMigrationLogFileName(constants.MigrationTypeFullData, channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildMigrationCSVLogFilePath(channelId, taskId int) string {
	logFile := BuildMigrationLogFileName(constants.MigrationTypeCSV, channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildLightningLogFilePath(channelId, taskId int) string {
	logFile := BuildMigrationLogFileName(constants.MigrationTypeLightning, channelId, taskId)
	return path.Join(getLogFileRootPattern(), logFile)
}

func BuildMigrationLogFileName(migrationType constants.MigrationType, channelId, taskId int) string {

	if GetGlobalConfig().IsClusterMode() {
		switch migrationType {
		case constants.MigrationTypeFullData:
			return fmt.Sprintf("migration_full-%d-%d.log", channelId, taskId)
		case constants.MigrationTypeCSV:
			return fmt.Sprintf("export_csv/migration_csv-%d-%d.log", channelId, taskId)
		case constants.MigrationTypeLightning:
			return fmt.Sprintf("import_csv/lightning-%d-%d.log", channelId, taskId)
		default:
			return fmt.Sprintf("migration-%d-%d.log", channelId, taskId)
		}
	} else {
		switch migrationType {
		case constants.MigrationTypeFullData:
			return fmt.Sprintf("migration_full-%d-%d.log", channelId, taskId)
		case constants.MigrationTypeCSV:
			return fmt.Sprintf("export_csv/migration_csv-%d-%d.log", channelId, taskId)
		case constants.MigrationTypeLightning:
			return fmt.Sprintf("import_csv/lightning-%d-%d-.*.log", channelId, taskId)
		default:
			return fmt.Sprintf("migration-%d-%d.log", channelId, taskId)
		}
	}
}
