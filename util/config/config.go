package config

import (
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"net"
	"os"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/models"

	"gitee.com/pingcap_enterprise/tms/util/log"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/versioninfo"
	"github.com/BurntSushi/toml"
)

// Config is server configuration.
type Config struct {
	*flag.FlagSet `json:"-"`

	Mode string `json:"mode"`

	ServerRole string `json:"server-role"`

	ClusterId string `toml:"cluster-id" json:"cluster-id"`

	ServerId string `toml:"server-id" json:"server-id"`

	TaskConcurrent int `toml:"task-concurrent" json:"task-concurrent"`

	EtcdEndpoints []string `toml:"etcd-endpoints" json:"etcd-endpoints"`

	ServerHost string `toml:"server-host" json:"server-host"`

	ServerPort int `toml:"server-port" json:"server-port"`

	DataDir string `toml:"data-dir" json:"data-dir"`

	DBConfig *models.DBConfig `toml:"db" json:"db"`

	LogConfig *log.LogConfig `toml:"log" json:"log"`

	IncrementConfig *IncrementConfig `toml:"increment" json:"increment"`

	ObjectParserConfig *ObjectParserConfig `toml:"object-parser" json:"object-parser"`

	ConfigFile string `json:"config-file"`

	PrintVersion bool

	GenKey bool

	Encrypt bool

	EnablePprof bool `toml:"enable-pprof" json:"enable-pprof"`
	PprofPort   int  `toml:"pprof-port" json:"pprof-port"`

	License string `toml:"license" json:"license"`

	StaticFilePath string `toml:"static-file-path" json:"static-file-path"`
}

var globalConfig *Config

func NewConfig(serverRole string) *Config {
	cfg := &Config{}
	cfg.ServerRole = serverRole
	cfg.FlagSet = flag.NewFlagSet("tims", flag.ContinueOnError)
	fs := cfg.FlagSet
	fs.Usage = func() {
		fmt.Fprintln(os.Stderr, "Usage of tims-server:")
		fs.PrintDefaults()
	}

	fs.BoolVar(&cfg.PrintVersion, "V", false, "print version information and exit")
	fs.StringVar(&cfg.ConfigFile, "config", "./config.toml", "path to the configuration file")
	fs.BoolVar(&cfg.GenKey, "genKey", false, "generate key function")
	fs.BoolVar(&cfg.Encrypt, "encrypt", false, "encrypt string")

	globalConfig = cfg
	return cfg
}
func GetGlobalConfig() *Config {
	return globalConfig
}
func (cfg *Config) String() string {
	config, err := json.Marshal(cfg)
	if err != nil {
		return "<nil>"
	}
	return string(config)
}

// Parse parses all config from command-line flags, environment vars or the configuration file
func (cfg *Config) Parse(args []string) error {
	err := cfg.FlagSet.Parse(args)
	switch err {
	case nil:
	case flag.ErrHelp:
		os.Exit(0)
	default:
		os.Exit(2)
	}

	if cfg.PrintVersion {
		fmt.Println(versioninfo.GetRawVersionInfo())
		os.Exit(0)
	}

	if cfg.ConfigFile != "" {
		if err = cfg.configFromFile(cfg.ConfigFile); err != nil {
			return err
		}
	} else {
		return errors.New("no config file")
	}
	cfg.rebuildConfig()
	return nil
}

func (cfg *Config) configFromFile(path string) error {
	metaData, err := toml.DecodeFile(path, cfg)
	if err != nil {
		return err
	}

	if undecoded := metaData.Undecoded(); len(undecoded) > 0 {
		var undecodedItems []string
		for _, item := range undecoded {
			undecodedItems = append(undecodedItems, item.String())
		}
		err = errors.New(fmt.Sprintf("server config file %s contained unknown configuration options: %s",
			path, strings.Join(undecodedItems, ", ")))
	}

	return err
}

func (cfg *Config) rebuildConfig() {
	if cfg.TaskConcurrent == 0 {
		cfg.TaskConcurrent = 1
	}
	if cfg.ServerPort == 0 {
		cfg.ServerPort = constants.ServerPort
	}
	if cfg.DataDir == "" {
		cfg.DataDir = constants.DataDir
	}
	if cfg.StaticFilePath == "" {
		cfg.StaticFilePath = "./dist"
	}
	if strings.EqualFold(strings.TrimSpace(cfg.ClusterId), "") && cfg.Mode == constants.CLUSTER_MODE {
		cfg.ClusterId = "cluster-1"
	}
	cfg.rebuildDBConfig()
	cfg.rebuildLogConfig()
	cfg.validateObjectParserConfig()
}

func (cfg *Config) rebuildDBConfig() {
	if cfg.DBConfig == nil {
		cfg.DBConfig = models.DefaultDBConfig
	}

	if cfg.DBConfig.DBFileDir == "" {
		cfg.DBConfig.DBFileDir = cfg.DataDir
	}
	if cfg.DBConfig.DBLogLevel == "" {
		cfg.DBConfig.DBLogLevel = "info"
	}
	if cfg.DBConfig.DBLogFile == "" {
		cfg.DBConfig.DBLogLevel = constants.LogFileSql
	}
}

func (cfg *Config) rebuildLogConfig() {
	if cfg.LogConfig == nil {
		cfg.LogConfig = log.DefaultLogConfig
	}
	if cfg.LogConfig.LogLevel == "" {
		cfg.LogConfig.LogLevel = log.DefaultLogConfig.LogLevel
	}
	if cfg.LogConfig.LogOutput == "" {
		cfg.LogConfig.LogOutput = log.DefaultLogConfig.LogOutput
	}
	if cfg.LogConfig.LogFileRoot == "" {
		cfg.LogConfig.LogFileRoot = cfg.DataDir + constants.LogDirPrefix
	} else if !strings.HasSuffix(cfg.LogConfig.LogFileRoot, "/") {
		cfg.LogConfig.LogFileRoot += "/"
	}
	if cfg.LogConfig.LogFileName == "" {
		cfg.LogConfig.LogFileName = log.DefaultLogConfig.LogFileName
	}
	if cfg.LogConfig.LogMaxSize == 0 {
		cfg.LogConfig.LogMaxSize = log.DefaultLogConfig.LogMaxSize
	}

	if cfg.LogConfig.LogMaxAge == 0 {
		cfg.LogConfig.LogMaxAge = log.DefaultLogConfig.LogMaxAge
	}

	if cfg.LogConfig.LogMaxBackups == 0 {
		cfg.LogConfig.LogMaxBackups = log.DefaultLogConfig.LogMaxBackups
	}
}

// IsClusterMode returns true if the server is running in cluster mode
func (cfg *Config) IsClusterMode() bool {
	return cfg.Mode == constants.CLUSTER_MODE
}

// GetLogConfig  returns the log config
func (cfg *Config) GetLogConfig() *log.LogConfig {
	return cfg.LogConfig
}

func initTestConfig() {
	globalConfig = &Config{
		FlagSet:        nil,
		Mode:           "",
		ServerRole:     "",
		ClusterId:      "",
		ServerId:       "",
		TaskConcurrent: 0,
		EtcdEndpoints:  nil,
		ServerHost:     "",
		ServerPort:     0,
		DataDir:        "",
		DBConfig:       nil,
		LogConfig:      log.DefaultLogConfig,
		ConfigFile:     "",
		PrintVersion:   false,
		GenKey:         false,
		License:        "",
		StaticFilePath: "",
	}
}

// validateObjectParserConfig validates the ObjectParserConfig settings
func (cfg *Config) validateObjectParserConfig() {
	if cfg.ObjectParserConfig == nil {
		return
	}

	if !cfg.ObjectParserConfig.Enable {
		return
	}

	// Validate Host field - should not contain protocol prefixes
	if cfg.ObjectParserConfig.Host != "" {
		host := strings.TrimSpace(cfg.ObjectParserConfig.Host)

		// Check for protocol prefixes and remove them with warning
		if strings.HasPrefix(strings.ToLower(host), "http://") {
			if log.GetRootLogger() != nil {
				log.Warnf("ObjectParser Host contains 'http://' prefix, removing it. Host should only contain IP or domain name")
			}
			cfg.ObjectParserConfig.Host = strings.TrimPrefix(strings.ToLower(host), "http://")
		} else if strings.HasPrefix(strings.ToLower(host), "https://") {
			if log.GetRootLogger() != nil {
				log.Warnf("ObjectParser Host contains 'https://' prefix, removing it. Host should only contain IP or domain name")
			}
			cfg.ObjectParserConfig.Host = strings.TrimPrefix(strings.ToLower(host), "https://")
		} else {
			cfg.ObjectParserConfig.Host = host
		}

		// Validate that the cleaned host is a valid IP or domain
		if cfg.ObjectParserConfig.Host != "" {
			if net.ParseIP(cfg.ObjectParserConfig.Host) == nil {
				// Not a valid IP, check if it's a valid domain name
				if !isValidDomainName(cfg.ObjectParserConfig.Host) {
					if log.GetRootLogger() != nil {
						log.Errorf("ObjectParser Host '%s' is neither a valid IP address nor a valid domain name", cfg.ObjectParserConfig.Host)
					}
				}
			}
		}
	}
}

// isValidDomainName checks if a string is a valid domain name
func isValidDomainName(domain string) bool {
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}

	// Check for valid characters and structure
	if strings.HasSuffix(domain, ".") {
		domain = domain[:len(domain)-1]
	}

	labels := strings.Split(domain, ".")
	for _, label := range labels {
		if len(label) == 0 || len(label) > 63 {
			return false
		}

		// Check if label contains only valid characters
		for _, char := range label {
			if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') ||
				(char >= '0' && char <= '9') || char == '-') {
				return false
			}
		}

		// Labels cannot start or end with hyphen
		if strings.HasPrefix(label, "-") || strings.HasSuffix(label, "-") {
			return false
		}
	}

	return true
}

type IncrementConfig struct {
	Enable                             bool   `toml:"enable" json:"enable"`
	DsgHost                            string `toml:"dsg-host" json:"dsg-host"`
	DsgPort                            int    `toml:"dsg-port" json:"dsg-port"`
	DsgAutoMaticEngineBootPort         int    `toml:"dsg-auto-matic-engine-boot-port" json:"dsg-auto-matic-engine-boot-port"`
	ExternalDsgHost                    string `toml:"external-dsg-host" json:"external-dsg-host"`
	ExternalDsgPort                    int    `toml:"external-dsg-port" json:"external-dsg-port"`
	ExternalDsgAutoMaticEngineBootPort int    `toml:"external-dsg-auto-matic-engine-boot-port" json:"external-dsg-auto-matic-engine-boot-port"`

	DsgUserID int `toml:"dsg-user-id" json:"dsg-user-id"`
}

type ObjectParserConfig struct {
	Enable   bool   `toml:"enable" json:"enable"`
	Protocol string `toml:"protocol" json:"protocol"` // "http" or "grpc", defaults to "grpc"
	Host     string `toml:"host" json:"host"`
	Port     int    `toml:"port" json:"port"`
}
