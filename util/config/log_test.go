package config

import "testing"

func Test_getLogFileRoot(t *testing.T) {

	initTestConfig()
	tests := []struct {
		name string
		want string
	}{
		{
			name: "默认",
			want: "./data/logs/",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getLogFileRootPattern(); got != tt.want {
				t.<PERSON>("getLogFileRootPattern() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBuildSQLAnalyzerLogFilePath(t *testing.T) {
	initTestConfig()

	type args struct {
		channelId int
		taskId    int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "成功",
			args: args{
				channelId: 22,
				taskId:    33,
			},
			want: "./data/logs/sqlanalyzer-22-33.log",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := BuildSQLAnalyzerLogFilePath(tt.args.channelId, tt.args.taskId); got != tt.want {
				t.<PERSON>("BuildSQLAnalyzerLogFilePath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBuildMigrationCSVLogFilePath(t *testing.T) {
	initTestConfig()

	type args struct {
		channelId int
		taskId    int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "成功",
			args: args{
				channelId: 22,
				taskId:    33,
			},
			want: "./data/logs/migration_csv-22-33.log",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := BuildMigrationCSVLogFilePath(tt.args.channelId, tt.args.taskId); got != tt.want {
				t.Errorf("BuildMigrationCSVLogFilePath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBuildMigrationFullDataLogFilePath(t *testing.T) {
	initTestConfig()

	type args struct {
		channelId int
		taskId    int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "成功",
			args: args{
				channelId: 22,
				taskId:    33,
			},
			want: "./data/logs/migration_full-22-33.log",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := BuildMigrationFullDataLogFilePath(tt.args.channelId, tt.args.taskId); got != tt.want {
				t.Errorf("BuildMigrationFullDataLogFilePath() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBuildO2TSyncDiffLogFilePath(t *testing.T) {
	initTestConfig()

	type args struct {
		channelId int
		taskId    int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "成功",
			args: args{
				channelId: 22,
				taskId:    33,
			},
			want: "./data/logs/o2t_sync_diff-22-33.log",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := BuildO2TSyncDiffLogFilePath(tt.args.channelId, tt.args.taskId); got != tt.want {
				t.Errorf("BuildO2TSyncDiffLogFilePath() = %v, want %v", got, tt.want)
			}
		})
	}
}
