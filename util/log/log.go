package log

import (
	"fmt"
	"io"
	"os"
	"path"
	"runtime"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

type RootLogger struct {
	defaultLogEntry *logrus.Entry

	forkFileEntry map[string]*logrus.Entry

	LogLevel       string
	LogOutput      string
	LogFileRoot    string
	LogFileName    string
	LogMaxSize     int
	LogMaxAge      int
	LogMaxBackups  int
	LogLocalTime   bool
	LogCompress    bool
	defaultWriters []io.Writer
}

const (
	// LogDebug debug level
	LogDebug = "debug"
	// LogInfo info level
	LogInfo = "info"
	// LogWarn warn level
	LogWarn = "warn"
	// LogError error level
	LogError = "error"
	// LogFatal fatal level
	LogFatal = "fatal"
)

const (
	// RecordSysField record sys name
	RecordSysField = "source_sys"
	// RecordModField record mod name
	RecordModField = "source_mod"
	// RecordFileField record file name
	RecordFileField = "source_file"
	// RecordFunField record fun name
	RecordFunField = "source_fun"
	// RecordLineField record line number
	RecordLineField = "source_line"
)

type LogConfig struct {
	LogLevel      string `toml:"log-level" json:"log-level"`
	LogOutput     string `toml:"log-output" json:"log-output"`
	LogFileRoot   string `toml:"log-file-root" json:"log-file-root"`
	LogFileName   string `toml:"log-file-name" json:"log-file-name"`
	LogMaxSize    int    `toml:"log-max-size" json:"log-max-size"`
	LogMaxAge     int    `toml:"log-max-age" json:"log-max-age"`
	LogMaxBackups int    `toml:"log-max-backups" json:"log-max-backups"`
}

var DefaultLogConfig = &LogConfig{
	LogLevel:      "info",
	LogOutput:     constants.OutputFile,
	LogFileRoot:   constants.DataDir + constants.LogDirPrefix,
	LogFileName:   "tims",
	LogMaxSize:    512,
	LogMaxAge:     30,
	LogMaxBackups: 0,
}

var rootLogger *RootLogger
var assessmentLog *logrus.Entry
var tidbStatsLog *logrus.Entry

// CustomTextFormatter implements custom log format with specific field order
type CustomTextFormatter struct {
	TimestampFormat string
}

// InitTestLogger init a logger, for test only
func InitTestLogger() {
	conf := LogConfig{
		LogLevel: "info",
	}
	NewRootLoggerFromConfig(&conf)
}

func NewRootLoggerFromConfig(config *LogConfig) {
	if config == nil {
		panic("new root logger failed. log config is nil")
	}

	lr := &RootLogger{
		LogLevel:      config.LogLevel,
		LogOutput:     config.LogOutput,
		LogFileRoot:   config.LogFileRoot,
		LogFileName:   config.LogFileName,
		LogMaxSize:    config.LogMaxSize,
		LogMaxAge:     config.LogMaxAge,
		LogMaxBackups: config.LogMaxBackups,
		LogLocalTime:  true,
		LogCompress:   true,
	}

	lr.defaultLogEntry, lr.defaultWriters = lr.forkEntry(lr.LogFileName)
	lr.forkFileEntry = map[string]*logrus.Entry{lr.LogFileName: lr.defaultLogEntry}
	rootLogger = lr
}

func NewAssessmentLogger() {
	assessmentLog = rootLogger.ForkFile("assessment")
}
func NewTidbStatsLogLogger() {
	tidbStatsLog = rootLogger.ForkFile("tidbstats")
}

func (p *RootLogger) ForkFile(fileName string) *logrus.Entry {
	if entry, ok := p.forkFileEntry[fileName]; ok {
		return entry
	} else {

		p.forkFileEntry[fileName], _ = p.forkEntry(fileName)
		return p.forkFileEntry[fileName]
	}
}

func (p *RootLogger) Entry() *logrus.Entry {
	return p.defaultLogEntry
}

func (p *RootLogger) GetRootWriters() []io.Writer {
	return p.defaultWriters
}

func GetRootLogger() *RootLogger {
	return rootLogger
}

func GetAssessmentLoggerEntry() *logrus.Entry {
	return assessmentLog
}

func GetTidbStatsLoggerEntry() *logrus.Entry {
	return tidbStatsLog
}

func GetRootLoggerEntry() *logrus.Entry {
	return rootLogger.Entry()
}

func Debug(args ...interface{}) {
	GetRootLoggerEntry().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	GetRootLoggerEntry().Debugf(format, args...)
}

func Info(args ...interface{}) {
	GetRootLoggerEntry().Info(args...)
}

func Infof(format string, args ...interface{}) {
	GetRootLoggerEntry().Infof(format, args...)
}

func Warn(args ...interface{}) {
	GetRootLoggerEntry().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	GetRootLoggerEntry().Warnf(format, args...)
}

func Error(args ...interface{}) {
	GetRootLoggerEntry().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	GetRootLoggerEntry().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	GetRootLoggerEntry().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	GetRootLoggerEntry().Fatalf(format, args...)
}

// Format implements the custom log format: time level file line msg
func (f *CustomTextFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	var output strings.Builder

	// Set default timestamp format if not specified
	timestampFormat := f.TimestampFormat
	if timestampFormat == "" {
		timestampFormat = "2006-01-02 15:04:05"
	}

	// Get caller information
	var fileName string
	var lineNumber int

	// Try different skip levels to find the right caller
	for skip := 0; skip < 12; skip++ {
		if pc, file, line, ok := runtime.Caller(skip); ok {
			fn := runtime.FuncForPC(pc)
			if fn != nil && !strings.Contains(fn.Name(), "logrus") &&
				!strings.Contains(fn.Name(), "log.go") &&
				!strings.Contains(path.Base(file), "log.go") {
				fileName = path.Base(file)
				lineNumber = line
				break
			}
		}
	}

	// If we couldn't find caller info, use entry's caller if available
	if fileName == "" && entry.HasCaller() {
		fileName = path.Base(entry.Caller.File)
		lineNumber = entry.Caller.Line
	}

	// Format: time="..." level=info file=filename line=number msg="..."
	output.WriteString(`time="`)
	output.WriteString(entry.Time.Format(timestampFormat))
	output.WriteString(`" level=`)
	output.WriteString(entry.Level.String())

	if fileName != "" {
		output.WriteString(` file=`)
		output.WriteString(fileName)
		output.WriteString(`:`)
		output.WriteString(fmt.Sprintf("%d", lineNumber))
	}

	output.WriteString(` msg="`)
	output.WriteString(entry.Message)
	output.WriteString(`"`)

	// Add other fields if any
	for key, value := range entry.Data {
		output.WriteString(` `)
		output.WriteString(key)
		output.WriteString(`="`)
		output.WriteString(fmt.Sprintf("%v", value))
		output.WriteString(`"`)
	}

	output.WriteString("\n")
	return []byte(output.String()), nil
}

func Caller() logrus.Fields {
	if pc, file, line, ok := runtime.Caller(1); ok {
		ptr := runtime.FuncForPC(pc)
		return map[string]interface{}{
			RecordFunField:  ptr.Name(),
			RecordFileField: path.Base(file),
			RecordLineField: line,
		}
	}
	return map[string]interface{}{}
}

func (p *RootLogger) forkEntry(fileName string) (*logrus.Entry, []io.Writer) {
	logger := logrus.New()

	// Set log format
	//logger.SetFormatter(&logrus.JSONFormatter{DisableHTMLEscape: true})
	logger.SetFormatter(&CustomTextFormatter{
		TimestampFormat: "2006-01-02 15:04:05", // Custom time format without T and timezone
	})
	// Enable caller information to show file and line number
	logger.SetReportCaller(true)
	// Set log level
	logger.SetLevel(getLogLevel(p.LogLevel))

	// Define output type writer
	writers := []io.Writer{os.Stdout}

	// Determine whether the log output contains the file type
	if strings.Contains(strings.ToLower(p.LogOutput), constants.OutputFile) {
		writers = append(writers, p.CreateFileWriter(fileName))
		// remove the os.Stdout output
		writers = writers[1:]
	}

	// Set log output
	logger.SetOutput(io.MultiWriter(writers...))
	return logrus.NewEntry(logger), writers
}

func (p *RootLogger) CreateFileWriter(fileName string) io.Writer {
	return &lumberjack.Logger{
		Filename:   p.LogFileRoot + fileName + ".log",
		MaxSize:    p.LogMaxSize,
		MaxAge:     p.LogMaxAge,
		MaxBackups: p.LogMaxBackups,
		LocalTime:  p.LogLocalTime,
		Compress:   p.LogCompress,
	}
}

// Tool method to get log level
func getLogLevel(level string) logrus.Level {
	switch strings.ToLower(level) {
	case LogDebug:
		return logrus.DebugLevel
	case LogInfo:
		return logrus.InfoLevel
	case LogWarn:
		return logrus.WarnLevel
	case LogError:
		return logrus.ErrorLevel
	case LogFatal:
		return logrus.FatalLevel
	}
	return logrus.DebugLevel
}
