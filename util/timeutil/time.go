package timeutil

import (
	"time"
)

const timeFormat = "2006-01-02 15:04:05"

func FormatTime(t time.Time) string {
	return t.Format(timeFormat)
}

func GetTMSNullTime() time.Time {
	nullTime, _ := time.ParseInLocation(timeFormat, "1970-01-01 00:00:00", time.Local)
	return nullTime
}

func IsTMSNullTime(t time.Time) bool {
	if t.Year() == 1970 && t.Month() == 1 && t.Day() == 1 && t.Hour() == 0 && t.Minute() == 0 && t.Second() == 0 {
		return true
	}
	return false
}

func IsNotTMSNullTime(t time.Time) bool {
	return IsTMSNullTime(t)
}

func GetNowTime() time.Time {
	nowTime, _ := time.ParseInLocation(timeFormat, time.Now().Format(timeFormat), time.Local)
	return nowTime
}
