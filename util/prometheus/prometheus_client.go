package prometheus

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/go-resty/resty/v2"
)

const (
	PrometheusQueryRangeURL = "/api/v1/query_range"
	PROMQL_TIDB_CPU         = `irate(process_cpu_seconds_total{ job="tidb",@LABEL}[1m])`
	PROMQL_TIDB_MEM         = `process_resident_memory_bytes{job="tidb",@LABEL}`
	PROMQL_TIDB_DISK_IO     = `rate(node_disk_io_time_seconds_total{@LABEL}[30m]) or irate(node_disk_io_time_seconds_total{@LABEL}[1m])`
	PROMQL_TIDB_TPS         = `sum(rate(tidb_session_transaction_duration_seconds_count{@LABEL}[1m]))`
	PROMQL_TIDB_QPS         = `sum(rate(tidb_executor_statement_total{@LABEL}[1m]))`
	PROMQL_TIKV_CPU         = `sum(rate(process_cpu_seconds_total{ job=~".*tikv",@LABEL}[1m])) by (instance)`
	PROMQL_TIKV_MEM         = `avg(process_resident_memory_bytes{job=~".*tikv",@LABEL}) by (instance)`
	PROMQL_TIKV_DISK_IO     = `rate(node_disk_io_time_seconds_total{@LABEL}[30m]) or irate(node_disk_io_time_seconds_total{@LABEL}[1m])`
	PROMQL_TIKV_TPS         = `sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_count{type="commit",@LABEL}[1m]))`
	PROMQL_TIKV_QPS         = `sum(rate(tidb_tikvclient_request_seconds_count{@LABEL}[1m]))`

	PrometheusQueryInstantURL = "/api/v1/query"

	PROMQL_TIKV_USED_SPACE  = `sum(tikv_store_size_bytes{type="used",@LABEL}) by (instance)`
	PROMQL_TIKV_FREE_SPACE  = `sum(tikv_store_size_bytes{type="available",@LABEL}) by (instance)`
	PROMQL_TIKV_TOTAL_SPACE = `sum(tikv_store_size_bytes{type="used",@LABEL} + tikv_store_size_bytes{type="available",@LABEL}) by (instance)`

	PROMQL_TIFLASH_CPU                = `sum(tiflash_system_current_metric_LogicalCPUCores{@LABEL}) by (instance)`
	PROMQL_TIFLASH_MEM                = `sum(tiflash_system_current_metric_PhysicalMemoryBytes{@LABEL}) by (instance)`
	PROMQL_TIDB_MAX_PROCS             = `tidb_server_maxprocs{job="tidb",@LABEL}`
	PROMQL_TIDB_MAX_PROCS_BY_INSTANCE = `sum(tidb_server_maxprocs{job="tidb",@LABEL}) by (instance)`
	PROMQL_TIDB_MEM_BY_INSTANCE       = `node_memory_MemTotal_bytes{}`

	PROMQL_TIDB_NODE_COUNT = `count(up{job="tidb"})`
	PROMQL_TIKV_NODE_COUNT = `count(up{job="tikv"})`
)

func QueryRange(ctx context.Context, promURL, promQL, start, end string, step string) (string, error) {
	client := resty.New()
	resp, err := client.R().
		SetQueryParams(map[string]string{
			"query": promQL,
			"start": start,
			"end":   end,
			"step":  step,
		}).
		Get(promURL + PrometheusQueryRangeURL)
	if err != nil {
		return "", err
	}
	if resp.StatusCode() != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode(), resp.String())
	}
	return resp.String(), nil
}

func QueryInstant(ctx context.Context, promURL, promQL string) (string, error) {
	client := resty.New()
	resp, err := client.R().
		SetQueryParam("query", promQL).
		Get(promURL + PrometheusQueryInstantURL)
	if err != nil {
		return "", err
	}
	if resp.StatusCode() != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode(), resp.String())
	}
	return resp.String(), nil
}

// adjustPromQL Adjust PromQL expression, replace placeholders with labels and time range
func AdjustPromQL(promQL string, Label string, Range string) string {
	promQL = strings.ReplaceAll(promQL, "@LABEL", Label)
	if Range != "" {
		promQL = strings.ReplaceAll(promQL, "@RANGE", Range)
	} else {
		promQL = strings.ReplaceAll(promQL, "@RANGE", "1m")
	}
	return promQL
}
