package database

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/pingcap/tidb/pkg/errno"
	"github.com/stretchr/testify/assert"
)

// Test Oracle error codes
func TestIsRetryableError_OracleErrors(t *testing.T) {
	tests := []struct {
		name        string
		err         error
		shouldRetry bool
	}{
		// Connection and network errors
		{
			name:        "ORA-00028 - session killed",
			err:         errors.New("ORA-00028: your session has been killed"),
			shouldRetry: true,
		},
		{
			name:        "ORA-00600 - internal error",
			err:         errors.New("ORA-00600: internal error code"),
			shouldRetry: true,
		},
		{
			name:        "ORA-01012 - not logged on",
			err:         errors.New("ORA-01012: not logged on"),
			shouldRetry: true,
		},
		{
			name:        "ORA-01033 - initialization in progress",
			err:         errors.New("ORA-01033: ORACLE initialization or shutdown in progress"),
			shouldRetry: true,
		},
		{
			name:        "ORA-01034 - OR<PERSON>LE not available",
			err:         errors.New("ORA-01034: ORACLE not available"),
			shouldRetry: true,
		},
		{
			name:        "ORA-01041 - internal error",
			err:         errors.New("ORA-01041: internal error. hostdef extension doesn't exist"),
			shouldRetry: true,
		},
		{
			name:        "ORA-01092 - instance terminated",
			err:         errors.New("ORA-01092: ORACLE instance terminated. Disconnection forced"),
			shouldRetry: true,
		},
		{
			name:        "ORA-02396 - maximum idle time exceeded",
			err:         errors.New("ORA-02396: exceeded maximum idle time, please connect again"),
			shouldRetry: true,
		},
		{
			name:        "ORA-03113 - end-of-file on communication channel",
			err:         errors.New("ORA-03113: end-of-file on communication channel"),
			shouldRetry: true,
		},
		{
			name:        "ORA-03114 - not connected",
			err:         errors.New("ORA-03114: not connected to ORACLE"),
			shouldRetry: true,
		},
		{
			name:        "ORA-03135 - connection lost contact",
			err:         errors.New("ORA-03135: connection lost contact"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12500 - listener failed to start",
			err:         errors.New("ORA-12500: TNS:listener failed to start a dedicated server process"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12502 - no CONNECT_DATA",
			err:         errors.New("ORA-12502: TNS:listener received no CONNECT_DATA from client"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12504 - no SERVICE_NAME",
			err:         errors.New("ORA-12504: TNS:listener was not given the SERVICE_NAME in CONNECT_DATA"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12505 - unknown SID",
			err:         errors.New("ORA-12505: TNS:listener does not currently know of SID given in connect descriptor"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12514 - unknown service",
			err:         errors.New("ORA-12514: TNS:listener does not currently know of service requested in connect descriptor"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12516 - no available handler",
			err:         errors.New("ORA-12516: TNS:listener could not find available handler with matching protocol stack"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12518 - could not hand off connection",
			err:         errors.New("ORA-12518: TNS:listener could not hand off client connection"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12521 - unknown instance",
			err:         errors.New("ORA-12521: TNS:listener does not currently know of instance requested in connect descriptor"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12535 - operation timed out",
			err:         errors.New("ORA-12535: TNS:operation timed out"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12537 - connection closed",
			err:         errors.New("ORA-12537: TNS:connection closed"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12541 - no listener",
			err:         errors.New("ORA-12541: TNS:no listener"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12543 - destination unreachable",
			err:         errors.New("ORA-12543: TNS:destination host unreachable"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12545 - connect failed",
			err:         errors.New("ORA-12545: Connect failed because target host or object does not exist"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12547 - lost contact",
			err:         errors.New("ORA-12547: TNS:lost contact"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12549 - resource quota exceeded",
			err:         errors.New("ORA-12549: TNS:operating system resource quota exceeded"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12552 - operation interrupted",
			err:         errors.New("ORA-12552: TNS:operation was interrupted"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12571 - packet writer failure",
			err:         errors.New("ORA-12571: TNS:packet writer failure"),
			shouldRetry: true,
		},
		{
			name:        "ORA-12582 - invalid socket",
			err:         errors.New("ORA-12582: TNS:invalid socket"),
			shouldRetry: true,
		},
		{
			name:        "ORA-25408 - can not safely replay call",
			err:         errors.New("ORA-25408: can not safely replay call"),
			shouldRetry: true,
		},
		// Temporary resource issues
		{
			name:        "ORA-00018 - maximum sessions exceeded",
			err:         errors.New("ORA-00018: maximum number of sessions exceeded"),
			shouldRetry: true,
		},
		{
			name:        "ORA-00020 - maximum processes exceeded",
			err:         errors.New("ORA-00020: maximum number of processes exceeded"),
			shouldRetry: true,
		},
		{
			name:        "ORA-00051 - timeout waiting for resource",
			err:         errors.New("ORA-00051: timeout occurred while waiting for a resource"),
			shouldRetry: true,
		},
		{
			name:        "ORA-00060 - deadlock detected",
			err:         errors.New("ORA-00060: deadlock detected while waiting for resource"),
			shouldRetry: true,
		},
		{
			name:        "ORA-00061 - different DML_LOCKS setting",
			err:         errors.New("ORA-00061: another instance has a different DML_LOCKS setting"),
			shouldRetry: true,
		},
		{
			name:        "ORA-04020 - deadlock detected locking object",
			err:         errors.New("ORA-04020: deadlock detected while trying to lock object"),
			shouldRetry: true,
		},
		{
			name:        "ORA-04021 - timeout locking object",
			err:         errors.New("ORA-04021: timeout occurred while waiting to lock object"),
			shouldRetry: true,
		},
		{
			name:        "ORA-08176 - consistent read failure",
			err:         errors.New("ORA-08176: consistent read failure; rollback data not available"),
			shouldRetry: true,
		},
		// Non-retryable errors
		{
			name:        "ORA-00942 - table does not exist",
			err:         errors.New("ORA-00942: table or view does not exist"),
			shouldRetry: false,
		},
		{
			name:        "ORA-00001 - unique constraint violated",
			err:         errors.New("ORA-00001: unique constraint violated"),
			shouldRetry: false,
		},
		{
			name:        "ORA-01400 - cannot insert NULL",
			err:         errors.New("ORA-01400: cannot insert NULL into column"),
			shouldRetry: false,
		},
		{
			name:        "ORA-01722 - invalid number",
			err:         errors.New("ORA-01722: invalid number"),
			shouldRetry: false,
		},
		{
			name:        "Invalid Oracle error code",
			err:         errors.New("ORA-ABC: invalid error code"),
			shouldRetry: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetryableError(tt.err)
			assert.Equal(t, tt.shouldRetry, result, "Expected %v for error: %v", tt.shouldRetry, tt.err)
		})
	}
}

// Test MySQL error codes
func TestIsRetryableError_MySQLErrors(t *testing.T) {
	tests := []struct {
		name        string
		err         error
		shouldRetry bool
	}{
		// Connection errors
		{
			name:        "CR_CONNECTION_ERROR",
			err:         &mysql.MySQLError{Number: 2002, Message: "Connection error"},
			shouldRetry: true,
		},
		{
			name:        "CR_CONN_HOST_ERROR",
			err:         &mysql.MySQLError{Number: 2003, Message: "Connection host error"},
			shouldRetry: true,
		},
		{
			name:        "CR_IPSOCK_ERROR",
			err:         &mysql.MySQLError{Number: 2004, Message: "IP socket error"},
			shouldRetry: true,
		},
		{
			name:        "CR_UNKNOWN_HOST",
			err:         &mysql.MySQLError{Number: 2005, Message: "Unknown host"},
			shouldRetry: true,
		},
		{
			name:        "CR_SERVER_GONE_ERROR",
			err:         &mysql.MySQLError{Number: 2006, Message: "Server gone error"},
			shouldRetry: true,
		},
		{
			name:        "CR_VERSION_ERROR",
			err:         &mysql.MySQLError{Number: 2007, Message: "Version error"},
			shouldRetry: true,
		},
		{
			name:        "CR_WRONG_HOST_INFO",
			err:         &mysql.MySQLError{Number: 2009, Message: "Wrong host info"},
			shouldRetry: true,
		},
		{
			name:        "CR_SERVER_HANDSHAKE_ERR",
			err:         &mysql.MySQLError{Number: 2012, Message: "Server handshake error"},
			shouldRetry: true,
		},
		{
			name:        "CR_SERVER_LOST",
			err:         &mysql.MySQLError{Number: 2013, Message: "Server lost"},
			shouldRetry: true,
		},
		{
			name:        "CR_SSL_CONNECTION_ERROR",
			err:         &mysql.MySQLError{Number: 2026, Message: "SSL connection error"},
			shouldRetry: true,
		},
		{
			name:        "CR_LOST_CONNECTION",
			err:         &mysql.MySQLError{Number: 2055, Message: "Lost connection"},
			shouldRetry: true,
		},
		// Temporary errors
		{
			name:        "ER_CON_COUNT_ERROR",
			err:         &mysql.MySQLError{Number: 1040, Message: "Too many connections"},
			shouldRetry: true,
		},
		{
			name:        "ER_OUT_OF_RESOURCES",
			err:         &mysql.MySQLError{Number: 1041, Message: "Out of resources"},
			shouldRetry: true,
		},
		{
			name:        "ER_BAD_HOST_ERROR",
			err:         &mysql.MySQLError{Number: 1042, Message: "Bad host error"},
			shouldRetry: true,
		},
		{
			name:        "ER_HANDSHAKE_ERROR",
			err:         &mysql.MySQLError{Number: 1043, Message: "Handshake error"},
			shouldRetry: true,
		},
		{
			name:        "ER_UNKNOWN_COM_ERROR",
			err:         &mysql.MySQLError{Number: 1047, Message: "Unknown command error"},
			shouldRetry: true,
		},
		{
			name:        "ER_SERVER_SHUTDOWN",
			err:         &mysql.MySQLError{Number: 1053, Message: "Server shutdown"},
			shouldRetry: true,
		},
		{
			name:        "ER_HOST_IS_BLOCKED",
			err:         &mysql.MySQLError{Number: 1129, Message: "Host is blocked"},
			shouldRetry: true,
		},
		{
			name:        "ER_HOST_NOT_PRIVILEGED",
			err:         &mysql.MySQLError{Number: 1130, Message: "Host not privileged"},
			shouldRetry: true,
		},
		{
			name:        "ER_ABORTING_CONNECTION",
			err:         &mysql.MySQLError{Number: 1152, Message: "Aborting connection"},
			shouldRetry: true,
		},
		{
			name:        "ER_NET_PACKET_TOO_LARGE",
			err:         &mysql.MySQLError{Number: 1153, Message: "Net packet too large"},
			shouldRetry: true,
		},
		{
			name:        "ER_NET_READ_ERROR",
			err:         &mysql.MySQLError{Number: 1158, Message: "Net read error"},
			shouldRetry: true,
		},
		{
			name:        "ER_NET_READ_INTERRUPTED",
			err:         &mysql.MySQLError{Number: 1159, Message: "Net read interrupted"},
			shouldRetry: true,
		},
		{
			name:        "ER_NET_ERROR_ON_WRITE",
			err:         &mysql.MySQLError{Number: 1160, Message: "Net error on write"},
			shouldRetry: true,
		},
		{
			name:        "ER_NET_WRITE_INTERRUPTED",
			err:         &mysql.MySQLError{Number: 1161, Message: "Net write interrupted"},
			shouldRetry: true,
		},
		{
			name:        "ER_NEW_ABORTING_CONNECTION",
			err:         &mysql.MySQLError{Number: 1184, Message: "New aborting connection"},
			shouldRetry: true,
		},
		{
			name:        "ER_TOO_MANY_USER_CONNECTIONS",
			err:         &mysql.MySQLError{Number: 1203, Message: "Too many user connections"},
			shouldRetry: true,
		},
		{
			name:        "ER_LOCK_WAIT_TIMEOUT",
			err:         &mysql.MySQLError{Number: 1205, Message: "Lock wait timeout"},
			shouldRetry: true,
		},
		{
			name:        "ER_LOCK_DEADLOCK",
			err:         &mysql.MySQLError{Number: 1213, Message: "Lock deadlock"},
			shouldRetry: true,
		},
		// TiDB specific errors
		{
			name:        "ErrPDServerTimeout",
			err:         &mysql.MySQLError{Number: errno.ErrPDServerTimeout, Message: "PD server timeout"},
			shouldRetry: true,
		},
		{
			name:        "ErrTiKVServerBusy",
			err:         &mysql.MySQLError{Number: errno.ErrTiKVServerBusy, Message: "TiKV server busy"},
			shouldRetry: true,
		},
		{
			name:        "ErrResolveLockTimeout",
			err:         &mysql.MySQLError{Number: errno.ErrResolveLockTimeout, Message: "Resolve lock timeout"},
			shouldRetry: true,
		},
		{
			name:        "ErrInfoSchemaExpired",
			err:         &mysql.MySQLError{Number: errno.ErrInfoSchemaExpired, Message: "Info schema expired"},
			shouldRetry: true,
		},
		{
			name:        "ErrInfoSchemaChanged",
			err:         &mysql.MySQLError{Number: errno.ErrInfoSchemaChanged, Message: "Info schema changed"},
			shouldRetry: true,
		},
		{
			name:        "ErrWriteConflictInTiDB",
			err:         &mysql.MySQLError{Number: errno.ErrWriteConflictInTiDB, Message: "Write conflict in TiDB"},
			shouldRetry: true,
		},
		{
			name:        "ErrWriteConflict",
			err:         &mysql.MySQLError{Number: errno.ErrWriteConflict, Message: "Write conflict"},
			shouldRetry: true,
		},
		// Special case for ErrUnknown with retryable messages
		{
			name:        "ErrUnknown with retryable message - info schema out of date",
			err:         &mysql.MySQLError{Number: errno.ErrUnknown, Message: "Information schema is out of date"},
			shouldRetry: true,
		},
		{
			name:        "ErrUnknown with retryable message - info schema changed",
			err:         &mysql.MySQLError{Number: errno.ErrUnknown, Message: "Information schema is changed"},
			shouldRetry: true,
		},
		{
			name:        "ErrUnknown with non-retryable message",
			err:         &mysql.MySQLError{Number: errno.ErrUnknown, Message: "Some other error"},
			shouldRetry: false,
		},
		// Non-retryable errors
		{
			name:        "ER_NO_SUCH_TABLE",
			err:         &mysql.MySQLError{Number: 1146, Message: "Table doesn't exist"},
			shouldRetry: false,
		},
		{
			name:        "ER_DUP_ENTRY",
			err:         &mysql.MySQLError{Number: 1062, Message: "Duplicate entry"},
			shouldRetry: false,
		},
		{
			name:        "ER_ACCESS_DENIED_ERROR",
			err:         &mysql.MySQLError{Number: 1045, Message: "Access denied"},
			shouldRetry: false,
		},
		{
			name:        "ER_BAD_DB_ERROR",
			err:         &mysql.MySQLError{Number: 1049, Message: "Unknown database"},
			shouldRetry: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetryableError(tt.err)
			assert.Equal(t, tt.shouldRetry, result, "Expected %v for error: %v", tt.shouldRetry, tt.err)
		})
	}
}

// Test network error patterns
func TestIsRetryableError_NetworkErrors(t *testing.T) {
	tests := []struct {
		name        string
		err         error
		shouldRetry bool
	}{
		{
			name:        "connection refused",
			err:         errors.New("connection refused"),
			shouldRetry: true,
		},
		{
			name:        "connection reset",
			err:         errors.New("connection reset by peer"),
			shouldRetry: true,
		},
		{
			name:        "connection timeout",
			err:         errors.New("connection timeout"),
			shouldRetry: true,
		},
		{
			name:        "connection lost",
			err:         errors.New("connection lost"),
			shouldRetry: true,
		},
		{
			name:        "network is unreachable",
			err:         errors.New("network is unreachable"),
			shouldRetry: true,
		},
		{
			name:        "no route to host",
			err:         errors.New("no route to host"),
			shouldRetry: true,
		},
		{
			name:        "broken pipe",
			err:         errors.New("broken pipe"),
			shouldRetry: true,
		},
		{
			name:        "i/o timeout",
			err:         errors.New("i/o timeout"),
			shouldRetry: true,
		},
		{
			name:        "use of closed network connection",
			err:         errors.New("use of closed network connection"),
			shouldRetry: true,
		},
		{
			name:        "connection closed by foreign host",
			err:         errors.New("connection closed by foreign host"),
			shouldRetry: true,
		},
		{
			name:        "remote host closed connection",
			err:         errors.New("remote host closed connection"),
			shouldRetry: true,
		},
		{
			name:        "temporarily unavailable",
			err:         errors.New("temporarily unavailable"),
			shouldRetry: true,
		},
		{
			name:        "Case insensitive - CONNECTION REFUSED",
			err:         errors.New("CONNECTION REFUSED"),
			shouldRetry: true,
		},
		{
			name:        "Non-retryable network error",
			err:         errors.New("invalid network address"),
			shouldRetry: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetryableError(tt.err)
			assert.Equal(t, tt.shouldRetry, result, "Expected %v for error: %v", tt.shouldRetry, tt.err)
		})
	}
}

// Test nil error handling
func TestIsRetryableError_NilError(t *testing.T) {
	result := IsRetryableError(nil)
	assert.False(t, result, "Nil error should not be retryable")
}

// Test Oracle error code extraction
func TestExtractOracleErrorCode(t *testing.T) {
	tests := []struct {
		name     string
		errMsg   string
		expected string
	}{
		{
			name:     "Valid Oracle error code",
			errMsg:   "ORA-00001: unique constraint violated",
			expected: "ORA-00001",
		},
		{
			name:     "Valid Oracle error code with more text",
			errMsg:   "Error occurred: ORA-12345: some error message here",
			expected: "ORA-12345",
		},
		{
			name:     "Multiple Oracle error codes - should get first",
			errMsg:   "ORA-00001: error1 ORA-00002: error2",
			expected: "ORA-00001",
		},
		{
			name:     "Invalid Oracle error code - too short",
			errMsg:   "ORA-123: invalid",
			expected: "",
		},
		{
			name:     "Invalid Oracle error code - non-numeric",
			errMsg:   "ORA-ABCDE: invalid",
			expected: "",
		},
		{
			name:     "No Oracle error code",
			errMsg:   "Some other error message",
			expected: "",
		},
		{
			name:     "Oracle error code with prefix",
			errMsg:   "prefix ORA-54321: error message",
			expected: "ORA-54321",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractOracleErrorCode(tt.errMsg)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test Oracle error code validation
func TestIsValidOracleCode(t *testing.T) {
	tests := []struct {
		name     string
		code     string
		expected bool
	}{
		{
			name:     "Valid Oracle error code",
			code:     "ORA-00001",
			expected: true,
		},
		{
			name:     "Valid Oracle error code with 5 digits",
			code:     "ORA-12345",
			expected: true,
		},
		{
			name:     "Invalid - too short",
			code:     "ORA-123",
			expected: false,
		},
		{
			name:     "Invalid - too long",
			code:     "ORA-123456",
			expected: false,
		},
		{
			name:     "Invalid - non-numeric",
			code:     "ORA-ABCDE",
			expected: false,
		},
		{
			name:     "Invalid - wrong prefix",
			code:     "ERR-12345",
			expected: false,
		},
		{
			name:     "Invalid - no prefix",
			code:     "12345",
			expected: false,
		},
		{
			name:     "Invalid - empty",
			code:     "",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidOracleCode(tt.code)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Test default retry configuration
func TestDefaultRetryConfig(t *testing.T) {
	config := DefaultRetryConfig()

	assert.Equal(t, 3, config.MaxRetries)
	assert.Equal(t, 100*time.Millisecond, config.InitialDelay)
	assert.Equal(t, 5*time.Second, config.MaxDelay)
	assert.Equal(t, 2.0, config.BackoffFactor)
}

// Test retry execution with successful function
func TestExecuteWithRetry_Success(t *testing.T) {
	ctx := context.Background()
	config := DefaultRetryConfig()

	callCount := 0
	fn := func() error {
		callCount++
		return nil
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.NoError(t, err)
	assert.Equal(t, 1, callCount, "Function should be called exactly once on success")
}

// Test retry execution with non-retryable error
func TestExecuteWithRetry_NonRetryableError(t *testing.T) {
	ctx := context.Background()
	config := DefaultRetryConfig()

	callCount := 0
	expectedErr := errors.New("syntax error")
	fn := func() error {
		callCount++
		return expectedErr
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.Equal(t, expectedErr, err)
	assert.Equal(t, 1, callCount, "Function should be called exactly once for non-retryable error")
}

// Test retry execution with retryable error that eventually succeeds
func TestExecuteWithRetry_RetryableErrorThenSuccess(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	callCount := 0
	fn := func() error {
		callCount++
		if callCount < 3 {
			return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
		}
		return nil
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.NoError(t, err)
	assert.Equal(t, 3, callCount, "Function should be called 3 times before success")
}

// Test retry execution with retryable error that exhausts retries
func TestExecuteWithRetry_RetryableErrorExhausted(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    2,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	callCount := 0
	expectedErr := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	fn := func() error {
		callCount++
		return expectedErr
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "operation failed after 3 retries")
	assert.Equal(t, 3, callCount, "Function should be called MaxRetries+1 times")
}

// Test retry execution with context cancellation
func TestExecuteWithRetry_ContextCancellation(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	config := &RetryConfig{
		MaxRetries:    10,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
	}

	callCount := 0
	fn := func() error {
		callCount++
		if callCount == 2 {
			cancel() // Cancel context on second call
		}
		return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.Equal(t, context.Canceled, err)
	assert.Equal(t, 2, callCount, "Function should be called twice before context cancellation")
}

// Test retry execution with timeout
func TestExecuteWithRetry_ContextTimeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	config := &RetryConfig{
		MaxRetries:    10,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
	}

	callCount := 0
	fn := func() error {
		callCount++
		return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.Equal(t, context.DeadlineExceeded, err)
	assert.LessOrEqual(t, callCount, 3, "Function should be called limited times due to timeout")
}

// Test retry execution with nil config (should use default)
func TestExecuteWithRetry_NilConfig(t *testing.T) {
	ctx := context.Background()

	callCount := 0
	fn := func() error {
		callCount++
		return nil
	}

	err := ExecuteWithRetry(ctx, nil, fn)

	assert.NoError(t, err)
	assert.Equal(t, 1, callCount, "Function should be called exactly once")
}

// Test retry execution with result - success
func TestExecuteWithRetryAndResult_Success(t *testing.T) {
	ctx := context.Background()
	config := DefaultRetryConfig()

	callCount := 0
	expectedResult := "success"
	fn := func() (string, error) {
		callCount++
		return expectedResult, nil
	}

	result, err := ExecuteWithRetryAndResult(ctx, config, fn)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, 1, callCount, "Function should be called exactly once on success")
}

// Test retry execution with result - retryable error then success
func TestExecuteWithRetryAndResult_RetryableErrorThenSuccess(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	callCount := 0
	expectedResult := "success"
	fn := func() (string, error) {
		callCount++
		if callCount < 3 {
			return "", &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
		}
		return expectedResult, nil
	}

	result, err := ExecuteWithRetryAndResult(ctx, config, fn)

	assert.NoError(t, err)
	assert.Equal(t, expectedResult, result)
	assert.Equal(t, 3, callCount, "Function should be called 3 times before success")
}

// Test retry execution with result - retryable error exhausted
func TestExecuteWithRetryAndResult_RetryableErrorExhausted(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    2,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	callCount := 0
	expectedErr := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	fn := func() (string, error) {
		callCount++
		return "", expectedErr
	}

	result, err := ExecuteWithRetryAndResult(ctx, config, fn)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "operation failed after 3 retries")
	assert.Equal(t, "", result)
	assert.Equal(t, 3, callCount, "Function should be called MaxRetries+1 times")
}

// Test backoff calculation
func TestExecuteWithRetry_BackoffCalculation(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  10 * time.Millisecond,
		MaxDelay:      50 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	callCount := 0
	callTimes := make([]time.Time, 0)

	fn := func() error {
		callCount++
		callTimes = append(callTimes, time.Now())
		return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	}

	start := time.Now()
	err := ExecuteWithRetry(ctx, config, fn)

	assert.Error(t, err)
	assert.Equal(t, 4, callCount, "Function should be called 4 times")
	assert.Len(t, callTimes, 4, "Should have 4 call times")

	// Check that delays are approximately correct
	// First call should be immediate
	assert.Less(t, callTimes[0].Sub(start), 5*time.Millisecond)

	// Second call should be after ~10ms
	if len(callTimes) > 1 {
		delay1 := callTimes[1].Sub(callTimes[0])
		assert.GreaterOrEqual(t, delay1, 10*time.Millisecond)
		assert.Less(t, delay1, 20*time.Millisecond)
	}

	// Third call should be after ~20ms
	if len(callTimes) > 2 {
		delay2 := callTimes[2].Sub(callTimes[1])
		assert.GreaterOrEqual(t, delay2, 20*time.Millisecond)
		assert.Less(t, delay2, 30*time.Millisecond)
	}

	// Fourth call should be after ~40ms, but capped at MaxDelay (50ms)
	if len(callTimes) > 3 {
		delay3 := callTimes[3].Sub(callTimes[2])
		assert.GreaterOrEqual(t, delay3, 40*time.Millisecond)
		assert.Less(t, delay3, 60*time.Millisecond)
	}
}

// Test mixed error types in retry logic
func TestExecuteWithRetry_MixedErrorTypes(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    5,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	callCount := 0
	fn := func() error {
		callCount++
		switch callCount {
		case 1:
			return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
		case 2:
			return errors.New("ORA-03113: end-of-file on communication channel")
		case 3:
			return errors.New("connection refused")
		case 4:
			return errors.New("syntax error") // Non-retryable
		default:
			return nil
		}
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "syntax error")
	assert.Equal(t, 4, callCount, "Function should be called 4 times before non-retryable error")
}

// Benchmark retry logic performance
func BenchmarkIsRetryableError_MySQLError(b *testing.B) {
	err := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		IsRetryableError(err)
	}
}

func BenchmarkIsRetryableError_OracleError(b *testing.B) {
	err := errors.New("ORA-03113: end-of-file on communication channel")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		IsRetryableError(err)
	}
}

func BenchmarkIsRetryableError_NetworkError(b *testing.B) {
	err := errors.New("connection refused")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		IsRetryableError(err)
	}
}

func BenchmarkExecuteWithRetry_Success(b *testing.B) {
	ctx := context.Background()
	config := DefaultRetryConfig()

	fn := func() error {
		return nil
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ExecuteWithRetry(ctx, config, fn)
	}
}

func BenchmarkExecuteWithRetry_NonRetryableError(b *testing.B) {
	ctx := context.Background()
	config := DefaultRetryConfig()

	fn := func() error {
		return errors.New("syntax error")
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ExecuteWithRetry(ctx, config, fn)
	}
}

// Test edge cases and error conditions
func TestExecuteWithRetry_EdgeCases(t *testing.T) {
	t.Run("Zero delay", func(t *testing.T) {
		ctx := context.Background()
		config := &RetryConfig{
			MaxRetries:    2,
			InitialDelay:  0,
			MaxDelay:      0,
			BackoffFactor: 2.0,
		}

		callCount := 0
		fn := func() error {
			callCount++
			if callCount < 3 {
				return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
			}
			return nil
		}

		start := time.Now()
		err := ExecuteWithRetry(ctx, config, fn)
		duration := time.Since(start)

		assert.NoError(t, err)
		assert.Equal(t, 3, callCount)
		assert.Less(t, duration, 50*time.Millisecond, "Should complete quickly with zero delay")
	})

	t.Run("Large backoff factor", func(t *testing.T) {
		ctx := context.Background()
		config := &RetryConfig{
			MaxRetries:    2,
			InitialDelay:  1 * time.Millisecond,
			MaxDelay:      10 * time.Millisecond,
			BackoffFactor: 100.0,
		}

		callCount := 0
		fn := func() error {
			callCount++
			return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
		}

		start := time.Now()
		err := ExecuteWithRetry(ctx, config, fn)
		duration := time.Since(start)

		assert.Error(t, err)
		assert.Equal(t, 3, callCount)
		// Should be limited by MaxDelay
		assert.Less(t, duration, 50*time.Millisecond)
	})

	t.Run("Negative backoff factor", func(t *testing.T) {
		ctx := context.Background()
		config := &RetryConfig{
			MaxRetries:    2,
			InitialDelay:  10 * time.Millisecond,
			MaxDelay:      100 * time.Millisecond,
			BackoffFactor: -1.0,
		}

		callCount := 0
		fn := func() error {
			callCount++
			return &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
		}

		err := ExecuteWithRetry(ctx, config, fn)

		assert.Error(t, err)
		assert.Equal(t, 3, callCount)
	})
}

// Test error message formatting
func TestExecuteWithRetry_ErrorMessageFormatting(t *testing.T) {
	ctx := context.Background()
	config := &RetryConfig{
		MaxRetries:    2,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	originalErr := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	fn := func() error {
		return originalErr
	}

	err := ExecuteWithRetry(ctx, config, fn)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "operation failed after 3 retries")
	assert.Contains(t, err.Error(), "Server gone error")
}
