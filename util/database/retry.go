package database

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/pingcap/errors"
	"github.com/pingcap/tidb/pkg/errno"
)

// RetryConfig defines the configuration for database operation retries
type RetryConfig struct {
	MaxRetries    int           `toml:"max_retries" json:"max_retries"`       // Maximum number of retry attempts
	InitialDelay  time.Duration `toml:"initial_delay" json:"initial_delay"`   // Initial delay between retries
	MaxDelay      time.Duration `toml:"max_delay" json:"max_delay"`           // Maximum delay between retries
	BackoffFactor float64       `toml:"backoff_factor" json:"backoff_factor"` // Exponential backoff multiplier
}

// DefaultRetryConfig returns a sensible default retry configuration
func DefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
	}
}

// RetryableOracleErrors defines Oracle error codes that are safe to retry
var RetryableOracleErrors = map[string]bool{
	// Connection and network errors
	"ORA-00028": true, // your session has been killed
	"ORA-00600": true, // internal error code
	"ORA-00603": true, // ORACLE server session terminated by fatal error
	"ORA-01012": true, // not logged on
	"ORA-01033": true, // ORACLE initialization or shutdown in progress
	"ORA-01034": true, // ORACLE not available
	"ORA-01041": true, // internal error. hostdef extension doesn't exist
	"ORA-01092": true, // ORACLE instance terminated. Disconnection forced
	"ORA-02396": true, // exceeded maximum idle time, please connect again
	"ORA-03113": true, // end-of-file on communication channel
	"ORA-03114": true, // not connected to ORACLE
	"ORA-03135": true, // connection lost contact
	"ORA-12500": true, // TNS:listener failed to start a dedicated server process
	"ORA-12502": true, // TNS:listener received no CONNECT_DATA from client
	"ORA-12504": true, // TNS:listener was not given the SERVICE_NAME in CONNECT_DATA
	"ORA-12505": true, // TNS:listener does not currently know of SID given in connect descriptor
	"ORA-12514": true, // TNS:listener does not currently know of service requested in connect descriptor
	"ORA-12516": true, // TNS:listener could not find available handler with matching protocol stack
	"ORA-12518": true, // TNS:listener could not hand off client connection
	"ORA-12521": true, // TNS:listener does not currently know of instance requested in connect descriptor
	"ORA-12535": true, // TNS:operation timed out
	"ORA-12537": true, // TNS:connection closed
	"ORA-12541": true, // TNS:no listener
	"ORA-12543": true, // TNS:destination host unreachable
	"ORA-12545": true, // Connect failed because target host or object does not exist
	"ORA-12547": true, // TNS:lost contact
	"ORA-12549": true, // TNS:operating system resource quota exceeded
	"ORA-12552": true, // TNS:operation was interrupted
	"ORA-12571": true, // TNS:packet writer failure
	"ORA-12582": true, // TNS:invalid socket
	"ORA-25408": true, // can not safely replay call
	// Temporary resource issues
	"ORA-00018": true, // maximum number of sessions exceeded
	"ORA-00020": true, // maximum number of processes exceeded
	"ORA-00051": true, // timeout occurred while waiting for a resource
	"ORA-00060": true, // deadlock detected while waiting for resource
	"ORA-00061": true, // another instance has a different DML_LOCKS setting
	"ORA-04020": true, // deadlock detected while trying to lock object
	"ORA-04021": true, // timeout occurred while waiting to lock object
	"ORA-08176": true, // consistent read failure; rollback data not available
}

// RetryableMySQLErrors defines MySQL/TiDB error codes that are safe to retry
var RetryableMySQLErrors = map[uint16]bool{
	// Connection errors
	2002: true, // CR_CONNECTION_ERROR
	2003: true, // CR_CONN_HOST_ERROR
	2004: true, // CR_IPSOCK_ERROR
	2005: true, // CR_UNKNOWN_HOST
	2006: true, // CR_SERVER_GONE_ERROR
	2007: true, // CR_VERSION_ERROR
	2009: true, // CR_WRONG_HOST_INFO
	2012: true, // CR_SERVER_HANDSHAKE_ERR
	2013: true, // CR_SERVER_LOST
	2026: true, // CR_SSL_CONNECTION_ERROR
	2055: true, // CR_LOST_CONNECTION

	// Temporary errors
	1040: true, // ER_CON_COUNT_ERROR (Too many connections)
	1041: true, // ER_OUT_OF_RESOURCES
	1042: true, // ER_BAD_HOST_ERROR
	1043: true, // ER_HANDSHAKE_ERROR
	1047: true, // ER_UNKNOWN_COM_ERROR
	1053: true, // ER_SERVER_SHUTDOWN
	1129: true, // ER_HOST_IS_BLOCKED
	1130: true, // ER_HOST_NOT_PRIVILEGED
	1152: true, // ER_ABORTING_CONNECTION
	1153: true, // ER_NET_PACKET_TOO_LARGE
	1158: true, // ER_NET_READ_ERROR
	1159: true, // ER_NET_READ_INTERRUPTED
	1160: true, // ER_NET_ERROR_ON_WRITE
	1161: true, // ER_NET_WRITE_INTERRUPTED
	1184: true, // ER_NEW_ABORTING_CONNECTION
	1203: true, // ER_TOO_MANY_USER_CONNECTIONS
	1205: true, // ER_LOCK_WAIT_TIMEOUT

	// TiDB specific errors (from errno package)
	errno.ErrPDServerTimeout:     true,
	errno.ErrTiKVServerBusy:      true,
	errno.ErrResolveLockTimeout:  true,
	errno.ErrInfoSchemaExpired:   true,
	errno.ErrInfoSchemaChanged:   true,
	errno.ErrWriteConflictInTiDB: true,
	errno.ErrWriteConflict:       true,
	errno.ErrLockDeadlock:        true, // This is 1213
}

// IsRetryableError checks whether the error is retryable for Oracle, MySQL, or TiDB
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	err = errors.Cause(err) // check the original error

	// Check for MySQL/TiDB errors
	if mysqlErr, ok := err.(*mysql.MySQLError); ok {
		// Check specific MySQL/TiDB error codes
		if RetryableMySQLErrors[mysqlErr.Number] {
			return true
		}

		// Handle special case for ErrUnknown (TiDB compatibility)
		if mysqlErr.Number == errno.ErrUnknown {
			retryable1105Msgs := []string{
				"Information schema is out of date",
				"Information schema is changed",
			}
			for _, msg := range retryable1105Msgs {
				if strings.Contains(mysqlErr.Message, msg) {
					return true
				}
			}
		}
		return false
	}

	// Check for Oracle errors
	errStr := err.Error()
	if strings.Contains(errStr, "ORA-") {
		// Extract Oracle error code
		if oraCode := extractOracleErrorCode(errStr); oraCode != "" {
			return RetryableOracleErrors[oraCode]
		}
	}

	// Check for common network/connection errors in error message
	errMsg := strings.ToLower(errStr)
	networkErrors := []string{
		"connection refused",
		"connection reset",
		"connection timeout",
		"connection lost",
		"network is unreachable",
		"no route to host",
		"broken pipe",
		"i/o timeout",
		"use of closed network connection",
		"connection closed by foreign host",
		"remote host closed connection",
		"temporarily unavailable",
	}

	for _, netErr := range networkErrors {
		if strings.Contains(errMsg, netErr) {
			return true
		}
	}

	return false
}

// extractOracleErrorCode extracts Oracle error code from error message
func extractOracleErrorCode(errMsg string) string {
	// Look for ORA-XXXXX pattern
	parts := strings.Fields(errMsg)
	for _, part := range parts {
		if strings.HasPrefix(part, "ORA-") && len(part) >= 9 {
			// Extract the ORA-XXXXX part (first 9 characters)
			code := part[:9]
			if isValidOracleCode(code) {
				return code
			}
		}
	}
	return ""
}

// isValidOracleCode validates if the Oracle error code format is correct
func isValidOracleCode(code string) bool {
	if len(code) != 9 || !strings.HasPrefix(code, "ORA-") {
		return false
	}

	// Check if the last 5 characters are digits
	numPart := code[4:]
	if _, err := strconv.Atoi(numPart); err != nil {
		return false
	}

	return true
}

// RetryFunc is a function type that can be retried
type RetryFunc func() error

// ExecuteWithRetry executes a function with retry logic based on the provided configuration
func ExecuteWithRetry(ctx context.Context, config *RetryConfig, fn RetryFunc) error {
	if config == nil {
		config = DefaultRetryConfig()
	}

	var lastErr error
	delay := config.InitialDelay

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		// Check if context is cancelled
		if ctx != nil {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
			}
		}

		// Execute the function
		err := fn()
		if err == nil {
			return nil // Success
		}

		lastErr = err

		// If this is the last attempt, don't retry
		if attempt == config.MaxRetries {
			break
		}

		// Check if the error is retryable
		if !IsRetryableError(err) {
			return err // Non-retryable error, fail immediately
		}

		// Wait before retrying
		if delay > 0 {
			timer := time.NewTimer(delay)
			select {
			case <-timer.C:
				// Continue with retry
			case <-ctx.Done():
				timer.Stop()
				return ctx.Err()
			}
		}

		// Calculate next delay with exponential backoff
		delay = time.Duration(float64(delay) * config.BackoffFactor)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}
	}

	// All retries exhausted
	return fmt.Errorf("operation failed after %d retries, last error: %w", config.MaxRetries+1, lastErr)
}

// RetryableFunc is a function type that can be retried and returns a result
type RetryableFunc[T any] func() (T, error)

// ExecuteWithRetryAndResult executes a function with retry logic and returns the result
func ExecuteWithRetryAndResult[T any](ctx context.Context, config *RetryConfig, fn RetryableFunc[T]) (T, error) {
	var result T
	var lastErr error

	if config == nil {
		config = DefaultRetryConfig()
	}

	delay := config.InitialDelay

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		// Check if context is cancelled
		if ctx != nil {
			select {
			case <-ctx.Done():
				return result, ctx.Err()
			default:
			}
		}

		// Execute the function
		res, err := fn()
		if err == nil {
			return res, nil // Success
		}

		lastErr = err

		// If this is the last attempt, don't retry
		if attempt == config.MaxRetries {
			break
		}

		// Check if the error is retryable
		if !IsRetryableError(err) {
			return result, err // Non-retryable error, fail immediately
		}

		// Wait before retrying
		if delay > 0 {
			timer := time.NewTimer(delay)
			select {
			case <-timer.C:
				// Continue with retry
			case <-ctx.Done():
				timer.Stop()
				return result, ctx.Err()
			}
		}

		// Calculate next delay with exponential backoff
		delay = time.Duration(float64(delay) * config.BackoffFactor)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}
	}

	// All retries exhausted
	return result, fmt.Errorf("operation failed after %d retries, last error: %w", config.MaxRetries+1, lastErr)
}
