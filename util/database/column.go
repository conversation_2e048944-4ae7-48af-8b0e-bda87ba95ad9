package database

import (
	"bytes"
	"database/sql"
	"strconv"
)

type SpecialColumnTypeResult struct {
	hasGodrorJSON      bool
	godrorJSONIndexMap map[int]bool
}

func (i *SpecialColumnTypeResult) IsGoDrorJSON(index int) bool {
	if !i.hasGodrorJSON {
		return false
	}
	if _, ok := i.godrorJSONIndexMap[index]; ok {
		return true
	} else {
		return false
	}
}

func (i *SpecialColumnTypeResult) String() string {
	bs := bytes.Buffer{}
	if i.hasGodrorJSON {
		bs.WriteString("< has godror.JSON,")
	}
	if len(i.godrorJSONIndexMap) > 0 {
		bs.WriteString(" godror.JSON index:")
		for k := range i.godrorJSONIndexMap {
			bs.WriteString(" ")
			bs.WriteString(strconv.Itoa(k))
		}
	}
	bs.WriteString(">")
	return bs.String()
}

func ExtractSpecialColumnTypes(colTypes []*sql.ColumnType) *SpecialColumnTypeResult {
	var hasGodrorJSON bool
	godrorJSONIndexMap := make(map[int]bool)
	for idx, ct := range colTypes {
		if "godror.JSON" == ct.ScanType().String() {
			hasGodrorJSON = true
			godrorJSONIndexMap[idx] = true
		}
	}
	return &SpecialColumnTypeResult{
		hasGodrorJSON:      hasGodrorJSON,
		godrorJSONIndexMap: godrorJSONIndexMap,
	}
}
