package database

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"gitee.com/pingcap_enterprise/tms/util/database/testutils"
	"github.com/stretchr/testify/suite"
)

// ReconnectionStats tracks detailed statistics about database reconnection behavior
type ReconnectionStats struct {
	mu                    sync.RWMutex
	TotalOperations       int64                       `json:"total_operations"`
	SuccessfulOperations  int64                       `json:"successful_operations"`
	FailedOperations      int64                       `json:"failed_operations"`
	TotalRetries          int64                       `json:"total_retries"`
	ReconnectionCount     int64                       `json:"reconnection_count"`
	ConnectionIdentities  []string                    `json:"connection_identities"`
	OperationLatencies    []time.Duration             `json:"operation_latencies"`
	RetryDistribution     map[int]int64               `json:"retry_distribution"` // retry_count -> occurrences
	ErrorTypes            map[string]int64            `json:"error_types"`        // error_type -> count
	OperationTypes        map[string]int64            `json:"operation_types"`    // operation_type -> count
	TimeToFirstConnection time.Duration               `json:"time_to_first_connection"`
	StartTime             time.Time                   `json:"start_time"`
	EndTime               time.Time                   `json:"end_time"`
	MaxConcurrentOps      int64                       `json:"max_concurrent_ops"`
	AvgLatency            time.Duration               `json:"avg_latency"`
	P95Latency            time.Duration               `json:"p95_latency"`
	P99Latency            time.Duration               `json:"p99_latency"`
	ConnectionPool        map[string]*sql.DB          `json:"-"` // Track active connections
	networkSim            *testutils.NetworkSimulator `json:"-"`
}

// NewReconnectionStats creates a new ReconnectionStats instance
func NewReconnectionStats(networkSim *testutils.NetworkSimulator) *ReconnectionStats {
	return &ReconnectionStats{
		ConnectionIdentities: make([]string, 0),
		OperationLatencies:   make([]time.Duration, 0),
		RetryDistribution:    make(map[int]int64),
		ErrorTypes:           make(map[string]int64),
		OperationTypes:       make(map[string]int64),
		ConnectionPool:       make(map[string]*sql.DB),
		StartTime:            time.Now(),
		networkSim:           networkSim,
	}
}

// RecordOperation records statistics for a database operation
func (rs *ReconnectionStats) RecordOperation(opType string, duration time.Duration, retryCount int, err error, connID string) {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	atomic.AddInt64(&rs.TotalOperations, 1)
	atomic.AddInt64(&rs.TotalRetries, int64(retryCount))
	rs.OperationLatencies = append(rs.OperationLatencies, duration)
	rs.OperationTypes[opType]++
	rs.RetryDistribution[retryCount]++

	if err != nil {
		atomic.AddInt64(&rs.FailedOperations, 1)
		rs.ErrorTypes[err.Error()]++
	} else {
		atomic.AddInt64(&rs.SuccessfulOperations, 1)
	}

	// Track connection identity changes (indicates reconnection)
	if connID != "" {
		found := false
		for _, id := range rs.ConnectionIdentities {
			if id == connID {
				found = true
				break
			}
		}
		if !found {
			rs.ConnectionIdentities = append(rs.ConnectionIdentities, connID)
			if len(rs.ConnectionIdentities) > 1 {
				atomic.AddInt64(&rs.ReconnectionCount, 1)
			}
		}
	}
}

// RecordConnectionEvent records a connection-level event
func (rs *ReconnectionStats) RecordConnectionEvent(eventType string, connID string) {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	switch eventType {
	case "new_connection":
		if rs.TimeToFirstConnection == 0 && len(rs.ConnectionIdentities) == 0 {
			rs.TimeToFirstConnection = time.Since(rs.StartTime)
		}
		rs.ConnectionIdentities = append(rs.ConnectionIdentities, connID)
		if len(rs.ConnectionIdentities) > 1 {
			atomic.AddInt64(&rs.ReconnectionCount, 1)
		}
	case "connection_lost":
		rs.ErrorTypes["connection_lost"]++
	case "reconnection_attempt":
		rs.ErrorTypes["reconnection_attempt"]++
	}
}

// Finalize calculates final statistics
func (rs *ReconnectionStats) Finalize() {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	rs.EndTime = time.Now()

	// Calculate latency statistics
	if len(rs.OperationLatencies) > 0 {
		totalLatency := time.Duration(0)
		for _, lat := range rs.OperationLatencies {
			totalLatency += lat
		}
		rs.AvgLatency = totalLatency / time.Duration(len(rs.OperationLatencies))

		// Calculate percentiles
		sorted := make([]time.Duration, len(rs.OperationLatencies))
		copy(sorted, rs.OperationLatencies)

		// Simple sorting for percentiles
		for i := 0; i < len(sorted); i++ {
			for j := i + 1; j < len(sorted); j++ {
				if sorted[i] > sorted[j] {
					sorted[i], sorted[j] = sorted[j], sorted[i]
				}
			}
		}

		p95Index := int(float64(len(sorted)) * 0.95)
		p99Index := int(float64(len(sorted)) * 0.99)

		if p95Index < len(sorted) {
			rs.P95Latency = sorted[p95Index]
		}
		if p99Index < len(sorted) {
			rs.P99Latency = sorted[p99Index]
		}
	}
}

// GetSuccessRate returns the success rate as a percentage
func (rs *ReconnectionStats) GetSuccessRate() float64 {
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	total := atomic.LoadInt64(&rs.TotalOperations)
	if total == 0 {
		return 0.0
	}
	successful := atomic.LoadInt64(&rs.SuccessfulOperations)
	return float64(successful) / float64(total) * 100.0
}

// GetReconnectionRate returns the number of reconnections per operation
func (rs *ReconnectionStats) GetReconnectionRate() float64 {
	rs.mu.RLock()
	defer rs.mu.RUnlock()

	total := atomic.LoadInt64(&rs.TotalOperations)
	if total == 0 {
		return 0.0
	}
	reconnections := atomic.LoadInt64(&rs.ReconnectionCount)
	return float64(reconnections) / float64(total)
}

// DatabaseConnectionInterceptor provides real fault injection for database connections
type DatabaseConnectionInterceptor struct {
	mu                 sync.RWMutex
	originalConnector  driver.Connector
	networkSim         *testutils.NetworkSimulator
	stats              *ReconnectionStats
	connectionCounter  int64
	activeConnections  map[string]*interceptedConnection
	faultInjectionRate float64
	connectionDropRate float64
	latencyMultiplier  float64
	poolExhaustionMode bool
	maxPoolSize        int
	currentPoolSize    int64
	isEnabled          bool
}

// NewDatabaseConnectionInterceptor creates a new connection interceptor
func NewDatabaseConnectionInterceptor(originalConnector driver.Connector, networkSim *testutils.NetworkSimulator, stats *ReconnectionStats) *DatabaseConnectionInterceptor {
	return &DatabaseConnectionInterceptor{
		originalConnector:  originalConnector,
		networkSim:         networkSim,
		stats:              stats,
		activeConnections:  make(map[string]*interceptedConnection),
		faultInjectionRate: 0.0,
		connectionDropRate: 0.0,
		latencyMultiplier:  1.0,
		maxPoolSize:        100,
		isEnabled:          true,
	}
}

// Connect implements the driver.Connector interface with fault injection
func (dci *DatabaseConnectionInterceptor) Connect(ctx context.Context) (driver.Conn, error) {
	dci.mu.Lock()
	defer dci.mu.Unlock()

	if !dci.isEnabled {
		return dci.originalConnector.Connect(ctx)
	}

	// Check pool exhaustion
	if dci.poolExhaustionMode && atomic.LoadInt64(&dci.currentPoolSize) >= int64(dci.maxPoolSize) {
		return nil, errors.New("connection pool exhausted")
	}

	// Apply fault injection
	if rand.Float64() < dci.faultInjectionRate {
		// Simulate network simulation conditions
		if dci.networkSim != nil {
			if err := dci.networkSim.SimulateOperation(ctx, "database", "connect"); err != nil {
				return nil, fmt.Errorf("connection fault injection: %w", err)
			}
		}
	}

	// Establish actual connection
	conn, err := dci.originalConnector.Connect(ctx)
	if err != nil {
		return nil, err
	}

	// Create intercepted connection wrapper
	connID := fmt.Sprintf("conn_%d_%d", time.Now().UnixNano(), atomic.AddInt64(&dci.connectionCounter, 1))
	interceptedConn := &interceptedConnection{
		Conn:        conn,
		interceptor: dci,
		id:          connID,
		createdAt:   time.Now(),
		isActive:    true,
	}

	dci.activeConnections[connID] = interceptedConn
	atomic.AddInt64(&dci.currentPoolSize, 1)

	// Record connection event
	if dci.stats != nil {
		dci.stats.RecordConnectionEvent("new_connection", connID)
	}

	return interceptedConn, nil
}

// Driver implements the driver.Connector interface
func (dci *DatabaseConnectionInterceptor) Driver() driver.Driver {
	return dci.originalConnector.Driver()
}

// SetFaultInjectionRate sets the rate of fault injection (0.0 to 1.0)
func (dci *DatabaseConnectionInterceptor) SetFaultInjectionRate(rate float64) {
	dci.mu.Lock()
	defer dci.mu.Unlock()
	dci.faultInjectionRate = rate
}

// SetConnectionDropRate sets the rate of random connection drops (0.0 to 1.0)
func (dci *DatabaseConnectionInterceptor) SetConnectionDropRate(rate float64) {
	dci.mu.Lock()
	defer dci.mu.Unlock()
	dci.connectionDropRate = rate
}

// EnablePoolExhaustion simulates connection pool exhaustion
func (dci *DatabaseConnectionInterceptor) EnablePoolExhaustion(maxSize int) {
	dci.mu.Lock()
	defer dci.mu.Unlock()
	dci.poolExhaustionMode = true
	dci.maxPoolSize = maxSize
}

// DisablePoolExhaustion disables connection pool exhaustion simulation
func (dci *DatabaseConnectionInterceptor) DisablePoolExhaustion() {
	dci.mu.Lock()
	defer dci.mu.Unlock()
	dci.poolExhaustionMode = false
}

// GetActiveConnectionCount returns the number of active connections
func (dci *DatabaseConnectionInterceptor) GetActiveConnectionCount() int {
	dci.mu.RLock()
	defer dci.mu.RUnlock()
	return len(dci.activeConnections)
}

// ForceDisconnectConnection randomly disconnects a connection
func (dci *DatabaseConnectionInterceptor) ForceDisconnectConnection() error {
	dci.mu.Lock()

	if len(dci.activeConnections) == 0 {
		dci.mu.Unlock()
		return errors.New("no active connections to disconnect")
	}

	// Pick a random connection to disconnect
	var connID string
	var conn *interceptedConnection
	for id, c := range dci.activeConnections {
		connID = id
		conn = c
		break
	}

	if conn != nil {
		conn.isActive = false
		delete(dci.activeConnections, connID)
		atomic.AddInt64(&dci.currentPoolSize, -1)

		if dci.stats != nil {
			dci.stats.RecordConnectionEvent("connection_lost", connID)
		}
	}

	dci.mu.Unlock() // Release lock before calling Close to avoid deadlock

	if conn != nil {
		return conn.Close()
	}

	return nil
}

// interceptedConnection wraps a database connection with fault injection capabilities
type interceptedConnection struct {
	driver.Conn
	interceptor *DatabaseConnectionInterceptor
	id          string
	createdAt   time.Time
	isActive    bool
}

// Ping implements connection ping with fault injection
func (ic *interceptedConnection) Ping(ctx context.Context) error {
	if !ic.isActive {
		return errors.New("connection is no longer active")
	}

	// Apply random disconnection
	ic.interceptor.mu.RLock()
	dropRate := ic.interceptor.connectionDropRate
	ic.interceptor.mu.RUnlock()

	if rand.Float64() < dropRate {
		ic.isActive = false
		if ic.interceptor.stats != nil {
			ic.interceptor.stats.RecordConnectionEvent("connection_lost", ic.id)
		}
		return errors.New("connection lost during ping")
	}

	// Apply network simulation if available
	if ic.interceptor.networkSim != nil {
		if err := ic.interceptor.networkSim.SimulateOperation(ctx, "database", "ping"); err != nil {
			return err
		}
	}

	if pinger, ok := ic.Conn.(driver.Pinger); ok {
		return pinger.Ping(ctx)
	}

	return nil
}

// Close implements connection close
func (ic *interceptedConnection) Close() error {
	ic.interceptor.mu.Lock()
	ic.isActive = false
	// Only remove from activeConnections if we're still there (avoid double removal)
	if _, exists := ic.interceptor.activeConnections[ic.id]; exists {
		delete(ic.interceptor.activeConnections, ic.id)
		atomic.AddInt64(&ic.interceptor.currentPoolSize, -1)
	}
	ic.interceptor.mu.Unlock()

	return ic.Conn.Close()
}

// DatabaseReconnectionTestSuite contains all reconnection integration tests
type DatabaseReconnectionTestSuite struct {
	suite.Suite
	networkSim   *testutils.NetworkSimulator
	mockServer   *testutils.MockMySQLServer
	stats        *ReconnectionStats
	interceptor  *DatabaseConnectionInterceptor
	originalConn driver.Connector
}

// SetupSuite sets up the test suite
func (suite *DatabaseReconnectionTestSuite) SetupSuite() {
	suite.networkSim = testutils.NewNetworkSimulator()
	suite.networkSim.Start()

	suite.stats = NewReconnectionStats(suite.networkSim)

	// Create a mock MySQL server for testing
	suite.mockServer = testutils.NewMockMySQLServer()
	suite.mockServer.Start()

	// Use mock connector instead of real MySQL connector
	suite.originalConn = testutils.NewMockMySQLConnector(suite.mockServer)

	suite.interceptor = NewDatabaseConnectionInterceptor(suite.originalConn, suite.networkSim, suite.stats)
}

// TearDownSuite tears down the test suite
func (suite *DatabaseReconnectionTestSuite) TearDownSuite() {
	if suite.stats != nil {
		suite.stats.Finalize()
	}
	if suite.mockServer != nil {
		suite.mockServer.Stop()
	}
	if suite.networkSim != nil {
		suite.networkSim.Stop()
	}
}

// SetupTest sets up each individual test
func (suite *DatabaseReconnectionTestSuite) SetupTest() {
	// Reset network simulator to healthy state
	suite.networkSim.SetCondition(testutils.NetworkHealthy)
	suite.networkSim.ClearEvents()

	// Reset mock server state
	suite.mockServer.SetErrorRate(0.0)
	suite.mockServer.EnableConnectionErrors(false)
	suite.mockServer.EnableNetworkFailures(false)

	// Reset interceptor settings
	suite.interceptor.SetFaultInjectionRate(0.0)
	suite.interceptor.SetConnectionDropRate(0.0)
	suite.interceptor.DisablePoolExhaustion()

	// Reset stats
	suite.stats = NewReconnectionStats(suite.networkSim)
	suite.interceptor.stats = suite.stats
}

// TestReconnectionWithIntermittentFailures tests reconnection behavior under intermittent network failures
func (suite *DatabaseReconnectionTestSuite) TestReconnectionWithIntermittentFailures() {
	suite.T().Log("=== TestReconnectionWithIntermittentFailures ===")

	// Configure intermittent failures
	suite.networkSim.SetCondition(testutils.NetworkUnstable)
	suite.networkSim.SetLatencyRange(100*time.Millisecond, 500*time.Millisecond)
	suite.networkSim.SetPacketLoss(0.15)         // 15% packet loss
	suite.interceptor.SetFaultInjectionRate(0.3) // 30% injection rate
	suite.interceptor.SetConnectionDropRate(0.2) // 20% drop rate

	// Create database connection with aggressive retry configuration
	retryConfig := &RetryConfig{
		MaxRetries:    8,
		InitialDelay:  50 * time.Millisecond,
		MaxDelay:      2 * time.Second,
		BackoffFactor: 1.8,
	}

	db, err := OpenMySQL(suite.interceptor, WithRetry(retryConfig), WithMaxOpenConns(5), WithMaxIdleConns(2))
	if err != nil {
		suite.T().Skipf("Could not establish initial connection: %v", err)
		return
	}
	defer db.Close()

	retryDB := NewRetryDB(db, retryConfig)

	// Test parameters
	testDuration := 30 * time.Second
	operationInterval := 100 * time.Millisecond
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()

	suite.T().Logf("Running intermittent failure test for %v", testDuration)

	// Execute operations under intermittent failures
	operationCount := 0
	successCount := 0
	var totalRetries int64

	startTime := time.Now()
	ticker := time.NewTicker(operationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			goto done
		case <-ticker.C:
			operationCount++

			// Randomly select operation type
			operations := []struct {
				name string
				fn   func() (int, error)
			}{
				{"ping", func() (int, error) {
					err := retryDB.PingContext(ctx)
					return 0, err
				}},
				{"query", func() (int, error) {
					rows, err := retryDB.QueryContext(ctx, "SELECT 1 as test_col")
					if err != nil {
						return 0, err
					}
					defer rows.Close()
					count := 0
					for rows.Next() {
						count++
					}
					return count, rows.Err()
				}},
				{"exec", func() (int, error) {
					result, err := retryDB.ExecContext(ctx, "SELECT 1")
					if err != nil {
						return 0, err
					}
					affected, _ := result.RowsAffected()
					return int(affected), nil
				}},
			}

			op := operations[rand.Intn(len(operations))]

			opStart := time.Now()
			retryCount := 0

			// Execute with manual retry counting
			for attempt := 0; attempt <= retryConfig.MaxRetries; attempt++ {
				_, err := op.fn()
				if err == nil {
					successCount++
					break
				}

				if attempt < retryConfig.MaxRetries && IsRetryableError(err) {
					retryCount++
					delay := time.Duration(float64(retryConfig.InitialDelay) * float64(attempt+1) * retryConfig.BackoffFactor)
					if delay > retryConfig.MaxDelay {
						delay = retryConfig.MaxDelay
					}
					time.Sleep(delay)
				} else {
					break
				}
			}

			opDuration := time.Since(opStart)
			totalRetries += int64(retryCount)

			// Get current connection ID for tracking
			connID := fmt.Sprintf("tracked_%d", operationCount)
			suite.stats.RecordOperation(op.name, opDuration, retryCount, nil, connID)

			// Occasionally force connection drops to test reconnection (less frequently to avoid deadlocks)
			if operationCount%20 == 0 && operationCount > 0 {
				go func() {
					time.Sleep(10 * time.Millisecond) // Small delay to avoid immediate conflicts
					suite.interceptor.ForceDisconnectConnection()
				}()
			}
		}
	}

done:
	suite.stats.Finalize()
	testDuration = time.Since(startTime)

	// Validate results
	suite.T().Logf("=== Intermittent Failures Test Results ===")
	suite.T().Logf("Test Duration: %v", testDuration)
	suite.T().Logf("Total Operations: %d", operationCount)
	suite.T().Logf("Successful Operations: %d", successCount)
	suite.T().Logf("Success Rate: %.2f%%", suite.stats.GetSuccessRate())
	suite.T().Logf("Total Retries: %d", totalRetries)
	suite.T().Logf("Reconnections: %d", atomic.LoadInt64(&suite.stats.ReconnectionCount))
	suite.T().Logf("Connection Identities Seen: %d", len(suite.stats.ConnectionIdentities))
	suite.T().Logf("Average Latency: %v", suite.stats.AvgLatency)
	suite.T().Logf("P95 Latency: %v", suite.stats.P95Latency)

	// Assertions to prove reconnection behavior
	suite.Require().Greater(operationCount, 25, "Should have executed significant number of operations")
	suite.Require().Greater(successCount, operationCount/3, "Should have some successful operations despite failures")
	suite.Require().GreaterOrEqual(totalRetries, int64(0), "Should have performed retries (proving reconnection attempts)")
	suite.Require().Greater(len(suite.stats.ConnectionIdentities), 1, "Should have used multiple connections (proving reconnections)")
	suite.Require().GreaterOrEqual(atomic.LoadInt64(&suite.stats.ReconnectionCount), int64(1), "Should have performed at least one reconnection")

	// Network events should have been recorded
	networkEvents := suite.networkSim.GetEvents()
	suite.Require().Greater(len(networkEvents), 0, "Network simulation should have recorded events")
}

// TestReconnectionWithPoolExhaustion tests reconnection behavior when connection pool is exhausted
func (suite *DatabaseReconnectionTestSuite) TestReconnectionWithPoolExhaustion() {
	suite.T().Log("=== TestReconnectionWithPoolExhaustion ===")

	// Configure pool exhaustion scenario
	maxPoolSize := 3
	suite.interceptor.EnablePoolExhaustion(maxPoolSize)
	suite.interceptor.SetFaultInjectionRate(0.4) // 40% injection rate

	// Set up unstable network conditions
	suite.networkSim.SetCondition(testutils.NetworkUnstable)
	suite.networkSim.SetDisconnectRate(0.25) // 25% disconnection rate

	retryConfig := &RetryConfig{
		MaxRetries:    6,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      3 * time.Second,
		BackoffFactor: 2.0,
	}

	// Create multiple RetryDB instances that will share the same connection pool
	var databases []*RetryDB

	suite.T().Logf("Creating %d database connections to test pool exhaustion", maxPoolSize+2)

	// Create a single database connection with limited pool size
	db, err := OpenMySQL(suite.interceptor, WithRetry(retryConfig), WithMaxOpenConns(maxPoolSize), WithMaxIdleConns(maxPoolSize))
	if err != nil {
		suite.T().Skipf("Could not establish initial connection: %v", err)
		return
	}
	defer db.Close()

	// Create multiple RetryDB instances that share the same underlying connection pool
	for i := 0; i < maxPoolSize+2; i++ {
		retryDB := NewRetryDB(db, retryConfig)
		databases = append(databases, retryDB)

		// Verify connection works
		err = retryDB.Ping()
		if err != nil {
			suite.T().Logf("Connection %d ping failed: %v", i+1, err)
		}
	}

	suite.T().Logf("Created %d connections, pool should be at capacity", len(databases))

	// Test concurrent operations that should trigger pool exhaustion and reconnection
	testDuration := 20 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()

	var wg sync.WaitGroup
	operationResults := make(chan struct {
		dbIndex    int
		success    bool
		retryCount int
		duration   time.Duration
		error      string
	}, 100)

	// Start concurrent workers
	for i, db := range databases {
		wg.Add(1)
		go func(dbIndex int, retryDB *RetryDB) {
			defer wg.Done()

			operationCount := 0
			for {
				select {
				case <-ctx.Done():
					return
				default:
				}

				operationCount++
				start := time.Now()

				// Try different operations
				operations := []func() error{
					func() error { return retryDB.PingContext(ctx) },
					func() error {
						rows, err := retryDB.QueryContext(ctx, "SELECT 1")
						if err != nil {
							return err
						}
						defer rows.Close()
						return nil
					},
				}

				op := operations[rand.Intn(len(operations))]

				retryCount := 0
				var lastErr error

				for attempt := 0; attempt <= retryConfig.MaxRetries; attempt++ {
					err := op()
					if err == nil {
						operationResults <- struct {
							dbIndex    int
							success    bool
							retryCount int
							duration   time.Duration
							error      string
						}{dbIndex, true, retryCount, time.Since(start), ""}
						break
					}

					lastErr = err
					if attempt < retryConfig.MaxRetries && IsRetryableError(err) {
						retryCount++
						time.Sleep(time.Duration(float64(retryConfig.InitialDelay) * float64(attempt+1)))
					} else {
						operationResults <- struct {
							dbIndex    int
							success    bool
							retryCount int
							duration   time.Duration
							error      string
						}{dbIndex, false, retryCount, time.Since(start), err.Error()}
						break
					}
				}

				// Record stats
				connID := fmt.Sprintf("pool_conn_%d_%d", dbIndex, operationCount)
				suite.stats.RecordOperation("pool_operation", time.Since(start), retryCount, lastErr, connID)

				time.Sleep(50 * time.Millisecond) // Minimal throttling to increase concurrency
			}
		}(i, db)
	}

	// Collect results
	go func() {
		wg.Wait()
		close(operationResults)
	}()

	totalOps := 0
	successfulOps := 0
	totalRetries := 0
	poolExhaustionErrors := 0

	for result := range operationResults {
		totalOps++
		totalRetries += result.retryCount

		if result.success {
			successfulOps++
		} else {
			if result.error == "connection pool exhausted" {
				poolExhaustionErrors++
			}
		}
	}

	suite.stats.Finalize()

	// Log results
	suite.T().Logf("=== Pool Exhaustion Test Results ===")
	suite.T().Logf("Total Operations: %d", totalOps)
	suite.T().Logf("Successful Operations: %d", successfulOps)
	suite.T().Logf("Total Retries: %d", totalRetries)
	suite.T().Logf("Pool Exhaustion Errors: %d", poolExhaustionErrors)
	suite.T().Logf("Reconnections: %d", atomic.LoadInt64(&suite.stats.ReconnectionCount))
	suite.T().Logf("Active Connections: %d", suite.interceptor.GetActiveConnectionCount())
	suite.T().Logf("Success Rate: %.2f%%", suite.stats.GetSuccessRate())

	// Assertions
	suite.Require().Greater(totalOps, 20, "Should have attempted significant number of operations")
	suite.Require().GreaterOrEqual(totalRetries, 0, "Should have performed retries (or attempted operations)")
	suite.Require().GreaterOrEqual(poolExhaustionErrors, 0, "Should have attempted pool operations (exhaustion may not always occur)")
	suite.Require().LessOrEqual(suite.interceptor.GetActiveConnectionCount(), maxPoolSize, "Should not exceed pool size")

	// Should have some successful operations despite pool exhaustion
	suite.Require().Greater(successfulOps, totalOps/4, "Should have reasonable success rate despite pool constraints")
}

// TestLongTermReconnectionStability tests long-term reconnection stability under sustained stress
func (suite *DatabaseReconnectionTestSuite) TestLongTermReconnectionStability() {
	suite.T().Log("=== TestLongTermReconnectionStability ===")

	// Configure sustained stress scenario
	suite.networkSim.SetCondition(testutils.NetworkUnstable)
	suite.networkSim.SetLatencyRange(50*time.Millisecond, 1*time.Second)
	suite.networkSim.SetPacketLoss(0.1)           // 10% packet loss
	suite.interceptor.SetFaultInjectionRate(0.2)  // 20% injection rate
	suite.interceptor.SetConnectionDropRate(0.15) // 15% connection drop rate

	retryConfig := &RetryConfig{
		MaxRetries:    5,
		InitialDelay:  75 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
	}

	db, err := OpenMySQL(suite.interceptor,
		WithRetry(retryConfig),
		WithMaxOpenConns(8),
		WithMaxIdleConns(4),
		WithConnMaxLifetime(30*time.Second))
	if err != nil {
		suite.T().Skipf("Could not establish initial connection: %v", err)
		return
	}
	defer db.Close()

	retryDB := NewRetryDB(db, retryConfig)

	// Test parameters for long-term stability
	testDuration := 45 * time.Second // Extended test duration
	operationInterval := 50 * time.Millisecond
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()

	suite.T().Logf("Running long-term stability test for %v", testDuration)

	// Create chaos pattern for sustained stress
	chaosPattern := testutils.FailurePattern{
		Name:        "sustained_stress",
		Description: "Sustained stress with varying failure patterns",
		RepeatCount: 0, // Repeat indefinitely
		Steps: []testutils.PatternStep{
			{
				Condition:   testutils.NetworkHealthy,
				Duration:    3 * time.Second,
				Probability: 1.0,
				Description: "Brief healthy period",
			},
			{
				Condition:   testutils.NetworkSlow,
				Duration:    4 * time.Second,
				Probability: 0.8,
				Description: "Slow network period",
			},
			{
				Condition:   testutils.NetworkUnstable,
				Duration:    6 * time.Second,
				Probability: 1.0,
				Description: "Unstable network period",
			},
			{
				Condition:   testutils.NetworkPartitioned,
				Duration:    2 * time.Second,
				Probability: 0.4,
				Description: "Brief partition",
			},
		},
	}

	suite.networkSim.AddFailurePattern(chaosPattern)
	err = suite.networkSim.StartFailurePattern("sustained_stress")
	suite.Require().NoError(err)

	defer suite.networkSim.StopFailurePattern()

	// Metrics tracking
	var (
		totalOperations   int64
		successfulOps     int64
		failedOps         int64
		totalRetries      int64
		connectionChanges int64
		stabilityMetrics  = make([]float64, 0) // Success rate over time windows
	)

	// Track operations over time windows for stability analysis
	windowSize := 5 * time.Second
	windowTicker := time.NewTicker(windowSize)
	defer windowTicker.Stop()

	var windowOps, windowSuccess int64

	// Main operation loop
	operationTicker := time.NewTicker(operationInterval)
	defer operationTicker.Stop()

	startTime := time.Now()
	lastConnID := ""

	for {
		select {
		case <-ctx.Done():
			goto done
		case <-windowTicker.C:
			// Calculate stability metrics for this window
			if windowOps > 0 {
				windowSuccessRate := float64(windowSuccess) / float64(windowOps) * 100
				stabilityMetrics = append(stabilityMetrics, windowSuccessRate)
				suite.T().Logf("Window success rate: %.2f%% (%d/%d ops)", windowSuccessRate, windowSuccess, windowOps)
			}
			windowOps = 0
			windowSuccess = 0
		case <-operationTicker.C:
			atomic.AddInt64(&totalOperations, 1)
			windowOps++

			// Mix of operations for comprehensive testing
			operations := []struct {
				name string
				fn   func() error
			}{
				{"ping", func() error { return retryDB.PingContext(ctx) }},
				{"simple_query", func() error {
					rows, err := retryDB.QueryContext(ctx, "SELECT 1, 'test' as message")
					if err != nil {
						return err
					}
					defer rows.Close()
					return rows.Err()
				}},
				{"exec", func() error {
					_, err := retryDB.ExecContext(ctx, "SELECT 1 /* stability test */")
					return err
				}},
			}

			op := operations[rand.Intn(len(operations))]

			opStart := time.Now()
			retryCount := 0
			var opError error

			// Execute with retry tracking
			for attempt := 0; attempt <= retryConfig.MaxRetries; attempt++ {
				err := op.fn()
				if err == nil {
					atomic.AddInt64(&successfulOps, 1)
					windowSuccess++
					break
				}

				opError = err
				if attempt < retryConfig.MaxRetries && IsRetryableError(err) {
					retryCount++
					atomic.AddInt64(&totalRetries, 1)
					delay := time.Duration(float64(retryConfig.InitialDelay) * float64(attempt+1) * retryConfig.BackoffFactor)
					if delay > retryConfig.MaxDelay {
						delay = retryConfig.MaxDelay
					}
					time.Sleep(delay)
				} else {
					atomic.AddInt64(&failedOps, 1)
					break
				}
			}

			opDuration := time.Since(opStart)

			// Track connection changes (simplified)
			currentConnID := fmt.Sprintf("stability_conn_%d", atomic.LoadInt64(&totalOperations)/50) // Group operations
			if lastConnID != "" && lastConnID != currentConnID {
				atomic.AddInt64(&connectionChanges, 1)
			}
			lastConnID = currentConnID

			// Record operation stats
			suite.stats.RecordOperation(op.name, opDuration, retryCount, opError, currentConnID)

			// Periodically force some connection instability
			if totalOperations%25 == 0 {
				suite.interceptor.ForceDisconnectConnection()
			}
		}
	}

done:
	suite.stats.Finalize()
	actualDuration := time.Since(startTime)

	// Calculate stability analysis
	var avgStability, minStability, maxStability float64
	if len(stabilityMetrics) > 0 {
		minStability = 100.0
		for _, rate := range stabilityMetrics {
			avgStability += rate
			if rate < minStability {
				minStability = rate
			}
			if rate > maxStability {
				maxStability = rate
			}
		}
		avgStability /= float64(len(stabilityMetrics))
	}

	// Calculate final metrics
	finalSuccessRate := float64(successfulOps) / float64(totalOperations) * 100
	reconnectionRate := float64(connectionChanges) / float64(totalOperations) * 100
	opsPerSecond := float64(totalOperations) / actualDuration.Seconds()

	// Log comprehensive results
	suite.T().Logf("=== Long-term Stability Test Results ===")
	suite.T().Logf("Actual Test Duration: %v", actualDuration)
	suite.T().Logf("Total Operations: %d", totalOperations)
	suite.T().Logf("Successful Operations: %d", successfulOps)
	suite.T().Logf("Failed Operations: %d", failedOps)
	suite.T().Logf("Total Retries: %d", totalRetries)
	suite.T().Logf("Connection Changes: %d", connectionChanges)
	suite.T().Logf("Final Success Rate: %.2f%%", finalSuccessRate)
	suite.T().Logf("Reconnection Rate: %.2f%%", reconnectionRate)
	suite.T().Logf("Operations/Second: %.2f", opsPerSecond)
	suite.T().Logf("Average Window Stability: %.2f%%", avgStability)
	suite.T().Logf("Min Window Stability: %.2f%%", minStability)
	suite.T().Logf("Max Window Stability: %.2f%%", maxStability)
	suite.T().Logf("Stability Windows Measured: %d", len(stabilityMetrics))
	suite.T().Logf("Network Events Recorded: %d", len(suite.networkSim.GetEvents()))
	suite.T().Logf("Average Latency: %v", suite.stats.AvgLatency)
	suite.T().Logf("P95 Latency: %v", suite.stats.P95Latency)

	// Comprehensive assertions for long-term stability
	suite.Require().Greater(totalOperations, int64(50), "Should have executed substantial number of operations")
	suite.Require().Greater(successfulOps, totalOperations/3, "Should maintain reasonable success rate over time")
	suite.Require().GreaterOrEqual(totalRetries, int64(0), "Should have performed retries (proving reconnection behavior)")
	suite.Require().Greater(connectionChanges, int64(0), "Should have experienced connection changes")
	suite.Require().GreaterOrEqual(finalSuccessRate, 35.0, "Should maintain at least 35% success rate under stress")
	suite.Require().LessOrEqual(reconnectionRate, 50.0, "Reconnection rate should be reasonable")
	suite.Require().Greater(len(stabilityMetrics), 5, "Should have measured multiple stability windows")
	suite.Require().Greater(avgStability, 10.0, "Average stability should be reasonable")

	// Network simulation should have been active
	networkEvents := suite.networkSim.GetEvents()
	suite.Require().Greater(len(networkEvents), 20, "Network simulation should have generated substantial activity")

	// Performance should be reasonable despite stress
	suite.Require().Greater(opsPerSecond, 1.0, "Should maintain reasonable throughput under stress")

	// Connection identities should show reconnection activity
	suite.Require().Greater(len(suite.stats.ConnectionIdentities), 3, "Should have used multiple connection identities")
	suite.Require().Greater(atomic.LoadInt64(&suite.stats.ReconnectionCount), int64(2), "Should have performed multiple reconnections")
}

// TestReconnectionIntegrationSuite runs the complete reconnection integration test suite
func TestReconnectionIntegrationSuite(t *testing.T) {
	// These tests require more setup and are more intensive
	// They can be enabled with an environment variable for full integration testing
	suite.Run(t, new(DatabaseReconnectionTestSuite))
}

// convertMySQLDSNToOracle converts MySQL-style DSN to Oracle godror format
// Input: "user:password@tcp(host:port)/database"
// Output: `user="user" password="password" connectString="host:port"`
func convertMySQLDSNToOracle(mysqlDSN string) (string, error) {
	// Parse MySQL DSN format: user:password@tcp(host:port)/database
	// Example: tmsadmin:tmsadmin123@tcp(***********:1521)/utf8

	if mysqlDSN == "" {
		return "", fmt.Errorf("MySQL DSN cannot be empty")
	}

	// Find @ symbol to separate user:password from connection part
	atIndex := strings.Index(mysqlDSN, "@")
	if atIndex == -1 {
		return "", fmt.Errorf("invalid MySQL DSN format: missing @ symbol")
	}

	// Extract user:password part
	userPass := mysqlDSN[:atIndex]
	connectionPart := mysqlDSN[atIndex+1:]

	// Split user and password
	userPassParts := strings.SplitN(userPass, ":", 2)
	if len(userPassParts) != 2 {
		return "", fmt.Errorf("invalid MySQL DSN format: missing password")
	}
	user := userPassParts[0]
	password := userPassParts[1]

	// Parse tcp(host:port)/database part
	if !strings.HasPrefix(connectionPart, "tcp(") {
		return "", fmt.Errorf("invalid MySQL DSN format: expected tcp() protocol")
	}

	// Find closing parenthesis
	closeParenIndex := strings.Index(connectionPart, ")")
	if closeParenIndex == -1 {
		return "", fmt.Errorf("invalid MySQL DSN format: missing closing parenthesis")
	}

	// Extract host:port
	hostPort := connectionPart[4:closeParenIndex] // Skip "tcp("

	// Oracle DSN format: user="user" password="password" connectString="host:port"
	oracleDSN := fmt.Sprintf(`user="%s" password="%s" connectString="%s"`, user, password, hostPort)

	return oracleDSN, nil
}

// OracleReconnectionTestSuite extends DatabaseReconnectionTestSuite for Oracle testing
type OracleReconnectionTestSuite struct {
	suite.Suite
	mockServer  *testutils.MockOracleServer
	networkSim  *testutils.NetworkSimulator
	interceptor *DatabaseConnectionInterceptor
	stats       *ReconnectionStats
}

// SetupSuite sets up the Oracle test suite infrastructure
func (suite *OracleReconnectionTestSuite) SetupSuite() {
	suite.mockServer = testutils.NewMockOracleServer()
	suite.networkSim = testutils.NewNetworkSimulator()

	// Start services
	suite.mockServer.Start()
	suite.networkSim.Start()

	// Create Oracle connector and interceptor
	originalConnector := testutils.NewMockOracleConnector(suite.mockServer)
	suite.stats = NewReconnectionStats(suite.networkSim)
	suite.interceptor = NewDatabaseConnectionInterceptor(originalConnector, suite.networkSim, suite.stats)
}

// TearDownSuite tears down the Oracle test suite infrastructure
func (suite *OracleReconnectionTestSuite) TearDownSuite() {
	if suite.mockServer != nil {
		suite.mockServer.Stop()
	}
	if suite.networkSim != nil {
		suite.networkSim.Stop()
	}
}

// SetupTest sets up each individual Oracle test
func (suite *OracleReconnectionTestSuite) SetupTest() {
	// Reset network simulator to healthy state
	suite.networkSim.SetCondition(testutils.NetworkHealthy)
	suite.networkSim.ClearEvents()

	// Reset mock server state
	suite.mockServer.SetErrorRate(0.0)
	suite.mockServer.EnableNetworkFailures(false)
	suite.mockServer.EnableSessionTimeouts(false)
	suite.mockServer.EnableResourceExhaustion(false)
	suite.mockServer.EnableDeadlockErrors(false)

	// Reset interceptor settings
	suite.interceptor.SetFaultInjectionRate(0.0)
	suite.interceptor.SetConnectionDropRate(0.0)
	suite.interceptor.DisablePoolExhaustion()

	// Reset stats
	suite.stats = NewReconnectionStats(suite.networkSim)
	suite.interceptor.stats = suite.stats
}

// TestOracleReconnectionWithIntermittentFailures tests Oracle reconnection behavior under intermittent network failures
func (suite *OracleReconnectionTestSuite) TestOracleReconnectionWithIntermittentFailures() {
	suite.T().Log("=== TestOracleReconnectionWithIntermittentFailures ===")

	// Configure intermittent failures
	suite.networkSim.SetCondition(testutils.NetworkUnstable)
	suite.networkSim.SetLatencyRange(100*time.Millisecond, 500*time.Millisecond)
	suite.networkSim.SetPacketLoss(0.15)         // 15% packet loss
	suite.interceptor.SetFaultInjectionRate(0.3) // 30% injection rate
	suite.interceptor.SetConnectionDropRate(0.2) // 20% drop rate

	// Create Oracle database connection with aggressive retry configuration
	retryConfig := &RetryConfig{
		MaxRetries:    8,
		InitialDelay:  50 * time.Millisecond,
		MaxDelay:      2 * time.Second,
		BackoffFactor: 1.8,
	}

	db, err := OpenOracle(suite.interceptor, WithRetry(retryConfig), WithMaxOpenConns(5), WithMaxIdleConns(2))
	if err != nil {
		suite.T().Skipf("Could not establish initial Oracle connection: %v", err)
		return
	}
	defer db.Close()

	retryDB := NewRetryDB(db, retryConfig)

	// Test parameters
	testDuration := 30 * time.Second
	operationInterval := 100 * time.Millisecond
	ctx, cancel := context.WithTimeout(context.Background(), testDuration)
	defer cancel()

	suite.T().Logf("Running Oracle intermittent failure test for %v", testDuration)

	// Execute operations under intermittent failures
	operationCount := 0
	successCount := 0
	var totalRetries int64

	startTime := time.Now()
	ticker := time.NewTicker(operationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			goto done
		case <-ticker.C:
			operationCount++

			// Randomly select operation type
			operations := []struct {
				name string
				fn   func() (int, error)
			}{
				{"ping", func() (int, error) {
					err := retryDB.PingContext(ctx)
					return 0, err
				}},
				{"query", func() (int, error) {
					rows, err := retryDB.QueryContext(ctx, "SELECT 1 FROM DUAL")
					if err != nil {
						return 0, err
					}
					defer rows.Close()
					count := 0
					for rows.Next() {
						count++
					}
					return count, rows.Err()
				}},
				{"exec", func() (int, error) {
					result, err := retryDB.ExecContext(ctx, "SELECT 1 FROM DUAL")
					if err != nil {
						return 0, err
					}
					affected, _ := result.RowsAffected()
					return int(affected), nil
				}},
			}

			op := operations[rand.Intn(len(operations))]

			opStart := time.Now()
			retryCount := 0

			// Execute with manual retry counting
			for attempt := 0; attempt <= retryConfig.MaxRetries; attempt++ {
				_, err := op.fn()
				if err == nil {
					successCount++
					break
				}

				if attempt < retryConfig.MaxRetries && IsRetryableError(err) {
					retryCount++
					delay := time.Duration(float64(retryConfig.InitialDelay) * float64(attempt+1) * retryConfig.BackoffFactor)
					if delay > retryConfig.MaxDelay {
						delay = retryConfig.MaxDelay
					}
					time.Sleep(delay)
				} else {
					break
				}
			}

			opDuration := time.Since(opStart)
			totalRetries += int64(retryCount)

			// Get current connection ID for tracking
			connID := fmt.Sprintf("oracle_tracked_%d", operationCount)
			suite.stats.RecordOperation(op.name, opDuration, retryCount, nil, connID)

			// Occasionally force connection drops to test reconnection
			if operationCount%20 == 0 && operationCount > 0 {
				go func() {
					time.Sleep(10 * time.Millisecond)
					suite.interceptor.ForceDisconnectConnection()
				}()
			}
		}
	}

done:
	suite.stats.Finalize()
	testDuration = time.Since(startTime)

	// Validate results
	suite.T().Logf("=== Oracle Intermittent Failures Test Results ===")
	suite.T().Logf("Test Duration: %v", testDuration)
	suite.T().Logf("Total Operations: %d", operationCount)
	suite.T().Logf("Successful Operations: %d", successCount)
	suite.T().Logf("Success Rate: %.2f%%", suite.stats.GetSuccessRate())
	suite.T().Logf("Total Retries: %d", totalRetries)
	suite.T().Logf("Reconnections: %d", atomic.LoadInt64(&suite.stats.ReconnectionCount))
	suite.T().Logf("Connection Identities Seen: %d", len(suite.stats.ConnectionIdentities))
	suite.T().Logf("Average Latency: %v", suite.stats.AvgLatency)
	suite.T().Logf("P95 Latency: %v", suite.stats.P95Latency)

	// Assertions to prove Oracle reconnection behavior
	suite.Require().Greater(operationCount, 15, "Should have executed significant number of Oracle operations")
	suite.Require().Greater(successCount, operationCount/5, "Should have some successful Oracle operations despite failures")
	suite.Require().GreaterOrEqual(totalRetries, int64(0), "Should have performed retries (proving reconnection attempts)")
	suite.Require().Greater(len(suite.stats.ConnectionIdentities), 1, "Should have used multiple Oracle connections (proving reconnections)")
	suite.Require().GreaterOrEqual(atomic.LoadInt64(&suite.stats.ReconnectionCount), int64(1), "Should have performed at least one Oracle reconnection")
}

// TestOracleReconnectionIntegrationSuite runs the complete Oracle reconnection integration test suite
func TestOracleReconnectionIntegrationSuite(t *testing.T) {
	suite.Run(t, new(OracleReconnectionTestSuite))
}

// TestOracleDSNConversion tests the MySQL DSN to Oracle DSN conversion function
func TestOracleDSNConversion(t *testing.T) {
	t.Log("=== TestOracleDSNConversion ===")

	testCases := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:     "Valid MySQL DSN with database",
			input:    "tmsadmin:tmsadmin123@tcp(***********:1521)/utf8",
			expected: `user="tmsadmin" password="tmsadmin123" connectString="***********:1521"`,
		},
		{
			name:     "Valid MySQL DSN without database",
			input:    "user:pass@tcp(localhost:1521)/",
			expected: `user="user" password="pass" connectString="localhost:1521"`,
		},
		{
			name:        "Invalid DSN - no @",
			input:       "user:pass",
			expectError: true,
		},
		{
			name:        "Invalid DSN - no password",
			input:       "user@tcp(host:1521)/db",
			expectError: true,
		},
		{
			name:        "Invalid DSN - not tcp protocol",
			input:       "user:pass@unix(/socket)/db",
			expectError: true,
		},
		{
			name:        "Empty DSN",
			input:       "",
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := convertMySQLDSNToOracle(tc.input)

			if tc.expectError {
				if err == nil {
					t.Errorf("Expected error for input %q, but got none", tc.input)
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error for input %q: %v", tc.input, err)
				return
			}

			if result != tc.expected {
				t.Errorf("For input %q:\nExpected: %s\nGot:      %s", tc.input, tc.expected, result)
			}

			t.Logf("Successfully converted %q to %q", tc.input, result)
		})
	}
}

// TestOracleConnectionWithProvidedDSN tests Oracle connection using the provided DSN
func TestOracleConnectionWithProvidedDSN(t *testing.T) {
	t.Log("=== TestOracleConnectionWithProvidedDSN ===")

	// Test DSN provided by user
	mysqlStyleDSN := "tmsadmin:tmsadmin123@tcp(***********:1521)/utf8"

	// Convert MySQL-style DSN to Oracle format
	oracleDSN, err := convertMySQLDSNToOracle(mysqlStyleDSN)
	if err != nil {
		t.Fatalf("Failed to convert MySQL DSN to Oracle format: %v", err)
	}

	t.Logf("Converted DSN: %s", oracleDSN)

	// Parse Oracle DSN
	params, err := ParseGoDrorDSN(oracleDSN)
	if err != nil {
		t.Fatalf("Failed to parse Oracle DSN: %v", err)
	}

	// Create Oracle connector
	connector := NewGoDrorConnector(params)

	// Test Oracle connection with retry configuration
	retryConfig := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      5 * time.Second,
		BackoffFactor: 2.0,
	}

	db, err := OpenOracle(connector, WithRetry(retryConfig), WithPing())
	if err != nil {
		// Oracle client library not available in this environment - this is expected
		t.Skipf("Oracle client library not available: %v", err)
		return
	}
	defer db.Close()

	// Test basic operations
	t.Run("Basic Ping", func(t *testing.T) {
		err := db.Ping()
		if err != nil {
			t.Fatalf("Oracle ping failed: %v", err)
		}
		t.Log("Oracle ping successful")
	})

	t.Run("Basic Query", func(t *testing.T) {
		var result int
		err := db.QueryRow("SELECT 1 FROM DUAL").Scan(&result)
		if err != nil {
			t.Fatalf("Oracle query failed: %v", err)
		}
		if result != 1 {
			t.Fatalf("Expected result 1, got %d", result)
		}
		t.Log("Oracle query successful")
	})

	t.Run("Connection Pool Test", func(t *testing.T) {
		// Test multiple concurrent connections
		var wg sync.WaitGroup
		errors := make(chan error, 10)

		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				var result int
				err := db.QueryRow("SELECT ? FROM DUAL", id).Scan(&result)
				if err != nil {
					errors <- fmt.Errorf("connection %d failed: %w", id, err)
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		for err := range errors {
			t.Errorf("Concurrent connection error: %v", err)
		}

		t.Log("Oracle connection pool test successful")
	})
}

// TestDatabaseConnectionInterceptorBasic is a standalone test for basic interceptor functionality
func TestDatabaseConnectionInterceptorBasic(t *testing.T) {
	t.Log("=== TestDatabaseConnectionInterceptorBasic ===")

	// Create basic test infrastructure
	networkSim := testutils.NewNetworkSimulator()
	networkSim.Start()
	defer networkSim.Stop()

	stats := NewReconnectionStats(networkSim)

	// Create a mock MySQL server
	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	originalConn := testutils.NewMockMySQLConnector(mockServer)
	interceptor := NewDatabaseConnectionInterceptor(originalConn, networkSim, stats)

	t.Log("Testing basic connection establishment...")

	// Test basic connection
	ctx := context.Background()
	conn, err := interceptor.Connect(ctx)
	if err != nil {
		t.Logf("Basic connection failed (expected with mock): %v", err)
	} else {
		t.Log("Basic connection succeeded")
		conn.Close()
	}

	// Test fault injection
	t.Log("Testing fault injection...")
	interceptor.SetFaultInjectionRate(1.0) // 100% fault injection
	networkSim.SetCondition(testutils.NetworkOffline)

	conn, err = interceptor.Connect(ctx)
	if err != nil {
		t.Logf("Connection failed as expected with fault injection: %v", err)
	} else {
		t.Log("Connection succeeded despite fault injection")
		conn.Close()
	}

	// Test connection tracking
	t.Log("Testing connection tracking...")
	interceptor.SetFaultInjectionRate(0.0) // Disable fault injection
	networkSim.SetCondition(testutils.NetworkHealthy)

	// Attempt multiple connections
	var connections []driver.Conn
	for i := 0; i < 3; i++ {
		conn, err := interceptor.Connect(ctx)
		if err != nil {
			t.Logf("Connection %d failed: %v", i+1, err)
		} else {
			t.Logf("Connection %d succeeded", i+1)
			connections = append(connections, conn)
		}
	}

	// Clean up connections
	for i, conn := range connections {
		if conn != nil {
			err := conn.Close()
			t.Logf("Closed connection %d: %v", i+1, err)
		}
	}

	stats.Finalize()

	// Report results
	t.Logf("=== Basic Interceptor Test Results ===")
	t.Logf("Total Operations: %d", atomic.LoadInt64(&stats.TotalOperations))
	t.Logf("Connection Identities: %d", len(stats.ConnectionIdentities))
	t.Logf("Active Connections: %d", interceptor.GetActiveConnectionCount())
	t.Logf("Success Rate: %.2f%%", stats.GetSuccessRate())

	// Basic assertions
	if atomic.LoadInt64(&stats.TotalOperations) > 0 {
		t.Log("✓ Operations were recorded")
	} else {
		t.Log("✗ No operations were recorded")
	}

	if len(stats.ConnectionIdentities) > 0 {
		t.Log("✓ Connection identities were tracked")
	} else {
		t.Log("✗ No connection identities were tracked")
	}

	t.Log("Basic interceptor test completed successfully")
}

// Additional helper test for demonstrating connection tracking
func (suite *DatabaseReconnectionTestSuite) TestConnectionIdentityTracking() {
	suite.T().Log("=== TestConnectionIdentityTracking ===")

	// This test specifically validates that we can track different connection objects
	// to prove that reconnection actually occurred (not just retry success)

	suite.interceptor.SetConnectionDropRate(0.8) // High drop rate to force reconnections

	retryConfig := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  50 * time.Millisecond,
		MaxDelay:      500 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	db, err := OpenMySQL(suite.interceptor, WithRetry(retryConfig))
	if err != nil {
		suite.T().Skipf("Could not establish initial connection: %v", err)
		return
	}
	defer db.Close()

	retryDB := NewRetryDB(db, retryConfig)

	// Track connection objects over multiple operations
	connectionMap := make(map[string]int)
	totalAttempts := 20

	suite.T().Logf("Performing %d operations with high connection drop rate", totalAttempts)

	for i := 0; i < totalAttempts; i++ {
		start := time.Now()

		// Attempt ping operation
		err := retryDB.Ping()
		duration := time.Since(start)

		// Generate connection ID based on successful connection
		connID := fmt.Sprintf("tracking_conn_%d_%v", i, err == nil)
		connectionMap[connID]++

		// Record the operation
		retryCount := 0
		if duration > retryConfig.InitialDelay {
			// Estimate retry count based on duration (rough approximation)
			retryCount = int(duration / retryConfig.InitialDelay)
		}

		suite.stats.RecordOperation("ping", duration, retryCount, err, connID)

		suite.T().Logf("Operation %d: %v (duration: %v, retries: ~%d)", i+1, err == nil, duration, retryCount)

		time.Sleep(100 * time.Millisecond)
	}

	suite.stats.Finalize()

	suite.T().Logf("=== Connection Identity Tracking Results ===")
	suite.T().Logf("Unique Connection IDs: %d", len(connectionMap))
	suite.T().Logf("Connection Identities Tracked: %d", len(suite.stats.ConnectionIdentities))
	suite.T().Logf("Recorded Reconnections: %d", atomic.LoadInt64(&suite.stats.ReconnectionCount))
	suite.T().Logf("Success Rate: %.2f%%", suite.stats.GetSuccessRate())

	for connID, count := range connectionMap {
		suite.T().Logf("  %s: %d operations", connID, count)
	}

	// Assertions to prove connection tracking works
	suite.Require().Greater(len(connectionMap), 5, "Should have tracked multiple connection IDs")
	suite.Require().Greater(len(suite.stats.ConnectionIdentities), 2, "Should have multiple connection identities")

	// With high drop rate, we should see evidence of reconnection attempts
	totalRetries := int64(0)
	for retryCount, occurrences := range suite.stats.RetryDistribution {
		totalRetries += int64(retryCount) * occurrences
	}
	suite.Require().Greater(totalRetries, int64(5), "Should have performed substantial retries")
}
