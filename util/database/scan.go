package database

import (
	"database/sql"
	"strconv"
	"strings"

	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/pingcap/errors"
)

// ColumnData saves column's data and null status
type ColumnData struct {
	Data   []byte
	IsNull bool
}

// String returns the string representation of column data
func (cd *ColumnData) String() string {
	if cd.IsNull {
		return ""
	}
	return string(cd.Data)
}

// Int64 converts column data to int64
func (cd *ColumnData) Int64() (int64, error) {
	if cd.IsNull {
		return 0, nil
	}
	return strconv.ParseInt(string(cd.Data), 10, 64)
}

// Float64 converts column data to float64
func (cd *ColumnData) Float64() (float64, error) {
	if cd.IsNull {
		return 0, nil
	}
	return strconv.ParseFloat(string(cd.Data), 64)
}

// ScanRow scans a database row into a map with column names as keys
// This function handles various database types including Oracle-specific types
func ScanRow(rows *sql.Rows) (map[string]*ColumnData, error) {
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	// Get column types to handle special cases
	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, errors.Trace(err)
	}

	specialColumnResult := ExtractSpecialColumnTypes(columnTypes)

	// Prepare containers for scanning
	colVals := make([][]byte, len(cols))
	anyVals := make([]any, len(cols))
	colValsI := make([]any, len(colVals))

	// Set up scan targets based on column types
	for i := range colValsI {
		if specialColumnResult.IsGoDrorJSON(i) {
			colValsI[i] = &anyVals[i]
		} else {
			colValsI[i] = &colVals[i]
		}
	}

	// Scan the row
	err = rows.Scan(colValsI...)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// Build result map
	result := make(map[string]*ColumnData)
	for i := range colVals {
		var data *ColumnData
		if specialColumnResult.IsGoDrorJSON(i) {
			byteData, strErr := stringutil.GetStringFromAny(anyVals[i])
			if strErr != nil {
				return nil, errors.Trace(strErr)
			}
			data = &ColumnData{
				Data:   []byte(byteData),
				IsNull: anyVals[i] == nil,
			}
		} else {
			data = &ColumnData{
				Data:   colVals[i],
				IsNull: colVals[i] == nil,
			}
		}
		result[cols[i]] = data
	}

	return result, nil
}

// ScanRowWithUppercaseKeys scans a database row into a map with uppercase column names as keys
// This is useful for Oracle databases which return uppercase column names
func ScanRowWithUppercaseKeys(rows *sql.Rows) (map[string]*ColumnData, error) {
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	// Get column types to handle special cases
	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, errors.Trace(err)
	}

	specialColumnResult := ExtractSpecialColumnTypes(columnTypes)

	// Prepare containers for scanning
	colVals := make([][]byte, len(cols))
	anyVals := make([]any, len(cols))
	colValsI := make([]any, len(colVals))

	// Set up scan targets based on column types
	for i := range colValsI {
		if specialColumnResult.IsGoDrorJSON(i) {
			colValsI[i] = &anyVals[i]
		} else {
			colValsI[i] = &colVals[i]
		}
	}

	// Scan the row
	err = rows.Scan(colValsI...)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// Build result map with uppercase keys
	result := make(map[string]*ColumnData)
	for i := range colVals {
		var data *ColumnData
		if specialColumnResult.IsGoDrorJSON(i) {
			byteData, strErr := stringutil.GetStringFromAny(anyVals[i])
			if strErr != nil {
				return nil, errors.Trace(strErr)
			}
			data = &ColumnData{
				Data:   []byte(byteData),
				IsNull: anyVals[i] == nil,
			}
		} else {
			data = &ColumnData{
				Data:   colVals[i],
				IsNull: colVals[i] == nil,
			}
		}
		// Oracle returns rows by uppercase column names, but TiDB/MySQL uses lowercase
		// so we need unified format
		result[strings.ToUpper(cols[i])] = data
	}

	return result, nil
}

// ScanRowsToInterfaces scans multiple rows to interface arrays
// This is useful when you need to process multiple rows at once
func ScanRowsToInterfaces(rows *sql.Rows) ([][]any, error) {
	var rowsData [][]any
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	for rows.Next() {
		colVals := make([]any, len(cols))

		err = rows.Scan(colVals...)
		if err != nil {
			return nil, errors.Trace(err)
		}
		rowsData = append(rowsData, colVals)
	}

	return rowsData, nil
}

// ScanRowToStringMap scans a database row into a map[string]string
// This is a convenience function for simple string-only scanning
func ScanRowToStringMap(rows *sql.Rows) (map[string]string, error) {
	cols, err := rows.Columns()
	if err != nil {
		return nil, errors.Trace(err)
	}

	// Use sql.NullString for proper null handling
	colVals := make([]*sql.NullString, len(cols))
	colValsI := make([]any, len(colVals))
	for i := range colValsI {
		colVals[i] = &sql.NullString{}
		colValsI[i] = colVals[i]
	}

	// Scan the row
	err = rows.Scan(colValsI...)
	if err != nil {
		return nil, errors.Trace(err)
	}

	// Build result map
	result := make(map[string]string)
	for i, col := range cols {
		if colVals[i].Valid {
			result[col] = colVals[i].String
		} else {
			result[col] = ""
		}
	}

	return result, nil
}
