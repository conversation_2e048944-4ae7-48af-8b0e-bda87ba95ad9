package database

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"gitee.com/pingcap_enterprise/tms/util/database/testutils"
	"github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/require"
)

// BenchmarkRetryLogic benchmarks the retry logic performance
func BenchmarkRetryLogic(b *testing.B) {
	b.Run("IsRetryableError", func(b *testing.B) {
		b.Run("OracleError", func(b *testing.B) {
			err := fmt.Errorf("ORA-03113: end-of-file on communication channel")
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				IsRetryableError(err)
			}
		})

		b.Run("MySQLError", func(b *testing.B) {
			err := &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				IsRetryableError(err)
			}
		})

		b.<PERSON>("NetworkError", func(b *testing.B) {
			err := fmt.Errorf("connection refused")
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				IsRetryableError(err)
			}
		})

		b.Run("NonRetryableError", func(b *testing.B) {
			err := fmt.Errorf("syntax error")
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				IsRetryableError(err)
			}
		})
	})

	b.Run("ExecuteWithRetry", func(b *testing.B) {
		config := DefaultRetryConfig()
		ctx := context.Background()

		b.Run("SuccessfulOperation", func(b *testing.B) {
			fn := func() error { return nil }
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				ExecuteWithRetry(ctx, config, fn)
			}
		})

		b.Run("NonRetryableError", func(b *testing.B) {
			fn := func() error { return fmt.Errorf("syntax error") }
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				ExecuteWithRetry(ctx, config, fn)
			}
		})

		b.Run("RetryableErrorThenSuccess", func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				callCount := 0
				fn := func() error {
					callCount++
					if callCount < 2 {
						return &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
					}
					return nil
				}
				ExecuteWithRetry(ctx, config, fn)
			}
		})
	})
}

// BenchmarkConnectionPooling benchmarks connection pool performance
func BenchmarkConnectionPooling(b *testing.B) {
	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	connector := testutils.NewMockMySQLConnector(mockServer)

	b.Run("OpenMySQL", func(b *testing.B) {
		b.Run("WithoutRetry", func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				db, err := OpenMySQL(connector)
				if err == nil {
					db.Close()
				}
			}
		})

		b.Run("WithDefaultRetry", func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				db, err := OpenMySQL(connector, WithDefaultRetry())
				if err == nil {
					db.Close()
				}
			}
		})

		b.Run("WithPing", func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				db, err := OpenMySQL(connector, WithPing())
				if err == nil {
					db.Close()
				}
			}
		})

		b.Run("WithPoolOptions", func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				db, err := OpenMySQL(connector,
					WithMaxIdleConns(10),
					WithMaxOpenConns(20),
					WithConnMaxLifetime(30*time.Minute),
				)
				if err == nil {
					db.Close()
				}
			}
		})
	})

	b.Run("ConnectionPoolStress", func(b *testing.B) {
		db, err := OpenMySQL(connector,
			WithMaxIdleConns(5),
			WithMaxOpenConns(10),
			WithConnMaxLifetime(30*time.Second),
			WithDefaultRetry(),
		)
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, DefaultRetryConfig())

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				retryDB.Ping()
			}
		})
	})
}

// BenchmarkRetryDBOperations benchmarks RetryDB wrapper operations
func BenchmarkRetryDBOperations(b *testing.B) {
	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	connector := testutils.NewMockMySQLConnector(mockServer)
	db, err := OpenMySQL(connector, WithDefaultRetry())
	require.NoError(b, err)
	defer db.Close()

	retryConfigs := map[string]*RetryConfig{
		"minimal": {
			MaxRetries:    1,
			InitialDelay:  1 * time.Millisecond,
			MaxDelay:      10 * time.Millisecond,
			BackoffFactor: 1.5,
		},
		"default": DefaultRetryConfig(),
		"aggressive": {
			MaxRetries:    10,
			InitialDelay:  10 * time.Millisecond,
			MaxDelay:      1 * time.Second,
			BackoffFactor: 2.0,
		},
	}

	for configName, config := range retryConfigs {
		retryDB := NewRetryDB(db, config)

		b.Run(fmt.Sprintf("Ping_%s", configName), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				retryDB.Ping()
			}
		})

		b.Run(fmt.Sprintf("PingContext_%s", configName), func(b *testing.B) {
			ctx := context.Background()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				retryDB.PingContext(ctx)
			}
		})

		b.Run(fmt.Sprintf("Query_%s", configName), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				rows, err := retryDB.Query("SELECT 1")
				if err == nil {
					rows.Close()
				}
			}
		})

		b.Run(fmt.Sprintf("QueryContext_%s", configName), func(b *testing.B) {
			ctx := context.Background()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				rows, err := retryDB.QueryContext(ctx, "SELECT 1")
				if err == nil {
					rows.Close()
				}
			}
		})

		b.Run(fmt.Sprintf("Exec_%s", configName), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				retryDB.Exec("SELECT 1")
			}
		})

		b.Run(fmt.Sprintf("ExecContext_%s", configName), func(b *testing.B) {
			ctx := context.Background()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				retryDB.ExecContext(ctx, "SELECT 1")
			}
		})
	}
}

// BenchmarkRetryDBWithErrors benchmarks RetryDB operations under error conditions
func BenchmarkRetryDBWithErrors(b *testing.B) {
	errorRates := []float64{0.1, 0.3, 0.5}

	for _, errorRate := range errorRates {
		b.Run(fmt.Sprintf("ErrorRate_%.0f%%", errorRate*100), func(b *testing.B) {
			mockServer := testutils.NewMockMySQLServer()
			mockServer.Start()
			defer mockServer.Stop()

			mockServer.SetErrorRate(errorRate)
			mockServer.EnableConnectionErrors(true)

			connector := testutils.NewMockMySQLConnector(mockServer)
			db, err := OpenMySQL(connector, WithDefaultRetry())
			require.NoError(b, err)
			defer db.Close()

			retryDB := NewRetryDB(db, &RetryConfig{
				MaxRetries:    5,
				InitialDelay:  1 * time.Millisecond,
				MaxDelay:      50 * time.Millisecond,
				BackoffFactor: 2.0,
			})

			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				retryDB.Ping()
			}
		})
	}
}

// BenchmarkConcurrentOperations benchmarks concurrent database operations
func BenchmarkConcurrentOperations(b *testing.B) {
	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	connector := testutils.NewMockMySQLConnector(mockServer)
	db, err := OpenMySQL(connector,
		WithMaxIdleConns(10),
		WithMaxOpenConns(20),
		WithDefaultRetry(),
	)
	require.NoError(b, err)
	defer db.Close()

	retryDB := NewRetryDB(db, DefaultRetryConfig())

	b.Run("ConcurrentPing", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				retryDB.Ping()
			}
		})
	})

	b.Run("ConcurrentQuery", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				rows, err := retryDB.Query("SELECT 1")
				if err == nil {
					rows.Close()
				}
			}
		})
	})

	b.Run("ConcurrentMixedOperations", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				switch b.N % 3 {
				case 0:
					retryDB.Ping()
				case 1:
					rows, err := retryDB.Query("SELECT 1")
					if err == nil {
						rows.Close()
					}
				case 2:
					retryDB.Exec("SELECT 1")
				}
			}
		})
	})
}

// BenchmarkNetworkSimulation benchmarks network simulation overhead
func BenchmarkNetworkSimulation(b *testing.B) {
	networkSim := testutils.NewNetworkSimulator()
	networkSim.Start()
	defer networkSim.Stop()

	ctx := context.Background()

	b.Run("HealthyNetwork", func(b *testing.B) {
		networkSim.SetCondition(testutils.NetworkHealthy)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			networkSim.SimulateOperation(ctx, "test-host", "ping")
		}
	})

	b.Run("SlowNetwork", func(b *testing.B) {
		networkSim.SetCondition(testutils.NetworkSlow)
		networkSim.SetLatency(10*time.Millisecond, 50*time.Millisecond)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			networkSim.SimulateOperation(ctx, "test-host", "ping")
		}
	})

	b.Run("UnstableNetwork", func(b *testing.B) {
		networkSim.SetCondition(testutils.NetworkUnstable)
		networkSim.SetPacketLoss(0.1)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			networkSim.SimulateOperation(ctx, "test-host", "ping")
		}
	})
}

// BenchmarkChaosEngineering benchmarks chaos engineering overhead
func BenchmarkChaosEngineering(b *testing.B) {
	chaosEngine := testutils.NewChaosEngine()
	chaosEngine.Start()
	defer chaosEngine.Stop()

	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	chaosEngine.SetMySQLServer(mockServer)

	experiment := &testutils.ChaosExperiment{
		ID:          "benchmark_test",
		Name:        "Benchmark Test",
		Description: "Testing chaos engine overhead",
		Duration:    1 * time.Second,
		FailureRate: 0.1,
		Enabled:     true,
		TargetType:  "mysql",
		Conditions: []testutils.ChaosCondition{
			{
				Type:        "error_injection",
				Probability: 0.3,
				Parameters: map[string]interface{}{
					"error_type": "connection",
				},
				Active: true,
			},
		},
	}

	b.Run("ExperimentManagement", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			exp := *experiment
			exp.ID = fmt.Sprintf("test_%d", i)
			chaosEngine.AddExperiment(&exp)
			chaosEngine.RemoveExperiment(exp.ID)
		}
	})

	b.Run("EventLogging", func(b *testing.B) {
		chaosEngine.AddExperiment(experiment)
		defer chaosEngine.RemoveExperiment(experiment.ID)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = chaosEngine.GetEventLog()
		}
	})
}

// BenchmarkMemoryUsage benchmarks memory usage under various conditions
func BenchmarkMemoryUsage(b *testing.B) {
	b.Run("RetryConfigCreation", func(b *testing.B) {
		var ms1, ms2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&ms1)

		b.ResetTimer()
		configs := make([]*RetryConfig, b.N)
		for i := 0; i < b.N; i++ {
			configs[i] = DefaultRetryConfig()
		}
		b.StopTimer()

		runtime.GC()
		runtime.ReadMemStats(&ms2)

		allocatedBytes := ms2.TotalAlloc - ms1.TotalAlloc
		b.ReportMetric(float64(allocatedBytes)/float64(b.N), "bytes/config")

		// Keep reference to prevent optimization
		_ = configs
	})

	b.Run("MockServerCreation", func(b *testing.B) {
		var ms1, ms2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&ms1)

		b.ResetTimer()
		servers := make([]*testutils.MockMySQLServer, b.N)
		for i := 0; i < b.N; i++ {
			servers[i] = testutils.NewMockMySQLServer()
		}
		b.StopTimer()

		runtime.GC()
		runtime.ReadMemStats(&ms2)

		allocatedBytes := ms2.TotalAlloc - ms1.TotalAlloc
		b.ReportMetric(float64(allocatedBytes)/float64(b.N), "bytes/server")

		// Keep reference to prevent optimization
		_ = servers
	})

	b.Run("NetworkSimulatorEvents", func(b *testing.B) {
		networkSim := testutils.NewNetworkSimulator()
		networkSim.Start()
		defer networkSim.Stop()

		ctx := context.Background()

		var ms1, ms2 runtime.MemStats
		runtime.GC()
		runtime.ReadMemStats(&ms1)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			networkSim.SimulateOperation(ctx, "test-host", fmt.Sprintf("operation_%d", i))
		}
		b.StopTimer()

		runtime.GC()
		runtime.ReadMemStats(&ms2)

		allocatedBytes := ms2.TotalAlloc - ms1.TotalAlloc
		b.ReportMetric(float64(allocatedBytes)/float64(b.N), "bytes/event")

		events := networkSim.GetEvents()
		b.ReportMetric(float64(len(events)), "events_recorded")
	})
}

// BenchmarkScalability benchmarks scalability under load
func BenchmarkScalability(b *testing.B) {
	connectionCounts := []int{1, 5, 10, 20, 50}

	for _, connCount := range connectionCounts {
		b.Run(fmt.Sprintf("Connections_%d", connCount), func(b *testing.B) {
			mockServer := testutils.NewMockMySQLServer()
			mockServer.SetMaxConnections(connCount * 2) // Allow some headroom
			mockServer.Start()
			defer mockServer.Stop()

			connector := testutils.NewMockMySQLConnector(mockServer)

			// Create multiple database connections
			dbs := make([]*RetryDB, connCount)
			for i := 0; i < connCount; i++ {
				db, err := OpenMySQL(connector, WithDefaultRetry())
				require.NoError(b, err)
				defer db.Close()

				dbs[i] = NewRetryDB(db, DefaultRetryConfig())
			}

			b.ResetTimer()

			// Benchmark operations across all connections
			var wg sync.WaitGroup
			for i := 0; i < b.N; i++ {
				wg.Add(1)
				go func(dbIndex int) {
					defer wg.Done()
					db := dbs[dbIndex%connCount]
					db.Ping()
				}(i)
			}
			wg.Wait()
		})
	}
}

// BenchmarkErrorRecoveryTime benchmarks time to recover from errors
func BenchmarkErrorRecoveryTime(b *testing.B) {
	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	connector := testutils.NewMockMySQLConnector(mockServer)
	db, err := OpenMySQL(connector, WithDefaultRetry())
	require.NoError(b, err)
	defer db.Close()

	retryDB := NewRetryDB(db, &RetryConfig{
		MaxRetries:    5,
		InitialDelay:  10 * time.Millisecond,
		MaxDelay:      200 * time.Millisecond,
		BackoffFactor: 2.0,
	})

	b.Run("RecoveryFromConnectionError", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Simulate connection failure followed by recovery
			mockServer.SetErrorRate(1.0) // 100% error rate
			mockServer.EnableConnectionErrors(true)

			start := time.Now()

			// First operation will fail
			retryDB.Ping()

			// Restore service
			mockServer.SetErrorRate(0.0)
			mockServer.EnableConnectionErrors(false)

			// Second operation should succeed (after retries)
			err := retryDB.Ping()

			recoveryTime := time.Since(start)

			if err == nil {
				b.ReportMetric(float64(recoveryTime.Milliseconds()), "recovery_time_ms")
			}
		}
	})

	b.Run("RecoveryFromDeadlock", func(b *testing.B) {
		mockServer.EnableLockTimeouts(true)

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			start := time.Now()

			// This should trigger lock timeout and retry
			retryDB.Exec("UPDATE test_table SET value = ? WHERE id = ?", "test", 1)

			recoveryTime := time.Since(start)
			b.ReportMetric(float64(recoveryTime.Milliseconds()), "deadlock_recovery_ms")
		}
	})
}

// BenchmarkConfigurationOverhead benchmarks configuration parsing overhead
func BenchmarkConfigurationOverhead(b *testing.B) {
	b.Run("DefaultRetryConfig", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = DefaultRetryConfig()
		}
	})

	b.Run("CustomRetryConfig", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = &RetryConfig{
				MaxRetries:    10,
				InitialDelay:  50 * time.Millisecond,
				MaxDelay:      5 * time.Second,
				BackoffFactor: 2.5,
			}
		}
	})

	b.Run("MySQLConfigValidation", func(b *testing.B) {
		config := MySQLConfig{
			User:     "testuser",
			Password: "testpass",
			Host:     "localhost",
			Port:     3306,
			Database: "testdb",
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			config.Validate()
		}
	})

	b.Run("OptionApplication", func(b *testing.B) {
		options := []Option{
			WithMaxIdleConns(10),
			WithMaxOpenConns(20),
			WithConnMaxLifetime(30 * time.Minute),
			WithDefaultRetry(),
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			applyOptions(options...)
		}
	})
}

// BenchmarkOracleVsMySQL compares Oracle and MySQL mock server performance
func BenchmarkOracleVsMySQL(b *testing.B) {
	b.Run("Oracle", func(b *testing.B) {
		oracleServer := testutils.NewMockOracleServer()
		oracleServer.Start()
		defer oracleServer.Stop()

		connector := testutils.NewMockOracleConnector(oracleServer)
		db, err := OpenOracle(connector, WithDefaultRetry())
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, DefaultRetryConfig())

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			retryDB.Ping()
		}
	})

	b.Run("MySQL", func(b *testing.B) {
		mysqlServer := testutils.NewMockMySQLServer()
		mysqlServer.Start()
		defer mysqlServer.Stop()

		connector := testutils.NewMockMySQLConnector(mysqlServer)
		db, err := OpenMySQL(connector, WithDefaultRetry())
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, DefaultRetryConfig())

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			retryDB.Ping()
		}
	})
}

// BenchmarkComplexScenarios benchmarks complex real-world scenarios
func BenchmarkComplexScenarios(b *testing.B) {
	b.Run("DatabaseWithChaosEngineering", func(b *testing.B) {
		// Set up chaos engineering environment
		chaosEngine := testutils.NewChaosEngine()
		networkSim := testutils.NewNetworkSimulator()
		mockServer := testutils.NewMockMySQLServer()

		chaosEngine.SetNetworkSimulator(networkSim)
		chaosEngine.SetMySQLServer(mockServer)

		chaosEngine.Start()
		networkSim.Start()
		mockServer.Start()

		defer func() {
			chaosEngine.Stop()
			networkSim.Stop()
			mockServer.Stop()
		}()

		// Add mild chaos
		experiment := testutils.CreateDatabaseConnectionChaos()
		experiment.Duration = time.Duration(b.N) * time.Millisecond
		experiment.FailureRate = 0.1 // 10% failure rate

		chaosEngine.AddExperiment(experiment)
		chaosEngine.StartExperiment(experiment.ID)

		// Set up database connection
		connector := testutils.NewMockMySQLConnector(mockServer)
		db, err := OpenMySQL(connector, WithDefaultRetry())
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, &RetryConfig{
			MaxRetries:    3,
			InitialDelay:  5 * time.Millisecond,
			MaxDelay:      100 * time.Millisecond,
			BackoffFactor: 2.0,
		})

		b.ResetTimer()

		// Perform operations under chaos
		for i := 0; i < b.N; i++ {
			// Mix of different operations
			switch i % 4 {
			case 0:
				retryDB.Ping()
			case 1:
				rows, err := retryDB.Query("SELECT 1")
				if err == nil {
					rows.Close()
				}
			case 2:
				retryDB.Exec("SELECT 1")
			case 3:
				tx, err := retryDB.Begin()
				if err == nil {
					tx.Rollback()
				}
			}
		}

		chaosEngine.StopExperiment(experiment.ID)
	})

	b.Run("HighThroughputWithRetries", func(b *testing.B) {
		mockServer := testutils.NewMockMySQLServer()
		mockServer.SetErrorRate(0.2) // 20% error rate to force retries
		mockServer.EnableConnectionErrors(true)
		mockServer.Start()
		defer mockServer.Stop()

		connector := testutils.NewMockMySQLConnector(mockServer)
		db, err := OpenMySQL(connector,
			WithMaxIdleConns(20),
			WithMaxOpenConns(50),
			WithDefaultRetry(),
		)
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, &RetryConfig{
			MaxRetries:    2,
			InitialDelay:  1 * time.Millisecond,
			MaxDelay:      10 * time.Millisecond,
			BackoffFactor: 2.0,
		})

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				// High-frequency operations
				retryDB.Ping()
			}
		})
	})
}

// BenchmarkMemoryProfiler provides detailed memory profiling
func BenchmarkMemoryProfiler(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping memory profiler in short mode")
	}

	b.Run("DetailedMemoryProfile", func(b *testing.B) {
		var allocsBefore, allocsAfter uint64
		var totalAllocBefore, totalAllocAfter uint64

		// Set up components
		mockServer := testutils.NewMockMySQLServer()
		mockServer.Start()
		defer mockServer.Stop()

		connector := testutils.NewMockMySQLConnector(mockServer)

		runtime.GC()
		var ms1 runtime.MemStats
		runtime.ReadMemStats(&ms1)
		allocsBefore = ms1.Mallocs
		totalAllocBefore = ms1.TotalAlloc

		b.ResetTimer()

		// Perform memory-intensive operations
		for i := 0; i < b.N; i++ {
			db, err := OpenMySQL(connector, WithDefaultRetry())
			if err == nil {
				retryDB := NewRetryDB(db, DefaultRetryConfig())
				retryDB.Ping()
				rows, err := retryDB.Query("SELECT 1")
				if err == nil {
					rows.Close()
				}
				db.Close()
			}
		}

		b.StopTimer()

		runtime.GC()
		var ms2 runtime.MemStats
		runtime.ReadMemStats(&ms2)
		allocsAfter = ms2.Mallocs
		totalAllocAfter = ms2.TotalAlloc

		// Report memory metrics
		b.ReportMetric(float64(allocsAfter-allocsBefore)/float64(b.N), "allocs/op")
		b.ReportMetric(float64(totalAllocAfter-totalAllocBefore)/float64(b.N), "bytes/op")
		b.ReportMetric(float64(ms2.HeapInuse)/1024/1024, "heap_inuse_mb")
		b.ReportMetric(float64(ms2.StackInuse)/1024/1024, "stack_inuse_mb")
	})
}
