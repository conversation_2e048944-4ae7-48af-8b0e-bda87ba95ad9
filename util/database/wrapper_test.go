package database

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test NewRetryDB creation
func TestNewRetryDB(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	t.Run("With custom config", func(t *testing.T) {
		config := &RetryConfig{
			MaxRetries:    5,
			InitialDelay:  200 * time.Millisecond,
			MaxDelay:      10 * time.Second,
			BackoffFactor: 3.0,
		}

		retryDB := NewRetryDB(db, config)

		assert.NotNil(t, retryDB)
		assert.Equal(t, db, retryDB.DB)
		assert.Equal(t, config, retryDB.retryConfig)
	})

	t.Run("With nil config (should use default)", func(t *testing.T) {
		retryDB := NewRetryDB(db, nil)

		assert.NotNil(t, retryDB)
		assert.Equal(t, db, retryDB.DB)
		assert.NotNil(t, retryDB.retryConfig)
		assert.Equal(t, 3, retryDB.retryConfig.MaxRetries)
		assert.Equal(t, 100*time.Millisecond, retryDB.retryConfig.InitialDelay)
		assert.Equal(t, 5*time.Second, retryDB.retryConfig.MaxDelay)
		assert.Equal(t, 2.0, retryDB.retryConfig.BackoffFactor)
	})

	// Ensure mock expectations are met
	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.QueryContext with success
func TestRetryDB_QueryContext_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful query
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "test1").
		AddRow(2, "test2")
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)

	ctx := context.Background()
	result, err := retryDB.QueryContext(ctx, "SELECT * FROM users")

	assert.NoError(t, err)
	assert.NotNil(t, result)
	result.Close()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.QueryContext with retryable error then success
func TestRetryDB_QueryContext_RetryableErrorThenSuccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable error followed by success
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnError(retryableError)
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnError(retryableError)

	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "test1").
		AddRow(2, "test2")
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)

	ctx := context.Background()
	result, err := retryDB.QueryContext(ctx, "SELECT * FROM users")

	assert.NoError(t, err)
	assert.NotNil(t, result)
	result.Close()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.QueryContext with non-retryable error
func TestRetryDB_QueryContext_NonRetryableError(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock non-retryable error
	nonRetryableError := errors.New("syntax error")
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnError(nonRetryableError)

	ctx := context.Background()
	result, err := retryDB.QueryContext(ctx, "SELECT * FROM users")

	assert.Error(t, err)
	assert.Equal(t, nonRetryableError, err)
	assert.Nil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.Query (without context)
func TestRetryDB_Query_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful query
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "test1").
		AddRow(2, "test2")
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)

	result, err := retryDB.Query("SELECT * FROM users")

	assert.NoError(t, err)
	assert.NotNil(t, result)
	result.Close()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.ExecContext with success
func TestRetryDB_ExecContext_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful exec
	mock.ExpectExec("INSERT INTO users").WillReturnResult(sqlmock.NewResult(1, 1))

	ctx := context.Background()
	result, err := retryDB.ExecContext(ctx, "INSERT INTO users (name) VALUES (?)", "test")

	assert.NoError(t, err)
	assert.NotNil(t, result)

	lastInsertID, err := result.LastInsertId()
	assert.NoError(t, err)
	assert.Equal(t, int64(1), lastInsertID)

	rowsAffected, err := result.RowsAffected()
	assert.NoError(t, err)
	assert.Equal(t, int64(1), rowsAffected)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.ExecContext with retryable error then success
func TestRetryDB_ExecContext_RetryableErrorThenSuccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable error followed by success
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	mock.ExpectExec("INSERT INTO users").WillReturnError(retryableError)
	mock.ExpectExec("INSERT INTO users").WillReturnResult(sqlmock.NewResult(1, 1))

	ctx := context.Background()
	result, err := retryDB.ExecContext(ctx, "INSERT INTO users (name) VALUES (?)", "test")

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.Exec (without context)
func TestRetryDB_Exec_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful exec
	mock.ExpectExec("INSERT INTO users").WillReturnResult(sqlmock.NewResult(1, 1))

	result, err := retryDB.Exec("INSERT INTO users (name) VALUES (?)", "test")

	assert.NoError(t, err)
	assert.NotNil(t, result)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.PrepareContext with success
func TestRetryDB_PrepareContext_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful prepare
	mock.ExpectPrepare("SELECT \\* FROM users WHERE id = \\?")

	ctx := context.Background()
	stmt, err := retryDB.PrepareContext(ctx, "SELECT * FROM users WHERE id = ?")

	assert.NoError(t, err)
	assert.NotNil(t, stmt)
	stmt.Close()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.PrepareContext with retryable error then success
func TestRetryDB_PrepareContext_RetryableErrorThenSuccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable error followed by success
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	mock.ExpectPrepare("SELECT \\* FROM users WHERE id = \\?").WillReturnError(retryableError)
	mock.ExpectPrepare("SELECT \\* FROM users WHERE id = \\?")

	ctx := context.Background()
	stmt, err := retryDB.PrepareContext(ctx, "SELECT * FROM users WHERE id = ?")

	assert.NoError(t, err)
	assert.NotNil(t, stmt)
	stmt.Close()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.Prepare (without context)
func TestRetryDB_Prepare_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful prepare
	mock.ExpectPrepare("SELECT \\* FROM users WHERE id = \\?")

	stmt, err := retryDB.Prepare("SELECT * FROM users WHERE id = ?")

	assert.NoError(t, err)
	assert.NotNil(t, stmt)
	stmt.Close()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.BeginTx with success
func TestRetryDB_BeginTx_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful begin transaction
	mock.ExpectBegin()

	ctx := context.Background()
	tx, err := retryDB.BeginTx(ctx, nil)

	assert.NoError(t, err)
	assert.NotNil(t, tx)

	// Mock rollback to clean up
	mock.ExpectRollback()
	tx.Rollback()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.BeginTx with retryable error then success
func TestRetryDB_BeginTx_RetryableErrorThenSuccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable error followed by success
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	mock.ExpectBegin().WillReturnError(retryableError)
	mock.ExpectBegin()

	ctx := context.Background()
	tx, err := retryDB.BeginTx(ctx, nil)

	assert.NoError(t, err)
	assert.NotNil(t, tx)

	// Mock rollback to clean up
	mock.ExpectRollback()
	tx.Rollback()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.Begin (without context)
func TestRetryDB_Begin_Success(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful begin transaction
	mock.ExpectBegin()

	tx, err := retryDB.Begin()

	assert.NoError(t, err)
	assert.NotNil(t, tx)

	// Mock rollback to clean up
	mock.ExpectRollback()
	tx.Rollback()

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.PingContext with success
func TestRetryDB_PingContext_Success(t *testing.T) {
	db, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful ping
	mock.ExpectPing()

	ctx := context.Background()
	err = retryDB.PingContext(ctx)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.PingContext with retryable error then success
func TestRetryDB_PingContext_RetryableErrorThenSuccess(t *testing.T) {
	db, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable error followed by success
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	mock.ExpectPing().WillReturnError(retryableError)
	mock.ExpectPing().WillReturnError(retryableError)
	mock.ExpectPing()

	ctx := context.Background()
	err = retryDB.PingContext(ctx)

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.Ping (without context)
func TestRetryDB_Ping_Success(t *testing.T) {
	db, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful ping
	mock.ExpectPing()

	err = retryDB.Ping()

	assert.NoError(t, err)
	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.QueryRowContext (note: QueryRow doesn't return error directly)
func TestRetryDB_QueryRowContext(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful query row
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "test1")
	mock.ExpectQuery("SELECT \\* FROM users WHERE id = \\?").WillReturnRows(rows)

	ctx := context.Background()
	row := retryDB.QueryRowContext(ctx, "SELECT * FROM users WHERE id = ?", 1)

	assert.NotNil(t, row)

	var id int
	var name string
	err = row.Scan(&id, &name)
	assert.NoError(t, err)
	assert.Equal(t, 1, id)
	assert.Equal(t, "test1", name)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test RetryDB.QueryRow (without context)
func TestRetryDB_QueryRow(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful query row
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "test1")
	mock.ExpectQuery("SELECT \\* FROM users WHERE id = \\?").WillReturnRows(rows)

	row := retryDB.QueryRow("SELECT * FROM users WHERE id = ?", 1)

	assert.NotNil(t, row)

	var id int
	var name string
	err = row.Scan(&id, &name)
	assert.NoError(t, err)
	assert.Equal(t, 1, id)
	assert.Equal(t, "test1", name)

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Test connection pool options
func TestOpenDBOption_WithOptions(t *testing.T) {
	t.Run("WithMaxIdleConns", func(t *testing.T) {
		opt := &OpenDBOption{}
		option := WithMaxIdleConns(10)
		option(opt)
		assert.Equal(t, 10, opt.MaxIdleConns)
	})

	t.Run("WithMaxOpenConns", func(t *testing.T) {
		opt := &OpenDBOption{}
		option := WithMaxOpenConns(20)
		option(opt)
		assert.Equal(t, 20, opt.MaxOpenConns)
	})

	t.Run("WithConnMaxLifetime", func(t *testing.T) {
		opt := &OpenDBOption{}
		duration := 30 * time.Minute
		option := WithConnMaxLifetime(duration)
		option(opt)
		assert.Equal(t, duration, opt.ConnMaxLifetime)
	})

	t.Run("WithPing", func(t *testing.T) {
		opt := &OpenDBOption{}
		option := WithPing()
		option(opt)
		assert.True(t, opt.Ping)
	})

	t.Run("WithPingTimeout", func(t *testing.T) {
		opt := &OpenDBOption{}
		option := WithPingTimeout(30)
		option(opt)
		assert.True(t, opt.Ping)
		assert.Equal(t, 30, opt.PingTimeout)
	})

	t.Run("WithSetModuleName", func(t *testing.T) {
		opt := &OpenDBOption{}
		option := WithSetModuleName("test-module")
		option(opt)
		assert.Equal(t, "test-module", opt.ModuleName)
	})

	t.Run("WithRetry", func(t *testing.T) {
		opt := &OpenDBOption{}
		config := &RetryConfig{
			MaxRetries:    5,
			InitialDelay:  200 * time.Millisecond,
			MaxDelay:      10 * time.Second,
			BackoffFactor: 3.0,
		}
		option := WithRetry(config)
		option(opt)
		assert.True(t, opt.EnableRetry)
		assert.Equal(t, config, opt.RetryConfig)
	})

	t.Run("WithDefaultRetry", func(t *testing.T) {
		opt := &OpenDBOption{}
		option := WithDefaultRetry()
		option(opt)
		assert.True(t, opt.EnableRetry)
		assert.NotNil(t, opt.RetryConfig)
		assert.Equal(t, 3, opt.RetryConfig.MaxRetries)
	})
}

// Test applyOptions function
func TestApplyOptions(t *testing.T) {
	opts := applyOptions(
		WithMaxIdleConns(10),
		WithMaxOpenConns(20),
		WithConnMaxLifetime(30*time.Minute),
		WithPingTimeout(15),
		WithSetModuleName("test-module"),
		WithDefaultRetry(),
	)

	assert.Equal(t, 10, opts.MaxIdleConns)
	assert.Equal(t, 20, opts.MaxOpenConns)
	assert.Equal(t, 30*time.Minute, opts.ConnMaxLifetime)
	assert.True(t, opts.Ping)
	assert.Equal(t, 15, opts.PingTimeout)
	assert.Equal(t, "test-module", opts.ModuleName)
	assert.True(t, opts.EnableRetry)
	assert.NotNil(t, opts.RetryConfig)
}

// Test MySQLConfig validation
func TestMySQLConfig_Validate(t *testing.T) {
	t.Run("Valid config", func(t *testing.T) {
		config := MySQLConfig{
			User:     "testuser",
			Password: "testpass",
			Host:     "localhost",
			Port:     3306,
			Database: "testdb",
		}

		err := config.Validate()
		assert.NoError(t, err)
	})

	t.Run("Missing username", func(t *testing.T) {
		config := MySQLConfig{
			Password: "testpass",
			Host:     "localhost",
			Port:     3306,
			Database: "testdb",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "username is required")
	})

	t.Run("Missing password", func(t *testing.T) {
		config := MySQLConfig{
			User:     "testuser",
			Host:     "localhost",
			Port:     3306,
			Database: "testdb",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "password is required")
	})

	t.Run("Missing host", func(t *testing.T) {
		config := MySQLConfig{
			User:     "testuser",
			Password: "testpass",
			Port:     3306,
			Database: "testdb",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "host is required")
	})

	t.Run("Invalid port", func(t *testing.T) {
		config := MySQLConfig{
			User:     "testuser",
			Password: "testpass",
			Host:     "localhost",
			Port:     0,
			Database: "testdb",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "valid port is required")
	})

	t.Run("Negative port", func(t *testing.T) {
		config := MySQLConfig{
			User:     "testuser",
			Password: "testpass",
			Host:     "localhost",
			Port:     -1,
			Database: "testdb",
		}

		err := config.Validate()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "valid port is required")
	})
}

// Test MySQLConnector creation
func TestNewMySQLConnector(t *testing.T) {
	config := MySQLConfig{
		User:     "testuser",
		Password: "testpass",
		Host:     "localhost",
		Port:     3306,
		Database: "testdb",
		Charset:  "utf8mb4",
		Timezone: time.UTC,
	}

	connector := NewMySQLConnector(config)
	assert.NotNil(t, connector)

	mysqlConnector, ok := connector.(*MySQLConnector)
	assert.True(t, ok)
	assert.Equal(t, config, mysqlConnector.config)
}

// Test MySQLConnector Driver method
func TestMySQLConnector_Driver(t *testing.T) {
	config := MySQLConfig{
		User:     "testuser",
		Password: "testpass",
		Host:     "localhost",
		Port:     3306,
		Database: "testdb",
	}

	connector := &MySQLConnector{config: config}
	driver := connector.Driver()

	assert.NotNil(t, driver)
	_, ok := driver.(*mysql.MySQLDriver)
	assert.True(t, ok)
}

// Mock connector for testing OpenMySQL and OpenOracle
type mockConnector struct {
	shouldFailPing    bool
	shouldFailConnect bool
}

func (m *mockConnector) Connect(ctx context.Context) (driver.Conn, error) {
	if m.shouldFailConnect {
		return nil, errors.New("connection failed")
	}
	return &mockConn{shouldFailPing: m.shouldFailPing}, nil
}

func (m *mockConnector) Driver() driver.Driver {
	return &mockDriver{}
}

type mockDriver struct{}

func (m *mockDriver) Open(name string) (driver.Conn, error) {
	return &mockConn{}, nil
}

type mockConn struct {
	shouldFailPing bool
}

func (m *mockConn) Prepare(query string) (driver.Stmt, error) {
	return nil, nil
}

func (m *mockConn) Close() error {
	return nil
}

func (m *mockConn) Begin() (driver.Tx, error) {
	return nil, nil
}

func (m *mockConn) Ping(ctx context.Context) error {
	if m.shouldFailPing {
		return errors.New("ping failed")
	}
	return nil
}

// Test OpenMySQL with various configurations
func TestOpenMySQL(t *testing.T) {
	t.Run("Successful connection without ping", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenMySQL(connector)

		assert.NoError(t, err)
		assert.NotNil(t, db)
		db.Close()
	})

	t.Run("Successful connection with ping", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenMySQL(connector, WithPing())

		assert.NoError(t, err)
		assert.NotNil(t, db)
		db.Close()
	})

	t.Run("Successful connection with ping timeout", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenMySQL(connector, WithPingTimeout(5))

		assert.NoError(t, err)
		assert.NotNil(t, db)
		db.Close()
	})

	t.Run("Connection with ping failure", func(t *testing.T) {
		connector := &mockConnector{shouldFailPing: true}

		db, err := OpenMySQL(connector, WithPing())

		assert.Error(t, err)
		assert.Nil(t, db)
	})

	t.Run("Connection with retry on ping failure", func(t *testing.T) {
		connector := &mockConnector{shouldFailPing: true}

		retryConfig := &RetryConfig{
			MaxRetries:    1,
			InitialDelay:  1 * time.Millisecond,
			MaxDelay:      10 * time.Millisecond,
			BackoffFactor: 2.0,
		}

		db, err := OpenMySQL(connector, WithPing(), WithRetry(retryConfig))

		assert.Error(t, err)
		assert.Nil(t, db)
	})

	t.Run("Connection with pool options", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenMySQL(connector,
			WithMaxIdleConns(10),
			WithMaxOpenConns(20),
			WithConnMaxLifetime(30*time.Minute),
		)

		assert.NoError(t, err)
		assert.NotNil(t, db)

		// Verify pool settings (these are internal to sql.DB, but we can check they don't error)
		assert.NoError(t, db.Ping())

		db.Close()
	})
}

// Test OpenOracle with various configurations
func TestOpenOracle(t *testing.T) {
	t.Run("Successful connection without ping", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenOracle(connector)

		assert.NoError(t, err)
		assert.NotNil(t, db)
		db.Close()
	})

	t.Run("Successful connection with ping", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenOracle(connector, WithPing())

		assert.NoError(t, err)
		assert.NotNil(t, db)
		db.Close()
	})

	t.Run("Successful connection with module name", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenOracle(connector, WithSetModuleName("call DBMS_APPLICATION_INFO.SET_MODULE('TMS','TMS Server')"))

		assert.NoError(t, err)
		assert.NotNil(t, db)
		db.Close()
	})

	t.Run("Connection with ping failure", func(t *testing.T) {
		connector := &mockConnector{shouldFailPing: true}

		db, err := OpenOracle(connector, WithPing())

		assert.Error(t, err)
		assert.Nil(t, db)
	})

	t.Run("Connection with retry on ping failure", func(t *testing.T) {
		connector := &mockConnector{shouldFailPing: true}

		retryConfig := &RetryConfig{
			MaxRetries:    1,
			InitialDelay:  1 * time.Millisecond,
			MaxDelay:      10 * time.Millisecond,
			BackoffFactor: 2.0,
		}

		db, err := OpenOracle(connector, WithPing(), WithRetry(retryConfig))

		assert.Error(t, err)
		assert.Nil(t, db)
	})

	t.Run("Connection with pool options", func(t *testing.T) {
		connector := &mockConnector{}

		db, err := OpenOracle(connector,
			WithMaxIdleConns(10),
			WithMaxOpenConns(20),
			WithConnMaxLifetime(30*time.Minute),
		)

		assert.NoError(t, err)
		assert.NotNil(t, db)

		// Verify pool settings (these are internal to sql.DB, but we can check they don't error)
		assert.NoError(t, db.Ping())

		db.Close()
	})
}

// Test context cancellation during retry operations
func TestRetryDB_ContextCancellation(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    10,
		InitialDelay:  100 * time.Millisecond,
		MaxDelay:      1 * time.Second,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable errors
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnError(retryableError)
	mock.ExpectQuery("SELECT \\* FROM users").WillReturnError(retryableError)

	// Create context with short timeout
	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Millisecond)
	defer cancel()

	result, err := retryDB.QueryContext(ctx, "SELECT * FROM users")

	assert.Error(t, err)
	assert.Equal(t, context.DeadlineExceeded, err)
	assert.Nil(t, result)
}

// Test concurrent access to RetryDB
func TestRetryDB_ConcurrentAccess(t *testing.T) {
	db, mock, err := sqlmock.New()
	require.NoError(t, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock multiple successful queries
	for i := 0; i < 10; i++ {
		rows := sqlmock.NewRows([]string{"id", "name"}).
			AddRow(i, fmt.Sprintf("test%d", i))
		mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)
	}

	// Run concurrent queries
	errChan := make(chan error, 10)
	for i := 0; i < 10; i++ {
		go func(id int) {
			result, err := retryDB.Query("SELECT * FROM users")
			if err != nil {
				errChan <- err
				return
			}
			result.Close()
			errChan <- nil
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		err := <-errChan
		assert.NoError(t, err)
	}

	assert.NoError(t, mock.ExpectationsWereMet())
}

// Benchmark RetryDB operations
func BenchmarkRetryDB_QueryContext_Success(b *testing.B) {
	db, mock, err := sqlmock.New()
	require.NoError(b, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful queries
	for i := 0; i < b.N; i++ {
		rows := sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "test1")
		mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, err := retryDB.QueryContext(ctx, "SELECT * FROM users")
		if err != nil {
			b.Fatal(err)
		}
		result.Close()
	}
}

func BenchmarkRetryDB_ExecContext_Success(b *testing.B) {
	db, mock, err := sqlmock.New()
	require.NoError(b, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock successful executions
	for i := 0; i < b.N; i++ {
		mock.ExpectExec("INSERT INTO users").WillReturnResult(sqlmock.NewResult(1, 1))
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := retryDB.ExecContext(ctx, "INSERT INTO users (name) VALUES (?)", "test")
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkRetryDB_QueryContext_WithRetry(b *testing.B) {
	db, mock, err := sqlmock.New()
	require.NoError(b, err)
	defer db.Close()

	config := &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  1 * time.Millisecond,
		MaxDelay:      10 * time.Millisecond,
		BackoffFactor: 2.0,
	}

	retryDB := NewRetryDB(db, config)

	// Mock retryable error followed by success for each iteration
	retryableError := &mysql.MySQLError{Number: 2006, Message: "Server gone error"}
	for i := 0; i < b.N; i++ {
		mock.ExpectQuery("SELECT \\* FROM users").WillReturnError(retryableError)

		rows := sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "test1")
		mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		result, err := retryDB.QueryContext(ctx, "SELECT * FROM users")
		if err != nil {
			b.Fatal(err)
		}
		result.Close()
	}
}

// Test error handling edge cases
func TestRetryDB_EdgeCases(t *testing.T) {
	t.Run("Nil database pointer", func(t *testing.T) {
		// This would normally panic, but we test the wrapper creation
		var db *sql.DB
		config := DefaultRetryConfig()

		retryDB := NewRetryDB(db, config)
		assert.NotNil(t, retryDB)
		assert.Nil(t, retryDB.DB)
	})

	t.Run("Query with empty result set", func(t *testing.T) {
		db, mock, err := sqlmock.New()
		require.NoError(t, err)
		defer db.Close()

		config := DefaultRetryConfig()
		retryDB := NewRetryDB(db, config)

		// Mock empty result set
		rows := sqlmock.NewRows([]string{"id", "name"})
		mock.ExpectQuery("SELECT \\* FROM users").WillReturnRows(rows)

		result, err := retryDB.Query("SELECT * FROM users")
		assert.NoError(t, err)
		assert.NotNil(t, result)

		// Verify empty result
		hasNext := result.Next()
		assert.False(t, hasNext)

		result.Close()
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("Complex query with multiple parameters", func(t *testing.T) {
		db, mock, err := sqlmock.New()
		require.NoError(t, err)
		defer db.Close()

		config := DefaultRetryConfig()
		retryDB := NewRetryDB(db, config)

		// Mock complex query with multiple parameters
		rows := sqlmock.NewRows([]string{"id", "name", "email"}).
			AddRow(1, "test1", "<EMAIL>")
		mock.ExpectQuery("SELECT \\* FROM users WHERE id = \\? AND name = \\? AND active = \\?").
			WithArgs(1, "test1", true).
			WillReturnRows(rows)

		result, err := retryDB.Query("SELECT * FROM users WHERE id = ? AND name = ? AND active = ?", 1, "test1", true)
		assert.NoError(t, err)
		assert.NotNil(t, result)

		result.Close()
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
