-- Oracle test data for database retry/reconnection testing
-- This file contains SQL statements to set up test scenarios for Oracle database connections

-- Create test tables for retry mechanism testing
CREATE TABLE retry_test_users (
    id NUMBER PRIMARY KEY,
    name VA<PERSON>HAR2(100) NOT NULL,
    email VARCHAR2(200) UNIQUE,
    created_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20) DEFAULT 'ACTIVE'
);

CREATE TABLE retry_test_orders (
    id NUMBER PRIMARY KEY,
    user_id NUMBER NOT NULL,
    amount NUMBER(10,2) NOT NULL,
    order_date DATE DEFAULT SYSDATE,
    status VARCHAR2(20) DEFAULT 'PENDING',
    CONSTRAINT fk_orders_user FOREIGN KEY (user_id) REFERENCES retry_test_users(id)
);

CREATE TABLE retry_test_products (
    id NUMBER PRIMARY KEY,
    name VA<PERSON><PERSON>R2(100) NOT NULL,
    price NUMBER(10,2) NOT NULL,
    stock_quantity NUMBER DEFAULT 0,
    category VARCHAR2(50)
);

-- Create sequences for primary keys
CREATE SEQUENCE retry_test_users_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE retry_test_orders_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE retry_test_products_seq START WITH 1 INCREMENT BY 1;

-- Create triggers for auto-increment
CREATE OR REPLACE TRIGGER retry_test_users_trigger
    BEFORE INSERT ON retry_test_users
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := retry_test_users_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER retry_test_orders_trigger
    BEFORE INSERT ON retry_test_orders
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := retry_test_orders_seq.NEXTVAL;
    END IF;
END;
/

CREATE OR REPLACE TRIGGER retry_test_products_trigger
    BEFORE INSERT ON retry_test_products
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := retry_test_products_seq.NEXTVAL;
    END IF;
END;
/

-- Insert test data for users
INSERT INTO retry_test_users (name, email) VALUES ('Alice Johnson', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Bob Smith', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Carol Williams', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('David Brown', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Eve Davis', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Frank Miller', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Grace Wilson', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Henry Moore', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Ivy Taylor', '<EMAIL>');
INSERT INTO retry_test_users (name, email) VALUES ('Jack Anderson', '<EMAIL>');

-- Insert test data for products
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Laptop', 999.99, 50, 'Electronics');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Mouse', 29.99, 200, 'Electronics');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Keyboard', 79.99, 150, 'Electronics');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Monitor', 299.99, 75, 'Electronics');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Book', 19.99, 300, 'Books');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Pen', 2.99, 1000, 'Stationery');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Notebook', 9.99, 500, 'Stationery');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Chair', 149.99, 25, 'Furniture');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Desk', 249.99, 15, 'Furniture');
INSERT INTO retry_test_products (name, price, stock_quantity, category) VALUES ('Lamp', 39.99, 100, 'Furniture');

-- Insert test data for orders
INSERT INTO retry_test_orders (user_id, amount) VALUES (1, 999.99);
INSERT INTO retry_test_orders (user_id, amount) VALUES (2, 109.98);
INSERT INTO retry_test_orders (user_id, amount) VALUES (3, 299.99);
INSERT INTO retry_test_orders (user_id, amount) VALUES (4, 19.99);
INSERT INTO retry_test_orders (user_id, amount) VALUES (5, 289.98);
INSERT INTO retry_test_orders (user_id, amount) VALUES (1, 49.98);
INSERT INTO retry_test_orders (user_id, amount) VALUES (2, 149.99);
INSERT INTO retry_test_orders (user_id, amount) VALUES (6, 79.99);
INSERT INTO retry_test_orders (user_id, amount) VALUES (7, 12.98);
INSERT INTO retry_test_orders (user_id, amount) VALUES (8, 249.99);

-- Create stored procedures for testing retry scenarios
CREATE OR REPLACE PROCEDURE test_deadlock_scenario AS
    v_user_id NUMBER := 1;
    v_amount NUMBER := 100.00;
BEGIN
    -- This procedure intentionally creates deadlock scenarios for testing
    UPDATE retry_test_users SET name = 'Updated User' WHERE id = v_user_id;
    
    -- Add artificial delay to increase chance of deadlock
    DBMS_LOCK.SLEEP(1);
    
    INSERT INTO retry_test_orders (user_id, amount) VALUES (v_user_id, v_amount);
    
    COMMIT;
END test_deadlock_scenario;
/

CREATE OR REPLACE PROCEDURE test_lock_timeout_scenario AS
    v_user_id NUMBER := 2;
BEGIN
    -- This procedure creates lock timeout scenarios
    FOR i IN 1..5 LOOP
        UPDATE retry_test_users SET name = 'Locked User ' || i WHERE id = v_user_id;
        DBMS_LOCK.SLEEP(2);
    END LOOP;
    
    COMMIT;
END test_lock_timeout_scenario;
/

CREATE OR REPLACE PROCEDURE test_resource_busy_scenario AS
    v_count NUMBER := 0;
BEGIN
    -- This procedure simulates resource busy scenarios
    SELECT COUNT(*) INTO v_count FROM retry_test_users;
    
    -- Perform resource-intensive operations
    FOR i IN 1..1000 LOOP
        INSERT INTO retry_test_orders (user_id, amount) 
        VALUES (MOD(i, 10) + 1, DBMS_RANDOM.VALUE(10, 1000));
        
        IF MOD(i, 100) = 0 THEN
            COMMIT;
        END IF;
    END LOOP;
    
    COMMIT;
END test_resource_busy_scenario;
/

-- Create function for connection testing
CREATE OR REPLACE FUNCTION test_connection_health RETURN VARCHAR2 AS
    v_result VARCHAR2(100);
    v_session_count NUMBER;
    v_db_status VARCHAR2(20);
BEGIN
    -- Check session count
    SELECT COUNT(*) INTO v_session_count FROM v$session WHERE status = 'ACTIVE';
    
    -- Check database status
    SELECT status INTO v_db_status FROM v$instance WHERE ROWNUM = 1;
    
    v_result := 'OK - Sessions: ' || v_session_count || ', DB Status: ' || v_db_status;
    
    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
END test_connection_health;
/

-- Create view for monitoring retry test activity
CREATE OR REPLACE VIEW retry_test_activity AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email,
    COUNT(o.id) as order_count,
    COALESCE(SUM(o.amount), 0) as total_amount,
    MAX(o.order_date) as last_order_date
FROM retry_test_users u
LEFT JOIN retry_test_orders o ON u.id = o.user_id
GROUP BY u.id, u.name, u.email
ORDER BY u.id;

-- Create indexes for performance testing
CREATE INDEX idx_users_email ON retry_test_users(email);
CREATE INDEX idx_users_status ON retry_test_users(status);
CREATE INDEX idx_orders_user_id ON retry_test_orders(user_id);
CREATE INDEX idx_orders_date ON retry_test_orders(order_date);
CREATE INDEX idx_orders_status ON retry_test_orders(status);
CREATE INDEX idx_products_category ON retry_test_products(category);
CREATE INDEX idx_products_price ON retry_test_products(price);

-- Grant necessary permissions (adjust schema name as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON retry_test_users TO tms_test_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON retry_test_orders TO tms_test_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON retry_test_products TO tms_test_user;
-- GRANT SELECT ON retry_test_activity TO tms_test_user;
-- GRANT EXECUTE ON test_deadlock_scenario TO tms_test_user;
-- GRANT EXECUTE ON test_lock_timeout_scenario TO tms_test_user;
-- GRANT EXECUTE ON test_resource_busy_scenario TO tms_test_user;
-- GRANT EXECUTE ON test_connection_health TO tms_test_user;

-- Create package for retry testing utilities
CREATE OR REPLACE PACKAGE retry_test_utils AS
    -- Procedure to simulate various Oracle errors for testing
    PROCEDURE simulate_ora_error(p_error_code IN NUMBER);
    
    -- Function to get current connection info
    FUNCTION get_connection_info RETURN VARCHAR2;
    
    -- Procedure to create artificial load
    PROCEDURE create_artificial_load(p_duration_seconds IN NUMBER DEFAULT 10);
    
    -- Function to check retry test environment
    FUNCTION check_test_environment RETURN VARCHAR2;
    
END retry_test_utils;
/

CREATE OR REPLACE PACKAGE BODY retry_test_utils AS

    PROCEDURE simulate_ora_error(p_error_code IN NUMBER) AS
    BEGIN
        CASE p_error_code
            WHEN 28 THEN
                -- ORA-00028: your session has been killed
                RAISE_APPLICATION_ERROR(-20028, 'Simulated: your session has been killed');
            WHEN 60 THEN
                -- ORA-00060: deadlock detected
                RAISE_APPLICATION_ERROR(-20060, 'Simulated: deadlock detected while waiting for resource');
            WHEN 1012 THEN
                -- ORA-01012: not logged on
                RAISE_APPLICATION_ERROR(-21012, 'Simulated: not logged on');
            WHEN 3113 THEN
                -- ORA-03113: end-of-file on communication channel
                RAISE_APPLICATION_ERROR(-23113, 'Simulated: end-of-file on communication channel');
            WHEN 3114 THEN
                -- ORA-03114: not connected to ORACLE
                RAISE_APPLICATION_ERROR(-23114, 'Simulated: not connected to ORACLE');
            WHEN 12541 THEN
                -- ORA-12541: TNS:no listener
                RAISE_APPLICATION_ERROR(-32541, 'Simulated: TNS:no listener');
            ELSE
                RAISE_APPLICATION_ERROR(-20000, 'Simulated generic Oracle error: ' || p_error_code);
        END CASE;
    END simulate_ora_error;

    FUNCTION get_connection_info RETURN VARCHAR2 AS
        v_info VARCHAR2(1000);
        v_sid NUMBER;
        v_serial NUMBER;
        v_username VARCHAR2(30);
        v_program VARCHAR2(64);
        v_machine VARCHAR2(64);
    BEGIN
        SELECT sid, serial#, username, program, machine 
        INTO v_sid, v_serial, v_username, v_program, v_machine
        FROM v$session 
        WHERE sid = USERENV('SID');
        
        v_info := 'SID: ' || v_sid || 
                  ', Serial: ' || v_serial || 
                  ', User: ' || v_username || 
                  ', Program: ' || v_program || 
                  ', Machine: ' || v_machine;
        
        RETURN v_info;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN 'Error getting connection info: ' || SQLERRM;
    END get_connection_info;

    PROCEDURE create_artificial_load(p_duration_seconds IN NUMBER DEFAULT 10) AS
        v_end_time DATE;
        v_dummy NUMBER;
    BEGIN
        v_end_time := SYSDATE + (p_duration_seconds / 86400);
        
        WHILE SYSDATE < v_end_time LOOP
            -- Create CPU load
            SELECT SUM(LEVEL) INTO v_dummy FROM DUAL CONNECT BY LEVEL <= 1000;
            
            -- Create some I/O load
            INSERT INTO retry_test_orders (user_id, amount) 
            VALUES (MOD(DBMS_RANDOM.VALUE(1, 100), 10) + 1, DBMS_RANDOM.VALUE(1, 1000));
            
            DELETE FROM retry_test_orders WHERE id = (
                SELECT MAX(id) FROM retry_test_orders
            );
        END LOOP;
        
        COMMIT;
    END create_artificial_load;

    FUNCTION check_test_environment RETURN VARCHAR2 AS
        v_users_count NUMBER;
        v_orders_count NUMBER;
        v_products_count NUMBER;
        v_result VARCHAR2(500);
    BEGIN
        SELECT COUNT(*) INTO v_users_count FROM retry_test_users;
        SELECT COUNT(*) INTO v_orders_count FROM retry_test_orders;
        SELECT COUNT(*) INTO v_products_count FROM retry_test_products;
        
        IF v_users_count >= 10 AND v_orders_count >= 10 AND v_products_count >= 10 THEN
            v_result := 'READY - Users: ' || v_users_count || 
                       ', Orders: ' || v_orders_count || 
                       ', Products: ' || v_products_count;
        ELSE
            v_result := 'NOT_READY - Insufficient test data. Users: ' || v_users_count || 
                       ', Orders: ' || v_orders_count || 
                       ', Products: ' || v_products_count;
        END IF;
        
        RETURN v_result;
    END check_test_environment;

END retry_test_utils;
/

-- Commit all changes
COMMIT;

-- Display setup completion message
SELECT 'Oracle retry test environment setup completed successfully!' as status FROM dual;
SELECT retry_test_utils.check_test_environment() as environment_status FROM dual;