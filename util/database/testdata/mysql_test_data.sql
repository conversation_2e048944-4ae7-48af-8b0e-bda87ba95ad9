-- Simplified MySQL test data for database retry/reconnection testing
-- This file contains only basic CRUD operations for reliable testing

-- Create test tables for retry mechanism testing
CREATE TABLE IF NOT EXISTS retry_test_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(200) UNIQUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'ACTIVE'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS retry_test_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'PENDING'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS retry_test_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    stock_quantity INT DEFAULT 0,
    category VARCHAR(50),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for testing locks and deadlocks
CREATE TABLE IF NOT EXISTS retry_test_locks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    resource_name VARCHAR(100) NOT NULL UNIQUE,
    locked_by VARCHAR(100),
    lock_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create table for performance testing
CREATE TABLE IF NOT EXISTS retry_test_performance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    test_name VARCHAR(100) NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP NULL,
    duration_ms INT,
    operation_count INT DEFAULT 0,
    success_count INT DEFAULT 0,
    error_count INT DEFAULT 0,
    notes TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert test data for users
INSERT IGNORE INTO retry_test_users (name, email) VALUES 
('Alice Johnson', '<EMAIL>'),
('Bob Smith', '<EMAIL>'),
('Carol Williams', '<EMAIL>'),
('David Brown', '<EMAIL>'),
('Eve Davis', '<EMAIL>'),
('Frank Miller', '<EMAIL>'),
('Grace Wilson', '<EMAIL>'),
('Henry Moore', '<EMAIL>'),
('Ivy Taylor', '<EMAIL>'),
('Jack Anderson', '<EMAIL>'),
('Karen Thompson', '<EMAIL>'),
('Leo Garcia', '<EMAIL>'),
('Maya Patel', '<EMAIL>'),
('Nathan Lee', '<EMAIL>'),
('Olivia White', '<EMAIL>');

-- Insert test data for products
INSERT IGNORE INTO retry_test_products (name, price, stock_quantity, category) VALUES 
('Laptop', 999.99, 50, 'Electronics'),
('Mouse', 29.99, 200, 'Electronics'),
('Keyboard', 79.99, 150, 'Electronics'),
('Monitor', 299.99, 75, 'Electronics'),
('Tablet', 399.99, 100, 'Electronics'),
('Smartphone', 699.99, 80, 'Electronics'),
('Headphones', 149.99, 120, 'Electronics'),
('Book', 19.99, 300, 'Books'),
('Pen', 2.99, 1000, 'Stationery'),
('Notebook', 9.99, 500, 'Stationery'),
('Pencil', 1.99, 800, 'Stationery'),
('Chair', 149.99, 25, 'Furniture'),
('Desk', 249.99, 15, 'Furniture'),
('Lamp', 39.99, 100, 'Furniture'),
('Bookshelf', 89.99, 30, 'Furniture');

-- Insert test data for orders
INSERT IGNORE INTO retry_test_orders (user_id, amount, status) VALUES 
(1, 999.99, 'COMPLETED'),
(2, 109.98, 'PENDING'),
(3, 299.99, 'COMPLETED'),
(4, 19.99, 'PENDING'),
(5, 289.98, 'COMPLETED'),
(1, 49.98, 'CANCELLED'),
(2, 149.99, 'COMPLETED'),
(6, 79.99, 'PENDING'),
(7, 12.98, 'COMPLETED'),
(8, 249.99, 'PENDING'),
(9, 399.99, 'COMPLETED'),
(10, 699.99, 'PENDING'),
(11, 149.99, 'COMPLETED'),
(12, 21.98, 'PENDING'),
(13, 89.99, 'COMPLETED');

-- Insert initial lock test data
INSERT IGNORE INTO retry_test_locks (resource_name, locked_by, data) VALUES 
('test_resource_1', NULL, 'Test data for resource 1'),
('test_resource_2', NULL, 'Test data for resource 2'),
('test_resource_3', NULL, 'Test data for resource 3');

-- Insert some performance test records
INSERT IGNORE INTO retry_test_performance (test_name, duration_ms, operation_count, success_count, error_count, notes) VALUES
('connection_test', 1500, 100, 95, 5, 'Initial connection testing'),
('query_performance', 2000, 500, 485, 15, 'Query performance under load'),
('retry_mechanism', 3000, 200, 180, 20, 'Retry mechanism effectiveness'),
('deadlock_recovery', 4500, 50, 45, 5, 'Deadlock detection and recovery'),
('timeout_handling', 2500, 150, 140, 10, 'Timeout handling verification');

-- Create indexes for better performance (MySQL 5.7 compatible syntax)
CREATE INDEX idx_users_email ON retry_test_users(email);
CREATE INDEX idx_users_status ON retry_test_users(status);
CREATE INDEX idx_orders_user_id ON retry_test_orders(user_id);
CREATE INDEX idx_orders_date ON retry_test_orders(order_date);
CREATE INDEX idx_orders_status ON retry_test_orders(status);
CREATE INDEX idx_products_category ON retry_test_products(category);
CREATE INDEX idx_products_price ON retry_test_products(price);
CREATE INDEX idx_perf_test_name ON retry_test_performance(test_name);
CREATE INDEX idx_perf_start_time ON retry_test_performance(start_time);

-- Display setup completion message (optional, will be ignored if not supported)
SELECT 'MySQL retry test environment setup completed successfully!' as status;