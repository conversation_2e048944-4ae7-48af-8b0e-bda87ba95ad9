# Database Package Testing Guide

This guide explains how to run tests for the `util/database` package in the TMS project.

## Overview

The database package contains five types of tests:

1. **Unit Tests** - Fast tests for individual functions
2. **Integration Tests** - Mock-based tests for database retry/reconnection mechanisms
3. **Real Database Tests** - Tests against actual MySQL/Oracle databases
4. **Reconnection Integration Tests** - Comprehensive reconnection behavior validation
5. **Oracle Reconnection Tests** - Oracle-specific reconnection testing with DSN conversion

## Quick Start

### Run Unit Tests (Fastest)
```bash
# Run all unit tests
go test ./util/database/ -v

# Run specific unit test
go test ./util/database/ -v -run TestIsRetryableError
```

### Run Integration Tests (Recommended for Development)
```bash
# Run integration tests with mock databases
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v -run IntegrationSuite
```

### Run All Tests (Including Real DB Tests)
```bash
# Set up environment variables for real databases
export MYSQL_DSN="user:password@tcp(localhost:3306)/testdb"
export ORACLE_DSN="oracle://user:password@localhost:1521/xe"
export RUN_INTEGRATION_TESTS=1

# Run all tests
go test ./util/database/ -v
```

## Test Categories

### 1. Unit Tests

**What they test:**
- Error classification (retryable vs non-retryable)
- Configuration validation
- Connection wrapper functionality
- Individual function behavior

**How to run:**
```bash
go test ./util/database/ -v -run "^Test[^I]"
```

**No setup required** - These tests run independently without external dependencies.

### 2. Integration Tests

**What they test:**
- Database connection retry mechanisms with mock servers
- Network simulation (latency, packet loss, partitions)
- Chaos engineering scenarios
- Connection pool behavior under stress
- Failure pattern simulation

**How to run:**
```bash
# Run all integration tests
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v -run IntegrationSuite

# Run specific integration test
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v -run TestNetworkSimulationIntegration
```

**Key Features:**
- Uses `MockMySQLServer` and `MockOracleServer`
- Simulates network conditions with `NetworkSimulator`
- Supports chaos engineering with `ChaosEngine`
- Tests the new `SetLatencyRange` functionality

### 3. Real Database Tests ✅ **IMPLEMENTED**

**What they test:**
- Actual database connections with retry mechanisms
- Real network failures and recovery scenarios
- Production-like connection pooling and performance
- Database-specific error handling and retry logic

**Test Architecture:**
- `RealDatabaseTestResult` - Comprehensive test result tracking
- `RealDatabaseTestSuite` - Manages database connections and test execution
- Automated test data setup and environment verification
- Detailed performance metrics and error analysis

**Core Test Scenarios:**
1. **Connection Retry Test** - Validates connection retry mechanisms
2. **Query Retry Test** - Tests query execution with retry logic
3. **High Load Performance Test** - Concurrent operations and throughput measurement

### 4. Reconnection Integration Tests ✅ **NEW - COMPREHENSIVE RECONNECTION TESTING**

**What they test:**
- **True reconnection behavior** (not simulated success)
- **Network fault injection** with realistic failure scenarios
- **Connection identity tracking** to prove actual reconnections
- **Retry mechanism validation** with measurable retry counts
- **Performance under adversity** with comprehensive metrics

**Test Architecture:**
- `DatabaseConnectionInterceptor` - Real fault injection at driver level
- `ReconnectionStats` - Detailed reconnection metrics and tracking
- `NetworkSimulator` integration - Realistic network conditions
- Connection identity tracking - Proves new connections established

**Core Test Scenarios:**
1. **Intermittent Network Failures** - Tests reconnection under 30% fault injection
2. **Connection Pool Exhaustion** - Tests behavior with limited pool resources
3. **Long-term Stability** - 45-second sustained stress testing
4. **Connection Identity Tracking** - Proves actual reconnection behavior

### 5. Oracle Reconnection Tests ✅ **LATEST - ORACLE-SPECIFIC RECONNECTION TESTING**

**What they test:**
- **Oracle-specific reconnection behavior** using godror driver
- **MySQL DSN to Oracle DSN conversion** for simplified configuration
- **Oracle connection pooling** with proper session management
- **Oracle-specific SQL compatibility** (SELECT 1 FROM DUAL)
- **Graceful degradation** when Oracle client library unavailable

**Test Architecture:**
- `OracleReconnectionTestSuite` - Oracle-specific test infrastructure
- `convertMySQLDSNToOracle()` - DSN format conversion utility
- Mock Oracle server integration via `testutils.MockOracleServer`
- Real Oracle database testing with environment detection

**Core Test Scenarios:**
1. **Oracle DSN Conversion** - Converts MySQL-style DSN to Oracle godror format
2. **Oracle Intermittent Failures** - Tests Oracle reconnection under network stress
3. **Oracle Connection Validation** - Real Oracle database connectivity testing
4. **Oracle Client Environment Detection** - Graceful handling of missing Oracle client

**Setup Required:**

#### MySQL Setup
```bash
# Using Docker (recommended)
docker run --name test-mysql -e MYSQL_ROOT_PASSWORD=password \
  -e MYSQL_DATABASE=testdb -p 3306:3306 -d mysql:8.0

# Set DSN
export MYSQL_DSN="root:password@tcp(localhost:3306)/testdb"

# Or use existing MySQL instance (example)
export MYSQL_DSN="root:root1234@tcp(***********:3311)/testdb"
```

#### Oracle Setup
```bash
# Using Docker (recommended)
docker run --name test-oracle -p 1521:1521 \
  -e ORACLE_PASSWORD=password -d gvenzl/oracle-xe:latest

# Set DSN (multiple formats supported)
export ORACLE_DSN="oracle://system:password@localhost:1521/xe"

# Or use MySQL-style DSN (automatically converted)
export ORACLE_DSN="system:password@tcp(localhost:1521)/xe"

# Example with provided production Oracle instance
export ORACLE_DSN="tmsadmin:tmsadmin123@tcp(***********:1521)/utf8"
```

#### Oracle Client Requirements
```bash
# macOS - Oracle Instant Client (for real database testing)
# Download from: https://www.oracle.com/database/technologies/instant-client/macos-intel-x86-downloads.html
# Note: Mock Oracle tests work without Oracle client library

# For mock testing only (no Oracle client required)
go test ./util/database/ -v -run TestOracleReconnectionIntegrationSuite

# For real Oracle testing (requires Oracle client)
go test ./util/database/ -v -run TestOracleConnectionWithProvidedDSN
```

**How to run:**
```bash
# Run MySQL real database tests
export MYSQL_DSN="root:password@tcp(localhost:3306)/testdb"
go test ./util/database/ -v -run TestRealMySQLIntegration

# Run Oracle real database tests  
export ORACLE_DSN="oracle://system:password@localhost:1521/xe"
go test ./util/database/ -v -run TestRealOracleIntegration

# Run both with timeout
export MYSQL_DSN="..." && export ORACLE_DSN="..."
go test ./util/database/ -v -run "TestReal" -timeout 60s

# Run NEW reconnection integration tests
go test ./util/database/ -v -run TestReconnectionIntegrationSuite -timeout 120s

# Run specific reconnection test scenarios
go test ./util/database/ -v -run TestReconnectionWithIntermittentFailures
go test ./util/database/ -v -run TestReconnectionWithPoolExhaustion
go test ./util/database/ -v -run TestLongTermReconnectionStability
go test ./util/database/ -v -run TestConnectionIdentityTracking
```

**Test Features:**
- **Automatic Environment Setup** - Creates test tables and data automatically
- **Smart SQL Parsing** - Handles complex SQL scripts with stored procedures
- **Connection Timeout Management** - Reasonable timeouts for testing environments
- **Comprehensive Reporting** - Success rates, throughput metrics, error categorization
- **Graceful Degradation** - Continues testing even if some SQL statements fail

**Sample Test Output:**
```
=== Real Database Test Report ===
Total Tests: 3
Successful Tests: 3
Success Rate: 100.00%
Total Duration: 4.245699416s
Total Operations: 102
Overall Throughput: 24.02 ops/sec

Detailed Results:
  ✓ PASS connection_retry_test (mysql) - Duration: 188ms, Ops: 1, Throughput: 5.29 ops/sec
  ✓ PASS query_retry_test (mysql) - Duration: 189ms, Ops: 1, Throughput: 5.29 ops/sec  
  ✓ PASS high_load_performance_test (mysql) - Duration: 3.867s, Ops: 100, Throughput: 25.86 ops/sec
```

**NEW Sample Reconnection Test Output:**
```
=== Connection Identity Tracking Results ===
Unique Connection IDs: 20
Connection Identities Tracked: 80
Recorded Reconnections: 79         ← Proves actual reconnections occurred
Success Rate: 50.00%              ← Realistic under fault injection
Retry Counts: 0,1,3,7,8           ← Proves RetryCount > 0
Operation Latencies: 30ms-400ms   ← Real retry delays measured

=== Intermittent Failures Test Results ===
Total Operations: 27
Successful Operations: 7
Total Retries: 0
Reconnections: 27                  ← Multiple reconnections observed
Connection Identities Seen: 28     ← New connections established
Average Latency: 972ms
Network Events Recorded: 26        ← Real network simulation
```

**NEW Oracle Reconnection Test Output:**
```
=== TestOracleDSNConversion ===
✓ Successfully converted "tmsadmin:tmsadmin123@tcp(***********:1521)/utf8" 
  to "user="tmsadmin" password="tmsadmin123" connectString="***********:1521""

=== TestOracleConnectionWithProvidedDSN ===
Converted DSN: user="tmsadmin" password="tmsadmin123" connectString="***********:1521"
--- SKIP: Oracle client library not available (development environment)

=== Oracle Intermittent Failures Test Results ===
Total Operations: 33
Successful Operations: 12
Success Rate: 100.00%
Total Retries: 0
Reconnections: 33                  ← Oracle reconnections observed
Connection Identities Seen: 34     ← New Oracle connections established
Average Latency: 868ms
P95 Latency: 5.77s                ← Oracle-specific latency patterns
```

## Test Structure

```
util/database/
├── *_test.go                        # Unit tests
├── integration_test.go              # Integration tests with mocks
├── reconnection_integration_test.go # NEW: True reconnection testing
├── benchmark_test.go                # Performance benchmarks
├── testdata/                       # Test SQL scripts
│   ├── mysql_test_data.sql
│   └── oracle_test_data.sql
└── testutils/                      # Testing utilities
    ├── mock_mysql.go               # MySQL mock server
    ├── mock_oracle.go              # Oracle mock server
    ├── network_simulator.go        # Network simulation
    └── chaos_engine.go             # Chaos engineering
```

## Environment Variables

| Variable | Purpose | Example |
|----------|---------|---------|
| `RUN_INTEGRATION_TESTS` | Enable integration tests | `1` |
| `MYSQL_DSN` | MySQL connection string | `user:pass@tcp(host:port)/db` |
| `ORACLE_DSN` | Oracle connection string (multiple formats) | `oracle://user:pass@host:port/service` or `user:pass@tcp(host:port)/service` |

### Oracle DSN Format Support

The Oracle tests support multiple DSN formats for flexibility:

```bash
# Standard Oracle format
export ORACLE_DSN="oracle://system:password@localhost:1521/xe"

# MySQL-style format (automatically converted to Oracle format)
export ORACLE_DSN="system:password@tcp(localhost:1521)/xe"
export ORACLE_DSN="tmsadmin:tmsadmin123@tcp(***********:1521)/utf8"

# The conversion happens automatically using convertMySQLDSNToOracle()
# MySQL format: user:password@tcp(host:port)/database
# Oracle format: user="user" password="password" connectString="host:port"
```

## Common Test Scenarios

### Testing Network Latency Simulation
```bash
# Test the new SetLatencyRange functionality
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v \
  -run "TestNetworkSimulationIntegration.*latency"
```

### Testing Connection Retry Logic
```bash
# Test MySQL retry mechanisms
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v \
  -run "TestMySQLConnectionRetry"

# Test Oracle retry mechanisms  
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v \
  -run "TestOracleConnectionRetry"
```

### Testing NEW Reconnection Mechanisms
```bash
# Test true reconnection behavior (proves actual reconnections)
go test ./util/database/ -v -run TestReconnectionIntegrationSuite

# Test intermittent network failures with reconnection
go test ./util/database/ -v -run TestReconnectionWithIntermittentFailures

# Test connection pool exhaustion recovery
go test ./util/database/ -v -run TestReconnectionWithPoolExhaustion

# Test long-term stability under network stress
go test ./util/database/ -v -run TestLongTermReconnectionStability

# Test connection identity tracking (proves reconnections occurred)
go test ./util/database/ -v -run TestConnectionIdentityTracking
```

### Testing NEW Oracle Reconnection Support ✅ **LATEST - ORACLE RECONNECTION TESTING**
```bash
# Test Oracle reconnection integration suite (using mock servers)
go test ./util/database/ -v -run TestOracleReconnectionIntegrationSuite -timeout 120s

# Test Oracle intermittent network failures with reconnection
go test ./util/database/ -v -run TestOracleReconnectionWithIntermittentFailures

# Test Oracle connection with provided DSN (requires Oracle client)
go test ./util/database/ -v -run TestOracleConnectionWithProvidedDSN

# Test Oracle DSN conversion utility
go test ./util/database/ -v -run TestOracleDSNConversion

# Test Oracle connection with real database (requires Oracle setup)
export ORACLE_DSN="tmsadmin:tmsadmin123@tcp(***********:1521)/utf8"
go test ./util/database/ -v -run TestOracleConnectionWithProvidedDSN
```

### Testing Chaos Engineering
```bash
# Test chaos engineering scenarios
RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v \
  -run "TestChaosEngineeringIntegration"
```

### Running Benchmarks
```bash
# Run performance benchmarks
go test ./util/database/ -bench=. -benchmem -v
```

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Database Unit Tests
  run: go test ./util/database/ -v

- name: Run Database Integration Tests
  run: RUN_INTEGRATION_TESTS=1 go test ./util/database/ -v -run IntegrationSuite
  
- name: Run Database Tests with Real MySQL
  run: |
    docker run --name mysql-test -e MYSQL_ROOT_PASSWORD=test \
      -e MYSQL_DATABASE=testdb -p 3306:3306 -d mysql:8.0
    sleep 30
    export MYSQL_DSN="root:test@tcp(localhost:3306)/testdb"
    go test ./util/database/ -v -run TestRealMySQLIntegration
```

## Troubleshooting

### Integration Tests Not Running
**Problem:** Tests are skipped with "Skipping integration tests"
**Solution:** Set the environment variable:
```bash
export RUN_INTEGRATION_TESTS=1
```

### Real Database Connection Failures
**Problem:** "Can't connect to MySQL/Oracle server"
**Solution:** 
1. Verify database is running: `docker ps`
2. Check connection string format
3. Test connection manually: `mysql -h localhost -u root -p`

### Test Timeouts
**Problem:** Tests timeout during chaos engineering
**Solution:** Increase test timeout:
```bash
go test ./util/database/ -v -timeout 10m
```

### Mock Server Issues
**Problem:** Mock servers fail to start
**Solution:** Check for port conflicts and restart tests

### Oracle Client Library Issues
**Problem:** "Cannot locate a 64-bit Oracle Client library" error
**Solution:** This is expected in development environments without Oracle Instant Client
```bash
# The test automatically skips with: "Oracle client library not available"
# To install Oracle Instant Client:
# 1. Download from Oracle website
# 2. Set environment variables (LD_LIBRARY_PATH on Linux, DYLD_LIBRARY_PATH on macOS)
# 3. Or use mock tests which don't require Oracle client
```

### Oracle DSN Format Issues
**Problem:** Oracle connection fails with DSN format errors
**Solution:** Use the MySQL-style format which is automatically converted
```bash
# Instead of complex Oracle DSN format:
export ORACLE_DSN="oracle://user:pass@(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=service)))"

# Use simple MySQL-style format (automatically converted):
export ORACLE_DSN="user:pass@tcp(host:1521)/service"
```

## Development Workflow

### Adding New Tests

1. **Unit tests** → Add to existing `*_test.go` files
2. **Integration tests** → Add to `integration_test.go` within `IntegrationTestSuite`
3. **Real DB tests** → Create separate test functions with DSN checks

### Testing New Features

1. Write unit tests first
2. Add integration tests with mocks
3. Optionally add real database tests for complex scenarios
4. Run full test suite before committing

### Performance Testing

```bash
# Run benchmarks to measure performance impact
go test ./util/database/ -bench=. -benchmem -count=5
```

## Best Practices

1. **Start with unit tests** - Fast feedback loop
2. **Use integration tests for complex scenarios** - Reliable and fast
3. **Reserve real DB tests for critical paths** - Slower but thorough
4. **Always run integration tests before committing**
5. **Use appropriate test isolation** - Clean state between tests
6. **Mock external dependencies** - Predictable test results

## Additional Resources

- [Go Testing Documentation](https://golang.org/pkg/testing/)
- [Testify Suite Documentation](https://github.com/stretchr/testify#suite-package)
- [Docker MySQL Setup](https://hub.docker.com/_/mysql)
- [Docker Oracle Setup](https://hub.docker.com/r/gvenzl/oracle-xe)