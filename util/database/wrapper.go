package database

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/lib/godror"
	"gitee.com/pingcap_enterprise/tms/lib/godror/dsn"
	"github.com/go-sql-driver/mysql"
	"github.com/pingcap/errors"
	"time"
)

type DriverType int

const DriverTypeGoDror DriverType = 0
const DriverTypeGoOra DriverType = 1

func ParseGoDrorDSN(connectStr string) (godror.ConnectionParams, error) {
	return godror.ParseDSN(connectStr)
}

func NewGoDrorPassword(s string) godror.Password {
	return godror.NewPassword(s)
}

func NewGoDrorConnector(oraDSN dsn.ConnectionParams) driver.Connector {
	return godror.NewConnector(oraDSN)
}

func GoDrorPrefetchCount(rowCount int) godror.Option {
	return godror.PrefetchCount(rowCount)
}

func GoDrorFetchArraySize(rowCount int) godror.Option {
	return godror.FetchArraySize(rowCount)
}

func OpenOracle(connector driver.Connector, opts ...Option) (*sql.DB, error) {
	// Create and configure database connection
	db := sql.OpenDB(connector)

	// Apply connection pool options
	dbOptions := applyOptions(opts...)
	db.SetMaxIdleConns(dbOptions.MaxIdleConns)
	db.SetMaxOpenConns(dbOptions.MaxOpenConns)
	db.SetConnMaxLifetime(dbOptions.ConnMaxLifetime)

	if dbOptions.Ping {
		var pingErr error
		if dbOptions.PingTimeout != 0 {
			// Ping database with timeout and retry if enabled
			ctx, cancel := context.WithTimeout(context.Background(), time.Duration(dbOptions.PingTimeout)*time.Second)
			defer cancel()

			if dbOptions.EnableRetry {
				pingErr = ExecuteWithRetry(ctx, dbOptions.RetryConfig, func() error {
					return db.PingContext(ctx)
				})
			} else {
				pingErr = db.PingContext(ctx)
			}
		} else {
			// Ping database with retry if enabled
			if dbOptions.EnableRetry {
				pingErr = ExecuteWithRetry(context.Background(), dbOptions.RetryConfig, func() error {
					return db.Ping()
				})
			} else {
				pingErr = db.Ping()
			}
		}

		if pingErr != nil {
			db.Close()
			return nil, errors.Trace(pingErr)
		}
	}
	if dbOptions.ModuleName != "" {
		if _, setModuleErr := db.Exec(dbOptions.ModuleName); setModuleErr != nil {
			db.Close()
			return nil, errors.Trace(setModuleErr)
		}
	}

	return db, nil
}

// OpenDBOption holds configuration for database connection pooling
type OpenDBOption struct {
	MaxIdleConns    int
	MaxOpenConns    int
	ConnMaxLifetime time.Duration

	Ping        bool
	PingTimeout int
	ModuleName  string

	// Retry configuration
	EnableRetry bool
	RetryConfig *RetryConfig
}

// Option is a function that configures OpenDBOption
type Option func(*OpenDBOption)

// WithMaxIdleConns sets the maximum number of idle connections
func WithMaxIdleConns(n int) Option {
	return func(o *OpenDBOption) {
		o.MaxIdleConns = n
	}
}

// WithPing ping db after connection
func WithPing() Option {
	return func(o *OpenDBOption) {
		o.Ping = true
	}
}

// WithPingTimeout ping db after connection
func WithPingTimeout(seconds int) Option {
	return func(o *OpenDBOption) {
		o.Ping = true
		o.PingTimeout = seconds
	}
}

// WithSetModuleName set module after connection
func WithSetModuleName(moduleName string) Option {
	return func(o *OpenDBOption) {
		o.ModuleName = moduleName
	}
}

// WithMaxOpenConns sets the maximum number of open connections
func WithMaxOpenConns(n int) Option {
	return func(o *OpenDBOption) {
		o.MaxOpenConns = n
	}
}

// WithConnMaxLifetime sets the maximum lifetime of a connection
func WithConnMaxLifetime(d time.Duration) Option {
	return func(o *OpenDBOption) {
		o.ConnMaxLifetime = d
	}
}

// WithRetry enables retry mechanism with custom configuration
func WithRetry(config *RetryConfig) Option {
	return func(o *OpenDBOption) {
		o.EnableRetry = true
		o.RetryConfig = config
	}
}

// WithDefaultRetry enables retry mechanism with default configuration
func WithDefaultRetry() Option {
	return func(o *OpenDBOption) {
		o.EnableRetry = true
		o.RetryConfig = DefaultRetryConfig()
	}
}

// applyOptions applies the provided options to an OpenDBOption instance
func applyOptions(opts ...Option) *OpenDBOption {
	opt := &OpenDBOption{}
	for _, apply := range opts {
		apply(opt)
	}
	return opt
}

// RetryDB wraps a *sql.DB with retry functionality
type RetryDB struct {
	*sql.DB
	retryConfig *RetryConfig
}

// NewRetryDB creates a new RetryDB wrapper
func NewRetryDB(db *sql.DB, config *RetryConfig) *RetryDB {
	if config == nil {
		config = DefaultRetryConfig()
	}
	return &RetryDB{
		DB:          db,
		retryConfig: config,
	}
}

// QueryContext executes a query with retry logic
func (rdb *RetryDB) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return ExecuteWithRetryAndResult(ctx, rdb.retryConfig, func() (*sql.Rows, error) {
		return rdb.DB.QueryContext(ctx, query, args...)
	})
}

// Query executes a query with retry logic
func (rdb *RetryDB) Query(query string, args ...interface{}) (*sql.Rows, error) {
	return ExecuteWithRetryAndResult(context.Background(), rdb.retryConfig, func() (*sql.Rows, error) {
		return rdb.DB.Query(query, args...)
	})
}

// QueryRowContext executes a query that returns a single row with retry logic
func (rdb *RetryDB) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	// Note: sql.Row doesn't return error directly, so we can't retry at this level
	// The error is returned when scanning the row
	return rdb.DB.QueryRowContext(ctx, query, args...)
}

// QueryRow executes a query that returns a single row with retry logic
func (rdb *RetryDB) QueryRow(query string, args ...interface{}) *sql.Row {
	return rdb.DB.QueryRow(query, args...)
}

// ExecContext executes an exec statement with retry logic
func (rdb *RetryDB) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return ExecuteWithRetryAndResult(ctx, rdb.retryConfig, func() (sql.Result, error) {
		return rdb.DB.ExecContext(ctx, query, args...)
	})
}

// Exec executes an exec statement with retry logic
func (rdb *RetryDB) Exec(query string, args ...interface{}) (sql.Result, error) {
	return ExecuteWithRetryAndResult(context.Background(), rdb.retryConfig, func() (sql.Result, error) {
		return rdb.DB.Exec(query, args...)
	})
}

// PrepareContext prepares a statement with retry logic
func (rdb *RetryDB) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	return ExecuteWithRetryAndResult(ctx, rdb.retryConfig, func() (*sql.Stmt, error) {
		return rdb.DB.PrepareContext(ctx, query)
	})
}

// Prepare prepares a statement with retry logic
func (rdb *RetryDB) Prepare(query string) (*sql.Stmt, error) {
	return ExecuteWithRetryAndResult(context.Background(), rdb.retryConfig, func() (*sql.Stmt, error) {
		return rdb.DB.Prepare(query)
	})
}

// BeginTx begins a transaction with retry logic
func (rdb *RetryDB) BeginTx(ctx context.Context, opts *sql.TxOptions) (*sql.Tx, error) {
	return ExecuteWithRetryAndResult(ctx, rdb.retryConfig, func() (*sql.Tx, error) {
		return rdb.DB.BeginTx(ctx, opts)
	})
}

// Begin begins a transaction with retry logic
func (rdb *RetryDB) Begin() (*sql.Tx, error) {
	return ExecuteWithRetryAndResult(context.Background(), rdb.retryConfig, func() (*sql.Tx, error) {
		return rdb.DB.Begin()
	})
}

// PingContext pings the database with retry logic
func (rdb *RetryDB) PingContext(ctx context.Context) error {
	return ExecuteWithRetry(ctx, rdb.retryConfig, func() error {
		return rdb.DB.PingContext(ctx)
	})
}

// Ping pings the database with retry logic
func (rdb *RetryDB) Ping() error {
	return ExecuteWithRetry(context.Background(), rdb.retryConfig, func() error {
		return rdb.DB.Ping()
	})
}

// MySQLConfig holds configuration parameters for MySQL/TiDB database connection
type MySQLConfig struct {
	User          string
	Password      string
	Host          string
	Port          int
	Database      string
	Charset       string
	ConnectParams string
	Timezone      *time.Location
}

// Validate checks if the MySQL configuration is valid
func (c *MySQLConfig) Validate() error {
	if c.User == "" {
		return errors.New("username is required")
	}
	if c.Password == "" {
		return errors.New("password is required")
	}
	if c.Host == "" {
		return errors.New("host is required")
	}
	if c.Port <= 0 {
		return errors.New("valid port is required")
	}
	return nil
}

// MySQLConnector implements the driver.Connector interface for MySQL connections
type MySQLConnector struct {
	config MySQLConfig
}

// NewMySQLConnector creates a new MySQL connector with the given configuration
func NewMySQLConnector(config MySQLConfig) driver.Connector {
	return &MySQLConnector{config: config}
}

// Connect implements the driver.Connector interface
func (c *MySQLConnector) Connect(ctx context.Context) (driver.Conn, error) {
	// Build MySQL DSN
	mysqlConfig := mysql.NewConfig()
	mysqlConfig.User = c.config.User
	mysqlConfig.Passwd = c.config.Password
	mysqlConfig.Net = "tcp"
	mysqlConfig.Addr = fmt.Sprintf("%s:%d", c.config.Host, c.config.Port)
	mysqlConfig.DBName = c.config.Database

	// Set charset if provided
	if c.config.Charset != "" {
		mysqlConfig.Params = map[string]string{"charset": c.config.Charset}
	}

	// Set timezone if provided
	if c.config.Timezone != nil {
		mysqlConfig.Loc = c.config.Timezone
	} else {
		// Default to local timezone
		mysqlConfig.Loc = time.Local
	}

	// Parse time by default for better compatibility
	mysqlConfig.ParseTime = true

	// Apply additional connection parameters
	if c.config.ConnectParams != "" && mysqlConfig.Params == nil {
		mysqlConfig.Params = make(map[string]string)
	}
	// Note: Additional parsing of ConnectParams could be added here if needed

	// Create MySQL connector
	connector, err := mysql.NewConnector(mysqlConfig)
	if err != nil {
		return nil, errors.Trace(err)
	}

	return connector.Connect(ctx)
}

// Driver implements the driver.Connector interface
func (c *MySQLConnector) Driver() driver.Driver {
	return &mysql.MySQLDriver{}
}

// OpenMySQL establishes a connection to a MySQL/TiDB database with the provided configuration
// It provides the same functionality as OpenOracle but for MySQL/TiDB databases
func OpenMySQL(connector driver.Connector, opts ...Option) (*sql.DB, error) {
	// Create and configure database connection
	db := sql.OpenDB(connector)

	// Apply connection pool options
	dbOptions := applyOptions(opts...)
	db.SetMaxIdleConns(dbOptions.MaxIdleConns)
	db.SetMaxOpenConns(dbOptions.MaxOpenConns)
	db.SetConnMaxLifetime(dbOptions.ConnMaxLifetime)

	if dbOptions.Ping {
		var pingErr error
		if dbOptions.PingTimeout != 0 {
			// Ping database with timeout and retry if enabled
			ctx, cancel := context.WithTimeout(context.Background(), time.Duration(dbOptions.PingTimeout)*time.Second)
			defer cancel()

			if dbOptions.EnableRetry {
				pingErr = ExecuteWithRetry(ctx, dbOptions.RetryConfig, func() error {
					return db.PingContext(ctx)
				})
			} else {
				pingErr = db.PingContext(ctx)
			}
		} else {
			// Ping database with retry if enabled
			if dbOptions.EnableRetry {
				pingErr = ExecuteWithRetry(context.Background(), dbOptions.RetryConfig, func() error {
					return db.Ping()
				})
			} else {
				pingErr = db.Ping()
			}
		}

		if pingErr != nil {
			db.Close()
			return nil, errors.Trace(pingErr)
		}
	}

	// Note: ModuleName is Oracle-specific, so we skip it for MySQL

	return db, nil
}
