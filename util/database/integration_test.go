package database

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"gitee.com/pingcap_enterprise/tms/util/database/testutils"
	"github.com/go-sql-driver/mysql"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// IntegrationTestSuite contains integration tests for database retry/reconnection mechanisms
type IntegrationTestSuite struct {
	suite.Suite
	mysqlServer  *testutils.MockMySQLServer
	oracleServer *testutils.MockOracleServer
	networkSim   *testutils.NetworkSimulator
	chaosEngine  *testutils.ChaosEngine
}

// SetupSuite sets up the test suite
func (suite *IntegrationTestSuite) SetupSuite() {
	// Initialize mock servers
	suite.mysqlServer = testutils.NewMockMySQLServer()
	suite.oracleServer = testutils.NewMockOracleServer()
	suite.networkSim = testutils.NewNetworkSimulator()
	suite.chaosEngine = testutils.NewChaosEngine()

	// Configure chaos engine
	suite.chaosEngine.SetMySQLServer(suite.mysqlServer)
	suite.chaosEngine.SetOracleServer(suite.oracleServer)
	suite.chaosEngine.SetNetworkSimulator(suite.networkSim)

	// Start services
	suite.mysqlServer.Start()
	suite.oracleServer.Start()
	suite.networkSim.Start()
	suite.chaosEngine.Start()
}

// TearDownSuite tears down the test suite
func (suite *IntegrationTestSuite) TearDownSuite() {
	if suite.chaosEngine != nil {
		suite.chaosEngine.Stop()
	}
	if suite.networkSim != nil {
		suite.networkSim.Stop()
	}
	if suite.mysqlServer != nil {
		suite.mysqlServer.Stop()
	}
	if suite.oracleServer != nil {
		suite.oracleServer.Stop()
	}
}

// SetupTest sets up each individual test
func (suite *IntegrationTestSuite) SetupTest() {
	// Reset servers to clean state
	suite.mysqlServer.SetErrorRate(0.0)
	suite.mysqlServer.EnableConnectionErrors(false)
	suite.mysqlServer.EnableNetworkFailures(false)
	suite.mysqlServer.EnableLockTimeouts(false)
	suite.mysqlServer.EnableResourceExhaustion(false)
	suite.mysqlServer.EnableTiDBErrors(false)

	suite.oracleServer.SetErrorRate(0.0)
	suite.oracleServer.EnableNetworkFailures(false)
	suite.oracleServer.EnableSessionTimeouts(false)
	suite.oracleServer.EnableResourceExhaustion(false)
	suite.oracleServer.EnableDeadlockErrors(false)

	suite.networkSim.SetCondition(testutils.NetworkHealthy)
	suite.networkSim.ClearEvents()

	suite.chaosEngine.ClearEventLog()
}

// TestMySQLConnectionRetry tests MySQL connection retry mechanisms
func (suite *IntegrationTestSuite) TestMySQLConnectionRetry() {
	connector := testutils.NewMockMySQLConnector(suite.mysqlServer)

	suite.Run("Successful connection without retry", func() {
		db, err := OpenMySQL(connector, WithDefaultRetry())
		suite.NoError(err)
		suite.NotNil(db)

		err = db.Ping()
		suite.NoError(err)

		db.Close()
	})

	suite.Run("Connection with transient failures", func() {
		// Enable connection errors with high rate
		suite.mysqlServer.SetErrorRate(0.8)
		suite.mysqlServer.EnableConnectionErrors(true)

		config := &RetryConfig{
			MaxRetries:    5,
			InitialDelay:  10 * time.Millisecond,
			MaxDelay:      100 * time.Millisecond,
			BackoffFactor: 2.0,
		}

		db, err := OpenMySQL(connector, WithRetry(config), WithPing())

		// Should eventually succeed despite high error rate
		if err == nil {
			db.Close()
		}

		// Check that retries were attempted
		errorHistory := suite.mysqlServer.GetErrorHistory()
		suite.True(len(errorHistory) > 0, "Expected some errors to be recorded")
	})

	suite.Run("Connection pool behavior under stress", func() {
		suite.mysqlServer.SetMaxConnections(5)

		db, err := OpenMySQL(connector,
			WithMaxOpenConns(10),
			WithMaxIdleConns(3),
			WithConnMaxLifetime(30*time.Second),
			WithDefaultRetry(),
		)
		suite.NoError(err)
		defer db.Close()

		// Test concurrent connections
		errChan := make(chan error, 10)
		for i := 0; i < 10; i++ {
			go func() {
				err := db.Ping()
				errChan <- err
			}()
		}

		// Collect results
		successCount := 0
		for i := 0; i < 10; i++ {
			err := <-errChan
			if err == nil {
				successCount++
			}
		}

		// Should have some successes even with limited server connections
		suite.True(successCount > 0, "Expected at least some successful connections")
	})
}

// TestOracleConnectionRetry tests Oracle connection retry mechanisms
func (suite *IntegrationTestSuite) TestOracleConnectionRetry() {
	connector := testutils.NewMockOracleConnector(suite.oracleServer)

	suite.Run("Successful connection without retry", func() {
		db, err := OpenOracle(connector, WithDefaultRetry())
		suite.NoError(err)
		suite.NotNil(db)

		err = db.Ping()
		suite.NoError(err)

		db.Close()
	})

	suite.Run("Connection with network failures", func() {
		// Enable network failures
		suite.oracleServer.SetErrorRate(0.7)
		suite.oracleServer.EnableNetworkFailures(true)

		config := &RetryConfig{
			MaxRetries:    10,
			InitialDelay:  5 * time.Millisecond,
			MaxDelay:      50 * time.Millisecond,
			BackoffFactor: 1.5,
		}

		db, err := OpenOracle(connector, WithRetry(config), WithPing())

		if err == nil {
			db.Close()
		}

		// Check that network failures were recorded
		errorHistory := suite.oracleServer.GetErrorHistory()
		suite.True(len(errorHistory) > 0, "Expected network failures to be recorded")
	})

	suite.Run("Session timeout recovery", func() {
		suite.oracleServer.EnableSessionTimeouts(true)

		db, err := OpenOracle(connector, WithDefaultRetry())
		suite.NoError(err)
		defer db.Close()

		// Initial ping should succeed
		err = db.Ping()
		suite.NoError(err)

		// Wait for session timeout (simplified simulation)
		time.Sleep(100 * time.Millisecond)

		// Ping again - might fail due to timeout but should be retryable
		retryDB := NewRetryDB(db, DefaultRetryConfig())
		err = retryDB.Ping()
		// Error is acceptable here as we're testing the retry mechanism
	})
}

// TestRetryDBOperations tests RetryDB wrapper operations
func (suite *IntegrationTestSuite) TestRetryDBOperations() {
	connector := testutils.NewMockMySQLConnector(suite.mysqlServer)

	db, err := OpenMySQL(connector, WithDefaultRetry())
	suite.NoError(err)
	defer db.Close()

	retryDB := NewRetryDB(db, &RetryConfig{
		MaxRetries:    3,
		InitialDelay:  10 * time.Millisecond,
		MaxDelay:      100 * time.Millisecond,
		BackoffFactor: 2.0,
	})

	suite.Run("Query operations with retry", func() {
		// Enable intermittent errors
		suite.mysqlServer.SetErrorRate(0.4)
		suite.mysqlServer.EnableConnectionErrors(true)

		ctx := context.Background()

		// Test query execution with retries
		rows, err := retryDB.QueryContext(ctx, "SELECT * FROM test_table")
		if err == nil {
			rows.Close()
		}

		// Test exec operations with retries
		_, err = retryDB.ExecContext(ctx, "INSERT INTO test_table (name) VALUES (?)", "test")
		// Error is acceptable here as we're testing the retry mechanism
	})

	suite.Run("Transaction operations with retry", func() {
		suite.mysqlServer.SetErrorRate(0.3)
		suite.mysqlServer.EnableLockTimeouts(true)

		ctx := context.Background()

		tx, err := retryDB.BeginTx(ctx, nil)
		if err == nil {
			// Perform some operations
			_, err = tx.ExecContext(ctx, "UPDATE test_table SET name = ? WHERE id = ?", "updated", 1)

			if err != nil {
				tx.Rollback()
			} else {
				tx.Commit()
			}
		}

		// Check that lock timeouts were handled
		errorHistory := suite.mysqlServer.GetErrorHistory()
		suite.True(len(errorHistory) >= 0, "Error history should be accessible")
	})
}

// TestNetworkSimulationIntegration tests integration with network simulation
func (suite *IntegrationTestSuite) TestNetworkSimulationIntegration() {
	tester := testutils.NewNetworkConnectivityTester(suite.networkSim)

	suite.Run("Network latency simulation", func() {
		suite.networkSim.SetLatencyRange(50*time.Millisecond, 200*time.Millisecond)
		suite.networkSim.SetCondition(testutils.NetworkSlow)

		ctx := context.Background()

		// Test multiple operations
		for i := 0; i < 5; i++ {
			_ = tester.TestConnectivity(ctx, "test-host", "ping", 2)
			// Some operations should succeed despite latency
		}

		// Check that latency was applied
		events := suite.networkSim.GetEvents()
		suite.True(len(events) > 0, "Expected network events to be recorded")

		// Check success rate
		successRate := tester.GetSuccessRate("test-host", "ping")
		suite.True(successRate >= 0.0 && successRate <= 1.0, "Success rate should be between 0 and 1")
	})

	suite.Run("Network partition simulation", func() {
		suite.networkSim.SetCondition(testutils.NetworkPartitioned)

		ctx := context.Background()

		result := tester.TestConnectivity(ctx, "partitioned-host", "query", 3)

		// Operations might fail due to partition
		if !result.Success {
			suite.Contains(result.ErrorMessage, "timeout", "Expected timeout-related error during partition")
		}

		// Restore network
		suite.networkSim.SetCondition(testutils.NetworkHealthy)

		// Operations should succeed again
		result = tester.TestConnectivity(ctx, "partitioned-host", "query", 1)
		// Result may vary based on simulation
	})
}

// TestChaosEngineeringIntegration tests integration with chaos engineering
func (suite *IntegrationTestSuite) TestChaosEngineeringIntegration() {
	suite.Run("Database connection chaos experiment", func() {
		experiment := testutils.CreateDatabaseConnectionChaos()
		experiment.Duration = 5 * time.Second // Short duration for testing

		suite.chaosEngine.AddExperiment(experiment)

		err := suite.chaosEngine.StartExperiment(experiment.ID)
		suite.NoError(err)

		// Let the experiment run
		time.Sleep(6 * time.Second)

		// Check experiment results
		expResult, err := suite.chaosEngine.GetExperiment(experiment.ID)
		suite.NoError(err)
		suite.True(expResult.Metrics.ExperimentsRun > 0, "Expected experiment to have run")

		// Check event log
		events := suite.chaosEngine.GetEventLog()
		suite.True(len(events) > 0, "Expected chaos events to be recorded")
	})

	suite.Run("Mixed chaos experiment", func() {
		experiment := testutils.CreateMixedChaos()
		experiment.Duration = 3 * time.Second // Short duration for testing

		suite.chaosEngine.AddExperiment(experiment)

		err := suite.chaosEngine.StartExperiment(experiment.ID)
		suite.NoError(err)

		// Test database operations during chaos
		connector := testutils.NewMockMySQLConnector(suite.mysqlServer)
		db, err := OpenMySQL(connector, WithDefaultRetry())
		if err == nil {
			defer db.Close()

			retryDB := NewRetryDB(db, &RetryConfig{
				MaxRetries:    5,
				InitialDelay:  10 * time.Millisecond,
				MaxDelay:      200 * time.Millisecond,
				BackoffFactor: 2.0,
			})

			// Perform operations during chaos
			for i := 0; i < 10; i++ {
				_ = retryDB.Ping()
				time.Sleep(100 * time.Millisecond)
			}
		}

		// Wait for experiment to complete
		time.Sleep(4 * time.Second)

		// Generate report
		report := suite.chaosEngine.GenerateReport()
		suite.True(report.TotalExperiments > 0, "Expected experiments in report")
		suite.True(len(report.Recommendations) >= 0, "Expected recommendations to be generated")
	})
}

// TestFailurePatterns tests predefined failure patterns
func (suite *IntegrationTestSuite) TestFailurePatterns() {
	suite.Run("Cascading failure pattern", func() {
		pattern := testutils.CreateCascadingFailurePattern()

		// Reduce duration for testing
		for i := range pattern.Steps {
			pattern.Steps[i].Duration = 500 * time.Millisecond
		}

		suite.networkSim.AddFailurePattern(pattern)

		err := suite.networkSim.StartFailurePattern(pattern.Name)
		suite.NoError(err)

		// Monitor network conditions during pattern execution
		initialCondition := suite.networkSim.GetCondition()

		// Wait for pattern to execute some steps
		time.Sleep(2 * time.Second)

		currentCondition := suite.networkSim.GetCondition()

		// Condition might have changed during pattern execution
		_ = initialCondition
		_ = currentCondition

		suite.networkSim.StopFailurePattern()

		// Check that events were recorded
		events := suite.networkSim.GetEvents()
		suite.True(len(events) > 0, "Expected pattern events to be recorded")
	})

	suite.Run("Intermittent failure pattern", func() {
		pattern := testutils.CreateIntermittentFailurePattern()
		pattern.RepeatCount = 1 // Limit repeats for testing

		// Reduce duration for testing
		for i := range pattern.Steps {
			pattern.Steps[i].Duration = 200 * time.Millisecond
		}

		suite.networkSim.AddFailurePattern(pattern)

		err := suite.networkSim.StartFailurePattern(pattern.Name)
		suite.NoError(err)

		// Test connectivity during intermittent failures
		tester := testutils.NewNetworkConnectivityTester(suite.networkSim)
		ctx := context.Background()

		successCount := 0
		for i := 0; i < 10; i++ {
			result := tester.TestConnectivity(ctx, "test-host", "operation", 1)
			if result.Success {
				successCount++
			}
			time.Sleep(100 * time.Millisecond)
		}

		suite.networkSim.StopFailurePattern()

		// Some operations should succeed even with intermittent failures
		successRate := float64(successCount) / 10.0
		suite.True(successRate >= 0.0 && successRate <= 1.0, "Success rate should be valid")
	})
}

// TestEndToEndScenarios tests complete end-to-end scenarios
func (suite *IntegrationTestSuite) TestEndToEndScenarios() {
	suite.Run("Database resilience under network chaos", func() {
		// Set up network chaos
		suite.networkSim.SetCondition(testutils.NetworkUnstable)
		suite.networkSim.SetLatencyRange(100*time.Millisecond, 1*time.Second)
		suite.networkSim.SetPacketLoss(0.1) // 10% packet loss

		// Set up database with errors
		suite.mysqlServer.SetErrorRate(0.3)
		suite.mysqlServer.EnableConnectionErrors(true)
		suite.mysqlServer.EnableNetworkFailures(true)

		// Create database connection with robust retry config
		connector := testutils.NewMockMySQLConnector(suite.mysqlServer)

		config := &RetryConfig{
			MaxRetries:    10,
			InitialDelay:  50 * time.Millisecond,
			MaxDelay:      2 * time.Second,
			BackoffFactor: 1.8,
		}

		db, err := OpenMySQL(connector, WithRetry(config))
		if err != nil {
			suite.T().Skipf("Could not establish initial connection: %v", err)
		}
		defer db.Close()

		retryDB := NewRetryDB(db, config)

		// Perform operations under chaotic conditions
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		successCount := 0
		errorCount := 0

		for i := 0; i < 20; i++ {
			select {
			case <-ctx.Done():
				break
			default:
			}

			// Test various operations
			operations := []func() error{
				func() error { return retryDB.PingContext(ctx) },
				func() error {
					rows, err := retryDB.QueryContext(ctx, "SELECT 1")
					if err != nil {
						return err
					}
					rows.Close()
					return nil
				},
				func() error {
					_, err := retryDB.ExecContext(ctx, "SELECT 1")
					return err
				},
			}

			for _, op := range operations {
				err := op()
				if err != nil {
					errorCount++
				} else {
					successCount++
				}

				time.Sleep(100 * time.Millisecond)
			}
		}

		// Calculate metrics
		totalOps := successCount + errorCount
		if totalOps > 0 {
			successRate := float64(successCount) / float64(totalOps)
			suite.T().Logf("Success rate under chaos: %.2f%% (%d/%d)", successRate*100, successCount, totalOps)

			// Even under chaotic conditions, some operations should succeed due to retries
			suite.True(successCount > 0, "Expected at least some operations to succeed with retry mechanism")
		}

		// Check that the system recorded the chaos properly
		networkEvents := suite.networkSim.GetEvents()
		mysqlErrors := suite.mysqlServer.GetErrorHistory()

		suite.True(len(networkEvents) > 0 || len(mysqlErrors) > 0,
			"Expected either network events or MySQL errors to be recorded")
	})

	suite.Run("Recovery after total outage", func() {
		// Simulate total outage
		suite.networkSim.SetCondition(testutils.NetworkOffline)
		suite.mysqlServer.Stop()

		connector := testutils.NewMockMySQLConnector(suite.mysqlServer)

		// Attempt connection during outage
		db, err := OpenMySQL(connector, WithDefaultRetry(), WithPingTimeout(1))
		suite.Error(err, "Expected connection to fail during outage")

		// Restore services
		suite.mysqlServer.Start()
		suite.networkSim.SetCondition(testutils.NetworkHealthy)

		// Connection should succeed after restoration
		db, err = OpenMySQL(connector, WithDefaultRetry(), WithPingTimeout(5))
		if err == nil {
			suite.NotNil(db)
			err = db.Ping()
			suite.NoError(err, "Expected ping to succeed after restoration")
			db.Close()
		}
	})
}

// TestPerformanceUnderChaos tests performance characteristics under chaotic conditions
func (suite *IntegrationTestSuite) TestPerformanceUnderChaos() {
	suite.Run("Latency measurement under various conditions", func() {
		connector := testutils.NewMockMySQLConnector(suite.mysqlServer)

		conditions := []struct {
			name      string
			setup     func()
			threshold time.Duration
		}{
			{
				name: "healthy_network",
				setup: func() {
					suite.networkSim.SetCondition(testutils.NetworkHealthy)
					suite.mysqlServer.SetErrorRate(0.0)
				},
				threshold: 100 * time.Millisecond,
			},
			{
				name: "slow_network",
				setup: func() {
					suite.networkSim.SetCondition(testutils.NetworkSlow)
					suite.networkSim.SetLatencyRange(50*time.Millisecond, 200*time.Millisecond)
					suite.mysqlServer.SetErrorRate(0.0)
				},
				threshold: 500 * time.Millisecond,
			},
			{
				name: "unstable_network",
				setup: func() {
					suite.networkSim.SetCondition(testutils.NetworkUnstable)
					suite.mysqlServer.SetErrorRate(0.2)
				},
				threshold: 1 * time.Second,
			},
		}

		for _, condition := range conditions {
			suite.Run(condition.name, func() {
				condition.setup()

				db, err := OpenMySQL(connector, WithDefaultRetry())
				if err != nil {
					suite.T().Skipf("Could not establish connection for %s: %v", condition.name, err)
				}
				defer db.Close()

				retryDB := NewRetryDB(db, DefaultRetryConfig())

				// Measure operation latencies
				var totalLatency time.Duration
				successfulOps := 0

				for i := 0; i < 10; i++ {
					start := time.Now()
					err := retryDB.Ping()
					latency := time.Since(start)

					if err == nil {
						totalLatency += latency
						successfulOps++
					}
				}

				if successfulOps > 0 {
					avgLatency := totalLatency / time.Duration(successfulOps)
					suite.T().Logf("Average latency for %s: %v", condition.name, avgLatency)

					// Latency should be reasonable even under adverse conditions
					suite.True(avgLatency < condition.threshold*2,
						"Average latency should be within reasonable bounds")
				}
			})
		}
	})
}

// Helper functions for integration tests

// createTestDatabase creates a test database (when using real databases)
func createTestDatabase(dsn string) (*sql.DB, error) {
	// This would be used with real databases like MySQL or Oracle
	// For now, we're using mock servers
	return nil, fmt.Errorf("real database testing not implemented in this example")
}

// loadTestData loads test data into the database
func loadTestData(db *sql.DB, sqlFile string) error {
	// This would load SQL from test data files
	// For now, we're using mock servers that don't require real data
	return nil
}

// waitForDatabaseReady waits for database to be ready
func waitForDatabaseReady(db *sql.DB, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			if err := db.Ping(); err == nil {
				return nil
			}
		}
	}
}

// TestIntegrationSuite runs the integration test suite
func TestIntegrationSuite(t *testing.T) {
	// Skip integration tests if not explicitly requested
	if os.Getenv("RUN_INTEGRATION_TESTS") == "" {
		t.Skip("Skipping integration tests. Set RUN_INTEGRATION_TESTS environment variable to run.")
	}

	suite.Run(t, new(IntegrationTestSuite))
}

// RealDatabaseTestResult represents the result of a real database test
type RealDatabaseTestResult struct {
	DatabaseType   string        `json:"database_type"`
	TestName       string        `json:"test_name"`
	StartTime      time.Time     `json:"start_time"`
	Duration       time.Duration `json:"duration"`
	Success        bool          `json:"success"`
	RetryCount     int           `json:"retry_count"`
	ErrorMessage   string        `json:"error_message,omitempty"`
	OperationCount int           `json:"operation_count"`
	ThroughputOps  float64       `json:"throughput_ops"`
}

// RealDatabaseTestSuite manages real database testing
type RealDatabaseTestSuite struct {
	t         *testing.T
	mysqlDB   *sql.DB
	oracleDB  *sql.DB
	results   []RealDatabaseTestResult
	startTime time.Time
}

// NewRealDatabaseTestSuite creates a new real database test suite
func NewRealDatabaseTestSuite(t *testing.T) *RealDatabaseTestSuite {
	return &RealDatabaseTestSuite{
		t:         t,
		results:   make([]RealDatabaseTestResult, 0),
		startTime: time.Now(),
	}
}

// initializeMySQL sets up MySQL database for testing
func (suite *RealDatabaseTestSuite) initializeMySQL(dsn string) error {
	// Parse MySQL DSN
	cfg, err := mysql.ParseDSN(dsn)
	if err != nil {
		return fmt.Errorf("failed to parse MySQL DSN: %w", err)
	}

	// Set reasonable timeouts for testing
	cfg.Timeout = 10 * time.Second
	cfg.ReadTimeout = 10 * time.Second
	cfg.WriteTimeout = 10 * time.Second

	// Create connector using project's unified interface
	connector, err := mysql.NewConnector(cfg)
	if err != nil {
		return fmt.Errorf("failed to create MySQL connector: %w", err)
	}

	// Use project's OpenMySQL function with retry configuration
	suite.mysqlDB, err = OpenMySQL(connector, WithDefaultRetry(), WithPing())
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// Test basic connectivity before proceeding
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = suite.mysqlDB.PingContext(ctx)
	if err != nil {
		return fmt.Errorf("failed to ping MySQL database: %w", err)
	}

	// Execute test data setup
	return suite.executeSQLScript(suite.mysqlDB, "testdata/mysql_test_data.sql")
}

// initializeOracle sets up Oracle database for testing
func (suite *RealDatabaseTestSuite) initializeOracle(dsn string) error {
	// Parse Oracle DSN using project's function
	params, err := ParseGoDrorDSN(dsn)
	if err != nil {
		return fmt.Errorf("failed to parse Oracle DSN: %w", err)
	}

	// Create connector using project's unified interface
	connector := NewGoDrorConnector(params)

	// Use project's OpenOracle function with retry configuration
	suite.oracleDB, err = OpenOracle(connector, WithDefaultRetry(), WithPing())
	if err != nil {
		return fmt.Errorf("failed to open Oracle connection: %w", err)
	}

	// Execute test data setup
	return suite.executeSQLScript(suite.oracleDB, "testdata/oracle_test_data.sql")
}

// executeSQLScript executes SQL script from file directly
func (suite *RealDatabaseTestSuite) executeSQLScript(db *sql.DB, filename string) error {
	sqlData, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read SQL script %s: %w", filename, err)
	}

	// Execute SQL statements directly - testdata files are now optimized
	return suite.executeSQL(db, string(sqlData))
}

// executeSQL executes SQL statements directly with basic parsing
func (suite *RealDatabaseTestSuite) executeSQL(db *sql.DB, sqlContent string) error {
	// Split SQL content into individual statements
	statements := suite.splitSQLStatements(sqlContent)

	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		_, err := db.Exec(stmt)
		if err != nil {
			// Log warning but continue for optional statements
			suite.t.Logf("Warning: Failed to execute statement %d: %v", i+1, err)
			suite.t.Logf("Statement: %s", stmt)
		}
	}

	// Verify basic table structure exists
	return suite.verifyTestEnvironment(db)
}

// splitSQLStatements splits SQL content into individual statements
func (suite *RealDatabaseTestSuite) splitSQLStatements(sqlContent string) []string {
	var statements []string

	// Split by lines and build statements
	lines := strings.Split(sqlContent, "\n")
	var currentStatement strings.Builder

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// Skip comments and empty lines
		if line == "" || strings.HasPrefix(line, "--") {
			continue
		}

		// Add line to current statement
		currentStatement.WriteString(line)
		currentStatement.WriteString(" ")

		// Check if statement is complete
		if strings.HasSuffix(line, ";") {
			stmt := strings.TrimSpace(currentStatement.String())
			if stmt != "" {
				statements = append(statements, stmt)
			}
			currentStatement.Reset()
		}
	}

	// Add final statement if any
	if currentStatement.Len() > 0 {
		stmt := strings.TrimSpace(currentStatement.String())
		if stmt != "" {
			statements = append(statements, stmt)
		}
	}

	return statements
}

// verifyTestEnvironment checks if basic test tables exist and have data
func (suite *RealDatabaseTestSuite) verifyTestEnvironment(db *sql.DB) error {
	// Check if test tables exist and have data
	tables := []string{"retry_test_users", "retry_test_orders", "retry_test_products"}

	for _, table := range tables {
		var count int
		query := fmt.Sprintf("SELECT COUNT(*) FROM %s", table)
		err := db.QueryRow(query).Scan(&count)
		if err != nil {
			// If table doesn't exist, that's a problem with testdata execution
			suite.t.Logf("Warning: Table %s does not exist or is not accessible: %v", table, err)
			continue
		}
		suite.t.Logf("Table %s has %d records", table, count)
	}

	return nil
}

// testConnectionRetry tests database connection retry mechanisms
func (suite *RealDatabaseTestSuite) testConnectionRetry(db *sql.DB, dbType string) RealDatabaseTestResult {
	result := RealDatabaseTestResult{
		DatabaseType: dbType,
		TestName:     "connection_retry_test",
		StartTime:    time.Now(),
	}

	// Test basic connectivity with retry
	maxRetries := 5
	for attempt := 0; attempt < maxRetries; attempt++ {
		err := db.Ping()
		if err == nil {
			result.Success = true
			result.RetryCount = attempt
			break
		}
		result.ErrorMessage = err.Error()
		result.RetryCount = attempt + 1

		if attempt < maxRetries-1 {
			time.Sleep(100 * time.Millisecond)
		}
	}

	result.Duration = time.Since(result.StartTime)
	result.OperationCount = result.RetryCount + 1

	if result.Duration.Seconds() > 0 {
		result.ThroughputOps = float64(result.OperationCount) / result.Duration.Seconds()
	}

	return result
}

// testQueryRetry tests query execution with retry mechanisms
func (suite *RealDatabaseTestSuite) testQueryRetry(db *sql.DB, dbType string) RealDatabaseTestResult {
	result := RealDatabaseTestResult{
		DatabaseType: dbType,
		TestName:     "query_retry_test",
		StartTime:    time.Now(),
	}

	var query string
	switch dbType {
	case "mysql":
		query = "SELECT COUNT(*) FROM retry_test_users"
	case "oracle":
		query = "SELECT COUNT(*) FROM retry_test_users"
	}

	// Execute query with retry logic
	maxRetries := 3
	var count int
	for attempt := 0; attempt < maxRetries; attempt++ {
		err := db.QueryRow(query).Scan(&count)
		if err == nil {
			result.Success = true
			result.RetryCount = attempt
			break
		}
		result.ErrorMessage = err.Error()
		result.RetryCount = attempt + 1

		if attempt < maxRetries-1 {
			time.Sleep(50 * time.Millisecond)
		}
	}

	result.Duration = time.Since(result.StartTime)
	result.OperationCount = result.RetryCount + 1

	if result.Duration.Seconds() > 0 {
		result.ThroughputOps = float64(result.OperationCount) / result.Duration.Seconds()
	}

	return result
}

// testHighLoadPerformance tests database performance under high load
func (suite *RealDatabaseTestSuite) testHighLoadPerformance(db *sql.DB, dbType string) RealDatabaseTestResult {
	result := RealDatabaseTestResult{
		DatabaseType: dbType,
		TestName:     "high_load_performance_test",
		StartTime:    time.Now(),
	}

	const operationCount = 100
	const concurrency = 5
	var successCount int64
	var errorCount int64

	var wg sync.WaitGroup
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for j := 0; j < operationCount/concurrency; j++ {
				var query string
				switch dbType {
				case "mysql":
					query = "SELECT id, name FROM retry_test_users LIMIT 1"
				case "oracle":
					query = "SELECT id, name FROM retry_test_users WHERE ROWNUM <= 1"
				}

				var id int
				var name string
				err := db.QueryRow(query).Scan(&id, &name)
				if err != nil {
					atomic.AddInt64(&errorCount, 1)
				} else {
					atomic.AddInt64(&successCount, 1)
				}
			}
		}(i)
	}

	wg.Wait()

	result.Duration = time.Since(result.StartTime)
	result.OperationCount = operationCount
	result.Success = errorCount == 0
	if !result.Success {
		result.ErrorMessage = fmt.Sprintf("%d operations failed out of %d", errorCount, operationCount)
	}

	if result.Duration.Seconds() > 0 {
		result.ThroughputOps = float64(operationCount) / result.Duration.Seconds()
	}

	return result
}

// addResult adds a test result to the suite
func (suite *RealDatabaseTestSuite) addResult(result RealDatabaseTestResult) {
	suite.results = append(suite.results, result)
}

// generateReport generates a comprehensive test report
func (suite *RealDatabaseTestSuite) generateReport() {
	totalTests := len(suite.results)
	successfulTests := 0
	totalDuration := time.Duration(0)
	totalOperations := 0

	for _, result := range suite.results {
		if result.Success {
			successfulTests++
		}
		totalDuration += result.Duration
		totalOperations += result.OperationCount
	}

	suite.t.Logf("\n=== Real Database Test Report ===")
	suite.t.Logf("Total Tests: %d", totalTests)
	suite.t.Logf("Successful Tests: %d", successfulTests)
	suite.t.Logf("Success Rate: %.2f%%", float64(successfulTests)/float64(totalTests)*100)
	suite.t.Logf("Total Duration: %v", totalDuration)
	suite.t.Logf("Total Operations: %d", totalOperations)

	if totalDuration.Seconds() > 0 {
		suite.t.Logf("Overall Throughput: %.2f ops/sec", float64(totalOperations)/totalDuration.Seconds())
	}

	suite.t.Logf("\nDetailed Results:")
	for _, result := range suite.results {
		status := "✓ PASS"
		if !result.Success {
			status = "✗ FAIL"
		}
		suite.t.Logf("  %s %s (%s) - Duration: %v, Ops: %d, Throughput: %.2f ops/sec",
			status, result.TestName, result.DatabaseType, result.Duration,
			result.OperationCount, result.ThroughputOps)
		if !result.Success && result.ErrorMessage != "" {
			suite.t.Logf("    Error: %s", result.ErrorMessage)
		}
	}
}

// cleanup closes database connections
func (suite *RealDatabaseTestSuite) cleanup() {
	if suite.mysqlDB != nil {
		suite.mysqlDB.Close()
	}
	if suite.oracleDB != nil {
		suite.oracleDB.Close()
	}
}

// TestRealMySQLIntegration tests against a real MySQL database
func TestRealMySQLIntegration(t *testing.T) {
	dsn := os.Getenv("MYSQL_DSN")
	if dsn == "" {
		t.Skip("Skipping real MySQL test. Set MYSQL_DSN environment variable to run.")
	}

	suite := NewRealDatabaseTestSuite(t)
	defer suite.cleanup()

	// Initialize MySQL database
	err := suite.initializeMySQL(dsn)
	require.NoError(t, err, "Failed to initialize MySQL database")

	t.Run("Connection Retry", func(t *testing.T) {
		result := suite.testConnectionRetry(suite.mysqlDB, "mysql")
		suite.addResult(result)
		assert.True(t, result.Success, "MySQL connection retry should succeed")
	})

	t.Run("Query Retry", func(t *testing.T) {
		result := suite.testQueryRetry(suite.mysqlDB, "mysql")
		suite.addResult(result)
		assert.True(t, result.Success, "MySQL query retry should succeed")
	})

	t.Run("High Load Performance", func(t *testing.T) {
		result := suite.testHighLoadPerformance(suite.mysqlDB, "mysql")
		suite.addResult(result)
		// Allow some failures in high load test, but throughput should be reasonable
		assert.Greater(t, result.ThroughputOps, 10.0, "MySQL should maintain reasonable throughput under load")
	})

	// Generate comprehensive report
	suite.generateReport()
}

// TestRealOracleIntegration tests against a real Oracle database
func TestRealOracleIntegration(t *testing.T) {
	dsn := os.Getenv("ORACLE_DSN")
	if dsn == "" {
		t.Skip("Skipping real Oracle test. Set ORACLE_DSN environment variable to run.")
	}

	suite := NewRealDatabaseTestSuite(t)
	defer suite.cleanup()

	// Initialize Oracle database
	err := suite.initializeOracle(dsn)
	require.NoError(t, err, "Failed to initialize Oracle database")

	t.Run("Connection Retry", func(t *testing.T) {
		result := suite.testConnectionRetry(suite.oracleDB, "oracle")
		suite.addResult(result)
		assert.True(t, result.Success, "Oracle connection retry should succeed")
	})

	t.Run("Query Retry", func(t *testing.T) {
		result := suite.testQueryRetry(suite.oracleDB, "oracle")
		suite.addResult(result)
		assert.True(t, result.Success, "Oracle query retry should succeed")
	})

	t.Run("High Load Performance", func(t *testing.T) {
		result := suite.testHighLoadPerformance(suite.oracleDB, "oracle")
		suite.addResult(result)
		// Allow some failures in high load test, but throughput should be reasonable
		assert.Greater(t, result.ThroughputOps, 5.0, "Oracle should maintain reasonable throughput under load")
	})

	// Generate comprehensive report
	suite.generateReport()
}

// Benchmark integration tests
func BenchmarkIntegrationRetryMechanisms(b *testing.B) {
	if os.Getenv("RUN_INTEGRATION_BENCHMARKS") == "" {
		b.Skip("Skipping integration benchmarks. Set RUN_INTEGRATION_BENCHMARKS environment variable to run.")
	}

	// Set up mock server
	mockServer := testutils.NewMockMySQLServer()
	mockServer.Start()
	defer mockServer.Stop()

	connector := testutils.NewMockMySQLConnector(mockServer)

	b.Run("connection_establishment", func(b *testing.B) {
		mockServer.SetErrorRate(0.1) // 10% error rate

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			db, err := OpenMySQL(connector, WithDefaultRetry())
			if err == nil {
				db.Close()
			}
		}
	})

	b.Run("ping_operations", func(b *testing.B) {
		mockServer.SetErrorRate(0.2) // 20% error rate

		db, err := OpenMySQL(connector, WithDefaultRetry())
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, DefaultRetryConfig())

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			retryDB.Ping()
		}
	})

	b.Run("query_operations", func(b *testing.B) {
		mockServer.SetErrorRate(0.15) // 15% error rate

		db, err := OpenMySQL(connector, WithDefaultRetry())
		require.NoError(b, err)
		defer db.Close()

		retryDB := NewRetryDB(db, DefaultRetryConfig())

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			rows, err := retryDB.Query("SELECT 1")
			if err == nil {
				rows.Close()
			}
		}
	})
}
