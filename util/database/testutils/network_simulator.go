package testutils

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// NetworkCondition represents different network conditions that can be simulated
type NetworkCondition int

const (
	NetworkHealthy NetworkCondition = iota
	NetworkSlow
	NetworkUnstable
	NetworkPartitioned
	NetworkOffline
)

// NetworkEvent represents a network event that occurred during simulation
type NetworkEvent struct {
	Timestamp time.Time
	EventType string
	Details   string
	Duration  time.Duration
}

// NetworkSimulator simulates various network conditions for database connections
type NetworkSimulator struct {
	mu               sync.RWMutex
	condition        NetworkCondition
	isActive         bool
	baseLatency      time.Duration
	jitterRange      time.Duration
	minLatency       time.Duration
	maxLatency       time.Duration
	useLatencyRange  bool // true if using min/max range, false if using base+jitter
	packetLossRate   float64
	disconnectRate   float64
	reconnectDelay   time.Duration
	bandwidthLimit   int64 // bytes per second
	events           []NetworkEvent
	partitionedHosts map[string]bool
	slowHosts        map[string]bool
	failurePatterns  []FailurePattern
	currentPattern   int
}

// FailurePattern defines a pattern of network failures over time
type FailurePattern struct {
	Name        string
	Steps       []PatternStep
	RepeatCount int
	Description string
}

// PatternStep represents a single step in a failure pattern
type PatternStep struct {
	Condition   NetworkCondition
	Duration    time.Duration
	Probability float64 // 0.0 to 1.0
	Description string
}

// NewNetworkSimulator creates a new network simulator
func NewNetworkSimulator() *NetworkSimulator {
	return &NetworkSimulator{
		condition:        NetworkHealthy,
		isActive:         false,
		baseLatency:      10 * time.Millisecond,
		jitterRange:      5 * time.Millisecond,
		minLatency:       0,
		maxLatency:       0,
		useLatencyRange:  false, // Default to base+jitter model
		packetLossRate:   0.0,
		disconnectRate:   0.0,
		reconnectDelay:   1 * time.Second,
		bandwidthLimit:   0, // Unlimited
		events:           make([]NetworkEvent, 0),
		partitionedHosts: make(map[string]bool),
		slowHosts:        make(map[string]bool),
		failurePatterns:  make([]FailurePattern, 0),
		currentPattern:   -1,
	}
}

// Start activates the network simulator
func (ns *NetworkSimulator) Start() {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.isActive = true
	ns.recordEvent("simulator_start", "Network simulator activated", 0)
}

// Stop deactivates the network simulator
func (ns *NetworkSimulator) Stop() {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.isActive = false
	ns.recordEvent("simulator_stop", "Network simulator deactivated", 0)
}

// IsActive returns whether the simulator is currently active
func (ns *NetworkSimulator) IsActive() bool {
	ns.mu.RLock()
	defer ns.mu.RUnlock()
	return ns.isActive
}

// SetCondition sets the current network condition
func (ns *NetworkSimulator) SetCondition(condition NetworkCondition) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	oldCondition := ns.condition
	ns.condition = condition
	ns.recordEvent("condition_change",
		fmt.Sprintf("Changed from %s to %s", ns.conditionString(oldCondition), ns.conditionString(condition)), 0)
}

// GetCondition returns the current network condition
func (ns *NetworkSimulator) GetCondition() NetworkCondition {
	ns.mu.RLock()
	defer ns.mu.RUnlock()
	return ns.condition
}

// SetLatency sets the base latency and jitter range
func (ns *NetworkSimulator) SetLatency(base, jitter time.Duration) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.baseLatency = base
	ns.jitterRange = jitter
	ns.useLatencyRange = false
	ns.recordEvent("latency_change",
		fmt.Sprintf("Base: %v, Jitter: %v", base, jitter), 0)
}

// SetLatencyRange sets the latency range using min and max values
func (ns *NetworkSimulator) SetLatencyRange(min, max time.Duration) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.minLatency = min
	ns.maxLatency = max
	ns.useLatencyRange = true
	ns.recordEvent("latency_range_change",
		fmt.Sprintf("Min: %v, Max: %v", min, max), 0)
}

// SetPacketLoss sets the packet loss rate (0.0 to 1.0)
func (ns *NetworkSimulator) SetPacketLoss(rate float64) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.packetLossRate = rate
	ns.recordEvent("packet_loss_change",
		fmt.Sprintf("Rate: %.2f%%", rate*100), 0)
}

// SetDisconnectRate sets the random disconnection rate (0.0 to 1.0)
func (ns *NetworkSimulator) SetDisconnectRate(rate float64) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.disconnectRate = rate
	ns.recordEvent("disconnect_rate_change",
		fmt.Sprintf("Rate: %.2f%%", rate*100), 0)
}

// SetReconnectDelay sets the delay before reconnection is allowed
func (ns *NetworkSimulator) SetReconnectDelay(delay time.Duration) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.reconnectDelay = delay
	ns.recordEvent("reconnect_delay_change",
		fmt.Sprintf("Delay: %v", delay), 0)
}

// SetBandwidthLimit sets the bandwidth limit in bytes per second (0 for unlimited)
func (ns *NetworkSimulator) SetBandwidthLimit(bytesPerSecond int64) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.bandwidthLimit = bytesPerSecond
	ns.recordEvent("bandwidth_change",
		fmt.Sprintf("Limit: %d bytes/sec", bytesPerSecond), 0)
}

// PartitionHost simulates network partition for a specific host
func (ns *NetworkSimulator) PartitionHost(host string) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.partitionedHosts[host] = true
	ns.recordEvent("host_partition",
		fmt.Sprintf("Host %s partitioned", host), 0)
}

// UnpartitionHost removes network partition for a specific host
func (ns *NetworkSimulator) UnpartitionHost(host string) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	delete(ns.partitionedHosts, host)
	ns.recordEvent("host_unpartition",
		fmt.Sprintf("Host %s unpartitioned", host), 0)
}

// SlowHost simulates slow network for a specific host
func (ns *NetworkSimulator) SlowHost(host string) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.slowHosts[host] = true
	ns.recordEvent("host_slow",
		fmt.Sprintf("Host %s marked as slow", host), 0)
}

// UnslowHost removes slow network simulation for a specific host
func (ns *NetworkSimulator) UnslowHost(host string) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	delete(ns.slowHosts, host)
	ns.recordEvent("host_unslow",
		fmt.Sprintf("Host %s unmarked as slow", host), 0)
}

// AddFailurePattern adds a failure pattern to the simulator
func (ns *NetworkSimulator) AddFailurePattern(pattern FailurePattern) {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.failurePatterns = append(ns.failurePatterns, pattern)
	ns.recordEvent("pattern_added",
		fmt.Sprintf("Pattern '%s' added", pattern.Name), 0)
}

// StartFailurePattern starts executing a specific failure pattern
func (ns *NetworkSimulator) StartFailurePattern(patternName string) error {
	ns.mu.Lock()
	defer ns.mu.Unlock()

	for i, pattern := range ns.failurePatterns {
		if pattern.Name == patternName {
			ns.currentPattern = i
			ns.recordEvent("pattern_started",
				fmt.Sprintf("Pattern '%s' started", patternName), 0)
			go ns.executePattern(pattern)
			return nil
		}
	}

	return fmt.Errorf("pattern '%s' not found", patternName)
}

// StopFailurePattern stops the currently executing failure pattern
func (ns *NetworkSimulator) StopFailurePattern() {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	if ns.currentPattern >= 0 {
		patternName := ns.failurePatterns[ns.currentPattern].Name
		ns.currentPattern = -1
		ns.recordEvent("pattern_stopped",
			fmt.Sprintf("Pattern '%s' stopped", patternName), 0)
	}
}

// SimulateOperation simulates a network operation with current conditions
func (ns *NetworkSimulator) SimulateOperation(ctx context.Context, host string, operation string) error {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	if !ns.isActive {
		return nil // Pass through without simulation
	}

	// Check if host is partitioned
	if ns.partitionedHosts[host] {
		ns.recordEvent("operation_blocked",
			fmt.Sprintf("Operation '%s' blocked for partitioned host %s", operation, host), 0)
		return errors.New("network is unreachable")
	}

	// Check global network condition
	switch ns.condition {
	case NetworkOffline:
		ns.recordEvent("operation_offline",
			fmt.Sprintf("Operation '%s' failed - network offline", operation), 0)
		return errors.New("connection refused")

	case NetworkPartitioned:
		if rand.Float64() < 0.8 { // 80% chance of failure during partition
			ns.recordEvent("operation_partitioned",
				fmt.Sprintf("Operation '%s' failed - network partitioned", operation), 0)
			return errors.New("connection timeout")
		}
	}

	// Simulate packet loss
	if rand.Float64() < ns.packetLossRate {
		ns.recordEvent("packet_lost",
			fmt.Sprintf("Packet lost for operation '%s'", operation), 0)
		return errors.New("i/o timeout")
	}

	// Simulate random disconnections
	if rand.Float64() < ns.disconnectRate {
		ns.recordEvent("random_disconnect",
			fmt.Sprintf("Random disconnect during operation '%s'", operation), 0)
		return errors.New("connection lost")
	}

	// Simulate latency
	latency := ns.calculateLatency(host)
	if latency > 0 {
		start := time.Now()
		timer := time.NewTimer(latency)
		defer timer.Stop()

		select {
		case <-timer.C:
			duration := time.Since(start)
			ns.recordEvent("latency_applied",
				fmt.Sprintf("Operation '%s' delayed by %v", operation, duration), duration)
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	// Simulate bandwidth limitations
	if ns.bandwidthLimit > 0 {
		// Simplified bandwidth simulation - add delay based on data size
		// In a real implementation, this would be more sophisticated
		estimatedDataSize := int64(1024) // Assume 1KB data size
		transferTime := time.Duration(estimatedDataSize * int64(time.Second) / ns.bandwidthLimit)
		if transferTime > 0 {
			timer := time.NewTimer(transferTime)
			defer timer.Stop()

			select {
			case <-timer.C:
				ns.recordEvent("bandwidth_delay",
					fmt.Sprintf("Operation '%s' bandwidth limited by %v", operation, transferTime), transferTime)
			case <-ctx.Done():
				return ctx.Err()
			}
		}
	}

	return nil
}

// GetEvents returns all recorded events
func (ns *NetworkSimulator) GetEvents() []NetworkEvent {
	ns.mu.RLock()
	defer ns.mu.RUnlock()
	events := make([]NetworkEvent, len(ns.events))
	copy(events, ns.events)
	return events
}

// ClearEvents clears all recorded events
func (ns *NetworkSimulator) ClearEvents() {
	ns.mu.Lock()
	defer ns.mu.Unlock()
	ns.events = ns.events[:0]
}

// GetStatistics returns network simulation statistics
func (ns *NetworkSimulator) GetStatistics() NetworkStatistics {
	ns.mu.RLock()
	defer ns.mu.RUnlock()

	stats := NetworkStatistics{
		TotalEvents:      len(ns.events),
		PartitionedHosts: len(ns.partitionedHosts),
		SlowHosts:        len(ns.slowHosts),
		CurrentCondition: ns.conditionString(ns.condition),
		IsActive:         ns.isActive,
	}

	// Count event types
	eventCounts := make(map[string]int)
	for _, event := range ns.events {
		eventCounts[event.EventType]++
	}
	stats.EventCounts = eventCounts

	return stats
}

// NetworkStatistics contains statistics about network simulation
type NetworkStatistics struct {
	TotalEvents      int
	PartitionedHosts int
	SlowHosts        int
	CurrentCondition string
	IsActive         bool
	EventCounts      map[string]int
}

// Helper methods

func (ns *NetworkSimulator) calculateLatency(host string) time.Duration {
	var latency time.Duration

	// Calculate base latency using the appropriate model
	if ns.useLatencyRange {
		// Use min/max range model with uniform distribution
		if ns.maxLatency > ns.minLatency {
			rangeDuration := ns.maxLatency - ns.minLatency
			randomOffset := time.Duration(rand.Int63n(int64(rangeDuration)))
			latency = ns.minLatency + randomOffset
		} else {
			latency = ns.minLatency
		}
	} else {
		// Use base + jitter model
		latency = ns.baseLatency

		// Add jitter
		if ns.jitterRange > 0 {
			jitter := time.Duration(rand.Int63n(int64(ns.jitterRange)))
			latency += jitter
		}
	}

	// Check for slow hosts
	if ns.slowHosts[host] {
		latency *= 5 // 5x slower for slow hosts
	}

	// Apply condition multipliers
	switch ns.condition {
	case NetworkSlow:
		latency *= 3
	case NetworkUnstable:
		// Random latency spikes
		if rand.Float64() < 0.3 { // 30% chance of spike
			latency *= time.Duration(2 + rand.Intn(8)) // 2-10x multiplier
		}
	}

	return latency
}

func (ns *NetworkSimulator) recordEvent(eventType, details string, duration time.Duration) {
	event := NetworkEvent{
		Timestamp: time.Now(),
		EventType: eventType,
		Details:   details,
		Duration:  duration,
	}
	ns.events = append(ns.events, event)

	// Limit event history to prevent memory growth
	if len(ns.events) > 10000 {
		ns.events = ns.events[1000:] // Keep last 9000 events
	}
}

func (ns *NetworkSimulator) conditionString(condition NetworkCondition) string {
	switch condition {
	case NetworkHealthy:
		return "Healthy"
	case NetworkSlow:
		return "Slow"
	case NetworkUnstable:
		return "Unstable"
	case NetworkPartitioned:
		return "Partitioned"
	case NetworkOffline:
		return "Offline"
	default:
		return "Unknown"
	}
}

func (ns *NetworkSimulator) executePattern(pattern FailurePattern) {
	for repeat := 0; repeat < pattern.RepeatCount || pattern.RepeatCount == 0; repeat++ {
		for _, step := range pattern.Steps {
			// Check if pattern was stopped
			ns.mu.RLock()
			if ns.currentPattern == -1 {
				ns.mu.RUnlock()
				return
			}
			ns.mu.RUnlock()

			// Apply step condition if probability check passes
			if rand.Float64() < step.Probability {
				ns.SetCondition(step.Condition)

				ns.mu.Lock()
				ns.recordEvent("pattern_step",
					fmt.Sprintf("Pattern step: %s (%s)", step.Description, ns.conditionString(step.Condition)), step.Duration)
				ns.mu.Unlock()
			}

			// Wait for step duration
			time.Sleep(step.Duration)
		}

		// If RepeatCount is 0, repeat indefinitely
		if pattern.RepeatCount == 0 {
			repeat = -1 // Reset counter to avoid overflow
		}
	}

	// Pattern completed, reset to healthy
	ns.SetCondition(NetworkHealthy)
}

// Predefined failure patterns

// CreateCascadingFailurePattern creates a pattern that simulates cascading failures
func CreateCascadingFailurePattern() FailurePattern {
	return FailurePattern{
		Name:        "cascading_failure",
		Description: "Simulates cascading failures with increasing severity",
		RepeatCount: 1,
		Steps: []PatternStep{
			{
				Condition:   NetworkSlow,
				Duration:    30 * time.Second,
				Probability: 1.0,
				Description: "Initial slowdown",
			},
			{
				Condition:   NetworkUnstable,
				Duration:    60 * time.Second,
				Probability: 1.0,
				Description: "Network becomes unstable",
			},
			{
				Condition:   NetworkPartitioned,
				Duration:    30 * time.Second,
				Probability: 0.7,
				Description: "Partial partition",
			},
			{
				Condition:   NetworkOffline,
				Duration:    15 * time.Second,
				Probability: 0.3,
				Description: "Complete outage",
			},
			{
				Condition:   NetworkSlow,
				Duration:    45 * time.Second,
				Probability: 1.0,
				Description: "Recovery phase",
			},
		},
	}
}

// CreateIntermittentFailurePattern creates a pattern with intermittent failures
func CreateIntermittentFailurePattern() FailurePattern {
	return FailurePattern{
		Name:        "intermittent_failure",
		Description: "Simulates intermittent network issues",
		RepeatCount: 0, // Repeat indefinitely
		Steps: []PatternStep{
			{
				Condition:   NetworkHealthy,
				Duration:    2 * time.Minute,
				Probability: 1.0,
				Description: "Normal operation",
			},
			{
				Condition:   NetworkSlow,
				Duration:    30 * time.Second,
				Probability: 0.8,
				Description: "Brief slowdown",
			},
			{
				Condition:   NetworkHealthy,
				Duration:    1 * time.Minute,
				Probability: 1.0,
				Description: "Recovery",
			},
			{
				Condition:   NetworkPartitioned,
				Duration:    10 * time.Second,
				Probability: 0.3,
				Description: "Brief partition",
			},
		},
	}
}

// CreateStressTestPattern creates a pattern for stress testing
func CreateStressTestPattern() FailurePattern {
	return FailurePattern{
		Name:        "stress_test",
		Description: "High-frequency failures for stress testing",
		RepeatCount: 10,
		Steps: []PatternStep{
			{
				Condition:   NetworkUnstable,
				Duration:    5 * time.Second,
				Probability: 1.0,
				Description: "Unstable burst",
			},
			{
				Condition:   NetworkHealthy,
				Duration:    2 * time.Second,
				Probability: 1.0,
				Description: "Brief recovery",
			},
			{
				Condition:   NetworkPartitioned,
				Duration:    3 * time.Second,
				Probability: 0.5,
				Description: "Random partition",
			},
			{
				Condition:   NetworkOffline,
				Duration:    1 * time.Second,
				Probability: 0.2,
				Description: "Quick outage",
			},
		},
	}
}

// NetworkConnectivityTester provides utilities to test network connectivity under various conditions
type NetworkConnectivityTester struct {
	simulator *NetworkSimulator
	results   []ConnectivityTestResult
	mu        sync.RWMutex
}

// ConnectivityTestResult represents the result of a connectivity test
type ConnectivityTestResult struct {
	Timestamp     time.Time
	Host          string
	Operation     string
	Success       bool
	Duration      time.Duration
	ErrorMessage  string
	RetryAttempts int
}

// NewNetworkConnectivityTester creates a new connectivity tester
func NewNetworkConnectivityTester(simulator *NetworkSimulator) *NetworkConnectivityTester {
	return &NetworkConnectivityTester{
		simulator: simulator,
		results:   make([]ConnectivityTestResult, 0),
	}
}

// TestConnectivity tests connectivity to a host with the given operation
func (tester *NetworkConnectivityTester) TestConnectivity(ctx context.Context, host, operation string, maxRetries int) ConnectivityTestResult {
	start := time.Now()

	result := ConnectivityTestResult{
		Timestamp:     start,
		Host:          host,
		Operation:     operation,
		Success:       false,
		Duration:      0,
		ErrorMessage:  "",
		RetryAttempts: 0,
	}

	for attempt := 0; attempt <= maxRetries; attempt++ {
		err := tester.simulator.SimulateOperation(ctx, host, operation)
		if err == nil {
			result.Success = true
			result.Duration = time.Since(start)
			result.RetryAttempts = attempt
			break
		}

		result.ErrorMessage = err.Error()
		result.RetryAttempts = attempt + 1

		// Wait before retry (except on last attempt)
		if attempt < maxRetries {
			time.Sleep(100 * time.Millisecond)
		}
	}

	if !result.Success {
		result.Duration = time.Since(start)
	}

	tester.mu.Lock()
	tester.results = append(tester.results, result)
	tester.mu.Unlock()

	return result
}

// GetResults returns all test results
func (tester *NetworkConnectivityTester) GetResults() []ConnectivityTestResult {
	tester.mu.RLock()
	defer tester.mu.RUnlock()
	results := make([]ConnectivityTestResult, len(tester.results))
	copy(results, tester.results)
	return results
}

// ClearResults clears all test results
func (tester *NetworkConnectivityTester) ClearResults() {
	tester.mu.Lock()
	defer tester.mu.Unlock()
	tester.results = tester.results[:0]
}

// GetSuccessRate returns the success rate for a specific host and operation
func (tester *NetworkConnectivityTester) GetSuccessRate(host, operation string) float64 {
	tester.mu.RLock()
	defer tester.mu.RUnlock()

	var total, successful int
	for _, result := range tester.results {
		if (host == "" || result.Host == host) && (operation == "" || result.Operation == operation) {
			total++
			if result.Success {
				successful++
			}
		}
	}

	if total == 0 {
		return 0.0
	}

	return float64(successful) / float64(total)
}

// GetAverageRetries returns the average number of retries for a specific host and operation
func (tester *NetworkConnectivityTester) GetAverageRetries(host, operation string) float64 {
	tester.mu.RLock()
	defer tester.mu.RUnlock()

	var total, count int
	for _, result := range tester.results {
		if (host == "" || result.Host == host) && (operation == "" || result.Operation == operation) {
			total += result.RetryAttempts
			count++
		}
	}

	if count == 0 {
		return 0.0
	}

	return float64(total) / float64(count)
}
