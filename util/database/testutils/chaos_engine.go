package testutils

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// ChaosExperiment represents a chaos engineering experiment
type ChaosExperiment struct {
	ID          string
	Name        string
	Description string
	Duration    time.Duration
	FailureRate float64
	Enabled     bool
	TargetType  string // "oracle", "mysql", "network", "all"
	Conditions  []ChaosCondition
	Schedule    *ChaosSchedule
	Metrics     ChaosMetrics
}

// ChaosCondition defines a specific failure condition
type ChaosCondition struct {
	Type        string  // "error_injection", "latency", "partition", "resource_exhaustion"
	Probability float64 // 0.0 to 1.0
	Parameters  map[string]interface{}
	Active      bool
}

// ChaosSchedule defines when the experiment should run
type ChaosSchedule struct {
	StartTime   time.Time
	EndTime     time.Time
	Recurring   bool
	Interval    time.Duration
	DaysOfWeek  []time.Weekday
	TimeWindows []TimeWindow
}

// TimeWindow represents a time window during which chaos can occur
type TimeWindow struct {
	Start time.Time
	End   time.Time
}

// ChaosMetrics tracks the results of chaos experiments
type ChaosMetrics struct {
	mu               sync.RWMutex
	ExperimentsRun   int
	FailuresInjected int
	ErrorsDetected   []ChaosError
	StartTime        time.Time
	LastUpdate       time.Time
	SuccessRate      float64
	MTTR             time.Duration // Mean Time To Recovery
	Availability     float64
}

// ChaosError represents an error that occurred during chaos testing
type ChaosError struct {
	Timestamp    time.Time
	Type         string
	Message      string
	Recovered    bool
	RecoveryTime time.Duration
	Context      map[string]string
}

// ChaosEngine orchestrates chaos engineering experiments
type ChaosEngine struct {
	mu           sync.RWMutex
	experiments  map[string]*ChaosExperiment
	isActive     bool
	oracleServer *MockOracleServer
	mysqlServer  *MockMySQLServer
	networkSim   *NetworkSimulator
	eventLog     []ChaosEvent
	cancelFuncs  map[string]context.CancelFunc
}

// ChaosEvent represents an event in the chaos engine
type ChaosEvent struct {
	Timestamp    time.Time
	ExperimentID string
	EventType    string
	Details      string
	Impact       string
}

// NewChaosEngine creates a new chaos engineering engine
func NewChaosEngine() *ChaosEngine {
	return &ChaosEngine{
		experiments: make(map[string]*ChaosExperiment),
		isActive:    false,
		eventLog:    make([]ChaosEvent, 0),
		cancelFuncs: make(map[string]context.CancelFunc),
	}
}

// SetOracleServer sets the Oracle server for chaos testing
func (ce *ChaosEngine) SetOracleServer(server *MockOracleServer) {
	ce.mu.Lock()
	defer ce.mu.Unlock()
	ce.oracleServer = server
}

// SetMySQLServer sets the MySQL server for chaos testing
func (ce *ChaosEngine) SetMySQLServer(server *MockMySQLServer) {
	ce.mu.Lock()
	defer ce.mu.Unlock()
	ce.mysqlServer = server
}

// SetNetworkSimulator sets the network simulator for chaos testing
func (ce *ChaosEngine) SetNetworkSimulator(sim *NetworkSimulator) {
	ce.mu.Lock()
	defer ce.mu.Unlock()
	ce.networkSim = sim
}

// Start activates the chaos engine
func (ce *ChaosEngine) Start() {
	ce.mu.Lock()
	defer ce.mu.Unlock()
	ce.isActive = true
	ce.logEvent("", "engine_start", "Chaos engine started", "none")
}

// Stop deactivates the chaos engine and stops all experiments
func (ce *ChaosEngine) Stop() {
	ce.mu.Lock()
	defer ce.mu.Unlock()

	// Cancel all running experiments
	for expID, cancelFunc := range ce.cancelFuncs {
		cancelFunc()
		delete(ce.cancelFuncs, expID)
	}

	ce.isActive = false
	ce.logEvent("", "engine_stop", "Chaos engine stopped", "none")
}

// AddExperiment adds a new chaos experiment
func (ce *ChaosEngine) AddExperiment(experiment *ChaosExperiment) {
	ce.mu.Lock()
	defer ce.mu.Unlock()

	experiment.Metrics.StartTime = time.Now()
	ce.experiments[experiment.ID] = experiment
	ce.logEvent(experiment.ID, "experiment_added",
		fmt.Sprintf("Experiment '%s' added", experiment.Name), "none")
}

// RemoveExperiment removes a chaos experiment
func (ce *ChaosEngine) RemoveExperiment(experimentID string) error {
	ce.mu.Lock()
	defer ce.mu.Unlock()

	// Stop the experiment if it's running
	if cancelFunc, exists := ce.cancelFuncs[experimentID]; exists {
		cancelFunc()
		delete(ce.cancelFuncs, experimentID)
	}

	if _, exists := ce.experiments[experimentID]; !exists {
		return fmt.Errorf("experiment '%s' not found", experimentID)
	}

	delete(ce.experiments, experimentID)
	ce.logEvent(experimentID, "experiment_removed",
		fmt.Sprintf("Experiment '%s' removed", experimentID), "none")

	return nil
}

// StartExperiment starts a specific chaos experiment
func (ce *ChaosEngine) StartExperiment(experimentID string) error {
	ce.mu.Lock()
	defer ce.mu.Unlock()

	if !ce.isActive {
		return errors.New("chaos engine is not active")
	}

	experiment, exists := ce.experiments[experimentID]
	if !exists {
		return fmt.Errorf("experiment '%s' not found", experimentID)
	}

	if !experiment.Enabled {
		return fmt.Errorf("experiment '%s' is disabled", experimentID)
	}

	// Check if experiment is already running
	if _, running := ce.cancelFuncs[experimentID]; running {
		return fmt.Errorf("experiment '%s' is already running", experimentID)
	}

	// Create context for the experiment
	ctx, cancel := context.WithCancel(context.Background())
	ce.cancelFuncs[experimentID] = cancel

	ce.logEvent(experimentID, "experiment_started",
		fmt.Sprintf("Experiment '%s' started", experiment.Name), "low")

	// Start the experiment in a goroutine
	go ce.runExperiment(ctx, experiment)

	return nil
}

// StopExperiment stops a specific chaos experiment
func (ce *ChaosEngine) StopExperiment(experimentID string) error {
	ce.mu.Lock()
	defer ce.mu.Unlock()

	cancelFunc, exists := ce.cancelFuncs[experimentID]
	if !exists {
		return fmt.Errorf("experiment '%s' is not running", experimentID)
	}

	cancelFunc()
	delete(ce.cancelFuncs, experimentID)

	ce.logEvent(experimentID, "experiment_stopped",
		fmt.Sprintf("Experiment '%s' stopped", experimentID), "none")

	return nil
}

// GetExperiment returns a specific experiment
func (ce *ChaosEngine) GetExperiment(experimentID string) (*ChaosExperiment, error) {
	ce.mu.RLock()
	defer ce.mu.RUnlock()

	experiment, exists := ce.experiments[experimentID]
	if !exists {
		return nil, fmt.Errorf("experiment '%s' not found", experimentID)
	}

	// Return a copy to prevent external modification
	expCopy := *experiment
	return &expCopy, nil
}

// ListExperiments returns all experiments
func (ce *ChaosEngine) ListExperiments() map[string]*ChaosExperiment {
	ce.mu.RLock()
	defer ce.mu.RUnlock()

	experiments := make(map[string]*ChaosExperiment)
	for id, exp := range ce.experiments {
		expCopy := *exp
		experiments[id] = &expCopy
	}

	return experiments
}

// GetRunningExperiments returns currently running experiments
func (ce *ChaosEngine) GetRunningExperiments() []string {
	ce.mu.RLock()
	defer ce.mu.RUnlock()

	running := make([]string, 0, len(ce.cancelFuncs))
	for expID := range ce.cancelFuncs {
		running = append(running, expID)
	}

	return running
}

// GetEventLog returns the event log
func (ce *ChaosEngine) GetEventLog() []ChaosEvent {
	ce.mu.RLock()
	defer ce.mu.RUnlock()

	events := make([]ChaosEvent, len(ce.eventLog))
	copy(events, ce.eventLog)
	return events
}

// ClearEventLog clears the event log
func (ce *ChaosEngine) ClearEventLog() {
	ce.mu.Lock()
	defer ce.mu.Unlock()
	ce.eventLog = ce.eventLog[:0]
}

// runExperiment executes a chaos experiment
func (ce *ChaosEngine) runExperiment(ctx context.Context, experiment *ChaosExperiment) {
	defer func() {
		ce.mu.Lock()
		delete(ce.cancelFuncs, experiment.ID)
		ce.mu.Unlock()
	}()

	startTime := time.Now()
	endTime := startTime.Add(experiment.Duration)

	// Update metrics
	experiment.Metrics.mu.Lock()
	experiment.Metrics.ExperimentsRun++
	experiment.Metrics.StartTime = startTime
	experiment.Metrics.mu.Unlock()

	ce.logEvent(experiment.ID, "experiment_execution",
		fmt.Sprintf("Executing experiment '%s' for %v", experiment.Name, experiment.Duration), "medium")

	for {
		select {
		case <-ctx.Done():
			ce.logEvent(experiment.ID, "experiment_cancelled",
				fmt.Sprintf("Experiment '%s' cancelled", experiment.Name), "low")
			return
		case <-time.After(1 * time.Second): // Check every second
			if time.Now().After(endTime) {
				ce.logEvent(experiment.ID, "experiment_completed",
					fmt.Sprintf("Experiment '%s' completed", experiment.Name), "low")
				return
			}

			// Apply chaos conditions
			ce.applyChaosConditions(experiment)
		}
	}
}

// applyChaosConditions applies the chaos conditions for an experiment
func (ce *ChaosEngine) applyChaosConditions(experiment *ChaosExperiment) {
	for _, condition := range experiment.Conditions {
		if !condition.Active {
			continue
		}

		// Check if condition should be applied based on probability
		if rand.Float64() > condition.Probability {
			continue
		}

		switch condition.Type {
		case "error_injection":
			ce.injectErrors(experiment, condition)
		case "latency":
			ce.injectLatency(experiment, condition)
		case "partition":
			ce.injectPartition(experiment, condition)
		case "resource_exhaustion":
			ce.injectResourceExhaustion(experiment, condition)
		}
	}
}

// injectErrors injects database errors
func (ce *ChaosEngine) injectErrors(experiment *ChaosExperiment, condition ChaosCondition) {
	errorType, _ := condition.Parameters["error_type"].(string)

	switch experiment.TargetType {
	case "oracle":
		if ce.oracleServer != nil {
			ce.injectOracleErrors(experiment, errorType)
		}
	case "mysql":
		if ce.mysqlServer != nil {
			ce.injectMySQLErrors(experiment, errorType)
		}
	case "all":
		if ce.oracleServer != nil {
			ce.injectOracleErrors(experiment, errorType)
		}
		if ce.mysqlServer != nil {
			ce.injectMySQLErrors(experiment, errorType)
		}
	}
}

// injectOracleErrors injects Oracle-specific errors
func (ce *ChaosEngine) injectOracleErrors(experiment *ChaosExperiment, errorType string) {
	switch errorType {
	case "connection":
		ce.oracleServer.EnableNetworkFailures(true)
		ce.logEvent(experiment.ID, "oracle_connection_errors",
			"Injected Oracle connection errors", "high")
	case "deadlock":
		ce.oracleServer.EnableDeadlockErrors(true)
		ce.logEvent(experiment.ID, "oracle_deadlock_errors",
			"Injected Oracle deadlock errors", "medium")
	case "session_timeout":
		ce.oracleServer.EnableSessionTimeouts(true)
		ce.logEvent(experiment.ID, "oracle_session_timeouts",
			"Injected Oracle session timeouts", "medium")
	case "resource":
		ce.oracleServer.EnableResourceExhaustion(true)
		ce.logEvent(experiment.ID, "oracle_resource_errors",
			"Injected Oracle resource exhaustion", "high")
	}

	// Update metrics
	experiment.Metrics.mu.Lock()
	experiment.Metrics.FailuresInjected++
	experiment.Metrics.mu.Unlock()
}

// injectMySQLErrors injects MySQL-specific errors
func (ce *ChaosEngine) injectMySQLErrors(experiment *ChaosExperiment, errorType string) {
	switch errorType {
	case "connection":
		ce.mysqlServer.EnableConnectionErrors(true)
		ce.logEvent(experiment.ID, "mysql_connection_errors",
			"Injected MySQL connection errors", "high")
	case "lock_timeout":
		ce.mysqlServer.EnableLockTimeouts(true)
		ce.logEvent(experiment.ID, "mysql_lock_timeouts",
			"Injected MySQL lock timeouts", "medium")
	case "network":
		ce.mysqlServer.EnableNetworkFailures(true)
		ce.logEvent(experiment.ID, "mysql_network_errors",
			"Injected MySQL network errors", "high")
	case "resource":
		ce.mysqlServer.EnableResourceExhaustion(true)
		ce.logEvent(experiment.ID, "mysql_resource_errors",
			"Injected MySQL resource exhaustion", "high")
	case "tidb":
		ce.mysqlServer.EnableTiDBErrors(true)
		ce.logEvent(experiment.ID, "tidb_errors",
			"Injected TiDB-specific errors", "medium")
	}

	// Update metrics
	experiment.Metrics.mu.Lock()
	experiment.Metrics.FailuresInjected++
	experiment.Metrics.mu.Unlock()
}

// injectLatency injects network latency
func (ce *ChaosEngine) injectLatency(experiment *ChaosExperiment, condition ChaosCondition) {
	if ce.networkSim == nil {
		return
	}

	minLatency, _ := condition.Parameters["min_latency"].(time.Duration)
	maxLatency, _ := condition.Parameters["max_latency"].(time.Duration)

	if minLatency == 0 {
		minLatency = 100 * time.Millisecond
	}
	if maxLatency == 0 {
		maxLatency = 1 * time.Second
	}

	ce.networkSim.SetLatency(minLatency, maxLatency)
	ce.networkSim.SetCondition(NetworkSlow)

	ce.logEvent(experiment.ID, "latency_injection",
		fmt.Sprintf("Injected latency: %v - %v", minLatency, maxLatency), "medium")

	// Update metrics
	experiment.Metrics.mu.Lock()
	experiment.Metrics.FailuresInjected++
	experiment.Metrics.mu.Unlock()
}

// injectPartition injects network partitions
func (ce *ChaosEngine) injectPartition(experiment *ChaosExperiment, condition ChaosCondition) {
	if ce.networkSim == nil {
		return
	}

	hosts, _ := condition.Parameters["hosts"].([]string)
	duration, _ := condition.Parameters["duration"].(time.Duration)

	if duration == 0 {
		duration = 30 * time.Second
	}

	// Partition specified hosts or use random partition
	if len(hosts) > 0 {
		for _, host := range hosts {
			ce.networkSim.PartitionHost(host)
		}
	} else {
		ce.networkSim.SetCondition(NetworkPartitioned)
	}

	ce.logEvent(experiment.ID, "partition_injection",
		fmt.Sprintf("Injected network partition for %v", duration), "high")

	// Schedule partition removal
	go func() {
		time.Sleep(duration)
		if len(hosts) > 0 {
			for _, host := range hosts {
				ce.networkSim.UnpartitionHost(host)
			}
		} else {
			ce.networkSim.SetCondition(NetworkHealthy)
		}
	}()

	// Update metrics
	experiment.Metrics.mu.Lock()
	experiment.Metrics.FailuresInjected++
	experiment.Metrics.mu.Unlock()
}

// injectResourceExhaustion injects resource exhaustion scenarios
func (ce *ChaosEngine) injectResourceExhaustion(experiment *ChaosExperiment, condition ChaosCondition) {
	resourceType, _ := condition.Parameters["resource_type"].(string)

	switch resourceType {
	case "connections":
		switch experiment.TargetType {
		case "oracle":
			if ce.oracleServer != nil {
				ce.oracleServer.SetMaxConnections(1) // Severely limit connections
			}
		case "mysql":
			if ce.mysqlServer != nil {
				ce.mysqlServer.SetMaxConnections(1) // Severely limit connections
			}
		case "all":
			if ce.oracleServer != nil {
				ce.oracleServer.SetMaxConnections(1) // Severely limit connections
			}
			if ce.mysqlServer != nil {
				ce.mysqlServer.SetMaxConnections(1) // Severely limit connections
			}
		}
	case "memory":
		// Simulate memory pressure (implementation would depend on actual system)
		ce.logEvent(experiment.ID, "memory_exhaustion",
			"Simulated memory exhaustion", "high")
	case "bandwidth":
		if ce.networkSim != nil {
			ce.networkSim.SetBandwidthLimit(1024) // 1KB/s limit
		}
	}

	ce.logEvent(experiment.ID, "resource_exhaustion",
		fmt.Sprintf("Injected %s exhaustion", resourceType), "high")

	// Update metrics
	experiment.Metrics.mu.Lock()
	experiment.Metrics.FailuresInjected++
	experiment.Metrics.mu.Unlock()
}

// logEvent logs a chaos event
func (ce *ChaosEngine) logEvent(experimentID, eventType, details, impact string) {
	event := ChaosEvent{
		Timestamp:    time.Now(),
		ExperimentID: experimentID,
		EventType:    eventType,
		Details:      details,
		Impact:       impact,
	}

	ce.eventLog = append(ce.eventLog, event)

	// Limit log size
	if len(ce.eventLog) > 10000 {
		ce.eventLog = ce.eventLog[1000:] // Keep last 9000 events
	}
}

// Predefined experiments

// CreateDatabaseConnectionChaos creates an experiment for database connection chaos
func CreateDatabaseConnectionChaos() *ChaosExperiment {
	return &ChaosExperiment{
		ID:          "db_connection_chaos",
		Name:        "Database Connection Chaos",
		Description: "Tests database retry mechanisms under connection failures",
		Duration:    5 * time.Minute,
		FailureRate: 0.3,
		Enabled:     true,
		TargetType:  "all",
		Conditions: []ChaosCondition{
			{
				Type:        "error_injection",
				Probability: 0.8,
				Parameters: map[string]interface{}{
					"error_type": "connection",
				},
				Active: true,
			},
			{
				Type:        "latency",
				Probability: 0.6,
				Parameters: map[string]interface{}{
					"min_latency": 200 * time.Millisecond,
					"max_latency": 2 * time.Second,
				},
				Active: true,
			},
		},
	}
}

// CreateNetworkPartitionChaos creates an experiment for network partition scenarios
func CreateNetworkPartitionChaos() *ChaosExperiment {
	return &ChaosExperiment{
		ID:          "network_partition_chaos",
		Name:        "Network Partition Chaos",
		Description: "Tests system behavior under network partitions",
		Duration:    3 * time.Minute,
		FailureRate: 0.4,
		Enabled:     true,
		TargetType:  "network",
		Conditions: []ChaosCondition{
			{
				Type:        "partition",
				Probability: 0.7,
				Parameters: map[string]interface{}{
					"duration": 30 * time.Second,
				},
				Active: true,
			},
			{
				Type:        "latency",
				Probability: 0.5,
				Parameters: map[string]interface{}{
					"min_latency": 500 * time.Millisecond,
					"max_latency": 5 * time.Second,
				},
				Active: true,
			},
		},
	}
}

// CreateResourceExhaustionChaos creates an experiment for resource exhaustion
func CreateResourceExhaustionChaos() *ChaosExperiment {
	return &ChaosExperiment{
		ID:          "resource_exhaustion_chaos",
		Name:        "Resource Exhaustion Chaos",
		Description: "Tests system behavior under resource constraints",
		Duration:    4 * time.Minute,
		FailureRate: 0.5,
		Enabled:     true,
		TargetType:  "all",
		Conditions: []ChaosCondition{
			{
				Type:        "resource_exhaustion",
				Probability: 0.8,
				Parameters: map[string]interface{}{
					"resource_type": "connections",
				},
				Active: true,
			},
			{
				Type:        "resource_exhaustion",
				Probability: 0.6,
				Parameters: map[string]interface{}{
					"resource_type": "bandwidth",
				},
				Active: true,
			},
		},
	}
}

// CreateMixedChaos creates an experiment with mixed failure types
func CreateMixedChaos() *ChaosExperiment {
	return &ChaosExperiment{
		ID:          "mixed_chaos",
		Name:        "Mixed Chaos Experiment",
		Description: "Comprehensive chaos test with multiple failure types",
		Duration:    10 * time.Minute,
		FailureRate: 0.4,
		Enabled:     true,
		TargetType:  "all",
		Conditions: []ChaosCondition{
			{
				Type:        "error_injection",
				Probability: 0.7,
				Parameters: map[string]interface{}{
					"error_type": "connection",
				},
				Active: true,
			},
			{
				Type:        "error_injection",
				Probability: 0.5,
				Parameters: map[string]interface{}{
					"error_type": "deadlock",
				},
				Active: true,
			},
			{
				Type:        "latency",
				Probability: 0.6,
				Parameters: map[string]interface{}{
					"min_latency": 100 * time.Millisecond,
					"max_latency": 1 * time.Second,
				},
				Active: true,
			},
			{
				Type:        "partition",
				Probability: 0.3,
				Parameters: map[string]interface{}{
					"duration": 20 * time.Second,
				},
				Active: true,
			},
			{
				Type:        "resource_exhaustion",
				Probability: 0.4,
				Parameters: map[string]interface{}{
					"resource_type": "connections",
				},
				Active: true,
			},
		},
	}
}

// ChaosReport generates a comprehensive report of chaos experiments
type ChaosReport struct {
	GeneratedAt          time.Time
	TotalExperiments     int
	RunningExperiments   int
	CompletedExperiments int
	TotalFailures        int
	OverallSuccessRate   float64
	ExperimentSummaries  []ExperimentSummary
	TopErrors            []string
	Recommendations      []string
}

// ExperimentSummary provides a summary of an experiment's results
type ExperimentSummary struct {
	ExperimentID     string
	Name             string
	Duration         time.Duration
	FailuresInjected int
	ErrorsDetected   int
	SuccessRate      float64
	Status           string
}

// GenerateReport generates a comprehensive chaos engineering report
func (ce *ChaosEngine) GenerateReport() ChaosReport {
	ce.mu.RLock()
	defer ce.mu.RUnlock()

	report := ChaosReport{
		GeneratedAt:          time.Now(),
		TotalExperiments:     len(ce.experiments),
		RunningExperiments:   len(ce.cancelFuncs),
		CompletedExperiments: 0,
		TotalFailures:        0,
		ExperimentSummaries:  make([]ExperimentSummary, 0),
		TopErrors:            make([]string, 0),
		Recommendations:      make([]string, 0),
	}

	// Analyze experiments
	var totalSuccessRate float64
	for _, exp := range ce.experiments {
		exp.Metrics.mu.RLock()

		summary := ExperimentSummary{
			ExperimentID:     exp.ID,
			Name:             exp.Name,
			Duration:         exp.Duration,
			FailuresInjected: exp.Metrics.FailuresInjected,
			ErrorsDetected:   len(exp.Metrics.ErrorsDetected),
			SuccessRate:      exp.Metrics.SuccessRate,
		}

		if _, running := ce.cancelFuncs[exp.ID]; running {
			summary.Status = "Running"
		} else if exp.Metrics.ExperimentsRun > 0 {
			summary.Status = "Completed"
			report.CompletedExperiments++
		} else {
			summary.Status = "Not Run"
		}

		report.TotalFailures += exp.Metrics.FailuresInjected
		totalSuccessRate += exp.Metrics.SuccessRate

		report.ExperimentSummaries = append(report.ExperimentSummaries, summary)
		exp.Metrics.mu.RUnlock()
	}

	// Calculate overall success rate
	if len(ce.experiments) > 0 {
		report.OverallSuccessRate = totalSuccessRate / float64(len(ce.experiments))
	}

	// Generate recommendations
	report.Recommendations = ce.generateRecommendations(report)

	return report
}

// generateRecommendations generates recommendations based on chaos test results
func (ce *ChaosEngine) generateRecommendations(report ChaosReport) []string {
	recommendations := make([]string, 0)

	if report.OverallSuccessRate < 0.8 {
		recommendations = append(recommendations,
			"Overall success rate is below 80%. Consider improving retry mechanisms and error handling.")
	}

	if report.TotalFailures > 1000 {
		recommendations = append(recommendations,
			"High number of failures detected. Review system resilience and monitoring.")
	}

	if report.RunningExperiments == 0 && report.CompletedExperiments == 0 {
		recommendations = append(recommendations,
			"No experiments have been run. Start with basic connection chaos experiments.")
	}

	if len(report.ExperimentSummaries) < 3 {
		recommendations = append(recommendations,
			"Consider adding more diverse chaos experiments to improve test coverage.")
	}

	// Analyze specific patterns
	for _, summary := range report.ExperimentSummaries {
		if summary.SuccessRate < 0.5 {
			recommendations = append(recommendations,
				fmt.Sprintf("Experiment '%s' has very low success rate. Review its configuration and system response.", summary.Name))
		}
	}

	return recommendations
}
