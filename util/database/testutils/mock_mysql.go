package testutils

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/pingcap/tidb/pkg/errno"
)

// MockMySQLServer simulates a MySQL/TiDB database server with configurable error injection
type MockMySQLServer struct {
	mu                 sync.RWMutex
	isRunning          bool
	connectionCount    int
	maxConnections     int
	errorRate          float64
	latencyMin         time.Duration
	latencyMax         time.Duration
	networkFailures    bool
	connectionErrors   bool
	lockTimeouts       bool
	resourceExhaustion bool
	tidbErrors         bool
	connections        map[string]*MockMySQLConnection
	errorHistory       []MockError
}

// MockMySQLConnection represents a simulated MySQL connection
type MockMySQLConnection struct {
	id              string
	server          *MockMySQLServer
	isAlive         bool
	connectionStart time.Time
	lastActivity    time.Time
	transactionOpen bool
	statementCount  int
}

// NewMockMySQLServer creates a new mock MySQL server
func NewMockMySQLServer() *MockMySQLServer {
	return &MockMySQLServer{
		isRunning:      true,
		maxConnections: 100,
		errorRate:      0.0,
		latencyMin:     1 * time.Millisecond,
		latencyMax:     10 * time.Millisecond,
		connections:    make(map[string]*MockMySQLConnection),
		errorHistory:   make([]MockError, 0),
	}
}

// Start starts the mock server
func (s *MockMySQLServer) Start() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.isRunning = true
}

// Stop stops the mock server and kills all connections
func (s *MockMySQLServer) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.isRunning = false

	// Kill all active connections
	for _, conn := range s.connections {
		conn.isAlive = false
	}
}

// SetMaxConnections sets the maximum number of concurrent connections
func (s *MockMySQLServer) SetMaxConnections(max int) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.maxConnections = max
}

// SetErrorRate sets the probability of errors (0.0 to 1.0)
func (s *MockMySQLServer) SetErrorRate(rate float64) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.errorRate = rate
}

// SetLatencyRange sets the simulated network latency range
func (s *MockMySQLServer) SetLatencyRange(min, max time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.latencyMin = min
	s.latencyMax = max
}

// EnableNetworkFailures enables/disables network failure simulation
func (s *MockMySQLServer) EnableNetworkFailures(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.networkFailures = enable
}

// EnableConnectionErrors enables/disables connection error simulation
func (s *MockMySQLServer) EnableConnectionErrors(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.connectionErrors = enable
}

// EnableLockTimeouts enables/disables lock timeout simulation
func (s *MockMySQLServer) EnableLockTimeouts(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.lockTimeouts = enable
}

// EnableResourceExhaustion enables/disables resource exhaustion simulation
func (s *MockMySQLServer) EnableResourceExhaustion(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.resourceExhaustion = enable
}

// EnableTiDBErrors enables/disables TiDB-specific error simulation
func (s *MockMySQLServer) EnableTiDBErrors(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.tidbErrors = enable
}

// GetConnectionCount returns the current number of active connections
func (s *MockMySQLServer) GetConnectionCount() int {
	s.mu.RLock()
	defer s.mu.RUnlock()
	count := 0
	for _, conn := range s.connections {
		if conn.isAlive {
			count++
		}
	}
	return count
}

// GetErrorHistory returns the history of injected errors
func (s *MockMySQLServer) GetErrorHistory() []MockError {
	s.mu.RLock()
	defer s.mu.RUnlock()
	history := make([]MockError, len(s.errorHistory))
	copy(history, s.errorHistory)
	return history
}

// ClearErrorHistory clears the error history
func (s *MockMySQLServer) ClearErrorHistory() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.errorHistory = s.errorHistory[:0]
}

// Connect simulates establishing a connection to MySQL/TiDB
func (s *MockMySQLServer) Connect(ctx context.Context) (*MockMySQLConnection, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Simulate network latency
	if err := s.simulateLatency(ctx); err != nil {
		return nil, err
	}

	// Check if server is running
	if !s.isRunning {
		err := &mysql.MySQLError{Number: 1053, Message: "Server shutdown in progress"}
		s.recordError("1053", err.Error(), "", "connect")
		return nil, err
	}

	// Check connection limit
	activeConnections := 0
	for _, conn := range s.connections {
		if conn.isAlive {
			activeConnections++
		}
	}

	if activeConnections >= s.maxConnections {
		err := &mysql.MySQLError{Number: 1040, Message: "Too many connections"}
		s.recordError("1040", err.Error(), "", "connect")
		return nil, err
	}

	// Check for connection errors
	if s.connectionErrors && s.shouldInjectError() {
		connectionErrors := []struct {
			code uint16
			msg  string
		}{
			{2002, "Can't connect to local MySQL server through socket"},
			{2003, "Can't connect to MySQL server on host"},
			{2004, "Can't create TCP/IP socket"},
			{2005, "Unknown MySQL server host"},
			{2006, "MySQL server has gone away"},
			{2007, "Protocol mismatch"},
			{2009, "Wrong host info"},
			{2012, "Error in server handshake"},
			{2013, "Lost connection to MySQL server during query"},
			{2026, "SSL connection error"},
			{2055, "Lost connection to MySQL server"},
		}
		errInfo := connectionErrors[rand.Intn(len(connectionErrors))]
		err := &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
		s.recordError(fmt.Sprintf("%d", errInfo.code), err.Error(), "", "connect")
		return nil, err
	}

	// Check for network failures
	if s.networkFailures && s.shouldInjectError() {
		networkErrors := []struct {
			code uint16
			msg  string
		}{
			{1042, "Can't get hostname for your address"},
			{1043, "Bad handshake"},
			{1129, "Host is blocked because of many connection errors"},
			{1130, "Host is not allowed to connect to this MySQL server"},
			{1152, "Aborted connection"},
			{1158, "Got an error reading communication packets"},
			{1159, "Got timeout reading communication packets"},
			{1160, "Got an error writing communication packets"},
			{1161, "Got timeout writing communication packets"},
			{1184, "Aborted connection"},
		}
		errInfo := networkErrors[rand.Intn(len(networkErrors))]
		err := &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
		s.recordError(fmt.Sprintf("%d", errInfo.code), err.Error(), "", "connect")
		return nil, err
	}

	// Check for resource exhaustion
	if s.resourceExhaustion && s.shouldInjectError() {
		resourceErrors := []struct {
			code uint16
			msg  string
		}{
			{1041, "Out of memory; restart server and try again"},
			{1047, "Unknown command"},
			{1153, "Got a packet bigger than max_allowed_packet bytes"},
			{1203, "User already has more than max_user_connections active connections"},
		}
		errInfo := resourceErrors[rand.Intn(len(resourceErrors))]
		err := &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
		s.recordError(fmt.Sprintf("%d", errInfo.code), err.Error(), "", "connect")
		return nil, err
	}

	// Create successful connection
	connID := fmt.Sprintf("conn_%d_%d", time.Now().UnixNano(), rand.Int63())
	conn := &MockMySQLConnection{
		id:              connID,
		server:          s,
		isAlive:         true,
		connectionStart: time.Now(),
		lastActivity:    time.Now(),
	}

	s.connections[connID] = conn
	s.connectionCount++

	return conn, nil
}

// shouldInjectError determines if an error should be injected based on error rate
func (s *MockMySQLServer) shouldInjectError() bool {
	return rand.Float64() < s.errorRate
}

// simulateLatency simulates network latency
func (s *MockMySQLServer) simulateLatency(ctx context.Context) error {
	if s.latencyMin == 0 && s.latencyMax == 0 {
		return nil
	}

	latency := s.latencyMin
	if s.latencyMax > s.latencyMin {
		latency += time.Duration(rand.Int63n(int64(s.latencyMax - s.latencyMin)))
	}

	timer := time.NewTimer(latency)
	defer timer.Stop()

	select {
	case <-timer.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// recordError records an error in the history
func (s *MockMySQLServer) recordError(code, msg, connID, operation string) {
	s.errorHistory = append(s.errorHistory, MockError{
		Timestamp:    time.Now(),
		ErrorCode:    code,
		ErrorMsg:     msg,
		ConnectionID: connID,
		Operation:    operation,
	})
}

// MockMySQLConnection methods

// Close closes the mock connection
func (c *MockMySQLConnection) Close() error {
	c.server.mu.Lock()
	defer c.server.mu.Unlock()

	c.isAlive = false
	delete(c.server.connections, c.id)
	return nil
}

// Ping simulates a ping operation
func (c *MockMySQLConnection) Ping(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
		c.server.recordError("2006", err.Error(), c.id, "ping")
		return err
	}

	// Check if server is running
	if !c.server.isRunning {
		err := &mysql.MySQLError{Number: 1053, Message: "Server shutdown in progress"}
		c.server.recordError("1053", err.Error(), c.id, "ping")
		c.isAlive = false
		return err
	}

	// Check for random connection errors
	if c.server.connectionErrors && c.server.shouldInjectError() {
		connectionErrors := []struct {
			code uint16
			msg  string
		}{
			{2006, "MySQL server has gone away"},
			{2013, "Lost connection to MySQL server during query"},
			{2055, "Lost connection to MySQL server"},
		}
		errInfo := connectionErrors[rand.Intn(len(connectionErrors))]
		err := &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
		c.server.recordError(fmt.Sprintf("%d", errInfo.code), err.Error(), c.id, "ping")
		c.isAlive = false
		return err
	}

	c.lastActivity = time.Now()
	return nil
}

// Query simulates a query operation
func (c *MockMySQLConnection) Query(ctx context.Context, query string, args ...interface{}) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
		c.server.recordError("2006", err.Error(), c.id, "query")
		return err
	}

	// Check for lock timeout errors
	if c.server.lockTimeouts && c.server.shouldInjectError() {
		lockErrors := []struct {
			code uint16
			msg  string
		}{
			{1205, "Lock wait timeout exceeded; try restarting transaction"},
			{1213, "Deadlock found when trying to get lock; try restarting transaction"},
		}
		errInfo := lockErrors[rand.Intn(len(lockErrors))]
		err := &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
		c.server.recordError(fmt.Sprintf("%d", errInfo.code), err.Error(), c.id, "query")
		return err
	}

	// Check for TiDB-specific errors
	if c.server.tidbErrors && c.server.shouldInjectError() {
		tidbErrors := []struct {
			code uint16
			msg  string
		}{
			{errno.ErrPDServerTimeout, "PD server timeout"},
			{errno.ErrTiKVServerBusy, "TiKV server busy"},
			{errno.ErrResolveLockTimeout, "Resolve lock timeout"},
			{errno.ErrInfoSchemaExpired, "Information schema is out of date"},
			{errno.ErrInfoSchemaChanged, "Information schema has been changed"},
			{errno.ErrWriteConflictInTiDB, "Write conflict in TiDB"},
			{errno.ErrWriteConflict, "Write conflict"},
		}
		errInfo := tidbErrors[rand.Intn(len(tidbErrors))]
		err := &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
		c.server.recordError(fmt.Sprintf("%d", errInfo.code), err.Error(), c.id, "query")
		return err
	}

	// Special case for ErrUnknown with retryable messages
	if c.server.tidbErrors && c.server.shouldInjectError() {
		unknownErrors := []string{
			"Information schema is out of date",
			"Information schema is changed",
		}
		msg := unknownErrors[rand.Intn(len(unknownErrors))]
		err := &mysql.MySQLError{Number: errno.ErrUnknown, Message: msg}
		c.server.recordError(fmt.Sprintf("%d", errno.ErrUnknown), err.Error(), c.id, "query")
		return err
	}

	c.lastActivity = time.Now()
	c.statementCount++
	return nil
}

// Exec simulates an execute operation
func (c *MockMySQLConnection) Exec(ctx context.Context, query string, args ...interface{}) error {
	return c.Query(ctx, query, args...)
}

// Begin simulates beginning a transaction
func (c *MockMySQLConnection) Begin(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
		c.server.recordError("2006", err.Error(), c.id, "begin")
		return err
	}

	c.transactionOpen = true
	c.lastActivity = time.Now()
	return nil
}

// Commit simulates committing a transaction
func (c *MockMySQLConnection) Commit(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
		c.server.recordError("2006", err.Error(), c.id, "commit")
		return err
	}

	c.transactionOpen = false
	c.lastActivity = time.Now()
	return nil
}

// Rollback simulates rolling back a transaction
func (c *MockMySQLConnection) Rollback(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
		c.server.recordError("2006", err.Error(), c.id, "rollback")
		return err
	}

	c.transactionOpen = false
	c.lastActivity = time.Now()
	return nil
}

// MockMySQLConnector implements driver.Connector for testing
type MockMySQLConnector struct {
	server *MockMySQLServer
}

// NewMockMySQLConnector creates a new mock MySQL connector
func NewMockMySQLConnector(server *MockMySQLServer) driver.Connector {
	return &MockMySQLConnector{server: server}
}

// Connect implements driver.Connector interface
func (c *MockMySQLConnector) Connect(ctx context.Context) (driver.Conn, error) {
	mockConn, err := c.server.Connect(ctx)
	if err != nil {
		return nil, err
	}
	return &MockMySQLDriverConn{conn: mockConn}, nil
}

// Driver implements driver.Connector interface
func (c *MockMySQLConnector) Driver() driver.Driver {
	return &MockMySQLDriver{}
}

// MockMySQLDriver implements driver.Driver
type MockMySQLDriver struct{}

// Open implements driver.Driver interface
func (d *MockMySQLDriver) Open(name string) (driver.Conn, error) {
	// For testing purposes, create a new server instance
	server := NewMockMySQLServer()
	mockConn, err := server.Connect(context.Background())
	if err != nil {
		return nil, err
	}
	return &MockMySQLDriverConn{conn: mockConn}, nil
}

// MockMySQLDriverConn implements driver.Conn
type MockMySQLDriverConn struct {
	conn *MockMySQLConnection
}

// Prepare implements driver.Conn interface
func (c *MockMySQLDriverConn) Prepare(query string) (driver.Stmt, error) {
	if !c.conn.isAlive {
		return nil, &mysql.MySQLError{Number: 2006, Message: "MySQL server has gone away"}
	}
	return &MockMySQLStmt{conn: c.conn, query: query}, nil
}

// Close implements driver.Conn interface
func (c *MockMySQLDriverConn) Close() error {
	return c.conn.Close()
}

// Begin implements driver.Conn interface
func (c *MockMySQLDriverConn) Begin() (driver.Tx, error) {
	err := c.conn.Begin(context.Background())
	if err != nil {
		return nil, err
	}
	return &MockMySQLTx{conn: c.conn}, nil
}

// Ping implements driver.Pinger interface
func (c *MockMySQLDriverConn) Ping(ctx context.Context) error {
	return c.conn.Ping(ctx)
}

// MockMySQLStmt implements driver.Stmt
type MockMySQLStmt struct {
	conn  *MockMySQLConnection
	query string
}

// Close implements driver.Stmt interface
func (s *MockMySQLStmt) Close() error {
	return nil
}

// NumInput implements driver.Stmt interface
func (s *MockMySQLStmt) NumInput() int {
	return -1 // Unknown number of parameters
}

// Exec implements driver.Stmt interface
func (s *MockMySQLStmt) Exec(args []driver.Value) (driver.Result, error) {
	// Convert driver.Value to interface{}
	interfaceArgs := make([]interface{}, len(args))
	for i, arg := range args {
		interfaceArgs[i] = arg
	}

	err := s.conn.Exec(context.Background(), s.query, interfaceArgs...)
	if err != nil {
		return nil, err
	}
	return &MockMySQLResult{}, nil
}

// Query implements driver.Stmt interface
func (s *MockMySQLStmt) Query(args []driver.Value) (driver.Rows, error) {
	// Convert driver.Value to interface{}
	interfaceArgs := make([]interface{}, len(args))
	for i, arg := range args {
		interfaceArgs[i] = arg
	}

	err := s.conn.Query(context.Background(), s.query, interfaceArgs...)
	if err != nil {
		return nil, err
	}
	return &MockMySQLRows{}, nil
}

// MockMySQLTx implements driver.Tx
type MockMySQLTx struct {
	conn *MockMySQLConnection
}

// Commit implements driver.Tx interface
func (tx *MockMySQLTx) Commit() error {
	return tx.conn.Commit(context.Background())
}

// Rollback implements driver.Tx interface
func (tx *MockMySQLTx) Rollback() error {
	return tx.conn.Rollback(context.Background())
}

// MockMySQLResult implements driver.Result
type MockMySQLResult struct{}

// LastInsertId implements driver.Result interface
func (r *MockMySQLResult) LastInsertId() (int64, error) {
	return 1, nil
}

// RowsAffected implements driver.Result interface
func (r *MockMySQLResult) RowsAffected() (int64, error) {
	return 1, nil
}

// MockMySQLRows implements driver.Rows
type MockMySQLRows struct {
	pos int
}

// Columns implements driver.Rows interface
func (r *MockMySQLRows) Columns() []string {
	return []string{"id", "name"}
}

// Close implements driver.Rows interface
func (r *MockMySQLRows) Close() error {
	return nil
}

// Next implements driver.Rows interface
func (r *MockMySQLRows) Next(dest []driver.Value) error {
	if r.pos >= 2 { // Return 2 rows
		return driver.ErrSkip
	}

	dest[0] = int64(r.pos + 1)
	dest[1] = fmt.Sprintf("name%d", r.pos+1)
	r.pos++

	return nil
}

// MySQLErrorGenerator provides utilities to generate specific MySQL errors
type MySQLErrorGenerator struct{}

// NewMySQLErrorGenerator creates a new MySQL error generator
func NewMySQLErrorGenerator() *MySQLErrorGenerator {
	return &MySQLErrorGenerator{}
}

// GenerateConnectionError generates a random MySQL connection error
func (g *MySQLErrorGenerator) GenerateConnectionError() error {
	errors := []struct {
		code uint16
		msg  string
	}{
		{2002, "Can't connect to local MySQL server through socket"},
		{2003, "Can't connect to MySQL server on host"},
		{2004, "Can't create TCP/IP socket"},
		{2005, "Unknown MySQL server host"},
		{2006, "MySQL server has gone away"},
		{2007, "Protocol mismatch"},
		{2009, "Wrong host info"},
		{2012, "Error in server handshake"},
		{2013, "Lost connection to MySQL server during query"},
		{2026, "SSL connection error"},
		{2055, "Lost connection to MySQL server"},
	}
	errInfo := errors[rand.Intn(len(errors))]
	return &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
}

// GenerateResourceError generates a random MySQL resource error
func (g *MySQLErrorGenerator) GenerateResourceError() error {
	errors := []struct {
		code uint16
		msg  string
	}{
		{1040, "Too many connections"},
		{1041, "Out of memory; restart server and try again"},
		{1047, "Unknown command"},
		{1153, "Got a packet bigger than max_allowed_packet bytes"},
		{1203, "User already has more than max_user_connections active connections"},
	}
	errInfo := errors[rand.Intn(len(errors))]
	return &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
}

// GenerateLockError generates a random MySQL lock error
func (g *MySQLErrorGenerator) GenerateLockError() error {
	errors := []struct {
		code uint16
		msg  string
	}{
		{1205, "Lock wait timeout exceeded; try restarting transaction"},
		{1213, "Deadlock found when trying to get lock; try restarting transaction"},
	}
	errInfo := errors[rand.Intn(len(errors))]
	return &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
}

// GenerateNetworkError generates a random MySQL network error
func (g *MySQLErrorGenerator) GenerateNetworkError() error {
	errors := []struct {
		code uint16
		msg  string
	}{
		{1042, "Can't get hostname for your address"},
		{1043, "Bad handshake"},
		{1129, "Host is blocked because of many connection errors"},
		{1130, "Host is not allowed to connect to this MySQL server"},
		{1152, "Aborted connection"},
		{1158, "Got an error reading communication packets"},
		{1159, "Got timeout reading communication packets"},
		{1160, "Got an error writing communication packets"},
		{1161, "Got timeout writing communication packets"},
		{1184, "Aborted connection"},
	}
	errInfo := errors[rand.Intn(len(errors))]
	return &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
}

// GenerateTiDBError generates a random TiDB-specific error
func (g *MySQLErrorGenerator) GenerateTiDBError() error {
	errors := []struct {
		code uint16
		msg  string
	}{
		{errno.ErrPDServerTimeout, "PD server timeout"},
		{errno.ErrTiKVServerBusy, "TiKV server busy"},
		{errno.ErrResolveLockTimeout, "Resolve lock timeout"},
		{errno.ErrInfoSchemaExpired, "Information schema is out of date"},
		{errno.ErrInfoSchemaChanged, "Information schema has been changed"},
		{errno.ErrWriteConflictInTiDB, "Write conflict in TiDB"},
		{errno.ErrWriteConflict, "Write conflict"},
		{errno.ErrLockDeadlock, "Deadlock found when trying to get lock; try restarting transaction"},
	}
	errInfo := errors[rand.Intn(len(errors))]
	return &mysql.MySQLError{Number: errInfo.code, Message: errInfo.msg}
}

// GenerateUnknownErrorWithRetryableMessage generates ErrUnknown with retryable message
func (g *MySQLErrorGenerator) GenerateUnknownErrorWithRetryableMessage() error {
	messages := []string{
		"Information schema is out of date",
		"Information schema is changed",
	}
	msg := messages[rand.Intn(len(messages))]
	return &mysql.MySQLError{Number: errno.ErrUnknown, Message: msg}
}

// GenerateSpecificError generates a specific MySQL error by code
func (g *MySQLErrorGenerator) GenerateSpecificError(code uint16) error {
	errorMap := map[uint16]string{
		// Connection errors
		2002: "Can't connect to local MySQL server through socket",
		2003: "Can't connect to MySQL server on host",
		2004: "Can't create TCP/IP socket",
		2005: "Unknown MySQL server host",
		2006: "MySQL server has gone away",
		2007: "Protocol mismatch",
		2009: "Wrong host info",
		2012: "Error in server handshake",
		2013: "Lost connection to MySQL server during query",
		2026: "SSL connection error",
		2055: "Lost connection to MySQL server",

		// Temporary errors
		1040: "Too many connections",
		1041: "Out of memory; restart server and try again",
		1042: "Can't get hostname for your address",
		1043: "Bad handshake",
		1047: "Unknown command",
		1053: "Server shutdown in progress",
		1129: "Host is blocked because of many connection errors",
		1130: "Host is not allowed to connect to this MySQL server",
		1152: "Aborted connection",
		1153: "Got a packet bigger than max_allowed_packet bytes",
		1158: "Got an error reading communication packets",
		1159: "Got timeout reading communication packets",
		1160: "Got an error writing communication packets",
		1161: "Got timeout writing communication packets",
		1184: "Aborted connection",
		1203: "User already has more than max_user_connections active connections",
		1205: "Lock wait timeout exceeded; try restarting transaction",
		1213: "Deadlock found when trying to get lock; try restarting transaction",
	}

	if message, exists := errorMap[code]; exists {
		return &mysql.MySQLError{Number: code, Message: message}
	}

	return &mysql.MySQLError{Number: code, Message: fmt.Sprintf("Unknown error code %d", code)}
}
