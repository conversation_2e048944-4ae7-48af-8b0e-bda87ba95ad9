package testutils

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// MockOracleServer simulates an Oracle database server with configurable error injection
type MockOracleServer struct {
	mu                 sync.RWMutex
	isRunning          bool
	connectionCount    int
	maxConnections     int
	errorRate          float64
	latencyMin         time.Duration
	latencyMax         time.Duration
	networkFailures    bool
	sessionTimeouts    bool
	resourceExhaustion bool
	deadlockErrors     bool
	connections        map[string]*MockOracleConnection
	errorHistory       []MockError
}

// MockError represents an injected error with metadata
type MockError struct {
	Timestamp    time.Time
	ErrorCode    string
	ErrorMsg     string
	ConnectionID string
	Operation    string
}

// MockOracleConnection represents a simulated Oracle connection
type MockOracleConnection struct {
	id              string
	server          *MockOracleServer
	isAlive         bool
	sessionStart    time.Time
	lastActivity    time.Time
	transactionOpen bool
	statementCount  int
}

// NewMockOracleServer creates a new mock Oracle server
func NewMockOracleServer() *MockOracleServer {
	return &MockOracleServer{
		isRunning:      true,
		maxConnections: 100,
		errorRate:      0.0,
		latencyMin:     1 * time.Millisecond,
		latencyMax:     10 * time.Millisecond,
		connections:    make(map[string]*MockOracleConnection),
		errorHistory:   make([]MockError, 0),
	}
}

// Start starts the mock server
func (s *MockOracleServer) Start() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.isRunning = true
}

// Stop stops the mock server and kills all connections
func (s *MockOracleServer) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.isRunning = false

	// Kill all active connections
	for _, conn := range s.connections {
		conn.isAlive = false
	}
}

// SetMaxConnections sets the maximum number of concurrent connections
func (s *MockOracleServer) SetMaxConnections(max int) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.maxConnections = max
}

// SetErrorRate sets the probability of errors (0.0 to 1.0)
func (s *MockOracleServer) SetErrorRate(rate float64) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.errorRate = rate
}

// SetLatencyRange sets the simulated network latency range
func (s *MockOracleServer) SetLatencyRange(min, max time.Duration) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.latencyMin = min
	s.latencyMax = max
}

// EnableNetworkFailures enables/disables network failure simulation
func (s *MockOracleServer) EnableNetworkFailures(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.networkFailures = enable
}

// EnableSessionTimeouts enables/disables session timeout simulation
func (s *MockOracleServer) EnableSessionTimeouts(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.sessionTimeouts = enable
}

// EnableResourceExhaustion enables/disables resource exhaustion simulation
func (s *MockOracleServer) EnableResourceExhaustion(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.resourceExhaustion = enable
}

// EnableDeadlockErrors enables/disables deadlock error simulation
func (s *MockOracleServer) EnableDeadlockErrors(enable bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.deadlockErrors = enable
}

// GetConnectionCount returns the current number of active connections
func (s *MockOracleServer) GetConnectionCount() int {
	s.mu.RLock()
	defer s.mu.RUnlock()
	count := 0
	for _, conn := range s.connections {
		if conn.isAlive {
			count++
		}
	}
	return count
}

// GetErrorHistory returns the history of injected errors
func (s *MockOracleServer) GetErrorHistory() []MockError {
	s.mu.RLock()
	defer s.mu.RUnlock()
	history := make([]MockError, len(s.errorHistory))
	copy(history, s.errorHistory)
	return history
}

// ClearErrorHistory clears the error history
func (s *MockOracleServer) ClearErrorHistory() {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.errorHistory = s.errorHistory[:0]
}

// Connect simulates establishing a connection to Oracle
func (s *MockOracleServer) Connect(ctx context.Context) (*MockOracleConnection, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Simulate network latency
	if err := s.simulateLatency(ctx); err != nil {
		return nil, err
	}

	// Check if server is running
	if !s.isRunning {
		err := errors.New("ORA-01034: ORACLE not available")
		s.recordError("ORA-01034", err.Error(), "", "connect")
		return nil, err
	}

	// Check connection limit
	activeConnections := 0
	for _, conn := range s.connections {
		if conn.isAlive {
			activeConnections++
		}
	}

	if activeConnections >= s.maxConnections {
		err := errors.New("ORA-00018: maximum number of sessions exceeded")
		s.recordError("ORA-00018", err.Error(), "", "connect")
		return nil, err
	}

	// Check for network failures
	if s.networkFailures && s.shouldInjectError() {
		errorCodes := []string{
			"ORA-12500: TNS:listener failed to start a dedicated server process",
			"ORA-12502: TNS:listener received no CONNECT_DATA from client",
			"ORA-12535: TNS:operation timed out",
			"ORA-12541: TNS:no listener",
			"ORA-12543: TNS:destination host unreachable",
			"ORA-12547: TNS:lost contact",
			"ORA-12571: TNS:packet writer failure",
		}
		errorMsg := errorCodes[rand.Intn(len(errorCodes))]
		code := errorMsg[:9] // Extract ORA-XXXXX
		err := errors.New(errorMsg)
		s.recordError(code, err.Error(), "", "connect")
		return nil, err
	}

	// Check for resource exhaustion
	if s.resourceExhaustion && s.shouldInjectError() {
		errorCodes := []string{
			"ORA-00020: maximum number of processes exceeded",
			"ORA-00051: timeout occurred while waiting for a resource",
			"ORA-12549: TNS:operating system resource quota exceeded",
		}
		errorMsg := errorCodes[rand.Intn(len(errorCodes))]
		code := errorMsg[:9] // Extract ORA-XXXXX
		err := errors.New(errorMsg)
		s.recordError(code, err.Error(), "", "connect")
		return nil, err
	}

	// Create successful connection
	connID := fmt.Sprintf("conn_%d_%d", time.Now().UnixNano(), rand.Int63())
	conn := &MockOracleConnection{
		id:           connID,
		server:       s,
		isAlive:      true,
		sessionStart: time.Now(),
		lastActivity: time.Now(),
	}

	s.connections[connID] = conn
	s.connectionCount++

	return conn, nil
}

// shouldInjectError determines if an error should be injected based on error rate
func (s *MockOracleServer) shouldInjectError() bool {
	return rand.Float64() < s.errorRate
}

// simulateLatency simulates network latency
func (s *MockOracleServer) simulateLatency(ctx context.Context) error {
	if s.latencyMin == 0 && s.latencyMax == 0 {
		return nil
	}

	latency := s.latencyMin
	if s.latencyMax > s.latencyMin {
		latency += time.Duration(rand.Int63n(int64(s.latencyMax - s.latencyMin)))
	}

	timer := time.NewTimer(latency)
	defer timer.Stop()

	select {
	case <-timer.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

// recordError records an error in the history
func (s *MockOracleServer) recordError(code, msg, connID, operation string) {
	s.errorHistory = append(s.errorHistory, MockError{
		Timestamp:    time.Now(),
		ErrorCode:    code,
		ErrorMsg:     msg,
		ConnectionID: connID,
		Operation:    operation,
	})
}

// MockOracleConnection methods

// Close closes the mock connection
func (c *MockOracleConnection) Close() error {
	c.server.mu.Lock()
	defer c.server.mu.Unlock()

	c.isAlive = false
	delete(c.server.connections, c.id)
	return nil
}

// Ping simulates a ping operation
func (c *MockOracleConnection) Ping(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := errors.New("ORA-03114: not connected to ORACLE")
		c.server.recordError("ORA-03114", err.Error(), c.id, "ping")
		return err
	}

	// Check if server is running
	if !c.server.isRunning {
		err := errors.New("ORA-01092: ORACLE instance terminated. Disconnection forced")
		c.server.recordError("ORA-01092", err.Error(), c.id, "ping")
		c.isAlive = false
		return err
	}

	// Check for session timeouts
	if c.server.sessionTimeouts && time.Since(c.lastActivity) > 30*time.Second {
		err := errors.New("ORA-02396: exceeded maximum idle time, please connect again")
		c.server.recordError("ORA-02396", err.Error(), c.id, "ping")
		c.isAlive = false
		return err
	}

	// Check for random network errors
	if c.server.networkFailures && c.server.shouldInjectError() {
		errorCodes := []string{
			"ORA-03113: end-of-file on communication channel",
			"ORA-03135: connection lost contact",
			"ORA-12537: TNS:connection closed",
			"ORA-12552: TNS:operation was interrupted",
		}
		errorMsg := errorCodes[rand.Intn(len(errorCodes))]
		code := errorMsg[:9] // Extract ORA-XXXXX
		err := errors.New(errorMsg)
		c.server.recordError(code, err.Error(), c.id, "ping")
		c.isAlive = false
		return err
	}

	c.lastActivity = time.Now()
	return nil
}

// Query simulates a query operation
func (c *MockOracleConnection) Query(ctx context.Context, query string, args ...interface{}) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := errors.New("ORA-03114: not connected to ORACLE")
		c.server.recordError("ORA-03114", err.Error(), c.id, "query")
		return err
	}

	// Check for deadlock errors
	if c.server.deadlockErrors && c.server.shouldInjectError() {
		errorCodes := []string{
			"ORA-00060: deadlock detected while waiting for resource",
			"ORA-04020: deadlock detected while trying to lock object",
			"ORA-04021: timeout occurred while waiting to lock object",
		}
		errorMsg := errorCodes[rand.Intn(len(errorCodes))]
		code := errorMsg[:9] // Extract ORA-XXXXX
		err := errors.New(errorMsg)
		c.server.recordError(code, err.Error(), c.id, "query")
		return err
	}

	// Check for consistent read failures
	if c.server.shouldInjectError() {
		err := errors.New("ORA-08176: consistent read failure; rollback data not available")
		c.server.recordError("ORA-08176", err.Error(), c.id, "query")
		return err
	}

	c.lastActivity = time.Now()
	c.statementCount++
	return nil
}

// Exec simulates an execute operation
func (c *MockOracleConnection) Exec(ctx context.Context, query string, args ...interface{}) error {
	return c.Query(ctx, query, args...)
}

// Begin simulates beginning a transaction
func (c *MockOracleConnection) Begin(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := errors.New("ORA-03114: not connected to ORACLE")
		c.server.recordError("ORA-03114", err.Error(), c.id, "begin")
		return err
	}

	c.transactionOpen = true
	c.lastActivity = time.Now()
	return nil
}

// Commit simulates committing a transaction
func (c *MockOracleConnection) Commit(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := errors.New("ORA-03114: not connected to ORACLE")
		c.server.recordError("ORA-03114", err.Error(), c.id, "commit")
		return err
	}

	c.transactionOpen = false
	c.lastActivity = time.Now()
	return nil
}

// Rollback simulates rolling back a transaction
func (c *MockOracleConnection) Rollback(ctx context.Context) error {
	c.server.mu.RLock()
	defer c.server.mu.RUnlock()

	// Simulate latency
	if err := c.server.simulateLatency(ctx); err != nil {
		return err
	}

	// Check if connection is alive
	if !c.isAlive {
		err := errors.New("ORA-03114: not connected to ORACLE")
		c.server.recordError("ORA-03114", err.Error(), c.id, "rollback")
		return err
	}

	c.transactionOpen = false
	c.lastActivity = time.Now()
	return nil
}

// MockOracleConnector implements driver.Connector for testing
type MockOracleConnector struct {
	server *MockOracleServer
}

// NewMockOracleConnector creates a new mock Oracle connector
func NewMockOracleConnector(server *MockOracleServer) driver.Connector {
	return &MockOracleConnector{server: server}
}

// Connect implements driver.Connector interface
func (c *MockOracleConnector) Connect(ctx context.Context) (driver.Conn, error) {
	mockConn, err := c.server.Connect(ctx)
	if err != nil {
		return nil, err
	}
	return &MockOracleDriverConn{conn: mockConn}, nil
}

// Driver implements driver.Connector interface
func (c *MockOracleConnector) Driver() driver.Driver {
	return &MockOracleDriver{}
}

// MockOracleDriver implements driver.Driver
type MockOracleDriver struct{}

// Open implements driver.Driver interface
func (d *MockOracleDriver) Open(name string) (driver.Conn, error) {
	// For testing purposes, create a new server instance
	server := NewMockOracleServer()
	mockConn, err := server.Connect(context.Background())
	if err != nil {
		return nil, err
	}
	return &MockOracleDriverConn{conn: mockConn}, nil
}

// MockOracleDriverConn implements driver.Conn
type MockOracleDriverConn struct {
	conn *MockOracleConnection
}

// Prepare implements driver.Conn interface
func (c *MockOracleDriverConn) Prepare(query string) (driver.Stmt, error) {
	if !c.conn.isAlive {
		return nil, errors.New("ORA-03114: not connected to ORACLE")
	}
	return &MockOracleStmt{conn: c.conn, query: query}, nil
}

// Close implements driver.Conn interface
func (c *MockOracleDriverConn) Close() error {
	return c.conn.Close()
}

// Begin implements driver.Conn interface
func (c *MockOracleDriverConn) Begin() (driver.Tx, error) {
	err := c.conn.Begin(context.Background())
	if err != nil {
		return nil, err
	}
	return &MockOracleTx{conn: c.conn}, nil
}

// Ping implements driver.Pinger interface
func (c *MockOracleDriverConn) Ping(ctx context.Context) error {
	return c.conn.Ping(ctx)
}

// MockOracleStmt implements driver.Stmt
type MockOracleStmt struct {
	conn  *MockOracleConnection
	query string
}

// Close implements driver.Stmt interface
func (s *MockOracleStmt) Close() error {
	return nil
}

// NumInput implements driver.Stmt interface
func (s *MockOracleStmt) NumInput() int {
	return -1 // Unknown number of parameters
}

// Exec implements driver.Stmt interface
func (s *MockOracleStmt) Exec(args []driver.Value) (driver.Result, error) {
	// Convert driver.Value to interface{}
	interfaceArgs := make([]interface{}, len(args))
	for i, arg := range args {
		interfaceArgs[i] = arg
	}

	err := s.conn.Exec(context.Background(), s.query, interfaceArgs...)
	if err != nil {
		return nil, err
	}
	return &MockOracleResult{}, nil
}

// Query implements driver.Stmt interface
func (s *MockOracleStmt) Query(args []driver.Value) (driver.Rows, error) {
	// Convert driver.Value to interface{}
	interfaceArgs := make([]interface{}, len(args))
	for i, arg := range args {
		interfaceArgs[i] = arg
	}

	err := s.conn.Query(context.Background(), s.query, interfaceArgs...)
	if err != nil {
		return nil, err
	}
	return &MockOracleRows{}, nil
}

// MockOracleTx implements driver.Tx
type MockOracleTx struct {
	conn *MockOracleConnection
}

// Commit implements driver.Tx interface
func (tx *MockOracleTx) Commit() error {
	return tx.conn.Commit(context.Background())
}

// Rollback implements driver.Tx interface
func (tx *MockOracleTx) Rollback() error {
	return tx.conn.Rollback(context.Background())
}

// MockOracleResult implements driver.Result
type MockOracleResult struct{}

// LastInsertId implements driver.Result interface
func (r *MockOracleResult) LastInsertId() (int64, error) {
	return 1, nil
}

// RowsAffected implements driver.Result interface
func (r *MockOracleResult) RowsAffected() (int64, error) {
	return 1, nil
}

// MockOracleRows implements driver.Rows
type MockOracleRows struct {
	pos int
}

// Columns implements driver.Rows interface
func (r *MockOracleRows) Columns() []string {
	return []string{"id", "name"}
}

// Close implements driver.Rows interface
func (r *MockOracleRows) Close() error {
	return nil
}

// Next implements driver.Rows interface
func (r *MockOracleRows) Next(dest []driver.Value) error {
	if r.pos >= 2 { // Return 2 rows
		return driver.ErrSkip
	}

	dest[0] = int64(r.pos + 1)
	dest[1] = fmt.Sprintf("name%d", r.pos+1)
	r.pos++

	return nil
}

// OracleErrorGenerator provides utilities to generate specific Oracle errors
type OracleErrorGenerator struct{}

// NewOracleErrorGenerator creates a new Oracle error generator
func NewOracleErrorGenerator() *OracleErrorGenerator {
	return &OracleErrorGenerator{}
}

// GenerateConnectionError generates a random Oracle connection error
func (g *OracleErrorGenerator) GenerateConnectionError() error {
	errorMsgs := []string{
		"ORA-00028: your session has been killed",
		"ORA-01012: not logged on",
		"ORA-01033: ORACLE initialization or shutdown in progress",
		"ORA-01034: ORACLE not available",
		"ORA-01092: ORACLE instance terminated. Disconnection forced",
		"ORA-03113: end-of-file on communication channel",
		"ORA-03114: not connected to ORACLE",
		"ORA-03135: connection lost contact",
		"ORA-12500: TNS:listener failed to start a dedicated server process",
		"ORA-12535: TNS:operation timed out",
		"ORA-12541: TNS:no listener",
		"ORA-12547: TNS:lost contact",
	}
	return errors.New(errorMsgs[rand.Intn(len(errorMsgs))])
}

// GenerateResourceError generates a random Oracle resource error
func (g *OracleErrorGenerator) GenerateResourceError() error {
	errorMsgs := []string{
		"ORA-00018: maximum number of sessions exceeded",
		"ORA-00020: maximum number of processes exceeded",
		"ORA-00051: timeout occurred while waiting for a resource",
		"ORA-12549: TNS:operating system resource quota exceeded",
	}
	return errors.New(errorMsgs[rand.Intn(len(errorMsgs))])
}

// GenerateDeadlockError generates a random Oracle deadlock error
func (g *OracleErrorGenerator) GenerateDeadlockError() error {
	errorMsgs := []string{
		"ORA-00060: deadlock detected while waiting for resource",
		"ORA-04020: deadlock detected while trying to lock object",
		"ORA-04021: timeout occurred while waiting to lock object",
	}
	return errors.New(errorMsgs[rand.Intn(len(errorMsgs))])
}

// GenerateNetworkError generates a random Oracle network error
func (g *OracleErrorGenerator) GenerateNetworkError() error {
	errorMsgs := []string{
		"ORA-12502: TNS:listener received no CONNECT_DATA from client",
		"ORA-12504: TNS:listener was not given the SERVICE_NAME in CONNECT_DATA",
		"ORA-12505: TNS:listener does not currently know of SID given in connect descriptor",
		"ORA-12514: TNS:listener does not currently know of service requested in connect descriptor",
		"ORA-12516: TNS:listener could not find available handler with matching protocol stack",
		"ORA-12518: TNS:listener could not hand off client connection",
		"ORA-12521: TNS:listener does not currently know of instance requested in connect descriptor",
		"ORA-12537: TNS:connection closed",
		"ORA-12543: TNS:destination host unreachable",
		"ORA-12545: Connect failed because target host or object does not exist",
		"ORA-12552: TNS:operation was interrupted",
		"ORA-12571: TNS:packet writer failure",
		"ORA-12582: TNS:invalid socket",
	}
	return errors.New(errorMsgs[rand.Intn(len(errorMsgs))])
}

// GenerateSpecificError generates a specific Oracle error by code
func (g *OracleErrorGenerator) GenerateSpecificError(code string) error {
	errorMap := map[string]string{
		"ORA-00028": "your session has been killed",
		"ORA-00600": "internal error code",
		"ORA-00603": "ORACLE server session terminated by fatal error",
		"ORA-01012": "not logged on",
		"ORA-01033": "ORACLE initialization or shutdown in progress",
		"ORA-01034": "ORACLE not available",
		"ORA-01041": "internal error. hostdef extension doesn't exist",
		"ORA-01092": "ORACLE instance terminated. Disconnection forced",
		"ORA-02396": "exceeded maximum idle time, please connect again",
		"ORA-03113": "end-of-file on communication channel",
		"ORA-03114": "not connected to ORACLE",
		"ORA-03135": "connection lost contact",
		"ORA-12500": "TNS:listener failed to start a dedicated server process",
		"ORA-12502": "TNS:listener received no CONNECT_DATA from client",
		"ORA-12504": "TNS:listener was not given the SERVICE_NAME in CONNECT_DATA",
		"ORA-12505": "TNS:listener does not currently know of SID given in connect descriptor",
		"ORA-12514": "TNS:listener does not currently know of service requested in connect descriptor",
		"ORA-12516": "TNS:listener could not find available handler with matching protocol stack",
		"ORA-12518": "TNS:listener could not hand off client connection",
		"ORA-12521": "TNS:listener does not currently know of instance requested in connect descriptor",
		"ORA-12535": "TNS:operation timed out",
		"ORA-12537": "TNS:connection closed",
		"ORA-12541": "TNS:no listener",
		"ORA-12543": "TNS:destination host unreachable",
		"ORA-12545": "Connect failed because target host or object does not exist",
		"ORA-12547": "TNS:lost contact",
		"ORA-12549": "TNS:operating system resource quota exceeded",
		"ORA-12552": "TNS:operation was interrupted",
		"ORA-12571": "TNS:packet writer failure",
		"ORA-12582": "TNS:invalid socket",
		"ORA-25408": "can not safely replay call",
		"ORA-00018": "maximum number of sessions exceeded",
		"ORA-00020": "maximum number of processes exceeded",
		"ORA-00051": "timeout occurred while waiting for a resource",
		"ORA-00060": "deadlock detected while waiting for resource",
		"ORA-00061": "another instance has a different DML_LOCKS setting",
		"ORA-04020": "deadlock detected while trying to lock object",
		"ORA-04021": "timeout occurred while waiting to lock object",
		"ORA-08176": "consistent read failure; rollback data not available",
	}

	if message, exists := errorMap[code]; exists {
		return fmt.Errorf("%s: %s", code, message)
	}

	return fmt.Errorf("ORA-99999: unknown error code %s", code)
}
