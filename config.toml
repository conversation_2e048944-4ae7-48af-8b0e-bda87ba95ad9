#server ip
#server-host =  "127.0.0.1"

#server listen port
server-port = 8082

#data storage directory, include database file, log file; default value is './data'
data-dir = "./data"

static-file-path = "./dist"

license =  ""
[db]
host = ""
port = 3306
user = ""
password = ""
schema = ""
# database log level, its value is info, warn, silent, error; dafault value is warn.
db-log-level = "info"
# database log file name, default value is 'sql'
#db-log-file = "sql"
# sqlite database file directory, default value is the same as  'data-dir'
#db-file-dir = "./data"

[log]
# log level
#log-level ="info"
log-level ="debug"

#log file name, default value is 'tms'
#log-file-name = "tms"

#root directory to store log file, default value is the same as 'data-dir'
#log-file-root = "./data"

#max size(M) of log
log-max-size = 512

log-max-age = 30

log-max-backups = 0


