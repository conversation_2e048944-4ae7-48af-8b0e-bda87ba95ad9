{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(go build:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./genmock.sh:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(wc:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "Bash(which:*)", "Bash(whereis:*)", "<PERSON><PERSON>(curl:*)", "Bash(ping:*)", "<PERSON><PERSON>(netstat:*)", "Bash(tar:*)", "Bash(zip:*)", "Ba<PERSON>(unzip:*)", "Bash(gzip:*)", "<PERSON><PERSON>(jq:*)", "Bash(less:*)", "Bash(more:*)", "Bash(vi:*)", "Bash(vim:*)", "Bash(nano:*)", "<PERSON><PERSON>(code:*)", "Bash(git:*)", "Bash(npm:*)", "Bash(yarn:*)", "Bash(pnpm:*)", "Bash(node:*)", "Bash(npx:*)", "Bash(vite:*)", "<PERSON><PERSON>(go:*)", "Bash(gofmt:*)", "<PERSON><PERSON>(goimports:*)", "Bash(java:*)", "<PERSON><PERSON>(javac:*)", "Bash(jar:*)", "<PERSON><PERSON>(javadoc:*)", "<PERSON><PERSON>(mvn:*)", "<PERSON><PERSON>(gradle:*)", "<PERSON><PERSON>(gradlew:*)", "<PERSON><PERSON>(make:*)", "Bash(cmake:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(ps:*)", "<PERSON><PERSON>(top:*)", "<PERSON><PERSON>(du:*)", "Bash(df:*)", "Bash(lsof:*)", "Bash(env:*)", "Bash(export:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(history:*)", "<PERSON><PERSON>(alias:*)", "Bash(tree:*)", "<PERSON><PERSON>(htop:*)", "<PERSON><PERSON>(wget:*)", "Bash(ss:*)", "HttpRequest", "Edit", "Diff", "<PERSON><PERSON>", "Python", "Search", "WebFetch", "Bash(rg:*)", "mcp__sequentialthinking__sequentialthinking", "<PERSON><PERSON>(mysql:*)", "mcp__zen__refactor", "mcp__zen__consensus", "mcp__zen__analyze", "mcp__zen__listmodels", "mcp__zen__thinkdeep", "mcp__zen__chat", "mcp__zen__debug", "Bash(rm:*)", "<PERSON>sh(swag init:*)", "Bash(MYSQL_DSN=\"root:root1234@tcp(10.2.103.35:3311)/testdb\" go test ./util/database/ -v -run \"TestRealMySQLIntegration\" -timeout 30s)", "Bash(MYSQL_DSN=\"root:root1234@tcp(10.2.103.35:3311)/testdb\" go test ./util/database/ -v -run \"TestRealMySQLIntegration\" -timeout 30s)", "Bash(MYSQL_DSN=\"root:root1234@tcp(10.2.103.35:3311)/testdb\" go test ./util/database/ -v -run \"TestRealMySQLIntegration\" -timeout 30s)", "Bash(MYSQL_DSN=\"root:root1234@tcp(10.2.103.35:3311)/testdb\" go test ./util/database/ -v -run \"TestRealMySQLIntegration\" -timeout 30s)", "Bash(MYSQL_DSN=\"root:root1234@tcp(10.2.103.35:3311)/testdb\" go test ./util/database/ -v -run \"TestRealMySQLIntegration\" -timeout 20s)", "<PERSON><PERSON>(timeout 20 go build:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(do echo \"=== Processing $file ===\")", "Bash(done)"], "deny": []}, "outputStyle": "Explanatory"}