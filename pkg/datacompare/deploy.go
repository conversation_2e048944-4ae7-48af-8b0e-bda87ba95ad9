package datacompare

import (
	"gitee.com/pingcap_enterprise/tms/common/constants"
	//"context"
	//"database/sql"
	//"github.com/pingcap/errors"
	//"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	//"strconv"
)

var defaultDataCompareDeployTasks = []EnvironmentDeployTask{
	createLibrary(),
	createModuleLibFunction(),
	createCompareFunction(),
	createCompressCompareFunction(),
	invokeCompareFunction(),
	invokeCompressCompareFunction(),
}

func GetDefaultDataCompareDeployTasks() []EnvironmentDeployTask {
	tasks := make([]EnvironmentDeployTask, len(defaultDataCompareDeployTasks))
	for idx, deployTask := range defaultDataCompareDeployTasks {
		tasks[idx] = deployTask
		tasks[idx].TaskNumber = idx + 1
	}
	return tasks
}

func createLibrary() EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName: constants.DataCompareCreateLibrary,
		SQLModel: constants.DeployModeSQL,
		DeployJob: DeployJob{
			validateParam:         true,
			execSQLFailureMessage: "Library创建失败",
			execSQLSuccessMessage: "Library创建成功",
			taskSQL:               `create or replace library yzbt_crc32lib as '${ORACLE_HOME}/lib/yzbt_crc32.so';`,
		},
		//DeployPreCheckSteps: []DeployPreCheck{
		//	{
		//		preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int) (shouldSkip bool, err error) {
		//			checkSQL := "SELECT count(*) as count FROM DBA_LIBRARIES WHERE LIBRARY_NAME = 'YZBT_CRC32LIB' AND OWNER='TO_FIX_OWNER'"
		//			_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
		//			if err != nil {
		//				return false, err
		//			}
		//			if len(rets) == 0 {
		//				return false, errors.New("Library检查失败，结果为空")
		//			}
		//			total, _ := strconv.Atoi(rets[0]["COUNT"])
		//			if total == 1 {
		//				if runMode == constants.ExecuteRunModeRun {
		//					return false, errors.New("Library已经创建，部署任务中止")
		//				} else {
		//					return true, errors.New("Library已经创建，部署任务中止")
		//				}
		//			}
		//			return false, nil
		//		},
		//		onSuccess: "对象未创建，创建中...",
		//		onFailure: "对象检查失败",
		//		onSkip:    "对象已创建，跳过",
		//	},
		//},
	}
}

func createModuleLibFunction() EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName: constants.DataCompareCreateModuleLibFunction,
		SQLModel: constants.DeployModeProcedure,
		DeployJob: DeployJob{
			execSQLFailureMessage: "函数创建失败",
			execSQLSuccessMessage: "函数创建成功",
			taskSQL: `
create or replace function yzbtcrc32 wrapped 
a000000
354
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
8
b5 db
Pkzacqu2iofUz9leSq6JP10Nnqwwg1z/f8sVfHQCOItuFfyiY9wphV05tmZEXq3RUVb3fbIv
mW5BNo03gZxBJCwbn6v/dGT+c756pxlIhBmMZjZAmRjaUbTRkUnip3/odlT6t6BgWkk69jm2
UFyhBO+ikSRrSbgbQDVpyXUg1jxe7WKVfEHn7J7BZGh182Ogwq6dlitFujsGoJjXW6thfwU=
`,
		},
	}
}

func createCompareFunction() EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName: constants.DataCompareCreateCompareFunction,
		SQLModel: constants.DeployModeProcedure,
		DeployJob: DeployJob{
			execSQLFailureMessage: "函数创建失败",
			execSQLSuccessMessage: "函数创建成功",
			taskSQL: `
create or replace function crc32 wrapped 
a000000
354
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
abcd
8
f2 eb
zK2FA0FWcyJD8+fW6nRcKxEk1yMwg3nwLp4VfC8CTMGOIN6zAtTldw7IGiaydz4a3jV4TNI5
Garuw00ruH90gpr4PqPCs7FG7q2KXSX+vkqrQVApcAykkayP4P3ndxlfmVDwpnv8eUsQsAop
EHsLjEZS6q6dbq9zOGZPT86BX0kYQ7x12tF2AlRFf+de+JzQOeoquSSVnrpBw8nen1eXfrWn
Xv+k8qUrbR9u59q5
`,
		},
	}
}

func createCompressCompareFunction() EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName: constants.DataCompareCreateCompressCompareFunction,
		SQLModel: constants.DeployModeProcedure,
		DeployJob: DeployJob{
			execSQLFailureMessage: "函数创建失败",
			execSQLSuccessMessage: "函数创建成功",
			taskSQL: `
CREATE OR REPLACE FUNCTION TCF (
    CADENA IN VARCHAR2
) RETURN VARCHAR2
AS
BEGIN
    RETURN
        RAWTOHEX(
        	DBMS_CRYPTO.HASH(
            	UTL_RAW.CAST_TO_RAW(CADENA), -- 转换输入字符串为 RAW 类型
            	DBMS_CRYPTO.HASH_MD5         -- 使用 MD5 哈希算法
            )
        )
    ;
END;
/
`,
		},
	}
}

func invokeCompareFunction() EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName: constants.DataCompareInvokeCompareFunction,
		SQLModel: constants.DeployModeSQL,
		DeployJob: DeployJob{
			execSQLFailureMessage: "校验失败",
			execSQLSuccessMessage: "校验成功",
			taskSQL:               `select crc32('TMS') from dual;`,
		},
	}
}

func invokeCompressCompareFunction() EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName: constants.DataCompareInvokeCompressCompareFunction,
		SQLModel: constants.DeployModeSQL,
		DeployJob: DeployJob{
			execSQLFailureMessage: "校验失败",
			execSQLSuccessMessage: "校验成功",
			taskSQL:               `select TCF('TMS') from dual;`,
		},
	}
}
