package datacompare

import (
	"context"
	"database/sql"
)

type EnvironmentDeployTask struct {
	TaskNumber          int
	TaskName            string
	SQLModel            string
	DeployPreCheckSteps []DeployPreCheck
	DeployJob           DeployJob
}

func (i EnvironmentDeployTask) GetDeployPreCheckSteps() []DeployPreCheck {
	return i.DeployPreCheckSteps
}

func (i EnvironmentDeployTask) GetTaskSQL() string {
	return i.DeployJob.taskSQL
}

func (i EnvironmentDeployTask) HasToValidateParam() bool {
	return i.DeployJob.validateParam
}

func (i EnvironmentDeployTask) GetExecSQLFailureMessage() string {
	return i.DeployJob.execSQLFailureMessage
}

func (i EnvironmentDeployTask) GetSqlSuccessMessage() string {
	return i.DeployJob.execSQLSuccessMessage
}

func (i EnvironmentDeployTask) HasPreCheckSQLs() bool {
	return len(i.DeployPreCheckSteps) > 0
}

type DeployPreCheck struct {
	onSuccess string
	onFailure string
	onSkip    string

	preCheckFunc func(ctx context.Context, oracleConn *sql.DB, runMode int) (shouldSkip bool, err error)
}

type PreCheckFunc func(ctx context.Context, oracleConn *sql.DB, runMode int) (shouldSkip bool, err error)

func (i DeployPreCheck) GetPreCheckFunc() PreCheckFunc {
	return i.preCheckFunc
}

func (i DeployPreCheck) GetPreCheckSuccessMessage() string {
	return i.onSuccess
}

func (i DeployPreCheck) GetPreCheckSkipMessage() string {
	return i.onSkip
}

func (i DeployPreCheck) GetPreCheckFailureMessage() string {
	return i.onFailure
}

type DeployJob struct {
	validateParam         bool
	taskSQL               string
	execSQLFailureMessage string
	execSQLSuccessMessage string
}
