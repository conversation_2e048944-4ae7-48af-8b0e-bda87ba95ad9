package datacompare

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datacompare"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
	"go.uber.org/zap"
)

func FindDeployTask(task *datacompare.EnvDeployTask) (EnvironmentDeployTask, bool) {
	var defaultTask EnvironmentDeployTask
	for _, dTask := range GetDefaultDataCompareDeployTasks() {
		//if dTask.TaskNumber == task.TaskNumber && dTask.TaskName == task.TaskName {
		if dTask.TaskName == task.TaskName && dTask.SQLModel == task.SQLModel {
			return dTask, true
		}
	}
	return defaultTask, false
}

func SubstituteSQL(task *datacompare.EnvDeployTask, oracleHome string, deployTask EnvironmentDeployTask) []string {

	taskSQL := task.TaskSQL
	taskSQL = strings.ReplaceAll(taskSQL, "${ORACLE_HOME}", oracleHome)

	isProcedure := deployTask.SQLModel == constants.DeployModeProcedure
	taskSQLS := make([]string, 0)

	if isProcedure {
		taskSQLS = []string{taskSQL}
	} else {
		taskSQLS = strings.Split(taskSQL, "\n")
		taskSQLS = lo.Map(taskSQLS, func(s string, _ int) string {
			ss := strings.TrimSpace(s)
			if strings.HasSuffix(ss, ";") {
				ss = ss[:len(ss)-1]
			}
			return ss
		})
	}

	taskSQLS = lo.Filter(taskSQLS, func(s string, _ int) bool {
		return len(s) > 0
	})

	return taskSQLS
}

func BuildDataCompareParameter(ctx context.Context, taskInfo *task.Task) (*structs.DataCompareTaskParam, error) {
	// default params
	taskDefaultParams, getParamDetailsErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, taskInfo.TaskParamTmplateId)
	if getParamDetailsErr != nil {
		log.Error("get param template details failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Int("TaskparamTemplateID", taskInfo.TaskParamTmplateId), zap.Error(getParamDetailsErr))
		return nil, getParamDetailsErr
	}
	log.Debugf("BuildDataCompareParameter->taskDefaultParams:%d, taskId:%d, taskParamTemplateId:%d",
		len(taskDefaultParams),
		taskInfo.TaskID,
		taskInfo.TaskParamTmplateId)

	// task params
	taskCustomParams, getTaskParamConfigsErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx,
		&task.TaskParamConfig{
			ChannelID:           taskInfo.ChannelId,
			TaskID:              taskInfo.TaskID,
			TaskparamTemplateID: taskInfo.TaskParamTmplateId,
		})
	if getTaskParamConfigsErr != nil {
		log.Error("get datacompare task param configs failed.", zap.Int("channelId", taskInfo.ChannelId), zap.Int("taskId", taskInfo.TaskID), zap.Int("TaskparamTemplateID", taskInfo.TaskParamTmplateId), zap.Error(getTaskParamConfigsErr))
		return nil, getTaskParamConfigsErr
	}
	log.Debugf("BuildDataCompareParameter->taskCustomParams:%d, taskId:%d, taskParamTemplateId:%d",
		len(taskCustomParams),
		taskInfo.TaskID,
		taskInfo.TaskParamTmplateId)

	paramsMap := make(map[string]string)
	for _, taskDefaultParam := range taskDefaultParams {
		paramsMap[taskDefaultParam.ParamName] = taskDefaultParam.ParamValueDefault
	}
	for _, taskCustomParam := range taskCustomParams {
		paramsMap[taskCustomParam.ParamName] = taskCustomParam.ParamValueCurrent
	}
	log.Debugf("BuildDataCompareParameter->paramsMap:%d", len(paramsMap))

	var dataCompareTaskParam structs.DataCompareTaskParam
	var err error

	dataCompareTaskParam.TaskId = taskInfo.TaskID

	if value, ok := paramsMap[constants.DC_PARAM_SOURCE_SNAPSHOT]; ok {
		dataCompareTaskParam.SourceSnapshot = value
	}
	if value, ok := paramsMap[constants.DC_PARAM_TARGET_SNAPSHOT]; ok {
		dataCompareTaskParam.TargetSnapshot = value
	}
	if value, ok := paramsMap[constants.DC_PARAM_ORACLE_SCN]; ok {
		dataCompareTaskParam.OracleScn = value
	}
	if value, ok := paramsMap[constants.DC_PARAM_SOURCE_CHARSET]; ok {
		dataCompareTaskParam.SourceCharset = value
	}
	if value, ok := paramsMap[constants.DC_PARAM_TARGET_CHARSET]; ok {
		dataCompareTaskParam.TargetCharset = value
	}
	if value, ok := paramsMap[constants.DC_PARAM_CHUNK_SIZE]; ok {
		dataCompareTaskParam.ChunkSize, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.ChunkSize = 1000
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_CHECK_THREAD_COUNT]; ok {
		dataCompareTaskParam.CheckThreadCount, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.CheckThreadCount = 1
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_USE_CHECKSUM]; ok {
		dataCompareTaskParam.UseChecksum, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.UseChecksum = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_ONLY_USE_CHECKSUM]; ok {
		dataCompareTaskParam.OnlyUseChecksum, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.OnlyUseChecksum = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_FIX_SQL_DIR]; ok {
		dataCompareTaskParam.FixSqlDir = value
	} else {
		dataCompareTaskParam.FixSqlDir = constants.FIX_SQL_PATH
	}

	if value, ok := paramsMap[constants.DC_PARAM_FIX_TARGET]; ok {
		dataCompareTaskParam.FixTarget = value
	}
	if value, ok := paramsMap[constants.DC_ORACLE_HOME]; ok {
		dataCompareTaskParam.OracleHome = value
	}
	if value, ok := paramsMap[constants.DC_PARAM_EMPTY_STRING_NULL_COMPARE_SWITCH]; ok {
		dataCompareTaskParam.EmptyStringNullCompare, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.EmptyStringNullCompare = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_ASCII_0_SWITCH]; ok {
		dataCompareTaskParam.Ascii0Switch, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.Ascii0Switch = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_IGNORE_STRUCT_CHECK]; ok {
		dataCompareTaskParam.IgnoreStructCheck, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.IgnoreStructCheck = true
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_IGNORE_STATS]; ok {
		dataCompareTaskParam.IgnoreStats, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.IgnoreStats = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_IGNORE_DATA_CHECK]; ok {
		dataCompareTaskParam.IgnoreDataCheck, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.IgnoreDataCheck = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_USE_CHECKPOINT]; ok {
		dataCompareTaskParam.UseCheckpoint, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.UseCheckpoint = false
		}
	}
	if value, ok := paramsMap[constants.DC_FLOAT_TRUNC_PRECISION]; ok {
		dataCompareTaskParam.FloatTruncPrecision, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.FloatTruncPrecision = 6
		}
	}
	if value, ok := paramsMap[constants.DC_DOUBLE_TRUNC_PRECISION]; ok {
		dataCompareTaskParam.DoubleTruncPrecision, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.DoubleTruncPrecision = 9
		}
	}
	if value, ok := paramsMap[constants.DC_LOW_CASE_SCHEMA_NAME]; ok {
		dataCompareTaskParam.LowCaseSchemaName, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.LowCaseSchemaName = 1
		}
	}
	if value, ok := paramsMap[constants.DC_LOW_CASE_TABLE_NAME]; ok {
		dataCompareTaskParam.LowCaseTableName, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.LowCaseTableName = 1
		}
	}
	if value, ok := paramsMap[constants.DC_COMPRESS_MODE]; ok {
		dataCompareTaskParam.CompressMode, err = strconv.ParseBool(value)
		if err != nil {
			dataCompareTaskParam.CompressMode = false
		}
	}
	if value, ok := paramsMap[constants.DC_PARAM_MISSING_TABLES_THRESHOLD]; ok {
		dataCompareTaskParam.MissingTablesThreshold, err = strconv.ParseFloat(value, 64)
		if err != nil {
			dataCompareTaskParam.MissingTablesThreshold = 100.0
		} else if dataCompareTaskParam.MissingTablesThreshold < 0 {
			dataCompareTaskParam.MissingTablesThreshold = 0
		} else if dataCompareTaskParam.MissingTablesThreshold > 100 {
			dataCompareTaskParam.MissingTablesThreshold = 100
		}
	} else {
		dataCompareTaskParam.MissingTablesThreshold = 100.0
	}
	if value, ok := paramsMap[constants.DC_PARAM_TABLE_CHUNK_FAIL_THRESHOLD]; ok {
		dataCompareTaskParam.TableChunkFailThreshold, err = strconv.ParseFloat(value, 64)
		if err != nil {
			dataCompareTaskParam.TableChunkFailThreshold = 100.0
		} else if dataCompareTaskParam.TableChunkFailThreshold < 0 {
			dataCompareTaskParam.TableChunkFailThreshold = 0
		} else if dataCompareTaskParam.TableChunkFailThreshold > 100 {
			dataCompareTaskParam.TableChunkFailThreshold = 100
		}
	} else {
		dataCompareTaskParam.TableChunkFailThreshold = 100.0
	}
	if value, ok := paramsMap[constants.DC_PARAM_OVERALL_TABLE_ERROR_THRESHOLD]; ok {
		dataCompareTaskParam.OverallTableErrorThreshold, err = strconv.ParseFloat(value, 64)
		if err != nil {
			dataCompareTaskParam.OverallTableErrorThreshold = 100.0
		} else if dataCompareTaskParam.OverallTableErrorThreshold < 0 {
			dataCompareTaskParam.OverallTableErrorThreshold = 0
		} else if dataCompareTaskParam.OverallTableErrorThreshold > 100 {
			dataCompareTaskParam.OverallTableErrorThreshold = 100
		}
	} else {
		dataCompareTaskParam.OverallTableErrorThreshold = 100.0
	}
	if value, ok := paramsMap[constants.DC_PARAM_TABLE_CHUNK_FAIL_COUNT]; ok {
		dataCompareTaskParam.TableChunkFailMinCount, err = strconv.Atoi(value)
		if err != nil {
			dataCompareTaskParam.TableChunkFailMinCount = 16
		} else if dataCompareTaskParam.TableChunkFailMinCount < 0 {
			dataCompareTaskParam.TableChunkFailMinCount = 16
		}
	} else {
		dataCompareTaskParam.TableChunkFailMinCount = 16
	}

	bytes, _ := json.Marshal(dataCompareTaskParam)
	log.Infof("build success, dataCompareParams:%s", string(bytes))

	return &dataCompareTaskParam, nil
}
