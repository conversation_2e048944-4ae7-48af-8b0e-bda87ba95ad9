package channel

import (
	"bytes"
	"fmt"
	"strings"
)

type SourceTableColumnSQLBuilder struct {
	SchemaNames      []string
	Hint             string
	PunctReplacement string
}

func (i *SourceTableColumnSQLBuilder) TestSQL() string {
	return `
select SCHEMA_NAME_S AS owner,
       TABLE_NAME_S AS table_name,
       COLUMN_NAME_S AS column_name,
       COMMENT_CONTENT AS comments,
       to_number(a.column_id) AS column_id,
       DATA_DEFAULT_S AS data_default,
       DATA_TYPE_S AS data_type,
       DATA_LENGTH_S AS data_length,
       DATA_PRECISION_S AS data_precision,
       DATA_SCALE_S AS data_scale,
       NULLABLE_S AS nullable
    FROM TABLE_COLUMN_CUSTOM_MAP_RULES a
`
}

func (i *SourceTableColumnSQLBuilder) QuerySQL() string {
	const basicSQL = `
SELECT ${SQL_HINT} a.owner,
       a.table_name,
       a.column_name,
       ${COMMENTS_EXPR},
       to_number(a.column_id) AS column_id,
       a.data_default,
       b.data_type,
       b.data_length,
       b.data_precision,
       b.data_scale,
       b.nullable
FROM (SELECT xs.*
      FROM XMLTABLE(
                   '/ROWSET/ROW' PASSING (SELECT DBMS_XMLGEN.GETXMLTYPE(
                                                         q'[select t.OWNER,t.table_name,t.COLUMN_NAME,t.COLUMN_ID,t.DATA_DEFAULT,c.COMMENTS
from dba_tab_columns t, dba_col_comments c
where t.table_name = c.table_name
and t.column_name = c.column_name
and t.owner = c.owner
and upper(t.owner) IN (${SCHEMA_NAMES}) and upper(t.table_name) not like 'BIN$%'
]'
                                                 )
                                          FROM DUAL)
                   COLUMNS OWNER VARCHAR2(100) PATH 'OWNER',table_name varchar2(100),COLUMN_NAME varchar2(100),COLUMN_ID varchar2(100),
                       DATA_DEFAULT VARCHAR2(1000), COMMENTS VARCHAR2(1000)
           ) xs) a,
     dba_tab_columns b JOIN dba_tables dt ON b.owner = dt.owner AND b.table_name = dt.table_name
WHERE a.owner = b.OWNER
  AND a.table_name = b.TABLE_NAME
  AND a.COLUMN_NAME = b.COLUMN_NAME
  AND a.COLUMN_ID = b.COLUMN_ID
  AND ${ADDITIONAL_CONDITION}
ORDER BY a.owner, a.table_name, a.column_name`

	condition := i.buildCondition().String()
	commentsExpr := i.buildCommentsExpr()

	targetSQL := basicSQL
	targetSQL = strings.ReplaceAll(targetSQL, "${COMMENTS_EXPR}", commentsExpr)
	targetSQL = strings.ReplaceAll(targetSQL, "${ADDITIONAL_CONDITION}", condition)
	targetSQL = strings.ReplaceAll(targetSQL, "${SCHEMA_NAMES}", fmt.Sprintf("'%s'", strings.Join(i.SchemaNames, "','")))
	targetSQL = strings.ReplaceAll(targetSQL, "${SQL_HINT}", i.Hint)

	return targetSQL
}

func (i *SourceTableColumnSQLBuilder) CountSQL() string {
	const basicSQL = `
SELECT COUNT(*) AS COUNT
FROM
  dba_tab_columns a
  JOIN dba_col_comments c ON a.TABLE_NAME = c.TABLE_NAME
  JOIN dba_tables dt ON a.owner = dt.owner AND a.table_name = dt.table_name
  AND a.COLUMN_NAME = c.COLUMN_NAME
  AND a.OWNER = c.OWNER
  AND upper(a.owner) IN (${SCHEMA_NAMES})
  AND upper(a.table_name) not like 'BIN$%'
WHERE
  ${WHERE_CONDITION}
`

	condition := i.buildCondition()
	conditionStr := condition.String()

	targetSQL := basicSQL
	targetSQL = strings.ReplaceAll(targetSQL, "${SCHEMA_NAMES}", fmt.Sprintf("'%s'", strings.Join(i.SchemaNames, "','")))
	targetSQL = strings.ReplaceAll(targetSQL, "${WHERE_CONDITION}", conditionStr)
	targetSQL = strings.ReplaceAll(targetSQL, "${SQL_HINT}", i.Hint)
	return targetSQL
}

func (i *SourceTableColumnSQLBuilder) buildCondition() *bytes.Buffer {
	condition := &bytes.Buffer{}
	condition.WriteString("1 = 1")
	if len(i.SchemaNames) > 0 {
		condition.WriteString(fmt.Sprintf(" AND a.OWNER IN ('%s')", strings.Join(i.SchemaNames, "','")))
	}
	return condition
}

func (i *SourceTableColumnSQLBuilder) buildCommentsExpr() string {
	if i.PunctReplacement == "" {
		return `regexp_replace(a.comments, '[[:punct:]]', '', 1) AS comments`
	}
	return `regexp_replace(a.comments, '[[:punct:]]', '` + i.PunctReplacement + `', 1) AS comments`
}

func (i *SourceTableColumnSQLBuilder) SetSchemaNames(ss []string) {
	i.SchemaNames = ss
}

func (i *SourceTableColumnSQLBuilder) SetSQLHint(hint string) {
	i.Hint = hint
}

func (i *SourceTableColumnSQLBuilder) SetPunctReplacement(pr string) {
	i.PunctReplacement = pr
}

func DefaultSourceTableColumnSQLBuilder() *SourceTableColumnSQLBuilder {
	return &SourceTableColumnSQLBuilder{}
}
