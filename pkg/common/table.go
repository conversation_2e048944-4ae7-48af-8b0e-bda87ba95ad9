package common

import (
	"context"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
	"go.uber.org/zap"
)

type CaseFunc func(string) string

// GetTableStatusFromDataSource get table status from data source, exist or not exists
func GetTableStatusFromDataSource(ctx context.Context, channelSchemaTableIds []int, taskInfo *task.Task, datasourceS *datasource.Datasource, datasourceT *datasource.Datasource, targetSchemaCaseFunc, targetTableCaseFunc CaseFunc) ([]*channel.ChannelSchemaTable, []int, []*channel.ChannelSchemaTable, []*channel.ChannelSchemaTable, error) {

	var channelSchemaTables []*channel.ChannelSchemaTable
	var schemaSArray, schemaTArray []string
	var getTableErr error

	// handle target table schema/table names with case
	log.Info("get tables from data source for validate exist status, taskId:%d, channelId:%d", taskInfo.TaskID, taskInfo.ChannelId)

	if len(channelSchemaTableIds) == 0 {
		channelSchemaTables, getTableErr = models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
		if getTableErr != nil {
			log.Error("get channel schema tables failed, taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, taskInfo.ChannelId, getTableErr)
			return nil, nil, nil, nil, getTableErr
		}
	} else {
		channelSchemaTables, getTableErr = models.GetChannelReaderWriter().GetChannelSchemaTablesByPks(ctx, channelSchemaTableIds)
		if getTableErr != nil {
			log.Error("get channel schema tables failed, taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, taskInfo.ChannelId, getTableErr)
			return nil, nil, nil, nil, getTableErr
		}
	}
	for _, channelSchemaTable := range channelSchemaTables {
		schemaSArray = append(schemaSArray, channelSchemaTable.SchemaNameS)
		schemaTArray = append(schemaTArray, channelSchemaTable.SchemaNameT, targetTableCaseFunc(channelSchemaTable.SchemaNameT))
	}
	schemaSArray = lo.Uniq(schemaSArray)
	schemaTArray = lo.Uniq(schemaTArray)

	oracleConn, openOracleErr := models.OpenOracle(models.BuildOracleConfigFromDatasource(datasourceS, ""))
	if openOracleErr != nil {
		log.Error("open oracle db failed, taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, taskInfo.ChannelId, openOracleErr)
		return nil, nil, nil, nil, openOracleErr
	}
	log.Info("open oracle db success, get oracle tables.", zap.Int("task_id", taskInfo.TaskID), zap.Int("channel_id", taskInfo.ChannelId), zap.Any("schemaSArray", schemaSArray))
	oracleTables, getOTableErr := models.GetDatasourceReaderWriter().GetOracleTables(ctx, oracleConn, schemaSArray)
	if getOTableErr != nil {
		log.Error("get oracle tables failed, taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, taskInfo.ChannelId, getOTableErr)
		return nil, nil, nil, nil, getOTableErr
	}

	tidbConn, openTiDBErr := models.OpenMysql(datasourceT.UserName, datasourceT.PasswordValue, datasourceT.HostIp, datasourceT.HostPort, "")
	if openTiDBErr != nil {
		log.Error("open tidb db failed, taskId:%d, channelId:%d, err:%v", taskInfo.TaskID, taskInfo.ChannelId, openTiDBErr)
		return nil, nil, nil, nil, openTiDBErr
	}
	log.Info("open tidb db success, get tidb tables, taskId:%d, channelId:%d, schemaTArray:%v", taskInfo.TaskID, taskInfo.ChannelId, schemaTArray)
	tidbTables, getTableErr := models.GetDatasourceReaderWriter().GetMysqlTables(ctx, tidbConn, schemaTArray)
	if getTableErr != nil {
		log.Error("get tidb tables failed, taskId:%d, channelId:%d, schemaTArray:%v, err:%v", taskInfo.TaskID, taskInfo.ChannelId, schemaTArray, getTableErr)
		return nil, nil, nil, nil, getTableErr
	}

	oracleTableMap := make(structs.SchemaTableMapping)
	tidbTableMap := make(structs.SchemaTableMapping)
	for _, oracleSchema := range oracleTables {
		for _, oracleObj := range oracleSchema.ObjectTypes {
			for _, oracleTable := range oracleObj.Tables {
				oracleTableMap[structs.SchemaTablePair{SchemaName: oracleSchema.SchemaName, TableName: oracleTable.TableName}] = true
			}
		}
	}
	for _, tidbSchema := range tidbTables {
		for _, tidbObj := range tidbSchema.ObjectTypes {
			for _, tidbTable := range tidbObj.Tables {
				tidbTableMap[structs.SchemaTablePair{SchemaName: targetSchemaCaseFunc(tidbSchema.SchemaName), TableName: targetTableCaseFunc(tidbTable.TableName)}] = true
			}
		}
	}

	log.Debugf("mapping oracleTableMap:%v, tidbTableMap:%v, channelId:%d, taskId:%d", oracleTableMap.String(), tidbTableMap.String(), taskInfo.ChannelId, taskInfo.TaskID)
	existTables := make([]*channel.ChannelSchemaTable, 0)
	oracleNotExistTables := make([]*channel.ChannelSchemaTable, 0)
	tidbNotExistTables := make([]*channel.ChannelSchemaTable, 0)
	for _, channelSchemaTable := range channelSchemaTables {
		pairS := structs.SchemaTablePair{SchemaName: channelSchemaTable.SchemaNameS, TableName: channelSchemaTable.TableNameS}
		pairT := structs.SchemaTablePair{SchemaName: targetSchemaCaseFunc(channelSchemaTable.SchemaNameT), TableName: targetTableCaseFunc(channelSchemaTable.TableNameT)}
		log.Debugf("check table exist, sourceTable:%v, targetTable:%v, tableInOracle:%v, tableInTiDB:%v, channelId:%d, taskId:%d", pairS, pairT, oracleTableMap[pairS], tidbTableMap[pairT], taskInfo.ChannelId, taskInfo.TaskID)
		if _, ok := oracleTableMap[pairS]; !ok {
			oracleNotExistTables = append(oracleNotExistTables, channelSchemaTable)
			continue
		}
		if _, ok := tidbTableMap[pairT]; !ok {
			tidbNotExistTables = append(tidbNotExistTables, channelSchemaTable)
			continue
		}
		existTables = append(existTables, channelSchemaTable)
	}
	existTablesIds := lo.Map(existTables, func(v *channel.ChannelSchemaTable, _ int) int {
		return v.ChannelSchtableId
	})
	return existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, nil
}
