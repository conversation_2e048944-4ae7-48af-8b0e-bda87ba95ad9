package common

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"os"
	"path"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	stringUtil "gitee.com/pingcap_enterprise/tms/util/string"
)

var defaultColumnDataCsvHeader = []string{
	"通道ID",
	"源Schema", "源表名", "源列名称", "目标列名称",
	"列顺序编号",
	"源列类型", "目标列类型",
	"源列缺省值", "目标列缺省值",
	"源列数据长度", "目标列数据长度",
	"源列PRECISION", "目标列PRECISION",
	"源列SCALE", "目标列SCALE",
	"源列是否允许为空", "目标列是否为空",
	"源列备注", "目标列备注",
	"DIGEST"}

func GetDefaultColumnDataHandler() *ColumnDataCsvHandler {
	return &ColumnDataCsvHandler{
		csvHeader: defaultColumnDataCsvHeader,
	}
}

type ColumnDataCsvHandler struct {
	csvHeader   []string
	csvDir      string
	csvFileName string
	channelId   int
}

func (i *ColumnDataCsvHandler) SetCsvDir(dir string) *ColumnDataCsvHandler {
	i.csvDir = dir
	return i
}

func (i *ColumnDataCsvHandler) SetChannelId(channelId int) *ColumnDataCsvHandler {
	i.channelId = channelId
	return i
}

func (i *ColumnDataCsvHandler) SetCsvFileName(fileName string) *ColumnDataCsvHandler {
	i.csvFileName = fileName
	return i
}

func (i *ColumnDataCsvHandler) SetCsvHeader(header []string) *ColumnDataCsvHandler {
	i.csvHeader = header
	return i
}

func (i *ColumnDataCsvHandler) ReadCSVFile() ([][]string, error) {
	fp := path.Join(i.csvDir, i.csvFileName)
	csvFile, openCsvErr := os.Open(fp)
	if openCsvErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_OPEN_FILE_FAILED, "open column data csv file failed, %v", openCsvErr)
	}
	csvReader := csv.NewReader(bufio.NewReader(csvFile))

	records, readErr := csvReader.ReadAll()
	if readErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_OPEN_FILE_FAILED, "read column data csv file failed, %v", readErr)
	}
	return records, nil
}

type CSVColumnSummary struct {
	TotalColumnNum int64
	SkipColumnNum  int64

	ValidColumnNum int64
	ValidTableNum  int64
}

func (i *ColumnDataCsvHandler) ExtractColumnDataForUpdate(wrappers []*ColumnDataWrapper) ([]*channel.TableColumnCustomMapRule, *CSVColumnSummary, error) {
	summary := &CSVColumnSummary{}
	schemaTablePairs := make(map[structs.SchemaTablePair]bool)
	rets := make([]*channel.TableColumnCustomMapRule, 0, len(wrappers))
	for _, w := range wrappers {
		if w.HasNotModified() {
			continue
		}
		columnDatum := w.GetColumnDatum()
		schemaTablePairs[structs.SchemaTablePair{SchemaName: columnDatum.SchemaNameS, TableName: columnDatum.TableNameS}] = true
		rets = append(rets, columnDatum)
	}

	summary.SkipColumnNum = int64(len(wrappers)) - int64(len(rets))
	summary.TotalColumnNum = int64(len(wrappers))
	summary.ValidColumnNum = int64(len(rets))
	summary.ValidTableNum = int64(len(schemaTablePairs))

	if len(rets) == 0 {
		extractErr := errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_INVALID, "no changed column data found in csv file")
		return nil, summary, extractErr
	}

	return rets, summary, nil
}

// ValidateColumnEntity validate
func (i *ColumnDataCsvHandler) ValidateColumnEntity(wrappers []*ColumnDataWrapper) error {
	for _, wrapper := range wrappers {
		columnDatum := wrapper.datum

		if columnDatum.SchemaNameS == "" || columnDatum.TableNameS == "" || columnDatum.ColumnNameS == "" {
			return errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_INVALID, "schemaNameS, tableNameS, columnNameS should not be empty, Column[%s.%s.%s]", columnDatum.SchemaNameS, columnDatum.TableNameS, columnDatum.ColumnNameS)
		}
		if columnDatum.ColumnID == 0 {
			return errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_INVALID, "columnID should not be 0, Column[%s.%s.%s]", columnDatum.SchemaNameS, columnDatum.TableNameS, columnDatum.ColumnNameS)
		}

		primaryFieldChecksum := i.ColumnPrimaryFieldChecksum(columnDatum)
		sourceFieldChecksum := i.ColumnSourceFieldChecksum(columnDatum)
		allFieldChecksum := i.ColumnAllFieldChecksum(columnDatum)

		if primaryFieldChecksum != wrapper.csvChecksumData.PrimaryFieldChecksum {
			log.Errorf("primaryFieldChecksum not match, Column[%s.%s.%s], csvChecksum:%s, currentChecksum:%s", columnDatum.SchemaNameS, columnDatum.TableNameS, columnDatum.ColumnNameS, wrapper.csvChecksumData.PrimaryFieldChecksum, primaryFieldChecksum)
			return errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_INVALID, "primaryFieldChecksum not match, Column[%s.%s.%s]", columnDatum.SchemaNameS, columnDatum.TableNameS, columnDatum.ColumnNameS)
		}
		if sourceFieldChecksum != wrapper.csvChecksumData.SourceFieldChecksum {
			log.Errorf("sourceFieldChecksum not match, Column[%s.%s.%s], csvChecksum:%s, currentChecksum:%s", columnDatum.SchemaNameS, columnDatum.TableNameS, columnDatum.ColumnNameS, wrapper.csvChecksumData.SourceFieldChecksum, sourceFieldChecksum)
			return errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_INVALID, "sourceFieldChecksum not match, source fields or checksum changed, Column[%s.%s.%s]", columnDatum.SchemaNameS, columnDatum.TableNameS, columnDatum.ColumnNameS)
		}

		wrapper.csvChecksumData.AllFieldChecksumMatch = allFieldChecksum == wrapper.csvChecksumData.AllFieldChecksum
	}
	return nil
}

func (i *ColumnDataCsvHandler) ParseColumnRecordToEntity(record []string) (*ColumnDataWrapper, error) {

	if len(record) != len(i.csvHeader) {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_HEADER_NOT_MATCH, "column data csv header not match")
	}

	var parseUintErr error
	var schemaNameS, tableNameS, columnNameS string

	columnNameFunc := func() string {
		return schemaNameS + "." + tableNameS + "." + columnNameS
	}

	channelId, parseIntErr := parse.ParseInt(record[0])
	if parseIntErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse channel id failed")
	}
	schemaNameS = record[1]
	tableNameS = record[2]
	columnNameS = record[3]
	columnNameT := record[4]
	columnID, parseUintErr := parse.ParseUInt(record[5])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse column id failed, Column[%s]", columnNameFunc())
	}

	dataTypeS := record[6]
	dataTypeT := record[7]

	dataDefaultS := record[8]
	dataDefaultT := record[9]

	dataLengthS, parseUintErr := parse.ParseUInt(record[10])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse source data length failed, Column[%s]", columnNameFunc())
	}
	dataLengthT, parseUintErr := parse.ParseUInt(record[11])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse target data length failed, Column[%s]", columnNameFunc())
	}

	dataPrecisionS, parseUintErr := parse.ParseUInt(record[12])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse source data precision failed, Column[%s]", columnNameFunc())
	}
	dataPrecisionT, parseUintErr := parse.ParseUInt(record[13])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse target data precision failed, Column[%s]", columnNameFunc())
	}

	dataScaleS, parseUintErr := parse.ParseUInt(record[14])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse source data scale failed, Column[%s]", columnNameFunc())
	}
	dataScaleT, parseUintErr := parse.ParseUInt(record[15])
	if parseUintErr != nil {
		return nil, errors.NewErrorf(errors.TMS_COLUMN_DATA_CSV_DATA_PARSE_FAILED, "parse target data scale failed, Column[%s]", columnNameFunc())
	}

	nullableS := record[16]
	nullableT := record[17]
	columnCommentS := record[18]
	columnCommentT := record[19]

	primaryFieldChecksum, sourceFieldChecksum, allFieldChecksum := i.splitChecksumString(record[20])

	columnDatum := &channel.TableColumnCustomMapRule{
		ChannelId:   channelId,
		SchemaNameS: schemaNameS, TableNameS: tableNameS, ColumnNameS: columnNameS, ColumnNameT: columnNameT,
		ColumnID:  columnID,
		DataTypeS: dataTypeS, DataTypeT: dataTypeT,
		DataDefaultS: dataDefaultS, DataDefaultT: dataDefaultT,
		DataLengthS: dataLengthS, DataLengthT: dataLengthT,
		DataPrecisionS: dataPrecisionS, DataPrecisionT: dataPrecisionT,
		DataScaleS: dataScaleS, DataScaleT: dataScaleT,
		NullableS: nullableS, NullableT: nullableT,
		ColumnCommentS: columnCommentS, ColumnCommentT: columnCommentT,
		ColumnDigest: primaryFieldChecksum,
	}

	columnDatumWrapper := ColumnDataWrapper{
		datum: columnDatum,
		csvChecksumData: Checksum{
			PrimaryFieldChecksum:  primaryFieldChecksum,
			SourceFieldChecksum:   sourceFieldChecksum,
			AllFieldChecksum:      allFieldChecksum,
			AllFieldChecksumMatch: false,
		},
	}

	return &columnDatumWrapper, nil
}

func (i *ColumnDataCsvHandler) ParseColumnRecordsToColumnWrappers(records [][]string) ([]*ColumnDataWrapper, error) {
	columnWrapperData := make([]*ColumnDataWrapper, 0, len(records))
	for idx, record := range records {
		if idx == 0 {
			continue
		}

		column, parseErr := i.ParseColumnRecordToEntity(record)
		if parseErr != nil {
			return nil, parseErr
		}

		columnWrapperData = append(columnWrapperData, column)
	}
	return columnWrapperData, nil
}

func (i *ColumnDataCsvHandler) joinChecksumString(primaryField, sourceField, allField string) string {
	return primaryField + "|" + sourceField + "|" + allField
}

func (i *ColumnDataCsvHandler) splitChecksumString(checksums string) (primaryField, sourceField, allField string) {
	checksumArray := strings.Split(checksums, "|")
	if len(checksumArray) != 3 {
		return "", "", ""
	}
	return checksumArray[0], checksumArray[1], checksumArray[2]
}

func (i *ColumnDataCsvHandler) WriteColumnData(data []*channel.TableColumnCustomMapRule) error {
	csvRecords := make([][]string, len(data)+1)
	csvRecords[0] = i.csvHeader

	for idx, column := range data {

		primaryFieldChecksum := i.ColumnPrimaryFieldChecksum(column)
		sourceFieldChecksum := i.ColumnSourceFieldChecksum(column)
		allFieldChecksum := i.ColumnAllFieldChecksum(column)

		csvRecords[idx+1] = []string{
			parse.FormatInt(column.ChannelId),
			column.SchemaNameS, column.TableNameS, column.ColumnNameS, column.ColumnNameT,
			parse.FormatUint(column.ColumnID),
			column.DataTypeS, column.DataTypeT,
			column.DataDefaultS, column.DataDefaultT,
			parse.FormatUint(column.DataLengthS), parse.FormatUint(column.DataLengthT),
			parse.FormatUint(column.DataPrecisionS), parse.FormatUint(column.DataPrecisionT),
			parse.FormatUint(column.DataScaleS), parse.FormatUint(column.DataScaleT),
			column.NullableS, column.NullableT,
			column.ColumnCommentS, column.ColumnCommentT,
			i.joinChecksumString(primaryFieldChecksum, sourceFieldChecksum, allFieldChecksum),
		}
	}

	writeCsvErr := file.OpenAndWriteToCSVFile(i.csvDir, i.csvFileName, csvRecords)
	return writeCsvErr
}

func (i *ColumnDataCsvHandler) ColumnSourceFieldChecksum(column *channel.TableColumnCustomMapRule) string {
	fields := []string{
		column.SchemaNameS, column.TableNameS, column.ColumnNameS,
		column.DataTypeS,
		column.DataDefaultS,
		parse.FormatUint(column.DataLengthS),
		parse.FormatUint(column.DataPrecisionS),
		parse.FormatUint(column.DataScaleS),
		column.NullableS, column.ColumnCommentS,
	}
	sourceFieldsStr := strings.Join(fields, ",")
	checksum := stringUtil.MD5(sourceFieldsStr, stringUtil.WithMD5Upper(), stringUtil.WithMD5Salt(constants.TMS_MD5_SALT))
	return checksum
}

func (i *ColumnDataCsvHandler) ColumnPrimaryFieldChecksum(column *channel.TableColumnCustomMapRule) string {
	fields := []string{
		column.SchemaNameS, column.TableNameS, column.ColumnNameS,
	}
	sourceFieldsStr := strings.Join(fields, ",")
	checksum := stringUtil.MD5(sourceFieldsStr, stringUtil.WithMD5Upper(), stringUtil.WithMD5Salt(constants.TMS_MD5_SALT))
	return checksum
}

func (i *ColumnDataCsvHandler) CalculateFieldsChecksum(schemaNameS, tableNameS, columnNameS string) string {
	fields := []string{
		schemaNameS, tableNameS, columnNameS,
	}
	sourceFieldsStr := strings.Join(fields, ",")
	checksum := stringUtil.MD5(sourceFieldsStr, stringUtil.WithMD5Upper(), stringUtil.WithMD5Salt(constants.TMS_MD5_SALT))
	return checksum
}

func (i *ColumnDataCsvHandler) ColumnAllFieldChecksum(column *channel.TableColumnCustomMapRule) string {
	fields := []string{
		parse.FormatInt(column.ChannelId),
		column.SchemaNameS, column.TableNameS, column.ColumnNameS, column.ColumnNameT,
		parse.FormatUint(column.ColumnID),
		column.DataTypeS, column.DataTypeT,
		column.DataDefaultS, column.DataDefaultT,
		parse.FormatUint(column.DataLengthS), parse.FormatUint(column.DataLengthT),
		parse.FormatUint(column.DataPrecisionS), parse.FormatUint(column.DataPrecisionT),
		parse.FormatUint(column.DataScaleS), parse.FormatUint(column.DataScaleT),
		column.NullableS, column.NullableT,
		column.ColumnCommentS, column.ColumnCommentT,
	}
	sourceFieldsStr := strings.Join(fields, ",")
	checksum := stringUtil.MD5(sourceFieldsStr, stringUtil.WithMD5Upper(), stringUtil.WithMD5Salt(constants.TMS_MD5_SALT))
	return checksum
}

func (i *ColumnDataCsvHandler) ValidateColumnMessages(data []*message.TableColumnCustomMapRule) error {
	if len(data) == 0 {
		return errors.NewErrorf(errors.TMS_UPDATE_COLUMN_DIGEST_NODATA, "no column data found")
	}
	for _, item := range data {
		if item.ColumnDigest == "" {
			validateErr := errors.NewErrorf(errors.TMS_UPDATE_COLUMN_DIGEST_MISSED, fmt.Sprintf("Schema:%s, Table:%s, Column:%s", item.SchemaNameS, item.TableNameS, item.ColumnNameS))
			return validateErr
		}
		if i.CalculateFieldsChecksum(item.SchemaNameS, item.TableNameS, item.ColumnNameS) != item.ColumnDigest {
			validateErr := errors.NewErrorf(errors.TMS_UPDATE_COLUMN_DIGEST_NOT_EQUAL, fmt.Sprintf("Schema:%s, Table:%s, Column:%s", item.SchemaNameS, item.TableNameS, item.ColumnNameS))
			return validateErr
		}
	}
	return nil
}

type Checksum struct {
	PrimaryFieldChecksum string
	SourceFieldChecksum  string
	AllFieldChecksum     string

	AllFieldChecksumMatch bool
}

type ColumnDataWrapper struct {
	datum           *channel.TableColumnCustomMapRule
	csvChecksumData Checksum
}

func (i *ColumnDataWrapper) HasNotModified() bool {
	return i.csvChecksumData.AllFieldChecksumMatch
}

func (i *ColumnDataWrapper) GetColumnDatum() *channel.TableColumnCustomMapRule {
	return i.datum
}
