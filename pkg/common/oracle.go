package common

import (
	"context"
	"database/sql"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type OracleStore interface {
	DBVersion(ctx context.Context) (string, error)
	DBVersionCode(ctx context.Context) (string, error)
	TableColumnsWithSQL(ctx context.Context, sql string) ([]*message.TableColumnCustomMapRule, error)
	TableColumnsChanWithSQL(ctx context.Context, sql string, ruleChan chan *channel.TableColumnCustomMapRule) error
	CountColumnsWithSQL(ctx context.Context, sql string) (int64, error)
}

type OracleStoreImpl struct {
	conn *oracle.Oracle
}

func NewOracleStore(conn *oracle.Oracle) OracleStore {
	return &OracleStoreImpl{conn: conn}
}

func (i *OracleStoreImpl) DBVersion(ctx context.Context) (string, error) {
	querySQL := `SELECT BANNER FROM v$version`
	_, res, err := oracle.Query(ctx, i.conn.OracleDB, querySQL)
	if err != nil {
		return "", err
	}
	return res[0]["BANNER"], nil
}

func (i *OracleStoreImpl) DBVersionCode(ctx context.Context) (string, error) {
	querySQL := `select value from v$parameter a where a.NAME='compatible'`
	_, res, err := oracle.Query(ctx, i.conn.OracleDB, querySQL)
	if err != nil {
		return "", err
	}
	return res[0]["VALUE"], nil
}

func (i *OracleStoreImpl) CountColumnsWithSQL(ctx context.Context, sql string) (int64, error) {
	_, res, err := oracle.Query(ctx, i.conn.OracleDB, sql)
	if err != nil {
		return 0, err
	}
	return strconv.ParseInt(res[0]["COUNT"], 10, 64)
}

func (i *OracleStoreImpl) TableColumnsChanWithSQL(ctx context.Context, querySQL string, ruleChan chan *channel.TableColumnCustomMapRule) error {
	var totalScanNum int

	defer close(ruleChan)
	defer func() {
		log.Infof("TableColumnsChanWithSQL done, total scan num:%d", totalScanNum)
	}()

	rows, err := i.conn.OracleDB.QueryContext(ctx, querySQL)
	if err != nil {
		log.Errorf("oracle db query failed, sql:%s, err:%s", querySQL, err)
		return err
	}
	defer rows.Close()

	var owner, tableName, columnName, columnId, comment, dataDefault, dataType, dataLength, dataPrecision, dataScale, nullable *sql.NullString

	getStringContent := func(ns *sql.NullString) string {
		if ns == nil || !ns.Valid {
			return ""
		}
		return ns.String
	}

	for rows.Next() {
		totalScanNum++
		err = rows.Scan(
			&owner,
			&tableName,
			&columnName,
			&comment,
			&columnId,
			&dataDefault,
			&dataType,
			&dataLength,
			&dataPrecision,
			&dataScale,
			&nullable,
		)
		if err != nil {
			log.Errorf("rows.Scan oracle db failed, sql:%d, err:%s", querySQL, err)
			return err
		}
		rule := channel.TableColumnCustomMapRule{
			SchemaNameS:    getStringContent(owner),
			TableNameS:     getStringContent(tableName),
			ColumnID:       parseStrToUint(getStringContent(columnId)),
			ColumnNameS:    getStringContent(columnName),
			ColumnNameT:    getStringContent(columnName),
			ColumnCommentS: getStringContent(comment),
			ColumnCommentT: getStringContent(comment),
			DataDefaultS:   strings.TrimSpace(getStringContent(dataDefault)),
			DataDefaultT:   strings.TrimSpace(getStringContent(dataDefault)),
			DataTypeS:      getStringContent(dataType),
			DataTypeT:      getStringContent(dataType),
			DataLengthS:    parseStrToUint(getStringContent(dataLength)),
			DataLengthT:    parseStrToUint(getStringContent(dataLength)),
			DataPrecisionS: parseStrToUint(getStringContent(dataPrecision)),
			DataPrecisionT: parseStrToUint(getStringContent(dataPrecision)),
			DataScaleS:     parseStrToUint(getStringContent(dataScale)),
			DataScaleT:     parseStrToUint(getStringContent(dataScale)),
			NullableS:      getStringContent(nullable),
			NullableT:      getStringContent(nullable),
		}
		ruleChan <- &rule
	}
	return nil
}

func (i *OracleStoreImpl) TableColumnsWithSQL(ctx context.Context, sql string) ([]*message.TableColumnCustomMapRule, error) {
	_, res, err := oracle.Query(ctx, i.conn.OracleDB, sql)
	if err != nil {
		return nil, err
	}

	rets := make([]*message.TableColumnCustomMapRule, 0, len(res))

	for _, v := range res {
		rets = append(rets, &message.TableColumnCustomMapRule{
			SchemaNameS:    v["OWNER"],
			TableNameS:     v["TABLE_NAME"],
			ColumnID:       parseStrToUint(v["COLUMN_ID"]),
			ColumnNameS:    v["COLUMN_NAME"],
			ColumnNameT:    v["COLUMN_NAME"],
			ColumnCommentS: v["COMMENTS"],
			ColumnCommentT: v["COMMENTS"],
			DataDefaultS:   strings.TrimSpace(v["DATA_DEFAULT"]),
			DataDefaultT:   strings.TrimSpace(v["DATA_DEFAULT"]),
			DataTypeS:      v["DATA_TYPE"],
			DataTypeT:      v["DATA_TYPE"],
			DataLengthS:    parseStrToUint(v["DATA_LENGTH"]),
			DataLengthT:    parseStrToUint(v["DATA_LENGTH"]),
			DataPrecisionS: parseStrToUint(v["DATA_PRECISION"]),
			DataPrecisionT: parseStrToUint(v["DATA_PRECISION"]),
			DataScaleS:     parseStrToUint(v["DATA_SCALE"]),
			DataScaleT:     parseStrToUint(v["DATA_SCALE"]),
			NullableS:      v["NULLABLE"],
			NullableT:      v["NULLABLE"],
		})
	}

	return rets, nil
}

func parseStrToUint(v string) uint {
	if v == "" {
		return 0
	}
	uintVal, _ := strconv.ParseUint(v, 10, 64)
	return uint(uintVal)
}
