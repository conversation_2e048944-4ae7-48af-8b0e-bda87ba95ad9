package common

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/assessment"
	fileUtil "gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/gomarkdown/markdown"
	"github.com/gomarkdown/markdown/html"
	"github.com/gomarkdown/markdown/parser"
)

type ReportData interface {
	GetFieldByName(name string) reflect.Value
}

// sanitizeMdCell 统一转义 Markdown 单元格中的特殊字符，避免影响表格结构与样式渲染
func sanitizeMdCell(s string) string {
	if s == "" {
		return ""
	}
	// 统一换行符并替换为 <br>
	s = strings.ReplaceAll(s, "\r\n", "\n")
	s = strings.ReplaceAll(s, "\n", "<br>")
	// 先处理反斜杠与表格分隔符
	s = strings.ReplaceAll(s, "\\", "\\\\")
	s = strings.ReplaceAll(s, "|", "\\|")
	// 转义其它常见 Markdown 符号
	replacer := strings.NewReplacer(
		"*", "\\*", "_", "\\_", "`", "\\`", "~", "\\~",
		"[", "\\[", "]", "\\]",
		"#", "\\#", "+", "\\+", "-", "\\-", "!", "\\!",
		"$", "\\$",
	)
	s = replacer.Replace(s)
	return strings.TrimSpace(s)
}

const (
	OBJECT_ASS_REPORT           int = 1
	SQL_COMPATIBILITY_REPORT    int = 2
	SQL_COMPATIBILITY_REPORT_V2 int = 3
	SQL_PERFORMANCE_REPORT      int = 4
)

func GenerateReport(ctx context.Context, taskId, rptType int, title, dataDir, zipName string, htmlData ReportData) error {
	log.Debugf("start GenerateReport, taskid:%d,  report:%v", taskId, zipName)
	if htmlData == nil {
		log.Errorf("GenerateReport error: htmlData is nil")
		return fmt.Errorf("htmlData is nil")
	}

	fileUtil.RemoveFile(dataDir)
	fileUtil.RemoveFile(zipName)

	htmlTemplateDetail, err := models.GetAssessReaderWriter().GetHtmlTemplateDetail(ctx, "O2M_OBJECT_ASSESS_REPORT")
	if err != nil {
		log.Errorf("Get htmlTemplateDetail err:%s", err)
	}
	htmlTemplateDetail = strings.ReplaceAll(htmlTemplateDetail, "{{.Title}}", title)

	htmlRptCfgs, err := models.GetAssessReaderWriter().GetHtmlRptCfg(ctx, rptType)
	if err != nil {
		log.Errorf("GetHtmlRptCfg err:%s", err)
	}

	fileUtil.MkdirIfNotExist(dataDir)

	defaultRef := ""
	htmlBody := `<body>
<div class="container">
  <div class="left">
    <ul style="list-style-type: square;">`
	for _, v := range htmlRptCfgs {
		if v.ParentRptId == 0 {
			if strings.EqualFold(defaultRef, "") {
				defaultRef = fmt.Sprintf("./%s%s", v.FilePath, v.FileName)
			}
			htmlBody += fmt.Sprintf(`<li class="awr"><a class="awr" href="./%s%s" target="content" >%s</a><ol>`, v.FilePath, v.FileName, v.RptName)
			if !strings.EqualFold(v.RangeListName, "") {
				GenerateSubReport(dataDir, v, htmlData)
			}
		}
		for _, subv := range htmlRptCfgs {
			if subv.ParentRptId == v.RptId {
				htmlBody += fmt.Sprintf(`<li class="awr"><a class="awr" href="./%s%s" target="content" style="padding-left: 10;">%s</a></li>`, subv.FilePath, subv.FileName, subv.RptName)
				if !strings.EqualFold(subv.RangeListName, "") {
					GenerateSubReport(dataDir, subv, htmlData)
				}
			}
		}
		if v.ParentRptId == 0 {
			htmlBody += fmt.Sprintf(`</ol></li>`)
		}
	}
	htmlBody += `</ul>
  </div>
  <div class="right">
		<iframe name="content" src="` + defaultRef + `" width="100%" height="100%" style="border:0"></iframe>
  </div>
</div>
</body>
</html>
`
	htmlTemplateDetail += htmlBody
	if err != nil {
		log.Errorf("Get htmlDetail err:%s", err)
	}

	reportFilename := fmt.Sprintf("%s/index.html", dataDir)
	err = fileUtil.CreateAndWriteFile(reportFilename, htmlTemplateDetail)
	if err != nil {
		log.Errorf("Get htmlDetail err:%s", err)
	}
	log.Debugf("finish create :%s", reportFilename)

	GenerateCss(ctx, dataDir)

	log.Debugf("start zip report, dataDir:%s, report:%s", dataDir, zipName)
	err = fileUtil.ZipPathWithFolders(dataDir, zipName)
	if err != nil {
		log.Errorf("ZipPathWithFolders err:%s", err)
	}
	log.Debugf("finish zip report, dataDir:%s, report:%s", dataDir, zipName)

	log.Debugf("GenerateReport finish.taskId:%d.", taskId)
	return nil
}

func GenerateCss(ctx context.Context, reportPath string) {
	log.Debugf("start GenerateCss:%s", fmt.Sprintf("%s/common.css", reportPath))

	htmlTemplateDetail, err := models.GetAssessReaderWriter().GetHtmlTemplateDetail(ctx, "O2M_OBJECT_ASSESS_REPORT_CSS")
	if err != nil {
		log.Errorf("Get htmlTemplateDetail err:%s", err)
	}

	templateFilename := fmt.Sprintf("%s/common.css", reportPath)
	file, err := os.Create(templateFilename)
	if err != nil {
		log.Errorf("create templateFilename err:%s", err)
	}

	file.WriteString(htmlTemplateDetail)
	file.Close()
	log.Debugf("finish GenerateCss:%s", reportPath)
}

func GenerateSubReport(dataDir string, htmlRptCfg *assessment.HtmlRptCfg, htmlData ReportData) {
	log.Debugf("start GenerateSubReport:%s", fmt.Sprintf("%s%s%s", dataDir, htmlRptCfg.FilePath, htmlRptCfg.FileName))
	fileUtil.MkdirIfNotExist(fmt.Sprintf("%s%s", dataDir, htmlRptCfg.FilePath))

	markdownInfo := createMarkdownInfo(htmlData, htmlRptCfg)
	//log.Infof("createMarkdownInfo: %s = %s", htmlRptCfg.RangeListName, markdownInfo)

	md := []byte(markdownInfo)
	html := MdToHTML(md)

	fullHTML := fmt.Sprintf(`
	<html>
		<head>
		<meta charset="utf-8">
		<link rel="stylesheet" href="../common.css" type="text/css">
		<script type="text/javascript">
		function toggleErrorMessages(id) {
			var detail = document.getElementById('error-detail-' + id);
			var toggle = document.getElementById('error-toggle-' + id);
			if (detail && toggle) {
				if (detail.style.display === 'none') {
					detail.style.display = 'block';
					toggle.textContent = 'Collapse All';
				} else {
					detail.style.display = 'none';
					var count = detail.children.length;
					toggle.textContent = 'Expand All (' + count + ')';
				}
			}
		}
		</script>
		</head>
		<body>%s</body>
	</html>`, string(html))

	reportNewFile := fmt.Sprintf("%s%s%s", dataDir, htmlRptCfg.FilePath, htmlRptCfg.FileName)
	err := fileUtil.CreateAndWriteFile(reportNewFile, fullHTML)
	if err != nil {
		log.Errorf("GenerateSubReport err:%s", err)
	}
	log.Debugf("finish GenerateSubReport:%s", fmt.Sprintf("%s%s%s", dataDir, htmlRptCfg.FilePath, htmlRptCfg.FileName))
}

func createMarkdownInfo(htmlData ReportData, htmlRptCfg *assessment.HtmlRptCfg) string {
	var markdownInfo strings.Builder

	headers := strings.Split(htmlRptCfg.HeaderNames, "|")
	filedNames := strings.Split(htmlRptCfg.FieldNames, "|")

	// 生成 markdown 标题
	markdownInfo.WriteString("# ")
	markdownInfo.WriteString(htmlRptCfg.RptName)
	markdownInfo.WriteString(`
`)

	for _, header := range headers {
		markdownInfo.WriteString(sanitizeMdCell(header))
		markdownInfo.WriteString("|")
	}
	markdownInfo.WriteString(`
`)

	for range headers {
		markdownInfo.WriteString(`----|`)
	}
	markdownInfo.WriteString(`
`)

	// 目标字段名称
	targetObject := htmlRptCfg.RangeListName

	// 使用反射访问字段
	//value := reflect.ValueOf(dataMap).Elem() // 获取 dataMap 的值（解引用指针）
	//field := value.FieldByName(targetObject) // 通过字段名获取字段值
	field := htmlData.GetFieldByName(targetObject)

	// 检查字段是否存在
	if !field.IsValid() {
		log.Errorf("createMarkdownInfo error: Field %s does not exist in dataMap\n", targetObject)
		return ""
	}

	// 检查字段是否是切片类型
	if field.Kind() != reflect.Slice {
		log.Errorf("createMarkdownInfo error: Field %s is not a slice\n", targetObject)
		return ""
	}

	// 遍历切片
	for i := 0; i < field.Len(); i++ {
		element := field.Index(i) // 获取切片中的元素

		// 仅对指针类型检查 IsNil
		if element.Kind() == reflect.Ptr {
			if element.IsNil() {
				log.Errorf("createMarkdownInfo error: Element %d in %s is nil\n", i, targetObject)
				continue
			}
			element = element.Elem() // 解引用指针
		}

		// 现在 element 是结构体（来自非指针切片）或解引用后的结构体
		if element.Kind() != reflect.Struct {
			log.Errorf("createMarkdownInfo error: Element %d in %s is not a struct\n", i, targetObject)
			continue
		}

		for _, filedName := range filedNames {
			if strings.HasPrefix(filedName, "=") {
				filedName = strings.TrimPrefix(filedName, "=")
				markdownInfo.WriteString(sanitizeMdCell(filedName))
				markdownInfo.WriteString("|")
				continue
			}
			
			// 特殊处理MessageLog字段 - 显示展开/折叠功能（虽然字段名是MessageLog，但内部使用MessageLogs存储多个信息）
			if filedName == "MessageLog" {
				// 检查是否有MessageLogs字段（包含多个错误信息）
				mLogsField := element.FieldByName("MessageLogs")
				if mLogsField.IsValid() && mLogsField.Kind() == reflect.Slice && mLogsField.Len() > 0 {
					// 获取错误码用于生成唯一ID
					errorCodeField := element.FieldByName("ReplayExecCode")
					errorCode := ""
					if errorCodeField.IsValid() {
						errorCode = strings.ReplaceAll(errorCodeField.String(), " ", "_")
						errorCode = strings.ReplaceAll(errorCode, ":", "_")
						errorCode = strings.ReplaceAll(errorCode, "-", "_")
					}
					
					// 收集所有非空的错误信息
					var messages []string
					for j := 0; j < mLogsField.Len(); j++ {
						msg := mLogsField.Index(j).String()
						if msg != "" {
							messages = append(messages, msg)
						}
					}
					
					if len(messages) == 0 {
						markdownInfo.WriteString(`-|`)
						continue
					}
					
					// 如果只有一个错误信息，直接显示
					if len(messages) == 1 {
						markdownInfo.WriteString(sanitizeMdCell(messages[0]))
						markdownInfo.WriteString("|")
						continue
					}
					
					// 多个错误信息，生成带展开功能的HTML
					// 显示第一个错误信息（截断过长的）
					firstMsg := messages[0]
					if len(firstMsg) > 200 {
						firstMsg = firstMsg[:200] + "..."
					}
					
					// 生成HTML内容，注意避免markdown转义
					htmlContent := fmt.Sprintf(`%s<br><span style="color:#0066CC;cursor:pointer;text-decoration:underline;" onclick="toggleErrorMessages('%s')"><span id="error-toggle-%s">Expand All (%d)</span></span><div id="error-detail-%s" style="display:none;margin-top:5px;padding:10px;background:#f5f5f5;border:1px solid #ddd;max-height:300px;overflow-y:auto;">`,
						sanitizeMdCell(firstMsg), errorCode, errorCode, len(messages), errorCode)
					
					// 添加所有错误信息
					for idx, msg := range messages {
						// 每个错误信息换行显示，并编号
						htmlContent += fmt.Sprintf(`<div style="padding:5px 0;border-bottom:1px solid #eee;word-wrap:break-word;"><strong>%d.</strong> %s</div>`, 
							idx+1, sanitizeMdCell(msg))
					}
					htmlContent += `</div>`
					
					markdownInfo.WriteString(htmlContent)
					markdownInfo.WriteString("|")
					continue
				}
				
				// 如果没有MessageLogs，尝试使用MessageLog字段（向后兼容）
				mLogField := element.FieldByName("MessageLog")
				if mLogField.IsValid() && mLogField.Kind() == reflect.String {
					valueStr := mLogField.String()
					if valueStr == "" {
						markdownInfo.WriteString(`-|`)
					} else {
						markdownInfo.WriteString(sanitizeMdCell(valueStr))
						markdownInfo.WriteString("|")
					}
					continue
				}
				
				markdownInfo.WriteString(`-|`)
				continue
			}
			
			// 获取字段的值
			mField := element.FieldByName(filedName)

			// 检查字段是否存在
			if !mField.IsValid() {
				log.Errorf("createMarkdownInfo error: Element %d in %s does not have %s fields\n", i, targetObject, filedName)
				markdownInfo.WriteString(`-|`)
				continue
			}

			// 处理指针类型字段
			if mField.Kind() == reflect.Ptr {
				if mField.IsNil() {
					markdownInfo.WriteString(`-|`)
					continue
				}
				mField = mField.Elem()
			}

			var valueStr string
			switch mField.Kind() {
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				valueStr = fmt.Sprintf("%d", mField.Int())
			case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
				valueStr = fmt.Sprintf("%d", mField.Uint())
			case reflect.Float32, reflect.Float64:
				valueStr = fmt.Sprintf("%.2f", mField.Float())
			case reflect.Bool:
				valueStr = fmt.Sprintf("%t", mField.Bool())
			case reflect.String:
				valueStr = mField.String()
			case reflect.Slice:
				// 处理切片类型（如果不是MessageLogs的特殊处理）
				if mField.Len() > 0 {
					var items []string
					for j := 0; j < mField.Len(); j++ {
						item := mField.Index(j)
						items = append(items, fmt.Sprintf("%v", item.Interface()))
					}
					valueStr = strings.Join(items, ", ")
				} else {
					valueStr = ""
				}
			default:
				valueStr = fmt.Sprintf("%v", mField.Interface())
			}

			valueStr = sanitizeMdCell(valueStr)

			log.Debugf("createMarkdownInfo: %s.%s = %s", targetObject, filedName, valueStr)

			if strings.EqualFold(valueStr, "") {
				markdownInfo.WriteString(`-|`)
			} else {
				markdownInfo.WriteString(fmt.Sprintf(`%s|`, valueStr))
			}
		}
		markdownInfo.WriteString(`
`)
	}

	return markdownInfo.String()
}

func MdToHTML(md []byte) []byte {
	// create markdown parser with extensions
	extensions := parser.CommonExtensions | parser.AutoHeadingIDs | parser.NoEmptyLineBeforeBlock
	p := parser.NewWithExtensions(extensions)
	doc := p.Parse(md)

	// create HTML renderer with extensions
	htmlFlags := html.CommonFlags | html.HrefTargetBlank
	opts := html.RendererOptions{Flags: htmlFlags}
	renderer := html.NewRenderer(opts)

	out := markdown.Render(doc, renderer)
	// 规范化换行到 HTML5 风格
	// 将 <br/>、<br />、<br\> 转为 <br>
	s := string(out)
	s = strings.ReplaceAll(s, "<br/>", "<br>")
	s = strings.ReplaceAll(s, "<br />", "<br>")
	s = strings.ReplaceAll(s, "<br\\>", "<br>")
	return []byte(s)
}
