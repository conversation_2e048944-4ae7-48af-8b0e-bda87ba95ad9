package common

import (
	"context"
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"github.com/sirupsen/logrus"
)

const FieldKeyChannelId = "channelid"
const FieldKeyTaskId = "taskid"
const FieldKeyComment = "comment"

const TaskLogGroupStat TaskLogGroup = "tidbstatistics"
const TaskLogGroupAssessment TaskLogGroup = "assessment"
const TaskLogGroupSQLAnalyzer TaskLogGroup = "sqlanalyzer"
const TaskLogGroupTest TaskLogGroup = "test"

type TaskLogGroup string

func (i TaskLogGroup) String() string {
	return string(i)
}

// Handler log handler, support save to db, log task group
type Handler struct {
	fields logrus.Fields
	ctx    context.Context

	saveToDB     bool
	logTaskGroup TaskLogGroup
}

// WithField return an exist Handler with field
func (i *Handler) WithField(key string, value any) *Handler {
	if i.fields == nil {
		i.fields = logrus.Fields{}
	}
	i.fields[key] = value
	return i
}

// WithContext return an exist Handler with context
func (i *Handler) WithContext(ctx context.Context) *Handler {
	i.ctx = ctx
	return i
}

// WithField return a new Handler with field
func WithField(key string, value any) *Handler {
	h := Handler{}
	h.WithField(key, value)
	return &h
}

// WithContext return a new Handler with context
func WithContext(ctx context.Context) *Handler {
	h := Handler{}
	h.WithContext(ctx)
	return &h
}

// C alias of WithContext
func C(ctx context.Context) *Handler {
	return WithContext(ctx)
}

// WithDBRecord enable save log to db flag
func (i *Handler) WithDBRecord() *Handler {
	i.saveToDB = true
	return i
}

// D alias of WithDBRecord
func (i *Handler) D() *Handler {
	return i.WithDBRecord()
}

func (i *Handler) WithTaskGroup(g TaskLogGroup) *Handler {
	i.logTaskGroup = g
	return i
}

// G alias of WithTaskGroup
func (i *Handler) G(g TaskLogGroup) *Handler {
	return i.WithTaskGroup(g)
}

func (i *Handler) getLogger() *logrus.Entry {
	switch i.logTaskGroup {
	case TaskLogGroupStat:
		return log.GetTidbStatsLoggerEntry()
	case TaskLogGroupAssessment:
		return log.GetAssessmentLoggerEntry()
	default:
		return log.GetRootLoggerEntry()
	}
}

// beforeWriteLog do something before write log, like save to db
func (i *Handler) beforeWriteLog(logLevel string, msg string) {
	if i.saveToDB {
		var (
			err       error
			taskId    int
			channelId int
		)
		channelId, err = parse.ParseAnyInt(i.fields[FieldKeyChannelId])
		if err != nil {
			log.Errorf("beforeWriteLog: parse channel id failed, err: %v", err)
			return
		}
		taskId, err = parse.ParseAnyInt(i.fields[FieldKeyTaskId])
		if err != nil {
			log.Errorf("beforeWriteLog: parse task id failed, err: %v", err)
			return
		}
		logData := &task.TaskLogDetail{
			ChannelId:  channelId,
			TaskId:     taskId,
			LogGroup:   i.logTaskGroup.String(),
			LogTime:    time.Now(),
			LogLevel:   logLevel,
			LogMessage: msg,
			Entity:     &common.Entity{Comment: fmt.Sprintf("%v", i.fields[FieldKeyComment])},
		}
		err = models.GetTaskReaderWriter().CreateTaskLogDetail(i.getContext(), logData)
		if err != nil {
			log.Errorf("CreateTaskLogDetail run failed, err: %v", err)
			return
		}
	}
}

func (i *Handler) getContext() context.Context {
	ctx := i.ctx
	if ctx == nil {
		ctx = context.TODO()
	}
	return ctx
}

func (i *Handler) Debug(args ...interface{}) {
	i.beforeWriteLog(log.LogDebug, fmt.Sprint(args...))
	i.getLogger().Debug(args...)
}

func (i *Handler) Debugf(format string, args ...interface{}) {
	i.beforeWriteLog(log.LogDebug, fmt.Sprintf(format, args...))
	i.getLogger().Debugf(format, args...)
}

func (i *Handler) Info(args ...interface{}) {
	i.beforeWriteLog(log.LogInfo, fmt.Sprint(args...))
	i.getLogger().Info(args...)
}

func (i *Handler) Infof(format string, args ...interface{}) {
	i.beforeWriteLog(log.LogInfo, fmt.Sprintf(format, args...))
	i.getLogger().Infof(format, args...)
}

func (i *Handler) Warn(args ...interface{}) {
	i.beforeWriteLog(log.LogWarn, fmt.Sprint(args...))
	i.getLogger().Warn(args...)
}

func (i *Handler) Warnf(format string, args ...interface{}) {
	i.beforeWriteLog(log.LogWarn, fmt.Sprintf(format, args...))
	i.getLogger().Warnf(format, args...)
}

func (i *Handler) Error(args ...interface{}) {
	i.beforeWriteLog(log.LogError, fmt.Sprint(args...))
	i.getLogger().Error(args...)
}

func (i *Handler) Errorf(format string, args ...interface{}) {
	i.beforeWriteLog(log.LogError, fmt.Sprintf(format, args...))
	i.getLogger().Errorf(format, args...)
}

func (i *Handler) Fatal(args ...interface{}) {
	i.beforeWriteLog(log.LogFatal, fmt.Sprint(args...))
	i.getLogger().Fatal(args...)
}

func (i *Handler) Fatalf(format string, args ...interface{}) {
	i.beforeWriteLog(log.LogFatal, fmt.Sprintf(format, args...))
	i.getLogger().Fatalf(format, args...)
}

// InitStatisticTaskLogContextHandler init a statistic log Handler with context, channel id, task id
func InitStatisticTaskLogContextHandler(ctx context.Context, channelId int, taskId int) *Handler {
	return C(ctx).D().G(TaskLogGroupStat).WithField(FieldKeyChannelId, channelId).WithField(FieldKeyTaskId, taskId)
}

// InitAssessmentTaskLogContextHandler init an assessment log Handler with context, channel id, task id
func InitAssessmentTaskLogContextHandler(ctx context.Context, channelId int, taskId int) *Handler {
	return C(ctx).D().G(TaskLogGroupAssessment).WithField(FieldKeyChannelId, channelId).WithField(FieldKeyTaskId, taskId)
}

// InitSQLAnalyzerTaskLogContextHandler init a sqlanalyzer log Handler with context, channel id, task id
func InitSQLAnalyzerTaskLogContextHandler(ctx context.Context, channelId int, taskId int) *Handler {
	return C(ctx).D().G(TaskLogGroupSQLAnalyzer).WithField(FieldKeyChannelId, channelId).WithField(FieldKeyTaskId, taskId)
}

// InitTestLogHandler init a test log Handler with context
func InitTestLogHandler() *Handler {
	return C(context.TODO()).G(TaskLogGroupTest)
}
