package structure

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
)

func BuildStructureMigrationParam(ctx context.Context, taskInfo *task.Task, templateId int) (*structs.StructureParam, error) {

	taskParamMap := make(map[string]string)

	// task params merge
	defaultTaskCfgs, getDetailErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, templateId)
	if getDetailErr != nil {
		return nil, fmt.Errorf("get task params detail info failed: %v", getDetailErr)
	}

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.ChannelId,
		TaskID:              taskInfo.TaskID,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if getParamErr != nil {
		return nil, fmt.Errorf("get task params info failed: %v", getParamErr)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}

	param := &structs.StructureParam{}

	if value, ok := taskParamMap[constants.ParamsMigrationStructTableParallel]; ok {
		v, err := parse.ParseUInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseUInt migration_table_parallel failed: %v", err)
		}
		param.MigrationTableParallel = v
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructIndexParallel]; ok {
		v, err := parse.ParseUInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseUInt migration_index_parallel failed: %v", err)
		}
		param.MigrationIndexParallel = v
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructLowerCaseFieldNames]; ok {
		v, err := parse.ParseUInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseUInt lower_case_field_names failed: %v", err)
		}
		param.LowerCaseFieldNames = v
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructLowerCaseTableNames]; ok {
		v, err := parse.ParseUInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseUInt lower_case_table_names failed: %v", err)
		}
		param.LowerCaseTableNames = v
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructSyncMetaBatchSize]; ok {
		v, err := parse.ParseUInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseUInt sync_meta_batch_size failed: %v", err)
		}
		param.SyncMetaBatchSize = v
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructSyncMetaSQLHint]; ok {
		param.SyncMetaSQLHint = value
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructPunctReplacement]; ok {
		param.PunctReplacement = strings.TrimSpace(value)
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructCreatePkWithTable]; ok {
		param.CreatePkWithTable = strings.TrimSpace(value)
	} else {
		param.CreatePkWithTable = "Y"
	}

	if value, ok := taskParamMap[constants.ParamsMigrationStructRunVersion]; ok {
		param.RunVersion = strings.TrimSpace(value)
	} else {
		param.RunVersion = "v1"
	}

	log.Infof("build structure migration param success, taskId:%d, %v", taskInfo.TaskID, param.String())
	return param, nil
}
