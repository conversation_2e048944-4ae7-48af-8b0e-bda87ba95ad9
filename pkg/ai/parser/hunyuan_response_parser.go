package parser

// HunyuanResponseParser 腾讯混元响应解析器
type HunyuanResponseParser struct {
	BaseResponseParser
}

func NewHunyuanResponseParser() *HunyuanResponseParser {
	return &HunyuanResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Hunyuan"},
	}
}

func (p *HunyuanResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		Response struct {
			RequestID string `json:"RequestId"`
			Choices   []struct {
				Index   int `json:"Index"`
				Message struct {
					Role    string `json:"Role"`
					Content string `json:"Content"`
				} `json:"Message"`
				FinishReason string `json:"FinishReason"`
			} `json:"Choices"`
			Usage struct {
				PromptTokens     int `json:"PromptTokens"`
				CompletionTokens int `json:"CompletionTokens"`
				TotalTokens      int `json:"TotalTokens"`
			} `json:"Usage"`
		} `json:"Response"`
	}

	if err := p.ParseJ<PERSON>(resp, &jsonResp); err != nil {
		return "", err
	}

	if len(jsonResp.Response.Choices) == 0 {
		return "", p.FormatError("no choices returned", resp)
	}

	choice := jsonResp.Response.Choices[0]

	if err := p.ValidateContent(choice.Message.Content, resp); err != nil {
		return "", err
	}

	return choice.Message.Content, nil
}

func (p *HunyuanResponseParser) GetProviderName() string {
	return p.ProviderName
}
