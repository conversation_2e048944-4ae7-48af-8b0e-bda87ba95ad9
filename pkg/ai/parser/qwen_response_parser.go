package parser

// QwenResponseParser 阿里通义千问响应解析器
type QwenResponseParser struct {
	BaseResponseParser
}

func NewQwenResponseParser() *QwenResponseParser {
	return &QwenResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Qwen"},
	}
}

func (p *QwenResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		RequestID string `json:"request_id"`
		Output    struct {
			Text         string `json:"text"`
			FinishReason string `json:"finish_reason"`
		} `json:"output"`
		Usage struct {
			InputTokens  int `json:"input_tokens"`
			OutputTokens int `json:"output_tokens"`
			TotalTokens  int `json:"total_tokens"`
		} `json:"usage"`
	}

	if err := p.ParseJSON(resp, &jsonResp); err != nil {
		return "", err
	}

	if err := p.<PERSON>idate<PERSON>ontent(jsonResp.Output.Text, resp); err != nil {
		return "", err
	}

	return jsonResp.Output.Text, nil
}

func (p *QwenResponseParser) GetProviderName() string {
	return p.ProviderName
}
