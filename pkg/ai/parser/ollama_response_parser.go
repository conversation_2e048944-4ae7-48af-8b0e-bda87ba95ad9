package parser

// OllamaResponseParser Ollama 响应解析器
type OllamaResponseParser struct {
	BaseResponseParser
}

func NewOllamaResponseParser() *OllamaResponseParser {
	return &OllamaResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Ollama"},
	}
}

func (p *OllamaResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		Model     string `json:"model"`
		CreatedAt string `json:"created_at"`
		Message   struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		Done               bool   `json:"done"`
		DoneReason         string `json:"done_reason,omitempty"`
		TotalDuration      int64  `json:"total_duration"`
		LoadDuration       int64  `json:"load_duration"`
		PromptEvalCount    int    `json:"prompt_eval_count"`
		PromptEvalDuration int64  `json:"prompt_eval_duration"`
		EvalCount          int    `json:"eval_count"`
		EvalDuration       int64  `json:"eval_duration"`
	}

	if err := p.ParseJSON(resp, &jsonResp); err != nil {
		return "", err
	}

	if !jsonResp.Done {
		return "", p.FormatError("response incomplete, done: false", resp)
	}

	if jsonResp.DoneReason != "" && jsonResp.DoneReason != "stop" {
		return "", p.FormatError("response incomplete, done_reason: %s", resp, jsonResp.DoneReason)
	}

	if err := p.ValidateContent(jsonResp.Message.Content, resp); err != nil {
		return "", err
	}

	return jsonResp.Message.Content, nil
}

func (p *OllamaResponseParser) GetProviderName() string {
	return p.ProviderName
}
