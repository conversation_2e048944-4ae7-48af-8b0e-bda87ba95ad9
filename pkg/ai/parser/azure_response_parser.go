package parser

import (
	"fmt"
	"github.com/sashabaranov/go-openai"
)

// AzureResponseParser Azure OpenAI 响应解析器
type AzureResponseParser struct {
	BaseResponseParser
}

func NewAzureResponseParser() *AzureResponseParser {
	return &AzureResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Azure OpenAI"},
	}
}

// ParseResponse 解析 Azure OpenAI 的响应
func (p *AzureResponseParser) ParseResponse(resp string) (string, error) {
	// Azure 使用 SDK，这里的 resp 实际上是内容字符串
	// 如果是错误情况，会在 service 层处理
	if err := p.ValidateContent(resp, resp); err != nil {
		return "", err
	}
	return resp, nil
}

// ParseChatCompletionResponse 解析 ChatCompletion 响应对象
func (p *AzureResponseParser) ParseChatCompletionResponse(resp openai.ChatCompletionResponse) (string, error) {
	if len(resp.Choices) == 0 {
		return "", p.FormatError("no choices returned", fmt.Sprintf("%+v", resp))
	}

	choice := resp.Choices[0]
	if choice.FinishReason != openai.FinishReasonStop && choice.FinishReason != "" {
		return "", p.FormatError("response incomplete, finish_reason: %s", fmt.Sprintf("%+v", resp), choice.FinishReason)
	}

	content := choice.Message.Content
	if err := p.ValidateContent(content, fmt.Sprintf("%+v", resp)); err != nil {
		return "", err
	}

	return content, nil
}

// ParseStreamResponse 解析流式响应
func (p *AzureResponseParser) ParseStreamResponse(chunks []openai.ChatCompletionStreamChoice) (string, error) {
	if len(chunks) == 0 {
		return "", p.FormatError("no stream chunks received", "empty stream")
	}

	// 聚合所有内容
	var fullContent string
	for _, chunk := range chunks {
		fullContent += chunk.Delta.Content
	}

	if err := p.ValidateContent(fullContent, fmt.Sprintf("stream with %d chunks", len(chunks))); err != nil {
		return "", err
	}

	return fullContent, nil
}

// ParseError 解析 Azure OpenAI 错误
func (p *AzureResponseParser) ParseError(err error) error {
	if err == nil {
		return nil
	}

	// 检查是否是 OpenAI API 错误
	if apiErr, ok := err.(*openai.APIError); ok {
		return fmt.Errorf("%s API error: status=%d, message=%s, type=%s, code=%s",
			p.ProviderName, apiErr.HTTPStatusCode, apiErr.Message, apiErr.Type, apiErr.Code)
	}

	// 检查是否是请求错误
	if reqErr, ok := err.(*openai.RequestError); ok {
		return fmt.Errorf("%s request error: status=%d, err=%v",
			p.ProviderName, reqErr.HTTPStatusCode, reqErr.Err)
	}

	// 其他错误
	return fmt.Errorf("%s error: %v", p.ProviderName, err)
}

func (p *AzureResponseParser) GetProviderName() string {
	return p.ProviderName
}
