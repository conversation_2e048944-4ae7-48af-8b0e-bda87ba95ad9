package parser

// ClaudeResponseParser Claude 响应解析器
type ClaudeResponseParser struct {
	BaseResponseParser
}

func NewClaudeResponseParser() *ClaudeResponseParser {
	return &ClaudeResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Claude"},
	}
}

func (p *ClaudeResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		ID      string `json:"id"`
		Type    string `json:"type"`
		Role    string `json:"role"`
		Content []struct {
			Type string `json:"type"`
			Text string `json:"text"`
		} `json:"content"`
		Model        string `json:"model"`
		StopReason   string `json:"stop_reason"`
		StopSequence string `json:"stop_sequence"`
		Usage        struct {
			InputTokens  int `json:"input_tokens"`
			OutputTokens int `json:"output_tokens"`
		} `json:"usage"`
	}

	if err := p.<PERSON>(resp, &jsonResp); err != nil {
		return "", err
	}

	if len(jsonResp.Content) == 0 {
		return "", p.FormatError("no content", resp)
	}

	if jsonResp.StopReason != "end_turn" && jsonResp.StopReason != "" {
		return "", p.FormatError("response incomplete, stop_reason: %s", resp, jsonResp.StopReason)
	}

	if err := p.ValidateContent(jsonResp.Content[0].Text, resp); err != nil {
		return "", err
	}

	return jsonResp.Content[0].Text, nil
}

func (p *ClaudeResponseParser) GetProviderName() string {
	return p.ProviderName
}
