package parser

// ErnieResponseParser 百度文心一言响应解析器
type ErnieResponseParser struct {
	BaseResponseParser
}

func NewErnieResponseParser() *ErnieResponseParser {
	return &ErnieResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "ERNIE"},
	}
}

func (p *ErnieResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		ID               string `json:"id"`
		Object           string `json:"object"`
		Created          int64  `json:"created"`
		SentenceID       int    `json:"sentence_id"`
		IsEnd            bool   `json:"is_end"`
		IsTruncated      bool   `json:"is_truncated"`
		Result           string `json:"result"`
		NeedClearHistory bool   `json:"need_clear_history"`
		Usage            struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		} `json:"usage"`
	}

	if err := p.Parse<PERSON>(resp, &jsonResp); err != nil {
		return "", err
	}

	if err := p.ValidateContent(jsonResp.Result, resp); err != nil {
		return "", err
	}

	return jsonResp.Result, nil
}

func (p *ErnieResponseParser) GetProviderName() string {
	return p.ProviderName
}
