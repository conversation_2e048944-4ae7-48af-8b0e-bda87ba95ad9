package parser

// GeminiResponseParser Gemini 响应解析器
type GeminiResponseParser struct {
	BaseResponseParser
}

func NewGeminiResponseParser() *GeminiResponseParser {
	return &GeminiResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Gemini"},
	}
}

func (p *GeminiResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		Candidates []struct {
			Content struct {
				Parts []struct {
					Text string `json:"text"`
				} `json:"parts"`
				Role string `json:"role"`
			} `json:"content"`
			FinishReason  string        `json:"finishReason"`
			Index         int           `json:"index"`
			SafetyRatings []interface{} `json:"safetyRatings"`
		} `json:"candidates"`
		UsageMetadata struct {
			PromptTokenCount     int `json:"promptTokenCount"`
			CandidatesTokenCount int `json:"candidatesTokenCount"`
			TotalTokenCount      int `json:"totalTokenCount"`
		} `json:"usageMetadata"`
	}

	if err := p.ParseJ<PERSON>(resp, &jsonResp); err != nil {
		return "", err
	}

	if len(jsonResp.Candidates) == 0 {
		return "", p.FormatError("no candidates", resp)
	}

	candidate := jsonResp.Candidates[0]
	if candidate.FinishReason != "STOP" && candidate.FinishReason != "" {
		return "", p.FormatError("response incomplete, finish_reason: %s", resp, candidate.FinishReason)
	}

	if len(candidate.Content.Parts) == 0 {
		return "", p.FormatError("no content parts", resp)
	}

	if err := p.ValidateContent(candidate.Content.Parts[0].Text, resp); err != nil {
		return "", err
	}

	return candidate.Content.Parts[0].Text, nil
}

func (p *GeminiResponseParser) GetProviderName() string {
	return p.ProviderName
}
