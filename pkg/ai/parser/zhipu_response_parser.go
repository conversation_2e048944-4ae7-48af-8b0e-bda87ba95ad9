package parser

// ZhipuResponseParser 智谱AI响应解析器
type ZhipuResponseParser struct {
	BaseResponseParser
}

func NewZhipuResponseParser() *ZhipuResponseParser {
	return &ZhipuResponseParser{
		BaseResponseParser: BaseResponseParser{ProviderName: "Zhipu"},
	}
}

func (p *ZhipuResponseParser) ParseResponse(resp string) (string, error) {
	var jsonResp struct {
		ID      string `json:"id"`
		Created int64  `json:"created"`
		Model   string `json:"model"`
		Choices []struct {
			Index   int `json:"index"`
			Message struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			} `json:"message"`
			FinishReason string `json:"finish_reason"`
		} `json:"choices"`
		Usage struct {
			PromptTokens     int `json:"prompt_tokens"`
			CompletionTokens int `json:"completion_tokens"`
			TotalTokens      int `json:"total_tokens"`
		} `json:"usage"`
	}

	if err := p.ParseJ<PERSON>(resp, &jsonResp); err != nil {
		return "", err
	}

	if len(jsonResp.Choices) == 0 {
		return "", p.FormatError("no choices returned", resp)
	}

	choice := jsonResp.Choices[0]
	if choice.FinishReason != "stop" && choice.FinishReason != "" {
		return "", p.FormatError("response incomplete, finish_reason: %s", resp, choice.FinishReason)
	}

	if err := p.ValidateContent(choice.Message.Content, resp); err != nil {
		return "", err
	}

	return choice.Message.Content, nil
}

func (p *ZhipuResponseParser) GetProviderName() string {
	return p.ProviderName
}
