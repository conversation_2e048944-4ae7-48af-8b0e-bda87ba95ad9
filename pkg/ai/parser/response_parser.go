package parser

import (
	"encoding/json"
	"fmt"
)

// ResponseParser 定义响应解析器接口
type ResponseParser interface {
	ParseResponse(resp string) (string, error)
	GetProviderName() string
}

// BaseResponseParser 提供基础的响应解析功能
type BaseResponseParser struct {
	ProviderName string
}

// ParseJSON 通用的 JSON 解析方法，包含原始响应
func (p *BaseResponseParser) ParseJSON(resp string, target interface{}) error {
	if err := json.Unmarshal([]byte(resp), target); err != nil {
		return fmt.Errorf("failed to parse %s response, resp:%s, err:%w",
			p.ProviderName, resp, err)
	}
	return nil
}

// FormatError 统一的错误格式化
func (p *BaseResponseParser) FormatError(errorType string, resp string, args ...interface{}) error {
	baseMsg := fmt.Sprintf(errorType, args...)
	return fmt.Errorf("%s from %s API, resp:%s", baseMsg, p.ProviderName, resp)
}

// ValidateContent 验证内容是否为空
func (p *BaseResponseParser) ValidateContent(content, resp string) error {
	if content == "" {
		return p.FormatError("empty content", resp)
	}
	return nil
}

// CreateResponseParser 创建对应的 ResponseParser
func CreateResponseParser(providerType string) ResponseParser {
	switch providerType {
	case "openai":
		return NewOpenAIResponseParser()
	case "azure":
		return NewAzureResponseParser()
	case "claude":
		return NewClaudeResponseParser()
	case "gemini":
		return NewGeminiResponseParser()
	case "qwen":
		return NewQwenResponseParser()
	case "zhipu":
		return NewZhipuResponseParser()
	case "hunyuan":
		return NewHunyuanResponseParser()
	case "ernie":
		return NewErnieResponseParser()
	case "ollama":
		return NewOllamaResponseParser()
	default:
		return nil
	}
}
