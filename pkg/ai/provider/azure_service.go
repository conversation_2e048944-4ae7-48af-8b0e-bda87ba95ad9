package provider

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"github.com/sashabaranov/go-openai"
)

type AzureConvertAdaptor struct {
	config         structs.LLMProviderAPIConfig
	responseParser *parser.AzureResponseParser
}

func NewAzureAdaptor(config structs.LLMProviderAPIConfig) *AzureConvertAdaptor {
	// Note: Azure service uses the third-party github.com/sashabaranov/go-openai library
	// which expects specific struct fields. Therefore, MaxTokensName parameter cannot be
	// dynamically configured for this provider. The library uses "max_tokens" field.
	return &AzureConvertAdaptor{
		config:         config,
		responseParser: parser.NewAzureResponseParser(),
	}
}

// setAzureModelMapper 设置 Azure 模型映射函数，便于后续复用
func (i *AzureConvertAdaptor) setAzureModelMapper(config *openai.ClientConfig) {
	// 中文注释：设置 Azure OpenAI 的模型映射函数，保证模型名一致
	config.AzureModelMapperFunc = func(model string) string {
		azureModelMapping := map[string]string{
			i.config.GetModel(): i.config.GetModel(),
		}
		return azureModelMapping[i.config.GetModel()]
	}
}

// TestConnection 测试 Azure OpenAI 连接
func (i *AzureConvertAdaptor) TestConnection(ctx context.Context) (string, error) {
	config := openai.DefaultAzureConfig(i.config.GetAPIKey(), i.config.GetEndpoint())
	i.setAzureModelMapper(&config)
	client := openai.NewClientWithConfig(config)
	models, getModelErr := client.ListModels(ctx)
	if getModelErr != nil {
		log.Errorf("create azure openai client failed, endpoint:%s, err:%v", i.config.GetEndpoint(), getModelErr)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", i.config.GetEndpoint(), i.responseParser.ParseError(getModelErr)))
	}
	return "azure modelNum:" + parse.FormatInt(len(models.Models)), nil
}

// Convert 发送对话请求，返回AI响应
func (i *AzureConvertAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	config := openai.DefaultAzureConfig(i.config.GetAPIKey(), i.config.GetEndpoint())
	i.setAzureModelMapper(&config)
	client := openai.NewClientWithConfig(config)
	prompts := item.GeneratePrompts()
	messages := make([]openai.ChatCompletionMessage, 0, len(prompts)+1)
	for _, prompt := range prompts {
		messages = append(messages, openai.ChatCompletionMessage{
			Role:    openai.ChatMessageRoleSystem,
			Content: prompt,
		})
	}
	messages = append(messages, openai.ChatCompletionMessage{
		Role:    openai.ChatMessageRoleUser,
		Content: item.GetSQL(),
	})

	// Use configured max tokens, default to 2000 if not set
	maxTokens := i.config.GetMaxTokens(defaultCompletionMaxTokens)

	req := openai.ChatCompletionRequest{
		Model:       i.config.GetModel(),
		Messages:    messages,
		MaxTokens:   maxTokens,
		Temperature: i.config.GetTemperature(),
		Stream:      i.config.IsStream(),
	}

	reqBody, _ := json.Marshal(req)
	log.Debugf("postToLLMApi, body:%v", string(reqBody))
	// Handle streaming response if enabled
	if i.config.IsStream() {
		stream, streamErr := client.CreateChatCompletionStream(ctx, req)
		if streamErr != nil {
			log.Errorf("create chat completion stream failed, taskId:%d, endpoint:%s, err:%v", taskId, i.config.GetEndpoint(), streamErr)
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", i.config.GetEndpoint(), i.responseParser.ParseError(streamErr)))
		}
		defer stream.Close()

		var chunks []openai.ChatCompletionStreamChoice
		for {
			response, err := stream.Recv()
			if err != nil {
				if err == io.EOF {
					break
				}
				log.Errorf("receive stream response failed, taskId:%d, endpoint:%s, err:%v", taskId, i.config.GetEndpoint(), err)
				return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", i.config.GetEndpoint(), i.responseParser.ParseError(err)))
			}

			if len(response.Choices) > 0 {
				chunks = append(chunks, response.Choices[0])
			}
		}

		content, parseErr := i.responseParser.ParseStreamResponse(chunks)
		if parseErr != nil {
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", i.config.GetEndpoint(), parseErr))
		}
		return content, prompts, nil
	}

	// Non-streaming response
	resp, clientErr := client.CreateChatCompletion(ctx, req)
	if clientErr != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, endpoint:%s, err:%v", taskId, i.config.GetEndpoint(), clientErr)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", i.config.GetEndpoint(), i.responseParser.ParseError(clientErr)))
	}
	content, parseErr := i.responseParser.ParseChatCompletionResponse(resp)
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", i.config.GetEndpoint(), parseErr))
	}
	return content, prompts, nil
}
