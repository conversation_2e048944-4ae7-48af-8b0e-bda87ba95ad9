package provider

import (
	"encoding/json"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"strings"
	"unicode"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

var (
	oracleSemanticEquivalenceMap = map[string]bool{
		"TO_NUMBER":      true,
		"TO_CHAR":        true,
		"TO_DATE":        true,
		"MONTHS_BETWEEN": true,
		"ADD_MONTHS":     true,
		"TRUNC":          true,
		"SYS_GUID":       true,
		"NVL":            true,
		"NVL2":           true,
		"DECODE":         true,
		"LENGTH":         true,
		"SUBSTR":         true,
		"INSTR":          true,
		"LISTAGG":        true,
		"CONCAT":         true,
		"CHR":            true,
	}
)

func IsInSemanticEquivalenceList(val string) bool {
	if val == "" {
		return false
	}
	val = strings.ToUpper(val)
	_, ok := oracleSemanticEquivalenceMap[val]
	return ok
}

type ConvertItem struct {
	SchemaName       string
	PackageName      string
	ObjectType       string
	ObjectName       string
	DependencyUUID   string
	ObjectDefinition string
	PromptId         uint
	PromptText       string
	Depth            uint
	Dependencies     []*objectparser.OracleDependencyVO
	ReservedWords    []message.ReservedWord
	Identifiers      []message.MethodInvoke
	MethodInvokes    []message.MethodInvoke
	PLSQLSegment     message.PLSQLSegment

	FeatureID constants.FeatureID
}

type RewriteSQLItem struct {
	SchemaName   string
	SQL          string
	TiDBExecCode string
}

func (i ConvertItem) GetDisplayName() string {
	if i.PackageName == "" {
		return fmt.Sprintf("%s.%s.%s", i.SchemaName, i.ObjectType, i.ObjectName)
	} else {
		return fmt.Sprintf("%s.[%s].%s.%s", i.SchemaName, i.PackageName, i.ObjectType, i.ObjectName)
	}
}

func (i ConvertItem) String() string {
	return fmt.Sprintf("ConvertItem-> DependencyUUID:%s, SchemaName:%s, PackageName:%s, ObjectType:%s, ObjectName:%s, ObjectDefinition:%s, PromptId:%d, PromptText:%s",
		i.DependencyUUID, i.SchemaName, i.PackageName, i.ObjectType, i.ObjectName, i.getObjectShortDefinition(), i.PromptId, i.PromptText)
}

func (i ConvertItem) getObjectShortDefinition() string {
	if len(i.ObjectDefinition) < 30 {
		return i.ObjectDefinition
	}
	return i.ObjectDefinition[:30] + "..."
}

// ToJavaClassName 将输入字符串转换为符合Java类名格式的类名（驼峰命名法，首字母大写）
func ToJavaClassName(input string) string {
	trimmed := strings.TrimSpace(input)
	if trimmed == "" {
		return ""
	}
	trimmed = strings.ToLower(trimmed)

	var builder strings.Builder
	capitalizeNext := true

	for _, r := range trimmed {
		if unicode.IsLetter(r) || unicode.IsDigit(r) {
			if capitalizeNext {
				builder.WriteRune(unicode.ToUpper(r))
				capitalizeNext = false
			} else {
				builder.WriteRune(r)
			}
		} else {
			// 遇到非字母或数字的字符，设置下一个字符为大写
			capitalizeNext = true
		}
	}

	className := builder.String()

	if len(className) > 0 {
		runes := []rune(className)
		if unicode.IsDigit(runes[0]) {
			className = "_" + className
		}
	}

	return className
}

// ToJavaFunctionName 将输入字符串转换为Java规范的函数名
// 例如：P_CALC_STAR_REWARD -> pCalcStarReward
func ToJavaFunctionName(input string) string {
	// 如果输入为空，直接返回
	if input == "" {
		return ""
	}

	// 将字符串按下划线分割并转换为小写
	parts := strings.Split(strings.ToLower(input), "_")
	var result strings.Builder

	// 处理第一个单词，保持小写
	if len(parts) > 0 && parts[0] != "" {
		result.WriteString(parts[0])
	}

	// 处理后续单词，首字母大写
	for i := 1; i < len(parts); i++ {
		if parts[i] != "" {
			// 将首字母大写，其余保持不变
			part := strings.ToLower(parts[i])
			if len(part) > 0 {
				result.WriteString(strings.ToUpper(string(part[0])))
				if len(part) > 1 {
					result.WriteString(part[1:])
				}
			}
		}
	}

	return result.String()
}

// GeneratePrompts 生成提示文本数组
func (i ConvertItem) GeneratePrompts() []string {
	prompts := []string{i.PromptText}

	// 收集方法调用
	invokeMethods := i.collectInvokeMethods()

	// 收集函数调用提示
	externalHints := i.collectExternalHints()
	internalHints := i.collectInternalHints(invokeMethods)

	// 添加提示到结果
	prompts = i.appendHints(prompts, externalHints, internalHints)

	namingPrompt := i.getJavaNamingPrompt()

	// 记录日志
	i.logPromptDetails(invokeMethods, externalHints, internalHints, namingPrompt)

	// 添加Java命名信息
	prompts = append(prompts, namingPrompt)

	return prompts
}

// collectInvokeMethods 收集符合条件的方法调用
func (i ConvertItem) collectInvokeMethods() []message.MethodInvoke {
	invokeMethods := make([]message.MethodInvoke, 0)
	for _, methodInvoke := range i.MethodInvokes {
		for _, parent := range methodInvoke.Parents {
			if i.isValidMethodInvoke(parent, methodInvoke) {
				invokeMethods = append(invokeMethods, message.MethodInvoke{
					OwnerName:             methodInvoke.OwnerName,
					FuncName:              methodInvoke.FuncName,
					Count:                 methodInvoke.Count,
					IsOwnerInReservedWord: methodInvoke.IsOwnerInReservedWord,
				})
			}
		}
	}
	return invokeMethods
}

// isValidMethodInvoke 判断方法调用是否有效
func (i ConvertItem) isValidMethodInvoke(parent message.SQLParent, method message.MethodInvoke) bool {
	return strings.EqualFold(parent.ParentName, i.ObjectName) &&
		strings.EqualFold(parent.ParentType, i.ObjectType) &&
		method.OwnerName == "" &&
		IsInSemanticEquivalenceList(method.FuncName)
}

// collectExternalHints 收集外部函数调用提示
func (i ConvertItem) collectExternalHints() []string {
	hints := make([]string, 0, 10)
	for _, dep := range i.Dependencies {
		hint := i.buildExternalFuncCallHint(dep)
		hints = append(hints, hint)
	}
	return hints
}

// collectInternalHints 收集内部函数调用提示
func (i ConvertItem) collectInternalHints(invokeMethods []message.MethodInvoke) []string {
	hints := make([]string, 0, 10)
	for _, invokeMethod := range invokeMethods {
		hint := i.buildInternalFuncCallHint(invokeMethod)
		hints = append(hints, hint)
	}
	return hints
}

// appendHints 将外部和内部提示添加到结果
func (i ConvertItem) appendHints(prompts, externalHints, internalHints []string) []string {
	if len(externalHints) > 0 {
		externalHints = lo.Uniq(externalHints)
		prompts = append(prompts, "同时，"+strings.Join(externalHints, ";\n")+";\n")
	}
	if len(internalHints) > 0 {
		internalHints = lo.Uniq(internalHints)
		prompts = append(prompts, "另外，"+strings.Join(internalHints, ";\n")+";\n")
	}
	return prompts
}

// logPromptDetails 记录提示生成的相关信息
func (i ConvertItem) logPromptDetails(invokeMethods []message.MethodInvoke, externalHints, internalHints []string, namingPrompt string) {
	invokeMethodsBytes, _ := json.Marshal(&invokeMethods)
	log.Infof("Generating prompt of %s, uuid:%s, invokeMethods:%v, externalFuncCallHints:%v, internalFuncCallHints:%v, namingPrompt:%v",
		i.GetDisplayName(), i.DependencyUUID, string(invokeMethodsBytes), externalHints, internalHints, namingPrompt)
}

// getJavaNamingPrompt 添加Java文件和函数命名信息
func (i ConvertItem) getJavaNamingPrompt() string {
	javaFunctionName := ToJavaFunctionName(i.ObjectName)
	javaFileName := i.getJavaFileName()
	return fmt.Sprintf("最后，将JAVA文件名命名为%s，函数命名为%s；如果不止一个文件，则{FileNamePrefix}设置为%s",
		javaFileName, javaFunctionName, javaFileName)
}

// getJavaFileName 获取Java文件名
func (i ConvertItem) getJavaFileName() string {
	if i.PackageName == "" {
		return ToJavaClassName(i.ObjectName)
	}
	return ToJavaClassName(i.PackageName + "_" + i.ObjectName)
}

func (i ConvertItem) buildInternalFuncCallHint(invokeMethod message.MethodInvoke) string {
	//hint := fmt.Sprintf("将Oracle内置函数%s转化为功能相同的JAVA函数调用（名称要求相同并且包含具体实现）或者MySQL函数调用", invokeMethod.FuncName)
	hint := fmt.Sprintf("将Cursor或者内部SQL中出现的Oracle内置函数%s转化为功能相同的MySQL函数调用", invokeMethod.FuncName)
	return hint
}

func (i ConvertItem) buildExternalFuncCallHint(dep *objectparser.OracleDependencyVO) string {

	packageName := i.PackageName
	referencedPackageName := dep.ReferencedPackageName

	// 单独的函数或者存储过程
	if packageName == "" {
		return fmt.Sprintf("将函数调用%s视为一个外部的JAVA函数调用%s.%s",
			dep.ReferencedName,
			ToJavaClassName(dep.ReferencedName),
			ToJavaFunctionName(dep.ReferencedName))
	} else {
		if referencedPackageName == "" {
			// TODO 应该走不到这里来
			return fmt.Sprintf("将函数调用%s视为一个外部的JAVA函数调用%s.%s",
				dep.ReferencedName,
				ToJavaClassName(dep.ReferencedName),
				ToJavaFunctionName(dep.ReferencedName))
		} else if referencedPackageName == packageName {
			return fmt.Sprintf("将函数调用%s视为一个外部的JAVA函数调用%s.%s",
				dep.ReferencedName,
				ToJavaClassName(packageName+"_"+dep.ReferencedName),
				ToJavaFunctionName(dep.ReferencedName))
		} else {
			return fmt.Sprintf("将函数调用%s.%s视为一个外部的JAVA函数调用%s.%s",
				referencedPackageName,
				dep.ReferencedName,
				ToJavaClassName(referencedPackageName+"_"+dep.ReferencedName),
				ToJavaFunctionName(dep.ReferencedName))
		}
	}
}

func (i ConvertItem) GetSQL() string {
	sql, writed := i.PLSQLSegment.CombineSegmentedSQL(i.ObjectType, i.ObjectName)
	if writed {
		return sql
	} else {
		return i.ObjectDefinition
	}
}

type ConvertContextInterface interface {
	GetSQL() string
	GeneratePrompts() []string
}

func (i RewriteSQLItem) GetSQL() string {
	return i.SQL
}

// GeneratePrompts 生成提示文本数组
func (i RewriteSQLItem) GeneratePrompts() []string {
	prompts := []string{
		fmt.Sprintf(`你是数据库迁移专家，将Oracle SQL转换为MySQL SQL。

输入信息：
- MySQL错误码：%s
- Oracle SQL：%s

转换规则：
- NVL(expr1, expr2) → IFNULL(expr1, expr2)
- DECODE(expr, search1, result1, ..., default) → CASE WHEN expr = search1 THEN result1 ... ELSE default END
- ROWNUM → LIMIT子句
- FROM DUAL → 省略FROM DUAL
- TO_DATE() → STR_TO_DATE()
- CONNECT BY → 递归CTE或子查询重写
- (+) 外连接 → LEFT JOIN/RIGHT JOIN
- SYSDATE → NOW()
- TO_CHAR() → DATE_FORMAT()
- MONTHS_BETWEEN() → TIMESTAMPDIFF(MONTH, ...)
- ADD_MONTHS() → DATE_ADD(..., INTERVAL ... MONTH)
- TRUNC(date) → DATE(date)

输出格式（严格遵守）：
成功转换：直接输出转换后的完整SQL语句，不需要解释，不使用markdown格式
转换失败：必须输出ERROR:后跟编号列表

ERROR:格式示例：
ERROR:
1. 函数XMLAGG不支持转换
2. CONNECT BY语法需要重写为递归CTE
3. CLOB数据类型需要特殊处理
4. 复杂的PL/SQL块无法直接转换为SQL语句

常见无法转换的情况：
- Oracle特有函数（如XMLAGG、LISTAGG等复杂函数）
- 层次查询语法（CONNECT BY）
- PL/SQL块结构（BEGIN...END）
- Oracle特有数据类型（CLOB、BLOB等需要特殊处理）
- 复杂的分析函数组合

转换结果：`, i.TiDBExecCode, i.SQL),
	}
	return prompts
}
