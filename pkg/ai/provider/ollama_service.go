package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// OllamaChatMessage represents a chat message for Ollama
type OllamaChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OllamaChatRequest represents the request structure for Ollama
type OllamaChatRequest struct {
	Model       string              `json:"model"`
	Messages    []OllamaChatMessage `json:"messages"`
	Stream      bool                `json:"stream,omitempty"`
	Temperature float64             `json:"temperature,omitempty"`
	Options     *OllamaOptions      `json:"options,omitempty"`
}

// OllamaOptions represents optional parameters for Ollama
type OllamaOptions struct {
	NumPredict  int     `json:"num_predict,omitempty"` // Max tokens to generate
	Temperature float64 `json:"temperature,omitempty"`
}

// OllamaStreamResponse represents a streaming response from Ollama
type OllamaStreamResponse struct {
	Model     string `json:"model"`
	CreatedAt string `json:"created_at"`
	Message   struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"message"`
	Done          bool   `json:"done"`
	DoneReason    string `json:"done_reason,omitempty"`
	TotalDuration int64  `json:"total_duration,omitempty"`
	EvalCount     int    `json:"eval_count,omitempty"`
}

// OllamaChatResponse represents the non-streaming response from Ollama
type OllamaChatResponse struct {
	Model     string `json:"model"`
	CreatedAt string `json:"created_at"`
	Message   struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"message"`
	Done               bool   `json:"done"`
	DoneReason         string `json:"done_reason,omitempty"`
	TotalDuration      int64  `json:"total_duration"`
	LoadDuration       int64  `json:"load_duration"`
	PromptEvalCount    int    `json:"prompt_eval_count"`
	PromptEvalDuration int64  `json:"prompt_eval_duration"`
	EvalCount          int    `json:"eval_count"`
	EvalDuration       int64  `json:"eval_duration"`
}

// OllamaAdaptor implements Ollama API adaptation
type OllamaAdaptor struct {
	config         structs.LLMProviderAPIConfig
	responseParser parser.ResponseParser
	httpClient     *http.Client
}

// NewOllamaAdaptor creates a new Ollama adaptor
func NewOllamaAdaptor(config structs.LLMProviderAPIConfig) *OllamaAdaptor {
	if config.MaxTokensName == "" {
		config.MaxTokensName = "num_predict"
	}
	return &OllamaAdaptor{
		config:         config,
		responseParser: parser.NewOllamaResponseParser(),
		httpClient: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second,
		},
	}
}

// getEndpoint returns the Ollama API endpoint
func (a *OllamaAdaptor) getEndpoint() string {
	return a.config.GetEndpoint()
}

// TestConnection tests the connection to Ollama service
func (a *OllamaAdaptor) TestConnection(ctx context.Context) (string, error) {
	// Create a simple test request
	req := map[string]interface{}{
		"model": a.config.GetModel(),
		"messages": []OllamaChatMessage{
			{
				Role:    "user",
				Content: "Hello, this is a connection test.",
			},
		},
		"stream": false,
		"options": map[string]interface{}{
			a.config.MaxTokensName: defaultTestMaxTokens,
			"temperature":          float64(a.config.GetTemperature()),
		},
	}

	// Marshal request to JSON
	reqBody, err := json.Marshal(req)
	if err != nil {
		endpoint := a.getEndpoint()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", a.getEndpoint(), bytes.NewReader(reqBody))
	if err != nil {
		endpoint := a.getEndpoint()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := a.httpClient.Do(httpReq)
	if err != nil {
		endpoint := a.getEndpoint()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		endpoint := a.getEndpoint()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, status:%d, err:%s", endpoint, resp.StatusCode, string(body)))
	}

	return "ollama connection test ok", nil
}

// Convert sends a conversation request and returns AI response
func (a *OllamaAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	prompts := item.GeneratePrompts()

	// Build messages array
	messages := make([]OllamaChatMessage, 0, len(prompts)+1)
	// Add system prompts
	for _, prompt := range prompts {
		messages = append(messages, OllamaChatMessage{
			Role:    "system",
			Content: prompt,
		})
	}
	// Add user message
	messages = append(messages, OllamaChatMessage{
		Role:    "user",
		Content: item.GetSQL(),
	})

	// Use configured max tokens
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)

	req := map[string]interface{}{
		"model":       a.config.GetModel(),
		"messages":    messages,
		"stream":      a.config.IsStream(),
		"temperature": float64(a.config.GetTemperature()),
		"options": map[string]interface{}{
			a.config.MaxTokensName: maxTokens,
			"temperature":          float64(a.config.GetTemperature()),
		},
	}

	// Log request for debugging
	reqBody, _ := json.Marshal(req)
	log.Debugf("postToLLMApi, body:%v", string(reqBody))

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", a.getEndpoint(), bytes.NewReader(reqBody))
	if err != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, err.Error())
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")

	// Handle streaming response if enabled
	if a.config.IsStream() {
		resp, err := a.httpClient.Do(httpReq)
		if err != nil {
			log.Errorf("create chat completion stream failed, taskId:%d, err:%v", taskId, err)
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, err.Error())
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			log.Errorf("stream request failed, taskId:%d, status:%d, body:%s", taskId, resp.StatusCode, string(body))
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("status %d: %s", resp.StatusCode, string(body)))
		}

		var fullContent strings.Builder
		scanner := bufio.NewScanner(resp.Body)

		for scanner.Scan() {
			line := scanner.Text()

			// Parse each line as JSON
			var streamResp OllamaStreamResponse
			if err := json.Unmarshal([]byte(line), &streamResp); err != nil {
				log.Warnf("failed to parse stream response: %v, data: %s", err, line)
				continue
			}

			// Accumulate content
			if streamResp.Message.Content != "" {
				fullContent.WriteString(streamResp.Message.Content)
			}

			// Check if streaming is done
			if streamResp.Done {
				break
			}
		}

		if err := scanner.Err(); err != nil {
			log.Errorf("error reading stream, taskId:%d, err:%v", taskId, err)
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, err.Error())
		}

		// Build response in expected format for parser
		completeResp := map[string]interface{}{
			"message": map[string]string{
				"role":    "assistant",
				"content": fullContent.String(),
			},
		}

		respBytes, _ := json.Marshal(completeResp)
		content, parseErr := a.responseParser.ParseResponse(string(respBytes))
		if parseErr != nil {
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, parseErr.Error())
		}
		return content, prompts, nil
	}

	// Non-streaming response
	resp, err := a.httpClient.Do(httpReq)
	if err != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, err:%v", taskId, err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, err.Error())
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Errorf("request failed, taskId:%d, status:%d, body:%s", taskId, resp.StatusCode, string(body))
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("status %d: %s", resp.StatusCode, string(body)))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body failed, taskId:%d, err:%v", taskId, err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, err.Error())
	}

	// Parse response using the parser
	content, parseErr := a.responseParser.ParseResponse(string(body))
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, parseErr.Error())
	}
	return content, prompts, nil
}
