package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// ChatMessage represents a chat message
type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatCompletionRequest represents the request structure
type ChatCompletionRequest struct {
	Model       string        `json:"model"`
	Messages    []ChatMessage `json:"messages"`
	MaxTokens   int           `json:"max_tokens,omitempty"`
	Temperature float64       `json:"temperature,omitempty"`
	Stream      bool          `json:"stream,omitempty"`
}

// ChatCompletionResponse represents the response structure
type ChatCompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int         `json:"index"`
		Message ChatMessage `json:"message"`
		Delta   struct {
			Content string `json:"content,omitempty"`
			Role    string `json:"role,omitempty"`
		} `json:"delta,omitempty"`
		FinishReason string `json:"finish_reason,omitempty"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// OpenAIAdaptor 实现 OpenAI 官方 RESTful API 适配
type OpenAIAdaptor struct {
	config         structs.LLMProviderAPIConfig
	responseParser parser.ResponseParser
	httpClient     *http.Client
}

// NewOpenAIAdaptor 构造函数
func NewOpenAIAdaptor(config structs.LLMProviderAPIConfig) *OpenAIAdaptor {
	if config.MaxTokensName == "" {
		config.MaxTokensName = "max_tokens"
	}
	return &OpenAIAdaptor{
		config:         config,
		responseParser: parser.NewOpenAIResponseParser(),
		httpClient: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second,
		},
	}
}

// getEndpoint 获取 API 端点
func (a *OpenAIAdaptor) getEndpoint() string {
	return a.config.GetEndpoint()
}

// TestConnection 测试与 OpenAI 服务的连接
func (a *OpenAIAdaptor) TestConnection(ctx context.Context) (string, error) {
	// Create a simple test request
	req := map[string]interface{}{
		"model": a.config.GetModel(),
		"messages": []ChatMessage{
			{
				Role:    "user",
				Content: "Hello, this is a connection test.",
			},
		},
		a.config.MaxTokensName: defaultTestMaxTokens,
		"temperature":          float64(a.config.GetTemperature()),
	}

	// Marshal request to JSON
	reqBody, err := json.Marshal(req)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
	}

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", a.getEndpoint(), bytes.NewReader(reqBody))
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+a.config.GetAPIKey())

	// Send request
	resp, err := a.httpClient.Do(httpReq)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, status:%d, err:%s", a.getEndpoint(), resp.StatusCode, string(body)))
	}

	return "openai connection test ok", nil
}

// Convert 发送对话请求，返回AI响应
func (a *OpenAIAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	prompts := item.GeneratePrompts()

	// Build messages array
	messages := make([]ChatMessage, 0, len(prompts)+1)
	// Add system prompts
	for _, prompt := range prompts {
		messages = append(messages, ChatMessage{
			Role:    "system",
			Content: prompt,
		})
	}
	// Add user message
	messages = append(messages, ChatMessage{
		Role:    "user",
		Content: item.GetSQL(),
	})

	// Use configured max tokens
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)

	req := map[string]interface{}{
		"model":                a.config.GetModel(),
		"messages":             messages,
		a.config.MaxTokensName: maxTokens,
		"temperature":          float64(a.config.GetTemperature()),
		"stream":               a.config.IsStream(),
	}

	// Log request for debugging
	reqBody, _ := json.Marshal(req)
	log.Debugf("postToLLMApi, body:%v", string(reqBody))

	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", a.getEndpoint(), bytes.NewReader(reqBody))
	if err != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
	}

	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+a.config.GetAPIKey())

	// Handle streaming response if enabled
	if a.config.IsStream() {
		httpReq.Header.Set("Accept", "text/event-stream")

		resp, err := a.httpClient.Do(httpReq)
		if err != nil {
			log.Errorf("create chat completion stream failed, taskId:%d, endpoint:%s, err:%v", taskId, a.getEndpoint(), err)
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			body, _ := io.ReadAll(resp.Body)
			log.Errorf("stream request failed, taskId:%d, endpoint:%s, status:%d, body:%s", taskId, a.getEndpoint(), resp.StatusCode, string(body))
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", a.getEndpoint(), resp.StatusCode, string(body)))
		}

		var fullContent strings.Builder
		var responseID string
		var responseModel string
		var finishReason string

		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			line := scanner.Text()

			// Skip empty lines
			if line == "" {
				continue
			}

			// Check for data: prefix
			if !strings.HasPrefix(line, "data: ") {
				continue
			}

			// Remove "data: " prefix
			data := strings.TrimPrefix(line, "data: ")

			// Check for [DONE] signal
			if data == "[DONE]" {
				break
			}

			// Parse JSON response
			var streamResp ChatCompletionResponse
			if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
				log.Warnf("failed to parse stream response: %v, data: %s", err, data)
				continue
			}

			// Capture metadata from first response
			if responseID == "" && streamResp.ID != "" {
				responseID = streamResp.ID
			}
			if responseModel == "" && streamResp.Model != "" {
				responseModel = streamResp.Model
			}

			if len(streamResp.Choices) > 0 {
				choice := streamResp.Choices[0]
				if choice.Delta.Content != "" {
					fullContent.WriteString(choice.Delta.Content)
				}
				if choice.FinishReason != "" {
					finishReason = choice.FinishReason
				}
			}
		}

		if err := scanner.Err(); err != nil {
			log.Errorf("error reading stream, taskId:%d, endpoint:%s, err:%v", taskId, a.getEndpoint(), err)
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
		}

		// Build a complete response in the expected format for parser
		completeResp := ChatCompletionResponse{
			ID:     responseID,
			Object: "chat.completion",
			Model:  responseModel,
			Choices: []struct {
				Index   int         `json:"index"`
				Message ChatMessage `json:"message"`
				Delta   struct {
					Content string `json:"content,omitempty"`
					Role    string `json:"role,omitempty"`
				} `json:"delta,omitempty"`
				FinishReason string `json:"finish_reason,omitempty"`
			}{
				{
					Index: 0,
					Message: ChatMessage{
						Role:    "assistant",
						Content: fullContent.String(),
					},
					FinishReason: finishReason,
				},
			},
		}

		respBytes, _ := json.Marshal(completeResp)
		content, parseErr := a.responseParser.ParseResponse(string(respBytes))
		if parseErr != nil {
			return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), parseErr))
		}
		return content, prompts, nil
	}

	// Non-streaming response
	resp, err := a.httpClient.Do(httpReq)
	if err != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, endpoint:%s, err:%v", taskId, a.getEndpoint(), err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Errorf("request failed, taskId:%d, endpoint:%s, status:%d, body:%s", taskId, a.getEndpoint(), resp.StatusCode, string(body))
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", a.getEndpoint(), resp.StatusCode, string(body)))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("read response body failed, taskId:%d, endpoint:%s, err:%v", taskId, a.getEndpoint(), err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), err))
	}

	// Parse response using the parser
	content, parseErr := a.responseParser.ParseResponse(string(body))
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.getEndpoint(), parseErr))
	}
	return content, prompts, nil
}
