package provider

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/structs"
)

type ConverterAdaptor interface {
	TestConnection(ctx context.Context) (string, error)
	Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error)
}

func GetConverter(param structs.LLMProviderAPIConfig) (ConverterAdaptor, error) {
	switch param.Source { // means llm provider
	// OpenAI-compatible providers
	case "openai", "deepseek", "vllm", "llama.cpp",
		"groq", "fireworks", "perplexity", "grok":
		return NewOpenAIAdaptor(param), nil
	// Platform-specific providers
	case "azure", "azure_api":
		return NewAzureAdaptor(param), nil
	case "gemini":
		return NewGeminiAdaptor(param), nil
	case "claude":
		return NewClaudeAdaptor(param), nil
	case "ollama":
		return NewOllamaAdaptor(param), nil
	// Chinese providers with custom protocols
	case "ernie", "erniebot", "baidu":
		return NewErnieAdaptor(param), nil
	case "qwen", "qianwen", "tongyi":
		return NewQwenAdaptor(param), nil
	case "hunyuan", "tencent":
		return NewHunyuanAdaptor(param), nil
	case "zhipu", "chatglm", "glm":
		return NewZhipuAdaptor(param), nil
	default:
		return nil, fmt.Errorf("unsupported ai source type %s", param.Source)
	}
}
