package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// QwenAdaptor 实现阿里通义千问 RESTful API 适配
type QwenAdaptor struct {
	config         structs.LLMProviderAPIConfig
	client         *http.Client
	responseParser parser.ResponseParser
}

// NewQwenAdaptor 构造函数
func NewQwenAdaptor(config structs.LLMProviderAPIConfig) *QwenAdaptor {
	if config.MaxTokensName == "" {
		config.MaxTokensName = "max_tokens"
	}
	return &QwenAdaptor{
		config: config,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second, // 300秒超时
		},
		responseParser: parser.NewQwenResponseParser(),
	}
}

// postToQwen 发送 POST 请求到阿里通义千问并返回响应字符串
func (a *QwenAdaptor) postToQwen(ctx context.Context, url string, body []byte) (string, error) {
	log.Debugf("postToLLMApi, url:%s, body:%v", url, string(body))

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	req.Header.Set("Content-Type", "application/json")
	if key := a.config.GetAPIKey(); key != "" {
		req.Header.Set("Authorization", "Bearer "+key)
	}
	// Add SSE headers for streaming
	if a.config.Stream {
		req.Header.Set("Accept", "text/event-stream")
	}
	resp, err := a.client.Do(req)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", url, resp.StatusCode, string(respBody)))
	}

	// Handle streaming response if enabled
	if a.config.Stream {
		return a.handleStreamResponse(resp.Body)
	}

	// Non-streaming response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	return string(respBody), nil
}

// handleStreamResponse 处理阿里通义千问流式响应
func (a *QwenAdaptor) handleStreamResponse(body io.Reader) (string, error) {
	reader := bufio.NewReader(body)
	var fullContent strings.Builder

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Qwen uses SSE format with specific structure
		if strings.HasPrefix(line, "data:") {
			data := strings.TrimPrefix(line, "data:")
			data = strings.TrimSpace(data)

			var streamChunk struct {
				Output struct {
					Text         string `json:"text"`
					FinishReason string `json:"finish_reason"`
				} `json:"output"`
			}

			if err := json.Unmarshal([]byte(data), &streamChunk); err != nil {
				// Skip malformed chunks
				continue
			}

			if streamChunk.Output.Text != "" {
				fullContent.WriteString(streamChunk.Output.Text)
			}
		}
	}

	// Build a complete response in the expected format
	completeResp := map[string]interface{}{
		"request_id": "stream_response",
		"output": map[string]interface{}{
			"text":          fullContent.String(),
			"finish_reason": "stop",
		},
		"usage": map[string]interface{}{
			"input_tokens":  0,
			"output_tokens": 0,
			"total_tokens":  0,
		},
	}

	respBytes, err := json.Marshal(completeResp)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}

	return string(respBytes), nil
}

// TestConnection 测试与阿里通义千问服务的连接
func (a *QwenAdaptor) TestConnection(ctx context.Context) (string, error) {
	url := a.buildChatCompletionUrl()
	// Use a small token limit for connection test
	body, err := a.buildChatRequestBody([]string{"Hello, this is a connection test."}, nil, defaultTestMaxTokens, a.config.GetTemperature())
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	_, err = a.postToQwen(ctx, url, body)
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	return "qwen connection test ok", nil
}

// Convert 发送对话请求，返回AI响应
func (a *QwenAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	url := a.buildChatCompletionUrl()
	prompts := item.GeneratePrompts()

	// Use configured max tokens, default to 2000 if not set
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)

	body, err := a.buildChatRequestBody([]string{item.GetSQL()}, prompts, maxTokens, a.config.GetTemperature())
	if err != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	resp, err := a.postToQwen(ctx, url, body)
	if err != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, endpoint:%s, err:%v", taskId, url, err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	content, parseErr := a.responseParser.ParseResponse(resp)
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, parseErr))
	}
	return content, prompts, nil
}

// buildChatCompletionUrl 构建阿里通义千问聊天接口 URL
func (a *QwenAdaptor) buildChatCompletionUrl() string {
	return a.config.GetEndpoint()
}

// buildChatRequestBody 构建 chat 请求体 (阿里通义千问格式)
func (a *QwenAdaptor) buildChatRequestBody(userMessages, systemMessages []string, maxTokens int, temperature float32) ([]byte, error) {
	type message struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	}
	var messages []message
	// 添加系统消息
	for _, sys := range systemMessages {
		messages = append(messages, message{Role: "system", Content: sys})
	}
	// 添加用户消息
	for _, user := range userMessages {
		messages = append(messages, message{Role: "user", Content: user})
	}

	model := a.config.GetModel()
	if model == "" {
		model = "qwen-turbo"
	}

	req := map[string]interface{}{
		"model": model,
		"input": map[string]interface{}{
			"messages": messages,
		},
		"parameters": map[string]interface{}{
			a.config.MaxTokensName: maxTokens,
			"temperature":          temperature,
			"stream":               a.config.Stream,
		},
	}
	return json.Marshal(req)
}
