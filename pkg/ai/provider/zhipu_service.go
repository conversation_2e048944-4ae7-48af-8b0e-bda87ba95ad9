package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// ZhipuAdaptor 实现智谱AI GLM RESTful API 适配
type ZhipuAdaptor struct {
	config         structs.LLMProviderAPIConfig
	client         *http.Client
	responseParser parser.ResponseParser
}

// NewZhipuAdaptor 构造函数
func NewZhipuAdaptor(config structs.LLMProviderAPIConfig) *ZhipuAdaptor {
	if config.MaxTokensName == "" {
		config.MaxTokensName = "max_tokens"
	}
	return &ZhipuAdaptor{
		config: config,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second, // 300秒超时
		},
		responseParser: parser.NewZhipuResponseParser(),
	}
}

// postToZhipu 发送 POST 请求到智谱AI并返回响应字符串
func (a *ZhipuAdaptor) postToZhipu(ctx context.Context, url string, body []byte) (string, error) {
	log.Debugf("postToLLMApi, url:%s, body:%v", url, string(body))

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	req.Header.Set("Content-Type", "application/json")
	if key := a.config.GetAPIKey(); key != "" {
		req.Header.Set("Authorization", "Bearer "+key)
	}
	resp, err := a.client.Do(req)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", url, resp.StatusCode, string(respBody)))
	}

	// Handle streaming response if enabled
	if a.config.Stream {
		return a.handleStreamResponse(resp.Body)
	}

	// Non-streaming response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	return string(respBody), nil
}

// handleStreamResponse 处理智谱AI流式响应
func (a *ZhipuAdaptor) handleStreamResponse(body io.Reader) (string, error) {
	reader := bufio.NewReader(body)
	var fullContent strings.Builder

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Zhipu uses SSE format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")
			if data == "[DONE]" {
				break
			}

			var streamChunk struct {
				ID      string `json:"id"`
				Created int64  `json:"created"`
				Model   string `json:"model"`
				Choices []struct {
					Index int `json:"index"`
					Delta struct {
						Role    string `json:"role"`
						Content string `json:"content"`
					} `json:"delta"`
					FinishReason string `json:"finish_reason"`
				} `json:"choices"`
			}

			if err := json.Unmarshal([]byte(data), &streamChunk); err != nil {
				// Skip malformed chunks
				continue
			}

			if len(streamChunk.Choices) > 0 && streamChunk.Choices[0].Delta.Content != "" {
				fullContent.WriteString(streamChunk.Choices[0].Delta.Content)
			}
		}
	}

	// Build a complete response in the expected format
	completeResp := map[string]interface{}{
		"id":      "chat_stream",
		"created": time.Now().Unix(),
		"model":   a.config.GetModel(),
		"choices": []map[string]interface{}{
			{
				"index": 0,
				"message": map[string]interface{}{
					"role":    "assistant",
					"content": fullContent.String(),
				},
				"finish_reason": "stop",
			},
		},
		"usage": map[string]interface{}{
			"prompt_tokens":     0,
			"completion_tokens": 0,
			"total_tokens":      0,
		},
	}

	respBytes, err := json.Marshal(completeResp)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}

	return string(respBytes), nil
}

// TestConnection 测试与智谱AI服务的连接
func (a *ZhipuAdaptor) TestConnection(ctx context.Context) (string, error) {
	url := a.buildChatCompletionUrl()
	// Use a small token limit for connection test
	body, err := a.buildChatRequestBody([]string{"Hello, this is a connection test."}, nil, defaultTestMaxTokens, a.config.GetTemperature())
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	_, err = a.postToZhipu(ctx, url, body)
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	return "zhipu connection test ok", nil
}

// Convert 发送对话请求，返回AI响应
func (a *ZhipuAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	url := a.buildChatCompletionUrl()
	prompts := item.GeneratePrompts()

	// Use configured max tokens, default to 2000 if not set
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)
	body, err := a.buildChatRequestBody([]string{item.GetSQL()}, prompts, maxTokens, a.config.GetTemperature())
	if err != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	resp, err := a.postToZhipu(ctx, url, body)
	if err != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, endpoint:%s, err:%v", taskId, url, err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	content, parseErr := a.responseParser.ParseResponse(resp)
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, parseErr))
	}
	return content, prompts, nil
}

// buildChatCompletionUrl 构建智谱AI聊天接口 URL
func (a *ZhipuAdaptor) buildChatCompletionUrl() string {
	return a.config.GetEndpoint()
}

// buildChatRequestBody 构建 chat 请求体 (智谱AI格式)
func (a *ZhipuAdaptor) buildChatRequestBody(userMessages, systemMessages []string, maxTokens int, temperature float32) ([]byte, error) {
	type message struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	}
	var messages []message
	// 添加系统消息
	for _, sys := range systemMessages {
		messages = append(messages, message{Role: "system", Content: sys})
	}
	// 添加用户消息
	for _, user := range userMessages {
		messages = append(messages, message{Role: "user", Content: user})
	}

	model := a.config.GetModel()
	if model == "" {
		model = "glm-4"
	}

	req := map[string]interface{}{
		"model":                model,
		"messages":             messages,
		a.config.MaxTokensName: maxTokens,
		"temperature":          temperature,
		"stream":               a.config.Stream,
	}
	return json.Marshal(req)
}
