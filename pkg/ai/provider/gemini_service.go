package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// GeminiConvertAdaptor 实现 Gemini RESTful API 适配
// 兼容 Google Gemini API，支持 TestConnection 和 Convert
// 详细文档：https://ai.google.dev/api/rest

type GeminiConvertAdaptor struct {
	config         structs.LLMProviderAPIConfig
	client         *http.Client
	responseParser parser.ResponseParser
}

// NewGeminiAdaptor 构造函数
func NewGeminiAdaptor(config structs.LLMProviderAPIConfig) *GeminiConvertAdaptor {
	return &GeminiConvertAdaptor{
		config: config,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second, // 300秒超时
		},
		responseParser: parser.NewGeminiResponseParser(),
	}
}

// TestConnection 测试 Gemini 服务的连接
func (a *GeminiConvertAdaptor) TestConnection(ctx context.Context) (string, error) {
	url := a.buildChatCompletionUrl()
	// Use a small token limit for connection test
	body, err := a.buildChatRequestBody([]string{"Hello, this is a connection test."}, nil, defaultTestMaxTokens, a.config.GetTemperature())
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	_, err = a.postToGemini(ctx, url, body)
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	return "gemini connection test ok", nil
}

// Convert 发送对话请求，返回AI响应
func (a *GeminiConvertAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	url := a.buildChatCompletionUrl()
	prompts := item.GeneratePrompts()

	// Use configured max tokens, default to 2000 if not set
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)
	body, err := a.buildChatRequestBody([]string{item.GetSQL()}, prompts, maxTokens, a.config.GetTemperature())
	if err != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	resp, err := a.postToGemini(ctx, url, body)
	if err != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, endpoint:%s, err:%v", taskId, url, err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	content, parseErr := a.responseParser.ParseResponse(resp)
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, parseErr))
	}
	return content, prompts, nil
}

// buildChatCompletionUrl 构建 Gemini 聊天接口 URL
func (a *GeminiConvertAdaptor) buildChatCompletionUrl() string {
	return a.config.GetEndpoint()
}

// buildChatRequestBody 构建 chat 请求体（Gemini 格式）
func (a *GeminiConvertAdaptor) buildChatRequestBody(userMessages, systemMessages []string, maxTokens int, temperature float32) ([]byte, error) {
	// Gemini API 需要 messages 字段，格式略有不同
	type content struct {
		Role  string `json:"role"`
		Parts []struct {
			Text string `json:"text"`
		} `json:"parts"`
	}
	var messages []content
	for _, sys := range systemMessages {
		messages = append(messages, content{
			Role: "user",
			Parts: []struct {
				Text string `json:"text"`
			}{{Text: sys}},
		})
	}
	for _, user := range userMessages {
		messages = append(messages, content{
			Role: "user",
			Parts: []struct {
				Text string `json:"text"`
			}{{Text: user}},
		})
	}
	req := map[string]interface{}{
		"contents": messages,
		"generationConfig": map[string]interface{}{
			"maxOutputTokens": maxTokens,
			"temperature":     temperature,
		},
	}
	return json.Marshal(req)
}

// postToGemini 发送 POST 请求到 Gemini 并返回响应字符串
func (a *GeminiConvertAdaptor) postToGemini(ctx context.Context, url string, body []byte) (string, error) {
	log.Debugf("postToLLMApi, url:%s, body:%v", url, string(body))

	// Gemini 使用 API key 作为 URL 参数
	if key := a.config.GetAPIKey(); key != "" {
		if strings.Contains(url, "?") {
			url += "&key=" + key
		} else {
			url += "?key=" + key
		}
	}
	// Add alt=sse for streaming
	if a.config.Stream {
		url += "&alt=sse"
	}
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := a.client.Do(req)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", url, resp.StatusCode, string(respBody)))
	}

	// Handle streaming response if enabled
	if a.config.Stream {
		return a.handleStreamResponse(resp.Body)
	}

	// Non-streaming response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	return string(respBody), nil
}

// handleStreamResponse 处理 Gemini 流式响应
func (a *GeminiConvertAdaptor) handleStreamResponse(body io.Reader) (string, error) {
	reader := bufio.NewReader(body)
	var fullContent strings.Builder

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Gemini uses SSE format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			var streamChunk struct {
				Candidates []struct {
					Content struct {
						Parts []struct {
							Text string `json:"text"`
						} `json:"parts"`
					} `json:"content"`
				} `json:"candidates"`
			}

			if err := json.Unmarshal([]byte(data), &streamChunk); err != nil {
				// Skip malformed chunks
				continue
			}

			// Extract text from parts
			if len(streamChunk.Candidates) > 0 && len(streamChunk.Candidates[0].Content.Parts) > 0 {
				fullContent.WriteString(streamChunk.Candidates[0].Content.Parts[0].Text)
			}
		}
	}

	// Build a complete response in the expected format
	completeResp := map[string]interface{}{
		"candidates": []map[string]interface{}{
			{
				"content": map[string]interface{}{
					"parts": []map[string]interface{}{
						{
							"text": fullContent.String(),
						},
					},
					"role": "model",
				},
				"finishReason": "STOP",
			},
		},
	}

	respBytes, err := json.Marshal(completeResp)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}

	return string(respBytes), nil
}
