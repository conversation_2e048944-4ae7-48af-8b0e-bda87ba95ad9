package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// ErnieAdaptor 实现百度文心一言 RESTful API 适配
type ErnieAdaptor struct {
	config         structs.LLMProviderAPIConfig
	client         *http.Client
	responseParser parser.ResponseParser
}

// NewErnieAdaptor 构造函数
func NewErnieAdaptor(config structs.LLMProviderAPIConfig) *ErnieAdaptor {
	if config.MaxTokensName == "" {
		config.MaxTokensName = "max_tokens"
	}
	return &ErnieAdaptor{
		config: config,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second, // 300秒超时
		},
		responseParser: parser.NewErnieResponseParser(),
	}
}

// postToErnie 发送 POST 请求到百度文心一言并返回响应字符串
func (a *ErnieAdaptor) postToErnie(ctx context.Context, url string, body []byte) (string, error) {
	log.Debugf("postToLLMApi, url:%s, body:%v", url, string(body))

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	req.Header.Set("Content-Type", "application/json")
	if key := a.config.GetAPIKey(); key != "" {
		req.Header.Set("Authorization", "Bearer "+key)
	}
	resp, err := a.client.Do(req)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", url, resp.StatusCode, string(respBody)))
	}

	// Handle streaming response if enabled
	if a.config.Stream {
		return a.handleStreamResponse(resp.Body)
	}

	// Non-streaming response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	return string(respBody), nil
}

// handleStreamResponse 处理百度文心一言流式响应
func (a *ErnieAdaptor) handleStreamResponse(body io.Reader) (string, error) {
	reader := bufio.NewReader(body)
	var fullContent strings.Builder

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Ernie uses SSE format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			var streamChunk struct {
				ID               string `json:"id"`
				Object           string `json:"object"`
				Created          int64  `json:"created"`
				Result           string `json:"result"`
				IsTruncated      bool   `json:"is_truncated"`
				NeedClearHistory bool   `json:"need_clear_history"`
			}

			if err := json.Unmarshal([]byte(data), &streamChunk); err != nil {
				// Skip malformed chunks
				continue
			}

			if streamChunk.Result != "" {
				fullContent.WriteString(streamChunk.Result)
			}
		}
	}

	// Build a complete response in the expected format
	completeResp := map[string]interface{}{
		"id":                 "chat_stream",
		"object":             "chat.completion",
		"created":            time.Now().Unix(),
		"result":             fullContent.String(),
		"is_truncated":       false,
		"need_clear_history": false,
	}

	respBytes, err := json.Marshal(completeResp)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}

	return string(respBytes), nil
}

// TestConnection 测试与百度文心一言服务的连接
func (a *ErnieAdaptor) TestConnection(ctx context.Context) (string, error) {
	url := a.buildChatCompletionUrl()
	// Use a small token limit for connection test
	body, err := a.buildChatRequestBody([]string{"Hello, this is a connection test."}, nil, defaultTestMaxTokens, a.config.GetTemperature())
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	_, err = a.postToErnie(ctx, url, body)
	if err != nil {
		endpoint := a.buildChatCompletionUrl()
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", endpoint, err))
	}
	return "ernie connection test ok", nil
}

// Convert 发送对话请求，返回AI响应
func (a *ErnieAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	url := a.buildChatCompletionUrl()
	prompts := item.GeneratePrompts()

	// Use configured max tokens, default to 2000 if not set
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)

	body, err := a.buildChatRequestBody([]string{item.GetSQL()}, prompts, maxTokens, a.config.GetTemperature())
	if err != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	resp, err := a.postToErnie(ctx, url, body)
	if err != nil {
		log.Errorf("convert PLSQL to Java failed, taskId:%d, endpoint:%s, err:%v", taskId, url, err)
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", url, err))
	}
	content, parseErr := a.responseParser.ParseResponse(resp)
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", url, parseErr))
	}
	return content, prompts, nil
}

// buildChatCompletionUrl 构建百度文心一言聊天接口 URL
func (a *ErnieAdaptor) buildChatCompletionUrl() string {
	return a.config.GetEndpoint()
}

// buildChatRequestBody 构建 chat 请求体 (百度文心一言格式)
func (a *ErnieAdaptor) buildChatRequestBody(userMessages, systemMessages []string, maxTokens int, temperature float32) ([]byte, error) {
	type message struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	}
	var messages []message
	// 添加系统消息
	for _, sys := range systemMessages {
		messages = append(messages, message{Role: "system", Content: sys})
	}
	// 添加用户消息
	for _, user := range userMessages {
		messages = append(messages, message{Role: "user", Content: user})
	}
	req := map[string]interface{}{
		"messages":             messages,
		a.config.MaxTokensName: maxTokens,
		"temperature":          temperature,
		"stream":               a.config.Stream,
	}
	return json.Marshal(req)
}
