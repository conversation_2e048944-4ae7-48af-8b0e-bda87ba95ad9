package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/parser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// ClaudeConvertAdaptor 实现 Claude RESTful API 适配
// 兼容 Anthropic Claude API，支持 TestConnection 和 Convert
// 官方文档：https://docs.anthropic.com/claude/reference/messages_post

type ClaudeConvertAdaptor struct {
	config         structs.LLMProviderAPIConfig
	client         *http.Client
	responseParser parser.ResponseParser
}

// NewClaudeAdaptor 构造函数
func NewClaudeAdaptor(config structs.LLMProviderAPIConfig) *ClaudeConvertAdaptor {
	if config.MaxTokensName == "" {
		config.MaxTokensName = "max_tokens"
	}
	return &ClaudeConvertAdaptor{
		config: config,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second, // 300秒超时
		},
		responseParser: parser.NewClaudeResponseParser(),
	}
}

// TestConnection 测试 Claude 服务的连接
func (a *ClaudeConvertAdaptor) TestConnection(ctx context.Context) (string, error) {
	url := a.buildClaudeUrl()
	// Use a small token limit for connection test
	body, err := a.buildClaudeRequestBody([]string{"Hello, this is a connection test."}, nil, defaultTestMaxTokens, a.config.GetTemperature())
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	_, err = a.postToClaude(ctx, url, body)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_CONNECTION_TEST, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	return "claude connection test ok", nil
}

// Convert 发送对话请求，返回AI响应
func (a *ClaudeConvertAdaptor) Convert(ctx context.Context, item ConvertContextInterface, taskId int) (string, []string, error) {
	url := a.buildClaudeUrl()
	prompts := item.GeneratePrompts()

	// Use configured max tokens, default to 2000 if not set
	maxTokens := a.config.GetMaxTokens(defaultCompletionMaxTokens)

	body, err := a.buildClaudeRequestBody([]string{item.GetSQL()}, prompts, maxTokens, defaultTestMaxTokens)
	if err != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	resp, err := a.postToClaude(ctx, url, body)
	if err != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	content, parseErr := a.responseParser.ParseResponse(resp)
	if parseErr != nil {
		return "", prompts, tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), parseErr))
	}
	return content, prompts, nil
}

// buildClaudeUrl 构建 Claude 聊天接口 URL
func (a *ClaudeConvertAdaptor) buildClaudeUrl() string {
	return a.config.GetEndpoint()
}

// buildClaudeRequestBody 构建 Claude 请求体
func (a *ClaudeConvertAdaptor) buildClaudeRequestBody(userMessages, systemMessages []string, maxTokens int, temperature float32) ([]byte, error) {
	type message struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	}
	var messages []message
	// Claude 只支持 user/assistant 角色
	for _, user := range userMessages {
		messages = append(messages, message{Role: "user", Content: user})
	}
	req := map[string]interface{}{
		"model":                a.config.GetModel(),
		a.config.MaxTokensName: maxTokens,
		"temperature":          temperature,
		"messages":             messages,
		"stream":               a.config.Stream,
	}
	// Claude 使用单独的 system 字段
	if len(systemMessages) > 0 {
		req["system"] = strings.Join(systemMessages, "\n")
	}
	return json.Marshal(req)
}

// postToClaude 发送 POST 请求到 Claude 并返回响应字符串
func (a *ClaudeConvertAdaptor) postToClaude(ctx context.Context, url string, body []byte) (string, error) {
	log.Debugf("postToLLMApi, url:%s, body:%v", url, string(body))

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(body))
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("anthropic-version", "2023-06-01")
	if key := a.config.GetAPIKey(); key != "" {
		req.Header.Set("x-api-key", key)
	}
	resp, err := a.client.Do(req)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_SEND_REQUEST, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		respBody, _ := io.ReadAll(resp.Body)
		return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_RESPONSE, fmt.Sprintf("endpoint:%s, status:%d, err:%s", a.config.GetEndpoint(), resp.StatusCode, string(respBody)))
	}

	// Handle streaming response if enabled
	if a.config.Stream {
		return a.handleStreamResponse(resp.Body)
	}

	// Non-streaming response
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_READ_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}
	return string(respBody), nil
}

// handleStreamResponse 处理 Claude 流式响应
func (a *ClaudeConvertAdaptor) handleStreamResponse(body io.Reader) (string, error) {
	reader := bufio.NewReader(body)
	var fullContent strings.Builder

	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", tmserrors.NewError(tmserrors.TMS_LLM_RECEIVE_STREAM_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Claude uses a different streaming format
		if strings.HasPrefix(line, "data: ") {
			data := strings.TrimPrefix(line, "data: ")

			var streamEvent struct {
				Type  string `json:"type"`
				Index int    `json:"index"`
				Delta struct {
					Type string `json:"type"`
					Text string `json:"text"`
				} `json:"delta"`
			}

			if err := json.Unmarshal([]byte(data), &streamEvent); err != nil {
				// Skip malformed chunks
				continue
			}

			// Claude sends different event types
			if streamEvent.Type == "content_block_delta" && streamEvent.Delta.Type == "text_delta" {
				fullContent.WriteString(streamEvent.Delta.Text)
			}
		}
	}

	// Build a complete response in the expected format
	completeResp := map[string]interface{}{
		"id":   "msg_stream",
		"type": "message",
		"role": "assistant",
		"content": []map[string]interface{}{
			{
				"type": "text",
				"text": fullContent.String(),
			},
		},
		"model":       a.config.GetModel(),
		"stop_reason": "end_turn",
	}

	respBytes, err := json.Marshal(completeResp)
	if err != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_PARSE_RESPONSE, fmt.Sprintf("endpoint:%s, err:%s", a.config.GetEndpoint(), err))
	}

	return string(respBytes), nil
}
