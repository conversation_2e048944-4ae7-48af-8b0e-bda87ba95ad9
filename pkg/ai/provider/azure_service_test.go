package provider

import "testing"

func TestToJavaClassName(t *testing.T) {
	type args struct {
		input string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{name: "1", args: args{input: "XX_BB"}, want: "XxBb"},
		{name: "2", args: args{input: "DDaCuuIIOO"}, want: "Ddacuuiioo"},
		{name: "3", args: args{input: "_Double"}, want: "Double"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToJavaClassName(tt.args.input); got != tt.want {
				t.Errorf("ToJavaClassName() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToJavaFunctionName(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"P_CALC_STAR_REWARD", "pCalcStarReward"},
		{"HELLO_WORLD", "helloWorld"},
		{"P_TEST", "pTest"},
		{"SINGLE", "single"},
		{"", ""},
		{"P_", "p"},
		{"A", "a"},
		{"MULTIPLE___UNDERSCORES", "multipleUnderscores"},
		{"p_calc_complex_name", "pCalcComplexName"},
		{"V1_TEST_FUNC", "v1TestFunc"},
		{"ABC123_XYZ", "abc123Xyz"},
	}

	for _, test := range tests {
		result := ToJavaFunctionName(test.input)
		if result != test.expected {
			t.Errorf("For input '%s', expected '%s', but got '%s'",
				test.input, test.expected, result)
		}
	}
}
