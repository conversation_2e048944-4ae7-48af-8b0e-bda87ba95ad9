package ai

import (
	"context"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/provider"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type AIConvertorProxy struct {
	taskId int
	param  structs.LLMProviderAPIConfig
}

func NewAIConvertorProxy(taskId int, param structs.LLMProviderAPIConfig) *AIConvertorProxy {
	return &AIConvertorProxy{
		taskId: taskId,
		param:  param,
	}
}

func (i *AIConvertorProxy) Convert(ctx context.Context, item provider.ConvertContextInterface) (string, []string, error) {
	converter, getErr := provider.GetConverter(i.param)
	if getErr != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LLM_INIT_CLIENT, getErr.Error())
	}

	ret, prompts, err := converter.Convert(ctx, item, i.taskId)
	if err != nil {
		return ret, prompts, err
	}

	log.Debugf("convert result: %s", ret)

	return ret, prompts, nil
}

func (i *AIConvertorProxy) TestConnection(ctx context.Context) (string, error) {
	converter, getErr := provider.GetConverter(i.param)
	if getErr != nil {
		return "", tmserrors.NewError(tmserrors.TMS_LLM_INIT_CLIENT, getErr.Error())
	}

	return converter.TestConnection(ctx)
}
