package license

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"

	"gitee.com/pingcap_enterprise/tms/util/log"
)

// singletonCache manages LicenseManager singleton instances.
type singletonCache struct {
	mu        sync.Mutex                 // Protects access to instances and onces maps
	instances map[string]*LicenseManager // Maps cache key to LicenseManager instance
	onces     map[string]*sync.Once      // Maps cache key to sync.Once for initialization
	lastErr   error
}

// globalCache is the global singleton cache for LicenseManager instances.
var globalCache = &singletonCache{
	instances: make(map[string]*LicenseManager),
	onces:     make(map[string]*sync.Once),
}

// generateCache<PERSON>ey creates a unique key for a license and config combination.
// It uses the license and Config.EncryptionKey and Config.HMACKey to ensure uniqueness.
// If Config has additional fields, extend this function to include them.
func generateCacheKey(license string, conf Config) string {
	return fmt.Sprintf("%s:%s:%s", license, conf.EncryptionKey, conf.HMACKey)
}

// LicenseManager manages license validation, database operations, and state chains.
type LicenseManager struct {
	license            string                        // The license key
	featureChains      map[string]*StateChainManager // Maps feature IDs to state chain managers
	mu                 sync.RWMutex                  // Read-write mutex for thread-safe access
	conf               Config                        // Configuration for HMAC and other settings
	optimisticExecutor *OptimisticLicenseExecutor    // Optimistic locking executor for atomic operations
}

// NewLicenseManager creates or returns a singleton LicenseManager for the given license and config.
// It uses sync.Once to ensure that initialization occurs exactly once per unique (license, conf) pair.
// If the same parameters are provided, the same instance is returned from the cache.
func NewLicenseManager(license string, conf Config) (*LicenseManager, error) {
	cacheKey := generateCacheKey(license, conf)

	// Get or create sync.Once for this cache key
	globalCache.mu.Lock()
	once, exists := globalCache.onces[cacheKey]
	if !exists {
		once = &sync.Once{}
		globalCache.onces[cacheKey] = once
	}
	globalCache.mu.Unlock()

	var manager *LicenseManager
	var initErr error

	// Perform initialization exactly once
	once.Do(func() {
		manager = &LicenseManager{
			license:            license,
			featureChains:      make(map[string]*StateChainManager),
			conf:               conf,
			optimisticExecutor: NewOptimisticLicenseExecutor(),
		}

		// Initialize the license (validate JWT, setup database, and state chains)
		if err := manager.initializeLicense(license); err != nil {
			initErr = err
			globalCache.mu.Lock()
			globalCache.lastErr = err
			globalCache.mu.Unlock()
			return
		}

		// Store the initialized instance in the cache
		globalCache.mu.Lock()
		globalCache.instances[cacheKey] = manager
		globalCache.mu.Unlock()
	})

	if initErr != nil {
		return nil, initErr
	}
	if globalCache.lastErr != nil {
		return nil, globalCache.lastErr
	}

	// Return the cached instance
	globalCache.mu.Lock()
	defer globalCache.mu.Unlock()
	return globalCache.instances[cacheKey], globalCache.lastErr
}

// initializeLicense validates the JWT license and initializes the database and state chains.
// It checks license expiration, validates claims, and sets up feature data and state chains.
func (lm *LicenseManager) initializeLicense(license string) error {
	ctx := context.Background()
	ls := NewLicenseService()
	result, err := ls.VerifyLicense(ctx, license)
	if err != nil {
		return fmt.Errorf("license verification failed: %w", err)
	}

	if result.IsExpired() {
		return fmt.Errorf("license has expired at %s", time.Unix(result.ExpireTime(), 0))
	}

	claims := result.claims
	if err := claims.validateSub(); err != nil {
		return err
	}

	if time.Now().Unix() > claims.LoadDeadline {
		log.Warn("load deadline exceeded, skipping database initialization")
		log.Info("loading state chain from file...")
		for _, feature := range claims.Features {
			chainFilePath := lm.generateChainFilePath(feature)
			scm, err := NewStateChainManager(chainFilePath, lm.conf)
			if err != nil {
				return fmt.Errorf("failed to create state chain manager for %s: %w", feature.FeatureID, err)
			}
			lm.featureChains[feature.FeatureID] = scm

			if err := lm.validateStateChain(context.Background(), scm, feature.FeatureID, license); err != nil {
				return fmt.Errorf("state chain validation failed for %s: %w", feature.FeatureID, err)
			}
		}
	} else {
		log.Info("load deadline not exceeded, try initializing database and state chains files...")
		// Run database initialization in a transaction
		err = models.Transaction(ctx, func(transactionCtx context.Context) error {
			for _, feature := range claims.Features {
				if err := lm.initializeFeature(transactionCtx, feature, license); err != nil {
					return fmt.Errorf("failed to initialize feature %s: %w", feature.FeatureID, err)
				}

				chainFilePath := lm.generateChainFilePath(feature)
				scm, err := NewStateChainManager(chainFilePath, lm.conf)
				if err != nil {
					return fmt.Errorf("failed to create state chain manager for %s: %w", feature.FeatureID, err)
				}
				lm.featureChains[feature.FeatureID] = scm

				if err := lm.validateStateChain(transactionCtx, scm, feature.FeatureID, license); err != nil {
					return fmt.Errorf("state chain validation failed for %s: %w", feature.FeatureID, err)
				}
			}
			return nil
		})
		if err != nil {
			return fmt.Errorf("transaction failed: %w", err)
		}
	}
	return nil
}

// generateChainFilePath generates a file path for a feature's state chain file.
// It uses a SHA-256 hash of the license to create a unique directory and includes
// both license hash and feature ID in the filename to prevent conflicts between
// different licenses having the same feature ID.
func (lm *LicenseManager) generateChainFilePath(feature LicenseFeature) string {
	hash := sha256.Sum256([]byte(lm.license))
	hashPrefix := hex.EncodeToString(hash[:])[:10]

	// Include license hash in filename to prevent conflicts between different licenses
	// with the same feature ID
	featureHash := sha256.Sum256([]byte(fmt.Sprintf("%s:%s", lm.license, feature.FeatureID)))
	featureHashPrefix := hex.EncodeToString(featureHash[:])[:8]

	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}
	return filepath.Join(homeDir, ".tms", hashPrefix, fmt.Sprintf("state_chain_%s_%s.dat", feature.FeatureID, featureHashPrefix))
}

// initializeFeature inserts or validates feature data in the database.
// It creates a new feature license if it doesn't exist, or verifies the existing one.
func (lm *LicenseManager) initializeFeature(ctx context.Context, feature LicenseFeature, license string) error {
	existingFeature, getErr := models.GetSystemInfoReaderWriter().GetFeatureLicense(ctx, feature.FeatureID, license)
	if getErr != nil && existingFeature == nil {
		return fmt.Errorf("failed to check existing feature: %w", getErr)
	}

	if existingFeature == nil {
		signature := lm.calculateSignature(feature.FeatureID, feature.MaxUsageCount, 0, license)

		log.Infof("feature not found in db, initializing db record and file:%s, maxUsageCount:%d", feature.FeatureID, feature.MaxUsageCount)

		var saveErr error
		existingFeature, saveErr = models.GetSystemInfoReaderWriter().InsertFeatureLicense(ctx, &common.FeatureLicense{
			FeatureCode:           feature.FeatureID,
			FeatureName:           feature.FeatureName,
			MaxUsageCount:         feature.MaxUsageCount,
			CurrentUsageCount:     0,
			LicenseKey:            license,
			VerificationSignature: signature,
		})
		if saveErr != nil {
			return fmt.Errorf("failed to insert feature: %w", saveErr)
		}
		if existingFeature == nil {
			log.Warnf("feature %s already exists", feature.FeatureID)
		} else {
			log.Infof("feature %s initialized successfully", feature.FeatureID)
		}

		var initErr error
		var hashErr error
		scm := lm.featureChains[feature.FeatureID]
		if scm == nil {
			chainFilePath := lm.generateChainFilePath(feature)
			scm, initErr = NewStateChainManager(chainFilePath, lm.conf)
			if initErr != nil {
				return fmt.Errorf("failed to create state chain manager: %w", initErr)
			}
			lm.featureChains[feature.FeatureID] = scm
		}

		record := StateChainRecord{
			FeatureCode:       feature.FeatureID,
			LicenseKey:        license,
			CurrentUsageCount: 0,
			PreviousHash:      "",
		}
		record.CurrentHash, hashErr = CalculateHash(record, "")
		if hashErr != nil {
			return fmt.Errorf("failed to calculate initial hash: %w", hashErr)
		}
		return scm.AppendRecord(record)
	} else {
		log.Infof("feature found in db, skip and validating, feature:%s, maxUsageCount:%d", feature.FeatureID, feature.MaxUsageCount)
	}

	expectedSignature := lm.calculateSignature(feature.FeatureID, feature.MaxUsageCount, existingFeature.CurrentUsageCount, license)
	if existingFeature.VerificationSignature != expectedSignature {
		return fmt.Errorf("signature mismatch for feature %s", feature.FeatureID)
	}

	return nil
}

// calculateSignature generates an HMAC-SHA256 signature for a feature.
// It combines feature code, max count, current count, and license key.
func (lm *LicenseManager) calculateSignature(featureCode string, maxCount, currentCount uint, license string) string {
	data := fmt.Sprintf("%s:%d:%d:%s", featureCode, maxCount, currentCount, license)
	h := hmac.New(sha256.New, []byte(lm.conf.HMACKey))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

// validateStateChain ensures the state chain matches the database.
// It verifies chain integrity and consistency of usage counts.
// Enhanced with automatic recovery for certain inconsistency scenarios.
func (lm *LicenseManager) validateStateChain(ctx context.Context, scm *StateChainManager, featureID, license string) error {
	records, loadErr := scm.LoadChain()
	if loadErr != nil {
		return fmt.Errorf("failed to load state chain: %w", loadErr)
	}
	if verifyErr := scm.VerifyChainIntegrity(records); verifyErr != nil {
		return fmt.Errorf("state chain integrity check failed: %w", verifyErr)
	}

	existingFeature, getErr := models.GetSystemInfoReaderWriter().GetFeatureLicense(ctx, featureID, license)
	if getErr != nil {
		return fmt.Errorf("failed to get feature license: %w", getErr)
	}
	if existingFeature == nil {
		log.Errorf("feature license not found for featureID: %s, license: %s", featureID, license)
		return fmt.Errorf("feature license not found for featureID: %s", featureID)
	}

	lastRecord, err := scm.GetLastRecord()
	if err != nil {
		return fmt.Errorf("failed to get last state chain record: %w", err)
	}

	if lastRecord != nil && lastRecord.CurrentUsageCount != existingFeature.CurrentUsageCount {
		log.Warnf("State chain inconsistency detected for feature %s: chain count=%d, db count=%d",
			featureID, lastRecord.CurrentUsageCount, existingFeature.CurrentUsageCount)

		// Determine which source is more reliable
		// If state chain count is higher, it suggests database update failed after file update
		// If database count is higher, it suggests file corruption or incomplete write
		if lastRecord.CurrentUsageCount > existingFeature.CurrentUsageCount {
			// State chain is ahead - likely database update failed
			// This is a recoverable scenario logged by ExecuteFeature
			log.Warnf("State chain ahead of database for feature %s - may indicate previous database update failure", featureID)
			return fmt.Errorf("state chain count %d is ahead of database count %d - inconsistent state detected",
				lastRecord.CurrentUsageCount, existingFeature.CurrentUsageCount)
		} else {
			// Database is ahead - this is more concerning as it suggests file issues
			log.Errorf("Database ahead of state chain for feature %s - potential file corruption", featureID)
			return fmt.Errorf("database count %d is ahead of state chain count %d - potential file corruption",
				existingFeature.CurrentUsageCount, lastRecord.CurrentUsageCount)
		}
	}

	return nil
}

// ExecuteFeature handles a feature execution request using optimistic locking.
// It ensures atomic updates to both database and state chain within a single transaction.
// Automatically retries on version conflicts with exponential backoff.
func (lm *LicenseManager) ExecuteFeature(ctx context.Context, featureID string) error {
	// Use read lock for checking if feature chain exists
	lm.mu.RLock()
	_, exists := lm.featureChains[featureID]
	lm.mu.RUnlock()

	if !exists {
		return fmt.Errorf("no state chain manager for feature %s", featureID)
	}

	// Delegate to optimistic executor for atomic execution with retry logic
	return lm.optimisticExecutor.ExecuteFeature(ctx, lm, featureID)
}

// ValidateRequestNum validates if the requested feature usage is within limits.
// Delegates to the optimistic executor for consistent validation logic.
func (lm *LicenseManager) ValidateRequestNum(ctx context.Context, req OperationRequest) error {
	// Delegate to optimistic executor for consistent validation
	return lm.optimisticExecutor.ValidateRequestNum(ctx, lm, req)
}

// listFeaturesInternal is the internal, non-locking implementation of ListFeatures.
// It retrieves and validates feature licenses without acquiring a lock, relying on the caller's lock.
func (lm *LicenseManager) listFeaturesInternal(ctx context.Context, license string) ([]common.FeatureLicense, error) {
	featureLicenses, err := models.GetSystemInfoReaderWriter().ListFeatureLicenses(ctx, license)
	if err != nil {
		return nil, fmt.Errorf("failed to list feature licenses: %w", err)
	}

	var validFeatures []common.FeatureLicense
	for _, feature := range featureLicenses {
		scm, exists := lm.featureChains[feature.FeatureCode]
		if !exists {
			log.Warnf("no state chain manager for feature, featureID: %s", feature.FeatureCode)
			continue
		}

		if err := lm.validateStateChain(ctx, scm, feature.FeatureCode, license); err != nil {
			log.Warnf("state chain validation failed for feature, featureID: %s, error: %v", feature.FeatureCode, err)
			continue
		}

		expectedSignature := lm.calculateSignature(feature.FeatureCode, feature.MaxUsageCount, feature.CurrentUsageCount, license)
		if feature.VerificationSignature != expectedSignature {
			log.Warnf("signature mismatch for feature, featureID: %s", feature.FeatureCode)
			continue
		}

		validFeatures = append(validFeatures, *feature)
	}

	return validFeatures, nil
}

// ListFeatures retrieves and validates feature licenses for a given license key.
// It uses a read lock to allow concurrent reads.
func (lm *LicenseManager) ListFeatures(ctx context.Context, license string) ([]common.FeatureLicense, error) {
	lm.mu.RLock()
	defer lm.mu.RUnlock()
	return lm.listFeaturesInternal(ctx, license)
}

// GetFeatureId maps an object type to a feature ID.
func (lm *LicenseManager) GetFeatureId(objectType string) constants.FeatureID {
	switch strings.TrimSpace(strings.ToUpper(objectType)) {
	case "PROCEDURE":
		return constants.OpProcedureNum
	case "FUNCTION":
		return constants.OpFunctionNum
	case "PACKAGE", "PACKAGE BODY":
		return constants.OpPackageNum
	case "TABLE":
		return constants.OpTableNum
	case "TRIGGER":
		return constants.OpTriggerNum
	}
	return constants.OpUnknown
}
