package license

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
)

// TestNewStateChainManager tests the creation of a new StateChainManager
func TestNewStateChainManager(t *testing.T) {
	tests := []struct {
		name        string
		config      Config
		filePath    string
		expectedErr bool
	}{
		{
			name: "Valid config with plain hex key",
			config: Config{
				EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
				HMACKey:       "test-hmac-key",
			},
			filePath:    "test_statechain.dat",
			expectedErr: false,
		},
		{
			name: "Valid config with hyphenated hex key",
			config: Config{
				EncryptionKey: "12345678-90abcdef-12345678-90abcdef-12345678-90abcdef-12345678-90abcdef",
				HMACKey:       "test-hmac-key",
			},
			filePath:    "test_statechain.dat",
			expectedErr: false,
		},
		{
			name: "Invalid encryption key length",
			config: Config{
				EncryptionKey: "1234567890abcdef",
				HMACKey:       "test-hmac-key",
			},
			filePath:    "test_statechain.dat",
			expectedErr: true,
		},
		{
			name: "Invalid hex encryption key",
			config: Config{
				EncryptionKey: "not-a-hex-key",
				HMACKey:       "test-hmac-key",
			},
			filePath:    "test_statechain.dat",
			expectedErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			scm, err := NewStateChainManager(tt.filePath, tt.config)
			if (err != nil) != tt.expectedErr {
				t.Errorf("NewStateChainManager() error = %v, expectedErr %v", err, tt.expectedErr)
			}
			if !tt.expectedErr && scm == nil {
				t.Errorf("NewStateChainManager() returned nil manager for valid input")
			}
		})
	}
}

// TestAppendRecord tests appending records to the state chain
func TestAppendRecord(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	record := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 1,
		PreviousHash:      "",
	}

	err = scm.AppendRecord(record)
	if err != nil {
		t.Errorf("AppendRecord() failed: %v", err)
	}

	// Verify file exists and has content
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Errorf("AppendRecord() did not create file")
	}
}

// TestLoadChain tests loading and decrypting the state chain
func TestLoadChain(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	// Test with empty file
	records, err := scm.LoadChain()
	if err != nil {
		t.Errorf("LoadChain() with empty file failed: %v", err)
	}
	if len(records) != 0 {
		t.Errorf("LoadChain() with empty file returned %d records, expected 0", len(records))
	}

	// Test with one record
	record := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 1,
		PreviousHash:      "",
	}
	err = scm.AppendRecord(record)
	if err != nil {
		t.Fatalf("AppendRecord() failed: %v", err)
	}

	records, err = scm.LoadChain()
	if err != nil {
		t.Errorf("LoadChain() failed: %v", err)
	}
	if len(records) != 1 {
		t.Errorf("LoadChain() returned %d records, expected 1", len(records))
	}
	if len(records) > 0 {
		if records[0].FeatureCode != record.FeatureCode ||
			records[0].LicenseKey != record.LicenseKey ||
			records[0].CurrentUsageCount != record.CurrentUsageCount {
			t.Errorf("LoadChain() returned incorrect record data")
		}
	}
}

// TestVerifyChainIntegrity tests the chain integrity verification
func TestVerifyChainIntegrity(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	// Create two records with proper hash chain
	record1 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 1,
		PreviousHash:      "",
	}
	hash1, err := CalculateHash(record1, "")
	if err != nil {
		t.Fatalf("CalculateHash() failed: %v", err)
	}
	record1.CurrentHash = hash1

	record2 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 2,
		PreviousHash:      hash1,
	}
	hash2, err := CalculateHash(record2, hash1)
	if err != nil {
		t.Fatalf("CalculateHash() failed: %v", err)
	}
	record2.CurrentHash = hash2

	// Append records
	if err := scm.AppendRecord(record1); err != nil {
		t.Fatalf("AppendRecord() failed: %v", err)
	}
	if err := scm.AppendRecord(record2); err != nil {
		t.Fatalf("AppendRecord() failed: %v", err)
	}

	// Load and verify chain
	records, err := scm.LoadChain()
	if err != nil {
		t.Fatalf("LoadChain() failed: %v", err)
	}

	err = scm.VerifyChainIntegrity(records)
	if err != nil {
		t.Errorf("VerifyChainIntegrity() failed: %v", err)
	}

	// Test corrupted chain
	records[1].PreviousHash = "invalid_hash"
	err = scm.VerifyChainIntegrity(records)
	if err == nil {
		t.Errorf("VerifyChainIntegrity() should have failed with corrupted hash")
	}
}

// TestGetLastRecord tests retrieving the last record
func TestGetLastRecord(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	// Test with empty chain
	record, err := scm.GetLastRecord()
	if err != nil {
		t.Errorf("GetLastRecord() with empty chain failed: %v", err)
	}
	if record != nil {
		t.Errorf("GetLastRecord() with empty chain returned non-nil record")
	}

	// Test with one record
	expected := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 1,
		PreviousHash:      "",
	}
	if err := scm.AppendRecord(expected); err != nil {
		t.Fatalf("AppendRecord() failed: %v", err)
	}

	record, err = scm.GetLastRecord()
	if err != nil {
		t.Errorf("GetLastRecord() failed: %v", err)
	}
	if record == nil {
		t.Errorf("GetLastRecord() returned nil record")
	} else {
		if record.FeatureCode != expected.FeatureCode ||
			record.LicenseKey != expected.LicenseKey ||
			record.CurrentUsageCount != expected.CurrentUsageCount {
			t.Errorf("GetLastRecord() returned incorrect record data")
		}
	}
}

// TestHMACVerification tests HMAC verification
// TestHMACVerification tests HMAC verification
func TestHMACVerification(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	record := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 1,
		PreviousHash:      "",
	}
	if err := scm.AppendRecord(record); err != nil {
		t.Fatalf("AppendRecord() failed: %v", err)
	}

	// Test valid HMAC by loading the chain
	_, err = scm.LoadChain()
	if err != nil {
		t.Errorf("LoadChain() failed for valid content: %v", err)
	}

	// Test corrupted HMAC
	content, err := os.ReadFile(filePath)
	if err != nil {
		t.Fatalf("Failed to read file: %v", err)
	}
	// Corrupt the HMAC (last byte)
	content[len(content)-1] = content[len(content)-1] ^ 0xFF
	if err := os.WriteFile(filePath, content, 0644); err != nil {
		t.Fatalf("Failed to write corrupted file: %v", err)
	}

	// Verify LoadChain fails with corrupted HMAC
	_, err = scm.LoadChain()
	if err == nil {
		t.Errorf("LoadChain() should have failed for corrupted HMAC")
	} else if !strings.Contains(err.Error(), "record HMAC verification failed") {
		t.Errorf("LoadChain() failed with unexpected error: %v", err)
	}
}

// TestConcurrentAccess tests concurrent access to the state chain
func TestConcurrentAccess(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	var wg sync.WaitGroup
	numGoroutines := 10
	recordsPerGoroutine := 5

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			for j := 0; j < recordsPerGoroutine; j++ {
				record := StateChainRecord{
					FeatureCode:       fmt.Sprintf("feature%d", id),
					LicenseKey:        fmt.Sprintf("license%d-%d", id, j),
					CurrentUsageCount: uint(j + 1),
					PreviousHash:      "",
				}
				if err := scm.AppendRecord(record); err != nil {
					t.Errorf("AppendRecord() failed in goroutine %d: %v", id, err)
				}
			}
		}(i)
	}

	wg.Wait()

	records, err := scm.LoadChain()
	if err != nil {
		t.Fatalf("LoadChain() failed: %v", err)
	}
	if len(records) != numGoroutines*recordsPerGoroutine {
		t.Errorf("Expected %d records, got %d", numGoroutines*recordsPerGoroutine, len(records))
	}
}

func TestSequentialAppendAndVerify(t *testing.T) {
	tmpDir := t.TempDir()
	filePath := filepath.Join(tmpDir, "statechain.dat")
	config := Config{
		EncryptionKey: "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		HMACKey:       "test-hmac-key",
	}

	scm, err := NewStateChainManager(filePath, config)
	if err != nil {
		t.Fatalf("NewStateChainManager() failed: %v", err)
	}

	// Step 1: Write record1 and record2
	record1 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 1,
		PreviousHash:      "",
	}
	hash1, err := CalculateHash(record1, "")
	if err != nil {
		t.Fatalf("CalculateHash() failed for record1: %v", err)
	}
	record1.CurrentHash = hash1
	if err := scm.AppendRecord(record1); err != nil {
		t.Fatalf("AppendRecord() failed for record1: %v", err)
	}

	record2 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 2,
		PreviousHash:      hash1,
	}
	hash2, err := CalculateHash(record2, hash1)
	if err != nil {
		t.Fatalf("CalculateHash() failed for record2: %v", err)
	}
	record2.CurrentHash = hash2
	if err := scm.AppendRecord(record2); err != nil {
		t.Fatalf("AppendRecord() failed for record2: %v", err)
	}

	// Verify after record1 and record2
	records, err := scm.LoadChain()
	if err != nil {
		t.Fatalf("LoadChain() failed after record2: %v", err)
	}
	if err := scm.VerifyChainIntegrity(records); err != nil {
		t.Errorf("VerifyChainIntegrity() failed after record2: %v", err)
	}

	// Step 2: Write record3
	record3 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 3,
		PreviousHash:      hash2,
	}
	hash3, err := CalculateHash(record3, hash2)
	if err != nil {
		t.Fatalf("CalculateHash() failed for record3: %v", err)
	}
	record3.CurrentHash = hash3
	if err := scm.AppendRecord(record3); err != nil {
		t.Fatalf("AppendRecord() failed for record3: %v", err)
	}

	// Verify after record3
	records, err = scm.LoadChain()
	if err != nil {
		t.Fatalf("LoadChain() failed after record3: %v", err)
	}
	if err := scm.VerifyChainIntegrity(records); err != nil {
		t.Errorf("VerifyChainIntegrity() failed after record3: %v", err)
	}

	// Step 3: Write record4 and record5
	record4 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 4,
		PreviousHash:      hash3,
	}
	hash4, err := CalculateHash(record4, hash3)
	if err != nil {
		t.Fatalf("CalculateHash() failed for record4: %v", err)
	}
	record4.CurrentHash = hash4
	if err := scm.AppendRecord(record4); err != nil {
		t.Fatalf("AppendRecord() failed for record4: %v", err)
	}

	record5 := StateChainRecord{
		FeatureCode:       "feature1",
		LicenseKey:        "license123",
		CurrentUsageCount: 5,
		PreviousHash:      hash4,
	}
	hash5, err := CalculateHash(record5, hash4)
	if err != nil {
		t.Fatalf("CalculateHash() failed for record5: %v", err)
	}
	record5.CurrentHash = hash5
	if err := scm.AppendRecord(record5); err != nil {
		t.Fatalf("AppendRecord() failed for record5: %v", err)
	}

	// Verify after record4 and record5
	records, err = scm.LoadChain()
	if err != nil {
		t.Fatalf("LoadChain() failed after record5: %v", err)
	}
	if err := scm.VerifyChainIntegrity(records); err != nil {
		t.Errorf("VerifyChainIntegrity() failed after record5: %v", err)
	}
}
