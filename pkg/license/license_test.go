package license

import (
	"context"
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"reflect"
	"testing"
	"time"
)

func TestLicenseService_doGenerateLicense(t *testing.T) {
	log.InitTestLogger()

	type fields struct {
		secret string
	}
	type args struct {
		machineID    string
		startTime    time.Time
		numberOfDate int
		flag         string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			args: args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &LicenseService{}
			_, err := s.GenerateKey()
			if (err != nil) != tt.wantErr {
				t.Errorf("doGenerateLicense() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestLicenseService_VerifyLicense(t *testing.T) {
	log.InitTestLogger()

	type fields struct {
		secret string
	}
	type args struct {
		ctx      context.Context
		tokenStr string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    VerifyResult
		wantErr bool
	}{
		{
			name: "x",
			args: args{
				ctx:      context.Background(),
				tokenStr: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CeVwT-D-YyjQ-7J6NNXKrOCQ63zGPkZ824phY14s5v0",
			},
			want: VerifyResult{
				claims: &LicenseClaims{
					CreateTime: **********,
					ExpireTime: **********,
					Sub: LicenseClaimsSub{
						MachineID: "45184aabc6a0c247db9ae1e724998d4f5e61d796d66468a455008ba53d5aafdb",
						Flag:      FLAG_KEY,
					},
					LoadDeadline: **********,
					Features: []LicenseFeature{
						{
							FeatureID:     constants.OpProcedureNum.String(),
							FeatureName:   "存储过程转换次数",
							MaxUsageCount: 8,
						},
						{
							FeatureID:     constants.OpFunctionNum.String(),
							FeatureName:   "函数转换次数",
							MaxUsageCount: 8,
						},
						{
							FeatureID:     constants.OpPackageNum.String(),
							FeatureName:   "包转换次数",
							MaxUsageCount: 4,
						},
					},
				},
				isExpired: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &LicenseService{}
			got, err := s.VerifyLicense(tt.args.ctx, tt.args.tokenStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyLicense() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got.isExpired, tt.want.isExpired) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got.claims.ExpireTime, tt.want.claims.ExpireTime) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got.claims.CreateTime, tt.want.claims.CreateTime) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got.claims.LoadDeadline, tt.want.claims.LoadDeadline) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got.claims.Features, tt.want.claims.Features) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got.claims.Sub.Flag, tt.want.claims.Sub.Flag) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got.claims.Sub.MachineID, tt.want.claims.Sub.MachineID) {
				t.Errorf("VerifyLicense() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestLicenseService_GenerateLicenseFromKey(t *testing.T) {
	log.InitTestLogger()

	type args struct {
		opt GenerateOpt
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "1",
			args: args{opt: GenerateOpt{
				SignedToken:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CeVwT-D-YyjQ-7J6NNXKrOCQ63zGPkZ824phY14s5v0",
				NumberOfDate:   100,
				OpProcedureNum: 1,
				OpFunctionNum:  2,
				OpPackageNum:   3,
			}},
			want:    "",
			wantErr: false,
		},
		{
			name: "2",
			args: args{opt: GenerateOpt{
				SignedToken:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************.klsD3gs3OE4KCyyy-P5Z3W3OZZkFp67jRtEmtZK4ZU8",
				NumberOfDate:   100,
				OpProcedureNum: 1,
				OpFunctionNum:  2,
				OpPackageNum:   3,
			}},
			want:    "",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &LicenseService{}
			_, err := s.GenerateLicenseFromKey(tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateLicenseFromKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
