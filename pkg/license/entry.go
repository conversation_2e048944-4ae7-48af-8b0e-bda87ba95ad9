package license

import (
	"context"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

var expired bool
var expiredTime int64
var licenseManager *LicenseManager

func IsExpired() bool {
	return expired
}

func GetExpiredTime() int64 {
	return expiredTime
}

// SetExpired set expired to true/false, used by license interceptor
func SetExpired(isExpired bool) {
	expired = isExpired
}

func StartCheckLicenseWorker(ctx context.Context, license string) error {
	ls := NewLicenseService()
	verifyResult, verifyErr := ls.VerifyLicense(ctx, license)
	if verifyErr != nil {
		log.Error("verify license failed,", verifyErr)
		return verifyErr
	}

	expired = verifyResult.IsExpired()
	expiredTime = verifyResult.ExpireTime()

	if expired {
		log.Infof("license expired, expired time:%s", time.Unix(expiredTime, 0).Format(constants.TIME_FORMAT))
	} else {
		log.Infof("license is not expired, expired time:%s", time.Unix(expiredTime, 0).Format(constants.TIME_FORMAT))
	}

	var initErr error
	licenseManager, initErr = NewLicenseManager(license, Config{
		EncryptionKey: EncryptionKey,
		HMACKey:       HMACKey,
	})
	if initErr != nil {
		log.Errorf("init license manager failed. err:%v", initErr)
		return initErr
	}

	now := time.Now()
	next := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location()).Sub(now)
	timer := time.NewTimer(next)
	go func() {
		for {
			select {
			case <-ctx.Done():
				timer.Stop()
				return
			case <-timer.C:
				verifyResult, verifyErr = ls.VerifyLicense(ctx, license)
				if verifyErr != nil {
					if !strings.Contains(verifyErr.Error(), "Token is expired") {
						log.Errorf("verify license failed. set license to expired. err:%v", verifyErr)
					}
					expired = true
				} else {
					expired = verifyResult.IsExpired()
				}
				if expired {
					log.Infof("license expired.expired time:%s", time.Unix(expiredTime, 0).Format(constants.TIME_FORMAT))
				} else {
					log.Infof("license is not expired.expired time:%s", time.Unix(expiredTime, 0).Format(constants.TIME_FORMAT))
				}
				now = time.Now()
				next = time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location()).Sub(now)
				timer.Reset(next)
			}
		}
	}()
	return nil
}
