package license

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/denisbrodbeck/machineid"
)

// ErrVersionConflict indicates that optimistic locking detected a version conflict
var ErrVersionConflict = errors.New("version conflict detected")

// OptimisticLicenseExecutor implements optimistic locking for license feature execution
type OptimisticLicenseExecutor struct {
	maxRetries      int
	baseRetryDelay  time.Duration
	maxRetryDelay   time.Duration
	conflictBackoff func(attempt int) time.Duration
}

// NewOptimisticLicenseExecutor creates a new optimistic license executor with default configuration
func NewOptimisticLicenseExecutor() *OptimisticLicenseExecutor {
	return &OptimisticLicenseExecutor{
		maxRetries:     3,
		baseRetryDelay: 10 * time.Millisecond,
		maxRetryDelay:  100 * time.Millisecond,
		conflictBackoff: func(attempt int) time.Duration {
			// Exponential backoff with jitter
			delay := time.Duration(attempt*attempt) * 10 * time.Millisecond
			if delay > 100*time.Millisecond {
				delay = 100 * time.Millisecond
			}
			return delay
		},
	}
}

// NewOptimisticLicenseExecutorWithConfig creates a new optimistic license executor with custom configuration
func NewOptimisticLicenseExecutorWithConfig(maxRetries int, baseDelay, maxDelay time.Duration) *OptimisticLicenseExecutor {
	return &OptimisticLicenseExecutor{
		maxRetries:     maxRetries,
		baseRetryDelay: baseDelay,
		maxRetryDelay:  maxDelay,
		conflictBackoff: func(attempt int) time.Duration {
			delay := time.Duration(attempt*attempt) * baseDelay
			if delay > maxDelay {
				delay = maxDelay
			}
			return delay
		},
	}
}

// ExecuteFeature executes a feature with optimistic locking and automatic retry on version conflicts
func (ole *OptimisticLicenseExecutor) ExecuteFeature(ctx context.Context, lm *LicenseManager, featureID string) error {
	for attempt := 0; attempt < ole.maxRetries; attempt++ {
		if attempt > 0 {
			// Apply exponential backoff on retry
			delay := ole.conflictBackoff(attempt)
			log.Debugf("Version conflict retry %d for feature %s, waiting %v", attempt, featureID, delay)

			select {
			case <-time.After(delay):
				// Continue with retry
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		err := ole.attemptExecution(ctx, lm, featureID)
		if err == nil {
			if attempt > 0 {
				log.Infof("Feature %s execution succeeded on attempt %d", featureID, attempt+1)
			}
			return nil
		}

		// Check if it's a version conflict that we can retry
		if errors.Is(err, common.ErrVersionConflict) || errors.Is(err, ErrVersionConflict) {
			log.Debugf("Version conflict on attempt %d for feature %s: %v", attempt+1, featureID, err)
			continue
		}

		// Other errors are not retryable
		return fmt.Errorf("feature execution failed on attempt %d: %w", attempt+1, err)
	}

	return fmt.Errorf("max retries (%d) exceeded for feature %s due to version conflicts", ole.maxRetries, featureID)
}

// attemptExecution performs a single attempt at executing the feature with optimistic locking
func (ole *OptimisticLicenseExecutor) attemptExecution(ctx context.Context, lm *LicenseManager, featureID string) error {
	// Get current machine ID for auditing
	machineID, err := machineid.ProtectedID(constants.APP_NAME)
	if err != nil {
		machineID = "unknown"
	}

	// Execute in a single database transaction for atomicity
	return models.Transaction(ctx, func(transactionCtx context.Context) error {
		// 1. Read current feature state with version
		currentFeature, err := models.GetSystemInfoReaderWriter().GetFeatureLicenseWithVersion(
			transactionCtx, featureID, lm.license)
		if err != nil {
			return fmt.Errorf("failed to get feature with version: %w", err)
		}

		if currentFeature == nil {
			return fmt.Errorf("feature %s not found", featureID)
		}

		// 2. Validate signature to ensure data integrity
		expectedSignature := lm.calculateSignature(featureID, currentFeature.MaxUsageCount,
			currentFeature.CurrentUsageCount, lm.license)
		if currentFeature.VerificationSignature != expectedSignature {
			return fmt.Errorf("signature mismatch for feature %s", featureID)
		}

		// 3. Check usage limits
		if currentFeature.CurrentUsageCount >= currentFeature.MaxUsageCount {
			return fmt.Errorf("feature %s has reached maximum usage count (%d/%d)",
				featureID, currentFeature.CurrentUsageCount, currentFeature.MaxUsageCount)
		}

		// 4. Prepare new state
		newCount := currentFeature.CurrentUsageCount + 1
		newSignature := lm.calculateSignature(featureID, currentFeature.MaxUsageCount, newCount, lm.license)

		// 5. Prepare state chain record
		scm, exists := lm.featureChains[featureID]
		if !exists {
			return fmt.Errorf("no state chain manager for feature %s", featureID)
		}

		lastRecord, err := scm.GetLastRecord()
		if err != nil {
			return fmt.Errorf("failed to get last state chain record: %w", err)
		}

		newRecord := StateChainRecord{
			FeatureCode:       featureID,
			LicenseKey:        lm.license,
			CurrentUsageCount: newCount,
			PreviousHash:      "",
		}

		if lastRecord != nil {
			newRecord.PreviousHash = lastRecord.CurrentHash
		}

		newRecord.CurrentHash, err = CalculateHash(newRecord, newRecord.PreviousHash)
		if err != nil {
			return fmt.Errorf("failed to calculate hash: %w", err)
		}

		// 6. Update database with optimistic locking - this is atomic within the transaction
		updateFeature := &common.FeatureLicense{
			ID:                    currentFeature.ID,
			CurrentUsageCount:     newCount,
			VerificationSignature: newSignature,
			Version:               currentFeature.Version, // Used for optimistic lock check
			ModifiedBy:            machineID,
		}

		updateErr := models.GetSystemInfoReaderWriter().UpdateFeatureLicenseWithVersion(transactionCtx, updateFeature)
		if updateErr != nil {
			if errors.Is(updateErr, common.ErrVersionConflict) {
				return ErrVersionConflict // Will trigger retry
			}
			return fmt.Errorf("failed to update feature license: %w", updateErr)
		}

		// 7. Update state chain after successful database update
		// Note: This happens within the transaction, so if state chain fails,
		// the database update will be rolled back
		if err := scm.AppendRecord(newRecord); err != nil {
			return fmt.Errorf("state chain update failed: %w", err)
		}

		log.Debugf("Successfully executed feature %s: %d -> %d (version %d -> %d)",
			featureID, currentFeature.CurrentUsageCount, newCount,
			currentFeature.Version, updateFeature.Version)

		return nil
	})
}

// ValidateRequestNum validates if the requested feature usage is within limits using optimistic locking
func (ole *OptimisticLicenseExecutor) ValidateRequestNum(ctx context.Context, lm *LicenseManager, req OperationRequest) error {
	// This is a read-only operation, so we don't need optimistic locking
	// But we use the version-aware method for consistency
	features, err := lm.ListFeatures(ctx, lm.license)
	if err != nil {
		return fmt.Errorf("failed to list features: %w", err)
	}

	// Define feature types to check
	featureTypes := map[string]uint{
		constants.OpProcedureNum.String(): req.OpProcedureNum,
		constants.OpFunctionNum.String():  req.OpFunctionNum,
		constants.OpTriggerNum.String():   req.OpTriggerNum,
		constants.OpPackageNum.String():   req.OpPackageNum,
	}

	// Validate each feature type
	for featureType, requestedNum := range featureTypes {
		if requestedNum == 0 {
			continue
		}

		var foundFeature *common.FeatureLicense
		for _, feature := range features {
			if feature.FeatureCode == featureType {
				foundFeature = &feature
				break
			}
		}

		if foundFeature == nil {
			return fmt.Errorf("no license found for feature type: %s", featureType)
		}

		// Verify signature
		expectedSignature := lm.calculateSignature(foundFeature.FeatureCode, foundFeature.MaxUsageCount,
			foundFeature.CurrentUsageCount, lm.license)
		if foundFeature.VerificationSignature != expectedSignature {
			return fmt.Errorf("signature mismatch for feature %s", foundFeature.FeatureCode)
		}

		// Check usage limits
		newCount := foundFeature.CurrentUsageCount + requestedNum
		if newCount > foundFeature.MaxUsageCount {
			return fmt.Errorf("requested %s count (%d) would exceed maximum allowed (%d) for feature %s",
				featureType, newCount, foundFeature.MaxUsageCount, foundFeature.FeatureCode)
		}
	}

	return nil
}
