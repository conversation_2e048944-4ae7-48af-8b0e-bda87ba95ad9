package license

import (
	"sync"
	"testing"
)

func TestLicenseManager_generateChainFilePath(t *testing.T) {
	type fields struct {
		license       string
		featureChains map[string]*StateChainManager
		mu            sync.RWMutex
		conf          Config
	}
	type args struct {
		feature LicenseFeature
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "1",
			fields: fields{
				license: "xxa",
			},
			args: args{feature: LicenseFeature{FeatureID: "ID_1"}},
			want: "/Users/<USER>/.tms/73223b6e4b/state_chain_ID_1.dat",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lm := &LicenseManager{
				license:       tt.fields.license,
				featureChains: tt.fields.featureChains,
				mu:            tt.fields.mu,
				conf:          tt.fields.conf,
			}
			if got := lm.generateChainFilePath(tt.args.feature); got != tt.want {
				t.Errorf("generateChainFilePath() = %v, want %v", got, tt.want)
			}
		})
	}
}
