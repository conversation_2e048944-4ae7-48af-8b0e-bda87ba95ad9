package license

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOptimisticLicenseExecutor_SingleExecution(t *testing.T) {
	log.InitTestLogger()

	// Create test license manager
	lm, cleanup := createTestLicenseManager(t)
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	// Test single execution
	err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
	assert.NoError(t, err, "Single execution should succeed")

	// Verify the count was incremented
	feature, err := models.GetSystemInfoReaderWriter().GetFeatureLicenseWithVersion(
		ctx, constants.OpProcedureNum.String(), lm.license)
	require.NoError(t, err)
	assert.Equal(t, uint(1), feature.CurrentUsageCount, "Usage count should be incremented to 1")
	assert.Equal(t, int64(2), feature.Version, "Version should be incremented to 2")
}

func TestOptimisticLicenseExecutor_ConcurrentExecution(t *testing.T) {
	log.InitTestLogger()

	// Create test license manager
	lm, cleanup := createTestLicenseManager(t)
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	// Test concurrent execution
	const numGoroutines = 10
	var wg sync.WaitGroup
	var successCount int32
	var mu sync.Mutex
	errors := make([]error, 0)

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())

			mu.Lock()
			if err != nil {
				errors = append(errors, fmt.Errorf("goroutine %d: %w", id, err))
			} else {
				successCount++
			}
			mu.Unlock()
		}(i)
	}

	wg.Wait()

	// All executions should succeed (may require retries due to conflicts)
	assert.Empty(t, errors, "All concurrent executions should succeed")
	assert.Equal(t, int32(numGoroutines), successCount, "All goroutines should succeed")

	// Verify final count
	feature, err := models.GetSystemInfoReaderWriter().GetFeatureLicenseWithVersion(
		ctx, constants.OpProcedureNum.String(), lm.license)
	require.NoError(t, err)
	assert.Equal(t, uint(numGoroutines), feature.CurrentUsageCount,
		"Final usage count should equal number of executions")
}

func TestOptimisticLicenseExecutor_UsageLimitExceeded(t *testing.T) {
	log.InitTestLogger()

	// Create test license manager with low limits
	lm, cleanup := createTestLicenseManagerWithLimits(t, 2, 2, 2)
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	// Execute up to the limit
	for i := 0; i < 2; i++ {
		err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
		assert.NoError(t, err, "Execution within limit should succeed")
	}

	// Next execution should fail
	err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
	assert.Error(t, err, "Execution beyond limit should fail")
	assert.Contains(t, err.Error(), "maximum usage count", "Error should mention usage limit")
}

func TestOptimisticLicenseExecutor_VersionConflictRetry(t *testing.T) {
	log.InitTestLogger()

	// Create test license manager
	lm, cleanup := createTestLicenseManager(t)
	defer cleanup()

	// Create executor with more retry attempts for this test
	executor := NewOptimisticLicenseExecutorWithConfig(5, 1*time.Millisecond, 10*time.Millisecond)
	ctx := context.Background()

	// Simulate version conflicts by running highly concurrent operations
	const numGoroutines = 20
	var wg sync.WaitGroup
	successes := make([]bool, numGoroutines)
	errors := make([]error, numGoroutines)

	// Launch all goroutines simultaneously to maximize conflict probability
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
			successes[id] = (err == nil)
			errors[id] = err
		}(i)
	}

	wg.Wait()

	// Count successes and failures
	successCount := 0
	for i := 0; i < numGoroutines; i++ {
		if successes[i] {
			successCount++
		} else {
			t.Logf("Goroutine %d failed: %v", i, errors[i])
		}
	}

	// Most should succeed (allowing for some to fail due to retry exhaustion)
	assert.GreaterOrEqual(t, successCount, numGoroutines-2,
		"Most concurrent executions should succeed with retries")

	// Verify final count matches successful executions
	feature, err := models.GetSystemInfoReaderWriter().GetFeatureLicenseWithVersion(
		ctx, constants.OpProcedureNum.String(), lm.license)
	require.NoError(t, err)
	assert.Equal(t, uint(successCount), feature.CurrentUsageCount,
		"Final count should match successful executions")
}

func TestOptimisticLicenseExecutor_ValidationRequest(t *testing.T) {
	log.InitTestLogger()

	// Create test license manager with specific limits
	lm, cleanup := createTestLicenseManagerWithLimits(t, 5, 3, 2)
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	// Test validation within limits
	req := OperationRequest{
		OpProcedureNum: 3,
		OpFunctionNum:  2,
		OpPackageNum:   1,
	}

	err := executor.ValidateRequestNum(ctx, lm, req)
	assert.NoError(t, err, "Validation within limits should succeed")

	// Test validation beyond limits
	req = OperationRequest{
		OpProcedureNum: 6, // Exceeds limit of 5
		OpFunctionNum:  2,
		OpPackageNum:   1,
	}

	err = executor.ValidateRequestNum(ctx, lm, req)
	assert.Error(t, err, "Validation beyond limits should fail")
	assert.Contains(t, err.Error(), "exceed", "Error should mention exceeding limits")
}

func TestOptimisticLicenseExecutor_StateChainConsistency(t *testing.T) {
	log.InitTestLogger()

	// Create test license manager
	lm, cleanup := createTestLicenseManager(t)
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	// Execute several features
	const numExecutions = 5
	for i := 0; i < numExecutions; i++ {
		err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
		require.NoError(t, err, "Execution %d should succeed", i+1)
	}

	// Verify database and state chain consistency
	feature, err := models.GetSystemInfoReaderWriter().GetFeatureLicenseWithVersion(
		ctx, constants.OpProcedureNum.String(), lm.license)
	require.NoError(t, err)

	scm := lm.featureChains[constants.OpProcedureNum.String()]
	require.NotNil(t, scm, "State chain manager should exist")

	lastRecord, err := scm.GetLastRecord()
	require.NoError(t, err, "Should be able to get last state chain record")
	require.NotNil(t, lastRecord, "Last record should exist")

	// Verify consistency
	assert.Equal(t, feature.CurrentUsageCount, lastRecord.CurrentUsageCount,
		"Database and state chain counts should match")
	assert.Equal(t, uint(numExecutions), feature.CurrentUsageCount,
		"Count should match number of executions")

	// Verify state chain integrity
	records, err := scm.LoadChain()
	require.NoError(t, err)

	err = scm.VerifyChainIntegrity(records)
	assert.NoError(t, err, "State chain should maintain integrity")
}

func BenchmarkOptimisticLicenseExecutor_SingleThread(b *testing.B) {
	log.InitTestLogger()

	// Create test license manager with high limits
	lm, cleanup := createTestLicenseManagerWithLimits(b, uint(b.N+100), uint(b.N+100), uint(b.N+100))
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
		if err != nil {
			b.Fatalf("Execution failed: %v", err)
		}
	}
}

func BenchmarkOptimisticLicenseExecutor_Concurrent(b *testing.B) {
	log.InitTestLogger()

	// Create test license manager with high limits
	lm, cleanup := createTestLicenseManagerWithLimits(b, uint(b.N+100), uint(b.N+100), uint(b.N+100))
	defer cleanup()

	executor := NewOptimisticLicenseExecutor()
	ctx := context.Background()

	b.ResetTimer()

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			err := executor.ExecuteFeature(ctx, lm, constants.OpProcedureNum.String())
			if err != nil {
				b.Errorf("Execution failed: %v", err)
			}
		}
	})
}

// Helper functions for test setup

func createTestLicenseManager(t testing.TB) (*LicenseManager, func()) {
	return createTestLicenseManagerWithLimits(t, 100, 100, 100)
}

func createTestLicenseManagerWithLimits(t testing.TB, procLimit, funcLimit, pkgLimit uint) (*LicenseManager, func()) {
	// This is a simplified test setup - in real tests you would need to:
	// 1. Set up a test database
	// 2. Create test license with proper JWT
	// 3. Initialize the license manager with test data

	// For now, create a mock license manager structure
	lm := &LicenseManager{
		license:            "test-license-key",
		featureChains:      make(map[string]*StateChainManager),
		optimisticExecutor: NewOptimisticLicenseExecutor(),
		conf: Config{
			EncryptionKey: EncryptionKey,
			HMACKey:       HMACKey,
		},
	}

	// Set up test state chain managers (simplified)
	// In real implementation, these would be properly initialized

	cleanup := func() {
		// Clean up test resources
	}

	return lm, cleanup
}

// Example of testing with real database (commented out as it requires full setup)
/*
func TestOptimisticLicenseExecutor_WithRealDatabase(t *testing.T) {
	// Set up test database
	db := setupTestDatabase(t)
	defer db.Close()

	// Initialize models with test DB
	models.InitializeWithDB(db)

	// Create test license
	testLicense := createTestJWTLicense(t)

	// Create license manager
	lm, err := NewLicenseManager(testLicense, Config{
		EncryptionKey: "test-encryption-key",
		HMACKey:       "test-hmac-key",
	})
	require.NoError(t, err)

	// Run tests...
}
*/
