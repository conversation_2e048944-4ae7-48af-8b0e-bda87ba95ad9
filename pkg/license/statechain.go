package license

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/binary"
	"encoding/gob"
	"encoding/hex"
	"errors"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// StateChainRecord represents a record in the state chain.
type StateChainRecord struct {
	FeatureCode       string
	LicenseKey        string
	CurrentUsageCount uint
	PreviousHash      string
	CurrentHash       string
}

// Config holds the base state chain configuration (keys).
type Config struct {
	EncryptionKey string `yaml:"encryption_key"`
	HMACKey       string `yaml:"hmac_key"`
}

// StateChainManager manages the state chain file for a specific feature.
type StateChainManager struct {
	filePath      string
	encryptionKey []byte
	hmacKey       []byte
	mu            sync.Mutex // Mutex for state chain file operations
}

// NewStateChainManager creates a new StateChainManager for a given feature.
func NewStateChainManager(filePath string, config Config) (*StateChainManager, error) {
	// 获取文件所在的目录并创建
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	keyStr := strings.ReplaceAll(config.EncryptionKey, "-", "")

	encKey, err := hex.DecodeString(keyStr)
	if err != nil {
		return nil, fmt.Errorf("failed to decode encryption secret: %w", err)
	}
	if len(encKey) != 32 {
		return nil, fmt.Errorf("encryption secret must be 32 bytes")
	}
	return &StateChainManager{
		filePath:      filePath,
		encryptionKey: encKey,
		hmacKey:       []byte(config.HMACKey),
		mu:            sync.Mutex{},
	}, nil
}

// CalculateHash calculates the hash of a state chain record.
func CalculateHash(record StateChainRecord, previousHash string) (string, error) {
	data := fmt.Sprintf("%s:%s:%d:%s", record.FeatureCode, record.LicenseKey, record.CurrentUsageCount, previousHash)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:]), nil
}

// AppendRecord appends a new record to the state chain file with atomic write operation.
// It uses a temporary file and atomic rename to ensure consistency.
func (scm *StateChainManager) AppendRecord(record StateChainRecord) error {
	scm.mu.Lock()
	defer scm.mu.Unlock()

	// Encode the record
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	if err := enc.Encode(record); err != nil {
		return fmt.Errorf("failed to encode state chain record: %w", err)
	}

	// Encrypt the encoded record
	ciphertext, err := encrypt(buf.Bytes(), scm.encryptionKey)
	if err != nil {
		return fmt.Errorf("failed to encrypt state chain record: %w", err)
	}

	// Calculate HMAC over the ciphertext
	h := hmac.New(sha256.New, scm.hmacKey)
	h.Write(ciphertext)
	hmacBytes := h.Sum(nil)

	// Prepare record data: len(ciphertext) | ciphertext | len(hmac) | hmac
	var recordData bytes.Buffer
	if err := binary.Write(&recordData, binary.BigEndian, uint32(len(ciphertext))); err != nil {
		return fmt.Errorf("failed to write ciphertext length: %w", err)
	}
	if _, err := recordData.Write(ciphertext); err != nil {
		return fmt.Errorf("failed to write ciphertext: %w", err)
	}
	if err := binary.Write(&recordData, binary.BigEndian, uint32(len(hmacBytes))); err != nil {
		return fmt.Errorf("failed to write HMAC length: %w", err)
	}
	if _, err := recordData.Write(hmacBytes); err != nil {
		return fmt.Errorf("failed to write HMAC: %w", err)
	}

	// Perform atomic write operation
	return scm.atomicAppendToFile(recordData.Bytes())
}

// atomicAppendToFile performs atomic append operation using temporary file and rename.
// This ensures that the file is never left in a corrupted state even if the process crashes.
// Enhanced with file locking for multi-process safety.
func (scm *StateChainManager) atomicAppendToFile(data []byte) error {
	// Create lock file for inter-process synchronization
	lockFile := scm.filePath + ".lock"
	lockFd, err := scm.acquireFileLock(lockFile)
	if err != nil {
		return fmt.Errorf("failed to acquire file lock: %w", err)
	}
	defer func() {
		scm.releaseFileLock(lockFd, lockFile)
	}()

	// Read existing content first
	var existingContent []byte
	if content, err := os.ReadFile(scm.filePath); err == nil {
		existingContent = content
	} else if !os.IsNotExist(err) {
		return fmt.Errorf("failed to read existing state chain file: %w", err)
	}

	// Create temporary file in the same directory
	tempFile := scm.filePath + ".tmp"
	file, err := os.OpenFile(tempFile, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return fmt.Errorf("failed to create temporary file: %w", err)
	}
	defer func() {
		file.Close()
		os.Remove(tempFile) // Clean up temp file on error
	}()

	// Write existing content first
	if len(existingContent) > 0 {
		if _, err := file.Write(existingContent); err != nil {
			return fmt.Errorf("failed to write existing content to temp file: %w", err)
		}
	}

	// Write new data
	if _, err := file.Write(data); err != nil {
		return fmt.Errorf("failed to write new data to temp file: %w", err)
	}

	// Sync to ensure data is written to disk
	if err := file.Sync(); err != nil {
		return fmt.Errorf("failed to sync temp file: %w", err)
	}

	// Close file before rename
	if err := file.Close(); err != nil {
		return fmt.Errorf("failed to close temp file: %w", err)
	}

	// Atomic rename
	if err := os.Rename(tempFile, scm.filePath); err != nil {
		return fmt.Errorf("failed to rename temp file to state chain file: %w", err)
	}

	return nil
}

// acquireFileLock acquires an exclusive file lock for inter-process synchronization
func (scm *StateChainManager) acquireFileLock(lockFile string) (*os.File, error) {
	// Try to acquire lock with timeout
	maxRetries := 100
	retryDelay := 10 * time.Millisecond

	for i := 0; i < maxRetries; i++ {
		lockFd, err := os.OpenFile(lockFile, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644)
		if err == nil {
			// Successfully created lock file
			// Write process ID for debugging
			fmt.Fprintf(lockFd, "%d", os.Getpid())
			return lockFd, nil
		}

		if os.IsExist(err) {
			// Lock file exists, wait and retry
			time.Sleep(retryDelay)
			continue
		}

		// Other error occurred
		return nil, fmt.Errorf("failed to create lock file: %w", err)
	}

	return nil, fmt.Errorf("failed to acquire file lock after %d retries", maxRetries)
}

// releaseFileLock releases the file lock
func (scm *StateChainManager) releaseFileLock(lockFd *os.File, lockFile string) {
	if lockFd != nil {
		lockFd.Close()
		os.Remove(lockFile)
	}
}

func (scm *StateChainManager) LoadChain() ([]StateChainRecord, error) {
	scm.mu.Lock()
	defer scm.mu.Unlock()

	content, err := os.ReadFile(scm.filePath)
	if err != nil {
		//if os.IsNotExist(err) {
		//	return nil, nil // File doesn't exist, return empty chain
		//}
		return nil, fmt.Errorf("failed to read state chain file")
	}

	var records []StateChainRecord
	reader := bytes.NewReader(content)

	for reader.Len() > 0 {
		// Read ciphertext length
		var ciphertextLen uint32
		if err := binary.Read(reader, binary.BigEndian, &ciphertextLen); err != nil {
			if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
				break
			}
			return nil, fmt.Errorf("failed to read ciphertext length: %w", err)
		}

		// Read ciphertext
		ciphertext := make([]byte, ciphertextLen)
		if _, err := io.ReadFull(reader, ciphertext); err != nil {
			return nil, fmt.Errorf("failed to read ciphertext: %w", err)
		}

		// Read HMAC length
		var hmacLen uint32
		if err := binary.Read(reader, binary.BigEndian, &hmacLen); err != nil {
			return nil, fmt.Errorf("failed to read HMAC length: %w", err)
		}

		// Read HMAC
		receivedHMAC := make([]byte, hmacLen)
		if _, err := io.ReadFull(reader, receivedHMAC); err != nil {
			return nil, fmt.Errorf("failed to read HMAC: %w", err)
		}

		// Verify HMAC
		h := hmac.New(sha256.New, scm.hmacKey)
		h.Write(ciphertext)
		expectedHMAC := h.Sum(nil)
		if !hmac.Equal(receivedHMAC, expectedHMAC) {
			return nil, fmt.Errorf("record HMAC verification failed")
		}

		// Decrypt
		plaintext, err := decrypt(ciphertext, scm.encryptionKey)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt state chain record: %w", err)
		}

		// Decode
		var record StateChainRecord
		dec := gob.NewDecoder(bytes.NewReader(plaintext))
		if err := dec.Decode(&record); err != nil {
			return nil, fmt.Errorf("failed to decode state chain record: %w", err)
		}
		records = append(records, record)
	}

	return records, nil
}

// verifyHMACInternal verifies the HMAC of the file content (internal use, assumes lock is held).
func (scm *StateChainManager) verifyHMACInternal(data []byte) bool {
	if len(data) < sha256.Size {
		log.Errorf("verifyHMACInternal: Data too short (%d bytes), expected at least %d\n", len(data), sha256.Size)
		return false
	}

	receivedHMAC := data[len(data)-sha256.Size:]
	content := data[:len(data)-sha256.Size]

	h := hmac.New(sha256.New, scm.hmacKey)
	h.Write(content)
	expectedHMAC := h.Sum(nil)

	return hmac.Equal(receivedHMAC, expectedHMAC)
}

// VerifyChainIntegrity checks the integrity of the loaded state chain.
func (scm *StateChainManager) VerifyChainIntegrity(records []StateChainRecord) error {
	if len(records) <= 1 {
		return nil
	}
	for i := 0; i < len(records); i++ {
		previousHash := ""
		if i > 0 {
			previousHash = records[i-1].CurrentHash
		}
		expectedHash, err := CalculateHash(records[i], previousHash)
		if err != nil {
			return fmt.Errorf("failed to calculate expected hash for record %d: %w", i, err)
		}
		if records[i].CurrentHash != expectedHash {
			return fmt.Errorf("state chain integrity check failed at record %d: hash mismatch", i)
		}
		if i > 0 && records[i].PreviousHash != records[i-1].CurrentHash {
			return fmt.Errorf("state chain integrity check failed at record %d: previous hash mismatch", i)
		}
	}
	return nil
}

// GetLastRecord returns the last record in the state chain.
func (scm *StateChainManager) GetLastRecord() (*StateChainRecord, error) {
	records, err := scm.LoadChain()
	if err != nil {
		return nil, err
	}
	if len(records) > 0 {
		return &records[len(records)-1], nil
	}
	return nil, nil
}

// Encryption and Decryption functions (util/crypto.go) - No changes needed
func encrypt(plaintext []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, aesGCM.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := aesGCM.Seal(nonce, nonce, plaintext, nil)
	return ciphertext, nil
}

func decrypt(ciphertext []byte, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := aesGCM.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, errors.New("ciphertext too short")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	plaintext, err := aesGCM.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}
