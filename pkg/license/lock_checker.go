package license

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
)

// LockFileStatus represents the status of a lock file
type LockFileStatus struct {
	FilePath  string
	PID       int
	IsRunning bool
	Error     error
}

// LockCheckResult contains the overall result of lock file checking
type LockCheckResult struct {
	LicenseHash   string
	LockDirectory string
	ActiveLocks   []LockFileStatus
	DeadLocks     []LockFileStatus
	ErrorLocks    []LockFileStatus
}

// CheckLockFiles checks all lock files for a given license and returns their status
func CheckLockFiles(license string) (*LockCheckResult, error) {
	// Generate lock directory path (same logic as generateChainFilePath)
	hash := sha256.Sum256([]byte(license))
	hashPrefix := hex.EncodeToString(hash[:])[:10]

	homeDir, err := os.UserHomeDir()
	if err != nil {
		homeDir = "."
	}
	lockDir := filepath.Join(homeDir, ".tms", hashPrefix)

	result := &LockCheckResult{
		LicenseHash:   hashPrefix,
		LockDirectory: lockDir,
		ActiveLocks:   []LockFileStatus{},
		DeadLocks:     []LockFileStatus{},
		ErrorLocks:    []LockFileStatus{},
	}

	// Check if lock directory exists
	if _, err := os.Stat(lockDir); os.IsNotExist(err) {
		return result, nil // No locks exist
	}

	// Find all .lock files in the directory
	lockFiles, err := filepath.Glob(filepath.Join(lockDir, "*.lock"))
	if err != nil {
		return result, fmt.Errorf("failed to scan lock directory: %w", err)
	}

	// Check each lock file
	for _, lockFile := range lockFiles {
		status := checkSingleLockFile(lockFile)

		if status.Error != nil {
			result.ErrorLocks = append(result.ErrorLocks, status)
		} else if status.IsRunning {
			result.ActiveLocks = append(result.ActiveLocks, status)
		} else {
			result.DeadLocks = append(result.DeadLocks, status)
		}
	}

	return result, nil
}

// checkSingleLockFile checks the status of a single lock file
func checkSingleLockFile(lockFilePath string) LockFileStatus {
	status := LockFileStatus{
		FilePath: lockFilePath,
	}

	// Read PID from lock file
	pidData, err := os.ReadFile(lockFilePath)
	if err != nil {
		status.Error = fmt.Errorf("failed to read lock file: %w", err)
		return status
	}

	pidStr := strings.TrimSpace(string(pidData))
	pid, err := strconv.Atoi(pidStr)
	if err != nil {
		status.Error = fmt.Errorf("invalid PID in lock file: %s", pidStr)
		return status
	}

	status.PID = pid

	// Check if process is running
	status.IsRunning = isProcessRunning(pid)

	return status
}

// isProcessRunning checks if a process with given PID is still running
func isProcessRunning(pid int) bool {
	// Try to send signal 0 to the process (doesn't actually send a signal, just checks if process exists)
	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}

	// On Unix systems, use kill(pid, 0) to check if process exists
	err = process.Signal(syscall.Signal(0))
	if err != nil {
		// Process doesn't exist or we don't have permission
		return false
	}

	return true
}

// PrintLockCheckResult prints a formatted report of the lock check result
func PrintLockCheckResult(result *LockCheckResult) {
	fmt.Println("License Lock Files Checker")
	fmt.Println("==========================")
	fmt.Printf("License Hash: %s\n", result.LicenseHash)
	fmt.Printf("Lock Directory: %s\n\n", result.LockDirectory)

	// Print active locks
	for _, lock := range result.ActiveLocks {
		fmt.Printf("[✓] ACTIVE: %s (PID: %d - running)\n",
			filepath.Base(lock.FilePath), lock.PID)
	}

	// Print dead locks
	for _, lock := range result.DeadLocks {
		fmt.Printf("[✗] DEAD:   %s (PID: %d - not running)\n",
			filepath.Base(lock.FilePath), lock.PID)
	}

	// Print error locks
	for _, lock := range result.ErrorLocks {
		fmt.Printf("[!] ERROR:  %s (%s)\n",
			filepath.Base(lock.FilePath), lock.Error.Error())
	}

	// Print cleanup commands for dead locks
	if len(result.DeadLocks) > 0 {
		fmt.Println()
		fmt.Println("Cleanup Commands:")
		for _, lock := range result.DeadLocks {
			fmt.Printf("[!] CLEAN:  rm \"%s\"\n", lock.FilePath)
		}
	}

	// Print summary
	fmt.Printf("\nSummary: %d active, %d dead, %d error lock file(s) found.\n",
		len(result.ActiveLocks), len(result.DeadLocks), len(result.ErrorLocks))

	if len(result.DeadLocks) > 0 {
		fmt.Println("Recommendation: Run the CLEAN commands to remove dead lock files.")
	} else if len(result.ActiveLocks) == 0 && len(result.ErrorLocks) == 0 {
		fmt.Println("No lock files found. License system is clear.")
	} else if len(result.ActiveLocks) > 0 {
		fmt.Println("Active lock files detected. License system is currently in use.")
	}
}

// ProcessCheckLockCommand processes the hidden -check-lock command
func ProcessCheckLockCommand(args []string) {
	// Parse config file path if provided
	configFile := "./config.toml" // default
	for i, arg := range args {
		if (arg == "-config" || arg == "--config") && i+1 < len(args) {
			configFile = args[i+1]
			break
		}
	}

	// Try to get license from config file
	license, err := getLicenseFromConfig(configFile)
	if err != nil {
		fmt.Printf("Error: Failed to get license from config file '%s': %v\n", configFile, err)
		fmt.Println("Please ensure the config file exists and contains a valid license.")
		os.Exit(1)
	}

	if strings.TrimSpace(license) == "" {
		fmt.Println("Error: License is empty in config file.")
		fmt.Println("Please configure a valid license in the config file.")
		os.Exit(1)
	}

	// Verify license first (skip verification to avoid log dependency issues)
	// In a production environment, you might want to do basic JWT parsing instead
	if !isValidJWTFormat(license) {
		fmt.Println("Error: License format appears to be invalid (not a valid JWT)")
		os.Exit(1)
	}

	// Check lock files
	result, err := CheckLockFiles(license)
	if err != nil {
		fmt.Printf("Error: Failed to check lock files: %v\n", err)
		os.Exit(1)
	}

	// Print results
	PrintLockCheckResult(result)
}

// isValidJWTFormat checks if the license string looks like a valid JWT
func isValidJWTFormat(license string) bool {
	parts := strings.Split(license, ".")
	return len(parts) == 3 && len(parts[0]) > 0 && len(parts[1]) > 0 && len(parts[2]) > 0
}

// getLicenseFromConfig reads license from config file
func getLicenseFromConfig(configFile string) (string, error) {
	// Try to read the config file and extract license
	// This is a simplified version - in production you might want to use the same
	// config parsing logic as the main application

	configData, err := os.ReadFile(configFile)
	if err != nil {
		return "", err
	}

	// Simple TOML parsing for license field
	lines := strings.Split(string(configData), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "license") && strings.Contains(line, "=") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				license := strings.TrimSpace(parts[1])
				// Remove quotes if present
				license = strings.Trim(license, `"'`)
				return license, nil
			}
		}
	}

	return "", fmt.Errorf("license not found in config file")
}
