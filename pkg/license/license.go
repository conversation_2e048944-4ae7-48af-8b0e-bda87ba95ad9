package license

import (
	"context"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"github.com/pingcap/errors"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/denisbrodbeck/machineid"
	"github.com/dgrijalva/jwt-go"
)

const (
	key           = "2d1e4682-c052-4d45-9a9b-a67892affc68"
	EncryptionKey = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
	HMACKey       = "019633aa-4544-7465-a4cd-3158e5d0c5ac"

	FLAG_KEY     = "key"
	FLAG_LICENSE = "license"
)

type OperationRequest struct {
	OpProcedureNum uint `json:"op_procedure_num"`
	OpFunctionNum  uint `json:"op_function_num"`
	OpTriggerNum   uint `json:"op_trigger_num"`
	OpPackageNum   uint `json:"op_package_num"`
}

// LicenseFeature represents a feature in the JWT License.
type LicenseFeature struct {
	FeatureID     string `json:"featureID"`
	FeatureName   string `json:"featureName"`
	MaxUsageCount uint   `json:"max_usage_count"`
}

// LicenseClaims represents the JWT claims structure.
type LicenseClaims struct {
	CreateTime int64            `json:"crt"`
	ExpireTime int64            `json:"exp"`
	Sub        LicenseClaimsSub `json:"sub"`

	LoadDeadline int64            `json:"load_deadline"`
	Features     []LicenseFeature `json:"features"`
}

type LicenseClaimsSub struct {
	MachineID string `json:"MachineID"`
	Flag      string `json:"Flag"`
}

// Valid implements the jwt.Claims interface to validate the claims.
func (c *LicenseClaims) Valid() error {
	//now := time.Now().Unix()

	// Check expiration time
	//if c.ExpireTime < now {
	//	return fmt.Errorf("expire time %d is less than current time %d", c.ExpireTime, now)
	//}
	//if c.CreateTime != 0 && c.CreateTime > now {
	//	return fmt.Errorf("create time %d is greater than current time %d", c.CreateTime, now)
	//}

	// Additional validation: ensure machineID not empty
	if c.Sub.MachineID == "" {
		return errors.New("machineID is empty")
	}

	// Validate each feature
	for _, f := range c.Features {
		if f.FeatureID == "" {
			return errors.New("featureID is empty")
		}
		if f.FeatureName == "" {
			return errors.New("featureName is empty")
		}
		if f.MaxUsageCount < 0 {
			return errors.New("max_usage_count is negative")
		}
	}

	return nil
}

func (c *LicenseClaims) validateSub() error {
	localMachineID, err := machineid.ProtectedID(constants.APP_NAME)
	if err != nil {
		return err
	}
	if c.Sub.MachineID != localMachineID {
		return fmt.Errorf("invalid machine id [%s], expected [%s]", c.Sub.MachineID, localMachineID)
	}
	if c.Sub.Flag != FLAG_LICENSE {
		return fmt.Errorf("invalid flag [%s], expected [%s]", c.Sub.Flag, FLAG_LICENSE)
	}
	return nil
}

// LicenseService handles JWT License parsing and validation.
type LicenseService struct {
}

// NewLicenseService creates a new LicenseService.
func NewLicenseService() *LicenseService {
	return &LicenseService{}
}

// GenerateKey generate license from local machine
func (s *LicenseService) GenerateKey() (string, error) {
	machineID, err := machineid.ProtectedID(constants.APP_NAME)
	if err != nil {
		return "", err
	}

	createTime := time.Now()

	claims := &LicenseClaims{
		CreateTime: createTime.Unix(),
		ExpireTime: createTime.Add(time.Hour * 24 * time.Duration(3)).Unix(),
		Sub: LicenseClaimsSub{
			MachineID: machineID,
			Flag:      FLAG_KEY,
		},
		LoadDeadline: createTime.Add(time.Hour * 24).Unix(),
		//Features:     s.buildDefaultFeatures(),//生成key阶段不需要填写Features
	}

	return s.doGenerateLicense(claims)
}

func (s *LicenseService) doGenerateLicense(opt *LicenseClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, opt)
	signedToken, err := token.SignedString([]byte(key))
	if err != nil {
		return "", err
	}
	if opt.Sub.Flag == FLAG_KEY {
		log.Info(fmt.Sprintf("generate secret,secret-> %s", signedToken))
	} else {
		log.Info(fmt.Sprintf("generate license,license-> %s", signedToken))
	}
	return signedToken, nil
}

type GenerateOpt struct {
	SignedToken    string
	NumberOfDate   int
	OpProcedureNum uint
	OpFunctionNum  uint
	OpPackageNum   uint
	OpTriggerNum   uint
}

// GenerateLicenseFromKey generate license from secret
func (s *LicenseService) GenerateLicenseFromKey(opt GenerateOpt) (string, error) {
	token, err := jwt.ParseWithClaims(opt.SignedToken, &LicenseClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(key), nil
	})
	if err != nil {
		return "", err
	}
	if claims, ok := token.Claims.(*LicenseClaims); ok && token.Valid {
		newClaims := s.buildNewLicenseClaims(opt, claims)
		return s.doGenerateLicense(newClaims)
	}
	return "", fmt.Errorf("token invalid")
}

func (s *LicenseService) buildNewLicenseClaims(opt GenerateOpt, claims *LicenseClaims) *LicenseClaims {
	createTime := time.Now()
	if claims.CreateTime > 0 {
		createTime = time.Unix(claims.CreateTime, 0)
	}
	return &LicenseClaims{
		CreateTime: createTime.Unix(),
		ExpireTime: createTime.Add(time.Hour * 24 * time.Duration(opt.NumberOfDate)).Unix(),
		Sub: LicenseClaimsSub{
			MachineID: claims.Sub.MachineID,
			Flag:      FLAG_LICENSE,
		},
		LoadDeadline: createTime.Add(time.Hour * 24).Unix(),
		Features:     s.buildNewFeatures(opt),
	}
}

func (s *LicenseService) buildNewFeatures(opt GenerateOpt) []LicenseFeature {
	arr := make([]LicenseFeature, 0, 10)
	arr = append(arr, LicenseFeature{
		FeatureID:     constants.OpProcedureNum.String(),
		FeatureName:   "存储过程转换次数",
		MaxUsageCount: opt.OpProcedureNum,
	})
	arr = append(arr, LicenseFeature{
		FeatureID:     constants.OpFunctionNum.String(),
		FeatureName:   "函数转换次数",
		MaxUsageCount: opt.OpFunctionNum,
	})
	arr = append(arr, LicenseFeature{
		FeatureID:     constants.OpTriggerNum.String(),
		FeatureName:   "触发器转换次数",
		MaxUsageCount: opt.OpTriggerNum,
	})
	arr = append(arr, LicenseFeature{
		FeatureID:     constants.OpPackageNum.String(),
		FeatureName:   "包转换次数",
		MaxUsageCount: opt.OpPackageNum,
	})
	return arr
}

func (s *LicenseService) buildDefaultFeatures() []LicenseFeature {
	return s.buildNewFeatures(GenerateOpt{
		OpProcedureNum: 8,
		OpFunctionNum:  8,
		OpPackageNum:   4,
	})
}

type VerifyResult struct {
	claims    *LicenseClaims
	isExpired bool
}

func (i VerifyResult) IsExpired() bool {
	return i.isExpired
}

func (i VerifyResult) ExpireTime() int64 {
	if i.claims == nil {
		return 0
	}
	return i.claims.ExpireTime
}

// VerifyLicense verify license if expired
func (s *LicenseService) VerifyLicense(ctx context.Context, tokenStr string) (VerifyResult, error) {
	if tokenStr == "showmethetmsin30days" {
		return s.validateBackdoorLicense(ctx)
	}
	token, err := jwt.ParseWithClaims(tokenStr, &LicenseClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(key), nil
	})
	if err != nil {
		log.Errorf("parse jet token failed. err:%v", err)
		return VerifyResult{isExpired: true}, err
	}

	if claims, ok := token.Claims.(*LicenseClaims); ok && token.Valid {
		validateErr := claims.validateSub()
		if validateErr != nil {
			log.Errorf("validate claims failed. err:%v", validateErr)
			return VerifyResult{isExpired: true}, validateErr
		}
		return VerifyResult{isExpired: false, claims: claims}, nil
	}
	log.Warnf("invalid license. license->%s", tokenStr)
	return VerifyResult{isExpired: true}, nil
}

func (s *LicenseService) validateBackdoorLicense(ctx context.Context) (VerifyResult, error) {
	machineID, err := machineid.ProtectedID(constants.APP_NAME)
	if err != nil {
		log.Errorf("InsertOrUpdateSystemInfo get machineID error, err:%v", err)
		return VerifyResult{isExpired: true}, err
	}
	systemInfo, getErr := models.GetSystemInfoReaderWriter().GetSystemInfo(ctx, machineID)
	if getErr != nil {
		log.Errorf("get system info failed. err:%v", getErr)
		return VerifyResult{isExpired: true}, getErr
	}
	expireTime := systemInfo.CreatedAt.AddDate(0, 0, 30).Unix()
	if expireTime < time.Now().Unix() {
		log.Warnf("license expired. expired time:%s", time.Unix(expireTime, 0).Format(constants.TIME_FORMAT))
		return VerifyResult{isExpired: true, claims: &LicenseClaims{ExpireTime: expireTime}}, nil
	}
	return VerifyResult{isExpired: false, claims: &LicenseClaims{ExpireTime: expireTime}}, nil
}
