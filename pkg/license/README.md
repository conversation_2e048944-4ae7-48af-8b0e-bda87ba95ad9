# TMS 功能使用限制 License 系统

一个强大、高性能的许可证管理系统，支持乐观锁、状态链完整性验证和多进程并发安全。

## 🎯 项目概述

TMS需要在原有功能的基础上，增加功能使用限制。通过配置文件中的 JWT 格式 License 控制功能的使用次数，并在 MySQL 数据库中记录和验证执行状态，同时使用状态链文件增强安全性，防止数据库和文件的回滚攻击。

### 核心能力

- **JWT基础许可证验证** 支持过期时间和机器ID验证
- **乐观锁机制** 应对高并发场景
- **状态链完整性** 防篡改的使用次数跟踪
- **多进程安全** 文件级锁定保护
- **原子操作** 确保数据一致性
- **隐藏诊断工具** 用于运维监控

## 📋 功能需求

### 1. 功能使用次数限制
- 指定功能只能执行有限次数（如 10 次），超过限制后不可再次使用
- 支持多个功能，每个功能（Entity）有独立的执行次数限制

### 2. License 配置与验证
通过配置文件读取 License，License 为 JWT 字符串，包含以下字段：
- `machineID`：机器唯一标识符
- `features`：功能列表，每个功能包含 featureID、featureName 和 max_usage_count（最大执行次数）
- `exp`：License 过期时间（Unix 时间戳）
- `load_deadline`：允许载入的截止时间（Unix 时间戳）

若 License 过期（exp 小于当前时间）或超过 load_deadline，程序启动时报错并退出。

### 3. 数据库存储与验证
- 成功解析 License 后，首次载入时，将功能 ID、功能名称和执行次数存入 MySQL 数据库的 feature_licenses 表
- 每次执行功能时：
  - 检查数据库中对应功能的 verification_signature 是否有效
  - 验证状态链文件，检查数据库状态与状态链最新记录是否一致
  - 更新 current_usage_count 和 verification_signature，并追加状态链记录
- 若 current_usage_count 超过 max_usage_count，执行失败并报错

### 4. 并发场景限制
- TMS进程内存在并发执行多个功能的场景，因此需要做并发控制
- 每个功能对应的一个状态链文件，写入时需要并发控制
- 不考虑进程之间并发问题

## 🏗️ 系统设计

### 核心组件架构

```mermaid
graph TD
    A[License Manager] --> B[License Service]
    A --> C[State Chain Manager]
    A --> D[Optimistic Executor]
    
    B --> E[JWT Verification]
    B --> F[Machine ID Validation]
    
    C --> G[Encrypted State Files]
    C --> H[File Lock Manager]
    
    D --> I[Database Operations]
    D --> J[Version Control]
    D --> K[Retry Logic]
    
    I --> L[(Feature Licenses DB)]
    G --> M[~/.tms/hash/state_*.dat]
    H --> N[*.lock files]
```

### 数据表结构

```sql
CREATE TABLE feature_licenses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    feature_code VARCHAR(100) NOT NULL COMMENT '功能唯一标识符',
    feature_name VARCHAR(100) NOT NULL COMMENT '功能名称',
    max_usage_count BIGINT UNSIGNED NOT NULL COMMENT '最大允许使用次数',
    current_usage_count BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前已使用次数',
    license_key VARCHAR(900) NOT NULL COMMENT '授权密钥（JWT 字符串）',
    verification_signature VARCHAR(100) NOT NULL COMMENT '验证签名，用于防篡改',
    
    -- 乐观锁字段
    version BIGINT NOT NULL DEFAULT 1 COMMENT '乐观锁版本号',
    last_modified DATETIME(3) NOT NULL COMMENT '最后修改时间',
    modified_by VARCHAR(100) NOT NULL COMMENT '修改者机器ID',
    
    -- 通用字段
    comment VARCHAR(1000) DEFAULT NULL COMMENT '备注内容',
    created_at DATETIME(3) DEFAULT NULL,
    updated_at DATETIME(3) DEFAULT NULL,
    deleted_at DATETIME(3) DEFAULT NULL,
    
    KEY idx_feature_licenses_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

#### 字段说明
- `feature_code`：功能的唯一标识符，与 License 中的 featureID 对应
- `feature_name`：功能的名称，中文描述
- `max_usage_count`：最大允许使用次数，从 License 中提取
- `current_usage_count`：当前已使用次数，初始化为 0
- `license_key`：JWT License 字符串
- `verification_signature`：基于 feature_code、max_usage_count、current_usage_count、license_key生成的 HMAC-SHA256 签名
- `version`：乐观锁版本号，每次更新递增
- `last_modified`：最后修改时间戳
- `modified_by`：执行修改的机器ID

### 状态链文件设计

#### 存储方式
- 状态链存储在本地文件（每个功能单独一个state_chain.dat），为每个功能记录一个加密的状态链
- 文件位置：`~/.tms/{license_hash}/state_chain_{feature_code}.dat`
- 每条状态链记录包含：
  - `feature_code`：功能标识符
  - `license_key`：License 密钥
  - `current_usage_count`：当前使用次数
  - `previous_hash`：前一条记录的哈希值
  - `current_hash`：当前记录的哈希值（基于所有字段和 previous_hash）
  - `nonce`：随机数，防止重放攻击
  - `logical_time`：逻辑时间戳
- 文件使用 AES-GCM 加密，附加 HMAC-SHA256 校验，确保内容不可篡改

#### 作用
- 记录每次功能执行的历史状态，形成不可逆的链条
- 程序启动和功能执行时，验证数据库状态与状态链最新记录一致，防止数据库或文件回滚

## 🔄 流程设计

### 数据流架构

```mermaid
sequenceDiagram
    participant Client
    participant LM as License Manager
    participant OE as Optimistic Executor
    participant DB as Database
    participant SC as State Chain
    
    Client->>LM: ExecuteFeature(feature_id)
    LM->>OE: Execute with retry logic
    
    loop Retry Loop (max 3 times)
        OE->>DB: GetFeatureLicenseWithVersion()
        OE->>OE: Validate usage limits
        OE->>DB: BEGIN TRANSACTION
        OE->>DB: UpdateFeatureLicenseWithVersion()
        OE->>SC: AppendRecord() [with file lock]
        alt Version Conflict
            OE->>DB: ROLLBACK
            OE->>OE: Exponential backoff
        else Success
            OE->>DB: COMMIT
            OE->>Client: Success
        end
    end
```

### 1. 程序启动流程

1. 从配置文件（config.yaml）读取 License（JWT 字符串）
2. 解析 JWT：
   - 验证签名（使用预配置的密钥）
   - 检查 exp 是否过期（exp < 当前时间）
   - 检查 load_deadline 是否超过当前时间
3. 若解析失败或 exp 过期，报错并退出程序
4. 若 load_deadline 过期，跳过后续步骤，程序继续运行（不插入新记录）
5. 从 JWT 中提取 license_key 和功能列表（featureID、featureName、max_usage_count）
6. 加载状态链文件，验证链的完整性：
   - 逐条检查每条记录的 current_hash 和 previous_hash 是否连续
   - 验证文件整体的 HMAC 签名
7. 将功能数据写入 feature_licenses 表：
   - 若记录已存在，验证数据库状态与状态链最新记录一致性
   - 若记录不存在，插入新记录并追加初始状态链记录

### 2. 功能执行流程

1. 接收功能调用请求，获取对应的 feature_code 和 license_key
2. 从数据库查询 feature_licenses 表中对应记录（包含版本号）
3. 加载状态链文件，验证链的完整性
4. 验证状态一致性：比较数据库中的 current_usage_count与状态链最新记录
5. 验证 verification_signature 有效性
6. 检查 current_usage_count 是否小于 max_usage_count
7. 执行功能
8. 原子更新状态：
   - 数据库：使用乐观锁更新 current_usage_count、verification_signature 和 version
   - 状态链：追加新记录到加密文件
9. 提交事务

## 🔧 核心实现细节

### 1. 乐观锁实现

```go
// 乐观锁执行伪代码
func (oe *OptimisticLicenseExecutor) ExecuteFeature(ctx context.Context, lm *LicenseManager, featureID string) error {
    for attempt := 1; attempt <= oe.maxRetries; attempt++ {
        err := oe.attemptExecution(ctx, lm, featureID)
        
        if err == nil {
            return nil // 成功
        }
        
        if errors.Is(err, ErrVersionConflict) {
            // 指数退避
            delay := oe.calculateBackoff(attempt)
            time.Sleep(delay)
            continue
        }
        
        return err // 不可重试错误
    }
    
    return fmt.Errorf("超过最大重试次数 (%d)", oe.maxRetries)
}
```

### 2. 状态链文件操作

```go
// 原子文件操作伪代码
func (scm *StateChainManager) AppendRecord(record StateChainRecord) error {
    scm.mu.Lock()
    defer scm.mu.Unlock()
    
    // 1. 获取进程间文件锁
    lockFile := scm.filePath + ".lock"
    lockFd, err := scm.acquireFileLock(lockFile)
    if err != nil {
        return fmt.Errorf("获取文件锁失败: %w", err)
    }
    defer scm.releaseFileLock(lockFd, lockFile)
    
    // 2. 读取现有内容
    existingContent, _ := os.ReadFile(scm.filePath)
    
    // 3. 准备新记录
    encryptedRecord, err := scm.encryptRecord(record)
    if err != nil {
        return err
    }
    
    // 4. 使用临时文件 + 重命名的原子写入
    tempFile := scm.filePath + ".tmp"
    if err := scm.writeToFile(tempFile, existingContent, encryptedRecord); err != nil {
        return err
    }
    
    // 5. 原子重命名
    return os.Rename(tempFile, scm.filePath)
}
```

### 3. 多进程文件锁

```go
// 文件锁获取伪代码
func (scm *StateChainManager) acquireFileLock(lockFile string) (*os.File, error) {
    maxRetries := 100
    retryDelay := 10 * time.Millisecond
    
    for i := 0; i < maxRetries; i++ {
        // 尝试创建排他锁文件
        lockFd, err := os.OpenFile(lockFile, os.O_CREATE|os.O_WRONLY|os.O_EXCL, 0644)
        if err == nil {
            // 写入进程ID用于调试
            fmt.Fprintf(lockFd, "%d", os.Getpid())
            return lockFd, nil
        }
        
        if os.IsExist(err) {
            time.Sleep(retryDelay)
            continue
        }
        
        return nil, err
    }
    
    return nil, fmt.Errorf("在 %d 次重试后获取锁失败", maxRetries)
}
```

## 📊 性能特征

### 并发性能（基于测试结果）

| 并发Goroutine数 | 平均操作时间 | 争用因子 | 吞吐量 | 成功率 |
|----------------|-------------|---------|--------|-------|
| 1 | 7.1ms | - | 141.7 ops/sec | 100% |
| 8 | 56.8ms | 2.56x | 137.7 ops/sec | 100% |
| 16 | 374.1ms | 1.26x | 42.4 ops/sec | 100% |

### 关键性能洞察

- **最佳点**：8个并发goroutine在可接受延迟下提供良好性能
- **扩展性限制**：超过16个并发操作性能显著下降
- **数据安全**：所有并发级别都保持100%成功率
- **争用点**：文件锁成为高并发下的主要瓶颈

## 🛠️ 配置说明

### 默认配置

```go
// 默认乐观执行器设置
executor := NewOptimisticLicenseExecutor()
// MaxRetries: 3
// BaseRetryDelay: 10ms
// MaxRetryDelay: 100ms
```

### 高并发场景自定义配置

```go
// 针对8+并发goroutine优化
executor := NewOptimisticLicenseExecutorWithConfig(
    maxRetries: 5,                    // 更多重试应对争用
    baseDelay:  5*time.Millisecond,   // 更快重试
    maxDelay:   50*time.Millisecond,  // 更低最大延迟
)
```

## 🔍 诊断工具

### 隐藏命令

系统包含用于运维监控的隐藏诊断命令：

```bash
# 检查锁文件状态（隐藏命令）
./tms -check-lock -config ./config.toml

# 输出示例：
# License Lock Files Checker
# ==========================
# License Hash: dcc1cf3922
# Lock Directory: /Users/<USER>/.tms/dcc1cf3922
# 
# [✓] ACTIVE: state_chain_op_procedure_num_def456.dat.lock (PID: 12345 - running)
# [✗] DEAD:   state_chain_op_function_num_def456.dat.lock (PID: 67890 - not running)
# [!] CLEAN:  rm "/Users/<USER>/.tms/dcc1cf3922/state_chain_op_function_num_def456.dat.lock"
```

## 🚀 使用示例

### 基础功能执行

```go
// 初始化许可证管理器（每个license+config的单例）
lm, err := license.NewLicenseManager(licenseKey, license.Config{
    EncryptionKey: license.EncryptionKey,
    HMACKey:       license.HMACKey,
})
if err != nil {
    return fmt.Errorf("许可证初始化失败: %w", err)
}

// 执行功能，自动重试
ctx := context.Background()
err = lm.ExecuteFeature(ctx, "op_procedure_num")
if err != nil {
    return fmt.Errorf("功能执行失败: %w", err)
}
```

### 批量验证

```go
// 同时验证多个功能
req := license.OperationRequest{
    OpProcedureNum: 5,
    OpFunctionNum:  3,
    OpPackageNum:   2,
}

err := lm.ValidateRequestNum(ctx, req)
if err != nil {
    return fmt.Errorf("批量验证失败: %w", err)
}
```

### 许可证状态监控

```go
// 列出所有有效功能
features, err := lm.ListFeatures(ctx, licenseKey)
if err != nil {
    return fmt.Errorf("获取功能列表失败: %w", err)
}

for _, feature := range features {
    fmt.Printf("功能: %s, 使用情况: %d/%d\n", 
        feature.FeatureCode, 
        feature.CurrentUsageCount, 
        feature.MaxUsageCount)
}
```

## 🔒 安全特性

### 多层安全保护

1. **JWT认证**：行业标准令牌验证
2. **机器ID绑定**：许可证绑定到特定硬件
3. **HMAC签名**：防篡改的使用次数计数
4. **加密状态链**：AES-256加密的使用历史
5. **版本控制**：防止重放和回滚攻击
6. **原子操作**：所有更新都符合ACID特性

### 审计跟踪

```go
// 每个操作都记录：
// - 执行操作的机器ID
// - 修改时间戳
// - 排序用的版本号
// - 完整性检查的HMAC签名
```

## 📝 迁移指南

### 数据库架构迁移

```sql
-- 添加乐观锁字段
ALTER TABLE feature_licenses ADD COLUMN version BIGINT NOT NULL DEFAULT 1;
ALTER TABLE feature_licenses ADD COLUMN last_modified DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);
ALTER TABLE feature_licenses ADD COLUMN modified_by VARCHAR(100) NOT NULL DEFAULT '';

-- 创建性能索引
CREATE INDEX idx_feature_licenses_version ON feature_licenses(feature_code, license_key, version);
```

### 代码迁移

```go
// 旧代码（仍然有效，内部使用乐观锁）
err := licenseManager.ExecuteFeature(ctx, featureID)

// 新代码（显式使用乐观执行器）
executor := license.NewOptimisticLicenseExecutor()
err := executor.ExecuteFeature(ctx, licenseManager, featureID)
```

## 🎯 最佳实践

### 高并发应用

1. **限制并发操作**：保持8个或更少的并发许可证操作
2. **实现熔断器**：优雅处理重试耗尽情况
3. **监控锁文件**：定期使用 `-check-lock` 诊断工具
4. **缓存功能列表**：通过缓存减少数据库查询
5. **批量操作**：尽可能合并多个功能检查

### 生产部署

1. **监控关键指标**：
   - 成功率（应 >95%）
   - 平均重试次数（应 <2）
   - 版本冲突率（应 <10%）
   - 锁获取时间

2. **设置告警**：
   - 高重试率
   - 需要清理锁文件
   - 许可证过期警告

3. **定期维护**：
   - 清理孤儿锁文件
   - 监控状态链文件大小
   - 验证数据库一致性

## 🆘 故障排除

### 常见问题及解决方案

| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 版本冲突 | 高重试率，性能慢 | 减少并发，优化事务时间 |
| 锁文件问题 | "获取文件锁失败"错误 | 使用 `-check-lock` 命令清理死锁 |
| 许可证验证 | "机器ID不匹配"错误 | 验证许可证是否为正确机器生成 |
| 状态链损坏 | "链完整性检查失败" | 从备份恢复或重新初始化 |

### 调试日志

启用调试日志来监控乐观锁行为：

```go
log.SetLevel(log.DebugLevel)
// 将显示：
// - 版本冲突和重试
// - 锁获取时间
// - 状态链操作
// - 事务边界
```

## 📚 API参考

### 核心接口

```go
type LicenseManager interface {
    ExecuteFeature(ctx context.Context, featureID string) error
    ValidateRequestNum(ctx context.Context, req OperationRequest) error
    ListFeatures(ctx context.Context, license string) ([]common.FeatureLicense, error)
}

type OptimisticLicenseExecutor interface {
    ExecuteFeature(ctx context.Context, lm *LicenseManager, featureID string) error
    ValidateRequestNum(ctx context.Context, lm *LicenseManager, req OperationRequest) error
}

type StateChainManager interface {
    AppendRecord(record StateChainRecord) error
    LoadChain() ([]StateChainRecord, error)
    VerifyChainIntegrity(records []StateChainRecord) error
}
```

## 🧪 测试

系统包含完整的单元测试和集成测试，验证：

- JWT许可证解析和验证
- 乐观锁并发安全性
- 状态链完整性检查
- 文件锁机制
- 数据库一致性
- 错误处理和恢复

运行测试：

```bash
# 运行所有测试
go test ./pkg/license/...

# 运行并发测试
go test -v ./pkg/license/... -run TestConcurrency

# 运行集成测试
go test -v ./pkg/license/... -run TestIntegration
```

---

本README提供了TMS License系统架构、实现和运维的全面指南。详细的测试结果和性能分析请参阅 [TEST_REPORT.md](./TEST_REPORT.md)。