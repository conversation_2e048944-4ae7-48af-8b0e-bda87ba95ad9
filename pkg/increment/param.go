package increment

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
)

func BuildIncrementParams(ctx context.Context, channelId, taskId, templateId int) (*structs.IncrementParam, error) {
	var ()
	taskParamMap := make(map[string]string)

	// task params merge
	defaultTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, templateId)
	if getParamErr != nil {
		return nil, fmt.Errorf("get task params detail info failed: %v", getParamErr)
	}

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, getParamsErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           channelId,
		TaskID:              taskId,
		TaskparamTemplateID: templateId,
	})
	if getParamsErr != nil {
		return nil, fmt.Errorf("get task params info failed: %v", getParamsErr)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}

	//if value, ok := taskParamMap[constants.ParamsIncrementSourcePath]; ok {
	//	sourcePath = value
	//} else {
	//	sourcePath = "/home/<USER>/dsg"
	//}

	return &structs.IncrementParam{}, nil
}
