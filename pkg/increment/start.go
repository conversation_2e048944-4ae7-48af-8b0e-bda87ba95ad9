package increment

import (
	"context"
	"path"

	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/pingcap/errors"
)

func StartIncrementTask(ctx context.Context, channelId, taskId int) error {
	//TODO
	log.Error("StartIncrementTask not implemented")
	return errors.New("StartIncrementTask not implemented")
}

type PathHelper struct {
}

func (i PathHelper) SourcePrimaryPath(homePath, subSystem, taskName string) string {
	return path.Join(homePath, subSystem, taskName, "ds")
}

func (i PathHelper) TargetPrimaryPath(homePath, subSystem, taskName string) string {
	return path.Join(homePath, subSystem, taskName, "dt")
}

/*
*
SELECT FORCE_LOGGING,SUPPLEMENTAL_LOG_DATA_MIN, SUPPLEMENTAL_LOG_DATA_PK, SUPPLEMENTAL_LOG_DATA_UI, SUPPLEMENTAL_LOG_DATA_FK, SUPPLEMENTAL_LOG_DATA_ALL
FROM V$DATABASE;
*/
type SupplementalLogVariables struct {
	ForceLogging           bool
	SupplementalLogDataMin bool
	SupplementalLogDataPk  bool
	SupplementalLogDataUi  bool
	SupplementalLogDataFk  bool
	SupplementalLogDataAll bool
}

func (i SupplementalLogVariables) IsSupplementalLogValid() bool {
	return i.SupplementalLogDataAll || (i.SupplementalLogDataPk && i.SupplementalLogDataUi)
}

type DBPSView struct {
	DBPS_XKCCCF bool
	DBPS_XKCCLE bool
	DBPS_XKCCLH bool
	DBPS_XKCCCP bool
	DBPS_XKCCDI bool
	DBPS_XKCRMF bool
	DBPS_XKTUXE bool
	DBPS_XLE    bool
	DBPS_XBH    bool
}

func (i DBPSView) ValidateMissingView() ([]string, bool) {
	missingView := make([]string, 0)
	var result bool
	if !i.DBPS_XKCCCF {
		missingView = append(missingView, "DBPS_XKCCCF")
		result = true
	}
	if !i.DBPS_XKCCLE {
		missingView = append(missingView, "DBPS_XKCCLE")
		result = true
	}
	if !i.DBPS_XKCCLH {
		missingView = append(missingView, "DBPS_XKCCLH")
		result = true
	}
	if !i.DBPS_XKCCCP {
		missingView = append(missingView, "DBPS_XKCCCP")
		result = true
	}
	if !i.DBPS_XKCCDI {
		missingView = append(missingView, "DBPS_XKCCDI")
		result = true
	}
	if !i.DBPS_XKCRMF {
		missingView = append(missingView, "DBPS_XKCRMF")
		result = true
	}
	if !i.DBPS_XKTUXE {
		missingView = append(missingView, "DBPS_XKTUXE")
		result = true
	}
	if !i.DBPS_XLE {
		missingView = append(missingView, "DBPS_XLE")
		result = true
	}
	if !i.DBPS_XBH {
		missingView = append(missingView, "DBPS_XBH")
		result = true
	}
	return missingView, result
}

/*
*
select a.owner,a.table_name,a.log_group_name,a.log_group_type from ALL_LOG_GROUPS a;
*/
type OracleLogGroup struct {
	SchemaName           string `json:"schema_name"`
	TableName            string `json:"table_name"`
	HasAllColumnLogging  bool   `json:"has_all_column_logging"`
	HasPrimaryKeyLogging bool   `json:"has_primary_key_logging"`
	HasUniqueKeyLogging  bool   `json:"has_unique_key_logging"`
	HasForeignKeyLogging bool   `json:"has_foreign_key_logging"`
}

func (i OracleLogGroup) IsSupplementalLogValid() bool {
	return i.HasAllColumnLogging || (i.HasPrimaryKeyLogging && i.HasUniqueKeyLogging)
}
