package increment

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/pkg/dsg"
	migrationpkg "gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/increment"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/google/uuid"
	"github.com/samber/lo"
)

type DsgTaskHelper struct {
	UserId    int
	ChannelId int
	TaskId    int
	Adaptor   dsg.Adaptor

	SourcePath string
	SourceHost *datasource.OpMachineConf
	TargetPath string
	TargetHost *datasource.OpMachineConf

	err       error
	shortUUID string

	physicalSubSystemId       int
	defineConfig              string
	param                     dsg.SaveTaskRequestBodyParams
	migrationInstallation     dsg.SaveTaskRequestBodyMigrationInstallation
	sourceDatabaseInformation dsg.SaveTaskRequestBodySourceDatabaseInformation
	targetDatabaseInformation dsg.SaveTaskRequestBodyTargetDatabaseInformation
}

func (i *DsgTaskHelper) GetRequestBody() (*dsg.SaveTaskRequestBody, error) {
	if i.err != nil {
		return nil, i.err
	}
	return &dsg.SaveTaskRequestBody{
		Params:                    i.param,
		MigrationInstallation:     i.migrationInstallation,
		SourceDatabaseInformation: i.sourceDatabaseInformation,
		TargetDatabaseInformation: i.targetDatabaseInformation,
	}, nil
}

func (i *DsgTaskHelper) BuildParams(ctx context.Context) {
	if i.err != nil {
		return
	}

	nowTime := time.Now()
	oneMonthLater := nowTime.AddDate(0, 1, 0)
	const timeFormat = "2006-01-02T15:04:05.000Z"

	taskName := i.GetTaskName()
	params := dsg.SaveTaskRequestBodyParams{
		TaskName: taskName,
		Type:     string(dsg.TaskTypeProduction),
		Describe: taskName,
		Time:     []string{nowTime.Format(timeFormat), oneMonthLater.Format(timeFormat)},
	}
	i.param = params
}

func (i *DsgTaskHelper) BuildMigrationInstallation(ctx context.Context) {
	if i.err != nil {
		return
	}

	subSystemId, getErr := i.MustGetTMSSubSystemID(ctx)
	if getErr != nil {
		i.err = getErr
		return
	}

	createSourceHostErr := i.createSourceHostIfNotExist(ctx, subSystemId)
	if createSourceHostErr != nil {
		i.err = createSourceHostErr
		return
	}

	createTargetHostErr := i.createTargetHostIfNotExist(ctx, subSystemId)
	if createTargetHostErr != nil {
		i.err = createTargetHostErr
		return
	}

	p := dsg.SaveTaskRequestBodyMigrationInstallation{
		SourIP:    i.SourceHost.HostIP,
		SourPath:  i.SourcePath,
		Subsystem: subSystemId,
		TarIP:     i.TargetHost.HostIP,
		TarPath:   i.TargetPath,
	}
	i.migrationInstallation = p
}

func (i *DsgTaskHelper) GetTaskName() string {
	return fmt.Sprintf("tms_%d_%d", i.ChannelId, i.TaskId)
}

func (i *DsgTaskHelper) BuildDefineConfig(ctx context.Context) {
	if i.err != nil {
		return
	}
	i.defineConfig = ""
}

func (i *DsgTaskHelper) BuildSourceDatabaseInformation(ctx context.Context, ds *datasource.Datasource, validateHostIp string) {
	if i.err != nil {
		return
	}

	if !ds.EnableIncrementSync {
		i.err = errors.NewError(errors.TMS_DSG_DATASOURCE_NOT_ENABLE_INCREMENT, "DSG")
		return
	}

	sourceDataSource, createErr := i.createSourceDataSource(ctx, ds, validateHostIp)
	if createErr != nil {
		i.err = createErr
		return
	}

	var sourceDatabaseInfo dsg.SaveTaskRequestBodySourceDatabaseInformation

	if ds.LogFileStoreMode == "A" { // 如果开启了增量同步并且存储模式为ASM
		sourceDatabaseInfo = i.buildASMSourceDatabase(ds, sourceDataSource)
	} else if ds.LogFileStoreMode == "F" {
		if ds.PDBFlag == "PDB" { // 由前端确保12C+的数据库才会有PDB标识，TODO 未测试完成
			sourceDatabaseInfo = i.buildPDBSourceDatabase(sourceDataSource)
		} else {
			sourceDatabaseInfo = i.buildNormalSourceDatabase(sourceDataSource)
		}
	} else {
		i.err = errors.NewError(errors.TMS_DSG_DATASOURCE_HAS_NO_LOG_STORE_MODE, "DSG")
		return
	}
	i.sourceDatabaseInformation = sourceDatabaseInfo
}

func (i *DsgTaskHelper) buildASMSourceDatabase(
	ds *datasource.Datasource,
	source dsg.DataSource,
) dsg.SaveTaskRequestBodySourceDatabaseInformation {
	simplifyIp := strings.ReplaceAll(ds.HostIp, ".", "")

	asmReq := dsg.SaveTaskRequestBodySourceDatabaseInformation{
		CharacterSet:   "utf8", // DSG预留字段，默认UTF8，无法修改
		DatabaseType:   source.DbType,
		OracleHome:     i.SourcePath,
		OraclePassword: source.DbPasswd,
		OracleUser:     source.DbUser,
		SourName:       source.DbID,

		OracleTNS:         fmt.Sprintf("%s%s", source.ServiceName, simplifyIp),
		OracleASMHOME:     i.SourcePath, // ASM一般不用
		OracleASMTNS:      fmt.Sprintf("%s%s_asm", source.ServiceName, simplifyIp),
		OracleASMUser:     source.AsmDbUser,
		OracleASMPassword: source.AsmDbPasswd,

		DataFile:       "",
		OracleASMSID:   "",
		OracleSID:      "",
		OracleTNSAdmin: "",
		PDBName:        "",
	}
	return asmReq
}

func (i *DsgTaskHelper) buildNormalSourceDatabase(source dsg.DataSource) dsg.SaveTaskRequestBodySourceDatabaseInformation {
	pdbReq := dsg.SaveTaskRequestBodySourceDatabaseInformation{
		CharacterSet:   "utf8", // DSG预留字段，默认UTF8，无法修改
		DatabaseType:   source.DbType,
		OracleHome:     source.OracleHome,
		OraclePassword: source.DbPasswd,
		OracleUser:     source.DbUser,
		SourName:       source.DbID,
		OracleSID:      source.OracleSid,

		PDBName:        "",
		OracleASMSID:   "",
		OracleASMHOME:  "",
		OracleASMTNS:   "",
		OracleTNS:      "",
		DataFile:       "",
		OracleTNSAdmin: "",
	}
	return pdbReq
}

func (i *DsgTaskHelper) buildPDBSourceDatabase(source dsg.DataSource) dsg.SaveTaskRequestBodySourceDatabaseInformation {
	pdbReq := dsg.SaveTaskRequestBodySourceDatabaseInformation{
		CharacterSet:   "utf8", // DSG预留字段，默认UTF8，无法修改
		DatabaseType:   source.DbType,
		OracleHome:     source.OracleHome,
		OraclePassword: source.DbPasswd,
		OracleUser:     source.DbUser,
		SourName:       source.DbID,
		OracleSID:      source.OracleSid,
		PDBName:        source.PdbName,

		OracleASMSID:   "",
		OracleASMHOME:  "",
		OracleASMTNS:   "",
		OracleTNS:      "",
		DataFile:       "",
		OracleTNSAdmin: "",
	}
	return pdbReq
}

func (i *DsgTaskHelper) createSourceDataSource(ctx context.Context, ds *datasource.Datasource, validateHostIp string) (dsg.DataSource, error) {
	physicalSubSystemId, err := i.MustGetTMSSubSystemID(ctx)
	if err != nil {
		i.err = err
		return dsg.DataSource{}, err
	}

	dsgDataSourceName := i.createDsgDataSourceName(ds)
	dsgDbType := i.getDsgOracleDBType(ds)
	createDataSourceRequest := i.createDefaultSaveDataSourceReq(dsgDataSourceName, dsgDbType, ds, physicalSubSystemId)

	var isASM bool
	// 如果数据源配置了ASM，需要一并填入ASM连接信息
	if ds.EnableIncrementSync && ds.LogFileStoreMode == "A" {
		isASM = true
		createDataSourceRequest.LinkFlag = "TNS"
		createDataSourceRequest.Asm = true
		createDataSourceRequest.AsmDbIp = ds.ASMDBIp
		createDataSourceRequest.AsmDbPort = ds.ASMDBPort
		createDataSourceRequest.AsmDbUser = ds.ASMDBUser
		log.Infof("creating dsg datasource with asm info, asmDbIp:%s, asmDbPort:%d, asmDbUser:%s, asmDbName:%s, asmDbPasswd:%s",
			ds.ASMDBIp, ds.ASMDBPort, ds.ASMDBUser, ds.ASMDBName, ds.ASMDBPasswdValue)
		createDataSourceRequest.AsmDbPasswd = ds.ASMDBPasswdValue
		createDataSourceRequest.AsmDbName = ds.ASMDBName
		createDataSourceRequest.AsmHome = i.SourcePath
		createDataSourceRequest.AsmOracleHome = i.SourcePath
	}
	if ds.EnableIncrementSync && ds.LogFileStoreMode == "F" {
		// 根据DSG提供的数据源定义，使用文件系统，以下几个值必须为空，不然会自动生成tns_name，导致同步异常
		createDataSourceRequest.HostIP = ""
		createDataSourceRequest.HostPort = 0
		createDataSourceRequest.ServiceName = ""
	}

	createDataSourceRequestBytes, _ := json.Marshal(&createDataSourceRequest)
	log.Infof("createDataSourceRequest, invoke SaveDataSourceInfo, dsgDataSourceName:%s, req:%v", dsgDataSourceName, string(createDataSourceRequestBytes))
	_, createErr := i.Adaptor.SaveDataSourceInfo(ctx, createDataSourceRequest)
	if createErr != nil {
		i.err = createErr
		return dsg.DataSource{}, createErr
	}

	if isASM {
		log.Infof("after create source datasource with asm, invoke GetDataSourceInfo for update machine ip, dsgDataSourceName:%s", dsgDataSourceName)
		savedDataSources, getErr := i.Adaptor.GetDataSourceInfo(ctx, dsg.GetDataSourceInfoRequest{
			Offset: 0,
			Limit:  10,
			UserID: i.UserId,
			DBName: dsgDataSourceName,
		})
		if getErr != nil {
			log.Errorf("after create source datasource with asm, GetDataSourceInfo failed, taskId:%d, dsgDataSourceName:%s, err: %v", i.TaskId, dsgDataSourceName, getErr)
		} else {
			if len(savedDataSources.DataInfo.Rows) == 0 {
				log.Errorf("after create source datasource with asm, GetDataSourceInfo not found datasources, taskId:%d, dsgDataSourceName:%s, err: %v", i.TaskId, dsgDataSourceName, getErr)
			} else if len(savedDataSources.DataInfo.Rows) >= 2 {
				log.Errorf("after create source datasource with asm, GetDataSourceInfo found more than one datasources, taskId:%d, dsgDataSourceName:%s, err: %v", i.TaskId, dsgDataSourceName, getErr)
			} else {
				savedDatasource := savedDataSources.DataInfo.Rows[0]
				savedDatasourceId := savedDatasource.DbID
				updateDataSourceReq := dsg.UpdateASMDataSourceInfoRequest{
					Asm:               true,
					AsmDbIp:           ds.ASMDBIp,
					AsmDbName:         ds.ASMDBName,
					AsmDbPasswd:       ds.ASMDBPasswdValue,
					AsmDbPort:         ds.ASMDBPort,
					AsmDbUser:         ds.ASMDBUser,
					AsmHome:           i.SourcePath,
					DataCenter:        savedDatasource.DataCenter,
					DBId:              savedDatasourceId,
					DbName:            dsgDataSourceName,
					DbPasswd:          ds.PasswordValue,
					DBType:            dsgDbType,
					DbUser:            ds.UserName,
					HostIP:            ds.HostIp,
					HostPort:          ds.HostPort,
					LinkFlag:          "TNS",
					MachineIP:         validateHostIp,
					PhysicalSubsystem: physicalSubSystemId,
					ServiceName:       ds.ServiceName,
					UserId:            i.UserId,
					IsBackupDb:        "N",
					IsProxy:           false,
				}
				updateDataSourceReqByte, _ := json.Marshal(&updateDataSourceReq)
				log.Infof("after create source datasource with asm, invoke UpdateASMDataSourceInfo, dsgDataSourceName:%s, req:%v", dsgDataSourceName, string(updateDataSourceReqByte))
				time.Sleep(time.Second * 2)
				_, updateErr := i.Adaptor.UpdateASMDataSourceInfo(ctx, updateDataSourceReq)
				if updateErr != nil {
					i.err = errors.NewError(errors.TMS_DSG_UPDATE_ASM_DATASOURCE_FAILED, updateErr.Error())
				}
			}
		}
	}

	getDataSourceRsp, getErr := i.Adaptor.GetDataSourceInfo(ctx, dsg.GetDataSourceInfoRequest{
		Offset: 0,
		Limit:  1000,
		PsID:   physicalSubSystemId,
		UserID: i.UserId,
		DBName: dsgDataSourceName,
	})
	if getErr != nil {
		i.err = getErr
		return dsg.DataSource{}, getErr
	}

	dataSources := dsg.DataSources(getDataSourceRsp.DataInfo.Rows)

	if dataSources.Len() == 0 {
		i.err = errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "DSG")
		return dsg.DataSource{}, errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "DSG")
	}
	sort.Sort(dataSources)

	latestDataSource := dataSources[0]
	return latestDataSource, nil
}

func (i *DsgTaskHelper) createDsgDataSourceName(ds *datasource.Datasource) string {
	shortUUID := i.getShortUUID()
	return fmt.Sprintf("%d_%d_%s_%s", i.ChannelId, i.TaskId, ds.DatasourceName, shortUUID)
}

func (i *DsgTaskHelper) createDefaultSaveDataSourceReq(dsgDataSourceName string, dsgDbType string, ds *datasource.Datasource, physicalSubSystemId int) dsg.SaveDataSourceInfoRequest {
	return dsg.SaveDataSourceInfoRequest{
		DbName:            dsgDataSourceName,
		DBType:            dsgDbType,
		IsRac:             false,
		HostIP:            ds.HostIp,
		MachineIP:         ds.HostIp,
		HostPort:          ds.HostPort,
		DbUser:            ds.UserName,
		DbPasswd:          ds.PasswordValue,
		ServiceName:       ds.ServiceName,
		OracleSid:         ds.OracleSID,
		OracleHome:        ds.OracleHome,
		DataCenter:        "TMS",
		PhysicalSubsystem: physicalSubSystemId,
		UserId:            i.UserId,
		IsBackupDb:        "N",
		IsProxy:           false,
		LinkFlag:          "SID",
	}
}

func (i *DsgTaskHelper) getDsgOracleDBType(ds *datasource.Datasource) string {
	var dbVersion string
	if ds.DBVersion == "" {
		dbVersion = "oracle11g"
	} else {
		if strings.Contains(ds.DBVersion, "11") {
			dbVersion = "oracle11g"
		} else if strings.Contains(ds.DBVersion, "19") {
			dbVersion = "oracle19c"
		} else {
			dbVersion = "oracle12c+"
		}
	}
	return dbVersion
}

func (i *DsgTaskHelper) createTargetDataSource(ctx context.Context, dt *datasource.Datasource) (dsg.DataSource, error) {
	physicalSubSystemId, err := i.MustGetTMSSubSystemID(ctx)
	if err != nil {
		i.err = err
		return dsg.DataSource{}, err
	}

	shortUUID := i.getShortUUID()
	dsgDataSourceName := fmt.Sprintf("%d_%d_%s_%s", i.ChannelId, i.TaskId, dt.DatasourceName, shortUUID)

	createDataSourceRequest := dsg.SaveDataSourceInfoRequest{
		DbName:            dsgDataSourceName,
		DBType:            dt.DbType,
		IsRac:             false,
		HostIP:            dt.HostIp,
		MachineIP:         dt.HostIp,
		HostPort:          dt.HostPort,
		DbUser:            dt.UserName,
		DbPasswd:          dt.PasswordValue,
		DataCenter:        "TMS",
		PhysicalSubsystem: physicalSubSystemId,
		UserId:            i.UserId,
		IsBackupDb:        "N",
		IsProxy:           false,

		Asm: true,
	}
	_, createErr := i.Adaptor.SaveDataSourceInfo(ctx, createDataSourceRequest)
	if createErr != nil {
		i.err = createErr
		return dsg.DataSource{}, createErr
	}

	getDataSourceRsp, getErr := i.Adaptor.GetDataSourceInfo(ctx, dsg.GetDataSourceInfoRequest{
		Offset: 0,
		Limit:  1000,
		PsID:   physicalSubSystemId,
		UserID: i.UserId,
		DBName: dsgDataSourceName,
	})
	if getErr != nil {
		i.err = getErr
		return dsg.DataSource{}, getErr
	}

	dataSources := dsg.DataSources(getDataSourceRsp.DataInfo.Rows)

	if dataSources.Len() == 0 {
		i.err = errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "DSG")
		return dsg.DataSource{}, errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "DSG")
	}
	sort.Sort(dataSources)

	latestDataSource := dataSources[0]
	return latestDataSource, nil
}

func (i *DsgTaskHelper) BuildTargetDatabaseInformation(ctx context.Context, dt *datasource.Datasource, validateHostIp string) {
	if i.err != nil {
		return
	}

	targetDataSource, createErr := i.createTargetDataSource(ctx, dt)
	if createErr != nil {
		i.err = createErr
		return
	}

	i.targetDatabaseInformation = dsg.SaveTaskRequestBodyTargetDatabaseInformation{
		DatabaseType:    targetDataSource.DbType,
		TarAddress:      targetDataSource.HostIP,
		TarCharacterSet: "utf8",
		TarDatabase:     "",
		TarName:         targetDataSource.DbID,
		TarPassword:     targetDataSource.DbPasswd,
		TarPost:         parse.FormatInt(dt.HostPort),
		TarUsername:     dt.UserName,
	}
}

func (i *DsgTaskHelper) createSourceHostIfNotExist(ctx context.Context, subSystemId int) error {

	h := i.SourceHost

	createErr := i.CreateHostIfNotExist(ctx, subSystemId, h.HostIP, h.HostName, h.OsName, h.BasePath)
	if createErr != nil {
		return createErr
	}
	updateErr := i.UpdateHostPorts(ctx, subSystemId, h.HostIP, h.HostName, h.PortMin, h.PortMax)
	if updateErr != nil {
		return updateErr
	}
	return nil
}

func (i *DsgTaskHelper) MustGetTMSSubSystemID(ctx context.Context) (int, error) {
	if i.physicalSubSystemId != 0 {
		log.Infof("physicalSubSystemId exist, return directly, taskId:%d, physicalSubSystemId:%d", i.TaskId, i.physicalSubSystemId)
		return i.physicalSubSystemId, nil
	}
	var tmsSubSystemId int
	var getErr error
	var createErr error

	tmsSubSystemId, getErr = i.getTMSSubSystemID(ctx)
	if getErr != nil {
		log.Errorf("getTMSSubSystemID failed, err: %v", getErr)
		return 0, getErr
	}

	if tmsSubSystemId != 0 {
		return tmsSubSystemId, nil
	}

	createErr = i.createTMSSubSystem(ctx)
	if createErr != nil {
		log.Errorf("createTMSSubSystem failed, err: %v", createErr)
		return 0, createErr
	}

	tmsSubSystemId, getErr = i.getTMSSubSystemID(ctx)
	if getErr != nil {
		log.Errorf("getTMSSubSystemID failed, err: %v", getErr)
		return 0, getErr
	}
	return tmsSubSystemId, nil
}

func (i *DsgTaskHelper) getTMSSubSystemID(ctx context.Context) (int, error) {
	if i.physicalSubSystemId != 0 {
		return i.physicalSubSystemId, nil
	}

	apiReq := dsg.QueryPhysicalSubSystemRequest{
		UserID: i.UserId,
		Offset: 0,
		Limit:  100 * 100,
	}

	apiResp, err := i.Adaptor.QueryPhysicalSubSystem(ctx, apiReq)
	if err != nil {
		log.Errorf("getTMSSubSystemID, invoke QueryPhysicalSubSystem failed, err: %v", err)
		return 0, err
	}

	var hitTmsSubSystem bool
	var tmsSubSystemId int

	log.Infof("getTMSSubSystemID, taskId:%d, totalPhysicalSubSystemNum:%d", i.TaskId, apiResp.DataInfo.Total)
	for _, ss := range apiResp.DataInfo.Rows {
		if ss.GroupMachineName == constants.TMS_SUB_SYSTEM {
			hitTmsSubSystem = true
			tmsSubSystemId = ss.ID
			i.physicalSubSystemId = ss.ID
			break
		}
	}

	// 如果存在直接返回，否则创建
	if hitTmsSubSystem {
		log.Infof("getTMSSubSystemID, tmsSubSystem already exist, psId:%d, taskId:%d, return", tmsSubSystemId, i.TaskId)
		return tmsSubSystemId, nil
	}
	return 0, nil
}

func (i *DsgTaskHelper) createTMSSubSystem(ctx context.Context) error {
	log.Infof("physicalSubSystemId not found, create new one, taskId:%d", i.TaskId)
	_, createSubSystemErr := i.Adaptor.InsertPhysicalSubSystem(ctx, dsg.InsertPhysicalSubSystemRequest{
		UserID:           i.UserId,
		GroupMachineName: constants.TMS_SUB_SYSTEM,
		GroupMachineDesc: constants.TMS_SUB_SYSTEM,
	})
	if createSubSystemErr != nil {
		return createSubSystemErr
	}
	return nil
}

func (i *DsgTaskHelper) getShortUUID() string {
	if i.shortUUID != "" {
		return i.shortUUID
	}
	i.shortUUID = strings.ToUpper(uuid.NewString()[:6])
	return i.shortUUID
}

func (i *DsgTaskHelper) createTargetHostIfNotExist(ctx context.Context, subSystemId int) error {
	h := i.TargetHost

	createErr := i.CreateHostIfNotExist(ctx, subSystemId, h.HostIP, h.HostName, h.OsName, h.BasePath)
	if createErr != nil {
		return createErr
	}
	updateErr := i.UpdateHostPorts(ctx, subSystemId, h.HostIP, h.HostName, h.PortMin, h.PortMax)
	if updateErr != nil {
		return updateErr
	}
	return nil
}

func (i *DsgTaskHelper) CreateHostIfNotExist(ctx context.Context, subSystemId int, hostIP, hostname, osName, homePath string) error {
	hostRsp, getHostErr := i.Adaptor.QueryAllHostInfo(ctx, dsg.QueryAllHostInfoRequest{
		Offset:        0,
		Limit:         1000,
		UserID:        i.UserId,
		AffDataCenter: "TMS",
		HostIP:        hostIP,
		HostName:      hostname,
		PsID:          subSystemId,
	})
	if getHostErr != nil {
		return getHostErr
	}

	for _, host := range hostRsp.DataInfo.Rows {
		if host.HostIP == hostIP {
			log.Infof("CreateHostIfNotExist, host already exist, ip:%s, hostname:%s, return", hostIP, hostname)
			return nil
		}
	}

	req := dsg.InsertNewHostInfoRequest{
		AffDataCenter:       "TMS",
		HostIP:              hostIP,
		HostName:            hostname,
		PhysicalSubSystemId: subSystemId,
		OsName:              osName,
		SecretName:          homePath,
	}
	_, insertErr := i.Adaptor.InsertNewHostInfo(ctx, req)
	if insertErr != nil {
		return insertErr
	}

	return nil
}

func (i *DsgTaskHelper) UpdateHostPorts(ctx context.Context, subSystemId int, hostIP, hostname string, portMin, portMax int) error {
	hostRsp, getHostErr := i.Adaptor.QueryAllHostInfo(ctx, dsg.QueryAllHostInfoRequest{
		Offset:        0,
		Limit:         1000,
		UserID:        i.UserId,
		AffDataCenter: "TMS",
		HostIP:        hostIP,
		HostName:      hostname,
		PsID:          subSystemId,
	})
	if getHostErr != nil {
		return getHostErr
	}

	var hitHost bool
	var macId int
	for _, host := range hostRsp.DataInfo.Rows {
		if host.HostIP == hostIP {
			log.Infof("UpdateHostPorts, host found and updating ports, ip:%s, hostname:%s", hostIP, hostname)
			hitHost = true
			macId = host.MacID
			break
		}
	}

	if !hitHost {
		i.err = errors.NewError(errors.TMS_DSG_HOST_NOT_EXIST, hostIP)
		return nil
	}

	req := dsg.UpdateHostPortInfoRequest{
		MacID:   macId,
		PortMin: portMin,
		PortMax: portMax,
	}
	_, updateErr := i.Adaptor.UpdateHostPortInfo(ctx, req)
	if updateErr != nil {
		return updateErr
	}

	return nil
}

func (i *DsgTaskHelper) WriteYLoader(ctx context.Context, tId int, oldYLoader, newYLoader message.YLoader) (*dsg.WriteKeyIniResponse, error) {
	var contentBytes []byte
	var formBytes []byte
	var marshalErr error
	contentArray := []message.YLoader{newYLoader}

	contentBytes, marshalErr = json.Marshal(&contentArray)
	if marshalErr != nil {
		return nil, marshalErr
	}

	formBytes, marshalErr = json.Marshal(&oldYLoader)
	if marshalErr != nil {
		return nil, marshalErr
	}

	dsgReq := dsg.WriteKeyIniRequest{
		FileName: "yloader.ini",
		TID:      tId,
		Content:  string(contentBytes),
		Form:     string(formBytes),
	}
	updateRsp, getSourceErr := i.Adaptor.WriteKeyIni(ctx, dsgReq)
	return &updateRsp, getSourceErr
}

func (i *DsgTaskHelper) UpdateDsgSourceConfig(ctx context.Context, tid int, setDmValue string) error {
	taskId := i.TaskId
	// fetch source vm config from dsg and verify vm value, based on taskMethod Increment
	readReq := dsg.ReadSourceDataSourceIniRequest{FileName: "vm", TID: tid}
	log.Infof("invoke ReadSourceDataSourceIni, taskId:%d, dsgId:%d, req:%v", taskId, tid, readReq)
	sourceIniRsp, getSourceErr := i.Adaptor.ReadSourceDataSourceIni(ctx, readReq)
	if getSourceErr != nil {
		log.Errorf("invoke ReadSourceDataSourceIni failed, taskId:%d, dsgId:%d, err: %v", taskId, tid, getSourceErr)
		return getSourceErr
	}
	log.Infof("invoke ReadSourceDataSourceIni req:%v, rsp:%v", readReq, sourceIniRsp)

	vm := sourceIniRsp.DataInfo.Res
	vmTokens := strings.Split(vm, "\n")
	for idx, vmToken := range vmTokens {
		vmToken = strings.TrimSpace(vmToken)
		if !strings.Contains(vmToken, "set dm 1.1") {
			continue
		}
		if vmToken == setDmValue {
			break
		}
		vmTokens[idx] = setDmValue

		newVmValue := strings.Join(vmTokens, "\n")

		dsgReq := dsg.WriteSourceDataSourceIniRequest{
			FileName: "vm",
			TID:      tid,
			Content:  newVmValue,
		}
		log.Infof("invoke WriteSourceDataSourceIni, taskId:%d, dsgId:%d, content:%v, req:%v", taskId, tid, newVmValue, dsgReq)
		updateRsp, updateSourceErr := i.Adaptor.WriteSourceDataSourceIni(ctx, dsgReq)
		if updateSourceErr != nil {
			log.Errorf("invoke WriteSourceDataSourceIni failed, taskId:%d, dsgId:%d, err: %v", taskId, tid, updateSourceErr)
			return updateSourceErr
		}
		log.Infof("invoke WriteSourceDataSourceIni req:%v, rsp:%v", dsgReq, updateRsp)
		break
	}
	return nil
}

func (i *DsgTaskHelper) UpdateDsgTargetConfig(ctx context.Context, tid int) error {
	taskId := i.TaskId

	readTargetIni, readErr := i.Adaptor.ReadTargetDataSourceIni(ctx, dsg.ReadTargetDataSourceIniRequest{
		FileName: "yloader.ini",
		TID:      tid,
	})
	if readErr != nil {
		log.Errorf("invoke ReadTargetDataSourceIni failed, taskId:%d, tid:%d, err: %v", taskId, tid, readErr)
		return readErr
	} else {
		oldYLoader := buildYLoaderFromForm(readTargetIni.DataInfo.Form)
		newYLoader := buildYLoaderFromForm(readTargetIni.DataInfo.Form)

		newYLoader.DbLang = `UTF-8`
		newYLoader.TableCreateFull = "y"
		newYLoader.TableCreateFullIndex = "y"
		newYLoader.TableExistsRealDo = "none"

		log.Infof("update target config, taskId:%d, tid:%d, newYLoader:%v", taskId, tid, newYLoader)
		_, writeErr := i.WriteYLoader(ctx, tid, oldYLoader, newYLoader)
		if writeErr != nil {
			log.Errorf("invoke WriteYLoader failed, taskId:%d, dsgId:%d, err: %v", taskId, tid, writeErr)
			return writeErr
		}
	}
	return nil
}

func (i *DsgTaskHelper) GetDsgTaskTime(ctx context.Context, dsgTaskName string) (*message.DsgTaskTime, error) {
	var startBinaryTime, stopBinaryTime, startMigrateTime, stopMigrateTime string
	var lastStartBinaryTime, lastStopBinaryTime, lastStartMigrateTime, lastStopMigrateTime string
	var pauseMigrateTime, resumeMigrateTime string

	logResponse, getLogErr := i.Adaptor.ListOperateLog(ctx, dsg.ListOperateLogRequest{
		Offset:   0,
		Limit:    1000,
		TaskName: dsgTaskName,
	})
	if getLogErr != nil {
		log.Errorf("invoke ListOperateLog failed, taskId:%s, err: %v", dsgTaskName, getLogErr)
		return nil, getLogErr
	}

	operateLogs := logResponse.DataInfo.Rows
	sort.Slice(operateLogs, func(i, j int) bool {
		return operateLogs[i].ID < operateLogs[j].ID
	})

	for _, operateLog := range operateLogs {
		switch operateLog.OperTitle {
		case "启动任务":
			startBinaryTime = operateLog.OperTime
			stopBinaryTime = ""
			stopMigrateTime = ""
			startMigrateTime = lastStartMigrateTime
			lastStartBinaryTime = operateLog.OperTime
		case "停止任务":
			startBinaryTime = ""
			startMigrateTime = ""
			stopBinaryTime = operateLog.OperTime
			lastStopBinaryTime = operateLog.OperTime
		case "开始迁移":
			startMigrateTime = operateLog.OperTime
			stopMigrateTime = ""
			stopBinaryTime = ""
			lastStartMigrateTime = operateLog.OperTime
		case "清空缓存":
			startMigrateTime = ""
			lastStartMigrateTime = ""
		case "执行指定任务目录下指定脚本":
			if strings.Contains(operateLog.OperParam, "kill_19.sh") {
				pauseMigrateTime = operateLog.OperTime
				resumeMigrateTime = ""
			}
			if strings.Contains(operateLog.OperParam, "kill_18.sh") {
				resumeMigrateTime = operateLog.OperTime
				pauseMigrateTime = ""
			}
		}
	}

	var cstZone = time.FixedZone("CST", 8*3600)
	dtt := &message.DsgTaskTime{
		StartBinaryTime:      startBinaryTime,
		StopBinaryTime:       stopBinaryTime,
		StartMigrateTime:     startMigrateTime,
		StopMigrateTime:      stopMigrateTime,
		PauseMigrateTime:     pauseMigrateTime,
		ResumeMigrateTime:    resumeMigrateTime,
		LastStartBinaryTime:  parseDsgTime(lastStartBinaryTime, cstZone),
		LastStopBinaryTime:   parseDsgTime(lastStopBinaryTime, cstZone),
		LastStartMigrateTime: parseDsgTime(lastStartMigrateTime, cstZone),
		LastStopMigrateTime:  parseDsgTime(lastStopMigrateTime, cstZone),
	}

	return dtt, nil
}

func (i *DsgTaskHelper) GetTaskLogs(ctx context.Context, dsgTaskName string, offset, limit int) (message.DsgOperationLogs, int64, error) {
	logResponse, getLogErr := i.Adaptor.ListOperateLog(ctx, dsg.ListOperateLogRequest{
		Offset:   offset,
		Limit:    limit,
		TaskName: dsgTaskName,
	})
	if getLogErr != nil {
		log.Errorf("invoke ListOperateLog failed, taskId:%s, err: %v", dsgTaskName, getLogErr)
		return nil, 0, getLogErr
	}

	operateLogs := logResponse.DataInfo.Rows
	sort.Slice(operateLogs, func(i, j int) bool {
		return operateLogs[i].ID < operateLogs[j].ID
	})

	logs := lo.Map(operateLogs, func(ol dsg.OperateLog, _ int) message.DsgOperationLog {
		return message.DsgOperationLog{
			ID:                ol.ID,
			TmsOperationTitle: getOperationLogTitle(ol),
			OperationTitle:    ol.OperTitle,
			BusinessType:      ol.BusinessType,
			OperationMethod:   ol.OperMethod,
			RequestMethod:     ol.RequestMethod,
			OperationID:       ol.OperID,
			OperationName:     ol.OperName,
			RoleName:          ol.RoleName,
			UsergroupName:     ol.UsergroupName,
			OperationURL:      ol.OperURL,
			OperationIP:       ol.OperIP,
			OperationSource:   ol.OperSource,
			OperationParam:    ol.OperParam,
			JSONResult:        ol.JSONResult,
			OperationStatus:   ol.OperStatus,
			ErrorMsg:          ol.ErrorMsg,
			OperationTime:     ol.OperTime,
			BeginTime:         ol.BeginTime,
			EndTime:           ol.EndTime,
			Limit:             ol.Limit,
			Offset:            ol.Offset,
			SortName:          ol.SortName,
			UserID:            ol.UserID,
		}
	})

	return logs, int64(logResponse.DataInfo.Total), nil
}

func getOperationLogTitle(ol dsg.OperateLog) string {
	switch ol.OperTitle {
	case "清空缓存":
		return "重置"
	case "执行指定任务目录下指定脚本":
		if strings.Contains(ol.OperParam, "kill_19.sh") {
			return "暂停迁移"
		}
		if strings.Contains(ol.OperParam, "kill_18.sh") {
			return "恢复迁移"
		}
	}
	return ol.OperTitle
}

func (i *DsgTaskHelper) PauseSync(ctx context.Context, incrementId int) error {
	log.Infof("PauseSync, invoke OperationScript, fileName:%s, taskId:%d, incrementId:%d", "kill_19.sh", i.TaskId, incrementId)
	_, err := i.Adaptor.OperationScript(ctx, dsg.OperationScriptRequest{
		TID:      incrementId,
		FileName: "kill_19.sh;echo 'EXEXFLAG'=$?",
	})
	if err != nil {
		return err
	}
	return nil
}

func (i *DsgTaskHelper) ResumeSync(ctx context.Context, incrementId int) error {
	log.Infof("ResumeSync, invoke OperationScript, fileName:%s, taskId:%d, incrementId:%d", "kill_18.sh", i.TaskId, incrementId)
	_, err := i.Adaptor.OperationScript(ctx, dsg.OperationScriptRequest{
		TID:      incrementId,
		FileName: "kill_18.sh;echo 'EXEXFLAG'=$?",
	})
	if err != nil {
		return err
	}
	return nil
}

// ResumeSyncWithSCN resume sync with scn
// 1. get source connection
// 2. parse scn or timestamp to scn
// 3. create or update increment task info
// 4. stop task
// 5. clear cache
// 6. update source config with scn value
// 7. start task
// 8. sync
func (i *DsgTaskHelper) ResumeSyncWithSCN(ctx context.Context, scnOption increment.IncrementTask, taskInfo *task.Task, channelInfo *channel.ChannelInformation) error {
	taskId := taskInfo.TaskID

	sourceConns, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if setUpErr != nil {
		log.Errorf("ResumeSyncWithSCN, set up source database connection failed, taskId:%d, err: %v", taskId, setUpErr)
		return setUpErr
	}

	var scnValue string
	var timeValue string
	var parseErr error
	if scnOption.ScnType == string(message.SCNType_TIME) {
		scnValue, timeValue, parseErr = i.ParseTimestampToSCN(ctx, scnOption.ScnUserInputValue, sourceConns, taskInfo)
	} else {
		scnValue, timeValue, parseErr = i.ParseSCNToTimestamp(ctx, scnOption.ScnUserInputValue, sourceConns, taskInfo)
	}
	if parseErr != nil {
		log.Errorf("ResumeSyncWithSCN, parse scn or timestamp failed, taskId:%d, err: %v", taskId, parseErr)
		return parseErr
	}
	dsgTaskName := fmt.Sprintf("tms_%d_%d", taskInfo.ChannelId, taskInfo.TaskID)
	incrementTaskInfo := increment.IncrementTask{
		TaskId:            taskId,
		DsgTaskName:       dsgTaskName,
		ScnType:           scnOption.ScnType,
		ScnValue:          scnValue,
		TimeValue:         timeValue,
		ScnUserInputValue: scnOption.ScnUserInputValue,
	}
	_, createITIErr := models.GetIncrementReaderWriter().CreateOrUpdateIncrementTask(ctx, &incrementTaskInfo)
	if createITIErr != nil {
		log.Errorf("ResumeSyncWithSCN, create increment task info failed, taskId:%d, err: %v", taskId, createITIErr)
		return errors.NewError(errors.TMS_DSG_CREATE_SYNC_INFO_FAILED, createITIErr.Error())
	}

	log.Infof("ResumeSyncWithSCN, invoke StopSync, taskId:%d, dsgId:%d", taskId, taskInfo.IncrementId)
	_, stopErr := i.Adaptor.StopTask(ctx, dsg.StopTaskRequest{TID: taskInfo.IncrementId})
	if stopErr != nil {
		log.Errorf("ResumeSyncWithSCN failed, invoke StopTask failed, taskId:%d, err: %v", taskId, stopErr)
		return stopErr
	}
	_, clearErr := i.Adaptor.ClearCache(ctx, dsg.ClearCacheRequest{TID: taskInfo.IncrementId, Limit: 100, Offset: 0})
	if clearErr != nil {
		log.Errorf("ResumeSyncWithSCN failed, invoke ClearCache failed, taskId:%d, err: %v", taskId, clearErr)
		return clearErr
	}

	log.Infof("ResumeSyncDsgTask, read source config for update default values, taskId:%d, dsgId:%d", taskId, taskInfo.IncrementId)
	setVmValue := strings.ReplaceAll(dsg.TaskMethodParamIncrementOnlyWithSCN, "${SCN_VALUE}", scnValue)
	updateSourceConfigErr := i.UpdateDsgSourceConfig(ctx, taskInfo.IncrementId, setVmValue)
	if updateSourceConfigErr != nil {
		log.Errorf("ResumeSyncWithSCN failed, update source config failed, taskId:%d, dsgId:%d, err: %v", taskId, taskInfo.IncrementId, updateSourceConfigErr)
		return updateSourceConfigErr
	}

	_, startErr := i.Adaptor.StartTask(ctx, dsg.StartTaskRequest{TID: taskInfo.IncrementId})
	if startErr != nil {
		log.Errorf("ResumeSyncWithSCN failed, invoke StartTask failed, taskId:%d, err: %v", taskId, startErr)
		return startErr
	}
	_, syncErr := i.Adaptor.Sync(ctx, dsg.SyncRequest{TID: taskInfo.IncrementId})
	if syncErr != nil {
		log.Errorf("ResumeSyncWithSCN failed, invoke Sync failed, taskId:%d, err: %v", taskId, syncErr)
		return syncErr
	}
	return nil
}

func (i *DsgTaskHelper) ParseTimestampToSCN(ctx context.Context, inputVal string, sourceConns *oracle.Oracle, taskInfo *task.Task) (string, string, error) {
	var timeToScnSQL = `SELECT TIMESTAMP_TO_SCN(TO_TIMESTAMP('${TIME_VALUE}', 'YYYY-MM-DD HH24:MI:SS')) AS SCN_VALUE FROM DUAL`
	timeToScnSQL = strings.ReplaceAll(timeToScnSQL, "${TIME_VALUE}", inputVal)
	_, ret, queryErr := oracle.Query(ctx, sourceConns.OracleDB, timeToScnSQL)
	if queryErr != nil {
		log.Errorf("ParseTimestampToSCN, query time to scn failed, taskId:%d, dsgTaskId:%d, sql:%s, err: %v", taskInfo.TaskID, taskInfo.IncrementId, timeToScnSQL, queryErr)
		return "", "", queryErr
	}
	scnValue := ret[0]["SCN_VALUE"]
	timeValue := inputVal
	return scnValue, timeValue, nil
}

func (i *DsgTaskHelper) ParseSCNToTimestamp(ctx context.Context, inputVal string, sourceConns *oracle.Oracle, taskInfo *task.Task) (string, string, error) {
	var scnToTimeSQL = `SELECT TO_CHAR(SCN_TO_TIMESTAMP(${SCN_VALUE}), 'YYYY-MM-DD HH24:MI:SS') AS TIME_VALUE FROM DUAL`
	scnToTimeSQL = strings.ReplaceAll(scnToTimeSQL, "${SCN_VALUE}", inputVal)
	_, ret, queryErr := oracle.Query(ctx, sourceConns.OracleDB, scnToTimeSQL)
	if queryErr != nil {
		log.Errorf("ParseSCNToTimestamp, query scn to time failed, taskId:%d, dsgTaskId:%d, sql:%s, err: %v", taskInfo.TaskID, taskInfo.IncrementId, scnToTimeSQL, queryErr)
		return "", "", queryErr
	}
	scnValue := inputVal
	timeValue := ret[0]["TIME_VALUE"]
	return scnValue, timeValue, nil
}

func (i *DsgTaskHelper) SaveInstallParam(ctx context.Context, dsgTaskName string) {
	var installParam increment.IncrementInstallParam
	installParam.TaskId = i.TaskId
	installParam.DsgTaskName = dsgTaskName
	installParam.SourceIP = i.SourceHost.HostIP
	installParam.TargetIP = i.TargetHost.HostIP

	_, createErr := models.GetIncrementReaderWriter().CreateOrUpdateIncrementInstallParam(ctx, &installParam)

	if createErr != nil {
		i.err = createErr
		log.Errorf("SaveInstallParam failed, taskId:%d, err: %v", i.TaskId, createErr)
	}
}

func (i *DsgTaskHelper) ValidateDataSourceWithSourceIP(ctx context.Context, sourceDS *datasource.Datasource, sourceDeployIP string) error {
	if sourceDS.LogFileStoreMode != "F" {
		return nil
	}
	if sourceDS.HostIp != sourceDeployIP {
		return errors.NewError(errors.TMS_DSG_SOURCE_IP_NOT_MATCH, sourceDS.HostIp+"!="+sourceDeployIP)
	}
	return nil
}

func (i *DsgTaskHelper) GetOracleLogGroups(ctx context.Context, conns *migrationpkg.MigrationSourceDBConns) (map[structs.SchemaTablePair]*OracleLogGroup, error) {

	const sql = `SELECT a.owner,a.table_name,a.log_group_name,a.log_group_type FROM ALL_LOG_GROUPS a`

	lgs := make(map[structs.SchemaTablePair]*OracleLogGroup)
	_, res, err := oracle.Query(ctx, conns.GetSourceDB().OracleDB, sql)
	if err != nil {
		return lgs, err
	}
	for _, r := range res {
		st := structs.SchemaTablePair{
			SchemaName: r["OWNER"],
			TableName:  r["TABLE_NAME"],
		}
		if _, ok := lgs[st]; !ok {
			lgs[st] = &OracleLogGroup{
				SchemaName: r["OWNER"],
				TableName:  r["TABLE_NAME"],
			}
		}

		switch r["LOG_GROUP_TYPE"] {
		case "ALL COLUMN LOGGING":
			lgs[st].HasAllColumnLogging = true
		case "PRIMARY KEY LOGGING":
			lgs[st].HasPrimaryKeyLogging = true
		case "UNIQUE KEY LOGGING":
			lgs[st].HasUniqueKeyLogging = true
		case "FOREIGN KEY LOGGING":
			lgs[st].HasForeignKeyLogging = true
		}
	}
	return lgs, nil
}

func (i *DsgTaskHelper) GetOracleSupplementalLogVariables(ctx context.Context, conns *migrationpkg.MigrationSourceDBConns) (SupplementalLogVariables, error) {
	const sql = `SELECT FORCE_LOGGING,SUPPLEMENTAL_LOG_DATA_MIN, SUPPLEMENTAL_LOG_DATA_PK, SUPPLEMENTAL_LOG_DATA_UI, SUPPLEMENTAL_LOG_DATA_FK, SUPPLEMENTAL_LOG_DATA_ALL FROM V$DATABASE`
	var supplementalLogVariables SupplementalLogVariables

	_, res, err := oracle.Query(ctx, conns.GetSourceDB().OracleDB, sql)
	if err != nil {
		return supplementalLogVariables, err
	}
	if len(res) == 0 {
		return supplementalLogVariables, nil
	}
	supplementalLogVariables.ForceLogging, _ = strconv.ParseBool(res[0]["FORCE_LOGGING"])
	supplementalLogVariables.SupplementalLogDataMin, _ = strconv.ParseBool(res[0]["SUPPLEMENTAL_LOG_DATA_MIN"])
	supplementalLogVariables.SupplementalLogDataPk, _ = strconv.ParseBool(res[0]["SUPPLEMENTAL_LOG_DATA_PK"])
	supplementalLogVariables.SupplementalLogDataUi, _ = strconv.ParseBool(res[0]["SUPPLEMENTAL_LOG_DATA_UI"])
	supplementalLogVariables.SupplementalLogDataFk, _ = strconv.ParseBool(res[0]["SUPPLEMENTAL_LOG_DATA_FK"])
	supplementalLogVariables.SupplementalLogDataAll, _ = strconv.ParseBool(res[0]["SUPPLEMENTAL_LOG_DATA_ALL"])

	return supplementalLogVariables, err
}

func (i *DsgTaskHelper) GetOracleDBPSViews(ctx context.Context, conns *migrationpkg.MigrationSourceDBConns) (DBPSView, error) {
	const sql = `SELECT OWNER,OBJECT_NAME FROM ALL_OBJECTS WHERE OBJECT_TYPE='VIEW' AND STATUS='VALID' AND OBJECT_NAME LIKE '%DBPS%'`
	var dbpsView DBPSView

	_, res, err := oracle.Query(ctx, conns.GetSourceDB().OracleDB, sql)
	if err != nil {
		return dbpsView, err
	}
	if len(res) == 0 {
		return dbpsView, nil
	}
	for _, r := range res {
		objectName := r["OBJECT_NAME"]
		switch objectName {
		case "DBPS_XKCCCF":
			dbpsView.DBPS_XKCCCF = true
		case "DBPS_XKCCLE":
			dbpsView.DBPS_XKCCLE = true
		case "DBPS_XKCCLH":
			dbpsView.DBPS_XKCCLH = true
		case "DBPS_XKCCCP":
			dbpsView.DBPS_XKCCCP = true
		case "DBPS_XKCCDI":
			dbpsView.DBPS_XKCCDI = true
		case "DBPS_XKCRMF":
			dbpsView.DBPS_XKCRMF = true
		case "DBPS_XKTUXE":
			dbpsView.DBPS_XKTUXE = true
		case "DBPS_XLE":
			dbpsView.DBPS_XLE = true
		case "DBPS_XBH":
			dbpsView.DBPS_XBH = true
		}
	}

	return dbpsView, err
}

func (i *DsgTaskHelper) GetMissingOracleSupplementalLogTables(tables []message.DsgTable, groups map[structs.SchemaTablePair]*OracleLogGroup) []message.DsgTable {
	var missingTables []message.DsgTable
	for _, t := range tables {
		st := structs.SchemaTablePair{
			SchemaName: t.SchemaNameS,
			TableName:  t.TableNameS,
		}
		if tableLogInfo, ok := groups[st]; !ok {
			missingTables = append(missingTables, t)
		} else {
			if tableLogInfo.IsSupplementalLogValid() {
				continue
			} else {
				missingTables = append(missingTables, t)
			}
		}
	}
	return missingTables
}

func parseDsgTime(timeVal string, loc *time.Location) time.Time {
	t, err := time.ParseInLocation(constants.TIME_FORMAT, timeVal, loc)
	if err != nil {
		return timeutil.GetTMSNullTime()
	}
	return t
}

func buildYLoaderFromForm(yloaderForm dsg.TargetDataSourceIni) message.YLoader {
	return message.YLoader{
		RealDispFwait:              yloaderForm.RealDispFwait,
		FullRepairRealQuit:         yloaderForm.FullRepairRealQuit,
		UsedMap:                    yloaderForm.UsedMap,
		DelayInterval:              yloaderForm.DelayInterval,
		RealDispSize:               yloaderForm.RealDispSize,
		FullCopyUnusedSize:         yloaderForm.FullCopyUnusedSize,
		UpdatePkukCol:              yloaderForm.UpdatePkukCol,
		WaitFreeSize:               yloaderForm.WaitFreeSize,
		WaitRetry:                  yloaderForm.WaitRetry,
		SQLTrans:                   yloaderForm.SQLTrans,
		RealInsertErrFdelete:       yloaderForm.RealInsertErrFdelete,
		ErrorRetryDdl:              yloaderForm.ErrorRetryDdl,
		TableCreateFullIdxEnd:      yloaderForm.TableCreateFullIdxEnd,
		AddcolRowidInvisible:       yloaderForm.AddcolRowidInvisible,
		ErrorRetry:                 yloaderForm.ErrorRetry,
		FilterString00:             yloaderForm.FilterString00,
		PackForJava:                yloaderForm.PackForJava,
		TableExistsFullDo:          yloaderForm.TableExistsFullDo,
		TableExistsRealDo:          yloaderForm.TableExistsRealDo,
		DbLang:                     yloaderForm.DbLang,
		RealLoadCompleted:          yloaderForm.RealLoadCompleted,
		RealDispFcount:             yloaderForm.RealDispFcount,
		CountsumDetailDb:           yloaderForm.CountsumDetailDb,
		FullInsertErrFdelete:       yloaderForm.FullInsertErrFdelete,
		FullCopy:                   yloaderForm.FullCopy,
		FullSingleParr:             yloaderForm.FullSingleParr,
		CountsumIntervalDb:         yloaderForm.CountsumIntervalDb,
		RealDispMode:               yloaderForm.RealDispMode,
		CtypeIfxDecimal:            yloaderForm.CtypeIfxDecimal,
		FullCindexErrorRetry:       yloaderForm.FullCindexErrorRetry,
		CreateTableFcol:            yloaderForm.CreateTableFcol,
		SesBlen:                    yloaderForm.SesBlen,
		DbName:                     yloaderForm.DbName,
		XsqlRows:                   yloaderForm.XsqlRows,
		FullLoadCompleted:          yloaderForm.FullLoadCompleted,
		MaxBrows:                   yloaderForm.MaxBrows,
		RealDdlFtable:              yloaderForm.RealDdlFtable,
		MapCname:                   yloaderForm.MapCname,
		TableCreateRealConstraint:  yloaderForm.TableCreateRealConstraint,
		RowmapType:                 yloaderForm.RowmapType,
		TableCreateFullIndex:       yloaderForm.TableCreateFullIndex,
		MapTname:                   yloaderForm.MapTname,
		Trans:                      yloaderForm.Trans,
		DbType:                     yloaderForm.DbType,
		SQLErrChange2Bind:          yloaderForm.SQLErrChange2Bind,
		CtypeNumberAdjust:          yloaderForm.CtypeNumberAdjust,
		RealThread:                 yloaderForm.RealThread,
		RealDispRows:               yloaderForm.RealDispRows,
		TableCreateRealIndex:       yloaderForm.TableCreateRealIndex,
		LangGbk2Gb18030:            yloaderForm.LangGbk2Gb18030,
		CountsumTime:               yloaderForm.CountsumTime,
		DbPwd:                      yloaderForm.DbPwd,
		CountsumDetail:             yloaderForm.CountsumDetail,
		TableCreateFullConstraint:  yloaderForm.TableCreateFullConstraint,
		UsedQuotes:                 yloaderForm.UsedQuotes,
		FullInsertFdelete:          yloaderForm.FullInsertFdelete,
		DbHost:                     yloaderForm.DbHost,
		TableCreateFullIndexThread: yloaderForm.TableCreateFullIndexThread,
		Map:                        yloaderForm.Map,
		DataFormat:                 yloaderForm.DataFormat,
		RealDispFi:                 yloaderForm.RealDispFi,
		FullThread:                 yloaderForm.FullThread,
		EncryptPwd:                 yloaderForm.EncryptPwd,
		FullSQLMode:                yloaderForm.FullSQLMode,
		FilterString:               yloaderForm.FilterString,
		TableCreateFullIdxPar:      yloaderForm.TableCreateFullIdxPar,
		UpdateNobefore:             yloaderForm.UpdateNobefore,
		TableExistsCheck:           yloaderForm.TableExistsCheck,
		Home:                       yloaderForm.Home,
		TableCreateReal:            yloaderForm.TableCreateReal,
		QueueName:                  yloaderForm.QueueName,
		OutputLog2Db:               yloaderForm.OutputLog2Db,
		DefineConfig:               yloaderForm.DefineConfig,
		Service:                    yloaderForm.Service,
		FullCopySize:               yloaderForm.FullCopySize,
		DbUser:                     yloaderForm.DbUser,
		TableCreateFull:            yloaderForm.TableCreateFull,
		CfgFilename:                yloaderForm.CfgFilename,
		CreateTableAddcol:          yloaderForm.CreateTableAddcol,
		RealUpdateFobjn:            yloaderForm.RealUpdateFobjn,
		RealInsertFobjn:            yloaderForm.RealInsertFobjn,
		IdbLang:                    yloaderForm.IdbLang,
		OutputRba:                  yloaderForm.OutputRba,
		McSchema:                   yloaderForm.McSchema,
		CountsumTotalDb:            yloaderForm.CountsumTotalDb,
		CountsumFullDb:             yloaderForm.CountsumFullDb,
		UpdateUsedDi:               yloaderForm.UpdateUsedDi,
		RealInsertFdMode:           yloaderForm.RealInsertFdMode,
		RealInsertFdelete:          yloaderForm.RealInsertFdelete,
		UpdateErrUsedDi:            yloaderForm.UpdateErrUsedDi,
		AddcolRowidColumn:          yloaderForm.AddcolRowidColumn,
		AddcolRowid2Uk:             yloaderForm.AddcolRowid2Uk,
		UpdateFilterCol:            yloaderForm.UpdateFilterCol,
		UnusedPkuk:                 yloaderForm.UnusedPkuk,
		FullLangFromForce:          yloaderForm.FullLangFromForce,
		CtypeVarchar2Binary:        yloaderForm.CtypeVarchar2Binary,
		CtypeVarchar2Long:          yloaderForm.CtypeVarchar2Long,
		TableCreateIncPk:           yloaderForm.TableCreateIncPk,
		FullDdlFilter:              yloaderForm.FullDdlFilter,
		RealDdlFobjn:               yloaderForm.RealDdlFobjn,
		RealDeleteFobjn:            yloaderForm.RealDeleteFobjn,
		RealCopy:                   yloaderForm.RealCopy,
		RealSqlMode:                yloaderForm.RealSqlMode,
		FullDdlFtable:              yloaderForm.FullDdlFtable,
		FullInsertFtable:           yloaderForm.FullInsertFtable,
		RealDeleteFtable:           yloaderForm.RealDeleteFtable,
		Ftable:                     yloaderForm.Ftable,
		RealUpdateFtable:           yloaderForm.RealUpdateFtable,
		RealInsertFtable:           yloaderForm.RealInsertFtable,
	}
}
