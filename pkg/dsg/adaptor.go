package dsg

import (
	"context"
	"fmt"
	"net/url"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/go-resty/resty/v2"
)

// TODO 补充logger

const timeFormat = "2006-01-02 15:04:05"

const (
	userAgent      = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
	acceptLanguage = "zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7"
	acceptEncoding = "gzip, deflate"
	acceptCharset  = "utf8"
)

type Adaptor interface {
	SetToken(info string)

	// 调用成功区域
	SelectAllTask(context.Context, SelectAllTaskRequest) (SelectAllTaskResponse, error)
	GetToken(context.Context, GetTokenRequest) (GetTokenResponse, error)
	GetSimulationToken(context.Context, GetTokenRequest) (GetTokenResponse, error)
	ValidateUser(context.Context, ValidateUserRequest) (ValidateUserResponse, error)
	GetModuleList(context.Context, GetModuleListRequest) (GetModuleListResponse, error)
	ListSysUserGroups(context.Context, ListSysUserGroupsRequest) (ListSysUserGroupsResponse, error)
	ListSysUserGroupMap(context.Context, ListSysUserGroupMapRequest) (ListSysUserGroupMapResponse, error)
	QueryCustomMachineGroupInfo(context.Context, QueryCustomMachineGroupInfoRequest) (QueryCustomMachineGroupInfoResponse, error)
	QueryPhysicalSubSystem(context.Context, QueryPhysicalSubSystemRequest) (QueryPhysicalSubSystemResponse, error)
	InsertPhysicalSubSystem(context.Context, InsertPhysicalSubSystemRequest) (InsertPhysicalSubSystemResponse, error)
	GetDataSourceInfo(context.Context, GetDataSourceInfoRequest) (GetDataSourceInfoResponse, error)
	SaveDataSourceInfo(context.Context, SaveDataSourceInfoRequest) (SaveDataSourceInfoResponse, error)
	UpdateASMDataSourceInfo(context.Context, UpdateASMDataSourceInfoRequest) (UpdateASMDataSourceInfoResponse, error)
	ValidateDataSource(context.Context, ValidateDataSourceRequest) (ValidateDataSourceResponse, error)

	InsertNewHostInfo(context.Context, InsertNewHostInfoRequest) (InsertNewHostInfoResponse, error)
	SelectTaskInfoByTaskName(context.Context, SelectTaskInfoByTaskNameRequest) (SelectTaskInfoByTaskNameResponse, error)
	SelectTaskInfoByStatus(context.Context, SelectTaskInfoByStatusRequest) (SelectTaskInfoByStatusResponse, error)
	GetPGMDInstallStatus(context.Context, GetPGMDInstallStatusRequest) (GetPGMDInstallStatusResponse, error)
	SelectDefineMapping(context.Context, SelectDefineMappingRequest) (SelectDefineMappingResponse, error)
	ReadSourceDataSourceIni(context.Context, ReadSourceDataSourceIniRequest) (ReadSourceDataSourceIniResponse, error)
	ReadTargetDataSourceIni(context.Context, ReadTargetDataSourceIniRequest) (ReadTargetDataSourceIniResponse, error)
	SelectCompareDefineMapping(context.Context, SelectCompareDefineMappingRequest) (SelectCompareDefineMappingResponse, error)
	GetCompareSelectedTable(context.Context, GetCompareSelectedTableRequest) (GetCompareSelectedTableResponse, error)
	GetCompareSelectedUser(context.Context, GetCompareSelectedUserRequest) (GetCompareSelectedUserResponse, error)
	GetSelectedUser(context.Context, GetSelectedUserRequest) (GetSelectedUserResponse, error)
	GetUnSelectedUser(context.Context, GetUnSelectedUserRequest) (GetUnSelectedUserResponse, error)
	GetUnSelectedTable(context.Context, GetUnSelectedTableRequest) (GetUnSelectedTableResponse, error)
	GetUnTargetSelectedTable(context.Context, GetUnTargetSelectedTableRequest) (GetUnTargetSelectedTableResponse, error)
	QueryAllHostInfo(context.Context, QueryAllHostInfoRequest) (QueryAllHostInfoResponse, error)
	GetSelectedTable(context.Context, GetSelectedTableRequest) (GetSelectedTableResponse, error)
	SelectTaskInfoByNameAndStatus(context.Context, SelectTaskInfoByNameAndStatusRequest) (SelectTaskInfoByNameAndStatusResponse, error)

	WriteSourceDataSourceIni(context.Context, WriteSourceDataSourceIniRequest) (WriteSourceDataSourceIniResponse, error)
	WriteTargetDataSourceIni(context.Context, WriteTargetDataSourceIniRequest) (WriteTargetDataSourceIniResponse, error)
	WriteKeyIni(context.Context, WriteKeyIniRequest) (WriteKeyIniResponse, error)
	ClearCache(context.Context, ClearCacheRequest) (ClearCacheResponse, error)
	SaveCompareDefineMappingResult(context.Context, SaveCompareDefineMappingResultRequest) (SaveCompareDefineMappingResultResponse, error)
	SaveDefineMapping(context.Context, SaveDefineMappingRequest) (SaveDefineMappingResponse, error)
	DownloadLogFileInfo(context.Context, DownloadLogFileInfoRequest) (DownloadLogFileInfoResponse, error)
	SaveTableMappingResult(context.Context, SaveTableMappingResultRequest) (SaveTableMappingResultResponse, error)
	SaveCompareMappingResult(context.Context, SaveCompareMappingResultRequest) (SaveCompareMappingResultResponse, error)

	StartTask(context.Context, StartTaskRequest) (StartTaskResponse, error)
	CreateVerifyByMappingIni(context.Context, CreateVerifyByMappingIniRequest) (CreateVerifyByMappingIniResponse, error)
	SaveMappingResult(context.Context, SaveMappingResultRequest) (SaveMappingResultResponse, error)
	SaveTaskInfo(context.Context, SaveTaskInfoRequest) (SaveTaskInfoResponse, error)
	DeleteTask(context.Context, DeleteTaskRequest) (DeleteTaskResponse, error)
	StopTask(context.Context, StopTaskRequest) (StopTaskResponse, error)

	VerifyHost(context.Context, VerifyHostRequest) (VerifyHostResponse, error)
	UpdateHostPortInfo(context.Context, UpdateHostPortInfoRequest) (UpdateHostPortInfoResponse, error)
	GetHostPorts(context.Context, GetHostPortsRequest) (GetHostPortsResponse, error)

	OverWriteConfig(context.Context, OverWriteConfigRequest) (OverWriteConfigResponse, error)
	Sync(context.Context, SyncRequest) (SyncResponse, error)

	GetMonitorList(context.Context, GetMonitorListRequest) (GetMonitorListResponse, error)
	GetMonitorDetail(context.Context, GetMonitorDetailRequest) (GetMonitorDetailResponse, error)
	GetTableSyncInfo(context.Context, GetTableSyncInfoRequest) (GetTableSyncInfoResponse, error)
	ListOperateLog(context.Context, ListOperateLogRequest) (ListOperateLogResponse, error)

	GetRealTaskInfo(context.Context, GetRealTaskInfoRequest) (GetRealTaskInfoResponse, error)
	GetTableRealInfo(context.Context, GetTableRealInfoRequest) (GetTableRealInfoResponse, error)
	GetTableDmlDdlInfo(context.Context, GetTableDmlDdlInfoRequest) (GetTableDmlDdlInfoResponse, error)
	GetWarningInfo(context.Context, GetWarningInfoRequest) (GetWarningInfoResponse, error)
	OperationScript(context.Context, OperationScriptRequest) (OperationScriptResponse, error)
}

// NewDsgAdaptor creates a new DSG HTTP API adaptor
func NewDsgAdaptor(config *config.IncrementConfig) (Adaptor, error) {
	dsgUrl0, err := url.Parse(fmt.Sprintf("%s:%d", config.DsgHost, config.DsgPort))
	if err != nil {
		return nil, err
	}

	dsgUrl1, err := url.Parse(fmt.Sprintf("%s:%d", config.DsgHost, config.DsgAutoMaticEngineBootPort))
	if err != nil {
		return nil, err
	}

	client := &adaptor{
		host:  config.DsgHost,
		port1: config.DsgAutoMaticEngineBootPort,
		port0: config.DsgPort,

		dsgUrl:                 dsgUrl0,
		autoMaticEngineBootUrl: dsgUrl1,
		client:                 resty.New(),
	}

	getTokenRsp, err := client.GetToken(context.TODO(), GetTokenRequest{})
	if err != nil {
		return nil, err
	}
	client.SetToken(getTokenRsp.DataInfo)

	return client, nil
}

type adaptor struct {
	host  string
	port0 int
	port1 int
	token string

	dsgUrl                 *url.URL
	autoMaticEngineBootUrl *url.URL
	client                 *resty.Client
}

func (a *adaptor) UpdateHostPortInfo(ctx context.Context, request UpdateHostPortInfoRequest) (UpdateHostPortInfoResponse, error) {
	const api = "/permission/hostAndDataSourceConfig/updateHostPortInfo.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"mac_id":   parse.FormatInt(request.MacID),
		"port_max": parse.FormatInt(request.PortMax),
		"port_min": parse.FormatInt(request.PortMin),
	}

	rsp := UpdateHostPortInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) InsertNewHostInfo(ctx context.Context, request InsertNewHostInfoRequest) (InsertNewHostInfoResponse, error) {
	const api = "permission/hostAndDataSourceConfig/insertNewHostInfo.do"

	formData := map[string]string{
		"tokenID":         a.getToken(),
		"host_ip":         request.HostIP,
		"host_name":       request.HostName,
		"ps_id":           parse.FormatInt(request.PhysicalSubSystemId),
		"os_name":         request.OsName,
		"secret_name":     request.SecretName,
		"aff_data_center": request.AffDataCenter,
	}

	rsp := InsertNewHostInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SaveDataSourceInfo(ctx context.Context, request SaveDataSourceInfoRequest) (SaveDataSourceInfoResponse, error) {
	const api = "permission/hostAndDataSourceConfig/saveDataSourceInfo.do"
	/**
	pgsql_name:
	machine_ip: ***********
	db_type: oracle11g
	is_rac: false
	host_ip: ***********
	host_port: 1521
	db_alias_name:
	db_user: tmsadmin
	db_passwd: tmsadmin
	service_name: utf8
	s3_url:
	srcFilePath:
	link_flag: TNS
	tns_name:
	tns_names_ora_file:
	db_name: ds_pen
	cluster_user:
	cluster_password:
	cluster_url:
	asm_oracle_home:
	oracle_home:
	oracle_sid:
	asm_home:
	nls_lang:
	asm_sid:
	pdb_flag:
	odbc:
	ASM:
	PDB:
	pdb_name:
	pdb_db_name:
	data_center: 南湖
	physical_subsystem: 2
	store_info:
	jdbc_param:
	dsg_proxy_table:
	cache_log_path:
	cache_journal_log:
	http_port:
	dn_name:
	host_list:
	gcluster:
	userID: 1
	is_backup_db: N
	dbInfo_bakup:
	replica_set:
	is_proxy: false
	proxy_cluster:
	asm: true
	db_connection_mode:
	asm_db_ip: ***********
	asm_db_user: tmsadmin
	asm_db_passwd: tmsadmin
	asm_db_port: 1521
	asm_db_name: utf8
	*/

	formData := map[string]string{
		"tokenID":            a.getToken(),
		"pgsql_name":         request.PgsqlName,
		"machine_ip":         request.MachineIP,
		"db_type":            request.DBType,
		"is_rac":             stringutil.BoolToTrueOrFalse(request.IsRac),
		"host_ip":            request.HostIP,
		"host_port":          parse.FormatInt(request.HostPort),
		"db_alias_name":      request.DBAliasName,
		"db_user":            request.DbUser,
		"db_passwd":          request.DbPasswd,
		"service_name":       request.ServiceName,
		"s3_url":             request.S3Url,
		"srcFilePath":        request.SrcFilePath,
		"link_flag":          request.LinkFlag,
		"tns_name":           request.TnsName,
		"tns_names_ora_file": request.TnsNamesOraFile,
		"db_name":            request.DbName,
		"cluster_user":       request.ClusterUser,
		"cluster_password":   request.ClusterPassword,
		"cluster_url":        request.ClusterUrl,
		"asm_oracle_home":    request.AsmOracleHome,
		"oracle_home":        request.OracleHome,
		"oracle_sid":         request.OracleSid,
		"asm_home":           request.AsmHome,
		"nls_lang":           request.NlsLang,
		"asm_sid":            request.AsmSid,
		"pdb_flag":           request.PdbFlag,
		"odbc":               request.Odbc,
		"ASM":                request.AsmName,
		"PDB":                request.Pdb,
		"pdb_name":           request.PdbName,
		"pdb_db_name":        request.PdbDbName,
		"data_center":        request.DataCenter,
		"physical_subsystem": parse.FormatInt(request.PhysicalSubsystem),
		"store_info":         request.StoreInfo,
		"jdbc_param":         request.JdbcParam,
		"dsg_proxy_table":    request.DsgProxyTable,
		"cache_log_path":     request.CacheLogPath,
		"cache_journal_log":  request.CacheJournalLog,
		"http_port":          request.HttpPort,
		"dn_name":            request.DnName,
		"host_list":          request.HostList,
		"gcluster":           request.Gcluster,
		"userID":             parse.FormatInt(request.UserId),
		"is_backup_db":       request.IsBackupDb,
		"dbInfo_bakup":       request.DbInfoBakup,
		"replica_set":        request.ReplicaSet,
		"is_proxy":           stringutil.BoolToTrueOrFalse(request.IsProxy),
		"proxy_cluster":      request.ProxyCluster,
		"asm":                stringutil.BoolToTrueOrFalse(request.Asm),
		"db_connection_mode": request.DbConnectionMode,
		"asm_db_ip":          request.AsmDbIp,
		"asm_db_user":        request.AsmDbUser,
		"asm_db_passwd":      request.AsmDbPasswd,
		"asm_db_port":        parse.FormatInt(request.AsmDbPort),
		"asm_db_name":        request.AsmDbName,
	}

	rsp := SaveDataSourceInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}

	if rsp.DataInfo != 1 {
		return rsp, errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "创建数据源"+request.DbName+"失败")
	}

	return rsp, nil
}

/*
*
db_id: 35
machine_ip: ***********
db_type: oracle11g
is_rac: false
host_ip: ***********
host_port: 1522
db_user: soe
db_passwd: soe
service_name: orcl
link_flag: TNS
db_name: 527_7629_Ora-33-1522-ASM_63F62A94
asm_home: /home/<USER>/tms-sub-system/tms_527_7629/ds
data_center: TMS
physical_subsystem: 3
userID: 1
is_backup_db: N
is_proxy: false
asm: true
asm_db_ip: ***********
asm_db_user: sys
asm_db_passwd: oracle
asm_db_port: 1522
asm_db_name: +ASM
tokenID: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4kFP3GAK5e4387tQwg0OqGIegnhRpQ_A5Iz8lGvP5NI
*/
func (a *adaptor) UpdateASMDataSourceInfo(ctx context.Context, request UpdateASMDataSourceInfoRequest) (UpdateASMDataSourceInfoResponse, error) {
	const api = "permission/hostAndDataSourceConfig/updateDataSourceInfo.do"

	formData := map[string]string{
		"db_id":     parse.FormatInt(request.DBId),
		"db_type":   request.DBType,
		"db_user":   request.DbUser,
		"db_passwd": request.DbPasswd,
		"db_name":   request.DbName,

		"host_ip":   request.HostIP,
		"host_port": parse.FormatInt(request.HostPort),

		"service_name": request.ServiceName,
		"link_flag":    request.LinkFlag,
		"asm_home":     request.AsmHome,
		"data_center":  request.DataCenter,

		"physical_subsystem": parse.FormatInt(request.PhysicalSubsystem),
		"userID":             parse.FormatInt(request.UserId),

		"is_rac":       stringutil.BoolToTrueOrFalse(request.IsRac),
		"is_backup_db": request.IsBackupDb,
		"is_proxy":     stringutil.BoolToTrueOrFalse(request.IsProxy),

		"asm":           stringutil.BoolToTrueOrFalse(request.Asm),
		"asm_db_ip":     request.AsmDbIp,
		"asm_db_user":   request.AsmDbUser,
		"asm_db_passwd": request.AsmDbPasswd,
		"asm_db_port":   parse.FormatInt(request.AsmDbPort),
		"asm_db_name":   request.AsmDbName,

		"machine_ip": request.MachineIP,
		"tokenID":    a.getToken(),
	}

	rsp := UpdateASMDataSourceInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}

	if rsp.DataInfo != 1 {
		return rsp, errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "更新数据源"+request.DbName+"失败")
	}

	return rsp, nil
}

func (a *adaptor) ValidateDataSource(ctx context.Context, request ValidateDataSourceRequest) (ValidateDataSourceResponse, error) {
	const api = "permission/hostAndDataSourceConfig/validatorDataSource.do"

	formData := map[string]string{
		"tokenID":            a.getToken(),
		"pgsql_name":         request.PgsqlName,
		"machine_ip":         request.MachineIP,
		"db_type":            request.DBType,
		"is_rac":             stringutil.BoolToTrueOrFalse(request.IsRac),
		"host_ip":            request.HostIP,
		"host_port":          parse.FormatInt(request.HostPort),
		"db_alias_name":      request.DBAliasName,
		"db_user":            request.DbUser,
		"db_passwd":          request.DbPasswd,
		"service_name":       request.ServiceName,
		"s3_url":             request.S3Url,
		"srcFilePath":        request.SrcFilePath,
		"link_flag":          request.LinkFlag,
		"tns_name":           request.TnsName,
		"tns_names_ora_file": request.TnsNamesOraFile,
		"db_name":            request.DbName,
		"cluster_user":       request.ClusterUser,
		"cluster_password":   request.ClusterPassword,
		"cluster_url":        request.ClusterUrl,
		"asm_oracle_home":    request.AsmOracleHome,
		"oracle_home":        request.OracleHome,
		"oracle_sid":         request.OracleSid,
		"asm_home":           request.AsmHome,
		"nls_lang":           request.NlsLang,
		"asm_sid":            request.AsmSid,
		"pdb_flag":           request.PdbFlag,
		"odbc":               request.Odbc,
		"ASM":                request.AsmName,
		"PDB":                request.Pdb,
		"pdb_name":           request.PdbName,
		"pdb_db_name":        request.PdbDbName,
		"data_center":        request.DataCenter,
		"physical_subsystem": parse.FormatInt(request.PhysicalSubsystem),
		"store_info":         request.StoreInfo,
		"jdbc_param":         request.JdbcParam,
		"dsg_proxy_table":    request.DsgProxyTable,
		"cache_log_path":     request.CacheLogPath,
		"cache_journal_log":  request.CacheJournalLog,
		"http_port":          request.HttpPort,
		"dn_name":            request.DnName,
		"host_list":          request.HostList,
		"gcluster":           request.Gcluster,
		"userID":             parse.FormatInt(request.UserId),
		"is_backup_db":       request.IsBackupDb,
		"dbInfo_bakup":       request.DbInfoBakup,
		"replica_set":        request.ReplicaSet,
		"is_proxy":           stringutil.BoolToTrueOrFalse(request.IsProxy),
		"proxy_cluster":      request.ProxyCluster,
		"asm":                stringutil.BoolToTrueOrFalse(request.Asm),
		"db_connection_mode": request.DbConnectionMode,
		"asm_db_ip":          request.AsmDbIp,
		"asm_db_user":        request.AsmDbUser,
		"asm_db_passwd":      request.AsmDbPasswd,
		"asm_db_port":        parse.FormatInt(request.AsmDbPort),
		"asm_db_name":        request.AsmDbName,
	}

	rsp := ValidateDataSourceResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}

	if !rsp.DataInfo.FlagAsm {
		return rsp, errors.NewError(errors.TMS_DSG_CREATE_DATASOURCE_FAILED, "校验数据源"+request.DbName+"失败")
	}

	return rsp, nil
}

func (a *adaptor) InsertPhysicalSubSystem(ctx context.Context, request InsertPhysicalSubSystemRequest) (InsertPhysicalSubSystemResponse, error) {
	const api = "permission/hostAndDataSourceConfig/insertPhysicalSubsystem.do"

	formData := map[string]string{
		"tokenID":            a.getToken(),
		"group_machine_name": request.GroupMachineName,
		"group_machine_desc": request.GroupMachineDesc,
		"user_id":            parse.FormatInt(request.UserID),
	}

	rsp := InsertPhysicalSubSystemResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) VerifyHost(ctx context.Context, request VerifyHostRequest) (VerifyHostResponse, error) {
	/**
	aff_data_center: 稻香湖
	host_ip: ***********         --  必填
	host_name: vm10-2-103-15
	ps_id: 2
	os_name: linux
	secret_name: /home/<USER>
	mac_id: 3
	*/

	const api = "/permission/hostAndDataSourceConfig/verifyHost.do"

	formData := map[string]string{
		"tokenID":         a.getToken(),
		"aff_data_center": request.AffDataCenter,
		"host_ip":         request.HostIP,
		"host_name":       request.HostName,
		"ps_id":           parse.FormatInt(request.PSID),
		"os_name":         request.OSName,
		"secret_name":     request.SecretName,
		"mac_id":          parse.FormatInt(request.MacID),
	}

	rsp := VerifyHostResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetTableSyncInfo(ctx context.Context, request GetTableSyncInfoRequest) (GetTableSyncInfoResponse, error) {
	const api = "autoMaticEngineBoot/monitorController/getTableSyncInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
		"limit":   parse.FormatInt(request.Limit),
		"offset":  parse.FormatInt(request.Offset),
		"userID":  parse.FormatInt(request.UserID),
	}

	rsp := GetTableSyncInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetDsgURL() *url.URL {
	return a.dsgUrl
}

func (a *adaptor) GetAutoMaticEngineBootURL() *url.URL {
	return a.autoMaticEngineBootUrl
}

//
//func GenerateToken() (string, error) {
//
//	subMap := map[string]any{
//		"fingerprint": userAgent + acceptLanguage + acceptEncoding + acceptCharset,
//		"ip":          "************",
//		"userID":      1,
//		"username":    "admin",
//	}
//	subMapBytes, _ := json.Marshal(&subMap)
//
//	iat := time.Now().Unix() + 1 // seconds
//
//	claims := jwt.MapClaims{
//		"sub":      string(subMapBytes),
//		"iat":      iat,
//		"jti":      jwtID,
//		"username": "admin",
//	}
//
//	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
//
//	base64JwtKey, _ := base64.RawStdEncoding.DecodeString(jwtKey) // 价值一天工作量的一行代码
//
//	signedToken, err := token.SignedString(base64JwtKey)
//	if err != nil {
//		return "", err
//	}
//	return signedToken, nil
//}

func (a *adaptor) Sync(ctx context.Context, request SyncRequest) (SyncResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/sync.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := SyncResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetToken(ctx context.Context, request GetTokenRequest) (GetTokenResponse, error) {
	const api = "permission/serviceByUserInfo/getToken.do"

	formData := map[string]string{}

	rsp := GetTokenResponse{}
	log.Debugf("GetToken url:%v, api:%s, formData:%v", a.GetDsgURL(), api, formData)
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		log.Debugf("GetToken error: %v", err)
		return rsp, err
	}
	log.Debugf("GetToken url:%v, api:%s, formData:%v, response: %v", a.GetDsgURL(), api, formData, rsp)
	return rsp, nil
}

func (a *adaptor) GetSimulationToken(ctx context.Context, request GetTokenRequest) (GetTokenResponse, error) {
	const api = "permission/serviceByUserInfo/getToken.do"

	formData := map[string]string{}

	header := map[string]string{
		"User-Agent":      request.UserAgent,
		"Accept-Language": request.AcceptLanguage,
		"Accept-Encoding": request.AcceptEncoding,
		"Accept-Charset":  request.AcceptCharset,
	}

	rsp := GetTokenResponse{}
	err := a.postFormDataWithHeader(ctx, a.GetDsgURL(), api, formData, &rsp, header)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) DeleteTask(ctx context.Context, request DeleteTaskRequest) (DeleteTaskResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/delete.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := DeleteTaskResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SelectAllTask(ctx context.Context, request SelectAllTaskRequest) (SelectAllTaskResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/selectAllTaskInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"offset":  parse.FormatInt(request.Offset),
		"limit":   parse.FormatInt(request.Limit),
		"userId":  parse.FormatInt(request.UserID),
	}
	if request.TID != 0 {
		formData["tId"] = parse.FormatInt(request.TID)
	}
	if request.TaskName != "" {
		formData["taskName"] = request.TaskName
	}

	rsp := SelectAllTaskResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) ClearCache(ctx context.Context, request ClearCacheRequest) (ClearCacheResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/clearCache.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
		"offset":  parse.FormatInt(request.Offset),
		"limit":   parse.FormatInt(request.Limit),
	}

	rsp := ClearCacheResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

// OverWriteConfig 修改 全量增量配置 下拉框
func (a *adaptor) OverWriteConfig(ctx context.Context, request OverWriteConfigRequest) (OverWriteConfigResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/overwrite.do"

	formData := map[string]string{
		"tokenID":    a.getToken(),
		"tId":        parse.FormatInt(request.TID),
		"offset":     parse.FormatInt(request.Offset),
		"limit":      parse.FormatInt(request.Limit),
		"taskMethod": parse.FormatInt(int(request.TaskMethod)),
	}

	rsp := OverWriteConfigResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) StartTask(ctx context.Context, request StartTaskRequest) (StartTaskResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/start.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := StartTaskResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) StopTask(ctx context.Context, request StopTaskRequest) (StopTaskResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/stop.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := StopTaskResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

// CreateVerifyByMappingIni 根据根据mapping配置生成verify.ini
func (a *adaptor) CreateVerifyByMappingIni(ctx context.Context, request CreateVerifyByMappingIniRequest) (CreateVerifyByMappingIniResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/createVerifyByMappingInI.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := CreateVerifyByMappingIniResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SelectCompareDefineMapping(ctx context.Context, request SelectCompareDefineMappingRequest) (SelectCompareDefineMappingResponse, error) {
	const api = "autoMaticEngineBoot/comparisonTaskController/selectCpDefineMappingInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := SelectCompareDefineMappingResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetCompareSelectedTable(ctx context.Context, request GetCompareSelectedTableRequest) (GetCompareSelectedTableResponse, error) {
	const api = "autoMaticEngineBoot/comparisonTaskController/getCpSelectedTable.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := GetCompareSelectedTableResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetCompareSelectedUser(ctx context.Context, request GetCompareSelectedUserRequest) (GetCompareSelectedUserResponse, error) {
	const api = "autoMaticEngineBoot/comparisonTaskController/getCpSelectedUser.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := GetCompareSelectedUserResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SaveCompareMappingResult(ctx context.Context, request SaveCompareMappingResultRequest) (SaveCompareMappingResultResponse, error) {
	const api = "/autoMaticEngineBoot/comparisonTaskController/saveCpMappingResultInfo.do"

	formData := map[string]string{
		"tokenID":     a.getToken(),
		"tId":         parse.FormatInt(request.TID),
		"mappingType": parse.FormatInt(request.MappingType),
		"rows":        request.Rows,
	}

	rsp := SaveCompareMappingResultResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SaveCompareDefineMappingResult(ctx context.Context, request SaveCompareDefineMappingResultRequest) (SaveCompareDefineMappingResultResponse, error) {
	const api = "autoMaticEngineBoot/comparisonTaskController/saveCpDefineMappingResultInfo.do"

	formData := map[string]string{
		"tId":     parse.FormatInt(request.TID),
		"real":    request.Real,
		"map":     request.Map,
		"full":    request.Full,
		"tokenID": a.getToken(),
	}

	rsp := SaveCompareDefineMappingResultResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SaveTableMappingResult(ctx context.Context, request SaveTableMappingResultRequest) (SaveTableMappingResultResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/saveTableMappingResultInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"rows":    request.Rows,
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := SaveTableMappingResultResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

// SaveDefineMapping 2.6 用户保存mapping配置结果数据
func (a *adaptor) SaveMappingResult(ctx context.Context, request SaveMappingResultRequest) (SaveMappingResultResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/saveMappingResultInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"rows":    request.Rows,
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := SaveMappingResultResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetUnTargetSelectedTable(ctx context.Context, request GetUnTargetSelectedTableRequest) (GetUnTargetSelectedTableResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/getUnTargetSelectedTable.do"

	formData := map[string]string{
		"tokenID":    a.getToken(),
		"tId":        parse.FormatInt(request.TID),
		"offset":     parse.FormatInt(request.Offset),
		"limit":      parse.FormatInt(request.Limit),
		"schemaName": request.SchemaName,
	}

	rsp := GetUnTargetSelectedTableResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetUnSelectedTable(ctx context.Context, request GetUnSelectedTableRequest) (GetUnSelectedTableResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/getUnSelectedTable.do"

	formData := map[string]string{
		"tokenID":    a.getToken(),
		"tId":        parse.FormatInt(request.TID),
		"offset":     parse.FormatInt(request.Offset),
		"limit":      parse.FormatInt(request.Limit),
		"schemaName": request.SchemaName,
	}

	rsp := GetUnSelectedTableResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetSelectedTable(ctx context.Context, request GetSelectedTableRequest) (GetSelectedTableResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/getSelectedTable.do"

	formData := map[string]string{
		"tokenID":    a.getToken(),
		"tId":        parse.FormatInt(request.TID),
		"offset":     parse.FormatInt(request.Offset),
		"limit":      parse.FormatInt(request.Limit),
		"schemaName": request.SchemaName,
	}

	rsp := GetSelectedTableResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetUnSelectedUser(ctx context.Context, request GetUnSelectedUserRequest) (GetUnSelectedUserResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/getUnSelectedUser.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
		"offset":  parse.FormatInt(request.Offset),
		"limit":   parse.FormatInt(request.Limit),
	}

	rsp := GetUnSelectedUserResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetSelectedUser(ctx context.Context, request GetSelectedUserRequest) (GetSelectedUserResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/getSelectedUser.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"tId":     parse.FormatInt(request.TID),
		"offset":  parse.FormatInt(request.Offset),
		"limit":   parse.FormatInt(request.Limit),
	}

	rsp := GetSelectedUserResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetModuleList(ctx context.Context, request GetModuleListRequest) (GetModuleListResponse, error) {
	const api = "permission/roleController/moduleList.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
	}

	rsp := GetModuleListResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetMonitorDetail(ctx context.Context, request GetMonitorDetailRequest) (GetMonitorDetailResponse, error) {
	const api = "autoMaticEngineBoot/monitorController/getMonitorDetail.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"userID":  parse.FormatInt(request.UserId),
		"tId":     parse.FormatInt(request.TID),
	}

	rsp := GetMonitorDetailResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetMonitorList(ctx context.Context, request GetMonitorListRequest) (GetMonitorListResponse, error) {
	const api = "autoMaticEngineBoot/monitorController/getMonitorList.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"userID":  parse.FormatInt(request.UserId),
		"tId":     parse.FormatInt(request.TID),
		"offset":  parse.FormatInt(request.Offset),
		"limit":   parse.FormatInt(request.Limit),
	}

	rsp := GetMonitorListResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) delToken(formData map[string]string) {
	delete(formData, "Token")
	delete(formData, "tokenID")
}

func (a *adaptor) postFormData(ctx context.Context, dsgUrl *url.URL, api string, formData map[string]string, rsp ResponseInterface) error {
	dsgUrl.Path = api
	uri := dsgUrl.String()

	httpResponse, err := a.client.R().
		SetContext(ctx).
		SetFormData(formData).
		SetHeader("Token", a.getToken()).
		SetHeader("User-Agent", userAgent).
		SetHeader("Accept-Language", acceptLanguage).
		SetHeader("Accept-Encoding", acceptEncoding).
		SetHeader("Accept-Charset", acceptCharset).
		SetResult(rsp).
		SetError(rsp).
		Post(uri)

	a.delToken(formData)

	if err != nil {
		log.Errorf("postFormData failed, api:%s, formData:%v, err:%v", api, formData, err)
		return err
	}
	log.Infof("postFormData success, api:%s, formData :%v, response: %s", api, formData, httpResponse.String())

	if httpResponse.StatusCode() != 200 {
		err = fmt.Errorf("postFormData failed, api:%s, statusCode: %d, formData:%v", api, httpResponse.StatusCode(), formData)
		return err
	}
	err = rsp.Validate()
	if err != nil {
		log.Errorf("postFormData success but validate failed, api:%s, formData:%v, err:%v", api, formData, err)
		return err
	}
	return nil
}

func (a *adaptor) postFormDataWithHeader(ctx context.Context, dsgUrl *url.URL, api string, formData map[string]string, rsp ResponseInterface, headers map[string]string) error {
	dsgUrl.Path = api
	uri := dsgUrl.String()

	client := a.client.R()

	// 提前setHeader，因为下面的循环可能替换掉原有的header
	client.SetHeader("User-Agent", userAgent).
		SetHeader("Accept-Language", acceptLanguage).
		SetHeader("Accept-Encoding", acceptEncoding).
		SetHeader("Accept-Charset", acceptCharset)

	for k, v := range headers {
		client.SetHeader(k, v)
	}

	httpResponse, err := client.
		SetContext(ctx).
		SetFormData(formData).
		SetHeader("Token", a.getToken()).
		SetResult(rsp).
		SetError(rsp).
		Post(uri)
	a.delToken(formData)
	if err != nil {
		log.Errorf("postFormData failed, api:%s, formData:%v, err:%v", api, formData, err)
		return err
	}
	log.Infof("postFormDataWithHeader success, api:%s, formData :%v, response: %s", api, formData, httpResponse.String())
	if httpResponse.StatusCode() != 200 {
		err = fmt.Errorf("postFormData failed, api:%s, statusCode: %d, formData:%v", api, httpResponse.StatusCode(), formData)
		return err
	}
	err = rsp.Validate()
	if err != nil {
		log.Errorf("postFormData success but validate failed, api:%s, formData:%v, err:%v", api, formData, err)
		return err
	}
	return nil
}

func (a *adaptor) ValidateUser(ctx context.Context, req ValidateUserRequest) (ValidateUserResponse, error) {
	const api = "permission/serviceByUserInfo/validateUser.do"

	formData := map[string]string{
		"account":      req.Account,
		"pwd":          req.Password,
		"tokenID":      "0",
		"type":         "",
		"encryptOrNot": req.EncryptOrNot,
	}

	timestampHeader := map[string]string{
		"timestamp": "*************",
		"Token":     "0",
	}
	rsp := ValidateUserResponse{}
	err := a.postFormDataWithHeader(ctx, a.GetDsgURL(), api, formData, &rsp, timestampHeader)
	if err != nil {
		return rsp, err
	}

	return rsp, nil
}

func (a *adaptor) WriteKeyIni(ctx context.Context, req WriteKeyIniRequest) (WriteKeyIniResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/writeKeyIni.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"fileName": req.FileName,
		"tId":      parse.FormatInt(req.TID),
		"form":     req.Form, // form字段是之前的配置，仅做快照备份用，不做校验
		"content":  req.Content,
	}

	rsp := WriteKeyIniResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) WriteSourceDataSourceIni(ctx context.Context, req WriteSourceDataSourceIniRequest) (WriteSourceDataSourceIniResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/writeDsIni.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"fileName": req.FileName,
		"tId":      parse.FormatInt(req.TID),
		"content":  req.Content,
	}

	rsp := WriteSourceDataSourceIniResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) WriteTargetDataSourceIni(ctx context.Context, req WriteTargetDataSourceIniRequest) (WriteTargetDataSourceIniResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/writeDtIni.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"fileName": req.FileName,
		"tId":      parse.FormatInt(req.TID),
		"content":  req.Content,
	}

	rsp := WriteTargetDataSourceIniResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) ReadSourceDataSourceIni(ctx context.Context, req ReadSourceDataSourceIniRequest) (ReadSourceDataSourceIniResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/readDsIni.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"fileName": req.FileName,
		"tId":      parse.FormatInt(req.TID),
	}

	rsp := ReadSourceDataSourceIniResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) ReadTargetDataSourceIni(ctx context.Context, req ReadTargetDataSourceIniRequest) (ReadTargetDataSourceIniResponse, error) {
	const api = "autoMaticEngineBoot/taskButton/readDtIni.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"fileName": req.FileName,
		"tId":      parse.FormatInt(req.TID),
	}

	rsp := ReadTargetDataSourceIniResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SelectDefineMapping(ctx context.Context, req SelectDefineMappingRequest) (SelectDefineMappingResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/selectDefineMappingInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"offset":  parse.FormatInt(req.Offset),
		"limit":   parse.FormatInt(req.Limit),
		"tId":     parse.FormatInt(req.TID),
	}

	rsp := SelectDefineMappingResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SaveDefineMapping(ctx context.Context, req SaveDefineMappingRequest) (SaveDefineMappingResponse, error) {
	const api = "autoMaticEngineBoot/OpMappingConf/saveDefineMappingResultInfo.do"

	formData := map[string]string{
		"tId":     parse.FormatInt(req.TID),
		"real":    req.Real,
		"map":     req.Map,
		"full":    req.Full,
		"tokenID": a.getToken(),
		//"offset":  parse.FormatInt(req.Offset),
		//"limit":   parse.FormatInt(req.Limit),
	}

	rsp := SaveDefineMappingResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetPGMDInstallStatus(ctx context.Context, req GetPGMDInstallStatusRequest) (GetPGMDInstallStatusResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/getPgmdInstallStatus.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"taskName": req.TaskName,
	}

	rsp := GetPGMDInstallStatusResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SelectTaskInfoByTaskName(ctx context.Context, req SelectTaskInfoByTaskNameRequest) (SelectTaskInfoByTaskNameResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/selectTaskInfoByTname.do"

	formData := map[string]string{
		"tokenID":   a.getToken(),
		"task_name": req.TaskName,
	}

	rsp := SelectTaskInfoByTaskNameResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) DownloadLogFileInfo(ctx context.Context, req DownloadLogFileInfoRequest) (DownloadLogFileInfoResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/downloadLogFileInfo.do"

	formData := map[string]string{
		"filePath": req.FilePath,
		"nodeName": req.NodeName,
		"hostIp":   req.HostIP,
		"type":     req.Type,
		"dataType": req.DataType,
		"fileName": req.FileName,
		"tokenID":  a.getToken(),
	}

	rsp := DownloadLogFileInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SaveTaskInfo(ctx context.Context, req SaveTaskInfoRequest) (SaveTaskInfoResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/saveTaskInfo.do"

	formData := map[string]string{
		"task_name":      req.TaskName,
		"task_parm":      req.TaskParm,
		"install_status": req.InstallStatus,
		"tokenID":        a.getToken(),
	}

	rsp := SaveTaskInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SelectTaskInfoByNameAndStatus(ctx context.Context, req SelectTaskInfoByNameAndStatusRequest) (SelectTaskInfoByNameAndStatusResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/selectTaskInfoByNameAndStatus.do"

	formData := map[string]string{
		"tokenID":        a.getToken(),
		"task_name":      req.TaskName,
		"install_status": req.InstallStatus,
	}

	rsp := SelectTaskInfoByNameAndStatusResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SelectTaskInfoByStatus(ctx context.Context, req SelectTaskInfoByStatusRequest) (SelectTaskInfoByStatusResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/selectTaskInfoByStatus.do"

	formData := map[string]string{
		"tokenID":       a.getToken(),
		"installStatus": req.InstallStatus,
	}

	rsp := SelectTaskInfoByStatusResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetDataSourceInfo(ctx context.Context, req GetDataSourceInfoRequest) (GetDataSourceInfoResponse, error) {
	const api = "permission/hostAndDataSourceConfig/getDataSourcePageInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
		"offset":  parse.FormatInt(req.Offset),
		"limit":   parse.FormatInt(req.Limit),
		"userID":  parse.FormatInt(req.UserID),
		"psId":    parse.FormatInt(req.PsID),
		"db_name": req.DBName,
	}

	rsp := GetDataSourceInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) getToken() string {
	return a.token
}

func (a *adaptor) QueryAllHostInfo(ctx context.Context, req QueryAllHostInfoRequest) (QueryAllHostInfoResponse, error) {
	const api = "permission/hostAndDataSourceConfig/queryAllHostInfo.do"

	formData := map[string]string{
		"offset":    parse.FormatInt(req.Offset),
		"limit":     parse.FormatInt(req.Limit),
		"userID":    parse.FormatInt(req.UserID),
		"host_ip":   req.HostIP,
		"host_name": req.HostName,
		"user_name": req.UserName,
		"host_port": req.HostPort,
		"os_name":   req.OsName,
		"tokenID":   a.getToken(),
	}

	rsp := QueryAllHostInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) QueryPhysicalSubSystem(ctx context.Context, req QueryPhysicalSubSystemRequest) (QueryPhysicalSubSystemResponse, error) {
	const api = "/permission/hostAndDataSourceConfig/queryPhysicalSubsystem.do"

	formData := map[string]string{
		"offset":             parse.FormatInt(req.Offset),
		"limit":              parse.FormatInt(req.Limit),
		"group_machine_name": req.GroupMachineName,
		"group_machine_desc": req.GroupMachineDesc,
		"create_time":        req.CreateTime,
		"tokenID":            a.getToken(),
		"user_id":            parse.FormatInt(req.UserID),
	}

	rsp := QueryPhysicalSubSystemResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) QueryCustomMachineGroupInfo(ctx context.Context, req QueryCustomMachineGroupInfoRequest) (QueryCustomMachineGroupInfoResponse, error) {
	const api = "/permission/customMachineGroupController/queryCustomMachineGroupInfo.do"

	formData := map[string]string{
		"tokenID": a.getToken(),
	}

	rsp := QueryCustomMachineGroupInfoResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) ListSysUserGroupMap(ctx context.Context, req ListSysUserGroupMapRequest) (ListSysUserGroupMapResponse, error) {
	const api = "permission/sysUserGroupMapTb/list"

	formData := map[string]string{}

	rsp := ListSysUserGroupMapResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) ListSysUserGroups(ctx context.Context, req ListSysUserGroupsRequest) (ListSysUserGroupsResponse, error) {
	const api = "permission/sysUserGroupTb/list"

	formData := map[string]string{}

	rsp := ListSysUserGroupsResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) ListOperateLog(ctx context.Context, req ListOperateLogRequest) (ListOperateLogResponse, error) {
	const api = "permission/sysUserInfo/getOperLogInfo.do"

	var operSource string

	if req.TaskName != "" {
		operSource = req.TaskName
	}
	if req.DataSourceName != "" {
		operSource = req.DataSourceName
	}

	formData := map[string]string{
		"offset":     parse.FormatInt(req.Offset),
		"limit":      parse.FormatInt(req.Limit),
		"operSource": operSource,
	}

	rsp := ListOperateLogResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetHostPorts(ctx context.Context, req GetHostPortsRequest) (GetHostPortsResponse, error) {
	const api = "permission/hostAndDataSourceConfig/detailsPort.do"

	formData := map[string]string{
		"offset":    parse.FormatInt(req.Offset),
		"limit":     parse.FormatInt(req.Limit),
		"host_ip":   req.HostIP,
		"tokenID":   a.getToken(),
		"sortName":  req.SortName,
		"sortOrder": req.SortOrder,
	}
	if req.Port != 0 {
		formData["port"] = parse.FormatInt(req.Port)
	}

	rsp := GetHostPortsResponse{}
	err := a.postFormData(ctx, a.GetDsgURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) SetToken(info string) {
	a.token = info
}

func (a *adaptor) GetRealTaskInfo(ctx context.Context, req GetRealTaskInfoRequest) (GetRealTaskInfoResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/getRealTaskInfo.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"taskName": req.TaskName,
	}

	rsp := GetRealTaskInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetTableRealInfo(ctx context.Context, req GetTableRealInfoRequest) (GetTableRealInfoResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/getTableRealInfo.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"taskName": req.TaskName,
		"limit":    parse.FormatInt(req.Limit),
	}

	rsp := GetTableRealInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetTableDmlDdlInfo(ctx context.Context, req GetTableDmlDdlInfoRequest) (GetTableDmlDdlInfoResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/getTableDmlDdlInfo.do"

	formData := map[string]string{
		"tokenID":      a.getToken(),
		"taskName":     req.TaskName,
		"avgTime":      req.AvgMinute,
		"intervalTime": req.IntervalHour,
	}

	rsp := GetTableDmlDdlInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetWarningInfo(ctx context.Context, req GetWarningInfoRequest) (GetWarningInfoResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/getWarningInfo.do"

	formData := map[string]string{
		"tokenID":    a.getToken(),
		"taskName":   req.TaskName,
		"targetType": string(req.TargetType),
	}

	rsp := GetWarningInfoResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) OperationScript(ctx context.Context, req OperationScriptRequest) (OperationScriptResponse, error) {
	const api = "autoMaticEngineBoot/opSaveTaskInfoTb/operationSh.do"

	formData := map[string]string{
		"tokenID":  a.getToken(),
		"fileName": req.FileName,
		"tId":      parse.FormatInt(req.TID),
	}

	rsp := OperationScriptResponse{}
	err := a.postFormData(ctx, a.GetAutoMaticEngineBootURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}
