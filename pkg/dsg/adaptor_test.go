package dsg

import (
	"context"
	"net/url"
	"reflect"
	"testing"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

const ip = "http://***********"
const port0 = 8200
const port1 = 8201
const tid = 8
const userId = 1
const subSystemId = 8

func MustNewTestAdaptor() Adaptor {
	a, err := NewDsgAdaptor(&config.IncrementConfig{
		DsgHost:                    ip,
		DsgPort:                    port0,
		DsgAutoMaticEngineBootPort: port1,
	})
	if err != nil {
		panic(err)
	}
	return a
}

func Test_GetToken(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	tokenRsp, err := a.GetToken(ctx, GetTokenRequest{})
	assert.Nil(t, err)
	t.Log(tokenRsp.DataInfo)
}

func Test_adaptor_ValidateUser(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	type fields struct {
		host   string
		port   int
		token  string
		dsgUrl *url.URL
		client *resty.Client
	}
	type args struct {
		ctx     context.Context
		request ValidateUserRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    ValidateUserResponse
		wantErr bool
	}{
		{
			args: args{
				ctx: ctx,
				request: ValidateUserRequest{
					Account:      "admin",
					Password:     "pBjmyIdtT2+u7QIbCk8sRQylhBLfSXHCT/5w0QRwPOEvSH/u+pL2sw==",
					TokenID:      0,
					Type:         "",
					EncryptOrNot: "encrypt",
				},
			},
			want:    ValidateUserResponse{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := a.ValidateUser(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ValidateUser() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_adaptor_Failed_API(t *testing.T) {
	//log.InitTestLogger()
	//a := MustNewTestAdaptor()
	//ctx := context.TODO()
	//
	//var got any
	//var err error
}

func Test_adaptor_QueryAllAPI(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	//got, err = a.GetModuleList(ctx, GetModuleListRequest{})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.ListSysUserGroups(ctx, ListSysUserGroupsRequest{})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.ListSysUserGroupMap(ctx, ListSysUserGroupMapRequest{})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.QueryCustomMachineGroupInfo(ctx, QueryCustomMachineGroupInfoRequest{})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.QueryPhysicalSubSystem(ctx, QueryPhysicalSubSystemRequest{Limit: 10, UserID: userId})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetDataSourceInfo(ctx, GetDataSourceInfoRequest{})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.SelectTaskInfoByStatus(ctx, SelectTaskInfoByStatusRequest{InstallStatus: "1"})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.SelectTaskInfoByNameAndStatus(ctx, SelectTaskInfoByNameAndStatusRequest{InstallStatus: "1", TaskName: "wuchao-test-0523b"})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.SelectTaskInfoByTaskName(ctx, SelectTaskInfoByTaskNameRequest{TaskName: "wuchao-test-0523b"})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	got, err = a.GetPGMDInstallStatus(ctx, GetPGMDInstallStatusRequest{TaskName: "tms_462_7438"})
	assert.Nil(t, err)
	t.Log(got)
	got, err = a.GetPGMDInstallStatus(ctx, GetPGMDInstallStatusRequest{TaskName: "ref_Task_1"})
	assert.Nil(t, err)
	t.Log(got)
	got, err = a.GetPGMDInstallStatus(ctx, GetPGMDInstallStatusRequest{TaskName: "tms_466_7437"})
	assert.Nil(t, err)
	t.Log(got)
	got, err = a.GetPGMDInstallStatus(ctx, GetPGMDInstallStatusRequest{TaskName: "ps_full_inc"})
	assert.Nil(t, err)
	t.Log(got)
	got, err = a.GetPGMDInstallStatus(ctx, GetPGMDInstallStatusRequest{TaskName: "not_exist_dsg_task"})
	assert.NotNil(t, err)
	t.Log(got)
	got, err = a.GetPGMDInstallStatus(ctx, GetPGMDInstallStatusRequest{TaskName: "ps_full_inc\n"})
	assert.NotNil(t, err)
	t.Log(got)
	//got, err = a.SelectDefineMapping(ctx, SelectDefineMappingRequest{Limit: 10000, TID: tid})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.ReadSourceDataSourceIni(ctx, ReadSourceDataSourceIniRequest{FileName: "vm", TID: tid})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.ReadTargetDataSourceIni(ctx, ReadTargetDataSourceIniRequest{FileName: "yloader.ini", TID: tid})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.SelectCompareDefineMapping(ctx, SelectCompareDefineMappingRequest{TID: tid})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetCompareSelectedTable(ctx, GetCompareSelectedTableRequest{TID: tid})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetCompareSelectedUser(ctx, GetCompareSelectedUserRequest{TID: tid})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.QueryAllHostInfo(ctx, QueryAllHostInfoRequest{Limit: 10, UserID: 1})
	//assert.Nil(t, err)
	//t.Log(got)
}

func Test_adaptor_QueryAllAPI2(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	//got, err = a.VerifyHost(ctx, VerifyHostRequest{
	//	//AffDataCenter: "稻香湖",
	//	HostIP:        "***********",
	//	//HostName:      "test_host_name",
	//	//PSID:          2,
	//	//OSName:        "linux",
	//	//SecretName:    "/home/<USER>/dsadasfasfdsaf",
	//	//MacID:         3333,
	//})
	//assert.Nil(t, err)
	//assert.Truef(t, got.(VerifyHostResponse).DataInfo, "got: %v", got)
	//t.Log(got)
	//
	//got, err = a.GetSelectedUser(ctx, GetSelectedUserRequest{TID: tid, Limit: 10})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	got, err = a.GetUnSelectedUser(ctx, GetUnSelectedUserRequest{TID: 87, Limit: 10})
	assert.Nil(t, err)
	t.Log(got)
	//
	//got, err = a.GetUnSelectedTable(ctx, GetUnSelectedTableRequest{TID: tid, Limit: 10, SchemaName: "YUANDS,DT,SOE,FINDPT"})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetTableSyncInfo(ctx, GetTableSyncInfoRequest{TID: tid, Limit: 10, UserID: userId})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetSelectedTable(ctx, GetSelectedTableRequest{TID: tid, Limit: 10, SchemaName: "YUANDS,DT,SOE,FINDPT"})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetUnTargetSelectedTable(ctx, GetUnTargetSelectedTableRequest{TID: tid, Limit: 10, SchemaName: "YUANDS,DT,SOE,FINDPT"})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.SelectAllTask(ctx, SelectAllTaskRequest{TaskName: "tms_447_7347", UserID: userId, Limit: 10, Offset: 0})
	//assert.Nil(t, err)
	//t.Log(got)

}

func Test_adaptor_WriteOrSaveAPIs(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	got, err = a.WriteSourceDataSourceIni(ctx, WriteSourceDataSourceIniRequest{
		FileName: "vm",
		TID:      tid,
		Content: `connect ***********:10000
user root/dbps
set ds 1 -itvl 2 -state start
set dt 1.1 -tgt db 2 -xf1 y -state start
set dm 1.1 -cfg -sync ftcqIM5
exit2-testsuit`,
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.WriteSourceDataSourceIni(ctx, WriteSourceDataSourceIniRequest{
		FileName: "select_cond.ini",
		TID:      tid,
		Content:  `testsuit-select golang cond`,
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.WriteSourceDataSourceIni(ctx, WriteSourceDataSourceIniRequest{
		FileName: "start",
		TID:      tid,
		Content:  "#testsuit-golang\n\n	#!/bin/sh\n#set -x\n\nexport AOX_HOST=***********\nexport AOX_PORT=10003\n\nexport PGMD_SUPPORT=on\n\nusage(){\n\techo \"Usage: $0 ha_force_option[ha|HA] active_flag_forceoption[force]\"\n}\n\nif [ $# -gt 5 ]; then\n\tusage\n\texit\nelse\n\tif [ \"$1\" = \"-h\" -o \"$1\" = \"-help\" ]; then\n\t\tusage\n\t\texit\n\tfi\nfi\n\nvalid_arguments=\nfor option in $*\ndo\n\tcase $option in \n\t\tha|HA)\n\t\t\tha_force_option=ha;\n\t\t\tif [ -n \"${valid_arguments}\" ]; then\n\t\t\t\tvalid_arguments=\"${valid_arguments} ${ha_force_option}\"\n\t\t\telse\n\t\t\t\tvalid_arguments=${ha_force_option}\n\t\t\tfi\n\t\t\t;;\n\t\tforce|FORCE)\n\t\t\tactive_flag_forceoption=force\n\t\t\tif [ -n \"${valid_arguments}\" ]; then\n\t\t\t\tvalid_arguments=\"${valid_arguments} ${active_flag_forceoption}\"\n\t\t\telse\n\t\t\t\tvalid_arguments=${active_flag_forceoption}\n\t\t\tfi\n\t\t\t;;\t\t\t\n\tesac\ndone\n\n#. `dirname $0`/fun_precheck\nif [ -n \"${valid_arguments}\" ]; then\n\t. `dirname $0`/fun_precheck ${valid_arguments}\nelse\n\t. `dirname $0`/fun_precheck\nfi\n\nif [ \"$EXECUTABLE_FLAG\" != \"1\" ]; then\n\texit\nelse\n#####main begin\n#USER=`whoami`\nUSER=$LOGNAME\n#if [  $USER = root ]; then\n#\techo \"Warning: Please Do not run with root user!\"\n#\texit 0\n#fi\n\n#DBPS_NAME=`echo $DBPS_HOME |awk -F\"/\" '{print $NF}'`\nDBPS_NAME=`basename $DBPS_HOME`\n\nif [ -f $DBPS_HOME/rmp/run_state.ctl ];then\n\trm $DBPS_HOME/rmp/run_state.ctl\nfi\n\npid=`ps -ef|grep $DBPS_HOME/bin/dbpsd|grep $DBPS_PORT|grep -v grep|awk '{print $2}'`\nif [ ! -z \"$pid\" ]; then\n\techo \"INFO: DBPSD process is running ,stop it first please!\"\n\texit 0\nfi\n\npid=`ps -ef|grep $DBPS_HOME/bin/vagentd|grep $LOCAL_VAGENTD_PORT|grep -v grep|awk '{print $2}'`\nif [ ! -z \"$pid\" ]; then\n\techo \"INFO: VAGENTD process is running ,stop it first please!\"\n\texit 0\nfi\n\npid=`ps -ef|grep $DBPS_HOME/bin/sender|grep -v grep|awk '{print $2}'`\nif [ ! -z \"$pid\" ]; then\n\techo \"INFO: Sender process is running ,stop it first please!\"\n\texit 0\nfi\n\npid=`ps -ef|grep $DBPS_HOME/bin/archivelog|grep -v grep|awk '{print $2}'`\nif [ ! -z \"$pid\" ]\nthen\n\techo \"INFO: ARCHIVELOG process is running ,stop it first please!\"\n\texit 0\nfi\n\nif [ \"$OXAD_FLAG\" = \"1\" -o \"$ASM_ENABLE\" = \"true\" -o \"$ASM_ENABLE\" = \"TRUE\" -o \"$ASM_ENABLE\" = \"1\" -o \"$ASM_ENABLE\" = \"on\" -o \"$ASM_ENABLE\" = \"ON\" ]; then\n\t$DBPS_HOME/scripts/start_oxad $valid_arguments\nfi\n\n#cd $DBPS_HOME\n#For Bourne, Bash, or Korn shell:\nXLDR_HOME=$DBPS_HOME/rmp;export XLDR_HOME\nVCFS_HOME=$DBPS_HOME/vcfsa;export VCFS_HOME\n#For C or tcsh shell:\n#setenv XLDR_HOME $DBPS_HOME/rmp\n#setenv VCFS_HOME $DBPS_HOME/vcfsa\n\n#add at 20130925 for new archivelog\n#modified at 20131114 for new archivelog\n#add at 20140114\nif [ ! -e $DBPS_HOME/log/log.vagentd ]; then\n  touch $DBPS_HOME/log/log.vagentd\nfi\n$DBPS_HOME/bin/archivelog $DBPS_HOME/log/log.vagentd $DBPS_HOME/log/archivelog/log.vagentd_archlog   $DBPS_HOME/log/archivelog/log.vagentd_errlog err $INTERVAL_S $ARCHDIR_MAXSIZE > /dev/null &\n#$DBPS_HOME/bin/archivelog $DBPS_HOME/log/log.vagentd $DBPS_HOME/log/archivelog/log.vagentd_archlog $DBPS_HOME/log/archivelog/log.vagentd_errlog \"rowid ORA- Err err\" $INTERVAL_S $ARCHDIR_MAXSIZE > /dev/null &\n\n#common options: -en_temp_tab -xtag A1 -no_exp_objs\n#$DBPS_HOME/bin/vagentd $LOCAL_VAGENTD_PORT >> $DBPS_HOME/log/log.vagentd 2>&1 &\n#modified at 20131114\ngetoption option_vagentd\n#modified at 20131123\n$DBPS_HOME/bin/vagentd -role ds -select_mode -no_func_index -no_exp_objs -no_check_comp $LOCAL_VAGENTD_PORT ${option[1]}   >> $DBPS_HOME/log/log.vagentd 2>&1 &\n\n\n#add at 20130925 for new archivelog\n#modified at 20131114 for new archivelog\n#add at 20140114\nif [ ! -e $DBPS_HOME/log/log.sender ]; then\n  touch $DBPS_HOME/log/log.sender\nfi\n#$DBPS_HOME/bin/archivelog $DBPS_HOME/log/log.sender $DBPS_HOME/log/archivelog/log.sender_archlog $DBPS_HOME/log/archivelog/log.sender_errlog err $INTERVAL_S $ARCHDIR_MAXSIZE > /dev/null &\n$DBPS_HOME/bin/archivelog $DBPS_HOME/log/log.sender $DBPS_HOME/log/archivelog/log.sender_archlog $DBPS_HOME/log/archivelog/log.sender_errlog \"ORA- Err err\" $INTERVAL_S $ARCHDIR_MAXSIZE > /dev/null &\n\n#$DBPS_HOME/bin/sender -tseq 1 >> $DBPS_HOME/log/log.sender 2>&1 &\n#modified at 20131114\ngetoption option_sender\n#modified at 20131123\n$DBPS_HOME/bin/sender -tseq 1 ${option[2]} >> $DBPS_HOME/log/log.sender 2>&1 &\n\n#For Bourne, Bash, or Korn shell:\nVCFS_HOME=$DBPS_HOME/vcfsd;export VCFS_HOME\n#For C or tcsh shell:\n#setenv VCFS_HOME $DBPS_HOME/vcfsd\n\n#$DBPS_HOME/bin/dbpsd $DBPS_PORT > $DBPS_HOME/log/log.dbpsd 2>&1 &\n#modified at 20131114\ngetoption option_dbpsd\n#modified at 20131123\n$DBPS_HOME/bin/dbpsd $DBPS_PORT ${option[0]} > $DBPS_HOME/log/log.dbpsd 2>&1 &\n\n\n#$DBPS_HOME/bin/x_logger\nsleep 1\n$DBPS_HOME/scripts/check $valid_arguments\n\n$DBPS_HOME/scripts/hastatus_update $DBPS_HOME dbpsd online\n$DBPS_HOME/scripts/hastatus_update $DBPS_HOME vagentd online\n$DBPS_HOME/scripts/hastatus_update $DBPS_HOME sender online\n#####main end\n\n$DBPS_HOME/scripts/start_oxad\nsleep 2\n$DBPS_HOME/scripts/start_aoxd\n\nfi\n",
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.WriteTargetDataSourceIni(ctx, WriteTargetDataSourceIniRequest{
		FileName: "yloader.ini",
		TID:      tid,
		Content:  "#testuit-golang-\n\n# configure for load   data to DB\n[YLD]\n  home=/home/<USER>/oracle-tidb/wuchao-test-0527/dt/rmp                    # work directory, EX: home=/dsg/odcs/dt/rmp\n  cfg_filename=cfg.loaderno                # xagentd or nfmd recv configure filename.\n  data_format=xf1                          # load data format: (xdt | xf1)?\n  db_type=oracle                # load data database type, source database type.\n\n  # set for startup nfmd service.\n  service=127.0.0.1,10020\n\n  #debug=n                                 # running in debug mode? (y/n)\n  ses_blen=30MB                            # session buffer size. (10 ~ 300)MB\n  wait_free_size=10MB                      # home directory is less than the value will be set to wait\n\n  # wait_retry=A,B,C,D,E \n  #   A - wait read file seconds.              B - error retry wait seconds.\n  #   C - idle close DB connection seconds.    D - retry addition seconds.\n  #   E - retry addition maximum seconds.\n  wait_retry=1,30,600,0,65535\n\n  # configure for full import.\n  #full_completed=n                       # force set full import is completed? (y/n), set y, not load syncX data.\n  full_load_completed=r                  # full data load complete do? (r: remove, b: backup, n: ignore)\n  full_thread=6                          # full import thread count. (1 ~ 32)\n  full_sql_mode=y                         # full import used sql execute mode? (y/n)\n  #full_out_dml_sql=n                      # full import need output DML sql? (y/n)\n  #full_dku=n                              # full used duplicate key update? (y/n)\n  #full_copy=n                             # full import used copy? (y/n)\n  #full_copy_size=0MB                      # full copy cache size. (0 - a xdt,xf1 file size)\n   full_single_parr=y                      # full import single table parralel? (y/n)\n   full_repair_real_quit=y                 # full repair, realX loading need quit? (y/n)\n  #full_insert_fdelete=n                   # first sync, insert first exec delete? (y/n)\n  #full_insert_err_fdelete=n               # first sync, insert error, exec delete,insert? (y/n)\n\n  #used_rowmap=n                           # for Oracle, need create rowid mapping? (y/n)\n  rowmap_type=S                             # rowmap type. (EX: S, A, D, -), (- unused)\n  sql_err_change2bind=y                     # sql mode loading error auto changed bind mode loading? (y/n)\n   sql_trans=y                             # sql mode loading used transaction? (y/n)\n  #sync_mtab_to_1=n                        # sync more tables to one table? (y/n)\n\n  # set for PostgreSQL, BLOB large than this size, unused copy mode loading.\n  full_copy_unused_size=200KB\n\n  # configure for real (increment) load. (max queue number: 64)\n  #real=                                  # only load this queue number data?\n  real_load_completed=b                    # real data load complete do? (r: remove, b: backup, n: ignore)\n  real_thread=1                            # increment import thread count. (1 ~ 32)\n  #real_sql_mode=n                        # real import used sql execute mode? (y/n)\n  #real_out_dml_sql=n                     # real import need output DML sql? (y/n)\n  #real_copy=n                            # real insert used copy? (y/n)\n  real_disp_mode=f                         # real dispatch mode (m - memory, f - file, c - file out xdt)\n  #real_disp_dir=                          # real dispatch work directory.\n  #real_disp_merge=n                       # real dispatch merge DML?(y/n)\n   real_disp_fi=y                          # real force dispatch insert?(y/n)\n  #real_disp_fd=n                          # real force dispatch delete?(y/n)\n  #real_disp_fu=n                          # real force dispatch update?(y/n)\n  #real_disp_rows=0                        # real dispatch max rows to be merged.(0 ~ 900000)\n  real_disp_size=510                       # real dispatch group size.\n  real_disp_fcount=100                     # real dispatch max file count.(1 ~ 10000)\n  real_disp_fwait=1000                     # real dispatch than this need wait.(10 ~ 10000)\n  #real_disp_err=n                         # dispatch loading quick maybe error?(y/n)\n  #real_insert_fdelete=n                   # increment, insert first exec delete? (y/n)\n  real_insert_err_fdelete=y               # increment, insert error, retry exec delete,insert? (y/n)\n\n  # increment, insert first exec delete, check mode. (r: rowid, k: pkuk, n: ignore)\n  #real_insert_fd_mode=r\n\n\n  # configure for filter data, all data.\n  #full_ddl_filter=n                       # filter full ddl? (y/n) (exclude UK/PK/Index table)\n  #full_insert_filter=n                    # filter sync insert data? (y/n)\n  #real_insert_filter=n                    # filter real insert data? (y/n)\n  #real_delete_filter=n                    # filter real delete data? (y/n)\n  #real_update_filter=n                    # filter real update data? (y/n)\n  #real_ddl_filter=n                       # filter real ddl? (y/n) (exclude create/drop table)\n  #real_nodict_filter=n                    # filter real not found dict xf1/xdt data? (y/n)\n  #real_used_target_dict=n                 # real loading used target dict? (y/n)\n  #real_dml_merge=n                        # for kafka, real DML(insert,delete,update) data merge? (y/n)\n  #real_used_trans=n                       # real loading used transaction commit? (y/n)\n  #real_drop_table_do=drop                 # source do drop table, target do? (drop | rename | none)\n\n  # real ddl transfer call ALI adam-dcs service? need call /home/<USER>/dtp/pgmcli//bin/ali_tsql\n  # real_ddl_call_tsql=\"127.0.0.1,37007\"\n\n  # real connect lsyncd service for load synchronization.\n  # lsyncd=\"127.0.0.1,3200\"\n\n  ## first used this yxad host & port.\n  # full_yxad=*************,8900 \n  # real_yxad=*************,8901 \n\n  ## -startup y used, yxad startup command parameter.\n  #yxad_cmd_par=\"-psql whup \"\n\n  # configure for filter objn include data.\n  # full_ddl_fobjn=1254,124124, ...\n  # real_ddl_fobjn=1254,124124, ...\n  # full_insert_fobjn=1254,124124, ...\n  # real_insert_fobjn=1254,124124, ...\n  # real_delete_fobjn=1254,124124, ...\n  # real_update_fobjn=1254,124124, ...\n\n  # configure for filter objn include data.\n  # full_ddl_ftable=DS.T1,DS.T2, ...\n  # real_ddl_ftable=DS.T1,DS.T2, ...\n  # full_insert_ftable=DS.*, ...\n  # real_insert_ftable=*.T1, ...\n  # real_delete_ftable=DS.T1,DS.T2, ...\n  # real_update_ftable=DS.T1,DS.T2, ...\n\n  # only import this table to DB, other filter.\n  #only_load_table=\"\"\n  # only import this table objn to DB, other filter.\n  #only_load_objn=\"\"\n\n  ## delay drop/insert schema table ...\n  ## delay drop schema include *__DEL table, for DB2 DDL:\n  ##   EX: ALTER TABLE DS.T1 DETACH PARTITION PART3 \n  #delay_drop_schema=DS1\n  #\n  #delay_insert_schema=DS1\n\n  ## delete sql used temp table? (y/n) for MySQL.\n  #delete_sql_used_tmp_table=n\n\n  ## for MySQL transtrion start write XXX._dsg_slive table.\n  #trans_wdml_dbname=\n\n  # output rmp/y1_cache/delay.log interval seconds.\n  delay_interval=360\n\n  # filter/no filter include this key ddl? (max support count: 32)\n  #filter_ddl_key=\n\n  # wait execute include this key ddl? (max support count: 32)\n  #wait_ddl_key=\n\n  #view_mode=n                            # set (n - normal, q - quick, f - fly).\n  #project_name=                          # loading project name.\n  queue_name=y1                            # loading queue short name.\n  error_retry=65534                        # set error retry times. (range: 0 ~ 65534)\n  error_retry_ddl=3                        # Exec DDL, set error retry times.\n  #error_idata=n                          # input data error need report error? (y/n)\n  #error_dump_data=n                      # loading error need dump detail data? (y/n)\n   full_cindex_error_retry=1                # full end create index/PK/UK error retry times.\n\n  # set error ignore find key words.\n  # Ex: error_ignore=\"SQL0327N\" \n  #error_ignore=\"\"\n\n  update_nobefore=y                      # update no before value? (y/n)\n  update_pkuk_col=y                        # support update PK/UK column data? (y/n)\n  #update_filter_col=n                    # update filter no changed column? (y/n)\n  #update_used_di=n                       # update exec used delete and insert? (y/n)\n  #update_err_used_di=n                   # update error call: delete & insert? (y/n)\n  #update_ba_exchange=n                   # update before & after exchange? (y/n)\n\n  # (L - last column, A - all column, N - unused)\n  #update_lost_set_null=N                  # update where condition lost data set null.\n\n  # for DB2 loading, NOTE: PK/UK no change.\n  #update_used_merge=n                     # update used merge into loading? (y/n)\n\n  ## set used update changed to delete & insert table.\n  #update2di_table=\"owner.table\"\n\n  #clob2null=n                            # CLOB column force set null? (y/n)\n  #blob2null=n                            # BLOB column force set null? (y/n)\n  #long2null=n                            # LONG column force set null? (y/n)\n  #longraw2null=n                         # LONG RAW column force set null? (y/n)\n  #dub_clob2null=n                        # delete and update before value, CLOB column force set null? (y/n)\n\n  #set for output LOB/LONG data to directory.\n  #lob_save_dir=/dsg/lob_data[,aiod_host,aiod_port] \n  #lob_col_value=\"oss://dsg1/lob,/dsg/put.sh\" \n  #set CLOB/BLOB/LONG/LONG RAW column need output to file? (y/n) \n  #clob2file=y \n  #blob2file=y \n  #long2file=y \n  #longraw2file=y \n\n  #idb_used_rowid=n                       # input (source DB) data include rowid? (y/n)\n  #idb_lang=                              # set input data charset. (gbk, utf8, ...)\n  #idb_lang_force=                        # set input data charset, dict csid>0 used. (gbk, utf8, ...)\n  lang_gbk2gb18030=y                       # force changed GBK to GB18030? (y/n)\n  #conv_err_repair=n                      # automatically fix problems with character set conversion? (y/n)\n  #conv_force_repair=n                    # character conversion force used repair? (y/n)\n  #yc_restart=n                           # yloader.ini changed, yloader need auto restart? (y/n)\n\n  used_map=y                               # used mapping info? (y/n)\n  used_quotes=y                            # used quotes? EX: \"Tab1\" (y/n)?\n  #ctype_varchar2binary=n                 # for MySQL, varchar replace used varbinary ? (y/n)\n  #ctype_varchar2citext=n                 # for PostgreSQL, varchar/nvarchar replace used citext ? (y/n)\n  #ctype_support_varchar2=n               # for PostgreSQL, support varchar2/nvarchar2 ? (y/n)\n  #ctype_define_number=                   # define number type, EX: number,numeric,decimal \n  #ctype_define_date=                     # define date type(saved: YYYY-MM-DD HH24:MI:SS), EX: date,timestamp(0) ...\n  #ctype_define_clob=                     # define clob type, EX: text,clob ...\n  #ctype_define_blob=                     # define blob type, EX: bytea,blob ...\n  #ctype_timezone_adjust=\"00:00\"          # need adjust output time timezone? EX: 8:0\n  #ctype_char2nchar=n                     # for GreenPlum, char,varchar replace nchar,nvarchar. (y/n)\n  #ctype_clob2varchar=n                   # force clob type changed to varchar(MAX) for create table. (y/n)\n  #ctype_char2varchar=n                   # force char type changed to varchar(N) for create table. (y/n)\n  #ctype_time2varchar=n                   # force time type changed to varchar(N) for loading. (y/n)\n  #ctype_blob2clob=n                      # force blob type changed to clob for loading. (y/n)\n  #ctype_bool2int=n                       # boolean type used int? (y/n)\n  #ctype_varchar2long=0                   # varchar(N) change to long for create table.\n  #invalid_date_change=n                  # invalid date changed? EX: 0000-00-00 (y/n)\n  #ctab_lost_part=n                       # create table lost partition information? (y/n)\n  #ctab_lost_default=n                    # create table lost default value? (y/n)\n  #ctab_lost_not_null=n                   # create table lost not null constraint? (y/n)\n   ctype_ifx_decimal=y                    # input decimal is from Informix DB? (y/n)\n   ctype_number_adjust=y                  # input number data auto adjust? (y/n), EX: 01.230 -> 1.23\n  #ctype_oname_adjust=n                   # automatic correction of illegal object names? (y/n)\n  #ctype_strexp=1                         # create table expand char/varchar length.\n  #ctype_mssql_datetime2date=n                # SQL Server datetime force changed to date? (y/n)\n  #ctype_mssql_tps2bigint=n                   # SQL Server timestamp force changed to bigint? (y/n)\n  #ctype_mssql_text2varchar=0                 # SQL Server text force used varchar(N).\n  filter_string_00=y                       # filter string 0x00 data? (y/n)\n  #extra_len_trunc=n                      # extra length force truncate data? (y/n)\n\n  # set output txad date/timestamp data format.\n  # EX: date_oformat=%%?\n  #  y : year, m : month, d : day, h : hour, i : minutes, s : second, u : microsecond\n  #date_oformat=\"\"\n  #timestamp_oformat=\"\"\n\n  ## print real table change info.\n  #\n  # countsum_dir configure output file: \n  #    /_countsum_full.log\n  #    /_countsum_total.log\n  #    /_countsum_interval.log\n  # EX: \n  #  # configure real output interval seconds, 0 - unused this function.\n  #  # i - insert, d - delete, u - update, o - DDL\n  #  countsum_time=120,[iduo]\n  # \n  #  # configure output file directory. countsum_dir default output yloader cache dir.\n  #  countsum_dir=\"/dsg/log/,[tag]\"\n  # \n  #  countsum_shell=\"/dsg/bin/r.sh\"  # output end exec shell, , \n  #  countsum_detail=y                 # output detail changed info? (y/n)\n  #  countsum_full=n                   # output full sync statistic info? (y/n)\n  #  countsum_db=n                     # output to MC DB schema name? (y/n)\n  #  countsum_txad=192.168.1.151,8900  # output to txad service \n  #  countsum_txad_schema=DSG          # output to txad schema name \n  #  countsum_txad_mtable=n            # output to txad more tables \n  #\n  countsum_time=\"120,\"\n  countsum_detail=y\n  #countsum_full=n\n  #countsum_dir=\",\"\n  #countsum_shell=\"\"\n  #countsum_db=n\n  #countsum_txad=\",0\"\n  #countsum_txad_schema=\n  #countsum_txad_mtable=n\n\n  ## output MC DB table schema name, used YID? (y/n)\n  # EX: mc_schema=DSG[,y/n] \n  #mc_schema=\",n\"\n  #output_monitor=n                       # output CPU,MEM information? (y/n)\n\n  #output_scn2cache=n                      # output source & target Oracle SCN to scn.log? (y/n)\n  #output_blood=n                          # output blood analysis table? (y/n)\n  #output_rba=n                            # output RBA information to DB? (y/n)\n  #output_log2db=n                         # output log.log to MC DB? (y/n)\n  #output_ddl2db=n                         # output execute DDL to MC DB? (y/n)\n  #output_last_time=0,30                   # current_time - last_time(minutes), last_time update interval.\n  #output_last_timea=n,                    # PARA_LAST_DATA_TIME,PARA_REP_BUSI_DATE tables to DB? (y/n)\n  #output_last_timer=n,                    # PARA_LAST_SYS_BUSI_DATE tables to DB? (y/n)\n\n  table_exists_check=y                     # need check table is exists?(y/n)\n  table_exists_full_do=none               # full load table exists do? (trunc | drop | delete | none)\n  table_create_full=n                      # full enable create new table? (y/n)\n  table_create_full_index=n                # full import create table index? (y/n)\n  table_create_full_index_thread=6                # full end create index thread count? (1 ~ 16)\n  table_create_full_constraint=n                  # full import create table constraint? PK,UK (y/n)\n  #table_create_full_idx_par=n                    # single table create index used multi-thread? (y/n)\n  table_create_full_idx_end=n                     # full end create index, PK,UK? (y/n)\n\n  # full create PK/UK first delete duplicate data. parallel N\n  #table_create_full_pkuk_ddup=-1              \n\n  table_create_real=y                      # real enable create new table? (y/n)\n  table_exists_real_do=drop                # real load table exists do? (trunc | drop | delete | none)\n  table_create_real_index=y                # real import create table index? (y/n)\n  table_create_real_constraint=y                  # real import create table constraint? PK,UK (y/n)\n\n  # table structure different do? (A : add column, F : filter column, N - do nothing)\n  #table_struct_diff_do=N\n\n  # full loading first drop target table index,pk/uk, full end recreate the index,pk/uk?\n  #table_index_recreate=n\n\n  # table create UK error, changed create unique index to index? (y/n)\n  #create_uk_err2index=n\n\n  # for MySQL/PostgreSQL create table used PK/UK distributed?\n  # u - unused, d - default, h - hash, r - replication.\n  #table_create_dist_pkuk=u\n\n  # for Hive full create text table changed to ORC table? (y/n)\n  #table_create_hive_t2o=n\n\n  # for DB2 create table used ALWAYS? (y/n)\n  #table_create_used_always=n\n\n  # full create table add tail string\n  #   EX: db2: table_create_full_add=\"not logged initially\" \n  #table_create_full_add=\"\" \n  # real create table add tail string\n  #   EX: db2: table_create_real_add=\"data capture changes\" \n  #table_create_real_add=\"\" \n\n  # real update set add string\n  #real_update_set_add=\"\" \n\n  # full/real create table end immediate execute sql shell.\n  #ctb_after_full_sql_shell=\"\"\n  #ctb_after_real_sql_shell=\"\"\n\n  # drop table add tail string\n  #   EX: oracle: table_drop_add=\"purge\" \n  #   EX: oracle: create_index_add=\"unusable\" \n  #   EX: oracle: create_pkuk_add=\"nologging parallel 6\" \n  #       loading end, user need exec: alter table DT.T1 logging parallel 1;\n  #   EX: tencent mysql: table_insert_header_add=\"/*multi_sync*/\" \n  #table_drop_add=\"\"\n  #table_insert_header_add=\"\"\n  #create_index_add=\"\"\n  #create_pkuk_add=\"\"\n  #create_pkuk_col_add=\"\"\n\n  # trunc sql transfer shell filename.\n  # call /tmp/trunc.sql s_owner s_tablename t_owner t_tablename\n  # EX: trunc_sql_shell=\"/dsg/trunc.sh\"\n  #trunc_sql_shell=\n\n  # for table exists do delete? max support: 128\n  # format: \"DROW,OWNER.TABLE[WHERE_SQL]\"\n  #table_exists_delete=\"1000,DS.T1[COL1=1]\"\n  #table_exists_delete=\"DS.T1[COL1=1]\"\n  #table_exists_delete=\"DSG_*.T1[COL1=1]\"\n\n  # for PostgreSQL, create index add hint. format: \"key:add_key:xreg\"\n  # key: pk, uk, idx. \n  # EX: create_index_add_xreg=\"idx:using bitmap:.*DQ\" \n\n\n  #wait_cfg_file=                           # load wait SCN & DDL file.\n  #wait_out_file=                           # wait output file.\n  #full_start_file=                         # full import start exec shell file.\n  #full_end_file=                           # full import end exec shell file.\n  #full_end_wait_ofile=                     # full import end output wait file, remove the file continue.\n  #full_end_quit=n                          # full import end yloader quit? (y/n)\n  #sess_sql_file=                           # session init sql file.\n\n  #no_data_minutes=0                        # no data more than N minutes, start exec shell file.\n  #no_data_file=                            # no data loading start exec shell file.\n\n  ## addcol=XXX column to the front of the table? (y/n)\n  #addcol_to_table_front=n\n\n  ## set addcol_XXX column operation parameters info.\n  #addcol_name_forder=                    # add column name front order\n  #addcol_name_border=                    # add column name behind order\n\n  #addcol_opc_column=:4                   # add operation code column (name varchar(N))\n  #addcol_opc_insert=I                    # insert operation code\n  #addcol_opc_delete=D                    # delete operation code\n  #addcol_opc_update=U                    # update operation code\n  #addcol_opc_bupdate=BU                  # before update operation code\n  #addcol_opc_aupdate=AU                  # after update operation code\n\n  #addcol_del2update=n                      # delete changed to update operation code? (y/n)\n  #addcol_del2insert=n                      # delete changed to insert operation code? (y/n)\n  #addcol_upd2insert=n                      # update changed to insert operation code? (y/n)\n  #addcol_upd2insert_fb=n                   # update changed to insert, filter before insert? (y/n)\n  #addcol_inc_update_seq=n                  # update seq need increase? (y/n)\n\n  # update changed to insert, filter PK/UK the same before insert? (y/n)\n  #addcol_upd2insert_fbpks=n               \n\n  #addcol_tranid_column=                    # add transaction id column (name int)\n  #addcol_seq_column=                       # add transaction sequence column (name int)\n\n  # set load sequence\n  #start_loadseq=1\n  #addcol_loadseq_column=                   # add load sequence column (name int)\n\n  ## add source tablename column (name varchar(128))\n  ## addcol_stablename_column=COL_NAME:FLG\n  # FLG - 0: source tablename\n  #       1: source owner.tablename\n  #       2: source owner\n  #addcol_stablename_column=:0\n  #addcol_stablename_t2del=n                 # used stablename trunc changed to delete? (y/n)\n  #addcol_stablename_rwhere=n                # used stablename in real where delete/update sql? (y/n)\n  #addcol_stablename2uk=n                    # table PKUK+stablename = new UK? (y/n)\n\n  ## add target tablename column (name varchar(128))\n  ## addcol_ttablename_column=COL_NAME:FLG\n  # FLG - 0: target tablename\n  #       1: target owner.tablename\n  #       2: target owner\n  #addcol_ttablename_column=:0\n\n  # add SCN code column (name int)\n  # addcol_scn_column=COL_SCN,FLG,SCN_VALUE\n  # FLG - f: force used this SCN value\n  #       n: not found SCN used this SCN value\n  #addcol_scn_column=\"\"\n\n  ## add SCN time column (name date)\n  ## addcol_sct_column=FLG0,COL_NAME,FLG1,TIME \n  # FLG0 - -1: TIMESTAMP\n  #         0: DATE \n  #         1: VARCHAR save date,      EX: 2012-12-12 12:12:12 \n  #         2: VARCHAR save timestamp, EX: 2012-12-12 12:12:12.123\n  #         3: VARCHAR save date,      EX: 20121212121212\n  #         4: VARCHAR save timestamp, EX: 20121212121212.123\n  #         5: VARCHAR save timestamp, EX: 2012-12-12 12:12:12.123456\n  #         6: VARCHAR save timestamp, EX: 20121212121212.123456\n  #         7: NUMBER(19)            , EX: 20121212121212\n  #         8: NUMBER(19)            , EX: 20121212\n  # FLG1 - f: force used this time\n  #        n: not found SCT used this time\n  #addcol_sct_column=\"0,\"\n  #addcol_sct_xupdate=y                     # addcol_sct_column need execute update? (y/n)\n\n  #addcol_loaderid_column=:0                # add loader id column (name int | varchar(N))\n  #addcol_loaderid_value=                   # loader id value\n  #addcol_loaderid_t2del=n                  # used loader id trunc changed to delete? (y/n)\n  #addcol_loaderid_rwhere=n                 # used loader id in real where update/delete sql? (y/n)\n  #addcol_loaderid_cindex=y                 # create loader id index? (y/n)\n  #addcol_loaderid2uk=n                     # table PKUK+loaderid = new UK? (y/n)\n\n  #addcol_rowid_column=                     # add rowid column (name varchar(32))\n  #addcol_rowid2uk=n                        # no pk/uk table add rowid create UK? (y/n)\n  #addcol_rowid_mlid=n                      # rowid need merge loaderid column? (y/n)\n  #addcol_rowid_mdist=n                     # rowid need merge distribution_keys column? (y/n)\n  #addcol_rowid_cdist=n                     # rowid create distribution key column? (y/n)\n  #addcol_rowid_set=y                       # add rowid column in SQL set? (y/n)\n  #addcol_rowid_create=n                    # no rowid, used data create rowid data? (y/n)\n  addcol_rowid_invisible=y                  # to Oracle >=12, add rowid column ? (y/n)\n  #pk2uk=n                                  # convert primary key to unique key? (y/n)\n  #unused_pkuk=n                            # unused table primary key, unique key? (y/n)\n\n  ## addcol_loadtime_column=COL_NAME:LEN:VARCHAR_LEN \n  # LEN - -1: TIMESTAMP\n  #        0: DATE \n  #        1: VARCHAR save date,      EX: 2012-12-12 12:12:12 \n  #        2: VARCHAR save timestamp, EX: 2012-12-12 12:12:12.123\n  #        3: VARCHAR save date,      EX: 20121212121212\n  #        4: VARCHAR save timestamp, EX: 20121212121212.123\n  #        5: VARCHAR save timestamp, EX: 2012-12-12 12:12:12.123456\n  #        6: VARCHAR save timestamp, EX: 20121212121212.123456\n  #        7: NUMBER(19)            , EX: 20121212121212\n  #        8: NUMBER(19)            , EX: 20121212\n  #addcol_loadtime_column=:0:28             # add load time column (name timestamp | date | varchar)\n  #addcol_loadtime_index=n                  # load time column create index? (y/n)\n  #addcol_loadtime2uk=n                     # table PK/UK + loadtime(8) = new UK? (y/n)\n  #addcol_loadtime_xupdate=y                # loadtime column need execute update? (y/n)\n  #addcol_loadtime_cpart=n                  # loadtime column create partition? to HIVE used.(y/n)\n\n  ## set output source table PK column info. (modify primary key column)\n  ## max support primary key table count 256\n  #pk=owner.table.col1.col2\n\n  ## set redis key&value column info. max support count 256.\n  #kval=\"OWNER.TABLE.COL1.COL2=FCOL1=COL3.COL4\"\n  #kval=\"OWNER.TABLE.COL1.COL2=COL3.COL4\"\n  #kval=\"OWNER.TABLE.COL1.COL2=*\"\n\n  ## set output source table shard key column info. (for UDB)\n  ## max support shard key table count 64\n  #shard_key=owner.table.col1.col2\n\n  ## for cols=... and ncols=... \n  ## create table need filter column? (y/n)\n  create_table_fcol=y\n\n  ## for addcol_XXX, addcol=... create table need add this column? (y/n)\n  create_table_addcol=y\n\n  ## max support (cols & ncols) count 3072\n  ## set output source table column info. (column filter)\n  #cols=owner.table.col1.col2\n  #cols_s=owner.table.col1.col2\n  ## set not output source table column info.\n  #ncols=owner.table.col1.col2\n  #ncols_s=owner.table.col1.col2\n\n  # only output LOB & PKUK column data? (y/n)\n  #only_output_lob_pkuk_col=n\n\n  ## set transfer source table column info. (tansfer column to date)\n  ## max support transfer_col2date count 64\n  #transfer_col2date=owner.table.col1.col2\n\n  ## set transfer source table column info. (desensitization column)\n  ## max support transfer_col table count 2048, col count 32\n  # FUNCTION - trim('AB', 'T')  : 'A' and 'B' replace 'T', only replace left,right\n  #          - trim2('AB', 'T') : 'A' and 'B' replace 'T', replace all\n  #          - shift(1,3,'0aA') : shift char, 0 : number, a : a~z, A : A~Z\n  #          - esc('AB', '\\0x5c')      : 'A' replace '\\A', 'B' replace '\\B', replace all\n  #          - nls_esc('AB', '\\0x5c')  : 'A' replace '\\A', 'B' replace '\\B', replace all\n  #          - hide(N, 6, '*')  : N ~ N+6 replace '*' \n  #          - mess             : mess up the order column data.\n  #          - replace('aD', 'Ad',...)  : 'aD' replace 'Ad', max parameter count 16\n  #\n  #transfer_tcol=owner.table.col1[FUNCTION]\n\n  ## set source table new column output order info.\n  # not support new addcol configure.\n  # max support count: 128\n  #col_order=\"owner.table.col1.col2\"\n\n  ## set target table new column output order info.\n  # only for UDB(java yxad),txad used. support new addcol configure.\n  # max support count: 128\n  #tcol_order=\"source_owner.source_table.target_col1.target_col2\"\n\n  ## set source table column rename info.\n  # max support count: 256\n  #col_rename_s=\"owner.table.src_col.tgt_col\"\n  #col_rename=\"owner.table.src_col.tgt_col\"\n  #col_rename=\"owner.table.src_col.tgt_col(tgt_type_info)\"\n\n  ## set add more column.\n  #add_mcol_s=\"owner.table.column_name[XFUNC()]\"\n  #add_mcol=\"owner.table.column_name[XFUNC()]\"\n  # EX: add_mcol=\"DS.T1.C1[xml_get_col('C1:VARCHAR(64)', 'row,id', 'C2:INT', 'row/c15,m=15')]\"\n\n  ## set monitor abnormal data, max support : 64\n  #abnormal_rows=\"owner.table.where_sql\"\n  #abnormal_rows_s=\"owner.table.where_sql\"\n  # EX: abnormal_rows=\"DS.T1.COL1 > 20\"\n  \n  ## abnormal data find, do operation set.\n  ## do set: f - filter, s - stop, n - none\n  #abnormal_do=f\n\n  ## set need write abnormal data to log file? (y/n)\n  #abnormal_wlog=y\n\n  ## set for transfer col data. max support table count 2048, col 48\n  # EX FUNCTION : HASH(?)\n  # transfer_xcol=owner.table.col[FUNCTION]\n\n  ## set for transfer call DB function. max support table count 32, col 8\n  # EX FUNCTION : ENCRYPT(?)\n  # transfer_db_func=owner.table.col[FUNCTION]\n\n  ## set output source table rows info. (row filter)\n  # max support rows count: 128\n  #rows=\"owner.table.where_sql\"\n  #rows_s=\"owner.table.where_sql\"\n  # EX: rows=\"DS.T1.COL1 > 20\"\n  \n  ## rows no change need filter for update operation? (y/n)\n  #rows_update_no_change_filter=n \n\n  ## set column transfer info. \n  # a single table max support addcol count: 64\n  #addcol_s=\"owner.table.column_name(type info)[expression info]\"\n  #addcol=\"owner.table.column_name(type info)[expression info]\"\n  # EX: addcol=\"DS.T1.ACOL1(int)[COL2+30]\"\n\n  ## set addcol_map_file name for value_decode()\n  # EX: addcol_map_file=\"/dsg/config/transfer_col_map.ini\"\n\n  ## set yloader internal hide column. \n  #addcol_hide_s=\"owner.table.column_name(varchar(12))[expression info]\"\n  #addcol_hide=\"owner.table.column_name(varchar(N))[expression info]\"\n  # EX: addcol_hide=\"DS.T1.?1(varchar(32))[COL2+30]\"\n\n  #sender_used=n                            # use yloader as sender.\n  #sender_odir=                             # set sender output to aiod directory home, no set used aiod -home XXX\n  #sender_rtime=-1                          # sender success remove  xf1/xdt time minutes.\n\n  ## force changed target tablename(map_tname), column name (map_cname)?\n  # L - all changed lower\n  # U - all changed upper\n  # W - included upper and lower not changed, other changed lower.\n  # P - included upper and lower not changed, other changed upper.\n  # EX: map_tname=L \n  map_tname= \n  map_cname= \n\n  ## load table mapping information.\n  ## map, cols, col_rename case sensitive!\n  ## set map example: (MySQL engine: M: myisam, I: innodb, C: ndbcluster)\n  ## schema - owner name    : oracle \n  ##        - schema        : postgresql, greenplum, db2\n  ##        - database name : mysql, sql server\n  #map=src_owner.src_tname:schema.tgt_tname[,engine:M]\n  #map=src_owner.src_tname:schema.tgt_tname[,data_ts:USERS][,index_ts:USERS_IDX][,long_ts:LONG_IDX]\n  #map=src_owner.src_tname:schema.tgt_tname[,owner:DT1]\n  #map=src_owner.src_tname:schema.tgt_tname[,index_name_tag:Y1_]\n  #map=src_owner[.src_tname]:schema[.tgt_tname]\n\nmap=DSG:dsg\n\n  ## tablespace mapping information.\n  ## a - all type, data & index tablespace, d - data tablespace, i - index tablespace\n  #ts_map=\"source_tablespace:target_tablespace,a\"\n\n#############################################################################\n\n# configure for connect DB DB\n# support MySQL:yxad, PostgreSQL:pxad, DBOne:ixad, K-DB:kxad, CDB:cdbd, SUNDB:kxad\n# DB default port: MySQL 3306, PostgreSQL 5432, DBOne 9001\n[YXAC.DB]\n  # connect agentd (yxad, oxad, txad ...) listening host & port\n  service=***********,10021\n\n  # target DB language code(EX: GBK, UTF-8)\n  db_lang=                \n\n  ## set for login database.\n  # example for oracle used OCI connect:\n  #   db_host=$ORACLE_HOME@$TNS_NAME \n  #   db_name=$ORACLE_SID,$ORA12C_PDB_NAME \n  #\n  # used ODBC, example for DB2,HANA,KDB ...\n  #   db_host=\n  #   db_name=$ODBC_DSN_NAME\n  #\n  # used ODBC, example for SQL Server,Informix \n  #   db_host=$DATABASE_NAME\n  #   db_name=$ODBC_DSN_NAME\n  #\n  # example for MySQL,PostgreSQL,GBase,greenplum ...\n  #   db_host=$DB_HOST,\n  #   db_name=$DB_NAME\n  db_host=***********,4000                      # DB running host & port\n  db_name=                                # login DB name or DSN name\n\n  db_user=timsdev                       # login DB username\n  db_pwd=fGFle2xtfgg                         # login DB password\n  encrypt_pwd=y                         # input password is encrypted? (y | n)\n\n  #load_nthr=1                            # load data cache thread count? (1 ~ 16)\n  xsql_rows=65535                          # exec sql max commit rows. (1 ~ 65535)\n  max_brows=3000,3000,3000,3000               # full,real_i,real_d,real_u : max rows of bind. (1 ~ 65535)\n  pack_for_java=n                        # pack data for java mode? (y | n)\n  #send_compress=n                        # yxac send data need compress? (y | n)\n  #send_dump=n                            # yxac send data need dump detail data? (y | n)\n  #debug=n                                # yxac running in debug mode? (y | n)\n\n  ## only HUAWEI mppdb need set trans=n.\n  trans=y                                  # transaction need? exec BEGIN & END? (y | n)\n  #force_used_schema=n                    # set force used schema in SQL? RDS MySQL need. (y | n)\n\n  # loading data speed, 0 - unlimited, (N kb/s)\n  # EX: load_speed=start_hour:end_hour:speed_limited \n  #     load_speed=6:18:1m     # 6<= H <18 speed 1mb/s \n  #     load_speed=10m         # 0 ~ 24 speed 10mb/s \n  #load_speed=0kb/s\n\n  # for internal debug used.\n  #_ignore_load_data=n                      # ignore load data to DB. (y/n)\n  #_ignore_send_data=n                      # ignore send data to *xad. (y/n)\n  #_ignore_send_ddl=n                       # ignore send exec ddl sql to *xad. (y/n)\n\n#############################################################################\n\n# configure for connect MC DB\n# support MySQL:yxad, PostgreSQL:pxad, DBOne:ixad, K-DB:kxad, CDB:cdbd, SUNDB:kxad\n# DB default port: MySQL 3306, PostgreSQL 5432, DBOne 9001\n[YXAC.MC]\n  # connect agentd (yxad, oxad, txad ...) listening host & port\n  #service=\n\n  # target DB language code(EX: GBK, UTF-8)\n  #db_lang=                \n\n  ## set for login database.\n  # example for oracle used OCI connect:\n  #   db_host=$ORACLE_HOME@ \n  #   db_name=$ORACLE_SID, \n  #\n  # used ODBC, example for DB2,HANA,KDB ...\n  #   db_host=\n  #   db_name=$ODBC_DSN_NAME\n  #\n  # used ODBC, example for SQL Server,Informix \n  #   db_host=$DATABASE_NAME\n  #   db_name=$ODBC_DSN_NAME\n  #\n  # example for MySQL,PostgreSQL,GBase,greenplum ...\n  #   db_host=$DB_HOST,\n  #   db_name=$DB_NAME\n  #db_host=                      # DB running host & port\n  #db_name=                                 # login DB name or DSN name\n\n  #db_user=                             # login DB username\n  #db_pwd=                            # login DB password\n  #encrypt_pwd=                            # input password is encrypted? (y | n)\n\n  #load_nthr=1                            # load data cache thread count? (1 ~ 16)\n  xsql_rows=65535                          # exec sql max commit rows. (1 ~ 65535)\n  max_brows=3000,3000,3000,3000               # full,real_i,real_d,real_u : max rows of bind. (1 ~ 65535)\n  #pack_for_java=n                        # pack data for java mode? (y | n)\n  #send_compress=n                        # yxac send data need compress? (y | n)\n  #send_dump=n                            # yxac send data need dump detail data? (y | n)\n  #debug=n                                # yxac running in debug mode? (y | n)\n\n  ## only HUAWEI mppdb need set trans=n.\n  trans=y                                  # transaction need? exec BEGIN & END? (y | n)\n  #force_used_schema=n                    # set force used schema in SQL? RDS MySQL need. (y | n)\n\n  # loading data speed, 0 - unlimited, (N kb/s)\n  # EX: load_speed=start_hour:end_hour:speed_limited \n  #     load_speed=6:18:1m     # 6<= H <18 speed 1mb/s \n  #     load_speed=10m         # 0 ~ 24 speed 10mb/s \n  #load_speed=0kb/s\n\n  # for internal debug used.\n  #_ignore_load_data=n                      # ignore load data to DB. (y/n)\n  #_ignore_send_data=n                      # ignore send data to *xad. (y/n)\n  #_ignore_send_ddl=n                       # ignore send exec ddl sql to *xad. (y/n)\n\n",
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.WriteKeyIni(ctx, WriteKeyIniRequest{
		FileName: "yloader.ini",
		TID:      tid,
		Content:  `[{"real_ddl_fobjn":"n","update_pkuk_col":"n","real_insert_err_fdelete":"y","error_retry_ddl":"22","update_err_used_di":"y","error_retry":"121","real_insert_fobjn":"y","full_lang_from_force":"y","update_used_di":"y","update_filter_col":"y","db_lang":"UTF-8","table_exists_real_do":"none","full_ddl_filter":"y","real_load_completed":"n","real_delete_ftable":"15","countsum_detail_db":"y","table_create_inc_pk":"y","real_insert_fd_mode":"n","addcol_rowid_column":"test","countsum_interval_db":"y","ftable":"17","max_brows":"10000","real_update_ftable":"16","real_ddl_ftable":"22","name":"test","real_insert_fdelete":"n","map_cname":"L","table_create_real_constraint":"y","map_tname":"U","full_insert_ftable":"13","real_delete_fobjn":"n","real_thread":"2","table_create_real_index":"y","countsum_time":89,"full_ddl_ftable":"n","ctype_varchar2binary":"test123","used_quotes":"y","addcol_rowid2uk":"y","mc_schema":"ttt","output_rba":"y","countsum_total_db":"y","real_update_fobjn":"y","filter_string":"r","ctype_varchar2long":"123","table_exists_check":"y","table_create_real":"y","countsum_full_db":"y","queue_name":"test_load","unused_pkuk":"n","output_log2db":"y","defineConfig":"name=test\n","idb_lang":"utf8mb4","[{\"1\":\"2\",\"real_disp_fwait\":\"1000\",\"full_repair_real_quit\":\"y\",\"used_map\":\"y\",\"delay_interval\":\"360\",\"real_disp_size\":\"510\",\"full_copy_unused_size\":\"200KB\",\"update_pkuk_col\":\"y\",\"wait_free_size\":\"10MB\",\"wait_retry\":\"1,30,600,0,65535\",\"sql_trans\":\"y\",\"real_insert_err_fdelete\":\"y\",\"error_retry_ddl\":\"3\",\"table_create_full_idx_end\":\"n\",\"addcol_rowid_invisible\":\"y\",\"error_retry\":\"65534\",\"filter_string_00\":\"y\",\"pack_for_java\":\"n\",\"table_exists_full_do\":\"none\",\"table_exists_real_do\":\"drop\",\"db_lang\":\"\",\"real_load_completed\":\"b\",\"real_disp_fcount\":\"100\",\"full_single_parr\":\"y\",\"real_disp_mode\":\"f\",\"ctype_ifx_decimal\":\"y\",\"full_cindex_error_retry\":\"1\",\"create_table_fcol\":\"y\",\"ses_blen\":\"30MB\",\"db_name\":\"\",\"xsql_rows\":\"65535\",\"full_load_completed\":\"r\",\"max_brows\":\"3000,3000,3000,3000\",\"map_cname\":\"\",\"table_create_real_constraint\":\"y\",\"rowmap_type\":\"S\",\"table_create_full_index\":\"n\",\"map_tname\":\"\",\"trans\":\"y\",\"db_type\":\"oracle\",\"sql_err_change2bind\":\"y\",\"ctype_number_adjust\":\"y\",\"real_thread\":\"1\",\"table_create_real_index\":\"y\",\"lang_gbk2gb18030\":\"y\",\"countsum_time\":\"61\",\"db_pwd\":\"fGFle2xtfgg\",\"countsum_detail\":\"y\",\"table_create_full_constraint\":\"n\",\"used_quotes\":\"y\",\"db_host\":\"***********,4000\",\"table_create_full_index_thread\":\"6\",\"map\":\"DSG:dsg\",\"data_format\":\"xf1\",\"real_disp_fi\":\"y\",\"full_thread\":\"6\",\"encrypt_pwd\":\"y\",\"full_sql_mode\":\"y\",\"update_nobefore\":\"y\",\"table_exists_check\":\"y\",\"home\":\"/home/<USER>/oracle-tidb/wuchao-test-0523a/dt/rmp\",\"table_create_real\":\"y\",\"queue_name\":\"y1\",\"defineConfig\":\"1":"2\\n\",\"service\":\"***********,10001\",\"db_user\":\"timsdev\",\"table_create_full\":\"n\",\"cfg_filename\":\"cfg.loaderno\",\"create_table_addcol\":\"y\"}]","real_insert_ftable":"14","full_load_completed":"b","full_copy":"y","full_sql_mode":"n","full_thread":"22","full_insert_err_fdelete":"y","table_create_full_index":"y","table_create_full_idx_par":"y","table_create_full":"y","table_create_full_idx_end":"y","table_create_full_constraint":"y","table_exists_full_do":"none","full_insert_fdelete":"y","full_copy_size":"2222"}]`,
		Form:     `{"real_ddl_fobjn":"n","update_pkuk_col":"n","real_insert_err_fdelete":"y","error_retry_ddl":"22","update_err_used_di":"y","error_retry":"121","real_insert_fobjn":"y","full_lang_from_force":"y","update_used_di":"y","update_filter_col":"y","db_lang":"UTF-8","table_exists_real_do":null,"full_ddl_filter":"y","real_load_completed":"n","real_delete_ftable":"15","countsum_detail_db":"n","table_create_inc_pk":"y","real_insert_fd_mode":"n","addcol_rowid_column":"test","countsum_interval_db":"n","ftable":"17","max_brows":"10000","real_update_ftable":"16","real_ddl_ftable":null,"name":"test","real_insert_fdelete":"n","map_cname":"L","table_create_real_constraint":"n","map_tname":"U","full_insert_ftable":"13","real_delete_fobjn":"n","real_thread":"2","table_create_real_index":"y","countsum_time":"90","full_ddl_ftable":"n","ctype_varchar2binary":"test123","used_quotes":"y","addcol_rowid2uk":"y","mc_schema":"ttt","output_rba":"y","countsum_total_db":"y","real_update_fobjn":"y","filter_string":"n","ctype_varchar2long":"123","table_exists_check":"y","table_create_real":"n","countsum_full_db":"y","queue_name":"test_load","unused_pkuk":"n","output_log2db":"n","defineConfig":"name=test\n","idb_lang":"utf8mb4","[{\"1\":\"2\",\"real_disp_fwait\":\"1000\",\"full_repair_real_quit\":\"y\",\"used_map\":\"y\",\"delay_interval\":\"360\",\"real_disp_size\":\"510\",\"full_copy_unused_size\":\"200KB\",\"update_pkuk_col\":\"y\",\"wait_free_size\":\"10MB\",\"wait_retry\":\"1,30,600,0,65535\",\"sql_trans\":\"y\",\"real_insert_err_fdelete\":\"y\",\"error_retry_ddl\":\"3\",\"table_create_full_idx_end\":\"n\",\"addcol_rowid_invisible\":\"y\",\"error_retry\":\"65534\",\"filter_string_00\":\"y\",\"pack_for_java\":\"n\",\"table_exists_full_do\":\"none\",\"table_exists_real_do\":\"drop\",\"db_lang\":\"\",\"real_load_completed\":\"b\",\"real_disp_fcount\":\"100\",\"full_single_parr\":\"y\",\"real_disp_mode\":\"f\",\"ctype_ifx_decimal\":\"y\",\"full_cindex_error_retry\":\"1\",\"create_table_fcol\":\"y\",\"ses_blen\":\"30MB\",\"db_name\":\"\",\"xsql_rows\":\"65535\",\"full_load_completed\":\"r\",\"max_brows\":\"3000,3000,3000,3000\",\"map_cname\":\"\",\"table_create_real_constraint\":\"y\",\"rowmap_type\":\"S\",\"table_create_full_index\":\"n\",\"map_tname\":\"\",\"trans\":\"y\",\"db_type\":\"oracle\",\"sql_err_change2bind\":\"y\",\"ctype_number_adjust\":\"y\",\"real_thread\":\"1\",\"table_create_real_index\":\"y\",\"lang_gbk2gb18030\":\"y\",\"countsum_time\":\"61\",\"db_pwd\":\"fGFle2xtfgg\",\"countsum_detail\":\"y\",\"table_create_full_constraint\":\"n\",\"used_quotes\":\"y\",\"db_host\":\"***********,4000\",\"table_create_full_index_thread\":\"6\",\"map\":\"DSG:dsg\",\"data_format\":\"xf1\",\"real_disp_fi\":\"y\",\"full_thread\":\"6\",\"encrypt_pwd\":\"y\",\"full_sql_mode\":\"y\",\"update_nobefore\":\"y\",\"table_exists_check\":\"y\",\"home\":\"/home/<USER>/oracle-tidb/wuchao-test-0523a/dt/rmp\",\"table_create_real\":\"y\",\"queue_name\":\"y1\",\"defineConfig\":\"1":"2\\n\",\"service\":\"***********,10001\",\"db_user\":\"timsdev\",\"table_create_full\":\"n\",\"cfg_filename\":\"cfg.loaderno\",\"create_table_addcol\":\"y\"}]","real_insert_ftable":"14"}`,
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.ClearCache(ctx, ClearCacheRequest{
		TID:    tid,
		Offset: 0,
		Limit:  10,
	})
	assert.Nil(t, err)
	t.Log(got)

}

func Test_adaptor_WriteOrSaveAPIs2(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	got, err = a.SaveDefineMapping(ctx, SaveDefineMappingRequest{
		TID:  tid,
		Real: "real_where= (u.name in ('DSG') and o.name in ('DSG1'))",
		Map: `DSG:dsg
t1:t1
t2:t2
`,
		Full: "full_where= (u.name in ('DSG') and o.name in ('DSG1'))",
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.SaveCompareDefineMappingResult(ctx, SaveCompareDefineMappingResultRequest{
		TID:  tid,
		Real: "real_where= (u.name in ('DSG') and o.name in ('DSG1'))",
		Map: `DSG:dsg
t1:t1
t2:t2
`,
		Full: "full_where= (u.name in ('DSG') and o.name in ('DSG1'))",
	})
	assert.Nil(t, err)
	t.Log(got)

	got, err = a.DownloadLogFileInfo(ctx, DownloadLogFileInfoRequest{
		FileName: "log.vagentd",
		FilePath: "log",
		HostIP:   "***********",
		NodeName: "wuchao-test-0527",
		DataType: "ds",
		Type:     "migrate",
	})
	assert.Nil(t, err)
	t.Log(got)

}

func Test_adaptor_Task_APIs(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	//got, err = a.SaveMappingResult(ctx, SaveMappingResultRequest{
	//	TID:  8,
	//	Rows: `[{"label":"FINDPT","key":"FINDPT","target":"FINDPT"},{"label":"PENADMIN","key":"PENADMIN","target":"PENADMIN"}]`,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.CreateVerifyByMappingIni(ctx, CreateVerifyByMappingIniRequest{
	//	TID: 9,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.SaveTableMappingResult(ctx, SaveTableMappingResultRequest{
	//	TID:  8,
	//	Rows: `[{"key":"FINDPT.ACCOUNT","label":"FINDPT","tableLabel":"ACCOUNT","targetTable":"ACCOUNT","targetLabel":"FINDPT"},{"key":"FINDPT.BRANCH","label":"FINDPT","tableLabel":"BRANCH","targetTable":"BRANCH","targetLabel":"FINDPT"}]`,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.SaveCompareMappingResult(ctx, SaveCompareMappingResultRequest{
	//	TID:         8,
	//	Rows:        `[{"key":"FINDPT.ACCOUNT","label":"FINDPT","tableLabel":"ACCOUNT","targetTable":"ACCOUNT","targetLabel":"FINDPT"},{"key":"FINDPT.BRANCH","label":"FINDPT","tableLabel":"BRANCH","targetTable":"BRANCH","targetLabel":"FINDPT"}]`,
	//	MappingType: 2,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.StopTask(ctx, StopTaskRequest{
	//	TID: 11,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//
	got, err = a.StartTask(ctx, StartTaskRequest{
		TID: 9,
	})
	assert.Nil(t, err)
	t.Log(got)

	//nowTime := time.Now()
	//taskName := "golang_testsuit_" + parse.FormatInt(nowTime.Unix())
	//taskName := `tms_test_case2`
	//got, err = a.SaveTaskInfo(ctx, SaveTaskInfoRequest{
	//	TaskName:      taskName,
	//	InstallStatus: "1",
	//	//TaskParm:      `{"params":{"taskName":"` + taskName + `","type":"1","describe":"` + taskName + `","time":["2024-05-28T16:00:00.000Z","2024-05-29T16:00:00.000Z"]},"migrationInstallation":{"sourIp":"***********","sourPath":"/home/<USER>/oracle-tidb/tttt/ds","subsystem":2,"tarIp":"***********","tarPath":"/home/<USER>/oracle-tidb/tttt/dt"},"sourceDatabaseInformation":{"sourName":16,"Oracle_ASM_TNS":"10210315_asm","Database_Type":"tidb","Oracle_ASM_HOME":"/home/<USER>/oracle-tidb/tttt/ds","Oracle_Home":"/home/<USER>/oracle-tidb/tttt/ds","Oracle_ASM_SID":"","Oracle_SID":"","Oracle_User":"timsdev","Oracle_Password":"fGFle2xtfgg","PDB_name":"","Oracle_TNS":"10210315","data_file":"","Oracle_TNS_admin":"","characterSet":"utf8"},"targetDatabaseInformation":{"tarName":15,"databaseType":"oracle11g","tarAddress":"***********","tarPost":"1521","tarDatabase":"","tarUsername":"tmsadmin","tarPassword":"eGF/bWhhZWI9Pj8M","tarcharacterSet":"utf8"}}`,
	//	TaskParm: `{"params":{"taskName":"tms_test_case2","type":"1","describe":"tms_test_case2","time":["2024-06-19T17:04:08.739Z","2024-07-19T17:04:08.739Z"]},"migrationInstallation":{"sourIp":"***********","sourPath":"/home/<USER>/tms-sub-system/tms_not_exist/ds1","subsystem":8,"tarIp":"***********","tarPath":"/home/<USER>/tms-sub-system/not_exist/dt1"},"sourceDatabaseInformation":{"PDB_name":"","sourName":144,"data_file":"","Oracle_SID":"dsgtest","Oracle_TNS":"","Oracle_Home":"/oracle/app/oracle/product/11.2.0/db_1","Oracle_User":"tmsadmin","characterSet":"utf8","Database_Type":"oracle11g","Oracle_ASM_SID":"","Oracle_ASM_TNS":"","Oracle_ASM_HOME":"","Oracle_ASM_User":"","Oracle_Password":"eGF/bWhhZWI9Pj8M","Oracle_TNS_admin":"","Oracle_ASM_Password":""},"targetDatabaseInformation":{"tarName":143,"databaseType":"tidb","tarAddress":"***********","tarPost":"4000","tarDatabase":"","tarUsername":"timsdev","tarPassword":"fGFle2xtfgg","tarcharacterSet":"utf8"}}`,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
}

func Test_adaptor_Task_CreateTask(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	nowTime := time.Now()
	taskName := "golang_testsuit_" + parse.FormatInt(nowTime.Unix())
	got, err = a.SaveTaskInfo(ctx, SaveTaskInfoRequest{
		TaskName:      taskName,
		InstallStatus: string(InstallStatusSave),
		TaskParm:      `{"params":{"taskName":"` + taskName + `","type":"1","describe":"` + taskName + `","time":["2024-05-28T16:00:00.000Z","2024-05-29T16:00:00.000Z"]},"migrationInstallation":{"sourIp":"***********","sourPath":"/home/<USER>/oracle-tidb/tttt/ds","subsystem":2,"tarIp":"***********","tarPath":"/home/<USER>/oracle-tidb/tttt/dt"},"sourceDatabaseInformation":{"sourName":16,"Oracle_ASM_TNS":"10210315_asm","Database_Type":"tidb","Oracle_ASM_HOME":"/home/<USER>/oracle-tidb/tttt/ds","Oracle_Home":"/home/<USER>/oracle-tidb/tttt/ds","Oracle_ASM_SID":"","Oracle_SID":"","Oracle_User":"timsdev","Oracle_Password":"fGFle2xtfgg","PDB_name":"","Oracle_TNS":"10210315","data_file":"","Oracle_TNS_admin":"","characterSet":"utf8"},"targetDatabaseInformation":{"tarName":15,"databaseType":"oracle11g","tarAddress":"***********","tarPost":"1521","tarDatabase":"","tarUsername":"tmsadmin","tarPassword":"eGF/bWhhZWI9Pj8M","tarcharacterSet":"utf8"}}`,
	})
	assert.Nil(t, err)
	t.Log(got)
}

func Test_adaptor_Task_StartSync(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	got, err = a.Sync(ctx, SyncRequest{
		TID: 49,
	})
	assert.Nil(t, err)
	t.Log(got)
}

func Test_adaptor_Task_CreateSubSystem(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	shortUUID := uuid.NewString()[0:8]
	got, err = a.InsertPhysicalSubSystem(ctx, InsertPhysicalSubSystemRequest{
		UserID:           userId,
		GroupMachineName: constants.TMS_SUB_SYSTEM + shortUUID,
		GroupMachineDesc: constants.TMS_SUB_SYSTEM + shortUUID,
	})
	assert.Nil(t, err)
	t.Log(got)
}

func Test_adaptor_Task_CreateHost(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	got, err = a.InsertNewHostInfo(ctx, InsertNewHostInfoRequest{
		AffDataCenter:       "TMS",
		HostIP:              "***********",
		HostName:            "vm172-16-201-7",
		PhysicalSubSystemId: subSystemId,
		OsName:              "Linux",
		SecretName:          "/home/<USER>",
	})
	assert.Nil(t, err)
	t.Log(got)
}

func Test_adaptor_Task_CreateDataSource(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	got, err = a.SaveDataSourceInfo(ctx, SaveDataSourceInfoRequest{
		PgsqlName:         "",
		MachineIP:         "***********",
		DBType:            "oracle11g",
		IsRac:             false,
		HostIP:            "***********",
		HostPort:          1522,
		DBAliasName:       "",
		DbUser:            "tmsadmin",
		DbPasswd:          "tmsadmin123",
		ServiceName:       "dsgtest",
		S3Url:             "",
		SrcFilePath:       "",
		LinkFlag:          "SID",
		TnsName:           "",
		TnsNamesOraFile:   "",
		DbName:            "tc_ds_test_0619",
		ClusterUser:       "",
		ClusterPassword:   "",
		ClusterUrl:        "",
		AsmOracleHome:     "",
		OracleHome:        "/oracle/app/oracle/product/11.2.0/db_1",
		OracleSid:         "dsgtest",
		AsmHome:           "",
		NlsLang:           "",
		AsmSid:            "",
		PdbFlag:           "",
		Odbc:              "",
		AsmName:           "",
		Pdb:               "",
		PdbName:           "",
		PdbDbName:         "",
		DataCenter:        "TMS",
		PhysicalSubsystem: 8,
		StoreInfo:         "",
		JdbcParam:         "",
		DsgProxyTable:     "",
		CacheLogPath:      "",
		CacheJournalLog:   "",
		HttpPort:          "",
		DnName:            "",
		HostList:          "",
		Gcluster:          "",
		UserId:            0,
		IsBackupDb:        "",
		DbInfoBakup:       "",
		ReplicaSet:        "",
		IsProxy:           false,
		ProxyCluster:      "",
		Asm:               false,
		DbConnectionMode:  "",
		AsmDbIp:           "",
		AsmDbUser:         "",
		AsmDbPasswd:       "",
		AsmDbPort:         0,
		AsmDbName:         "",
	})
	assert.Nil(t, err)
	t.Log(got)
}

func Test_adaptor_Task_MonitorAPIs(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	//got, err = a.GetMonitorList(ctx, GetMonitorListRequest{
	//	UserId: userId,
	//	TID:    150,
	//	Offset: 0,
	//	Limit:  100,
	//})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.GetMonitorDetail(ctx, GetMonitorDetailRequest{
	//	UserId: userId,
	//	TID:    150,
	//})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.GetTableSyncInfo(ctx, GetTableSyncInfoRequest{
	//	TID:    150,
	//	UserID: userId,
	//	Offset: 0,
	//	Limit:  100,
	//})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.ListOperateLog(ctx, ListOperateLogRequest{
	//	Offset:   0,
	//	Limit:    1000,
	//	TaskName: "tms_462_7506",
	//})
	got, err = a.GetHostPorts(ctx, GetHostPortsRequest{
		Offset: 0,
		HostIP: "***********",
		Limit:  10,
	})
	assert.Nil(t, err)
	t.Log(got)

}

func Test_adaptor_Task_RealInfo_APIs(t *testing.T) {
	log.InitTestLogger()
	a := MustNewTestAdaptor()
	ctx := context.TODO()

	var got any
	var err error

	//got, err = a.GetRealTaskInfo(ctx, GetRealTaskInfoRequest{
	//	TaskName: "tms_466_7549",
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetTableRealInfo(ctx, GetTableRealInfoRequest{
	//	TaskName: "tms_466_7549",
	//	Limit:    10,
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetTableDmlDdlInfo(ctx, GetTableDmlDdlInfoRequest{
	//	TaskName:     "tms_466_7549",
	//	AvgMinute:      "5",
	//	IntervalHour: "60",
	//})
	//assert.Nil(t, err)
	//t.Log(got)
	//
	//got, err = a.GetWarningInfo(ctx, GetWarningInfoRequest{
	//	TaskName:   "tms_466_7549",
	//	TargetType: TargetTypeSource,
	//})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.GetWarningInfo(ctx, GetWarningInfoRequest{
	//	TaskName:   "tms_507_7581",
	//	TargetType: TargetTypeSource,
	//})
	//assert.Nil(t, err)
	//t.Log(got)

	//got, err = a.GetRealTaskInfo(ctx, GetRealTaskInfoRequest{
	//	TaskName:   "tms_507_7581",
	//})
	//assert.Nil(t, err)
	//t.Log(got)

	got, err = a.OperationScript(ctx, OperationScriptRequest{
		TID:      187,
		FileName: "kill_18.sh;echo 'EXEXFLAG'=$?",
	})
	assert.Nil(t, err)
	t.Log(got)

	//got, err = a.OperationScript(ctx, OperationScriptRequest{
	//	TID:      187,
	//	FileName: "kill_19.sh;echo 'EXEXFLAG'=$?",
	//})
	//assert.Nil(t, err)
	//t.Log(got)

}
