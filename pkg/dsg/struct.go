package dsg

import (
	"encoding/json"
	"fmt"
)

type ResponseStat int
type InstallStatus string
type TaskType string
type TaskMethod int
type TaskMethodParam string

type ResponseInterface interface {
	Validate() error
}

const (
	Success      ResponseStat = 0
	Processing   ResponseStat = 1
	Failed       ResponseStat = 2
	NotExists    ResponseStat = -1
	TokenInvalid ResponseStat = -103

	InstallStatusSave    InstallStatus = "0"
	InstallStatusInstall InstallStatus = "1"

	TaskMethodFull      TaskMethod = 1
	TaskMethodIncrement TaskMethod = 2
	TaskMethodAll       TaskMethod = 3

	TaskMethodParamFull                 = `set dm 1.1 -cfg -sync afcqM4 -rule r`
	TaskMethodParamIncrementOnly        = `set dm 1.1 -cfg -sync nNM4  -rule r`
	TaskMethodParamIncrementTable       = `set dm 1.1 -cfg -sync nIqBM4  -rule r`
	TaskMethodParamFullIncrement        = `set dm 1.1 -cfg -sync fcqIM4 -rule r`
	TaskMethodParamIncrementOnlyWithSCN = `set dm 1.1 -cfg -sync nNM1 -type us ${SCN_VALUE}`

	SourceVMSyncParam

	TaskTypeTest       TaskType = "1"
	TaskTypeDrill      TaskType = "2"
	TaskTypeProduction TaskType = "3"
)

func (i ResponseStat) IsSuccess() bool {
	return i == Success
}

func (i ResponseStat) Int() int {
	return int(i)
}

func (i ResponseStat) IsProcessing() bool {
	return i == Processing
}

func (i ResponseStat) IsFailed() bool {
	return i == Failed
}

func (i ResponseStat) IsNotExists() bool {
	return i == NotExists
}

type BaseResponse struct {
	StatFlag ResponseStat `json:"statFlag"`
	Message  string       `json:"message"`
}

func (i BaseResponse) Validate() error {
	if !i.StatFlag.IsSuccess() {
		return fmt.Errorf("req failed, statFlag:%d, message:%s", i.StatFlag, i.Message)
	}
	return nil
}

func (i InsertNewHostInfoResponse) Validate() error {
	if !i.StatFlag.IsSuccess() {
		return fmt.Errorf("req failed, statFlag:%d, message:%s", i.StatFlag, i.DataInfo)
	}
	return nil
}

func (i GetPGMDInstallStatusResponse) Validate() error {
	return nil
}

func (i BaseResponse) GetResponse() string {
	return fmt.Sprintf("statFlag:%d, message:%s", i.StatFlag, i.Message)
}

type SysUserGroup struct {
	ID        int    `json:"id"`
	GroupName string `json:"groupName"`
	GroupDesc string `json:"groupDesc"`
	GroupType string `json:"groupType"`
	UpLevelID int    `json:"upLevelId"`
}

type SysUserGroupMap struct {
	UserGroupID int     `json:"userGroupId"`
	UID         int     `json:"uId"`
	GroupDesc   *string `json:"groupDesc"`
	ID          int     `json:"id"`
	Account     string  `json:"account"`
}

type ListSysUserGroupsResponse struct {
	BaseResponse
	DataInfo []SysUserGroup `json:"dataInfo"`
}

type UserInfo struct {
	InputAccountName string      `json:"inputAccountName"`
	UpdatePwdFlag    bool        `json:"updatePwdFlag"`
	TokenID          string      `json:"tokenID"`
	RoleID           string      `json:"roleId"`
	ResultCode       int         `json:"resultCode"`
	UserName         string      `json:"userName"`
	UserID           int         `json:"userID"`
	ResultInfo       string      `json:"resultInfo"`
	UserNameCn       string      `json:"userNameCn"`
	CustomConfig     string      `json:"customConfig"`
	ExpirePwd        bool        `json:"expirePwd"`
	RoleName         string      `json:"roleName"`
	PwdUpdateTime    interface{} `json:"pwdUpdateTime"`
}

type ValidateUserResponse struct {
	BaseResponse
	DataInfo UserInfo `json:"dataInfo"`
}

type ListSysUserGroupMapResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []SysUserGroupMap `json:"rows"`
		Total int               `json:"total"`
	} `json:"dataInfo"`
}

type QueryCustomMachineGroupInfoRequest struct {
}

type ListSysUserGroupMapRequest struct {
}

type ListSysUserGroupsRequest struct {
}

type ValidateUserRequest struct {
	Account      string `json:"account"`
	Password     string `json:"pwd"`
	TokenID      int    `json:"tokenID"`
	Type         string `json:"type"`
	EncryptOrNot string `json:"encryptOrNot"`
}

type QueryPhysicalSubSystemRequest struct {
	UserID           int    `json:"user_id"`
	Offset           int    `json:"offset"`
	Limit            int    `json:"limit"`
	GroupMachineName string `json:"group_machine_name"`
	GroupMachineDesc string `json:"group_machine_desc"`
	CreateTime       string `json:"create_time"`
}

type InsertPhysicalSubSystemRequest struct {
	UserID           int    `json:"user_id"`
	GroupMachineName string `json:"group_machine_name"`
	GroupMachineDesc string `json:"group_machine_desc"`
}

type GetTokenRequest struct {
	UserAgent      string `json:"userAgent"`
	AcceptLanguage string `json:"acceptLanguage"`
	AcceptEncoding string `json:"acceptEncoding"`
	AcceptCharset  string `json:"acceptCharset"`
}

type GetTokenResponse struct {
	BaseResponse
	DataInfo string `json:"dataInfo"`
}

type QueryAllHostInfoRequest struct {
	Offset        int    `json:"offset"`
	Limit         int    `json:"limit"`
	SortOrder     string `json:"sortOrder"`
	UserID        int    `json:"userID"`
	AffDataCenter string `json:"affDataCenter"`
	HostIP        string `json:"hostIp"`
	HostName      string `json:"hostName"`
	UserName      string `json:"userName"`
	HostPort      string `json:"hostPort"`
	PsID          int    `json:"psId"`
	OsName        string `json:"osName"`
	SecretName    string `json:"secretName"`
	Allocation    string `json:"allocation"`
}

type GetDataSourceInfoRequest struct {
	Offset int    `json:"offset"`
	Limit  int    `json:"limit"`
	PsID   int    `json:"psId"`
	UserID int    `json:"userID"`
	DBName string `json:"dbName"`
}

type InsertNewHostInfoRequest struct {
	AffDataCenter       string `json:"aff_data_center"`
	HostIP              string `json:"host_ip"`
	HostName            string `json:"host_name"`
	PhysicalSubSystemId int    `json:"ps_id"`
	OsName              string `json:"os_name"`
	SecretName          string `json:"secret_name"`
}

type SaveDataSourceInfoRequest struct {
	PgsqlName         string `json:"pgsql_name"`
	MachineIP         string `json:"machine_ip"`
	DBType            string `json:"db_type"`
	IsRac             bool   `json:"is_rac"`
	HostIP            string `json:"host_ip"`
	HostPort          int    `json:"host_port"`
	DBAliasName       string `json:"db_alias_name"`
	DbUser            string `json:"db_user"`
	DbPasswd          string `json:"db_passwd"`
	ServiceName       string `json:"service_name"`
	S3Url             string `json:"s3_url"`
	SrcFilePath       string `json:"srcFilePath"`
	LinkFlag          string `json:"link_flag"`
	TnsName           string `json:"tns_name"`
	TnsNamesOraFile   string `json:"tns_names_ora_file"`
	DbName            string `json:"db_name"`
	ClusterUser       string `json:"cluster_user"`
	ClusterPassword   string `json:"cluster_password"`
	ClusterUrl        string `json:"cluster_url"`
	AsmOracleHome     string `json:"asm_oracle_home"`
	OracleHome        string `json:"oracle_home"`
	OracleSid         string `json:"oracle_sid"`
	AsmHome           string `json:"asm_home"`
	NlsLang           string `json:"nls_lang"`
	AsmSid            string `json:"asm_sid"`
	PdbFlag           string `json:"pdb_flag"`
	Odbc              string `json:"odbc"`
	AsmName           string `json:"asm_name"`
	Pdb               string `json:"PDB"`
	PdbName           string `json:"pdb_name"`
	PdbDbName         string `json:"pdb_db_name"`
	DataCenter        string `json:"data_center"`
	PhysicalSubsystem int    `json:"physical_subsystem"`
	StoreInfo         string `json:"store_info"`
	JdbcParam         string `json:"jdbc_param"`
	DsgProxyTable     string `json:"dsg_proxy_table"`
	CacheLogPath      string `json:"cache_log_path"`
	CacheJournalLog   string `json:"cache_journal_log"`
	HttpPort          string `json:"http_port"`
	DnName            string `json:"dn_name"`
	HostList          string `json:"host_list"`
	Gcluster          string `json:"gcluster"`
	UserId            int    `json:"userID"`
	IsBackupDb        string `json:"is_backup_db"`
	DbInfoBakup       string `json:"dbInfo_bakup"`
	ReplicaSet        string `json:"replica_set"`
	IsProxy           bool   `json:"is_proxy"`
	ProxyCluster      string `json:"proxy_cluster"`
	Asm               bool   `json:"asm"`
	DbConnectionMode  string `json:"db_connection_mode"`
	AsmDbIp           string `json:"asm_db_ip"`
	AsmDbUser         string `json:"asm_db_user"`
	AsmDbPasswd       string `json:"asm_db_passwd"`
	AsmDbPort         int    `json:"asm_db_port"`
	AsmDbName         string `json:"asm_db_name"`
}

type UpdateASMDataSourceInfoRequest struct {
	DBId              int    `json:"db_id"`
	PgsqlName         string `json:"pgsql_name"`
	MachineIP         string `json:"machine_ip"`
	DBType            string `json:"db_type"`
	IsRac             bool   `json:"is_rac"`
	HostIP            string `json:"host_ip"`
	HostPort          int    `json:"host_port"`
	DBAliasName       string `json:"db_alias_name"`
	DbUser            string `json:"db_user"`
	DbPasswd          string `json:"db_passwd"`
	ServiceName       string `json:"service_name"`
	S3Url             string `json:"s3_url"`
	SrcFilePath       string `json:"srcFilePath"`
	LinkFlag          string `json:"link_flag"`
	TnsName           string `json:"tns_name"`
	TnsNamesOraFile   string `json:"tns_names_ora_file"`
	DbName            string `json:"db_name"`
	ClusterUser       string `json:"cluster_user"`
	ClusterPassword   string `json:"cluster_password"`
	ClusterUrl        string `json:"cluster_url"`
	AsmOracleHome     string `json:"asm_oracle_home"`
	OracleHome        string `json:"oracle_home"`
	OracleSid         string `json:"oracle_sid"`
	AsmHome           string `json:"asm_home"`
	NlsLang           string `json:"nls_lang"`
	AsmSid            string `json:"asm_sid"`
	PdbFlag           string `json:"pdb_flag"`
	Odbc              string `json:"odbc"`
	AsmName           string `json:"asm_name"`
	Pdb               string `json:"PDB"`
	PdbName           string `json:"pdb_name"`
	PdbDbName         string `json:"pdb_db_name"`
	DataCenter        string `json:"data_center"`
	PhysicalSubsystem int    `json:"physical_subsystem"`
	StoreInfo         string `json:"store_info"`
	JdbcParam         string `json:"jdbc_param"`
	DsgProxyTable     string `json:"dsg_proxy_table"`
	CacheLogPath      string `json:"cache_log_path"`
	CacheJournalLog   string `json:"cache_journal_log"`
	HttpPort          string `json:"http_port"`
	DnName            string `json:"dn_name"`
	HostList          string `json:"host_list"`
	Gcluster          string `json:"gcluster"`
	UserId            int    `json:"userID"`
	IsBackupDb        string `json:"is_backup_db"`
	DbInfoBakup       string `json:"dbInfo_bakup"`
	ReplicaSet        string `json:"replica_set"`
	IsProxy           bool   `json:"is_proxy"`
	ProxyCluster      string `json:"proxy_cluster"`
	Asm               bool   `json:"asm"`
	DbConnectionMode  string `json:"db_connection_mode"`
	AsmDbIp           string `json:"asm_db_ip"`
	AsmDbUser         string `json:"asm_db_user"`
	AsmDbPasswd       string `json:"asm_db_passwd"`
	AsmDbPort         int    `json:"asm_db_port"`
	AsmDbName         string `json:"asm_db_name"`
}

type SelectTaskInfoByStatusRequest struct {
	InstallStatus string `json:"installStatus"`
}

type SelectTaskInfoByNameAndStatusRequest struct {
	TaskName      string `json:"taskName"`
	InstallStatus string `json:"installStatus"`
}

type SaveTaskInfoRequest struct {
	TaskName      string `json:"taskName"`
	InstallStatus string `json:"installStatus"`
	TaskParm      string `json:"taskParm"`
}

type DownloadLogFileInfoRequest struct {
	FileName string `json:"fileName"`
	FilePath string `json:"filePath"`
	HostIP   string `json:"hostIp"`
	NodeName string `json:"nodeName"`
	DataType string `json:"dataType"`
	Type     string `json:"type"`
}

type SelectTaskInfoByTaskNameRequest struct {
	TaskName string `json:"taskName"`
}

type GetPGMDInstallStatusRequest struct {
	TaskName string `json:"taskName"`
}

type ReadSourceDataSourceIniRequest struct {
	FileName string `json:"fileName"  validate:"required,oneof=vm stat select_cond.ini" enums:"vm,stat,select_cond.ini"`
	TID      int    `json:"tId"`
}

type WriteSourceDataSourceIniRequest struct {
	FileName string `json:"fileName"  validate:"required,oneof=vm stat select_cond.ini" enums:"vm,stat,select_cond.ini"`
	TID      int    `json:"tId"`
	Content  string `json:"content"`
}

type ReadTargetDataSourceIniRequest struct {
	FileName string `json:"fileName"`
	TID      int    `json:"tId"`
}

type StartTaskRequest struct {
	TID int `json:"tId"`
}

type StopTaskRequest struct {
	TID int `json:"tId"`
}

type VerifyHostRequest struct {
	AffDataCenter string `json:"aff_data_center"`
	HostIP        string `json:"host_ip" validate:"required"`
	HostName      string `json:"host_name"`
	PSID          int    `json:"ps_id"`
	OSName        string `json:"os_name"`
	SecretName    string `json:"secret_name"`
	MacID         int    `json:"mac_id"`
}

type GetHostPortsRequest struct {
	HostIP    string `json:"host_ip"`
	Limit     int    `json:"limit"`
	Offset    int    `json:"offset"`
	Port      int    `json:"port"`
	SortName  string `json:"sortName"`
	SortOrder string `json:"sortOrder"`
}

type UpdateHostPortInfoRequest struct {
	MacID   int `json:"mac_id"`
	PortMin int `json:"port_min"`
	PortMax int `json:"port_max"`
}

type SelectAllTaskRequest struct {
	TID      int    `json:"tId"`
	Offset   int    `json:"offset"`
	Limit    int    `json:"limit"`
	TaskName string `json:"taskName"`
	UserID   int    `json:"userId"`
}

type SyncRequest struct {
	TID int `json:"tId"`
}

type GetTaskRequest struct {
	TID int `json:"tId"`
}

type DeleteTaskRequest struct {
	TID int `json:"tId"`
}

type OverWriteConfigRequest struct {
	TID        int        `json:"tId"`
	Offset     int        `json:"offset"`
	Limit      int        `json:"limit"`
	TaskMethod TaskMethod `json:"taskMethod"`
}

type ClearCacheRequest struct {
	TID    int `json:"tId"`
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
}

type WriteTargetDataSourceIniRequest struct {
	FileName string `json:"fileName"`
	TID      int    `json:"tId"`
	Content  string `json:"content"`
	Form     string `json:"form"`
}

type WriteKeyIniRequest struct {
	FileName string `json:"fileName"`
	TID      int    `json:"tId"`
	Content  string `json:"content"`
	Form     string `json:"form"`
}

type CreateVerifyByMappingIniRequest struct {
	TID int `json:"tId"`
}

type GetTableSyncInfoRequest struct {
	TID    int `json:"tId"`
	UserID int `json:"userId"`
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
}

type ListOperateLogRequest struct {
	Offset         int    `json:"offset"`
	Limit          int    `json:"limit"`
	TaskName       string `json:"taskName"`
	DataSourceName string `json:"dataSourceName"`
}

type GetTableRealInfoRequest struct {
	TaskName string `json:"taskName"`
	Limit    int    `json:"limit"`
}

type GetRealTaskInfoRequest struct {
	TaskName string `json:"taskName"`
}

type GetTableDmlDdlInfoRequest struct {
	TaskName     string `json:"taskName"`
	AvgMinute    string `json:"avgTime"`
	IntervalHour string `json:"intervalTime"`
}

type GetWarningInfoRequest struct {
	TaskName   string     `json:"taskName"`
	TargetType TargetType `json:"targetType"`
}

type OperationScriptRequest struct {
	TID      int    `json:"tId"`
	FileName string `json:"fileName"`
}

type GetTableSyncInfoResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []TableSyncInfo `json:"rows"`
		Total int             `json:"total"`
	}
}

type OperateLog struct {
	ID            int    `json:"id"`
	OperTitle     string `json:"operTitle"`
	BusinessType  int    `json:"businessType"`
	OperMethod    string `json:"operMethod"`
	RequestMethod string `json:"requestMethod"`
	OperID        int    `json:"operId"`
	OperName      string `json:"operName"`
	RoleName      string `json:"roleName"`
	UsergroupName string `json:"usergroupName"`
	OperURL       string `json:"operUrl"`
	OperIP        string `json:"operIp"`
	OperSource    string `json:"operSource"`
	OperParam     string `json:"operParam"`
	JSONResult    string `json:"jsonResult"`
	OperStatus    int    `json:"operStatus"`
	ErrorMsg      any    `json:"errorMsg"`
	OperTime      string `json:"operTime"`
	BeginTime     any    `json:"beginTime"`
	EndTime       any    `json:"endTime"`
	Limit         int    `json:"limit"`
	Offset        int    `json:"offset"`
	SortName      any    `json:"sortName"`
	UserID        any    `json:"userId"`
}

type ListOperateLogResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []OperateLog `json:"rows"`
		Total int          `json:"total"`
	} `json:"dataInfo"`
}

type GetRealTaskInfoResponse struct {
	BaseResponse
	DataInfo struct {
		RealSpeed      float64 `json:"real_speed(Bytes/s)"`
		NodeName       string  `json:"node_name"`
		NotLoadNum     int     `json:"not_load_num"`
		BeginTime      string  `json:"begin_time"`
		DatabaseSCNOra string  `json:"database_scn_ora"`
		AnalysisSCN    int64   `json:"analysis_scn"`
		FileNo         int     `json:"file_no"`
		AnalysisSCNOra string  `json:"analysis_scn_ora"`
		DatabaseSCN    int64   `json:"database_scn"`
		LastBeginTime  string  `json:"last_begin_time"`
		QNO            int     `json:"qno"`
		UsedTime       float64 `json:"used_time"`
		SizeDsg        int     `json:"size_dsg"`
		DelayTime      float64 `json:"delay_time"`
		LoadTime       string  `json:"load_time"`
	} `json:"dataInfo"`
}

type TableRealInfo struct {
	NodeName   string `json:"node_name"`
	NodeId     int    `json:"node_id"`
	DeleteNum  int    `json:"del_number"`
	UpdateNum  int    `json:"upd_number"`
	InsertNum  int    `json:"ins_number"`
	DdlNum     int    `json:"ddl_number"`
	SchemaName string `json:"owr"`
	TableName  string `json:"tab"`
}

type GetTableRealInfoResponse struct {
	BaseResponse
	DataInfo []TableRealInfo `json:"dataInfo"`
}

type TableDmlDdlInfo struct {
	NodeName           string `json:"node_name"`
	NodeId             int    `json:"node_id"`
	FiveMinuteInterval string `json:"five_minute_interval"`
	SchemaName         string `json:"owr"`
	TableName          string `json:"tab"`
	TotalDelete        int    `json:"total_delete"`
	TotalUpdate        int    `json:"total_update"`
	TotalInsert        int    `json:"total_insert"`
	TotalDdl           int    `json:"total_ddl"`
	TotalChanges       int    `json:"total_changes"`
}

type GetTableDmlDdlInfoResponse struct {
	BaseResponse
	DataInfo []TableDmlDdlInfo `json:"dataInfo"`
}

type GetWarningInfoResponse struct {
	BaseResponse
	DataInfo []WarningInfo `json:"dataInfo"`
}

type OperationScriptResponse struct {
	BaseResponse
	DataInfo []string `json:"dataInfo"`
}

type WarningInfo struct {
	SendEmalFlag int    `json:"send_emal_flag"`
	UpdateTime   string `json:"update_time"`
	ErrorMsg     string `json:"error_msg"`
	TimeStamp    string `json:"time_stamp"`
	ErrorType    string `json:"error_type"`
	NodeName     string `json:"node_name"`
	TargetType   string `json:"target_type"`
	ErrorCode    int    `json:"error_code"`
	MsgLen       int    `json:"msg_len"`
	ErrorStatus  int    `json:"error_status"`
	MsgMD5       string `json:"msg_md5"`
	ErrorCount   int    `json:"error_count"`
}

type TableSyncInfo struct {
	Owner           string `json:"owner"`
	Tabname         string `json:"tabname"`
	TargetStartTime string `json:"target_start_time"`
	SourceEndTime   string `json:"source_end_time"`
	ExportNum       int    `json:"export_num"`
	TargetEndTime   string `json:"target_end_time"`
	SourceStartTime string `json:"source_start_time"`
	LoadNum         int    `json:"load_num"`
	NodeID          int    `json:"node_id"`
}

type GetModuleListRequest struct {
}

type GetMonitorDetailRequest struct {
	UserId int `json:"userId"`
	TID    int `json:"tId"`
}

type GetMonitorListRequest struct {
	UserId int `json:"userId"`
	TID    int `json:"tId"`
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
}

type Module struct {
	ID              int    `json:"id"`
	RID             int    `json:"rId"`
	RName           string `json:"rName"`
	EName           string `json:"eName"`
	Description     string `json:"description"`
	Status          string `json:"status"`
	Limit           int    `json:"limit"`
	Offset          int    `json:"offset"`
	UNum            int    `json:"uNum"`
	SortName        string `json:"sortName"`
	SortOrder       string `json:"sortOrder"`
	Module          string `json:"module"`
	ModuleName      string `json:"moduleName"`
	ModulePath      string `json:"modulePath"`
	ModuleStatus    string `json:"moduleStatus"`
	CreateTime      string `json:"createTime"`
	CreateTimeStart string `json:"createTimeStart"`
	CreateTimeEnd   string `json:"createTimeEnd"`
	UID             int    `json:"uid"`
}

type GetModuleListResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []Module `json:"rows"`
		Total int      `json:"total"`
	} `json:"dataInfo"`
}

type GetMonitorDetailResponse struct {
	BaseResponse
	DataInfo []MonitorInfo `json:"dataInfo"`
}

type GetMonitorListResponse struct {
	BaseResponse
	DataInfo struct {
		Rows []MonitorListItem `json:"rows"`
	} `json:"dataInfo"`
}

type MonitorListItem struct {
	TID        int    `json:"tId"`
	DtPercent  string `json:"dtPercent"`
	UpdateTime string `json:"update_time"`
	CreateTime string `json:"create_time"`

	SyncInfo   string   `json:"sync_info"`
	TaskName   string   `json:"taskName"`
	TaskParam  string   `json:"taskParm"`
	SyncStatus string   `json:"sync_status"`
	TaskType   TaskType `json:"task_type"`

	ResourcesInfo string `json:"resourcesInfo"`
	DsCPURate     string `json:"dsCpuRate"`
	DsDiskRate    string `json:"dsDiskRate"`
	DsMemoryRate  string `json:"dsMemoryRate"`
	DtCPURate     string `json:"dtCpuRate"`
	DtDiskRate    string `json:"dtDiskRate"`
	DtMemoryRate  string `json:"dtMemoryRate"`
}

type MonitorInfo struct {
	Rno        int `json:"rno"`
	Sno        int `json:"sno"`
	FinishTime int `json:"finish_time"`
}

type SaveDefineMappingRequest struct {
	Offset int    `json:"offset"`
	Limit  int    `json:"limit"`
	TID    int    `json:"tId"`
	Real   string `json:"real"`
	Map    string `json:"map"`
	Full   string `json:"full"`
}

type SelectDefineMappingRequest struct {
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
	TID    int `json:"tId"`
}

type SaveMappingResultRequest struct {
	TID  int    `json:"tId"`
	Rows string `json:"rows"`
}

type SaveTableMappingResultRequest struct {
	TID  int    `json:"tId"`
	Rows string `json:"rows"`
}

type SaveCompareMappingResultRequest struct {
	TID         int    `json:"tId"`
	Rows        string `json:"rows"`
	MappingType int    `json:"mappingType"`
}

type SaveCompareDefineMappingResultRequest struct {
	TID  int    `json:"tId"`
	Map  string `json:"map"`
	Real string `json:"real"`
	Full string `json:"full"`
}

type GetUnSelectedUserRequest struct {
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
	TID    int `json:"tId"`
}

type GetUnSelectedTableRequest struct {
	Offset     int    `json:"offset"`
	Limit      int    `json:"limit"`
	TID        int    `json:"tId"`
	SchemaName string `json:"schemaName"`
}

type GetSelectedTableRequest struct {
	Offset     int    `json:"offset"`
	Limit      int    `json:"limit"`
	TID        int    `json:"tId"`
	SchemaName string `json:"schemaName"`
}

type GetUnTargetSelectedTableRequest struct {
	Offset     int    `json:"offset"`
	Limit      int    `json:"limit"`
	TID        int    `json:"tId"`
	SchemaName string `json:"schemaName"`
}

type GetSelectedUserRequest struct {
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
	TID    int `json:"tId"`
}

type SelectAllDataSourceRequest struct {
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
	TID    int `json:"tId"`
}

type GetCompareSelectedUserRequest struct {
	TID int `json:"tId"`
}

type GetCompareSelectedTableRequest struct {
	TID int `json:"tId"`
}

type SelectCompareDefineMappingRequest struct {
	TID int `json:"tId"`
}

type MachineGroup struct {
	GroupMachineName string `json:"group_machine_name"`
	GroupMachineDesc string `json:"group_machine_desc"`
	ID               int    `json:"id"`
	GroupType        string `json:"group_type"`
}

type QueryCustomMachineGroupInfoResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []MachineGroup `json:"rows"`
		Total int            `json:"total"`
	} `json:"dataInfo"`
}

type PhysicalSubSystem struct {
	CreateTime       string `json:"create_time"`
	GroupMachineName string `json:"group_machine_name"`
	GroupMachineDesc string `json:"group_machine_desc"`
	ID               int    `json:"id"`
	GroupType        string `json:"group_type"`
}

type QueryPhysicalSubSystemResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []PhysicalSubSystem `json:"rows"`
		Total int                 `json:"total"`
	} `json:"dataInfo"`
}

type Host struct {
	HostIP         string `json:"host_ip"`
	Allocation     string `json:"allocation"`
	PsName         string `json:"ps_name"`
	XcmpFlag       int    `json:"xcmp_flag"`
	IsAllocation   string `json:"isAllocation"`
	MacID          int    `json:"mac_id"`
	HostType       string `json:"host_type"`
	AffDataCenter  string `json:"aff_data_center"`
	SecretName     string `json:"secret_name"`
	HostStat       int    `json:"host_stat"`
	CreateTime     string `json:"createTime"`
	AllocationPort string `json:"allocationPort"`
	PsID           int    `json:"ps_id"`
	OsName         string `json:"os_name"`
	HostName       string `json:"host_name"`
}

type QueryAllHostInfoResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []Host `json:"rows"`
		Total int    `json:"total"`
	} `json:"dataInfo"`
}

type DataSource struct {
	DbType            string `json:"db_type"`
	PdbDbName         string `json:"pdb_db_name"`
	HostPort          string `json:"host_port"`
	PdbFlag           string `json:"pdb_flag"`
	OracleHome        string `json:"oracle_home"`
	AsmHome           string `json:"asm_home"`
	DbID              int    `json:"db_id"`
	GroupMachineName  string `json:"group_machine_name"`
	AsmDbName         string `json:"asm_db_name,omitempty"`
	AsmDbUser         string `json:"asm_db_user,omitempty"`
	HostIP            string `json:"host_ip"`
	AsmDbPasswd       string `json:"asm_db_passwd,omitempty"`
	ServiceName       string `json:"service_name"`
	MachineIP         string `json:"machine_ip"`
	PdbName           string `json:"pdb_name"`
	DataCenter        string `json:"data_center"`
	PhysicalSubsystem int    `json:"physical_subsystem"`
	AsmDbIP           string `json:"asm_db_ip,omitempty"`
	AsmSid            string `json:"asm_sid"`
	AsmDbPort         string `json:"asm_db_port,omitempty"`
	DbName            string `json:"db_name"`
	DbPasswd          string `json:"db_passwd"`
	CreateTime        string `json:"createTime"`
	DbUser            string `json:"db_user"`
	PgsqlName         string `json:"pgsql_name"`
	LinkFlag          string `json:"link_flag"`
	Asm               string `json:"asm"`
	OracleSid         string `json:"oracle_sid"`
	IsCheck           string `json:"is_check,omitempty"`
}

type DataSources []DataSource

func (d DataSources) Len() int {
	return len(d)
}

func (d DataSources) Swap(i, j int) {
	d[i], d[j] = d[j], d[i]
}

func (d DataSources) Less(i, j int) bool {
	return d[i].DbID > d[j].DbID
}

type GetDataSourceInfoResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []DataSource `json:"rows"`
		Total int          `json:"total"`
	} `json:"dataInfo"`
}

type SelectTaskInfoByStatusResponse struct {
	BaseResponse
	DataInfo []Task `json:"dataInfo"`
}

type SelectTaskInfoByNameAndStatusResponse struct {
	BaseResponse
	DataInfo []Task `json:"dataInfo"`
}

type Task struct {
	TID           int    `json:"tid"`
	TaskName      string `json:"taskName"`
	TaskParm      string `json:"taskParm"`
	TaskType      string `json:"taskType"`
	TaskModel     string `json:"taskModel"`
	TaskMethod    string `json:"taskMethod"`
	InstallStatus string `json:"installStatus"`
	DelStatus     string `json:"delStatus"`
	TaskStatus    string `json:"taskStatus"`
	UpdateTime    string `json:"updateTime"`
	CreateTime    string `json:"createTime"`

	ReceivePort  string `json:"receivePort"`
	AnalysisPort string `json:"analysisPort"`
	TarPath      string `json:"tarPath"`
	SourPath     string `json:"sourPath"`
}

type SaveTaskInfoResponse struct {
	BaseResponse
	DataInfo Task `json:"dataInfo"`
}

type StartTaskResponse struct {
	BaseResponse
}

type StopTaskResponse struct {
	BaseResponse
}

type VerifyHostResponse struct {
	BaseResponse
	DataInfo bool `json:"dataInfo"`
}

type UpdateHostPortInfoResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type GetHostPortsResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []HostPort `json:"rows"`
		Total int        `json:"total"`
	} `json:"dataInfo"`
}

type HostPort struct {
	Notes    string `json:"notes"`
	Port     int    `json:"port"`
	Describe string `json:"describe"`
}

type OverWriteConfigResponse struct {
	BaseResponse
}

type ClearCacheResponse struct {
	BaseResponse
}

type SyncResponse struct {
	BaseResponse
}

type DeleteTaskResponse struct {
	BaseResponse
}

type DownloadLogFileInfoResponse struct {
	BaseResponse
	DataInfo bool `json:"dataInfo"`
}

type TaskLimitOffset struct {
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
	Task
}

type SelectTaskInfoByTaskNameResponse struct {
	BaseResponse
	DataInfo []TaskLimitOffset `json:"dataInfo"`
}

type GetPGMDInstallStatusResponse struct {
	BaseResponse
	DataInfo string `json:"dataInfo"`
}

type SaveDefineMappingResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type SaveMappingResultResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type SaveTableMappingResultResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type SaveCompareMappingResultResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type SaveCompareDefineMappingResultResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type ReadSourceDataSourceIniResponse struct {
	BaseResponse
	DataInfo struct {
		Res string `json:"res"`
	} `json:"dataInfo"`
}

type WriteSourceDataSourceIniResponse struct {
	BaseResponse
}

type WriteTargetDataSourceIniResponse struct {
	BaseResponse
}

type InsertPhysicalSubSystemResponse struct {
	BaseResponse
	DataInfo string `json:"dataInfo"`
}

func (o *InsertPhysicalSubSystemResponse) UnmarshalJSON(data []byte) error {
	failRsp := struct {
		StatFlag ResponseStat `json:"statFlag"`
		DataInfo string       `json:"dataInfo"`
	}{}
	successRsp := struct {
		StatFlag ResponseStat `json:"statFlag"`
		DataInfo struct {
			StatFlag ResponseStat `json:"statFlag"`
			DataInfo bool         `json:"dataInfo"`
		} `json:"dataInfo"`
	}{}
	if unmarshalSuccessErr := json.Unmarshal(data, &successRsp); unmarshalSuccessErr != nil {
		unmarshalFailErr := json.Unmarshal(data, &failRsp)
		if unmarshalFailErr != nil {
			return unmarshalFailErr
		} else {
			o.BaseResponse.StatFlag = failRsp.StatFlag
			o.DataInfo = failRsp.DataInfo
			return nil
		}
	} else {
		o.BaseResponse.StatFlag = successRsp.StatFlag
		o.DataInfo = fmt.Sprintf("statFlag:%d, dataInfo:%t", successRsp.DataInfo.StatFlag, successRsp.DataInfo.DataInfo)
		return nil
	}
}

func (o *SaveTaskInfoResponse) UnmarshalJSON(data []byte) error {
	failRsp := struct {
		StatFlag ResponseStat `json:"statFlag"`
		DataInfo bool         `json:"dataInfo"`
	}{}
	successRsp := struct {
		StatFlag ResponseStat `json:"statFlag"`
		DataInfo Task         `json:"dataInfo"`
	}{}
	if unmarshalSuccessErr := json.Unmarshal(data, &successRsp); unmarshalSuccessErr != nil {
		unmarshalFailErr := json.Unmarshal(data, &failRsp)
		if unmarshalFailErr != nil {
			return unmarshalFailErr
		} else {
			o.BaseResponse.StatFlag = failRsp.StatFlag
			o.BaseResponse.Message = fmt.Sprintf("statFlag:%d, dataInfo:%t", failRsp.StatFlag, failRsp.DataInfo)
			return nil
		}
	} else {
		o.BaseResponse.StatFlag = successRsp.StatFlag
		o.DataInfo = successRsp.DataInfo
		return nil
	}
}

func (o *InsertNewHostInfoResponse) UnmarshalJSON(data []byte) error {
	failRsp := struct {
		StatFlag ResponseStat `json:"statFlag"`
		DataInfo string       `json:"dataInfo"`
	}{}
	successRsp := struct {
		StatFlag ResponseStat `json:"statFlag"`
		DataInfo int          `json:"dataInfo"`
	}{}
	if unmarshalSuccessErr := json.Unmarshal(data, &successRsp); unmarshalSuccessErr != nil {
		unmarshalFailErr := json.Unmarshal(data, &failRsp)
		if unmarshalFailErr != nil {
			return unmarshalFailErr
		} else {
			o.BaseResponse.StatFlag = failRsp.StatFlag
			o.DataInfo = failRsp.DataInfo
			return nil
		}
	} else {
		o.BaseResponse.StatFlag = successRsp.StatFlag
		o.DataInfo = fmt.Sprintf("statFlag:%d, dataInfo:%d", successRsp.StatFlag, successRsp.DataInfo)
		return nil
	}
}

type SaveDataSourceInfoResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type UpdateASMDataSourceInfoResponse struct {
	BaseResponse
	DataInfo int `json:"dataInfo"`
}

type ValidateDataSourceResponse struct {
	BaseResponse
	DataInfo struct {
		FlagAsm   bool `json:"flagAsm"`
		Flag      bool `json:"flag"`
		FlagNoAsm bool `json:"flagNoAsm"`
	} `json:"dataInfo"`
}

type InsertNewHostInfoResponse struct {
	BaseResponse
	DataInfo string `json:"dataInfo"`
}

type WriteKeyIniResponse struct {
	BaseResponse
}

type CreateVerifyByMappingIniResponse struct {
	BaseResponse
}

type TargetDataSourceIni struct {
	RealDispFwait              string `json:"real_disp_fwait"`
	FullRepairRealQuit         string `json:"full_repair_real_quit"`
	UsedMap                    string `json:"used_map"`
	DelayInterval              string `json:"delay_interval"`
	RealDispSize               string `json:"real_disp_size"`
	FullCopyUnusedSize         string `json:"full_copy_unused_size"`
	UpdatePkukCol              string `json:"update_pkuk_col"`
	WaitFreeSize               string `json:"wait_free_size"`
	WaitRetry                  string `json:"wait_retry"`
	SQLTrans                   string `json:"sql_trans"`
	RealInsertErrFdelete       string `json:"real_insert_err_fdelete"`
	ErrorRetryDdl              string `json:"error_retry_ddl"`
	TableCreateFullIdxEnd      string `json:"table_create_full_idx_end"`
	AddcolRowidInvisible       string `json:"addcol_rowid_invisible"`
	ErrorRetry                 string `json:"error_retry"`
	FilterString00             string `json:"filter_string_00"`
	PackForJava                string `json:"pack_for_java"`
	TableExistsFullDo          string `json:"table_exists_full_do"`
	TableExistsRealDo          string `json:"table_exists_real_do"`
	DbLang                     string `json:"db_lang"`
	RealLoadCompleted          string `json:"real_load_completed"`
	RealDispFcount             string `json:"real_disp_fcount"`
	CountsumDetailDb           string `json:"countsum_detail_db"`
	FullInsertErrFdelete       string `json:"full_insert_err_fdelete"`
	FullCopy                   string `json:"full_copy"`
	FullSingleParr             string `json:"full_single_parr"`
	CountsumIntervalDb         string `json:"countsum_interval_db"`
	RealDispMode               string `json:"real_disp_mode"`
	CtypeIfxDecimal            string `json:"ctype_ifx_decimal"`
	FullCindexErrorRetry       string `json:"full_cindex_error_retry"`
	CreateTableFcol            string `json:"create_table_fcol"`
	SesBlen                    string `json:"ses_blen"`
	DbName                     string `json:"db_name"`
	XsqlRows                   string `json:"xsql_rows"`
	FullLoadCompleted          string `json:"full_load_completed"`
	MaxBrows                   string `json:"max_brows"`
	RealDdlFtable              string `json:"real_ddl_ftable"`
	MapCname                   string `json:"map_cname"`
	TableCreateRealConstraint  string `json:"table_create_real_constraint"`
	RowmapType                 string `json:"rowmap_type"`
	TableCreateFullIndex       string `json:"table_create_full_index"`
	MapTname                   string `json:"map_tname"`
	Trans                      string `json:"trans"`
	DbType                     string `json:"db_type"`
	SQLErrChange2Bind          string `json:"sql_err_change2bind"`
	CtypeNumberAdjust          string `json:"ctype_number_adjust"`
	RealThread                 string `json:"real_thread"`
	RealDispRows               string `json:"real_disp_rows"`
	TableCreateRealIndex       string `json:"table_create_real_index"`
	LangGbk2Gb18030            string `json:"lang_gbk2gb18030"`
	CountsumTime               string `json:"countsum_time"`
	DbPwd                      string `json:"db_pwd"`
	CountsumDetail             string `json:"countsum_detail"`
	TableCreateFullConstraint  string `json:"table_create_full_constraint"`
	UsedQuotes                 string `json:"used_quotes"`
	FullInsertFdelete          string `json:"full_insert_fdelete"`
	DbHost                     string `json:"db_host"`
	TableCreateFullIndexThread string `json:"table_create_full_index_thread"`
	Map                        string `json:"map"`
	DataFormat                 string `json:"data_format"`
	RealDispFi                 string `json:"real_disp_fi"`
	FullThread                 string `json:"full_thread"`
	EncryptPwd                 string `json:"encrypt_pwd"`
	FullSQLMode                string `json:"full_sql_mode"`
	FilterString               string `json:"filter_string"`
	TableCreateFullIdxPar      string `json:"table_create_full_idx_par"`
	UpdateNobefore             string `json:"update_nobefore"`
	TableExistsCheck           string `json:"table_exists_check"`
	Home                       string `json:"home"`
	TableCreateReal            string `json:"table_create_real"`
	QueueName                  string `json:"queue_name"`
	OutputLog2Db               string `json:"output_log2db"`
	DefineConfig               string `json:"defineConfig"`
	Service                    string `json:"service"`
	FullCopySize               string `json:"full_copy_size"`
	DbUser                     string `json:"db_user"`
	TableCreateFull            string `json:"table_create_full"`
	CfgFilename                string `json:"cfg_filename"`
	CreateTableAddcol          string `json:"create_table_addcol"`
	RealUpdateFobjn            string `json:"real_update_fobjn"`
	RealInsertFobjn            string `json:"real_insert_fobjn"`
	IdbLang                    string `json:"idb_lang"`
	OutputRba                  string `json:"output_rba"`
	McSchema                   string `json:"mc_schema"`
	CountsumTotalDb            string `json:"countsum_total_db"`
	CountsumFullDb             string `json:"countsum_full_db"`
	UpdateUsedDi               string `json:"update_used_di"`
	RealInsertFdMode           string `json:"real_insert_fd_mode"`
	RealInsertFdelete          string `json:"real_insert_fdelete"`
	UpdateErrUsedDi            string `json:"update_err_used_di"`
	AddcolRowidColumn          string `json:"addcol_rowid_column"`
	AddcolRowid2Uk             string `json:"addcol_rowid2uk"`
	UpdateFilterCol            string `json:"update_filter_col"`
	UnusedPkuk                 string `json:"unused_pkuk"`
	FullLangFromForce          string `json:"full_lang_from_force"`
	CtypeVarchar2Binary        string `json:"ctype_varchar2binary"`
	CtypeVarchar2Long          string `json:"ctype_varchar2long"`
	TableCreateIncPk           string `json:"table_create_inc_pk"`
	FullDdlFilter              string `json:"full_ddl_filter"`
	RealDdlFobjn               string `json:"real_ddl_fobjn"`
	RealCopy                   string `json:"real_copy"`
	RealSqlMode                string `json:"real_sql_mode"`
	RealDeleteFobjn            string `json:"real_delete_fobjn"`
	FullDdlFtable              string `json:"full_ddl_ftable"`
	FullInsertFtable           string `json:"full_insert_ftable"`
	RealDeleteFtable           string `json:"real_delete_ftable"`
	Ftable                     string `json:"ftable"`
	RealUpdateFtable           string `json:"real_update_ftable"`
	RealInsertFtable           string `json:"real_insert_ftable"`
}

type ReadTargetDataSourceIniResponse struct {
	BaseResponse
	DataInfo struct {
		Res  string              `json:"res"`
		Form TargetDataSourceIni `json:"form"`
	} `json:"dataInfo"`
}

type UserLabel struct {
	Label string `json:"label"`
	Key   string `json:"key"`
}

type User struct {
	Label  string `json:"label"`
	Key    string `json:"key"`
	Target string `json:"target"`
}

type GetUnSelectedUserResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []UserLabel `json:"rows"`
		Total int         `json:"total"`
	} `json:"dataInfo"`
}

type TableLabel struct {
	TableLabel string `json:"tableLabel"`
	Label      string `json:"label"`
	Key        string `json:"key"`
}

type Table struct {
	TargetTable string `json:"targetTable"` // target table, lower case
	TargetLabel string `json:"targetLabel"` // target schema, lower case
	TableLabel  string `json:"tableLabel"`  // source table, upper case
	Label       string `json:"label"`       // source schema, upper case
	Key         string `json:"key"`         // source schema+table, format = SCHEMA_NAME.TABLE_NAME
}

type GetUnSelectedTableResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []TableLabel `json:"rows"`
		Total int          `json:"total"`
	} `json:"dataInfo"`
}

type GetSelectedTableResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []Table `json:"rows"`
		Total int     `json:"total"`
	} `json:"dataInfo"`
}

type TargetTable struct {
	TargetTable string `json:"targetTable"`
	TargetLabel string `json:"targetLabel"`
	Key         string `json:"key"`
}

type GetUnTargetSelectedTableResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []TargetTable `json:"rows"`
		Total int           `json:"total"`
	} `json:"dataInfo"`
}

type GetSelectedUserResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []User `json:"rows"`
		Total int    `json:"total"`
	} `json:"dataInfo"`
}

type DataSourceLabel struct {
	Key    string `json:"key"`
	Target string `json:"target"`
}

type SelectAllDataSourceResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []DataSourceLabel `json:"rows"`
		Total int               `json:"total"`
	} `json:"dataInfo"`
}

type GetCompareSelectedUserResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []User `json:"rows"`
		Total int    `json:"total"`
	} `json:"dataInfo"`
}

type GetCompareSelectedTableResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []TableLabel `json:"rows"`
		Total int          `json:"total"`
	} `json:"dataInfo"`
}

type SelectCompareDefineMappingResponse struct {
	BaseResponse
	DataInfo string `json:"dataInfo"`
}

type SelectDefineMappingResponse struct {
	BaseResponse
	DataInfo struct {
		Real string `json:"real"`
		Map  string `json:"map"`
		Full string `json:"full"`
	} `json:"dataInfo"`
}

type SelectAllTaskResponse struct {
	BaseResponse
	DataInfo struct {
		Rows  []Task `json:"rows"`
		Total int    `json:"total"`
	} `json:"dataInfo"`
}

type GetTaskResponse struct {
	BaseResponse
	DataInfo Task `json:"dataInfo"`
}

type SaveTaskRequestBodyParams struct {
	TaskName string   `json:"taskName"`
	Type     string   `json:"type"`
	Describe string   `json:"describe"`
	Time     []string `json:"time"`
}

type SaveTaskRequestBodyMigrationInstallation struct {
	SourIP    string `json:"sourIp"`
	SourPath  string `json:"sourPath"`
	Subsystem int    `json:"subsystem"`
	TarIP     string `json:"tarIp"`
	TarPath   string `json:"tarPath"`
}

type ValidateDataSourceRequest struct {
	PgsqlName         string `json:"pgsql_name"`
	MachineIP         string `json:"machine_ip"`
	DBType            string `json:"db_type"`
	IsRac             bool   `json:"is_rac"`
	HostIP            string `json:"host_ip"`
	HostPort          int    `json:"host_port"`
	DBAliasName       string `json:"db_alias_name"`
	DbUser            string `json:"db_user"`
	DbPasswd          string `json:"db_passwd"`
	ServiceName       string `json:"service_name"`
	S3Url             string `json:"s3_url"`
	SrcFilePath       string `json:"srcFilePath"`
	LinkFlag          string `json:"link_flag"`
	TnsName           string `json:"tns_name"`
	TnsNamesOraFile   string `json:"tns_names_ora_file"`
	DbName            string `json:"db_name"`
	ClusterUser       string `json:"cluster_user"`
	ClusterPassword   string `json:"cluster_password"`
	ClusterUrl        string `json:"cluster_url"`
	AsmOracleHome     string `json:"asm_oracle_home"`
	OracleHome        string `json:"oracle_home"`
	OracleSid         string `json:"oracle_sid"`
	AsmHome           string `json:"asm_home"`
	NlsLang           string `json:"nls_lang"`
	AsmSid            string `json:"asm_sid"`
	PdbFlag           string `json:"pdb_flag"`
	Odbc              string `json:"odbc"`
	AsmName           string `json:"asm_name"`
	Pdb               string `json:"PDB"`
	PdbName           string `json:"pdb_name"`
	PdbDbName         string `json:"pdb_db_name"`
	DataCenter        string `json:"data_center"`
	PhysicalSubsystem int    `json:"physical_subsystem"`
	StoreInfo         string `json:"store_info"`
	JdbcParam         string `json:"jdbc_param"`
	DsgProxyTable     string `json:"dsg_proxy_table"`
	CacheLogPath      string `json:"cache_log_path"`
	CacheJournalLog   string `json:"cache_journal_log"`
	HttpPort          string `json:"http_port"`
	DnName            string `json:"dn_name"`
	HostList          string `json:"host_list"`
	Gcluster          string `json:"gcluster"`
	UserId            int    `json:"userID"`
	IsBackupDb        string `json:"is_backup_db"`
	DbInfoBakup       string `json:"dbInfo_bakup"`
	ReplicaSet        string `json:"replica_set"`
	IsProxy           bool   `json:"is_proxy"`
	ProxyCluster      string `json:"proxy_cluster"`
	Asm               bool   `json:"asm"`
	DbConnectionMode  string `json:"db_connection_mode"`
	AsmDbIp           string `json:"asm_db_ip"`
	AsmDbUser         string `json:"asm_db_user"`
	AsmDbPasswd       string `json:"asm_db_passwd"`
	AsmDbPort         int    `json:"asm_db_port"`
	AsmDbName         string `json:"asm_db_name"`
}

type SaveTaskRequestBodySourceDatabaseInformation struct {
	PDBName           string `json:"PDB_name"`
	SourName          int    `json:"sourName"`
	DataFile          string `json:"data_file"`
	OracleSID         string `json:"Oracle_SID"`
	OracleTNS         string `json:"Oracle_TNS"`
	OracleHome        string `json:"Oracle_Home"`
	OracleUser        string `json:"Oracle_User"`
	CharacterSet      string `json:"characterSet"`
	DatabaseType      string `json:"Database_Type"`
	OracleASMSID      string `json:"Oracle_ASM_SID"`
	OracleASMTNS      string `json:"Oracle_ASM_TNS"`
	OracleASMHOME     string `json:"Oracle_ASM_HOME"`
	OracleASMUser     string `json:"Oracle_ASM_User"`
	OraclePassword    string `json:"Oracle_Password"`
	OracleTNSAdmin    string `json:"Oracle_TNS_admin"`
	OracleASMPassword string `json:"Oracle_ASM_Password"`
}

type SaveTaskRequestBodyTargetDatabaseInformation struct {
	TarName         int    `json:"tarName"`
	DatabaseType    string `json:"databaseType"`
	TarAddress      string `json:"tarAddress"`
	TarPost         string `json:"tarPost"`
	TarDatabase     string `json:"tarDatabase"`
	TarUsername     string `json:"tarUsername"`
	TarPassword     string `json:"tarPassword"`
	TarCharacterSet string `json:"tarcharacterSet"`
}

type SaveTaskRequestBody struct {
	Params                    SaveTaskRequestBodyParams                    `json:"params"`
	MigrationInstallation     SaveTaskRequestBodyMigrationInstallation     `json:"migrationInstallation"`
	SourceDatabaseInformation SaveTaskRequestBodySourceDatabaseInformation `json:"sourceDatabaseInformation"`
	TargetDatabaseInformation SaveTaskRequestBodyTargetDatabaseInformation `json:"targetDatabaseInformation"`
	DefineConfig              string                                       `json:"defineConfig,omitempty"`
}
