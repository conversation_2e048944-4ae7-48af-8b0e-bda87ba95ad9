// Package queries provides SQL query definitions for Oracle transaction statistics.
// This package contains complex Oracle queries for analyzing transaction performance data.
package queries

// Transaction-related query constants for Oracle database operations.
// These queries extract transaction statistics from Oracle's AWR (Automatic Workload Repository).

// GetTransactionDataBlocksSQL retrieves transaction data block statistics with redo per second calculations.
const GetTransactionDataBlocksSQL = `SELECT blockchange_hist.snap_id AS SnapshotID,
       TO_CHAR(blockchange_hist.snaptime, 'YYYY-MM-DD HH24:MI:SS') as SnapshotTime,
       case when transaction_hist.statval=0 then 0
            when transaction_hist.statval>0 then ROUND(blockchange_hist.statval/transaction_hist.statval, 2) end AS Redo_psec
  FROM (SELECT s.snap_id,
               g.value AS stattot,
               s.end_interval_time AS snaptime,
               NVL(DECODE(GREATEST(VALUE,
                                   NVL(lag(VALUE)
                                       OVER(PARTITION BY s.dbid,
                                            s.instance_number,
                                            g.stat_name ORDER BY s.snap_id),
                                       0)),
                          VALUE,
                          VALUE - LAG(VALUE)
                          OVER(PARTITION BY s.dbid,
                               s.instance_number,
                               g.stat_name ORDER BY s.snap_id),
                          VALUE),
                   0) AS statval,
               (EXTRACT(day FROM s.end_interval_time) -
               EXTRACT(day FROM s.begin_interval_time)) * 86400 +
               (EXTRACT(hour FROM s.end_interval_time) -
               EXTRACT(hour FROM s.begin_interval_time)) * 3600 +
               (EXTRACT(minute FROM s.end_interval_time) -
               EXTRACT(minute FROM s.begin_interval_time)) * 60 +
               (EXTRACT(second FROM s.end_interval_time) -
               EXTRACT(second FROM s.begin_interval_time)) as elapsed_time
          FROM dba_hist_snapshot s, dba_hist_sysstat g, v$instance i
         WHERE s.snap_id = g.snap_id
           AND s.begin_interval_time >= sysdate - NVL('15', 0.5)
           AND s.instance_number = i.instance_number
           AND s.instance_number = g.instance_number
           AND g.stat_name = 'db block changes') blockchange_hist,
(SELECT s.snap_id,
               g.value AS stattot,
               s.end_interval_time AS snaptime,
               NVL(DECODE(GREATEST(VALUE,
                                   NVL(lag(VALUE)
                                       OVER(PARTITION BY s.dbid,
                                            s.instance_number,
                                            g.stat_name ORDER BY s.snap_id),
                                       0)),
                          VALUE,
                          VALUE - LAG(VALUE)
                          OVER(PARTITION BY s.dbid,
                               s.instance_number,
                               g.stat_name ORDER BY s.snap_id),
                          VALUE),
                   0) AS statval,
               (EXTRACT(day FROM s.end_interval_time) -
               EXTRACT(day FROM s.begin_interval_time)) * 86400 +
               (EXTRACT(hour FROM s.end_interval_time) -
               EXTRACT(hour FROM s.begin_interval_time)) * 3600 +
               (EXTRACT(minute FROM s.end_interval_time) -
               EXTRACT(minute FROM s.begin_interval_time)) * 60 +
               (EXTRACT(second FROM s.end_interval_time) -
               EXTRACT(second FROM s.begin_interval_time)) as elapsed_time
          FROM dba_hist_snapshot s, dba_hist_sysstat g, v$instance i
         WHERE s.snap_id = g.snap_id
           AND s.begin_interval_time >= sysdate - NVL('15', 0.5)
           AND s.instance_number = i.instance_number
           AND s.instance_number = g.instance_number
           AND g.stat_name = 'user commits' 
           ) transaction_hist           
where blockchange_hist.snap_id=transaction_hist.snap_id   
ORDER BY 1`

// GetTransactionLogVolumeSQL retrieves transaction log volume statistics with redo per second.
const GetTransactionLogVolumeSQL = `
SELECT redo_hist.snap_id AS SnapshotID,
       TO_CHAR(redo_hist.snaptime, 'YYYY-MM-DD HH24:MI:SS') as SnapshotTime,
       ROUND(redo_hist.statval/elapsed_time, 2) AS Redo_psec
  FROM (SELECT s.snap_id,
               g.value AS stattot,
               s.end_interval_time AS snaptime,
               NVL(DECODE(GREATEST(VALUE,
                                   NVL(lag(VALUE)
                                       OVER(PARTITION BY s.dbid,
                                            s.instance_number,
                                            g.stat_name ORDER BY s.snap_id),
                                       0)),
                          VALUE,
                          VALUE - LAG(VALUE)
                          OVER(PARTITION BY s.dbid,
                               s.instance_number,
                               g.stat_name ORDER BY s.snap_id),
                          VALUE),
                   0) AS statval,
               (EXTRACT(day FROM s.end_interval_time) -
               EXTRACT(day FROM s.begin_interval_time)) * 86400 +
               (EXTRACT(hour FROM s.end_interval_time) -
               EXTRACT(hour FROM s.begin_interval_time)) * 3600 +
               (EXTRACT(minute FROM s.end_interval_time) -
               EXTRACT(minute FROM s.begin_interval_time)) * 60 +
               (EXTRACT(second FROM s.end_interval_time) -
               EXTRACT(second FROM s.begin_interval_time)) as elapsed_time
          FROM dba_hist_snapshot s, dba_hist_sysstat g, v$instance i
         WHERE s.snap_id = g.snap_id
           AND s.begin_interval_time >= sysdate - NVL('15', 0.5)
           AND s.instance_number = i.instance_number
           AND s.instance_number = g.instance_number
           AND g.stat_name = 'redo size') redo_hist
 ORDER BY 1`

// GetLogVolumePerSecondSQL retrieves log volume per second statistics.
const GetLogVolumePerSecondSQL = `SELECT transaction_hist.snap_id AS SnapshotID,
       TO_CHAR(transaction_hist.snaptime, 'YYYY-MM-DD HH24:MI:SS') as SnapshotTime,
       ROUND(transaction_hist.statval/elapsed_time, 2) AS transaction_psec
  FROM (SELECT s.snap_id,
               g.value AS stattot,
               s.end_interval_time AS snaptime,
               NVL(DECODE(GREATEST(VALUE,
                                   NVL(lag(VALUE)
                                       OVER(PARTITION BY s.dbid,
                                            s.instance_number,
                                            g.stat_name ORDER BY s.snap_id),
                                       0)),
                          VALUE,
                          VALUE - LAG(VALUE)
                          OVER(PARTITION BY s.dbid,
                               s.instance_number,
                               g.stat_name ORDER BY s.snap_id),
                          VALUE),
                   0) AS statval,
               (EXTRACT(day FROM s.end_interval_time) -
               EXTRACT(day FROM s.begin_interval_time)) * 86400 +
               (EXTRACT(hour FROM s.end_interval_time) -
               EXTRACT(hour FROM s.begin_interval_time)) * 3600 +
               (EXTRACT(minute FROM s.end_interval_time) -
               EXTRACT(minute FROM s.begin_interval_time)) * 60 +
               (EXTRACT(second FROM s.end_interval_time) -
               EXTRACT(second FROM s.begin_interval_time)) as elapsed_time
          FROM dba_hist_snapshot s, dba_hist_sysstat g, v$instance i
         WHERE s.snap_id = g.snap_id
           AND s.begin_interval_time >= sysdate - NVL('15', 0.5)
           AND s.instance_number = i.instance_number
           AND s.instance_number = g.instance_number
           AND g.stat_name = 'user commits'
           ) transaction_hist
 ORDER BY 1`

// GetTransactionPerSecondSQL retrieves transaction per second statistics.
const GetTransactionPerSecondSQL = `SELECT transaction_hist.snap_id AS SnapshotID,
       TO_CHAR(transaction_hist.snaptime, 'YYYY-MM-DD HH24:MI:SS') as SnapshotTime,
       ROUND(transaction_hist.statval/elapsed_time, 2) AS transaction_psec
  FROM (SELECT s.snap_id,
               g.value AS stattot,
               s.end_interval_time AS snaptime,
               NVL(DECODE(GREATEST(VALUE,
                                   NVL(lag(VALUE)
                                       OVER(PARTITION BY s.dbid,
                                            s.instance_number,
                                            g.stat_name ORDER BY s.snap_id),
                                       0)),
                          VALUE,
                          VALUE - LAG(VALUE)
                          OVER(PARTITION BY s.dbid,
                               s.instance_number,
                               g.stat_name ORDER BY s.snap_id),
                          VALUE),
                   0) AS statval,
               (EXTRACT(day FROM s.end_interval_time) -
               EXTRACT(day FROM s.begin_interval_time)) * 86400 +
               (EXTRACT(hour FROM s.end_interval_time) -
               EXTRACT(hour FROM s.begin_interval_time)) * 3600 +
               (EXTRACT(minute FROM s.end_interval_time) -
               EXTRACT(minute FROM s.begin_interval_time)) * 60 +
               (EXTRACT(second FROM s.end_interval_time) -
               EXTRACT(second FROM s.begin_interval_time)) as elapsed_time
          FROM dba_hist_snapshot s, dba_hist_sysstat g, v$instance i
         WHERE s.snap_id = g.snap_id
           AND s.begin_interval_time >= sysdate - NVL('15', 0.5)
           AND s.instance_number = i.instance_number
           AND s.instance_number = g.instance_number
           AND g.stat_name = 'user commits' 
           ) transaction_hist
 ORDER BY 1`
