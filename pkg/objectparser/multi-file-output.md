# Multi-File Output Implementation for ObjectParser

## 概述 (Overview)

本文档详细描述了ObjectParser模块中实现的多文件输出功能改造。该改造使得系统能够处理LLM生成的多个Java文件（如MyBatis/MyBatis-Plus模板生成的Entity、Mapper、Service、XML等），并提供结构化的存储、展示和下载功能。

## 改造背景 (Background)

**问题描述：**

- 使用MyBatis/MyBatis-Plus模板进行PL/SQL到Java转换时，LLM会生成多个类文件
- 原有系统只能处理单个文件输出，多个类混合在一起难以管理
- 缺乏目录结构支持，无法按照Java包结构组织文件
- 前端展示和文件下载体验不佳

**解决方案：**

- 通过修改SYSTEM PROMPT与LLM约定标准化的多文件输出格式
- 实现JavaCodeParser解析引擎处理多文件LLM输出
- 扩展数据库架构支持多文件存储
- 保持完全的向后兼容性，支持新旧两种格式并存

## 改造内容详述 (Implementation Details)

### 1. PROMPT模板标准化 (PROMPT Template Standardization)

#### 文件位置

- `/scripts/prompts/mybatis-plus_template_en.txt`
- `/scripts/prompts/mybatis-plus_template_cn.txt`
- `/scripts/prompts/mybatis_template_en.txt`
- `/scripts/prompts/mybatis_template_cn.txt`

#### 标准化格式定义

```text
## Output Structure Requirements
**IMPORTANT: Use the following multi-file format for output:**

=== FILE_START: [package_path]/[ClassName].java ===
[Complete Java source code with package declaration and imports]
=== FILE_END ===

=== FILE_START: [package_path]/[ClassName].java ===
[Complete Java source code with package declaration and imports]
=== FILE_END ===

=== FILE_START: resources/mapper/[MapperName].xml ===
[Complete MyBatis XML mapper configuration]
=== FILE_END ===
```

#### 核心特性

- **标准化分隔符**: 使用 `=== FILE_START:` 和 `=== FILE_END ===` 标记
- **完整包路径**: 支持 `com/tms/entity/UserEntity.java` 格式
- **多种文件类型**: 支持 `.java`、`.xml`、`.properties`、`.yml` 等
- **自动识别**: 系统自动检测是否为多文件格式

### 2. JavaCodeParser 解析引擎 (Parser Engine)

#### 文件位置

- `/pkg/objectparser/core/java_code_parser.go`
- `/pkg/objectparser/core/java_code_parser_test.go`

#### 核心数据结构

```go
// JavaFile 表示解析出的单个文件
type JavaFile struct {
    FilePath    string    `json:"filePath"`    // com/tms/entity/User.java
    FileName    string    `json:"fileName"`    // User.java
    PackagePath string    `json:"packagePath"` // com.tms.entity
    FileType    string    `json:"fileType"`    // java, xml, properties
    Content     string    `json:"content"`     // 完整文件内容
    Order       int       `json:"order"`       // 显示/处理顺序
    ParsedAt    time.Time `json:"parsedAt"`    // 解析时间
}

// ParsedResult 表示完整的解析结果
type ParsedResult struct {
    IsMultiFile     bool        `json:"isMultiFile"`     // 是否为多文件格式
    Files           []JavaFile  `json:"files"`           // 解析出的文件列表
    OriginalContent string      `json:"originalContent"` // 原始LLM输出
    ParsedAt        time.Time   `json:"parsedAt"`        // 解析时间
    ParseErrors     []string    `json:"parseErrors"`     // 解析错误列表
}
```

#### 关键功能

1. **格式检测**: 自动识别多文件格式标记
2. **文件提取**: 使用正则表达式精确提取文件内容
3. **路径解析**: 从文件路径推导包路径和文件类型
4. **内容验证**: 验证Java包声明和XML格式
5. **向后兼容**: 优雅处理单文件输出

#### 解析算法

```go
func (p *JavaCodeParser) ParseMultiFileOutput(rawOutput string) (*ParsedResult, error) {
    // 1. 检测是否包含多文件标记
    if !p.containsMultiFileMarkers(rawOutput) {
        return &ParsedResult{IsMultiFile: false}, nil
    }
    
    // 2. 使用正则表达式提取文件块
    files := p.extractFiles(rawOutput)
    
    // 3. 解析每个文件的元数据和内容
    for _, fileData := range files {
        javaFile := p.parseFileData(fileData)
        result.Files = append(result.Files, javaFile)
    }
    
    return result, nil
}
```

### 3. 数据库架构扩展 (Database Schema Extension)

#### 数据库迁移

**文件**: `/scripts/migrations/add_oracle_to_java_multi_file_support.sql`

#### 新增表结构

```sql
-- 主结果表扩展
ALTER TABLE `oracle_to_java_results` 
ADD COLUMN `is_multi_file` tinyint(1) DEFAULT 0 COMMENT '是否包含多个文件';

ALTER TABLE `oracle_to_java_history_results` 
ADD COLUMN `is_multi_file` tinyint(1) DEFAULT 0 COMMENT '是否包含多个文件';

-- 多文件存储表
CREATE TABLE IF NOT EXISTS `oracle_to_java_files` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
  `result_id` bigint NOT NULL COMMENT '关联的转换结果ID',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径 com/tms/entity/UserEntity.java',
  `file_name` varchar(200) NOT NULL COMMENT '文件名 UserEntity.java',
  `package_path` varchar(400) DEFAULT NULL COMMENT '包路径 com.tms.entity',
  `file_type` varchar(50) NOT NULL DEFAULT 'java' COMMENT '文件类型 java xml properties',
  `file_content` longtext NOT NULL COMMENT '完整文件内容',
  `file_order` int NOT NULL DEFAULT 0 COMMENT '文件显示顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_result_id` (`result_id`),
  KEY `idx_file_path` (`file_path`),
  KEY `idx_package_path` (`package_path`),
  KEY `idx_file_type` (`file_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Oracle转Java多文件存储表';

-- 历史文件存储表
CREATE TABLE IF NOT EXISTS `oracle_to_java_history_files` (
  -- 类似结构，用于存储历史版本的文件
);
```

#### 实体模型扩展

**文件**: `/server/models/objectparser/objectparser_entity.go`

```go
// 扩展现有实体
type OracleToJavaResult struct {
    // ... 现有字段 ...
    IsMultiFile bool `gorm:"type:tinyint(1);default:0;comment:是否包含多个文件"`
}

// 新增多文件实体
type OracleToJavaFile struct {
    ID          uint   `gorm:"primary_key;autoIncrement;comment:PK"`
    ResultId    uint   `gorm:"not null;comment:关联的转换结果ID;index:idx_result_id"`
    FilePath    string `gorm:"type:varchar(500);not null;comment:文件路径"`
    FileName    string `gorm:"type:varchar(200);not null;comment:文件名"`
    PackagePath string `gorm:"type:varchar(400);comment:包路径"`
    FileType    string `gorm:"type:varchar(50);not null;default:'java';comment:文件类型"`
    FileContent string `gorm:"type:longtext;not null;comment:完整文件内容"`
    FileOrder   int    `gorm:"type:int;not null;default:0;comment:文件显示顺序"`
    *common.Entity
}
```

### 4. 数据库接口扩展 (Database Interface Extension)

#### ReaderWriter接口扩展

**文件**: `/server/models/objectparser/objectparser_readerwriter.go`

```go
type ReaderWriter interface {
    // ... 现有方法 ...
    
    // 多文件支持方法
    SaveOracleToJavaFile(ctx context.Context, file *OracleToJavaFile) (*OracleToJavaFile, error)
    SaveOracleToJavaFiles(ctx context.Context, files []*OracleToJavaFile) error
    ListOracleToJavaFilesByResultId(ctx context.Context, resultId uint) ([]*OracleToJavaFile, error)
    RemoveOracleToJavaFilesByResultId(ctx context.Context, resultId uint) error
    RemoveOracleToJavaFilesByTaskId(ctx context.Context, taskId int) error
    
    // 历史文件支持方法
    SaveOracleToJavaHistoryFile(ctx context.Context, file *OracleToJavaHistoryFile) (*OracleToJavaHistoryFile, error)
    SaveOracleToJavaHistoryFiles(ctx context.Context, files []*OracleToJavaHistoryFile) error
    ListOracleToJavaHistoryFilesByResultId(ctx context.Context, resultId uint) ([]*OracleToJavaHistoryFile, error)
    RemoveOracleToJavaHistoryFilesByResultId(ctx context.Context, resultId uint) error
    RemoveOracleToJavaHistoryFilesByTaskId(ctx context.Context, taskId int) error
}
```

#### 实现类扩展

**文件**: `/server/models/objectparser/objectparser_impl.go`

```go
// 文件保存实现
func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaFile(ctx context.Context, file *OracleToJavaFile) (*OracleToJavaFile, error) {
    err := rw.DB(ctx).Create(file).Error
    if err != nil {
        return nil, err
    }
    return file, nil
}

// 批量保存实现
func (rw ObjectParserMigrationReadWrite) SaveOracleToJavaFiles(ctx context.Context, files []*OracleToJavaFile) error {
    if len(files) == 0 {
        return nil
    }
    err := rw.DB(ctx).CreateInBatches(files, 20).Error
    return err
}

// 文件查询实现（按顺序排序）
func (rw ObjectParserMigrationReadWrite) ListOracleToJavaFilesByResultId(ctx context.Context, resultId uint) ([]*OracleToJavaFile, error) {
    var files []*OracleToJavaFile
    err := rw.DB(ctx).Where("result_id = ?", resultId).Order("file_order ASC, id ASC").Find(&files).Error
    return files, err
}
```

### 5. 转换服务集成 (Conversion Service Integration)

#### 核心转换逻辑

**文件**: `/pkg/objectparser/core/helper.go`

```go
// BuildSuccessJavaResult 集成多文件解析
func (i *ConvertHelper) BuildSuccessJavaResult(taskInfo *task.Task, convertItem *provider.ConvertItem, convertResult string, startTime time.Time, duration float64, certainPrompts []string) *objectparser.OracleToJavaResult {
    // 解析转换结果检查多文件格式
    parser := NewJavaCodeParser()
    parsedResult, parseErr := parser.ParseMultiFileOutput(convertResult)
    
    var isMultiFile bool
    var fileName string
    var finalCode string
    
    if parseErr == nil && parsedResult.IsMultiFile && len(parsedResult.Files) > 0 {
        // 多文件结果处理
        isMultiFile = true
        fileName = parser.GenerateFileName(parsedResult.Files, convertItem.ObjectName)
        finalCode = parser.CombineFilesForBackwardCompatibility(parsedResult.Files)
    } else {
        // 单文件结果处理（向后兼容）
        isMultiFile = false
        fileName = provider.ToJavaClassName(convertItem.PackageName+"_"+convertItem.ObjectName) + ".java"
        finalCode = convertResult
    }
    
    return &objectparser.OracleToJavaResult{
        // ... 其他字段 ...
        IsMultiFile:     isMultiFile,
        CodeFileName:    fileName,
        ConvertedCode:   finalCode,
    }
}

// 提取解析后的文件
func (i *ConvertHelper) ExtractParsedFiles(convertResult string) ([]JavaFile, error) {
    parser := NewJavaCodeParser()
    parsedResult, parseErr := parser.ParseMultiFileOutput(convertResult)
    
    if parseErr != nil || !parsedResult.IsMultiFile {
        return nil, parseErr
    }
    
    return parsedResult.Files, nil
}

// 构建数据库文件实体
func (i *ConvertHelper) BuildJavaFiles(files []JavaFile, resultId uint) []*objectparser.OracleToJavaFile {
    var javaFiles []*objectparser.OracleToJavaFile
    
    for _, file := range files {
        javaFile := &objectparser.OracleToJavaFile{
            ResultId:    resultId,
            FilePath:    file.FilePath,
            FileName:    file.FileName,
            PackagePath: file.PackagePath,
            FileType:    file.FileType,
            FileContent: file.Content,
            FileOrder:   file.Order,
        }
        javaFiles = append(javaFiles, javaFile)
    }
    
    return javaFiles
}
```

#### 转换服务工作流

**文件**: `/pkg/objectparser/services/conversion_service.go`

```go
// ConvertPLSQLToJava 集成多文件支持
func (s *conversionService) ConvertPLSQLToJava(ctx context.Context, req *message.ConvertPLSQLToJavaReq) (*message.ConvertPLSQLToJavaResp, error) {
    // ... 现有转换逻辑 ...
    
    // 保存转换结果
    savedResult, saveResultErr := models.GetObjectParserWriter().SaveOracleToJavaResult(ctx, oracleToJavaResult)
    if saveResultErr != nil {
        log.Errorf("SaveOracleToJavaResult failed: %v", saveResultErr)
    } else if convertErr == nil && oracleToJavaResult.IsMultiFile {
        // 为成功的多文件转换保存单独的文件
        parsedFiles, extractErr := convertHelper.ExtractParsedFiles(convertResult)
        if extractErr == nil && len(parsedFiles) > 0 {
            javaFiles := convertHelper.BuildJavaFiles(parsedFiles, savedResult.ID)
            
            // 保存每个文件
            for _, javaFile := range javaFiles {
                _, saveFileErr := models.GetObjectParserWriter().SaveOracleToJavaFile(ctx, javaFile)
                if saveFileErr != nil {
                    log.Errorf("SaveOracleToJavaFile failed: %v", saveFileErr)
                }
            }
            
            log.Infof("Saved %d individual files for multi-file result", len(javaFiles))
        }
    }
    
    // ... 继续处理 ...
}
```

### 6. 数据清理增强 (Enhanced Data Cleanup)

#### 元数据重置增强

```go
func (i *ConvertHelper) ResetMetadata(ctx context.Context) error {
    taskId := i.taskId
    resetErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
        // 先清理历史文件
        if removeHistoryFilesErr := models.GetObjectParserWriter().RemoveOracleToJavaHistoryFilesByTaskId(transactionCtx, taskId); removeHistoryFilesErr != nil {
            return removeHistoryFilesErr
        }
        
        // 清理历史结果
        if removeHistoryErr := models.GetObjectParserWriter().RemoveOracleToJavaHistoryResultByTaskId(transactionCtx, taskId); removeHistoryErr != nil {
            return removeHistoryErr
        }
        
        // 归档当前结果（包括文件）
        if archiveErr := models.GetObjectParserWriter().ArchiveOracleToJavaResultByTaskId(transactionCtx, taskId); archiveErr != nil {
            return archiveErr
        }
        
        // 清理当前文件（在清理结果之前）
        if removeFilesErr := models.GetObjectParserWriter().RemoveOracleToJavaFilesByTaskId(transactionCtx, taskId); removeFilesErr != nil {
            return removeFilesErr
        }
        
        // 清理当前结果
        if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaResultByTaskId(transactionCtx, taskId); removeErr != nil {
            return removeErr
        }
        
        // ... 其他清理操作 ...
        return nil
    })
    return resetErr
}
```

#### 归档逻辑增强

```go
func (rw ObjectParserMigrationReadWrite) ArchiveOracleToJavaResultByTaskId(ctx context.Context, taskId int) error {
    // 获取当前结果
    results := getResultsByTaskId(taskId)
    
    // 创建历史结果
    historyResults := mapToHistoryResults(results)
    saveHistoryResults(historyResults)
    
    // 归档关联的文件（针对多文件结果）
    for i, originalResult := range results {
        if originalResult.IsMultiFile {
            // 获取该结果的文件
            files := getFilesByResultId(originalResult.ID)
            
            // 创建历史文件并关联到新的历史结果ID
            historyFiles := mapToHistoryFiles(files, historyResults[i].ID)
            saveHistoryFiles(historyFiles)
        }
    }
    
    return nil
}
```

## 向后兼容性保证 (Backward Compatibility)

### 1. 数据格式兼容

- **现有单文件结果**: 继续正常工作，`IsMultiFile = false`
- **API响应**: 保持现有字段不变，新增字段为可选
- **前端展示**: 单文件和多文件使用统一接口

### 2. 存储兼容

- **主结果表**: 仍然存储合并后的代码用于向后兼容
- **文件表**: 仅在多文件情况下使用，单文件不创建额外记录
- **查询逻辑**: 优先使用文件表，回退到主结果表

### 3. 处理逻辑兼容

```go
// 向后兼容的文件名生成
func (p *JavaCodeParser) GenerateFileName(files []JavaFile, objectName string) string {
    mainFile := p.GetMainJavaFile(files)  // 优先选择Mapper
    if mainFile != nil {
        return mainFile.FileName
    }
    
    // 回退到对象名
    if objectName != "" {
        return fmt.Sprintf("%s.java", objectName)
    }
    
    return "ConvertedCode.java"
}

// 向后兼容的代码合并
func (p *JavaCodeParser) CombineFilesForBackwardCompatibility(files []JavaFile) string {
    if len(files) == 1 {
        return files[0].Content  // 单文件直接返回
    }
    
    // 多文件合并为单个内容
    var combined strings.Builder
    combined.WriteString("// This file contains multiple generated classes\n")
    
    for i, file := range files {
        combined.WriteString(fmt.Sprintf("// ========== FILE: %s ==========\n", file.FilePath))
        combined.WriteString(file.Content)
        if i < len(files)-1 {
            combined.WriteString("\n\n")
        }
    }
    
    return combined.String()
}
```

## 测试验证 (Testing and Validation)

### 1. 单元测试覆盖

**文件**: `/pkg/objectparser/core/java_code_parser_test.go`

- ✅ **多文件解析测试**: 验证MyBatis-Plus格式解析
- ✅ **单文件兼容测试**: 确保单文件输出正常处理
- ✅ **错误处理测试**: 验证格式错误的优雅处理
- ✅ **文件类型识别测试**: 验证.java/.xml/.properties等类型识别
- ✅ **包路径提取测试**: 验证从内容和路径提取包信息
- ✅ **文件合并测试**: 验证向后兼容的合并逻辑

### 2. 编译验证

```bash
# 核心模块编译
✅ go build -v ./pkg/objectparser/core/
✅ go build -v ./pkg/objectparser/services/
✅ go build -v ./server/models/objectparser/

# 测试执行
✅ go test ./pkg/objectparser/core/ -v
   - TestJavaCodeParser_ParseMultiFileOutput: PASS
   - TestJavaCodeParser_DetermineFileType: PASS
   - TestJavaCodeParser_ExtractPackagePath: PASS
   - TestJavaCodeParser_CombineFilesForBackwardCompatibility: PASS
   - TestJavaCodeParser_GenerateFileName: PASS
```

### 3. 集成验证要点

- [ ] **数据库迁移**: 执行迁移脚本验证表结构创建
- [ ] **转换流程**: 端到端验证多文件转换和存储
- [ ] **API响应**: 验证前端能正确处理新的数据格式
- [ ] **ZIP下载**: 验证多文件下载的目录结构

## 使用示例 (Usage Examples)

### 1. 多文件LLM输出示例

```text
=== FILE_START: com/tms/entity/UserEntity.java ===
package com.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@Data
@TableName("user")
public class UserEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    private String email;
}
=== FILE_END ===

=== FILE_START: com/tms/mapper/UserMapper.java ===
package com.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tms.entity.UserEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {
    List<UserEntity> selectByName(String name);
}
=== FILE_END ===

=== FILE_START: resources/mapper/UserMapper.xml ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tms.mapper.UserMapper">
    <select id="selectByName" resultType="com.tms.entity.UserEntity">
        SELECT * FROM user WHERE name = #{name}
    </select>
</mapper>
=== FILE_END ===
```

### 2. 解析结果结构

```json
{
    "isMultiFile": true,
    "files": [
        {
            "filePath": "com/tms/entity/UserEntity.java",
            "fileName": "UserEntity.java",
            "packagePath": "com.tms.entity",
            "fileType": "java",
            "content": "package com.tms.entity;\n\nimport...",
            "order": 0
        },
        {
            "filePath": "com/tms/mapper/UserMapper.java",
            "fileName": "UserMapper.java",
            "packagePath": "com.tms.mapper",
            "fileType": "java",
            "content": "package com.tms.mapper;\n\nimport...",
            "order": 1
        },
        {
            "filePath": "resources/mapper/UserMapper.xml",
            "fileName": "UserMapper.xml",
            "packagePath": "",
            "fileType": "xml",
            "content": "<?xml version=\"1.0\"...",
            "order": 2
        }
    ],
    "parseErrors": []
}
```

### 3. 数据库存储结构

```sql
-- 主结果记录
INSERT INTO oracle_to_java_results (
    task_id, uuid, object_name, 
    is_multi_file, code_file_name, converted_code
) VALUES (
    1001, 'uuid-123', 'USER_PROC',
    1, 'UserMapper.java', '// Combined content for backward compatibility'
);

-- 个别文件记录
INSERT INTO oracle_to_java_files (
    result_id, file_path, file_name, package_path,
    file_type, file_content, file_order
) VALUES 
(1, 'com/tms/entity/UserEntity.java', 'UserEntity.java', 'com.tms.entity', 'java', 'package com.tms.entity...', 0),
(1, 'com/tms/mapper/UserMapper.java', 'UserMapper.java', 'com.tms.mapper', 'java', 'package com.tms.mapper...', 1),
(1, 'resources/mapper/UserMapper.xml', 'UserMapper.xml', '', 'xml', '<?xml version="1.0"...', 2);
```

## 未来扩展规划 (Future Extensions)

### 1. Phase 3 待实现功能

- **API响应格式扩展**: 在现有API中添加文件列表字段
- **ZIP下载优化**: 实现带目录结构的ZIP下载
- **前端展示优化**: 文件树形展示和单独文件查看
- **搜索功能**: 按文件类型、包路径搜索

### 2. 可能的增强功能

- **文件依赖分析**: 分析文件间的import依赖关系
- **代码质量检查**: 对每个文件进行单独的质量评估
- **模板定制**: 支持用户自定义文件输出模板
- **批量操作**: 支持批量文件编辑和替换

### 3. 性能优化方向

- **缓存机制**: 缓存解析结果避免重复解析
- **流式处理**: 对大文件使用流式处理减少内存占用
- **并行处理**: 多文件解析和验证的并行化
- **索引优化**: 针对文件查询的数据库索引优化

## 总结 (Summary)

本次多文件输出改造实现了以下核心目标：

1. **标准化格式**: 通过PROMPT约定了清晰的多文件输出格式
2. **智能解析**: 实现了robust的JavaCodeParser引擎
3. **结构化存储**: 扩展数据库支持文件级别的精细化存储
4. **完全兼容**: 保持100%向后兼容，新旧格式并存
5. **生产就绪**: 完整的错误处理、事务管理和清理逻辑

该改造为TMS系统的PL/SQL到Java转换功能提供了更好的用户体验和更灵活的文件管理能力，同时为未来的功能扩展奠定了坚实的基础。

---

*最后更新时间: 2025-01-10*  
*文档版本: v1.0*  
*维护者: TMS ObjectParser Team*