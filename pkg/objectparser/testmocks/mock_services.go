// Code generated by MockGen. DO NOT EDIT.
// Source: interfaces.go

// Package testmocks is a generated GoMock package.
package testmocks

import (
	context "context"
	reflect "reflect"

	services "gitee.com/pingcap_enterprise/tms/pkg/objectparser/services"
	message "gitee.com/pingcap_enterprise/tms/server/message"
	gomock "github.com/golang/mock/gomock"
)

// MockParsingService is a mock of ParsingService interface.
type MockParsingService struct {
	ctrl     *gomock.Controller
	recorder *MockParsingServiceMockRecorder
}

// MockParsingServiceMockRecorder is the mock recorder for MockParsingService.
type MockParsingServiceMockRecorder struct {
	mock *MockParsingService
}

// NewMockParsingService creates a new mock instance.
func NewMockParsingService(ctrl *gomock.Controller) *MockParsingService {
	mock := &MockParsingService{ctrl: ctrl}
	mock.recorder = &MockParsingServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockParsingService) EXPECT() *MockParsingServiceMockRecorder {
	return m.recorder
}

// GetDependencyFromMetadata mocks base method.
func (m *MockParsingService) GetDependencyFromMetadata(ctx context.Context, req *message.GetDependencyFromMetadataReq) (*message.GetDependencyFromMetadataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDependencyFromMetadata", ctx, req)
	ret0, _ := ret[0].(*message.GetDependencyFromMetadataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDependencyFromMetadata indicates an expected call of GetDependencyFromMetadata.
func (mr *MockParsingServiceMockRecorder) GetDependencyFromMetadata(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDependencyFromMetadata", reflect.TypeOf((*MockParsingService)(nil).GetDependencyFromMetadata), ctx, req)
}

// GetServiceStatus mocks base method.
func (m *MockParsingService) GetServiceStatus() services.ServiceStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceStatus")
	ret0, _ := ret[0].(services.ServiceStatus)
	return ret0
}

// GetServiceStatus indicates an expected call of GetServiceStatus.
func (mr *MockParsingServiceMockRecorder) GetServiceStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceStatus", reflect.TypeOf((*MockParsingService)(nil).GetServiceStatus))
}

// ParsePLSQLToAST mocks base method.
func (m *MockParsingService) ParsePLSQLToAST(ctx context.Context, req *message.PlSQLToJSONReq) (*message.PlSQLToJSONResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParsePLSQLToAST", ctx, req)
	ret0, _ := ret[0].(*message.PlSQLToJSONResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParsePLSQLToAST indicates an expected call of ParsePLSQLToAST.
func (mr *MockParsingServiceMockRecorder) ParsePLSQLToAST(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParsePLSQLToAST", reflect.TypeOf((*MockParsingService)(nil).ParsePLSQLToAST), ctx, req)
}

// ValidateConfiguration mocks base method.
func (m *MockParsingService) ValidateConfiguration() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateConfiguration")
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateConfiguration indicates an expected call of ValidateConfiguration.
func (mr *MockParsingServiceMockRecorder) ValidateConfiguration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateConfiguration", reflect.TypeOf((*MockParsingService)(nil).ValidateConfiguration))
}

// MockMetadataService is a mock of MetadataService interface.
type MockMetadataService struct {
	ctrl     *gomock.Controller
	recorder *MockMetadataServiceMockRecorder
}

// MockMetadataServiceMockRecorder is the mock recorder for MockMetadataService.
type MockMetadataServiceMockRecorder struct {
	mock *MockMetadataService
}

// NewMockMetadataService creates a new mock instance.
func NewMockMetadataService(ctrl *gomock.Controller) *MockMetadataService {
	mock := &MockMetadataService{ctrl: ctrl}
	mock.recorder = &MockMetadataServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMetadataService) EXPECT() *MockMetadataServiceMockRecorder {
	return m.recorder
}

// GetIncompatibleFeatureScoring mocks base method.
func (m *MockMetadataService) GetIncompatibleFeatureScoring(ctx context.Context, req *message.GetIncompatibleFeatureScoringReq) (*message.GetIncompatibleFeatureScoringResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncompatibleFeatureScoring", ctx, req)
	ret0, _ := ret[0].(*message.GetIncompatibleFeatureScoringResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncompatibleFeatureScoring indicates an expected call of GetIncompatibleFeatureScoring.
func (mr *MockMetadataServiceMockRecorder) GetIncompatibleFeatureScoring(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncompatibleFeatureScoring", reflect.TypeOf((*MockMetadataService)(nil).GetIncompatibleFeatureScoring), ctx, req)
}

// GetObjectDetail mocks base method.
func (m *MockMetadataService) GetObjectDetail(ctx context.Context, req *message.GetObjectDetailReq) (*message.GetObjectDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjectDetail", ctx, req)
	ret0, _ := ret[0].(*message.GetObjectDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjectDetail indicates an expected call of GetObjectDetail.
func (mr *MockMetadataServiceMockRecorder) GetObjectDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjectDetail", reflect.TypeOf((*MockMetadataService)(nil).GetObjectDetail), ctx, req)
}

// GetDefinitionsFromMetadata mocks base method.
func (m *MockMetadataService) GetDefinitionsFromMetadata(ctx context.Context, req *message.GetDefinitionsFromMetadataReq) (*message.GetDefinitionsFromMetadataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefinitionsFromMetadata", ctx, req)
	ret0, _ := ret[0].(*message.GetDefinitionsFromMetadataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefinitionsFromMetadata indicates an expected call of GetDefinitionsFromMetadata.
func (mr *MockMetadataServiceMockRecorder) GetDefinitionsFromMetadata(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefinitionsFromMetadata", reflect.TypeOf((*MockMetadataService)(nil).GetDefinitionsFromMetadata), ctx, req)
}

// GetServiceStatus mocks base method.
func (m *MockMetadataService) GetServiceStatus() services.ServiceStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceStatus")
	ret0, _ := ret[0].(services.ServiceStatus)
	return ret0
}

// GetServiceStatus indicates an expected call of GetServiceStatus.
func (mr *MockMetadataServiceMockRecorder) GetServiceStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceStatus", reflect.TypeOf((*MockMetadataService)(nil).GetServiceStatus))
}

// ListAnalyzeDetail mocks base method.
func (m *MockMetadataService) ListAnalyzeDetail(ctx context.Context, req *message.ListAnalyzeDetailReq) (*message.ListAnalyzeDetailResp, *message.Page, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAnalyzeDetail", ctx, req)
	ret0, _ := ret[0].(*message.ListAnalyzeDetailResp)
	ret1, _ := ret[1].(*message.Page)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListAnalyzeDetail indicates an expected call of ListAnalyzeDetail.
func (mr *MockMetadataServiceMockRecorder) ListAnalyzeDetail(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAnalyzeDetail", reflect.TypeOf((*MockMetadataService)(nil).ListAnalyzeDetail), ctx, req)
}

// ListBasicIncompatibleFeature mocks base method.
func (m *MockMetadataService) ListBasicIncompatibleFeature(ctx context.Context, req *message.ListBasicIncompatibleFeatureReq) (*message.ListBasicIncompatibleFeatureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBasicIncompatibleFeature", ctx, req)
	ret0, _ := ret[0].(*message.ListBasicIncompatibleFeatureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBasicIncompatibleFeature indicates an expected call of ListBasicIncompatibleFeature.
func (mr *MockMetadataServiceMockRecorder) ListBasicIncompatibleFeature(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBasicIncompatibleFeature", reflect.TypeOf((*MockMetadataService)(nil).ListBasicIncompatibleFeature), ctx, req)
}

// ListTaskIncompatibleFeature mocks base method.
func (m *MockMetadataService) ListTaskIncompatibleFeature(ctx context.Context, req *message.ListTaskIncompatibleFeatureReq) (*message.ListTaskIncompatibleFeatureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskIncompatibleFeature", ctx, req)
	ret0, _ := ret[0].(*message.ListTaskIncompatibleFeatureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTaskIncompatibleFeature indicates an expected call of ListTaskIncompatibleFeature.
func (mr *MockMetadataServiceMockRecorder) ListTaskIncompatibleFeature(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskIncompatibleFeature", reflect.TypeOf((*MockMetadataService)(nil).ListTaskIncompatibleFeature), ctx, req)
}

// UpdateBasicIncompatibleFeature mocks base method.
func (m *MockMetadataService) UpdateBasicIncompatibleFeature(ctx context.Context, req *message.UpdateBasicIncompatibleFeatureReq) (*message.UpdateBasicIncompatibleFeatureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBasicIncompatibleFeature", ctx, req)
	ret0, _ := ret[0].(*message.UpdateBasicIncompatibleFeatureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBasicIncompatibleFeature indicates an expected call of UpdateBasicIncompatibleFeature.
func (mr *MockMetadataServiceMockRecorder) UpdateBasicIncompatibleFeature(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBasicIncompatibleFeature", reflect.TypeOf((*MockMetadataService)(nil).UpdateBasicIncompatibleFeature), ctx, req)
}

// UpdateTaskIncompatibleFeature mocks base method.
func (m *MockMetadataService) UpdateTaskIncompatibleFeature(ctx context.Context, req *message.UpdateTaskIncompatibleFeatureReq) (*message.UpdateTaskIncompatibleFeatureResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTaskIncompatibleFeature", ctx, req)
	ret0, _ := ret[0].(*message.UpdateTaskIncompatibleFeatureResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaskIncompatibleFeature indicates an expected call of UpdateTaskIncompatibleFeature.
func (mr *MockMetadataServiceMockRecorder) UpdateTaskIncompatibleFeature(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaskIncompatibleFeature", reflect.TypeOf((*MockMetadataService)(nil).UpdateTaskIncompatibleFeature), ctx, req)
}

// ValidateConfiguration mocks base method.
func (m *MockMetadataService) ValidateConfiguration() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateConfiguration")
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateConfiguration indicates an expected call of ValidateConfiguration.
func (mr *MockMetadataServiceMockRecorder) ValidateConfiguration() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateConfiguration", reflect.TypeOf((*MockMetadataService)(nil).ValidateConfiguration))
}

// MockConversionService is a mock of ConversionService interface.
type MockConversionService struct {
	ctrl     *gomock.Controller
	recorder *MockConversionServiceMockRecorder
}

// MockConversionServiceMockRecorder is the mock recorder for MockConversionService.
type MockConversionServiceMockRecorder struct {
	mock *MockConversionService
}

// NewMockConversionService creates a new mock instance.
func NewMockConversionService(ctrl *gomock.Controller) *MockConversionService {
	mock := &MockConversionService{ctrl: ctrl}
	mock.recorder = &MockConversionServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockConversionService) EXPECT() *MockConversionServiceMockRecorder {
	return m.recorder
}

// ConvertPLSQLToJava mocks base method.
func (m *MockConversionService) ConvertPLSQLToJava(ctx context.Context, req *message.ConvertPLSQLToJavaReq) (*message.ConvertPLSQLToJavaResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertPLSQLToJava", ctx, req)
	ret0, _ := ret[0].(*message.ConvertPLSQLToJavaResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertPLSQLToJava indicates an expected call of ConvertPLSQLToJava.
func (mr *MockConversionServiceMockRecorder) ConvertPLSQLToJava(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertPLSQLToJava", reflect.TypeOf((*MockConversionService)(nil).ConvertPLSQLToJava), ctx, req)
}

// ConvertPLSQLToJavaInManual mocks base method.
func (m *MockConversionService) ConvertPLSQLToJavaInManual(ctx context.Context, req *message.ConvertPLSQLToJavaInManualReq) (*message.ConvertPLSQLToJavaInManualResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertPLSQLToJavaInManual", ctx, req)
	ret0, _ := ret[0].(*message.ConvertPLSQLToJavaInManualResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertPLSQLToJavaInManual indicates an expected call of ConvertPLSQLToJavaInManual.
func (mr *MockConversionServiceMockRecorder) ConvertPLSQLToJavaInManual(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertPLSQLToJavaInManual", reflect.TypeOf((*MockConversionService)(nil).ConvertPLSQLToJavaInManual), ctx, req)
}

// DownloadHistoryJavaCodes mocks base method.
func (m *MockConversionService) DownloadHistoryJavaCodes(ctx context.Context, req *message.DownloadHistoryJavaCodesReq) (*message.DownloadJavaCodesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadHistoryJavaCodes", ctx, req)
	ret0, _ := ret[0].(*message.DownloadJavaCodesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadHistoryJavaCodes indicates an expected call of DownloadHistoryJavaCodes.
func (mr *MockConversionServiceMockRecorder) DownloadHistoryJavaCodes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadHistoryJavaCodes", reflect.TypeOf((*MockConversionService)(nil).DownloadHistoryJavaCodes), ctx, req)
}

// DownloadJavaCodes mocks base method.
func (m *MockConversionService) DownloadJavaCodes(ctx context.Context, req *message.DownloadJavaCodesReq) (*message.DownloadJavaCodesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DownloadJavaCodes", ctx, req)
	ret0, _ := ret[0].(*message.DownloadJavaCodesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DownloadJavaCodes indicates an expected call of DownloadJavaCodes.
func (mr *MockConversionServiceMockRecorder) DownloadJavaCodes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DownloadJavaCodes", reflect.TypeOf((*MockConversionService)(nil).DownloadJavaCodes), ctx, req)
}

// GetPLSQLToJavaHistoryResults mocks base method.
func (m *MockConversionService) GetPLSQLToJavaHistoryResults(ctx context.Context, req *message.GetPLSQLToJavaHistoryResultsReq) (*message.GetPLSQLToJavaHistoryResultsResp, *message.Page, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPLSQLToJavaHistoryResults", ctx, req)
	ret0, _ := ret[0].(*message.GetPLSQLToJavaHistoryResultsResp)
	ret1, _ := ret[1].(*message.Page)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPLSQLToJavaHistoryResults indicates an expected call of GetPLSQLToJavaHistoryResults.
func (mr *MockConversionServiceMockRecorder) GetPLSQLToJavaHistoryResults(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPLSQLToJavaHistoryResults", reflect.TypeOf((*MockConversionService)(nil).GetPLSQLToJavaHistoryResults), ctx, req)
}

// GetPLSQLToJavaLogs mocks base method.
func (m *MockConversionService) GetPLSQLToJavaLogs(ctx context.Context, req *message.GetPLSQLToJavaLogsReq) (*message.GetPLSQLToJavaLogsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPLSQLToJavaLogs", ctx, req)
	ret0, _ := ret[0].(*message.GetPLSQLToJavaLogsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPLSQLToJavaLogs indicates an expected call of GetPLSQLToJavaLogs.
func (mr *MockConversionServiceMockRecorder) GetPLSQLToJavaLogs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPLSQLToJavaLogs", reflect.TypeOf((*MockConversionService)(nil).GetPLSQLToJavaLogs), ctx, req)
}

// GetPLSQLToJavaResults mocks base method.
func (m *MockConversionService) GetPLSQLToJavaResults(ctx context.Context, req *message.GetPLSQLToJavaResultsReq) (*message.GetPLSQLToJavaResultsResp, *message.Page, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPLSQLToJavaResults", ctx, req)
	ret0, _ := ret[0].(*message.GetPLSQLToJavaResultsResp)
	ret1, _ := ret[1].(*message.Page)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPLSQLToJavaResults indicates an expected call of GetPLSQLToJavaResults.
func (mr *MockConversionServiceMockRecorder) GetPLSQLToJavaResults(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPLSQLToJavaResults", reflect.TypeOf((*MockConversionService)(nil).GetPLSQLToJavaResults), ctx, req)
}

// GetPLSQLToJavaSummary mocks base method.
func (m *MockConversionService) GetPLSQLToJavaSummary(ctx context.Context, req *message.GetPLSQLToJavaSummaryReq) (*message.GetPLSQLToJavaSummaryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPLSQLToJavaSummary", ctx, req)
	ret0, _ := ret[0].(*message.GetPLSQLToJavaSummaryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPLSQLToJavaSummary indicates an expected call of GetPLSQLToJavaSummary.
func (mr *MockConversionServiceMockRecorder) GetPLSQLToJavaSummary(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPLSQLToJavaSummary", reflect.TypeOf((*MockConversionService)(nil).GetPLSQLToJavaSummary), ctx, req)
}

// TestAIConnect mocks base method.
func (m *MockConversionService) TestAIConnect(ctx context.Context, req *message.TestAIConnectReq) (*message.TestAIConnectResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TestAIConnect", ctx, req)
	ret0, _ := ret[0].(*message.TestAIConnectResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestAIConnect indicates an expected call of TestAIConnect.
func (mr *MockConversionServiceMockRecorder) TestAIConnect(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestAIConnect", reflect.TypeOf((*MockConversionService)(nil).TestAIConnect), ctx, req)
}

// MockArchiveService is a mock of ArchiveService interface.
type MockArchiveService struct {
	ctrl     *gomock.Controller
	recorder *MockArchiveServiceMockRecorder
}

// MockArchiveServiceMockRecorder is the mock recorder for MockArchiveService.
type MockArchiveServiceMockRecorder struct {
	mock *MockArchiveService
}

// NewMockArchiveService creates a new mock instance.
func NewMockArchiveService(ctrl *gomock.Controller) *MockArchiveService {
	mock := &MockArchiveService{ctrl: ctrl}
	mock.recorder = &MockArchiveServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockArchiveService) EXPECT() *MockArchiveServiceMockRecorder {
	return m.recorder
}

// GetArchiveData mocks base method.
func (m *MockArchiveService) GetArchiveData(ctx context.Context, req *message.GetArchiveDataReq) (*message.GetArchiveDataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchiveData", ctx, req)
	ret0, _ := ret[0].(*message.GetArchiveDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchiveData indicates an expected call of GetArchiveData.
func (mr *MockArchiveServiceMockRecorder) GetArchiveData(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchiveData", reflect.TypeOf((*MockArchiveService)(nil).GetArchiveData), ctx, req)
}

// GetArchiveTimes mocks base method.
func (m *MockArchiveService) GetArchiveTimes(ctx context.Context, req *message.GetArchiveTimesReq) (*message.GetArchiveTimesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetArchiveTimes", ctx, req)
	ret0, _ := ret[0].(*message.GetArchiveTimesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetArchiveTimes indicates an expected call of GetArchiveTimes.
func (mr *MockArchiveServiceMockRecorder) GetArchiveTimes(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetArchiveTimes", reflect.TypeOf((*MockArchiveService)(nil).GetArchiveTimes), ctx, req)
}

// GetLogVolumePerSecond mocks base method.
func (m *MockArchiveService) GetLogVolumePerSecond(ctx context.Context, req *message.GetLogVolumePerSecondReq) (*message.GetLogVolumePerSecondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogVolumePerSecond", ctx, req)
	ret0, _ := ret[0].(*message.GetLogVolumePerSecondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogVolumePerSecond indicates an expected call of GetLogVolumePerSecond.
func (mr *MockArchiveServiceMockRecorder) GetLogVolumePerSecond(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogVolumePerSecond", reflect.TypeOf((*MockArchiveService)(nil).GetLogVolumePerSecond), ctx, req)
}

// GetTransactionDataBlocks mocks base method.
func (m *MockArchiveService) GetTransactionDataBlocks(ctx context.Context, req *message.GetTransactionDataBlocksReq) (*message.GetTransactionDataBlocksResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionDataBlocks", ctx, req)
	ret0, _ := ret[0].(*message.GetTransactionDataBlocksResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionDataBlocks indicates an expected call of GetTransactionDataBlocks.
func (mr *MockArchiveServiceMockRecorder) GetTransactionDataBlocks(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionDataBlocks", reflect.TypeOf((*MockArchiveService)(nil).GetTransactionDataBlocks), ctx, req)
}

// GetTransactionLogVolume mocks base method.
func (m *MockArchiveService) GetTransactionLogVolume(ctx context.Context, req *message.GetTransactionLogVolumeReq) (*message.GetTransactionLogVolumeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionLogVolume", ctx, req)
	ret0, _ := ret[0].(*message.GetTransactionLogVolumeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionLogVolume indicates an expected call of GetTransactionLogVolume.
func (mr *MockArchiveServiceMockRecorder) GetTransactionLogVolume(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionLogVolume", reflect.TypeOf((*MockArchiveService)(nil).GetTransactionLogVolume), ctx, req)
}

// GetTransactionPerSecond mocks base method.
func (m *MockArchiveService) GetTransactionPerSecond(ctx context.Context, req *message.GetTransactionPerSecondReq) (*message.GetTransactionPerSecondResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionPerSecond", ctx, req)
	ret0, _ := ret[0].(*message.GetTransactionPerSecondResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionPerSecond indicates an expected call of GetTransactionPerSecond.
func (mr *MockArchiveServiceMockRecorder) GetTransactionPerSecond(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionPerSecond", reflect.TypeOf((*MockArchiveService)(nil).GetTransactionPerSecond), ctx, req)
}

// MockPromptService is a mock of PromptService interface.
type MockPromptService struct {
	ctrl     *gomock.Controller
	recorder *MockPromptServiceMockRecorder
}

// MockPromptServiceMockRecorder is the mock recorder for MockPromptService.
type MockPromptServiceMockRecorder struct {
	mock *MockPromptService
}

// NewMockPromptService creates a new mock instance.
func NewMockPromptService(ctrl *gomock.Controller) *MockPromptService {
	mock := &MockPromptService{ctrl: ctrl}
	mock.recorder = &MockPromptServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPromptService) EXPECT() *MockPromptServiceMockRecorder {
	return m.recorder
}

// DeletePrompt mocks base method.
func (m *MockPromptService) DeletePrompt(ctx context.Context, req *message.DeletePromptReq) (*message.DeletePromptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePrompt", ctx, req)
	ret0, _ := ret[0].(*message.DeletePromptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePrompt indicates an expected call of DeletePrompt.
func (mr *MockPromptServiceMockRecorder) DeletePrompt(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePrompt", reflect.TypeOf((*MockPromptService)(nil).DeletePrompt), ctx, req)
}

// DeleteTaskObjectPromptRelation mocks base method.
func (m *MockPromptService) DeleteTaskObjectPromptRelation(ctx context.Context, req *message.DeleteTaskObjectPromptRelationReq) (*message.DeleteTaskObjectPromptRelationResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTaskObjectPromptRelation", ctx, req)
	ret0, _ := ret[0].(*message.DeleteTaskObjectPromptRelationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTaskObjectPromptRelation indicates an expected call of DeleteTaskObjectPromptRelation.
func (mr *MockPromptServiceMockRecorder) DeleteTaskObjectPromptRelation(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTaskObjectPromptRelation", reflect.TypeOf((*MockPromptService)(nil).DeleteTaskObjectPromptRelation), ctx, req)
}

// ListPrompt mocks base method.
func (m *MockPromptService) ListPrompt(ctx context.Context, req *message.ListPromptReq) (*message.ListPromptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPrompt", ctx, req)
	ret0, _ := ret[0].(*message.ListPromptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPrompt indicates an expected call of ListPrompt.
func (mr *MockPromptServiceMockRecorder) ListPrompt(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPrompt", reflect.TypeOf((*MockPromptService)(nil).ListPrompt), ctx, req)
}

// ListTaskObjectPromptRelation mocks base method.
func (m *MockPromptService) ListTaskObjectPromptRelation(ctx context.Context, req *message.ListTaskObjectPromptRelationReq) (*message.ListTaskObjectPromptRelationResp, *message.Page, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaskObjectPromptRelation", ctx, req)
	ret0, _ := ret[0].(*message.ListTaskObjectPromptRelationResp)
	ret1, _ := ret[1].(*message.Page)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListTaskObjectPromptRelation indicates an expected call of ListTaskObjectPromptRelation.
func (mr *MockPromptServiceMockRecorder) ListTaskObjectPromptRelation(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaskObjectPromptRelation", reflect.TypeOf((*MockPromptService)(nil).ListTaskObjectPromptRelation), ctx, req)
}

// SavePrompt mocks base method.
func (m *MockPromptService) SavePrompt(ctx context.Context, req *message.SavePromptReq) (*message.SavePromptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SavePrompt", ctx, req)
	ret0, _ := ret[0].(*message.SavePromptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SavePrompt indicates an expected call of SavePrompt.
func (mr *MockPromptServiceMockRecorder) SavePrompt(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePrompt", reflect.TypeOf((*MockPromptService)(nil).SavePrompt), ctx, req)
}

// SaveTaskObjectPromptRelation mocks base method.
func (m *MockPromptService) SaveTaskObjectPromptRelation(ctx context.Context, req *message.SaveTaskObjectPromptRelationReq) (*message.SaveTaskObjectPromptRelationResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveTaskObjectPromptRelation", ctx, req)
	ret0, _ := ret[0].(*message.SaveTaskObjectPromptRelationResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveTaskObjectPromptRelation indicates an expected call of SaveTaskObjectPromptRelation.
func (mr *MockPromptServiceMockRecorder) SaveTaskObjectPromptRelation(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveTaskObjectPromptRelation", reflect.TypeOf((*MockPromptService)(nil).SaveTaskObjectPromptRelation), ctx, req)
}

// MockServiceFactory is a mock of ServiceFactory interface.
type MockServiceFactory struct {
	ctrl     *gomock.Controller
	recorder *MockServiceFactoryMockRecorder
}

// MockServiceFactoryMockRecorder is the mock recorder for MockServiceFactory.
type MockServiceFactoryMockRecorder struct {
	mock *MockServiceFactory
}

// NewMockServiceFactory creates a new mock instance.
func NewMockServiceFactory(ctrl *gomock.Controller) *MockServiceFactory {
	mock := &MockServiceFactory{ctrl: ctrl}
	mock.recorder = &MockServiceFactoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceFactory) EXPECT() *MockServiceFactoryMockRecorder {
	return m.recorder
}

// CreateArchiveService mocks base method.
func (m *MockServiceFactory) CreateArchiveService() services.ArchiveService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateArchiveService")
	ret0, _ := ret[0].(services.ArchiveService)
	return ret0
}

// CreateArchiveService indicates an expected call of CreateArchiveService.
func (mr *MockServiceFactoryMockRecorder) CreateArchiveService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateArchiveService", reflect.TypeOf((*MockServiceFactory)(nil).CreateArchiveService))
}

// CreateConversionService mocks base method.
func (m *MockServiceFactory) CreateConversionService() services.ConversionService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConversionService")
	ret0, _ := ret[0].(services.ConversionService)
	return ret0
}

// CreateConversionService indicates an expected call of CreateConversionService.
func (mr *MockServiceFactoryMockRecorder) CreateConversionService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConversionService", reflect.TypeOf((*MockServiceFactory)(nil).CreateConversionService))
}

// CreateMetadataService mocks base method.
func (m *MockServiceFactory) CreateMetadataService() services.MetadataService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMetadataService")
	ret0, _ := ret[0].(services.MetadataService)
	return ret0
}

// CreateMetadataService indicates an expected call of CreateMetadataService.
func (mr *MockServiceFactoryMockRecorder) CreateMetadataService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMetadataService", reflect.TypeOf((*MockServiceFactory)(nil).CreateMetadataService))
}

// CreateParsingService mocks base method.
func (m *MockServiceFactory) CreateParsingService() services.ParsingService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateParsingService")
	ret0, _ := ret[0].(services.ParsingService)
	return ret0
}

// CreateParsingService indicates an expected call of CreateParsingService.
func (mr *MockServiceFactoryMockRecorder) CreateParsingService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateParsingService", reflect.TypeOf((*MockServiceFactory)(nil).CreateParsingService))
}

// CreatePromptService mocks base method.
func (m *MockServiceFactory) CreatePromptService() services.PromptService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePromptService")
	ret0, _ := ret[0].(services.PromptService)
	return ret0
}

// CreatePromptService indicates an expected call of CreatePromptService.
func (mr *MockServiceFactoryMockRecorder) CreatePromptService() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePromptService", reflect.TypeOf((*MockServiceFactory)(nil).CreatePromptService))
}
