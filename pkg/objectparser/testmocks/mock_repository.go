// Code generated by MockGen. DO NOT EDIT.
// Source: repository.go

// Package testmocks is a generated GoMock package.
package testmocks

import (
	context "context"
	sql "database/sql"
	reflect "reflect"

	objectparser "gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetOracleObjectDefinition mocks base method.
func (m *MockRepository) GetOracleObjectDefinition(ctx context.Context, taskId int, schemaObjectKey string) (*objectparser.OracleObjectDefinition, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOracleObjectDefinition", ctx, taskId, schemaObjectKey)
	ret0, _ := ret[0].(*objectparser.OracleObjectDefinition)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOracleObjectDefinition indicates an expected call of GetOracleObjectDefinition.
func (mr *MockRepositoryMockRecorder) GetOracleObjectDefinition(ctx, taskId, schemaObjectKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOracleObjectDefinition", reflect.TypeOf((*MockRepository)(nil).GetOracleObjectDefinition), ctx, taskId, schemaObjectKey)
}

// SaveParsedObjectDefinition mocks base method.
func (m *MockRepository) SaveParsedObjectDefinition(ctx context.Context, definition *objectparser.OracleObjectDefinition) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveParsedObjectDefinition", ctx, definition)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveParsedObjectDefinition indicates an expected call of SaveParsedObjectDefinition.
func (mr *MockRepositoryMockRecorder) SaveParsedObjectDefinition(ctx, definition interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveParsedObjectDefinition", reflect.TypeOf((*MockRepository)(nil).SaveParsedObjectDefinition), ctx, definition)
}

// GetOracleDBConnection mocks base method.
func (m *MockRepository) GetOracleDBConnection(ctx context.Context, taskId int) (*sql.DB, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOracleDBConnection", ctx, taskId)
	ret0, _ := ret[0].(*sql.DB)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOracleDBConnection indicates an expected call of GetOracleDBConnection.
func (mr *MockRepositoryMockRecorder) GetOracleDBConnection(ctx, taskId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOracleDBConnection", reflect.TypeOf((*MockRepository)(nil).GetOracleDBConnection), ctx, taskId)
}

// Note: This is a simplified mock for testing purposes.
// Additional repository methods can be added as needed.
