// Code generated by MockGen. DO NOT EDIT.
// Source: adaptor.go

// Package testmocks is a generated GoMock package.
package testmocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockAdaptor is a mock of Adaptor interface.
type MockAdaptor struct {
	ctrl     *gomock.Controller
	recorder *MockAdaptorMockRecorder
}

// MockAdaptorMockRecorder is the mock recorder for MockAdaptor.
type MockAdaptorMockRecorder struct {
	mock *MockAdaptor
}

// NewMockAdaptor creates a new mock instance.
func NewMockAdaptor(ctrl *gomock.Controller) *MockAdaptor {
	mock := &MockAdaptor{ctrl: ctrl}
	mock.recorder = &MockAdaptorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAdaptor) EXPECT() *MockAdaptorMockRecorder {
	return m.recorder
}

// ExecuteOperation mocks base method for testing.
func (m *MockAdaptor) ExecuteOperation(ctx context.Context, operation string, params interface{}) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecuteOperation", ctx, operation, params)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExecuteOperation indicates an expected call of ExecuteOperation.
func (mr *MockAdaptorMockRecorder) ExecuteOperation(ctx, operation, params interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecuteOperation", reflect.TypeOf((*MockAdaptor)(nil).ExecuteOperation), ctx, operation, params)
}

// Note: This is a simplified mock for testing purposes.
// The actual Adaptor interface may have more methods.
