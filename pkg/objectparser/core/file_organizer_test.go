package core

import (
	"context"
	"testing"

	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"github.com/stretchr/testify/assert"
)

func TestFileOrganizer_ResolveFilePathConflict(t *testing.T) {
	tests := []struct {
		name          string
		filePaths     []string
		expectedPaths []string
	}{
		{
			name: "no conflicts",
			filePaths: []string{
				"com/tms/dao/UserDao.java",
				"com/tms/dao/ProductDao.java",
				"com/tms/service/UserService.java",
			},
			expectedPaths: []string{
				"com/tms/dao/UserDao.java",
				"com/tms/dao/ProductDao.java",
				"com/tms/service/UserService.java",
			},
		},
		{
			name: "java file conflicts",
			filePaths: []string{
				"com/tms/dao/UserDao.java",
				"com/tms/dao/UserDao.java",
				"com/tms/dao/UserDao.java",
				"com/tms/dao/ProductDao.java",
			},
			expectedPaths: []string{
				"com/tms/dao/UserDao.java",
				"com/tms/dao/UserDao_dup1.java",
				"com/tms/dao/UserDao_dup2.java",
				"com/tms/dao/ProductDao.java",
			},
		},
		{
			name: "xml file conflicts",
			filePaths: []string{
				"resources/mapper/UserMapper.xml",
				"resources/mapper/UserMapper.xml",
				"resources/mapper/ProductMapper.xml",
				"resources/mapper/UserMapper.xml",
			},
			expectedPaths: []string{
				"resources/mapper/UserMapper.xml",
				"resources/mapper/UserMapper_dup1.xml",
				"resources/mapper/ProductMapper.xml",
				"resources/mapper/UserMapper_dup2.xml",
			},
		},
		{
			name: "properties file conflicts",
			filePaths: []string{
				"resources/application.properties",
				"resources/application.properties",
				"resources/database.properties",
				"resources/application.properties",
			},
			expectedPaths: []string{
				"resources/application.properties",
				"resources/application_dup1.properties",
				"resources/database.properties",
				"resources/application_dup2.properties",
			},
		},
		{
			name: "mixed file types in same directory",
			filePaths: []string{
				"com/tms/config/AppConfig.java",
				"com/tms/config/AppConfig.xml",
				"com/tms/config/AppConfig.properties",
				"com/tms/config/AppConfig.java",
			},
			expectedPaths: []string{
				"com/tms/config/AppConfig.java",
				"com/tms/config/AppConfig.xml",
				"com/tms/config/AppConfig.properties",
				"com/tms/config/AppConfig_dup1.java",
			},
		},
		{
			name: "conflicts in different directories",
			filePaths: []string{
				"com/tms/dao/UserDao.java",
				"com/tms/service/UserDao.java",
				"com/tms/dao/UserDao.java",
				"com/tms/controller/UserDao.java",
			},
			expectedPaths: []string{
				"com/tms/dao/UserDao.java",
				"com/tms/service/UserDao.java",
				"com/tms/dao/UserDao_dup1.java",
				"com/tms/controller/UserDao.java",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			organizer := NewFileOrganizer()
			actualPaths := make([]string, 0, len(tt.filePaths))

			for _, path := range tt.filePaths {
				resolvedPath := organizer.ResolveFilePathConflict(path)
				actualPaths = append(actualPaths, resolvedPath)
			}

			assert.Equal(t, tt.expectedPaths, actualPaths)
		})
	}
}

// Mock implementation for testing
type mockConversionResult struct {
	id             uint
	taskId         int
	uuid           string
	objectName     string
	convertedCode  string
	convertPrompts string
	isMultiFile    bool
	codeFileName   string
}

func (m *mockConversionResult) GetID() uint               { return m.id }
func (m *mockConversionResult) GetTaskId() int            { return m.taskId }
func (m *mockConversionResult) GetUUID() string           { return m.uuid }
func (m *mockConversionResult) GetObjectName() string     { return m.objectName }
func (m *mockConversionResult) GetConvertedCode() string  { return m.convertedCode }
func (m *mockConversionResult) GetConvertPrompts() string { return m.convertPrompts }
func (m *mockConversionResult) IsMultiFileResult() bool   { return m.isMultiFile }
func (m *mockConversionResult) GetCodeFileName() string   { return m.codeFileName }

func TestFileOrganizer_ProcessConversionResult(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name          string
		result        ConversionResult
		expectedCount int
		expectedFile  string
		expectedPkg   string
	}{
		{
			name: "single file with package",
			result: &mockConversionResult{
				id:         1,
				taskId:     100,
				uuid:       "uuid-1",
				objectName: "USER_DAO",
				convertedCode: `package com.tms.dao;

public class UserDao {
    // code here
}`,
				convertPrompts: "prompt1@,@prompt2",
				isMultiFile:    false,
			},
			expectedCount: 1,
			expectedFile:  "com/tms/dao/UserDao.java",
			expectedPkg:   "com.tms.dao",
		},
		{
			name: "single file with default package",
			result: &mockConversionResult{
				id:         2,
				taskId:     100,
				uuid:       "uuid-2",
				objectName: "PRODUCT_SERVICE",
				convertedCode: `public class ProductService {
    // no package declaration
}`,
				convertPrompts: "prompt1",
				isMultiFile:    false,
			},
			expectedCount: 1,
			expectedFile:  "com/tms/dao/ProductService.java",
			expectedPkg:   "com.tms.dao",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			organizer := NewFileOrganizer()
			fileInfos, err := organizer.ProcessConversionResult(ctx, tt.result, false)

			assert.NoError(t, err)
			assert.Len(t, fileInfos, tt.expectedCount)

			if tt.expectedCount > 0 {
				assert.Equal(t, tt.expectedFile, fileInfos[0].FilePath)
				assert.Equal(t, tt.expectedPkg, fileInfos[0].PackagePath)
				assert.Equal(t, tt.result.GetUUID(), fileInfos[0].ObjectUUID)
			}
		})
	}
}

func TestFileOrganizer_BuildFileTree(t *testing.T) {
	fileInfos := []FileInfo{
		{
			FilePath:       "com/tms/dao/UserDao.java",
			FileName:       "UserDao.java",
			PackagePath:    "com.tms.dao",
			FileContent:    "content1",
			ObjectUUID:     "uuid1",
			ConvertPrompts: []string{"prompt1"},
		},
		{
			FilePath:       "com/tms/dao/ProductDao.java",
			FileName:       "ProductDao.java",
			PackagePath:    "com.tms.dao",
			FileContent:    "content2",
			ObjectUUID:     "uuid2",
			ConvertPrompts: []string{"prompt2"},
		},
		{
			FilePath:       "com/tms/service/UserService.java",
			FileName:       "UserService.java",
			PackagePath:    "com.tms.service",
			FileContent:    "content3",
			ObjectUUID:     "uuid3",
			ConvertPrompts: []string{"prompt3"},
		},
		{
			FilePath:       "resources/mapper/UserMapper.xml",
			FileName:       "UserMapper.xml",
			PackagePath:    "",
			FileContent:    "xml content",
			ObjectUUID:     "uuid4",
			ConvertPrompts: []string{"prompt4"},
		},
	}

	organizer := NewFileOrganizer()
	tree := organizer.BuildFileTree(fileInfos)

	// Verify root nodes
	assert.Len(t, tree, 2) // com and resources

	// Find com node
	var comNode, resourcesNode interface{}
	for _, node := range tree {
		if node.Title == "com" {
			comNode = node
		} else if node.Title == "resources" {
			resourcesNode = node
		}
	}

	assert.NotNil(t, comNode)
	assert.NotNil(t, resourcesNode)

	// Verify structure depth and files
	// This is a simplified test - in practice you'd verify the complete tree structure
}

func TestFileOrganizer_CompleteIntegration(t *testing.T) {
	ctx := context.Background()

	// Create test data with conflicts
	results := []ConversionResult{
		&mockConversionResult{
			id:         1,
			taskId:     100,
			uuid:       "uuid-1",
			objectName: "USER_DAO",
			convertedCode: `package com.tms.dao;
public class UserDao {}`,
			isMultiFile: false,
		},
		&mockConversionResult{
			id:         2,
			taskId:     100,
			uuid:       "uuid-2",
			objectName: "USER_DAO", // Same name - should create conflict
			convertedCode: `package com.tms.dao;
public class UserDao {}`,
			isMultiFile: false,
		},
		&mockConversionResult{
			id:         3,
			taskId:     100,
			uuid:       "uuid-3",
			objectName: "USER_DAO", // Another conflict
			convertedCode: `package com.tms.dao;
public class UserDao {}`,
			isMultiFile: false,
		},
	}

	// Process all results
	organizer := NewFileOrganizer()
	fileInfos, err := organizer.ProcessAllResults(ctx, results, false)

	assert.NoError(t, err)
	assert.Len(t, fileInfos, 3)

	// Verify file paths are unique with proper conflict resolution
	expectedPaths := []string{
		"com/tms/dao/UserDao.java",
		"com/tms/dao/UserDao_dup1.java",
		"com/tms/dao/UserDao_dup2.java",
	}

	actualPaths := make([]string, 0, len(fileInfos))
	for _, info := range fileInfos {
		actualPaths = append(actualPaths, info.FilePath)
	}

	assert.ElementsMatch(t, expectedPaths, actualPaths)
}

// Test wrapper implementations
func TestWrapperImplementations(t *testing.T) {
	// Test OracleToJavaResultWrapper
	result := &objectparser.OracleToJavaResult{
		ID:             1,
		UUID:           "test-uuid",
		ObjectName:     "TEST_OBJECT",
		ConvertedCode:  "test code",
		ConvertPrompts: "prompt1@,@prompt2",
		IsMultiFile:    true,
	}

	wrapper := &OracleToJavaResultWrapper{OracleToJavaResult: result}
	assert.Equal(t, uint(1), wrapper.GetID())
	assert.Equal(t, "test-uuid", wrapper.GetUUID())
	assert.Equal(t, "TEST_OBJECT", wrapper.GetObjectName())
	assert.Equal(t, "test code", wrapper.GetConvertedCode())
	assert.Equal(t, "prompt1@,@prompt2", wrapper.GetConvertPrompts())
	assert.True(t, wrapper.IsMultiFileResult())

	// Test OracleToJavaHistoryResultWrapper
	historyResult := &objectparser.OracleToJavaHistoryResult{
		ID:             2,
		UUID:           "history-uuid",
		ObjectName:     "HISTORY_OBJECT",
		ConvertedCode:  "history code",
		ConvertPrompts: "prompt3",
		IsMultiFile:    false,
	}

	historyWrapper := &OracleToJavaHistoryResultWrapper{OracleToJavaHistoryResult: historyResult}
	assert.Equal(t, uint(2), historyWrapper.GetID())
	assert.Equal(t, "history-uuid", historyWrapper.GetUUID())
	assert.Equal(t, "HISTORY_OBJECT", historyWrapper.GetObjectName())
	assert.Equal(t, "history code", historyWrapper.GetConvertedCode())
	assert.Equal(t, "prompt3", historyWrapper.GetConvertPrompts())
	assert.False(t, historyWrapper.IsMultiFileResult())
}
