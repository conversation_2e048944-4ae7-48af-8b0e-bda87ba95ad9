package core

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// PromptSelector handles the logic for selecting appropriate prompts for conversion
type PromptSelector struct {
	promptManager *PromptManager
}

// NewPromptSelector creates a new instance of PromptSelector
func NewPromptSelector(promptManager *PromptManager) *PromptSelector {
	return &PromptSelector{
		promptManager: promptManager,
	}
}

// SelectPromptForConversion selects the appropriate prompt for a given object
func (ps *PromptSelector) SelectPromptForConversion(
	ctx context.Context,
	objectUUID string,
	relations []*objectparser.OracleObjectTaskObjectPromptRelation,
	defaultPrompt *objectparser.OracleObjectTransformationPrompt,
) (*objectparser.OracleObjectTransformationPrompt, error) {
	// First check if object has a custom prompt binding
	for _, relation := range relations {
		if relation.DependencyUUID == objectUUID {
			// Found a custom binding, get the prompt
			prompt, err := ps.promptManager.GetPromptByID(ctx, relation.TaskPromptId)
			if err != nil {
				log.Warnf("Failed to get custom prompt %d for object %s: %v",
					relation.TaskPromptId, objectUUID, err)
				continue
			}
			log.Infof("Using custom prompt '%s' for object %s", prompt.PromptTitle, objectUUID)
			return prompt, nil
		}
	}

	// No custom binding found, use default
	log.Infof("Using default prompt for object %s", objectUUID)
	return defaultPrompt, nil
}

// DetermineEntityGenerationNeeds analyzes prompts to determine which frameworks need entity generation
func (ps *PromptSelector) DetermineEntityGenerationNeeds(
	ctx context.Context,
	objectPromptMap map[string]*objectparser.OracleObjectTransformationPrompt,
) (map[string]*objectparser.OracleObjectTransformationPrompt, error) {
	entityPrompts := make(map[string]*objectparser.OracleObjectTransformationPrompt)

	log.Infof("DetermineEntityGenerationNeeds: analyzing %d object-prompt mappings", len(objectPromptMap))

	// Check each unique prompt used
	processedPrompts := make(map[uint]bool)

	for _, prompt := range objectPromptMap {
		// Skip if already processed this prompt
		if processedPrompts[prompt.ID] {
			continue
		}
		processedPrompts[prompt.ID] = true

		log.Infof("Analyzing prompt for entity generation - ID: %d, Title: %s, Category: %s, Framework: %s, Language: %s",
			prompt.ID, prompt.PromptTitle, prompt.PromptCategory, prompt.TargetFramework, prompt.TargetLanguage)

		if prompt.PromptCategory == "TABLE" {
			log.Infof("Found TABLE prompt '%s' for framework %s", prompt.PromptTitle, prompt.TargetFramework)
			entityPrompts[prompt.TargetFramework] = prompt
		} else if prompt.PromptCategory == "CODE" &&
			prompt.TargetFramework != "" &&
			!strings.EqualFold(prompt.TargetFramework, "Java") {
			// CODE prompt with specific framework - find matching TABLE prompt
			log.Infof("CODE prompt '%s' has framework %s, looking for TABLE prompt",
				prompt.PromptTitle, prompt.TargetFramework)

			tablePrompt, err := ps.promptManager.FindTablePromptForFramework(ctx, prompt.TargetFramework)
			if err != nil {
				log.Warnf("Failed to find TABLE prompt for framework %s: %v",
					prompt.TargetFramework, err)
				continue
			}

			log.Infof("Found TABLE prompt '%s' for framework %s",
				tablePrompt.PromptTitle, prompt.TargetFramework)
			entityPrompts[prompt.TargetFramework] = tablePrompt
		} else if prompt.PromptCategory == "TRIGGER" {
			log.Infof("TRIGGER prompt '%s' has framework %s, check entity generation or not",
				prompt.PromptTitle, prompt.TargetFramework)
			if prompt.TargetFramework != constants.OPTargetFrameworkHibernate {
				delete(processedPrompts, prompt.ID)
			}
		} else {
			// Log why this prompt doesn't qualify for entity generation
			log.Infof("Prompt '%s' (ID: %d) does not qualify for entity generation - Category: %s, Framework: %s",
				prompt.PromptTitle, prompt.ID, prompt.PromptCategory, prompt.TargetFramework)
		}
	}

	log.Infof("DetermineEntityGenerationNeeds result: found %d frameworks needing entity generation", len(entityPrompts))
	return entityPrompts, nil
}

// BuildObjectPromptMap builds a complete mapping of objects to their prompts
func (ps *PromptSelector) BuildObjectPromptMap(
	ctx context.Context,
	objects []*objectparser.OracleDependency,
	relations []*objectparser.OracleObjectTaskObjectPromptRelation,
	defaultPrompt *objectparser.OracleObjectTransformationPrompt,
	defaultTriggerPrompt *objectparser.OracleObjectTransformationPrompt,
) (map[string]*objectparser.OracleObjectTransformationPrompt, error) {
	objectPromptMap := make(map[string]*objectparser.OracleObjectTransformationPrompt)

	// First, build a map of custom bindings
	customBindings := make(map[string]uint)
	for _, relation := range relations {
		customBindings[relation.DependencyUUID] = relation.TaskPromptId
	}

	// Now assign prompts to each object
	for _, obj := range objects {
		dp := defaultPrompt
		if obj.GetType() == constants.OracleObjectTypeTrigger {
			dp = defaultTriggerPrompt
		}

		if promptId, hasCustom := customBindings[obj.UUID]; hasCustom {
			// Object has custom prompt
			prompt, err := ps.promptManager.GetPromptByID(ctx, promptId)
			if err != nil {
				log.Warnf("Failed to get custom prompt %d for object %s, using default: %v", promptId, obj.UUID, err)
				objectPromptMap[obj.UUID] = dp
			} else {
				objectPromptMap[obj.UUID] = prompt
			}
		} else {
			// Use default prompt
			objectPromptMap[obj.UUID] = dp
		}
	}

	return objectPromptMap, nil
}

// ShouldGenerateEntity checks if entity generation is needed for a prompt
func (ps *PromptSelector) ShouldGenerateEntity(prompt *objectparser.OracleObjectTransformationPrompt) bool {
	if prompt == nil {
		return false
	}

	// TABLE prompts always need entity generation
	if prompt.PromptCategory == "TABLE" {
		return true
	}

	// CODE prompts with specific frameworks need entity generation
	if prompt.PromptCategory == "CODE" &&
		prompt.TargetFramework != "" &&
		!strings.EqualFold(prompt.TargetFramework, "Java") {
		return true
	}

	return false
}

// GetEntityPromptForCodePrompt finds the matching TABLE prompt for a CODE prompt
func (ps *PromptSelector) GetEntityPromptForCodePrompt(
	ctx context.Context,
	codePrompt *objectparser.OracleObjectTransformationPrompt,
) (*objectparser.OracleObjectTransformationPrompt, error) {
	if codePrompt.PromptCategory != "CODE" {
		return nil, fmt.Errorf("prompt is not a CODE prompt")
	}

	if codePrompt.TargetFramework == "" || strings.EqualFold(codePrompt.TargetFramework, "Java") {
		return nil, fmt.Errorf("CODE prompt does not have a specific framework")
	}

	return ps.promptManager.FindTablePromptForFramework(ctx, codePrompt.TargetFramework)
}

// ExtractFrameworksFromEntityPrompts extracts unique frameworks from entity prompts
func (ps *PromptSelector) ExtractFrameworksFromEntityPrompts(
	entityPrompts map[string]*objectparser.OracleObjectTransformationPrompt,
) []string {
	frameworks := make([]string, 0, len(entityPrompts))
	for framework := range entityPrompts {
		frameworks = append(frameworks, framework)
	}
	return frameworks
}

// GetPromptForObject retrieves the prompt for a specific object
func (ps *PromptSelector) GetPromptForObject(
	ctx context.Context,
	objectUUID string,
	relations []*objectparser.OracleObjectTaskObjectPromptRelation,
	defaultPrompt *objectparser.OracleObjectTransformationPrompt,
) (*objectparser.OracleObjectTransformationPrompt, error) {
	return ps.SelectPromptForConversion(ctx, objectUUID, relations, defaultPrompt)
}

// ShouldDelayEntityGeneration determines if entity generation should be delayed for trigger processing
func (ps *PromptSelector) ShouldDelayEntityGeneration(framework string, hasTriggers bool) bool {
	// Hibernate requires coupled conversion when triggers exist
	// Entity and triggers must be converted together
	return strings.EqualFold(framework, "Hibernate") && hasTriggers
}

// GetTriggerPrompt retrieves the TRIGGER prompt for a specific framework
func (ps *PromptSelector) GetTriggerPrompt(ctx context.Context, framework string) (*objectparser.OracleObjectTransformationPrompt, error) {
	return ps.promptManager.GetTriggerPromptForFramework(ctx, framework)
}

// HasTriggerPromptForFramework checks if a TRIGGER prompt exists for the framework
func (ps *PromptSelector) HasTriggerPromptForFramework(ctx context.Context, framework string) bool {
	prompt, err := ps.promptManager.GetTriggerPromptForFramework(ctx, framework)
	return err == nil && prompt != nil
}

// DetermineTriggerPromptNeeds analyzes prompts to determine which frameworks need trigger-specific prompts
func (ps *PromptSelector) DetermineTriggerPromptNeeds(
	ctx context.Context,
	objectPromptMap map[string]*objectparser.OracleObjectTransformationPrompt,
) (map[string]*objectparser.OracleObjectTransformationPrompt, error) {
	triggerPrompts := make(map[string]*objectparser.OracleObjectTransformationPrompt)

	log.Infof("DetermineTriggerPromptNeeds: analyzing %d object-prompt mappings for trigger needs", len(objectPromptMap))

	// Collect frameworks that need trigger prompts
	frameworksNeedingTriggerPrompts := make(map[string]bool)
	processedPrompts := make(map[uint]bool)

	for _, prompt := range objectPromptMap {
		// Skip if already processed this prompt
		if processedPrompts[prompt.ID] {
			continue
		}
		processedPrompts[prompt.ID] = true

		// Check if this prompt has a framework that might need trigger handling
		if prompt.TargetFramework != "" && !strings.EqualFold(prompt.TargetFramework, "Java") {
			frameworksNeedingTriggerPrompts[strings.ToLower(prompt.TargetFramework)] = true
		}
	}

	// For each framework that needs trigger prompts, try to find the appropriate TRIGGER prompt
	for framework := range frameworksNeedingTriggerPrompts {
		log.Infof("Looking for TRIGGER prompt for framework: %s", framework)

		triggerPrompt, err := ps.promptManager.GetTriggerPromptForFramework(ctx, framework)
		if err != nil {
			log.Warnf("No TRIGGER prompt found for framework %s: %v", framework, err)
			continue
		}

		log.Infof("Found TRIGGER prompt '%s' for framework %s",
			triggerPrompt.PromptTitle, framework)
		triggerPrompts[framework] = triggerPrompt
	}

	log.Infof("DetermineTriggerPromptNeeds result: found %d frameworks with trigger prompts", len(triggerPrompts))
	return triggerPrompts, nil
}

func (ps *PromptSelector) GetDefaultPrompts() (*objectparser.OracleObjectTransformationPrompt, *objectparser.OracleObjectTransformationPrompt, error) {
	var defaultCodePrompt *objectparser.OracleObjectTransformationPrompt
	var defaultTriggerPrompt *objectparser.OracleObjectTransformationPrompt

	for _, prompt := range ps.promptManager.allPromptsCache {
		if prompt.IsDefault {
			if prompt.PromptCategory == "CODE" {
				defaultCodePrompt = prompt
			} else if prompt.PromptCategory == "TRIGGER" {
				defaultTriggerPrompt = prompt
			}
		}
	}

	if defaultCodePrompt == nil {
		err := tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PROMPT_NO_DEFAULT, "No default CODE prompt found")
		return nil, nil, err
	}
	if defaultTriggerPrompt == nil {
		err := tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PROMPT_NO_DEFAULT, "No default TRIGGER prompt found")
		return nil, nil, err
	}

	return defaultCodePrompt, defaultTriggerPrompt, nil
}
