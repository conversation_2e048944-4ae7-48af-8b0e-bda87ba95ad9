package core

import (
	"reflect"
	"testing"

	"gitee.com/pingcap_enterprise/tms/common/structs"
)

func TestFilterTree(t *testing.T) {
	trees := []*structs.DependencyTreeVO{
		{
			Key: "root1",
			Children: []*structs.DependencyTreeVO{
				{
					Key: "child1",
					Children: []*structs.DependencyTreeVO{
						{Key: "child1.1"},
						{Key: "child1.2"},
					},
				},
				{
					Key: "child2",
					Children: []*structs.DependencyTreeVO{
						{Key: "child2.1"},
					},
				},
			},
		},
		{
			Key: "root2",
			Children: []*structs.DependencyTreeVO{
				{Key: "child3"},
			},
		},
	}

	type args struct {
		trees                []*structs.DependencyTreeVO
		keySet               map[string]bool
		excludeInvalidObject bool
	}
	tests := []struct {
		name string
		args args
		want []*structs.DependencyTreeVO
	}{
		{
			name: "filter with key set",
			args: args{
				trees:                trees,
				keySet:               map[string]bool{"child1.2": true, "child3": true},
				excludeInvalidObject: false,
			},
			want: []*structs.DependencyTreeVO{
				{
					Key: "root1",
					Children: []*structs.DependencyTreeVO{
						{
							Key: "child1",
							Children: []*structs.DependencyTreeVO{
								{Key: "child1.2"},
							},
						},
					},
				},
				{
					Key: "root2",
					Children: []*structs.DependencyTreeVO{
						{Key: "child3"},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterTree(tt.args.trees, tt.args.keySet, tt.args.excludeInvalidObject); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FilterTree() = %v, want %v", got, tt.want)
			}
		})
	}
}
