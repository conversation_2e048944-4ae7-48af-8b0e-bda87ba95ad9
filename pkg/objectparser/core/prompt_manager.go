package core

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// PromptManager handles prompt retrieval with fallback to default prompts
type PromptManager struct {
	// Cache for loaded prompts by title
	promptCache map[string]*objectparser.OracleObjectTransformationPrompt
	// Cache for all prompts loaded from database
	allPromptsCache []*objectparser.OracleObjectTransformationPrompt
	// Cache for prompts by ID
	promptByIDCache map[uint]*objectparser.OracleObjectTransformationPrompt
}

// NewPromptManager creates a new instance of PromptManager
func NewPromptManager() *PromptManager {
	return &PromptManager{
		promptCache:     make(map[string]*objectparser.OracleObjectTransformationPrompt),
		promptByIDCache: make(map[uint]*objectparser.OracleObjectTransformationPrompt),
	}
}

// GetEntityPrompt retrieves entity prompt for the specified framework from database
func (pm *PromptManager) GetEntityPrompt(ctx context.Context, frameworkType string) (string, error) {
	// Ensure prompts are loaded
	if len(pm.allPromptsCache) == 0 {
		if err := pm.LoadAllPrompts(ctx); err != nil {
			return "", fmt.Errorf("failed to load prompts: %w", err)
		}
	}

	// Filter by target_framework and prompt_category
	category := "TABLE"
	var matchingPrompts []*objectparser.OracleObjectTransformationPrompt

	for _, prompt := range pm.allPromptsCache {
		if prompt.PromptCategory == category &&
			strings.EqualFold(prompt.TargetFramework, frameworkType) {
			matchingPrompts = append(matchingPrompts, prompt)
		}
	}

	// Handle results
	if len(matchingPrompts) == 0 {
		return "", fmt.Errorf("no entity prompt found for framework %s with category %s", frameworkType, category)
	}

	if len(matchingPrompts) == 1 {
		log.Infof("Found entity prompt in database for framework %s", frameworkType)
		return matchingPrompts[0].PromptText, nil
	}

	// Multiple prompts found, log and return the one with smallest ID
	log.Warnf("Found %d entity prompts for framework %s with category %s, using the one with smallest ID",
		len(matchingPrompts), frameworkType, category)

	// Find the prompt with smallest ID
	selectedPrompt := matchingPrompts[0]
	for _, prompt := range matchingPrompts[1:] {
		if prompt.ID < selectedPrompt.ID {
			selectedPrompt = prompt
		}
	}

	log.Infof("Selected entity prompt ID %d for framework %s", selectedPrompt.ID, frameworkType)
	return selectedPrompt.PromptText, nil
}

// getPromptFromDatabase retrieves prompt from database by title
func (pm *PromptManager) getPromptFromDatabase(ctx context.Context, promptTitle string) (string, error) {
	// Check cache first
	if cached, exists := pm.promptCache[promptTitle]; exists {
		return cached.PromptText, nil
	}

	// Get all prompts from database
	prompts, err := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to list prompts: %w", err)
	}

	// Find the prompt by title
	for _, prompt := range prompts {
		if strings.ToLower(prompt.PromptTitle) == promptTitle {
			// Cache it for future use
			pm.promptCache[promptTitle] = prompt
			return prompt.PromptText, nil
		}
	}

	return "", fmt.Errorf("prompt with title '%s' not found", promptTitle)
}

// ClearCache clears the prompt cache
func (pm *PromptManager) ClearCache() {
	pm.promptCache = make(map[string]*objectparser.OracleObjectTransformationPrompt)
	pm.allPromptsCache = nil
	pm.promptByIDCache = make(map[uint]*objectparser.OracleObjectTransformationPrompt)
}

// LoadAllPrompts loads all prompts from database and caches them
func (pm *PromptManager) LoadAllPrompts(ctx context.Context) error {
	prompts, err := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
	if err != nil {
		return fmt.Errorf("failed to load all prompts: %w", err)
	}

	pm.allPromptsCache = prompts

	// Build caches
	for _, prompt := range prompts {
		pm.promptByIDCache[prompt.ID] = prompt
		pm.promptCache[strings.ToLower(prompt.PromptTitle)] = prompt
	}

	log.Infof("Loaded %d prompts into cache", len(prompts))
	return nil
}

// InitAllPrompts save all prompts and caches them
func (pm *PromptManager) InitAllPrompts(prompts []*objectparser.OracleObjectTransformationPrompt) error {
	pm.allPromptsCache = prompts

	// Build caches
	for _, prompt := range prompts {
		pm.promptByIDCache[prompt.ID] = prompt
		pm.promptCache[strings.ToLower(prompt.PromptTitle)] = prompt
	}

	log.Infof("Loaded %d prompts into cache", len(prompts))
	return nil
}

// GetPromptByID retrieves a prompt by its ID
func (pm *PromptManager) GetPromptByID(ctx context.Context, id uint) (*objectparser.OracleObjectTransformationPrompt, error) {
	// Check cache first
	if prompt, exists := pm.promptByIDCache[id]; exists {
		return prompt, nil
	}

	// Load from database if not cached
	prompt, err := models.GetObjectParserWriter().GetOracleObjectTransformationPrompt(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get prompt by ID %d: %w", id, err)
	}

	// Cache it
	pm.promptByIDCache[id] = prompt
	return prompt, nil
}

// GetDefaultPrompt retrieves the default prompt for a given category
func (pm *PromptManager) GetDefaultPrompt(ctx context.Context, category string) (*objectparser.OracleObjectTransformationPrompt, error) {
	// Ensure prompts are loaded
	if len(pm.allPromptsCache) == 0 {
		if err := pm.LoadAllPrompts(ctx); err != nil {
			return nil, err
		}
	}

	for _, prompt := range pm.allPromptsCache {
		if prompt.IsDefault && prompt.PromptCategory == category {
			return prompt, nil
		}
	}

	return nil, fmt.Errorf("no default prompt found for category: %s", category)
}

// FindTablePromptForFramework finds a TABLE category prompt for the given framework
func (pm *PromptManager) FindTablePromptForFramework(ctx context.Context, framework string) (*objectparser.OracleObjectTransformationPrompt, error) {
	// Ensure prompts are loaded
	if len(pm.allPromptsCache) == 0 {
		if err := pm.LoadAllPrompts(ctx); err != nil {
			return nil, err
		}
	}

	for _, prompt := range pm.allPromptsCache {
		if prompt.PromptCategory == "TABLE" &&
			strings.EqualFold(prompt.TargetFramework, framework) {
			return prompt, nil
		}
	}

	return nil, fmt.Errorf("no TABLE prompt found for framework: %s", framework)
}

// GetPromptsByCategory retrieves all prompts of a specific category
func (pm *PromptManager) GetPromptsByCategory(ctx context.Context, category string) ([]*objectparser.OracleObjectTransformationPrompt, error) {
	// Ensure prompts are loaded
	if len(pm.allPromptsCache) == 0 {
		if err := pm.LoadAllPrompts(ctx); err != nil {
			return nil, err
		}
	}

	var result []*objectparser.OracleObjectTransformationPrompt
	for _, prompt := range pm.allPromptsCache {
		if prompt.PromptCategory == category {
			result = append(result, prompt)
		}
	}

	return result, nil
}

// GetAllPrompts returns all cached prompts
func (pm *PromptManager) GetAllPrompts(ctx context.Context) ([]*objectparser.OracleObjectTransformationPrompt, error) {
	// Ensure prompts are loaded
	if len(pm.allPromptsCache) == 0 {
		if err := pm.LoadAllPrompts(ctx); err != nil {
			return nil, err
		}
	}

	return pm.allPromptsCache, nil
}

// GetTriggerPromptForFramework retrieves the TRIGGER category prompt for a specific framework
func (pm *PromptManager) GetTriggerPromptForFramework(ctx context.Context, framework string) (*objectparser.OracleObjectTransformationPrompt, error) {
	// Ensure prompts are loaded
	if len(pm.allPromptsCache) == 0 {
		if err := pm.LoadAllPrompts(ctx); err != nil {
			return nil, err
		}
	}

	// Find TRIGGER prompt for the specified framework
	for _, prompt := range pm.allPromptsCache {
		if prompt.PromptCategory == "TRIGGER" &&
			strings.EqualFold(prompt.TargetFramework, framework) {
			log.Infof("Found TRIGGER prompt for framework %s: %s", framework, prompt.PromptTitle)
			return prompt, nil
		}
	}

	return nil, fmt.Errorf("no TRIGGER prompt found for framework: %s", framework)
}
