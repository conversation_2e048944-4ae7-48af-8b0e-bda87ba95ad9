// Package core provides core functionality for object parsing and file organization
package core

import (
	"context"
	"fmt"
	"path"
	"path/filepath"
	"regexp"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// FileOrganizer handles Java code file organization and conflict resolution
type FileOrganizer struct {
	conflictTracker map[string]int // filePath -> count
	idGenerator     *IdGenerator
	preloadedFiles  map[uint][]*objectparser.OracleToJavaFile // resultId -> files (for optimization)
}

// NewFileOrganizer creates a new FileOrganizer instance
func NewFileOrganizer() *FileOrganizer {
	return &FileOrganizer{
		conflictTracker: make(map[string]int),
		idGenerator:     NewDefaultIdGenerator(),
		preloadedFiles:  make(map[uint][]*objectparser.OracleToJavaFile),
	}
}

// ConversionResult interface for both OracleToJavaResult and OracleToJavaHistoryResult
type ConversionResult interface {
	GetID() uint
	GetTaskId() int
	GetUUID() string
	GetObjectName() string
	GetConvertedCode() string
	GetConvertPrompts() string
	IsMultiFileResult() bool
	GetCodeFileName() string
}

// OracleToJavaResultWrapper wraps OracleToJavaResult to implement ConversionResult
type OracleToJavaResultWrapper struct {
	*objectparser.OracleToJavaResult
}

func (w *OracleToJavaResultWrapper) GetID() uint               { return w.ID }
func (w *OracleToJavaResultWrapper) GetTaskId() int            { return w.TaskId }
func (w *OracleToJavaResultWrapper) GetUUID() string           { return w.UUID }
func (w *OracleToJavaResultWrapper) GetObjectName() string     { return w.ObjectName }
func (w *OracleToJavaResultWrapper) GetConvertedCode() string  { return w.ConvertedCode }
func (w *OracleToJavaResultWrapper) GetConvertPrompts() string { return w.ConvertPrompts }
func (w *OracleToJavaResultWrapper) IsMultiFileResult() bool   { return w.IsMultiFile }
func (w *OracleToJavaResultWrapper) GetCodeFileName() string   { return w.CodeFileName }

// OracleToJavaHistoryResultWrapper wraps OracleToJavaHistoryResult to implement ConversionResult
type OracleToJavaHistoryResultWrapper struct {
	*objectparser.OracleToJavaHistoryResult
}

func (w *OracleToJavaHistoryResultWrapper) GetID() uint               { return w.ID }
func (w *OracleToJavaHistoryResultWrapper) GetTaskId() int            { return w.TaskId }
func (w *OracleToJavaHistoryResultWrapper) GetUUID() string           { return w.UUID }
func (w *OracleToJavaHistoryResultWrapper) GetObjectName() string     { return w.ObjectName }
func (w *OracleToJavaHistoryResultWrapper) GetConvertedCode() string  { return w.ConvertedCode }
func (w *OracleToJavaHistoryResultWrapper) GetConvertPrompts() string { return w.ConvertPrompts }
func (w *OracleToJavaHistoryResultWrapper) IsMultiFileResult() bool   { return w.IsMultiFile }
func (w *OracleToJavaHistoryResultWrapper) GetCodeFileName() string   { return w.CodeFileName }

// ResolveFilePathConflict resolves file path conflicts by adding _dup{no} suffix
func (f *FileOrganizer) ResolveFilePathConflict(filePath string) string {
	// Check if this is the first occurrence
	count := f.conflictTracker[filePath]
	if count == 0 {
		f.conflictTracker[filePath] = 1
		return filePath
	}

	// Generate new filename with _dup{no} suffix
	dir := filepath.Dir(filePath)
	fileName := filepath.Base(filePath)
	ext := filepath.Ext(fileName)
	nameWithoutExt := strings.TrimSuffix(fileName, ext)
	newFileName := fmt.Sprintf("%s_dup%d%s", nameWithoutExt, count, ext)

	// Increment counter for next duplicate
	f.conflictTracker[filePath]++

	return filepath.Join(dir, newFileName)
}

// ProcessConversionResult processes a single conversion result and returns file information
func (f *FileOrganizer) ProcessConversionResult(ctx context.Context, result ConversionResult, isHistory bool) ([]FileInfo, error) {
	var fileInfos []FileInfo

	if result.IsMultiFileResult() {
		// Handle multi-file results
		files, err := f.fetchMultiFiles(ctx, result.GetTaskId(), result.GetID(), isHistory)
		if err != nil {
			log.Errorf("Failed to fetch multi-files for result %d: %v", result.GetID(), err)
			return fileInfos, err
		}

		for _, file := range files {
			fileInfo := FileInfo{
				FilePath:       f.ResolveFilePathConflict(file.FilePath),
				FileName:       file.FileName,
				PackagePath:    file.PackagePath,
				FileContent:    file.FileContent,
				ObjectUUID:     result.GetUUID(),
				ConvertPrompts: strings.Split(result.GetConvertPrompts(), "@,@"),
			}
			fileInfos = append(fileInfos, fileInfo)
		}
	} else {
		// Handle single-file results
		packageName := f.extractPackageFromJavaCode(result.GetConvertedCode())
		packagePath := f.packageToPath(packageName)
		fileName := result.GetCodeFileName()
		filePath := path.Join(packagePath, fileName)

		fileInfo := FileInfo{
			FilePath:       f.ResolveFilePathConflict(filePath),
			FileName:       fileName,
			PackagePath:    packageName,
			FileContent:    result.GetConvertedCode(),
			ObjectUUID:     result.GetUUID(),
			ConvertPrompts: strings.Split(result.GetConvertPrompts(), "@,@"),
		}
		fileInfos = append(fileInfos, fileInfo)
	}

	return fileInfos, nil
}

// fetchMultiFiles fetches multi-file results from database or preloaded cache
func (f *FileOrganizer) fetchMultiFiles(ctx context.Context, taskId int, resultID uint, isHistory bool) ([]*objectparser.OracleToJavaFile, error) {
	// Check if files are preloaded
	if files, ok := f.preloadedFiles[resultID]; ok && len(files) > 0 {
		return files, nil
	}

	// Fallback to original method if not preloaded
	if isHistory {
		historyFiles, err := models.GetObjectParserWriter().ListOracleToJavaHistoryFilesByTaskIdAndResultId(ctx, taskId, resultID)
		if err != nil {
			return nil, err
		}

		// Convert history files to regular files for unified processing
		var files []*objectparser.OracleToJavaFile
		for _, hf := range historyFiles {
			files = append(files, &objectparser.OracleToJavaFile{
				ID:          hf.ID,
				TaskId:      hf.TaskId,
				ResultId:    hf.ResultId,
				FilePath:    hf.FilePath,
				FileName:    hf.FileName,
				PackagePath: hf.PackagePath,
				FileType:    hf.FileType,
				FileContent: hf.FileContent,
				FileOrder:   hf.FileOrder,
			})
		}
		return files, nil
	}

	return models.GetObjectParserWriter().ListOracleToJavaFilesByTaskIdAndResultId(ctx, taskId, resultID)
}

// preScanFileConflicts scans all conversion results to pre-populate conflict tracker
func (f *FileOrganizer) preScanFileConflicts(ctx context.Context, results []ConversionResult, isHistory bool) error {
	// Clear existing conflict tracker
	f.conflictTracker = make(map[string]int)

	// Track all file paths that will be generated
	pathCounter := make(map[string]int) // filePath -> count

	for _, result := range results {
		var filePaths []string

		if result.IsMultiFileResult() {
			// Handle multi-file results - get actual file paths from database
			files, err := f.fetchMultiFiles(ctx, result.GetTaskId(), result.GetID(), isHistory)
			if err != nil {
				log.Warnf("Failed to fetch multi-files for result %d during pre-scan: %v", result.GetID(), err)
				continue
			}

			for _, file := range files {
				filePaths = append(filePaths, file.FilePath)
			}
		} else {
			// Handle single-file results - generate file path
			packageName := f.extractPackageFromJavaCode(result.GetConvertedCode())
			packagePath := f.packageToPath(packageName)
			fileName := f.generateJavaFileName(result.GetObjectName())
			filePath := path.Join(packagePath, fileName)
			filePaths = append(filePaths, filePath)
		}

		// Count occurrences of each file path
		for _, filePath := range filePaths {
			pathCounter[filePath]++
		}
	}

	// Initialize conflict tracker based on the counts
	for filePath, count := range pathCounter {
		// If there are multiple files with the same path, set initial counter to 0
		// This way the first occurrence gets original name, and subsequent ones get _dup{n}
		if count > 1 {
			f.conflictTracker[filePath] = 0 // Start from 0, first occurrence gets original name
		}
	}

	return nil
}

// ProcessAllResults processes all conversion results and returns file information
func (f *FileOrganizer) ProcessAllResults(ctx context.Context, results []ConversionResult, isHistory bool) ([]FileInfo, error) {
	// Pre-scan all results to build conflict tracker
	if err := f.preScanFileConflicts(ctx, results, isHistory); err != nil {
		log.Warnf("Failed to pre-scan file conflicts: %v", err)
		// Continue processing even if pre-scan fails
	}

	var allFileInfos []FileInfo

	for _, result := range results {
		fileInfos, err := f.ProcessConversionResult(ctx, result, isHistory)
		if err != nil {
			log.Warnf("Failed to process result %d: %v", result.GetID(), err)
			continue
		}
		allFileInfos = append(allFileInfos, fileInfos...)
	}

	return allFileInfos, nil
}

// ProcessAllResultsWithPreloadedFiles processes all conversion results with preloaded files for better performance
func (f *FileOrganizer) ProcessAllResultsWithPreloadedFiles(ctx context.Context, results []ConversionResult, allFiles []*objectparser.OracleToJavaFile, allHistoryFiles []*objectparser.OracleToJavaHistoryFile, isHistory bool) ([]FileInfo, error) {
	// Build preloaded files map
	f.preloadedFiles = make(map[uint][]*objectparser.OracleToJavaFile)

	if isHistory && allHistoryFiles != nil {
		// Convert history files to regular files and group by resultId
		for _, hf := range allHistoryFiles {
			file := &objectparser.OracleToJavaFile{
				ID:          hf.ID,
				TaskId:      hf.TaskId,
				ResultId:    hf.ResultId,
				FilePath:    hf.FilePath,
				FileName:    hf.FileName,
				PackagePath: hf.PackagePath,
				FileType:    hf.FileType,
				FileContent: hf.FileContent,
				FileOrder:   hf.FileOrder,
			}
			f.preloadedFiles[hf.ResultId] = append(f.preloadedFiles[hf.ResultId], file)
		}
	} else if !isHistory && allFiles != nil {
		// Group files by resultId
		for _, file := range allFiles {
			f.preloadedFiles[file.ResultId] = append(f.preloadedFiles[file.ResultId], file)
		}
	}

	// Call the original ProcessAllResults which will now use preloaded files
	return f.ProcessAllResults(ctx, results, isHistory)
}

// BuildFileTree builds a hierarchical tree structure from file information
func (f *FileOrganizer) BuildFileTree(fileInfos []FileInfo) []*message.JavaCodeTreeNode {
	if len(fileInfos) == 0 {
		return []*message.JavaCodeTreeNode{}
	}

	// Create a map to store tree nodes by their key
	nodeMap := make(map[string]*message.JavaCodeTreeNode)

	// Process each file
	for _, fileInfo := range fileInfos {
		f.buildTreeNodesForFile(nodeMap, fileInfo)
	}

	// Convert map to tree structure
	return f.buildTreeFromNodeMap(nodeMap)
}

// buildTreeNodesForFile creates tree nodes for a single file
func (f *FileOrganizer) buildTreeNodesForFile(nodeMap map[string]*message.JavaCodeTreeNode, fileInfo FileInfo) {
	// Check if it's a resource file (no package or starts with resources/)
	if fileInfo.PackagePath == "" || strings.HasPrefix(fileInfo.FilePath, "resources/") {
		// Handle as directory path instead of package path
		dirPath := path.Dir(fileInfo.FilePath)
		if dirPath != "." && dirPath != "/" && dirPath != "" {
			pathParts := strings.Split(dirPath, "/")
			currentPath := ""

			for idx, part := range pathParts {
				if part == "" {
					continue
				}

				if idx == 0 {
					currentPath = part
				} else {
					currentPath = currentPath + "/" + part
				}

				// Create directory node if it doesn't exist
				if _, exists := nodeMap[currentPath]; !exists {
					nodeMap[currentPath] = &message.JavaCodeTreeNode{
						Key:            currentPath,
						Title:          part,
						FileName:       "",
						FilePackage:    "",
						FileContent:    "",
						ObjectUUID:     "",
						ConvertPrompts: []string{},
						Children:       []*message.JavaCodeTreeNode{},
						IsLeaf:         false,
					}
				}
			}
		}
	} else {
		// Handle Java packages
		packageParts := strings.Split(fileInfo.PackagePath, ".")
		if len(packageParts) > 0 && packageParts[0] != "" {
			// Build directory nodes for each package level
			currentPath := ""
			for i, part := range packageParts {
				if i == 0 {
					currentPath = part
				} else {
					currentPath = currentPath + "." + part
				}

				// Create directory node if it doesn't exist
				if _, exists := nodeMap[currentPath]; !exists {
					nodeMap[currentPath] = &message.JavaCodeTreeNode{
						Key:            currentPath,
						Title:          part,
						FileName:       "",
						FilePackage:    currentPath,
						FileContent:    "",
						ObjectUUID:     "",
						ConvertPrompts: []string{},
						Children:       []*message.JavaCodeTreeNode{},
						IsLeaf:         false,
					}
				}
			}
		}
	}

	// Create file node
	f.createFileNode(nodeMap, fileInfo.FilePath, fileInfo)
}

// createFileNode creates a file node in the tree
func (f *FileOrganizer) createFileNode(nodeMap map[string]*message.JavaCodeTreeNode, filePath string, fileInfo FileInfo) {
	// Extract the actual filename from the resolved path (which may contain _dup{N} suffix)
	resolvedFileName := filepath.Base(filePath)

	nodeMap[filePath] = &message.JavaCodeTreeNode{
		Key:            filePath,
		Title:          resolvedFileName,
		FileName:       resolvedFileName,
		FilePackage:    fileInfo.PackagePath,
		FileContent:    fileInfo.FileContent,
		ObjectUUID:     fileInfo.ObjectUUID,
		ConvertPrompts: fileInfo.ConvertPrompts,
		Children:       []*message.JavaCodeTreeNode{},
		IsLeaf:         true,
	}
}

// buildTreeFromNodeMap converts the flat node map to a hierarchical tree
func (f *FileOrganizer) buildTreeFromNodeMap(nodeMap map[string]*message.JavaCodeTreeNode) []*message.JavaCodeTreeNode {
	roots := make(map[string]*message.JavaCodeTreeNode)

	// Build parent-child relationships
	for key, node := range nodeMap {
		if node.IsLeaf {
			// Find parent for file nodes
			parentKey := f.findParentKey(key, nodeMap)
			if parentKey != "" {
				parent := nodeMap[parentKey]
				parent.Children = append(parent.Children, node)
			} else {
				// File without package - add to root
				roots[key] = node
			}
		} else {
			// Directory node - find its parent
			parentKey := f.findParentPackageKey(key)
			if parentKey != "" && nodeMap[parentKey] != nil {
				parent := nodeMap[parentKey]
				parent.Children = append(parent.Children, node)
			} else {
				// Root level package
				roots[key] = node
			}
		}
	}

	// Convert roots map to slice and sort
	return f.sortAndConvertRoots(roots)
}

// Helper methods

// extractPackageFromJavaCode extracts package declaration from Java code
func (f *FileOrganizer) extractPackageFromJavaCode(javaCode string) string {
	re := regexp.MustCompile(`package\s+([\w.]+)\s*;`)
	matches := re.FindStringSubmatch(javaCode)
	if len(matches) > 1 {
		return matches[1]
	}
	return "com.tms.dao" // Default package name
}

// packageToPath converts package name to directory path
func (f *FileOrganizer) packageToPath(packageName string) string {
	return strings.ReplaceAll(packageName, ".", "/")
}

// generateJavaFileName converts object name to proper Java class name
func (f *FileOrganizer) generateJavaFileName(objectName string) string {
	parts := strings.Split(strings.ToLower(objectName), "_")
	var result strings.Builder

	for _, part := range parts {
		if len(part) > 0 {
			result.WriteString(strings.ToUpper(string(part[0])))
			if len(part) > 1 {
				result.WriteString(part[1:])
			}
		}
	}

	return result.String() + ".java"
}

// findParentKey finds the parent package key for a file path
func (f *FileOrganizer) findParentKey(filePath string, nodeMap map[string]*message.JavaCodeTreeNode) string {
	dir := path.Dir(filePath)
	if dir == "." || dir == "/" || dir == "" {
		return ""
	}

	// First, check if the parent directory exists (for resource files)
	if _, exists := nodeMap[dir]; exists {
		return dir
	}

	// If not found as directory, try as package format (for Java files)
	packageKey := strings.ReplaceAll(dir, "/", ".")
	if _, exists := nodeMap[packageKey]; exists {
		return packageKey
	}

	return ""
}

// findParentPackageKey finds the parent package key for a package
func (f *FileOrganizer) findParentPackageKey(packageKey string) string {
	// Check if it's a directory-style key (contains /)
	if strings.Contains(packageKey, "/") {
		dir := path.Dir(packageKey)
		if dir == "." || dir == "/" || dir == "" {
			return ""
		}
		return dir
	}

	// Handle package-style key (contains .)
	parts := strings.Split(packageKey, ".")
	if len(parts) <= 1 {
		return ""
	}

	// Return parent package
	return strings.Join(parts[:len(parts)-1], ".")
}

// sortAndConvertRoots converts and sorts root nodes
func (f *FileOrganizer) sortAndConvertRoots(roots map[string]*message.JavaCodeTreeNode) []*message.JavaCodeTreeNode {
	var result []*message.JavaCodeTreeNode
	for _, root := range roots {
		result = append(result, root)
	}

	// Sort root nodes by title
	for i := 0; i < len(result); i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].Title > result[j].Title {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	// Sort all children recursively
	f.sortTreeNodes(result)

	return result
}

// sortTreeNodes recursively sorts all children of the given nodes
func (f *FileOrganizer) sortTreeNodes(nodes []*message.JavaCodeTreeNode) {
	for _, node := range nodes {
		if len(node.Children) > 0 {
			// Sort children by title (directories first, then files)
			for i := 0; i < len(node.Children); i++ {
				for j := i + 1; j < len(node.Children); j++ {
					child1 := node.Children[i]
					child2 := node.Children[j]

					// Directories come before files
					if !child1.IsLeaf && child2.IsLeaf {
						continue
					}
					if child1.IsLeaf && !child2.IsLeaf {
						node.Children[i], node.Children[j] = node.Children[j], node.Children[i]
						continue
					}

					// Within same type, sort by title
					if child1.Title > child2.Title {
						node.Children[i], node.Children[j] = node.Children[j], node.Children[i]
					}
				}
			}

			// Recursively sort children
			f.sortTreeNodes(node.Children)
		}
	}
}
