package core

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	lru "github.com/hashicorp/golang-lru"
)

// AnalysisCache provides LRU caching for analysis results to avoid redundant processing
type AnalysisCache struct {
	cache        *lru.Cache
	mu           sync.RWMutex
	hits         int64
	misses       int64
	maxCacheSize int
}

// CachedAnalysisResult wraps an analysis result with metadata
type CachedAnalysisResult struct {
	Result    dto.AnalyzeSQLResponse
	Timestamp time.Time
	TTL       time.Duration
}

// NewAnalysisCache creates a new analysis cache with the specified maximum size
func NewAnalysisCache(maxSize int) (*AnalysisCache, error) {
	if maxSize <= 0 {
		maxSize = 1000
	}

	cache, err := lru.New(maxSize)
	if err != nil {
		return nil, err
	}

	return &AnalysisCache{
		cache:        cache,
		maxCacheSize: maxSize,
	}, nil
}

// Get retrieves a cached analysis result if it exists and hasn't expired
func (c *AnalysisCache) Get(key string) (*dto.AnalyzeSQLResponse, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if val, ok := c.cache.Get(key); ok {
		cached := val.(*CachedAnalysisResult)
		// Check if the result has expired
		if time.Since(cached.Timestamp) < cached.TTL {
			atomic.AddInt64(&c.hits, 1)
			return &cached.Result, true
		}
		// Remove expired entry
		c.cache.Remove(key)
	}

	atomic.AddInt64(&c.misses, 1)
	return nil, false
}

// Set stores an analysis result in the cache
func (c *AnalysisCache) Set(key string, result dto.AnalyzeSQLResponse, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if ttl <= 0 {
		ttl = 1 * time.Hour // Default TTL
	}

	c.cache.Add(key, &CachedAnalysisResult{
		Result:    result,
		Timestamp: time.Now(),
		TTL:       ttl,
	})
}

// GetStats returns cache statistics
func (c *AnalysisCache) GetStats() (hits, misses int64, hitRate float64) {
	hits = atomic.LoadInt64(&c.hits)
	misses = atomic.LoadInt64(&c.misses)
	total := hits + misses
	if total > 0 {
		hitRate = float64(hits) / float64(total) * 100
	}
	return
}

// Clear removes all entries from the cache
func (c *AnalysisCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.cache.Purge()
	atomic.StoreInt64(&c.hits, 0)
	atomic.StoreInt64(&c.misses, 0)
}

// Size returns the current number of items in the cache
func (c *AnalysisCache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.cache.Len()
}

// generateCacheKey generates a cache key for an object definition
func GenerateCacheKey(objDef *objectparser.OracleObjectDefinition) string {
	// Use object's unique identifier and content hash as key
	// This ensures cache invalidation when object content changes
	hash := sha256.Sum256([]byte(objDef.AllText))
	return fmt.Sprintf("%s:%x", objDef.SchemaObjectKey, hash[:8])
}

// buildAnalysisResultFromCache builds an AnalysisResult from cached response
func (i *OracleObjectParserTask) buildAnalysisResultFromCache(task *AnalysisTask, cachedResp *dto.AnalyzeSQLResponse, analysisContext *AnalysisContext) *AnalysisResult {
	objDef := task.OracleObjectDefinition
	detailMap := analysisContext.detailMap
	objectScorer := analysisContext.objectScorer

	// Get detail from map
	detail, exist := detailMap[objDef.SchemaObjectKey]
	if !exist {
		// This shouldn't happen if cache is used correctly
		log.Warnf("Detail not found for cached object %s", objDef.SchemaObjectKey)
		return nil
	}

	// Populate detail from cached response
	detail.Status = constants.StatStatusSuccess.String()
	detail.MethodInvokeList = cachedResp.MarshalMethodInvokeListToString()
	detail.IdentifierList = cachedResp.MarshalIdentifierListToString()
	detail.TableReferenceList = cachedResp.MarshalTableReferenceListToString()
	detail.ReservedWordList = cachedResp.MarshalReservedWordListToString()
	detail.PLSQLSegment = cachedResp.MarshalPLSQLSegmentToString()
	detail.ReservedWordCount, detail.DatabaseLinkCount, detail.IncompatibleFeatureScoreContext =
		objectScorer.GetIncompatibleFeatureCount(cachedResp.GetReservedWordList(), cachedResp.GetTableReferenceList())

	return &AnalysisResult{
		Index:                task.Index,
		Detail:               detail,
		IncompatibleFeatures: objectScorer.BuildIncompatibleFeatures(detail, *cachedResp),
		Error:                nil,
	}
}

// analyzeObjectWithCache analyzes an object with caching support
func (i *OracleObjectParserTask) analyzeObjectWithCache(ctx context.Context, task *AnalysisTask, metadataHelper *MetadataHelper, analysisContext *AnalysisContext, cache *AnalysisCache) *AnalysisResult {
	if cache == nil {
		// No cache available, fall back to regular analysis
		return i.analyzeObject(ctx, task, metadataHelper, analysisContext)
	}

	objDef := task.OracleObjectDefinition
	cacheKey := GenerateCacheKey(objDef)

	// Try to get from cache
	if cachedResp, found := cache.Get(cacheKey); found {
		log.Debugf("Cache hit for object %s", objDef.SchemaObjectKey)
		result := i.buildAnalysisResultFromCache(task, cachedResp, analysisContext)
		if result != nil {
			return result
		}
		// Fall through if cache result couldn't be used
	}

	// Cache miss or invalid cached result, perform actual analysis
	result := i.analyzeObject(ctx, task, metadataHelper, analysisContext)

	// Cache successful results
	if result.Error == nil && result.Detail.Status == constants.StatStatusSuccess.String() {
		// Reconstruct response for caching
		resp := i.reconstructResponseFromDetail(result.Detail)
		if resp != nil {
			cache.Set(cacheKey, *resp, 1*time.Hour) // Cache for 1 hour
		}
	}

	return result
}

// reconstructResponseFromDetail reconstructs an AnalyzeSQLResponse from detail
// This parses the JSON strings stored in the detail back to their structured forms
func (i *OracleObjectParserTask) reconstructResponseFromDetail(detail *objectparser.OracleObjectDefinitionAnalyzeDetail) *dto.AnalyzeSQLResponse {
	if detail == nil {
		return nil
	}

	resp := &dto.AnalyzeSQLResponse{}

	// Parse MethodInvokeList
	if detail.MethodInvokeList != "" && detail.MethodInvokeList != "{}" {
		if err := json.Unmarshal([]byte(detail.MethodInvokeList), &resp.MethodInvokeList); err != nil {
			log.Warnf("Failed to unmarshal MethodInvokeList: %v", err)
		}
	}

	// Parse IdentifierList
	if detail.IdentifierList != "" && detail.IdentifierList != "{}" {
		if err := json.Unmarshal([]byte(detail.IdentifierList), &resp.IdentifierList); err != nil {
			log.Warnf("Failed to unmarshal IdentifierList: %v", err)
		}
	}

	// Parse TableReferenceList
	if detail.TableReferenceList != "" && detail.TableReferenceList != "{}" {
		if err := json.Unmarshal([]byte(detail.TableReferenceList), &resp.TableReferenceList); err != nil {
			log.Warnf("Failed to unmarshal TableReferenceList: %v", err)
		}
	}

	// Parse ReservedWordList
	if detail.ReservedWordList != "" && detail.ReservedWordList != "{}" {
		if err := json.Unmarshal([]byte(detail.ReservedWordList), &resp.ReservedWordList); err != nil {
			log.Warnf("Failed to unmarshal ReservedWordList: %v", err)
		}
	}

	// Parse PLSQLSegment
	if detail.PLSQLSegment != "" && detail.PLSQLSegment != "{}" {
		if err := json.Unmarshal([]byte(detail.PLSQLSegment), &resp.PLSQLSegment); err != nil {
			log.Warnf("Failed to unmarshal PLSQLSegment: %v", err)
		}
	}

	return resp
}
