package core

import (
	"context"
	"fmt"
	"runtime"
	"sync"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/adaptor"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"

	"time"

	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/panjf2000/ants/v2"
	"github.com/samber/lo"
)

// StartObjectParserTask in async way
func StartObjectParserTask(ctx context.Context, channelId, taskId int) {
	go func(Ctx context.Context, channelId, taskId int) {
		log.Infof("start object parser task, channelId:%d, taskId:%d", channelId, taskId)

		deleteLogErr := models.GetProgressLogReaderWriter().DeleteProgressLogDetailByChannelIdTaskIds(ctx, channelId, []int{taskId})
		if deleteLogErr != nil {
			log.Errorf("delete progress log detail failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, deleteLogErr)
		}

		SaveProcessLog(ctx, channelId, taskId, "start object parser task")

		channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
		if getChannelErr != nil {
			log.Errorf("get channel info error, channelId:%d, taskId:%d, err:%v", channelId, taskId, getChannelErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, getChannelErr)
			return
		}

		taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
		if getTaskErr != nil {
			log.Errorf("get task info error, channelId:%d, taskId:%d, err:%v", channelId, taskId, getTaskErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, getTaskErr)
			return
		}

		objectParserConfig := config.GetGlobalConfig().ObjectParserConfig
		if objectParserConfig == nil || objectParserConfig.Enable == false {
			err := fmt.Errorf("object parser service is disabled, channelId:%d, taskId:%d", channelId, taskId)
			log.Error(err)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, err)
			return
		}

		log.Infof("object parser service is enabled, setting up adaptor")
		objectParserAdaptor, err := adaptor.NewObjectParserAdaptor(objectParserConfig)
		if err != nil {
			log.Errorf("NewObjectParserAdaptor failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, err)
			return
		}

		channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, channelId)
		if getSchemaErr != nil {
			log.Errorf("GetChannelSchemas failed, channelId:%d, err: %v", channelId, getSchemaErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, getSchemaErr)
			return
		}
		targetSchemas := lo.Map(channelSchemas, func(item *channel.ChannelSchema, _ int) string {
			return item.SchemaNameS
		})

		dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
		if setUpErr != nil {
			log.Errorf("SetUpOracleDatabaseConns failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, setUpErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, setUpErr)
			return
		}

		defaultFeatures, getDefaultRuleErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(ctx)
		if getDefaultRuleErr != nil {
			log.Errorf("get default incompatible features error, channelId:%d, taskId:%d, err:%v", channelId, taskId, getDefaultRuleErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, getDefaultRuleErr)
			return
		}

		incompatibleFeatures, getRuleErr := models.GetObjectParserWriter().ListTaskOracleIncompatibleFeature(ctx, taskId)
		if getRuleErr != nil {
			log.Errorf("get incompatible features error, channelId:%d, taskId:%d, err:%v", channelId, taskId, getRuleErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, getRuleErr)
			return
		}

		objectParserParam, buildParamErr := BuildObjectParserParam(ctx, taskInfo)
		if buildParamErr != nil {
			log.Errorf("build object parser param error, channelId:%d, taskId:%d, err:%v", channelId, taskId, buildParamErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, buildParamErr)
			return
		}

		objectFilterCfgs, buildFilterErr := models.GetObjectParserWriter().ListObjectParserCfgsByTaskId(ctx, taskId)
		if buildFilterErr != nil {
			log.Errorf("list object filter cfs failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, buildFilterErr)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, buildFilterErr)
			return
		}

		ot := NewOracleObjectParserTask(channelInfo, taskInfo, defaultFeatures, incompatibleFeatures, objectParserAdaptor, dbConns, targetSchemas, objectParserParam, objectFilterCfgs)
		ot.ResetState(ctx)
		ot.ProcessStreaming(ctx)
	}(ctx, channelId, taskId)
}

func SaveTaskProcessErrorAndLog(ctx context.Context, channelId, taskId int, lastErr error) {
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.OPLogType, constants.OPStepInit, fmt.Sprintf("get metadata failed, err:%v", lastErr)),
		LogLevel:  log.LogError,
	})
	_ = models.GetTaskReaderWriter().UpdateTaskStatusAndMessage(ctx, taskId, constants.TASK_STATUS_FAILED, lastErr.Error())
}

func SaveProcessLog(ctx context.Context, channelId, taskId int, detail string) {
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.OPLogType, constants.OPStepInit, detail),
		LogLevel:  log.LogInfo,
	})
}

type OracleObjectParserTask struct {
	channelInfo          *channel.ChannelInformation
	taskInfo             *task.Task
	objectParserAdaptor  adaptor.Adaptor
	dbConns              *migration.MigrationSourceDBConns
	targetSchemas        []string
	defaultFeatures      []*objectparser.OracleIncompatibleFeature
	incompatibleFeatures []*objectparser.OracleTaskIncompatibleFeature
	objectParserParam    *structs.ObjectParserParam
	objectFilterCfgs     []*objectparser.ObjectParserCfg

	objectFilterMap map[string]bool
}

func NewOracleObjectParserTask(channelInfo *channel.ChannelInformation,
	taskInfo *task.Task,
	defaultFeatures []*objectparser.OracleIncompatibleFeature,
	incompatibleFeatures []*objectparser.OracleTaskIncompatibleFeature,
	objectParserAdaptor adaptor.Adaptor,
	dbConns *migration.MigrationSourceDBConns,
	targetSchemas []string,
	objectParserParam *structs.ObjectParserParam,
	objectFilterCfgs []*objectparser.ObjectParserCfg,
) *OracleObjectParserTask {
	t := &OracleObjectParserTask{
		channelInfo:          channelInfo,
		taskInfo:             taskInfo,
		objectParserAdaptor:  objectParserAdaptor,
		dbConns:              dbConns,
		targetSchemas:        targetSchemas,
		defaultFeatures:      defaultFeatures,
		incompatibleFeatures: incompatibleFeatures,
		objectParserParam:    objectParserParam,
		objectFilterCfgs:     objectFilterCfgs,
	}
	t.objectFilterMap = make(map[string]bool)
	for _, objectFilterCfg := range objectFilterCfgs {
		ot := objectFilterCfg.ObjectType
		if ot == constants.OracleObjectTypePackage {
			ot = constants.OracleObjectTypePackageBody
		}
		t.objectFilterMap[objectFilterCfg.SchemaName+"."+ot+"."+objectFilterCfg.ObjectName] = true
	}
	return t
}

func (i *OracleObjectParserTask) ResetState(ctx context.Context) error {
	startTime := time.Now()
	i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepInit, "resetting state before processing task"))
	log.Infof("before process task, reset state, channelId:%d, taskId:%d", i.channelInfo.ChannelId, i.taskInfo.TaskID)
	err := models.GetObjectParserWriter().DeleteOracleObjectDefinitionByTaskId(ctx, i.taskInfo.TaskID)
	if err != nil {
		log.Errorf("ResetState DeleteOracleObjectDefinitionByTaskId failed, channelId:%d, taskId:%d, err: %v", i.channelInfo.ChannelId, i.taskInfo.TaskID, err)
		return err
	}
	err = models.GetObjectParserWriter().DeleteOracleObjectDefinitionAnalyzeDetailByTaskId(ctx, i.taskInfo.TaskID)
	if err != nil {
		log.Errorf("ResetState DeleteOracleObjectDefinitionAnalyzeDetailByTaskId failed, channelId:%d, taskId:%d, err: %v", i.channelInfo.ChannelId, i.taskInfo.TaskID, err)
		return err
	}
	err = models.GetObjectParserWriter().DeleteOracleObjectDefinitionAnalyzeSummaryByTaskId(ctx, i.taskInfo.TaskID)
	if err != nil {
		log.Errorf("ResetState DeleteOracleObjectDefinitionAnalyzeSummaryByTaskId failed, channelId:%d, taskId:%d, err: %v", i.channelInfo.ChannelId, i.taskInfo.TaskID, err)
		return err
	}
	err = models.GetObjectParserWriter().DeleteOracleDependencyByTaskId(ctx, i.taskInfo.TaskID)
	if err != nil {
		log.Errorf("ResetState DeleteOracleDependencyByTaskId failed, channelId:%d, taskId:%d, err: %v", i.channelInfo.ChannelId, i.taskInfo.TaskID, err)
		return err
	}
	err = models.GetObjectParserWriter().DeleteOracleObjectDefinitionIncompatibleFeatureByTaskId(ctx, i.taskInfo.TaskID)
	if err != nil {
		log.Errorf("ResetState DeleteOracleObjectDefinitionIncompatibleFeatureByTaskId failed, channelId:%d, taskId:%d, err: %v", i.channelInfo.ChannelId, i.taskInfo.TaskID, err)
		return err
	}
	if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaResultByTaskId(ctx, i.taskInfo.TaskID); removeErr != nil {
		log.Errorf("RemoveOracleToJavaResultByTaskId failed, taskId:%d, serr: %v", i.taskInfo.TaskID, removeErr)
		return removeErr
	}
	if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaSummary(ctx, i.taskInfo.TaskID); removeErr != nil {
		log.Errorf("RemoveOracleToJavaSummary failed, taskId:%d, serr: %v", i.taskInfo.TaskID, removeErr)
		return removeErr
	}
	if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaLog(ctx, i.taskInfo.TaskID); removeErr != nil {
		log.Errorf("RemoveOracleToJavaLog failed, taskId:%d, serr: %v", i.taskInfo.TaskID, removeErr)
		return removeErr
	}
	i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepInit, "reset state completed, time cost: "+time.Since(startTime).String()))
	return nil
}

func (i *OracleObjectParserTask) getDurationAtLeast(startTime time.Time, minimalSecond float64) float64 {
	duration := time.Since(startTime).Seconds()
	if duration < minimalSecond {
		duration = minimalSecond
	}
	return duration
}

func (i *OracleObjectParserTask) GetIncompatibleFeatures() []*objectparser.OracleTaskIncompatibleFeature {
	return i.incompatibleFeatures
}
func (i *OracleObjectParserTask) GetDefaultIncompatibleFeatures() []*objectparser.OracleIncompatibleFeature {
	return i.defaultFeatures
}
func (i *OracleObjectParserTask) GetParam() *structs.ObjectParserParam {
	return i.objectParserParam
}
func (i *OracleObjectParserTask) GetChannelId() int {
	return i.channelInfo.ChannelId
}
func (i *OracleObjectParserTask) GetTaskId() int {
	return i.taskInfo.TaskID
}

func (i *OracleObjectParserTask) AppendProgressLog(ctx context.Context, detail string) {
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: i.GetChannelId(),
		TaskID:    i.GetTaskId(),
		Detail:    detail,
		LogLevel:  log.LogInfo,
	})
}

func (i *OracleObjectParserTask) AppendErrorProgressLog(ctx context.Context, detail string) {
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: i.GetChannelId(),
		TaskID:    i.GetTaskId(),
		Detail:    detail,
		LogLevel:  log.LogError,
	})
}

type MetadataBuildResult struct {
	definitionAnalyzeSummaries []*objectparser.OracleObjectDefinitionAnalyzeSummary
	definitionAnalyzeDetails   []*objectparser.OracleObjectDefinitionAnalyzeDetail
	definitionPos              []*objectparser.OracleObjectDefinition
	oracleDependencies         []*objectparser.OracleDependency
	metadataHelper             *MetadataHelper
}

type AnalysisContext struct {
	detailMap           map[string]*objectparser.OracleObjectDefinitionAnalyzeDetail
	summaryMap          map[string]*objectparser.OracleObjectDefinitionAnalyzeSummary
	summaryStartTimeMap map[string]time.Time
	objectScorer        *OracleObjectScorer
}

func (i *OracleObjectParserTask) finalizeTask(ctx context.Context, analysisContext *AnalysisContext) error {
	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID
	summaryMap, summaryStartTimeMap := analysisContext.summaryMap, analysisContext.summaryStartTimeMap

	var hasFailedSummary bool
	for _, summary := range summaryMap {
		if summary.SuccessNum == summary.TotalNum {
			summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[summary.SchemaName], 1)
			summary.Status = constants.StatStatusSuccess.String()
		} else {
			summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[summary.SchemaName], 1)
			summary.Status = constants.StatStatusFailed.String()
			hasFailedSummary = true
		}
	}
	summaries := lo.Values(summaryMap)
	finishErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		_, updateSummaryErr := models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeSummaries(transactionCtx, summaries)
		if updateSummaryErr != nil {
			log.Errorf("update oracle source analyze summaries error, channelId:%d, taskId:%d, err: %v", channelId, taskId, updateSummaryErr)
			return updateSummaryErr
		}

		i.taskInfo.EndTime = time.Now()
		if hasFailedSummary {
			i.taskInfo.TaskStatus = constants.TASK_STATUS_FAILED
		} else {
			i.taskInfo.TaskStatus = constants.TASK_STATUS_FINISH
		}
		_, updateTaskErr := models.GetTaskReaderWriter().UpdateTask(transactionCtx, i.taskInfo)
		if updateTaskErr != nil {
			log.Errorf("update task info error, channelId:%d, taskId:%d, err:%v", channelId, taskId, updateTaskErr)
			return updateTaskErr
		}

		return nil
	})
	if finishErr != nil {
		log.Errorf("finish task's Transaction failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, finishErr)
		return finishErr
	}

	var totalSuccess, totalFailed int
	for _, summary := range summaryMap {
		totalSuccess += summary.SuccessNum
		totalFailed += summary.FailedNum
	}

	log.Infof("finish task, channelId:%d, taskId:%d", channelId, taskId)
	i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepParseAST, fmt.Sprintf("object parser task completed successfully, total objects:%d, success:%d, failed:%d, schemas processed:%d", totalSuccess+totalFailed, totalSuccess, totalFailed, len(summaryMap))))

	return nil
}

func (i *OracleObjectParserTask) processObjectAnalysis(ctx context.Context, definitionPos []*objectparser.OracleObjectDefinition, metadataHelper *MetadataHelper, analysisContext *AnalysisContext) error {
	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID
	objectParserAdaptor := i.objectParserAdaptor
	detailMap, summaryMap, summaryStartTimeMap, objectScorer := analysisContext.detailMap, analysisContext.summaryMap, analysisContext.summaryStartTimeMap, analysisContext.objectScorer

	// Collect all incompatible features for batch save
	var allIncompatibleFeatures []*objectparser.OracleObjectDefinitionIncompatibleFeature

	// Create analysis cache for serial processing
	cache, err := NewAnalysisCache(1000) // Cache up to 1000 objects
	if err != nil {
		log.Warnf("Failed to create analysis cache: %v, proceeding without cache", err)
		cache = nil
	}

	// Initialize progress tracker for file system logging
	progressTracker := NewProgressTracker(i, constants.OPLogType, constants.OPStepParseAST, len(definitionPos))
	dbProgressTracker := NewDatabaseProgressTracker(i, constants.OPLogType, constants.OPStepParseAST, len(definitionPos))
	dbProgressTracker.ForceLog(ctx, fmt.Sprintf("parsing ast and analyzing object features, total objects: %d", len(definitionPos)))

	for idx, oracleObjectDefinitionCode := range definitionPos {
		// Check if context is cancelled for long-running loops
		select {
		case <-ctx.Done():
			log.Warnf("Context cancelled during object analysis, processed %d/%d objects, channelId:%d, taskId:%d", idx, len(definitionPos), channelId, taskId)
			return fmt.Errorf("context cancelled during object analysis: %w", ctx.Err())
		default:
			// Continue processing
		}

		log.Infof("start to analyze object definition, channelId:%d, taskId:%d, objectDefinitionId:%d, schemaName:%s, objectName:%s, objectType:%s", channelId, taskId, oracleObjectDefinitionCode.ID, oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType)

		var (
			incompatibleFeatures []*objectparser.OracleObjectDefinitionIncompatibleFeature
			actualReq            dto.AnalyzeSQLRequest
			actualResp           dto.AnalyzeSQLResponse
			analyzeErr           error
		)

		schemaName := oracleObjectDefinitionCode.SchemaName
		summary := summaryMap[schemaName]

		if _, exist := summaryStartTimeMap[schemaName]; !exist {
			summaryStartTimeMap[schemaName] = time.Now()
		}

		cacheKey := GenerateCacheKey(oracleObjectDefinitionCode)

		detail, exist := detailMap[oracleObjectDefinitionCode.SchemaObjectKey]
		if !exist {
			err := tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_MAP_SOURCE_CODE_TO_DETAIL_FAILED, "schema:%s, object:%s, type:%s", oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType)
			log.Errorf("detailUUIDMap not found, channelId:%d, taskId:%d, objectDefinitionId:%d, schemaName:%s, objectName:%s, objectType:%s", channelId, taskId, oracleObjectDefinitionCode.ID, oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType)

			// Create a new detail object to avoid nil pointer dereference
			detail = &objectparser.OracleObjectDefinitionAnalyzeDetail{
				TaskId:                          taskId,
				SchemaName:                      oracleObjectDefinitionCode.SchemaName,
				ObjectName:                      oracleObjectDefinitionCode.ObjectName,
				ObjectType:                      oracleObjectDefinitionCode.ObjectType,
				SchemaObjectKey:                 oracleObjectDefinitionCode.SchemaObjectKey,
				Status:                          constants.StatStatusFailed.String(),
				ErrorDetail:                     err.Error(),
				ReservedWordCount:               0,
				DatabaseLinkCount:               0,
				IncompatibleFeatureScoreContext: "",
			}
			summary.FailedNum += 1
			summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[schemaName], 1)

			goto SAVE_DETAIL
		}

		summary.Status = constants.StatStatusRunning.String()
		if metadataHelper.GetObjectStatus(oracleObjectDefinitionCode.SchemaObjectKey) == "INVALID" {
			err := tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_INVALID_OBJECT, "")
			log.Errorf("object invalid, channelId:%d, taskId:%d, objectDefinitionId:%d, schemaName:%s, objectName:%s, objectType:%s, err: %v", channelId, taskId, oracleObjectDefinitionCode.ID, oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType, err)
			detail.Status = constants.StatStatusFailed.String()
			detail.ErrorDetail = err.Error()
			summary.FailedNum += 1
			summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[schemaName], 1)
			goto SAVE_DETAIL
		}

		// Try to get from cache first
		if cache != nil {
			if cachedResp, found := cache.Get(cacheKey); found {
				log.Debugf("Cache hit for object %s", oracleObjectDefinitionCode.SchemaObjectKey)
				// Use cached response
				actualResp = *cachedResp
				detail.Status = constants.StatStatusSuccess.String()
				detail.MethodInvokeList = actualResp.MarshalMethodInvokeListToString()
				detail.IdentifierList = actualResp.MarshalIdentifierListToString()
				detail.TableReferenceList = actualResp.MarshalTableReferenceListToString()
				detail.ReservedWordList = actualResp.MarshalReservedWordListToString()
				detail.PLSQLSegment = actualResp.MarshalPLSQLSegmentToString()
				detail.ReservedWordCount, detail.DatabaseLinkCount, detail.IncompatibleFeatureScoreContext =
					objectScorer.GetIncompatibleFeatureCount(actualResp.GetReservedWordList(), actualResp.GetTableReferenceList())
				summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[schemaName], 1)
				summary.SuccessNum += 1
				incompatibleFeatures = objectScorer.BuildIncompatibleFeatures(detail, actualResp)
				goto SAVE_DETAIL
			}
		}

		// Cache miss, perform actual analysis
		actualReq = dto.AnalyzeSQLRequest{
			IsEncoded: true,
			PLSQL:     stringutil.EncodeSQL(oracleObjectDefinitionCode.AllText),
		}
		actualResp, analyzeErr = objectParserAdaptor.AnalyzeSQL(ctx, actualReq)
		if analyzeErr != nil {
			log.Errorf("adaptor.AnalyzeSQL failed, channelId:%d, taskId:%d, objectDefinitionId:%d, schemaName:%s, objectName:%s, objectType:%s, err: %v", channelId, taskId, oracleObjectDefinitionCode.ID, oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType, analyzeErr)
			detail.Status = constants.StatStatusFailed.String()
			detail.ErrorDetail = tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PARSE_OBJECT_FAILED, analyzeErr.Error()).Error()
			summary.FailedNum += 1
			summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[schemaName], 1)
			goto SAVE_DETAIL
		}

		// Cache successful result
		if cache != nil {
			cache.Set(cacheKey, actualResp, 1*time.Hour)
		}

		detail.Status = constants.StatStatusSuccess.String()
		detail.MethodInvokeList = actualResp.MarshalMethodInvokeListToString()
		detail.IdentifierList = actualResp.MarshalIdentifierListToString()
		detail.TableReferenceList = actualResp.MarshalTableReferenceListToString()
		detail.ReservedWordList = actualResp.MarshalReservedWordListToString()
		detail.PLSQLSegment = actualResp.MarshalPLSQLSegmentToString()
		detail.ReservedWordCount, detail.DatabaseLinkCount, detail.IncompatibleFeatureScoreContext =
			objectScorer.GetIncompatibleFeatureCount(actualResp.GetReservedWordList(), actualResp.GetTableReferenceList())
		summary.Duration = i.getDurationAtLeast(summaryStartTimeMap[schemaName], 1)
		summary.SuccessNum += 1
		incompatibleFeatures = objectScorer.BuildIncompatibleFeatures(detail, actualResp)

	SAVE_DETAIL:
		// Update progress tracker for file system logging
		itemDesc := fmt.Sprintf("%s.%s (%s)", oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType)
		isSuccess := detail.Status == constants.StatStatusSuccess.String()
		progressTracker.Update(ctx, idx+1, isSuccess, itemDesc)

		// Update database progress tracker (time-based only)
		if isSuccess {
			dbProgressTracker.UpdateProgressWithCounts(ctx, idx+1, idx+1, 0, "ast")
		} else {
			dbProgressTracker.UpdateProgressWithCounts(ctx, idx+1, idx, 1, "ast")
		}

		if detail.Status == constants.StatStatusFailed.String() {
			i.AppendErrorProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepParseAST, fmt.Sprintf("analysis failed %d/%d: %s, error:%v", idx+1, len(definitionPos), itemDesc, detail.ErrorDetail)))
		}

		// Collect incompatible features for batch save
		allIncompatibleFeatures = append(allIncompatibleFeatures, incompatibleFeatures...)

		_, updateDetailErr := models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeDetail(ctx, detail)
		if updateDetailErr != nil {
			log.Errorf("update oracle source analyze detail error, channelId:%d, taskId:%d, objectDefinitionId:%d, schemaName:%s, objectName:%s, objectType:%s, err: %v", channelId, taskId, oracleObjectDefinitionCode.ID, oracleObjectDefinitionCode.SchemaName, oracleObjectDefinitionCode.ObjectName, oracleObjectDefinitionCode.ObjectType, updateDetailErr)
			return updateDetailErr
		}
		_, updateSummaryErr := models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeSummary(ctx, summary)
		if updateSummaryErr != nil {
			log.Errorf("update oracle source analyze summary error, channelId:%d, taskId:%d, schemaName:%s, err: %v", channelId, taskId, oracleObjectDefinitionCode.SchemaName, updateSummaryErr)
			return updateSummaryErr
		}
	}

	// Batch save all incompatible features at once
	if len(allIncompatibleFeatures) > 0 {
		i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepParseAST, fmt.Sprintf("saving %d incompatible features in batches", len(allIncompatibleFeatures))))
		_, saveFeatureErr := i.saveIncompatibleFeaturesWithSmartBatching(ctx, allIncompatibleFeatures)
		if saveFeatureErr != nil {
			log.Errorf("batch save oracle source code incompatible features error, channelId:%d, taskId:%d, err: %v", channelId, taskId, saveFeatureErr)
			return saveFeatureErr
		}
	}

	// Final statistics
	success, failed, total, elapsed := progressTracker.GetStats()
	log.Infof("analyze and save source code success, channelId:%d, taskId:%d, total:%d, success:%d, failed:%d, time:%v", channelId, taskId, total, success, failed, elapsed)

	// Output cache statistics if cache was used
	if cache != nil {
		hits, misses, hitRate := cache.GetStats()
		log.Infof("Analysis cache stats - hits: %d, misses: %d, hit rate: %.1f%%", hits, misses, hitRate)
	}

	progressTracker.ForceLog(ctx, fmt.Sprintf("ast analysis completed, total: %d, success: %d, failed: %d, time: %v", total, success, failed, elapsed))

	return nil
}

func (i *OracleObjectParserTask) setupAnalysisContext(definitionAnalyzeDetails []*objectparser.OracleObjectDefinitionAnalyzeDetail, definitionAnalyzeSummaries []*objectparser.OracleObjectDefinitionAnalyzeSummary) *AnalysisContext {
	detailMap := make(map[string]*objectparser.OracleObjectDefinitionAnalyzeDetail)
	for _, detail := range definitionAnalyzeDetails {
		ot := detail.ObjectType
		if ot == constants.OracleObjectTypePackage {
			ot = constants.OracleObjectTypePackageBody
		}
		detailKey := detail.SchemaName + "." + ot + "." + detail.ObjectName
		if len(i.objectFilterCfgs) != 0 && !i.objectFilterMap[detailKey] {
			continue
		}
		detailMap[detail.SchemaObjectKey] = detail
	}
	summaryMap := make(map[string]*objectparser.OracleObjectDefinitionAnalyzeSummary)
	for _, summary := range definitionAnalyzeSummaries {
		summaryMap[summary.SchemaName] = summary
	}
	summaryStartTimeMap := make(map[string]time.Time)

	objectScorer := InitOracleObjectScorer(i.GetChannelId(), i.GetTaskId(), i.GetDefaultIncompatibleFeatures(), i.GetIncompatibleFeatures(), i.GetParam())

	return &AnalysisContext{
		detailMap:           detailMap,
		summaryMap:          summaryMap,
		summaryStartTimeMap: summaryStartTimeMap,
		objectScorer:        objectScorer,
	}
}

// saveObjectDefinitionsWithSmartBatching saves object definitions in batches with dynamic batch size adjustment
func (i *OracleObjectParserTask) saveObjectDefinitionsWithSmartBatching(ctx context.Context, items []*objectparser.OracleObjectDefinition, batcher *SmartBatcher) ([]*objectparser.OracleObjectDefinition, error) {
	return i.saveObjectDefinitionsWithSmartBatchingAndProgress(ctx, items, batcher, nil)
}

// saveObjectDefinitionsWithSmartBatchingAndProgress saves object definitions in batches with progress tracking
func (i *OracleObjectParserTask) saveObjectDefinitionsWithSmartBatchingAndProgress(ctx context.Context, items []*objectparser.OracleObjectDefinition, batcher *SmartBatcher, progressTracker ProgressUpdater) ([]*objectparser.OracleObjectDefinition, error) {

	if len(items) == 0 {
		return items, nil
	}

	var allResults []*objectparser.OracleObjectDefinition

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleObjectDefinition(ctx, batch)
		if err != nil {
			return nil, err
		}

		allResults = append(allResults, results...)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		log.Debugf("Saved batch of %d object definitions in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		// Update progress if tracker is provided
		if progressTracker != nil {
			progressTracker.Update(ctx, end, true, fmt.Sprintf("saved %d/%d object definitions", end, len(items)))
		}

		start = end
	}

	return allResults, nil
}

// saveOracleDependenciesWithSmartBatching saves oracle dependencies in batches with dynamic batch size adjustment
func (i *OracleObjectParserTask) saveOracleDependenciesWithSmartBatching(ctx context.Context, items []*objectparser.OracleDependency, batcher *SmartBatcher) ([]*objectparser.OracleDependency, error) {
	return i.saveOracleDependenciesWithSmartBatchingAndProgress(ctx, items, batcher, nil)
}

// saveOracleDependenciesWithSmartBatchingAndProgress saves oracle dependencies in batches with progress tracking
func (i *OracleObjectParserTask) saveOracleDependenciesWithSmartBatchingAndProgress(ctx context.Context, items []*objectparser.OracleDependency, batcher *SmartBatcher, progressTracker ProgressUpdater) ([]*objectparser.OracleDependency, error) {

	if len(items) == 0 {
		return items, nil
	}

	var allResults []*objectparser.OracleDependency

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleOracleDependency(ctx, batch)
		if err != nil {
			return nil, err
		}

		allResults = append(allResults, results...)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		log.Debugf("Saved batch of %d oracle dependencies in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		// Update progress if tracker is provided
		if progressTracker != nil {
			progressTracker.Update(ctx, end, true, fmt.Sprintf("saved %d/%d dependencies", end, len(items)))
		}

		start = end
	}

	return allResults, nil
}

// saveAnalyzeSummariesWithSmartBatching saves analyze summaries in batches with dynamic batch size adjustment
func (i *OracleObjectParserTask) saveAnalyzeSummariesWithSmartBatching(ctx context.Context, items []*objectparser.OracleObjectDefinitionAnalyzeSummary, batcher *SmartBatcher) ([]*objectparser.OracleObjectDefinitionAnalyzeSummary, error) {

	if len(items) == 0 {
		return items, nil
	}

	var allResults []*objectparser.OracleObjectDefinitionAnalyzeSummary

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleObjectDefinitionAnalyzeSummary(ctx, batch)
		if err != nil {
			return nil, err
		}

		allResults = append(allResults, results...)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		log.Debugf("Saved batch of %d analyze summaries in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		start = end
	}

	return allResults, nil
}

// saveAnalyzeDetailsWithSmartBatching saves analyze details in batches with dynamic batch size adjustment
func (i *OracleObjectParserTask) saveAnalyzeDetailsWithSmartBatching(ctx context.Context, items []*objectparser.OracleObjectDefinitionAnalyzeDetail, batcher *SmartBatcher) ([]*objectparser.OracleObjectDefinitionAnalyzeDetail, error) {
	return i.saveAnalyzeDetailsWithSmartBatchingAndProgress(ctx, items, batcher, nil)
}

// saveAnalyzeDetailsWithSmartBatchingAndProgress saves analyze details in batches with progress tracking
func (i *OracleObjectParserTask) saveAnalyzeDetailsWithSmartBatchingAndProgress(ctx context.Context, items []*objectparser.OracleObjectDefinitionAnalyzeDetail, batcher *SmartBatcher, progressTracker ProgressUpdater) ([]*objectparser.OracleObjectDefinitionAnalyzeDetail, error) {

	if len(items) == 0 {
		return items, nil
	}

	var allResults []*objectparser.OracleObjectDefinitionAnalyzeDetail

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleObjectDefinitionAnalyzeDetail(ctx, batch)
		if err != nil {
			return nil, err
		}

		allResults = append(allResults, results...)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		log.Debugf("Saved batch of %d analyze details in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		// Update progress if tracker is provided
		if progressTracker != nil {
			progressTracker.Update(ctx, end, true, fmt.Sprintf("saved %d/%d analyze details", end, len(items)))
		}

		start = end
	}

	return allResults, nil
}

// saveObjectDefinitionsWithDatabaseProgress saves object definitions with database progress tracking
func (i *OracleObjectParserTask) saveObjectDefinitionsWithDatabaseProgress(ctx context.Context, items []*objectparser.OracleObjectDefinition, batcher *SmartBatcher, dbTracker *DatabaseProgressTracker, startOffset int) (int, error) {
	if len(items) == 0 {
		return 0, nil
	}

	savedCount := 0

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleObjectDefinition(ctx, batch)
		if err != nil {
			return savedCount, err
		}

		savedCount += len(results)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		// Log to file system (debug level)
		log.Debugf("Saved batch of %d object definitions in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		// Update database progress tracker
		dbTracker.UpdateProgressWithCounts(ctx, startOffset+savedCount, startOffset+savedCount, 0, "definitions")

		start = end
	}

	return savedCount, nil
}

// saveOracleDependenciesWithDatabaseProgress saves oracle dependencies with database progress tracking
func (i *OracleObjectParserTask) saveOracleDependenciesWithDatabaseProgress(ctx context.Context, items []*objectparser.OracleDependency, batcher *SmartBatcher, dbTracker *DatabaseProgressTracker, startOffset int) (int, error) {
	if len(items) == 0 {
		return 0, nil
	}

	savedCount := 0

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleOracleDependency(ctx, batch)
		if err != nil {
			return savedCount, err
		}

		savedCount += len(results)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		// Log to file system (debug level)
		log.Debugf("Saved batch of %d oracle dependencies in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		// Update database progress tracker
		dbTracker.UpdateProgressWithCounts(ctx, startOffset+savedCount, startOffset+savedCount, 0, "dependencies")

		start = end
	}

	return savedCount, nil
}

// saveAnalyzeDetailsWithDatabaseProgress saves analyze details with database progress tracking
func (i *OracleObjectParserTask) saveAnalyzeDetailsWithDatabaseProgress(ctx context.Context, items []*objectparser.OracleObjectDefinitionAnalyzeDetail, batcher *SmartBatcher, dbTracker *DatabaseProgressTracker, startOffset int) (int, error) {
	if len(items) == 0 {
		return 0, nil
	}

	savedCount := 0

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleObjectDefinitionAnalyzeDetail(ctx, batch)
		if err != nil {
			return savedCount, err
		}

		savedCount += len(results)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		// Log to file system (debug level)
		log.Debugf("Saved batch of %d analyze details in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		// Update database progress tracker
		dbTracker.UpdateProgressWithCounts(ctx, startOffset+savedCount, startOffset+savedCount, 0, "analyze details")

		start = end
	}

	return savedCount, nil
}

// saveIncompatibleFeaturesWithSmartBatching saves incompatible features in batches with dynamic batch size adjustment
func (i *OracleObjectParserTask) saveIncompatibleFeaturesWithSmartBatching(ctx context.Context, items []*objectparser.OracleObjectDefinitionIncompatibleFeature) ([]*objectparser.OracleObjectDefinitionIncompatibleFeature, error) {

	if len(items) == 0 {
		return items, nil
	}

	// Create a dedicated batcher for incompatible features with smaller batch sizes
	// since these records are typically smaller but more numerous
	batcher := NewSmartBatcher(32, 256, 50*time.Millisecond)

	var allResults []*objectparser.OracleObjectDefinitionIncompatibleFeature

	for start := 0; start < len(items); {
		batchSize := batcher.GetCurrentBatchSize()
		end := start + batchSize
		if end > len(items) {
			end = len(items)
		}

		batch := items[start:end]
		startTime := time.Now()

		// Save batch
		results, err := models.GetObjectParserWriter().CreateOracleObjectDefinitionIncompatibleFeatures(ctx, batch)
		if err != nil {
			return nil, err
		}

		allResults = append(allResults, results...)

		// Record performance and adjust batch size
		duration := time.Since(startTime)
		batcher.RecordBatchPerformance(len(batch), duration)

		log.Debugf("Saved batch of %d incompatible features in %v (current batch size: %d)",
			len(batch), duration, batcher.GetCurrentBatchSize())

		start = end
	}

	return allResults, nil
}

// batchSaveIncompatibleFeatures accumulates and batch saves incompatible features from concurrent analysis
func (i *OracleObjectParserTask) batchSaveIncompatibleFeatures(ctx context.Context, featureAccumulator <-chan []*objectparser.OracleObjectDefinitionIncompatibleFeature, done chan<- bool) {
	defer func() {
		done <- true
	}()

	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID
	var allFeatures []*objectparser.OracleObjectDefinitionIncompatibleFeature
	batchThreshold := 1000                    // Save when we accumulate this many features
	ticker := time.NewTicker(5 * time.Second) // Or save every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case features, ok := <-featureAccumulator:
			if !ok {
				// Channel closed, save any remaining features
				if len(allFeatures) > 0 {
					log.Infof("Saving final batch of %d incompatible features", len(allFeatures))
					i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepParseAST,
						fmt.Sprintf("saving final batch of %d incompatible features", len(allFeatures))))
					if _, err := i.saveIncompatibleFeaturesWithSmartBatching(ctx, allFeatures); err != nil {
						log.Errorf("Failed to save final batch of incompatible features, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
					}
				}
				return
			}
			allFeatures = append(allFeatures, features...)

			// Check if we should save
			if len(allFeatures) >= batchThreshold {
				log.Infof("Batch threshold reached, saving %d incompatible features", len(allFeatures))
				if _, err := i.saveIncompatibleFeaturesWithSmartBatching(ctx, allFeatures); err != nil {
					log.Errorf("Failed to batch save incompatible features, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
				}
				allFeatures = nil // Reset
			}

		case <-ticker.C:
			// Periodic save
			if len(allFeatures) > 0 {
				log.Infof("Periodic save of %d incompatible features", len(allFeatures))
				if _, err := i.saveIncompatibleFeaturesWithSmartBatching(ctx, allFeatures); err != nil {
					log.Errorf("Failed to periodic save incompatible features, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
				}
				allFeatures = nil // Reset
			}

		case <-ctx.Done():
			// Context cancelled, save what we have and exit
			if len(allFeatures) > 0 {
				log.Warnf("Context cancelled, saving %d incompatible features before exit", len(allFeatures))
				if _, err := i.saveIncompatibleFeaturesWithSmartBatching(ctx, allFeatures); err != nil {
					log.Errorf("Failed to save incompatible features on context cancel, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
				}
			}
			return
		}
	}
}

// AnalysisTask represents a single object analysis task
type AnalysisTask struct {
	Index                  int
	OracleObjectDefinition *objectparser.OracleObjectDefinition
}

// AnalysisResult represents the result of analyzing a single object
type AnalysisResult struct {
	Index                int
	Detail               *objectparser.OracleObjectDefinitionAnalyzeDetail
	IncompatibleFeatures []*objectparser.OracleObjectDefinitionIncompatibleFeature
	Error                error
}

// processObjectAnalysisConcurrent processes object analysis using ants pool for concurrent execution
func (i *OracleObjectParserTask) processObjectAnalysisConcurrent(ctx context.Context, definitionPos []*objectparser.OracleObjectDefinition, metadataHelper *MetadataHelper, analysisContext *AnalysisContext) error {
	poolSize := i.objectParserParam.AnalyzeWorkerPoolSize
	if poolSize <= 0 {
		poolSize = runtime.NumCPU() * 2
	}

	totalObjects := len(definitionPos)
	resultChan := make(chan *AnalysisResult, totalObjects)

	// Channel for accumulating incompatible features
	featureAccumulator := make(chan []*objectparser.OracleObjectDefinitionIncompatibleFeature, poolSize)
	featuresDone := make(chan bool)

	// Start goroutine to accumulate and batch save incompatible features
	go i.batchSaveIncompatibleFeatures(ctx, featureAccumulator, featuresDone)

	// Create analysis cache
	cache, err := NewAnalysisCache(1000) // Cache up to 1000 objects
	if err != nil {
		log.Warnf("Failed to create analysis cache: %v, proceeding without cache", err)
		cache = nil
	}

	// Initialize progress tracker for file system logging
	progressTracker := NewProgressTracker(i, constants.OPLogType, constants.OPStepParseAST, totalObjects)
	progressTracker.ForceLog(ctx, fmt.Sprintf("starting concurrent AST analysis with %d workers for %d objects", poolSize, totalObjects))

	// Initialize database progress tracker for time-based logging only
	dbProgressTracker := NewDatabaseProgressTracker(i, constants.OPLogType, constants.OPStepParseAST, totalObjects)
	dbProgressTracker.ForceLog(ctx, fmt.Sprintf("starting concurrent AST analysis with %d workers for %d objects", poolSize, totalObjects))

	// Create ants pool
	pool, err := ants.NewPoolWithFunc(poolSize, func(payload interface{}) {
		task := payload.(*AnalysisTask)
		// Use analyzeObjectWithCache for better performance with caching
		result := i.analyzeObjectWithCache(ctx, task, metadataHelper, analysisContext, cache)
		resultChan <- result
	})
	if err != nil {
		log.Errorf("Failed to create ants pool: %v", err)
		close(featureAccumulator)
		<-featuresDone
		return err
	}
	defer pool.Release()

	// Submit tasks to pool
	for idx, objDef := range definitionPos {
		task := &AnalysisTask{
			Index:                  idx,
			OracleObjectDefinition: objDef,
		}

		if err := pool.Invoke(task); err != nil {
			log.Errorf("Failed to submit task %d to pool: %v", idx, err)
			resultChan <- &AnalysisResult{
				Index: idx,
				Error: err,
			}
		}
	}

	// Collect results and save in real-time
	summaryMutex := &sync.Mutex{}

	for completed := 0; completed < totalObjects; completed++ {
		select {
		case result := <-resultChan:
			itemDesc := fmt.Sprintf("%s.%s (%s)", result.Detail.SchemaName, result.Detail.ObjectName, result.Detail.ObjectType)
			isSuccess := result.Error == nil

			if !isSuccess {
				i.AppendErrorProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepParseAST,
					fmt.Sprintf("analysis failed %d/%d: %s, error: %v",
						completed+1, totalObjects, itemDesc, result.Error)))
			}

			// Update progress tracker for file system logging
			progressTracker.Update(ctx, completed+1, isSuccess, itemDesc)

			// Update database progress tracker (time-based only)
			if isSuccess {
				dbProgressTracker.UpdateProgressWithCounts(ctx, completed+1, completed+1, 0, "ast")
			} else {
				dbProgressTracker.UpdateProgressWithCounts(ctx, completed+1, completed, 1, "ast")
			}

			// Save result in real-time (send features to accumulator)
			if err := i.saveAnalysisResult(ctx, result, analysisContext, summaryMutex, featureAccumulator); err != nil {
				log.Errorf("Failed to save analysis result: %v", err)
			}

		case <-ctx.Done():
			close(featureAccumulator)
			<-featuresDone
			return fmt.Errorf("context cancelled during concurrent analysis")
		}
	}

	// Close feature accumulator and wait for batch save to complete
	close(featureAccumulator)
	<-featuresDone

	// Get final statistics
	success, failed, total, elapsed := progressTracker.GetStats()
	log.Infof("Concurrent analysis completed, total: %d, success: %d, failed: %d, time: %v",
		total, success, failed, elapsed)

	// Output cache statistics if cache was used
	if cache != nil {
		hits, misses, hitRate := cache.GetStats()
		log.Infof("Analysis cache stats - hits: %d, misses: %d, hit rate: %.1f%%", hits, misses, hitRate)
		i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepParseAST,
			fmt.Sprintf("cache performance: hits=%d, misses=%d, hit rate=%.1f%%", hits, misses, hitRate)))
	}

	progressTracker.ForceLog(ctx, fmt.Sprintf("ast analysis completed, total: %d, success: %d, failed: %d, time: %v",
		total, success, failed, elapsed))

	return nil
}

// analyzeObject analyzes a single object
func (i *OracleObjectParserTask) analyzeObject(ctx context.Context, task *AnalysisTask, metadataHelper *MetadataHelper, analysisContext *AnalysisContext) *AnalysisResult {
	objDef := task.OracleObjectDefinition
	detailMap := analysisContext.detailMap
	objectScorer := analysisContext.objectScorer

	result := &AnalysisResult{Index: task.Index}

	// Get or create detail
	detail, exist := detailMap[objDef.SchemaObjectKey]
	if !exist {
		err := tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_MAP_SOURCE_CODE_TO_DETAIL_FAILED,
			"schema:%s, object:%s, type:%s", objDef.SchemaName, objDef.ObjectName, objDef.ObjectType)

		detail = &objectparser.OracleObjectDefinitionAnalyzeDetail{
			TaskId:          i.taskInfo.TaskID,
			SchemaName:      objDef.SchemaName,
			ObjectName:      objDef.ObjectName,
			ObjectType:      objDef.ObjectType,
			SchemaObjectKey: objDef.SchemaObjectKey,
			Status:          constants.StatStatusFailed.String(),
			ErrorDetail:     err.Error(),
		}
		result.Detail = detail
		result.Error = err
		return result
	}

	// Check object status
	if metadataHelper.GetObjectStatus(objDef.SchemaObjectKey) == "INVALID" {
		err := tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_INVALID_OBJECT, "")
		detail.Status = constants.StatStatusFailed.String()
		detail.ErrorDetail = err.Error()
		result.Detail = detail
		result.Error = err
		return result
	}

	// Call Parser API for analysis
	actualReq := dto.AnalyzeSQLRequest{
		IsEncoded: true,
		PLSQL:     stringutil.EncodeSQL(objDef.AllText),
	}

	actualResp, analyzeErr := i.objectParserAdaptor.AnalyzeSQL(ctx, actualReq)
	if analyzeErr != nil {
		detail.Status = constants.StatStatusFailed.String()
		detail.ErrorDetail = tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PARSE_OBJECT_FAILED, analyzeErr.Error()).Error()
		result.Detail = detail
		result.Error = analyzeErr
		return result
	}

	// Analysis successful, populate results
	detail.Status = constants.StatStatusSuccess.String()
	detail.MethodInvokeList = actualResp.MarshalMethodInvokeListToString()
	detail.IdentifierList = actualResp.MarshalIdentifierListToString()
	detail.TableReferenceList = actualResp.MarshalTableReferenceListToString()
	detail.ReservedWordList = actualResp.MarshalReservedWordListToString()
	detail.PLSQLSegment = actualResp.MarshalPLSQLSegmentToString()
	detail.ReservedWordCount, detail.DatabaseLinkCount, detail.IncompatibleFeatureScoreContext =
		objectScorer.GetIncompatibleFeatureCount(actualResp.GetReservedWordList(), actualResp.GetTableReferenceList())

	result.Detail = detail
	result.IncompatibleFeatures = objectScorer.BuildIncompatibleFeatures(detail, actualResp)

	return result
}

// saveAnalysisResult saves the analysis result to database
func (i *OracleObjectParserTask) saveAnalysisResult(ctx context.Context, result *AnalysisResult, analysisContext *AnalysisContext, summaryMutex *sync.Mutex, featureAccumulator chan<- []*objectparser.OracleObjectDefinitionIncompatibleFeature) error {
	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID

	// Update summary with mutex protection
	summaryMutex.Lock()
	summary := analysisContext.summaryMap[result.Detail.SchemaName]
	if result.Error != nil {
		summary.FailedNum++
	} else {
		summary.SuccessNum++
	}
	summary.Duration = i.getDurationAtLeast(analysisContext.summaryStartTimeMap[result.Detail.SchemaName], 1)
	summaryMutex.Unlock()

	// Send incompatible features to accumulator channel
	if len(result.IncompatibleFeatures) > 0 {
		select {
		case featureAccumulator <- result.IncompatibleFeatures:
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	// Update detail
	_, err := models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeDetail(ctx, result.Detail)
	if err != nil {
		log.Errorf("update oracle source analyze detail error, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
		return err
	}

	// Update summary
	_, err = models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeSummary(ctx, summary)
	if err != nil {
		log.Errorf("update oracle source analyze summary error, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
		return err
	}

	return nil
}
