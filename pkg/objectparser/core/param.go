package core

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/crypto"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
)

func BuildObjectParserParam(ctx context.Context, taskInfo *task.Task) (*structs.ObjectParserParam, error) {
	log.Infof("build object-pareser config params, channelId: %d, taskId: %d, templateId:%d", taskInfo.ChannelId, taskInfo.TaskID, taskInfo.TaskParamTmplateId)

	objectParserParam := &structs.ObjectParserParam{}
	taskParamMap := make(map[string]string)

	// task params merge
	defaultTaskCfgs, getDetailErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, taskInfo.TaskParamTmplateId)
	if getDetailErr != nil {
		return nil, fmt.Errorf("get task params detail info failed: %v", getDetailErr)
	}

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.ChannelId,
		TaskID:              taskInfo.TaskID,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if getParamErr != nil {
		return nil, fmt.Errorf("get task params info failed: %v", getParamErr)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}

	var intVal int
	var floatVal float64
	var parseErr error
	if value, ok := taskParamMap[constants.ParamsObjectParserReservedWordScoringMode]; ok {
		intVal, parseErr = parse.ParseInt(value)
		if parseErr != nil {
			return nil, fmt.Errorf("parse.ParseInt keyword_scoring_mode failed: %v", parseErr)
		}
		objectParserParam.SetReservedWordScoringMode(intVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserDatabaseLinkScoringMode]; ok {
		intVal, parseErr = parse.ParseInt(value)
		if parseErr != nil {
			return nil, fmt.Errorf("parse.ParseInt database_link_scoring_mode failed: %v", parseErr)
		}
		objectParserParam.SetDatabaseLinkScoringMode(intVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserConvertJavaThread]; ok {
		intVal, _ = parse.ParseInt(value)
		objectParserParam.SetConvertJavaThread(intVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserHistogramScoreWeightFactor]; ok {
		floatVal, parseErr = parse.ParseFloat64(value)
		if parseErr != nil {
			return nil, fmt.Errorf("parse.ParseFloat64 histogram_score_weight_factor failed: %v", parseErr)
		}
		objectParserParam.SetHistogramScoreWeightFactor(floatVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserOpenAISource]; ok {
		if strings.TrimSpace(value) == "" {
			value = "openai"
		}
		objectParserParam.SetSource(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMSource]; ok {
		if strings.TrimSpace(value) == "" {
			value = "openai"
		}
		objectParserParam.SetSource(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserOpenAIModel]; ok {
		if strings.TrimSpace(value) == "" {
			value = "gpt-4o"
		}
		objectParserParam.SetModel(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMModel]; ok {
		if strings.TrimSpace(value) == "" {
			value = "gpt-4o"
		}
		objectParserParam.SetModel(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserOpenAIEndpoint]; ok {
		objectParserParam.SetEndpoint(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMEndpoint]; ok {
		objectParserParam.SetEndpoint(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserOpenAIKey]; ok {
		// 使用正则匹配（不区分大小写）
		matched, _ := regexp.MatchString(`(?i)^ENC_`, value)
		if matched { //已加密
			decryptKey := crypto.AesDefaultDecrypt(value[len("ENC_"):], crypto.DefaultKey)
			objectParserParam.SetAPIKey(decryptKey)
		} else { //未加密
			objectParserParam.SetAPIKey(value)
		}
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMAPIKey]; ok {
		// 使用正则匹配（不区分大小写）
		matched, _ := regexp.MatchString(`(?i)^ENC_`, value)
		if matched { //已加密
			decryptKey := crypto.AesDefaultDecrypt(value[len("ENC_"):], crypto.DefaultKey)
			objectParserParam.SetAPIKey(decryptKey)
		} else { //未加密
			objectParserParam.SetAPIKey(value)
		}
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMMaxTokens]; ok {
		intVal, _ = parse.ParseInt(value)
		objectParserParam.SetMaxTokens(intVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMMaxTokensName]; ok {
		objectParserParam.SetMaxTokensName(value)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMTimeout]; ok {
		intVal, _ = parse.ParseInt(value)
		objectParserParam.SetTimeout(intVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMTemperature]; ok {
		float32Val, _ := parse.ParseFloat32(value)
		objectParserParam.SetTemperature(float32Val)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserLLMStream]; ok {
		boolVal := parse.ParseBoolWithDefault(value, false)
		objectParserParam.SetStream(boolVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserAnalyzeWorkerPoolSize]; ok {
		intVal, _ = parse.ParseInt(value)
		objectParserParam.SetAnalyzeWorkerPoolSize(intVal)
	}
	if value, ok := taskParamMap[constants.ParamsObjectParserAnalyzeBatchSize]; ok {
		intVal, _ = parse.ParseInt(value)
		objectParserParam.SetAnalyzeBatchSize(intVal)
	}
	log.Infof("build object parser param success, taskId:%d, %v", taskInfo.TaskID, objectParserParam.String())
	return objectParserParam, nil
}
