package core

import (
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
)

func ComposeFeatures(features []*objectparser.OracleTaskIncompatibleFeature, defaultFeatures []*objectparser.OracleIncompatibleFeature, taskId int) map[structs.FeatureItem]*objectparser.OracleTaskIncompatibleFeature {
	composedFeatureMap := make(map[structs.FeatureItem]*objectparser.OracleTaskIncompatibleFeature)
	for _, feature := range features {
		fk := structs.FeatureItem{FeatureType: feature.FeatureType, FeatureKey: feature.FeatureKey}
		composedFeatureMap[fk] = &objectparser.OracleTaskIncompatibleFeature{
			ID:           feature.ID,
			TaskId:       feature.TaskId,
			FeatureType:  feature.FeatureType,
			FeatureKey:   feature.FeatureKey,
			FeatureScore: feature.FeatureScore,
			FeatureDesc:  feature.FeatureDesc,
			Entity:       feature.Entity,
		}
	}
	for _, defaultFeature := range defaultFeatures {
		fk := structs.FeatureItem{FeatureType: defaultFeature.FeatureType, FeatureKey: defaultFeature.FeatureKey}
		if _, ok := composedFeatureMap[fk]; !ok {
			composedFeatureMap[fk] = &objectparser.OracleTaskIncompatibleFeature{
				TaskId:       taskId,
				FeatureType:  defaultFeature.FeatureType,
				FeatureKey:   defaultFeature.FeatureKey,
				FeatureScore: defaultFeature.FeatureScore,
				FeatureDesc:  defaultFeature.FeatureDesc,
				Entity:       defaultFeature.Entity,
			}
		}
	}
	return composedFeatureMap
}
