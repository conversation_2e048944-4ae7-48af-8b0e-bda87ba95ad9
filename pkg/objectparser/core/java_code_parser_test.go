package core

import (
	"strings"
	"testing"
)

func TestJavaCodeParser_ParseMultiFileOutput(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name           string
		input          string
		expectMulti    bool
		expectFiles    int
		expectErrors   int
		expectMainFile string
	}{
		{
			name: "Valid Hibernate/JPA multi-file output",
			input: `=== FILE_START: com/tms/entity/UserEntity.java ===
package com.tms.entity;

import javax.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "user")
public class UserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
}
=== FILE_END ===

=== FILE_START: com/tms/repository/UserRepository.java ===
package com.tms.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.tms.entity.UserEntity;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, Long> {
}
=== FILE_END ===

=== FILE_START: com/tms/service/UserService.java ===
package com.tms.service;

import com.tms.entity.UserEntity;

public interface UserService {
    UserEntity save(UserEntity user);
}
=== FILE_END ===

=== FILE_START: com/tms/service/impl/UserServiceImpl.java ===
package com.tms.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.tms.entity.UserEntity;
import com.tms.repository.UserRepository;
import com.tms.service.UserService;

@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserRepository userRepository;
    
    public UserEntity save(UserEntity user) {
        return userRepository.save(user);
    }
}
=== FILE_END ===`,
			expectMulti:    true,
			expectFiles:    4,
			expectErrors:   0,
			expectMainFile: "UserRepository.java",
		},
		{
			name: "Valid MyBatis-Plus multi-file output",
			input: `=== FILE_START: com/tms/entity/UserEntity.java ===
package com.tms.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

@Data
@TableName("user")
public class UserEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
}
=== FILE_END ===

=== FILE_START: com/tms/mapper/UserMapper.java ===
package com.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tms.entity.UserEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {
}
=== FILE_END ===

=== FILE_START: resources/mapper/UserMapper.xml ===
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tms.mapper.UserMapper">
</mapper>
=== FILE_END ===`,
			expectMulti:    true,
			expectFiles:    3,
			expectErrors:   0,
			expectMainFile: "UserMapper.java",
		},
		{
			name: "Valid Hibernate DAO multi-file output",
			input: `=== FILE_START: com/tms/entity/UserEntity.java ===
package com.tms.entity;

import javax.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "user")
public class UserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
}
=== FILE_END ===

=== FILE_START: com/tms/dao/UserDAO.java ===
package com.tms.dao;

import org.hibernate.SessionFactory;
import org.hibernate.Session;
import org.hibernate.Transaction;
import com.tms.entity.UserEntity;

public class UserDAO {
    private SessionFactory sessionFactory;
    
    public UserDAO(SessionFactory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }
    
    public UserEntity save(UserEntity user) throws Exception {
        Session session = sessionFactory.openSession();
        Transaction transaction = null;
        try {
            transaction = session.beginTransaction();
            session.save(user);
            transaction.commit();
            return user;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw e;
        } finally {
            session.close();
        }
    }
}
=== FILE_END ===

=== FILE_START: com/tms/service/UserService.java ===
package com.tms.service;

import com.tms.entity.UserEntity;

public interface UserService {
    UserEntity save(UserEntity user) throws Exception;
}
=== FILE_END ===

=== FILE_START: com/tms/service/impl/UserServiceImpl.java ===
package com.tms.service.impl;

import com.tms.entity.UserEntity;
import com.tms.dao.UserDAO;
import com.tms.service.UserService;

public class UserServiceImpl implements UserService {
    private UserDAO userDAO;
    
    public UserServiceImpl(UserDAO userDAO) {
        this.userDAO = userDAO;
    }
    
    public UserEntity save(UserEntity user) throws Exception {
        return userDAO.save(user);
    }
}
=== FILE_END ===`,
			expectMulti:    true,
			expectFiles:    4,
			expectErrors:   0,
			expectMainFile: "UserDAO.java",
		},
		{
			name: "Single file output (no markers)",
			input: `package com.tms.dao;

import org.apache.ibatis.annotations.*;

@Mapper
public interface TestMapper {
    @Select("SELECT * FROM test")
    List<Test> selectAll();
}`,
			expectMulti:  false,
			expectFiles:  0,
			expectErrors: 0,
		},
		{
			name: "Invalid multi-file format (missing end marker)",
			input: `=== FILE_START: com/tms/entity/UserEntity.java ===
package com.tms.entity;

public class UserEntity {
    private Long id;
}`,
			expectMulti:  false,
			expectFiles:  0,
			expectErrors: 0,
		},
		{
			name: "Empty file content",
			input: `=== FILE_START: com/tms/entity/UserEntity.java ===

=== FILE_END ===`,
			expectMulti:  true,
			expectFiles:  0,
			expectErrors: 2, // Updated: Now includes fallback failure error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.ParseMultiFileOutput(tt.input)

			if err != nil && tt.expectFiles > 0 {
				t.Errorf("ParseMultiFileOutput() unexpected error = %v", err)
				return
			}

			if result.IsMultiFile != tt.expectMulti {
				t.Errorf("ParseMultiFileOutput() IsMultiFile = %v, want %v", result.IsMultiFile, tt.expectMulti)
			}

			if len(result.Files) != tt.expectFiles {
				t.Errorf("ParseMultiFileOutput() files count = %v, want %v", len(result.Files), tt.expectFiles)
			}

			if len(result.ParseErrors) != tt.expectErrors {
				t.Errorf("ParseMultiFileOutput() errors count = %v, want %v", len(result.ParseErrors), tt.expectErrors)
			}

			if tt.expectMainFile != "" {
				mainFile := parser.GetMainJavaFile(result.Files)
				if mainFile == nil {
					t.Errorf("GetMainJavaFile() returned nil, expected %s", tt.expectMainFile)
				} else if mainFile.FileName != tt.expectMainFile {
					t.Errorf("GetMainJavaFile() = %s, want %s", mainFile.FileName, tt.expectMainFile)
				}
			}
		})
	}
}

func TestJavaCodeParser_DetermineFileType(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		fileName string
		expected string
	}{
		{"UserEntity.java", "java"},
		{"UserMapper.xml", "xml"},
		{"application.properties", "properties"},
		{"config.yml", "yaml"},
		{"config.yaml", "yaml"},
		{"unknown.txt", "java"}, // default
	}

	for _, tt := range tests {
		t.Run(tt.fileName, func(t *testing.T) {
			result := parser.determineFileType(tt.fileName)
			if result != tt.expected {
				t.Errorf("determineFileType(%s) = %s, want %s", tt.fileName, result, tt.expected)
			}
		})
	}
}

func TestJavaCodeParser_ExtractPackagePath(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name     string
		content  string
		filePath string
		expected string
	}{
		{
			name:     "Java file with package declaration",
			content:  "package com.tms.entity;\n\npublic class User {}",
			filePath: "com/tms/entity/User.java",
			expected: "com.tms.entity",
		},
		{
			name:     "XML file (no package)",
			content:  "<?xml version=\"1.0\"?>\n<mapper>",
			filePath: "resources/mapper/UserMapper.xml",
			expected: "",
		},
		{
			name:     "Java file without package declaration",
			content:  "public class User {}",
			filePath: "com/tms/entity/User.java",
			expected: "com.tms.entity",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parser.extractPackagePath(tt.content, tt.filePath)
			if result != tt.expected {
				t.Errorf("extractPackagePath() = %s, want %s", result, tt.expected)
			}
		})
	}
}

func TestJavaCodeParser_ClassPattern(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name        string
		code        string
		expected    string
		shouldMatch bool
	}{
		// 标准情况
		{
			name:        "Standard public class declaration",
			code:        "public class MyClass {",
			expected:    "MyClass",
			shouldMatch: true,
		},
		{
			name:        "Public class with indentation",
			code:        "    public class IndentedClass {",
			expected:    "IndentedClass",
			shouldMatch: true,
		},
		{
			name:        "Public class with tab indentation",
			code:        "\tpublic class TabIndentedClass {",
			expected:    "TabIndentedClass",
			shouldMatch: true,
		},
		{
			name:        "Public class with multiple spaces",
			code:        "public    class    SpacedClass {",
			expected:    "SpacedClass",
			shouldMatch: true,
		},
		{
			name:        "Generic class",
			code:        "public class GenericClass<T> {",
			expected:    "GenericClass",
			shouldMatch: true,
		},
		{
			name:        "Class with underscores and numbers",
			code:        "public class Test_Class_123 {",
			expected:    "Test_Class_123",
			shouldMatch: true,
		},
		// 注释相关测试
		{
			name: "Single line comment with class exists",
			code: `// Assuming this utility class exists
public class ActualClass {`,
			expected:    "ActualClass",
			shouldMatch: true,
		},
		{
			name: "Multi-line comment with public class inside",
			code: `/* This is a comment
   with public class FakeClass inside */
public class RealClass {`,
			expected:    "RealClass",
			shouldMatch: true,
		},
		{
			name:        "Comment after on same line",
			code:        "/* comment */ public class SameLineClass {",
			expected:    "",
			shouldMatch: false, // 因为正则要求行首
		},
		{
			name:        "Single line comment at line start",
			code:        "//public class CommentedClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Multi-line comment at line start",
			code:        "/*public class CommentedClass {*/",
			expected:    "",
			shouldMatch: false,
		},
		// 不应该匹配的情况
		{
			name:        "Class without public",
			code:        "class PrivateClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Protected class",
			code:        "protected class ProtectedClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Abstract public class",
			code:        "abstract public class AbstractClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Public abstract class",
			code:        "public abstract class AbstractClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Public final class",
			code:        "public final class FinalClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Public static class",
			code:        "public static class StaticClass {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Interface declaration",
			code:        "public interface MyInterface {",
			expected:    "",
			shouldMatch: false,
		},
		{
			name:        "Enum declaration",
			code:        "public enum MyEnum {",
			expected:    "",
			shouldMatch: false,
		},
		// 特殊情况
		{
			name: "Class name on new line",
			code: `public class
NewLineClass {`,
			expected:    "",
			shouldMatch: false, // 修改后的正则不应该匹配跨行
		},
		{
			name: "String containing public class",
			code: `String desc = "This is a public class definition";
public class StringTestClass {`,
			expected:    "StringTestClass",
			shouldMatch: true,
		},
		{
			name: "Multiple public classes",
			code: `public class FirstClass {
}
public class SecondClass {`,
			expected:    "FirstClass",
			shouldMatch: true, // 只匹配第一个
		},
		// 实际案例
		{
			name: "BookChange case with comment",
			code: `package com.tms.dao;

import org.hibernate.Session;
import com.tms.util.BookAssertNotnull; // Assuming this utility class exists

public class BookChange {
    public static void change(Session session) {
        // method body
    }
}`,
			expected:    "BookChange",
			shouldMatch: true,
		},
		{
			name: "Only comment with class exists",
			code: `// Assuming this utility class exists
import something;`,
			expected:    "",
			shouldMatch: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			matches := parser.classPattern.FindStringSubmatch(tt.code)
			found := len(matches) > 1
			className := ""
			if found {
				className = matches[1]
			}

			if tt.shouldMatch {
				if !found {
					t.Errorf("Expected to match '%s', but no match found", tt.expected)
				} else if className != tt.expected {
					t.Errorf("Expected '%s', but got '%s'", tt.expected, className)
				}
			} else {
				if found {
					t.Errorf("Should not match, but found '%s'", className)
				}
			}
		})
	}
}

func TestJavaCodeParser_InferFileMetadata_WithClassPattern(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name             string
		content          string
		expectedFileName string
		expectedFileType string
	}{
		{
			name: "BookChange real e",
			content: `package com.tms.dao;

import org.hibernate.Session;
import org.hibernate.Transaction;
import com.tms.util.BookAssertNotnull;

public class BookAddCopy {

    public static void addCopy(Session session, String isbnIn, String barcodeIdIn) throws Exception {
        // "private" method logic for use within this class
        Transaction transaction = null;
        try {
            transaction = session.beginTransaction();

            // Assert not null checks
            BookAssertNotnull.assertNotnull(isbnIn);
            BookAssertNotnull.assertNotnull(barcodeIdIn);

            // Insert into book_copies
            session.createNativeQuery("INSERT INTO book_copies (isbn, barcode_id) VALUES (:isbn, :barcodeId)")
                    .setParameter("isbn", isbnIn)
                    .setParameter("barcodeId", barcodeIdIn)
                    .executeUpdate();

            transaction.commit();
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            // Handle DUP_VAL_ON_INDEX exception equivalent in Hibernate/MySQL
            if (e instanceof org.hibernate.exception.ConstraintViolationException) {
                // Do nothing, equivalent to THEN NULL in PL/SQL
            } else {
                throw e;
            }
        }
    }
}`,
			expectedFileName: "BookAddCopy.java",
			expectedFileType: "java",
		},
		{
			name: "BookChange example",
			content: `package com.tms.dao;

import com.tms.util.BookAssertNotnull; // Assuming this utility class exists

public class BookChange {
    public static void change() {}
}`,
			expectedFileName: "BookChange.java",
			expectedFileType: "java",
		},
		{
			name: "Class with comment containing 'class exists'",
			content: `package com.example;

// This utility class exists for testing
public class TestUtility {
    // implementation
}`,
			expectedFileName: "TestUtility.java",
			expectedFileType: "java",
		},
		{
			name: "No public class found but has interface",
			content: `package com.example;

// Just comments and imports
import java.util.*;

public interface MyInterface {
    // This will be matched
}`,
			expectedFileName: "MyInterface.java",
			expectedFileType: "java",
		},
		{
			name: "Multiple classes, only first public matched",
			content: `package com.example;

class PrivateFirst {}

public class PublicSecond {
    // This should be matched
}

public class PublicThird {
    // This won't be matched (only first)
}`,
			expectedFileName: "PublicSecond.java",
			expectedFileType: "java",
		},
		{
			name: "XML file",
			content: `<?xml version="1.0" encoding="UTF-8"?>
<mapper namespace="com.example.mapper.UserMapper">
    <select id="selectAll">SELECT * FROM users</select>
</mapper>`,
			expectedFileName: "mapper.xml",
			expectedFileType: "xml",
		},
		{
			name: "No recognizable pattern",
			content: `// Just comments without any keywords
// This is a simple comment file`,
			expectedFileName: "",
			expectedFileType: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fileName, fileType := parser.inferFileMetadata(tt.content, "")

			if fileName != tt.expectedFileName {
				t.Errorf("inferFileMetadata() fileName = %s, want %s", fileName, tt.expectedFileName)
			}

			if fileType != tt.expectedFileType {
				t.Errorf("inferFileMetadata() fileType = %s, want %s", fileType, tt.expectedFileType)
			}
		})
	}
}

func TestJavaCodeParser_CombineFilesForBackwardCompatibility(t *testing.T) {
	parser := NewJavaCodeParser()

	files := []JavaFile{
		{
			FilePath: "com/tms/entity/UserEntity.java",
			FileName: "UserEntity.java",
			Content:  "package com.tms.entity;\npublic class UserEntity {}",
		},
		{
			FilePath: "com/tms/mapper/UserMapper.java",
			FileName: "UserMapper.java",
			Content:  "package com.tms.mapper;\npublic interface UserMapper {}",
		},
	}

	result := parser.CombineFilesForBackwardCompatibility(files)

	// Check that result contains both files
	if !strings.Contains(result, "UserEntity") {
		t.Errorf("Combined result should contain UserEntity")
	}
	if !strings.Contains(result, "UserMapper") {
		t.Errorf("Combined result should contain UserMapper")
	}
	if !strings.Contains(result, "// ========== FILE:") {
		t.Errorf("Combined result should contain file separators")
	}
}

func TestJavaCodeParser_GenerateFileName(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name         string
		files        []JavaFile
		objectName   string
		expectedFile string
	}{
		{
			name: "DAO prioritized over repository and others",
			files: []JavaFile{
				{
					FilePath: "com/tms/entity/UserEntity.java",
					FileName: "UserEntity.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/repository/UserRepository.java",
					FileName: "UserRepository.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/dao/UserDAO.java",
					FileName: "UserDAO.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/service/UserService.java",
					FileName: "UserService.java",
					FileType: "java",
				},
			},
			objectName:   "TestObject",
			expectedFile: "UserDAO.java",
		},
		{
			name: "MyBatis mapper prioritized over entity",
			files: []JavaFile{
				{
					FilePath: "com/tms/entity/UserEntity.java",
					FileName: "UserEntity.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/mapper/UserMapper.java",
					FileName: "UserMapper.java",
					FileType: "java",
				},
			},
			objectName:   "TestObject",
			expectedFile: "UserMapper.java",
		},
		{
			name: "Hibernate repository prioritized over mapper",
			files: []JavaFile{
				{
					FilePath: "com/tms/mapper/UserMapper.java",
					FileName: "UserMapper.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/repository/UserRepository.java",
					FileName: "UserRepository.java",
					FileType: "java",
				},
			},
			objectName:   "TestObject",
			expectedFile: "UserRepository.java",
		},
		{
			name: "Repository prioritized over service",
			files: []JavaFile{
				{
					FilePath: "com/tms/service/UserService.java",
					FileName: "UserService.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/repository/UserRepository.java",
					FileName: "UserRepository.java",
					FileType: "java",
				},
			},
			objectName:   "TestObject",
			expectedFile: "UserRepository.java",
		},
		{
			name:         "Empty files uses object name",
			files:        []JavaFile{},
			objectName:   "TestObject",
			expectedFile: "TestObject.java",
		},
		{
			name: "Service over impl",
			files: []JavaFile{
				{
					FilePath: "com/tms/service/impl/UserServiceImpl.java",
					FileName: "UserServiceImpl.java",
					FileType: "java",
				},
				{
					FilePath: "com/tms/service/UserService.java",
					FileName: "UserService.java",
					FileType: "java",
				},
			},
			objectName:   "TestObject",
			expectedFile: "UserService.java",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parser.GenerateFileName(tt.files, tt.objectName)
			if result != tt.expectedFile {
				t.Errorf("GenerateFileName() = %s, want %s", result, tt.expectedFile)
			}
		})
	}
}

func TestJavaCodeParser_IntelligentSplitting_MultiplePackages(t *testing.T) {
	parser := NewJavaCodeParser()

	input := `package com.tms.entity;

import lombok.Data;

@Data
public class UserEntity {
    private Long id;
    private String name;
}

package com.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {
    List<UserEntity> selectByName(String name);
}`

	result, err := parser.ParseMultiFileOutput(input)
	if err != nil {
		t.Fatalf("ParseMultiFileOutput() unexpected error = %v", err)
	}

	if !result.IsMultiFile {
		t.Errorf("ParseMultiFileOutput() IsMultiFile = %v, want true", result.IsMultiFile)
	}

	if result.ParseMode != "intelligent_splitting" {
		t.Errorf("ParseMultiFileOutput() ParseMode = %v, want intelligent_splitting", result.ParseMode)
	}

	if len(result.Files) != 2 {
		t.Errorf("ParseMultiFileOutput() files count = %v, want 2", len(result.Files))
	}

	// Check first file (UserEntity)
	if result.Files[0].FileName != "UserEntity.java" {
		t.Errorf("First file name = %v, want UserEntity.java", result.Files[0].FileName)
	}

	if result.Files[0].PackagePath != "com.tms.entity" {
		t.Errorf("First file package = %v, want com.tms.entity", result.Files[0].PackagePath)
	}

	// Check second file (UserMapper)
	if result.Files[1].FileName != "UserMapper.java" {
		t.Errorf("Second file name = %v, want UserMapper.java", result.Files[1].FileName)
	}

	if result.Files[1].PackagePath != "com.tms.mapper" {
		t.Errorf("Second file package = %v, want com.tms.mapper", result.Files[1].PackagePath)
	}
}

func TestJavaCodeParser_IntelligentSplitting_XMLAndJava(t *testing.T) {
	parser := NewJavaCodeParser()

	input := `package com.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tms.mapper.UserMapper">
    <select id="selectByName" resultType="com.tms.entity.UserEntity">
        SELECT * FROM user WHERE name = #{name}
    </select>
</mapper>`

	result, err := parser.ParseMultiFileOutput(input)
	if err != nil {
		t.Fatalf("ParseMultiFileOutput() unexpected error = %v", err)
	}

	if !result.IsMultiFile {
		t.Errorf("ParseMultiFileOutput() IsMultiFile = %v, want true", result.IsMultiFile)
	}

	if result.ParseMode != "intelligent_splitting" {
		t.Errorf("ParseMultiFileOutput() ParseMode = %v, want intelligent_splitting", result.ParseMode)
	}

	if len(result.Files) < 2 {
		t.Errorf("ParseMultiFileOutput() files count = %v, want at least 2", len(result.Files))
	}

	// Check for Java file
	var javaFile *JavaFile
	var xmlFile *JavaFile

	for i := range result.Files {
		if result.Files[i].FileType == "java" {
			javaFile = &result.Files[i]
		} else if result.Files[i].FileType == "xml" {
			xmlFile = &result.Files[i]
		}
	}

	if javaFile == nil {
		t.Error("Expected Java file not found")
	} else if javaFile.FileName != "UserMapper.java" {
		t.Errorf("Java file name = %v, want UserMapper.java", javaFile.FileName)
	}

	if xmlFile == nil {
		t.Error("Expected XML file not found")
	} else if xmlFile.FileType != "xml" {
		t.Errorf("XML file type = %v, want xml", xmlFile.FileType)
	}
}

func TestJavaCodeParser_IntelligentSplitting_NoDelimiters(t *testing.T) {
	parser := NewJavaCodeParser()

	// Content without standard delimiters but with multiple classes
	input := `package com.tms.entity;
public class UserEntity {
    private Long id;
}

package com.tms.service;
public interface UserService {
    void save(UserEntity user);
}`

	result, err := parser.ParseMultiFileOutput(input)
	if err != nil {
		t.Fatalf("ParseMultiFileOutput() unexpected error = %v", err)
	}

	if !result.IsMultiFile {
		t.Errorf("ParseMultiFileOutput() IsMultiFile = %v, want true", result.IsMultiFile)
	}

	if result.ParseMode != "intelligent_splitting" {
		t.Errorf("ParseMultiFileOutput() ParseMode = %v, want intelligent_splitting", result.ParseMode)
	}

	if result.FallbackReason == "" {
		t.Error("Expected fallback reason to be set")
	}

	if len(result.Files) != 2 {
		t.Errorf("ParseMultiFileOutput() files count = %v, want 2", len(result.Files))
	}
}

func TestJavaCodeParser_FailureDetection(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name           string
		input          string
		expectFailure  bool
		expectedReason string
	}{
		{
			name: "Malformed delimiters with multiple classes",
			input: `### FILE_START: broken.java ###
package com.example;
public class Example {}
public class Another {}
### FILE_END ###`,
			expectFailure:  true,
			expectedReason: "No standard markers but multiple Java classes detected",
		},
		{
			name: "Multiple classes without delimiters",
			input: `package com.tms.entity;
public class User {}

package com.tms.mapper;  
public interface UserMapper {}`,
			expectFailure:  true,
			expectedReason: "No standard markers but multiple Java classes detected",
		},
		{
			name: "Single class with package - should not fail",
			input: `package com.tms.entity;
public class User {
    private String name;
}`,
			expectFailure: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.ParseMultiFileOutput(tt.input)

			if tt.expectFailure {
				if result.ParseMode != "intelligent_splitting" {
					t.Errorf("Expected intelligent splitting to be used, got: %s", result.ParseMode)
				}
				if result.FallbackReason == "" {
					t.Error("Expected fallback reason to be set")
				}
			} else {
				if err != nil && result.ParseMode == "intelligent_splitting" {
					t.Errorf("Unexpected fallback to intelligent splitting")
				}
			}
		})
	}
}

func TestJavaCodeParser_IntelligentSplitting_HibernateCode(t *testing.T) {
	parser := NewJavaCodeParser()

	// Hibernate code without standard delimiters
	input := `package com.tms.entity;

import javax.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "user")
public class UserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
}

package com.tms.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.tms.entity.UserEntity;

@Repository
public interface UserRepository extends JpaRepository<UserEntity, Long> {
    List<UserEntity> findByName(String name);
}`

	result, err := parser.ParseMultiFileOutput(input)
	if err != nil {
		t.Fatalf("ParseMultiFileOutput() unexpected error = %v", err)
	}

	if !result.IsMultiFile {
		t.Errorf("ParseMultiFileOutput() IsMultiFile = %v, want true", result.IsMultiFile)
	}

	if result.ParseMode != "intelligent_splitting" {
		t.Errorf("ParseMultiFileOutput() ParseMode = %v, want intelligent_splitting", result.ParseMode)
	}

	if len(result.Files) != 2 {
		t.Errorf("ParseMultiFileOutput() files count = %v, want 2", len(result.Files))
	}

	// Check first file (UserEntity)
	if len(result.Files) > 0 && result.Files[0].FileName != "UserEntity.java" {
		t.Errorf("First file name = %v, want UserEntity.java", result.Files[0].FileName)
	}

	if len(result.Files) > 0 && result.Files[0].PackagePath != "com.tms.entity" {
		t.Errorf("First file package = %v, want com.tms.entity", result.Files[0].PackagePath)
	}

	// Check second file (UserRepository)
	if len(result.Files) > 1 && result.Files[1].FileName != "UserRepository.java" {
		t.Errorf("Second file name = %v, want UserRepository.java", result.Files[1].FileName)
	}

	if len(result.Files) > 1 && result.Files[1].PackagePath != "com.tms.repository" {
		t.Errorf("Second file package = %v, want com.tms.repository", result.Files[1].PackagePath)
	}
}

func TestJavaCodeParser_LikelyContainsMultipleJavaClasses(t *testing.T) {
	parser := NewJavaCodeParser()

	tests := []struct {
		name     string
		content  string
		expected bool
	}{
		{
			name: "Multiple package declarations",
			content: `package com.tms.entity;
class A {}
package com.tms.mapper;
class B {}`,
			expected: true,
		},
		{
			name: "Multiple classes in same package",
			content: `package com.tms.entity;
public class UserEntity {}
public class OrderEntity {}`,
			expected: true,
		},
		{
			name: "Java + XML combination",
			content: `package com.tms.mapper;
interface UserMapper {}
<?xml version="1.0"?>
<mapper namespace="com.tms.mapper.UserMapper">
</mapper>`,
			expected: true,
		},
		{
			name: "Hibernate repository and entity",
			content: `package com.tms.entity;
@Entity
public class UserEntity {}

package com.tms.repository;
@Repository
public interface UserRepository extends JpaRepository<UserEntity, Long> {}`,
			expected: true,
		},
		{
			name: "Single class",
			content: `package com.tms.entity;
public class UserEntity {
    private Long id;
}`,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parser.likelyContainsMultipleJavaClasses(tt.content)
			if result != tt.expected {
				t.Errorf("likelyContainsMultipleJavaClasses() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestJavaCodeParser_IntelligentSplitting_HibernateDAOCode(t *testing.T) {
	parser := NewJavaCodeParser()

	// Pure Hibernate DAO code without standard delimiters
	input := `package com.tms.entity;

import javax.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "user")
public class UserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
}

package com.tms.dao;

import org.hibernate.SessionFactory;
import org.hibernate.Session;
import org.hibernate.Transaction;
import com.tms.entity.UserEntity;

public class UserDAO {
    private SessionFactory sessionFactory;
    
    public UserDAO(SessionFactory sessionFactory) {
        this.sessionFactory = sessionFactory;
    }
    
    public UserEntity save(UserEntity user) throws Exception {
        Session session = sessionFactory.openSession();
        Transaction transaction = null;
        try {
            transaction = session.beginTransaction();
            session.save(user);
            transaction.commit();
            return user;
        } catch (Exception e) {
            if (transaction != null) {
                transaction.rollback();
            }
            throw e;
        } finally {
            session.close();
        }
    }
}`

	result, err := parser.ParseMultiFileOutput(input)
	if err != nil {
		t.Fatalf("ParseMultiFileOutput() unexpected error = %v", err)
	}

	if !result.IsMultiFile {
		t.Errorf("ParseMultiFileOutput() IsMultiFile = %v, want true", result.IsMultiFile)
	}

	if result.ParseMode != "intelligent_splitting" {
		t.Errorf("ParseMultiFileOutput() ParseMode = %v, want intelligent_splitting", result.ParseMode)
	}

	if len(result.Files) != 2 {
		t.Errorf("ParseMultiFileOutput() files count = %v, want 2", len(result.Files))
	}

	// Check first file (UserEntity)
	if len(result.Files) > 0 && result.Files[0].FileName != "UserEntity.java" {
		t.Errorf("First file name = %v, want UserEntity.java", result.Files[0].FileName)
	}

	if len(result.Files) > 0 && result.Files[0].PackagePath != "com.tms.entity" {
		t.Errorf("First file package = %v, want com.tms.entity", result.Files[0].PackagePath)
	}

	// Check second file (UserDAO)
	if len(result.Files) > 1 && result.Files[1].FileName != "UserDAO.java" {
		t.Errorf("Second file name = %v, want UserDAO.java", result.Files[1].FileName)
	}

	if len(result.Files) > 1 && result.Files[1].PackagePath != "com.tms.dao" {
		t.Errorf("Second file package = %v, want com.tms.dao", result.Files[1].PackagePath)
	}

	// Test main file selection prioritizes DAO
	mainFile := parser.GetMainJavaFile(result.Files)
	if mainFile == nil {
		t.Error("GetMainJavaFile() returned nil")
	} else if mainFile.FileName != "UserDAO.java" {
		t.Errorf("GetMainJavaFile() = %s, want UserDAO.java", mainFile.FileName)
	}
}

func TestJavaCodeParser_DAOFilePriority(t *testing.T) {
	parser := NewJavaCodeParser()

	// Test various combinations to ensure DAO has highest priority
	tests := []struct {
		name         string
		files        []JavaFile
		expectedFile string
	}{
		{
			name: "DAO wins over all other types",
			files: []JavaFile{
				{FilePath: "com/tms/entity/UserEntity.java", FileName: "UserEntity.java", FileType: "java"},
				{FilePath: "com/tms/repository/UserRepository.java", FileName: "UserRepository.java", FileType: "java"},
				{FilePath: "com/tms/mapper/UserMapper.java", FileName: "UserMapper.java", FileType: "java"},
				{FilePath: "com/tms/service/UserService.java", FileName: "UserService.java", FileType: "java"},
				{FilePath: "com/tms/service/impl/UserServiceImpl.java", FileName: "UserServiceImpl.java", FileType: "java"},
				{FilePath: "com/tms/dao/UserDAO.java", FileName: "UserDAO.java", FileType: "java"},
			},
			expectedFile: "UserDAO.java",
		},
		{
			name: "DAO with different naming patterns",
			files: []JavaFile{
				{FilePath: "com/tms/entity/UserEntity.java", FileName: "UserEntity.java", FileType: "java"},
				{FilePath: "com/tms/dataaccess/UserDataAccessDAO.java", FileName: "UserDataAccessDAO.java", FileType: "java"},
				{FilePath: "com/tms/service/UserService.java", FileName: "UserService.java", FileType: "java"},
			},
			expectedFile: "UserDataAccessDAO.java",
		},
		{
			name: "DAO in dao package takes precedence",
			files: []JavaFile{
				{FilePath: "com/tms/dao/impl/UserDAOImpl.java", FileName: "UserDAOImpl.java", FileType: "java"},
				{FilePath: "com/tms/dao/UserDAO.java", FileName: "UserDAO.java", FileType: "java"},
				{FilePath: "com/tms/service/UserService.java", FileName: "UserService.java", FileType: "java"},
			},
			expectedFile: "UserDAO.java", // DAO package pattern takes precedence
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mainFile := parser.GetMainJavaFile(tt.files)
			if mainFile == nil {
				t.Error("GetMainJavaFile() returned nil")
			} else if mainFile.FileName != tt.expectedFile {
				t.Errorf("GetMainJavaFile() = %s, want %s", mainFile.FileName, tt.expectedFile)
			}
		})
	}
}
