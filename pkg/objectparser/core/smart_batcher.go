package core

import (
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/util/log"
)

// SmartBatcher dynamically adjusts batch size based on processing performance
type SmartBatcher struct {
	minBatchSize     int
	maxBatchSize     int
	targetLatency    time.Duration
	currentBatchSize int
	mu               sync.RWMutex

	// Performance statistics
	recentRates      []float64 // Recent processing rates (items/sec)
	maxRecentSamples int
}

// NewSmartBatcher creates a new smart batcher with specified parameters
func NewSmartBatcher(minSize, maxSize int, targetLatency time.Duration) *SmartBatcher {
	if minSize <= 0 {
		minSize = 64
	}
	if maxSize <= minSize {
		maxSize = minSize * 8
	}
	if targetLatency <= 0 {
		targetLatency = 100 * time.Millisecond
	}

	return &SmartBatcher{
		minBatchSize:     minSize,
		maxBatchSize:     maxSize,
		targetLatency:    targetLatency,
		currentBatchSize: minSize,
		maxRecentSamples: 10,
		recentRates:      make([]float64, 0, 10),
	}
}

// GetCurrentBatchSize returns the current batch size
func (b *SmartBatcher) GetCurrentBatchSize() int {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.currentBatchSize
}

// RecordBatchPerformance records the performance of a batch and adjusts batch size accordingly
func (b *SmartBatcher) RecordBatchPerformance(itemCount int, duration time.Duration) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if duration <= 0 || itemCount <= 0 {
		return
	}

	// Calculate processing rate
	itemsPerSecond := float64(itemCount) / duration.Seconds()

	// Update recent rates
	b.recentRates = append(b.recentRates, itemsPerSecond)
	if len(b.recentRates) > b.maxRecentSamples {
		b.recentRates = b.recentRates[1:]
	}

	// Calculate average rate
	avgRate := b.calculateAverageRate()

	// Calculate optimal batch size based on target latency
	optimalBatch := int(avgRate * b.targetLatency.Seconds())

	// Apply boundaries
	if optimalBatch < b.minBatchSize {
		b.currentBatchSize = b.minBatchSize
	} else if optimalBatch > b.maxBatchSize {
		b.currentBatchSize = b.maxBatchSize
	} else {
		// Smooth adjustment to avoid drastic changes
		delta := optimalBatch - b.currentBatchSize
		// Apply 50% of the delta for smooth transition
		b.currentBatchSize += delta / 2

		// Ensure we stay within bounds after adjustment
		if b.currentBatchSize < b.minBatchSize {
			b.currentBatchSize = b.minBatchSize
		} else if b.currentBatchSize > b.maxBatchSize {
			b.currentBatchSize = b.maxBatchSize
		}
	}

	log.Debugf("SmartBatcher adjusted batch size to %d (rate: %.1f items/sec, optimal: %d)",
		b.currentBatchSize, avgRate, optimalBatch)
}

// calculateAverageRate calculates the average processing rate
func (b *SmartBatcher) calculateAverageRate() float64 {
	if len(b.recentRates) == 0 {
		return 100.0 // Default rate
	}

	sum := 0.0
	for _, rate := range b.recentRates {
		sum += rate
	}
	return sum / float64(len(b.recentRates))
}

// GetStats returns current statistics
func (b *SmartBatcher) GetStats() (currentSize int, avgRate float64, samples int) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	currentSize = b.currentBatchSize
	avgRate = b.calculateAverageRate()
	samples = len(b.recentRates)
	return
}

// Reset resets the batcher to initial state
func (b *SmartBatcher) Reset() {
	b.mu.Lock()
	defer b.mu.Unlock()

	b.currentBatchSize = b.minBatchSize
	b.recentRates = make([]float64, 0, b.maxRecentSamples)
}

// AdjustForMemoryPressure reduces batch size when memory pressure is high
func (b *SmartBatcher) AdjustForMemoryPressure(memoryUsagePercent float64) {
	b.mu.Lock()
	defer b.mu.Unlock()

	if memoryUsagePercent > 80 {
		// High memory pressure - reduce batch size by 50%
		b.currentBatchSize = b.currentBatchSize / 2
		if b.currentBatchSize < b.minBatchSize {
			b.currentBatchSize = b.minBatchSize
		}
		log.Warnf("SmartBatcher reduced batch size to %d due to high memory pressure (%.1f%%)",
			b.currentBatchSize, memoryUsagePercent)
	} else if memoryUsagePercent > 60 {
		// Moderate memory pressure - reduce batch size by 25%
		b.currentBatchSize = (b.currentBatchSize * 3) / 4
		if b.currentBatchSize < b.minBatchSize {
			b.currentBatchSize = b.minBatchSize
		}
		log.Infof("SmartBatcher adjusted batch size to %d due to moderate memory pressure (%.1f%%)",
			b.currentBatchSize, memoryUsagePercent)
	}
}
