package core

import (
	"context"
	"encoding/json"
	"path"
	"regexp"
	"sort"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"
	"gitee.com/pingcap_enterprise/tms/server/models"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/parse"
)

type MessageBuilder struct {
	idGenerator *IdGenerator
}

func NewMessageBuilder() MessageBuilder {
	return MessageBuilder{
		idGenerator: NewDefaultIdGenerator(),
	}
}

func (i *MessageBuilder) ResetIdGenerator(prefix string) {
	i.idGenerator = NewIdGenerator(prefix)
}

func (i *MessageBuilder) ConstructEvaluateContext(j string) message.EvaluateContext {
	var arr message.EvaluateContext
	if j == "" {
		return arr
	}
	_ = json.Unmarshal([]byte(j), &arr)
	return arr
}

func (i *MessageBuilder) ConstructMethodInvokeList(j string) []message.MethodInvoke {
	var arr []message.MethodInvoke
	if j == "" {
		return arr
	}
	_ = json.Unmarshal([]byte(j), &arr)
	return arr
}

func (i *MessageBuilder) BuildMethodInvokeListFromVO(arr []dto.MethodInvoke) []message.MethodInvoke {
	var list []message.MethodInvoke
	for _, item := range arr {
		list = append(list, message.MethodInvoke{
			OwnerName:             item.OwnerName,
			FuncName:              item.FuncName,
			Count:                 item.Count,
			IsOwnerInReservedWord: item.IsOwnerInReservedWord,
		})
	}
	return list
}

func (i *MessageBuilder) BuildTableReferenceListFromVO(arr []dto.TableReference) []message.TableReference {
	var list []message.TableReference
	for _, item := range arr {
		list = append(list, message.TableReference{
			StatementType:  item.StatementType,
			StatementName:  item.StatementName,
			TableName:      item.TableName,
			Count:          item.Count,
			IsDatabaseLink: item.DatabaseLink,
		})
	}
	return list
}

func (i *MessageBuilder) BuildReservedWordListFromVO(arr []dto.ReservedWord) []message.ReservedWord {
	var list []message.ReservedWord
	for _, item := range arr {
		list = append(list, message.ReservedWord{
			Value:     item.Value,
			Count:     item.Count,
			Highlight: i.BuildReservedWordListFromVO(item.Highlight),
		})
	}
	return list
}

func (i *MessageBuilder) ConstructIdentifierList(j string) []message.MethodInvoke {
	var arr []message.MethodInvoke
	if j == "" {
		return arr
	}
	_ = json.Unmarshal([]byte(j), &arr)
	return arr
}

func (i *MessageBuilder) ConstructPLSQLSegment(j string) message.PLSQLSegment {
	var arr message.PLSQLSegment
	if j == "" {
		return arr
	}
	_ = json.Unmarshal([]byte(j), &arr)
	return arr
}

func (i *MessageBuilder) BuildAbstractSyntaxTree(tree *dto.AbstractSyntaxTreeNode) *message.AbstractSyntaxTreeNode {
	if tree == nil {
		return nil
	}

	children := make([]*message.AbstractSyntaxTreeNode, 0)
	for _, child := range tree.GetChildren() {
		children = append(children, i.BuildAbstractSyntaxTree(child))
	}

	return &message.AbstractSyntaxTreeNode{
		StmtType:  tree.GetStmtType(),
		StmtName:  tree.GetStmtName(),
		StmtValue: tree.GetStmtValue(),
		Key:       i.idGenerator.GetNextId(),
		Children:  children,
	}
}

func (i *MessageBuilder) BuildDetailMessage(detail *objectparser.OracleObjectDefinitionAnalyzeDetail) *message.OracleObjectDefinitionAnalyzeDetail {
	if detail == nil {
		return nil
	}

	rwList := i.ConstructReservedWordList(detail.ReservedWordList)
	trList := i.ConstructTableReferenceList(detail.TableReferenceList)
	rsMap := make(map[string]bool)
	for _, rwItem := range rwList {
		rsMap[rwItem.Value] = true
	}

	datum := &message.OracleObjectDefinitionAnalyzeDetail{
		ID:                              detail.ID,
		ChannelId:                       detail.ChannelId,
		TaskId:                          detail.TaskId,
		SchemaObjectKey:                 detail.SchemaObjectKey,
		SchemaName:                      detail.SchemaName,
		ObjectType:                      detail.ObjectType,
		ObjectName:                      detail.ObjectName,
		ObjectStatus:                    detail.ObjectStatus,
		Status:                          detail.Status,
		ErrorDetail:                     detail.ErrorDetail,
		ReservedWordCount:               detail.ReservedWordCount,
		DatabaseLinkCount:               detail.DatabaseLinkCount,
		IncompatibleFeatureScoreContext: i.ConstructEvaluateContext(detail.IncompatibleFeatureScoreContext),
		MethodInvokeList:                i.ConstructMethodInvokeList(detail.MethodInvokeList),
		IdentifierList:                  i.ConstructIdentifierList(detail.IdentifierList),
		PLSQLSegment:                    i.ConstructPLSQLSegment(detail.PLSQLSegment),
		TableReferenceList:              trList,
		ReservedWordList:                rwList,
	}
	return datum
}

func (i *MessageBuilder) ConstructTableReferenceList(j string) []message.TableReference {
	var arr []message.TableReference
	if j == "" {
		return arr
	}
	_ = json.Unmarshal([]byte(j), &arr)
	return arr
}

func (i *MessageBuilder) ConstructReservedWordList(j string) []message.ReservedWord {
	var arr []message.ReservedWord
	if j == "" {
		return arr
	}
	_ = json.Unmarshal([]byte(j), &arr)
	return arr
}

// ExtractTableNamesFromJSON extracts unique table names from TableReferenceList JSON
func (i *MessageBuilder) ExtractTableNamesFromJSON(tableReferenceListJSON string) []string {
	tableReferences := i.ConstructTableReferenceList(tableReferenceListJSON)
	return i.ExtractTableNames(tableReferences)
}

// ExtractTablesFromTriggerAnalysis extracts table schema.name pairs from TRIGGER's AnalyzeDetail
func (i *MessageBuilder) ExtractTablesFromTriggerAnalysis(
	triggerAnalyzeDetail *objectparser.OracleObjectDefinitionAnalyzeDetail,
) []structs.SchemaTablePair {
	var tables []structs.SchemaTablePair
	
	if triggerAnalyzeDetail == nil || triggerAnalyzeDetail.TableReferenceList == "" || triggerAnalyzeDetail.TableReferenceList == "{}" {
		return tables
	}

	// Extract table names from JSON
	tableNames := i.ExtractTableNamesFromJSON(triggerAnalyzeDetail.TableReferenceList)
	
	// Convert to SchemaTablePair using the trigger's schema as default
	for _, tableName := range tableNames {
		tables = append(tables, structs.SchemaTablePair{
			SchemaName: triggerAnalyzeDetail.SchemaName,
			TableName:  tableName,
		})
	}
	
	return tables
}

// ExtractTableNames extracts unique table names from TableReference list
func (i *MessageBuilder) ExtractTableNames(tableReferences []message.TableReference) []string {
	tableMap := make(map[string]bool)
	for _, ref := range tableReferences {
		if ref.TableName != "" && !ref.IsDatabaseLink {
			// Normalize table name by removing schema prefix if present
			tableName := ref.TableName
			if idx := strings.LastIndex(tableName, "."); idx >= 0 {
				tableName = tableName[idx+1:]
			}
			// Convert to uppercase for consistency
			tableMap[strings.ToUpper(tableName)] = true
		}
	}

	// Convert map to sorted slice
	var tables []string
	for table := range tableMap {
		tables = append(tables, table)
	}
	sort.Strings(tables)

	return tables
}

func (i *MessageBuilder) BuildScoring(total WeightedScoring) message.WeightedScoring {
	return message.WeightedScoring{
		FullScoring:     parse.TruncateFloat(total.FullScoring, 2),
		Scoring:         parse.TruncateFloat(total.Scoring, 2),
		Weight:          parse.TruncateFloat(total.Weight, 2),
		WeightedScoring: parse.TruncateFloat(total.WeightedScoring, 2),
	}
}

func (i *MessageBuilder) BuildJavaCodes(results []*objectparser.OracleToJavaResult) []message.OracleToJavaResult {
	var list []message.OracleToJavaResult
	for _, item := range results {
		list = append(list, message.OracleToJavaResult{
			ID:                item.ID,
			ChannelId:         item.ChannelId,
			TaskId:            item.TaskId,
			ObjectUUID:        item.UUID,
			SchemaName:        item.SchemaName,
			PackageName:       item.PackageName,
			FilePackage:       item.PackageName,
			ObjectName:        item.ObjectName,
			ObjectType:        item.ObjectType,
			Depth:             item.Depth,
			FileName:          item.CodeFileName,
			FileContent:       item.ConvertedCode,
			ConvertDuration:   item.ConvertDuration,
			ConvertStatus:     item.ConvertStatus,
			ConvertTime:       item.ConvertTime,
			ConvertErrMessage: item.ConvertErrMessage,
			ConvertSQL:        item.ConvertSQL,
			ConvertPrompts:    strings.Split(item.ConvertPrompts, "@,@"),
		})
	}
	return list
}

func (i *MessageBuilder) BuildHistoryJavaCodes(results []*objectparser.OracleToJavaHistoryResult) []message.OracleToJavaResult {
	var list []message.OracleToJavaResult
	for _, item := range results {
		list = append(list, message.OracleToJavaResult{
			ID:                item.ID,
			ChannelId:         item.ChannelId,
			TaskId:            item.TaskId,
			ObjectUUID:        item.UUID,
			SchemaName:        item.SchemaName,
			PackageName:       item.PackageName,
			FilePackage:       item.PackageName,
			ObjectName:        item.ObjectName,
			ObjectType:        item.ObjectType,
			Depth:             item.Depth,
			FileName:          item.CodeFileName,
			FileContent:       item.ConvertedCode,
			ConvertDuration:   item.ConvertDuration,
			ConvertStatus:     item.ConvertStatus,
			ConvertTime:       item.ConvertTime,
			ConvertErrMessage: item.ConvertErrMessage,
			ConvertSQL:        item.ConvertSQL,
			ConvertPrompts:    strings.Split(item.ConvertPrompts, "@,@"),
		})
	}
	return list
}

func (i *MessageBuilder) BuildLogs(results []*objectparser.OracleToJavaLog) []message.OracleToJavaLog {
	var list []message.OracleToJavaLog
	for _, item := range results {
		list = append(list, message.OracleToJavaLog{
			ID:         item.ID,
			ChannelId:  item.ChannelId,
			TaskId:     item.TaskId,
			LogMessage: item.LogMessage,
			LogLevel:   item.LogLevel,
			CreatedAt:  item.CreatedAt,
		})
	}
	return list

}

func (i *MessageBuilder) buildSegment(s dto.Segment) message.Segment {
	return message.Segment{
		Name:     s.Name,
		StartPos: s.StartPos,
		EndPos:   s.EndPos,
		SQL:      s.SQL,
	}
}

func (i *MessageBuilder) buildSegments(ss []dto.Segment) []message.Segment {
	segments := make([]message.Segment, 0, len(ss))
	for _, s := range ss {
		segments = append(segments, i.buildSegment(s))
	}
	return segments
}

func (i *MessageBuilder) buildPositions(ss []dto.Position) []message.Position {
	segments := make([]message.Position, 0, len(ss))
	for _, s := range ss {
		segments = append(segments, message.Position{
			BP:        s.BP,
			StartPos:  s.StartPos,
			SP:        s.SP,
			NP:        s.NP,
			CH:        s.CH,
			Token:     s.Token,
			StringVal: s.StringVal,
		})
	}
	return segments
}

func (i *MessageBuilder) BuildPLSQLSegmentFromVO(segment dto.PLSQLSegment) message.PLSQLSegment {
	return message.PLSQLSegment{
		SegmentPrefix: i.buildSegment(segment.SegmentPrefix),
		SegmentSuffix: i.buildSegment(segment.SegmentSuffix),
		Declares:      i.buildSegments(segment.Declares),
		Functions:     i.buildSegments(segment.Functions),
		Procedures:    i.buildSegments(segment.Procedures),
		LeftStartPos:  segment.LeftStartPos,
		RightStartPos: segment.RightStartPos,
		AllPositions:  i.buildPositions(segment.AllPositions),
	}
}

// BuildJavaCodeTree builds a hierarchical tree structure from conversion results
func (i *MessageBuilder) BuildJavaCodeTree(ctx context.Context, results []*objectparser.OracleToJavaResult) ([]*message.JavaCodeTreeNode, error) {
	if len(results) == 0 {
		return []*message.JavaCodeTreeNode{}, nil
	}

	// Create a map to store tree nodes by their key (package path)
	nodeMap := make(map[string]*message.JavaCodeTreeNode)

	// Process each result
	for _, result := range results {
		var fileInfos []FileInfo

		if result.IsMultiFile {
			// Handle multi-file results
			files, err := models.GetObjectParserWriter().ListOracleToJavaFilesByTaskIdAndResultId(ctx, result.TaskId, result.ID)
			if err != nil {
				continue // Skip this result if we can't get files
			}

			for _, file := range files {
				fileInfos = append(fileInfos, FileInfo{
					FilePath:       file.FilePath,
					FileName:       file.FileName,
					PackagePath:    file.PackagePath,
					FileContent:    file.FileContent,
					ObjectUUID:     result.UUID,
					ConvertPrompts: strings.Split(result.ConvertPrompts, "@,@"),
				})
			}
		} else {
			// Handle single-file results
			packageName := i.extractPackageFromJavaCode(result.ConvertedCode)
			packagePath := i.packageToPath(packageName)
			fileName := i.generateJavaFileName(result.ObjectName)
			filePath := path.Join(packagePath, fileName)

			fileInfos = append(fileInfos, FileInfo{
				FilePath:       filePath,
				FileName:       fileName,
				PackagePath:    packageName,
				FileContent:    result.ConvertedCode,
				ObjectUUID:     result.UUID,
				ConvertPrompts: strings.Split(result.ConvertPrompts, "@,@"),
			})
		}

		// Build tree nodes for each file
		for _, fileInfo := range fileInfos {
			i.buildTreeNodesForFile(nodeMap, fileInfo)
		}
	}

	// Convert map to tree structure
	return i.buildTreeFromNodeMap(nodeMap), nil
}

// FileInfo represents information about a single file
type FileInfo struct {
	FilePath       string
	FileName       string
	PackagePath    string
	FileContent    string
	ObjectUUID     string
	ConvertPrompts []string
}

// buildTreeNodesForFile creates tree nodes for a single file
func (i *MessageBuilder) buildTreeNodesForFile(nodeMap map[string]*message.JavaCodeTreeNode, fileInfo FileInfo) {
	// Check if it's a resource file (no package or starts with resources/)
	if fileInfo.PackagePath == "" || strings.HasPrefix(fileInfo.FilePath, "resources/") {
		// Handle as directory path instead of package path
		dirPath := path.Dir(fileInfo.FilePath)
		if dirPath != "." && dirPath != "/" && dirPath != "" {
			pathParts := strings.Split(dirPath, "/")
			currentPath := ""

			for idx, part := range pathParts {
				if part == "" {
					continue
				}

				if idx == 0 {
					currentPath = part
				} else {
					currentPath = currentPath + "/" + part
				}

				// Create directory node if it doesn't exist
				if _, exists := nodeMap[currentPath]; !exists {
					nodeMap[currentPath] = &message.JavaCodeTreeNode{
						Key:            currentPath,
						Title:          part,
						FileName:       "",
						FilePackage:    "",
						FileContent:    "",
						ObjectUUID:     "",
						ConvertPrompts: []string{},
						Children:       []*message.JavaCodeTreeNode{},
						IsLeaf:         false,
					}
				}
			}
		}
	} else {
		// Handle Java packages
		packageParts := strings.Split(fileInfo.PackagePath, ".")
		if len(packageParts) > 0 && packageParts[0] != "" {
			// Build directory nodes for each package level
			currentPath := ""
			for i, part := range packageParts {
				if i == 0 {
					currentPath = part
				} else {
					currentPath = currentPath + "." + part
				}

				// Create directory node if it doesn't exist
				if _, exists := nodeMap[currentPath]; !exists {
					nodeMap[currentPath] = &message.JavaCodeTreeNode{
						Key:            currentPath,
						Title:          part,
						FileName:       "",
						FilePackage:    currentPath,
						FileContent:    "",
						ObjectUUID:     "",
						ConvertPrompts: []string{},
						Children:       []*message.JavaCodeTreeNode{},
						IsLeaf:         false,
					}
				}
			}
		}
	}

	// Create file node
	i.createFileNode(nodeMap, fileInfo.FilePath, fileInfo)
}

// createFileNode creates a file node in the tree
func (i *MessageBuilder) createFileNode(nodeMap map[string]*message.JavaCodeTreeNode, filePath string, fileInfo FileInfo) {
	nodeMap[filePath] = &message.JavaCodeTreeNode{
		Key:            filePath,
		Title:          fileInfo.FileName,
		FileName:       fileInfo.FileName,
		FilePackage:    fileInfo.PackagePath,
		FileContent:    fileInfo.FileContent,
		ObjectUUID:     fileInfo.ObjectUUID,
		ConvertPrompts: fileInfo.ConvertPrompts,
		Children:       []*message.JavaCodeTreeNode{},
		IsLeaf:         true,
	}
}

// buildTreeFromNodeMap converts the flat node map to a hierarchical tree
func (i *MessageBuilder) buildTreeFromNodeMap(nodeMap map[string]*message.JavaCodeTreeNode) []*message.JavaCodeTreeNode {
	roots := make(map[string]*message.JavaCodeTreeNode)

	// Build parent-child relationships
	for key, node := range nodeMap {
		if node.IsLeaf {
			// Find parent for file nodes
			parentKey := i.findParentKey(key, nodeMap)
			if parentKey != "" {
				parent := nodeMap[parentKey]
				parent.Children = append(parent.Children, node)
			} else {
				// File without package - add to root
				roots[key] = node
			}
		} else {
			// Directory node - find its parent
			parentKey := i.findParentPackageKey(key)
			if parentKey != "" && nodeMap[parentKey] != nil {
				parent := nodeMap[parentKey]
				parent.Children = append(parent.Children, node)
			} else {
				// Root level package
				roots[key] = node
			}
		}
	}

	// Convert roots map to slice
	var result []*message.JavaCodeTreeNode
	for _, root := range roots {
		result = append(result, root)
	}

	// Sort root nodes by title
	sort.Slice(result, func(i, j int) bool {
		return result[i].Title < result[j].Title
	})

	// Sort all children recursively
	i.sortTreeNodes(result)

	return result
}

// findParentKey finds the parent package key for a file path
func (i *MessageBuilder) findParentKey(filePath string, nodeMap map[string]*message.JavaCodeTreeNode) string {
	// Extract directory from file path
	dir := path.Dir(filePath)
	if dir == "." || dir == "/" || dir == "" {
		return ""
	}

	// First, check if the parent directory exists (for resource files)
	if _, exists := nodeMap[dir]; exists {
		return dir
	}

	// If not found as directory, try as package format (for Java files)
	packageKey := strings.ReplaceAll(dir, "/", ".")
	if _, exists := nodeMap[packageKey]; exists {
		return packageKey
	}

	return ""
}

// findParentPackageKey finds the parent package key for a package
func (i *MessageBuilder) findParentPackageKey(packageKey string) string {
	// Check if it's a directory-style key (contains /)
	if strings.Contains(packageKey, "/") {
		dir := path.Dir(packageKey)
		if dir == "." || dir == "/" || dir == "" {
			return ""
		}
		return dir
	}

	// Handle package-style key (contains .)
	parts := strings.Split(packageKey, ".")
	if len(parts) <= 1 {
		return ""
	}

	// Return parent package
	return strings.Join(parts[:len(parts)-1], ".")
}

// extractPackageFromJavaCode extracts package declaration from Java code
func (i *MessageBuilder) extractPackageFromJavaCode(javaCode string) string {
	// Use regex to match: package com.tms.dao;
	re := regexp.MustCompile(`package\s+([\w.]+)\s*;`)
	matches := re.FindStringSubmatch(javaCode)
	if len(matches) > 1 {
		return matches[1] // Return com.tms.dao
	}
	return "com.tms.dao" // Default package name
}

// packageToPath converts package name to directory path
func (i *MessageBuilder) packageToPath(packageName string) string {
	// com.tms.dao → com/tms/dao
	return strings.ReplaceAll(packageName, ".", "/")
}

// generateJavaFileName converts object name to proper Java class name
func (i *MessageBuilder) generateJavaFileName(objectName string) string {
	// Convert SNAKE_CASE to PascalCase and add .java extension
	parts := strings.Split(strings.ToLower(objectName), "_")
	var result strings.Builder

	for _, part := range parts {
		if len(part) > 0 {
			result.WriteString(strings.ToUpper(string(part[0])))
			if len(part) > 1 {
				result.WriteString(part[1:])
			}
		}
	}

	return result.String() + ".java"
}

// sortTreeNodes recursively sorts all children of the given nodes
func (i *MessageBuilder) sortTreeNodes(nodes []*message.JavaCodeTreeNode) {
	for _, node := range nodes {
		if len(node.Children) > 0 {
			// Sort children by title (directories first, then files)
			sort.Slice(node.Children, func(i, j int) bool {
				child1 := node.Children[i]
				child2 := node.Children[j]

				// Directories come before files
				if !child1.IsLeaf && child2.IsLeaf {
					return true
				}
				if child1.IsLeaf && !child2.IsLeaf {
					return false
				}

				// Within same type, sort by title
				return child1.Title < child2.Title
			})

			// Recursively sort children
			i.sortTreeNodes(node.Children)
		}
	}
}

// BuildJavaCodeTreeForHistory builds a hierarchical tree structure from history conversion results
func (i *MessageBuilder) BuildJavaCodeTreeForHistory(ctx context.Context, results []*objectparser.OracleToJavaHistoryResult) ([]*message.JavaCodeTreeNode, error) {
	if len(results) == 0 {
		return []*message.JavaCodeTreeNode{}, nil
	}

	// Create a map to store tree nodes by their key (package path)
	nodeMap := make(map[string]*message.JavaCodeTreeNode)

	// Process each result
	for _, result := range results {
		var fileInfos []FileInfo

		if result.IsMultiFile {
			// Handle multi-file results
			files, err := models.GetObjectParserWriter().ListOracleToJavaHistoryFilesByTaskIdAndResultId(ctx, result.TaskId, result.ID)
			if err != nil {
				continue // Skip this result if we can't get files
			}

			for _, file := range files {
				fileInfos = append(fileInfos, FileInfo{
					FilePath:       file.FilePath,
					FileName:       file.FileName,
					PackagePath:    file.PackagePath,
					FileContent:    file.FileContent,
					ObjectUUID:     result.UUID,
					ConvertPrompts: strings.Split(result.ConvertPrompts, "@,@"),
				})
			}
		} else {
			// Handle single-file results
			packageName := i.extractPackageFromJavaCode(result.ConvertedCode)
			packagePath := i.packageToPath(packageName)
			fileName := i.generateJavaFileName(result.ObjectName)
			filePath := path.Join(packagePath, fileName)

			fileInfos = append(fileInfos, FileInfo{
				FilePath:       filePath,
				FileName:       fileName,
				PackagePath:    packageName,
				FileContent:    result.ConvertedCode,
				ObjectUUID:     result.UUID,
				ConvertPrompts: strings.Split(result.ConvertPrompts, "@,@"),
			})
		}

		// Build tree nodes for each file
		for _, fileInfo := range fileInfos {
			i.buildTreeNodesForFile(nodeMap, fileInfo)
		}
	}

	// Convert map to tree structure
	return i.buildTreeFromNodeMap(nodeMap), nil
}
