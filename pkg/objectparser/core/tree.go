package core

import (
	"strings"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"
)

// FindNodes finds all nodes matching the specified stmtType, stmtName, and stmtValue
func FindNodes(root *dto.AbstractSyntaxTreeNode, stmtType, stmtName, stmtValue string) []*dto.AbstractSyntaxTreeNode {
	var result []*dto.AbstractSyntaxTreeNode

	// Helper function to check if a node matches
	matches := func(node *dto.AbstractSyntaxTreeNode) bool {
		if node == nil {
			return false
		}
		return (stmtType == "" || strings.EqualFold(node.StmtType, stmtType)) &&
			(stmtName == "" || strings.EqualFold(node.StmtName, stmtName)) &&
			(stmtValue == "" || strings.EqualFold(node.StmtValue, stmtValue))
	}

	// Recursive function to traverse the tree
	var traverse func(node *dto.AbstractSyntaxTreeNode)
	traverse = func(node *dto.AbstractSyntaxTreeNode) {
		if node == nil {
			return
		}
		if matches(node) {
			// Add the node to the result, including its children
			result = append(result, &dto.AbstractSyntaxTreeNode{
				StmtType:  node.StmtType,
				StmtName:  node.StmtName,
				StmtValue: node.StmtValue,
				Key:       node.Key,
				Children:  node.Children,
			})
		}
		// Traverse the children
		for _, child := range node.Children {
			traverse(child)
		}
	}

	traverse(root)
	return result
}
