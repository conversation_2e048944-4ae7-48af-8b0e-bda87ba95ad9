package core

import (
	"encoding/json"
	"strings"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

type OracleObjectScorer struct {
	channelId, taskId int

	databaseLinkRule  *objectparser.OracleTaskIncompatibleFeature
	reservedWordRules map[string]*objectparser.OracleTaskIncompatibleFeature
	param             *structs.ObjectParserParam
}

func (i *OracleObjectScorer) GetChannelId() int {
	return i.channelId
}

func (i *OracleObjectScorer) GetTaskId() int {
	return i.taskId
}

func (i *OracleObjectScorer) getReservedWordScore(featureKey string, occurCount int) float64 {
	var val int
	rule, ok := i.reservedWordRules[featureKey]
	if ok {
		val = rule.FeatureScore * occurCount
	}
	if val <= 0 {
		return 0
	}
	return float64(val)
}

func (i *OracleObjectScorer) getDatabaseLinkScore(occurCount int) float64 {
	var val int
	if i.databaseLinkRule != nil {
		val = i.databaseLinkRule.FeatureScore * occurCount
	}
	if val <= 0 {
		return 0
	}
	return float64(val)
}

func (i *OracleObjectScorer) isFeatureMatch(featureType, featureKey string) bool {
	if featureType == constants.IncompatibleFeatureTypeReservedWord {
		_, ok := i.reservedWordRules[featureKey]
		return ok
	}
	return false
}

type ScoringResult struct {
	Total               WeightedScoring `json:"total"`
	IncompatibleFeature WeightedScoring `json:"incompatibleFeature"`
	Histogram           WeightedScoring `json:"histogram"`
}

type WeightedScoring struct {
	FullScoring     float64 `json:"fullScoring"`
	Scoring         float64 `json:"scoring"`
	Weight          float64 `json:"weight,omitempty"`
	WeightedScoring float64 `json:"weightedScoring,omitempty"`
}

func calculateAverage(numbers []float64) float64 {
	if len(numbers) == 0 {
		return 0 // 防止除以0
	}

	var sum float64
	for _, num := range numbers {
		sum += num
	}
	return sum / float64(len(numbers))
}

func (i *OracleObjectScorer) EvaluateTotalScoring(details []*objectparser.OracleObjectDefinitionAnalyzeDetail, options HistogramOptions) ScoringResult {

	builder := NewMessageBuilder()

	detailScoringList := make([]float64, 0, len(details))
	for _, detail := range details {
		var databaseLinkScoring = 0.00
		var reservedWordScoring = 0.00

		if detail.ObjectStatus != "VALID" {
			continue
		}
		tableReferenceList := builder.ConstructTableReferenceList(detail.TableReferenceList)
		reservedWordList := builder.ConstructReservedWordList(detail.ReservedWordList)

		for _, tableReference := range tableReferenceList {
			if !tableReference.IsDatabaseLink {
				continue
			}
			databaseLinkScoring += i.getDatabaseLinkScore(tableReference.Count)
		}
		for _, reservedWord := range reservedWordList {
			reservedWordScoring += i.getReservedWordScore(reservedWord.Value, reservedWord.Count)
		}

		detailScoringList = append(detailScoringList, 100.0-databaseLinkScoring-reservedWordScoring)
	}

	var histogramScoring float64
	if options.TransactionPerSecond {
		histogramScoring += options.ScoreFactor
	}
	if options.LogVolumePerSecond {
		histogramScoring += options.ScoreFactor
	}
	if options.TransactionLogVolume {
		histogramScoring += options.ScoreFactor
	}
	if options.TransactionDataBlocks {
		histogramScoring += options.ScoreFactor
	}
	if options.ArchiveData {
		histogramScoring += options.ScoreFactor
	}
	if options.ArchiveTimes {
		histogramScoring += options.ScoreFactor
	}

	incompatibleFeaturePercentage := options.GetIncompatibleFeaturePercentage()

	detailAverageScoring := calculateAverage(detailScoringList)

	return ScoringResult{
		Total: WeightedScoring{
			FullScoring: 100.00,
			Scoring:     detailAverageScoring*incompatibleFeaturePercentage + histogramScoring,
		},
		IncompatibleFeature: WeightedScoring{
			FullScoring:     100.00,
			Scoring:         detailAverageScoring,
			Weight:          incompatibleFeaturePercentage,
			WeightedScoring: detailAverageScoring * incompatibleFeaturePercentage,
		},
		Histogram: WeightedScoring{
			FullScoring: histogramScoring, // TODO 这里的满分是多少？
			Scoring:     histogramScoring, // TODO 这里的满分是多少？
		},
	}
}

func (i *OracleObjectScorer) GetIncompatibleFeatureCount(rws []dto.ReservedWord, trs []dto.TableReference) (uint, uint, string) {

	var reservedWordCount uint
	var databaseLinkCount uint
	evaluateContext := make(EvaluateContext, 0)

	for _, tableReference := range trs {
		if !tableReference.IsDatabaseLink() {
			continue
		}
		databaseLinkCount = 1
		evaluateContext = evaluateContext.AppendFeature(constants.IncompatibleFeatureTypeDatabaseLink, "DATABASE LINK")
		break
	}

	for _, reservedWord := range rws {
		if !i.isFeatureMatch(constants.IncompatibleFeatureTypeReservedWord, reservedWord.GetValue()) {
			log.Errorf("reserved word not match rule, skip it, channel:%d, task:%d, word:%s", i.GetChannelId(), i.GetTaskId(), reservedWord.GetValue())
			continue
		}
		evaluateContext = evaluateContext.AppendFeature(constants.IncompatibleFeatureTypeReservedWord, reservedWord.GetValue())
		reservedWordCount += 1
	}

	return reservedWordCount, databaseLinkCount, evaluateContext.String()
}

type EvaluateContextItem struct {
	FeatureType string `json:"featureType"`
	FeatureKey  string `json:"featureKey"`
	Score       int    `json:"score,omitempty"`
}

type EvaluateContext []EvaluateContextItem

func (i EvaluateContext) AppendFeatureScore(featureType, featureKey string, score int) EvaluateContext {
	return append(i, EvaluateContextItem{
		FeatureType: featureType,
		FeatureKey:  featureKey,
		Score:       score,
	})
}

func (i EvaluateContext) AppendFeature(featureType, featureKey string) EvaluateContext {
	return append(i, EvaluateContextItem{
		FeatureType: featureType,
		FeatureKey:  featureKey,
	})
}

func (i EvaluateContext) String() string {
	bs, _ := json.Marshal(i)
	return string(bs)
}

func (i *OracleObjectScorer) BuildIncompatibleFeatures(detail *objectparser.OracleObjectDefinitionAnalyzeDetail, rsp dto.AnalyzeSQLResponse) []*objectparser.OracleObjectDefinitionIncompatibleFeature {
	features := make([]*objectparser.OracleObjectDefinitionIncompatibleFeature, 0)

	for _, tableReference := range rsp.GetTableReferenceList() {
		if !tableReference.IsDatabaseLink() {
			continue
		}
		features = append(features, &objectparser.OracleObjectDefinitionIncompatibleFeature{
			ChannelId:  i.GetChannelId(),
			TaskId:     i.GetTaskId(),
			DetailId:   detail.ID,
			SchemaName: detail.SchemaName,
			ObjectName: detail.ObjectName,
			ObjectType: detail.ObjectType,

			IncompatibleType:  constants.IncompatibleFeatureTypeDatabaseLink,
			IncompatibleKey:   tableReference.GetTableName(),
			IncompatibleValue: tableReference.GetTableName(),
			IncompatibleDesc:  "", // TODO 将来这里需要增加提供给前端的描述信息吗？
			OccurCount:        tableReference.GetCount(),
		})
	}

	for _, reservedWord := range rsp.ReservedWordList {
		for _, highlight := range reservedWord.Highlight {
			features = append(features, &objectparser.OracleObjectDefinitionIncompatibleFeature{
				ChannelId:  i.GetChannelId(),
				TaskId:     i.GetTaskId(),
				DetailId:   detail.ID,
				SchemaName: detail.SchemaName,
				ObjectName: detail.ObjectName,
				ObjectType: detail.ObjectType,

				IncompatibleType:  constants.IncompatibleFeatureTypeReservedWord,
				IncompatibleKey:   reservedWord.GetValue(),
				IncompatibleValue: highlight.GetValue(),
				IncompatibleDesc:  "", // TODO 将来这里需要增加提供给前端的描述信息吗？
				OccurCount:        highlight.GetCount(),
			})
		}
	}

	return features
}

func InitOracleObjectScorer(channelId, taskId int,
	defaultFeatures []*objectparser.OracleIncompatibleFeature,
	incompatibleFeatures []*objectparser.OracleTaskIncompatibleFeature,
	param *structs.ObjectParserParam) *OracleObjectScorer {
	var databaseLinkRule *objectparser.OracleTaskIncompatibleFeature
	reservedWordRules := make(map[string]*objectparser.OracleTaskIncompatibleFeature)

	composedFeatures := ComposeFeatures(incompatibleFeatures, defaultFeatures, taskId)

	for _, rule := range lo.Values(composedFeatures) {
		rule.FeatureKey = strings.TrimSpace(strings.ToUpper(rule.FeatureKey)) // 强制转换为大写，并且去除前后空格，以便于后续的比较
		if rule.FeatureType == constants.IncompatibleFeatureTypeDatabaseLink {
			databaseLinkRule = rule
		}
		if rule.FeatureType == constants.IncompatibleFeatureTypeReservedWord {
			reservedWordRules[rule.FeatureKey] = rule
		}
	}
	log.Debugf("InitOracleObjectScorer, reservedWordRules:%v", lo.Keys(reservedWordRules))
	return &OracleObjectScorer{
		channelId:         channelId,
		taskId:            taskId,
		databaseLinkRule:  databaseLinkRule,
		reservedWordRules: reservedWordRules,
		param:             param,
	}
}

type ObjectKeyHelper struct{}

func NewObjectKeyHelper() *ObjectKeyHelper {
	return &ObjectKeyHelper{}
}

func (i *ObjectKeyHelper) GenerateSchemaObjectKey(schemaName, objectType, objectName string) string {
	return schemaName + "." + objectType + "." + objectName
}

type HistogramOptions struct {
	ScoreFactor           float64 `json:"scoreFactor"`
	ArchiveTimes          bool    `json:"archiveTimes"`
	ArchiveData           bool    `json:"archiveData"`
	TransactionDataBlocks bool    `json:"transactionDataBlocks"`
	TransactionLogVolume  bool    `json:"transactionLogVolume"`
	LogVolumePerSecond    bool    `json:"logVolumePerSecond"`
	TransactionPerSecond  bool    `json:"transactionPerSecond"`
}

func (i HistogramOptions) GetHistogramScorePercentage() float64 {
	var percentage float64
	if i.ArchiveTimes {
		percentage += i.ScoreFactor
	}
	if i.ArchiveData {
		percentage += i.ScoreFactor
	}
	if i.TransactionDataBlocks {
		percentage += i.ScoreFactor
	}
	if i.TransactionLogVolume {
		percentage += i.ScoreFactor
	}
	if i.LogVolumePerSecond {
		percentage += i.ScoreFactor
	}
	if i.TransactionPerSecond {
		percentage += i.ScoreFactor
	}
	return percentage / 100.0
}

func (i HistogramOptions) GetIncompatibleFeaturePercentage() float64 {
	return 1.00 - i.GetHistogramScorePercentage()
}
