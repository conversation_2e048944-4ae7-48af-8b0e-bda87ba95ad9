package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// OracleMetadataBatch represents a batch of Oracle objects
type OracleMetadataBatch struct {
	Objects    []datasource.OracleObjectDefinition
	Statuses   []datasource.OracleObject
	BatchIndex int
	IsLast     bool
}

// fetchOracleMetadataStreaming fetches Oracle metadata in streaming mode with pagination
func (i *OracleObjectParserTask) fetchOracleMetadataStreaming(ctx context.Context) (<-chan *OracleMetadataBatch, <-chan error) {
	batchChan := make(chan *OracleMetadataBatch, 2) // Buffer 2 batches
	errChan := make(chan error, 1)

	go func() {
		defer close(batchChan)
		defer close(errChan)

		dbConn := i.dbConns.GetSourceDB().OracleDB
		targetSchemas := i.targetSchemas
		channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID

		// Batch size for pagination
		batchSize := i.objectParserParam.AnalyzeBatchSize
		if batchSize <= 0 {
			batchSize = 1000
		}

		// For now, we'll fetch all objects and then simulate streaming
		// In a real implementation, you would add pagination support to the datasource interface
		log.Infof("Starting streaming fetch with batch size %d", batchSize)
		i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepFetchingObjectData,
			fmt.Sprintf("starting streaming data fetch (batch size: %d)", batchSize)))

		// Fetch all object definitions
		allObjects, err := models.GetDatasourceReaderWriter().GetOracleObjectDefinitionsBySchemas(
			ctx, dbConn, targetSchemas, constants.ValidObjectTypes)
		if err != nil {
			log.Errorf("Failed to fetch object definitions, channelId:%d, taskId:%d, err:%v", channelId, taskId, err)
			errChan <- err
			return
		}

		// Fetch all object statuses
		allStatuses, err := models.GetDatasourceReaderWriter().GetOracleObjectStatusBySchemas(
			ctx, dbConn, targetSchemas, constants.ValidObjectTypes)
		if err != nil {
			log.Warnf("Failed to fetch object statuses, continuing without status info: %v", err)
			allStatuses = []datasource.OracleObject{}
		}

		// Create status map for quick lookup
		statusMap := make(map[string]datasource.OracleObject)
		for _, status := range allStatuses {
			statusMap[status.Key()] = status
		}

		totalCount := len(allObjects)
		log.Infof("Total objects to process: %d", totalCount)

		// Simulate streaming by sending objects in batches
		batchIndex := 0
		for offset := 0; offset < totalCount; offset += batchSize {
			select {
			case <-ctx.Done():
				errChan <- ctx.Err()
				return
			default:
			}

			// Calculate batch boundaries
			end := offset + batchSize
			if end > totalCount {
				end = totalCount
			}

			// Extract batch of objects
			batchObjects := allObjects[offset:end]

			// Extract corresponding statuses
			batchStatuses := make([]datasource.OracleObject, 0, len(batchObjects))
			for _, obj := range batchObjects {
				if status, ok := statusMap[obj.Key()]; ok {
					batchStatuses = append(batchStatuses, status)
				}
			}

			// Send batch through channel
			batch := &OracleMetadataBatch{
				Objects:    batchObjects,
				Statuses:   batchStatuses,
				BatchIndex: batchIndex,
				IsLast:     end >= totalCount,
			}

			select {
			case batchChan <- batch:
				log.Debugf("Sent batch %d with %d objects", batchIndex, len(batchObjects))
				i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepSavingObjectData,
					fmt.Sprintf("object fetched batch %d/%d (objects: %d)", batchIndex+1, (totalCount+batchSize-1)/batchSize, len(batchObjects))))
			case <-ctx.Done():
				errChan <- ctx.Err()
				return
			}

			batchIndex++
		}

		log.Infof("Completed streaming fetch, total batches: %d", batchIndex)
		i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.OPLogType, constants.OPStepSavingObjectData,
			fmt.Sprintf("streaming data fetch completed (total batches: %d)", batchIndex)))
	}()

	return batchChan, errChan
}

// buildMetadataStreaming builds metadata in streaming mode, processing batches as they arrive
func (i *OracleObjectParserTask) buildMetadataStreaming(ctx context.Context,
	batchChan <-chan *OracleMetadataBatch,
	dbName, dbCharset string) (*MetadataBuildResult, error) {

	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID
	objectParserAdaptor := i.objectParserAdaptor
	targetSchemas := i.targetSchemas

	log.Infof("Starting streaming metadata build, channelId:%d, taskId:%d", channelId, taskId)

	// Aggregate results
	var allDefinitionPos []*objectparser.OracleObjectDefinition
	var allOracleDependencies []*objectparser.OracleDependency
	var allDefinitionAnalyzeSummaries []*objectparser.OracleObjectDefinitionAnalyzeSummary
	var allDefinitionAnalyzeDetails []*objectparser.OracleObjectDefinitionAnalyzeDetail

	// Create metadata helper with empty initial data (will be populated incrementally)
	metadataHelper := NewMetadataHelper(channelId, taskId, objectParserAdaptor, targetSchemas, []datasource.OracleObject{}, i, i.objectFilterCfgs)

	// Summary map to accumulate across batches
	summaryMap := make(map[string]*objectparser.OracleObjectDefinitionAnalyzeSummary)

	// Process each batch as it arrives
	batchCount := 0
	for batch := range batchChan {
		batchCount++
		log.Infof("Processing batch %d with %d objects", batch.BatchIndex, len(batch.Objects))

		// Update metadata helper with new object statuses
		for _, status := range batch.Statuses {
			metadataHelper.AddObjectStatus(status)
		}

		// Build metadata for this batch
		batchResult, err := i.processBatchMetadata(ctx, batch.Objects, dbName, dbCharset, metadataHelper, summaryMap)
		if err != nil {
			log.Errorf("Failed to process batch %d: %v", batch.BatchIndex, err)
			return nil, err
		}

		// Aggregate results
		allDefinitionPos = append(allDefinitionPos, batchResult.definitionPos...)
		allOracleDependencies = append(allOracleDependencies, batchResult.oracleDependencies...)
		allDefinitionAnalyzeDetails = append(allDefinitionAnalyzeDetails, batchResult.definitionAnalyzeDetails...)

		// Save batch results immediately to reduce memory usage
		if err := i.saveBatchMetadata(ctx, batchResult); err != nil {
			log.Errorf("Failed to save batch %d metadata: %v", batch.BatchIndex, err)
			return nil, err
		}

		// Clear batch data from memory after saving
		batchResult = nil
	}

	// Convert summary map to slice
	for _, summary := range summaryMap {
		allDefinitionAnalyzeSummaries = append(allDefinitionAnalyzeSummaries, summary)
	}

	log.Infof("Completed streaming metadata build, processed %d batches", batchCount)

	return &MetadataBuildResult{
		definitionAnalyzeSummaries: allDefinitionAnalyzeSummaries,
		definitionAnalyzeDetails:   allDefinitionAnalyzeDetails,
		definitionPos:              allDefinitionPos,
		oracleDependencies:         allOracleDependencies,
		metadataHelper:             metadataHelper,
	}, nil
}

// processBatchMetadata processes metadata for a single batch
func (i *OracleObjectParserTask) processBatchMetadata(ctx context.Context, objects []datasource.OracleObjectDefinition, dbName, dbCharset string, metadataHelper *MetadataHelper, summaryMap map[string]*objectparser.OracleObjectDefinitionAnalyzeSummary) (*MetadataBuildResult, error) {
	// Get database dependencies
	dbDependencies, err := models.GetDatasourceReaderWriter().GetOracleDependencies(ctx, i.dbConns.GetSourceDB().OracleDB)
	if err != nil {
		log.Errorf("Failed to get oracle dependencies from database: %v", err)
		return nil, err
	}

	// Build package body dependencies for this batch
	packageBodyDependencies, err := metadataHelper.BuildPackageBodyDependencies(ctx, objects)
	if err != nil {
		log.Errorf("Failed to build package body dependencies for batch: %v", err)
		return nil, err
	}

	// Merge both dependency sources - no need to deduplicate as they won't overlap
	allDependencies := make([]*datasource.Dependency, 0, len(packageBodyDependencies)+len(dbDependencies))
	allDependencies = append(allDependencies, packageBodyDependencies...)
	allDependencies = append(allDependencies, dbDependencies...)

	// Build oracle dependencies
	oracleDependencies := metadataHelper.BuildOracleDependency(allDependencies)

	// Filter dependencies based on objectFilterCfgs to keep only the dependency chain
	if len(i.objectFilterCfgs) > 0 {
		log.Infof("Filtering dependencies based on objectFilterCfgs, filter count: %d", len(i.objectFilterCfgs))

		// Build dependency tree to identify all objects in the dependency chain
		depHelper := NewOracleDependencyHelper(oracleDependencies, i.targetSchemas)
		depHelper.SetOracleObjectCfgFilter(i.objectFilterCfgs)

		// Build the complete dependency tree
		dependencyNodeMap := depHelper.BuildTreeNodeChildrenPointers()
		depHelper.FilterTreeNodeWithTargetSchema(dependencyNodeMap)
		depHelper.FilterTreeNodeType(dependencyNodeMap)
		depHelper.MakeTreeNodeChildrenByPointers(dependencyNodeMap)

		// Apply filter to prune nodes not in the filter list
		depHelper.PruneTreeNodeByFilter(dependencyNodeMap)

		// Collect all nodes that remain in the filtered dependency tree
		// This includes both the filtered objects and their dependencies
		filteredNodeKeys := make(map[string]bool)
		var collectNodes func(node *structs.DependencyTreeNode)
		collectNodes = func(node *structs.DependencyTreeNode) {
			if node == nil {
				return
			}
			nodeKey := node.SchemaName + "." + node.Type + "." + node.Name
			filteredNodeKeys[nodeKey] = true
			for _, child := range node.Children {
				collectNodes(child)
			}
		}

		// Collect all nodes from the filtered tree
		for _, node := range dependencyNodeMap {
			collectNodes(node)
		}

		// Filter oracleDependencies to keep only those in the dependency chain
		filteredDependencies := make([]*objectparser.OracleDependency, 0)
		for _, dep := range oracleDependencies {
			depKey := dep.SchemaName + "." + dep.Type + "." + dep.Name
			if filteredNodeKeys[depKey] {
				filteredDependencies = append(filteredDependencies, dep)
			}
		}

		log.Infof("Dependency filtering complete: original=%d, filtered=%d", len(oracleDependencies), len(filteredDependencies))
		oracleDependencies = filteredDependencies
	}

	// Build analysis details and positions
	definitionAnalyzeDetails := metadataHelper.BuildOracleObjectDefinitionAnalyzeDetails(objects)
	definitionAnalyzeDetails = metadataHelper.FilterOracleObjectDefinitionAnalyzeDetails(definitionAnalyzeDetails)

	definitionPos, err := metadataHelper.BuildOracleObjectDefinitionPos(objects, dbCharset)
	if err != nil {
		log.Errorf("Failed to build oracle source definitions for batch: %v", err)
		return nil, err
	}
	definitionPos = metadataHelper.FilterOracleObjectDefinitionPos(definitionPos)

	// Update summaries (accumulate across batches)
	for _, obj := range objects {
		if _, exists := summaryMap[obj.OwnerName]; !exists {
			summaryMap[obj.OwnerName] = &objectparser.OracleObjectDefinitionAnalyzeSummary{
				TaskId:     i.taskInfo.TaskID,
				SchemaName: obj.OwnerName,
				DBName:     dbName,
				TotalNum:   0,
				SuccessNum: 0,
				FailedNum:  0,
				Status:     constants.StatStatusRunning.String(),
			}
		}
		objKey := obj.OwnerName + "." + obj.ObjectType + "." + obj.ObjectName
		if len(i.objectFilterCfgs) == 0 || i.objectFilterMap[objKey] {
			summaryMap[obj.OwnerName].TotalNum++
		}
	}

	return &MetadataBuildResult{
		definitionPos:              definitionPos,
		oracleDependencies:         oracleDependencies,
		definitionAnalyzeDetails:   definitionAnalyzeDetails,
		definitionAnalyzeSummaries: nil, // Will be aggregated at the end
	}, nil
}

// saveBatchMetadata saves metadata for a single batch
func (i *OracleObjectParserTask) saveBatchMetadata(ctx context.Context, batchResult *MetadataBuildResult) error {
	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID

	batcher := NewSmartBatcher(32, 256, 50*time.Millisecond)

	// Calculate total items to save
	totalItems := len(batchResult.definitionPos) + len(batchResult.oracleDependencies) + len(batchResult.definitionAnalyzeDetails)

	// Create database progress tracker for time-based logging only
	dbProgressTracker := NewDatabaseProgressTracker(i, constants.OPLogType,
		constants.OPStepSavingMetaData, totalItems)

	// Count each object type for detailed logging
	var procedureCount, functionCount, packageCount, triggerCount int
	for _, def := range batchResult.definitionPos {
		switch def.ObjectType {
		case constants.OracleObjectTypeProcedure:
			procedureCount++
		case constants.OracleObjectTypeFunction:
			functionCount++
		case constants.OracleObjectTypePackage, constants.OracleObjectTypePackageBody:
			packageCount++
		case constants.OracleObjectTypeTrigger:
			triggerCount++
		}
	}

	dbProgressTracker.ForceLog(ctx, fmt.Sprintf("saving batch metadata: %d definitions(procedure-%d,function-%d,package-%d,trigger-%d), %d dependencies, %d details",
		len(batchResult.definitionPos), procedureCount, functionCount, packageCount, triggerCount, len(batchResult.oracleDependencies), len(batchResult.definitionAnalyzeDetails)))

	// Keep track of cumulative progress
	totalSavedCount := 0

	// Save definitions
	if len(batchResult.definitionPos) > 0 {
		savedCount, err := i.saveObjectDefinitionsWithDatabaseProgress(ctx, batchResult.definitionPos, batcher, dbProgressTracker, totalSavedCount)
		if err != nil {
			log.Errorf("Failed to save oracle source codes batch, channelId:%d, taskId:%d, err:%v", channelId, taskId, err)
			return err
		}
		totalSavedCount += savedCount
	}

	// Save dependencies
	if len(batchResult.oracleDependencies) > 0 {
		savedCount, err := i.saveOracleDependenciesWithDatabaseProgress(ctx, batchResult.oracleDependencies, batcher, dbProgressTracker, totalSavedCount)
		if err != nil {
			log.Errorf("Failed to save oracle dependencies batch, channelId:%d, taskId:%d, err:%v", channelId, taskId, err)
			return err
		}
		totalSavedCount += savedCount
	}

	// Save analysis details
	if len(batchResult.definitionAnalyzeDetails) > 0 {
		savedCount, err := i.saveAnalyzeDetailsWithDatabaseProgress(ctx, batchResult.definitionAnalyzeDetails, batcher, dbProgressTracker, totalSavedCount)
		if err != nil {
			log.Errorf("Failed to save oracle source analyze detail batch, channelId:%d, taskId:%d, err:%v", channelId, taskId, err)
			return err
		}
		totalSavedCount += savedCount
	}

	// Force a final log to show completion
	dbProgressTracker.ForceLog(ctx, fmt.Sprintf("completed saving batch metadata: total %d items saved", totalSavedCount))

	return nil
}

// DatabaseProgressTracker tracks progress specifically for database operations with time-based logging only
type DatabaseProgressTracker struct {
	progressLogger ProgressLogger
	logType        string
	logStep        string
	totalItems     int
	processedItems int
	successCount   int
	failedCount    int
	startTime      time.Time
	lastLogTime    time.Time
	mu             sync.Mutex
}

// NewDatabaseProgressTracker creates a new database progress tracker
func NewDatabaseProgressTracker(logger ProgressLogger, logType, logStep string, totalItems int) *DatabaseProgressTracker {
	now := time.Now()
	return &DatabaseProgressTracker{
		progressLogger: logger,
		logType:        logType,
		logStep:        logStep,
		totalItems:     totalItems,
		processedItems: 0,
		successCount:   0,
		failedCount:    0,
		startTime:      now,
		lastLogTime:    now,
	}
}

// UpdateProgressWithCounts updates the progress with success/failure counts and logs if 10 seconds have passed
func (dpt *DatabaseProgressTracker) UpdateProgressWithCounts(ctx context.Context, processed, success, failed int, objectType string) {
	dpt.mu.Lock()
	defer dpt.mu.Unlock()

	dpt.processedItems = processed
	dpt.successCount = success
	dpt.failedCount = failed

	// Only log if 10 seconds have passed
	if time.Since(dpt.lastLogTime) >= 10*time.Second {
		dpt.logProgress(ctx, objectType)
		dpt.lastLogTime = time.Now()
	}
}

// UpdateProgress updates the progress and logs if 10 seconds have passed (backward compatibility)
func (dpt *DatabaseProgressTracker) UpdateProgress(ctx context.Context, processed int, objectType string) {
	dpt.UpdateProgressWithCounts(ctx, processed, processed, 0, objectType)
}

// ForceLog forces a progress log
func (dpt *DatabaseProgressTracker) ForceLog(ctx context.Context, message string) {
	if dpt.progressLogger != nil {
		dpt.progressLogger.AppendProgressLog(ctx, constants.BuildProgressLog(
			dpt.logType, dpt.logStep, message))
	}
}

// logProgress logs the current progress in unified format
func (dpt *DatabaseProgressTracker) logProgress(ctx context.Context, objectType string) {
	elapsed := time.Since(dpt.startTime)
	rate := float64(dpt.processedItems) / elapsed.Seconds()
	percentage := float64(dpt.processedItems) * 100 / float64(dpt.totalItems)

	// Map object types to descriptive messages
	var description string
	switch objectType {
	case "body":
		description = "building package body dependencies"
	case "ast":
		description = "parsing and analyzing ast"
	case "definitions":
		description = "saving definitions"
	case "dependencies":
		description = "saving dependencies"
	case "analyze details":
		description = "saving analyze details"
	default:
		description = objectType
	}

	message := fmt.Sprintf("%s: progress %d/%d (%.1f%%), success: %d, failed: %d, rate: %.1f items/sec",
		description, dpt.processedItems, dpt.totalItems, percentage, dpt.successCount, dpt.failedCount, rate)

	dpt.progressLogger.AppendProgressLog(ctx, constants.BuildProgressLog(
		dpt.logType, dpt.logStep, message))
}

// ProcessStreaming is the main entry point for streaming processing
func (i *OracleObjectParserTask) ProcessStreaming(ctx context.Context) {
	channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID
	dbCharset, dbName := i.dbConns.GetSourceDS().Charset, i.dbConns.GetSourceDS().DbName

	batcher := NewSmartBatcher(32, 256, 50*time.Millisecond)

	// Ensure task status is properly set on any exit path
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("ProcessStreaming panic recovered, channelId:%d, taskId:%d, panic:%v", channelId, taskId, r)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, fmt.Errorf("process panic: %v", r))
		}
	}()

	// Start streaming fetch
	batchChan, errChan := i.fetchOracleMetadataStreaming(ctx)

	// Check for immediate errors
	select {
	case err := <-errChan:
		if err != nil {
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, err)
			return
		}
	default:
	}

	// Build metadata in streaming mode
	buildResult, err := i.buildMetadataStreaming(ctx, batchChan, dbName, dbCharset)
	if err != nil {
		SaveTaskProcessErrorAndLog(ctx, channelId, taskId, err)
		return
	}

	// Check for any errors from the fetch goroutine
	select {
	case err := <-errChan:
		if err != nil {
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, err)
			return
		}
	default:
	}

	// Save aggregated summaries
	if len(buildResult.definitionAnalyzeSummaries) > 0 {
		_, err = i.saveAnalyzeSummariesWithSmartBatching(ctx, buildResult.definitionAnalyzeSummaries, batcher)
		if err != nil {
			log.Errorf("Failed to save oracle source analyze summary, channelId:%d, taskId:%d, err:%v", channelId, taskId, err)
			SaveTaskProcessErrorAndLog(ctx, channelId, taskId, err)
			return
		}
	}

	// Continue with analysis phase (can also be made streaming if needed)
	analysisContext := i.setupAnalysisContext(buildResult.definitionAnalyzeDetails, buildResult.definitionAnalyzeSummaries)

	// Use concurrent or serial processing based on configuration
	var processErr error
	if i.objectParserParam.AnalyzeWorkerPoolSize > 0 {
		log.Infof("Using concurrent object analysis with %d workers", i.objectParserParam.AnalyzeWorkerPoolSize)
		processErr = i.processObjectAnalysisConcurrent(ctx, buildResult.definitionPos, buildResult.metadataHelper, analysisContext)
	} else {
		log.Infof("Using serial object analysis")
		processErr = i.processObjectAnalysis(ctx, buildResult.definitionPos, buildResult.metadataHelper, analysisContext)
	}

	if processErr != nil {
		log.Errorf("processObjectAnalysis failed, channelId:%d, taskId:%d, err:%v", channelId, taskId, processErr)
		SaveTaskProcessErrorAndLog(ctx, channelId, taskId, processErr)
		return
	}

	// Finalize task
	finalizeErr := i.finalizeTask(ctx, analysisContext)
	if finalizeErr != nil {
		log.Errorf("finalizeTask failed, channelId:%d, taskId:%d, err:%v", channelId, taskId, finalizeErr)
		SaveTaskProcessErrorAndLog(ctx, channelId, taskId, finalizeErr)
		return
	}
}
