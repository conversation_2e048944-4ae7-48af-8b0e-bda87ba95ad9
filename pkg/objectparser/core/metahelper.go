package core

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"sync"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/adaptor"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// ProgressLogger interface for progress logging
type ProgressLogger interface {
	AppendProgressLog(ctx context.Context, detail string)
}

type MetadataHelper struct {
	channelId        int
	taskId           int
	adaptor          adaptor.Adaptor
	targetSchemas    []string
	targetSchemaMap  map[string]bool
	objectKeyHelper  *ObjectKeyHelper
	objectStatusMap  map[string]datasource.OracleObject
	progressLogger   ProgressLogger
	objectFilterCfgs []*objectparser.ObjectParserCfg
	filterMapOnce    sync.Once
	objectFilterMap  map[string]bool
}

func NewMetadataHelper(channelId, taskId int, objectParserAdaptor adaptor.Adaptor, targetSchemas []string, objectStatus []datasource.OracleObject, logger ProgressLogger, objectFilterCfgs []*objectparser.ObjectParserCfg) *MetadataHelper {
	targetSchemaMap := make(map[string]bool)
	for _, schema := range targetSchemas {
		targetSchemaMap[schema] = true
	}
	osMap := make(map[string]datasource.OracleObject)
	for _, oo := range objectStatus {
		osMap[oo.Key()] = oo
	}

	return &MetadataHelper{
		channelId:        channelId,
		taskId:           taskId,
		adaptor:          objectParserAdaptor,
		targetSchemas:    targetSchemas,
		targetSchemaMap:  targetSchemaMap,
		objectKeyHelper:  NewObjectKeyHelper(),
		objectStatusMap:  osMap,
		progressLogger:   logger,
		objectFilterCfgs: objectFilterCfgs,
	}
}

func (i *MetadataHelper) GetObjectStatus(objectKey string) string {
	return i.objectStatusMap[objectKey].Status
}

// AddObjectStatus adds a new object status to the helper's map
func (i *MetadataHelper) AddObjectStatus(status datasource.OracleObject) {
	i.objectStatusMap[status.Key()] = status
}

// BuildPackageBodyDependencies builds dependencies for package bodies in the provided Oracle object definitions. if errors occur during the process, it logs the error and continues with the next package body.
func (i *MetadataHelper) BuildPackageBodyDependencies(ctx context.Context, allOracleObjectDefinitions []datasource.OracleObjectDefinition) ([]*datasource.Dependency, error) {
	log.Infof("building metadata, start BuildPackageBodyDependencies, channelId:%d, taskId:%d", i.channelId, i.taskId)

	// Count total package bodies to process
	packageBodies := make([]datasource.OracleObjectDefinition, 0)
	for _, body := range allOracleObjectDefinitions {
		if body.GetObjectType() == constants.OracleObjectTypePackageBody {
			packageBodies = append(packageBodies, body)
		}
	}
	totalPackageBodies := len(packageBodies)
	if totalPackageBodies <= 0 {
		return nil, nil
	}

	log.Infof("building metadata, total package bodies to process: %d, channelId:%d, taskId:%d", totalPackageBodies, i.channelId, i.taskId)

	dss := make([]*datasource.Dependency, 0)

	dbProgressTracker := NewDatabaseProgressTracker(i.progressLogger, constants.OPLogType, constants.OPStepParsePackageBody, len(packageBodies))

	// If we have a progress logger, use progress tracker
	progressTracker := NewProgressTracker(i.progressLogger, constants.OPLogType,
		constants.OPStepParsePackageBody, totalPackageBodies)
	dbProgressTracker.ForceLog(ctx, fmt.Sprintf("building package body dependencies, total: %d", totalPackageBodies))

	var successCount, failCount int
	for idx, body := range packageBodies {
		itemDesc := fmt.Sprintf("%s.%s", body.GetOwnerName(), body.GetObjectName())

		packageBodyDependencyRsp, packageDependencyErr := i.adaptor.GetPackageBodyDependency(ctx, dto.GetPackageBodyDependencyRequest{
			IsEncoded:   true,
			PLSQL:       stringutil.EncodeSQL(body.GetAllText()),
			OwnerName:   body.GetOwnerName(),
			PackageName: body.GetObjectName(),
		})

		if packageDependencyErr != nil {
			log.Errorf("BuildPackageBodyDependencies, GetPackageBodyDependency failed for %s, channelId:%d, taskId:%d, err: %v",
				itemDesc, i.channelId, i.taskId, packageDependencyErr)
			progressTracker.Update(ctx, idx+1, false, itemDesc)
			failCount++
			dbProgressTracker.UpdateProgressWithCounts(ctx, idx+1, successCount, failCount, "body")
			continue
		}
		successCount++
		dbProgressTracker.UpdateProgressWithCounts(ctx, idx+1, successCount, failCount, "body")

		ds := lo.Map(packageBodyDependencyRsp.Dependencies, i.buildDependency(body))
		dss = append(dss, ds...)
		progressTracker.Update(ctx, idx+1, true, itemDesc)
	}

	dbProgressTracker.ForceLog(ctx, fmt.Sprintf("completed building package body dependencies, found %d dependencies", len(dss)))

	log.Infof("building metadata, finish BuildPackageBodyDependencies, channelId:%d, taskId:%d, packageBodyDependencyNum:%d", i.channelId, i.taskId, len(dss))
	return dss, nil
}

func (i *MetadataHelper) buildDependency(body datasource.OracleObjectDefinition) func(item dto.Dependency, _ int) *datasource.Dependency {
	return func(item dto.Dependency, _ int) *datasource.Dependency {
		dd := &datasource.Dependency{
			SchemaName:  body.GetOwnerName(),
			PackageName: item.GetPackageName(),

			Owner: item.GetOwner(),
			Name:  item.GetName(),
			Type:  item.GetType(),

			ReferencedPackageName: item.GetReferencedPackageName(),
			ReferencedOwner:       item.GetReferencedOwner(),
			ReferencedName:        item.GetReferencedName(),
			ReferencedType:        item.GetReferencedType(),

			IsFromPackageBody: true,
		}
		return dd
	}
}

func (i *MetadataHelper) isSchemaMatch(schemaName string) bool {
	if len(i.targetSchemas) == 0 {
		return true
	}
	return i.targetSchemaMap[schemaName]
}

func (i *MetadataHelper) isObjectTypeMatch(objectType string) bool {
	// "PACKAGE BODY", "PROCEDURE", "FUNCTION", "TRIGGER", "TYPE"
	return objectType == "PACKAGE BODY" || objectType == "PROCEDURE" || objectType == "FUNCTION" || objectType == "TRIGGER" || objectType == "TYPE"
}

func (i *MetadataHelper) BuildOracleDependency(dependencies []*datasource.Dependency) []*objectparser.OracleDependency {

	walkedObject := make(map[string]bool)
	oracleDependencies := make([]*objectparser.OracleDependency, 0, len(dependencies))
	for _, dependency := range dependencies {
		if !i.isSchemaMatch(dependency.GetSchemaName()) {
			continue
		}
		if !i.isObjectTypeMatch(dependency.GetType()) {
			continue
		}

		od := &objectparser.OracleDependency{
			ChannelId:             i.channelId,
			TaskId:                i.taskId,
			UUID:                  uuid.NewString(),
			SchemaName:            dependency.GetSchemaName(),
			PackageName:           dependency.GetPackageName(),
			OwnerName:             dependency.GetOwner(),
			Name:                  dependency.GetName(),
			Type:                  dependency.GetType(),
			Status:                i.objectStatusMap[dependency.UniqueKey()].Status,
			ReferencedPackageName: dependency.GetReferencedPackageName(),
			ReferencedOwner:       dependency.GetReferencedOwner(),
			ReferencedName:        dependency.GetReferencedName(),
			ReferencedType:        dependency.GetReferencedType(),
			ReferencedLinkName:    dependency.GetReferencedName(),
			DependencyType:        dependency.GetDependencyType(),
			IsFromPackageBody:     dependency.GetIsFromPackageBody(),
		}
		walkedObject[dependency.UniqueKey()] = true
		oracleDependencies = append(oracleDependencies, od)
	}

	invalidDependencies := make([]*objectparser.OracleDependency, 0)
	for _, oo := range i.objectStatusMap {
		if oo.IsValid() {
			continue
		}
		if walkedObject[oo.Key()] {
			continue
		}
		od := &objectparser.OracleDependency{
			ChannelId:             i.channelId,
			TaskId:                i.taskId,
			UUID:                  uuid.NewString(),
			SchemaName:            oo.OwnerName,
			OwnerName:             oo.OwnerName,
			Name:                  oo.ObjectName,
			Type:                  oo.ObjectType,
			Status:                oo.Status,
			ReferencedPackageName: "TMS_FLAG",
			ReferencedOwner:       "TMS_FLAG",
			ReferencedName:        "TMS_FLAG",
			ReferencedType:        "TMS_FLAG",
		}
		log.Infof("building metadata, found invalid and no dependency object, channelId:%d, taskId:%d, schemaName:%s, objectType:%s, objectName:%s, status:%s", i.channelId, i.taskId, od.SchemaName, od.Type, od.Name, od.Status)
		invalidDependencies = append(invalidDependencies, od)
	}
	oracleDependencies = append(oracleDependencies, invalidDependencies...)

	log.Infof("building metadata, finish BuildOracleDependency, channelId:%d, taskId:%d, dependencyNum:%d, invalidDependencyNum:%d", i.channelId, i.taskId, len(oracleDependencies), len(invalidDependencies))
	return oracleDependencies
}

// GBKToUTF8 将 GBK 编码的字符串转换为 UTF-8 编码
func GBKToUTF8(gbkStr string) (string, error) {
	// 将输入字符串转换为字节流
	gbkBytes := []byte(gbkStr)

	// 使用 GBK 解码器将 GBK 转换为 UTF-8
	reader := transform.NewReader(bytes.NewReader(gbkBytes), simplifiedchinese.GBK.NewDecoder())

	// 读取转换后的 UTF-8 字节
	utf8Bytes, err := io.ReadAll(reader)
	if err != nil {
		return "", fmt.Errorf("failed to convert GBK to UTF-8: %v", err)
	}

	// 将字节转换为字符串
	return string(utf8Bytes), nil
}

// TextToUTF8 将指定字符集的文本转换为 UTF-8 编码
func TextToUTF8(text string, sourceCharset string) (string, error) {
	uniformCharset := common.MigrateOracleCharsetStringConvertMapping[sourceCharset]
	if sourceCharset == common.CharsetUTF8MB4 || uniformCharset == common.CharsetUTF8MB4 {
		return text, nil
	}
	if uniformCharset == "" {
		return GBKToUTF8(text)
	}
	convertUtf8Raw, err := common.CharsetConvert([]byte(text), uniformCharset, common.CharsetUTF8MB4)
	if err != nil {
		return "", fmt.Errorf("charset convert failed, err: %v", err)
	}
	return string(convertUtf8Raw), nil
}

func (i *MetadataHelper) BuildOracleObjectDefinitionPos(allOracleObjectDefinitions []datasource.OracleObjectDefinition, dbCharset string) ([]*objectparser.OracleObjectDefinition, error) {
	oracleObjectDefinitionPos := make([]*objectparser.OracleObjectDefinition, 0, len(allOracleObjectDefinitions))
	for _, objectDefinition := range allOracleObjectDefinitions {
		allText := objectDefinition.GetAllText()
		if utf8Text, parseErr := TextToUTF8(allText, dbCharset); parseErr != nil {
			return nil, parseErr
		} else {
			allText = utf8Text
		}

		schemaObjectKey := i.objectKeyHelper.GenerateSchemaObjectKey(objectDefinition.GetOwnerName(), objectDefinition.GetObjectType(), objectDefinition.GetObjectName())
		oracleObjectDefinitionPos = append(oracleObjectDefinitionPos, &objectparser.OracleObjectDefinition{
			ChannelId:       i.channelId,
			TaskId:          i.taskId,
			SchemaObjectKey: schemaObjectKey,
			SchemaName:      objectDefinition.GetOwnerName(),
			ObjectName:      objectDefinition.GetObjectName(),
			ObjectType:      objectDefinition.GetObjectType(),
			AllText:         allText,
			Entity:          nil,
		})
	}
	log.Infof("building metadata, finish BuildOracleObjectDefinitionPos, channelId:%d, taskId:%d, objectDefinitionNum:%d", i.channelId, i.taskId, len(oracleObjectDefinitionPos))
	return oracleObjectDefinitionPos, nil
}

func (i *MetadataHelper) BuildOracleObjectDefinitionAnalyzeDetails(definitions []datasource.OracleObjectDefinition) []*objectparser.OracleObjectDefinitionAnalyzeDetail {

	oracleObjectDefinitionAnalyzeDetails := make([]*objectparser.OracleObjectDefinitionAnalyzeDetail, 0, len(definitions))
	for _, oracleObjectDefinitionCode := range definitions {
		oracleObjectDefinitionAnalyzeDetails = append(oracleObjectDefinitionAnalyzeDetails, &objectparser.OracleObjectDefinitionAnalyzeDetail{
			ChannelId:          i.channelId,
			TaskId:             i.taskId,
			SchemaObjectKey:    i.objectKeyHelper.GenerateSchemaObjectKey(oracleObjectDefinitionCode.GetOwnerName(), oracleObjectDefinitionCode.GetObjectType(), oracleObjectDefinitionCode.GetObjectName()),
			SchemaName:         oracleObjectDefinitionCode.GetOwnerName(),
			ObjectType:         oracleObjectDefinitionCode.GetObjectType(),
			ObjectName:         oracleObjectDefinitionCode.GetObjectName(),
			ObjectStatus:       i.objectStatusMap[oracleObjectDefinitionCode.Key()].Status,
			Status:             constants.StatStatusWaiting.String(),
			MethodInvokeList:   "",
			IdentifierList:     "",
			TableReferenceList: "",
			ReservedWordList:   "",
			PLSQLSegment:       "",
			ErrorDetail:        "",
		})
	}
	log.Infof("building metadata, finish BuildOracleObjectDefinitionAnalyzeDetails, channelId:%d, taskId:%d, detailNum:%d", i.channelId, i.taskId, len(oracleObjectDefinitionAnalyzeDetails))
	return oracleObjectDefinitionAnalyzeDetails
}

func (i *MetadataHelper) initFilter() {
	i.filterMapOnce.Do(func() {
		if len(i.objectFilterCfgs) > 0 {
			i.objectFilterMap = make(map[string]bool)
			for _, cfg := range i.objectFilterCfgs {
				objectType := cfg.ObjectType
				if objectType == constants.OracleObjectTypePackage {
					objectType = constants.OracleObjectTypePackageBody
				}
				key := fmt.Sprintf("%s.%s.%s", cfg.SchemaName, objectType, cfg.ObjectName)
				i.objectFilterMap[key] = true
			}
		}
	})
}

func (i *MetadataHelper) isMatch(key string) bool {
	if len(i.objectFilterMap) == 0 {
		return true
	}
	return i.objectFilterMap[key]
}

func (i *MetadataHelper) FilterOracleObjectDefinitionAnalyzeDetails(details []*objectparser.OracleObjectDefinitionAnalyzeDetail) []*objectparser.OracleObjectDefinitionAnalyzeDetail {
	i.initFilter()

	if len(i.objectFilterCfgs) == 0 {
		return details
	}

	var filteredDetails []*objectparser.OracleObjectDefinitionAnalyzeDetail
	for _, detail := range details {
		objectType := detail.ObjectType
		if detail.ObjectType == constants.OracleObjectTypePackage {
			objectType = constants.OracleObjectTypePackageBody
		}
		key := fmt.Sprintf("%s.%s.%s", detail.SchemaName, objectType, detail.ObjectName)
		if i.isMatch(key) {
			filteredDetails = append(filteredDetails, detail)
		}
	}
	log.Infof("FilterOracleObjectDefinitionAnalyzeDetails, channelId:%d, taskId:%d, before filter count: %d, after filter count: %d", i.channelId, i.taskId, len(details), len(filteredDetails))
	return filteredDetails
}

func (i *MetadataHelper) FilterOracleObjectDefinitionPos(definitions []*objectparser.OracleObjectDefinition) []*objectparser.OracleObjectDefinition {
	i.initFilter()

	if len(i.objectFilterCfgs) == 0 {
		return definitions
	}

	var filteredDefinitions []*objectparser.OracleObjectDefinition
	for _, def := range definitions {
		objectType := def.ObjectType
		if def.ObjectType == constants.OracleObjectTypePackage {
			objectType = constants.OracleObjectTypePackageBody
		}
		key := fmt.Sprintf("%s.%s.%s", def.SchemaName, objectType, def.ObjectName)
		if i.isMatch(key) {
			filteredDefinitions = append(filteredDefinitions, def)
		}
	}
	log.Infof("FilterOracleObjectDefinitionPos, channelId:%d, taskId:%d, before filter count: %d, after filter count: %d", i.channelId, i.taskId, len(definitions), len(filteredDefinitions))
	return filteredDefinitions
}

func (i *MetadataHelper) ValidateOracleDependency(dependencies []*objectparser.OracleDependency) error {
	duplicateUuidCount := 0
	uuidMap := make(map[string]*objectparser.OracleDependency)
	for _, dependency := range dependencies {
		if existDependency, ok := uuidMap[dependency.UUID]; ok {
			log.Errorf("ValidateOracleDependency, duplicated UUID, channelId:%d, taskId:%d, UUID:%s, existItem[schemaName:%s, packageName:%s, objectName:%s, objectType:%s, referedSchemaName:%s, referedPackageName:%s, referedObjectName:%s, referedObjectType:%s], newItem[schemaName:%s, packageName:%s, objectName:%s, objectType:%s, referedSchemaName:%s, referedPackageName:%s, referedObjectName:%s, referedObjectType:%s]",
				i.channelId, i.taskId, dependency.UUID,
				existDependency.SchemaName, existDependency.PackageName, existDependency.Name, existDependency.Type, existDependency.ReferencedOwner, existDependency.ReferencedPackageName, existDependency.ReferencedName, existDependency.ReferencedType,
				dependency.SchemaName, dependency.PackageName, dependency.Name, dependency.Type, dependency.ReferencedOwner, dependency.ReferencedPackageName, dependency.ReferencedName, dependency.ReferencedType)
			duplicateUuidCount++
		}
		uuidMap[dependency.UUID] = dependency
	}
	if duplicateUuidCount > 0 {
		return tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_DUPLICATE_UUID, fmt.Sprintf("found %d duplicated UUID", duplicateUuidCount))
	}
	return nil
}
