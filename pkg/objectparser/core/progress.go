package core

import (
	"context"

	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
)

func GetProgress(ctx context.Context, taskId int) (*message.GetTaskProgressResp, error) {
	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("GetProgress, get task failed, taskId:%d, err:%v", taskId, getTaskErr)
		return nil, getTaskErr
	}

	summary, getSummaryErr := models.GetObjectParserWriter().GetOracleObjectDefinitionAnalyzeSummary(ctx, taskId)
	if getSummaryErr != nil {
		log.Warnf("GetProgress, get summary failed, taskId:%d, err:%v", taskId, getSummaryErr)
		// Continue processing logs even if summary is not found
	}

	logs, getLogErr := models.GetProgressLogReaderWriter().ListProcessLogDetails(ctx, taskId)
	if getLogErr != nil {
		log.Errorf("GetProgress, get logs failed, taskId:%d, err:%v", taskId, getLogErr)
		return nil, getLogErr
	}

	logMessages := make([]*message.TaskProgressLogDetail, 0)
	for _, l := range logs {
		logMessages = append(logMessages, &message.TaskProgressLogDetail{
			LogTime:    l.Entity.CreatedAt,
			LogLevel:   l.LogLevel,
			LogMessage: l.Detail,
		})
	}

	resp := &message.GetTaskProgressResp{
		TaskId:         taskId,
		StartTime:      taskInfo.StartTime,
		LastUpdateTime: taskInfo.EndTime,
		ProgressLogInfo: &message.TaskProgressLogInfo{
			TaskId:        taskId,
			LogStartTime:  taskInfo.StartTime,
			LogCount:      len(logs),
			LogDetailList: logMessages,
		},
	}

	// Only populate summary-related fields if summary exists
	if summary != nil && getSummaryErr == nil {
		resp.TotalDuration = parse.FormatFloat(summary.Duration, 2)
		resp.TotalNums = summary.TotalNum
		resp.SuccessNums = summary.SuccessNum
		resp.FailedNums = summary.FailedNum
		if summary.TotalNum > 0 {
			resp.Progress = float64(summary.SuccessNum+summary.FailedNum) / float64(summary.TotalNum)
		}
	} else {
		// Set default values when summary is not available
		resp.TotalDuration = "0"
		resp.TotalNums = 0
		resp.SuccessNums = 0
		resp.FailedNums = 0
		resp.Progress = 0
	}

	return resp, nil
}
