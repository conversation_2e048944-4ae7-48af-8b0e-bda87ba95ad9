package core

import (
	"encoding/json"
	"reflect"
	"testing"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"
)

func TestFindNodes(t *testing.T) {

	text := `{
            "stmtType": "statementList",
            "stmtName": "",
            "stmtValue": "statementList",
            "key": "T:191",
            "children": [
                {
                    "stmtType": "createPackage",
                    "stmtName": "packageName",
                    "stmtValue": "book",
                    "key": "T:190",
                    "children": [
                        {
                            "stmtType": "statementList",
                            "stmtName": "",
                            "stmtValue": "statementList",
                            "key": "T:189",
                            "children": [
                                {
                                    "stmtType": "createProcedure",
                                    "stmtName": "procedureName",
                                    "stmtValue": "assert_notnull",
                                    "key": "T:10",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:3",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "tested_variable",
                                                    "key": "T:2",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:1",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "",
                                            "stmtValue": "",
                                            "key": "T:9",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:8",
                                                    "children": [
                                                        {
                                                            "stmtType": "if",
                                                            "stmtName": "condition",
                                                            "stmtValue": "tested_variable IS NULL",
                                                            "key": "T:7",
                                                            "children": [
                                                                {
                                                                    "stmtType": "statementList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "then",
                                                                    "key": "T:6",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "raise",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:5",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "exception",
                                                                                    "stmtValue": "VALUE_ERROR",
                                                                                    "key": "T:4",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "stmtType": "createFunction",
                                    "stmtName": "functionName",
                                    "stmtValue": "book_copy_qty",
                                    "key": "T:40",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:13",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "isbn_in",
                                                    "key": "T:12",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:11",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "block",
                                            "stmtValue": "",
                                            "key": "T:38",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:25",
                                                    "children": [
                                                        {
                                                            "stmtType": "if",
                                                            "stmtName": "condition",
                                                            "stmtValue": "isbn_in IS NOT NULL",
                                                            "key": "T:22",
                                                            "children": [
                                                                {
                                                                    "stmtType": "statementList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "then",
                                                                    "key": "T:21",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "open",
                                                                            "stmtName": "cursorName",
                                                                            "stmtValue": "bc_cur",
                                                                            "key": "T:14",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "fetch",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:19",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "cursor",
                                                                                    "stmtName": "cursorName",
                                                                                    "stmtValue": "bc_cur",
                                                                                    "key": "T:15",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExprList",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "into",
                                                                                    "key": "T:17",
                                                                                    "children": [
                                                                                        {
                                                                                            "stmtType": "sqlExpr",
                                                                                            "stmtName": "",
                                                                                            "stmtValue": "number_o_copies",
                                                                                            "key": "T:16",
                                                                                            "children": []
                                                                                        }
                                                                                    ]
                                                                                },
                                                                                {
                                                                                    "stmtType": "bulkCollect",
                                                                                    "stmtName": "isBulkCollect",
                                                                                    "stmtValue": "false",
                                                                                    "key": "T:18",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        },
                                                                        {
                                                                            "stmtType": "close",
                                                                            "stmtName": "cursorName",
                                                                            "stmtValue": "bc_cur",
                                                                            "key": "T:20",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "return",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:24",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExpr",
                                                                    "stmtName": "",
                                                                    "stmtValue": "number_o_copies",
                                                                    "key": "T:23",
                                                                    "children": []
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameters",
                                                    "stmtName": "",
                                                    "stmtValue": "parameters",
                                                    "key": "T:37",
                                                    "children": [
                                                        {
                                                            "stmtType": "parameterName",
                                                            "stmtName": "",
                                                            "stmtValue": "number_o_copies",
                                                            "key": "T:28",
                                                            "children": [
                                                                {
                                                                    "stmtType": "dataType",
                                                                    "stmtName": "",
                                                                    "stmtValue": "NUMBER",
                                                                    "key": "T:26",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "sqlExpr",
                                                                    "stmtName": "defaultValue",
                                                                    "stmtValue": "0",
                                                                    "key": "T:27",
                                                                    "children": []
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "parameterName",
                                                            "stmtName": "",
                                                            "stmtValue": "bc_cur",
                                                            "key": "T:36",
                                                            "children": [
                                                                {
                                                                    "stmtType": "dataType",
                                                                    "stmtName": "",
                                                                    "stmtValue": "CURSOR",
                                                                    "key": "T:29",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "defaultValue",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:35",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "queryBlock",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:34",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "selectItemList",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "",
                                                                                    "key": "T:31",
                                                                                    "children": [
                                                                                        {
                                                                                            "stmtType": "selectItem",
                                                                                            "stmtName": "",
                                                                                            "stmtValue": "COUNT(*)",
                                                                                            "key": "T:30",
                                                                                            "children": []
                                                                                        }
                                                                                    ]
                                                                                },
                                                                                {
                                                                                    "stmtType": "tableSource",
                                                                                    "stmtName": "fromTable",
                                                                                    "stmtValue": "book_copies",
                                                                                    "key": "T:32",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "where",
                                                                                    "stmtValue": "isbn = isbn_in",
                                                                                    "key": "T:33",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "returnDataType",
                                            "stmtName": "dataType",
                                            "stmtValue": "NUMBER",
                                            "key": "T:39",
                                            "children": []
                                        }
                                    ]
                                },
                                {
                                    "stmtType": "createProcedure",
                                    "stmtName": "procedureName",
                                    "stmtValue": "add",
                                    "key": "T:84",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:55",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "isbn_in",
                                                    "key": "T:42",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:41",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "title_in",
                                                    "key": "T:44",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:43",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "author_in",
                                                    "key": "T:46",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:45",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "page_count_in",
                                                    "key": "T:48",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "NUMBER",
                                                            "key": "T:47",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "summary_in",
                                                    "key": "T:50",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:49",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "date_published_in",
                                                    "key": "T:52",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "DATE",
                                                            "key": "T:51",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "barcode_id_in",
                                                    "key": "T:54",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:53",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "",
                                            "stmtValue": "",
                                            "key": "T:83",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:82",
                                                    "children": [
                                                        {
                                                            "stmtType": "methodInvoke",
                                                            "stmtName": "methodName",
                                                            "stmtValue": "assert_notnull",
                                                            "key": "T:58",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExprList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "arguments",
                                                                    "key": "T:57",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "",
                                                                            "stmtValue": "isbn_in",
                                                                            "key": "T:56",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "insert",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:75",
                                                            "children": [
                                                                {
                                                                    "stmtType": "insertTable",
                                                                    "stmtName": "intoTable",
                                                                    "stmtValue": "books",
                                                                    "key": "T:59",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "values",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:67",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "valuesClause",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:66",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "isbn_in",
                                                                                    "key": "T:60",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "title_in",
                                                                                    "key": "T:61",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "summary_in",
                                                                                    "key": "T:62",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "author_in",
                                                                                    "key": "T:63",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "date_published_in",
                                                                                    "key": "T:64",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "page_count_in",
                                                                                    "key": "T:65",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    "stmtType": "columns",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:74",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "isbn",
                                                                            "key": "T:68",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "title",
                                                                            "key": "T:69",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "summary",
                                                                            "key": "T:70",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "author",
                                                                            "key": "T:71",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "date_published",
                                                                            "key": "T:72",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "page_count",
                                                                            "key": "T:73",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "if",
                                                            "stmtName": "condition",
                                                            "stmtValue": "barcode_id_in IS NOT NULL",
                                                            "key": "T:81",
                                                            "children": [
                                                                {
                                                                    "stmtType": "statementList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "then",
                                                                    "key": "T:80",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "methodInvoke",
                                                                            "stmtName": "methodName",
                                                                            "stmtValue": "add_copy",
                                                                            "key": "T:79",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExprList",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "arguments",
                                                                                    "key": "T:78",
                                                                                    "children": [
                                                                                        {
                                                                                            "stmtType": "sqlExpr",
                                                                                            "stmtName": "",
                                                                                            "stmtValue": "isbn_in",
                                                                                            "key": "T:76",
                                                                                            "children": []
                                                                                        },
                                                                                        {
                                                                                            "stmtType": "sqlExpr",
                                                                                            "stmtName": "",
                                                                                            "stmtValue": "barcode_id_in",
                                                                                            "key": "T:77",
                                                                                            "children": []
                                                                                        }
                                                                                    ]
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "stmtType": "createProcedure",
                                    "stmtName": "procedureName",
                                    "stmtValue": "add_copy",
                                    "key": "T:113",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:89",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "isbn_in",
                                                    "key": "T:86",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:85",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "barcode_id_in",
                                                    "key": "T:88",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:87",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "",
                                            "stmtValue": "",
                                            "key": "T:112",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:105",
                                                    "children": [
                                                        {
                                                            "stmtType": "methodInvoke",
                                                            "stmtName": "methodName",
                                                            "stmtValue": "assert_notnull",
                                                            "key": "T:92",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExprList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "arguments",
                                                                    "key": "T:91",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "",
                                                                            "stmtValue": "isbn_in",
                                                                            "key": "T:90",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "methodInvoke",
                                                            "stmtName": "methodName",
                                                            "stmtValue": "assert_notnull",
                                                            "key": "T:95",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExprList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "arguments",
                                                                    "key": "T:94",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "",
                                                                            "stmtValue": "barcode_id_in",
                                                                            "key": "T:93",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "insert",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:104",
                                                            "children": [
                                                                {
                                                                    "stmtType": "insertTable",
                                                                    "stmtName": "intoTable",
                                                                    "stmtValue": "book_copies",
                                                                    "key": "T:96",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "values",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:100",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "valuesClause",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:99",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "isbn_in",
                                                                                    "key": "T:97",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "valueItem",
                                                                                    "stmtName": "",
                                                                                    "stmtValue": "barcode_id_in",
                                                                                    "key": "T:98",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    "stmtType": "columns",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:103",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "isbn",
                                                                            "key": "T:101",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "insertColumn",
                                                                            "stmtName": "",
                                                                            "stmtValue": "barcode_id",
                                                                            "key": "T:102",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "exception",
                                                    "stmtName": "",
                                                    "stmtValue": "",
                                                    "key": "T:111",
                                                    "children": [
                                                        {
                                                            "stmtType": "exceptionItems",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:110",
                                                            "children": [
                                                                {
                                                                    "stmtType": "exceptionItem",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:109",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "when",
                                                                            "stmtValue": "DUP_VAL_ON_INDEX",
                                                                            "key": "T:106",
                                                                            "children": []
                                                                        },
                                                                        {
                                                                            "stmtType": "statementList",
                                                                            "stmtName": "",
                                                                            "stmtValue": "then",
                                                                            "key": "T:108",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "expression",
                                                                                    "stmtValue": "NULL",
                                                                                    "key": "T:107",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "stmtType": "createProcedure",
                                    "stmtName": "procedureName",
                                    "stmtValue": "change",
                                    "key": "T:157",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:128",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "isbn_in",
                                                    "key": "T:115",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:114",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "new_title",
                                                    "key": "T:117",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:116",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "new_author",
                                                    "key": "T:119",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:118",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "new_page_count",
                                                    "key": "T:121",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "NUMBER",
                                                            "key": "T:120",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "new_summary",
                                                    "key": "T:124",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:122",
                                                            "children": []
                                                        },
                                                        {
                                                            "stmtType": "sqlExpr",
                                                            "stmtName": "defaultValue",
                                                            "stmtValue": "NULL",
                                                            "key": "T:123",
                                                            "children": []
                                                        }
                                                    ]
                                                },
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "new_date_published",
                                                    "key": "T:127",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "DATE",
                                                            "key": "T:125",
                                                            "children": []
                                                        },
                                                        {
                                                            "stmtType": "sqlExpr",
                                                            "stmtName": "defaultValue",
                                                            "stmtValue": "NULL",
                                                            "key": "T:126",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "",
                                            "stmtValue": "",
                                            "key": "T:156",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:155",
                                                    "children": [
                                                        {
                                                            "stmtType": "methodInvoke",
                                                            "stmtName": "methodName",
                                                            "stmtValue": "assert_notnull",
                                                            "key": "T:131",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExprList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "arguments",
                                                                    "key": "T:130",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "",
                                                                            "stmtValue": "isbn_in",
                                                                            "key": "T:129",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "update",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:150",
                                                            "children": [
                                                                {
                                                                    "stmtType": "tableSource",
                                                                    "stmtName": "fromTable",
                                                                    "stmtValue": "books",
                                                                    "key": "T:132",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "updateItems",
                                                                    "stmtName": "",
                                                                    "stmtValue": "",
                                                                    "key": "T:148",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "updateSetItem",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:135",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "column",
                                                                                    "stmtValue": "title",
                                                                                    "key": "T:133",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "value",
                                                                                    "stmtValue": "new_title",
                                                                                    "key": "T:134",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        },
                                                                        {
                                                                            "stmtType": "updateSetItem",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:138",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "column",
                                                                                    "stmtValue": "author",
                                                                                    "key": "T:136",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "value",
                                                                                    "stmtValue": "new_author",
                                                                                    "key": "T:137",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        },
                                                                        {
                                                                            "stmtType": "updateSetItem",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:141",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "column",
                                                                                    "stmtValue": "page_count",
                                                                                    "key": "T:139",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "value",
                                                                                    "stmtValue": "new_page_count",
                                                                                    "key": "T:140",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        },
                                                                        {
                                                                            "stmtType": "updateSetItem",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:144",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "column",
                                                                                    "stmtValue": "summary",
                                                                                    "key": "T:142",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "value",
                                                                                    "stmtValue": "new_summary",
                                                                                    "key": "T:143",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        },
                                                                        {
                                                                            "stmtType": "updateSetItem",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:147",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "column",
                                                                                    "stmtValue": "date_published",
                                                                                    "key": "T:145",
                                                                                    "children": []
                                                                                },
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "value",
                                                                                    "stmtValue": "new_date_published",
                                                                                    "key": "T:146",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    "stmtType": "sqlExpr",
                                                                    "stmtName": "where",
                                                                    "stmtValue": "isbn = isbn_in",
                                                                    "key": "T:149",
                                                                    "children": []
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "if",
                                                            "stmtName": "condition",
                                                            "stmtValue": "SQL%ROWCOUNT = 0",
                                                            "key": "T:154",
                                                            "children": [
                                                                {
                                                                    "stmtType": "statementList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "then",
                                                                    "key": "T:153",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "raise",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:152",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "exception",
                                                                                    "stmtValue": "NO_DATA_FOUND",
                                                                                    "key": "T:151",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "stmtType": "createProcedure",
                                    "stmtName": "procedureName",
                                    "stmtValue": "remove_copy",
                                    "key": "T:169",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:160",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "barcode_id_in",
                                                    "key": "T:159",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:158",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "",
                                            "stmtValue": "",
                                            "key": "T:168",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:167",
                                                    "children": [
                                                        {
                                                            "stmtType": "methodInvoke",
                                                            "stmtName": "methodName",
                                                            "stmtValue": "assert_notnull",
                                                            "key": "T:163",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExprList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "arguments",
                                                                    "key": "T:162",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "",
                                                                            "stmtValue": "barcode_id_in",
                                                                            "key": "T:161",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "delete",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:166",
                                                            "children": [
                                                                {
                                                                    "stmtType": "tableSource",
                                                                    "stmtName": "fromTable",
                                                                    "stmtValue": "book_copies",
                                                                    "key": "T:164",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "sqlExpr",
                                                                    "stmtName": "where",
                                                                    "stmtValue": "barcode_id = barcode_id_in",
                                                                    "key": "T:165",
                                                                    "children": []
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "stmtType": "createProcedure",
                                    "stmtName": "procedureName",
                                    "stmtValue": "weed",
                                    "key": "T:188",
                                    "children": [
                                        {
                                            "stmtType": "parameters",
                                            "stmtName": "",
                                            "stmtValue": "parameters",
                                            "key": "T:172",
                                            "children": [
                                                {
                                                    "stmtType": "parameterName",
                                                    "stmtName": "",
                                                    "stmtValue": "isbn_in",
                                                    "key": "T:171",
                                                    "children": [
                                                        {
                                                            "stmtType": "dataType",
                                                            "stmtName": "",
                                                            "stmtValue": "VARCHAR2",
                                                            "key": "T:170",
                                                            "children": []
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            "stmtType": "block",
                                            "stmtName": "",
                                            "stmtValue": "",
                                            "key": "T:187",
                                            "children": [
                                                {
                                                    "stmtType": "statementList",
                                                    "stmtName": "",
                                                    "stmtValue": "statementList",
                                                    "key": "T:186",
                                                    "children": [
                                                        {
                                                            "stmtType": "methodInvoke",
                                                            "stmtName": "methodName",
                                                            "stmtValue": "assert_notnull",
                                                            "key": "T:175",
                                                            "children": [
                                                                {
                                                                    "stmtType": "sqlExprList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "arguments",
                                                                    "key": "T:174",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "sqlExpr",
                                                                            "stmtName": "",
                                                                            "stmtValue": "isbn_in",
                                                                            "key": "T:173",
                                                                            "children": []
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "delete",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:178",
                                                            "children": [
                                                                {
                                                                    "stmtType": "tableSource",
                                                                    "stmtName": "fromTable",
                                                                    "stmtValue": "book_copies@linkgbk",
                                                                    "key": "T:176",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "sqlExpr",
                                                                    "stmtName": "where",
                                                                    "stmtValue": "isbn = isbn_in",
                                                                    "key": "T:177",
                                                                    "children": []
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "delete",
                                                            "stmtName": "",
                                                            "stmtValue": "",
                                                            "key": "T:181",
                                                            "children": [
                                                                {
                                                                    "stmtType": "tableSource",
                                                                    "stmtName": "fromTable",
                                                                    "stmtValue": "books",
                                                                    "key": "T:179",
                                                                    "children": []
                                                                },
                                                                {
                                                                    "stmtType": "sqlExpr",
                                                                    "stmtName": "where",
                                                                    "stmtValue": "isbn = isbn_in",
                                                                    "key": "T:180",
                                                                    "children": []
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            "stmtType": "if",
                                                            "stmtName": "condition",
                                                            "stmtValue": "SQL%ROWCOUNT = 0",
                                                            "key": "T:185",
                                                            "children": [
                                                                {
                                                                    "stmtType": "statementList",
                                                                    "stmtName": "",
                                                                    "stmtValue": "then",
                                                                    "key": "T:184",
                                                                    "children": [
                                                                        {
                                                                            "stmtType": "raise",
                                                                            "stmtName": "",
                                                                            "stmtValue": "",
                                                                            "key": "T:183",
                                                                            "children": [
                                                                                {
                                                                                    "stmtType": "sqlExpr",
                                                                                    "stmtName": "exception",
                                                                                    "stmtValue": "NO_DATA_FOUND",
                                                                                    "key": "T:182",
                                                                                    "children": []
                                                                                }
                                                                            ]
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }`

	root := &dto.AbstractSyntaxTreeNode{}

	json.Unmarshal([]byte(text), root)

	type args struct {
		root      *dto.AbstractSyntaxTreeNode
		stmtType  string
		stmtName  string
		stmtValue string
	}
	tests := []struct {
		name string
		args args
		want []*dto.AbstractSyntaxTreeNode
	}{
		{
			name: "1",
			args: args{
				root:      root,
				stmtType:  "createProcedure",
				stmtName:  "procedureName",
				stmtValue: "assert_notnull",
			},
			want: []*dto.AbstractSyntaxTreeNode{
				{
					StmtType:  "createProcedure",
					StmtName:  "procedureName",
					StmtValue: "assert_notnull",
					Key:       "T:10",
					Children: []*dto.AbstractSyntaxTreeNode{
						{
							StmtType:  "parameters",
							StmtName:  "",
							StmtValue: "parameters",
							Key:       "T:3",
							Children: []*dto.AbstractSyntaxTreeNode{
								{
									StmtType:  "parameterName",
									StmtName:  "",
									StmtValue: "tested_variable",
									Key:       "T:2",
									Children: []*dto.AbstractSyntaxTreeNode{
										{
											StmtType:  "dataType",
											StmtName:  "",
											StmtValue: "VARCHAR2",
											Key:       "T:1",
											Children:  []*dto.AbstractSyntaxTreeNode{},
										},
									},
								},
							},
						},
						{
							StmtType:  "block",
							StmtName:  "",
							StmtValue: "",
							Key:       "T:9",
							Children: []*dto.AbstractSyntaxTreeNode{
								{
									StmtType:  "statementList",
									StmtName:  "",
									StmtValue: "statementList",
									Key:       "T:8",
									Children: []*dto.AbstractSyntaxTreeNode{
										{
											StmtType:  "if",
											StmtName:  "condition",
											StmtValue: "tested_variable IS NULL",
											Key:       "T:7",
											Children: []*dto.AbstractSyntaxTreeNode{
												{
													StmtType:  "statementList",
													StmtName:  "",
													StmtValue: "then",
													Key:       "T:6",
													Children: []*dto.AbstractSyntaxTreeNode{
														{
															StmtType:  "raise",
															StmtName:  "",
															StmtValue: "",
															Key:       "T:5",
															Children: []*dto.AbstractSyntaxTreeNode{
																{
																	StmtType:  "sqlExpr",
																	StmtName:  "exception",
																	StmtValue: "VALUE_ERROR",
																	Key:       "T:4",
																	Children:  []*dto.AbstractSyntaxTreeNode{},
																},
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FindNodes(tt.args.root, tt.args.stmtType, tt.args.stmtName, tt.args.stmtValue); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FindNodes() = %v, want %v", got, tt.want)
			}
		})
	}
}
