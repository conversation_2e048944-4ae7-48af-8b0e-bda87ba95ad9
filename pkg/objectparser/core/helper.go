package core

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/ai"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/provider"
	aiprovider "gitee.com/pingcap_enterprise/tms/pkg/ai/provider"
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

type ConvertHelper struct {
	taskId         int
	param          structs.LLMProviderAPIConfig
	aiHelper       *ai.AIConvertorProxy
	licenseManager *license.LicenseManager
	javaCodeParser *JavaCodeParser
}

func NewConvertHelper(taskId int, param structs.LLMProviderAPIConfig, licenseManager *license.LicenseManager) *ConvertHelper {
	return &ConvertHelper{
		taskId:         taskId,
		param:          param,
		licenseManager: licenseManager,
		aiHelper:       ai.NewAIConvertorProxy(taskId, param),
		javaCodeParser: NewJavaCodeParser(),
	}
}

func (i *ConvertHelper) GetDependencySchemaObjectKeys(dependencies []*objectparser.OracleDependency) []string {
	var schemaObjectKeys []string
	for _, dependency := range dependencies {
		schemaObjectKeys = append(schemaObjectKeys, dependency.GetSchemaObjectKey())
	}
	schemaObjectKeys = lo.Uniq(schemaObjectKeys)
	return schemaObjectKeys
}

func (i *ConvertHelper) BuildDefinitionMap(definitions []*objectparser.OracleObjectDefinition) map[string]objectparser.OracleObjectDefinition {
	definitionMap := make(map[string]objectparser.OracleObjectDefinition)
	for _, definition := range definitions {
		definitionMap[definition.SchemaObjectKey] = *definition
	}
	return definitionMap
}

func (i *ConvertHelper) BuildDefaultPromptAndMap(prompts []*objectparser.OracleObjectTransformationPrompt) (*objectparser.OracleObjectTransformationPrompt, *objectparser.OracleObjectTransformationPrompt, map[uint]*objectparser.OracleObjectTransformationPrompt, error) {
	var defaultCodePrompt *objectparser.OracleObjectTransformationPrompt
	var defaultTriggerPrompt *objectparser.OracleObjectTransformationPrompt
	promptMap := make(map[uint]*objectparser.OracleObjectTransformationPrompt)
	
	for _, prompt := range prompts {
		if prompt.IsDefault {
			if prompt.PromptCategory == "CODE" {
				defaultCodePrompt = prompt
			} else if prompt.PromptCategory == "TRIGGER" {
				defaultTriggerPrompt = prompt
			}
		}
		promptMap[prompt.ID] = prompt
	}
	
	// At least the CODE default prompt is required
	if defaultCodePrompt == nil {
		err := tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PROMPT_NO_DEFAULT, "No default CODE prompt found")
		return nil, nil, nil, err
	}
	
	return defaultCodePrompt, defaultTriggerPrompt, promptMap, nil
}

func (i *ConvertHelper) BuildConvertItems(
	objects []*objectparser.OracleDependency,
	details []*objectparser.OracleObjectDefinitionAnalyzeDetail,
	allFuncAndProcs []*objectparser.OracleDependency,
	objectDependencies []*objectparser.OracleDependencyVO,
	promptHelper *PromptHelper,
	convertObjects []message.ConvertObject,
	definitionMap map[string]objectparser.OracleObjectDefinition,
	tableContextMap map[string]string) []*provider.ConvertItem {

	messageBuilder := NewMessageBuilder()

	allFuncAndProcsMap := make(map[string]bool)
	for _, obj := range allFuncAndProcs {
		if obj.ReferencedPackageName == "" {
			allFuncAndProcsMap[obj.ReferencedName] = true
		} else {
			allFuncAndProcsMap[obj.ReferencedPackageName+"."+obj.ReferencedName] = true
		}
	}

	detailMap := make(map[string]*objectparser.OracleObjectDefinitionAnalyzeDetail)
	for _, detail := range details {
		detailMap[detail.SchemaObjectKey] = detail
	}

	objectDependenciesMap := make(map[string][]*objectparser.OracleDependencyVO)
	for _, object := range objectDependencies {
		if objectDependenciesMap[object.SourceUUID] == nil {
			objectDependenciesMap[object.SourceUUID] = make([]*objectparser.OracleDependencyVO, 0)
		}
		var objectReferencedObj string
		if object.ReferencedPackageName == "" {
			objectReferencedObj = object.ReferencedName
		} else {
			objectReferencedObj = object.ReferencedPackageName + "." + object.ReferencedName
		}
		mapss := lo.Keys(allFuncAndProcsMap)
		sort.Strings(mapss)

		if !allFuncAndProcsMap[objectReferencedObj] {
			continue
		}
		objectDependenciesMap[object.SourceUUID] = append(objectDependenciesMap[object.SourceUUID], object)
	}

	maxDepth := uint(0)
	convertObjectMap := make(map[string]message.ConvertObject)
	for _, convertObject := range convertObjects {
		convertObjectMap[convertObject.ObjectUUID] = convertObject
		if convertObject.Depth > maxDepth {
			maxDepth = convertObject.Depth
		}
	}
	dependencyArr := make([][]*objectparser.OracleDependency, maxDepth+1)
	for _, object := range objects {
		convertObject, ok := convertObjectMap[object.UUID]
		if ok {
			if dependencyArr[convertObject.Depth] == nil {
				dependencyArr[convertObject.Depth] = make([]*objectparser.OracleDependency, 0)
			}
			dependencyArr[convertObject.Depth] = append(dependencyArr[convertObject.Depth], object)
		} else {
			if dependencyArr[0] == nil {
				dependencyArr[0] = make([]*objectparser.OracleDependency, 0)
			}
			dependencyArr[0] = append(dependencyArr[0], object)
		}
	}

	convertItems := make([]*provider.ConvertItem, 0, len(objects))
	for idx, dependencyItems := range dependencyArr {
		for _, dependency := range dependencyItems {
			var (
				reservedWord string
				methodInvoke string
				identifier   string
				plsqlSegment string
			)

			if detailMap[dependency.GetSchemaObjectKey()] != nil {
				reservedWord = detailMap[dependency.GetSchemaObjectKey()].ReservedWordList
				methodInvoke = detailMap[dependency.GetSchemaObjectKey()].MethodInvokeList
				identifier = detailMap[dependency.GetSchemaObjectKey()].IdentifierList
				plsqlSegment = detailMap[dependency.GetSchemaObjectKey()].PLSQLSegment

			}
			promptId, prompt := promptHelper.GetCertainPrompt(dependency)
			dependencies := objectDependenciesMap[dependency.UUID]
			dependenciesBytes, _ := json.Marshal(dependencies)
			log.Debugf("bulding convert item, itemUUID:%s, itemName:%s, itemType:%s, featureId:%s, dependencies:%v", dependency.UUID, dependency.Name, dependency.Type, i.licenseManager.GetFeatureId(dependency.Type), string(dependenciesBytes))
			
			// Prepare object definition, add table DDL for TRIGGER objects that need it
			objectDefinition := definitionMap[dependency.GetSchemaObjectKey()].AllText
			if dependency.Type == "TRIGGER" && tableContextMap != nil {
				if tableDDL, exists := tableContextMap[dependency.UUID]; exists && tableDDL != "" {
					// tableContextMap only contains TRIGGER objects that use Hibernate prompts,
					// so it's safe to include the table DDL for any TRIGGER found in the map
					objectDefinition = tableDDL + "\n\n-- TRIGGER Definition --\n\n" + objectDefinition
					log.Infof("Enhanced TRIGGER %s with table DDL context for Hibernate prompt (total length: %d chars)", 
						dependency.GetSchemaObjectKey(), len(objectDefinition))
				}
			}
			
			convertItems = append(convertItems, &provider.ConvertItem{
				FeatureID:        i.licenseManager.GetFeatureId(dependency.Type),
				SchemaName:       dependency.SchemaName,
				PackageName:      dependency.PackageName,
				ObjectType:       dependency.Type,
				ObjectName:       dependency.Name,
				DependencyUUID:   dependency.UUID,
				ObjectDefinition: objectDefinition,
				ReservedWords:    messageBuilder.ConstructReservedWordList(reservedWord),
				MethodInvokes:    messageBuilder.ConstructMethodInvokeList(methodInvoke),
				Identifiers:      messageBuilder.ConstructIdentifierList(identifier),
				PLSQLSegment:     messageBuilder.ConstructPLSQLSegment(plsqlSegment),
				PromptId:         promptId,
				PromptText:       prompt,
				Depth:            maxDepth - uint(idx),
				Dependencies:     dependencies,
			})
		}
	}
	return convertItems
}

func (i *PromptHelper) GetCertainPrompt(dependency *objectparser.OracleDependency) (uint, string) {
	p := i.m[dependency.UUID]
	if p == nil {
		p = i.defaultPrompt
	}

	if i.reqPrompt != "" {
		return 0, i.reqPrompt
	} else {
		return p.ID, p.PromptText
	}
}

type PromptHelper struct {
	m             map[string]*objectparser.OracleObjectTransformationPrompt
	defaultPrompt *objectparser.OracleObjectTransformationPrompt
	reqPrompt     string
}

// SetObjectPromptMap sets the object-to-prompt mapping
func (ph *PromptHelper) SetObjectPromptMap(m map[string]*objectparser.OracleObjectTransformationPrompt) {
	ph.m = m
}

// SetDefaultPrompt sets the default prompt
func (ph *PromptHelper) SetDefaultPrompt(prompt *objectparser.OracleObjectTransformationPrompt) {
	ph.defaultPrompt = prompt
}

// SetRequestPrompt sets the request-specific prompt
func (ph *PromptHelper) SetRequestPrompt(prompt string) {
	ph.reqPrompt = prompt
}

func (i *ConvertHelper) BuildObjectPromptHelper(relations []*objectparser.OracleObjectTaskObjectPromptRelation, promptMap map[uint]*objectparser.OracleObjectTransformationPrompt, defaultPrompt *objectparser.OracleObjectTransformationPrompt, reqPrompt string) *PromptHelper {
	objectPromptMap := make(map[string]*objectparser.OracleObjectTransformationPrompt)
	for _, relation := range relations {
		if prompt, ok := promptMap[relation.TaskPromptId]; ok {
			objectPromptMap[relation.DependencyUUID] = prompt
		} else {
			objectPromptMap[relation.DependencyUUID] = defaultPrompt
		}
	}
	return &PromptHelper{
		m:             objectPromptMap,
		defaultPrompt: defaultPrompt,
		reqPrompt:     reqPrompt,
	}
}

func (i *ConvertHelper) BuildSuccessJavaResult(taskInfo *task.Task, convertItem *provider.ConvertItem, convertResult string, startTime time.Time, duration float64, certainPrompts []string) *objectparser.OracleToJavaResult {
	// Parse the convert result to check for multi-file format
	parsedResult, parseErr := i.javaCodeParser.ParseMultiFileOutput(convertResult)

	var isMultiFile bool
	var fileName string
	var finalCode string

	if parseErr == nil && parsedResult.IsMultiFile && len(parsedResult.Files) > 0 {
		// Multi-file result
		isMultiFile = true
		fileName = i.javaCodeParser.GenerateFileName(parsedResult.Files, convertItem.ObjectName)
		finalCode = i.javaCodeParser.CombineFilesForBackwardCompatibility(parsedResult.Files)
	} else {
		// Single file result (backward compatibility)
		isMultiFile = false
		fileName = provider.ToJavaClassName(convertItem.PackageName+"_"+convertItem.ObjectName) + ".java"
		finalCode = convertResult
	}

	return &objectparser.OracleToJavaResult{
		ChannelId:       taskInfo.ChannelId,
		TaskId:          taskInfo.TaskID,
		UUID:            convertItem.DependencyUUID,
		SchemaName:      convertItem.SchemaName,
		CodeFileName:    fileName,
		PackageName:     convertItem.PackageName,
		ObjectName:      convertItem.ObjectName,
		ConvertedCode:   finalCode,
		ObjectType:      convertItem.ObjectType,
		Depth:           convertItem.Depth,
		ConvertDuration: duration,
		ConvertSQL:      convertItem.GetSQL(),
		ConvertPrompts:  strings.Join(certainPrompts, "@,@"),
		ConvertStatus:   "SUCCESS",
		ConvertTime:     startTime,
		IsMultiFile:     isMultiFile,
	}
}

func (i *ConvertHelper) BuildFailedJavaResult(taskInfo *task.Task, convertItem *provider.ConvertItem, startTime time.Time, duration float64, certainPrompts []string, convertErr error) *objectparser.OracleToJavaResult {
	return &objectparser.OracleToJavaResult{
		ChannelId:         taskInfo.ChannelId,
		TaskId:            taskInfo.TaskID,
		UUID:              convertItem.DependencyUUID,
		SchemaName:        convertItem.SchemaName,
		PackageName:       convertItem.PackageName,
		ObjectName:        convertItem.ObjectName,
		ObjectType:        convertItem.ObjectType,
		Depth:             convertItem.Depth,
		CodeFileName:      provider.ToJavaClassName(convertItem.PackageName+"_"+convertItem.ObjectName) + ".java",
		ConvertDuration:   time.Since(startTime).Seconds(),
		ConvertStatus:     "FAILED",
		ConvertTime:       startTime,
		ConvertPrompts:    strings.Join(certainPrompts, "@,@"),
		ConvertSQL:        convertItem.GetSQL(),
		ConvertErrMessage: convertErr.Error(),
		IsMultiFile:       false, // Failed results are always single file
	}
}

func (i *ConvertHelper) BuildFailedJavaLog(info *task.Task, item *provider.ConvertItem, duration float64, err error) *objectparser.OracleToJavaLog {
	return &objectparser.OracleToJavaLog{
		ChannelId:  info.ChannelId,
		TaskId:     info.TaskID,
		LogLevel:   "error",
		LogMessage: fmt.Sprintf("%s, convert failed, time cost: %.2fs, err: %v", item.GetDisplayName(), duration, err),
	}
}

func (i *ConvertHelper) BuildSuccessJavaLog(info *task.Task, item *provider.ConvertItem, duration float64) *objectparser.OracleToJavaLog {
	return &objectparser.OracleToJavaLog{
		ChannelId:  info.ChannelId,
		TaskId:     info.TaskID,
		LogLevel:   "info",
		LogMessage: fmt.Sprintf("%s, convert success, time cost: %.2fs", item.GetDisplayName(), duration),
	}
}

func (i *ConvertHelper) BuildProcessingJavaLog(info *task.Task, item *provider.ConvertItem) *objectparser.OracleToJavaLog {
	return &objectparser.OracleToJavaLog{
		ChannelId:  info.ChannelId,
		TaskId:     info.TaskID,
		LogLevel:   "info",
		LogMessage: fmt.Sprintf("%s, converting", item.GetDisplayName()),
	}
}

// ExtractParsedFiles extracts individual files from a multi-file conversion result
// Returns nil if the result is not multi-file or parsing fails
func (i *ConvertHelper) ExtractParsedFiles(convertResult string) ([]JavaFile, error) {
	parsedResult, parseErr := i.javaCodeParser.ParseMultiFileOutput(convertResult)

	if parseErr != nil {
		return nil, parseErr
	}

	if !parsedResult.IsMultiFile || len(parsedResult.Files) == 0 {
		return nil, nil // Not multi-file or no files found
	}

	return parsedResult.Files, nil
}

// BuildJavaFiles converts parsed files to database entities
func (i *ConvertHelper) BuildJavaFiles(taskId uint, files []JavaFile, resultId uint) []*objectparser.OracleToJavaFile {
	var javaFiles []*objectparser.OracleToJavaFile

	for _, file := range files {
		javaFile := &objectparser.OracleToJavaFile{
			ResultId:    resultId,
			TaskId:      int(taskId),
			FilePath:    file.FilePath,
			FileName:    file.FileName,
			PackagePath: file.PackagePath,
			FileType:    file.FileType,
			FileContent: file.Content,
			FileOrder:   file.Order,
		}
		javaFiles = append(javaFiles, javaFile)
	}

	return javaFiles
}

func (i *ConvertHelper) ResetMetadata(ctx context.Context) error {
	taskId := i.taskId
	resetErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		// Remove history files first
		if removeHistoryFilesErr := models.GetObjectParserWriter().RemoveOracleToJavaHistoryFilesByTaskId(transactionCtx, taskId); removeHistoryFilesErr != nil {
			log.Errorf("RemoveOracleToJavaHistoryFilesByTaskId failed, taskId:%d, serr: %v", taskId, removeHistoryFilesErr)
			return removeHistoryFilesErr
		}
		if removeHistoryErr := models.GetObjectParserWriter().RemoveOracleToJavaHistoryResultByTaskId(transactionCtx, taskId); removeHistoryErr != nil {
			log.Errorf("RemoveOracleToJavaHistoryResultByTaskId failed, taskId:%d, serr: %v", taskId, removeHistoryErr)
			return removeHistoryErr
		}
		if archiveErr := models.GetObjectParserWriter().ArchiveOracleToJavaResultByTaskId(transactionCtx, taskId); archiveErr != nil {
			log.Errorf("ArchiveOracleToJavaResultByTaskId failed, taskId:%d, serr: %v", taskId, archiveErr)
			return archiveErr
		}
		// Remove current files before removing results
		if removeFilesErr := models.GetObjectParserWriter().RemoveOracleToJavaFilesByTaskId(transactionCtx, taskId); removeFilesErr != nil {
			log.Errorf("RemoveOracleToJavaFilesByTaskId failed, taskId:%d, serr: %v", taskId, removeFilesErr)
			return removeFilesErr
		}
		if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaResultByTaskId(transactionCtx, taskId); removeErr != nil {
			log.Errorf("RemoveOracleToJavaResultByTaskId failed, taskId:%d, serr: %v", taskId, removeErr)
			return removeErr
		}
		if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaSummary(transactionCtx, taskId); removeErr != nil {
			log.Errorf("RemoveOracleToJavaSummary failed, taskId:%d, serr: %v", taskId, removeErr)
			return removeErr
		}
		if removeErr := models.GetObjectParserWriter().RemoveOracleToJavaLog(transactionCtx, taskId); removeErr != nil {
			log.Errorf("RemoveOracleToJavaLog failed, taskId:%d, serr: %v", taskId, removeErr)
			return removeErr
		}
		return nil
	})
	return resetErr
}

func (i *ConvertHelper) ConvertPLSQLToJava(ctx context.Context, item *aiprovider.ConvertItem) (string, []string, error) {
	updateFeatureNumErr := i.licenseManager.ExecuteFeature(ctx, item.FeatureID.String())
	if updateFeatureNumErr != nil {
		return "", nil, tmserrors.NewError(tmserrors.TMS_LICENSE_FEATURE_EXCEED, item.FeatureID.String())
	}

	return i.aiHelper.Convert(ctx, item)
}
