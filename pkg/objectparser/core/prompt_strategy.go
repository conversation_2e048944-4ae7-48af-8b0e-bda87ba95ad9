package core

import (
	"fmt"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// ConversionPromptStrategy manages all prompt mapping strategies for conversion
// It provides a unified interface for prompt selection based on object type, framework, and context
type ConversionPromptStrategy struct {
	// EntityPrompts maps framework names to their entity generation prompts
	// Key: framework name (e.g., "hibernate", "mybatis"), Value: TABLE category prompt
	EntityPrompts map[string]*objectparser.OracleObjectTransformationPrompt

	// ObjectPrompts maps object UUIDs to their specific conversion prompts
	// Key: object UUID, Value: conversion prompt for that object
	ObjectPrompts map[string]*objectparser.OracleObjectTransformationPrompt

	// TriggerPrompts maps framework names to their trigger conversion prompts
	// Key: framework name, Value: TRIGGER category prompt
	TriggerPrompts map[string]*objectparser.OracleObjectTransformationPrompt

	// DefaultPrompt is the fallback prompt for CODE category objects when no specific prompt is found
	DefaultPrompt *objectparser.OracleObjectTransformationPrompt

	// DefaultTriggerPrompt is the fallback prompt for TRIGGER category objects when no specific prompt is found
	DefaultTriggerPrompt *objectparser.OracleObjectTransformationPrompt

	// DetectedFrameworks tracks all frameworks found in the conversion batch
	DetectedFrameworks map[string]bool

	// HasTriggers indicates whether any trigger objects exist in the batch
	HasTriggers bool

	// FrameworkPriority defines the priority order for framework selection
	// Used when multiple frameworks are detected
	FrameworkPriority []string

	taskId int
}

// NewConversionPromptStrategy creates a new prompt strategy instance
func NewConversionPromptStrategy(taskId int, defaultPrompt, defaultTriggerPrompt *objectparser.OracleObjectTransformationPrompt) *ConversionPromptStrategy {
	return &ConversionPromptStrategy{
		taskId:               taskId,
		EntityPrompts:        make(map[string]*objectparser.OracleObjectTransformationPrompt),
		ObjectPrompts:        make(map[string]*objectparser.OracleObjectTransformationPrompt),
		TriggerPrompts:       make(map[string]*objectparser.OracleObjectTransformationPrompt),
		DefaultPrompt:        defaultPrompt,
		DefaultTriggerPrompt: defaultTriggerPrompt,
		DetectedFrameworks:   make(map[string]bool),
		HasTriggers:          false,
		FrameworkPriority:    constants.OPTargetFrameworkPriorities, // Default priority
	}
}

// SetEntityPrompts sets the entity generation prompts for frameworks
func (s *ConversionPromptStrategy) SetEntityPrompts(prompts map[string]*objectparser.OracleObjectTransformationPrompt) {
	s.EntityPrompts = prompts
	// Update detected frameworks
	for framework := range prompts {
		s.registerFramework(framework)
	}
	log.Infof("Set %d entity prompts for frameworks: %v, taskId: %d", len(prompts), s.GetAllFrameworks(), s.taskId)
}

// SetObjectPrompts sets the object-specific conversion prompts
func (s *ConversionPromptStrategy) SetObjectPrompts(prompts map[string]*objectparser.OracleObjectTransformationPrompt) {
	s.ObjectPrompts = prompts
	log.Infof("Set %d object-specific prompts, taskId: %d", len(prompts), s.taskId)
}

// SetTriggerPrompts sets the trigger conversion prompts for frameworks
func (s *ConversionPromptStrategy) SetTriggerPrompts(prompts map[string]*objectparser.OracleObjectTransformationPrompt) {
	s.TriggerPrompts = prompts
	s.HasTriggers = len(prompts) > 0
	// Update detected frameworks
	for framework := range prompts {
		s.registerFramework(framework)
	}
	log.Infof("Set %d trigger prompts for frameworks, taskId: %d", len(prompts), s.taskId)
}

// GetPromptForObject returns the appropriate prompt for a specific object
// Priority for TRIGGER: Object-specific prompt > Default TRIGGER prompt > Default CODE prompt
// Priority for other types: Object-specific prompt > Default CODE prompt
func (s *ConversionPromptStrategy) GetPromptForObject(objectUUID string, objectType string, framework string) *objectparser.OracleObjectTransformationPrompt {
	// Validate inputs
	if objectUUID == "" {
		log.Errorf("GetPromptForObject called with empty objectUUID, taskId: %d", s.taskId)
		return s.getDefaultPromptForType(objectType)
	}

	// For TRIGGER objects: simplified logic without framework detection
	if objectType == "TRIGGER" {
		// Check if TRIGGER object has a specific prompt assigned
		if objectPrompt, exists := s.ObjectPrompts[objectUUID]; exists && objectPrompt != nil {
			log.Infof("Using object-specific prompt for TRIGGER %s: %s (ID: %d), taskId: %d",
				objectUUID, objectPrompt.PromptTitle, objectPrompt.ID, s.taskId)
			return objectPrompt
		}

		// Fall back to default TRIGGER prompt
		if s.DefaultTriggerPrompt != nil {
			log.Infof("Using default TRIGGER prompt for object %s: %s (ID: %d), taskId: %d",
				objectUUID, s.DefaultTriggerPrompt.PromptTitle, s.DefaultTriggerPrompt.ID, s.taskId)
			return s.DefaultTriggerPrompt
		}

		// If no default TRIGGER prompt exists, fall back to default CODE prompt
		if s.DefaultPrompt != nil {
			log.Warnf("No default TRIGGER prompt found for object %s, using default CODE prompt: %s (ID: %d), taskId: %d",
				objectUUID, s.DefaultPrompt.PromptTitle, s.DefaultPrompt.ID, s.taskId)
			return s.DefaultPrompt
		}

		// Critical error - no default prompts at all
		log.Errorf("No default prompts available for TRIGGER object %s, taskId: %d", objectUUID, s.taskId)
		return nil
	}

	// For non-TRIGGER objects: check object-specific prompt first
	if objectPrompt, exists := s.ObjectPrompts[objectUUID]; exists && objectPrompt != nil {
		log.Debugf("Using object-specific prompt for object %s, taskId: %d", objectUUID, s.taskId)
		return objectPrompt
	}

	// For all other object types (FUNCTION, PROCEDURE, PACKAGE BODY, TYPE), use CODE default prompt
	if s.DefaultPrompt != nil {
		log.Debugf("Using default CODE prompt for object %s, taskId: %d", objectUUID, s.taskId)
		return s.DefaultPrompt
	}

	// Critical error - no default CODE prompt
	log.Errorf("No default CODE prompt available for object %s, taskId: %d", objectUUID, s.taskId)
	return nil
}

// GetEntityPromptForFramework returns the entity generation prompt for a specific framework
func (s *ConversionPromptStrategy) GetEntityPromptForFramework(framework string) *objectparser.OracleObjectTransformationPrompt {
	if framework == "" {
		return nil
	}
	return s.EntityPrompts[strings.ToLower(framework)]
}

// GetTriggerPromptForFramework returns the trigger conversion prompt for a specific framework
func (s *ConversionPromptStrategy) GetTriggerPromptForFramework(framework string) *objectparser.OracleObjectTransformationPrompt {
	if framework == "" {
		return nil
	}
	return s.TriggerPrompts[strings.ToLower(framework)]
}

// ShouldUseEntityPrompt determines if entity generation is needed for a framework
func (s *ConversionPromptStrategy) ShouldUseEntityPrompt(framework string) bool {
	log.Infof("Evaluating entity prompt need for framework: %s, taskId: %d", framework, s.taskId)

	if framework == "" {
		log.Infof("Framework is empty, no entity prompt needed, taskId: %d", s.taskId)
		return false
	}

	// Check if we have an entity prompt for this framework
	_, hasEntityPrompt := s.EntityPrompts[strings.ToLower(framework)]
	log.Infof("Framework %s has dedicated entity prompt: %v, taskId: %d", framework, hasEntityPrompt, s.taskId)


	log.Infof("Framework %s final entity prompt decision: %v, taskId: %d", framework, hasEntityPrompt, s.taskId)
	return hasEntityPrompt
}


// GetAllFrameworks returns all detected frameworks
func (s *ConversionPromptStrategy) GetAllFrameworks() []string {
	frameworks := make([]string, 0, len(s.DetectedFrameworks))
	for framework := range s.DetectedFrameworks {
		frameworks = append(frameworks, framework)
	}
	return frameworks
}

// GetPrimaryFramework returns the primary framework based on priority
func (s *ConversionPromptStrategy) GetPrimaryFramework() string {
	// Check frameworks in priority order
	for _, priorityFramework := range s.FrameworkPriority {
		if s.DetectedFrameworks[priorityFramework] {
			return priorityFramework
		}
	}

	// If no priority framework found, return the first detected one
	for framework := range s.DetectedFrameworks {
		return framework
	}

	return ""
}

// DEPRECATED: DetectFrameworks ～analyzes all prompts to detect frameworks in use
func (s *ConversionPromptStrategy) DetectFrameworks() {
	//// Clear existing detections to ensure fresh analysis
	//s.DetectedFrameworks = make(map[string]bool)
	//
	//// Detect from entity prompts
	//for framework := range s.EntityPrompts {
	//	s.registerFramework(framework)
	//}
	//
	//// Detect from trigger prompts
	//for framework := range s.TriggerPrompts {
	//	s.registerFramework(framework)
	//}
	//
	//// Detect from object prompts (avoiding duplicates)
	//processedPrompts := make(map[uint]bool)
	//for _, prompt := range s.ObjectPrompts {
	//	if processedPrompts[prompt.ID] {
	//		continue
	//	}
	//	processedPrompts[prompt.ID] = true
	//	s.registerFramework(prompt.TargetFramework)
	//}
	//
	//log.Infof("Detected %d frameworks: %v, taskId: %d", len(s.DetectedFrameworks), s.GetAllFrameworks(), s.taskId)
}

// GetFrameworkForObject determines the framework for a specific object
func (s *ConversionPromptStrategy) GetFrameworkForObject(objectUUID string) string {
	// Check if object has a specific prompt with framework
	if objectPrompt, exists := s.ObjectPrompts[objectUUID]; exists {
		if objectPrompt.TargetFramework != "" && !strings.EqualFold(objectPrompt.TargetFramework, "java") {
			return strings.ToLower(objectPrompt.TargetFramework)
		}
	}

	// Return primary framework as fallback
	return s.GetPrimaryFramework()
}

// ValidateStrategy checks if the strategy configuration is valid
func (s *ConversionPromptStrategy) ValidateStrategy() error {
	var errors []string

	if s.DefaultPrompt == nil {
		errors = append(errors, "default CODE prompt is required")
	}

	// Validate prompts are not nil
	for uuid, prompt := range s.ObjectPrompts {
		if prompt == nil {
			errors = append(errors, fmt.Sprintf("nil prompt for object UUID: %s", uuid))
		}
	}

	// Check for framework consistency
	for framework, entityPrompt := range s.EntityPrompts {
		if entityPrompt == nil {
			errors = append(errors, fmt.Sprintf("nil entity prompt for framework: %s", framework))
			continue
		}
		if !strings.EqualFold(entityPrompt.TargetFramework, framework) &&
			entityPrompt.TargetFramework != "" {
			log.Warnf("Framework mismatch for entity prompt: map key=%s, prompt framework=%s, taskId: %d",
				framework, entityPrompt.TargetFramework, s.taskId)
		}
	}

	for framework, triggerPrompt := range s.TriggerPrompts {
		if triggerPrompt == nil {
			errors = append(errors, fmt.Sprintf("nil trigger prompt for framework: %s", framework))
			continue
		}
		if !strings.EqualFold(triggerPrompt.TargetFramework, framework) &&
			triggerPrompt.TargetFramework != "" {
			log.Warnf("Framework mismatch for trigger prompt: map key=%s, prompt framework=%s, taskId: %d",
				framework, triggerPrompt.TargetFramework, s.taskId)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("validation errors: %s", strings.Join(errors, "; "))
	}
	return nil
}

// LogStrategyStatistics logs detailed statistics about the prompt strategy
func (s *ConversionPromptStrategy) LogStrategyStatistics(taskId int) {
	log.Infof("Prompt Strategy Statistics, taskId: %d:", s.taskId)
	log.Infof("  - Default CODE prompt: %s (ID: %d), taskId: %d", s.DefaultPrompt.PromptTitle, s.DefaultPrompt.ID, s.taskId)
	if s.DefaultTriggerPrompt != nil {
		log.Infof("  - Default TRIGGER prompt: %s (ID: %d), taskId: %d", s.DefaultTriggerPrompt.PromptTitle, s.DefaultTriggerPrompt.ID, s.taskId)
	} else {
		log.Infof("  - Default TRIGGER prompt: none, taskId: %d", s.taskId)
	}
	log.Infof("  - Entity prompts: %d frameworks, taskId: %d", len(s.EntityPrompts), s.taskId)
	for framework, prompt := range s.EntityPrompts {
		log.Infof("    - %s: %s (ID: %d), taskId: %d", framework, prompt.PromptTitle, prompt.ID, s.taskId)
	}
	log.Infof("  - Trigger prompts: %d frameworks, taskId: %d", len(s.TriggerPrompts), s.taskId)
	for framework, prompt := range s.TriggerPrompts {
		log.Infof("    - %s: %s (ID: %d), taskId: %d", framework, prompt.PromptTitle, prompt.ID, s.taskId)
	}
	log.Infof("  - Object-specific prompts: %d objects, taskId: %d", len(s.ObjectPrompts), s.taskId)
	log.Infof("  - Detected frameworks: %v, taskId: %d", s.GetAllFrameworks(), s.taskId)
	log.Infof("  - Has triggers: %v, taskId: %d", s.HasTriggers, s.taskId)
	log.Infof("  - Primary framework: %s, taskId: %d", s.GetPrimaryFramework(), s.taskId)
}

// Clone creates a deep copy of the strategy
func (s *ConversionPromptStrategy) Clone() *ConversionPromptStrategy {
	strategy := &ConversionPromptStrategy{
		taskId:               s.taskId,
		EntityPrompts:        make(map[string]*objectparser.OracleObjectTransformationPrompt),
		ObjectPrompts:        make(map[string]*objectparser.OracleObjectTransformationPrompt),
		TriggerPrompts:       make(map[string]*objectparser.OracleObjectTransformationPrompt),
		DefaultPrompt:        s.DefaultPrompt,
		DefaultTriggerPrompt: s.DefaultTriggerPrompt,
		DetectedFrameworks:   make(map[string]bool),
		HasTriggers:          s.HasTriggers,
		FrameworkPriority:    append([]string{}, s.FrameworkPriority...),
	}

	// Deep copy maps
	for k, v := range s.EntityPrompts {
		strategy.EntityPrompts[k] = v
	}
	for k, v := range s.ObjectPrompts {
		strategy.ObjectPrompts[k] = v
	}
	for k, v := range s.TriggerPrompts {
		strategy.TriggerPrompts[k] = v
	}
	for k, v := range s.DetectedFrameworks {
		strategy.DetectedFrameworks[k] = v
	}

	return strategy
}

// Helper methods for reducing code duplication

// registerFramework registers a framework if it's valid and not Java
func (s *ConversionPromptStrategy) registerFramework(framework string) {
	if framework != "" && !strings.EqualFold(framework, constants.OPTargetFrameworkJava) {
		s.DetectedFrameworks[strings.ToLower(framework)] = true
	}
}



// getDefaultPromptForType returns the appropriate default prompt based on object type
func (s *ConversionPromptStrategy) getDefaultPromptForType(objectType string) *objectparser.OracleObjectTransformationPrompt {
	if objectType == "TRIGGER" && s.DefaultTriggerPrompt != nil {
		return s.DefaultTriggerPrompt
	}
	return s.DefaultPrompt
}
