package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// ProgressUpdater interface for progress tracking
type ProgressUpdater interface {
	Update(ctx context.Context, index int, success bool, itemDesc string)
	ForceLog(ctx context.Context, message string)
}

// ProgressTracker tracks and reports progress for long-running operations
type ProgressTracker struct {
	// Configuration
	progressLogger ProgressLogger
	logType        string
	logStep        string
	timeInterval   time.Duration
	countInterval  int

	// State
	mu                sync.Mutex
	startTime         time.Time
	lastProgressTime  time.Time
	lastProgressCount int
	totalItems        int
	currentIndex      int
	successCount      int
	failedCount       int

	// Current item info (optional)
	currentItemDesc string
}

// NewProgressTracker creates a new progress tracker
func NewProgressTracker(logger ProgressLogger, logType, logStep string, totalItems int) *ProgressTracker {
	now := time.Now()
	return &ProgressTracker{
		progressLogger:    logger,
		logType:           logType,
		logStep:           logStep,
		timeInterval:      10 * time.Second,
		countInterval:     100,
		startTime:         now,
		lastProgressTime:  now,
		lastProgressCount: 0,
		totalItems:        totalItems,
	}
}

// SetIntervals allows customization of progress intervals
func (pt *ProgressTracker) SetIntervals(timeInterval time.Duration, countInterval int) {
	pt.mu.Lock()
	defer pt.mu.Unlock()
	pt.timeInterval = timeInterval
	pt.countInterval = countInterval
}

// Update updates the progress and logs if necessary
func (pt *ProgressTracker) Update(ctx context.Context, index int, success bool, itemDesc string) {
	pt.mu.Lock()
	defer pt.mu.Unlock()

	pt.currentIndex = index
	pt.currentItemDesc = itemDesc

	if success {
		pt.successCount++
	} else {
		pt.failedCount++
	}

	// Check if we should log progress
	shouldLog := false

	// Time-based trigger
	if time.Since(pt.lastProgressTime) >= pt.timeInterval {
		shouldLog = true
	}

	// Count-based trigger
	processedSinceLastLog := (pt.successCount + pt.failedCount) - pt.lastProgressCount
	if processedSinceLastLog >= pt.countInterval {
		shouldLog = true
	}

	// Always log at the end
	if pt.successCount+pt.failedCount >= pt.totalItems {
		shouldLog = true
	}

	if shouldLog && pt.progressLogger != nil {
		pt.logProgress(ctx)
		pt.lastProgressTime = time.Now()
		pt.lastProgressCount = pt.successCount + pt.failedCount
	}
}

// ForceLog forces a progress log regardless of intervals
func (pt *ProgressTracker) ForceLog(ctx context.Context, message string) {
	pt.mu.Lock()
	defer pt.mu.Unlock()

	if pt.progressLogger != nil {
		pt.progressLogger.AppendProgressLog(ctx, constants.BuildProgressLog(
			pt.logType, pt.logStep, message))
	}
}

// logProgress logs the current progress
func (pt *ProgressTracker) logProgress(ctx context.Context) {
	processed := pt.successCount + pt.failedCount
	elapsed := time.Since(pt.startTime)
	rate := float64(processed) / elapsed.Seconds()
	percentage := float64(processed) * 100 / float64(pt.totalItems)

	message := fmt.Sprintf("progress: %d/%d (%.1f%%), success: %d, failed: %d, rate: %.1f items/sec",
		processed, pt.totalItems, percentage,
		pt.successCount, pt.failedCount, rate)

	if pt.currentItemDesc != "" {
		message += fmt.Sprintf(", current: %s", pt.currentItemDesc)
	}

	// Log to file system only (debug level), not to database progress log
	log.Debugf("[%s] %s", pt.logStep, message)
}

// GetStats returns current statistics
func (pt *ProgressTracker) GetStats() (success, failed, total int, elapsed time.Duration) {
	pt.mu.Lock()
	defer pt.mu.Unlock()
	return pt.successCount, pt.failedCount, pt.totalItems, time.Since(pt.startTime)
}

// GetSuccessCount returns the current success count
func (pt *ProgressTracker) GetSuccessCount() int {
	pt.mu.Lock()
	defer pt.mu.Unlock()
	return pt.successCount
}

// GetFailedCount returns the current failed count
func (pt *ProgressTracker) GetFailedCount() int {
	pt.mu.Lock()
	defer pt.mu.Unlock()
	return pt.failedCount
}
