package core

import (
	"fmt"
	"strings"
	"testing"

	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
)

// generateTestDependencies creates test dependencies for benchmarking
func generateTestDependencies(count int) []*objectparser.OracleDependency {
	deps := make([]*objectparser.OracleDependency, count)
	for i := 0; i < count; i++ {
		deps[i] = &objectparser.OracleDependency{
			UUID:              fmt.Sprintf("uuid-%d", i),
			SchemaName:        fmt.Sprintf("SCHEMA_%d", i%10),
			PackageName:       fmt.Sprintf("PACKAGE_%d", i%100),
			OwnerName:         fmt.Sprintf("OWNER_%d", i%10),
			Type:              "PROCEDURE",
			Name:              fmt.Sprintf("PROC_%d", i),
			ReferencedOwner:   fmt.Sprintf("REF_OWNER_%d", (i+1)%10),
			ReferencedName:    fmt.Sprintf("REF_PROC_%d", (i+1)%count),
			ReferencedType:    "FUNCTION",
			IsFromPackageBody: i%3 == 0,
		}
	}
	return deps
}

// generateTestObjectStatus creates test object status map
func generateTestObjectStatus(count int) map[string]string {
	statusMap := make(map[string]string)
	for i := 0; i < count; i++ {
		key := fmt.Sprintf("SCHEMA_%d.PROCEDURE.PROC_%d", i%10, i)
		statusMap[key] = "VALID"
		if i%5 == 0 {
			statusMap[key] = "INVALID"
		}
	}
	return statusMap
}

// generateTestFilter creates test object filter map
func generateTestFilter(count int) map[string]bool {
	filterMap := make(map[string]bool)
	// Add 20% of objects to filter
	for i := 0; i < count/5; i++ {
		key := fmt.Sprintf("SCHEMA_%d.PACKAGE_%d.PROCEDURE.PROC_%d", i%10, i%100, i)
		filterMap[key] = true
	}
	return filterMap
}

// BenchmarkBuildTreeNodeChildrenPointers benchmarks different sizes and implementations
func BenchmarkBuildTreeNodeChildrenPointers(b *testing.B) {
	sizes := []int{10000, 50000, 100000}

	for _, size := range sizes {
		// Prepare test data
		deps := generateTestDependencies(size)
		statusMap := generateTestObjectStatus(size)
		filterMap := generateTestFilter(size)
		targetSchemas := []string{"SCHEMA_0", "SCHEMA_1", "SCHEMA_2", "SCHEMA_3", "SCHEMA_4"}

		// Benchmark old implementation
		b.Run(fmt.Sprintf("Old_%d", size), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				helper := NewOracleDependencyHelper(deps, targetSchemas)
				helper.SetOracleObjectStatusListFromDetail(nil, false)
				helper.objectStatusMap = statusMap
				helper.objectFilterMap = filterMap

				_ = helper.BuildTreeNodeChildrenPointersOld()
			}
		})

		// Benchmark new optimized implementation
		b.Run(fmt.Sprintf("New_%d", size), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				helper := NewOracleDependencyHelper(deps, targetSchemas)
				helper.SetOracleObjectStatusListFromDetail(nil, false)
				helper.objectStatusMap = statusMap
				helper.objectFilterMap = filterMap

				_ = helper.BuildTreeNodeChildrenPointers()
			}
		})
	}
}

// BenchmarkBuildTreeNodeChildrenPointersMemory benchmarks memory allocations
func BenchmarkBuildTreeNodeChildrenPointersMemory(b *testing.B) {
	sizes := []int{1000, 10000, 100000}

	for _, size := range sizes {
		deps := generateTestDependencies(size)
		statusMap := generateTestObjectStatus(size)
		filterMap := generateTestFilter(size)
		targetSchemas := []string{"SCHEMA_0", "SCHEMA_1", "SCHEMA_2", "SCHEMA_3", "SCHEMA_4"}

		// Memory benchmark for old implementation
		b.Run(fmt.Sprintf("Old_Mem_%d", size), func(b *testing.B) {
			b.ReportAllocs()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				helper := NewOracleDependencyHelper(deps, targetSchemas)
				helper.SetOracleObjectStatusListFromDetail(nil, false)
				helper.objectStatusMap = statusMap
				helper.objectFilterMap = filterMap

				_ = helper.BuildTreeNodeChildrenPointersOld()
			}
		})

		// Memory benchmark for new implementation
		b.Run(fmt.Sprintf("New_Mem_%d", size), func(b *testing.B) {
			b.ReportAllocs()
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				helper := NewOracleDependencyHelper(deps, targetSchemas)
				helper.SetOracleObjectStatusListFromDetail(nil, false)
				helper.objectStatusMap = statusMap
				helper.objectFilterMap = filterMap

				_ = helper.BuildTreeNodeChildrenPointers()
			}
		})
	}
}

// BenchmarkStringConcatenation compares string concatenation methods
func BenchmarkStringConcatenation(b *testing.B) {
	schema := "TEST_SCHEMA"
	owner := "TEST_OWNER"

	b.Run("PlusOperator", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = schema + ".PACKAGE BODY." + owner
		}
	})

	b.Run("StringBuilder", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			var sb strings.Builder
			sb.WriteString(schema)
			sb.WriteString(".PACKAGE BODY.")
			sb.WriteString(owner)
			_ = sb.String()
		}
	})

	b.Run("Sprintf", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = fmt.Sprintf("%s.PACKAGE BODY.%s", schema, owner)
		}
	})
}

// BenchmarkFilterCheck compares filtering approaches
func BenchmarkFilterCheck(b *testing.B) {
	helper := &OracleDependencyHelper{
		objectFilterMap: generateTestFilter(10000),
	}

	deps := generateTestDependencies(1000)

	b.Run("WithFilterCheck", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			for _, dep := range deps {
				_ = helper.isObjectBeenFiltered(dep)
			}
		}
	})
}
