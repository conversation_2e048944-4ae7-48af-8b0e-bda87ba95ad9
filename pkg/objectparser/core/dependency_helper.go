package core

import (
	"fmt"
	"sort"
	"strings"
	"sync"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

type IdGenerator struct {
	*sync.RWMutex
	counter int64
	prefix  string
}

func NewIdGenerator(prefix string) *IdGenerator {
	if prefix == "" {
		prefix = "T"
	}
	return &IdGenerator{
		RWMutex: &sync.RWMutex{},
		counter: 0,
		prefix:  prefix,
	}
}

func NewDefaultIdGenerator() *IdGenerator {
	return NewIdGenerator("T")
}

func (i *IdGenerator) GetNextId() string {
	i.Lock()
	defer i.Unlock()
	i.counter++
	return fmt.Sprintf("%s:%d", i.prefix, i.counter)
}

type OracleDependencyHelper struct {
	oracleDependencies []*objectparser.OracleDependency
	targetSchemaMap    map[string]bool
	objectStatusMap    map[string]string

	objectFilterMap    map[string]bool
	objectCfgFilterMap map[string]bool
	dbLinkObjects      map[string]bool
	objectKeyMap       map[string]bool
	idGenerator        *IdGenerator

	includePromptRelation bool
	excludeInvalidObject  bool
	promptMap             map[string]structs.OracleObjectPrompt
	defaultPrompt         *structs.OracleObjectPrompt
	defaultTriggerPrompt  *structs.OracleObjectPrompt
}

func NewOracleDependencyHelper(items []*objectparser.OracleDependency, targetSchemas []string) OracleDependencyHelper {
	targetSchemaMap := make(map[string]bool)
	for _, schema := range targetSchemas {
		targetSchemaMap[schema] = true
	}
	return OracleDependencyHelper{
		oracleDependencies: items,
		targetSchemaMap:    targetSchemaMap,
		idGenerator:        NewDefaultIdGenerator(),
	}
}

func (i *OracleDependencyHelper) IsSchemaOwnerMatched(owner string) bool {
	if _, ok := i.targetSchemaMap[owner]; ok {
		return true
	}
	return false
}

func (i *OracleDependencyHelper) SetOracleObjectStatusList(oracleObjectStatusList []datasource.OracleObject) {
	objectMap := make(map[string]string)
	for _, item := range oracleObjectStatusList {
		objectMap[item.OwnerName+"."+item.ObjectName+"."+item.ObjectType] = item.Status
	}
	i.objectStatusMap = objectMap
}

func (i *OracleDependencyHelper) SetOracleObjectStatusListFromDetail(details []*objectparser.OracleObjectDefinitionAnalyzeDetail, excludeInvalidObject bool) {
	objectMap := make(map[string]string)
	for _, item := range details {
		objectMap[item.SchemaName+"."+item.ObjectType+"."+item.ObjectName] = item.ObjectStatus
	}
	i.objectStatusMap = objectMap
	i.excludeInvalidObject = excludeInvalidObject
}

func (i *OracleDependencyHelper) GetStatus() string {
	return fmt.Sprintf("Helper[objectStatusMap:%d, oracleDependencies:%d, targetSchemaMap:%d, objectFilterMap:%d, dbLinkObjects:%d, objectKeyMap:%d]",
		i.GetObjectStatusMapCount(), i.GetOracleDependenciesCount(), i.GetTargetSchemaMapCount(), i.GetObjectFilterMapCount(), i.GetDbLinkObjectsCount(), i.GetObjectKeyMapCount())
}

func (i *OracleDependencyHelper) GetObjectStatusMapCount() int {
	return len(i.objectStatusMap)
}

func (i *OracleDependencyHelper) GetOracleDependenciesCount() int {
	return len(i.oracleDependencies)
}

func (i *OracleDependencyHelper) GetTargetSchemaMapCount() int {
	return len(i.targetSchemaMap)
}

func (i *OracleDependencyHelper) GetObjectFilterMapCount() int {
	return len(i.objectFilterMap)
}

func (i *OracleDependencyHelper) GetDbLinkObjectsCount() int {
	return len(i.dbLinkObjects)
}

func (i *OracleDependencyHelper) GetObjectKeyMapCount() int {
	return len(i.objectKeyMap)
}

func (i *OracleDependencyHelper) appendParentToObjectFilterIfNeed(schemaName, packageName string) {
	if packageName != "" {
		i.objectFilterMap[schemaName+"."+""+"."+"PACKAGE BODY"+"."+packageName] = true
	}
	i.objectFilterMap["SCHEMA:"+schemaName] = true
}

func (i *OracleDependencyHelper) SetOracleObjectFilter(objectFilters []message.ObjectProperty) {
	if i.objectFilterMap == nil {
		i.objectFilterMap = make(map[string]bool)
	}
	for _, item := range objectFilters {
		i.objectFilterMap[item.Schema+"."+item.Package+"."+item.Type+"."+item.Title] = true
		i.appendParentToObjectFilterIfNeed(item.Schema, item.Package)
	}
}

func (i *OracleDependencyHelper) SetOracleObjectCfgFilter(objectFilterCfgs []*objectparser.ObjectParserCfg) {
	if len(objectFilterCfgs) == 0 {
		return
	}
	if i.objectCfgFilterMap == nil {
		i.objectCfgFilterMap = make(map[string]bool)
	}
	for _, cfg := range objectFilterCfgs {
		objectType := cfg.ObjectType
		if objectType == constants.OracleObjectTypePackage {
			objectType = constants.OracleObjectTypePackageBody
		}
		// 使用3段式的键：schema.type.name
		key := cfg.SchemaName + "." + objectType + "." + cfg.ObjectName
		i.objectCfgFilterMap[key] = true
	}
}

func (i *OracleDependencyHelper) SetOracleObjectUUIDFilter(uuid []string) {
	if i.objectFilterMap == nil {
		i.objectFilterMap = make(map[string]bool)
	}
	objectUUIDFilterMap := make(map[string]bool)
	for _, item := range uuid {
		objectUUIDFilterMap[strings.TrimSpace(item)] = true
	}
	for _, item := range i.oracleDependencies {
		if objectUUIDFilterMap[item.UUID] {
			i.objectFilterMap[item.GetSchemaName()+"."+item.GetPackageName()+"."+item.GetType()+"."+item.GetName()] = true
			i.appendParentToObjectFilterIfNeed(item.GetSchemaName(), item.GetPackageName())
		}
	}
}

func (i *OracleDependencyHelper) SetOracleObjectKeyFilter(keys []string) {
	if i.objectKeyMap == nil {
		i.objectKeyMap = make(map[string]bool)
	}
	for _, key := range keys {
		i.objectKeyMap[key] = true
	}
}

func (i *OracleDependencyHelper) getPackageOwnerUniqueKey(ownerName, packageName string) string {
	return ownerName + ".PACKAGE BODY." + packageName
}

func (i *OracleDependencyHelper) isObjectBeenFiltered(item *objectparser.OracleDependency) bool {
	// Short-circuit if no filters are configured
	if len(i.objectFilterMap) == 0 {
		return false
	}
	uniqueKey := item.UniqueKeyWithPackageName()
	_, ok := i.objectFilterMap[uniqueKey]
	return !ok
}

// BuildTreeNodeChildrenPointers builds a map of dependency tree nodes from oracle dependencies
// Performance optimizations:
// - Pre-allocated map with estimated capacity
// - Single map lookups using comma-ok idiom
// - Nil slices for empty children to save memory
// - Early filtering check to avoid unnecessary string operations
// - strings.Builder for efficient string concatenation
// For datasets with 100k+ dependencies, consider using BuildTreeNodeChildrenPointersParallel
// BuildTreeNodeChildrenPointersOld is the original unoptimized implementation for benchmarking comparison
func (i *OracleDependencyHelper) BuildTreeNodeChildrenPointersOld() map[string]*structs.DependencyTreeNode {
	nodeMap := make(map[string]*structs.DependencyTreeNode)
	for _, dependency := range i.oracleDependencies {
		isFiltered := i.isObjectBeenFiltered(dependency)
		if isFiltered {
			continue
		}

		uniqueKey := dependency.UniqueKey()
		if _, ok := nodeMap[uniqueKey]; !ok {
			objectStatus, exist := i.objectStatusMap[dependency.UniqueKey()]

			if dependency.IsFromPackageBody && !exist {
				packageOwnerUK := i.getPackageOwnerUniqueKey(dependency.GetSchemaName(), dependency.GetOwnerName())
				objectStatus = i.objectStatusMap[packageOwnerUK]
				exist = true
			}

			nodeMap[uniqueKey] = &structs.DependencyTreeNode{
				SchemaName:         dependency.GetSchemaName(),
				PackageName:        dependency.GetPackageName(),
				UUID:               dependency.GetUUID(),
				Owner:              dependency.GetOwnerName(),
				Name:               dependency.GetName(),
				Type:               dependency.GetType(),
				Status:             objectStatus,
				ChildrenUniqueKeys: make([]string, 0),
				Children:           make([]*structs.DependencyTreeNode, 0),
				IsFromPackageBody:  dependency.GetIsFromPackageBody(),
			}
		}
		nodeMap[uniqueKey].ChildrenUniqueKeys = append(nodeMap[uniqueKey].ChildrenUniqueKeys,
			dependency.ReferencedUniqueKey(),
			dependency.ReferencedSchemaAsOwnerUniqueKey(),
		)
	}

	for nodeKey, node := range nodeMap {
		nodeMap[nodeKey].Children = i.sortAndUniqueDependencyTreeNodeList(node.Children)
	}

	return nodeMap
}

// BuildTreeNodeChildrenPointers is an optimized parallel version for large datasets (100k+ dependencies)
func (i *OracleDependencyHelper) BuildTreeNodeChildrenPointers() map[string]*structs.DependencyTreeNode {
	//return i.BuildTreeNodeChildrenPointersOld()U
	depCount := len(i.oracleDependencies)

	// Determine optimal number of workers
	numWorkers := 4
	if depCount > 50000 {
		numWorkers = 8
	}
	if depCount > 200000 {
		numWorkers = 16
	}

	// Create channels for work distribution
	type workItem struct {
		start, end int
	}
	workChan := make(chan workItem, numWorkers)
	resultChan := make(chan map[string]*structs.DependencyTreeNode, numWorkers)

	// Check if we have filters
	hasFilters := len(i.objectFilterMap) > 0

	// Worker function
	worker := func() {
		for work := range workChan {
			localMap := make(map[string]*structs.DependencyTreeNode)

			for idx := work.start; idx < work.end && idx < depCount; idx++ {
				dependency := i.oracleDependencies[idx]

				// Optimized filtering check
				if hasFilters {
					uniqueKey := dependency.UniqueKeyWithPackageName()
					if _, ok := i.objectFilterMap[uniqueKey]; !ok {
						continue
					}
				}

				uniqueKey := dependency.UniqueKey()
				node, exists := localMap[uniqueKey]

				if !exists {
					// Single lookup for object status
					objectStatus, exist := i.objectStatusMap[uniqueKey]

					if dependency.IsFromPackageBody && !exist {
						var sb strings.Builder
						sb.WriteString(dependency.GetSchemaName())
						sb.WriteString(".PACKAGE BODY.")
						sb.WriteString(dependency.GetOwnerName())
						packageOwnerUK := sb.String()
						objectStatus = i.objectStatusMap[packageOwnerUK]
					}

					node = &structs.DependencyTreeNode{
						SchemaName:         dependency.GetSchemaName(),
						PackageName:        dependency.GetPackageName(),
						UUID:               dependency.GetUUID(),
						Owner:              dependency.GetOwnerName(),
						Name:               dependency.GetName(),
						Type:               dependency.GetType(),
						Status:             objectStatus,
						ChildrenUniqueKeys: make([]string, 0, 8),
						Children:           nil,
						IsFromPackageBody:  dependency.GetIsFromPackageBody(),
					}
					localMap[uniqueKey] = node
				}

				node.ChildrenUniqueKeys = append(node.ChildrenUniqueKeys,
					dependency.ReferencedUniqueKey(),
					dependency.ReferencedSchemaAsOwnerUniqueKey(),
				)
			}

			resultChan <- localMap
		}
	}

	// Start workers
	var wg sync.WaitGroup
	for w := 0; w < numWorkers; w++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			worker()
		}()
	}

	// Distribute work
	chunkSize := (depCount + numWorkers - 1) / numWorkers
	for i := 0; i < depCount; i += chunkSize {
		workChan <- workItem{start: i, end: i + chunkSize}
	}
	close(workChan)

	// Wait for workers and close result channel
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Merge results
	finalMap := make(map[string]*structs.DependencyTreeNode, depCount/3)
	for localMap := range resultChan {
		for key, node := range localMap {
			if existingNode, exists := finalMap[key]; exists {
				// Merge child keys
				existingNode.ChildrenUniqueKeys = append(existingNode.ChildrenUniqueKeys, node.ChildrenUniqueKeys...)
			} else {
				finalMap[key] = node
			}
		}
	}

	return finalMap
}

func (i *OracleDependencyHelper) MakeTreeNodeChildrenByPointers(nodes map[string]*structs.DependencyTreeNode) {
	for _, node := range nodes {
		for _, childrenUniqueKey := range node.ChildrenUniqueKeys {
			if _, ok := nodes[childrenUniqueKey]; !ok {
				continue
			}
			children := nodes[childrenUniqueKey]
			if node.GetType() == "PACKAGE BODY" {
				if children.GetPackageName() == "" {
					continue
				}
			}

			node.Children = append(node.Children, children)
		}
	}
}

func (i *OracleDependencyHelper) FilterTreeNodeType(nodes map[string]*structs.DependencyTreeNode) {
	// 'FUNCTION', 'PACKAGE BODY', 'PROCEDURE', 'TRIGGER', 'PACKAGE', 'TYPE'

	whiteTypeMap := map[string]bool{
		"FUNCTION":     true,
		"PACKAGE BODY": true,
		"PROCEDURE":    true,
		"TRIGGER":      true,
		"TYPE":         true,
	}

	for nodeKey, node := range nodes {
		if whiteTypeMap[node.Type] {
			continue
		}
		delete(nodes, nodeKey)
	}
}

func (i *OracleDependencyHelper) FilterTreeNodeWithTargetSchema(nodes map[string]*structs.DependencyTreeNode) {
	for nodeKey, node := range nodes {
		if node.IsFromPackageBody {
			continue
		}
		if _, ok := i.targetSchemaMap[node.Owner]; !ok {
			delete(nodes, nodeKey)
			continue
		}
	}
}

func (i *OracleDependencyHelper) PruneTreeNodeByFilter(nodes map[string]*structs.DependencyTreeNode) {
	deleteKeyMap := make(map[string]bool)
	if !i.isObjectCfgFilterNotExists() {
		for nodeKey, node := range nodes {
			nodeIdentifierKey := node.SchemaName + "." + node.Type + "." + node.Name
			if _, ok := i.objectCfgFilterMap[nodeIdentifierKey]; !ok {
				deleteKeyMap[nodeKey] = true
				continue
			}
		}
	}

	for deleteKey := range deleteKeyMap {
		delete(nodes, deleteKey)
	}

}

func (i *OracleDependencyHelper) isObjectFilterNotExists() bool {
	return len(i.objectFilterMap) == 0
}

func (i *OracleDependencyHelper) isObjectCfgFilterNotExists() bool {
	return len(i.objectCfgFilterMap) == 0
}

func (i *OracleDependencyHelper) OutputTreeNodes(nodeList []*structs.DependencyTreeNode, orderType int) []*structs.DependencyTreeNode {
	if i.isObjectFilterNotExists() {
		log.Infof("OutputTreeNodes, object filter not exists, start outputTreeNodesBySchema or Package")
		// 0 means schema, other means package
		if orderType == 0 {
			nodeList = i.outputTreeNodesBySchema(nodeList)
		} else {
			nodeList = i.outputTreeNodesBySchemaPackage(nodeList)
		}
	} else {
		log.Infof("OutputTreeNodes, object filter exists, skip outputTreeNodesBySchema or Package")
	}

	retDependencies := make([]*structs.DependencyTreeNode, 0)
	for _, node := range nodeList {
		node.Children = i.sortAndUniqueDependencyTreeNodeList(node.Children)
		retDependencies = append(retDependencies, node)
	}
	retDependencies = i.sortAndUniqueDependencyTreeNodeList(retDependencies)

	return retDependencies
}

func (i *OracleDependencyHelper) OutputToDisplayTree(ds []*structs.DependencyTreeNode) []*structs.DependencyTreeVO {
	retDs := make([]*structs.DependencyTreeVO, 0)
	for _, d := range ds {
		retDs = append(retDs, i.CloneToVO(d, 1, 0))
	}
	retDs = FilterTree(retDs, i.objectKeyMap, i.excludeInvalidObject)
	return retDs
}

// FilterTree filters the tree based on the keys array
func FilterTree(trees []*structs.DependencyTreeVO, keySet map[string]bool, excludeInvalidObject bool) []*structs.DependencyTreeVO {
	if len(keySet) == 0 {
		return trees
	}
	var dfs func(node *structs.DependencyTreeVO) *structs.DependencyTreeVO
	dfs = func(node *structs.DependencyTreeVO) *structs.DependencyTreeVO {
		if node == nil {
			return nil
		}

		// Check if this node or any of its children match the keys
		var filteredChildren []*structs.DependencyTreeVO
		for _, child := range node.Children {
			if filteredChild := dfs(child); filteredChild != nil {
				filteredChildren = append(filteredChildren, filteredChild)
			}
		}

		if excludeInvalidObject && node.Status == "INVALID" {
			return nil
		}

		// If the node's key matches or it has any filtered children, keep it
		if keySet[node.Key] || len(filteredChildren) > 0 {
			return &structs.DependencyTreeVO{
				Schema:       node.Schema,
				Package:      node.Package,
				Title:        node.Title,
				Type:         node.Type,
				Status:       node.Status,
				Key:          node.Key,
				UUID:         node.UUID,
				IsCycle:      node.IsCycle,
				IsReferenced: node.IsReferenced,
				HasDbLink:    node.HasDbLink,
				Prompt:       node.Prompt,
				Children:     filteredChildren,
				Level:        node.Level,
				Depth:        node.Depth,
				Owner:        node.Owner,
			}
		}

		// Otherwise, discard this node
		return nil
	}

	var result []*structs.DependencyTreeVO
	for _, tree := range trees {
		if filteredTree := dfs(tree); filteredTree != nil {
			result = append(result, filteredTree)
		}
	}
	return result
}

func (i *OracleDependencyHelper) getPromptOrDefault(node *structs.DependencyTreeNode) *structs.OracleObjectPrompt {
	if !i.includePromptRelation {
		return nil
	}
	// First check if there's a custom prompt for this object
	if prompt, ok := i.promptMap[node.UUID]; ok {
		return &prompt
	}
	
	// For TRIGGER objects, use TRIGGER default prompt if available
	if node.Type == "TRIGGER" && i.defaultTriggerPrompt != nil {
		return i.defaultTriggerPrompt
	}
	
	// For all other objects (FUNCTION, PROCEDURE, PACKAGE BODY, TYPE), use CODE default prompt
	if i.defaultPrompt != nil {
		return i.defaultPrompt
	}
	
	// No default prompt found
	return &structs.OracleObjectPrompt{
		RelationId:  0,
		PromptId:    0,
		PromptTitle: "prompt-not-found",
		PromptText:  "prompt-not-found",
	}
}

func (i *OracleDependencyHelper) CloneToVO(treeNode *structs.DependencyTreeNode, treeDepth, callDepth uint) *structs.DependencyTreeVO {
	vo := &structs.DependencyTreeVO{
		Schema:       treeNode.GetSchemaName(),
		Package:      treeNode.GetPackageName(),
		Owner:        treeNode.GetOwner(),
		Title:        treeNode.GetName(),
		Type:         treeNode.GetType(),
		Status:       treeNode.GetStatus(),
		IsCycle:      treeNode.GetIsCycle(),
		IsReferenced: callDepth >= 2,
		Depth:        treeDepth,
		Level:        callDepth,
		Key:          i.idGenerator.GetNextId(),
		UUID:         treeNode.GenerateUUIDIfNecessary(),
		Prompt:       i.getPromptOrDefault(treeNode),
		HasDbLink:    i.dbLinkObjects[treeNode.GetSchemaName()+"."+treeNode.GetType()+"."+treeNode.GetName()],
		Children:     make([]*structs.DependencyTreeVO, 0),
	}
	for _, child := range treeNode.Children {
		if treeNode.Type == "PACKAGE BODY" {
			vo.Children = append(vo.Children, i.CloneToVOWithRootPackageName(child, treeDepth+1, 1, treeNode.Name))
		} else {
			vo.Children = append(vo.Children, i.CloneToVO(child, treeDepth+1, callDepth+1))
		}
	}
	return vo
}

func (i *OracleDependencyHelper) CloneToVOWithRootPackageName(treeNode *structs.DependencyTreeNode, treeDepth, callDepth uint, rootPackageName string) *structs.DependencyTreeVO {
	title := treeNode.GetName()
	if treeNode.GetPackageName() != rootPackageName {
		title = treeNode.GetPackageName() + "." + treeNode.GetName()
	}
	treeNode.UniqueKey()

	vo := &structs.DependencyTreeVO{
		Schema:       treeNode.GetSchemaName(),
		Package:      treeNode.GetPackageName(),
		Owner:        treeNode.GetOwner(),
		Title:        title,
		Type:         treeNode.GetType(),
		Status:       treeNode.GetStatus(),
		IsCycle:      treeNode.GetIsCycle(),
		IsReferenced: callDepth >= 2,
		Depth:        treeDepth,
		Level:        callDepth,
		Key:          i.idGenerator.GetNextId(),
		UUID:         treeNode.GenerateUUIDIfNecessary(),
		Prompt:       i.getPromptOrDefault(treeNode),
		HasDbLink:    i.dbLinkObjects[treeNode.GetSchemaName()+"."+treeNode.GetType()+"."+treeNode.GetName()],
		Children:     make([]*structs.DependencyTreeVO, 0),
	}
	for _, child := range treeNode.Children {
		vo.Children = append(vo.Children, i.CloneToVOWithRootPackageName(child, treeDepth+1, callDepth+1, rootPackageName))
	}
	return vo
}

func (i *OracleDependencyHelper) OutputToDisplayGraph(ds []*structs.DependencyTreeNode) *structs.DependencyGraphVO {
	graphVo := &structs.DependencyGraphVO{
		Nodes: make([]structs.DependencyGraphNode, 0),
		Edges: make([]structs.DependencyGraphEdge, 0),
	}

	// 1. 如果当前节点是包体类型，则以包体名称为依据，关联的所有子节点，如果出现其他包，需要修改现实名称，如 PACKAGE_NAME.PROCEDURE_NAME
	// 2. 如果当前节点有包名，则以包名为依据，关联的所有子节点，如果出现其他包，需要修改现实名称，如 PACKAGE_NAME.PROCEDURE_NAME
	for _, d := range ds {
		if i.isPackageBodyType(d) {
			i.generateNodeAndEdgeWithRootPackageName(d, graphVo, 1, d.GetName())
		} else if d.HasPackageName() {
			i.generateNodeAndEdgeWithRootPackageName(d, graphVo, 1, d.GetPackageName())
		} else {
			i.generateNodeAndEdge(d, graphVo, 0)
		}
	}

	return graphVo
}

func (i *OracleDependencyHelper) generateNodeAndEdge(d *structs.DependencyTreeNode, graphVo *structs.DependencyGraphVO, callDepth int) {
	if d == nil {
		return
	}
	graphVo.AddGraphNode(structs.DependencyGraphNode{
		ID:           d.UniqueKeyWithSchemaPackage(),
		Label:        d.GetName(),
		Status:       d.GetStatus(),
		Type:         d.GetType(),
		HasDbLink:    i.dbLinkObjects[d.GetSchemaName()+"."+d.GetType()+"."+d.GetName()],
		IsReferenced: callDepth >= 2,
	})
	for _, child := range d.GetChildren() {
		if i.isPackageBodyType(child) {
			i.generateNodeAndEdgeWithRootPackageName(child, graphVo, 1, child.GetName())
		} else {
			i.generateNodeAndEdge(child, graphVo, callDepth+1)
		}
		graphVo.AddGraphEdge(structs.DependencyGraphEdge{
			SourceID: d.UniqueKeyWithSchemaPackage(),
			TargetID: child.UniqueKeyWithSchemaPackage(),
		})
	}
}

func (i *OracleDependencyHelper) generateNodeAndEdgeWithRootPackageName(d *structs.DependencyTreeNode, graphVo *structs.DependencyGraphVO, callDepth int, rootPackageName string) {
	if d == nil {
		return
	}

	label := d.GetName()
	if d.GetPackageName() != rootPackageName && i.isNotPackageBodyType(d) {
		label = d.GetPackageName() + "." + d.GetName()
	}

	graphVo.AddGraphNode(structs.DependencyGraphNode{
		ID:           d.UniqueKeyWithSchemaPackage(),
		Label:        label,
		Status:       d.GetStatus(),
		Type:         d.GetType(),
		HasDbLink:    i.dbLinkObjects[d.GetSchemaName()+"."+d.GetType()+"."+d.GetName()],
		IsReferenced: callDepth >= 2,
	})
	for _, child := range d.GetChildren() {
		i.generateNodeAndEdgeWithRootPackageName(child, graphVo, callDepth+1, rootPackageName)
		graphVo.AddGraphEdge(structs.DependencyGraphEdge{
			SourceID: d.UniqueKeyWithSchemaPackage(),
			TargetID: child.UniqueKeyWithSchemaPackage(),
		})
	}
}

func (i *OracleDependencyHelper) isNotPackageBodyType(d *structs.DependencyTreeNode) bool {
	if d == nil {
		return false
	}
	return d.Type != "PACKAGE BODY"
}

func (i *OracleDependencyHelper) isPackageBodyType(d *structs.DependencyTreeNode) bool {
	if d == nil {
		return false
	}
	return d.Type == "PACKAGE BODY"
}

// BreakCycle breaks the detected cycle by copying all nodes in the cycle and removing the reference causing the cycle
func (i *OracleDependencyHelper) BreakCycle(lastNode *structs.DependencyTreeNode, lastNodeParent *structs.DependencyTreeNode) []*structs.DependencyTreeNode {
	if lastNode == nil || lastNodeParent == nil {
		return nil
	}

	// Step 1: Copy all nodes in the cycle
	var cycleNodes []*structs.DependencyTreeNode
	nodeMap := make(map[string]*structs.DependencyTreeNode) // To track already copied nodes
	current := lastNode

	for {
		// Generate the unique key for the current node
		nodeKey := fmt.Sprintf("%s:%s:%s", current.Owner, current.Name, current.Type)

		// If this node is already copied, stop processing
		if _, exists := nodeMap[nodeKey]; exists {
			break
		}

		// Copy the current node
		copiedNode := &structs.DependencyTreeNode{
			Owner:              current.Owner,
			Name:               current.Name,
			Type:               current.Type,
			ChildrenUniqueKeys: append([]string{}, current.ChildrenUniqueKeys...),
		}
		nodeMap[nodeKey] = copiedNode
		cycleNodes = append(cycleNodes, copiedNode)

		// If we are back at the starting node, break
		if current == lastNode && len(cycleNodes) > 1 {
			break
		}

		// Move to the next child
		if len(current.Children) > 0 {
			current = current.Children[0] // Assuming a single cycle path
		} else {
			break
		}
	}

	// Step 2: Break the cycle by removing the reference in the last node's parent
	for i, child := range lastNodeParent.Children {
		if child == lastNode {
			lastNodeParent.Children = append(lastNodeParent.Children[:i], lastNodeParent.Children[i+1:]...)
			break
		}
	}

	return cycleNodes
}

// FindCycle finds if there's a cycle in the dependency tree, outputs the last node in the cycle, its parent, and the cycle length
func (i *OracleDependencyHelper) FindCycle(root *structs.DependencyTreeNode) (*structs.DependencyTreeNode, *structs.DependencyTreeNode, int, bool) {
	visited := make(map[string]bool)
	stack := make(map[string]int) // Track the depth of each node in the recursion stack

	lastNode, lastNodeParent, cycleLength, hasCycle := i.detectCycle(root, nil, visited, stack, 0)
	return lastNode, lastNodeParent, cycleLength, hasCycle
}

// detectCycle detects if a cycle exists in the dependency tree and returns the last node in the cycle, its parent, and the cycle length
func (i *OracleDependencyHelper) detectCycle(node *structs.DependencyTreeNode, parent *structs.DependencyTreeNode, visited map[string]bool, stack map[string]int, depth int) (*structs.DependencyTreeNode, *structs.DependencyTreeNode, int, bool) {
	if node == nil {
		return nil, nil, 0, false
	}

	// Generate a unique key for the current node
	nodeKey := node.UniqueKeyWithCycle()

	// If the node is in the current recursion stack, a cycle is detected
	if entryDepth, exists := stack[nodeKey]; exists {
		return node, parent, depth - entryDepth, true // Cycle length is the difference in depth
	}

	// If the node has already been visited, skip it
	if visited[nodeKey] {
		return nil, nil, 0, false
	}

	// Mark the node as visited and add it to the stack with the current depth
	visited[nodeKey] = true
	stack[nodeKey] = depth

	// Traverse children
	for _, child := range node.Children {
		if lastNode, lastNodeParent, cycleLength, hasCycle := i.detectCycle(child, node, visited, stack, depth+1); hasCycle {
			return lastNode, lastNodeParent, cycleLength, true
		}
	}

	// Remove the node from the stack after processing
	delete(stack, nodeKey)
	return nil, nil, 0, false
}

func (i *OracleDependencyHelper) sortAndUniqueDependencyTreeNodeList(retDependencies []*structs.DependencyTreeNode) []*structs.DependencyTreeNode {
	retDependencies = lo.Uniq(retDependencies)
	sort.Slice(retDependencies, func(i, j int) bool {
		// order by owner,type,name
		if retDependencies[i].SchemaName != retDependencies[j].SchemaName {
			return retDependencies[i].SchemaName < retDependencies[j].SchemaName
		}
		if retDependencies[i].PackageName != retDependencies[j].PackageName {
			return retDependencies[i].PackageName < retDependencies[j].PackageName
		}
		if retDependencies[i].Owner != retDependencies[j].Owner {
			return retDependencies[i].Owner < retDependencies[j].Owner
		}
		if retDependencies[i].Type != retDependencies[j].Type {
			return retDependencies[i].Type < retDependencies[j].Type
		}
		return retDependencies[i].Name < retDependencies[j].Name
	})
	return retDependencies
}

func (i *OracleDependencyHelper) outputTreeNodesBySchemaPackage(nodeList []*structs.DependencyTreeNode) []*structs.DependencyTreeNode {
	schemaNodeMap := make(map[string]*structs.DependencyTreeNode)
	for _, node := range nodeList {
		if i.isNotPackageBodyType(node) {
			continue
		}
		if _, ok := schemaNodeMap[node.SchemaName]; !ok {
			schemaNodeMap[node.SchemaName] = &structs.DependencyTreeNode{
				Name:               node.SchemaName,
				Type:               "SCHEMA",
				Status:             "VALID",
				ChildrenUniqueKeys: make([]string, 0),
				Children:           make([]*structs.DependencyTreeNode, 0),
			}
		}
		schemaNodeMap[node.SchemaName].Children = append(schemaNodeMap[node.SchemaName].Children, node)
	}
	return lo.Values(schemaNodeMap)
}

func (i *OracleDependencyHelper) outputTreeNodesBySchema(nodeList []*structs.DependencyTreeNode) []*structs.DependencyTreeNode {
	schemaNodeMap := make(map[string]*structs.DependencyTreeNode)
	for _, node := range nodeList {
		if node.IsFromPackageBody && node.GetType() != constants.OracleObjectTypePackageBody {
			continue
		}
		if _, ok := schemaNodeMap[node.SchemaName]; !ok {
			schemaNodeMap[node.SchemaName] = &structs.DependencyTreeNode{
				Name:               node.SchemaName,
				Type:               "SCHEMA",
				Status:             "VALID",
				ChildrenUniqueKeys: make([]string, 0),
				Children:           make([]*structs.DependencyTreeNode, 0),
			}
		}
		schemaNodeMap[node.SchemaName].Children = append(schemaNodeMap[node.SchemaName].Children, node)
	}

	return lo.Values(schemaNodeMap)
}

func (i *OracleDependencyHelper) FindCycleAndBreak(nodeMap []*structs.DependencyTreeNode, comment string) error {
	for idx, node := range nodeMap {
		cycleLastNode, _, cycleLength, hasCycle := i.FindCycle(node)
		if hasCycle {
			log.Errorf("FindCycleAndBreak, cycle found, comment:%s, cycleLength:%d, startNode:%v, stopNode:%v", comment, cycleLength, node.String(), cycleLastNode.String())
			nodeMap[idx] = i.DuplicateAndBreakCycle(node)
		}
	}
	return nil
}

func copyToCycleTypeNode(node *structs.DependencyTreeNode) *structs.DependencyTreeNode {
	return &structs.DependencyTreeNode{
		SchemaName:  node.SchemaName,
		PackageName: node.PackageName,
		Owner:       node.Owner,
		Name:        node.Name,
		Type:        node.Type,
		Status:      node.Status,
		IsCycle:     true,
	}
}

func (i *OracleDependencyHelper) DuplicateAndBreakCycle(startNode *structs.DependencyTreeNode) *structs.DependencyTreeNode {
	if startNode == nil {
		return nil
	}

	// Maintain a map to track already copied nodes (to handle potential shared subtrees)
	copiedNodes := make(map[string]*structs.DependencyTreeNode)

	// Helper function to recursively copy nodes
	var copyNode func(currNode *structs.DependencyTreeNode) *structs.DependencyTreeNode
	copyNode = func(currNode *structs.DependencyTreeNode) *structs.DependencyTreeNode {
		if currNode == nil {
			return nil
		}

		// Generate unique key for the current startNode
		nodeKey := currNode.UniqueKey()

		// If the startNode is already copied, return the copied version
		if copied, exists := copiedNodes[nodeKey]; exists {
			return copied
		}

		// Create a copy of the current startNode
		copiedNode := &structs.DependencyTreeNode{
			SchemaName:         currNode.SchemaName,
			PackageName:        currNode.PackageName,
			Owner:              currNode.Owner,
			Name:               currNode.Name,
			Type:               currNode.Type,
			Status:             currNode.Status,
			ChildrenUniqueKeys: append([]string{}, currNode.ChildrenUniqueKeys...),
			Children:           []*structs.DependencyTreeNode{},
		}
		copiedNodes[nodeKey] = copiedNode

		// Recursively copy children
		for _, child := range currNode.Children {
			if child.UniqueKey() == startNode.UniqueKey() {
				// Stop copying at the cycle last startNode to break the cycle
				copiedNode.Children = append(copiedNode.Children, copyToCycleTypeNode(child))
			} else {
				copiedNode.Children = append(copiedNode.Children, copyNode(child))
			}
		}

		return copiedNode
	}

	// Start copying from the input startNode
	rootCopy := copyNode(startNode)

	return rootCopy
}

func (i *OracleDependencyHelper) SetOracleContainDBLinkObject(dbLinkObjects map[string]bool) {
	i.dbLinkObjects = dbLinkObjects
}

type OracleObjectPrompt struct {
	PromptId    uint
	PromptTitle string
	PromptText  string
}

func (i *OracleDependencyHelper) SetPromptAndRelation(includePromptRelation bool, prompts []*objectparser.OracleObjectTransformationPrompt, relations []*objectparser.OracleObjectTaskObjectPromptRelation) {
	i.includePromptRelation = includePromptRelation
	if !includePromptRelation {
		return
	}
	var defaultCodePrompt *objectparser.OracleObjectTransformationPrompt
	var defaultTriggerPrompt *objectparser.OracleObjectTransformationPrompt
	oracleObjectPromptMap := make(map[string]structs.OracleObjectPrompt)

	promptMap := make(map[uint]*objectparser.OracleObjectTransformationPrompt)
	for _, prompt := range prompts {
		promptMap[prompt.ID] = prompt
		
		// Identify default prompts by category
		if prompt.IsDefault {
			if prompt.PromptCategory == "CODE" && defaultCodePrompt == nil {
				defaultCodePrompt = prompt
			} else if prompt.PromptCategory == "TRIGGER" && defaultTriggerPrompt == nil {
				defaultTriggerPrompt = prompt
			}
		}
	}

	for _, relation := range relations {
		_, ok := promptMap[relation.TaskPromptId]
		if !ok {
			continue
		}
		oracleObjectPromptMap[relation.DependencyUUID] = structs.OracleObjectPrompt{
			RelationId:  relation.ID,
			PromptTitle: promptMap[relation.TaskPromptId].PromptTitle,
			PromptId:    relation.TaskPromptId,
			PromptText:  promptMap[relation.TaskPromptId].PromptText,
		}
	}

	i.promptMap = oracleObjectPromptMap
	
	// Set CODE category default prompt
	if defaultCodePrompt != nil {
		i.defaultPrompt = &structs.OracleObjectPrompt{
			PromptId:    defaultCodePrompt.ID,
			PromptTitle: defaultCodePrompt.PromptTitle,
			PromptText:  defaultCodePrompt.PromptText,
		}
	}
	
	// Set TRIGGER category default prompt
	if defaultTriggerPrompt != nil {
		i.defaultTriggerPrompt = &structs.OracleObjectPrompt{
			PromptId:    defaultTriggerPrompt.ID,
			PromptTitle: defaultTriggerPrompt.PromptTitle,
			PromptText:  defaultTriggerPrompt.PromptText,
		}
	}
}
