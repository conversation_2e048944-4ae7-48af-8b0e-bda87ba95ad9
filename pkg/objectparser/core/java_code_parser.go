// Package core implements JavaCodeParser for parsing multi-file LLM output into structured Java files.
// This parser handles the standardized format defined in the PROMPT templates for MyBatis/MyBatis-Plus conversions.
package core

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// JavaFile represents a single Java or XML file parsed from LLM output
type JavaFile struct {
	FilePath    string    `json:"filePath"`    // com/tms/entity/User.java
	FileName    string    `json:"fileName"`    // User.java
	PackagePath string    `json:"packagePath"` // com.tms.entity
	FileType    string    `json:"fileType"`    // java, xml, properties
	Content     string    `json:"content"`     // Complete file content
	Order       int       `json:"order"`       // Display/processing order
	ParsedAt    time.Time `json:"parsedAt"`    // When this file was parsed
}

// JavaCodeParser handles parsing of LLM output in the standardized multi-file format
type JavaCodeParser struct {
	// File delimiters used in PROMPT templates
	startDelimiter string
	endDelimiter   string

	// Regex patterns for parsing
	fileStartPattern *regexp.Regexp
	packagePattern   *regexp.Regexp

	// Enhanced patterns for intelligent parsing
	classPattern           *regexp.Regexp
	interfacePattern       *regexp.Regexp
	xmlMapperPattern       *regexp.Regexp
	packageBoundaryPattern *regexp.Regexp
}

// ParsedResult represents the complete result of parsing LLM output
type ParsedResult struct {
	IsMultiFile     bool       `json:"isMultiFile"`     // Whether multiple files were detected
	Files           []JavaFile `json:"files"`           // Parsed files
	OriginalContent string     `json:"originalContent"` // Original LLM output
	ParsedAt        time.Time  `json:"parsedAt"`        // When parsing occurred
	ParseErrors     []string   `json:"parseErrors"`     // Any parsing errors encountered

	// Enhanced fields for intelligent parsing
	ParseMode       string   `json:"parseMode"`       // "standard" | "intelligent_splitting" | "hybrid"
	FallbackReason  string   `json:"fallbackReason"`  // Reason for using intelligent splitting
	ConfidenceScore float64  `json:"confidenceScore"` // Parsing confidence (0.0-1.0)
	MethodsUsed     []string `json:"methodsUsed"`     // List of parsing methods attempted
}

// NewJavaCodeParser creates a new parser instance with default delimiters
func NewJavaCodeParser() *JavaCodeParser {
	parser := &JavaCodeParser{
		startDelimiter: "=== FILE_START:",
		endDelimiter:   "=== FILE_END ===",
	}

	// Compile regex patterns for standard parsing
	parser.fileStartPattern = regexp.MustCompile(`=== FILE_START:\s*(.+?)\s*===`)
	parser.packagePattern = regexp.MustCompile(`package\s+([\w.]+)\s*;`)

	// Compile enhanced patterns for intelligent parsing (following 2025 best practices)
	parser.classPattern = regexp.MustCompile(`(?m)^\s*public[ \t]+class[ \t]+(\w+)`)
	parser.interfacePattern = regexp.MustCompile(`(?m)(?:public\s+)?interface\s+(\w+)`)
	parser.xmlMapperPattern = regexp.MustCompile(`(?s)<\?xml\s+version.*?<mapper\s+namespace\s*=\s*["']([^"']+)["'].*?</mapper>`)
	parser.packageBoundaryPattern = regexp.MustCompile(`(?m)^package\s+([\w.]+)\s*;`)

	return parser
}

// ParseMultiFileOutput parses LLM output containing multiple files in the standardized format
func (p *JavaCodeParser) ParseMultiFileOutput(rawOutput string) (*ParsedResult, error) {
	// First attempt: Standard format parsing
	result, err := p.parseStandardFormat(rawOutput)

	// Check if standard format parsing failed and we need intelligent fallback
	needsFallback := false
	var reason string

	if err != nil {
		needsFallback = true
		reason = "Standard parsing failed with error: " + err.Error()
	} else if result != nil && p.isStandardFormatParseFailed(rawOutput, result) {
		needsFallback = true
		if p.containsMultiFileMarkers(rawOutput) && len(result.Files) == 0 {
			reason = "Standard markers found but no valid files extracted"
		} else if !p.containsMultiFileMarkers(rawOutput) && p.likelyContainsMultipleJavaClasses(rawOutput) {
			reason = "No standard markers but multiple Java classes detected"
		} else {
			reason = "High error rate in standard parsing"
		}
	}

	if needsFallback {
		// Attempt intelligent splitting
		smartResult, smartErr := p.parseByIntelligentSplitting(rawOutput)
		if smartErr == nil && len(smartResult.Files) > 0 {
			// Success with intelligent splitting
			smartResult.FallbackReason = reason
			if result != nil {
				smartResult.ParseErrors = append(result.ParseErrors, "Fallback to intelligent splitting: "+reason)
			} else {
				smartResult.ParseErrors = []string{"Fallback to intelligent splitting: " + reason}
			}
			return smartResult, nil
		}

		// If intelligent splitting also failed, return original error or result with enhanced error info
		if result != nil {
			result.ParseErrors = append(result.ParseErrors, fmt.Sprintf("Intelligent splitting also failed: %v", smartErr))
		} else {
			result = &ParsedResult{
				OriginalContent: rawOutput,
				ParsedAt:        time.Now(),
				Files:           make([]JavaFile, 0),
				ParseErrors:     []string{fmt.Sprintf("Standard parsing failed: %v", err), fmt.Sprintf("Intelligent splitting also failed: %v", smartErr)},
				ParseMode:       "failed",
			}
		}
	}

	return result, err
}

// parseStandardFormat performs the original standard format parsing logic
func (p *JavaCodeParser) parseStandardFormat(rawOutput string) (*ParsedResult, error) {
	result := &ParsedResult{
		OriginalContent: rawOutput,
		ParsedAt:        time.Now(),
		Files:           make([]JavaFile, 0),
		ParseErrors:     make([]string, 0),
		ParseMode:       "standard",
		ConfidenceScore: 1.0,
		MethodsUsed:     []string{"standard"},
	}

	// Check if this is multi-file format
	if !p.containsMultiFileMarkers(rawOutput) {
		result.IsMultiFile = false
		return result, nil
	}

	result.IsMultiFile = true

	// Split content by file markers
	files, err := p.extractFiles(rawOutput)
	if err != nil {
		return result, fmt.Errorf("failed to extract files: %w", err)
	}

	// Process each file
	for i, fileData := range files {
		javaFile, parseErr := p.parseFileData(fileData, i)
		if parseErr != nil {
			errorMsg := fmt.Sprintf("Failed to parse file %d: %v", i+1, parseErr)
			result.ParseErrors = append(result.ParseErrors, errorMsg)
			continue
		}

		result.Files = append(result.Files, *javaFile)
	}

	if len(result.Files) == 0 {
		return result, fmt.Errorf("no valid files could be parsed from multi-file input")
	}

	return result, nil
}

// containsMultiFileMarkers checks if the input contains the multi-file format markers
func (p *JavaCodeParser) containsMultiFileMarkers(content string) bool {
	return strings.Contains(content, p.startDelimiter) && strings.Contains(content, p.endDelimiter)
}

// extractFiles splits the raw output into individual file data blocks
func (p *JavaCodeParser) extractFiles(content string) ([]fileData, error) {
	var files []fileData

	// Find all file start markers
	lines := strings.Split(content, "\n")
	var currentFile *fileData

	for i, line := range lines {
		// Check for file start marker
		if matches := p.fileStartPattern.FindStringSubmatch(line); len(matches) > 1 {
			// Save previous file if exists
			if currentFile != nil {
				files = append(files, *currentFile)
			}

			// Start new file
			filePath := strings.TrimSpace(matches[1])
			currentFile = &fileData{
				FilePath:  filePath,
				StartLine: i,
				Content:   make([]string, 0),
			}
			continue
		}

		// Check for file end marker
		if strings.TrimSpace(line) == p.endDelimiter {
			if currentFile != nil {
				currentFile.EndLine = i
			}
			continue
		}

		// Add content line if we're inside a file block
		if currentFile != nil && currentFile.StartLine < i {
			currentFile.Content = append(currentFile.Content, line)
		}
	}

	// Save last file if exists
	if currentFile != nil {
		files = append(files, *currentFile)
	}

	if len(files) == 0 {
		return nil, fmt.Errorf("no files found in multi-file format")
	}

	return files, nil
}

// fileData represents raw file data extracted from LLM output
type fileData struct {
	FilePath  string
	StartLine int
	EndLine   int
	Content   []string
}

// parseFileData converts raw file data into a structured JavaFile
func (p *JavaCodeParser) parseFileData(data fileData, order int) (*JavaFile, error) {
	if data.FilePath == "" {
		return nil, fmt.Errorf("file path is empty")
	}

	// Join content lines
	content := strings.Join(data.Content, "\n")
	content = strings.TrimSpace(content)

	if content == "" {
		return nil, fmt.Errorf("file content is empty for path: %s", data.FilePath)
	}

	// Extract file information
	fileName := filepath.Base(data.FilePath)
	fileType := p.determineFileType(fileName)
	packagePath := p.extractPackagePath(content, data.FilePath)

	javaFile := &JavaFile{
		FilePath:    p.normalizeFilePath(data.FilePath),
		FileName:    fileName,
		PackagePath: packagePath,
		FileType:    fileType,
		Content:     content,
		Order:       order,
		ParsedAt:    time.Now(),
	}

	// Validate file content
	if err := p.validateFileContent(javaFile); err != nil {
		return nil, fmt.Errorf("file validation failed: %w", err)
	}

	return javaFile, nil
}

// normalizeFilePath ensures the file path uses forward slashes and proper format
func (p *JavaCodeParser) normalizeFilePath(filePath string) string {
	// Convert backslashes to forward slashes
	normalized := strings.ReplaceAll(filePath, "\\", "/")

	// Remove leading slashes
	normalized = strings.TrimPrefix(normalized, "/")

	return normalized
}

// determineFileType extracts the file type from the file extension
func (p *JavaCodeParser) determineFileType(fileName string) string {
	ext := strings.ToLower(filepath.Ext(fileName))
	switch ext {
	case ".java":
		return "java"
	case ".xml":
		return "xml"
	case ".properties":
		return "properties"
	case ".yml", ".yaml":
		return "yaml"
	default:
		return "java" // Default to java
	}
}

// extractPackagePath extracts the package declaration from Java content or derives from file path
func (p *JavaCodeParser) extractPackagePath(content string, filePath string) string {
	// Try to extract from package declaration in Java files
	if matches := p.packagePattern.FindStringSubmatch(content); len(matches) > 1 {
		return matches[1]
	}

	// For XML and other files, derive from file path
	if strings.Contains(filePath, "resources/") {
		return "" // XML files don't have package paths
	}

	// Convert file path to package path
	// com/tms/entity/User.java -> com.tms.entity
	dir := filepath.Dir(filePath)
	if dir == "." || dir == "" {
		return ""
	}

	packagePath := strings.ReplaceAll(dir, "/", ".")
	packagePath = strings.ReplaceAll(packagePath, "\\", ".")

	return packagePath
}

// validateFileContent performs basic validation on the parsed file
func (p *JavaCodeParser) validateFileContent(file *JavaFile) error {
	if file.FileName == "" {
		return fmt.Errorf("file name is empty")
	}

	if file.Content == "" {
		return fmt.Errorf("file content is empty")
	}

	// Validate Java files have package declaration (unless in default package)
	if file.FileType == "java" && file.PackagePath != "" {
		expectedPackage := fmt.Sprintf("package %s;", file.PackagePath)
		if !strings.Contains(file.Content, expectedPackage) {
			// Package validation warning - could be logged if logger is available
		}
	}

	// Validate XML files have proper XML declaration
	if file.FileType == "xml" {
		if !strings.Contains(file.Content, "<?xml") {
			// XML validation warning - could be logged if logger is available
		}
	}

	return nil
}

// GetMainJavaFile returns the primary Java file (usually the repository/mapper or service)
func (p *JavaCodeParser) GetMainJavaFile(files []JavaFile) *JavaFile {
	if len(files) == 0 {
		return nil
	}

	// Priority order: TriggerManager > Interceptor > Handler > Listener > DAO > Repository/Mapper > Service > ServiceImpl > Entity
	// TriggerManager is for JDBC, Interceptor for MyBatis, Handler for MyBatis-Plus, Listener for Hibernate
	// DAO is for pure Hibernate, Repository is for Spring Data JPA, Mapper is for MyBatis
	priorities := map[string]int{
		"triggermanager": 10,
		"interceptor":    9,
		"handler":        8,
		"listener":       7,
		"dao":            6,
		"repository":     5,
		"mapper":         4,
		"service":        3,
		"impl":           2,
		"entity":         1,
	}

	var mainFile *JavaFile
	maxPriority := 0

	for i := range files {
		file := &files[i]
		if file.FileType != "java" {
			continue
		}

		priority := 0
		lowerPath := strings.ToLower(file.FilePath)

		// Check for trigger-related patterns first (highest priority)
		if strings.Contains(lowerPath, "triggermanager") {
			priority = priorities["triggermanager"]
		} else if strings.Contains(lowerPath, "interceptor") {
			priority = priorities["interceptor"]
		} else if strings.Contains(lowerPath, "handler") || strings.Contains(lowerPath, "metaobjecthandler") {
			priority = priorities["handler"]
		} else if strings.Contains(lowerPath, "listener") || strings.Contains(lowerPath, "entitylistener") {
			priority = priorities["listener"]
			// Check for impl before service since it's more specific (service.impl contains both keywords)
		} else if strings.Contains(lowerPath, "/impl/") || strings.Contains(lowerPath, "impl.java") {
			priority = priorities["impl"]
		} else if strings.Contains(lowerPath, "/dao/") || strings.Contains(lowerPath, "dao.java") {
			priority = priorities["dao"]
		} else if strings.Contains(lowerPath, "repository") {
			priority = priorities["repository"]
		} else if strings.Contains(lowerPath, "mapper") {
			priority = priorities["mapper"]
		} else if strings.Contains(lowerPath, "service") {
			priority = priorities["service"]
		} else if strings.Contains(lowerPath, "entity") {
			priority = priorities["entity"]
		}

		if priority > maxPriority {
			maxPriority = priority
			mainFile = file
		}
	}

	// If no priority match, return the first Java file
	if mainFile == nil {
		for i := range files {
			if files[i].FileType == "java" {
				return &files[i]
			}
		}
	}

	return mainFile
}

// GenerateFileName creates a backward-compatible filename for single-file storage
func (p *JavaCodeParser) GenerateFileName(files []JavaFile, objectName string) string {
	mainFile := p.GetMainJavaFile(files)
	if mainFile != nil {
		return mainFile.FileName
	}

	// Fallback to object name
	if objectName != "" {
		return fmt.Sprintf("%s.java", objectName)
	}

	return "ConvertedCode.java"
}

// CombineFilesForBackwardCompatibility creates a single content string for backward compatibility
func (p *JavaCodeParser) CombineFilesForBackwardCompatibility(files []JavaFile) string {
	if len(files) == 0 {
		return ""
	}

	if len(files) == 1 {
		return files[0].Content
	}

	var combined strings.Builder

	// Add header comment
	combined.WriteString("// This file contains multiple generated classes\n")
	combined.WriteString("// Generated by TMS ObjectParser Multi-File Engine\n")
	combined.WriteString(fmt.Sprintf("// Generated at: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	combined.WriteString("\n")

	// Combine all files with separators
	for i, file := range files {
		combined.WriteString(fmt.Sprintf("// ========== FILE: %s ==========\n", file.FilePath))
		combined.WriteString(file.Content)

		if i < len(files)-1 {
			combined.WriteString("\n\n")
		}
	}

	return combined.String()
}

// isStandardFormatParseFailed determines if standard format parsing failed and fallback is needed
func (p *JavaCodeParser) isStandardFormatParseFailed(rawOutput string, result *ParsedResult) bool {
	// Case 1: Has markers but no valid files extracted (includes empty content case)
	if p.containsMultiFileMarkers(rawOutput) && len(result.Files) == 0 {
		return true
	}

	// Case 2: Has markers but parsing had errors and extracted files is zero or very low
	if p.containsMultiFileMarkers(rawOutput) && len(result.ParseErrors) > 0 && len(result.Files) == 0 {
		return true
	}

	// Case 3: No markers but likely contains multiple Java classes
	if !p.containsMultiFileMarkers(rawOutput) && p.likelyContainsMultipleJavaClasses(rawOutput) {
		return true
	}

	// Case 4: High error rate in parsing (>50%)
	if len(result.ParseErrors) > 0 {
		totalAttempts := len(result.ParseErrors) + len(result.Files)
		errorRate := float64(len(result.ParseErrors)) / float64(totalAttempts)
		if errorRate > 0.5 {
			return true
		}
	}

	return false
}

// likelyContainsMultipleJavaClasses detects if content probably contains multiple Java classes
func (p *JavaCodeParser) likelyContainsMultipleJavaClasses(content string) bool {
	// Check for multiple package declarations
	packageMatches := p.packageBoundaryPattern.FindAllString(content, -1)
	if len(packageMatches) > 1 {
		return true
	}

	// Check for multiple class/interface declarations
	classMatches := p.classPattern.FindAllString(content, -1)
	interfaceMatches := p.interfacePattern.FindAllString(content, -1)
	totalJavaTypes := len(classMatches) + len(interfaceMatches)

	if totalJavaTypes > 1 {
		return true
	}

	// Check for XML + Java combination
	hasXML := p.xmlMapperPattern.MatchString(content)
	hasJava := p.packagePattern.MatchString(content)

	return hasXML && hasJava
}

// parseByIntelligentSplitting attempts to parse content using intelligent splitting methods
// This method is used as a fallback when standard format parsing fails
func (p *JavaCodeParser) parseByIntelligentSplitting(rawOutput string) (*ParsedResult, error) {
	result := &ParsedResult{
		OriginalContent: rawOutput,
		ParsedAt:        time.Now(),
		Files:           make([]JavaFile, 0),
		ParseErrors:     make([]string, 0),
		ParseMode:       "intelligent_splitting",
		ConfidenceScore: 0.0,
		MethodsUsed:     []string{"intelligent_splitting"},
	}

	var confidenceScore float64
	var methods []string

	// Method 1: Try extracting by package boundaries
	javaClasses, err := p.extractJavaClassBlocks(rawOutput)
	if err == nil && len(javaClasses) > 0 {
		result.Files = append(result.Files, javaClasses...)
		confidenceScore += 0.4
		methods = append(methods, "package_boundary_extraction")
	}

	// Method 2: Try extracting XML mappers
	xmlFiles, err := p.extractXMLMapperBlocks(rawOutput)
	if err == nil && len(xmlFiles) > 0 {
		result.Files = append(result.Files, xmlFiles...)
		confidenceScore += 0.3
		methods = append(methods, "xml_mapper_extraction")
	}

	// Method 3: Fallback to content-based splitting
	if len(result.Files) == 0 {
		fallbackFiles, err := p.extractByContentAnalysis(rawOutput)
		if err == nil && len(fallbackFiles) > 0 {
			result.Files = append(result.Files, fallbackFiles...)
			confidenceScore += 0.2
			methods = append(methods, "content_analysis_fallback")
		}
	}

	if len(result.Files) > 0 {
		result.IsMultiFile = true
		result.ConfidenceScore = confidenceScore
		result.MethodsUsed = methods

		// Assign order to files
		for i := range result.Files {
			result.Files[i].Order = i
			result.Files[i].ParsedAt = time.Now()
		}

		return result, nil
	}

	return result, fmt.Errorf("intelligent splitting failed to extract any valid files from content of length %d", len(rawOutput))
}

// extractJavaClassBlocks extracts Java classes/interfaces based on package boundaries
func (p *JavaCodeParser) extractJavaClassBlocks(content string) ([]JavaFile, error) {
	var files []JavaFile

	// Find all package declarations with their positions
	packageMatches := p.packageBoundaryPattern.FindAllStringSubmatch(content, -1)
	packagePositions := p.packageBoundaryPattern.FindAllStringIndex(content, -1)

	if len(packageMatches) == 0 {
		return files, fmt.Errorf("no package declarations found")
	}

	// Split content by package boundaries
	for i, match := range packageMatches {
		if len(match) < 2 {
			continue
		}

		packageName := match[1]
		startPos := packagePositions[i][0]

		// Determine end position (next package or end of content)
		var endPos int
		if i+1 < len(packagePositions) {
			endPos = packagePositions[i+1][0]
		} else {
			endPos = len(content)
		}

		// Extract the code block
		codeBlock := strings.TrimSpace(content[startPos:endPos])
		if codeBlock == "" {
			continue
		}

		// Infer file metadata
		fileName, fileType := p.inferFileMetadata(codeBlock, packageName)
		if fileName == "" {
			continue
		}

		// Create JavaFile
		javaFile := JavaFile{
			FilePath:    p.buildFilePath(packageName, fileName),
			FileName:    fileName,
			PackagePath: packageName,
			FileType:    fileType,
			Content:     codeBlock,
			ParsedAt:    time.Now(),
		}

		files = append(files, javaFile)
	}

	return files, nil
}

// extractXMLMapperBlocks extracts XML mapper files
func (p *JavaCodeParser) extractXMLMapperBlocks(content string) ([]JavaFile, error) {
	var files []JavaFile

	matches := p.xmlMapperPattern.FindAllStringSubmatch(content, -1)
	positions := p.xmlMapperPattern.FindAllStringIndex(content, -1)

	for i, match := range matches {
		if len(match) < 2 {
			continue
		}

		namespace := match[1]
		startPos := positions[i][0]
		endPos := positions[i][1]

		xmlContent := strings.TrimSpace(content[startPos:endPos])
		if xmlContent == "" {
			continue
		}

		// Generate mapper file name from namespace
		parts := strings.Split(namespace, ".")
		if len(parts) == 0 {
			continue
		}

		fileName := parts[len(parts)-1] + ".xml"
		filePath := "resources/mapper/" + fileName

		javaFile := JavaFile{
			FilePath:    filePath,
			FileName:    fileName,
			PackagePath: "",
			FileType:    "xml",
			Content:     xmlContent,
			ParsedAt:    time.Now(),
		}

		files = append(files, javaFile)
	}

	return files, nil
}

// extractByContentAnalysis performs fallback content analysis when other methods fail
func (p *JavaCodeParser) extractByContentAnalysis(content string) ([]JavaFile, error) {
	var files []JavaFile

	// Try to split by empty lines that might separate different classes
	blocks := strings.Split(content, "\n\n\n") // Triple newlines as separators

	for _, block := range blocks {
		block = strings.TrimSpace(block)
		if block == "" {
			continue
		}

		// Check if this block contains Java code
		if p.packagePattern.MatchString(block) || p.classPattern.MatchString(block) || p.interfacePattern.MatchString(block) {
			packageName := ""
			if matches := p.packagePattern.FindStringSubmatch(block); len(matches) > 1 {
				packageName = matches[1]
			}

			fileName, fileType := p.inferFileMetadata(block, packageName)
			if fileName == "" {
				fileName = "GeneratedCode.java"
				fileType = "java"
			}

			javaFile := JavaFile{
				FilePath:    p.buildFilePath(packageName, fileName),
				FileName:    fileName,
				PackagePath: packageName,
				FileType:    fileType,
				Content:     block,
				ParsedAt:    time.Now(),
			}

			files = append(files, javaFile)
		}
	}

	return files, nil
}

// inferFileMetadata attempts to infer file name and type from code content
func (p *JavaCodeParser) inferFileMetadata(content, packageName string) (string, string) {
	// Try to find class name
	if matches := p.classPattern.FindStringSubmatch(content); len(matches) > 1 {
		className := matches[1]
		return className + ".java", "java"
	}

	// Try to find interface name
	if matches := p.interfacePattern.FindStringSubmatch(content); len(matches) > 1 {
		interfaceName := matches[1]
		return interfaceName + ".java", "java"
	}

	// Check if it's XML
	if strings.Contains(content, "<?xml") {
		return "mapper.xml", "xml"
	}

	return "", ""
}

// buildFilePath constructs the full file path from package and file name
func (p *JavaCodeParser) buildFilePath(packageName, fileName string) string {
	if packageName == "" {
		return fileName
	}

	packagePath := strings.ReplaceAll(packageName, ".", "/")
	return packagePath + "/" + fileName
}
