package dto

import (
	"encoding/json"
	"strings"
)

type PingRequest struct {
}

type PingResponse struct {
	Message string `json:"message"`
	ErrorResponse
}

type ErrorPosition struct {
	Pos      int    `json:"pos"`
	Line     int    `json:"line"`
	LineText string `json:"lineText"`
	Column   int    `json:"column"`
	Token    string `json:"token"`
}

type ErrorResponse struct {
	Error      string        `json:"error"`
	StatusCode int           `json:"statusCode"`
	Position   ErrorPosition `json:"position"`
}

type AnalyzeSQLRequest struct {
	IsEncoded bool   `json:"isEncoded"`
	PLSQL     string `json:"plsql"`
	Comment   string `json:"comment"`
}

type SQLParent struct {
	ParentType string `json:"parentType"`
	ParentName string `json:"parentName"`
}

type MethodInvoke struct {
	OwnerName             string      `json:"ownerName"`
	FuncName              string      `json:"funcName"`
	Count                 int         `json:"count"`
	IsOwnerInReservedWord bool        `json:"isOwnerInReservedWord"`
	Parents               []SQLParent `json:"parents"`
}

type TableReference struct {
	StatementType string `json:"statementType"`
	StatementName string `json:"statementName"`
	TableName     string `json:"tableName"`
	Count         int    `json:"count"`
	DatabaseLink  bool   `json:"isDatabaseLink"`
}

func (receiver TableReference) GetCount() int {
	return receiver.Count
}

func (receiver TableReference) GetTableName() string {
	return receiver.TableName
}

func (receiver TableReference) IsDatabaseLink() bool {
	return strings.Contains(receiver.TableName, "@")
}

type ReservedWord struct {
	Value             string         `json:"value"`
	Count             int            `json:"count"`
	HasNonAlphaPrefix bool           `json:"hasNonAlphaPrefix,omitempty"`
	Highlight         []ReservedWord `json:"highlight,omitempty"`
}

func (receiver ReservedWord) GetCount() int {
	return receiver.Count
}

func (receiver ReservedWord) GetValue() string {
	return receiver.Value
}

func (receiver ReservedWord) GetHighlight() []ReservedWord {
	return receiver.Highlight
}

type AnalyzeSQLResponse struct {
	MethodInvokeList   []MethodInvoke   `json:"methodInvokeList"`
	IdentifierList     []MethodInvoke   `json:"identifierList"`
	TableReferenceList []TableReference `json:"tableReferenceList"`
	ReservedWordList   []ReservedWord   `json:"reservedWordList"`
	PLSQLSegment       PLSQLSegment     `json:"plsqlSegment"`
	ErrorResponse
}

type Segment struct {
	Name     string `json:"name"`
	StartPos int    `json:"startPos"`
	EndPos   int    `json:"endPos"`
	SQL      string `json:"sql"`
}

type Position struct {
	BP        int    `json:"bp"`
	StartPos  int    `json:"startPos"`
	SP        int    `json:"sp"`
	NP        int    `json:"np"`
	CH        string `json:"ch"`
	Token     string `json:"token"`
	StringVal string `json:"stringVal"`
}

type PLSQLSegment struct {
	SegmentPrefix Segment `json:"segmentPrefix"`
	SegmentSuffix Segment `json:"segmentSuffix"`

	Declares   []Segment `json:"declares"`
	Functions  []Segment `json:"functions"`
	Procedures []Segment `json:"procedures"`

	LeftStartPos  int        `json:"leftStartPos"`
	RightStartPos int        `json:"rightStartPos"`
	AllPositions  []Position `json:"allPositions"`
}

func (i AnalyzeSQLResponse) GetMethodInvokeList() []MethodInvoke {
	return i.MethodInvokeList
}

func (i AnalyzeSQLResponse) GetIdentifierList() []MethodInvoke {
	return i.IdentifierList
}

func (i AnalyzeSQLResponse) GetTableReferenceList() []TableReference {
	return i.TableReferenceList
}

func (i AnalyzeSQLResponse) GetDatabaseLinkTableReferenceList() []TableReference {
	var result []TableReference
	for _, tableReference := range i.TableReferenceList {
		if tableReference.IsDatabaseLink() {
			result = append(result, tableReference)
		}
	}
	return result
}

func (i AnalyzeSQLResponse) GetReservedWordList() []ReservedWord {
	return i.ReservedWordList
}

func (i AnalyzeSQLResponse) GetPlsqlSegment() PLSQLSegment {
	return i.PLSQLSegment
}

func (i AnalyzeSQLResponse) MarshalMethodInvokeListToString() string {
	if len(i.MethodInvokeList) == 0 {
		return "{}"
	}
	return forceMarshalList(i.MethodInvokeList)
}

func (i AnalyzeSQLResponse) MarshalIdentifierListToString() string {
	if len(i.IdentifierList) == 0 {
		return "{}"
	}
	return forceMarshalList(i.IdentifierList)
}

func (i AnalyzeSQLResponse) MarshalTableReferenceListToString() string {
	if len(i.TableReferenceList) == 0 {
		return "{}"
	}
	for idx, tableReference := range i.TableReferenceList {
		if tableReference.IsDatabaseLink() {
			i.TableReferenceList[idx].DatabaseLink = true
		}
	}
	return forceMarshalList(i.TableReferenceList)
}

func (i AnalyzeSQLResponse) MarshalReservedWordListToString() string {
	if len(i.ReservedWordList) == 0 {
		return "{}"
	}

	for idx, item := range i.ReservedWordList {
		for idx2, highlight := range item.Highlight {
			if highlight.Value == "" {
				continue
			}
			// first character is alpha
			if (highlight.Value[0] >= 'a' && highlight.Value[0] <= 'z') || (highlight.Value[0] >= 'A' && highlight.Value[0] <= 'Z') {
				continue
			}
			i.ReservedWordList[idx].Highlight[idx2].HasNonAlphaPrefix = true
		}
	}

	return forceMarshalList(i.ReservedWordList)
}

func (i AnalyzeSQLResponse) MarshalPLSQLSegmentToString() string {
	if len(i.PLSQLSegment.AllPositions) == 0 {
		return "{}"
	}
	return forceMarshalList(i.PLSQLSegment)
}

func forceMarshalList(arr any) string {
	bytes, err := json.Marshal(arr)
	if err != nil {
		return "{}"
	}
	return string(bytes)
}

type PlSQLToJSONRequest struct {
	PLSQL     string `json:"plsql"`
	IsEncoded bool   `json:"isEncoded"`
	Comment   string `json:"comment"`
}

type GetPackageBodyDependencyRequest struct {
	IsEncoded            bool   `json:"isEncoded"`
	PLSQL                string `json:"plsql"`
	OwnerName            string `json:"ownerName"`
	PackageName          string `json:"packageName"`
	ExcludeAmbiguityWord bool   `json:"excludeAmbiguityWord"`
}

type GetPackageBodyDependencyResponse struct {
	Dependencies []Dependency `json:"dependencies"`
	Total        int          `json:"total"`
	ErrorResponse
}

type Dependency struct {
	PackageName           string `json:"packageName"`
	Owner                 string `json:"owner"`
	Name                  string `json:"name"`
	Type                  string `json:"type"`
	ReferencedPackageName string `json:"referencedPackageName"`
	ReferencedOwner       string `json:"referencedOwner"`
	ReferencedName        string `json:"referencedName"`
	ReferencedType        string `json:"referencedType"`
}

func (i Dependency) GetPackageName() string {
	return i.PackageName
}

func (i Dependency) GetOwner() string {
	return i.Owner
}

func (i Dependency) GetName() string {
	return i.Name
}

func (i Dependency) GetType() string {
	return i.Type
}

func (i Dependency) GetReferencedPackageName() string {
	return i.ReferencedPackageName
}

func (i Dependency) GetReferencedOwner() string {
	return i.ReferencedOwner
}

func (i Dependency) GetReferencedName() string {
	return i.ReferencedName
}

func (i Dependency) GetReferencedType() string {
	return i.ReferencedType
}

type AbstractSyntaxTreeNode struct {
	StmtType  string `json:"stmtType"`
	StmtName  string `json:"stmtName"`
	StmtValue string `json:"stmtValue"`

	Key      string                    `json:"key"`
	Children []*AbstractSyntaxTreeNode `json:"children"`
}

func (i *AbstractSyntaxTreeNode) GetChildren() []*AbstractSyntaxTreeNode {
	return i.Children
}

func (i *AbstractSyntaxTreeNode) GetStmtType() string {
	return i.StmtType
}

func (i *AbstractSyntaxTreeNode) GetStmtName() string {
	return i.StmtName
}

func (i *AbstractSyntaxTreeNode) GetStmtValue() string {
	return i.StmtValue
}

type PlSQLToJSONResponse struct {
	Root *AbstractSyntaxTreeNode `json:"root"`
	ErrorResponse
}

type ResponseInterface interface {
	Validate() error
}

// Validate methods for response types
func (r *PingResponse) Validate() error {
	return nil
}

func (r *ErrorResponse) Validate() error {
	return nil
}

func (r *AnalyzeSQLResponse) Validate() error {
	return nil
}

func (r *GetPackageBodyDependencyResponse) Validate() error {
	return nil
}

func (r *PlSQLToJSONResponse) Validate() error {
	return nil
}

type SchemaObjectKey struct {
	SchemaName string
	ObjectType string
	ObjectName string

	DependencyObjectKey string
	HighlightLine       string
	PackageObjectKey    *SchemaObjectKey
}

func (i *SchemaObjectKey) String() string {
	return i.SchemaName + "." + i.ObjectType + "." + i.ObjectName
}

func (i *SchemaObjectKey) GetObjectKey() string {
	return i.SchemaName + "." + i.ObjectType + "." + i.ObjectName
}

func (i *SchemaObjectKey) GetPackageObjectKey() string {
	if i.PackageObjectKey != nil {
		return i.PackageObjectKey.String()
	}
	return ""
}

func (i *SchemaObjectKey) IsPackageObjectKeyExist() bool {
	return i.PackageObjectKey != nil
}

func (i *SchemaObjectKey) GetPackageName() string {
	if i.PackageObjectKey != nil {
		return i.PackageObjectKey.ObjectName
	}
	return ""
}
