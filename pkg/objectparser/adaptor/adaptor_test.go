package adaptor

import (
	"context"
	"testing"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"

	"gitee.com/pingcap_enterprise/tms/util/config"
)

func TestNewObjectParserAdaptor_UnsupportedProtocol(t *testing.T) {
	cfg := &config.ObjectParserConfig{
		Enable:   true,
		Protocol: "websocket",
		Host:     "localhost",
		Port:     8080,
	}

	_, err := NewObjectParserAdaptor(cfg)
	if err == nil {
		t.Error("Expected error for unsupported protocol, but got nil")
	}

	expected := "unsupported protocol: websocket, supported protocols are: http, grpc"
	if err.Error() != expected {
		t.<PERSON>("Expected error message '%s', but got '%s'", expected, err.Error())
	}
}

func TestNewObjectParserAdaptor_ProtocolValidation(t *testing.T) {
	// Test unsupported protocols return the right error
	unsupportedProtocols := []string{"ftp", "websocket", "tcp", "udp"}

	for _, protocol := range unsupportedProtocols {
		t.Run("Protocol_"+protocol+"_is_unsupported", func(t *testing.T) {
			cfg := &config.ObjectParserConfig{
				Enable:   true,
				Protocol: protocol,
				Host:     "localhost",
				Port:     8080,
			}

			_, err := NewObjectParserAdaptor(cfg)

			if err == nil {
				t.Error("Expected error for unsupported protocol, but got nil")
			}

			expectedError := "unsupported protocol: " + protocol + ", supported protocols are: http, grpc"
			if err.Error() != expectedError {
				t.Errorf("Expected error message '%s', but got '%s'", expectedError, err.Error())
			}
		})
	}
}

func TestNewObjectParserAdaptor_DefaultProtocol(t *testing.T) {
	// Test that empty protocol defaults to GRPC by checking error messages
	cfg := &config.ObjectParserConfig{
		Enable:   true,
		Protocol: "", // Empty protocol should default to GRPC
		Host:     "localhost",
		Port:     9090,
	}

	_, err := NewObjectParserAdaptor(cfg)

	// Should not get "unsupported protocol" error for empty protocol
	if err != nil && err.Error() == "unsupported protocol: , supported protocols are: http, grpc" {
		t.Error("Empty protocol should default to GRPC, not trigger unsupported protocol error")
	}

	// The error should indicate GRPC connection attempt (contains "GRPC" or "failed to connect")
	if err != nil && err.Error() != "" {
		errorMsg := err.Error()
		if !(contains(errorMsg, "GRPC") || contains(errorMsg, "failed to connect")) {
			t.Logf("Expected GRPC-related error, got: %s", errorMsg)
		}
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr ||
		(len(s) > len(substr) && indexOf(s, substr) >= 0)
}

func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// Test interface compatibility
func TestAdaptorInterfaceCompatibility(t *testing.T) {
	// Test that both HTTP and GRPC adaptors implement the same interface
	var httpAdaptor Adaptor
	var grpcAdaptor Adaptor

	// These should compile without error
	_ = httpAdaptor
	_ = grpcAdaptor

	// Test that all required methods are available
	ctx := context.Background()

	if httpAdaptor != nil {
		// These should compile
		_, _ = httpAdaptor.Ping(ctx, dto.PingRequest{})
		_, _ = httpAdaptor.AnalyzeSQL(ctx, dto.AnalyzeSQLRequest{})
		_, _ = httpAdaptor.PlSQLToJSON(ctx, dto.PlSQLToJSONRequest{})
		_, _ = httpAdaptor.GetPackageBodyDependency(ctx, dto.GetPackageBodyDependencyRequest{})
	}

	if grpcAdaptor != nil {
		// These should compile
		_, _ = grpcAdaptor.Ping(ctx, dto.PingRequest{})
		_, _ = grpcAdaptor.AnalyzeSQL(ctx, dto.AnalyzeSQLRequest{})
		_, _ = grpcAdaptor.PlSQLToJSON(ctx, dto.PlSQLToJSONRequest{})
		_, _ = grpcAdaptor.GetPackageBodyDependency(ctx, dto.GetPackageBodyDependencyRequest{})
	}
}
