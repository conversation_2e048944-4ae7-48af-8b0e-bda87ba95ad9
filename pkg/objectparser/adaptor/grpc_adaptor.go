package adaptor

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"

	"gitee.com/pingcap_enterprise/tms/proto"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// grpcAdaptor implements the Adaptor interface using GRPC
type grpcAdaptor struct {
	host   string
	port   int
	conn   *grpc.ClientConn
	client proto.PlsqlServiceClient
}

// NewGRPCObjectParserAdaptor creates a new GRPC adaptor
func NewGRPCObjectParserAdaptor(config *config.ObjectParserConfig) (Adaptor, error) {
	address := fmt.Sprintf("%s:%d", config.Host, config.Port)

	// Create GRPC connection
	conn, err := grpc.NewClient(address, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to GRPC server at %s: %w", address, err)
	}

	client := proto.NewPlsqlServiceClient(conn)

	grpcAdaptor := &grpcAdaptor{
		host:   config.Host,
		port:   config.Port,
		conn:   conn,
		client: client,
	}

	// Test connection with ping
	_, err = grpcAdaptor.Ping(context.TODO(), dto.PingRequest{})
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("GRPC ping failed: %w", err)
	}

	return grpcAdaptor, nil
}

// Close closes the GRPC connection
func (g *grpcAdaptor) Close() error {
	if g.conn != nil {
		return g.conn.Close()
	}
	return nil
}

// Ping implements the ping method
func (g *grpcAdaptor) Ping(ctx context.Context, req dto.PingRequest) (dto.PingResponse, error) {
	grpcReq := &proto.PingRequest{}

	grpcResp, err := g.client.Ping(ctx, grpcReq)
	if err != nil {
		log.Errorf("GRPC Ping failed: %v", err)
		return dto.PingResponse{}, err
	}

	return dto.PingResponse{
		Message: grpcResp.Message,
	}, nil
}

// AnalyzeSQL implements the SQL analysis method
func (g *grpcAdaptor) AnalyzeSQL(ctx context.Context, req dto.AnalyzeSQLRequest) (dto.AnalyzeSQLResponse, error) {
	grpcReq := &proto.AnalyzePlsqlRequest{
		Plsql:     req.PLSQL,
		Comment:   req.Comment,
		Jwt:       config.GetGlobalConfig().License,
		IsEncoded: req.IsEncoded,
	}

	grpcResp, err := g.client.AnalyzePlsql(ctx, grpcReq)
	if err != nil {
		log.Errorf("GRPC AnalyzePlsql failed: %v", err)
		return dto.AnalyzeSQLResponse{}, err
	}

	// Convert GRPC response to HTTP response format
	return g.convertAnalyzePlsqlResponse(grpcResp), nil
}

// PlSQLToJSON implements the PLSQL to JSON conversion method
func (g *grpcAdaptor) PlSQLToJSON(ctx context.Context, req dto.PlSQLToJSONRequest) (dto.PlSQLToJSONResponse, error) {
	grpcReq := &proto.ParseJsonRequest{
		Plsql:   req.PLSQL,
		Comment: req.Comment,
		Jwt:     config.GetGlobalConfig().License,
	}

	grpcResp, err := g.client.ParseToJson(ctx, grpcReq)
	if err != nil {
		log.Errorf("GRPC ParseToJson failed: %v", err)
		return dto.PlSQLToJSONResponse{}, err
	}

	// Convert GRPC response to HTTP response format
	return dto.PlSQLToJSONResponse{
		Root: g.convertJSONTreeNode(grpcResp.Root),
	}, nil
}

// GetPackageBodyDependency implements the package body dependency analysis method
func (g *grpcAdaptor) GetPackageBodyDependency(ctx context.Context, req dto.GetPackageBodyDependencyRequest) (dto.GetPackageBodyDependencyResponse, error) {
	grpcReq := &proto.GetDependencyRequest{
		Plsql:                req.PLSQL,
		OwnerName:            req.OwnerName,
		PackageName:          req.PackageName,
		Jwt:                  config.GetGlobalConfig().License,
		ExcludeAmbiguityWord: req.ExcludeAmbiguityWord,
		IsEncoded:            req.IsEncoded,
	}

	grpcResp, err := g.client.GetPackageBodyDependency(ctx, grpcReq)
	if err != nil {
		log.Errorf("GRPC GetPackageBodyDependency failed: %v", err)
		return dto.GetPackageBodyDependencyResponse{}, err
	}

	// Convert GRPC response to HTTP response format
	return g.convertPackageBodyDependencyResponse(grpcResp), nil
}

// Helper methods for converting between GRPC and HTTP formats

func (g *grpcAdaptor) convertAnalyzePlsqlResponse(grpcResp *proto.AnalyzePlsqlResponse) dto.AnalyzeSQLResponse {
	resp := dto.AnalyzeSQLResponse{
		MethodInvokeList:   make([]dto.MethodInvoke, len(grpcResp.MethodInvokeList)),
		IdentifierList:     make([]dto.MethodInvoke, len(grpcResp.IdentifierList)),
		TableReferenceList: make([]dto.TableReference, len(grpcResp.TableReferenceList)),
		ReservedWordList:   make([]dto.ReservedWord, len(grpcResp.ReservedWordList)),
		PLSQLSegment:       g.convertPLSQLSegment(grpcResp.PlsqlSegment),
	}

	// Convert method invoke list
	for i, grpcMethod := range grpcResp.MethodInvokeList {
		resp.MethodInvokeList[i] = dto.MethodInvoke{
			OwnerName:             grpcMethod.Owner,
			FuncName:              grpcMethod.FunName,
			Count:                 int(grpcMethod.Count),
			IsOwnerInReservedWord: grpcMethod.IsOwnerInReservedWord,
			Parents:               g.convertSQLParents(grpcMethod.Parents),
		}
	}

	// Convert identifier list (same structure as method invoke)
	for i, grpcIdent := range grpcResp.IdentifierList {
		resp.IdentifierList[i] = dto.MethodInvoke{
			OwnerName:             grpcIdent.Owner,
			FuncName:              grpcIdent.FunName,
			Count:                 int(grpcIdent.Count),
			IsOwnerInReservedWord: grpcIdent.IsOwnerInReservedWord,
			Parents:               g.convertSQLParents(grpcIdent.Parents),
		}
	}

	// Convert table reference list
	for i, grpcTable := range grpcResp.TableReferenceList {
		resp.TableReferenceList[i] = dto.TableReference{
			StatementType: grpcTable.StatementType,
			StatementName: grpcTable.StatementName,
			TableName:     grpcTable.TableName,
			Count:         int(grpcTable.Count),
			DatabaseLink:  grpcTable.DatabaseLink,
		}
	}

	// Convert reserved word list
	for i, grpcWord := range grpcResp.ReservedWordList {
		resp.ReservedWordList[i] = dto.ReservedWord{
			Value:     grpcWord.Value,
			Count:     int(grpcWord.Count),
			Highlight: g.convertReservedWordHighlights(grpcWord.Highlight),
		}
	}

	return resp
}

func (g *grpcAdaptor) convertSQLParents(grpcParents []*proto.SQLParent) []dto.SQLParent {
	parents := make([]dto.SQLParent, len(grpcParents))
	for i, grpcParent := range grpcParents {
		parents[i] = dto.SQLParent{
			ParentType: grpcParent.ParentType,
			ParentName: grpcParent.ParentName,
		}
	}
	return parents
}

func (g *grpcAdaptor) convertReservedWordHighlights(grpcHighlights []*proto.ReservedWordHighlight) []dto.ReservedWord {
	highlights := make([]dto.ReservedWord, len(grpcHighlights))
	for i, grpcHighlight := range grpcHighlights {
		highlights[i] = dto.ReservedWord{
			Value: grpcHighlight.Value,
			Count: int(grpcHighlight.Count),
		}
	}
	return highlights
}

func (g *grpcAdaptor) convertPLSQLSegment(grpcSegment *proto.PLSQLSegment) dto.PLSQLSegment {
	if grpcSegment == nil {
		return dto.PLSQLSegment{}
	}

	segment := dto.PLSQLSegment{
		LeftStartPos:  int(grpcSegment.LeftStartPos),
		RightStartPos: int(grpcSegment.RightStartPos),
		AllPositions:  make([]dto.Position, len(grpcSegment.AllPositions)),
	}

	// Convert segment prefix (take first element if exists)
	if len(grpcSegment.SegmentPrefix) > 0 {
		segment.SegmentPrefix = g.convertSQLSegment(grpcSegment.SegmentPrefix[0])
	}

	// Convert segment suffix (take first element if exists)
	if len(grpcSegment.SegmentSuffix) > 0 {
		segment.SegmentSuffix = g.convertSQLSegment(grpcSegment.SegmentSuffix[0])
	}

	// Convert declares, functions, procedures
	segment.Declares = make([]dto.Segment, len(grpcSegment.Declares))
	for i, grpcDeclare := range grpcSegment.Declares {
		segment.Declares[i] = g.convertSQLSegment(grpcDeclare)
	}

	segment.Functions = make([]dto.Segment, len(grpcSegment.Functions))
	for i, grpcFunc := range grpcSegment.Functions {
		segment.Functions[i] = g.convertSQLSegment(grpcFunc)
	}

	segment.Procedures = make([]dto.Segment, len(grpcSegment.Procedures))
	for i, grpcProc := range grpcSegment.Procedures {
		segment.Procedures[i] = g.convertSQLSegment(grpcProc)
	}

	// Convert positions - now we have full Position structure in GRPC
	for i, grpcPos := range grpcSegment.AllPositions {
		segment.AllPositions[i] = dto.Position{
			BP:        int(grpcPos.Bp),
			StartPos:  int(grpcPos.StartPos),
			SP:        int(grpcPos.Sp),
			NP:        int(grpcPos.Np),
			CH:        grpcPos.Ch,
			Token:     grpcPos.Token,
			StringVal: grpcPos.StringVal,
		}
	}

	return segment
}

func (g *grpcAdaptor) convertSQLSegment(grpcSegment *proto.SQLSegment) dto.Segment {
	if grpcSegment == nil {
		return dto.Segment{}
	}

	return dto.Segment{
		Name:     grpcSegment.Name,
		StartPos: int(grpcSegment.StartPos),
		EndPos:   int(grpcSegment.EndPos),
		SQL:      grpcSegment.Sql,
	}
}

func (g *grpcAdaptor) convertJSONTreeNode(grpcNode *proto.JSONTreeNode) *dto.AbstractSyntaxTreeNode {
	if grpcNode == nil {
		return nil
	}

	node := &dto.AbstractSyntaxTreeNode{
		StmtType:  grpcNode.StmtType,
		StmtName:  grpcNode.StmtName,
		StmtValue: grpcNode.StmtValue,
		Key:       grpcNode.StmtType + ":" + grpcNode.StmtName, // Generate key from type and name
		Children:  make([]*dto.AbstractSyntaxTreeNode, len(grpcNode.Children)),
	}

	for i, grpcChild := range grpcNode.Children {
		node.Children[i] = g.convertJSONTreeNode(grpcChild)
	}

	return node
}

func (g *grpcAdaptor) convertPackageBodyDependencyResponse(grpcResp *proto.GetDependencyResponse) dto.GetPackageBodyDependencyResponse {
	resp := dto.GetPackageBodyDependencyResponse{
		Dependencies: make([]dto.Dependency, len(grpcResp.Dependencies)),
		Total:        int(grpcResp.Total),
	}

	for i, grpcDep := range grpcResp.Dependencies {
		resp.Dependencies[i] = dto.Dependency{
			PackageName:           grpcDep.PackageName,
			Owner:                 grpcDep.Owner,
			Name:                  grpcDep.Name,
			Type:                  grpcDep.Type,
			ReferencedPackageName: grpcDep.ReferencedPackageName,
			ReferencedOwner:       grpcDep.ReferencedOwner,
			ReferencedName:        grpcDep.ReferencedName,
			ReferencedType:        grpcDep.ReferencedType,
		}
	}

	return resp
}
