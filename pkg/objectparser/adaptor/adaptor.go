package adaptor

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"

	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/go-resty/resty/v2"
)

type Adaptor interface {
	AnalyzeSQL(ctx context.Context, request dto.AnalyzeSQLRequest) (dto.AnalyzeSQLResponse, error)
	PlSQLToJSON(ctx context.Context, request dto.PlSQLToJSONRequest) (dto.PlSQLToJSONResponse, error)
	GetPackageBodyDependency(ctx context.Context, request dto.GetPackageBodyDependencyRequest) (dto.GetPackageBodyDependencyResponse, error)

	Ping(ctx context.Context, request dto.PingRequest) (dto.PingResponse, error)
}

// NewObjectParserAdaptor creates a new ObjectParser adaptor based on protocol configuration
func NewObjectParserAdaptor(config *config.ObjectParserConfig) (Adaptor, error) {
	// Default to GRPC if protocol is not specified or empty
	protocol := config.Protocol
	if protocol == "" {
		protocol = "http"
	}

	switch protocol {
	case "http":
		return NewHTTPObjectParserAdaptor(config)
	case "grpc":
		return NewGRPCObjectParserAdaptor(config)
	default:
		return nil, fmt.Errorf("unsupported protocol: %s, supported protocols are: http, grpc", protocol)
	}
}

// NewHTTPObjectParserAdaptor creates a new HTTP-based ObjectParser adaptor
func NewHTTPObjectParserAdaptor(config *config.ObjectParserConfig) (Adaptor, error) {
	// Construct URL with HTTP protocol since Host field now only contains IP/domain
	baseURL := fmt.Sprintf("http://%s:%d", config.Host, config.Port)
	druidUrl, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse ObjectParser URL '%s': %v", baseURL, err)
	}

	log.Infof("Initializing HTTP ObjectParser adaptor with URL: %s", baseURL)

	client := &adaptor{
		host: config.Host,
		port: config.Port,

		druidUrl: druidUrl,
		client:   resty.New(),
	}

	_, err = client.Ping(context.TODO(), dto.PingRequest{})
	if err != nil {
		return nil, fmt.Errorf("failed to ping ObjectParser at %s: %v", baseURL, err)
	}

	log.Infof("Successfully connected to ObjectParser at %s", baseURL)
	return client, nil
}

type adaptor struct {
	host string
	port int

	druidUrl *url.URL
	client   *resty.Client
}

func (a *adaptor) Ping(ctx context.Context, req dto.PingRequest) (dto.PingResponse, error) {
	const api = "ping"

	formData := map[string]string{}

	rsp := dto.PingResponse{}
	err := a.postFormData(ctx, a.getURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) AnalyzeSQL(ctx context.Context, req dto.AnalyzeSQLRequest) (dto.AnalyzeSQLResponse, error) {
	const api = "plsql/analyze"

	formData := map[string]string{
		"plsql":     req.PLSQL,
		"comment":   req.Comment,
		"isEncoded": strconv.FormatBool(req.IsEncoded),
		"jwt":       config.GetGlobalConfig().License,
	}

	rsp := dto.AnalyzeSQLResponse{}
	err := a.postFormData(ctx, a.getURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) PlSQLToJSON(ctx context.Context, req dto.PlSQLToJSONRequest) (dto.PlSQLToJSONResponse, error) {
	const api = "plsql/json"

	formData := map[string]string{
		"plsql":     req.PLSQL,
		"comment":   req.Comment,
		"isEncoded": strconv.FormatBool(req.IsEncoded),
		"jwt":       config.GetGlobalConfig().License,
	}

	rsp := dto.PlSQLToJSONResponse{}
	err := a.postFormData(ctx, a.getURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) GetPackageBodyDependency(ctx context.Context, req dto.GetPackageBodyDependencyRequest) (dto.GetPackageBodyDependencyResponse, error) {
	const api = "plsql/package-body/dependency"

	formData := map[string]string{
		"plsql":       req.PLSQL,
		"ownerName":   req.OwnerName,
		"packageName": req.PackageName,
		"isEncoded":   strconv.FormatBool(req.IsEncoded),
		"jwt":         config.GetGlobalConfig().License,
	}

	rsp := dto.GetPackageBodyDependencyResponse{}
	err := a.postFormData(ctx, a.getURL(), api, formData, &rsp)
	if err != nil {
		return rsp, err
	}
	return rsp, nil
}

func (a *adaptor) getURL() *url.URL {
	return a.druidUrl
}

func (a *adaptor) postFormData(ctx context.Context, druidUrl *url.URL, api string, formData map[string]string, rsp dto.ResponseInterface) error {
	druidUrl.Path = api
	uri := druidUrl.String()

	body, _ := json.Marshal(formData)

	httpResponse, err := a.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		SetResult(rsp).
		SetError(rsp).
		Post(uri)

	if err != nil {
		log.Errorf("postFormData failed, api:%s, err:%v", api, err)
		return err
	}
	log.Debugf("postFormData, validate status code, api:%s, responseLength: %d", api, len(httpResponse.Body()))

	if err = rsp.Validate(); err != nil {
		log.Errorf("postFormData success, but validate failed, api:%s, err:%v", api, err)
		return err
	}
	return nil
}

type ErrorResponse struct {
	Error      string        `json:"error"`
	StatusCode int           `json:"statusCode"`
	Position   ErrorPosition `json:"position"`
}

type ErrorPosition struct {
	Pos      int    `json:"pos"`
	Line     int    `json:"line"`
	LineText string `json:"lineText"`
	Column   int    `json:"column"`
	Token    string `json:"token"`
}

func (i ErrorResponse) Validate() error {
	if i.Error == "" || i.StatusCode == 0 {
		return nil
	}
	return fmt.Errorf("errMsg: %s, statusCode: %d, line:%d, column:%d, token:%s, lineText:%s",
		i.Error, i.StatusCode, i.Position.Line, i.Position.Column, i.Position.Token, i.Position.LineText)
}
