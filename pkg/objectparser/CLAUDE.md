# CLAUDE.md - ObjectParser Package

This file provides essential guidance for working with the ObjectParser package (`pkg/objectparser`) in the TMS (Table
Migration System) project.

## Package Overview

The ObjectParser package orchestrates the complete end-to-end transformation of Oracle PL/SQL code to Java through
intelligent analysis and AI-powered conversion.

**Business Workflow:**

```
Oracle DB → Metadata Extraction → Parser API Analysis → AI Prompt Generation → LLM Conversion → Result Delivery
```

**Core Capabilities:**

- **Oracle Metadata Extraction**: Retrieve PL/SQL procedures, functions, and package bodies with dependencies
- **Advanced Code Analysis**: Leverage external parser services for dependency analysis and incompatible feature
  detection
- **AI-Powered Conversion**: Transform PL/SQL to Java using multiple LLM providers (OpenAI, Azure, Claude, Gemini)
- **Result Management**: Web-based viewing, code downloading, and conversion history tracking

## Architecture Overview

### Directory Structure

```
pkg/objectparser/
├── adaptor/           # External parser service adaptors (HTTP/gRPC)
├── core/              # Core business logic and processing engines
├── dto/               # Data Transfer Objects for API communication
├── queries/           # SQL query templates for Oracle analysis
├── services/          # Service layer with clean interfaces
└── testmocks/         # Generated mocks for testing
```

### Core Components

#### 1. Core Engine (`core/`)

- **`executor.go`**: Main orchestration engine for object parsing tasks
- **`dependency_helper.go`**: Dependency tree building and cycle detection
- **`scorer.go`**: Incompatible feature scoring algorithms
- **`java_code_parser.go`**: Java code analysis and parsing utilities
- **`prompt_strategy.go`**: Unified prompt management strategy with framework detection

#### 2. Service Layer (`services/`)

- **Factory Pattern**: Clean dependency injection via `ServiceFactory`
- **Service Interfaces**: ParsingService, MetadataService, ConversionService, ArchiveService, PromptService
- **Business Logic Separation**: Each service handles specific domain concerns

#### 3. Adaptor Layer (`adaptor/`)

- **Protocol Support**: Both HTTP and gRPC communication with external parser services
- **Connection Management**: Automatic retry and health checking

## Key Workflows

### 1. Complete Object Parsing & Conversion Workflow

```go
// Main execution flow - Oracle to Java complete pipeline
func StartObjectParserTask(ctx context.Context, channelId, taskId int) {
    // Phase 1: Oracle metadata extraction
    oracleObjectDefinitions, err := fetchOracleMetadata(ctx, channelId, taskId)
    
    // Phase 2: Parser API analysis
    analysisResults, err := analyzeWithParserAPI(ctx, oracleObjectDefinitions)
    
    // Phase 3: AI prompt generation
    promptRelations, err := generateIntelligentPrompts(ctx, taskId, analysisResults)
    
    // Phase 4: LLM code conversion
    conversionResults, err := executeAIConversion(ctx, taskId, promptRelations)
    
    // Phase 5: Result management & delivery
    err = finalizeResults(ctx, taskId, conversionResults)
}
```

### 2. Service Factory Pattern

```go
// Create factory with dependencies
factory := NewServiceFactory(adaptor, objectParserRW, taskRW, datasourceRW)

// Create services through factory
parsingService := factory.CreateParsingService()
metadataService := factory.CreateMetadataService()
conversionService := factory.CreateConversionService()
archiveService := factory.CreateArchiveService()
promptService := factory.CreatePromptService()
```

### 3. Oracle Metadata Extraction

```go
// Retrieve object definitions
oracleObjectDefinitions, err := models.GetDatasourceReaderWriter().
    GetOracleObjectDefinitionsBySchemas(ctx, dbConn, targetSchemas, 
    []string{"PACKAGE BODY", "PROCEDURE", "FUNCTION"})

// Get object compilation status
oracleObjectStatuses, err := models.GetDatasourceReaderWriter().
    GetOracleObjectStatusBySchemas(ctx, dbConn, targetSchemas, defaultTypes)

// Fetch dependency relationships
dependencies, err := models.GetDatasourceReaderWriter().
    GetOracleDependencies(ctx, dbConn)
```

### 4. Parser API Analysis

```go
// AST analysis request
analyzeReq := dto.AnalyzeSQLRequest{
    IsEncoded: true,
    PLSQL:     stringutil.EncodeSQL(oracleObjectDefinitionCode.AllText),
}
analyzeResp, err := objectParserAdaptor.AnalyzeSQL(ctx, analyzeReq)

// Package body dependency analysis
depReq := dto.GetPackageBodyDependencyRequest{
    PLSQL:       encodedSQL,
    OwnerName:   schema,
    PackageName: packageName,
}
depResp, err := objectParserAdaptor.GetPackageBodyDependency(ctx, depReq)
```

### 5. AI-Powered Code Conversion

```go
// Build AI conversion request
conversionReq := &message.ConvertPLSQLToJavaReq{
    TaskId:      taskId,
    ObjectUUIDs: selectedObjectUUIDs,
    AIConfig: &message.AIConfig{
        Provider:    "openai",    // Supports multiple AI providers
        Model:      "gpt-4",
        Temperature: 0.1,
        MaxTokens:   4000,
    },
}

// Execute conversion
conversionResp, err := conversionService.ConvertPLSQLToJava(ctx, conversionReq)
```

### 6. Result Management & Download

```go
// Get conversion results (paginated)
resultsResp, page, err := conversionService.GetPLSQLToJavaResults(ctx, &message.GetPLSQLToJavaResultsReq{
    TaskId: taskId,
    Page:   pageInfo,
})

// Download Java code as ZIP
downloadResp, err := conversionService.DownloadJavaCodes(ctx, &message.DownloadJavaCodesReq{
    TaskId:      taskId,
    ObjectUUIDs: selectedUUIDs,
})
```

## Service Interfaces

### ParsingService

Handles PL/SQL parsing and dependency analysis:

- `ParsePLSQLToAST()`: Convert PL/SQL to Abstract Syntax Tree
- `GetDependencyFromMetadata()`: Extract dependency information

### MetadataService

Manages object definitions and compatibility analysis:

- `GetObjectDetail()`: Retrieve detailed object information
- `ListAnalyzeDetail()`: List analysis details with pagination
- `ListBasicIncompatibleFeature()`: Get incompatible Oracle features

### ConversionService

Handles PL/SQL to Java code conversion:

- `TestAIConnect()`: Test AI service connectivity
- `ConvertPLSQLToJava()`: Execute conversion process
- `GetPLSQLToJavaResults()`: Retrieve conversion results
- `DownloadJavaCodes()`: Download generated Java files

### ArchiveService

Provides Oracle archive log analysis:

- `GetArchiveData()`: Retrieve archive log statistics
- `GetTransactionDataBlocks()`: Get transaction data block info

### PromptService

Manages AI prompts for conversion:

- `ListPrompt()`: List available AI prompts
- `SavePrompt()`: Save or update prompts
- `ListTaskObjectPromptRelation()`: Manage task-object-prompt relationships

## Development Guidelines

### Error Handling Patterns

#### Transaction Safety

```go
// Always use transactions for multi-table operations
trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
    // Perform related database operations
    return nil
})
if trxErr != nil {
    log.Errorf("Transaction failed: %v", trxErr)
    return trxErr
}
```

#### Progress Logging

```go
// Use structured progress logging
i.AppendProgressLog(ctx, constants.BuildProgressLog(
    constants.ObjectParserLogType, 
    constants.ObjectParserStepParseAST, 
    fmt.Sprintf("progress:%d/%d, object:%s", idx+1, total, objectName)
))
```

### Dependency Management

#### Dependency Tree Building

```go
// Build intelligent dependency tree for context analysis
func buildIntelligentDependencyTree(dependencies []*objectparser.OracleDependency, targetSchemas []string) (*DependencyAnalysisResult, error) {
    helper := NewOracleDependencyHelper(dependencies, targetSchemas)
    
    // Build node relationships
    nodeMap := helper.BuildTreeNodeChildrenPointers()
    helper.MakeTreeNodeChildrenByPointers(nodeMap)
    
    // Apply filters
    helper.FilterTreeNodeType(nodeMap)
    helper.FilterTreeNodeWithTargetSchema(nodeMap)
    
    // Detect and break cycles
    nodeList := lo.Values(nodeMap)
    helper.FindCycleAndBreak(nodeList, "dependency analysis")
    
    return &DependencyAnalysisResult{
        NodeMap:           nodeMap,
        CycleCount:        helper.GetCycleCount(),
    }, nil
}
```

### Testing Patterns

#### Mock Generation

```bash
# Generate mocks for testing
./testmocks/genmock.sh
```

#### Service Testing

```go
// Use dependency injection for testable services
func TestParsingService(t *testing.T) {
    mockAdaptor := &testmocks.MockAdaptor{}
    service := NewParsingService(mockAdaptor)
    
    result, err := service.ParsePLSQLToAST(ctx, request)
    assert.NoError(t, err)
}
```

## API Integration

### External Parser Service

#### Available APIs

- **`/ping`**: Health check for parser service
- **`/plsql/analyze`**: Analyze PL/SQL code and extract features
- **`/plsql/json`**: Convert PL/SQL to JSON AST representation
- **`/plsql/package-body/dependency`**: Extract package body dependencies

#### Request/Response Patterns

```go
// Standard request structure
type AnalyzeSQLRequest struct {
    IsEncoded bool   `json:"isEncoded"`  // SQL encoding flag
    PLSQL     string `json:"plsql"`      // PL/SQL code (encoded if needed)
    Comment   string `json:"comment"`    // Optional comment
}

// Response with analysis results
type AnalyzeSQLResponse struct {
    MethodInvokeList   []MethodInvoke   `json:"methodInvokeList"`
    IdentifierList     []MethodInvoke   `json:"identifierList"`
    TableReferenceList []TableReference `json:"tableReferenceList"`
    ReservedWordList   []ReservedWord   `json:"reservedWordList"`
    PLSQLSegment       PLSQLSegment     `json:"plsqlSegment"`
}
```

## Performance Considerations

### Memory Management

- **Batch Processing**: Process objects in configurable batches
- **Connection Pooling**: Reuse database connections efficiently
- **JSON Streaming**: Use streaming for large AST structures

### Optimization Techniques

```go
// Efficient dependency tree optimization
helper.FilterTreeNodeType(nodeMap)
helper.FilterTreeNodeWithTargetSchema(nodeMap)
helper.PruneTreeNodeByFilter(nodeMap)

// Cycle detection with proper breaking
cycleNodes := helper.FindCycleAndBreak(nodeList, "analysis phase")
if len(cycleNodes) > 0 {
    log.Warnf("Detected and broke %d cycles", len(cycleNodes))
}
```

## Configuration

### ObjectParser Configuration

```go
type ObjectParserConfig struct {
    Enable   bool   `toml:"enable"`
    Host     string `toml:"host"`
    Port     int    `toml:"port"`
    Protocol string `toml:"protocol"` // "http" or "grpc"
}
```

## Common Patterns

### Progress Tracking

```go
// Always provide progress feedback for long-running operations
for idx, object := range objects {
    i.AppendProgressLog(ctx, constants.BuildProgressLog(
        constants.ObjectParserLogType,
        constants.ObjectParserStepParseAST,
        fmt.Sprintf("processing %d/%d: %s", idx+1, len(objects), object.Name)
    ))
    // Process object...
}
```

### Error Recovery

```go
// Graceful error handling with detailed logging
if analyzeErr != nil {
    log.Errorf("Analysis failed for object %s: %v", objectName, analyzeErr)
    detail.Status = constants.StatStatusFailed.String()
    detail.ErrorDetail = analyzeErr.Error()
    summary.FailedNum += 1
    // Continue processing other objects
}
```

### Resource Cleanup

```go
// Ensure proper cleanup of resources
defer func() {
    if dbConn != nil {
        dbConn.Close()
    }
}()
```

## Integration Points

### Database Models

- **`server/models/objectparser`**: Database entities and DAOs
- **`server/models/datasource`**: Oracle metadata access
- **`server/models/task`**: Task management and progress tracking

### AI Integration

- **`pkg/ai`**: AI provider integration for code conversion
- Context-aware prompt generation with dependency information
- Support for multiple AI providers (OpenAI, Azure, Claude, Gemini)

### Migration Pipeline

- **`pkg/migration`**: Integration with overall migration workflow
- Database connection management via unified interfaces
- Progress reporting to migration orchestrator

## Troubleshooting

### Common Issues

1. **Parser Service Connectivity**
   ```bash
   # Check parser service health
   curl http://parser-host:port/ping
   ```

2. **Memory Issues with Large Objects**
    - Increase batch sizes in configuration
    - Monitor memory usage during processing
    - Consider streaming for very large objects

3. **Dependency Cycles**
    - Review dependency analysis logs
    - Check for self-referencing objects
    - Validate package body relationships

### Debugging Tips

```go
// Enable detailed logging for development
log.SetLevel(log.DebugLevel)

// Use helper status for debugging
helper := NewOracleDependencyHelper(dependencies, schemas)
log.Debugf("Helper status: %s", helper.GetStatus())
```

## Recent Optimizations (Commit 9e17c9f3)

### 1. Unified Prompt Strategy Pattern
- Introduced `ConversionPromptStrategy` class for centralized prompt management
- Clear separation between TRIGGER, CODE, and TABLE prompt handling
- Intelligent framework detection with priority-based selection
- Reduced code duplication through helper methods

### 2. Enhanced Error Handling
- Added comprehensive validation in `ValidateStrategy()`
- Null-safety checks throughout prompt selection logic
- Proper error aggregation for batch operations
- Graceful fallback mechanisms for missing prompts

### 3. Performance Improvements
- Optimized map operations in `DetectFrameworks()`
- Reduced redundant framework detection logic
- Lazy initialization patterns where applicable

### 4. Code Quality Enhancements
- Extracted common framework logic into reusable methods (`registerFramework`, `requiresCoupledConversion`)
- Consolidated duplicate table DDL fetching logic
- Improved logging with detailed statistics
- Better separation of concerns in prompt selection

## Best Practices

1. **Always use the ServiceFactory**: Ensures proper dependency injection
2. **Wrap database operations in transactions**: Maintains data consistency
3. **Provide detailed progress logging**: Improves user experience and debugging
4. **Handle cycles gracefully**: Use provided cycle detection and breaking algorithms
5. **Validate external service responses**: Check all DTOs for proper validation
6. **Use batch processing**: Process large object sets efficiently
7. **Implement proper error recovery**: Continue processing when individual objects fail
8. **Monitor resource usage**: Track memory and connection usage for large migrations
9. **Validate prompt strategies**: Always call `ValidateStrategy()` before conversion

---

*This documentation is optimized for performance and focuses on essential ObjectParser functionality. Update as the
package evolves.*