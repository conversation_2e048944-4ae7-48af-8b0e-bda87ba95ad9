# Oracle触发器到ORM框架转换调研报告

## 一、调研背景

### 1.1 研究目的

在将Oracle数据库应用迁移到Java ORM框架（Hibernate、MyBatis、MyBatis-Plus）的过程中，需要将数据库层的触发器逻辑转换为应用层实现。本报告旨在提供完整的转换方案和最佳实践。

### 1.2 Oracle触发器概述

Oracle触发器是独立的数据库对象，与FUNCTION、PROCEDURE、TABLE等处于同一级别。在`ALL_OBJECTS`视图中，触发器的`OBJECT_TYPE`
值为'TRIGGER'。

Oracle支持的触发器类型：

- **表触发器（Table Triggers）**：响应DML操作（INSERT、UPDATE、DELETE）
- **视图触发器（View Triggers）**：INSTEAD OF触发器，处理不可更新视图
- **模式触发器（Schema Triggers）**：响应DDL事件
- **数据库触发器（Database Triggers）**：响应数据库级事件

### 1.3 转换挑战

- 事务一致性保证
- 触发时机的精确控制
- 跨表操作的处理
- 性能影响评估

## 二、转换模板

### 2.1 Java原生JDBC转换模板

#### 2.1.1 基础实体类

```java
package com.example.jdbc.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

public class Employee implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String name;
    private BigDecimal salary;
    private Long departmentId;
    private Timestamp createdTime;
    private String createdBy;
    private Timestamp updatedTime;
    private String updatedBy;
    private Long version;
    private Integer deleted;
    
    // Constructors
    public Employee() {}
    
    public Employee(String name, BigDecimal salary, Long departmentId) {
        this.name = name;
        this.salary = salary;
        this.departmentId = departmentId;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public BigDecimal getSalary() { return salary; }
    public void setSalary(BigDecimal salary) { this.salary = salary; }
    
    public Long getDepartmentId() { return departmentId; }
    public void setDepartmentId(Long departmentId) { this.departmentId = departmentId; }
    
    public Timestamp getCreatedTime() { return createdTime; }
    public void setCreatedTime(Timestamp createdTime) { this.createdTime = createdTime; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public Timestamp getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(Timestamp updatedTime) { this.updatedTime = updatedTime; }
    
    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    
    public Long getVersion() { return version; }
    public void setVersion(Long version) { this.version = version; }
    
    public Integer getDeleted() { return deleted; }
    public void setDeleted(Integer deleted) { this.deleted = deleted; }
}
```

#### 2.1.2 触发器管理器

```java
package com.example.jdbc.trigger;

import com.example.jdbc.entity.Employee;
import com.example.jdbc.entity.AuditLog;
import com.example.jdbc.util.SecurityContext;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class TriggerManager {
    private static final Logger logger = Logger.getLogger(TriggerManager.class.getName());
    
    // 触发器类型枚举
    public enum TriggerType {
        BEFORE_INSERT, AFTER_INSERT,
        BEFORE_UPDATE, AFTER_UPDATE,
        BEFORE_DELETE, AFTER_DELETE
    }
    
    // 触发器接口
    public interface Trigger {
        void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException;
    }
    
    // 触发器注册表
    private final List<TriggerRegistration> triggers = new ArrayList<>();
    
    // 触发器注册信息
    private static class TriggerRegistration {
        TriggerType type;
        Trigger trigger;
        int priority; // 执行优先级
        
        TriggerRegistration(TriggerType type, Trigger trigger, int priority) {
            this.type = type;
            this.trigger = trigger;
            this.priority = priority;
        }
    }
    
    // 构造函数，注册所有触发器
    public TriggerManager() {
        registerDefaultTriggers();
    }
    
    // 注册默认触发器
    private void registerDefaultTriggers() {
        // BEFORE INSERT 触发器
        registerTrigger(TriggerType.BEFORE_INSERT, new Trigger() {
            @Override
            public void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
                // 设置创建时间和更新时间
                Timestamp now = new Timestamp(System.currentTimeMillis());
                String currentUser = SecurityContext.getCurrentUser();
                
                employee.setCreatedTime(now);
                employee.setCreatedBy(currentUser);
                employee.setUpdatedTime(now);
                employee.setUpdatedBy(currentUser);
                
                if (employee.getDeleted() == null) {
                    employee.setDeleted(0);
                }
                
                // 业务验证
                validateEmployee(employee, true);
                
                // 自动设置默认部门
                if (employee.getDepartmentId() == null) {
                    employee.setDepartmentId(1L);
                }
                
                logger.info("BEFORE INSERT trigger executed for employee: " + employee.getName());
            }
        }, 100);
        
        // AFTER INSERT 触发器
        registerTrigger(TriggerType.AFTER_INSERT, new Trigger() {
            @Override
            public void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
                // 记录审计日志
                saveAuditLog(conn, "INSERT", employee, null);
                
                // 发送新员工通知
                sendNotification(conn, "NEW_EMPLOYEE", employee);
                
                // 初始化员工相关数据
                initializeEmployeeData(conn, employee);
                
                logger.info("AFTER INSERT trigger executed for employee ID: " + employee.getId());
            }
        }, 100);
        
        // BEFORE UPDATE 触发器
        registerTrigger(TriggerType.BEFORE_UPDATE, new Trigger() {
            @Override
            public void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
                // 更新时间戳
                employee.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
                employee.setUpdatedBy(SecurityContext.getCurrentUser());
                
                // 保护创建信息不被修改
                if (oldEmployee != null) {
                    employee.setCreatedTime(oldEmployee.getCreatedTime());
                    employee.setCreatedBy(oldEmployee.getCreatedBy());
                }
                
                // 业务验证
                validateEmployee(employee, false);
                
                // 版本控制（乐观锁）
                if (oldEmployee != null && !oldEmployee.getVersion().equals(employee.getVersion())) {
                    throw new SQLException("Concurrent modification detected");
                }
                employee.setVersion(employee.getVersion() + 1);
                
                logger.info("BEFORE UPDATE trigger executed for employee ID: " + employee.getId());
            }
        }, 100);
        
        // AFTER UPDATE 触发器
        registerTrigger(TriggerType.AFTER_UPDATE, new Trigger() {
            @Override
            public void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
                // 记录审计日志
                saveAuditLog(conn, "UPDATE", employee, oldEmployee);
                
                // 检测重要变更
                if (oldEmployee != null) {
                    detectSignificantChanges(conn, oldEmployee, employee);
                }
                
                // 同步相关表
                syncRelatedTables(conn, employee);
                
                logger.info("AFTER UPDATE trigger executed for employee ID: " + employee.getId());
            }
        }, 100);
        
        // BEFORE DELETE 触发器
        registerTrigger(TriggerType.BEFORE_DELETE, new Trigger() {
            @Override
            public void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
                // 检查关联数据
                if (hasActiveProjects(conn, employee.getId())) {
                    throw new SQLException("Cannot delete employee with active projects");
                }
                
                // 检查业务规则
                if (employee.getSalary() != null && employee.getSalary().compareTo(new BigDecimal("100000")) > 0) {
                    throw new SQLException("Cannot delete high-salary employees directly");
                }
                
                logger.info("BEFORE DELETE trigger executed for employee ID: " + employee.getId());
            }
        }, 100);
        
        // AFTER DELETE 触发器
        registerTrigger(TriggerType.AFTER_DELETE, new Trigger() {
            @Override
            public void execute(Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
                // 记录审计日志
                saveAuditLog(conn, "DELETE", null, employee);
                
                // 归档员工数据
                archiveEmployee(conn, employee);
                
                // 清理相关数据
                cleanupRelatedData(conn, employee.getId());
                
                logger.info("AFTER DELETE trigger executed for employee ID: " + employee.getId());
            }
        }, 100);
    }
    
    // 注册触发器
    public void registerTrigger(TriggerType type, Trigger trigger, int priority) {
        triggers.add(new TriggerRegistration(type, trigger, priority));
        triggers.sort((a, b) -> Integer.compare(b.priority, a.priority)); // 按优先级排序
    }
    
    // 执行触发器
    public void executeTriggers(TriggerType type, Connection conn, Employee employee, Employee oldEmployee) throws SQLException {
        for (TriggerRegistration reg : triggers) {
            if (reg.type == type) {
                reg.trigger.execute(conn, employee, oldEmployee);
            }
        }
    }
    
    // 验证员工数据
    private void validateEmployee(Employee employee, boolean isInsert) throws SQLException {
        if (employee.getName() == null || employee.getName().trim().isEmpty()) {
            throw new SQLException("Employee name is required");
        }
        
        if (employee.getSalary() != null) {
            if (employee.getSalary().compareTo(BigDecimal.ZERO) < 0) {
                throw new SQLException("Salary cannot be negative");
            }
            if (employee.getSalary().compareTo(new BigDecimal("1000000")) > 0) {
                throw new SQLException("Salary exceeds maximum limit");
            }
        } else if (isInsert) {
            throw new SQLException("Salary is required for new employees");
        }
    }
    
    // 保存审计日志
    private void saveAuditLog(Connection conn, String operation, Employee newEmployee, Employee oldEmployee) throws SQLException {
        String sql = "INSERT INTO audit_log (table_name, operation, record_id, old_value, new_value, " +
                    "operation_time, operation_user) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, "EMPLOYEE");
            ps.setString(2, operation);
            ps.setLong(3, newEmployee != null ? newEmployee.getId() : oldEmployee.getId());
            ps.setString(4, oldEmployee != null ? oldEmployee.toString() : null);
            ps.setString(5, newEmployee != null ? newEmployee.toString() : null);
            ps.setTimestamp(6, new Timestamp(System.currentTimeMillis()));
            ps.setString(7, SecurityContext.getCurrentUser());
            ps.executeUpdate();
        }
    }
    
    // 检查是否有活动项目
    private boolean hasActiveProjects(Connection conn, Long employeeId) throws SQLException {
        String sql = "SELECT COUNT(*) FROM project WHERE manager_id = ? AND status = 'ACTIVE'";
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setLong(1, employeeId);
            try (ResultSet rs = ps.executeQuery()) {
                return rs.next() && rs.getInt(1) > 0;
            }
        }
    }
    
    // 检测重要变更
    private void detectSignificantChanges(Connection conn, Employee oldEmployee, Employee newEmployee) throws SQLException {
        if (oldEmployee.getSalary() != null && newEmployee.getSalary() != null) {
            BigDecimal change = newEmployee.getSalary().subtract(oldEmployee.getSalary());
            if (change.abs().compareTo(new BigDecimal("10000")) > 0) {
                sendNotification(conn, "SIGNIFICANT_SALARY_CHANGE", newEmployee);
            }
        }
        
        if (!oldEmployee.getDepartmentId().equals(newEmployee.getDepartmentId())) {
            sendNotification(conn, "DEPARTMENT_CHANGE", newEmployee);
        }
    }
    
    // 发送通知
    private void sendNotification(Connection conn, String type, Employee employee) throws SQLException {
        String sql = "INSERT INTO notifications (type, employee_id, message, created_time) VALUES (?, ?, ?, ?)";
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, type);
            ps.setLong(2, employee.getId());
            ps.setString(3, "Notification for " + type);
            ps.setTimestamp(4, new Timestamp(System.currentTimeMillis()));
            ps.executeUpdate();
        }
    }
    
    // 初始化员工数据
    private void initializeEmployeeData(Connection conn, Employee employee) throws SQLException {
        // 初始化工作空间、权限等
        logger.info("Initializing data for employee: " + employee.getId());
    }
    
    // 同步相关表
    private void syncRelatedTables(Connection conn, Employee employee) throws SQLException {
        // 更新相关表中的员工信息
        logger.info("Syncing related tables for employee: " + employee.getId());
    }
    
    // 归档员工
    private void archiveEmployee(Connection conn, Employee employee) throws SQLException {
        String sql = "INSERT INTO employee_archive SELECT * FROM employee WHERE id = ?";
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setLong(1, employee.getId());
            ps.executeUpdate();
        }
    }
    
    // 清理相关数据
    private void cleanupRelatedData(Connection conn, Long employeeId) throws SQLException {
        // 清理权限、工作空间等
        logger.info("Cleaning up data for employee: " + employeeId);
    }
}
```

#### 2.1.3 DAO实现类

```java
package com.example.jdbc.dao;

import com.example.jdbc.entity.Employee;
import com.example.jdbc.trigger.TriggerManager;
import com.example.jdbc.trigger.TriggerManager.TriggerType;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class EmployeeDAO {
    private static final Logger logger = Logger.getLogger(EmployeeDAO.class.getName());
    private final TriggerManager triggerManager;
    private Connection connection;
    
    public EmployeeDAO(Connection connection) {
        this.connection = connection;
        this.triggerManager = new TriggerManager();
    }
    
    // INSERT操作
    public Employee insert(Employee employee) throws SQLException {
        Connection conn = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        boolean autoCommit = connection.getAutoCommit();
        
        try {
            conn = connection;
            conn.setAutoCommit(false); // 开启事务
            
            // 执行BEFORE INSERT触发器
            triggerManager.executeTriggers(TriggerType.BEFORE_INSERT, conn, employee, null);
            
            // 执行INSERT
            String sql = "INSERT INTO employee (name, salary, department_id, created_time, created_by, " +
                        "updated_time, updated_by, version, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            ps = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            ps.setString(1, employee.getName());
            ps.setBigDecimal(2, employee.getSalary());
            ps.setLong(3, employee.getDepartmentId());
            ps.setTimestamp(4, employee.getCreatedTime());
            ps.setString(5, employee.getCreatedBy());
            ps.setTimestamp(6, employee.getUpdatedTime());
            ps.setString(7, employee.getUpdatedBy());
            ps.setLong(8, 1L); // 初始版本号
            ps.setInt(9, employee.getDeleted());
            
            int affectedRows = ps.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Creating employee failed, no rows affected.");
            }
            
            // 获取生成的ID
            rs = ps.getGeneratedKeys();
            if (rs.next()) {
                employee.setId(rs.getLong(1));
                employee.setVersion(1L);
            } else {
                throw new SQLException("Creating employee failed, no ID obtained.");
            }
            
            // 执行AFTER INSERT触发器
            triggerManager.executeTriggers(TriggerType.AFTER_INSERT, conn, employee, null);
            
            conn.commit(); // 提交事务
            
            logger.info("Employee inserted successfully with ID: " + employee.getId());
            return employee;
            
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback(); // 回滚事务
                    logger.severe("Transaction rolled back due to: " + e.getMessage());
                } catch (SQLException ex) {
                    logger.severe("Error rolling back transaction: " + ex.getMessage());
                }
            }
            throw e;
        } finally {
            // 恢复自动提交设置
            if (conn != null) {
                conn.setAutoCommit(autoCommit);
            }
            // 关闭资源
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
    }
    
    // UPDATE操作
    public Employee update(Employee employee) throws SQLException {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean autoCommit = connection.getAutoCommit();
        
        try {
            conn = connection;
            conn.setAutoCommit(false); // 开启事务
            
            // 获取原始数据
            Employee oldEmployee = findById(employee.getId());
            if (oldEmployee == null) {
                throw new SQLException("Employee not found with ID: " + employee.getId());
            }
            
            // 执行BEFORE UPDATE触发器
            triggerManager.executeTriggers(TriggerType.BEFORE_UPDATE, conn, employee, oldEmployee);
            
            // 执行UPDATE
            String sql = "UPDATE employee SET name = ?, salary = ?, department_id = ?, " +
                        "updated_time = ?, updated_by = ?, version = ? " +
                        "WHERE id = ? AND version = ?";
            
            ps = conn.prepareStatement(sql);
            ps.setString(1, employee.getName());
            ps.setBigDecimal(2, employee.getSalary());
            ps.setLong(3, employee.getDepartmentId());
            ps.setTimestamp(4, employee.getUpdatedTime());
            ps.setString(5, employee.getUpdatedBy());
            ps.setLong(6, employee.getVersion());
            ps.setLong(7, employee.getId());
            ps.setLong(8, oldEmployee.getVersion());
            
            int affectedRows = ps.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Update failed, possibly due to concurrent modification");
            }
            
            // 执行AFTER UPDATE触发器
            triggerManager.executeTriggers(TriggerType.AFTER_UPDATE, conn, employee, oldEmployee);
            
            conn.commit(); // 提交事务
            
            logger.info("Employee updated successfully with ID: " + employee.getId());
            return employee;
            
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback(); // 回滚事务
                    logger.severe("Transaction rolled back due to: " + e.getMessage());
                } catch (SQLException ex) {
                    logger.severe("Error rolling back transaction: " + ex.getMessage());
                }
            }
            throw e;
        } finally {
            if (conn != null) {
                conn.setAutoCommit(autoCommit);
            }
            if (ps != null) ps.close();
        }
    }
    
    // DELETE操作
    public void delete(Long id) throws SQLException {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean autoCommit = connection.getAutoCommit();
        
        try {
            conn = connection;
            conn.setAutoCommit(false); // 开启事务
            
            // 获取要删除的数据
            Employee employee = findById(id);
            if (employee == null) {
                throw new SQLException("Employee not found with ID: " + id);
            }
            
            // 执行BEFORE DELETE触发器
            triggerManager.executeTriggers(TriggerType.BEFORE_DELETE, conn, employee, null);
            
            // 执行DELETE（或软删除）
            String sql = "UPDATE employee SET deleted = 1, updated_time = ?, updated_by = ? WHERE id = ?";
            // 如果是物理删除，使用: "DELETE FROM employee WHERE id = ?"
            
            ps = conn.prepareStatement(sql);
            ps.setTimestamp(1, new Timestamp(System.currentTimeMillis()));
            ps.setString(2, SecurityContext.getCurrentUser());
            ps.setLong(3, id);
            
            int affectedRows = ps.executeUpdate();
            
            if (affectedRows == 0) {
                throw new SQLException("Delete failed, no rows affected");
            }
            
            // 执行AFTER DELETE触发器
            triggerManager.executeTriggers(TriggerType.AFTER_DELETE, conn, employee, null);
            
            conn.commit(); // 提交事务
            
            logger.info("Employee deleted successfully with ID: " + id);
            
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback(); // 回滚事务
                    logger.severe("Transaction rolled back due to: " + e.getMessage());
                } catch (SQLException ex) {
                    logger.severe("Error rolling back transaction: " + ex.getMessage());
                }
            }
            throw e;
        } finally {
            if (conn != null) {
                conn.setAutoCommit(autoCommit);
            }
            if (ps != null) ps.close();
        }
    }
    
    // 批量INSERT（演示语句级触发器）
    public void batchInsert(List<Employee> employees) throws SQLException {
        Connection conn = null;
        PreparedStatement ps = null;
        boolean autoCommit = connection.getAutoCommit();
        
        try {
            conn = connection;
            conn.setAutoCommit(false);
            
            // 语句级BEFORE触发器
            logger.info("Executing statement-level BEFORE INSERT trigger for " + employees.size() + " employees");
            validateBatchInsert(employees);
            
            String sql = "INSERT INTO employee (name, salary, department_id, created_time, created_by, " +
                        "updated_time, updated_by, version, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            ps = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            
            for (Employee employee : employees) {
                // 行级BEFORE触发器
                triggerManager.executeTriggers(TriggerType.BEFORE_INSERT, conn, employee, null);
                
                ps.setString(1, employee.getName());
                ps.setBigDecimal(2, employee.getSalary());
                ps.setLong(3, employee.getDepartmentId());
                ps.setTimestamp(4, employee.getCreatedTime());
                ps.setString(5, employee.getCreatedBy());
                ps.setTimestamp(6, employee.getUpdatedTime());
                ps.setString(7, employee.getUpdatedBy());
                ps.setLong(8, 1L);
                ps.setInt(9, employee.getDeleted());
                
                ps.addBatch();
            }
            
            int[] results = ps.executeBatch();
            
            // 获取生成的ID
            ResultSet rs = ps.getGeneratedKeys();
            int index = 0;
            while (rs.next() && index < employees.size()) {
                employees.get(index).setId(rs.getLong(1));
                employees.get(index).setVersion(1L);
                
                // 行级AFTER触发器
                triggerManager.executeTriggers(TriggerType.AFTER_INSERT, conn, employees.get(index), null);
                index++;
            }
            
            // 语句级AFTER触发器
            logger.info("Executing statement-level AFTER INSERT trigger for " + employees.size() + " employees");
            auditBatchOperation(conn, "BATCH_INSERT", employees.size());
            
            conn.commit();
            
        } catch (SQLException e) {
            if (conn != null) {
                conn.rollback();
            }
            throw e;
        } finally {
            if (conn != null) {
                conn.setAutoCommit(autoCommit);
            }
            if (ps != null) ps.close();
        }
    }
    
    // 查询操作（不触发触发器）
    public Employee findById(Long id) throws SQLException {
        String sql = "SELECT * FROM employee WHERE id = ? AND deleted = 0";
        try (PreparedStatement ps = connection.prepareStatement(sql)) {
            ps.setLong(1, id);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToEmployee(rs);
                }
            }
        }
        return null;
    }
    
    // 映射ResultSet到Employee
    private Employee mapResultSetToEmployee(ResultSet rs) throws SQLException {
        Employee employee = new Employee();
        employee.setId(rs.getLong("id"));
        employee.setName(rs.getString("name"));
        employee.setSalary(rs.getBigDecimal("salary"));
        employee.setDepartmentId(rs.getLong("department_id"));
        employee.setCreatedTime(rs.getTimestamp("created_time"));
        employee.setCreatedBy(rs.getString("created_by"));
        employee.setUpdatedTime(rs.getTimestamp("updated_time"));
        employee.setUpdatedBy(rs.getString("updated_by"));
        employee.setVersion(rs.getLong("version"));
        employee.setDeleted(rs.getInt("deleted"));
        return employee;
    }
    
    // 批量操作验证
    private void validateBatchInsert(List<Employee> employees) throws SQLException {
        if (employees.size() > 1000) {
            throw new SQLException("Batch size exceeds maximum limit of 1000");
        }
        
        BigDecimal totalSalary = BigDecimal.ZERO;
        for (Employee emp : employees) {
            if (emp.getSalary() != null) {
                totalSalary = totalSalary.add(emp.getSalary());
            }
        }
        
        if (totalSalary.compareTo(new BigDecimal("10000000")) > 0) {
            throw new SQLException("Total salary for batch insert exceeds budget limit");
        }
    }
    
    // 批量操作审计
    private void auditBatchOperation(Connection conn, String operation, int recordCount) throws SQLException {
        String sql = "INSERT INTO batch_audit_log (operation, record_count, operation_time, operation_user) " +
                    "VALUES (?, ?, ?, ?)";
        try (PreparedStatement ps = conn.prepareStatement(sql)) {
            ps.setString(1, operation);
            ps.setInt(2, recordCount);
            ps.setTimestamp(3, new Timestamp(System.currentTimeMillis()));
            ps.setString(4, SecurityContext.getCurrentUser());
            ps.executeUpdate();
        }
    }
}
```

#### 2.1.4 使用示例

```java
package com.example.jdbc;

import com.example.jdbc.dao.EmployeeDAO;
import com.example.jdbc.entity.Employee;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

public class JdbcTriggerExample {
    
    public static void main(String[] args) {
        String url = "***********************************";
        String username = "system";
        String password = "password";
        
        try (Connection connection = DriverManager.getConnection(url, username, password)) {
            
            EmployeeDAO employeeDAO = new EmployeeDAO(connection);
            
            // 1. INSERT操作 - 触发BEFORE INSERT和AFTER INSERT
            Employee newEmployee = new Employee();
            newEmployee.setName("John Doe");
            newEmployee.setSalary(new BigDecimal("75000"));
            newEmployee.setDepartmentId(10L);
            
            Employee insertedEmployee = employeeDAO.insert(newEmployee);
            System.out.println("Inserted employee ID: " + insertedEmployee.getId());
            
            // 2. UPDATE操作 - 触发BEFORE UPDATE和AFTER UPDATE
            insertedEmployee.setSalary(new BigDecimal("85000"));
            Employee updatedEmployee = employeeDAO.update(insertedEmployee);
            System.out.println("Updated employee version: " + updatedEmployee.getVersion());
            
            // 3. DELETE操作 - 触发BEFORE DELETE和AFTER DELETE
            employeeDAO.delete(insertedEmployee.getId());
            System.out.println("Employee deleted successfully");
            
            // 4. 批量INSERT - 演示语句级触发器
            List<Employee> employees = Arrays.asList(
                new Employee("Alice", new BigDecimal("60000"), 20L),
                new Employee("Bob", new BigDecimal("65000"), 20L),
                new Employee("Charlie", new BigDecimal("70000"), 30L)
            );
            
            employeeDAO.batchInsert(employees);
            System.out.println("Batch insert completed");
            
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
```

### 2.2 Hibernate + JPA 转换模板（不使用Spring）

#### 2.1.1 实体类及监听器

```java
package com.example.hibernate.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "EMPLOYEE")
@EntityListeners(EmployeeEntityListener.class)
@NamedQueries({
    @NamedQuery(name = "Employee.findAll", query = "SELECT e FROM Employee e"),
    @NamedQuery(name = "Employee.findByName", query = "SELECT e FROM Employee e WHERE e.name = :name")
})
public class Employee {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "emp_seq")
    @SequenceGenerator(name = "emp_seq", sequenceName = "EMPLOYEE_SEQ", allocationSize = 1)
    private Long id;
    
    @Column(name = "name", nullable = false, length = 100)
    private String name;
    
    @Column(name = "salary", precision = 10, scale = 2)
    private BigDecimal salary;
    
    @Column(name = "department_id")
    private Long departmentId;
    
    @Column(name = "created_time", updatable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "created_by", updatable = false)
    private String createdBy;
    
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @Column(name = "updated_by")
    private String updatedBy;
    
    @Version
    @Column(name = "version")
    private Long version;
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public BigDecimal getSalary() { return salary; }
    public void setSalary(BigDecimal salary) { this.salary = salary; }
    
    public Long getDepartmentId() { return departmentId; }
    public void setDepartmentId(Long departmentId) { this.departmentId = departmentId; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(LocalDateTime updatedTime) { this.updatedTime = updatedTime; }
    
    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    
    public Long getVersion() { return version; }
    public void setVersion(Long version) { this.version = version; }
}
```

```java
package com.example.hibernate.listener;

import com.example.hibernate.entity.Employee;
import com.example.hibernate.entity.AuditLog;
import com.example.hibernate.util.SecurityContext;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.logging.Logger;

public class EmployeeEntityListener {
    private static final Logger logger = Logger.getLogger(EmployeeEntityListener.class.getName());
    
    @PrePersist
    public void beforeInsert(Employee employee) {
        // BEFORE INSERT 触发器逻辑
        LocalDateTime now = LocalDateTime.now();
        String currentUser = SecurityContext.getCurrentUser();
        
        employee.setCreatedTime(now);
        employee.setUpdatedTime(now);
        employee.setCreatedBy(currentUser);
        employee.setUpdatedBy(currentUser);
        
        // 业务规则验证
        validateSalary(employee);
        validateDepartment(employee);
        
        // 自动计算字段
        if (employee.getSalary() != null && employee.getSalary().compareTo(new BigDecimal("100000")) > 0) {
            // 高薪员工自动标记
            logger.info("High salary employee detected: " + employee.getName());
        }
    }
    
    @PreUpdate
    public void beforeUpdate(Employee employee) {
        // BEFORE UPDATE 触发器逻辑
        employee.setUpdatedTime(LocalDateTime.now());
        employee.setUpdatedBy(SecurityContext.getCurrentUser());
        
        // 业务规则验证
        validateSalary(employee);
        
        // 防止关键字段修改
        EntityManager em = getCurrentEntityManager();
        Employee original = em.find(Employee.class, employee.getId());
        if (original != null && !original.getCreatedBy().equals(employee.getCreatedBy())) {
            throw new IllegalStateException("Cannot modify created_by field");
        }
    }
    
    @PreRemove
    public void beforeDelete(Employee employee) {
        // BEFORE DELETE 触发器逻辑
        // 检查是否有关联数据
        EntityManager em = getCurrentEntityManager();
        Query query = em.createQuery("SELECT COUNT(p) FROM Project p WHERE p.managerId = :empId");
        query.setParameter("empId", employee.getId());
        Long count = (Long) query.getSingleResult();
        
        if (count > 0) {
            throw new IllegalStateException("Cannot delete employee with active projects");
        }
    }
    
    @PostPersist
    public void afterInsert(Employee employee) {
        // AFTER INSERT 触发器逻辑
        auditLog(employee, "INSERT");
        
        // 发送通知
        notifyHR(employee, "NEW_EMPLOYEE");
    }
    
    @PostUpdate
    public void afterUpdate(Employee employee) {
        // AFTER UPDATE 触发器逻辑
        auditLog(employee, "UPDATE");
        
        // 检测重要变更
        EntityManager em = getCurrentEntityManager();
        em.detach(employee);
        Employee original = em.find(Employee.class, employee.getId());
        
        if (original != null && original.getSalary() != null && employee.getSalary() != null) {
            BigDecimal salaryChange = employee.getSalary().subtract(original.getSalary());
            if (salaryChange.abs().compareTo(new BigDecimal("10000")) > 0) {
                notifyHR(employee, "SIGNIFICANT_SALARY_CHANGE");
            }
        }
    }
    
    @PostRemove
    public void afterDelete(Employee employee) {
        // AFTER DELETE 触发器逻辑
        auditLog(employee, "DELETE");
        
        // 归档删除的记录
        archiveEmployee(employee);
    }
    
    private void validateSalary(Employee employee) {
        if (employee.getSalary() == null) {
            throw new IllegalArgumentException("Salary cannot be null");
        }
        if (employee.getSalary().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Salary cannot be negative");
        }
        if (employee.getSalary().compareTo(new BigDecimal("1000000")) > 0) {
            throw new IllegalArgumentException("Salary exceeds maximum limit");
        }
    }
    
    private void validateDepartment(Employee employee) {
        if (employee.getDepartmentId() == null) {
            throw new IllegalArgumentException("Department is required");
        }
    }
    
    private void auditLog(Employee employee, String operation) {
        EntityManagerFactory emf = Persistence.createEntityManagerFactory("audit-unit");
        EntityManager em = emf.createEntityManager();
        EntityTransaction tx = em.getTransaction();
        
        try {
            tx.begin();
            
            AuditLog log = new AuditLog();
            log.setTableName("EMPLOYEE");
            log.setOperation(operation);
            log.setRecordId(employee.getId());
            log.setOperationTime(LocalDateTime.now());
            log.setOperationUser(SecurityContext.getCurrentUser());
            log.setOldValue(operation.equals("DELETE") ? employee.toString() : null);
            log.setNewValue(operation.equals("DELETE") ? null : employee.toString());
            
            em.persist(log);
            tx.commit();
        } catch (Exception e) {
            if (tx.isActive()) tx.rollback();
            logger.severe("Failed to create audit log: " + e.getMessage());
        } finally {
            em.close();
            emf.close();
        }
    }
    
    private void notifyHR(Employee employee, String eventType) {
        // 通知逻辑
        logger.info("HR Notification: " + eventType + " for employee " + employee.getId());
    }
    
    private void archiveEmployee(Employee employee) {
        // 归档逻辑
        logger.info("Archiving employee: " + employee.getId());
    }
    
    private EntityManager getCurrentEntityManager() {
        // 获取当前EntityManager的辅助方法
        return Persistence.createEntityManagerFactory("main-unit").createEntityManager();
    }
}
```

### 2.3 MyBatis-Plus 转换模板

#### 2.2.1 实体类

```java
package com.example.mybatisplus.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@TableName("employee")
public class Employee extends Model<Employee> implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("name")
    private String name;
    
    @TableField("salary")
    private BigDecimal salary;
    
    @TableField("department_id")
    private Long departmentId;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;
    
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
    
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;
    
    @Version
    @TableField("version")
    private Long version;
    
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public BigDecimal getSalary() { return salary; }
    public void setSalary(BigDecimal salary) { this.salary = salary; }
    
    public Long getDepartmentId() { return departmentId; }
    public void setDepartmentId(Long departmentId) { this.departmentId = departmentId; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(LocalDateTime updatedTime) { this.updatedTime = updatedTime; }
    
    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    
    public Long getVersion() { return version; }
    public void setVersion(Long version) { this.version = version; }
    
    public Integer getDeleted() { return deleted; }
    public void setDeleted(Integer deleted) { this.deleted = deleted; }
    
    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
```

#### 2.2.2 元数据处理器

```java
package com.example.mybatisplus.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.example.mybatisplus.util.SecurityContext;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
public class TriggerMetaObjectHandler implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        // BEFORE INSERT 触发器逻辑
        LocalDateTime now = LocalDateTime.now();
        String currentUser = SecurityContext.getCurrentUser();
        
        // 自动填充时间戳
        this.strictInsertFill(metaObject, "createdTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "updatedTime", LocalDateTime.class, now);
        this.strictInsertFill(metaObject, "createdBy", String.class, currentUser);
        this.strictInsertFill(metaObject, "updatedBy", String.class, currentUser);
        
        // 设置默认值
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        
        // 业务验证
        BigDecimal salary = (BigDecimal) metaObject.getValue("salary");
        if (salary != null) {
            if (salary.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("Salary cannot be negative");
            }
            if (salary.compareTo(new BigDecimal("1000000")) > 0) {
                throw new IllegalArgumentException("Salary exceeds maximum limit");
            }
        }
        
        // 自动计算字段
        Long departmentId = (Long) metaObject.getValue("departmentId");
        if (departmentId == null) {
            // 设置默认部门
            metaObject.setValue("departmentId", 1L);
        }
    }
    
    @Override
    public void updateFill(MetaObject metaObject) {
        // BEFORE UPDATE 触发器逻辑
        this.strictUpdateFill(metaObject, "updatedTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updatedBy", String.class, SecurityContext.getCurrentUser());
        
        // 验证更新规则
        BigDecimal salary = (BigDecimal) metaObject.getValue("salary");
        if (salary != null && salary.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Salary cannot be negative");
        }
    }
}
```

#### 2.2.3 拦截器

```java
package com.example.mybatisplus.interceptor;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.example.mybatisplus.entity.AuditLog;
import com.example.mybatisplus.entity.Employee;
import com.example.mybatisplus.mapper.AuditLogMapper;
import com.example.mybatisplus.util.SpringContextHolder;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Properties;

@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", 
               args = {MappedStatement.class, Object.class})
})
public class TriggerInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        
        // BEFORE 触发器逻辑（对于特殊情况）
        if (parameter instanceof Employee) {
            Employee employee = (Employee) parameter;
            beforeTrigger(employee, sqlCommandType);
        }
        
        // 执行原SQL
        Object result = invocation.proceed();
        
        // AFTER 触发器逻辑
        if (parameter instanceof Employee) {
            Employee employee = (Employee) parameter;
            afterTrigger(employee, sqlCommandType);
        }
        
        return result;
    }
    
    private void beforeTrigger(Employee employee, SqlCommandType commandType) {
        // 额外的BEFORE触发器逻辑（MetaObjectHandler未覆盖的部分）
        if (commandType == SqlCommandType.DELETE) {
            // BEFORE DELETE 触发器
            // 检查关联数据
            if (hasActiveProjects(employee.getId())) {
                throw new IllegalStateException("Cannot delete employee with active projects");
            }
        }
    }
    
    private void afterTrigger(Employee employee, SqlCommandType commandType) {
        // AFTER 触发器逻辑
        AuditLogMapper auditLogMapper = SpringContextHolder.getBean(AuditLogMapper.class);
        
        AuditLog auditLog = new AuditLog();
        auditLog.setTableName("EMPLOYEE");
        auditLog.setOperation(commandType.name());
        auditLog.setRecordId(employee.getId());
        auditLog.setOperationTime(LocalDateTime.now());
        auditLog.setOperationUser(SecurityContext.getCurrentUser());
        
        switch (commandType) {
            case INSERT:
                auditLog.setNewValue(employee.toString());
                // 发送新员工通知
                notifyNewEmployee(employee);
                break;
            case UPDATE:
                auditLog.setNewValue(employee.toString());
                // 检测重要变更
                detectSignificantChanges(employee);
                break;
            case DELETE:
                auditLog.setOldValue(employee.toString());
                // 归档删除的记录
                archiveEmployee(employee);
                break;
        }
        
        auditLogMapper.insert(auditLog);
    }
    
    private boolean hasActiveProjects(Long employeeId) {
        // 检查是否有活动项目
        return false; // 示例实现
    }
    
    private void notifyNewEmployee(Employee employee) {
        // 通知逻辑
        System.out.println("New employee notification: " + employee.getName());
    }
    
    private void detectSignificantChanges(Employee employee) {
        // 检测重要变更
        System.out.println("Checking significant changes for: " + employee.getId());
    }
    
    private void archiveEmployee(Employee employee) {
        // 归档逻辑
        System.out.println("Archiving employee: " + employee.getId());
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 配置属性
    }
}
```

### 2.4 MyBatis 原生转换模板

#### 2.3.1 实体类

```java
package com.example.mybatis.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class Employee implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String name;
    private BigDecimal salary;
    private Long departmentId;
    private LocalDateTime createdTime;
    private String createdBy;
    private LocalDateTime updatedTime;
    private String updatedBy;
    private Long version;
    private Integer deleted;
    
    // Constructors
    public Employee() {}
    
    public Employee(String name, BigDecimal salary, Long departmentId) {
        this.name = name;
        this.salary = salary;
        this.departmentId = departmentId;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public BigDecimal getSalary() { return salary; }
    public void setSalary(BigDecimal salary) { this.salary = salary; }
    
    public Long getDepartmentId() { return departmentId; }
    public void setDepartmentId(Long departmentId) { this.departmentId = departmentId; }
    
    public LocalDateTime getCreatedTime() { return createdTime; }
    public void setCreatedTime(LocalDateTime createdTime) { this.createdTime = createdTime; }
    
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
    
    public LocalDateTime getUpdatedTime() { return updatedTime; }
    public void setUpdatedTime(LocalDateTime updatedTime) { this.updatedTime = updatedTime; }
    
    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    
    public Long getVersion() { return version; }
    public void setVersion(Long version) { this.version = version; }
    
    public Integer getDeleted() { return deleted; }
    public void setDeleted(Integer deleted) { this.deleted = deleted; }
    
    @Override
    public String toString() {
        return "Employee{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", salary=" + salary +
                ", departmentId=" + departmentId +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                '}';
    }
}
```

#### 2.3.2 综合拦截器

```java
package com.example.mybatis.interceptor;

import com.example.mybatis.entity.Employee;
import com.example.mybatis.util.SecurityContext;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.defaults.DefaultSqlSession;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.logging.Logger;

@Intercepts({
    @Signature(type = ParameterHandler.class, method = "setParameters", 
               args = {PreparedStatement.class}),
    @Signature(type = Executor.class, method = "update", 
               args = {MappedStatement.class, Object.class})
})
public class ComprehensiveTriggerInterceptor implements Interceptor {
    
    private static final Logger logger = Logger.getLogger(ComprehensiveTriggerInterceptor.class.getName());
    private SqlSessionFactory sqlSessionFactory;
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (invocation.getTarget() instanceof ParameterHandler) {
            // BEFORE 触发器逻辑
            return handleBeforeTrigger(invocation);
        } else if (invocation.getTarget() instanceof Executor) {
            // 先保存原始值（用于AFTER触发器比较）
            MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
            Object parameter = invocation.getArgs()[1];
            Employee originalEmployee = null;
            
            if (parameter instanceof Employee && ms.getSqlCommandType() == SqlCommandType.UPDATE) {
                Employee emp = (Employee) parameter;
                originalEmployee = getOriginalEmployee(emp.getId());
            }
            
            // 执行SQL
            Object result = invocation.proceed();
            
            // AFTER 触发器逻辑
            handleAfterTrigger(invocation, originalEmployee);
            
            return result;
        }
        
        return invocation.proceed();
    }
    
    private Object handleBeforeTrigger(Invocation invocation) throws Throwable {
        ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
        Object parameterObject = parameterHandler.getParameterObject();
        
        if (parameterObject instanceof Employee) {
            Employee employee = (Employee) parameterObject;
            MetaObject metaObject = SystemMetaObject.forObject(employee);
            
            // 获取SQL类型
            MetaObject metaParameterHandler = SystemMetaObject.forObject(parameterHandler);
            MappedStatement mappedStatement = (MappedStatement) metaParameterHandler.getValue("mappedStatement");
            SqlCommandType commandType = mappedStatement.getSqlCommandType();
            
            String currentUser = SecurityContext.getCurrentUser();
            LocalDateTime now = LocalDateTime.now();
            
            switch (commandType) {
                case INSERT:
                    // BEFORE INSERT 触发器
                    metaObject.setValue("createdTime", now);
                    metaObject.setValue("createdBy", currentUser);
                    metaObject.setValue("updatedTime", now);
                    metaObject.setValue("updatedBy", currentUser);
                    
                    if (metaObject.getValue("deleted") == null) {
                        metaObject.setValue("deleted", 0);
                    }
                    
                    // 业务验证
                    validateEmployee(employee, true);
                    
                    // 自动计算字段
                    if (employee.getDepartmentId() == null) {
                        metaObject.setValue("departmentId", 1L); // 默认部门
                    }
                    break;
                    
                case UPDATE:
                    // BEFORE UPDATE 触发器
                    metaObject.setValue("updatedTime", now);
                    metaObject.setValue("updatedBy", currentUser);
                    
                    // 业务验证
                    validateEmployee(employee, false);
                    
                    // 防止修改创建信息
                    Employee original = getOriginalEmployee(employee.getId());
                    if (original != null) {
                        metaObject.setValue("createdTime", original.getCreatedTime());
                        metaObject.setValue("createdBy", original.getCreatedBy());
                    }
                    break;
                    
                case DELETE:
                    // BEFORE DELETE 触发器
                    if (hasRelatedData(employee.getId())) {
                        throw new IllegalStateException("Cannot delete employee with related data");
                    }
                    break;
            }
        }
        
        return invocation.proceed();
    }
    
    private void handleAfterTrigger(Invocation invocation, Employee originalEmployee) {
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        SqlCommandType commandType = ms.getSqlCommandType();
        
        if (!(parameter instanceof Employee)) {
            return;
        }
        
        Employee employee = (Employee) parameter;
        
        // 创建审计日志
        Map<String, Object> auditLog = new HashMap<>();
        auditLog.put("tableName", "EMPLOYEE");
        auditLog.put("operation", commandType.name());
        auditLog.put("recordId", employee.getId());
        auditLog.put("operationTime", LocalDateTime.now());
        auditLog.put("operationUser", SecurityContext.getCurrentUser());
        
        switch (commandType) {
            case INSERT:
                // AFTER INSERT 触发器
                auditLog.put("newValue", employee.toString());
                saveAuditLog(auditLog);
                
                // 发送通知
                sendNotification("NEW_EMPLOYEE", employee);
                
                // 初始化相关数据
                initializeRelatedData(employee);
                break;
                
            case UPDATE:
                // AFTER UPDATE 触发器
                auditLog.put("oldValue", originalEmployee != null ? originalEmployee.toString() : null);
                auditLog.put("newValue", employee.toString());
                saveAuditLog(auditLog);
                
                // 检测重要变更
                if (originalEmployee != null) {
                    detectSignificantChanges(originalEmployee, employee);
                }
                
                // 同步更新相关表
                syncRelatedTables(employee);
                break;
                
            case DELETE:
                // AFTER DELETE 触发器
                auditLog.put("oldValue", employee.toString());
                saveAuditLog(auditLog);
                
                // 归档数据
                archiveEmployee(employee);
                
                // 清理相关数据
                cleanupRelatedData(employee.getId());
                break;
        }
    }
    
    private void validateEmployee(Employee employee, boolean isInsert) {
        // 验证薪资
        if (employee.getSalary() != null) {
            if (employee.getSalary().compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("Salary cannot be negative");
            }
            if (employee.getSalary().compareTo(new BigDecimal("1000000")) > 0) {
                throw new IllegalArgumentException("Salary exceeds maximum limit");
            }
        } else if (isInsert) {
            throw new IllegalArgumentException("Salary is required for new employees");
        }
        
        // 验证姓名
        if (employee.getName() == null || employee.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Employee name is required");
        }
        
        // 验证部门
        if (isInsert && employee.getDepartmentId() == null) {
            throw new IllegalArgumentException("Department is required for new employees");
        }
    }
    
    private Employee getOriginalEmployee(Long id) {
        if (id == null || sqlSessionFactory == null) {
            return null;
        }
        
        try (SqlSession session = sqlSessionFactory.openSession()) {
            return session.selectOne("com.example.mybatis.mapper.EmployeeMapper.selectById", id);
        } catch (Exception e) {
            logger.warning("Failed to get original employee: " + e.getMessage());
            return null;
        }
    }
    
    private boolean hasRelatedData(Long employeeId) {
        if (sqlSessionFactory == null) {
            return false;
        }
        
        try (SqlSession session = sqlSessionFactory.openSession()) {
            Long count = session.selectOne("com.example.mybatis.mapper.ProjectMapper.countByManagerId", employeeId);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.warning("Failed to check related data: " + e.getMessage());
            return false;
        }
    }
    
    private void saveAuditLog(Map<String, Object> auditLog) {
        if (sqlSessionFactory == null) {
            return;
        }
        
        try (SqlSession session = sqlSessionFactory.openSession()) {
            session.insert("com.example.mybatis.mapper.AuditLogMapper.insert", auditLog);
            session.commit();
        } catch (Exception e) {
            logger.severe("Failed to save audit log: " + e.getMessage());
        }
    }
    
    private void detectSignificantChanges(Employee original, Employee current) {
        // 检测薪资重大变更
        if (original.getSalary() != null && current.getSalary() != null) {
            BigDecimal change = current.getSalary().subtract(original.getSalary());
            if (change.abs().compareTo(new BigDecimal("10000")) > 0) {
                sendNotification("SIGNIFICANT_SALARY_CHANGE", current);
            }
        }
        
        // 检测部门变更
        if (!original.getDepartmentId().equals(current.getDepartmentId())) {
            sendNotification("DEPARTMENT_CHANGE", current);
        }
    }
    
    private void sendNotification(String type, Employee employee) {
        logger.info("Notification [" + type + "] for employee: " + employee.getId());
        // 实际的通知逻辑
    }
    
    private void initializeRelatedData(Employee employee) {
        // 初始化相关数据
        logger.info("Initializing related data for employee: " + employee.getId());
    }
    
    private void syncRelatedTables(Employee employee) {
        // 同步相关表
        logger.info("Syncing related tables for employee: " + employee.getId());
    }
    
    private void archiveEmployee(Employee employee) {
        // 归档逻辑
        logger.info("Archiving employee: " + employee.getId());
    }
    
    private void cleanupRelatedData(Long employeeId) {
        // 清理相关数据
        logger.info("Cleaning up related data for employee: " + employeeId);
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以通过properties注入SqlSessionFactory
        String factoryClass = properties.getProperty("sqlSessionFactory");
        if (factoryClass != null) {
            try {
                this.sqlSessionFactory = (SqlSessionFactory) Class.forName(factoryClass)
                    .getDeclaredMethod("getInstance").invoke(null);
            } catch (Exception e) {
                logger.warning("Failed to inject SqlSessionFactory: " + e.getMessage());
            }
        }
    }
}
```

## 三、框架组件映射说明

### 3.1 Java原生JDBC触发器实现组件

| 组件                    | 作用              | 对应触发器类型 |
|-----------------------|-----------------|---------|
| **TriggerManager**    | 触发器管理器，注册和执行触发器 | 所有触发器类型 |
| **Trigger接口**         | 定义触发器执行接口       | 触发器主体   |
| **TriggerType枚举**     | 定义触发器类型         | 标识触发时机  |
| **Connection事务**      | 保证事务一致性         | 事务控制    |
| **PreparedStatement** | SQL执行载体         | DML操作   |
| **DAO方法**             | 在数据访问方法中调用触发器   | 触发器调用点  |

### 3.2 Hibernate/JPA 触发器实现组件

| 组件                   | 作用          | 对应触发器类型       |
|----------------------|-------------|---------------|
| **@PrePersist**      | 实体持久化前执行    | BEFORE INSERT |
| **@PostPersist**     | 实体持久化后执行    | AFTER INSERT  |
| **@PreUpdate**       | 实体更新前执行     | BEFORE UPDATE |
| **@PostUpdate**      | 实体更新后执行     | AFTER UPDATE  |
| **@PreRemove**       | 实体删除前执行     | BEFORE DELETE |
| **@PostRemove**      | 实体删除后执行     | AFTER DELETE  |
| **@EntityListeners** | 指定实体监听器类    | 所有触发器         |
| **EntityListener**   | 监听器类，包含触发逻辑 | 触发器主体         |

### 3.3 MyBatis-Plus 触发器实现组件

| 组件                        | 作用         | 对应触发器类型              |
|---------------------------|------------|----------------------|
| **MetaObjectHandler**     | 元数据对象处理器   | BEFORE INSERT/UPDATE |
| **@TableField(fill=...)** | 字段自动填充注解   | BEFORE INSERT/UPDATE |
| **Interceptor**           | MyBatis拦截器 | 所有触发器类型              |
| **@Intercepts**           | 拦截器注解      | 定义拦截点                |
| **@Signature**            | 方法签名       | 指定拦截的方法              |

### 3.4 MyBatis 原生触发器实现组件

| 组件                   | 作用     | 对应触发器类型         |
|----------------------|--------|-----------------|
| **Interceptor**      | 拦截器接口  | 所有触发器类型         |
| **ParameterHandler** | 参数处理拦截 | BEFORE触发器       |
| **Executor**         | 执行器拦截  | BEFORE/AFTER触发器 |
| **@Intercepts**      | 拦截器注解  | 定义拦截点           |
| **MetaObject**       | 元对象操作  | 字段自动填充          |

## 四、Oracle触发器到ORM框架的支持对比

| Oracle触发器类型            | 触发时机     | Java原生JDBC                         | Hibernate/JPA      | MyBatis-Plus                     | MyBatis                          | 说明         |
|------------------------|----------|------------------------------------|--------------------|----------------------------------|----------------------------------|------------|
| **BEFORE INSERT**      | 插入前      | ✅ TriggerManager.executeTriggers() | ✅ @PrePersist      | ✅ MetaObjectHandler.insertFill() | ✅ Interceptor (ParameterHandler) | 完全支持       |
| **AFTER INSERT**       | 插入后      | ✅ TriggerManager.executeTriggers() | ✅ @PostPersist     | ✅ Interceptor (Executor.update)  | ✅ Interceptor (Executor.update)  | 完全支持       |
| **BEFORE UPDATE**      | 更新前      | ✅ TriggerManager.executeTriggers() | ✅ @PreUpdate       | ✅ MetaObjectHandler.updateFill() | ✅ Interceptor (ParameterHandler) | 完全支持       |
| **AFTER UPDATE**       | 更新后      | ✅ TriggerManager.executeTriggers() | ✅ @PostUpdate      | ✅ Interceptor (Executor.update)  | ✅ Interceptor (Executor.update)  | 完全支持       |
| **BEFORE DELETE**      | 删除前      | ✅ TriggerManager.executeTriggers() | ✅ @PreRemove       | ✅ Interceptor (Executor.update)  | ✅ Interceptor (ParameterHandler) | 完全支持       |
| **AFTER DELETE**       | 删除后      | ✅ TriggerManager.executeTriggers() | ✅ @PostRemove      | ✅ Interceptor (Executor.update)  | ✅ Interceptor (Executor.update)  | 完全支持       |
| **FOR EACH ROW**       | 行级触发     | ✅ 默认行为                             | ✅ 默认行为             | ✅ 默认行为                           | ✅ 默认行为                           | 所有框架默认都是行级 |
| **FOR EACH STATEMENT** | 语句级触发    | ✅ 批量操作前后执行                         | ⚠️ 需自定义实现          | ⚠️ 批量操作时特殊处理                     | ⚠️ 批量操作时特殊处理                     | JDBC可完全控制  |
| **INSTEAD OF**         | 视图触发器    | ✅ 可在DAO层实现                         | ❌ 不支持              | ❌ 不支持                            | ❌ 不支持                            | JDBC可模拟实现  |
| **COMPOUND TRIGGER**   | 复合触发器    | ✅ TriggerManager支持多触发器             | ⚠️ 组合多个监听器         | ⚠️ 组合Handler+Interceptor         | ⚠️ 在Interceptor中实现多阶段            | JDBC可完全实现  |
| **条件触发 (WHEN)**        | 条件判断     | ✅ 在Trigger.execute()中判断            | ✅ 在监听器方法中判断        | ✅ 在Handler/Interceptor中判断        | ✅ 在Interceptor中判断                | 通过代码逻辑实现   |
| **访问:OLD/:NEW**        | 新旧值访问    | ✅ 方法参数传递新旧值                        | ⚠️ @PostUpdate中需查询 | ⚠️ 需要查询原值                        | ⚠️ 需要查询原值                        | JDBC可直接传递  |
| **自治事务**               | 独立事务     | ✅ 新建Connection                     | ✅ 新建EntityManager  | ✅ 新建SqlSession                   | ✅ 新建SqlSession                   | 可实现独立事务    |
| **异常处理**               | 阻止操作     | ✅ 抛出SQLException                   | ✅ 抛出异常             | ✅ 抛出异常                           | ✅ 抛出异常                           | 异常会回滚事务    |
| **级联触发**               | 触发器触发触发器 | ✅ 触发器可调用其他触发器                      | ✅ 监听器调用其他操作        | ✅ 可以触发其他操作                       | ✅ 可以触发其他操作                       | 通过代码调用     |
| **触发器优先级**             | 执行顺序控制   | ✅ 支持优先级排序                          | ⚠️ 依赖注册顺序          | ⚠️ 依赖配置顺序                        | ⚠️ 依赖配置顺序                        | JDBC可精确控制  |
| **动态启用/禁用**            | 运行时控制    | ✅ 可动态注册/注销                         | ❌ 编译时确定            | ⚠️ 需要条件判断                        | ⚠️ 需要条件判断                        | JDBC灵活控制   |
| **批量操作优化**             | 批处理支持    | ✅ 原生支持批处理                          | ⚠️ 需要特殊配置          | ✅ 支持批量操作                         | ✅ 支持批量操作                         | JDBC性能最优   |
| **DDL触发器**             | DDL操作触发  | ❌ 不支持                              | ❌ 不支持              | ❌ 不支持                            | ❌ 不支持                            | 不适用于ORM    |
| **系统事件触发器**            | 登录/启动等   | ❌ 不支持                              | ❌ 不支持              | ❌ 不支持                            | ❌ 不支持                            | 使用应用级事件    |

### 图例说明：

- ✅ 完全支持：框架原生提供相应功能
- ⚠️ 部分支持：需要额外配置或编码
- ❌ 不支持：框架无法实现或不适用

## 五、最佳实践建议

### 5.1 框架选择建议

| 框架                | 适用场景                                               | 优势                                                 | 劣势                                   |
|-------------------|----------------------------------------------------|----------------------------------------------------|--------------------------------------|
| **Java原生JDBC**    | • 性能要求极高<br>• 需要精确控制<br>• 轻量级项目<br>• 复杂触发器逻辑       | • 最大控制力和灵活性<br>• 性能最优<br>• 支持所有触发器特性<br>• 可动态管理触发器 | • 代码量大<br>• 需要手动管理事务<br>• 维护成本高      |
| **Hibernate/JPA** | • 领域驱动设计(DDD)<br>• 企业级应用<br>• 需要ORM特性<br>• 团队熟悉JPA | • 编程模型优雅<br>• 注解式配置<br>• 自动事务管理<br>• 最接近数据库触发器     | • 学习曲线陡峭<br>• 性能开销<br>• Entity与触发器耦合 |
| **MyBatis-Plus**  | • 需要SQL灵活性<br>• 快速开发<br>• 中大型项目<br>• 国内项目          | • 自动填充机制完善<br>• 拦截器功能强大<br>• 开发效率高<br>• 社区活跃       | • 依赖特定版本<br>• 过度封装<br>• 需要学习特有API    |
| **MyBatis原生**     | • 复杂SQL场景<br>• 需要SQL优化<br>• 遗留系统改造<br>• DBA参与开发    | • SQL完全可控<br>• 拦截器机制灵活<br>• 学习成本低<br>• 轻量级         | • 配置繁琐<br>• 代码重复<br>• 缺少自动功能         |

### 5.2 LLM自动转换策略

#### 5.2.1 架构模式分类

**解耦型架构**（JDBC/MyBatis/MyBatis-Plus）：

- Entity和触发器逻辑完全分离
- 可以独立转换，分别生成
- 通过配置或注册机制关联

**耦合型架构**（Hibernate/JPA）：

- Entity类包含触发器相关注解
- 触发器逻辑通过监听器与Entity绑定
- 必须在同一上下文中转换

#### 5.2.2 转换流程设计

```
解耦型框架转换流程：
┌─────────────┐     ┌──────────────┐     ┌──────────────┐
│ Oracle表结构 │────>│ LLM请求1:    │────>│ Java Entity  │
└─────────────┘     │ 转换表结构    │     └──────────────┘
                    └──────────────┘
                    
┌─────────────┐     ┌──────────────┐     ┌──────────────┐
│Oracle触发器 │────>│ LLM请求2:    │────>│ 拦截器/处理器 │
└─────────────┘     │ 转换触发器    │     └──────────────┘
                    └──────────────┘
                    
                    ┌──────────────┐     ┌──────────────┐
                    │ LLM请求3:    │────>│ 配置/注册代码 │
                    │ 生成配置      │     └──────────────┘
                    └──────────────┘

耦合型框架转换流程：
┌─────────────┐
│ Oracle表结构 │     ┌──────────────┐     ┌──────────────┐
├─────────────┤────>│ LLM单次请求: │────>│Entity+Listener│
│Oracle触发器 │     │ 联合转换     │     └──────────────┘
└─────────────┘     └──────────────┘
```

#### 5.2.3 LLM提示词策略

**解耦型框架提示词示例**：

```
# 第一步：转换Entity（独立请求）
"将Oracle表结构转换为[框架名]的Entity类，只生成纯数据模型"

# 第二步：转换触发器（独立请求）
"将Oracle触发器转换为[框架名]的拦截器/处理器，实现对应的触发逻辑"

# 第三步：生成配置（可选）
"生成连接Entity和触发器的配置代码"
```

**耦合型框架提示词示例**：

```
# 单次请求（包含完整上下文）
"将以下Oracle表和所有相关触发器转换为Hibernate/JPA：
1. 生成包含@EntityListeners注解的Entity类
2. 生成对应的EntityListener类
3. 确保触发器逻辑正确映射到JPA生命周期回调"
```

### 5.3 事务管理策略

| 策略         | 适用场景            | 实现方式                                     | 注意事项        |
|------------|-----------------|------------------------------------------|-------------|
| **主事务内执行** | 核心业务逻辑、数据一致性要求高 | 使用同一个Connection/EntityManager/SqlSession | 失败会导致整体回滚   |
| **独立事务**   | 审计日志、非关键操作      | 新建数据库连接执行                                | 需要处理部分失败的情况 |
| **异步处理**   | 通知、统计、缓存更新      | 使用消息队列或线程池                               | 需要考虑最终一致性   |
| **补偿事务**   | 分布式场景、跨系统操作     | Saga模式或TCC模式                             | 实现复杂，需要状态管理 |

### 5.4 性能优化建议

#### 5.4.1 通用优化策略

1. **避免N+1查询问题**
    - 在获取原始值时使用批量查询
    - 使用JOIN或子查询减少数据库访问次数
    - 考虑使用缓存存储频繁访问的数据

2. **触发器执行优化**
    - 将轻量级验证放在BEFORE触发器
    - 将重量级操作放在AFTER触发器
    - 使用条件判断避免不必要的触发器执行

3. **批量操作优化**
    - JDBC: 使用PreparedStatement.addBatch()
    - Hibernate: 配置hibernate.jdbc.batch_size
    - MyBatis: 使用foreach标签或批量执行器

4. **异步化策略**
    - 审计日志异步批量写入
    - 通知类操作使用消息队列
    - 统计更新使用定时任务

#### 5.4.2 框架特定优化

| 框架               | 优化技巧                                                                           |
|------------------|--------------------------------------------------------------------------------|
| **JDBC**         | • 使用连接池<br>• 预编译SQL语句<br>• 批量操作使用addBatch<br>• 合理设置fetchSize                   |
| **Hibernate**    | • 启用二级缓存<br>• 使用@BatchSize注解<br>• 配置Statement批处理<br>• 使用StatelessSession进行批量操作 |
| **MyBatis-Plus** | • 使用批量方法(saveBatch)<br>• 启用SQL打印分析性能<br>• 合理使用逻辑删除<br>• 避免过度使用Wrapper          |
| **MyBatis**      | • 使用批量执行器(BatchExecutor)<br>• 合理配置缓存<br>• 减少嵌套查询<br>• 使用存储过程处理复杂逻辑             |

### 5.5 代码组织建议

#### 5.5.1 包结构设计

```
项目结构（解耦型）：
├── entity/
│   └── Employee.java              # 纯数据模型
├── trigger/
│   ├── TriggerManager.java        # 触发器管理器
│   ├── handler/
│   │   ├── BeforeInsertHandler.java
│   │   └── AfterUpdateHandler.java
│   └── interceptor/
│       └── AuditInterceptor.java
└── dao/
    └── EmployeeDAO.java

项目结构（耦合型）：
├── entity/
│   ├── Employee.java              # 包含监听器注解
│   └── listener/
│       └── EmployeeEntityListener.java
└── repository/
    └── EmployeeRepository.java
```

#### 5.5.2 关注点分离

1. **业务验证** → 独立的Validator类
2. **审计日志** → 独立的AuditService
3. **通知发送** → 独立的NotificationService
4. **数据同步** → 独立的SyncService

#### 5.5.3 配置管理

- 将触发器规则外部化到配置文件
- 使用策略模式处理不同的触发条件
- 提供触发器的启用/禁用开关
- 支持触发器的优先级配置

### 5.6 测试策略

| 测试类型     | 测试重点      | 推荐工具                         |
|----------|-----------|------------------------------|
| **单元测试** | 触发器逻辑的正确性 | JUnit + Mockito              |
| **集成测试** | 触发器与数据库交互 | @DataJpaTest/@MybatisTest    |
| **事务测试** | 事务回滚和提交   | @Transactional + @Rollback   |
| **性能测试** | 触发器对性能的影响 | JMH/JMeter                   |
| **并发测试** | 乐观锁和并发控制  | CountDownLatch/CyclicBarrier |

### 5.7 迁移建议

#### 5.7.1 渐进式迁移策略

1. **第一阶段**：保留数据库触发器，同时实现应用层触发器
2. **第二阶段**：监控对比两者的执行结果
3. **第三阶段**：逐步禁用数据库触发器
4. **第四阶段**：完全移除数据库触发器

#### 5.7.2 回滚方案

- 保留原始数据库触发器的DDL脚本
- 实现触发器的版本管理
- 提供快速切换机制
- 建立监控和告警机制

### 5.8 监控和调试

1. **日志记录**
    - 记录触发器执行时间
    - 记录触发器影响的数据
    - 记录异常和错误信息

2. **性能监控**
    - 监控触发器执行耗时
    - 统计触发器执行频率
    - 分析性能瓶颈

3. **调试技巧**
    - 使用条件断点调试触发器
    - 启用SQL日志查看实际执行的语句
    - 使用测试数据隔离问题

## 六、总结

将Oracle触发器迁移到ORM框架是一个需要仔细规划的过程。虽然ORM框架不能完全复制数据库触发器的所有特性，但通过合理使用框架提供的机制，可以实现绝大部分功能。选择合适的框架和实现方式，需要根据项目的具体需求、团队技术栈和性能要求来决定。

在实际迁移过程中，建议：

1. 先进行充分的测试，确保触发器逻辑正确迁移
2. 注意事务边界和一致性要求
3. 监控性能影响，必要时进行优化
4. 保留关键业务的数据库触发器作为双重保障