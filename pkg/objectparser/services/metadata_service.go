package services

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/adaptor"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/core"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/config"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/samber/lo"
)

// Error message templates for consistent error handling
const (
	errorMessageInvalidRequest              = "invalid %s request: %w"
	errorMessageOperationFailed             = "%s failed for taskId=%d, channelId=%d: %w"
	errorMessageBasicOperationFailed        = "%s failed: %w"
	errorMessageAdaptorNotConfigured        = "adaptor is not configured"
	errorMessageDefinitionHandlerMissing    = "definition handler is not configured"
	errorMessageCompatibilityHandlerMissing = "compatibility handler is not configured"
	errorMessageServiceDisabled             = "ObjectParser service is disabled"
)

// Service status messages
const (
	statusMessageHealthy             = "Service is healthy"
	statusMessageAdaptorUnavailable  = "Adaptor not available"
	statusMessageHandlersUnavailable = "Required handlers not available"
	statusMessageServiceDisabled     = "Service is disabled"
)

// metadataService consolidates Definition and Compatibility analysis functionality
// It implements the MetadataService interface with direct business logic implementation
type metadataService struct {
	adaptor adaptor.Adaptor
	config  *config.ObjectParserConfig
	version string
}

// NewMetadataService creates a new metadata service with direct implementation.
// It consolidates Definition and Compatibility functionality into a unified service interface.
// The service handles configuration loading safely for test environments and provides
// comprehensive validation and error handling.
//
// Parameters:
//   - adaptor: ObjectParser adaptor for external service communication
//
// Returns a MetadataService implementation with direct business logic implementation.
func NewMetadataService(objectParserAdaptor adaptor.Adaptor) MetadataService {
	var objParserConfig *config.ObjectParserConfig

	// Safely get config - handle case where global config might be nil (for tests)
	if globalConfig := config.GetGlobalConfig(); globalConfig != nil {
		objParserConfig = globalConfig.ObjectParserConfig
	}

	service := &metadataService{
		adaptor: objectParserAdaptor,
		config:  objParserConfig,
		version: ServiceVersion,
	}

	// Note: Logging removed to avoid panic in test environment
	return service
}

// Definition Methods (from DefinitionHandler)

// GetObjectDetail retrieves detailed information about a specific database object.
// This method handles the complete workflow of object detail retrieval including:
// 1. Validating channel and setting up database connections
// 2. Retrieving Oracle object definitions from database
// 3. Analyzing PL/SQL code for syntax and structure
// 4. Building detailed analysis response
func (s *metadataService) GetObjectDetail(ctx context.Context, req *message.GetObjectDetailReq) (*message.GetObjectDetailResp, error) {
	if err := ValidateObjectDetailRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "object detail", err)
	}

	// Step 1: Get channel information
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		return nil, fmt.Errorf("failed to get channel for channelId=%d: %w", req.ChannelId, getChannelErr)
	}

	// Step 2: Set up Oracle database connections
	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		return nil, fmt.Errorf("failed to setup Oracle database connections for taskId=%d: %w", req.TaskId, setUpErr)
	}

	// Step 3: Process each object to get definitions
	rets := make([]*message.DefinitionAndDetail, 0, len(req.Objects))
	for _, obj := range req.Objects {
		switch obj.GetObjectType() {
		case "PACKAGE BODY", "PROCEDURE", "FUNCTION", "TRIGGER", "TYPE":
			// Get Oracle object definition from database
			packageDefinition, getErr := models.GetDatasourceReaderWriter().GetOracleDefinition(ctx, dbConns.GetSourceDB().OracleDB, obj.GetObjectSchema(), obj.GetObjectType(), obj.GetObjectName(), obj.GetObjectPackageName())
			if getErr != nil {
				return nil, fmt.Errorf("failed to get Oracle definition for %s.%s: %w", obj.GetObjectSchema(), obj.GetObjectName(), getErr)
			}

			// Build definition response
			rets = append(rets, &message.DefinitionAndDetail{
				Definition: &message.GetOracleObjectDefinition{
					ObjectSchema:            obj.GetObjectSchema(),
					ObjectType:              obj.GetObjectType(),
					ObjectName:              obj.GetObjectName(),
					ObjectPackageName:       obj.GetObjectPackageName(),
					ObjectText:              packageDefinition.Text,
					ObjectTextHighlightLine: packageDefinition.HighlightLine,
				},
			})
		default:
			return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED, "unsupported object type: %s", obj.GetObjectType())
		}
	}

	// Step 4: Analyze each object definition for details
	mb := core.NewMessageBuilder()

	for idx, ret := range rets {
		var plsql string
		var analyzeErrMessage string

		plsql = ret.Definition.ObjectText

		// Add corruption for testing if requested
		if req.ShouldCorrupt {
			plsql = "/* Test corruption applied */ " + plsql
		}

		// Encode SQL if requested
		if req.ShouldEncode {
			plsql = stringutil.EncodeSQL(plsql)
		}

		// Analyze SQL using adaptor
		analyzeResult, analyzeErr := s.adaptor.AnalyzeSQL(ctx, dto.AnalyzeSQLRequest{
			IsEncoded: req.ShouldEncode,
			PLSQL:     plsql,
		})
		if analyzeErr != nil {
			analyzeErrMessage = analyzeErr.Error()
		}

		// Build detailed analysis response
		rets[idx].Detail = &message.OracleObjectDefinitionAnalyzeDetail{
			SchemaName:         ret.Definition.GetObjectSchema(),
			ObjectType:         ret.Definition.GetObjectType(),
			ObjectName:         ret.Definition.GetObjectName(),
			ErrorDetail:        analyzeErrMessage,
			ReservedWordCount:  uint(len(analyzeResult.GetReservedWordList())),
			DatabaseLinkCount:  uint(len(analyzeResult.GetDatabaseLinkTableReferenceList())),
			MethodInvokeList:   mb.BuildMethodInvokeListFromVO(analyzeResult.GetMethodInvokeList()),
			IdentifierList:     mb.BuildMethodInvokeListFromVO(analyzeResult.GetIdentifierList()),
			TableReferenceList: mb.BuildTableReferenceListFromVO(analyzeResult.GetTableReferenceList()),
			ReservedWordList:   mb.BuildReservedWordListFromVO(analyzeResult.GetReservedWordList()),
			PLSQLSegment:       mb.BuildPLSQLSegmentFromVO(analyzeResult.GetPlsqlSegment()),
		}
	}

	return &message.GetObjectDetailResp{Data: rets}, nil
}

// GetDefinitionsFromMetadata extracts object definitions from database metadata.
// This method handles the complete workflow of metadata-based definition retrieval including:
// 1. Processing dependency UUIDs and object keys
// 2. Fetching object definitions and analysis details from metadata
// 3. Building comprehensive definition and detail responses
func (s *metadataService) GetDefinitionsFromMetadata(ctx context.Context, req *message.GetDefinitionsFromMetadataReq) (*message.GetDefinitionsFromMetadataResp, error) {
	if err := ValidateDefinitionsFromMetadataRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "definitions", err)
	}

	// Step 1: Validate channel
	_, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		return nil, fmt.Errorf("failed to retrieve channel for channelId=%d: %w", req.ChannelId, getChannelErr)
	}

	var schemaObjectKeys []*dto.SchemaObjectKey
	objectKeyHelper := core.NewObjectKeyHelper()

	// Step 2: Process dependency UUIDs if provided
	if len(req.DependencyUUIDs) != 0 {
		dependencies, getDepErr := models.GetObjectParserWriter().ListOracleDependencyByUUIDs(ctx, req.TaskId, req.DependencyUUIDs)
		if getDepErr != nil {
			return nil, fmt.Errorf("failed to list Oracle dependencies by UUIDs for taskId=%d: %w", req.TaskId, getDepErr)
		}

		for _, dependency := range dependencies {
			objKey := &dto.SchemaObjectKey{
				SchemaName:          dependency.GetSchemaName(),
				ObjectType:          dependency.GetType(),
				ObjectName:          dependency.GetName(),
				DependencyObjectKey: dependency.GetUUID(),
			}
			if dependency.GetPackageName() != "" {
				objKey.HighlightLine = dependency.GetType() + " " + dependency.GetName()
				objKey.PackageObjectKey = &dto.SchemaObjectKey{
					SchemaName: dependency.GetSchemaName(),
					ObjectType: constants.OracleObjectTypePackageBody,
					ObjectName: dependency.GetPackageName(),
				}
			}
			schemaObjectKeys = append(schemaObjectKeys, objKey)
		}
	}

	// Step 3: Process direct objects if provided
	if len(req.Objects) != 0 {
		for _, obj := range req.Objects {
			objKey := &dto.SchemaObjectKey{
				SchemaName: obj.GetObjectSchema(),
				ObjectType: obj.GetObjectType(),
				ObjectName: obj.GetObjectName(),
			}
			if obj.GetObjectPackageName() != "" {
				objKey.HighlightLine = obj.GetObjectType() + " " + obj.GetObjectName()
				objKey.PackageObjectKey = &dto.SchemaObjectKey{
					SchemaName: obj.GetObjectSchema(),
					ObjectType: constants.OracleObjectTypePackageBody,
					ObjectName: obj.GetObjectPackageName(),
				}
			}
			schemaObjectKeys = append(schemaObjectKeys, objKey)
		}
	}

	// Step 4: Validate that we have objects to process
	if len(schemaObjectKeys) == 0 {
		return nil, tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED, "no valid dependencyKeys or objects found")
	}

	// Step 5: Build fetch keys for metadata retrieval
	fetchKeys := make([]string, 0)
	for _, schemaObjectKey := range schemaObjectKeys {
		if schemaObjectKey.IsPackageObjectKeyExist() {
			fetchKeys = append(fetchKeys, schemaObjectKey.GetPackageObjectKey())
		} else {
			fetchKeys = append(fetchKeys, schemaObjectKey.GetObjectKey())
		}
	}
	fetchKeys = lo.Uniq(fetchKeys)

	// Step 6: Retrieve definitions and details from metadata
	definitionPos, getDefinitionErr := models.GetObjectParserWriter().GetOracleObjectDefinitionBySchemaObjectKeys(ctx, req.TaskId, fetchKeys)
	if getDefinitionErr != nil {
		return nil, fmt.Errorf("failed to get Oracle object definitions for taskId=%d: %w", req.TaskId, getDefinitionErr)
	}

	detailPos, getDetailErr := models.GetObjectParserWriter().GetOracleObjectDefinitionAnalyzeDetailBySchemaObjectKeys(ctx, req.TaskId, fetchKeys)
	if getDetailErr != nil {
		return nil, fmt.Errorf("failed to get Oracle object definition details for taskId=%d: %w", req.TaskId, getDetailErr)
	}

	// Step 7: Build lookup maps for efficient processing
	definitionPoMap := make(map[string]*objectparser.OracleObjectDefinition)
	for _, definition := range definitionPos {
		definitionPoMap[objectKeyHelper.GenerateSchemaObjectKey(definition.SchemaName, definition.ObjectType, definition.ObjectName)] = definition
	}

	detailPoMap := make(map[string]*objectparser.OracleObjectDefinitionAnalyzeDetail)
	for _, detail := range detailPos {
		detailPoMap[objectKeyHelper.GenerateSchemaObjectKey(detail.SchemaName, detail.ObjectType, detail.ObjectName)] = detail
	}

	// Step 8: Build response for each requested object
	messageBuilder := core.NewMessageBuilder()
	rets := make([]message.DefinitionAndDetail, 0, len(schemaObjectKeys))

	for _, schemaObjectKey := range schemaObjectKeys {
		fetchKey := schemaObjectKey.GetObjectKey()
		if schemaObjectKey.IsPackageObjectKeyExist() {
			fetchKey = schemaObjectKey.GetPackageObjectKey()
		}

		definitionPo := definitionPoMap[fetchKey]
		detailPo := detailPoMap[fetchKey]

		var allText string
		if definitionPo != nil {
			allText = definitionPo.AllText
		}

		retDefinition := &message.GetOracleObjectDefinition{
			ObjectSchema:            schemaObjectKey.SchemaName,
			ObjectType:              schemaObjectKey.ObjectType,
			ObjectName:              schemaObjectKey.ObjectName,
			ObjectPackageName:       schemaObjectKey.GetPackageName(),
			ObjectText:              allText,
			ObjectTextHighlightLine: schemaObjectKey.HighlightLine,
			DependencyObjectKey:     schemaObjectKey.DependencyObjectKey,
		}

		retDetail := messageBuilder.BuildDetailMessage(detailPo)

		ret := message.DefinitionAndDetail{
			Definition: retDefinition,
			Detail:     retDetail,
		}

		rets = append(rets, ret)
	}

	return &message.GetDefinitionsFromMetadataResp{Data: rets}, nil
}

// ListAnalyzeDetail lists detailed analysis information with pagination support.
func (s *metadataService) ListAnalyzeDetail(ctx context.Context, req *message.ListAnalyzeDetailReq) (*message.ListAnalyzeDetailResp, *message.Page, error) {
	if err := ValidateListAnalyzeDetailRequest(req); err != nil {
		return nil, nil, fmt.Errorf(errorMessageInvalidRequest, "analyze detail", err)
	}

	// Get analyze details using data access layer with filters
	details, total, getDetailErr := models.GetObjectParserWriter().ListOracleObjectDefinitionAnalyzeDetailByTaskId(
		ctx, req.TaskId, req.Page, req.PageSize,
		req.SchemaName, req.ObjectType, req.ObjectName)
	if getDetailErr != nil {
		return nil, nil, fmt.Errorf("failed to get analyze details for taskId=%d: %w", req.TaskId, getDetailErr)
	}

	// Build response using message builder
	messageBuilder := core.NewMessageBuilder()
	data := make([]*message.OracleObjectDefinitionAnalyzeDetail, 0, len(details))
	for _, detail := range details {
		data = append(data, messageBuilder.BuildDetailMessage(detail))
	}

	rsp := &message.ListAnalyzeDetailResp{Data: data}
	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}

	return rsp, page, nil
}

// Compatibility Methods (from CompatibilityHandler)

// ListBasicIncompatibleFeature lists basic incompatible features.
// This method retrieves default system-wide incompatible features used as baseline for compatibility analysis.
func (s *metadataService) ListBasicIncompatibleFeature(ctx context.Context, req *message.ListBasicIncompatibleFeatureReq) (*message.ListBasicIncompatibleFeatureResp, error) {
	if err := ValidateBasicIncompatibleFeatureRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "basic incompatible feature", err)
	}

	// Step 1: Retrieve basic incompatible features from data layer
	features, getFeatureErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(ctx)
	if getFeatureErr != nil {
		return nil, fmt.Errorf("failed to list basic Oracle incompatible features: %w", getFeatureErr)
	}

	// Step 2: Convert entity objects to message format
	featureMessages := make([]*message.IncompatibleFeature, 0, len(features))
	for _, item := range features {
		featureMessages = append(featureMessages, &message.IncompatibleFeature{
			ID:           item.ID,
			FeatureType:  item.FeatureType,
			FeatureKey:   item.FeatureKey,
			FeatureScore: item.FeatureScore,
			FeatureDesc:  item.FeatureDesc,
			BaseFields: &message.BaseFields{
				Comment:   item.Comment,
				CreatedAt: item.CreatedAt,
				UpdatedAt: item.UpdatedAt,
			},
		})
	}

	return &message.ListBasicIncompatibleFeatureResp{
		Features: featureMessages,
	}, nil
}

// ListTaskIncompatibleFeature lists task-specific incompatible features.
// This method retrieves incompatible features customized for a specific task, merging with default features.
func (s *metadataService) ListTaskIncompatibleFeature(ctx context.Context, req *message.ListTaskIncompatibleFeatureReq) (*message.ListTaskIncompatibleFeatureResp, error) {
	if err := ValidateTaskIncompatibleFeatureRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "task incompatible feature", err)
	}

	// Step 1: Retrieve default basic features
	defaultFeatures, getFeatureErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(ctx)
	if getFeatureErr != nil {
		return nil, fmt.Errorf("failed to list basic Oracle incompatible features: %w", getFeatureErr)
	}

	// Step 2: Retrieve task-specific feature overrides
	features, getFeatureErr := models.GetObjectParserWriter().ListTaskOracleIncompatibleFeature(ctx, req.TaskId)
	if getFeatureErr != nil {
		return nil, fmt.Errorf("failed to list task Oracle incompatible features for taskId=%d: %w", req.TaskId, getFeatureErr)
	}

	// Step 3: Compose features (task-specific overrides default features)
	composedFeatureMap := core.ComposeFeatures(features, defaultFeatures, req.TaskId)
	retFeatures := make([]*message.IncompatibleFeature, 0, len(composedFeatureMap))
	for _, feature := range composedFeatureMap {
		retFeatures = append(retFeatures, &message.IncompatibleFeature{
			ID:           feature.ID,
			TaskId:       feature.TaskId,
			FeatureType:  feature.FeatureType,
			FeatureKey:   feature.FeatureKey,
			FeatureScore: feature.FeatureScore,
			FeatureDesc:  feature.FeatureDesc,
			BaseFields: &message.BaseFields{
				Comment:   feature.Comment,
				CreatedAt: feature.CreatedAt,
				UpdatedAt: feature.UpdatedAt,
			},
		})
	}

	return &message.ListTaskIncompatibleFeatureResp{
		Features: retFeatures,
	}, nil
}

// UpdateTaskIncompatibleFeature updates task-specific incompatible feature information.
func (s *metadataService) UpdateTaskIncompatibleFeature(ctx context.Context, req *message.UpdateTaskIncompatibleFeatureReq) (*message.UpdateTaskIncompatibleFeatureResp, error) {
	if err := ValidateUpdateTaskIncompatibleFeatureRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "update task incompatible feature", err)
	}

	feature := &objectparser.OracleTaskIncompatibleFeature{
		ID:           req.Data.ID,
		TaskId:       req.TaskId,
		FeatureType:  req.Data.FeatureType,
		FeatureKey:   req.Data.FeatureKey,
		FeatureScore: req.Data.FeatureScore,
		FeatureDesc:  req.Data.FeatureDesc,
	}

	updateErr := models.GetObjectParserWriter().SaveTaskOracleIncompatibleFeature(ctx, feature)
	if updateErr != nil {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_FEATURE_UPDATE_FAILED, updateErr.Error())
	}
	return &message.UpdateTaskIncompatibleFeatureResp{}, nil
}

// UpdateBasicIncompatibleFeature updates basic incompatible feature information.
func (s *metadataService) UpdateBasicIncompatibleFeature(ctx context.Context, req *message.UpdateBasicIncompatibleFeatureReq) (*message.UpdateBasicIncompatibleFeatureResp, error) {
	if err := ValidateUpdateBasicIncompatibleFeatureRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "update basic incompatible feature", err)
	}

	feature := &objectparser.OracleIncompatibleFeature{
		ID:           req.Data.ID,
		FeatureType:  req.Data.FeatureType,
		FeatureKey:   req.Data.FeatureKey,
		FeatureScore: req.Data.FeatureScore,
		FeatureDesc:  req.Data.FeatureDesc,
	}

	updateErr := models.GetObjectParserWriter().SaveBasicOracleIncompatibleFeature(ctx, feature)
	if updateErr != nil {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_FEATURE_UPDATE_FAILED, updateErr.Error())
	}
	return &message.UpdateBasicIncompatibleFeatureResp{}, nil
}

// GetIncompatibleFeatureScoring calculates compatibility scoring for features.
func (s *metadataService) GetIncompatibleFeatureScoring(ctx context.Context, req *message.GetIncompatibleFeatureScoringReq) (*message.GetIncompatibleFeatureScoringResp, error) {
	if err := ValidateGetIncompatibleFeatureScoringRequest(req); err != nil {
		return nil, fmt.Errorf(errorMessageInvalidRequest, "incompatible feature scoring", err)
	}

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		return nil, tmserrors.NewErrorf(tmserrors.TIMS_TASK_NOT_FOUND, getTaskErr.Error())
	}

	param, buildParamErr := core.BuildObjectParserParam(ctx, taskInfo)
	if buildParamErr != nil {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_BUILD_PARAM_FAILED, buildParamErr.Error())
	}

	defaultFeatures, getDefaultErr := models.GetObjectParserWriter().ListBasicOracleIncompatibleFeature(ctx)
	if getDefaultErr != nil {
		return nil, getDefaultErr
	}

	incompatibleFeatureRules, getRuleErr := models.GetObjectParserWriter().ListTaskOracleIncompatibleFeature(ctx, req.TaskId)
	if getRuleErr != nil {
		return nil, getRuleErr
	}

	details, _, getDetailErr := models.GetObjectParserWriter().ListOracleObjectDefinitionAnalyzeDetailByTaskId(ctx, req.TaskId, 1, 1024*1024*1024, "", "", "")
	if getDetailErr != nil {
		return nil, getDetailErr
	}

	histogramOptions := core.HistogramOptions{
		ScoreFactor: param.HistogramScoreWeightFactor,

		ArchiveTimes:          req.HistogramOptions.ArchiveTimes,
		ArchiveData:           req.HistogramOptions.ArchiveData,
		TransactionDataBlocks: req.HistogramOptions.TransactionDataBlocks,
		TransactionLogVolume:  req.HistogramOptions.TransactionLogVolume,
		LogVolumePerSecond:    req.HistogramOptions.LogVolumePerSecond,
		TransactionPerSecond:  req.HistogramOptions.TransactionPerSecond,
	}

	mb := core.NewMessageBuilder()

	scorer := core.InitOracleObjectScorer(req.ChannelId, req.TaskId, defaultFeatures, incompatibleFeatureRules, param)

	scoreResult := scorer.EvaluateTotalScoring(details, histogramOptions)

	return &message.GetIncompatibleFeatureScoringResp{
		Total:               mb.BuildScoring(scoreResult.Total),
		IncompatibleFeature: mb.BuildScoring(scoreResult.IncompatibleFeature),
		Histogram:           mb.BuildScoring(scoreResult.Histogram),
	}, nil
}

// Service Lifecycle Methods

// ValidateConfiguration validates the service configuration and dependencies.
// It checks that all required components (adaptor) are properly configured
// and that the service is enabled. Returns an error if any validation fails.
func (s *metadataService) ValidateConfiguration() error {
	if s.adaptor == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, errorMessageAdaptorNotConfigured)
	}

	if s.config == nil {
		// In test environment, config can be nil - that's OK
	} else if !s.config.Enable {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, errorMessageServiceDisabled)
	}

	return nil
}

// GetServiceStatus returns the current health status of the metadata service.
// It checks the availability of all required components and returns a status indicating
// whether the service is healthy and ready to process requests.
func (s *metadataService) GetServiceStatus() ServiceStatus {
	isHealthy := true
	message := statusMessageHealthy

	// Check adaptor health
	if s.adaptor == nil {
		isHealthy = false
		message = statusMessageAdaptorUnavailable
	}

	// Check configuration (allow nil config in test mode)
	if s.config != nil && !s.config.Enable {
		isHealthy = false
		message = statusMessageServiceDisabled
	}

	return ServiceStatus{
		IsHealthy: isHealthy,
		Message:   message,
		Version:   s.version,
	}
}
