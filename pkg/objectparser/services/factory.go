// Package services provides service factory for ObjectParser dependency injection.
package services

import (
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/adaptor"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/server/models/users"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// serviceFactory creates service instances with proper dependency injection.
type serviceFactory struct {
	adaptor        adaptor.Adaptor
	objectParserRW objectparser.ReaderWriter
	taskRW         task.TaskReaderWriter
	datasourceRW   datasource.ReaderWriter
	userRW         users.ReaderWriter
}

// NewServiceFactory creates a service factory with required dependencies.
func NewServiceFactory(
	objectParserAdaptor adaptor.Adaptor,
	objectParserRW objectparser.ReaderWriter,
	taskRW task.TaskReaderWriter,
	datasourceRW datasource.ReaderWriter,
	userRW users.ReaderWriter,
) ServiceFactory {
	return &serviceFactory{
		adaptor:        objectParserAdaptor,
		objectParserRW: objectParserRW,
		taskRW:         taskRW,
		datasourceRW:   datasourceRW,
		userRW:         userRW,
	}
}

// CreateParsingService creates parsing service with required dependencies.
func (f *serviceFactory) CreateParsingService() ParsingService {
	log.Infof("Creating ParsingService with direct business logic implementation")
	return NewParsingService(f.adaptor)
}

// CreateMetadataService creates metadata service with required dependencies.
func (f *serviceFactory) CreateMetadataService() MetadataService {
	log.Infof("Creating MetadataService with direct business logic implementation")
	return NewMetadataService(f.adaptor)
}

// CreateConversionService creates conversion service with required dependencies.
func (f *serviceFactory) CreateConversionService() ConversionService {
	log.Infof("Creating ConversionService with direct ReaderWriter dependency")
	return NewConversionService(f.objectParserRW)
}

// CreateArchiveService creates archive service with required dependencies.
func (f *serviceFactory) CreateArchiveService() ArchiveService {
	log.Infof("Creating ArchiveService with full implementation")
	return NewArchiveService() // Archive service uses direct database connections
}

// CreatePromptService creates prompt service with required dependencies.
func (f *serviceFactory) CreatePromptService() PromptService {
	log.Infof("Creating PromptService with direct ReaderWriter dependency")
	return NewPromptService(f.objectParserRW)
}
