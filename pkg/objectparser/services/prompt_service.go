// Package services implements PromptService for AI prompt management operations.
// This service handles AI prompt management.
package services

import (
	"context"
	"fmt"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

// promptService implements PromptService interface.
// It handles AI prompt management using direct ReaderWriter access.
type promptService struct {
	objectParserRW objectparser.ReaderWriter
	version        string
}

// NewPromptService creates a new instance of PromptService.
func NewPromptService(objectParserRW objectparser.ReaderWriter) PromptService {
	return &promptService{
		objectParserRW: objectParserRW,
		version:        ServiceVersion,
	}
}

// ListPrompt lists available AI prompts.
func (s *promptService) ListPrompt(ctx context.Context, req *message.ListPromptReq) (*message.ListPromptResp, error) {
	log.Infof("ListPrompt")
	features, getFeatureErr := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
	if getFeatureErr != nil {
		log.Errorf("ListPrompt failed, err: %v", getFeatureErr)
		return nil, getFeatureErr
	}

	prompts := lo.Map(features, func(item *objectparser.OracleObjectTransformationPrompt, _ int) *message.Prompt {
		return &message.Prompt{
			ID:              item.ID,
			PromptTitle:     item.PromptTitle,
			TargetLanguage:  item.TargetLanguage,
			TargetFramework: item.TargetFramework,
			PromptCategory:  item.PromptCategory,
			PromptText:      item.PromptText,
			IsDefault:       item.IsDefault,
			UsageExample:    item.UsageExample,
			BaseFields: &message.BaseFields{
				Comment:   item.Comment,
				CreatedAt: item.CreatedAt,
				UpdatedAt: item.UpdatedAt,
			},
		}
	})

	return &message.ListPromptResp{
		Prompts: prompts,
	}, nil
}

// SavePrompt saves or updates an AI prompt.
func (s *promptService) SavePrompt(ctx context.Context, req *message.SavePromptReq) (*message.SavePromptResp, error) {
	log.Infof("SavePrompt, feature:%v", req)

	// Validate PromptCategory
	if req.PromptCategory != "CODE" && req.PromptCategory != "TABLE" && req.PromptCategory != "TRIGGER" {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_INVALID_CATEGORY,
			"prompt category must be 'CODE', 'TABLE', or 'TRIGGER', got: %s", req.PromptCategory)
	}

	// Validate TABLE prompts cannot be default
	if req.PromptCategory == "TABLE" && req.IsDefault {
		return nil, tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PROMPT_INVALID_CATEGORY,
			"TABLE prompts cannot be set as default")
	}

	// For CODE and TRIGGER categories, check if another prompt of the same category is already default
	if req.IsDefault && (req.PromptCategory == "CODE" || req.PromptCategory == "TRIGGER") {
		allPrompts, getErr := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
		if getErr != nil {
			log.Errorf("ListOracleObjectTransformationPrompt failed, err: %v", getErr)
			return nil, getErr
		}
		
		for _, prompt := range allPrompts {
			// Check if another prompt of the same category is already default
			if prompt.IsDefault && 
			   prompt.PromptCategory == req.PromptCategory && 
			   prompt.ID != req.ID {
				return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_HAS_DEFAULT, 
					fmt.Sprintf("%s category already has a default prompt: %s", req.PromptCategory, prompt.PromptTitle))
			}
		}
	}

	var comment string
	if req.BaseFields != nil {
		comment = req.BaseFields.Comment
	} else {
		comment = ""
	}

	prompt := &objectparser.OracleObjectTransformationPrompt{
		ID:              req.ID,
		PromptTitle:     req.PromptTitle,
		PromptCategory:  req.PromptCategory,
		TargetLanguage:  req.TargetLanguage,
		TargetFramework: req.TargetFramework,
		PromptText:      req.PromptText,
		IsDefault:       req.IsDefault,
		UsageExample:    req.UsageExample,
		Entity:          &common.Entity{Comment: comment},
	}

	_, updateErr := models.GetObjectParserWriter().SaveOracleObjectTransformationPrompt(ctx, prompt)
	if updateErr != nil {
		log.Errorf("SaveOracleObjectTransformationPrompt failed, feature:%v, err: %v", req, updateErr)
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_SAVE_FAILED, updateErr.Error())
	}
	return &message.SavePromptResp{}, nil
}

// DeletePrompt deletes an AI prompt.
func (s *promptService) DeletePrompt(ctx context.Context, req *message.DeletePromptReq) (*message.DeletePromptResp, error) {
	log.Infof("DeletePrompt, promptId:%d", req.PromptId)

	// First, get the prompt to be deleted
	promptToDelete, getErr := models.GetObjectParserWriter().GetOracleObjectTransformationPrompt(ctx, req.PromptId)
	if getErr != nil {
		log.Errorf("GetOracleObjectTransformationPrompt failed, promptId:%d, err: %v", req.PromptId, getErr)
		return nil, getErr
	}

	// Get all prompts to check if this is the last one for the combination
	allPrompts, listErr := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
	if listErr != nil {
		log.Errorf("ListOracleObjectTransformationPrompt failed, err: %v", listErr)
		return nil, listErr
	}

	// Count prompts with the same category, language, and framework
	count := 0
	for _, prompt := range allPrompts {
		if prompt.PromptCategory == promptToDelete.PromptCategory &&
			prompt.TargetLanguage == promptToDelete.TargetLanguage &&
			prompt.TargetFramework == promptToDelete.TargetFramework {
			count++
		}
	}

	// Check if this is the last prompt for this combination
	if count <= 1 {
		errMsg := fmt.Sprintf("Cannot delete the last prompt for category=%s, language=%s, framework=%s",
			promptToDelete.PromptCategory, promptToDelete.TargetLanguage, promptToDelete.TargetFramework)
		log.Errorf(errMsg)
		return nil, tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PROMPT_DELETE_LAST_ONE, errMsg)
	}

	// Proceed with deletion in transaction
	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		remoteErr := models.GetObjectParserWriter().RemoveOracleObjectTransformationPrompt(transactionCtx, req.PromptId)
		if remoteErr != nil {
			log.Errorf("RemoveOracleObjectTransformationPrompt failed, promptId:%d, err: %v", req.PromptId, remoteErr)
			return remoteErr
		}

		removeErr := models.GetObjectParserWriter().RemoveOracleObjectTaskObjectPromptRelationByPromptId(transactionCtx, req.PromptId)
		if removeErr != nil {
			log.Errorf("RemoveOracleObjectTaskObjectPromptRelationByPromptId failed, promptId:%d, err: %v", req.PromptId, removeErr)
			return removeErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("DeletePrompt failed, promptId:%d, err: %v", req.PromptId, trxErr)
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_RELATION_DELETE_FAILED, trxErr.Error())
	}

	return &message.DeletePromptResp{}, nil
}

// ListTaskObjectPromptRelation lists relationships between tasks, objects, and prompts with pagination.
func (s *promptService) ListTaskObjectPromptRelation(ctx context.Context, req *message.ListTaskObjectPromptRelationReq) (*message.ListTaskObjectPromptRelationResp, *message.Page, error) {
	log.Infof("ListTaskObjectPromptRelation, taskId:%d, page:%d, pageSize:%d", req.TaskId, req.Page, req.PageSize)
	relations, total, getRelationErr := models.GetObjectParserWriter().ListOracleObjectTaskObjectPromptRelation(ctx, req.TaskId, req.Page, req.PageSize)
	if getRelationErr != nil {
		log.Errorf("ListTaskObjectPromptRelation failed, taskId:%d, page:%d, pageSize:%d, err: %v", req.TaskId, req.Page, req.PageSize, getRelationErr)
		return nil, nil, getRelationErr
	}

	var messages []*message.PromptRelation
	for _, relation := range relations {
		messages = append(messages, &message.PromptRelation{
			ID:             relation.ID,
			ChannelId:      relation.ChannelId,
			TaskId:         relation.TaskId,
			PromptId:       relation.TaskPromptId,
			DependencyUUID: relation.DependencyUUID,
		})
	}

	page := &message.Page{
		Page:     req.Page,
		PageSize: req.PageSize,
		Total:    total,
	}

	return &message.ListTaskObjectPromptRelationResp{
		Data: messages,
	}, page, nil
}

// SaveTaskObjectPromptRelation saves or updates a task-object-prompt relationship.
func (s *promptService) SaveTaskObjectPromptRelation(ctx context.Context, req *message.SaveTaskObjectPromptRelationReq) (*message.SaveTaskObjectPromptRelationResp, error) {
	log.Infof("SaveTaskObjectPromptRelation, taskId:%d, taskPromptId:%d, relationId:%d", req.TaskId, req.TaskPromptId, req.RelationId)
	_, getPromptErr := models.GetObjectParserWriter().GetOracleObjectTransformationPrompt(ctx, req.TaskPromptId)
	if getPromptErr != nil {
		log.Errorf("SaveTaskObjectPromptRelation, get prompt failed, taskId:%d, taskPromptId:%d, err: %v", req.TaskId, req.TaskPromptId, getPromptErr)
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_NOT_FOUND, getPromptErr.Error())
	}

	prompt := &objectparser.OracleObjectTaskObjectPromptRelation{
		ID:             req.RelationId,
		ChannelId:      req.ChannelId,
		TaskId:         req.TaskId,
		DependencyUUID: req.DependencyUUID,
		TaskPromptId:   req.TaskPromptId,
	}

	relation, updateErr := models.GetObjectParserWriter().SaveOracleObjectTaskObjectPromptRelation(ctx, prompt)
	if updateErr != nil {
		log.Errorf("SaveOracleObjectTaskObjectPromptRelation failed, taskId:%d, taskPromptId:%d, relationId:%d, err: %v", req.TaskId, req.TaskPromptId, req.RelationId, updateErr)
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_RELATION_SAVE_FAILED, updateErr.Error())
	}
	return &message.SaveTaskObjectPromptRelationResp{
		ChannelId:    relation.ChannelId,
		TaskId:       relation.TaskId,
		TaskPromptId: relation.TaskPromptId,
		RelationId:   relation.ID,
	}, nil
}

// DeleteTaskObjectPromptRelation deletes a task-object-prompt relationship.
func (s *promptService) DeleteTaskObjectPromptRelation(ctx context.Context, req *message.DeleteTaskObjectPromptRelationReq) (*message.DeleteTaskObjectPromptRelationResp, error) {
	log.Infof("DeleteTaskObjectPromptRelation, taskId:%d, relationId:%d", req.TaskId, req.RelationId)
	remoteErr := models.GetObjectParserWriter().RemoveOracleObjectTaskObjectPromptRelation(ctx, req.TaskId, req.RelationId)
	if remoteErr != nil {
		log.Errorf("RemoveOracleObjectTaskObjectPromptRelation failed, taskId:%d, relationId:%d, err: %v", req.TaskId, req.RelationId, remoteErr)
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_PROMPT_RELATION_DELETE_FAILED, remoteErr.Error())
	}
	return &message.DeleteTaskObjectPromptRelationResp{}, nil
}
