// Package services implements ArchiveService for Oracle archive log operations.
// This service handles Oracle archive log queries and transaction analysis.
package services

import (
	"context"
	"time"

	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/queries"

	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
)

// archiveService implements ArchiveService interface.
// It handles Oracle archive log queries and transaction analysis using direct database access.
type archiveService struct {
	version string
}

// NewArchiveService creates a new instance of ArchiveService.
func NewArchiveService() ArchiveService {
	return &archiveService{
		version: ServiceVersion,
	}
}

// GetArchiveData retrieves archive log data with hourly statistics.
func (s *archiveService) GetArchiveData(ctx context.Context, req *message.GetArchiveDataReq) (*message.GetArchiveDataResp, error) {
	log.Infof("GetArchiveData channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetArchiveData failed, channelId:%d, err: %v", req.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		log.Errorf("GetArchiveData failed, channelId:%d, taskId:%d, err: %v", req.ChannelId, req.TaskId, setUpErr)
		return nil, setUpErr
	}

	startTime := time.Now()
	log.Infof("query oracle archive data sql, startTime:%v, sql:%s", startTime, queries.GetArchiveDataSQL)
	_, res, queryErr := oracle.Query(ctx, dbConns.GetSourceDB().OracleDB, queries.GetArchiveDataSQL)
	if queryErr != nil {
		log.Errorf("query oracle archive data sql failed, err:%v", queryErr)
		return nil, queryErr
	}
	log.Infof("query oracle archive data sql success, costTime:%v", time.Since(startTime))

	archiveData := make([]message.ArchiveData, 0, len(res))
	for _, item := range res {
		archiveData = append(archiveData, message.ArchiveData{
			Day:             item["DAY"],
			HourZero:        item["H00"],
			HourOne:         item["H01"],
			HourTwo:         item["H02"],
			HourThree:       item["H03"],
			HourFour:        item["H04"],
			HourFive:        item["H05"],
			HourSix:         item["H06"],
			HourSeven:       item["H07"],
			HourEight:       item["H08"],
			HourNine:        item["H09"],
			HourTen:         item["H10"],
			HourEleven:      item["H11"],
			HourTwelve:      item["H12"],
			HourThirteen:    item["H13"],
			HourFourteen:    item["H14"],
			HourFifteen:     item["H15"],
			HourSixteen:     item["H16"],
			HourSeventeen:   item["H17"],
			HourEighteen:    item["H18"],
			HourNineteen:    item["H19"],
			HourTwenty:      item["H20"],
			HourTwentyOne:   item["H21"],
			HourTwentyTwo:   item["H22"],
			HourTwentyThree: item["H23"],
			Total:           item["TOTAL"],
		})
	}
	hourData, dayData := extractArchiveDataToResponseData(archiveData)

	graphData := &message.GraphData{
		Times:  make([]string, 0),
		Values: make([][]float64, 0),
		Titles: make([]string, 0),
	}

	values := make([]float64, 0)
	for _, hourDatum := range hourData {
		graphData.Times = append(graphData.Times, hourDatum.Time)
		values = append(values, hourDatum.Value)
	}
	graphData.Values = append(graphData.Values, values)

	return &message.GetArchiveDataResp{
		GraphData: graphData,
		DayData:   dayData,
		HourData:  hourData,
	}, nil
}

// GetArchiveTimes retrieves archive log timing information.
func (s *archiveService) GetArchiveTimes(ctx context.Context, req *message.GetArchiveTimesReq) (*message.GetArchiveTimesResp, error) {
	log.Infof("GetArchiveTimes channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetArchiveTimes failed, channelId:%d, err: %v", req.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		log.Errorf("GetArchiveTimes failed, channelId:%d, taskId:%d, err: %v", req.ChannelId, req.TaskId, setUpErr)
		return nil, setUpErr
	}

	startTime := time.Now()
	log.Infof("query oracle archive times sql, startTime:%v, sql:%s", startTime, queries.GetArchiveTimesSQL)
	_, res, queryErr := oracle.Query(ctx, dbConns.GetSourceDB().OracleDB, queries.GetArchiveTimesSQL)
	if queryErr != nil {
		log.Errorf("query oracle archive times sql failed, err:%v", queryErr)
		return nil, queryErr
	}
	log.Infof("query oracle archive times sql, endTime:%v, costTime:%v", time.Now(), time.Since(startTime))

	archiveTimes := make([]message.ArchiveData, 0, len(res))
	for _, item := range res {
		archiveTimes = append(archiveTimes, message.ArchiveData{
			Day:             item["DAY"],
			HourZero:        item["H00"],
			HourOne:         item["H01"],
			HourTwo:         item["H02"],
			HourThree:       item["H03"],
			HourFour:        item["H04"],
			HourFive:        item["H05"],
			HourSix:         item["H06"],
			HourSeven:       item["H07"],
			HourEight:       item["H08"],
			HourNine:        item["H09"],
			HourTen:         item["H10"],
			HourEleven:      item["H11"],
			HourTwelve:      item["H12"],
			HourThirteen:    item["H13"],
			HourFourteen:    item["H14"],
			HourFifteen:     item["H15"],
			HourSixteen:     item["H16"],
			HourSeventeen:   item["H17"],
			HourEighteen:    item["H18"],
			HourNineteen:    item["H19"],
			HourTwenty:      item["H20"],
			HourTwentyOne:   item["H21"],
			HourTwentyTwo:   item["H22"],
			HourTwentyThree: item["H23"],
			Total:           item["TOTAL"],
		})
	}

	hourData, dayData := extractArchiveDataToResponseData(archiveTimes)

	graphData := &message.GraphData{
		Times:  make([]string, 0),
		Values: make([][]float64, 0),
		Titles: make([]string, 0),
	}

	values := make([]float64, 0)
	for _, hourDatum := range hourData {
		graphData.Times = append(graphData.Times, hourDatum.Time)
		values = append(values, hourDatum.Value)
	}
	graphData.Values = append(graphData.Values, values)

	return &message.GetArchiveTimesResp{
		GraphData: graphData,
		HourData:  hourData,
		DayData:   dayData,
	}, nil
}

// GetTransactionDataBlocks retrieves transaction data block statistics.
func (s *archiveService) GetTransactionDataBlocks(ctx context.Context, req *message.GetTransactionDataBlocksReq) (*message.GetTransactionDataBlocksResp, error) {
	log.Infof("GetTransactionDataBlocks channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetTransactionDataBlocks failed, channelId:%d, err: %v", req.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		log.Errorf("GetTransactionDataBlocks failed, channelId:%d, taskId:%d, err: %v", req.ChannelId, req.TaskId, setUpErr)
		return nil, setUpErr
	}

	startTime := time.Now()
	log.Infof("query oracle transaction data blocks sql, startTime:%v, sql:%s", startTime, queries.GetTransactionDataBlocksSQL)
	_, res, queryErr := oracle.Query(ctx, dbConns.GetSourceDB().OracleDB, queries.GetTransactionDataBlocksSQL)
	if queryErr != nil {
		log.Errorf("query oracle transaction data blocks sql failed, err:%v", queryErr)
		return nil, queryErr
	}
	log.Infof("query oracle transaction data blocks sql success, costTime:%v", time.Since(startTime))

	transactionDataBlocks := make([]message.TransactionDataBlock, 0, len(res))
	for _, item := range res {
		transactionDataBlocks = append(transactionDataBlocks, message.TransactionDataBlock{
			SnapshotID:     item["SNAPSHOTID"],
			SnapshotTime:   item["SNAPSHOTTIME"],
			PerSecondValue: parseStringToFloat64(item["REDO_PSEC"]),
		})
	}

	graphData := &message.GraphData{
		Times:  make([]string, 0),
		Values: make([][]float64, 0),
		Titles: make([]string, 0),
	}

	divideBy := req.DivideBy
	if divideBy <= 0 {
		divideBy = 1000
	}

	values := make([]float64, 0)
	for _, transactionDataBlock := range transactionDataBlocks {
		graphData.Times = append(graphData.Times, transactionDataBlock.SnapshotTime)
		values = append(values, parse.TruncateFloat(transactionDataBlock.PerSecondValue/divideBy, 0))
	}
	graphData.Values = append(graphData.Values, values)

	return &message.GetTransactionDataBlocksResp{
		Data:      transactionDataBlocks,
		GraphData: graphData,
	}, nil
}

// GetTransactionLogVolume retrieves transaction log volume information.
func (s *archiveService) GetTransactionLogVolume(ctx context.Context, req *message.GetTransactionLogVolumeReq) (*message.GetTransactionLogVolumeResp, error) {
	log.Infof("GetTransactionLogVolume channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetTransactionLogVolume failed, channelId:%d, err: %v", req.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		log.Errorf("GetTransactionLogVolume failed, channelId:%d, taskId:%d, err: %v", req.ChannelId, req.TaskId, setUpErr)
		return nil, setUpErr
	}

	startTime := time.Now()
	log.Infof("query oracle transaction log volume sql, startTime:%v, sql:%s", startTime, queries.GetTransactionLogVolumeSQL)
	_, res, queryErr := oracle.Query(ctx, dbConns.GetSourceDB().OracleDB, queries.GetTransactionLogVolumeSQL)
	if queryErr != nil {
		log.Errorf("query oracle transaction log volume sql failed, err:%v", queryErr)
		return nil, queryErr
	}
	log.Infof("query oracle transaction log volume sql success, costTime:%v", time.Since(startTime))

	transactionLogVolumes := make([]message.TransactionDataBlock, 0, len(res))
	for _, item := range res {
		transactionLogVolumes = append(transactionLogVolumes, message.TransactionDataBlock{
			SnapshotID:     item["SNAPSHOTID"],
			SnapshotTime:   item["SNAPSHOTTIME"],
			PerSecondValue: parseStringToFloat64(item["REDO_PSEC"]),
		})
	}

	graphData := &message.GraphData{
		Times:  make([]string, 0),
		Values: make([][]float64, 0),
		Titles: make([]string, 0),
	}

	divideBy := req.DivideBy
	if divideBy <= 0 {
		divideBy = 1024 * 1024
	}
	values := make([]float64, 0)
	for _, transactionLogVolume := range transactionLogVolumes {
		graphData.Times = append(graphData.Times, transactionLogVolume.SnapshotTime)
		values = append(values, parse.TruncateFloat(transactionLogVolume.PerSecondValue/divideBy, 0))
	}
	graphData.Values = append(graphData.Values, values)

	return &message.GetTransactionLogVolumeResp{
		Data:      transactionLogVolumes,
		GraphData: graphData,
	}, nil
}

// GetLogVolumePerSecond retrieves log volume per second statistics.
func (s *archiveService) GetLogVolumePerSecond(ctx context.Context, req *message.GetLogVolumePerSecondReq) (*message.GetLogVolumePerSecondResp, error) {
	log.Infof("GetLogVolumePerSecond channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetLogVolumePerSecond failed, channelId:%d, err: %v", req.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		log.Errorf("GetLogVolumePerSecond failed, channelId:%d, taskId:%d, err: %v", req.ChannelId, req.TaskId, setUpErr)
		return nil, setUpErr
	}

	startTime := time.Now()
	log.Infof("query oracle log volume per second sql, startTime:%v, sql:%s", startTime, queries.GetLogVolumePerSecondSQL)
	_, res, queryErr := oracle.Query(ctx, dbConns.GetSourceDB().OracleDB, queries.GetLogVolumePerSecondSQL)
	if queryErr != nil {
		log.Errorf("query oracle log volume per second sql failed, err:%v", queryErr)
		return nil, queryErr
	}
	log.Infof("query oracle log volume per second sql success, costTime:%v", time.Since(startTime))

	data := make([]message.TransactionDataBlock, 0, len(res))
	for _, item := range res {
		data = append(data, message.TransactionDataBlock{
			SnapshotID:     "",
			SnapshotTime:   item["TIME_STAMP"],
			PerSecondValue: parseStringToFloat64(item["BLOCKS_PER_SECOND"]),
		})
	}

	return &message.GetLogVolumePerSecondResp{
		Data: data,
	}, nil
}

// GetTransactionPerSecond retrieves transaction per second statistics.
func (s *archiveService) GetTransactionPerSecond(ctx context.Context, req *message.GetTransactionPerSecondReq) (*message.GetTransactionPerSecondResp, error) {
	log.Infof("GetTransactionPerSecond channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		log.Errorf("GetTransactionPerSecond failed, channelId:%d, err: %v", req.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	dbConns, setUpErr := migration.SetUpOracleDatabaseConns(ctx, channelInfo, req.TaskId)
	if setUpErr != nil {
		log.Errorf("GetTransactionPerSecond failed, channelId:%d, taskId:%d, err: %v", req.ChannelId, req.TaskId, setUpErr)
		return nil, setUpErr
	}

	startTime := time.Now()
	log.Infof("query oracle transaction per second sql, startTime:%v, sql:%s", startTime, queries.GetTransactionPerSecondSQL)
	_, res, queryErr := oracle.Query(ctx, dbConns.GetSourceDB().OracleDB, queries.GetTransactionPerSecondSQL)
	if queryErr != nil {
		log.Errorf("query oracle transaction per second sql failed, err:%v", queryErr)
		return nil, queryErr
	}
	log.Infof("query oracle transaction per second sql success, costTime:%v", time.Since(startTime))

	transactionDataBlocks := make([]message.TransactionDataBlock, 0, len(res))
	for _, item := range res {
		transactionDataBlocks = append(transactionDataBlocks, message.TransactionDataBlock{
			SnapshotID:     "",
			SnapshotTime:   item["TIME_STAMP"],
			PerSecondValue: parseStringToFloat64(item["TRANSACTIONS_PER_SECOND"]),
		})
	}

	graphData := &message.GraphData{
		Times:  make([]string, 0),
		Values: make([][]float64, 0),
		Titles: make([]string, 0),
	}

	values := make([]float64, 0)
	for _, transactionDataBlock := range transactionDataBlocks {
		graphData.Times = append(graphData.Times, transactionDataBlock.SnapshotTime)
		values = append(values, transactionDataBlock.PerSecondValue)
	}
	graphData.Values = append(graphData.Values, values)

	return &message.GetTransactionPerSecondResp{
		Data:      transactionDataBlocks,
		GraphData: graphData,
	}, nil
}

// Helper functions

// extractArchiveDataToResponseData converts archive data to response format.
func extractArchiveDataToResponseData(archiveTimes []message.ArchiveData) ([]message.Datum, []message.Datum) {
	hourData := make([]message.Datum, 0, len(archiveTimes)*24)
	dayData := make([]message.Datum, 0, len(archiveTimes))

	for _, item := range archiveTimes {
		dayData = append(dayData, message.Datum{Time: item.Day, Value: parseStringToFloat64(item.Total)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 00:00:00", Value: parseStringToFloat64(item.HourZero)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 01:00:00", Value: parseStringToFloat64(item.HourOne)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 02:00:00", Value: parseStringToFloat64(item.HourTwo)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 03:00:00", Value: parseStringToFloat64(item.HourThree)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 04:00:00", Value: parseStringToFloat64(item.HourFour)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 05:00:00", Value: parseStringToFloat64(item.HourFive)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 06:00:00", Value: parseStringToFloat64(item.HourSix)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 07:00:00", Value: parseStringToFloat64(item.HourSeven)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 08:00:00", Value: parseStringToFloat64(item.HourEight)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 09:00:00", Value: parseStringToFloat64(item.HourNine)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 10:00:00", Value: parseStringToFloat64(item.HourTen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 11:00:00", Value: parseStringToFloat64(item.HourEleven)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 12:00:00", Value: parseStringToFloat64(item.HourTwelve)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 13:00:00", Value: parseStringToFloat64(item.HourThirteen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 14:00:00", Value: parseStringToFloat64(item.HourFourteen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 15:00:00", Value: parseStringToFloat64(item.HourFifteen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 16:00:00", Value: parseStringToFloat64(item.HourSixteen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 17:00:00", Value: parseStringToFloat64(item.HourSeventeen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 18:00:00", Value: parseStringToFloat64(item.HourEighteen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 19:00:00", Value: parseStringToFloat64(item.HourNineteen)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 20:00:00", Value: parseStringToFloat64(item.HourTwenty)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 21:00:00", Value: parseStringToFloat64(item.HourTwentyOne)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 22:00:00", Value: parseStringToFloat64(item.HourTwentyTwo)})
		hourData = append(hourData, message.Datum{Time: item.Day + " 23:00:00", Value: parseStringToFloat64(item.HourTwentyThree)})
	}
	return hourData, dayData
}

// parseStringToFloat64 converts string to float64 with error handling.
func parseStringToFloat64(vv string) float64 {
	v, _ := parse.ParseFloat64(vv)
	return v
}
