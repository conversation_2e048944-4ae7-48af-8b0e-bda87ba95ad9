package services

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/adaptor"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/core"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/dto"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
)

// Service constants
const (
	ServiceVersion = "1.0.0"
)

// parsingService consolidates PLSQL parsing and dependency analysis functionality
// It implements the ParsingService interface with direct business logic implementation
type parsingService struct {
	adaptor adaptor.Adaptor
	config  *config.ObjectParserConfig
	version string
}

// NewParsingService creates a new parsing service with direct implementation
func NewParsingService(objectParserAdaptor adaptor.Adaptor) ParsingService {
	var objParserConfig *config.ObjectParserConfig

	// Safely get config - handle case where global config might be nil (for tests)
	if globalConfig := config.GetGlobalConfig(); globalConfig != nil {
		objParserConfig = globalConfig.ObjectParserConfig
	}

	service := &parsingService{
		adaptor: objectParserAdaptor,
		config:  objParserConfig,
		version: ServiceVersion,
	}

	// Note: Logging removed to avoid panic in test environment
	return service
}

// ParsePLSQLToAST converts PL/SQL code to JSON-formatted AST representation.
// This method handles the complete workflow of PL/SQL parsing including:
// 1. Retrieving dependency information
// 2. Getting object definitions
// 3. Extracting PL/SQL segments
// 4. Converting to AST using adaptor
// 5. Processing package body objects specially
func (s *parsingService) ParsePLSQLToAST(ctx context.Context, req *message.PlSQLToJSONReq) (*message.PlSQLToJSONResp, error) {
	if err := ValidatePLSQLRequest(req); err != nil {
		return nil, fmt.Errorf("invalid PLSQL request: %w", err)
	}

	// Step 1: Retrieve dependency information
	dependencies, getErr := models.GetObjectParserWriter().ListOracleDependencyByUUIDs(ctx, req.TaskId, []string{req.DependencyUUID})
	if getErr != nil {
		return nil, fmt.Errorf("failed to list Oracle dependencies for taskId=%d, dependencyUUID=%s: %w", req.TaskId, req.DependencyUUID, getErr)
	}
	if len(dependencies) != 1 {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_GET_DEPENDENCY_FAILED, "dependency count is not 1, actual count is %d", len(dependencies))
	}

	// Step 2: Build schema object key
	var schemaObjectKey string
	var isFromPackageBody bool

	dependency := dependencies[0]
	if dependency.GetPackageName() != "" {
		schemaObjectKey = fmt.Sprintf("%s.%s.%s", dependency.GetSchemaName(), constants.OracleObjectTypePackageBody, dependency.GetPackageName())
		isFromPackageBody = true
	} else {
		schemaObjectKey = fmt.Sprintf("%s.%s.%s", dependency.GetSchemaName(), dependency.GetType(), dependency.GetName())
	}

	// Step 3: Get object definitions
	definitions, getDefErr := models.GetObjectParserWriter().GetOracleObjectDefinitionBySchemaObjectKeys(ctx, req.TaskId, []string{schemaObjectKey})
	if getDefErr != nil {
		return nil, fmt.Errorf("failed to get object definition for taskId=%d, schemaObjectKey=%s: %w", req.TaskId, schemaObjectKey, getDefErr)
	}
	if len(definitions) != 1 {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED, "definition count is not 1, actual count is %d", len(definitions))
	}

	// Step 4: Get object analysis details
	objectDetails, getDetailErr := models.GetObjectParserWriter().GetOracleObjectDefinitionAnalyzeDetailBySchemaObjectKeys(ctx, req.TaskId, []string{schemaObjectKey})
	if getDetailErr != nil {
		return nil, fmt.Errorf("failed to get object analysis details for taskId=%d, schemaObjectKey=%s: %w", req.TaskId, schemaObjectKey, getDetailErr)
	}
	if len(objectDetails) != 1 {
		return nil, tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_GET_DEFINITIONS_FAILED, "detail count is not 1, actual count is %d", len(objectDetails))
	}
	objectDetail := objectDetails[0]

	// Step 5: Extract PL/SQL segment
	var plsql string
	if isFromPackageBody {
		messageBuilder := core.NewMessageBuilder()
		objectPLSQLSegment := messageBuilder.ConstructPLSQLSegment(objectDetail.PLSQLSegment)
		plsqlSegment, hit := objectPLSQLSegment.CombineSegmentedSQL(dependency.GetType(), dependency.GetName())
		if hit {
			plsql = plsqlSegment
		} else {
			plsql = definitions[0].AllText
		}
	} else {
		plsql = definitions[0].AllText
	}

	log.Infof("parsing plsql to json, objectName:%s, objectType:%s, plsqlLen: %d", objectDetail.ObjectName, objectDetail.ObjectType, len(plsql))
	log.Debug(plsql)

	// Step 6: Convert to AST using adaptor
	actualReq := dto.PlSQLToJSONRequest{
		PLSQL:     stringutil.EncodeSQL(plsql),
		IsEncoded: true,
		Comment:   req.Comment,
	}

	actualResp, err := s.adaptor.PlSQLToJSON(ctx, actualReq)
	if err != nil {
		// Fallback to full text if segmented parsing fails
		fallbackReq := dto.PlSQLToJSONRequest{
			PLSQL:     stringutil.EncodeSQL(definitions[0].AllText),
			IsEncoded: true,
			Comment:   req.Comment,
		}
		actualResp, err = s.adaptor.PlSQLToJSON(ctx, fallbackReq)
		if err != nil {
			return nil, fmt.Errorf("failed to parse PL/SQL to AST for taskId=%d, dependencyUUID=%s: %w", req.TaskId, req.DependencyUUID, err)
		}
	}

	// Step 7: Process package body objects specially
	var root *dto.AbstractSyntaxTreeNode

	if isFromPackageBody {
		var stmtName, stmtType, stmtValue string
		if dependency.Type == constants.OracleObjectTypeProcedure {
			stmtName = "procedureName"
			stmtType = "createProcedure"
			stmtValue = dependency.Name
		} else {
			stmtName = "functionName"
			stmtType = "createFunction"
			stmtValue = dependency.Name
		}
		hitNodes := core.FindNodes(actualResp.Root, stmtType, stmtName, stmtValue)
		root = &dto.AbstractSyntaxTreeNode{
			StmtType:  "statementList",
			StmtValue: "statementList",
			Key:       "root",
			Children:  hitNodes,
		}
	} else {
		root = actualResp.Root
	}

	// Step 8: Build response
	builder := core.NewMessageBuilder()

	return &message.PlSQLToJSONResp{
		Root: builder.BuildAbstractSyntaxTree(root),
	}, nil
}

// GetDependencyFromMetadata analyzes and returns dependency relationships from metadata.
// This method handles the complete workflow of dependency analysis including:
// 1. Validating tasks, channels, and target schemas
// 2. Retrieving dependency data, object details, and prompt relations
// 3. Building dependency graph with cycle detection
// 4. Filtering and ordering results according to user preferences
func (s *parsingService) GetDependencyFromMetadata(ctx context.Context, req *message.GetDependencyFromMetadataReq) (*message.GetDependencyFromMetadataResp, error) {
	if err := ValidateDependencyRequest(req); err != nil {
		return nil, fmt.Errorf("invalid dependency request: %w", err)
	}

	// Step 1: Validate task and channel
	_, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if getTaskErr != nil {
		return nil, fmt.Errorf("failed to get task for taskId=%d: %w", req.TaskId, getTaskErr)
	}

	_, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, req.ChannelId)
	if getChannelErr != nil {
		return nil, fmt.Errorf("failed to get channel for channelId=%d: %w", req.ChannelId, getChannelErr)
	}

	// Step 2: Determine target schemas
	targetSchemas := req.Schemas
	if len(targetSchemas) == 0 {
		channelSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemas(ctx, req.ChannelId)
		if getSchemaErr != nil {
			return nil, fmt.Errorf("failed to get channel schemas for channelId=%d: %w", req.ChannelId, getSchemaErr)
		}
		for _, item := range channelSchemas {
			targetSchemas = append(targetSchemas, item.SchemaNameS)
		}
	}

	if len(targetSchemas) == 0 {
		return nil, tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_GET_DEPENDENCY_FAILED, "no schemas specified or found")
	}

	// Step 3: Retrieve dependency data
	dependencyPos, getDepErr := models.GetObjectParserWriter().ListOracleDependencyByTaskId(ctx, req.TaskId)
	if getDepErr != nil {
		return nil, fmt.Errorf("failed to list Oracle dependencies for taskId=%d: %w", req.TaskId, getDepErr)
	}

	detailPos, _, getDetailErr := models.GetObjectParserWriter().ListOracleObjectDefinitionAnalyzeDetailByTaskId(ctx, req.TaskId, 1, 1024*1024*1024, "", "", "")
	if getDetailErr != nil {
		return nil, fmt.Errorf("failed to list Oracle object definition analyze details for taskId=%d: %w", req.TaskId, getDetailErr)
	}

	// Step 3.5: Load object parser configurations
	objectFilterCfgs, cfgErr := models.GetObjectParserWriter().ListObjectParserCfgsByTaskId(ctx, req.TaskId)
	if cfgErr != nil {
		// Log warning but continue processing
		log.Warnf("Failed to load object parser configs for taskId=%d: %v", req.TaskId, cfgErr)
		objectFilterCfgs = nil
	} else if len(objectFilterCfgs) > 0 {
		log.Infof("Loaded %d object filter configurations for taskId=%d", len(objectFilterCfgs), req.TaskId)
	}

	// Step 4: Retrieve prompt and relation data
	prompts, getPromptErr := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
	if getPromptErr != nil {
		return nil, fmt.Errorf("failed to list Oracle object transformation prompts: %w", getPromptErr)
	}

	relations, _, getRelationErr := models.GetObjectParserWriter().ListOracleObjectTaskObjectPromptRelation(ctx, uint(req.TaskId), 1, 1024*1024*1024)
	if getRelationErr != nil {
		return nil, fmt.Errorf("failed to list Oracle object task object prompt relations for taskId=%d: %w", req.TaskId, getRelationErr)
	}

	// Step 5: Identify database link objects
	messageBuilder := core.NewMessageBuilder()
	hasDbLinkObjects := map[string]bool{}
	for _, detailPo := range detailPos {
		tableReferenceList := messageBuilder.ConstructTableReferenceList(detailPo.TableReferenceList)
		for _, tableReference := range tableReferenceList {
			// Mark database link objects
			if tableReference.IsDatabaseLink || len(tableReference.TableName) > 0 && (tableReference.TableName[len(tableReference.TableName)-1:] == "@" || strings.Contains(tableReference.TableName, "@")) {
				hasDbLinkObjects[detailPo.SchemaObjectKey] = true
				tableReferenceObjectKey := fmt.Sprintf("%s.%s.%s", detailPo.SchemaName, strings.ToUpper(tableReference.StatementType), strings.ToUpper(tableReference.StatementName))
				hasDbLinkObjects[tableReferenceObjectKey] = true
			}
		}
	}

	// Step 6: Build dependency analysis using helper
	helper := core.NewOracleDependencyHelper(dependencyPos, targetSchemas)
	helper.SetOracleObjectStatusListFromDetail(detailPos, req.ExcludeInvalidObject)
	helper.SetOracleObjectFilter(req.ObjectFilters)
	helper.SetOracleObjectUUIDFilter(req.ObjectUUIDFilters)
	helper.SetOracleObjectKeyFilter(req.ObjectKeyFilters)
	helper.SetOracleContainDBLinkObject(hasDbLinkObjects)
	helper.SetPromptAndRelation(req.IncludePromptRelation, prompts, relations)
	helper.SetOracleObjectCfgFilter(objectFilterCfgs)

	dependencyNodeMap := helper.BuildTreeNodeChildrenPointers()

	// Step 7: Filter dependency nodes
	helper.FilterTreeNodeWithTargetSchema(dependencyNodeMap)
	helper.FilterTreeNodeType(dependencyNodeMap)

	// Step 8: Build tree node children
	helper.MakeTreeNodeChildrenByPointers(dependencyNodeMap)

	// Step 9: Apply user filters
	helper.PruneTreeNodeByFilter(dependencyNodeMap)

	// Step 10: Detect and break cycles
	nodeList := make([]*structs.DependencyTreeNode, 0, len(dependencyNodeMap))
	for _, node := range dependencyNodeMap {
		nodeList = append(nodeList, node)
	}
	sort.Slice(nodeList, func(i, j int) bool {
		a, b := nodeList[i], nodeList[j]

		if a.GetSchemaName() != b.GetSchemaName() {
			return a.GetSchemaName() < b.GetSchemaName()
		}
		if a.GetType() != b.GetType() {
			return a.GetType() < b.GetType()
		}
		if a.GetPackageName() != b.GetPackageName() {
			return a.GetPackageName() < b.GetPackageName()
		}
		return a.GetName() < b.GetName()
	})
	_ = helper.FindCycleAndBreak(nodeList, "nodeList")

	treeNodes := helper.OutputTreeNodes(nodeList, req.OrderType)
	_ = helper.FindCycleAndBreak(treeNodes, "treeNodes")

	// Step 11: Generate output displays
	displayTree := helper.OutputToDisplayTree(treeNodes)
	displayGraph := helper.OutputToDisplayGraph(treeNodes)

	// Step 12: Build response
	fields := req.Fields
	rsp := &message.GetDependencyFromMetadataResp{
		Dependencies: treeNodes,
		DisplayTree:  displayTree,
		DisplayGraph: displayGraph,
	}
	rsp.ApplyOutputFields(fields)

	return rsp, nil
}

// ValidateConfiguration validates the service configuration and dependencies
func (s *parsingService) ValidateConfiguration() error {
	if s.adaptor == nil {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "adaptor is not configured")
	}

	if s.config == nil {
		// In test environment, config can be nil - that's OK
	} else if !s.config.Enable {
		return tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_ADAPTOR_NOT_EXIST, "ObjectParser service is disabled")
	}

	return nil
}

// GetServiceStatus returns the current health status of the parsing service
func (s *parsingService) GetServiceStatus() ServiceStatus {
	isHealthy := true
	message := "Service is healthy"

	// Check adaptor health
	if s.adaptor == nil {
		isHealthy = false
		message = "Adaptor not available"
	}

	// Check configuration (allow nil config in test mode)
	if s.config != nil && !s.config.Enable {
		isHealthy = false
		message = "Service is disabled"
	}

	return ServiceStatus{
		IsHealthy: isHealthy,
		Message:   message,
		Version:   s.version,
	}
}
