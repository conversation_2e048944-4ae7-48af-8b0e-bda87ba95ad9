// Package services implements ConversionService for PL/SQL to Java conversion operations.
// This service handles AI-powered conversion workflows with comprehensive license validation,
// dependency resolution, and result management.
package services

import (
	"context"
	"database/sql"
	"fmt"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/pkg/ai"
	aiprovider "gitee.com/pingcap_enterprise/tms/pkg/ai/provider"
	"gitee.com/pingcap_enterprise/tms/pkg/license"
	"gitee.com/pingcap_enterprise/tms/pkg/migration"
	"gitee.com/pingcap_enterprise/tms/pkg/objectparser/core"
	"gitee.com/pingcap_enterprise/tms/server/crypto"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/objectparser"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	fileutil "gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
)

// conversionService implements ConversionService interface.
// It handles PL/SQL to Java code conversion using AI services with direct ReaderWriter.
type conversionService struct {
	objectParserRW objectparser.ReaderWriter
	version        string
}

// TableDefinition represents a table with its DDL
type TableDefinition struct {
	TableName string
	DDL       string
}

// ConversionPreparation contains all data for the conversion preparation phase
type ConversionPreparation struct {
	Request        *message.ConvertPLSQLToJavaReq
	TaskInfo       *task.Task
	ChannelInfo    *channel.ChannelInformation
	Params         *structs.ObjectParserParam
	LicenseManager *license.LicenseManager
	ConvertHelper  *core.ConvertHelper
	Metadata       *ConversionMetadata
	PromptSelector *core.PromptSelector
	Summary        *objectparser.OracleToJavaSummary // Conversion summary initialized early

	// Unified prompt strategy management
	PromptStrategy *core.ConversionPromptStrategy
}

// ConversionMetadata contains all metadata information for conversion
type ConversionMetadata struct {
	Objects         []*objectparser.OracleDependency
	Dependencies    []*objectparser.OracleDependencyVO
	Definitions     []*objectparser.OracleObjectDefinition
	AnalyzeDetails  []*objectparser.OracleObjectDefinitionAnalyzeDetail
	Prompts         []*objectparser.OracleObjectTransformationPrompt
	PromptRelations []*objectparser.OracleObjectTaskObjectPromptRelation
	AllFuncAndProcs []*objectparser.OracleDependency
}

// TaskBundle contains task-related information
type TaskBundle struct {
	TaskInfo    *task.Task
	ChannelInfo *channel.ChannelInformation
	Params      *structs.ObjectParserParam
}

// LicenseInfo contains license validation information
type LicenseInfo struct {
	Manager      *license.LicenseManager
	ObjectCounts map[string]uint
}

// ConversionContext contains the context for conversion execution
type ConversionContext struct {
	ConvertItems  []*aiprovider.ConvertItem
	DefinitionMap map[string]objectparser.OracleObjectDefinition
	PromptHelper  *core.PromptHelper
	DefaultPrompt *objectparser.OracleObjectTransformationPrompt
}

// NewConversionService creates a new instance of ConversionService.
func NewConversionService(objectParserRW objectparser.ReaderWriter) ConversionService {
	return &conversionService{
		objectParserRW: objectParserRW,
		version:        ServiceVersion,
	}
}

// writeFileWithDirectory writes file and creates directory structure if needed
func (s *conversionService) writeFileWithDirectory(workingDir, filePath, content string) error {
	fullPath := path.Join(workingDir, filePath)

	// Create directory structure
	dir := path.Dir(fullPath)
	if err := fileutil.CreateIfNotExist(dir); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// Write file
	return fileutil.CreateOrReplaceFile(fullPath, content)
}

// getSourceSchemas retrieves the target schemas from the channel configuration
func (s *conversionService) getSourceSchemas(ctx context.Context, taskInfo *task.Task) ([]string, error) {
	log.Infof("Retrieving source schemas for taskId: %d, channelId: %d", taskInfo.TaskID, taskInfo.ChannelId)

	channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Failed to get channel schemas, taskId: %d, channelId: %d, err: %v", taskInfo.TaskID, taskInfo.ChannelId, err)
		return nil, fmt.Errorf("failed to get channel schemas: %w", err)
	}

	var sourceSchemas []string
	for _, cs := range channelSchemas {
		sourceSchemas = append(sourceSchemas, cs.SchemaNameS)
	}

	log.Infof("Successfully retrieved %d source schemas for taskId: %d", len(sourceSchemas), taskInfo.TaskID)
	return sourceSchemas, nil
}

// fetchTableDDL retrieves the DDL for a specific table
func (s *conversionService) fetchTableDDL(ctx context.Context, dbConn *sql.DB, schema, table string) (string, error) {
	ddl, err := models.GetDatasourceReaderWriter().GetOracleDDL(ctx, schema, table, constants.OracleObjectTypeTable, dbConn)
	if err != nil {
		return "", fmt.Errorf("failed to get DDL for table %s.%s: %w", schema, table, err)
	}
	return ddl, nil
}

// extractUniqueTablesFromAnalysis extracts unique table names from all analysis details
func (s *conversionService) extractUniqueTablesFromAnalysis(
	ctx context.Context,
	analyzeDetails []*objectparser.OracleObjectDefinitionAnalyzeDetail,
) []structs.SchemaTablePair {
	builder := core.NewMessageBuilder()
	tableMap := make(map[structs.SchemaTablePair]bool)

	// Extract tables from each object's analysis
	for _, detail := range analyzeDetails {
		if detail.TableReferenceList != "" && detail.TableReferenceList != "{}" {
			tables := builder.ExtractTableNamesFromJSON(detail.TableReferenceList)
			for _, table := range tables {
				tableMap[structs.SchemaTablePair{SchemaName: detail.SchemaName, TableName: table}] = true
			}
		}
	}

	// Convert map to sorted slice
	var uniqueTables []structs.SchemaTablePair
	for table := range tableMap {
		uniqueTables = append(uniqueTables, table)
	}

	log.Infof("extractUniqueTablesFromAnalysis found %d unique tables", len(uniqueTables))
	return uniqueTables
}

// convertTablesToEntitiesWithPrompt converts table definitions to entity classes using a specific prompt
func (s *conversionService) convertTablesToEntitiesWithPrompt(
	ctx context.Context,
	schemaTableMap map[string][]TableDefinition,
	frameworkType string,
	entityPrompt *objectparser.OracleObjectTransformationPrompt,
	taskId int,
	aiConfig structs.LLMProviderAPIConfig,
) ([]*objectparser.OracleToJavaResult, error) {
	totalTables := 0
	for _, tables := range schemaTableMap {
		totalTables += len(tables)
	}

	if totalTables == 0 {
		log.Infof("No tables to convert to entities for taskId:%d", taskId)
		return nil, nil
	}

	log.Infof("Converting %d tables from %d schemas to %s entities using prompt '%s' for taskId:%d",
		totalTables, len(schemaTableMap), frameworkType, entityPrompt.PromptTitle, taskId)

	// Create AI helper
	aiHelper := ai.NewAIConvertorProxy(taskId, aiConfig)

	var results []*objectparser.OracleToJavaResult

	// Process each schema separately
	for schema, tableDefinitions := range schemaTableMap {
		log.Infof("Processing schema %s with %d tables for taskId:%d", schema, len(tableDefinitions), taskId)

		// Convert tables in batches within the same schema
		batchSize := 5
		for i := 0; i < len(tableDefinitions); i += batchSize {
			end := i + batchSize
			if end > len(tableDefinitions) {
				end = len(tableDefinitions)
			}

			batchTables := tableDefinitions[i:end]

			// Build DDL content for the batch
			var ddlContent strings.Builder
			var tableNames []string

			for idx, tableDef := range batchTables {
				if idx > 0 {
					ddlContent.WriteString("\n\n-- ========================================\n\n")
				}
				ddlContent.WriteString(fmt.Sprintf("-- Table: %s.%s\n", schema, tableDef.TableName))
				ddlContent.WriteString(tableDef.DDL)
				tableNames = append(tableNames, tableDef.TableName)
			}

			// Build conversion item for entity generation
			convertItem := &aiprovider.ConvertItem{
				FeatureID:        constants.OpTableNum, // Use package feature for entity conversion
				SchemaName:       schema,
				PackageName:      fmt.Sprintf("%s.%s", frameworkType, strings.ToLower(schema)),
				ObjectType:       constants.OracleObjectTypeTable,
				ObjectName:       fmt.Sprintf("%s_TABLES_%d_%d", schema, i+1, end),
				ObjectDefinition: ddlContent.String(), // Pass actual DDL
				PromptText:       entityPrompt.PromptText,
			}

			// Perform conversion
			startTime := time.Now()
			convertedCode, prompts, convertErr := aiHelper.Convert(ctx, convertItem)
			duration := time.Since(startTime).Seconds()

			// Log entity conversion progress
			entityLog := &objectparser.OracleToJavaLog{
				ChannelId: taskId,
				TaskId:    taskId,
				LogLevel:  "info",
				LogMessage: fmt.Sprintf("Converting %d tables to %s entities - Schema: %s, Tables: %s (batch %d-%d)",
					len(batchTables), frameworkType, schema, strings.Join(tableNames, ", "), i+1, end),
			}
			_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, entityLog)

			// Create result record
			result := &objectparser.OracleToJavaResult{
				TaskId:          taskId,
				UUID:            fmt.Sprintf("ENTITY_%s_%s_%d_%d", frameworkType, schema, i+1, end),
				SchemaName:      schema,
				PackageName:     fmt.Sprintf("%s.%s", frameworkType, strings.ToLower(schema)),
				ObjectName:      fmt.Sprintf("%s_Tables_%d_to_%d", schema, i+1, end),
				ObjectType:      constants.OracleObjectTypeTable,
				CodeFileName:    fmt.Sprintf("%s/%s_entities_%d_%d.java", strings.ToLower(schema), frameworkType, i+1, end),
				ConvertedCode:   convertedCode,
				ConvertDuration: duration,
				ConvertSQL:      ddlContent.String(),
				ConvertPrompts:  strings.Join(prompts, "@,@"),
				ConvertTime:     startTime,
				IsMultiFile:     true, // Entity generation typically produces multiple files
			}

			if convertErr != nil {
				log.Errorf("Failed to convert tables to entities, taskId:%d, schema:%s, batch:%d-%d, err: %v",
					taskId, schema, i+1, end, convertErr)
				result.ConvertStatus = "FAILED"
				result.ConvertErrMessage = convertErr.Error()
			} else {
				result.ConvertStatus = "SUCCESS"
				log.Infof("Successfully converted %d tables from schema %s, batch %d-%d for taskId:%d",
					len(batchTables), schema, i+1, end, taskId)
			}

			results = append(results, result)
		}
	}

	return results, nil
}

// TestAIConnect tests connectivity to AI services.
func (s *conversionService) TestAIConnect(ctx context.Context, req *message.TestAIConnectReq) (*message.TestAIConnectResp, error) {
	log.Infof("TestAIConnect channelId:%d, taskId:%d, templateId:%d", req.ChannelId, req.TaskId, req.TemplateId)

	var taskId int
	var llmConfig structs.LLMProviderAPIConfig

	if req.ChannelId != 0 && req.TaskId != 0 {
		// Step 1: Get task information
		taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, int(req.TaskId))
		if getTaskErr != nil {
			log.Errorf("TestAIConnect failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
			return &message.TestAIConnectResp{ErrMessage: getTaskErr.Error()}, getTaskErr
		}

		// Step 2: Build object parser parameters
		params, buildErr := core.BuildObjectParserParam(ctx, taskInfo)
		if buildErr != nil {
			log.Errorf("TestAIConnect failed, taskId:%d, err: %v", req.TaskId, buildErr)
			return &message.TestAIConnectResp{ErrMessage: buildErr.Error()}, buildErr
		}
		taskId = taskInfo.TaskID
		llmConfig = params.LLMProviderAPIConfig
	} else {
		// Step 1: Get template parameter details
		defaultTaskCfgs, getDetailErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, int(req.TemplateId))
		if getDetailErr != nil {
			log.Errorf("TestAIConnect failed to get template params, templateId:%d, err: %v", req.TemplateId, getDetailErr)
			return &message.TestAIConnectResp{ErrMessage: fmt.Sprintf("failed to get template parameters: %v", getDetailErr)}, getDetailErr
		}

		// Step 2: Build parameter map from template defaults
		taskParamMap := make(map[string]string)
		for _, taskCfg := range defaultTaskCfgs {
			taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
		}

		// Step 3: Build LLM configuration from template parameters
		llmConfig = structs.LLMProviderAPIConfig{}

		// Set source/provider
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMSource]; ok {
			if strings.TrimSpace(value) == "" {
				value = "openai"
			}
			llmConfig.Source = value
		} else if value, ok := taskParamMap[constants.ParamsObjectParserOpenAISource]; ok {
			if strings.TrimSpace(value) == "" {
				value = "openai"
			}
			llmConfig.Source = value
		} else {
			llmConfig.Source = "openai"
		}

		// Set model
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMModel]; ok {
			if strings.TrimSpace(value) == "" {
				value = "gpt-4o"
			}
			llmConfig.Model = value
		} else if value, ok := taskParamMap[constants.ParamsObjectParserOpenAIModel]; ok {
			if strings.TrimSpace(value) == "" {
				value = "gpt-4o"
			}
			llmConfig.Model = value
		} else {
			llmConfig.Model = "gpt-4o"
		}

		// Set endpoint
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMEndpoint]; ok {
			llmConfig.Endpoint = value
		} else if value, ok := taskParamMap[constants.ParamsObjectParserOpenAIEndpoint]; ok {
			llmConfig.Endpoint = value
		}

		// Set API key with decryption support
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMAPIKey]; ok {
			matched, _ := regexp.MatchString(`(?i)^ENC_`, value)
			if matched {
				decryptKey := crypto.AesDefaultDecrypt(value[len("ENC_"):], crypto.DefaultKey)
				llmConfig.APIKey = decryptKey
			} else {
				llmConfig.APIKey = value
			}
		} else if value, ok := taskParamMap[constants.ParamsObjectParserOpenAIKey]; ok {
			matched, _ := regexp.MatchString(`(?i)^ENC_`, value)
			if matched {
				decryptKey := crypto.AesDefaultDecrypt(value[len("ENC_"):], crypto.DefaultKey)
				llmConfig.APIKey = decryptKey
			} else {
				llmConfig.APIKey = value
			}
		}

		if value, ok := taskParamMap[constants.ParamsObjectParserLLMMaxTokens]; ok {
			intVal, _ := parse.ParseInt(value)
			llmConfig.SetMaxTokens(intVal)
		}
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMMaxTokensName]; ok {
			llmConfig.SetMaxTokensName(value)
		}
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMTimeout]; ok {
			intVal, _ := parse.ParseInt(value)
			llmConfig.SetTimeout(intVal)
		}
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMTemperature]; ok {
			float32Val, _ := parse.ParseFloat32(value)
			llmConfig.SetTemperature(float32Val)
		}
		if value, ok := taskParamMap[constants.ParamsObjectParserLLMStream]; ok {
			llmConfig.Stream = parse.ParseBoolWithDefault(value, false)
		}

		taskId = 0 // Use 0 for template-based testing
	}

	// Step 3: Create AI helper and test connection, taskId can be 0
	aiHelper := ai.NewAIConvertorProxy(taskId, llmConfig)

	testResult, testErr := aiHelper.TestConnection(ctx)
	if testErr != nil {
		log.Errorf("TestAIConnect failed, taskId:%d, err: %v", req.TaskId, testErr)
		return &message.TestAIConnectResp{ErrMessage: testErr.Error()}, testErr
	}

	return &message.TestAIConnectResp{Success: true, SuccessMessage: testResult}, nil
}

// GetPLSQLToJavaResults retrieves all current PL/SQL to Java conversion results.
func (s *conversionService) GetPLSQLToJavaResults(ctx context.Context, req *message.GetPLSQLToJavaResultsReq) (*message.GetPLSQLToJavaResultsResp, *message.Page, error) {
	// Get ALL conversion results for the task (no pagination)
	results, _, getErr := s.objectParserRW.ListOracleToJavaResultByTaskId(ctx, int(req.TaskId), 1, 1024*1024*1024)
	if getErr != nil {
		log.Errorf("GetPLSQLToJavaResults failed, taskId:%d, err: %v", req.TaskId, getErr)
		return nil, nil, getErr
	}

	// Batch fetch all files for the task to improve performance
	allFiles, filesErr := s.objectParserRW.ListOracleToJavaFilesByTaskId(ctx, int(req.TaskId))
	if filesErr != nil {
		log.Warnf("Failed to batch fetch files, taskId:%d, err: %v", req.TaskId, filesErr)
		// Continue with individual fetching if batch fetch fails
		allFiles = nil
	}

	// Build response using message builder
	builder := core.NewMessageBuilder()

	// Convert all results to ConversionResult interface
	var conversionResults []core.ConversionResult
	for _, result := range results {
		conversionResults = append(conversionResults, &core.OracleToJavaResultWrapper{OracleToJavaResult: result})
	}

	// Create FileOrganizer and process ALL results with conflict resolution
	organizer := core.NewFileOrganizer()
	fileInfos, err := organizer.ProcessAllResultsWithPreloadedFiles(ctx, conversionResults, allFiles, nil, false)
	if err != nil {
		log.Errorf("ProcessAllResults failed, taskId:%d, err: %v", req.TaskId, err)
		// Fallback to original method if new method fails
		codeTree, _ := builder.BuildJavaCodeTree(ctx, results)
		return &message.GetPLSQLToJavaResultsResp{
			CodeTree: codeTree,
		}, nil, nil
	}

	// Build tree structure
	codeTree := organizer.BuildFileTree(fileInfos)

	return &message.GetPLSQLToJavaResultsResp{
		CodeTree: codeTree,
	}, nil, nil
}

// GetPLSQLToJavaHistoryResults retrieves all historical PL/SQL to Java conversion results.
func (s *conversionService) GetPLSQLToJavaHistoryResults(ctx context.Context, req *message.GetPLSQLToJavaHistoryResultsReq) (*message.GetPLSQLToJavaHistoryResultsResp, *message.Page, error) {
	// Get ALL historical conversion results for the task (no pagination)
	results, _, getErr := s.objectParserRW.ListOracleToJavaHistoryResultByTaskId(ctx, int(req.TaskId), 1, 1024*1024*1024)
	if getErr != nil {
		log.Errorf("GetPLSQLToJavaHistoryResults failed, taskId:%d, err: %v", req.TaskId, getErr)
		return nil, nil, getErr
	}

	// Batch fetch all history files for the task to improve performance
	allHistoryFiles, filesErr := s.objectParserRW.ListOracleToJavaHistoryFilesByTaskId(ctx, int(req.TaskId))
	if filesErr != nil {
		log.Warnf("Failed to batch fetch history files, taskId:%d, err: %v", req.TaskId, filesErr)
		// Continue with individual fetching if batch fetch fails
		allHistoryFiles = nil
	}

	// Build response using message builder
	builder := core.NewMessageBuilder()

	// Convert all history results to ConversionResult interface
	var conversionResults []core.ConversionResult
	for _, result := range results {
		conversionResults = append(conversionResults, &core.OracleToJavaHistoryResultWrapper{OracleToJavaHistoryResult: result})
	}

	// Create FileOrganizer and process ALL results with conflict resolution
	organizer := core.NewFileOrganizer()
	fileInfos, err := organizer.ProcessAllResultsWithPreloadedFiles(ctx, conversionResults, nil, allHistoryFiles, true)
	if err != nil {
		log.Errorf("ProcessAllResults failed, taskId:%d, err: %v", req.TaskId, err)
		// Fallback to original method if new method fails
		codeTree, _ := builder.BuildJavaCodeTreeForHistory(ctx, results)
		return &message.GetPLSQLToJavaHistoryResultsResp{
			JavaCodes: builder.BuildHistoryJavaCodes(results),
			CodeTree:  codeTree,
		}, nil, nil
	}

	// Build tree structure
	codeTree := organizer.BuildFileTree(fileInfos)

	return &message.GetPLSQLToJavaHistoryResultsResp{
		JavaCodes: builder.BuildHistoryJavaCodes(results),
		CodeTree:  codeTree,
	}, nil, nil
}

// GetPLSQLToJavaLogs retrieves conversion process logs.
func (s *conversionService) GetPLSQLToJavaLogs(ctx context.Context, req *message.GetPLSQLToJavaLogsReq) (*message.GetPLSQLToJavaLogsResp, error) {
	// Get conversion logs using ReaderWriter
	results, getErr := s.objectParserRW.ListOracleToJavaLogByTaskId(ctx, int(req.TaskId))
	if getErr != nil {
		log.Errorf("GetPLSQLToJavaLogs failed, taskId:%d, err: %v", req.TaskId, getErr)
		return nil, getErr
	}

	// Build response using message builder
	builder := core.NewMessageBuilder()

	return &message.GetPLSQLToJavaLogsResp{
		Logs: builder.BuildLogs(results),
	}, nil
}

// GetPLSQLToJavaSummary retrieves conversion process summary information.
func (s *conversionService) GetPLSQLToJavaSummary(ctx context.Context, req *message.GetPLSQLToJavaSummaryReq) (*message.GetPLSQLToJavaSummaryResp, error) {
	// Get conversion summary using ReaderWriter
	summary, getErr := s.objectParserRW.GetOracleToJavaSummary(ctx, int(req.TaskId))
	if getErr != nil {
		log.Warnf("GetPLSQLToJavaSummary failed, taskId:%d, err: %v", req.TaskId, getErr)
		return &message.GetPLSQLToJavaSummaryResp{}, nil
	}
	if summary == nil {
		return &message.GetPLSQLToJavaSummaryResp{
			ChannelId: int(req.ChannelId),
			TaskId:    int(req.TaskId),
		}, nil
	}

	return &message.GetPLSQLToJavaSummaryResp{
		ID:               summary.ID,
		ChannelId:        summary.ChannelId,
		TaskId:           summary.TaskId,
		TotalObjectNum:   summary.TotalObjectNum,
		SuccessObjectNum: summary.SuccessObjectNum,
		FailedObjectNum:  summary.FailedObjectNum,
	}, nil
}

// DownloadJavaCodes downloads generated Java code files with proper directory structure.
func (s *conversionService) DownloadJavaCodes(ctx context.Context, req *message.DownloadJavaCodesReq) (*message.DownloadJavaCodesResp, error) {
	log.Infof("DownloadJavaCodes channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	// Step 1: Retrieve all conversion results for the task
	results, _, getErr := s.objectParserRW.ListOracleToJavaResultByTaskId(ctx, int(req.TaskId), 1, 1024*1024*1024)
	if getErr != nil {
		log.Errorf("DownloadJavaCodes failed, taskId:%d, err: %v", req.TaskId, getErr)
		return nil, getErr
	}

	// Step 2: Batch fetch all files for the task to improve performance
	allFiles, filesErr := s.objectParserRW.ListOracleToJavaFilesByTaskId(ctx, int(req.TaskId))
	if filesErr != nil {
		log.Warnf("Failed to batch fetch files, taskId:%d, err: %v", req.TaskId, filesErr)
		// Continue with individual fetching if batch fetch fails
		allFiles = nil
	}

	// Step 3: Create working directory for file generation
	nowTime := time.Now()
	workingDir := path.Join(config.GetGlobalConfig().DataDir, "javaCodes", fmt.Sprintf("task_%d_%d", req.TaskId, nowTime.Unix()))
	zipFileName := path.Join(workingDir, fmt.Sprintf("java_codes_%d_%d.zip", req.TaskId, nowTime.Unix()))
	log.Infof("DownloadJavaCodes, taskId:%d, mkdir workingDir:%s", req.TaskId, workingDir)

	mkdirErr := fileutil.CreateIfNotExist(workingDir)
	if mkdirErr != nil {
		log.Errorf("DownloadJavaCodes failed, mkdir dir failed, taskId:%d, dir:%s, err: %v", req.TaskId, workingDir, mkdirErr)
		return nil, mkdirErr
	}

	// Step 4: Convert results to ConversionResult interface and use FileOrganizer
	var conversionResults []core.ConversionResult
	for _, result := range results {
		conversionResults = append(conversionResults, &core.OracleToJavaResultWrapper{OracleToJavaResult: result})
	}

	// Use FileOrganizer to process files with conflict resolution
	organizer := core.NewFileOrganizer()
	fileInfos, processErr := organizer.ProcessAllResultsWithPreloadedFiles(ctx, conversionResults, allFiles, nil, false)
	if processErr != nil {
		log.Errorf("ProcessAllResults failed, taskId:%d, err: %v", req.TaskId, processErr)
		return nil, processErr
	}

	// Step 5: Write files to disk
	var javaCodePaths []string
	for _, fileInfo := range fileInfos {
		javaCodePaths = append(javaCodePaths, fileInfo.FilePath)

		log.Infof("DownloadJavaCodes, taskId:%d, create file:%s", req.TaskId, fileInfo.FilePath)

		writeErr := s.writeFileWithDirectory(workingDir, fileInfo.FilePath, fileInfo.FileContent)
		if writeErr != nil {
			log.Errorf("DownloadJavaCodes failed, create file failed, taskId:%d, fileName:%s, err: %v", req.TaskId, fileInfo.FilePath, writeErr)
			// Continue processing other files
		}
	}

	// Step 6: Package files into ZIP archive
	if len(javaCodePaths) == 0 {
		log.Warnf("DownloadJavaCodes no files to zip, taskId:%d", req.TaskId)
		return &message.DownloadJavaCodesResp{
			FilePath: "",
		}, nil
	}

	// 从ZIP文件名提取根文件夹名
	rootFolderName := strings.TrimSuffix(filepath.Base(zipFileName), ".zip")
	zipErr := fileutil.ZipFiles(zipFileName, workingDir, javaCodePaths, rootFolderName)
	if zipErr != nil {
		log.Errorf("DownloadJavaCodes failed, zip files failed, taskId:%d, err: %v", req.TaskId, zipErr)
		return nil, zipErr
	}

	log.Infof("DownloadJavaCodes success, taskId:%d, zipFile:%s, fileCount:%d", req.TaskId, zipFileName, len(javaCodePaths))
	return &message.DownloadJavaCodesResp{
		FilePath: zipFileName,
	}, nil
}

// DownloadHistoryJavaCodes downloads historical Java code files with proper directory structure.
func (s *conversionService) DownloadHistoryJavaCodes(ctx context.Context, req *message.DownloadHistoryJavaCodesReq) (*message.DownloadJavaCodesResp, error) {
	log.Infof("DownloadHistoryJavaCodes channelId:%d, taskId:%d", req.ChannelId, req.TaskId)
	// Step 1: Retrieve all historical conversion results for the task
	results, _, getErr := s.objectParserRW.ListOracleToJavaHistoryResultByTaskId(ctx, int(req.TaskId), 1, 1024*1024*1024)
	if getErr != nil {
		log.Errorf("DownloadHistoryJavaCodes failed, taskId:%d, err: %v", req.TaskId, getErr)
		return nil, getErr
	}

	// Step 2: Batch fetch all history files for the task to improve performance
	allHistoryFiles, filesErr := s.objectParserRW.ListOracleToJavaHistoryFilesByTaskId(ctx, int(req.TaskId))
	if filesErr != nil {
		log.Warnf("Failed to batch fetch history files, taskId:%d, err: %v", req.TaskId, filesErr)
		// Continue with individual fetching if batch fetch fails
		allHistoryFiles = nil
	}

	// Step 3: Create working directory for file generation
	nowTime := time.Now()
	workingDir := path.Join(config.GetGlobalConfig().DataDir, "javaCodes", fmt.Sprintf("task_%d_%d", req.TaskId, nowTime.Unix()))
	zipFileName := path.Join(workingDir, fmt.Sprintf("history_java_codes_%d_%d.zip", req.TaskId, nowTime.Unix()))
	log.Infof("DownloadHistoryJavaCodes, taskId:%d, mkdir workingDir:%s", req.TaskId, workingDir)

	mkdirErr := fileutil.CreateIfNotExist(workingDir)
	if mkdirErr != nil {
		log.Errorf("DownloadHistoryJavaCodes failed, mkdir dir failed, taskId:%d, dir:%s, err: %v", req.TaskId, workingDir, mkdirErr)
		return nil, mkdirErr
	}

	// Step 4: Convert results to ConversionResult interface and use FileOrganizer
	var conversionResults []core.ConversionResult
	for _, result := range results {
		conversionResults = append(conversionResults, &core.OracleToJavaHistoryResultWrapper{OracleToJavaHistoryResult: result})
	}

	// Use FileOrganizer to process files with conflict resolution
	organizer := core.NewFileOrganizer()
	fileInfos, processErr := organizer.ProcessAllResultsWithPreloadedFiles(ctx, conversionResults, nil, allHistoryFiles, true)
	if processErr != nil {
		log.Errorf("ProcessAllResults failed, taskId:%d, err: %v", req.TaskId, processErr)
		return nil, processErr
	}

	// Step 5: Write files to disk
	var javaCodePaths []string
	for _, fileInfo := range fileInfos {
		javaCodePaths = append(javaCodePaths, fileInfo.FilePath)

		log.Infof("DownloadHistoryJavaCodes, taskId:%d, create file:%s", req.TaskId, fileInfo.FilePath)

		writeErr := s.writeFileWithDirectory(workingDir, fileInfo.FilePath, fileInfo.FileContent)
		if writeErr != nil {
			log.Errorf("DownloadHistoryJavaCodes failed, create file failed, taskId:%d, fileName:%s, err: %v", req.TaskId, fileInfo.FilePath, writeErr)
			// Continue processing other files
		}
	}

	// Step 6: Package files into ZIP archive
	if len(javaCodePaths) == 0 {
		log.Warnf("DownloadHistoryJavaCodes no files to zip, taskId:%d", req.TaskId)
		return &message.DownloadJavaCodesResp{
			FilePath: "",
		}, nil
	}

	// 从ZIP文件名提取根文件夹名
	rootFolderName := strings.TrimSuffix(filepath.Base(zipFileName), ".zip")
	zipErr := fileutil.ZipFiles(zipFileName, workingDir, javaCodePaths, rootFolderName)
	if zipErr != nil {
		log.Errorf("DownloadHistoryJavaCodes failed, zip files failed, taskId:%d, err: %v", req.TaskId, zipErr)
		return nil, zipErr
	}

	log.Infof("DownloadHistoryJavaCodes success, taskId:%d, zipFile:%s, fileCount:%d", req.TaskId, zipFileName, len(javaCodePaths))
	return &message.DownloadJavaCodesResp{
		FilePath: zipFileName,
	}, nil
}

// ConvertPLSQLToJava is the refactored version using layered service approach
func (s *conversionService) ConvertPLSQLToJava(ctx context.Context, req *message.ConvertPLSQLToJavaReq) (*message.ConvertPLSQLToJavaResp, error) {
	log.Infof("ConvertPLSQLToJava started - channelId:%d, taskId:%d, objectCount:%d",
		req.ChannelId, req.TaskId, len(req.ConvertObjects))

	// Log task start
	startLog := &objectparser.OracleToJavaLog{
		ChannelId:  int(req.ChannelId),
		TaskId:     int(req.TaskId),
		LogLevel:   "info",
		LogMessage: fmt.Sprintf("Starting PL/SQL to Java conversion task - channelId:%d, taskId:%d, objectCount:%d",
			req.ChannelId, req.TaskId, len(req.ConvertObjects)),
	}
	_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, startLog)

	// Validate input
	if len(req.ConvertObjects) <= 0 {
		log.Infof("ConvertPLSQLToJava completed - no objects to convert, taskId:%d", req.TaskId)
		return &message.ConvertPLSQLToJavaResp{}, nil
	}

	// 1. Preparation phase
	prep, err := s.prepareConversion(ctx, req)
	if err != nil {
		log.Errorf("ConvertPLSQLToJava failed during preparation - taskId:%d, err: %v", req.TaskId, err)
		// Log preparation failure
		errorLog := &objectparser.OracleToJavaLog{
			ChannelId:  int(req.ChannelId),
			TaskId:     int(req.TaskId),
			LogLevel:   "error",
			LogMessage: fmt.Sprintf("Conversion task failed during preparation phase - error: %v", err),
		}
		_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, errorLog)
		return nil, fmt.Errorf("preparation failed: %w", err)
	}

	// 2. Entity generation (if needed)
	frameworks := prep.PromptStrategy.GetAllFrameworks()
	log.Infof("Entity generation check - taskId: %d, frameworks: %v, analyzeDetails: %d",
		req.TaskId, frameworks, len(prep.Metadata.AnalyzeDetails))

	if len(frameworks) > 0 && len(prep.Metadata.AnalyzeDetails) > 0 {
		// Check if any framework needs entity generation
		needsEntityGen := false
		for _, framework := range frameworks {
			if prep.PromptStrategy.ShouldUseEntityPrompt(framework) {
				needsEntityGen = true
				break
			}
		}

		if needsEntityGen {
			log.Infof("Entity generation needed for frameworks: %v", frameworks)
			if err := s.generateEntitiesForConversion(ctx, prep); err != nil {
				log.Errorf("Entity generation failed but continuing: %v", err)
				// Continue with main flow, entity generation failure should not block main conversion
			}
		} else {
			log.Infof("Skipping separate entity generation - will be handled by trigger prompts (trigger templates include entity generation) for taskId: %d", req.TaskId)
		}
	} else {
		if len(frameworks) == 0 {
			log.Infof("Skipping entity generation - no frameworks detected for taskId: %d", req.TaskId)
		}
		if len(prep.Metadata.AnalyzeDetails) == 0 {
			log.Infof("Skipping entity generation - no analyze details found for taskId: %d", req.TaskId)
		}
	}

	// 3. Execute main conversion
	if err := s.executeMainConversion(ctx, prep); err != nil {
		log.Errorf("ConvertPLSQLToJava failed during main conversion - taskId:%d, err: %v", req.TaskId, err)
		// Log main conversion failure
		errorLog := &objectparser.OracleToJavaLog{
			ChannelId:  int(req.ChannelId),
			TaskId:     int(req.TaskId),
			LogLevel:   "error",
			LogMessage: fmt.Sprintf("Conversion task failed during main conversion phase - error: %v", err),
		}
		_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, errorLog)
		return nil, fmt.Errorf("main conversion failed: %w", err)
	}

	log.Infof("ConvertPLSQLToJava completed successfully - taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
	return &message.ConvertPLSQLToJavaResp{}, nil
}

// prepareConversion prepares all necessary data for the conversion process
func (s *conversionService) prepareConversion(ctx context.Context, req *message.ConvertPLSQLToJavaReq) (*ConversionPreparation, error) {
	log.Infof("Preparing conversion for taskId: %d, objectCount: %d", req.TaskId, len(req.ConvertObjects))

	// Load task information
	taskBundle, err := s.loadTaskInfo(ctx, int64(req.TaskId))
	if err != nil {
		log.Errorf("Failed to load task info for conversion preparation, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("load task info: %w", err)
	}

	// Validate license
	licenseInfo, err := s.validateLicenseForConversion(ctx, taskBundle.TaskInfo, req.ConvertObjects)
	if err != nil {
		log.Errorf("Failed to validate license for conversion, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("validate license: %w", err)
	}

	// Create conversion helper
	convertHelper := core.NewConvertHelper(taskBundle.TaskInfo.TaskID,
		taskBundle.Params.LLMProviderAPIConfig, licenseInfo.Manager)
	if err := convertHelper.ResetMetadata(ctx); err != nil {
		log.Errorf("Failed to reset metadata for conversion, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("reset metadata: %w", err)
	}

	// Load metadata
	metadata, err := s.loadConversionMetadata(ctx, taskBundle.TaskInfo, req)
	if err != nil {
		log.Errorf("Failed to load conversion metadata, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("load metadata: %w", err)
	}

	// Load all prompts into cache
	promptManager := core.NewPromptManager()
	if err := promptManager.InitAllPrompts(metadata.Prompts); err != nil {
		log.Errorf("Failed to init prompts into cache, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("init prompts: %w", err)
	}

	// Initialize prompt selector
	promptSelector := core.NewPromptSelector(promptManager)

	// Get default prompts
	defaultPrompt, defaultTriggerPrompt, err := promptSelector.GetDefaultPrompts()
	if err != nil {
		log.Errorf("Failed to build default prompts, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("build default prompts: %w", err)
	}
	log.Infof("=== Default Prompts Configuration for taskId %d ===", req.TaskId)
	log.Infof("Default CODE prompt: ID=%d, Title=%s, Category=%s, Framework=%s, Language=%s, IsDefault=%v",
		defaultPrompt.ID, defaultPrompt.PromptTitle, defaultPrompt.PromptCategory,
		defaultPrompt.TargetFramework, defaultPrompt.TargetLanguage, defaultPrompt.IsDefault)
	log.Infof("Default TRIGGER prompt: ID=%d, Title=%s, Category=%s, Framework=%s, Language=%s, IsDefault=%v",
		defaultTriggerPrompt.ID, defaultTriggerPrompt.PromptTitle, defaultTriggerPrompt.PromptCategory,
		defaultTriggerPrompt.TargetFramework, defaultTriggerPrompt.TargetLanguage, defaultTriggerPrompt.IsDefault)
	log.Infof("=== End Default Prompts Configuration for taskId %d ===", req.TaskId)

	// Build object-prompt mapping
	objectPromptMap, err := promptSelector.BuildObjectPromptMap(
		ctx,
		metadata.Objects,
		metadata.PromptRelations,
		defaultPrompt,
		defaultTriggerPrompt,
	)
	if err != nil {
		log.Errorf("Failed to build object prompt map, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("build object prompt map: %w", err)
	}

	// Create unified prompt strategy
	promptStrategy := core.NewConversionPromptStrategy(int(req.TaskId), defaultPrompt, defaultTriggerPrompt)
	promptStrategy.SetObjectPrompts(objectPromptMap)

	// Determine entity generation needs
	entityPrompts, err := promptSelector.DetermineEntityGenerationNeeds(ctx, objectPromptMap)
	if err != nil {
		log.Errorf("Failed to determine entity generation needs, taskId: %d, err: %v", req.TaskId, err)
		return nil, fmt.Errorf("determine entity needs: %w", err)
	}
	promptStrategy.SetEntityPrompts(entityPrompts)

	// Detect if objects contain triggers
	hasTriggers := s.detectTriggers(metadata.Objects)
	promptStrategy.HasTriggers = hasTriggers
	triggerCount := s.countTriggerObjects(metadata.Objects)
	log.Infof("=== TRIGGER Object Analysis for taskId %d ===", req.TaskId)
	log.Infof("Total TRIGGER objects found: %d", triggerCount)
	log.Infof("Trigger detection result - hasTriggers: %v", hasTriggers)
	log.Infof("=== End TRIGGER Object Analysis for taskId %d ===", req.TaskId)

	// Determine trigger prompt needs if triggers exist
	if hasTriggers {
		triggerPrompts, err := promptSelector.DetermineTriggerPromptNeeds(ctx, objectPromptMap)
		if err != nil {
			log.Warnf("Failed to determine trigger prompt needs, taskId: %d, err: %v", req.TaskId, err)
		} else {
			promptStrategy.SetTriggerPrompts(triggerPrompts)
			log.Infof("Successfully set trigger prompts for taskId: %d, triggerPromptCount: %d", req.TaskId, len(triggerPrompts))
		}
	}

	// Detect frameworks and get primary framework
	//promptStrategy.DetectFrameworks()

	// Log comprehensive prompt strategy statistics
	promptStrategy.LogStrategyStatistics(int(req.TaskId))

	// Initialize conversion summary with total object count (filtered objects only)
	totalObjects := len(metadata.Objects) // This is already filtered (excludes PACKAGE and PACKAGE BODY)
	summary := &objectparser.OracleToJavaSummary{
		ChannelId:      taskBundle.TaskInfo.ChannelId,
		TaskId:         taskBundle.TaskInfo.TaskID,
		TotalObjectNum: totalObjects,
	}

	// Save the initial summary to database
	savedSummary, summaryErr := models.GetObjectParserWriter().SaveOracleToJavaSummary(ctx, summary)
	if summaryErr != nil {
		log.Errorf("Failed to save initial conversion summary, taskId: %d, err: %v", req.TaskId, summaryErr)
		return nil, fmt.Errorf("save initial summary: %w", summaryErr)
	}

	log.Infof("Initialized conversion summary early for taskId: %d, totalObjects: %d, summaryId: %d",
		req.TaskId, totalObjects, savedSummary.ID)

	// Validate the prompt strategy
	if err := promptStrategy.ValidateStrategy(); err != nil {
		log.Warnf("Prompt strategy validation warning for taskId %d: %v", req.TaskId, err)
	}

	log.Infof("Successfully prepared conversion for taskId: %d, channelId: %d, frameworks: %v, objects: %d, hasTriggers: %v, triggerPrompts: %d",
		req.TaskId, taskBundle.TaskInfo.ChannelId, promptStrategy.GetAllFrameworks(), len(metadata.Objects), hasTriggers, len(promptStrategy.TriggerPrompts))

	return &ConversionPreparation{
		Request:        req,
		TaskInfo:       taskBundle.TaskInfo,
		ChannelInfo:    taskBundle.ChannelInfo,
		Params:         taskBundle.Params,
		LicenseManager: licenseInfo.Manager,
		ConvertHelper:  convertHelper,
		Metadata:       metadata,
		PromptSelector: promptSelector,
		Summary:        savedSummary,
		PromptStrategy: promptStrategy,
	}, nil
}

// generateEntitiesForConversion generates entity classes from table definitions
func (s *conversionService) generateEntitiesForConversion(ctx context.Context, prep *ConversionPreparation) error {
	// Get all frameworks that need entity generation
	frameworks := prep.PromptStrategy.GetAllFrameworks()
	if len(frameworks) == 0 {
		log.Infof("No frameworks detected, skipping entity generation for taskId: %d", prep.TaskInfo.TaskID)
		return nil
	}

	// Check which frameworks actually need entity generation
	entityFrameworks := []string{}
	for _, framework := range frameworks {
		if prep.PromptStrategy.ShouldUseEntityPrompt(framework) {
			entityFrameworks = append(entityFrameworks, framework)
		}
	}

	if len(entityFrameworks) == 0 {
		log.Infof("No frameworks require entity generation for taskId: %d", prep.TaskInfo.TaskID)
		return nil
	}

	log.Infof("Generating entities for frameworks: %v, taskId: %d", entityFrameworks, prep.TaskInfo.TaskID)

	// Log detailed entity prompt information
	for _, framework := range entityFrameworks {
		prompt := prep.PromptStrategy.GetEntityPromptForFramework(framework)
		if prompt != nil {
			log.Infof("Entity prompt for framework %s - ID: %d, Title: %s, Category: %s",
				framework, prompt.ID, prompt.PromptTitle, prompt.PromptCategory)
		}
	}

	// Extract unique tables
	uniqueTables := s.extractUniqueTablesFromAnalysis(ctx, prep.Metadata.AnalyzeDetails)
	log.Infof("Extracted %d unique tables from analyze details for taskId: %d", len(uniqueTables), prep.TaskInfo.TaskID)
	if len(uniqueTables) == 0 {
		log.Infof("No unique tables found for entity generation, taskId: %d", prep.TaskInfo.TaskID)
		return nil
	}

	// Get source schemas
	sourceSchemas, err := s.getSourceSchemas(ctx, prep.TaskInfo)
	if err != nil {
		log.Errorf("Failed to get target schemas for entity generation, taskId: %d, err: %v", prep.TaskInfo.TaskID, err)
		return fmt.Errorf("get target schemas: %w", err)
	}

	// Create database connection
	dbConns, err := migration.SetUpOracleDatabaseConns(ctx, prep.ChannelInfo, prep.TaskInfo.TaskID)
	if err != nil {
		log.Errorf("Failed to setup database connections for entity generation, taskId: %d, err: %v", prep.TaskInfo.TaskID, err)
		return fmt.Errorf("setup db connections: %w", err)
	}
	defer dbConns.Close()

	// Log the action before connecting to Oracle to fetch DDLs
	log.Infof("Starting to connect Oracle and fetch table DDLs for entity generation - taskId: %d, uniqueTables: %d, schemas: %v",
		prep.TaskInfo.TaskID, len(uniqueTables), sourceSchemas)

	// Log entity conversion progress
	entityLog := &objectparser.OracleToJavaLog{
		ChannelId: prep.TaskInfo.ChannelId,
		TaskId:    prep.TaskInfo.TaskID,
		LogLevel:  "info",
		LogMessage: fmt.Sprintf("Fetching table DDLs from Oracle for entity generation - schemas: %v, tableNum: %d",
			sourceSchemas, len(uniqueTables)),
	}
	_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, entityLog)

	// Group by schema and fetch DDLs
	schemaTableDefMap, err := s.groupAndFetchTableDDLs(ctx, dbConns, uniqueTables, sourceSchemas)
	if err != nil {
		log.Errorf("Failed to fetch table DDLs for entity generation, taskId: %d, err: %v", prep.TaskInfo.TaskID, err)
		return fmt.Errorf("fetch table DDLs: %w", err)
	}

	if len(schemaTableDefMap) == 0 {
		log.Infof("No table DDLs found for entity generation after filtering, taskId: %d", prep.TaskInfo.TaskID)
		return nil
	}

	// Execute entity conversion for each framework
	var allEntityResults []*objectparser.OracleToJavaResult
	for _, framework := range entityFrameworks {
		entityPrompt := prep.PromptStrategy.GetEntityPromptForFramework(framework)
		if entityPrompt == nil {
			log.Warnf("No entity prompt found for framework %s, skipping", framework)
			continue
		}

		log.Infof("Generating entities for framework: %s using prompt: %s", framework, entityPrompt.PromptTitle)

		// Execute entity conversion with specific prompt
		entityResults, err := s.convertTablesToEntitiesWithPrompt(ctx, schemaTableDefMap, framework,
			entityPrompt, prep.TaskInfo.TaskID, prep.Params.LLMProviderAPIConfig)
		if err != nil {
			log.Errorf("Failed to convert tables to entities for framework %s, taskId: %d, err: %v",
				framework, prep.TaskInfo.TaskID, err)
			// Continue with other frameworks
			continue
		}

		allEntityResults = append(allEntityResults, entityResults...)
	}

	// Save all entity results
	if len(allEntityResults) > 0 {
		err = s.saveEntityResults(ctx, allEntityResults, prep.TaskInfo)
		if err != nil {
			log.Errorf("Failed to save entity results, taskId: %d, err: %v", prep.TaskInfo.TaskID, err)
			return err
		}
	}

	log.Infof("Successfully completed entity generation for taskId: %d, totalEntityCount: %d",
		prep.TaskInfo.TaskID, len(allEntityResults))
	return nil
}

// executeMainConversion executes the main PL/SQL to Java conversion
func (s *conversionService) executeMainConversion(ctx context.Context, prep *ConversionPreparation) error {
	log.Infof("Starting main conversion execution for taskId: %d", prep.TaskInfo.TaskID)

	// Build conversion context
	conversionContext, err := s.buildConversionContext(ctx, prep)
	if err != nil {
		log.Errorf("Failed to build conversion context, taskId: %d, err: %v", prep.TaskInfo.TaskID, err)
		return fmt.Errorf("build context: %w", err)
	}
	log.Infof("Built conversion context for taskId: %d, totalItems: %d", prep.TaskInfo.TaskID, len(conversionContext.ConvertItems))

	// Use the summary that was already initialized during preparation
	summary := prep.Summary
	log.Infof("Using pre-initialized summary for taskId: %d, totalItems: %d", prep.TaskInfo.TaskID, summary.TotalObjectNum)

	// Execute batch conversion
	log.Infof("Starting batch conversion for taskId: %d, totalItems: %d", prep.TaskInfo.TaskID, len(conversionContext.ConvertItems))
	for idx, item := range conversionContext.ConvertItems {
		if err := s.processSingleConversion(ctx, prep, item, idx, summary); err != nil {
			log.Errorf("Conversion failed for item %d/%d, taskId: %d, object: %s, err: %v",
				idx+1, len(conversionContext.ConvertItems), prep.TaskInfo.TaskID, item.GetDisplayName(), err)
			// Continue processing other items
		}
	}

	log.Infof("Completed main conversion execution for taskId: %d, success: %d, failed: %d, total: %d",
		prep.TaskInfo.TaskID, summary.SuccessObjectNum, summary.FailedObjectNum, summary.TotalObjectNum)
	return nil
}

// Bottom layer methods - atomic operations

// loadTaskInfo loads task and related information
func (s *conversionService) loadTaskInfo(ctx context.Context, taskId int64) (*TaskBundle, error) {
	log.Infof("Loading task info for taskId: %d", taskId)

	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, int(taskId))
	if err != nil {
		log.Errorf("Failed to get task info, taskId: %d, err: %v", taskId, err)
		return nil, fmt.Errorf("get task: %w", err)
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("Failed to get channel info, taskId: %d, channelId: %d, err: %v", taskId, taskInfo.ChannelId, err)
		return nil, fmt.Errorf("get channel: %w", err)
	}

	params, err := core.BuildObjectParserParam(ctx, taskInfo)
	if err != nil {
		log.Errorf("Failed to build object parser params, taskId: %d, err: %v", taskId, err)
		return nil, fmt.Errorf("build params: %w", err)
	}

	log.Infof("Successfully loaded task info for taskId: %d, channelId: %d, taskType: %d",
		taskId, taskInfo.ChannelId, taskInfo.TaskType)

	return &TaskBundle{
		TaskInfo:    taskInfo,
		ChannelInfo: channelInfo,
		Params:      params,
	}, nil
}

// validateLicenseForConversion validates license and counts objects
func (s *conversionService) validateLicenseForConversion(ctx context.Context, taskInfo *task.Task, convertObjects []message.ConvertObject) (*LicenseInfo, error) {
	log.Infof("Validating license for conversion, taskId: %d, objectCount: %d", taskInfo.TaskID, len(convertObjects))

	conf := config.GetGlobalConfig()
	manager, err := license.NewLicenseManager(conf.License, license.Config{
		EncryptionKey: license.EncryptionKey,
		HMACKey:       license.HMACKey,
	})
	if err != nil {
		log.Errorf("Failed to initialize license manager, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("init license manager: %w", err)
	}

	// Get objects to count by type
	objectUUIDs := make([]string, 0, len(convertObjects))
	for _, obj := range convertObjects {
		objectUUIDs = append(objectUUIDs, obj.ObjectUUID)
	}

	toBeConvertObjects, err := models.GetObjectParserWriter().ListOracleDependencyByUUIDs(ctx, taskInfo.TaskID, objectUUIDs)
	if err != nil {
		log.Errorf("Failed to list objects by UUIDs, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("list objects: %w", err)
	}
	log.Infof("Retrieved %d objects for license validation, taskId: %d", len(toBeConvertObjects), taskInfo.TaskID)

	// Filter out PACKAGE and PACKAGE BODY objects and count remaining objects by type
	objectCounts := make(map[string]uint)
	filteredCount := 0
	for _, obj := range toBeConvertObjects {
		switch obj.Type {
		case "PROCEDURE":
			objectCounts["PROCEDURE"]++
			filteredCount++
		case "FUNCTION":
			objectCounts["FUNCTION"]++
			filteredCount++
		case "PACKAGE", "PACKAGE BODY":
			// Skip packages - they are excluded from conversion
			log.Debugf("Skipping %s object %s for license validation", obj.Type, obj.Name)
		}
	}
	log.Infof("License validation - original objects: %d, after filtering (excluding PACKAGE/PACKAGE BODY): %d, taskId: %d",
		len(toBeConvertObjects), filteredCount, taskInfo.TaskID)

	// Validate license
	licenseRequest := license.OperationRequest{
		OpProcedureNum: objectCounts["PROCEDURE"],
		OpFunctionNum:  objectCounts["FUNCTION"],
		OpPackageNum:   0, // Packages are excluded from conversion
	}

	log.Infof("License validation request - taskId: %d, procedures: %d, functions: %d, packages: %d",
		taskInfo.TaskID, objectCounts["PROCEDURE"], objectCounts["FUNCTION"], 0)

	if err := manager.ValidateRequestNum(ctx, licenseRequest); err != nil {
		log.Errorf("License validation failed, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("license validation: %w", err)
	}

	log.Infof("License validation successful for taskId: %d", taskInfo.TaskID)

	return &LicenseInfo{
		Manager:      manager,
		ObjectCounts: objectCounts,
	}, nil
}

// loadConversionMetadata loads all metadata needed for conversion
func (s *conversionService) loadConversionMetadata(ctx context.Context, taskInfo *task.Task, req *message.ConvertPLSQLToJavaReq) (*ConversionMetadata, error) {
	log.Infof("Loading conversion metadata for taskId: %d, objectCount: %d", taskInfo.TaskID, len(req.GetObjectUUIDs()))

	// Get objects
	objects, err := models.GetObjectParserWriter().ListOracleDependencyByUUIDs(ctx, taskInfo.TaskID, req.GetObjectUUIDs())
	if err != nil {
		log.Errorf("Failed to list objects by UUIDs, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("list objects: %w", err)
	}
	log.Infof("Loaded %d objects for taskId: %d", len(objects), taskInfo.TaskID)

	// Filter out PACKAGE and PACKAGE BODY objects
	originalCount := len(objects)
	var filteredObjects []*objectparser.OracleDependency
	var filteredUUIDs []string

	for _, obj := range objects {
		if obj.Type != "PACKAGE" && obj.Type != "PACKAGE BODY" {
			filteredObjects = append(filteredObjects, obj)
			filteredUUIDs = append(filteredUUIDs, obj.UUID)
		}
	}
	objects = filteredObjects

	log.Infof("Filtered objects for taskId: %d, original: %d, after filtering (excluding PACKAGE/PACKAGE BODY): %d",
		taskInfo.TaskID, originalCount, len(objects))

	// Get dependencies
	var dependencies []*objectparser.OracleDependencyVO
	if len(filteredUUIDs) > 0 {
		dependencies, err = models.GetObjectParserWriter().ListObjectDependenciesByObjectUUID(ctx, taskInfo.TaskID, filteredUUIDs)
		if err != nil {
			log.Errorf("Failed to list object dependencies, taskId: %d, err: %v", taskInfo.TaskID, err)
			return nil, fmt.Errorf("list dependencies: %w", err)
		}
	} else {
		dependencies = []*objectparser.OracleDependencyVO{}
		log.Infof("No objects to fetch dependencies for (all were PACKAGE/PACKAGE BODY), taskId: %d", taskInfo.TaskID)
	}
	log.Infof("Loaded %d dependencies for taskId: %d", len(dependencies), taskInfo.TaskID)

	// Get prompt relations
	relations, _, err := models.GetObjectParserWriter().ListOracleObjectTaskObjectPromptRelation(ctx, req.TaskId, 1, 1024*1024*1024)
	if err != nil {
		log.Errorf("Failed to list prompt relations, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("list prompt relations: %w", err)
	}
	log.Infof("Loaded %d prompt relations for taskId: %d", len(relations), taskInfo.TaskID)

	// Get prompts
	prompts, err := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
	if err != nil {
		log.Errorf("Failed to list transformation prompts, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("list prompts: %w", err)
	}
	log.Infof("Loaded %d transformation prompts for taskId: %d", len(prompts), taskInfo.TaskID)

	// Build schema object keys
	helper := core.NewConvertHelper(taskInfo.TaskID, structs.LLMProviderAPIConfig{}, nil)
	schemaObjectKeys := helper.GetDependencySchemaObjectKeys(objects)
	log.Infof("Built %d schema object keys for taskId: %d", len(schemaObjectKeys), taskInfo.TaskID)

	// Get definitions
	var definitions []*objectparser.OracleObjectDefinition
	if len(schemaObjectKeys) > 0 {
		definitions, err = models.GetObjectParserWriter().GetOracleObjectDefinitionBySchemaObjectKeys(ctx, taskInfo.TaskID, schemaObjectKeys)
		if err != nil {
			log.Errorf("Failed to get object definitions, taskId: %d, err: %v", taskInfo.TaskID, err)
			return nil, fmt.Errorf("get definitions: %w", err)
		}
	} else {
		definitions = []*objectparser.OracleObjectDefinition{}
		log.Infof("No schema object keys to fetch definitions for, taskId: %d", taskInfo.TaskID)
	}
	log.Infof("Loaded %d object definitions for taskId: %d", len(definitions), taskInfo.TaskID)

	// Get analyze details
	var analyzeDetails []*objectparser.OracleObjectDefinitionAnalyzeDetail
	if len(schemaObjectKeys) > 0 {
		analyzeDetails, err = models.GetObjectParserWriter().GetOracleObjectDefinitionAnalyzeDetailBySchemaObjectKeys(ctx, taskInfo.TaskID, schemaObjectKeys)
		if err != nil {
			log.Errorf("Failed to get analyze details, taskId: %d, err: %v", taskInfo.TaskID, err)
			return nil, fmt.Errorf("get analyze details: %w", err)
		}
	} else {
		analyzeDetails = []*objectparser.OracleObjectDefinitionAnalyzeDetail{}
		log.Infof("No schema object keys to fetch analyze details for, taskId: %d", taskInfo.TaskID)
	}
	log.Infof("Loaded %d analyze details for taskId: %d", len(analyzeDetails), taskInfo.TaskID)

	// Get functions and procedures
	allFuncAndProcs, err := models.GetObjectParserWriter().ListOracleObjectFunctionAndProcedures(ctx, taskInfo.TaskID)
	if err != nil {
		log.Errorf("Failed to list functions and procedures, taskId: %d, err: %v", taskInfo.TaskID, err)
		return nil, fmt.Errorf("list functions and procedures: %w", err)
	}
	log.Infof("Loaded %d functions and procedures for taskId: %d", len(allFuncAndProcs), taskInfo.TaskID)

	log.Infof("Successfully loaded all conversion metadata for taskId: %d - objects: %d, dependencies: %d, definitions: %d, analyzeDetails: %d",
		taskInfo.TaskID, len(objects), len(dependencies), len(definitions), len(analyzeDetails))

	return &ConversionMetadata{
		Objects:         objects,
		Dependencies:    dependencies,
		Definitions:     definitions,
		AnalyzeDetails:  analyzeDetails,
		Prompts:         prompts,
		PromptRelations: relations,
		AllFuncAndProcs: allFuncAndProcs,
	}, nil
}

// groupAndFetchTableDDLs groups tables by schema and fetches their DDLs
func (s *conversionService) groupAndFetchTableDDLs(ctx context.Context, dbConns *migration.MigrationSourceDBConns,
	uniqueTables []structs.SchemaTablePair, targetSchemas []string) (map[string][]TableDefinition, error) {

	// Fetch DDLs
	schemaTableDefMap := make(map[string][]TableDefinition)
	for _, tableInfo := range uniqueTables {
		log.Infof("Fetching DDL for table %s in schema %s", tableInfo.TableName, tableInfo.SchemaName)

		ddl, err := s.fetchTableDDL(ctx, dbConns.GetSourceDB().OracleDB, tableInfo.SchemaName, tableInfo.TableName)
		if err != nil {
			log.Warnf("Failed to fetch DDL for %s.%s: %v", tableInfo.SchemaName, tableInfo.TableName, err)
			continue
		}

		schemaTableDefMap[tableInfo.SchemaName] = append(schemaTableDefMap[tableInfo.SchemaName], TableDefinition{
			TableName: tableInfo.TableName,
			DDL:       ddl,
		})
	}

	return schemaTableDefMap, nil
}

// saveEntityResults saves generated entity results to database
func (s *conversionService) saveEntityResults(ctx context.Context, entityResults []*objectparser.OracleToJavaResult, taskInfo *task.Task) error {
	for _, entityResult := range entityResults {
		entityResult.ChannelId = taskInfo.ChannelId
		savedResult, saveErr := models.GetObjectParserWriter().SaveOracleToJavaResult(ctx, entityResult)
		if saveErr != nil {
			log.Errorf("Failed to save entity result, taskId:%d, err: %v", taskInfo.TaskID, saveErr)
			continue
		}

		if entityResult.ConvertStatus == "SUCCESS" && entityResult.IsMultiFile {
			// Extract and save individual entity files
			helper := core.NewConvertHelper(taskInfo.TaskID, structs.LLMProviderAPIConfig{}, nil)
			parsedFiles, extractErr := helper.ExtractParsedFiles(entityResult.ConvertedCode)
			if extractErr == nil && len(parsedFiles) > 0 {
				javaFiles := helper.BuildJavaFiles(uint(taskInfo.TaskID), parsedFiles, savedResult.ID)
				saveFileErr := models.GetObjectParserWriter().SaveOracleToJavaFiles(ctx, javaFiles)
				if saveFileErr != nil {
					log.Errorf("Failed to save entity files, taskId:%d, err: %v", taskInfo.TaskID, saveFileErr)
				}
			}
		}

		// Log entity conversion
		entityLog := &objectparser.OracleToJavaLog{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			LogLevel:   "info",
			LogMessage: fmt.Sprintf("Entity conversion for %s: %s", entityResult.ObjectName, entityResult.ConvertStatus),
		}
		_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, entityLog)
	}

	return nil
}

// buildConversionContext builds the context needed for conversion execution
func (s *conversionService) buildConversionContext(ctx context.Context, prep *ConversionPreparation) (*ConversionContext, error) {
	log.Infof("Building conversion context for taskId: %d, frameworks: %v", prep.TaskInfo.TaskID, prep.PromptStrategy.GetAllFrameworks())

	// Build definition map
	definitionMap := prep.ConvertHelper.BuildDefinitionMap(prep.Metadata.Definitions)
	log.Infof("Built definition map with %d definitions for taskId: %d", len(definitionMap), prep.TaskInfo.TaskID)

	// Create an enhanced object-prompt map using the strategy
	enhancedObjectPromptMap := make(map[string]*objectparser.OracleObjectTransformationPrompt)
	for _, obj := range prep.Metadata.Objects {
		framework := prep.PromptStrategy.GetFrameworkForObject(obj.UUID)
		prompt := prep.PromptStrategy.GetPromptForObject(obj.UUID, obj.Type, framework)
		enhancedObjectPromptMap[obj.UUID] = prompt
	}

	// Create a new prompt helper that uses the enhanced object-prompt mapping
	enhancedPromptHelper := &core.PromptHelper{}
	enhancedPromptHelper.SetObjectPromptMap(enhancedObjectPromptMap)
	enhancedPromptHelper.SetDefaultPrompt(prep.PromptStrategy.DefaultPrompt)
	enhancedPromptHelper.SetRequestPrompt(prep.Request.Prompt)

	// Collect table DDL context for TRIGGER objects that will use Hibernate prompts
	var tableContextMap map[string]string
	hibernateTriggerObjects := s.filterTriggersNeedingTableDDL(prep.Metadata.Objects, prep.PromptStrategy)

	if len(hibernateTriggerObjects) > 0 {
		log.Infof("Collecting table DDL context for %d TRIGGER objects that use Hibernate prompts, taskId: %d", len(hibernateTriggerObjects), prep.Request.TaskId)
		// Setup database connections using existing migration logic
		dbConns, err := migration.SetUpOracleDatabaseConns(ctx, prep.ChannelInfo, prep.TaskInfo.TaskID)
		if err != nil {
			log.Warnf("Failed to setup database connections for TRIGGER table DDL collection (continuing without table context): %v, taskId: %d", err, prep.Request.TaskId)
		} else {
			defer dbConns.Close()
			tableContextMap, err = s.buildTableContextForTriggers(ctx, prep, hibernateTriggerObjects, dbConns)
			if err != nil {
				log.Warnf("Failed to build table context for triggers (continuing with conversion): %v, taskId: %d", err, prep.Request.TaskId)
				// Continue without table context - it's not a critical failure
			}
		}
	} else {
		log.Infof("No TRIGGER objects require table DDL context (none use Hibernate prompts), taskId: %d", prep.Request.TaskId)
	}

	// Build conversion items with enhanced prompt helper
	convertItems := prep.ConvertHelper.BuildConvertItems(
		prep.Metadata.Objects,
		prep.Metadata.AnalyzeDetails,
		prep.Metadata.AllFuncAndProcs,
		prep.Metadata.Dependencies,
		enhancedPromptHelper,
		prep.Request.ConvertObjects,
		definitionMap,
		tableContextMap,
	)
	log.Infof("Built %d conversion items for taskId: %d", len(convertItems), prep.TaskInfo.TaskID)

	log.Infof("Successfully built conversion context for taskId: %d - convertItems: %d, definitions: %d",
		prep.TaskInfo.TaskID, len(convertItems), len(definitionMap))

	return &ConversionContext{
		ConvertItems:  convertItems,
		DefinitionMap: definitionMap,
		PromptHelper:  enhancedPromptHelper,
		DefaultPrompt: prep.PromptStrategy.DefaultPrompt,
	}, nil
}

// processSingleConversion processes a single conversion item
func (s *conversionService) processSingleConversion(ctx context.Context, prep *ConversionPreparation,
	item *aiprovider.ConvertItem, idx int, summary *objectparser.OracleToJavaSummary) error {

	// Save processing log
	_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, prep.ConvertHelper.BuildProcessingJavaLog(prep.TaskInfo, item))

	// Perform conversion
	log.Infof("Converting item %d/%d - taskId:%d, object:%s, type:%s",
		idx+1, summary.TotalObjectNum, prep.TaskInfo.TaskID, item.GetDisplayName(), item.ObjectType)

	startTime := time.Now()
	convertResult, certainPrompts, convertErr := prep.ConvertHelper.ConvertPLSQLToJava(ctx, item)
	duration := time.Since(startTime).Seconds()

	var result *objectparser.OracleToJavaResult
	var logEntry *objectparser.OracleToJavaLog

	if convertErr != nil {
		log.Errorf("Conversion failed - taskId:%d, object:%s, duration:%.2fs, err: %v",
			prep.TaskInfo.TaskID, item.GetDisplayName(), duration, convertErr)
		summary.FailedObjectNum++
		result = prep.ConvertHelper.BuildFailedJavaResult(prep.TaskInfo, item, startTime, duration, certainPrompts, convertErr)
		logEntry = prep.ConvertHelper.BuildFailedJavaLog(prep.TaskInfo, item, duration, convertErr)
	} else {
		log.Infof("Conversion successful - taskId:%d, object:%s, duration:%.2fs",
			prep.TaskInfo.TaskID, item.GetDisplayName(), duration)
		summary.SuccessObjectNum++
		result = prep.ConvertHelper.BuildSuccessJavaResult(prep.TaskInfo, item, convertResult, startTime, duration, certainPrompts)
		logEntry = prep.ConvertHelper.BuildSuccessJavaLog(prep.TaskInfo, item, duration)
	}

	// Save result
	savedResult, saveErr := models.GetObjectParserWriter().SaveOracleToJavaResult(ctx, result)
	if saveErr != nil {
		log.Errorf("Save result failed, taskId:%d, object:%s, err: %v",
			prep.TaskInfo.TaskID, item.GetDisplayName(), saveErr)
	} else if convertErr == nil && result.IsMultiFile {
		// Save individual files for multi-file results
		parsedFiles, extractErr := prep.ConvertHelper.ExtractParsedFiles(convertResult)
		if extractErr == nil && len(parsedFiles) > 0 {
			javaFiles := prep.ConvertHelper.BuildJavaFiles(uint(prep.TaskInfo.TaskID), parsedFiles, savedResult.ID)
			if saveFileErr := models.GetObjectParserWriter().SaveOracleToJavaFiles(ctx, javaFiles); saveFileErr != nil {
				log.Errorf("Save files failed, taskId:%d, object:%s, err: %v",
					prep.TaskInfo.TaskID, item.GetDisplayName(), saveFileErr)
			}
		}
	}

	// Save log
	_, _ = models.GetObjectParserWriter().SaveOracleToJavaLog(ctx, logEntry)

	// Update summary
	_, _ = models.GetObjectParserWriter().SaveOracleToJavaSummary(ctx, summary)

	return nil
}

// ConvertPLSQLToJavaInManual initiates manual PL/SQL to Java conversion process.
func (s *conversionService) ConvertPLSQLToJavaInManual(ctx context.Context, req *message.ConvertPLSQLToJavaInManualReq) (*message.ConvertPLSQLToJavaInManualResp, error) {
	log.Infof("ConvertPLSQLToJavaInManual taskId:%d, sqlLen:%v, prompt:%s", req.TaskId, len(req.SQL), req.Prompt)
	// Step 1: Get task information
	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, int(req.TaskId))
	if getTaskErr != nil {
		log.Errorf("ConvertPLSQLToJavaInManual failed, taskId:%d, err: %v", req.TaskId, getTaskErr)
		return nil, getTaskErr
	}

	// Step 2: Build object parser parameters
	params, buildErr := core.BuildObjectParserParam(ctx, taskInfo)
	if buildErr != nil {
		log.Errorf("ConvertPLSQLToJavaInManual failed, taskId:%d, err: %v", req.TaskId, buildErr)
		return nil, buildErr
	}

	// Step 3: Initialize license manager
	conf := config.GetGlobalConfig()
	manager, initErr := license.NewLicenseManager(conf.License, license.Config{
		EncryptionKey: license.EncryptionKey,
		HMACKey:       license.HMACKey,
	})
	if initErr != nil {
		log.Errorf("init license manager failed, taskId:%d, err: %v", req.TaskId, initErr)
		return nil, initErr
	}

	// Step 4: Check if object type is allowed
	if req.ObjectType == "PACKAGE" || req.ObjectType == "PACKAGE BODY" {
		log.Errorf("Package conversion is not supported, taskId:%d, objectType:%s", req.TaskId, req.ObjectType)
		return nil, fmt.Errorf("package conversion is not supported, object type: %s", req.ObjectType)
	}

	// Step 5: Build license request based on object type
	licenseReq := license.OperationRequest{}
	switch req.ObjectType {
	case "PROCEDURE":
		licenseReq.OpProcedureNum = 1
	case "FUNCTION":
		licenseReq.OpFunctionNum = 1
	}

	// Step 6: Validate license for the operation
	validateErr := manager.ValidateRequestNum(ctx, licenseReq)
	if validateErr != nil {
		log.Errorf("ValidateRequestNum failed, taskId:%d, err: %v", req.TaskId, validateErr)
		return nil, validateErr
	}

	promptText := req.Prompt
	if promptText == "" {
		prompts, getPromptErr := models.GetObjectParserWriter().ListOracleObjectTransformationPrompt(ctx)
		if getPromptErr != nil {
			log.Errorf("ConvertPLSQLToJavaInManual failed to get prompts, taskId:%d, err: %v", req.TaskId, getPromptErr)
			return nil, getPromptErr
		}

		for _, prompt := range prompts {
			if prompt.IsDefault {
				promptText = prompt.PromptText
				log.Infof("ConvertPLSQLToJavaInManual using default prompt for taskId:%d", req.TaskId)
				break
			}
		}

		if promptText == "" {
			log.Warnf("ConvertPLSQLToJavaInManual no default prompt found, continuing with empty prompt for taskId:%d", req.TaskId)
		}
	}

	// Step 7: Create convert helper and perform conversion
	aiHelper := core.NewConvertHelper(taskInfo.TaskID, params.LLMProviderAPIConfig, manager)

	// Build conversion item for manual conversion
	convertItem := &aiprovider.ConvertItem{
		FeatureID:        manager.GetFeatureId(req.ObjectType),
		SchemaName:       req.SchemaName,
		PackageName:      req.PackageName,
		ObjectType:       req.ObjectType,
		ObjectName:       req.ObjectName,
		ObjectDefinition: req.SQL,
		PromptText:       promptText,
	}

	// Step 8: Perform AI conversion
	convertedCode, prompts, convertErr := aiHelper.ConvertPLSQLToJava(ctx, convertItem)
	if convertErr != nil {
		log.Errorf("ConvertPLSQLToJavaInManual failed, taskId:%d, err: %v", req.TaskId, convertErr)
		return nil, convertErr
	}

	return &message.ConvertPLSQLToJavaInManualResp{
		JavaCode: convertedCode,
		Prompts:  prompts,
	}, nil
}

// detectTriggers checks if any of the objects contain triggers
func (s *conversionService) detectTriggers(objects []*objectparser.OracleDependency) bool {
	for _, obj := range objects {
		if obj.Type == "TRIGGER" {
			log.Infof("Found TRIGGER object: %s.%s", obj.SchemaName, obj.Name)
			return true
		}
	}
	return false
}

// countTriggerObjects counts the total number of TRIGGER objects
func (s *conversionService) countTriggerObjects(objects []*objectparser.OracleDependency) int {
	count := 0
	for _, obj := range objects {
		if obj.Type == "TRIGGER" {
			count++
		}
	}
	return count
}

// filterTriggersNeedingTableDDL filters TRIGGER objects that will use Hibernate prompts and need table DDL
func (s *conversionService) filterTriggersNeedingTableDDL(
	objects []*objectparser.OracleDependency,
	promptStrategy *core.ConversionPromptStrategy,
) []*objectparser.OracleDependency {
	hibernateTriggers := make([]*objectparser.OracleDependency, 0)

	for _, obj := range objects {
		if obj.Type != "TRIGGER" {
			continue
		}

		// Get the prompt that will be used for this TRIGGER object
		selectedPrompt := promptStrategy.GetPromptForObject(obj.UUID, obj.Type, "")
		if selectedPrompt == nil {
			log.Warnf("No prompt found for TRIGGER object %s, skipping table DDL collection", obj.GetSchemaObjectKey())
			continue
		}

		// Check if the selected prompt is for Hibernate framework
		if strings.EqualFold(selectedPrompt.TargetFramework, "Hibernate") {
			hibernateTriggers = append(hibernateTriggers, obj)
			log.Infof("TRIGGER object %s will use Hibernate prompt '%s' (ID: %d), including in table DDL collection",
				obj.GetSchemaObjectKey(), selectedPrompt.PromptTitle, selectedPrompt.ID)
		} else {
			log.Infof("TRIGGER object %s will use non-Hibernate prompt '%s' (framework: %s), skipping table DDL collection",
				obj.GetSchemaObjectKey(), selectedPrompt.PromptTitle, selectedPrompt.TargetFramework)
		}
	}

	log.Infof("Filtered %d Hibernate TRIGGER objects from %d total TRIGGER objects for table DDL collection",
		len(hibernateTriggers), len(objects))
	return hibernateTriggers
}

// buildTableContextForTriggers collects table DDL for TRIGGER objects to ensure
// Hibernate TRIGGER templates can generate both Entity classes and EntityListeners
func (s *conversionService) buildTableContextForTriggers(
	ctx context.Context,
	prep *ConversionPreparation,
	triggerObjects []*objectparser.OracleDependency,
	dbConns *migration.MigrationSourceDBConns,
) (map[string]string, error) {
	log.Infof("Building table context for %d TRIGGER objects, taskId: %d", len(triggerObjects), prep.Request.TaskId)

	tableContextMap := make(map[string]string)
	messageBuilder := core.NewMessageBuilder()

	// Process each TRIGGER object
	for _, triggerObj := range triggerObjects {
		log.Debugf("Processing TRIGGER object: %s.%s, taskId: %d", triggerObj.SchemaName, triggerObj.Name, prep.Request.TaskId)

		// Find analyze detail for this trigger
		var triggerAnalyzeDetail *objectparser.OracleObjectDefinitionAnalyzeDetail
		for _, detail := range prep.Metadata.AnalyzeDetails {
			if detail.SchemaObjectKey == triggerObj.GetSchemaObjectKey() {
				triggerAnalyzeDetail = detail
				break
			}
		}

		if triggerAnalyzeDetail == nil {
			log.Warnf("No analyze detail found for TRIGGER %s, skipping table DDL collection, taskId: %d", triggerObj.GetSchemaObjectKey(), prep.Request.TaskId)
			continue
		}

		// Extract tables from trigger analysis
		tables := messageBuilder.ExtractTablesFromTriggerAnalysis(triggerAnalyzeDetail)
		if len(tables) == 0 {
			log.Warnf("No table references found in TRIGGER %s analysis, taskId: %d", triggerObj.GetSchemaObjectKey(), prep.Request.TaskId)
			continue
		}

		log.Infof("Found %d table references in TRIGGER %s: %v, taskId: %d", len(tables), triggerObj.GetSchemaObjectKey(), tables, prep.Request.TaskId)

		// Collect DDL for each table using existing fetchTableDDL method
		var tableDDLs []string
		for _, table := range tables {
			// Use existing fetchTableDDL method which calls GetOracleDDL
			ddl, err := s.fetchTableDDL(ctx, dbConns.GetSourceDB().OracleDB, table.SchemaName, table.TableName)
			if err != nil {
				log.Warnf("Failed to get DDL for table %s.%s referenced by TRIGGER %s: %v, taskId: %d",
					table.SchemaName, table.TableName, triggerObj.GetSchemaObjectKey(), err, prep.Request.TaskId)
				continue
			}

			if ddl != "" {
				tableDDLs = append(tableDDLs, fmt.Sprintf("-- Table: %s.%s\n%s", table.SchemaName, table.TableName, ddl))
				log.Debugf("Collected DDL for table %s.%s (length: %d chars), taskId: %d", table.SchemaName, table.TableName, len(ddl), prep.Request.TaskId)
			}
		}

		// Store combined table DDLs for this trigger
		if len(tableDDLs) > 0 {
			combinedDDL := strings.Join(tableDDLs, "\n\n")
			tableContextMap[triggerObj.UUID] = combinedDDL
			log.Infof("Collected DDL context for TRIGGER %s: %d tables, total length: %d chars, taskId: %d",
				triggerObj.GetSchemaObjectKey(), len(tableDDLs), len(combinedDDL), prep.Request.TaskId)
		}
	}

	log.Infof("Completed table context collection for TRIGGERs: %d mappings created, taskId: %d", len(tableContextMap), prep.Request.TaskId)
	return tableContextMap, nil
}
