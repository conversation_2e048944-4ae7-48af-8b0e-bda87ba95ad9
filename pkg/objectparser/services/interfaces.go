// Package services contains interface definitions for ObjectParser services.
// This package implements the Service pattern with clean architecture principles
// and comprehensive dependency injection support.
package services

import (
	"context"

	"gitee.com/pingcap_enterprise/tms/server/message"
)

// ParsingService consolidates PLSQL parsing and dependency analysis functionality.
// This service combines PLSQLParsingHandler and DependencyHandler capabilities
// into a unified interface for parsing operations.
type ParsingService interface {
	// PLSQL Parsing functionality (from PLSQLParsingHandler)
	ParsePLSQLToAST(ctx context.Context, req *message.PlSQLToJSONReq) (*message.PlSQLToJSONResp, error)

	// Dependency Analysis functionality (from DependencyHandler)
	GetDependencyFromMetadata(ctx context.Context, req *message.GetDependencyFromMetadataReq) (*message.GetDependencyFromMetadataResp, error)

	// Service lifecycle methods
	ValidateConfiguration() error
	GetServiceStatus() ServiceStatus
}

// MetadataService consolidates Definition and Compatibility analysis functionality.
// This service combines DefinitionHandler and CompatibilityHandler capabilities
// into a unified interface for metadata operations.
type MetadataService interface {
	// Definition methods (from DefinitionHandler)
	GetObjectDetail(ctx context.Context, req *message.GetObjectDetailReq) (*message.GetObjectDetailResp, error)
	GetDefinitionsFromMetadata(ctx context.Context, req *message.GetDefinitionsFromMetadataReq) (*message.GetDefinitionsFromMetadataResp, error)
	ListAnalyzeDetail(ctx context.Context, req *message.ListAnalyzeDetailReq) (*message.ListAnalyzeDetailResp, *message.Page, error)

	// Compatibility methods (from CompatibilityHandler)
	ListBasicIncompatibleFeature(ctx context.Context, req *message.ListBasicIncompatibleFeatureReq) (*message.ListBasicIncompatibleFeatureResp, error)
	ListTaskIncompatibleFeature(ctx context.Context, req *message.ListTaskIncompatibleFeatureReq) (*message.ListTaskIncompatibleFeatureResp, error)
	UpdateTaskIncompatibleFeature(ctx context.Context, req *message.UpdateTaskIncompatibleFeatureReq) (*message.UpdateTaskIncompatibleFeatureResp, error)
	UpdateBasicIncompatibleFeature(ctx context.Context, req *message.UpdateBasicIncompatibleFeatureReq) (*message.UpdateBasicIncompatibleFeatureResp, error)
	GetIncompatibleFeatureScoring(ctx context.Context, req *message.GetIncompatibleFeatureScoringReq) (*message.GetIncompatibleFeatureScoringResp, error)

	// Service lifecycle methods
	ValidateConfiguration() error
	GetServiceStatus() ServiceStatus
}

// ConversionService handles PL/SQL to Java code conversion using AI services.
// This service encapsulates the entire workflow of converting PL/SQL procedures to Java code
// using various AI providers and maintains conversion history and results.
type ConversionService interface {
	// TestAIConnect tests connectivity to AI services
	TestAIConnect(ctx context.Context, req *message.TestAIConnectReq) (*message.TestAIConnectResp, error)

	// GetPLSQLToJavaResults retrieves current PL/SQL to Java conversion results with pagination
	GetPLSQLToJavaResults(ctx context.Context, req *message.GetPLSQLToJavaResultsReq) (*message.GetPLSQLToJavaResultsResp, *message.Page, error)

	// GetPLSQLToJavaHistoryResults retrieves historical PL/SQL to Java conversion results with pagination
	GetPLSQLToJavaHistoryResults(ctx context.Context, req *message.GetPLSQLToJavaHistoryResultsReq) (*message.GetPLSQLToJavaHistoryResultsResp, *message.Page, error)

	// GetPLSQLToJavaLogs retrieves conversion process logs
	GetPLSQLToJavaLogs(ctx context.Context, req *message.GetPLSQLToJavaLogsReq) (*message.GetPLSQLToJavaLogsResp, error)

	// GetPLSQLToJavaSummary retrieves conversion process summary information
	GetPLSQLToJavaSummary(ctx context.Context, req *message.GetPLSQLToJavaSummaryReq) (*message.GetPLSQLToJavaSummaryResp, error)

	// DownloadJavaCodes downloads generated Java code files
	DownloadJavaCodes(ctx context.Context, req *message.DownloadJavaCodesReq) (*message.DownloadJavaCodesResp, error)

	// DownloadHistoryJavaCodes downloads historical Java code files
	DownloadHistoryJavaCodes(ctx context.Context, req *message.DownloadHistoryJavaCodesReq) (*message.DownloadJavaCodesResp, error)

	// ConvertPLSQLToJava initiates PL/SQL to Java conversion process
	ConvertPLSQLToJava(ctx context.Context, req *message.ConvertPLSQLToJavaReq) (*message.ConvertPLSQLToJavaResp, error)

	// ConvertPLSQLToJavaInManual initiates manual PL/SQL to Java conversion process
	ConvertPLSQLToJavaInManual(ctx context.Context, req *message.ConvertPLSQLToJavaInManualReq) (*message.ConvertPLSQLToJavaInManualResp, error)
}

// ArchiveService handles Oracle archive log queries and transaction analysis.
// This service provides functionality for querying Oracle archive logs and transaction statistics.
type ArchiveService interface {
	// GetArchiveData retrieves archive log data with hourly statistics
	GetArchiveData(ctx context.Context, req *message.GetArchiveDataReq) (*message.GetArchiveDataResp, error)

	// GetArchiveTimes retrieves archive log timing information
	GetArchiveTimes(ctx context.Context, req *message.GetArchiveTimesReq) (*message.GetArchiveTimesResp, error)

	// GetTransactionDataBlocks retrieves transaction data block statistics
	GetTransactionDataBlocks(ctx context.Context, req *message.GetTransactionDataBlocksReq) (*message.GetTransactionDataBlocksResp, error)

	// GetTransactionLogVolume retrieves transaction log volume information
	GetTransactionLogVolume(ctx context.Context, req *message.GetTransactionLogVolumeReq) (*message.GetTransactionLogVolumeResp, error)

	// GetLogVolumePerSecond retrieves log volume per second statistics
	GetLogVolumePerSecond(ctx context.Context, req *message.GetLogVolumePerSecondReq) (*message.GetLogVolumePerSecondResp, error)

	// GetTransactionPerSecond retrieves transaction per second statistics
	GetTransactionPerSecond(ctx context.Context, req *message.GetTransactionPerSecondReq) (*message.GetTransactionPerSecondResp, error)
}

// PromptService handles AI prompt management for code conversion.
// This service manages AI prompts used in PL/SQL to Java conversion and maintains
// relationships between tasks, objects, and prompts.
type PromptService interface {
	// ListPrompt lists available AI prompts
	ListPrompt(ctx context.Context, req *message.ListPromptReq) (*message.ListPromptResp, error)

	// SavePrompt saves or updates an AI prompt
	SavePrompt(ctx context.Context, req *message.SavePromptReq) (*message.SavePromptResp, error)

	// DeletePrompt deletes an AI prompt
	DeletePrompt(ctx context.Context, req *message.DeletePromptReq) (*message.DeletePromptResp, error)

	// ListTaskObjectPromptRelation lists relationships between tasks, objects, and prompts with pagination
	ListTaskObjectPromptRelation(ctx context.Context, req *message.ListTaskObjectPromptRelationReq) (*message.ListTaskObjectPromptRelationResp, *message.Page, error)

	// SaveTaskObjectPromptRelation saves or updates a task-object-prompt relationship
	SaveTaskObjectPromptRelation(ctx context.Context, req *message.SaveTaskObjectPromptRelationReq) (*message.SaveTaskObjectPromptRelationResp, error)

	// DeleteTaskObjectPromptRelation deletes a task-object-prompt relationship
	DeleteTaskObjectPromptRelation(ctx context.Context, req *message.DeleteTaskObjectPromptRelationReq) (*message.DeleteTaskObjectPromptRelationResp, error)
}

// ServiceStatus represents the health status of a service
type ServiceStatus struct {
	IsHealthy bool   `json:"isHealthy"`
	Message   string `json:"message"`
	Version   string `json:"version"`
}

// ServiceFactory creates service instances with dependency injection.
type ServiceFactory interface {
	// CreateParsingService creates parsing service with required dependencies
	CreateParsingService() ParsingService

	// CreateMetadataService creates metadata service with required dependencies
	CreateMetadataService() MetadataService

	// CreateConversionService creates conversion service with required dependencies
	CreateConversionService() ConversionService

	// CreateArchiveService creates archive service with required dependencies
	CreateArchiveService() ArchiveService

	// CreatePromptService creates prompt service with required dependencies
	CreatePromptService() PromptService
}
