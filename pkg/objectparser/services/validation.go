package services

import (
	"fmt"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/server/message"
)

// Validation constants
const (
	MinChannelID   = 1
	MinTaskID      = 1
	MinPLSQLLength = 1
)

// ValidationError represents validation-specific errors
type ValidationError struct {
	Field   string
	Message string
	Value   interface{}
}

func (e ValidationError) Error() string {
	return e.Message
}

// ValidatePLSQLRequest validates PL/SQL parsing request with detailed error reporting
func ValidatePLSQLRequest(req *message.PlSQLToJSONReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	if req.ChannelId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("channelId must be positive, got: %d", req.ChannelId))
	}

	if req.TaskId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("taskId must be positive, got: %d", req.TaskId))
	}

	return nil
}

// ValidateDependencyRequest validates dependency analysis request with detailed error reporting
func ValidateDependencyRequest(req *message.GetDependencyFromMetadataReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	if req.ChannelId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("channelId must be positive, got: %d", req.ChannelId))
	}

	if req.TaskId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("taskId must be positive, got: %d", req.TaskId))
	}

	return nil
}

// ValidateObjectDetailRequest validates object detail request
func ValidateObjectDetailRequest(req *message.GetObjectDetailReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	if req.ChannelId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("channelId must be positive, got: %d", req.ChannelId))
	}

	if req.TaskId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("taskId must be positive, got: %d", req.TaskId))
	}

	return nil
}

// ValidateDefinitionsFromMetadataRequest validates definitions from metadata request
func ValidateDefinitionsFromMetadataRequest(req *message.GetDefinitionsFromMetadataReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	if req.ChannelId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("channelId must be positive, got: %d", req.ChannelId))
	}

	if req.TaskId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("taskId must be positive, got: %d", req.TaskId))
	}

	return nil
}

// ValidateListAnalyzeDetailRequest validates list analyze detail request
func ValidateListAnalyzeDetailRequest(req *message.ListAnalyzeDetailReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	if req.ChannelId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("channelId must be positive, got: %d", req.ChannelId))
	}

	if req.TaskId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("taskId must be positive, got: %d", req.TaskId))
	}

	return nil
}

// ValidateCompatibilityRequest validates compatibility-related requests
func ValidateCompatibilityRequest(channelId, taskId int) error {
	if channelId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("channelId must be positive, got: %d", channelId))
	}

	if taskId <= 0 {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID,
			fmt.Sprintf("taskId must be positive, got: %d", taskId))
	}

	return nil
}

// ValidateBasicIncompatibleFeatureRequest validates basic incompatible feature request
func ValidateBasicIncompatibleFeatureRequest(req *message.ListBasicIncompatibleFeatureReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	// ListBasicIncompatibleFeatureReq doesn't have channelId/taskId fields
	return nil
}

// ValidateTaskIncompatibleFeatureRequest validates task incompatible feature request
func ValidateTaskIncompatibleFeatureRequest(req *message.ListTaskIncompatibleFeatureReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	return ValidateCompatibilityRequest(req.ChannelId, req.TaskId)
}

// ValidateUpdateTaskIncompatibleFeatureRequest validates update task incompatible feature request
func ValidateUpdateTaskIncompatibleFeatureRequest(req *message.UpdateTaskIncompatibleFeatureReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	return ValidateCompatibilityRequest(req.ChannelId, req.TaskId)
}

// ValidateUpdateBasicIncompatibleFeatureRequest validates update basic incompatible feature request
func ValidateUpdateBasicIncompatibleFeatureRequest(req *message.UpdateBasicIncompatibleFeatureReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	// UpdateBasicIncompatibleFeatureReq doesn't have channelId/taskId fields
	return nil
}

// ValidateGetIncompatibleFeatureScoringRequest validates get incompatible feature scoring request
func ValidateGetIncompatibleFeatureScoringRequest(req *message.GetIncompatibleFeatureScoringReq) error {
	if req == nil {
		return tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "request cannot be nil")
	}

	return ValidateCompatibilityRequest(req.ChannelId, req.TaskId)
}
