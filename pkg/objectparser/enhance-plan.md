一、基于 ants 的并发处理优化方案

1. 修改 ObjectParserParam 结构体，增加并发配置

// common/structs/objectparser.go
type ObjectParserParam struct {
LLMProviderAPIConfig

      ReservedWordScoringMode    IncompatibleFeatureScoringMode
      DatabaseLinkScoringMode    IncompatibleFeatureScoringMode
      HistogramScoreWeightFactor float64
      ConvertJavaThread          int

      // 新增并发分析配置
      AnalyzeWorkerPoolSize      int  // 分析并发数，默认 CPU * 2
      AnalyzeBatchSize           int  // 每批处理的对象数量，默认 100

}

// 新增设置方法
func (i *ObjectParserParam) SetAnalyzeWorkerPoolSize(value int) {
if value <= 0 {
value = runtime.NumCPU() * 2
}
i.AnalyzeWorkerPoolSize = value
}

func (i *ObjectParserParam) SetAnalyzeBatchSize(value int) {
if value <= 0 {
value = 100
}
i.AnalyzeBatchSize = value
}

2. 修改 BuildObjectParserParam 添加并发参数构造

// pkg/objectparser/core/param.go
func BuildObjectParserParam(ctx context.Context, taskInfo *task.Task) (*structs.ObjectParserParam, error) {
// ... 现有代码 ...

      // 添加分析并发参数
      if value, ok := taskParamMap[constants.ParamsObjectParserAnalyzeWorkerPoolSize]; ok {
          intVal, _ = parse.ParseInt(value)
          objectParserParam.SetAnalyzeWorkerPoolSize(intVal)
      }

      if value, ok := taskParamMap[constants.ParamsObjectParserAnalyzeBatchSize]; ok {
          intVal, _ = parse.ParseInt(value)
          objectParserParam.SetAnalyzeBatchSize(intVal)
      }

      return objectParserParam, nil

}

3. 实现并发的 processObjectAnalysisConcurrent

// pkg/objectparser/core/executor.go

// 分析任务结构
type AnalysisTask struct {
Index int
OracleObjectDefinition *objectparser.OracleObjectDefinition
}

// 分析结果结构
type AnalysisResult struct {
Index int
Detail                  *objectparser.OracleObjectDefinitionAnalyzeDetail
IncompatibleFeatures    []*objectparser.OracleObjectDefinitionIncompatibleFeature
Error error
}

func (i *OracleObjectParserTask) processObjectAnalysisConcurrent(ctx context.Context, definitionPos []*
objectparser.OracleObjectDefinition, metadataHelper *MetadataHelper,
analysisContext *AnalysisContext) error {
channelId, taskId := i.channelInfo.ChannelId, i.taskInfo.TaskID
poolSize := i.objectParserParam.AnalyzeWorkerPoolSize
if poolSize <= 0 {
poolSize = runtime.NumCPU() * 2
}

      totalObjects := len(definitionPos)
      resultChan := make(chan *AnalysisResult, totalObjects)

      i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.ObjectParserLogType, constants.ObjectParserStepParseAST,
          fmt.Sprintf("starting concurrent AST analysis with %d workers for %d objects", poolSize, totalObjects)))

      // 创建 ants 池
      pool, err := ants.NewPoolWithFunc(poolSize, func(payload interface{}) {
          task := payload.(*AnalysisTask)
          result := i.analyzeObject(ctx, task, metadataHelper, analysisContext)
          resultChan <- result
      })
      if err != nil {
          log.Errorf("Failed to create ants pool: %v", err)
          return err
      }
      defer pool.Release()

      // 提交任务到池
      startTime := time.Now()
      for idx, objDef := range definitionPos {
          task := &AnalysisTask{
              Index:                   idx,
              OracleObjectDefinition: objDef,
          }

          if err := pool.Invoke(task); err != nil {
              log.Errorf("Failed to submit task %d to pool: %v", idx, err)
              resultChan <- &AnalysisResult{
                  Index: idx,
                  Error: err,
              }
          }
      }

      // 收集结果并实时保存
      successCount, failedCount := 0, 0
      for completed := 0; completed < totalObjects; completed++ {
          select {
          case result := <-resultChan:
              if result.Error != nil {
                  failedCount++
                  i.AppendErrorProgressLog(ctx, constants.BuildProgressLog(constants.ObjectParserLogType, constants.ObjectParserStepParseAST,
                      fmt.Sprintf("analysis failed %d/%d, error: %v", completed+1, totalObjects, result.Error)))
              } else {
                  successCount++
                  // 实时保存结果
                  if err := i.saveAnalysisResult(ctx, result, analysisContext); err != nil {
                      log.Errorf("Failed to save analysis result: %v", err)
                  }
              }

              // 每处理 100 个对象报告一次进度
              if (completed+1)%100 == 0 || completed+1 == totalObjects {
                  elapsed := time.Since(startTime)
                  rate := float64(completed+1) / elapsed.Seconds()
                  i.AppendProgressLog(ctx, constants.BuildProgressLog(constants.ObjectParserLogType, constants.ObjectParserStepParseAST,
                      fmt.Sprintf("progress: %d/%d (%.1f%%), success: %d, failed: %d, rate: %.1f objects/sec",
                          completed+1, totalObjects, float64(completed+1)*100/float64(totalObjects),
                          successCount, failedCount, rate)))
              }

          case <-ctx.Done():
              return fmt.Errorf("context cancelled during concurrent analysis")
          }
      }

      log.Infof("Concurrent analysis completed, total: %d, success: %d, failed: %d, time: %v",
          totalObjects, successCount, failedCount, time.Since(startTime))

      return nil

}

// 单个对象分析方法
func (i *OracleObjectParserTask) analyzeObject(ctx context.Context, task *AnalysisTask, metadataHelper *MetadataHelper,
analysisContext *AnalysisContext) *AnalysisResult {
objDef := task.OracleObjectDefinition
detailMap := analysisContext.detailMap
summaryMap := analysisContext.summaryMap
objectScorer := analysisContext.objectScorer

      result := &AnalysisResult{Index: task.Index}

      // 获取或创建 detail
      detail, exist := detailMap[objDef.SchemaObjectKey]
      if !exist {
          err := tmserrors.NewErrorf(tmserrors.TMS_OBJECT_PARSER_MAP_SOURCE_CODE_TO_DETAIL_FAILED,
              "schema:%s, object:%s, type:%s", objDef.SchemaName, objDef.ObjectName, objDef.ObjectType)

          detail = &objectparser.OracleObjectDefinitionAnalyzeDetail{
              TaskId:          i.taskInfo.TaskID,
              SchemaName:      objDef.SchemaName,
              ObjectName:      objDef.ObjectName,
              ObjectType:      objDef.ObjectType,
              SchemaObjectKey: objDef.SchemaObjectKey,
              Status:          constants.StatStatusFailed.String(),
              ErrorDetail:     err.Error(),
          }
          result.Detail = detail
          result.Error = err
          return result
      }

      // 检查对象状态
      if metadataHelper.GetObjectStatus(objDef.SchemaObjectKey) == "INVALID" {
          err := tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_INVALID_OBJECT, "")
          detail.Status = constants.StatStatusFailed.String()
          detail.ErrorDetail = err.Error()
          result.Detail = detail
          result.Error = err
          return result
      }

      // 调用 Parser API 分析
      actualReq := dto.AnalyzeSQLRequest{
          IsEncoded: true,
          PLSQL:     stringutil.EncodeSQL(objDef.AllText),
      }

      actualResp, analyzeErr := i.objectParserAdaptor.AnalyzeSQL(ctx, actualReq)
      if analyzeErr != nil {
          detail.Status = constants.StatStatusFailed.String()
          detail.ErrorDetail = tmserrors.NewError(tmserrors.TMS_OBJECT_PARSER_PARSE_OBJECT_FAILED, analyzeErr.Error()).Error()
          result.Detail = detail
          result.Error = analyzeErr
          return result
      }

      // 分析成功，填充结果
      detail.Status = constants.StatStatusSuccess.String()
      detail.MethodInvokeList = actualResp.MarshalMethodInvokeListToString()
      detail.IdentifierList = actualResp.MarshalIdentifierListToString()
      detail.TableReferenceList = actualResp.MarshalTableReferenceListToString()
      detail.ReservedWordList = actualResp.MarshalReservedWordListToString()
      detail.PLSQLSegment = actualResp.MarshalPLSQLSegmentToString()
      detail.ReservedWordCount, detail.DatabaseLinkCount, detail.IncompatibleFeatureScoreContext =
          objectScorer.GetIncompatibleFeatureCount(actualResp.GetReservedWordList(), actualResp.GetTableReferenceList())

      result.Detail = detail
      result.IncompatibleFeatures = objectScorer.BuildIncompatibleFeatures(detail, actualResp)

      return result

}

// 保存分析结果
func (i *OracleObjectParserTask) saveAnalysisResult(ctx context.Context, result *AnalysisResult, analysisContext *
AnalysisContext) error {
// 使用互斥锁保护 summary 更新
summaryMutex := &sync.Mutex{}

      summaryMutex.Lock()
      summary := analysisContext.summaryMap[result.Detail.SchemaName]
      if result.Error != nil {
          summary.FailedNum++
      } else {
          summary.SuccessNum++
      }
      summary.Duration = i.getDurationAtLeast(analysisContext.summaryStartTimeMap[result.Detail.SchemaName], 1)
      summaryMutex.Unlock()

      // 保存不兼容特性
      if len(result.IncompatibleFeatures) > 0 {
          _, err := models.GetObjectParserWriter().CreateOracleObjectDefinitionIncompatibleFeatures(ctx, result.IncompatibleFeatures)
          if err != nil {
              return err
          }
      }

      // 更新 detail
      _, err := models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeDetail(ctx, result.Detail)
      if err != nil {
          return err
      }

      // 更新 summary
      _, err = models.GetObjectParserWriter().UpdateOracleObjectDefinitionAnalyzeSummary(ctx, summary)
      return err

}

二、智能批处理和缓存方案

1. 智能批处理实现

// pkg/objectparser/core/smart_batcher.go
package core

import (
"sync"
"time"
)

type SmartBatcher struct {
minBatchSize int
maxBatchSize int
targetLatency time.Duration
currentBatchSize int
mu sync.RWMutex

      // 性能统计
      recentRates      []float64  // 最近的处理速率
      maxRecentSamples int

}

func NewSmartBatcher(minSize, maxSize int, targetLatency time.Duration) *SmartBatcher {
return &SmartBatcher{
minBatchSize:     minSize,
maxBatchSize:     maxSize,
targetLatency:    targetLatency,
currentBatchSize: minSize,
maxRecentSamples: 10,
recentRates:      make([]float64, 0, 10),
}
}

func (b *SmartBatcher) GetCurrentBatchSize() int {
b.mu.RLock()
defer b.mu.RUnlock()
return b.currentBatchSize
}

func (b *SmartBatcher) RecordBatchPerformance(itemCount int, duration time.Duration) {
b.mu.Lock()
defer b.mu.Unlock()

      if duration <= 0 {
          return
      }

      // 计算处理速率
      itemsPerSecond := float64(itemCount) / duration.Seconds()

      // 更新最近速率记录
      b.recentRates = append(b.recentRates, itemsPerSecond)
      if len(b.recentRates) > b.maxRecentSamples {
          b.recentRates = b.recentRates[1:]
      }

      // 计算平均速率
      avgRate := b.calculateAverageRate()

      // 基于目标延迟计算最优批量大小
      optimalBatch := int(avgRate * b.targetLatency.Seconds())

      // 应用边界限制
      if optimalBatch < b.minBatchSize {
          b.currentBatchSize = b.minBatchSize
      } else if optimalBatch > b.maxBatchSize {
          b.currentBatchSize = b.maxBatchSize
      } else {
          // 平滑调整，避免剧烈变化
          delta := optimalBatch - b.currentBatchSize
          b.currentBatchSize += delta / 2
      }

      log.Debugf("SmartBatcher adjusted batch size to %d (rate: %.1f items/sec, optimal: %d)",
          b.currentBatchSize, avgRate, optimalBatch)

}

func (b *SmartBatcher) calculateAverageRate() float64 {
if len(b.recentRates) == 0 {
return 100.0 // 默认速率
}

      sum := 0.0
      for _, rate := range b.recentRates {
          sum += rate
      }
      return sum / float64(len(b.recentRates))

}

// 在 OracleObjectParserTask 中使用
func (i *OracleObjectParserTask) saveMetadataWithSmartBatching(ctx context.Context, ...) error {
batcher := NewSmartBatcher(64, 512, 100*time.Millisecond)

      // 分批保存
      for start := 0; start < len(definitionPos); {
          batchSize := batcher.GetCurrentBatchSize()
          end := start + batchSize
          if end > len(definitionPos) {
              end = len(definitionPos)
          }

          batch := definitionPos[start:end]
          startTime := time.Now()

          // 批量保存
          _, err := models.GetObjectParserWriter().CreateOracleObjectDefinition(ctx, batch)
          if err != nil {
              return err
          }

          // 记录性能并调整批量大小
          duration := time.Since(startTime)
          batcher.RecordBatchPerformance(len(batch), duration)

          start = end
      }

      return nil

}

2. LRU 缓存实现

// pkg/objectparser/core/analysis_cache.go
package core

import (
"sync"
"time"
lru "github.com/hashicorp/golang-lru"
)

type AnalysisCache struct {
cache        *lru.Cache
mu sync.RWMutex
hits int64
misses int64
maxCacheSize int
}

type CachedAnalysisResult struct {
Result dto.AnalyzeSQLResponse
Timestamp time.Time
TTL time.Duration
}

func NewAnalysisCache(maxSize int) (*AnalysisCache, error) {
cache, err := lru.New(maxSize)
if err != nil {
return nil, err
}

      return &AnalysisCache{
          cache:        cache,
          maxCacheSize: maxSize,
      }, nil

}

func (c *AnalysisCache) Get(key string) (*dto.AnalyzeSQLResponse, bool) {
c.mu.RLock()
defer c.mu.RUnlock()

      if val, ok := c.cache.Get(key); ok {
          cached := val.(*CachedAnalysisResult)
          // 检查是否过期
          if time.Since(cached.Timestamp) < cached.TTL {
              c.hits++
              return &cached.Result, true
          }
          // 过期则删除
          c.cache.Remove(key)
      }

      c.misses++
      return nil, false

}

func (c *AnalysisCache) Set(key string, result dto.AnalyzeSQLResponse, ttl time.Duration) {
c.mu.Lock()
defer c.mu.Unlock()

      c.cache.Add(key, &CachedAnalysisResult{
          Result:    result,
          Timestamp: time.Now(),
          TTL:       ttl,
      })

}

func (c *AnalysisCache) GetStats() (hits, misses int64, hitRate float64) {
c.mu.RLock()
defer c.mu.RUnlock()

      hits = c.hits
      misses = c.misses
      total := hits + misses
      if total > 0 {
          hitRate = float64(hits) / float64(total) * 100
      }
      return

}

// 生成缓存键
func generateCacheKey(objDef *objectparser.OracleObjectDefinition) string {
// 使用对象的唯一标识和内容的哈希值作为键
hash := sha256.Sum256([]byte(objDef.AllText))
return fmt.Sprintf("%s:%x", objDef.SchemaObjectKey, hash[:8])
}

// 在分析方法中使用缓存
func (i *OracleObjectParserTask) analyzeObjectWithCache(ctx context.Context, task *AnalysisTask, cache *AnalysisCache) *
AnalysisResult {
objDef := task.OracleObjectDefinition
cacheKey := generateCacheKey(objDef)

      // 尝试从缓存获取
      if cachedResp, found := cache.Get(cacheKey); found {
          log.Debugf("Cache hit for object %s", objDef.SchemaObjectKey)
          // 使用缓存的结果构建 AnalysisResult
          return i.buildAnalysisResultFromCache(task, cachedResp)
      }

      // 缓存未命中，执行实际分析
      result := i.analyzeObject(ctx, task, metadataHelper, analysisContext)

      // 如果分析成功，缓存结果
      if result.Error == nil && result.Detail.Status == constants.StatStatusSuccess.String() {
          // 从 detail 重建响应对象用于缓存
          resp := i.reconstructResponseFromDetail(result.Detail)
          cache.Set(cacheKey, resp, 1*time.Hour) // 缓存 1 小时
      }

      return result

}

3. 整合到 Process 流程

// 修改 Process 方法，整合所有优化
func (i *OracleObjectParserTask) Process(ctx context.Context) {
// ... 前面的代码保持不变 ...

      // 创建分析缓存
      analysisCache, err := NewAnalysisCache(1000) // 缓存最多 1000 个对象
      if err != nil {
          log.Warnf("Failed to create analysis cache: %v", err)
          analysisCache = nil
      }

      // 使用并发分析替代原来的串行分析
      if i.objectParserParam.AnalyzeWorkerPoolSize > 0 {
          // 使用并发版本
          processErr := i.processObjectAnalysisConcurrent(ctx, definitionPos, metadataHelper, analysisContext)
          if processErr != nil {
              log.Errorf("processObjectAnalysisConcurrent failed, channelId:%d, taskId:%d, err:%v",
                  channelId, taskId, processErr)
              SaveTaskProcessErrorAndLog(ctx, channelId, taskId, processErr)
              return
          }
      } else {
          // 保留原有串行版本作为降级方案
          processErr := i.processObjectAnalysis(ctx, definitionPos, metadataHelper, analysisContext)
          if processErr != nil {
              log.Errorf("processObjectAnalysis failed, channelId:%d, taskId:%d, err:%v",
                  channelId, taskId, processErr)
              SaveTaskProcessErrorAndLog(ctx, channelId, taskId, processErr)
              return
          }
      }

      // 输出缓存统计
      if analysisCache != nil {
          hits, misses, hitRate := analysisCache.GetStats()
          log.Infof("Analysis cache stats - hits: %d, misses: %d, hit rate: %.1f%%",
              hits, misses, hitRate)
      }

      // ... 后续代码保持不变 ...

}

三、流式处理优化（内存优化）

// 流式获取对象定义
func (i *OracleObjectParserTask) fetchOracleMetadataStreaming(ctx context.Context) (<-chan *OracleMetadataBatch, <-chan
error) {
batchChan := make(chan *OracleMetadataBatch, 2)
errChan := make(chan error, 1)

      go func() {
          defer close(batchChan)
          defer close(errChan)

          dbConn := i.dbConns.GetSourceDB().OracleDB
          targetSchemas := i.targetSchemas

          offset := 0
          batchSize := 1000

          for {
              select {
              case <-ctx.Done():
                  errChan <- ctx.Err()
                  return
              default:
              }

              // 分页查询
              objects, err := models.GetDatasourceReaderWriter().
                  GetOracleObjectDefinitionsBySchemasPaginated(ctx, dbConn,
                      targetSchemas, constants.ValidObjectTypes, offset, batchSize)

              if err != nil {
                  errChan <- err
                  return
              }

              if len(objects) == 0 {
                  break
              }

              batchChan <- &OracleMetadataBatch{
                  Objects:    objects,
                  BatchIndex: offset / batchSize,
                  IsLast:     len(objects) < batchSize,
              }

              offset += batchSize
          }
      }()

      return batchChan, errChan

}

四、新增的常量定义

// common/constants/params.go
const (
// ... 现有常量 ...

      // Object Parser 并发分析参数
      ParamsObjectParserAnalyzeWorkerPoolSize = "analyze_worker_pool_size"
      ParamsObjectParserAnalyzeBatchSize      = "analyze_batch_size"

)

这个方案的主要优势：

1. 与现有 ants 使用保持一致：采用类似 datasource_impl.go 的模式
2. 参数可配置：通过任务参数控制并发数和批量大小
3. 智能批处理：根据实际处理速度动态调整批量大小
4. LRU 缓存：避免重复解析相同的对象
5. 向后兼容：保留原有串行处理作为降级方案
6. 实时进度反馈：定期报告处理进度和速率

╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│ Ready to code? │
│ │
│ Here is Claude's plan:
│
│
╭─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│
│ │ 基于对 OracleObjectParserTask.Process 函数的深入分析和您的要求，我将实施以下优化方案： │ │
│ │ │ │
│ │ 实施计划 │ │
│ │ │ │
│ │ 1. 基于 ants 的并发处理优化 │ │
│ │ │ │
│ │ - 在 ObjectParserParam 结构体中增加并发配置参数： │ │
│ │ - AnalyzeWorkerPoolSize（分析并发数，默认 CPU * 2） │ │
│ │ - AnalyzeBatchSize（每批处理对象数，默认 100） │ │
│ │ - 修改 BuildObjectParserParam 添加并发参数构造 │ │
│ │ - 实现 processObjectAnalysisConcurrent 方法，使用 ants.NewPoolWithFunc 创建 worker pool │ │
│ │ - 支持实时进度报告和错误聚合 │ │
│ │ - 预期效果：处理时间从 35 分钟降至 3-5 分钟 │ │
│ │ │ │
│ │ 2. 流式数据处理（内存优化） │ │
│ │ │ │
│ │ - 实现 fetchOracleMetadataStreaming 方法，分页查询对象（每批 1000 条） │ │
│ │ - 使用 channel 传递批次数据，避免一次性加载 │ │
│ │ - 修改 buildMetadata 和 saveMetadata 支持流式处理 │ │
│ │ - 预期效果：内存占用从 2-3GB 降至 500MB │ │
│ │ │ │
│ │ 3. 智能批处理和缓存 │ │
│ │ │ │
│ │ - 实现 SmartBatcher 类，根据处理速度动态调整批量大小（64-512） │ │
│ │ - 添加 LRU 缓存（使用 hashicorp/golang-lru），缓存解析结果 │ │
│ │ - 在 analyzeObject 中集成缓存，避免重复解析 │ │
│ │ - 优化数据库批量操作参数 │ │
│ │ │ │
│ │ 技术细节 │ │
│ │ │ │
│ │ - 使用 ants 池与项目其他模块保持一致 │ │
│ │ - 通过互斥锁保护并发更新 summary │ │
│ │ - 实时保存分析结果，避免内存积累 │ │
│ │ - 保留原有串行处理作为降级方案 │ │
│ │ - 新增常量定义在 common/constants/params.go │ │
│ │ │ │
│ │ 这些优化将显著提升系统在极端场景下的性能和稳定性，同时保持向后兼容。
