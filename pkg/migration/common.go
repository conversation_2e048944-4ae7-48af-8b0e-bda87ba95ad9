package migration

import (
	"context"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmso2t "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/o2t"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

const (
	ChunkStatusWaiting = "WAITING"
	ChunkStatusRunning = "RUNNING"
	ChunkStatusSuccess = "SUCCESS"
	ChunkStatusFailed  = "FAILED"
	ChunkStatusIgnore  = "IGNORE"
)

func buildChunkWhereCondition(res map[string]string, wherePrefix string) (string, string) {
	rowIdRange := res["CMD"]
	var combineWhereRange string
	if wherePrefix == "" {
		combineWhereRange = rowIdRange
	} else {
		combineWhereRange = tmso2t.StringsBuilder(rowIdRange, ` AND `, wherePrefix)
	}
	return rowIdRange, combineWhereRange
}

func buildSingleChunkWhereCondition(wherePrefix string) string {
	if wherePrefix == "" {
		return `1 = 1`
	} else {
		return tmso2t.StringsBuilder(`1 = 1 AND `, wherePrefix)
	}
}

func createErrorProgressLogDetail(ctx context.Context, taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable, logType string, err error) {
	_, createLogErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID:   taskInfo.ChannelId,
		TaskID:      taskInfo.TaskID,
		SchemaNameS: channelSchemaTable.SchemaNameS,
		TableNameS:  channelSchemaTable.TableNameS,
		Detail:      constants.BuildProgressLog(logType, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("table [%s.%s], taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, err)),
		LogLevel:    log.LogError,
	})
	if createLogErr != nil {
		log.Errorf("create progress log detail record failed: %v", err)
	}
}

func createFullDataErrorProgressLogDetail(ctx context.Context, taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable, err error) {
	createErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, constants.MigrationLogTypeFullData, err)
}

func createCSVErrorProgressLogDetail(ctx context.Context, taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable, err error) {
	createErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, constants.MigrationLogTypeCSV, err)
}
