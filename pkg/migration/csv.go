package migration

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	transferconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/csv/oracle/o2t"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/pingcap/errors"
	"golang.org/x/sync/errgroup"
)

type ExecuteMode string

const ExecuteModeSingle ExecuteMode = "single"
const ExecuteModeCluster ExecuteMode = "cluster"

// StartCSVMigrationTask 单机版执行CSV导出+导入
func StartCSVMigrationTask(ctx context.Context, channelId, taskId int) {

	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("get task failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getTaskErr)
		return
	}
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getChannelErr)
		return
	}

	csvParam, buildParamErr := BuildCSVMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build csv migration param failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildParamErr)
		return
	}

	dataSources, getErr := GetDatabaseSources(ctx, channelInfo, taskInfo.TaskID)
	if getErr != nil {
		log.Errorf("get database sources failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return
	}

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		log.Errorf("build task table config map failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildErr)
		return
	}

	mt := NewCSVMigrationTask(channelInfo, taskInfo, csvParam, dataSources, taskTableConfigMap)
	mt.SetExecuteMode(ExecuteModeSingle)
	go mt.Process(ctx)
}

// optional func of ChunkMigrateParam
type ChunkMigrateParamOptFunc func(*ChunkMigrateParam)

func WithChannelInfo(information *channel.ChannelInformation) ChunkMigrateParamOptFunc {
	return func(i *ChunkMigrateParam) {
		i.ChannelInfo = information
	}
}

func WithTaskInfo(taskInfo *task.Task) ChunkMigrateParamOptFunc {
	return func(i *ChunkMigrateParam) {
		i.TaskInfo = taskInfo
	}
}
func WithSourceDBType(sourceDBType string) ChunkMigrateParamOptFunc {
	return func(i *ChunkMigrateParam) {
		i.SourceDBType = sourceDBType
	}
}

func WithClearData(clearData bool) ChunkMigrateParamOptFunc {
	return func(i *ChunkMigrateParam) {
		i.ClearData = clearData
	}
}

func WithCSVMigrationConfigParam(migrationParam *structs.CSVMigrationConfigParam) ChunkMigrateParamOptFunc {
	return func(i *ChunkMigrateParam) {
		i.MigrationParam = migrationParam
	}
}

func WithMigrationDBConns(dbConns *MigrationDBConns) ChunkMigrateParamOptFunc {
	return func(i *ChunkMigrateParam) {
		i.DBConns = dbConns
	}
}

func RemoveTaskTableCSVFile(ctx context.Context, taskInfo *task.Task, migrationParam *structs.CSVMigrationConfigParam) error {

	channelId := taskInfo.ChannelId
	taskId := taskInfo.TaskID

	log.Infof("task finish, remove csv file, channelId: %d, taskId: %d", channelId, taskId)
	schemaTables, getErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getErr != nil {
		log.Errorf("get channel schema tables failed, taskId:%d, err:%v", taskId, getErr)
		return getErr
	}

	for _, table := range schemaTables {
		removeCSVFiles := make([]string, 0)
		logMessage := fmt.Sprintf("%s.%s table, migrate success, remove csv file", table.SchemaNameS, table.TableNameS)
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: taskInfo.ChannelId,
			TaskID:    taskInfo.TaskID,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportRunning, logMessage),
			LogLevel:  log.LogWarn,
		})

		csvFileDir := migrationParam.GetOutputDataDir(taskInfo.ChannelId, taskInfo.TaskID)
		var f structs.MyDumperFile
		if config.GetGlobalConfig().IsClusterMode() {
			f = getClusterModeCSVFilePattern(table.SchemaNameT, table.TableNameT)
		} else {
			f = getSingleModeCSVFilePattern(table.SchemaNameT, table.TableNameT)
		}
		log.Infof("remove csv file from %s, pattern:%s", csvFileDir, f.Pattern)

		csvFilePatternStr := f.Pattern
		csvFilePattern, compileErr := regexp.Compile(csvFilePatternStr)
		if compileErr != nil {
			log.Errorf("compile csv file pattern failed, pattern:%s, err:%v", csvFilePatternStr, compileErr)
			return compileErr
		}

		walkErr := filepath.WalkDir(csvFileDir, func(path string, info os.DirEntry, err error) error {
			if info.IsDir() {
				return nil
			}
			if csvFilePattern.MatchString(path) {
				removeCSVFiles = append(removeCSVFiles, path)
			} else {
				log.Debugf("skip remove csv file, file path:%s", path)
			}
			return nil
		})
		if walkErr != nil {
			log.Errorf("scanning csv file for delete failed, taskId:%d, err:%v", taskId, walkErr)
			return walkErr
		}
		log.Infof("task finish, remove csv file, channelId: %d, taskId: %d, table %s, removeCSVFiles:%d", channelId, taskId, table.TableNameS, len(removeCSVFiles))
		log.Debugf("csvFilePatternStr:%v, removeCSVFiles:%v", csvFilePatternStr, removeCSVFiles)
		for _, removeCSVFile := range removeCSVFiles {
			if removeErr := os.Remove(removeCSVFile); removeErr != nil {
				log.Errorf("remove csv file failed, taskId:%d, file path:%s, err:%v", taskId, removeCSVFile, removeErr)
				return removeErr
			}
		}
	}

	return nil
}

type ChunkMigrateParam struct {
	ChannelInfo         *channel.ChannelInformation
	TaskInfo            *task.Task
	MigrationParam      *structs.CSVMigrationConfigParam
	DBConns             *MigrationDBConns
	ChannelSchemaObject *channel.ChannelSchemaObject
	ClearData           bool
	SourceDBType        string
}

func (i ChunkMigrateParam) GetClearData() bool {
	return i.ClearData
}
func (i ChunkMigrateParam) GetTaskInfo() *task.Task {
	return i.TaskInfo
}
func (i ChunkMigrateParam) GetMigrationDBConns() *MigrationDBConns {
	return i.DBConns
}
func (i ChunkMigrateParam) GetMigrationParam() *structs.CSVMigrationConfigParam {
	return i.MigrationParam
}
func (i ChunkMigrateParam) GetSourceDBType() string {
	return i.SourceDBType
}
func (i ChunkMigrateParam) GetChannelId() int {
	return i.ChannelInfo.ChannelId
}

func (i ChunkMigrateParam) GetTaskId() int {
	return i.TaskInfo.TaskID
}

type MakeChunksFunc func(ctx context.Context, makeChunksParam ChunkMigrateParam) error

type MakeChunksFactory struct {
	channelId, taskId int
}

func (i MakeChunksFactory) GetMakeChunksFunc(sourceDSType string) (MakeChunksFunc, error) {
	switch sourceDSType {
	case constants.DB_TYPE_ORACLE:
		log.Infof("split task table into chunks, channelId: %d, taskId: %d", i.channelId, i.taskId)
		return splitTaskTablesIntoCSVChunks, nil
	case constants.DB_TYPE_ORACLE_ADG:
		log.Infof("fetch table chunks from oracle-adg, channelId: %d, taskId: %d", i.channelId, i.taskId)
		return fetchTableChunksIntoCSVChunks, nil
	case constants.DB_TYPE_CSV:
		log.Infof("validate table chunks meta from csv, channelId: %d, taskId: %d", i.channelId, i.taskId)
		return validateCSVChunks, nil
	}
	return nil, errors.Errorf("not support db type: %s for csv migration", sourceDSType)
}

func splitTaskTablesIntoCSVChunks(ctx context.Context, makeChunksParam ChunkMigrateParam) error {

	channelId := makeChunksParam.GetChannelId()
	taskId := makeChunksParam.GetTaskId()
	taskInfo := makeChunksParam.TaskInfo
	dbConns := makeChunksParam.DBConns
	migrationParam := makeChunksParam.MigrationParam
	channelSchemaObject := makeChunksParam.ChannelSchemaObject
	clearData := makeChunksParam.ClearData

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	// 1. 初始化任务
	// 2. 任务运行
	startTime := time.Now()

	channelSchemaTables, getErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getErr != nil {
		return getErr
	}
	log.Infof("get channel schema tables, channelId:%d, taskId:%d,len:%d", channelId, taskId, len(channelSchemaTables))

	var channelSchemaTableIds []int
	channelSchemaTableIdMap := make(map[int]*channel.ChannelSchemaTable)
	for _, channelSchemaTable := range channelSchemaTables {
		channelSchemaTableIds = append(channelSchemaTableIds, channelSchemaTable.ChannelSchtableId)
		channelSchemaTableIdMap[channelSchemaTable.ChannelSchtableId] = channelSchemaTable
	}

	log.Infof("get table exist status from oracle and tidb, channelId:%d, taskId:%d", channelId, taskId)
	lowerCase := strings.ToLower
	existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, getTableStatusErr :=
		commonpkg.GetTableStatusFromDataSource(ctx, channelSchemaTableIds, taskInfo, dbConns.GetSourceDS(), dbConns.GetTargetDS(), lowerCase, lowerCase)
	if getTableStatusErr != nil {
		log.Errorf("get table status failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, getTableStatusErr)
		return getTableStatusErr
	}
	log.Info("start csv data migration, taskId:%d, channelId:%d, oracleNotExistTables:%d, tidbNotExistTables:%d, existTables:%d",
		taskInfo.TaskID, taskInfo.ChannelId, len(oracleNotExistTables), len(tidbNotExistTables), len(existTables))

	saveTableStatusErr := saveNotExistTablesToSummaryAndChunks(ctx, "CSV", taskInfo, oracleNotExistTables, tidbNotExistTables, dbConns, migrationParam.GetChunkSize(), taskTableConfigMap)
	if saveTableStatusErr != nil {
		log.Errorf("saveNotExistTablesToSummaryAndChunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, saveTableStatusErr)
		return saveTableStatusErr
	}

	gInit := &errgroup.Group{}
	gInit.SetLimit(migrationParam.GetTaskThreads())

	tableTypeMap := make(map[string]map[string]string)

	for _, tableID := range existTablesIds {
		channelSchemaTable := channelSchemaTableIdMap[tableID]

		if tableTypeMap[channelSchemaTable.SchemaNameS] == nil {
			tablesMap, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableType(ctx, dbConns.GetSourceDB().OracleDB, channelSchemaTable.SchemaNameS)
			if err != nil {
				log.Errorf("get oracle table type failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
			}
			tableTypeMap[channelSchemaTable.SchemaNameS] = tablesMap
		}

		log.Infof("start split oracle table [%s.%s] into chunks, channelId:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelId, taskId)

		if config.GetGlobalConfig().IsClusterMode() {
			gInit.Go(func() error {
				return SplitCSVChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		} else {
			gInit.Go(func() error {
				return SplitCSVChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		}
	}

	if waitErr := gInit.Wait(); waitErr != nil {
		log.Errorf("split oracle table into chunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, waitErr)
		return waitErr
	}
	log.Infof("meta database data init done, cost [%v], channelId:%d, taskId:%d", time.Now().Sub(startTime).String(), channelId, taskId)
	return nil
}

func validateCSVChunks(ctx context.Context, makeChunksParam ChunkMigrateParam) error {

	var (
		summaryCount, detailCount int64
		countErr                  error
	)

	summaryCount, countErr = models.GetFullDataMigrationReaderWriter().CountTableMigrationSummary(ctx, makeChunksParam.GetTaskId())
	if countErr != nil {
		log.Errorf("count table migration summary failed, taskId:%d, err:%v", makeChunksParam.GetTaskId(), countErr)
		return countErr
	}
	detailCount, countErr = models.GetFullDataMigrationReaderWriter().CountTableMigrationDetail(ctx, makeChunksParam.GetTaskId())
	if countErr != nil {
		log.Errorf("count table migration detail failed, taskId:%d, err:%v", makeChunksParam.GetTaskId(), countErr)
		return countErr
	}

	if summaryCount == 0 {
		err := fmt.Errorf("no migration summary meta found, taskId:%d", makeChunksParam.GetTaskId())
		log.Error(err)
		return err
	}

	if detailCount == 0 {
		err := fmt.Errorf("no migration detail meta found, taskId:%d", makeChunksParam.GetTaskId())
		log.Error(err)
		return err
	}
	return nil
}

func fetchTableChunksIntoCSVChunks(ctx context.Context, makeChunksParam ChunkMigrateParam) error {
	channelId := makeChunksParam.GetChannelId()
	taskId := makeChunksParam.GetTaskId()
	taskInfo := makeChunksParam.TaskInfo
	dbConns := makeChunksParam.DBConns
	migrationParam := makeChunksParam.MigrationParam
	channelSchemaObject := makeChunksParam.ChannelSchemaObject
	clearData := makeChunksParam.ClearData

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	// 1. 初始化任务
	// 2. 任务运行
	startTime := time.Now()

	channelSchemaTables, getErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getErr != nil {
		return getErr
	}
	log.Infof("get channel schema tables, channelId:%d, taskId:%d,len:%d", channelId, taskId, len(channelSchemaTables))

	var channelSchemaTableIds []int
	channelSchemaTableIdMap := make(map[int]*channel.ChannelSchemaTable)
	for _, channelSchemaTable := range channelSchemaTables {
		channelSchemaTableIds = append(channelSchemaTableIds, channelSchemaTable.ChannelSchtableId)
		channelSchemaTableIdMap[channelSchemaTable.ChannelSchtableId] = channelSchemaTable
	}

	log.Infof("get table exist status from oracle and tidb, channelId:%d, taskId:%d", channelId, taskId)
	lowerCase := strings.ToLower
	existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, getTableStatusErr :=
		commonpkg.GetTableStatusFromDataSource(ctx, channelSchemaTableIds, taskInfo, dbConns.GetSourceDS(), dbConns.GetTargetDS(), lowerCase, lowerCase)
	if getTableStatusErr != nil {
		log.Errorf("get table status failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, getTableStatusErr)
		return getTableStatusErr
	}
	log.Info("start csv data migration, taskId:%d, channelId:%d, oracleNotExistTables:%d, tidbNotExistTables:%d, existTables:%d",
		taskInfo.TaskID, taskInfo.ChannelId, len(oracleNotExistTables), len(tidbNotExistTables), len(existTables))

	saveTableStatusErr := saveNotExistTablesToSummaryAndChunks(ctx, "CSV", taskInfo, oracleNotExistTables, tidbNotExistTables, dbConns, migrationParam.GetChunkSize(), taskTableConfigMap)
	if saveTableStatusErr != nil {
		log.Errorf("saveNotExistTablesToSummaryAndChunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, saveTableStatusErr)
		return saveTableStatusErr
	}

	gInit := &errgroup.Group{}
	gInit.SetLimit(migrationParam.GetTaskThreads())

	tableTypeMap := make(map[string]map[string]string)

	for _, tableID := range existTablesIds {
		channelSchemaTable := channelSchemaTableIdMap[tableID]

		if tableTypeMap[channelSchemaTable.SchemaNameS] == nil {
			tablesMap, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableType(ctx, dbConns.GetSourceDB().OracleDB, channelSchemaTable.SchemaNameS)
			if err != nil {
				log.Errorf("get oracle table type failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
			}
			tableTypeMap[channelSchemaTable.SchemaNameS] = tablesMap
		}

		log.Infof("start fetch oracle table [%s.%s] chunks, channelId:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelId, taskId)

		if config.GetGlobalConfig().IsClusterMode() {
			gInit.Go(func() error {
				return FetchCSVChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		} else {
			gInit.Go(func() error {
				return FetchCSVChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		}
	}

	if waitErr := gInit.Wait(); waitErr != nil {
		log.Errorf("fetch table chunks from oracle-adg into chunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, waitErr)
		return waitErr
	}
	log.Infof("meta database data init done, cost [%v], channelId:%d, taskId:%d", time.Now().Sub(startTime).String(), channelId, taskId)
	return nil
}

func buildErrGroupGoroutineLimit(threadNum int) *errgroup.Group {
	gSummary := &errgroup.Group{}
	gSummary.SetLimit(threadNum)
	return gSummary
}

type ImportChunks struct {
	Details     []*migration.TableMigrationDetail
	CurrentStep int
	TotalStep   int

	DataDir   string
	AllChunks bool
}

func (i ImportChunks) IsAllChunks() bool {
	return i.AllChunks
}

func (i *CSVMigrationTask) SetCSVImportStageFinish(ctx context.Context, migrationSummary *migration.TableMigrationSummary) error {
	return models.GetFullDataMigrationReaderWriter().UpdateCSVStageBySchemaSTableS(ctx, i.ChannelId, i.TaskId, migrationSummary.SchemaNameS, migrationSummary.TableNameS, map[string]interface{}{
		"import_to_db":  true,
		"error_message": "",
	})
}

func (i *CSVMigrationTask) SetCSVImportStageFailed(ctx context.Context, migrationSummary *migration.TableMigrationSummary, errorMessage string) error {
	return models.GetFullDataMigrationReaderWriter().UpdateCSVStageBySchemaSTableS(ctx, i.ChannelId, i.TaskId, migrationSummary.SchemaNameS, migrationSummary.TableNameS, map[string]interface{}{
		"import_to_db":  false,
		"error_message": errorMessage,
	})
}

func (i *CSVMigrationTask) SetSummaryStatus(ctx context.Context, s *migration.TableMigrationSummary, status string, startExecTime time.Time) {
	_ = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(ctx,
		&migration.TableMigrationSummary{
			ID:          s.ID,
			TaskID:      s.TaskID,
			SchemaNameS: s.SchemaNameS,
			TableNameS:  s.TableNameS,
		}, map[string]interface{}{
			"Task_Status": status,
			"Duration":    time.Now().Sub(startExecTime).Seconds(),
		})
}

func buildCSVDataMigrationReq(ctx context.Context, taskInfo *task.Task, migrationDetail *migration.TableMigrationDetail, dbConns MigrationSourceConns, csvParam *structs.CSVMigrationConfigParam, columnNameS []string) (*o2t.Rows, error) {

	var globalScn uint64
	var parseErr error

	if csvParam.GetConsistentRead() {
		log.Infof("buildCSVDataMigrationReq, consistent read is on, taskId:%d, chunkId:%d, scn:%s", taskInfo.TaskID, migrationDetail.ID, taskInfo.ScnNumber)
		if taskInfo.ScnNumber == "" {
			log.Errorf("buildCSVDataMigrationReq, consistent read is on, but scn number is empty, taskId:%d, chunkId:%d", taskInfo.TaskID, migrationDetail.ID)
			return nil, errors.New("consistent read is on, but scn number is empty")
		}
		globalScn, parseErr = strconv.ParseUint(taskInfo.ScnNumber, 10, 64)
		if parseErr != nil {
			log.Errorf("buildCSVDataMigrationReq, parse scn number failed, taskId:%d, chunkId:%d, scn:%s, err:%v", taskInfo.TaskID, migrationDetail.ID, taskInfo.ScnNumber, parseErr)
			return nil, parseErr
		}
	}

	tableNameS := fmt.Sprintf(`"%v"`, migrationDetail.TableNameS)
	if migrationDetail.TablePartition != "" {
		tableNameS = fmt.Sprintf(`"%v" PARTITION(%v)`, migrationDetail.TableNameS, migrationDetail.TablePartition)
	}

	outputDir := csvParam.GetOutputDataDir(taskInfo.ChannelId, migrationDetail.TaskID)
	return o2t.NewRows(ctx,
		meta.FullSyncMeta{
			SchemaNameS:    fmt.Sprintf(`"%v"`, migrationDetail.SchemaNameS),
			TableNameS:     tableNameS,
			SchemaNameT:    migrationDetail.SchemaNameT,
			TableNameT:     migrationDetail.TableNameT,
			ConsistentRead: stringutil.BoolToYesOrNo(csvParam.GetConsistentRead()),
			SQLHint:        migrationDetail.SqlHintS,
			ColumnDetailS:  migrationDetail.ColumnDetailS,
			ChunkDetailS:   migrationDetail.ChunkDetailS,
			CSVFile:        buildCSVFilePath(csvParam, taskInfo, migrationDetail),
			GlobalScnS:     globalScn,
		},
		dbConns.GetSourceDB(),
		&transferconfig.Config{
			AppConfig: transferconfig.AppConfig{
				InsertBatchSize: csvParam.GetInsertBatchSize(),
			},
			OracleConfig: transferconfig.OracleConfig{
				Charset: transfercommon.StringUPPER(dbConns.GetSourceDS().Charset),
			},
			CSVConfig: transferconfig.CSVConfig{
				Header:          csvParam.GetHeader(),
				Separator:       csvParam.GetSeparator(),
				Terminator:      csvParam.GetTerminator(),
				Charset:         transfercommon.StringUPPER(csvParam.GetCharset()),
				Delimiter:       csvParam.GetDelimiter(),
				EscapeBackslash: csvParam.GetEscapeBackslash(),
				Rows:            int(csvParam.GetChunkSize()),
				OutputDir:       outputDir,
				TaskThreads:     csvParam.GetTaskThreads(),
				TableThreads:    csvParam.GetTableThreads(),
				SQLThreads:      csvParam.GetSqlThreads(),
				NullValue:       csvParam.GetMydumperCSVNull(),
			}},
		columnNameS,
		transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(dbConns.GetSourceDS().Charset)],
	), nil
}

func buildCSVFilePath(csvParam *structs.CSVMigrationConfigParam, taskInfo *task.Task, migrationDetail *migration.TableMigrationDetail) string {
	if filepath.IsAbs(migrationDetail.CSVFile) {
		return migrationDetail.CSVFile
	}
	return filepath.Join(csvParam.GetOutputDataDir(taskInfo.ChannelId, migrationDetail.TaskID), migrationDetail.CSVFile)
}

// createSingleCSVMigrationDetailAndSummary create single csv migration detail and update summary
func createSingleCSVMigrationDetailAndSummary(ctx context.Context,
	schemaTable *channel.ChannelSchemaTable,
	sourceDS *datasource.Datasource,
	sourceColumnInfo string,
	taskInfo *task.Task,
	customConf TableMigrationCustomConf,
	tableTypeS string,
) error {

	whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())
	log.Infof("table [%s.%s], disable split or statistics is empty or chunk size is zero, stop splitting, save record and return, taskId:%d, enableSplit:%v, wherePrefix:%s, whereRange:%s", schemaTable.SchemaNameS, schemaTable.TableNameS, taskInfo.TaskID, customConf.GetEnableSplit(), customConf.GetWherePrefix(), whereRange)

	csvStage := buildCSVStage(taskInfo, schemaTable)

	trxErr := models.Transaction(ctx, func(txnCtx context.Context) error {
		summary := buildMigrationSummary(taskInfo, sourceDS, schemaTable, NO_CHUNK_DEFAULT_CHUNK_SIZE, tableTypeS)
		summary.TableTypeS = tableTypeS
		detail := buildCSVMigrationDetail("0", taskInfo, schemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)

		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(txnCtx, detail); createErr != nil {
			return createErr
		}
		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(txnCtx, summary); createErr != nil {
			return createErr
		}
		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA { //如果是csv迁移任务，需要保存csv_stage
			if _, createErr := models.GetFullDataMigrationReaderWriter().SaveCSVStage(txnCtx, csvStage); createErr != nil {
				return createErr
			}
		}

		return nil
	})
	return trxErr
}

// createTableNotExistMigrationDetailAndSummary create migration details and summaries which error message is table not exist
func createTableNotExistMigrationDetailAndSummary(ctx context.Context, schemaTable *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, sourceColumnInfo string, taskInfo *task.Task, tableMigrationCustomConf TableMigrationCustomConf) error {
	log.Infof("table [%s.%s], not exist, create single detail and sumamry, and fill error message with 'table not exist', taskId:%d", schemaTable.SchemaNameS, schemaTable.TableNameS, taskInfo.TaskID)

	whereRange := buildSingleChunkWhereCondition(tableMigrationCustomConf.GetWherePrefix())

	csvStage := buildCSVStage(taskInfo, schemaTable)

	trxErr := models.Transaction(ctx, func(txnCtx context.Context) error {
		summary := buildMigrationSummary(taskInfo, sourceDS, schemaTable, tableMigrationCustomConf.GetChunkSize(), "")
		summary.TaskStatus = constants.MigrationStatusInvalid

		detail := buildCSVMigrationDetail("0", taskInfo, schemaTable, sourceDS, sourceColumnInfo, tableMigrationCustomConf.GetSourceSqlHint(), tableMigrationCustomConf.GetTargetSqlHint(), whereRange)
		detail.ErrorDetail = "oracle table " + schemaTable.SchemaNameS + "." + schemaTable.TableNameS + " table not exist"
		detail.TaskStatus = constants.MigrationStatusInvalid

		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(txnCtx, detail); createErr != nil {
			return createErr
		}
		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(txnCtx, summary); createErr != nil {
			return createErr
		}

		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA { //如果是csv迁移任务，需要保存csv_stage
			if _, createErr := models.GetFullDataMigrationReaderWriter().SaveCSVStage(txnCtx, csvStage); createErr != nil {
				return createErr
			}
		}

		return nil
	})
	return trxErr
}

func SplitCSVChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	splitErr := splitCSVChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeS)
	if splitErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], split csv data chunks in single mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)

	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeSplit, taskTableConfigMap, channelSchemaTable, dbConns, taskInfo, splitErr, tableTypeS)
	return splitErr
}

func splitCSVChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	sourceDB := dbConns.GetSourceDB()
	sourceDS := dbConns.GetSourceDS()
	oracleCollation := dbConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInSingleMode(ctx, taskInfo, channelSchemaTable, dbConns.GetTargetDB()); clearErr != nil {
			log.Errorf("table [%s.%s], clear migration old data failed, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, clearErr)
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, clearErr)
			return clearErr
		}
	}

	tableSummaries, tableDetails, getTableErr := getTableMigrationMetasBySchemaTable(ctx, channelSchemaTable)
	if getTableErr != nil {
		log.Errorf("table [%s.%s], get table migration metas failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getTableErr)
		createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, getTableErr)
		return getTableErr
	}

	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)

	if len(tableSummaries) != 0 || len(tableDetails) != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v, taskId:%d",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, migrationParam.GetChunkSize(), sourceDS)

	im := buildO2TCSVConfig(ctx, channelSchemaTable, sourceDS, migrationParam, sourceDB)

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		if strings.Contains(adjustErr.Error(), "column info cann't be null") {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			createErr := createTableNotExistMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf)
			if createErr != nil {
				createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
				return createErr
			}
			return nil
		} else {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, adjustErr)
			return adjustErr
		}
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, getStatisticsErr)
		return getStatisticsErr
	}

	// 退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 || !customConf.GetEnableSplit() {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		log.Errorf("table [%s.%s], create single csv migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		if createErr != nil {
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		}
		return createErr
	}

	chunkRes, chunkNum, splitErr := createSplitTaskAndGetChunkResults(sourceDB, channelSchemaTable, customConf.GetChunkSize(), migrationParam.GetCallTimeout(), migrationParam.GetRemoveSplitTask())
	if splitErr != nil {
		log.Errorf("table [%s.%s], split task and get chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		}
		return createErr
	}

	migrationDetails := buildMigrationDetails(chunkNum, chunkRes, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf)

	migrationSummary := buildDefaultMigrationSummaryWithChunkTotalNum(taskInfo, sourceDS, channelSchemaTable, int64(len(migrationDetails)), customConf.GetChunkSize(), tableTypeS)

	csvStage := buildCSVStage(taskInfo, channelSchemaTable)

	// 元数据库信息 开启事务写入
	trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
		_, batchGetErr := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(trxCtx, migrationDetails, migrationParam.GetInsertBatchSize())
		if batchGetErr != nil {
			return batchGetErr
		}
		_, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(trxCtx, migrationSummary)
		if createErr != nil {
			return createErr
		}
		_, saveErr := models.GetFullDataMigrationReaderWriter().SaveCSVStage(trxCtx, csvStage)
		if saveErr != nil {
			return saveErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("csv migration, meta database data init failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskInfo.TaskID, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, trxErr)
		createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, trxErr)
		return trxErr
	}
	return nil
}

func getTableMigrationMetasBySchemaTable(ctx context.Context, channelSchemaTable *channel.ChannelSchemaTable) ([]*migration.TableMigrationSummary, []migration.TableMigrationDetail, error) {
	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      channelSchemaTable.TaskId,
		SchemaNameS: channelSchemaTable.SchemaNameS,
		TableNameS:  channelSchemaTable.TableNameS,
	})
	if getSummaryErr != nil {
		return nil, nil, getSummaryErr
	}

	tableDetails, getDetailErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
		TaskID:      channelSchemaTable.TaskId,
		SchemaNameS: channelSchemaTable.SchemaNameS,
		TableNameS:  channelSchemaTable.TableNameS,
	})
	if getDetailErr != nil {
		return nil, nil, getDetailErr
	}
	return tableSummaries, tableDetails, nil
}

// 不划分Chunk，直接通过查询Oracle中是否已有Chunk信息，取出来，填写到table_migration_details中
func FetchCSVChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	fetchErr := fetchCSVChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeS)
	if fetchErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], fetch csv data chunks in single mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, fetchErr)
	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeFetch, taskTableConfigMap, channelSchemaTable, dbConns, taskInfo, fetchErr, tableTypeS)
	return fetchErr
}

func fetchCSVChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	sourceDB := dbConns.GetSourceDB()
	sourceDS := dbConns.GetSourceDS()
	oracleCollation := dbConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInSingleMode(ctx, taskInfo, channelSchemaTable, dbConns.GetTargetDB()); clearErr != nil {
			log.Errorf("table [%s.%s], clear migration old data failed, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, clearErr)
			return clearErr
		}
	}

	tableSummaries, tableDetails, getTableErr := getTableMigrationMetasBySchemaTable(ctx, channelSchemaTable)
	if getTableErr != nil {
		log.Errorf("table [%s.%s], get table migration metas failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getTableErr)
		return getTableErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
	if len(tableSummaries) != 0 || len(tableDetails) != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v, taskId:%d",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
		return nil
	}

	im := buildO2TCSVConfig(ctx, channelSchemaTable, sourceDS, migrationParam, sourceDB)

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, ADG_DEFAULT_CHUNK_SIZE, sourceDS)

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		if strings.Contains(adjustErr.Error(), "column info cann't be null") {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			createErr := createTableNotExistMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf)
			if createErr != nil {
				return createErr
			}
			return nil
		} else {
			return adjustErr
		}
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		return getStatisticsErr
	}

	// 退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		log.Errorf("table [%s.%s], create single csv migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		if createErr != nil {
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		}
		return createErr
	}

	chunkRes, chunkNum, fetchErr := fetchTableChunkResults(sourceDB, channelSchemaTable)
	if fetchErr != nil {
		log.Errorf("table [%s.%s], fetch table chunk results failed, err: %v, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, fetchErr, taskInfo.TaskID)
		return fetchErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		return createErr
	}

	migrationDetails := buildMigrationDetails(chunkNum, chunkRes, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf)

	migrationSummary := buildDefaultMigrationSummaryWithChunkTotalNum(taskInfo, sourceDS, channelSchemaTable, int64(len(migrationDetails)), ADG_DEFAULT_CHUNK_SIZE, tableTypeS)

	csvStage := buildCSVStage(taskInfo, channelSchemaTable)

	// 元数据库信息 开启事务写入
	trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
		_, batchSaveErr := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(trxCtx, migrationDetails, migrationParam.GetInsertBatchSize())
		if batchSaveErr != nil {
			return batchSaveErr
		}
		_, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(trxCtx, migrationSummary)
		if createErr != nil {
			return createErr
		}
		_, saveErr := models.GetFullDataMigrationReaderWriter().SaveCSVStage(trxCtx, csvStage)
		if saveErr != nil {
			return saveErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("csv migration, meta database data init failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskInfo.TaskID, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, trxErr)
		return trxErr
	}
	return nil
}

func buildMigrationDetails(chunkNum int, chunkRes []map[string]string, taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, sourceColumnInfo string, customConf TableMigrationCustomConf) []*migration.TableMigrationDetail {
	migrationDetails := make([]*migration.TableMigrationDetail, 0, chunkNum)
	chunkNumDigits := parse.CountDigits(chunkNum)
	for idx, res := range chunkRes {
		rowIdRange, combineWhereRange := buildChunkWhereCondition(res, customConf.GetWherePrefix())
		log.Debugf("taskId:%d, table:%s, rowIdRange:%s, wherePrefix:%s, combineWhereRange:%s", taskInfo.TaskID, channelSchemaTable.TableNameS, rowIdRange, customConf.GetWherePrefix(), combineWhereRange)
		paddedIndex := parse.ZeroPad(idx, chunkNumDigits)
		detail := buildCSVMigrationDetail(paddedIndex, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), combineWhereRange)
		migrationDetails = append(migrationDetails, detail)
	}
	return migrationDetails
}

func buildCSVStage(taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable) *migration.CSVStage {
	csvStage := &migration.CSVStage{
		ChannelId:   taskInfo.ChannelId,
		TaskId:      taskInfo.TaskID,
		SchemaNameS: channelSchemaTable.SchemaNameS,
		TableNameS:  channelSchemaTable.TableNameS,
		ExportToCSV: false,
		ImportToDB:  false,
	}
	return csvStage
}

func buildO2TCSVConfig(ctx context.Context, channelSchemaTable *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, migrationParam *structs.CSVMigrationConfigParam, sourceDB *oracle.Oracle) *o2t.CSV {
	im := &o2t.CSV{
		Ctx: ctx,
		Cfg: &transferconfig.Config{
			SchemaConfig: transferconfig.SchemaConfig{SourceSchema: channelSchemaTable.SchemaNameS},
			AppConfig:    transferconfig.AppConfig{InsertBatchSize: migrationParam.GetInsertBatchSize()},
			OracleConfig: transferconfig.OracleConfig{Charset: sourceDS.Charset},
			MySQLConfig:  transferconfig.MySQLConfig{Charset: migrationParam.GetCharset()},
		},
		Oracle: sourceDB,
	}
	return im
}

//
//func (i *CSVMigrationTask) BatchRetryCSVMigrationByTableFailed(ctx context.Context, summaries []*migration.TableMigrationSummary) error {
//	taskInfo := i.TaskInfo
//	channelInfo := i.ChannelInfo
//	taskId := i.TaskInfo.TaskID
//	channelId := i.TaskInfo.ChannelId
//	csvParam:= i.CSVParam
//
//	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
//	if buildErr != nil {
//		return buildErr
//	}
//
//	// init transferdb logger
//	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeCSV, channelId, taskId)
//	additionalLoggerHelper.InitAdditionalLogger(logFileName)
//
//	sourceDBConns, setUpErr := SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
//	if setUpErr != nil {
//		log.Errorf("set up oracle database conns failed, taskId:%d, err:%v", taskId)
//		return setUpErr
//	}
//
//	migrationDBConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
//	if setUpErr != nil {
//		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
//		return setUpErr
//	}
//
//	outputDataDir := csvParam.GetOutputDataDir(channelId, taskId)
//	mkdirErr := fileutil.CreateIfNotExist(outputDataDir)
//	if mkdirErr != nil {
//		log.Errorf("create output data dir failed, taskId:%d, outputDataDir:%s, err:%v", taskId, outputDataDir, mkdirErr)
//		return fmt.Errorf("create output data dir failed, outputDataDir:%s, err:%v", outputDataDir, mkdirErr)
//	}
//
//	// 重运行失败表 chunk 的任务 failed
//	gS := &errgroup.Group{}
//	gS.SetLimit(csvParam.GetTableThreads())
//
//	for _, summary := range summaries {
//		d := summary
//
//		summaryId := d.ID
//		schemaName := d.SchemaNameS
//		tableName := d.TableNameS
//
//		gS.Go(func() error {
//			startExecTime := time.Now()
//
//			waitingAndFailedDetails, err := getWaitingAndFailedMigrationDetails(ctx, summary)
//			if err != nil {
//				log.Errorf("get waiting and failed migration details failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, err)
//				return err
//			}
//
//			progressHelper := buildProgressHelper(taskInfo.ChannelId, taskInfo.TaskID, d.SchemaNameS, d.TableNameS, constants.MigrationLogTypeCSV)
//			progressHelper.SetTotal(len(waitingAndFailedDetails))
//			progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
//			defer progressHelper.Close()
//			defer progressHelper.DisplayAndSaveProgress(ctx)
//
//			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
//				if !strings.EqualFold(d.TaskStatus, transfercommon.TaskStatusRunning) {
//					err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
//						TaskID:      d.TaskID,
//						SchemaNameS: d.SchemaNameS,
//						TableNameS:  d.TableNameS,
//					}, map[string]interface{}{
//						"Task_Status": transfercommon.TaskStatusRunning,
//					})
//					if err != nil {
//						return err
//					}
//				}
//
//				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
//					ChannelID:   taskInfo.ChannelId,
//					TaskID:      d.TaskID,
//					SchemaNameS: d.SchemaNameS,
//					TableNameS:  d.TableNameS,
//					Detail:      constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, migration retry initiated", d.SchemaNameS, d.TableNameS)),
//					LogLevel:    log.LogInfo,
//				})
//				if err != nil {
//					return fmt.Errorf("create progress log detail record failed: %v", err)
//				}
//				return nil
//			})
//			if err != nil {
//				log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
//				return err
//			}
//
//			if len(waitingAndFailedDetails) == 0 {
//				log.Warnf("table [%s.%s], task:%d, all failed chunks retry success", d.SchemaNameS, d.TableNameS, taskInfo.TaskID)
//				return nil
//			}
//			g := &errgroup.Group{}
//			g.SetLimit(csvParam.GetSqlThreads())
//
//			for idx, detail := range waitingAndFailedDetails {
//				migrationDetail := &(waitingAndFailedDetails[idx])
//
//				chunkId := migrationDetail.ID
//				chunk := migrationDetail.ChunkDetailS
//
//				g.Go(func() error {
//					chunkTime := time.Now()
//
//					errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
//						ID: detail.ID,
//					}, map[string]interface{}{
//						"Task_Status": transfercommon.TaskStatusRunning,
//						"Duration":    time.Now().Sub(chunkTime).Seconds(),
//					})
//					if errU != nil {
//						return fmt.Errorf("UpdateTableMigrationDetailById [%v] chunk status failed: %v", detail.String(), errU)
//					}
//
//					getColumnsSQL := BuildGetColumnSQL(migrationDetail)
//					columnNameS, getColumnErr := sourceDBConns.GetSourceDB().GetOracleTableRowsColumnCSV(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[sourceDBConns.GetSourceDS().Charset],
//						transfercommon.StringUPPER(csvParam.GetCharset()))
//					if getColumnErr != nil {
//						progressHelper.IncreaseFailedNum()
//						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)
//						log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
//							taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
//						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
//						if errC != nil {
//							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
//							return errC
//						} else {
//							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName)
//						}
//						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
//						if errS != nil {
//							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
//							return errS
//						} else {
//							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName)
//						}
//						return nil
//					}
//
//					if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
//						SchemaName: schemaName,
//						TableName:  tableName,
//					}]; ok {
//						if taskTableConfig.OperatorTag == "Y" {
//							oracleCollation := migrationDBConns.GetOracleCollation()
//							oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
//							if err != nil {
//								log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
//								return err
//							}
//							migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
//						}
//						if taskTableConfig.TablePartition != "" {
//							migrationDetail.TablePartition = taskTableConfig.TablePartition
//						}
//					}
//
//					rows, buildErr := buildCSVDataMigrationReq(ctx, taskInfo, migrationDetail, sourceDBConns, csvParam, columnNameS)
//					if buildErr != nil {
//						progressHelper.IncreaseFailedNum()
//						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)
//
//						log.Errorf("buildFullDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//							taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
//						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
//						if errC != nil {
//							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
//							return errC
//						} else {
//							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName)
//						}
//						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
//						if errS != nil {
//							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
//							return errS
//						} else {
//							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName)
//						}
//						return nil
//					}
//
//					migrateErr := doDataMigration(ctx, transferDBMigrateCSVFunc, taskInfo.ChannelId, migrationDetail, rows, progressHelper, constants.MigrationLogTypeCSV)
//					if migrateErr != nil {
//						log.Errorf("migration retry failed, taskId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err: %v",
//							taskId, chunkId, chunk, schemaName, tableName, migrateErr)
//						progressHelper.IncreaseFailedNum()
//						log.Errorf("get oracle table rows column failed, summaryId:%d, taskId:%d, schemaName:%s, tableName:%s, err:%v", d.ID, d.TaskID, d.SchemaNameS, d.TableNameS, err)
//						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, migrateErr.Error(), constants.MigrationLogTypeCSV)
//						if errC != nil {
//							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
//							return errC
//						} else {
//							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName)
//						}
//						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
//						if errS != nil {
//							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
//							return errS
//						} else {
//							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
//								taskId, summaryId, chunkId, chunk, schemaName, tableName)
//						}
//						return nil
//					}
//					return nil
//				})
//			}
//			if waitErr := g.Wait(); waitErr != nil {
//				log.Errorf("BatchRetryCSVMigrationTaskTable failed, taskId:%d, err:%v", taskInfo.TaskID, waitErr)
//				return waitErr
//			}
//			// 更新详情以及日志
//			_, _, tryErr := trySetCSVMigrationSummaryFailed(ctx, d, taskInfo, int(d.ChunkTotalNums), startExecTime)
//			if tryErr != nil {
//				log.Errorf("trySetCSVMigrationSummaryFailed failed, taskId:%d, err:%v", taskInfo.TaskID, tryErr)
//				return tryErr
//			}
//			return nil
//		})
//	}
//
//	if waitErr := gS.Wait(); waitErr != nil {
//		log.Errorf("BatchRetryCSVMigrationTaskTable failed, taskId:%d, err:%v", taskInfo.TaskID, waitErr)
//		return waitErr
//	}
//	return nil
//}

func buildCSVMigrationDetail(idx string, taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, sourceColumnInfo string, sourceSqlHint string, targetSqlHint string, whereRange string) *migration.TableMigrationDetail {
	csvFile := filepath.Join(
		transfercommon.StringUPPER(channelSchemaTable.SchemaNameS),
		transfercommon.StringUPPER(channelSchemaTable.TableNameS),
		transfercommon.StringsBuilder(channelSchemaTable.SchemaNameT,
			`.`,
			channelSchemaTable.TableNameT,
			`.`,
			idx, ".csv"))

	detail := &migration.TableMigrationDetail{
		TaskID:        taskInfo.TaskID,
		ServiceNameS:  sourceDS.ServiceName,
		DBNameS:       channelSchemaTable.DbNameS,
		SchemaNameS:   channelSchemaTable.SchemaNameS,
		TableNameS:    channelSchemaTable.TableNameS,
		SchemaNameT:   channelSchemaTable.SchemaNameT,
		TableNameT:    channelSchemaTable.TableNameT,
		ColumnDetailS: sourceColumnInfo,
		SqlHintS:      sourceSqlHint,
		SqlHintT:      targetSqlHint,
		ChunkDetailS:  whereRange,
		TaskStatus:    transfercommon.TaskStatusWaiting,
		CSVFile:       csvFile,
	}
	return detail
}
