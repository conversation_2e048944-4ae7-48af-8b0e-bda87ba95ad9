package migration

import (
	"context"
	"fmt"
	"sort"

	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/samber/lo"
)

// ChunkMetaHelper for C2T only, not for O2T(CSV)
type ChunkMetaHelper struct {
	walkPaths          []string
	expectedCsvFileNum int
}

func NewChunkMetaHelper(walkPaths []string, expectedCsvFileNum int) ChunkMetaHelper {
	sort.Strings(walkPaths)
	return ChunkMetaHelper{
		walkPaths:          walkPaths,
		expectedCsvFileNum: expectedCsvFileNum,
	}
}

func (i *ChunkMetaHelper) WalkForValidCSVFile() ([]message.CSVFileInfo, error) {
	infos, walkErr := file.WalkForCSVFiles(i.walkPaths)
	if walkErr != nil {
		return nil, walkErr
	}

	infos = lo.Filter(infos, func(fi message.CSVFileInfo, _ int) bool {
		return fi.IsValid
	})

	if len(infos) != i.expectedCsvFileNum {
		err := tmserrors.NewError(tmserrors.TMS_MIGRATION_CSV_FILE_NUM_NOT_MATCH, fmt.Sprintf("walk %v, found %d csv files, not %d", i.walkPaths, len(infos), i.expectedCsvFileNum))
		return nil, err
	}
	return infos, nil
}

func (i *ChunkMetaHelper) ExtractValidTableProperties(csvFileInfos []message.CSVFileInfo) []message.CSVTableProperty {
	tablePropertyMap := make(map[message.CSVTableProperty]int)
	for _, csvFileInfo := range csvFileInfos {
		tp := *csvFileInfo.TableProperty
		if _, ok := tablePropertyMap[tp]; !ok {
			tablePropertyMap[tp] = 0
		}
		tablePropertyMap[tp]++
	}

	tableProperties := make([]message.CSVTableProperty, 0, len(tablePropertyMap))
	for tableProperty, csvFileNum := range tablePropertyMap {
		if !tableProperty.IsValid {
			continue
		}
		tableProperties = append(tableProperties, message.CSVTableProperty{
			SchemaNameT: tableProperty.GetSchemaNameT(),
			TableNameT:  tableProperty.GetTableNameT(),
			CSVFileNum:  csvFileNum,
			IsValid:     tableProperty.IsValid,
		})
	}
	return tableProperties
}

func (i *ChunkMetaHelper) BuildChannelSchemaTables(taskInfo *task.Task, ps []message.CSVTableProperty) []*channel.ChannelSchemaTable {
	channelSchemaTables := make([]*channel.ChannelSchemaTable, 0, len(ps))
	channelSchemaMap := make(map[channel.ChannelSchema]bool)
	for _, p := range ps {
		channelSchemaTables = append(channelSchemaTables, &channel.ChannelSchemaTable{
			ChannelId:   taskInfo.ChannelId,
			TaskId:      taskInfo.TaskID,
			TaskType:    taskInfo.TaskType,
			SchemaNameS: p.GetSchemaNameT(),
			TableNameS:  p.GetTableNameT(),
			SchemaNameT: p.GetSchemaNameT(),
			TableNameT:  p.GetTableNameT(),
		})
		channelSchemaMap[channel.ChannelSchema{
			ChannelId:   taskInfo.ChannelId,
			TaskId:      taskInfo.TaskID,
			DbNameS:     "",
			SchemaNameS: p.GetSchemaNameT(),
			DbNameT:     "",
			SchemaNameT: p.GetSchemaNameT(),
		}] = true
	}

	return channelSchemaTables
}

func (i *ChunkMetaHelper) BuildChannelSchemas(taskInfo *task.Task, ps []message.CSVTableProperty, sources *MigrationDBSources) []*channel.ChannelSchema {
	channelSchemaMap := make(map[channel.ChannelSchema]bool)
	for _, p := range ps {
		channelSchemaMap[channel.ChannelSchema{
			ChannelId:   taskInfo.ChannelId,
			TaskId:      taskInfo.TaskID,
			DbNameS:     sources.GetSourceDS().DbName,
			SchemaNameS: p.GetSchemaNameT(),
			DbNameT:     sources.GetTargetDS().DbName,
			SchemaNameT: p.GetSchemaNameT(),
		}] = true
	}

	channelSchemas := make([]*channel.ChannelSchema, 0, len(channelSchemaMap))
	for channelSchema := range channelSchemaMap {
		channelSchemas = append(channelSchemas, &channelSchema)
	}
	return channelSchemas
}

func (i *ChunkMetaHelper) BuildMigrationSummaries(taskInfo *task.Task, ps []message.CSVTableProperty) []*migration.TableMigrationSummary {
	summaries := make([]*migration.TableMigrationSummary, 0, len(ps))
	for _, p := range ps {
		summaries = append(summaries, &migration.TableMigrationSummary{
			TaskID:           taskInfo.TaskID,
			SchemaNameS:      p.GetSchemaNameT(),
			TableNameS:       p.GetTableNameT(),
			TableTypeS:       "HEAP",
			SchemaNameT:      p.GetSchemaNameT(),
			TableNameT:       p.GetTableNameT(),
			ChunkSize:        NO_CHUNK_DEFAULT_CHUNK_SIZE,
			TaskStatus:       transfercommon.TaskStatusWaiting,
			ChunkTotalNums:   p.GetCSVFileNum(),
			ChunkSuccessNums: p.GetCSVFileNum(),
		})
	}
	return summaries
}

func (i *ChunkMetaHelper) BuildCSVStages(taskInfo *task.Task, ps []message.CSVTableProperty) []*migration.CSVStage {
	summaries := make([]*migration.CSVStage, 0, len(ps))
	for _, p := range ps {
		summaries = append(summaries, &migration.CSVStage{
			ChannelId:   taskInfo.ChannelId,
			TaskId:      taskInfo.TaskID,
			SchemaNameS: p.GetSchemaNameT(),
			TableNameS:  p.GetTableNameT(),
			ExportToCSV: true,
			ImportToDB:  false,
		})
	}
	return summaries
}

func (i *ChunkMetaHelper) BuildMigrationDetails(taskInfo *task.Task, ps []message.CSVFileInfo) []*migration.TableMigrationDetail {
	summaries := make([]*migration.TableMigrationDetail, 0, len(ps))
	for _, p := range ps {
		summaries = append(summaries, &migration.TableMigrationDetail{
			TaskID:       taskInfo.TaskID,
			SchemaNameS:  p.TableProperty.GetSchemaNameT(),
			TableNameS:   p.TableProperty.GetTableNameT(),
			SchemaNameT:  p.TableProperty.GetSchemaNameT(),
			TableNameT:   p.TableProperty.GetTableNameT(),
			TaskStatus:   transfercommon.TaskStatusSuccess,
			ImportStatus: ChunkStatusWaiting,
			ImportError:  "",
			CSVFile:      p.FilePath,
			CSVSize:      p.FileSize,
		})
	}
	return summaries
}

func (i *ChunkMetaHelper) HasChannelSchemaTable(ctx context.Context, taskInfo *task.Task) (bool, error) {
	num, err := models.GetChannelReaderWriter().GetChannelSchemaTablesCountsByTask(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if err != nil {
		return false, err
	}
	return num > 0, nil
}

func (i *ChunkMetaHelper) HasMigrationDetail(ctx context.Context, taskInfo *task.Task) (bool, error) {
	num, err := models.GetFullDataMigrationReaderWriter().CountTableMigrationDetail(ctx, taskInfo.TaskID)
	if err != nil {
		return false, err
	}
	return num > 0, nil
}

func (i *ChunkMetaHelper) HasMigrationSummary(ctx context.Context, taskInfo *task.Task) (bool, error) {
	num, err := models.GetFullDataMigrationReaderWriter().CountTableMigrationSummary(ctx, taskInfo.TaskID)
	if err != nil {
		return false, err
	}
	return num > 0, nil
}

func (i *ChunkMetaHelper) ValidateTaskMigrationMetadata(ctx context.Context, taskInfo *task.Task) error {
	log.Infof("validate task migration metadata [detail / summary] is exist, taskId:%d", taskInfo.TaskID)
	hasMigrationDetail, err := i.HasMigrationDetail(ctx, taskInfo)
	if err != nil {
		return err
	}
	if hasMigrationDetail {
		return tmserrors.NewError(tmserrors.TMS_MIGRATION_CSV_METADATA_EXIST, "migration detail exist")
	}

	hasMigrationSummary, err := i.HasMigrationSummary(ctx, taskInfo)
	if err != nil {
		return err
	}
	if hasMigrationSummary {
		return tmserrors.NewError(tmserrors.TMS_MIGRATION_CSV_METADATA_EXIST, "migration summary exist")
	}
	return nil
}

// 检查新的channel schema table是否包含在旧的channel schema table中，判断条件仅为schemaNameT和tableNameT
// 如果新的channel schema table不包含在旧的channel schema table中，则返回错误
func (i *ChunkMetaHelper) CheckChannelSchemaTables(ctx context.Context, oldChannelSchemaTables, newChannelSchemaTables []*channel.ChannelSchemaTable) error {
	oldChannelSchemaTableMap := make(map[structs.SchemaTablePair]bool)
	for _, oldChannelSchemaTable := range oldChannelSchemaTables {
		oldChannelSchemaTableMap[structs.SchemaTablePair{
			SchemaName: oldChannelSchemaTable.SchemaNameT,
			TableName:  oldChannelSchemaTable.TableNameT,
		}] = true
	}

	for _, newChannelSchemaTable := range newChannelSchemaTables {
		if _, ok := oldChannelSchemaTableMap[structs.SchemaTablePair{
			SchemaName: newChannelSchemaTable.SchemaNameT,
			TableName:  newChannelSchemaTable.TableNameT,
		}]; !ok {
			return tmserrors.NewError(tmserrors.TMS_MIGRATION_CSV_TABLE_NOT_MATCH, fmt.Sprintf("table [%s.%s] not match", newChannelSchemaTable.SchemaNameT, newChannelSchemaTable.TableNameT))
		}
	}
	return nil
}
