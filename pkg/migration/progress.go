package migration

import (
	"context"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/hpcloud/tail"
)

type ProgressHelper struct {
	channelId  int
	taskId     int
	schemaName string
	tableName  string

	total      int
	finishNum  int
	failedNum  int
	runningNum int

	ticker          *time.Ticker
	lock            sync.RWMutex
	progressLogType string
	lastLogDetail   *common.ProgressLogDetail

	lightningLogFile        string
	latestLightningProgress *LatestLightningProgress
	tailedFile              *tail.Tail
}

func (i *ProgressHelper) SetLightningLogFile(logFile string) {
	i.lightningLogFile = logFile
}

func buildProgressHelper(channelId, taskId int, schemaName, tableName string, progressLogType string) *ProgressHelper {
	return buildProgressHelperWithTicker(channelId, taskId, schemaName, tableName, time.NewTicker(time.Duration(5)*time.Second), progressLogType)
}

func buildProgressHelperWithTicker(channelId, taskId int, schemaName, tableName string, t *time.Ticker, progressLogType string) *ProgressHelper {
	return &ProgressHelper{
		channelId:               channelId,
		taskId:                  taskId,
		schemaName:              schemaName,
		tableName:               tableName,
		ticker:                  t,
		lock:                    sync.RWMutex{},
		progressLogType:         progressLogType,
		latestLightningProgress: &LatestLightningProgress{lock: sync.RWMutex{}},
	}
}

func (i *ProgressHelper) DisplayAndSaveProgressInTimeLoop(ctx context.Context) {
	go func() {
		for {
			select {
			case <-i.ticker.C:
				log.Infof("migration, channelId %d, task %d, total %d, finish %d, running %d, failed %d", i.channelId, i.taskId, i.total, i.finishNum, i.runningNum, i.failedNum)
				logMessage := fmt.Sprintf("%s.%s table, in progress [Completed %d/Running %d/Failed %d/TotalNum %d]", i.schemaName, i.tableName, i.finishNum, i.runningNum, i.failedNum, i.total)
				if i.finishNum == i.total && i.total != 0 {
					logMessage = fmt.Sprintf("%s.%s table, migration chunks completed [Completed %d/Running %d/Failed %d/TotalNum %d]", i.schemaName, i.tableName, i.finishNum, i.runningNum, i.failedNum, i.total)
				}

				progressLog := i.createProgressLog(constants.BuildProgressLog(i.progressLogType, constants.MigrationStepMigrateTableRunning, logMessage), log.LogInfo)
				err := i.createOrUpdateProgressLog(ctx, progressLog)
				if err != nil {
					log.Errorf("migration running, channelId %d, task %d, create progress log failed, err %v", i.channelId, i.taskId, err)
				}
				i.ticker.Reset(20 * time.Second)
			}
		}
	}()
}

type LatestLightningProgress struct {
	lock sync.RWMutex
	p    *structs.LightningProgress
}

func (i *LatestLightningProgress) SetProgress(progress *structs.LightningProgress) {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.p = progress
}

func (i *LatestLightningProgress) GetProgress() *structs.LightningProgress {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.p
}

func (i *ProgressHelper) TailAndSaveProgressInTimeLoop(ctx context.Context) {
	log.Infof("prepare to tail log file:%v", i.lightningLogFile)

	t, err := tail.TailFile(i.lightningLogFile, tail.Config{Follow: true, ReOpen: true})
	if err != nil {
		log.Errorf("tail lightning log file failed, err:%v", err)
		return
	}
	i.tailedFile = t

	go func() {
		log.Infof("tailing log file:%v", i.lightningLogFile)
		for line := range t.Lines {
			lineText := line.Text
			if !i.containsProgress(lineText) {
				continue
			}
			log.Infof("get latest lightning log, logFile:%s, line:%v", i.lightningLogFile, line)
			progressLog := parse.ParseLightningProgressLog(lineText)
			i.latestLightningProgress.SetProgress(progressLog)
			_, saveErr := models.GetFullDataMigrationReaderWriter().SaveLightningProgress(ctx, i.buildLightningProgress(progressLog))
			if saveErr != nil {
				log.Errorf("save lightning progress failed, err:%v", saveErr)
				return
			}
		}
	}()

	go func() {
		// log total, ignoreNum, explainedNum in every ticker cycle, and exit while exitChan has content
		for {
			select {
			case <-i.ticker.C:
				progressLog := i.latestLightningProgress.GetProgress()
				if progressLog != nil {
					logMessage := fmt.Sprintf("%s.%s table, importing，total=%v，remaining=%v, state=%v", i.schemaName, i.tableName, progressLog.Total, progressLog.Remaining, progressLog.State)
					newProgressLog := i.createProgressLog(constants.BuildProgressLog(i.progressLogType, constants.MigrationStepLightningImportRunning, logMessage), log.LogInfo)
					createErr := i.createOrUpdateProgressLog(ctx, newProgressLog)
					if createErr != nil {
						log.Errorf("lightning importing, channelId %d, task %d, create progress log failed, err %v", i.channelId, i.taskId, createErr)
						return
					}
				}
				i.ticker.Reset(20 * time.Second)
			}
		}
	}()

	// 等到上游的context被cancel
	log.Infof("waiting for tail context done, %s", i.lightningLogFile)
	for {
		select {
		case <-ctx.Done():
			log.Infof("context done, stop tail log file:%v", i.lightningLogFile)
			t.Stop()
			return
		}
	}
}
func (i *ProgressHelper) createOrUpdateProgressLog(ctx context.Context, logDetail *common.ProgressLogDetail) error {
	i.lock.Lock()
	defer i.lock.Unlock()

	if i.lastLogDetail == nil {
		logDetail, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, logDetail)
		if createErr != nil {
			return createErr
		}
		i.lastLogDetail = logDetail
	} else {
		nowTime := time.Now()
		i.lastLogDetail.Detail = logDetail.Detail
		i.lastLogDetail.CreatedAt = nowTime
		i.lastLogDetail.UpdatedAt = nowTime
		log.Infof("update progress log id:%d", logDetail.ProgressID)
		updateErr := models.GetProgressLogReaderWriter().UpdateProgressLogDetailTimeAndMessageByProgressID(ctx, i.lastLogDetail)
		if updateErr != nil {
			return updateErr
		}
	}
	return nil
}

func (i *ProgressHelper) DisplayAndSaveProgress(ctx context.Context) {
	log.Infof("migration, channelId %d, task %d, total %d, finish %d, running %d, failed %d", i.channelId, i.taskId, i.total, i.finishNum, i.runningNum, i.failedNum)
	logMessage := fmt.Sprintf("%s.%s table, in progress [Completed %d/Running %d/Failed %d/TotalNum %d]", i.schemaName, i.tableName, i.finishNum, i.runningNum, i.failedNum, i.total)
	if i.finishNum == i.total && i.total != 0 {
		logMessage = fmt.Sprintf("%s.%s table, migration chunks completed [Completed %d/Running %d/Failed %d/TotalNum %d]", i.schemaName, i.tableName, i.finishNum, i.runningNum, i.failedNum, i.total)
	}

	newProgressLog := i.createProgressLog(constants.BuildProgressLog(i.progressLogType, constants.MigrationStepMigrateTableRunning, logMessage), log.LogInfo)
	createErr := i.createOrUpdateProgressLog(ctx, newProgressLog)
	if createErr != nil {
		log.Errorf("migration running, channelId %d, task %d, create progress log failed, err %v", i.channelId, i.taskId, createErr)
	}
	return
}

func (i *ProgressHelper) TailAndSaveProgress(ctx context.Context) {
	log.Infof("reopen log file to fetch last progress log :%v", i.lightningLogFile)
	content, err := os.ReadFile(i.lightningLogFile)
	if err != nil {
		log.Errorf("open lightning log file failed, err:%v", err)
		return
	}

	var lastProgressLog string
	var completeMessage string
	var logMessage string
	for _, line := range strings.Split(string(content), "\n") {
		if containsCompleteMessage(line) {
			completeMessage = line
		}
		if !i.containsProgress(line) {
			continue
		}
		lastProgressLog = line
	}

	if completeMessage != "" {
		logMessage = fmt.Sprintf("%s.%s table, import completed=100%%", i.schemaName, i.tableName)
		// 防止lightning进程退出后，没有写入最后一条日志
		_, _ = models.GetFullDataMigrationReaderWriter().SaveLightningProgress(ctx, &migration.LightningProgress{
			ChannelID:   i.channelId,
			TaskID:      i.taskId,
			Time:        timeutil.GetNowTime().Format("2006-01-02 15:04:05"),
			LogLevel:    "INFO",
			Total:       "100%",
			SchemaNameS: i.schemaName,
			TableNameS:  i.tableName,
		})
	} else {
		progressLog := parse.ParseLightningProgressLog(lastProgressLog)
		if progressLog.Total == "100%" || progressLog.Total == "100.0%" || progressLog.Total == "100.00%" {
			logMessage = fmt.Sprintf("%s.%s, table import completed，total=%v, state=%v", i.schemaName, i.tableName, progressLog.Total, progressLog.State)
			_, _ = models.GetFullDataMigrationReaderWriter().SaveLightningProgress(ctx, &migration.LightningProgress{
				ChannelID:    i.channelId,
				TaskID:       i.taskId,
				Time:         timeutil.GetNowTime().Format("2006-01-02 15:04:05"),
				LogLevel:     "INFO",
				Total:        "100%",
				Tables:       progressLog.Tables,
				Chunks:       progressLog.Chunks,
				Engines:      progressLog.Engines,
				RestoreBytes: progressLog.RestoreBytes,
				RestoreRows:  progressLog.RestoreRows,
				ImportBytes:  progressLog.ImportBytes,
				EncodeSpeed:  progressLog.EncodeSpeed,
				State:        progressLog.State,
				Remaining:    progressLog.Remaining,
				SchemaNameS:  i.schemaName,
				TableNameS:   i.tableName,
			})
		} else {
			logMessage = fmt.Sprintf("%s.%s table, import exit，total=%v, state=%v", i.schemaName, i.tableName, progressLog.Total, progressLog.State)
		}
	}

	newProgressLog := i.createProgressLog(constants.BuildProgressLog(i.progressLogType, constants.MigrationStepLightningImportRunning, logMessage), log.LogInfo)
	createErr := i.createOrUpdateProgressLog(ctx, newProgressLog)
	if createErr != nil {
		log.Errorf("lightning importing, channelId %d, task %d, create progress log failed, err %v", i.channelId, i.taskId, createErr)
	}
}

func (i *ProgressHelper) createProgressLog(message string, logLevel string) *common.ProgressLogDetail {
	return &common.ProgressLogDetail{
		ChannelID:   i.channelId,
		TaskID:      i.taskId,
		SchemaNameS: i.schemaName,
		TableNameS:  i.tableName,
		Detail:      message,
		LogLevel:    logLevel,
	}
}

func (i *ProgressHelper) Close() {
	if i.ticker != nil {
		i.ticker.Stop()
	}
	if i.tailedFile != nil {
		_ = i.tailedFile.Stop()
	}
}

func (i *ProgressHelper) SetTotal(total int) {
	i.total = total
}

func (i *ProgressHelper) IncreaseFinishNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.finishNum++
}

func (i *ProgressHelper) IncreaseFailedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.failedNum++
}

func (i *ProgressHelper) DecreaseFinishNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.finishNum--
}

func (i *ProgressHelper) ResetFinishNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.finishNum = 0
}

func (i *ProgressHelper) DecreaseFailedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.failedNum--
}

func (i *ProgressHelper) ResetFailedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.failedNum = 0
}

func (i *ProgressHelper) IncreaseRunningNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.runningNum++
}

func (i *ProgressHelper) ResetRunningNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.runningNum = 0
}

func (i *ProgressHelper) DecreaseRunningNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.runningNum--
}

func (i *ProgressHelper) containsProgress(s string) bool {
	return strings.Contains(s, "[progress]")
}

func containsCompleteMessage(s string) bool {
	return strings.Contains(s, "the whole procedure completed")
}
