package migration

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringUtil "gitee.com/pingcap_enterprise/tms/util/string"
)

// ChunkFetchConfig 获取配置
type ChunkFetchConfig struct {
	TaskID      int
	SchemaNameS string
	TableNameS  string
	DeleteFirst bool // 是否先删除（ReFetch场景）
}

// ChunkFetchProgress 获取进度
type ChunkFetchProgress struct {
	TotalChunks     int
	ProcessedChunks int
	TotalRowIDs     int64
	SavedRowIDs     int64
	Status          string
	StartTime       time.Time
	EndTime         *time.Time
}

// ChunkFetchOrchestrator 编排器，负责整体流程控制
type ChunkFetchOrchestrator struct {
	config      *ChunkFetchConfig
	dbConns     *MigrationSourceDBConns
	extractor   *ChunkRowIDExtractor
	persister   *ChunkDataPersister
	taskInfo    *task.Task
	channelInfo *channel.ChannelInformation
	progress    *ChunkFetchProgress
}

// NewChunkFetchOrchestrator 创建编排器
func NewChunkFetchOrchestrator(config *ChunkFetchConfig) *ChunkFetchOrchestrator {
	return &ChunkFetchOrchestrator{
		config: config,
		progress: &ChunkFetchProgress{
			Status:    constants.ChunkDataStatusWaiting,
			StartTime: time.Now(),
		},
	}
}

// Initialize 初始化编排器
func (o *ChunkFetchOrchestrator) Initialize(ctx context.Context) error {
	// 获取任务信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, o.config.TaskID)
	if err != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}
	o.taskInfo = taskInfo

	// 获取通道信息
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel info failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}
	o.channelInfo = channelInfo

	// 设置数据库连接
	dbConns, err := SetUpOracleDatabaseConns(ctx, channelInfo, o.config.TaskID)
	if err != nil {
		log.Errorf("set up oracle database conns failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}
	o.dbConns = dbConns

	// 初始化子组件
	o.extractor = NewChunkRowIDExtractor(dbConns)
	o.persister = NewChunkDataPersister(500) // 批量大小500

	return nil
}

// Validate 验证是否可以执行
func (o *ChunkFetchOrchestrator) Validate(ctx context.Context) error {
	// 检查是否已有数据
	count, err := models.GetFullDataMigrationReaderWriter().CountChunkDataAnalyze(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)
	if err != nil {
		log.Errorf("count chunk data analyze failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return err
	}

	if !o.config.DeleteFirst && count != 0 {
		return errors.NewError(errors.TMS_MIGRATION_CHUNK_ANALYZE_EXIST, fmt.Sprintf("total:%d", count))
	}

	// 检查是否有失败的chunks
	details, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS,
		[]string{transfercommon.TaskStatusFailed})
	if err != nil {
		log.Errorf("get table migration detail failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return err
	}
	
	if len(details) == 0 {
		return errors.NewError(errors.TMS_MIGRATION_CHUNK_NOT_FOUND, "no failed chunks")
	}

	o.progress.TotalChunks = len(details)
	
	// 更新数据库中的总数
	err = models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeProgress(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, int64(len(details)), 0)
	if err != nil {
		log.Errorf("update chunk data analyze progress failed, taskId:%d, err:%v", o.config.TaskID, err)
		// 不影响继续执行
	}
	
	return nil
}

// PrepareForReFetch 为重新获取准备（删除现有数据）
func (o *ChunkFetchOrchestrator) PrepareForReFetch(ctx context.Context) error {
	if !o.config.DeleteFirst {
		return nil
	}

	log.Infof("Deleting existing chunk data, taskId:%d, schemaNameS:%s, tableNameS:%s",
		o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)

	deleteErr := models.GetFullDataMigrationReaderWriter().BatchDeleteChunkDataAnalyze(ctx, o.config.TaskID)
	if deleteErr != nil {
		log.Errorf("delete chunk data analyze failed, taskId:%d, err:%v", o.config.TaskID, deleteErr)
		return deleteErr
	}

	return nil
}

// UpdateStatus 更新状态
func (o *ChunkFetchOrchestrator) UpdateStatus(ctx context.Context, status string) error {
	// 确保summary记录存在
	_, err := models.GetFullDataMigrationReaderWriter().GetChunkDataAnalyzeSummary(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)
	if err != nil {
		if strings.Contains(err.Error(), "record not found") {
			// 创建新记录
			_, err = models.GetFullDataMigrationReaderWriter().SaveChunkDataAnalyzeSummary(ctx, &migration.ChunkDataAnalyzeSummary{
				ChannelId:   o.channelInfo.ChannelId,
				TaskId:      o.config.TaskID,
				SchemaNameS: o.config.SchemaNameS,
				TableNameS:  o.config.TableNameS,
				Status:      status,
			})
			if err != nil {
				log.Errorf("save chunk data analyze summary failed, taskId:%d, err:%v", o.config.TaskID, err)
				return err
			}
		} else {
			log.Errorf("get chunk data analyze summary failed, taskId:%d, err:%v", o.config.TaskID, err)
			return err
		}
	}

	// 更新状态
	err = models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeSummary(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, status)
	if err != nil {
		log.Errorf("update chunk data analyze summary failed, taskId:%d, status:%s, err:%v",
			o.config.TaskID, status, err)
		return err
	}

	o.progress.Status = status
	return nil
}

// ExecuteAsync 异步执行获取
func (o *ChunkFetchOrchestrator) ExecuteAsync(ctx context.Context) {
	// 使用独立的context避免被取消
	bgCtx := context.Background()

	go func() {
		requestID := fmt.Sprintf("fetch_%d_%s_%s_%d", o.config.TaskID, o.config.SchemaNameS,
			o.config.TableNameS, time.Now().Unix())
		log.Infof("[%s] Starting chunk data fetch, taskId:%d, schemaNameS:%s, tableNameS:%s",
			requestID, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)

		defer func() {
			// 更新状态为已完成
			if err := o.UpdateStatus(bgCtx, constants.ChunkDataStatusFetched); err != nil {
				log.Errorf("[%s] Failed to update status to fetched: %v", requestID, err)
			}
			
			// 更新最终进度
			if err := models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeProgress(
				bgCtx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, 
				int64(o.progress.TotalChunks), int64(o.progress.ProcessedChunks)); err != nil {
				log.Errorf("[%s] Failed to update final progress: %v", requestID, err)
			}

			endTime := time.Now()
			o.progress.EndTime = &endTime
			duration := endTime.Sub(o.progress.StartTime)
			log.Infof("[%s] Chunk data fetch completed, taskId:%d, duration:%v, totalRowIDs:%d",
				requestID, o.config.TaskID, duration, o.progress.SavedRowIDs)
		}()

		// 执行获取逻辑
		if err := o.doFetch(bgCtx, requestID); err != nil {
			log.Errorf("[%s] Fetch failed: %v", requestID, err)
		}
	}()
}

// doFetch 执行获取核心逻辑
func (o *ChunkFetchOrchestrator) doFetch(ctx context.Context, requestID string) error {
	// 获取失败的chunks
	details, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS,
		[]string{transfercommon.TaskStatusFailed})
	if err != nil {
		return err
	}

	// 遍历每个失败的chunk，提取ROWID
	for idx, detail := range details {
		log.Infof("[%s] Processing chunk %d/%d, chunkId:%d", 
			requestID, idx+1, len(details), detail.ID)

		// 提取ROWID
		rowIDs, err := o.extractor.ExtractRowIDs(ctx, &detail, requestID)
		if err != nil {
			log.Errorf("[%s] Extract ROWIDs failed for chunk %d: %v", requestID, detail.ID, err)
			continue
		}

		if len(rowIDs) == 0 {
			log.Infof("[%s] No ROWIDs found for chunk %d", requestID, detail.ID)
			continue
		}

		log.Infof("[%s] Extracted %d ROWIDs from chunk %d", requestID, len(rowIDs), detail.ID)

		// 构建ChunkDataAnalyze记录
		chunkDataList := o.buildChunkDataAnalyzeList(rowIDs, &detail)

		// 批量保存
		if err := o.persister.SaveChunkData(ctx, chunkDataList, requestID); err != nil {
			log.Errorf("[%s] Save chunk data failed for chunk %d: %v", requestID, detail.ID, err)
			continue
		}

		o.progress.ProcessedChunks++
		o.progress.TotalRowIDs += int64(len(rowIDs))
		o.progress.SavedRowIDs += int64(len(chunkDataList))
		
		// 更新数据库中的进度
		err = models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeProgress(
			ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, 
			int64(o.progress.TotalChunks), int64(o.progress.ProcessedChunks))
		if err != nil {
			log.Errorf("[%s] Update progress in database failed: %v", requestID, err)
			// 不影响继续执行
		}
	}

	return nil
}

// buildChunkDataAnalyzeList 构建ChunkDataAnalyze列表
func (o *ChunkFetchOrchestrator) buildChunkDataAnalyzeList(rowIDs []string, detail *migration.TableMigrationDetail) []migration.ChunkDataAnalyze {
	var chunkDataList []migration.ChunkDataAnalyze
	for _, rowID := range rowIDs {
		chunkData := migration.ChunkDataAnalyze{
			ChannelId:   o.channelInfo.ChannelId,
			TaskId:      o.config.TaskID,
			SchemaNameS: detail.SchemaNameS,
			TableNameS:  detail.TableNameS,
			ChunkId:     detail.ID,
			RowIDStr:    rowID,
			RowIDMD5:    stringUtil.MD5(rowID),
			Status:      "WAITING",
		}
		chunkDataList = append(chunkDataList, chunkData)
	}
	return chunkDataList
}

// GetProgress 获取当前进度
func (o *ChunkFetchOrchestrator) GetProgress() *ChunkFetchProgress {
	return o.progress
}

// ChunkRowIDExtractor 负责从Oracle提取ROWID
type ChunkRowIDExtractor struct {
	sourceDB *MigrationSourceDBConns
}

// NewChunkRowIDExtractor 创建提取器
func NewChunkRowIDExtractor(sourceDB *MigrationSourceDBConns) *ChunkRowIDExtractor {
	return &ChunkRowIDExtractor{sourceDB: sourceDB}
}

// ExtractRowIDs 提取ROWID
func (e *ChunkRowIDExtractor) ExtractRowIDs(ctx context.Context, chunk *migration.TableMigrationDetail, requestID string) ([]string, error) {
	// 构建查询SQL
	// 注意：这里仍然使用字符串拼接，因为chunk.ChunkDetailS是预定义的WHERE条件
	// 在实际使用中，应该确保chunk.ChunkDetailS是安全的
	querySQL := fmt.Sprintf(`SELECT ROWID FROM %s.%s WHERE %s`, 
		chunk.SchemaNameS, chunk.TableNameS, chunk.ChunkDetailS)
	
	log.Infof("[%s] Extracting ROWIDs with SQL: %s", requestID, querySQL)

	// 执行查询
	_, rows, err := oracle.Query(ctx, e.sourceDB.GetSourceDB().OracleDB, querySQL)
	if err != nil {
		log.Errorf("[%s] Query ROWIDs failed: %v", requestID, err)
		return nil, err
	}

	// 提取ROWID值
	var rowIDs []string
	for _, row := range rows {
		if rowID, ok := row["ROWID"]; ok && rowID != "" {
			rowIDs = append(rowIDs, rowID)
		}
	}

	return rowIDs, nil
}

// ChunkDataPersister 负责数据持久化
type ChunkDataPersister struct {
	batchSize int
}

// NewChunkDataPersister 创建持久化器
func NewChunkDataPersister(batchSize int) *ChunkDataPersister {
	return &ChunkDataPersister{batchSize: batchSize}
}

// SaveChunkData 批量保存chunk数据
func (p *ChunkDataPersister) SaveChunkData(ctx context.Context, chunkDataList []migration.ChunkDataAnalyze, requestID string) error {
	if len(chunkDataList) == 0 {
		return nil
	}

	log.Infof("[%s] Saving %d chunk data records in batches of %d", 
		requestID, len(chunkDataList), p.batchSize)

	err := models.GetFullDataMigrationReaderWriter().BatchSaveChunkDataAnalyze(ctx, chunkDataList, p.batchSize)
	if err != nil {
		log.Errorf("[%s] Batch save chunk data analyze failed: %v", requestID, err)
		return err
	}

	log.Infof("[%s] Successfully saved %d chunk data records", requestID, len(chunkDataList))
	return nil
}