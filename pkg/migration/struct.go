package migration

import "gitee.com/pingcap_enterprise/tms/common/structs"

type FailedChunkBatchRetryReq struct {
	TaskID      int
	SchemaNameS string
	TableNameS  string
}

type LightningBasicDir struct {
	BaseDir     string
	TomlConfDir string
	PbDir       string
	LogDir      string
	workingDir  string
}

func (i LightningBasicDir) GetWorkingDir() string {
	return i.workingDir
}
func (i LightningBasicDir) GetBaseDir() string {
	return i.BaseDir
}
func (i LightningBasicDir) GetTomlConfDir() string {
	return i.TomlConfDir
}
func (i LightningBasicDir) GetPbDir() string {
	return i.PbDir
}
func (i LightningBasicDir) GetLogDir() string {
	return i.LogDir
}

type LightningDirConf struct {
	Basic       *LightningBasicDir
	DataDir     string
	SortedKvDir string
}

func (i LightningDirConf) GetLogDir() string {
	return i.Basic.GetLogDir()
}
func (i LightningDirConf) GetBaseDir() string {
	return i.Basic.GetBaseDir()
}
func (i LightningDirConf) GetTomlConfDir() string {
	return i.Basic.GetTomlConfDir()
}
func (i LightningDirConf) GetPbDir() string {
	return i.Basic.GetPbDir()
}

func (i LightningDirConf) GetDataDir() string {
	return i.DataDir
}
func (i LightningDirConf) GetSortedKvDir() string {
	return i.SortedKvDir
}

type LightningFileConf struct {
	Dirs *LightningDirConf

	LogFile       string
	LogStdOutFile string

	TomlFile        string
	TomlFileContent *structs.LightningConfiguration
}

func (i LightningFileConf) GetSortedKvDir() string {
	return i.Dirs.GetSortedKvDir()
}
func (i LightningFileConf) GetDataDir() string {
	return i.Dirs.GetDataDir()
}
func (i LightningFileConf) GetBaseDir() string {
	return i.Dirs.GetBaseDir()
}

func (i LightningFileConf) GetLogDir() string {
	return i.Dirs.GetLogDir()
}
func (i LightningFileConf) GetPbDir() string {
	return i.Dirs.GetPbDir()
}

func (i LightningFileConf) GetTomlConfDir() string {
	return i.Dirs.GetTomlConfDir()
}

type TableMigrationCustomConf struct {
	sourceSqlHint string
	targetSqlHint string
	wherePrefix   string
	enableSplit   bool
	chunkSize     int64
}

func (i TableMigrationCustomConf) GetSourceSqlHint() string {
	return i.sourceSqlHint
}

func (i TableMigrationCustomConf) GetTargetSqlHint() string {
	return i.targetSqlHint
}
func (i TableMigrationCustomConf) GetWherePrefix() string {
	return i.wherePrefix
}
func (i TableMigrationCustomConf) GetEnableSplit() bool {
	return i.enableSplit
}
func (i TableMigrationCustomConf) GetChunkSize() int64 {
	return i.chunkSize
}
