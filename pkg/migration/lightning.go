package migration

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	commonerrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"gitee.com/pingcap_enterprise/tms/util/versioninfo"
)

var defaultSchemaTablePattern = `(["_a-zA-Z0-9]+)\/(["_a-zA-Z0-9]+)\/%s\.%s\.(\d+)\.csv`

var defaultDumperFile = structs.MyDumperFile{
	Pattern: `(["_a-zA-Z0-9]+)\/(["_a-zA-Z0-9]+)\/(["_a-zA-Z0-9]+)\.(["_a-zA-Z0-9]+)\.(\d+)\.csv`,
	Schema:  "$3",
	Table:   "$4",
	Type:    "csv",
}

// GetDefaultLightningConfiguration returns the default configuration for lightning
func GetDefaultLightningConfiguration() *structs.LightningConfiguration {
	return &structs.LightningConfiguration{
		Lightning: structs.Lightning{
			Level:             "info",
			MetaSchemaName:    "lightning_metadata",
			CheckRequirements: true,
			IndexConcurrency:  2,
			TableConcurrency:  6,
		},
		TiKVImporter: structs.TiKVImporter{
			Backend:             "local",
			IncrementalImport:   true,
			DuplicateResolution: "none",
			DiskQuota:           "10G",
		},
		MyDumper: structs.MyDumper{
			ReadBlockSizeSize:      "64KiB",
			BatchImportRatio:       0.75,
			CharacterSet:           "auto",
			DataCharacterSet:       "binary",
			DataInvalidCharReplace: `\uFFFD`,
			StrictFormat:           false,
			Filter:                 []string{"*.*", "!mysql.*", "!sys.*", "!INFORMATION_SCHEMA.*", "!PERFORMANCE_SCHEMA.*", "!METRICS_SCHEMA.*", "!INSPECTION_SCHEMA.*"},
			Files:                  []structs.MyDumperFile{defaultDumperFile},
			CSV: structs.MyDumperCSV{
				Header:            true,
				HeaderSchemaMatch: true,
				NotNull:           false,
				Null:              `\N`,
				BackslashEscape:   true,
				TrimLastSeparator: false,
			},
		},
		Checkpoint: structs.Checkpoint{
			Enable: false,
		},
		Tidb: structs.Tidb{
			Port:       4000,
			StatusPort: 10080,
			LogLevel:   "info",
		},
		PostRestore: structs.PostRestore{
			Checksum:       "off",
			ChecksumViaSQL: false,
			Analyze:        "off",
		},
		Cron: structs.Cron{
			SwitchMode:     "5m",
			LogProgress:    "5s",
			CheckDiskQuota: "30s",
		},
	}

}

// NewLightningConfiguration with options
func NewLightningConfiguration(options ...structs.OptionLightningConfiguration) *structs.LightningConfiguration {
	conf := GetDefaultLightningConfiguration()
	for _, option := range options {
		option(conf)
	}
	return conf
}

func CreateAndSaveTaskLightningTomlFile(channelInfo *channel.ChannelInformation, taskInfo *task.Task, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, isRetry, hasPrimaryUniqueKey bool, table *channel.ChannelSchemaTable) (string, string, string, error) {
	// get pwd
	workingDir, getWdErr := os.Getwd()
	if getWdErr != nil {
		log.Errorf("get pwd failed, err:%v", getWdErr)
		return "", "", "", getWdErr
	}

	dataRootDir := param.GetOutputDataRootDir(channelInfo.ChannelId, taskInfo.TaskID)
	dataDir := param.GetOutputDataDir(channelInfo.ChannelId, taskInfo.TaskID)
	sortedKvDir := path.Join(param.GetOutputSortedKVDir(channelInfo.ChannelId, taskInfo.TaskID), fmt.Sprintf("%s.%s", table.SchemaNameS, table.TableNameS))

	// create dataDir is not exists
	createDirErr := file.CreateIfNotExist(dataDir)
	if createDirErr != nil {
		log.Errorf("create dataDir failed, taskId:%d, dir:%s, err:%v", taskInfo.TaskID, dataDir, createDirErr)
		return "", "", "", createDirErr
	}

	// create sortedKvDir is not exists
	createDirErr = file.CreateIfNotExist(sortedKvDir)
	if createDirErr != nil {
		log.Errorf("create sortedKvDir failed, taskId:%d, dir:%s, err:%v", taskInfo.TaskID, sortedKvDir, createDirErr)
		return "", "", "", createDirErr
	}

	logDir := filepath.Join(workingDir, config.GetGlobalConfig().DataDir, "logs", "import_csv")
	createLogErr := file.CreateIfNotExist(logDir)
	if createLogErr != nil {
		log.Errorf("create import_csv log folder failed, taskId:%d, dir:%s, err:%v", taskInfo.TaskID, logDir, createLogErr)
		return "", "", "", createLogErr
	}

	logFile := filepath.Join(logDir, fmt.Sprintf("lightning-%d-%d.log", channelInfo.ChannelId, taskInfo.TaskID))
	logStdOutFile := filepath.Join(logDir, fmt.Sprintf("lightning-%d-%d.stdout.log", channelInfo.ChannelId, taskInfo.TaskID))

	var (
		lightningConfigContent *structs.LightningConfiguration
		lightningConfigFile    string
	)

	// 正常流程（不是重试），则导入模式为CSV；没有PK/UK，在任何阶段，都是CSV
	if isRetry && hasPrimaryUniqueKey {
		lightningConfigFile, lightningConfigContent = createTaskLightningSQLConf(dataRootDir, logFile, sortedKvDir, dataDir, tidbDatabaseSource, param, table.SchemaNameT, table.TableNameT)
	} else {
		lightningConfigFile, lightningConfigContent = createTaskLightningCSVConf(dataRootDir, logFile, sortedKvDir, dataDir, tidbDatabaseSource, param, table.SchemaNameT, table.TableNameT)
	}

	confStr, marshalErr := lightningConfigContent.MarshalTOML()
	if marshalErr != nil {
		log.Errorf("marshal conf as toml failed, taskId:%d, err:%v", taskInfo.TaskID, marshalErr)
		return "", "", "", marshalErr
	}

	log.Infof("create lightning config file: %s", lightningConfigFile)
	createErr := file.CreateOrReplaceFile(lightningConfigFile, confStr)
	if createErr != nil {
		log.Errorf("create conf file failed, err:%v", createErr)
		return "", "", "", createErr
	}

	log.Infof("create lightning log file: %s", logFile)
	createLogFileErr := file.CreateOrReplaceFile(logFile, "")
	if createLogFileErr != nil {
		log.Errorf("create empty log file failed, err:%v", createLogFileErr)
		return "", "", "", createLogFileErr
	}

	return lightningConfigFile, logFile, logStdOutFile, nil
}

type LightningHelper struct {
	ChannelId, TaskId int
	IsRetry           bool
}

func NewLightningHelper(channelId, taskId int, isRetry bool) *LightningHelper {
	return &LightningHelper{
		ChannelId: channelId,
		TaskId:    taskId,
		IsRetry:   isRetry,
	}
}

func (i *LightningHelper) buildConfigName(baseDir, schemaNameS, tableNameS string, importChunks *ImportChunks) string {
	if !i.IsRetry {
		if importChunks.IsAllChunks() {
			return path.Join(baseDir, fmt.Sprintf("/lightning-%s.%s.toml", schemaNameS, tableNameS))
		} else {
			return path.Join(baseDir, fmt.Sprintf("/lightning-%s.%s_%d_%d.toml", schemaNameS, tableNameS, importChunks.CurrentStep, importChunks.TotalStep))
		}
	} else {
		if importChunks.IsAllChunks() {
			return path.Join(baseDir, fmt.Sprintf("/lightning-retry-%s.%s.toml", schemaNameS, tableNameS))
		} else {
			return path.Join(baseDir, fmt.Sprintf("/lightning-retry-%s.%s_%d_%d.toml", schemaNameS, tableNameS, importChunks.CurrentStep, importChunks.TotalStep))
		}
	}
}

type SchemaTable struct {
	TableNameS  string
	TableNameT  string
	SchemaNameS string
	SchemaNameT string
}

func (i *LightningHelper) buildLightningDirs(schemaTable SchemaTable, param *structs.CSVMigrationConfigParam, importChunks *ImportChunks) (*LightningDirConf, error) {
	channelId, taskId := i.ChannelId, i.TaskId
	schemaName, tableName := schemaTable.SchemaNameS, schemaTable.TableNameS

	basicDir, getDirErr := i.buildBasicDir(param)
	if getDirErr != nil {
		log.Errorf("buildBasicDir failed, taskId:%d, err:%v", taskId, getDirErr)
		return nil, getDirErr
	}

	dataDir := i.buildDataDir(importChunks, param, channelId, taskId, basicDir.GetWorkingDir())
	sortedKvDir := i.buildSortedKvDir(schemaTable, param, importChunks, channelId, taskId, schemaName, tableName, basicDir.GetWorkingDir())

	dirs := &LightningDirConf{
		DataDir:     dataDir,
		SortedKvDir: sortedKvDir,
		Basic:       basicDir,
	}
	return dirs, nil
}

func (i *LightningHelper) buildBasicDir(param *structs.CSVMigrationConfigParam) (*LightningBasicDir, error) {
	channelId, taskId := i.ChannelId, i.TaskId

	workingDir, err := os.Getwd()
	if err != nil {
		log.Errorf("get pwd failed, err:%v", err)
		return nil, err
	}
	baseDir := param.GetOutputDataRootDir(channelId, taskId)
	if !filepath.IsAbs(baseDir) {
		baseDir = filepath.Join(workingDir, baseDir)
	}

	logDir := i.buildLogDir(workingDir)
	pbDir := i.buildPbDir(baseDir)
	tomlConfDir := i.buildTomlConfDir(baseDir)

	dirs := &LightningBasicDir{
		BaseDir: baseDir,

		TomlConfDir: tomlConfDir,
		PbDir:       pbDir,
		LogDir:      logDir,
		workingDir:  workingDir,
	}
	return dirs, nil
}

func (i *LightningHelper) buildLogDir(workingDir string) string {
	return filepath.Join(workingDir, config.GetGlobalConfig().DataDir, "logs", "import_csv")
}

func (i *LightningHelper) buildPbDir(baseDir string) string {
	return path.Join(baseDir, "pb")
}

func (i *LightningHelper) buildTomlConfDir(baseDir string) string {
	return path.Join(baseDir, "toml")
}

func (i *LightningHelper) buildSortedKvDir(schemaTable SchemaTable, param *structs.CSVMigrationConfigParam, importChunks *ImportChunks, channelId int, taskId int, schemaName string, tableName string, workingDir string) string {
	var sortedKvDir string
	if !importChunks.IsAllChunks() {
		sortedKvDir = filepath.Join(param.GetOutputSortedKVDir(channelId, taskId), fmt.Sprintf("%s.%s_%d_%d", schemaName, tableName, importChunks.CurrentStep, importChunks.TotalStep))
		if !filepath.IsAbs(sortedKvDir) {
			sortedKvDir = filepath.Join(workingDir, sortedKvDir)
		}
	} else {
		sortedKvDir = filepath.Join(param.GetOutputSortedKVDir(channelId, taskId), fmt.Sprintf("%s.%s", schemaTable.SchemaNameS, schemaTable.TableNameS))
		if !filepath.IsAbs(sortedKvDir) {
			sortedKvDir = filepath.Join(workingDir, sortedKvDir)
		}
	}
	return sortedKvDir
}

func (i *LightningHelper) buildDataDir(importChunks *ImportChunks, param *structs.CSVMigrationConfigParam, channelId int, taskId int, workingDir string) string {
	var dataDir string
	if !importChunks.IsAllChunks() {
		dataDir = importChunks.DataDir
	} else {
		dataDir = param.GetOutputDataDir(channelId, taskId)
		if !filepath.IsAbs(dataDir) {
			dataDir = filepath.Join(workingDir, dataDir)
		}
	}
	return dataDir
}

// buildLightningFileConfByDetails 需要根据detail里的CSV文件名，生成对应的lightning配置文件
func (i *LightningHelper) buildLightningFileConfByDetails(table SchemaTable, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, importChunks *ImportChunks) (LightningFileConf, error) {
	channelId, taskId := i.ChannelId, i.TaskId
	schemaName, tableName := table.SchemaNameT, table.TableNameT

	dirs, getDirErr := i.buildLightningDirs(table, param, importChunks)
	if getDirErr != nil {
		log.Errorf("buildLightningDirs failed, taskId:%d, err:%v", taskId, getDirErr)
		return LightningFileConf{}, getDirErr
	}

	logFile := filepath.Join(dirs.GetLogDir(), i.buildLogName(channelId, taskId, schemaName, tableName, importChunks))
	logStdOutFile := filepath.Join(dirs.GetLogDir(), i.buildLogStdOutName(channelId, taskId, schemaName, tableName, importChunks))

	checkpointFile := i.buildCheckpointFileName(dirs.GetPbDir(), schemaName, tableName, importChunks)

	tomlFile := i.buildConfigName(dirs.GetTomlConfDir(), schemaName, tableName, importChunks)
	tomlFileContent := i.buildConfigToml(logFile, dirs.SortedKvDir, dirs.DataDir, tidbDatabaseSource, param, schemaName, tableName, checkpointFile, importChunks)

	lightningFiles := LightningFileConf{
		Dirs:            dirs,
		LogFile:         logFile,
		LogStdOutFile:   logStdOutFile,
		TomlFile:        tomlFile,
		TomlFileContent: tomlFileContent,
	}
	return lightningFiles, nil
}

func (i *LightningHelper) buildLogStdOutName(channelId int, taskId int, schemaName string, tableName string, importChunks *ImportChunks) string {
	if importChunks.IsAllChunks() {
		return fmt.Sprintf("lightning-%d-%d-%s.%s.stdout.log", channelId, taskId, schemaName, tableName)
	} else {
		return fmt.Sprintf("lightning-%d-%d-%s.%s_%d_%d.stdout.log", channelId, taskId, schemaName, tableName, importChunks.CurrentStep, importChunks.TotalStep)
	}
}

func (i *LightningHelper) buildLogName(channelId int, taskId int, schemaName string, tableName string, importChunks *ImportChunks) string {
	if importChunks.IsAllChunks() {
		return fmt.Sprintf("lightning-%d-%d-%s.%s.log", channelId, taskId, schemaName, tableName)
	} else {
		return fmt.Sprintf("lightning-%d-%d-%s.%s_%d_%d.log", channelId, taskId, schemaName, tableName, importChunks.CurrentStep, importChunks.TotalStep)
	}
}

func createTaskLightningCSVConf(baseDir string, logFile string, sortedKvDir string, dataSourceDir string, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, schemaName, tableName string) (string, *structs.LightningConfiguration) {
	checkpointFile := getLightningCheckpointFileName(baseDir)
	configFile := getLightningTomlConfigFileName(baseDir)

	options := getLightningDefaultOptions(logFile, sortedKvDir, dataSourceDir, tidbDatabaseSource, param, schemaName, tableName)

	if param.GetTiKVImporterBackend() == "tidb" {
		options = append(options, structs.OptionCSVTiKVImporterBackend(param.GetTiKVImporterBackend()))
		options = append(options, structs.OptionCSVTiKVImporterIncrementalImport(false))                        // tidb backend not support incremental import
		options = append(options, structs.OptionCSVTiKVImporterOnDuplicate(param.GetTiKVImporterOnDuplicate())) // only for tidb backend
	} else {
		options = append(options, structs.OptionCSVTiKVImporterBackend(param.GetTiKVImporterBackend()))
		options = append(options, structs.OptionCSVTiKVImporterIncrementalImport(param.GetTiKVImporterIncrementalImport()))
		options = append(options, structs.OptionTiKVImporterStoreWriteBWLimit(param.GetTiKVImporterStoreWriteBWLimit()))
		options = append(options, structs.OptionTiKVImporterRangeConcurrency(param.GetTiKVImporterRangeConcurrency()))
	}

	if param.GetCheckpointDriver() == "mysql" {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
	} else {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
		options = append(options, structs.OptionCheckpointDSN(checkpointFile)) // only for file driver
	}

	lightningConf := NewLightningConfiguration(options...)
	return configFile, lightningConf
}

func (i *LightningHelper) buildNormalConf(lightningFileConf LightningFileConf, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, schemaName, tableName string) (string, *structs.LightningConfiguration) {
	baseDir, logFile, sortedKvDir, dataSourceDir := lightningFileConf.GetBaseDir(), lightningFileConf.LogFile, lightningFileConf.Dirs.SortedKvDir, lightningFileConf.Dirs.DataDir

	importChunks := &ImportChunks{AllChunks: true}
	checkpointFile := i.buildCheckpointFileName(baseDir, schemaName, tableName, importChunks)
	configFile := i.buildConfigName(baseDir, schemaName, tableName, importChunks)

	return configFile, i.buildConfigToml(logFile, sortedKvDir, dataSourceDir, tidbDatabaseSource, param, schemaName, tableName, checkpointFile, nil)
}

func (i *LightningHelper) buildConfigToml(logFile, sortedKvDir, dataSourceDir string, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, schemaNameT, tableNameT, checkpointFile string, importChunks *ImportChunks) *structs.LightningConfiguration {
	options := getLightningDefaultOptions(logFile, sortedKvDir, dataSourceDir, tidbDatabaseSource, param, schemaNameT, tableNameT)

	options = append(options, i.buildImporterBackendOptions(param, options)...)
	options = append(options, i.buildCheckpointOptions(param, checkpointFile)...)

	if !importChunks.IsAllChunks() {
		fs := i.buildMydumperFilesOption(importChunks, schemaNameT, tableNameT)
		options = append(options, structs.OptionMydumperFiles(fs))
	}

	lightningConf := NewLightningConfiguration(options...)
	return lightningConf
}

func (i *LightningHelper) buildImporterBackendOptions(param *structs.CSVMigrationConfigParam, options []structs.OptionLightningConfiguration) []structs.OptionLightningConfiguration {
	if i.IsRetry || param.GetTiKVImporterBackend() == "tidb" {
		options = append(options, structs.OptionCSVTiKVImporterBackend("tidb"))
		options = append(options, structs.OptionCSVTiKVImporterIncrementalImport(false))                        // tidb backend not support incremental import
		options = append(options, structs.OptionCSVTiKVImporterOnDuplicate(param.GetTiKVImporterOnDuplicate())) // only for tidb backend
	} else {
		options = append(options, structs.OptionCSVTiKVImporterBackend("local"))
		options = append(options, structs.OptionCSVTiKVImporterIncrementalImport(param.GetTiKVImporterIncrementalImport()))
		options = append(options, structs.OptionTiKVImporterStoreWriteBWLimit(param.GetTiKVImporterStoreWriteBWLimit()))
		options = append(options, structs.OptionTiKVImporterRangeConcurrency(param.GetTiKVImporterRangeConcurrency()))
	}
	return options
}

func (i *LightningHelper) buildCheckpointOptions(param *structs.CSVMigrationConfigParam, checkpointFile string) []structs.OptionLightningConfiguration {
	options := make([]structs.OptionLightningConfiguration, 0)
	if param.GetCheckpointDriver() == "mysql" {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
	} else {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
		options = append(options, structs.OptionCheckpointDSN(checkpointFile)) // only for file driver
	}
	return options
}

func (i *LightningHelper) buildMydumperFilesOption(importChunks *ImportChunks, schemaNameT string, tableNameT string) []structs.MyDumperFile {
	fs := make([]structs.MyDumperFile, 0, len(importChunks.Details))
	for _, detail := range importChunks.Details {
		f := structs.MyDumperFile{
			Pattern: i.escapeRegexDots(detail.CSVFile),
			Schema:  schemaNameT,
			Table:   tableNameT,
			Type:    "csv",
		}
		fs = append(fs, f)
	}
	return fs
}

func (i *LightningHelper) escapeRegexDots(fileName string) string {
	return strings.ReplaceAll(path.Base(fileName), `.`, `\.`)
}

// createTaskLightningSQLConf retry means backend = tidb, sql mode
func createTaskLightningSQLConf(baseDir string, logFile string, sortedKvDir string, dataSourceDir string, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, schemaName, tableName string) (string, *structs.LightningConfiguration) {
	checkpointFile := getLightningCheckpointFileName(baseDir)
	sqlBackendConfigFile := getLightningSQLBackendTomlConfigFileName(baseDir)

	options := getLightningDefaultOptions(logFile, sortedKvDir, dataSourceDir, tidbDatabaseSource, param, schemaName, tableName)

	options = append(options, structs.OptionCSVTiKVImporterBackend("tidb"))
	options = append(options, structs.OptionCSVTiKVImporterOnDuplicate(param.GetTiKVImporterOnDuplicate())) // only for tidb backend
	options = append(options, structs.OptionCSVTiKVImporterIncrementalImport(false))                        // tidb backend not support incremental import

	if param.GetCheckpointDriver() == "mysql" {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
	} else {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
		options = append(options, structs.OptionCheckpointDSN(checkpointFile)) // only for file driver
	}

	lightningConf := NewLightningConfiguration(options...)
	return sqlBackendConfigFile, lightningConf
}

// createTaskLightningSQLConf retry means backend = tidb, sql mode
func (i *LightningHelper) buildRetryConf(lightningFileConf LightningFileConf, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, schemaName, tableName string) (string, *structs.LightningConfiguration) {

	baseDir, logFile, sortedKvDir, dataSourceDir := lightningFileConf.GetBaseDir(), lightningFileConf.LogFile, lightningFileConf.GetSortedKvDir(), lightningFileConf.GetDataDir()

	importChunks := &ImportChunks{AllChunks: true}
	checkpointFile := i.buildCheckpointFileName(baseDir, schemaName, tableName, importChunks)
	configFile := i.buildConfigName(baseDir, schemaName, tableName, importChunks)

	options := getLightningDefaultOptions(logFile, sortedKvDir, dataSourceDir, tidbDatabaseSource, param, schemaName, tableName)

	options = append(options, structs.OptionCSVTiKVImporterBackend("tidb"))
	options = append(options, structs.OptionCSVTiKVImporterOnDuplicate(param.GetTiKVImporterOnDuplicate())) // only for tidb backend
	options = append(options, structs.OptionCSVTiKVImporterIncrementalImport(false))                        // tidb backend not support incremental import

	if param.GetCheckpointDriver() == "mysql" {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
	} else {
		options = append(options, structs.OptionCSVCheckpointEnable(param.GetCheckpointEnable()))
		options = append(options, structs.OptionCSVCheckpointDriver(param.GetCheckpointDriver()))
		options = append(options, structs.OptionCheckpointDSN(checkpointFile)) // only for file driver
	}

	lightningConf := NewLightningConfiguration(options...)
	return configFile, lightningConf
}

func getLightningDefaultOptions(logFile string, sortedKvDir string, dataSourceDir string, tidbDatabaseSource *datasource.Datasource, param *structs.CSVMigrationConfigParam, schemaName, tableName string) []structs.OptionLightningConfiguration {
	options := []structs.OptionLightningConfiguration{
		structs.OptionLightningLogFile(logFile),
		structs.OptionSortedKVDir(sortedKvDir),
		structs.OptionDataSourceDir(dataSourceDir),

		structs.OptionTiDBHost(tidbDatabaseSource.HostIp),
		structs.OptionTiDBPort(tidbDatabaseSource.HostPort),
		structs.OptionTiDBUser(tidbDatabaseSource.UserName),
		structs.OptionTiDBPassword(tidbDatabaseSource.PasswordValue),
		structs.OptionTiDBPDAddr(param.GetTiDBPDAddr()),
		structs.OptionTiDBLogLevel(param.GetTiDBLogLevel()),
		structs.OptionTiDBStatusPort(param.GetTiDBStatusPort()),

		structs.OptionCSVSeparator(param.GetSeparator()),
		structs.OptionCSVDelimiter(param.GetDelimiter()),
		structs.OptionCSVTerminator(param.GetTerminator()),
		structs.OptionCSVHeader(param.GetHeader()),
		structs.OptionCSVBackslashEscape(param.GetEscapeBackslash()),

		structs.OptionCSVLightningLogLevel(param.GetLightningLogLevel()),
		structs.OptionCSVLightningMetaSchemaName(param.GetLightningMetaSchemaName()),
		structs.OptionCSVLightningCheckRequirements(param.GetLightningCheckRequirements()),
		structs.OptionCSVLightningIndexConcurrency(param.GetLightningIndexConcurrency()),
		structs.OptionCSVLightningTableConcurrency(param.GetLightningTableConcurrency()),

		structs.OptionCSVPostRestoreAnalyze(param.GetPostRestoreAnalyze()),
		structs.OptionCSVPostRestoreChecksum(param.GetPostRestoreChecksum()),

		structs.OptionCSVTiKVImporterDiskQuota(param.GetTiKVImporterDiskQuota()),
		structs.OptionCSVTiKVImporterDuplicateResolution(param.GetTiKVImporterDuplicateResolution()),

		structs.OptionCSVMydumperCSVTrimLastSeparator(param.GetMydumperCSVTrimLastSeparator()),
		structs.OptionCSVMydumperCSVNull(param.GetMydumperCSVNull()),
		structs.OptionCSVMydumperCSVNotNull(param.GetMydumperCSVNotNull()),
		structs.OptionCSVMydumperCSVHeaderSchemaMatch(param.GetMydumperCSVHeaderSchemaMatch()),
		structs.OptionCSVMydumperFilter(param.GetMydumperFilter()),
		structs.OptionCSVMydumperStrictFormat(param.GetMydumperStrictFormat()),
		structs.OptionCSVMydumperDataInvalidCharReplace(param.GetMydumperDataInvalidCharReplace()),
		structs.OptionCSVMydumperDataCharacterSet(param.GetCharset()),
		structs.OptionCSVMydumperCharacterSet(param.GetMydumperCharacterSet()),
		structs.OptionCSVMydumperBatchImportRatio(param.GetMydumperBatchImportRatio()),
		structs.OptionCSVMydumperReadBlockSize(param.GetMydumperReadBlockSize()),

		structs.OptionCSVCronSwitchMode(param.GetCronSwitchMode()),
	}

	if config.GetGlobalConfig().IsClusterMode() {
		f := getClusterModeCSVFilePattern(schemaName, tableName)
		options = append(options, structs.OptionMydumperFiles([]structs.MyDumperFile{f}))
	} else {
		f := getSingleModeCSVFilePattern(schemaName, tableName)
		options = append(options, structs.OptionMydumperFiles([]structs.MyDumperFile{f}))
	}
	return options
}

func getSingleModeCSVFilePattern(schemaName, tableName string) structs.MyDumperFile {
	return structs.MyDumperFile{
		Pattern: fmt.Sprintf(defaultSchemaTablePattern, schemaName, tableName),
		Schema:  schemaName,
		Table:   tableName,
		Type:    defaultDumperFile.Type,
	}
}

func getClusterModeCSVFilePattern(schemaName, tableName string) structs.MyDumperFile {
	return structs.MyDumperFile{
		Pattern: defaultDumperFile.Pattern,
		Schema:  schemaName,
		Table:   tableName,
		Type:    defaultDumperFile.Type,
	}
}

func getLightningTomlConfigFileName(baseDir string) string {
	return path.Join(baseDir, "/lightning.toml")
}

func getLightningSQLBackendTomlConfigFileName(baseDir string) string {
	return path.Join(baseDir, "/lightning-sql.toml")
}

func getLightningCheckpointFileName(baseDir string) string {
	return path.Join(baseDir, "/checkpoint.pb")
}

func (i *LightningHelper) buildCheckpointFileName(baseDir, schemaNameS, tableNameS string, importChunks *ImportChunks) string {
	if importChunks.IsAllChunks() {
		return path.Join(baseDir, fmt.Sprintf("/checkpoint-%s.%s.pb", schemaNameS, tableNameS))
	} else {
		return path.Join(baseDir, fmt.Sprintf("/checkpoint-%s.%s_%d_%d.pb", schemaNameS, tableNameS, importChunks.CurrentStep, importChunks.TotalStep))
	}
}

func (i *ProgressHelper) buildLightningProgress(progress *structs.LightningProgress) *migration.LightningProgress {
	return &migration.LightningProgress{
		ChannelID:    i.channelId,
		TaskID:       i.taskId,
		SchemaNameS:  i.schemaName,
		TableNameS:   i.tableName,
		Time:         progress.Time,
		LogLevel:     progress.LogLevel,
		Total:        progress.Total,
		Tables:       progress.Tables,
		Chunks:       progress.Chunks,
		Engines:      progress.Engines,
		RestoreBytes: progress.RestoreBytes,
		RestoreRows:  progress.RestoreRows,
		ImportBytes:  progress.ImportBytes,
		EncodeSpeed:  progress.EncodeSpeed,
		State:        progress.State,
		Remaining:    progress.Remaining,
	}
}

func createFailedStatusProgress(ctx context.Context, channelId int, taskId int, logMessage string) {
	_, err := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportFailed, logMessage),
		LogLevel:  log.LogError,
	})
	if err != nil {
		log.Errorf("data import step, create progress log detail record failed: %v", err)
	}
}

func ExecCommands(command string, stdOutFile string, args ...string) error {

	outputFileFd, err := os.OpenFile(stdOutFile, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		log.Errorf("open output file failed, file:%s, err:%v", stdOutFile, err)
	}
	defer outputFileFd.Close()

	cmd := exec.Command(command, args...)

	log.Infof("exec command: %s", cmd.String())

	var stderr bytes.Buffer
	cmd.Stdout = io.Writer(outputFileFd)
	cmd.Stderr = &stderr

	err = cmd.Run() // will wait until cmd finished
	if err != nil {
		newErr := fmt.Errorf("exec command failed, command:%s, err:%v, stdErr:%v", cmd.String(), err, stderr.String())
		log.Error(newErr)
		return newErr
	}
	return nil
}

func deleteLightningTableMetasByTable(ctx context.Context, taskInfo *task.Task, schemaNameT, tableNameT string) error {
	log.Infof("delete lightning table metas, taskId: %d, schemaName:%s, tableName:%s", taskInfo.TaskID, schemaNameT, tableNameT)
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if err != nil {
		log.Errorf("get channel by channelId failed. channelId: %d, err:%s", taskInfo.ChannelId, err)
		return err
	}

	targetDBConn, err := SetUpTargetDatabaseConn(ctx, channelInfo)
	if err != nil {
		log.Errorf("set up target database conn failed, taskId:%d, err:%v", taskInfo.TaskID, err)
		return err
	}

	// task params merge
	defaultTaskCfgs, err := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, taskInfo.TaskParamTmplateId)
	if err != nil {
		return fmt.Errorf("get task params detail info failed: %v", err)
	}
	taskParamMap := make(map[string]string)

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, err := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.TaskID,
		TaskID:              taskInfo.ChannelId,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if err != nil {
		return fmt.Errorf("get task params info failed: %v", err)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}
	metaSchemaName, ok := taskParamMap[constants.ParamsCsvLightningMetaSchemaName]
	if !ok {
		return fmt.Errorf("get task params info failed, lightning.meta-schema-name is empty")
	}

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskInfo.TaskID)
	if err != nil {
		log.Errorf("get channel schema tables failed. err:%v", err)
		return err
	}
	tableNames := make([]string, 0, len(channelSchemaTables)*2)
	for _, channelSchemaTable := range channelSchemaTables {
		if strings.EqualFold(channelSchemaTable.SchemaNameT, schemaNameT) && strings.EqualFold(channelSchemaTable.TableNameT, tableNameT) {
			tableNames = append(tableNames, fmt.Sprintf("`%s`.`%s`", strings.ToLower(channelSchemaTable.SchemaNameS), strings.ToLower(channelSchemaTable.TableNameS)))
			tableNames = append(tableNames, fmt.Sprintf("`%s`.`%s`", strings.ToUpper(channelSchemaTable.SchemaNameS), strings.ToUpper(channelSchemaTable.TableNameS)))
		}
	}

	const deleteMetaSQL = `DELETE FROM %s.table_meta WHERE table_name IN (%s)`
	deleteSQL := fmt.Sprintf(deleteMetaSQL, metaSchemaName, `"`+strings.Join(tableNames, `","`)+`"`)
	log.Infof("BatchDeleteSubTasksById, delete meta sql: %s", stringutil.RemoveSpecialLetterForLog(deleteSQL))
	_, execErr := targetDBConn.MySQLDB.ExecContext(ctx, deleteSQL)
	if execErr != nil {
		log.Errorf("delete meta failed. sql:%s, err:%s", stringutil.RemoveSpecialLetterForLog(deleteSQL), execErr)
		return execErr
	}
	return nil
}

func GetLightningVersion(lightningPath string) (*versioninfo.ServerInfo, error) {
	cmd := exec.Command(lightningPath, "-V")

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run() // will wait until cmd finished
	if err != nil {
		log.Errorf("get lightning version failed, err:%v", err)
		return nil, commonerrors.NewError(commonerrors.TMS_MIGRATION_LIGHTNING_INVALID, err.Error())
	}

	output := stdout.String()
	log.Infof("get lightning version success, output:%s", output)

	versionInfo := versioninfo.ParseServerInfo(output)

	return &versionInfo, nil
}

func StartCSVDataImportByWorker(ctx context.Context, channelId, taskId int) error {
	var errorLogMessage string
	defer func() {
		if errorLogMessage != "" {
			createFailedStatusProgress(ctx, channelId, taskId, errorLogMessage)
		}
	}()

	_, err := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportBegin, "start importing by worker"),
		LogLevel:  log.LogInfo,
	})
	if err != nil {
		log.Errorf("data import step, create progress log detail record failed: %v", err)
	}

	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf("get channel failed, taskId:%d, err:%v", taskId, err)
		return err
	}
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		log.Errorf("get task failed, taskId:%d, err:%v", taskId, err)
		return err
	}
	tidbDatabaseSource, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		log.Errorf("get tidb datasource failed, taskId:%d, err:%v", taskId, err)
		return err
	}
	taskParamTempConfig, err := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if err != nil {
		log.Errorf("get task param template failed, taskId:%d, err:%v", taskId, err)
		return err
	}
	csvParam, err := BuildCSVMigrationParam(ctx, taskInfo, taskParamTempConfig.TaskparamTemplateID)
	if err != nil {
		log.Errorf("build csv migration param failed, taskId:%d, err:%v", taskId, err)
		return err
	}

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		log.Errorf("get channel schema table failed, taskId:%d, err:%v", taskId, err)
		return err
	}
	if len(channelSchemaTables) == 0 {
		log.Errorf("get channel schema table failed, len is 0")
		return fmt.Errorf("get channel schema table failed, len is 0")
	}
	table := channelSchemaTables[0]

	var hasPrimaryUniqueKey = true
	var isRetry bool

	isRetry = taskInfo.RunParams != ""
	if isRetry {
		log.Infof("setup database conns, channelId: %d, taskId: %d", channelId, taskId)
		targetDBConn, setUpErr := SetUpTargetDatabaseConn(ctx, channelInfo)
		if setUpErr != nil {
			log.Errorf("setup database conns failed, taskId:%d, err:%v", taskId, setUpErr)
			return setUpErr
		}
		hasPrimaryUniqueKey = CheckTableHasPrimaryUniqueKey(channelSchemaTables[0])
		if !hasPrimaryUniqueKey {
			// 没有PK/UK的表，truncate 数据
			log.Warnf("table %s.%s has no primary key or unique key, truncate table when retry", table.SchemaNameT, table.TableNameT)
			if err := models.GetFullDataMigrationReaderWriter().TruncateTargetSchemaTable(ctx, targetDBConn.MySQLDB, table.SchemaNameT, table.TableNameT); err != nil {
				log.Errorf("truncate table %s.%s failed, err:%v", table.SchemaNameT, table.TableNameT, err)
				return err
			}
			_, err := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
				ChannelID: channelId,
				TaskID:    taskId,
				Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportBegin, fmt.Sprintf("%s.%s table has no primary/unique key, truncate before retry", table.SchemaNameT, table.TableNameT)),
				LogLevel:  log.LogWarn,
			})
			if err != nil {
				log.Errorf("data import step, create progress log detail record failed: %v", err)
			}
		} else {
			log.Infof("task %d is retry, contains pk/uk, ready to invoke lightning", taskId)
		}
	}

	// create lightning config file
	log.Infof("create lightning config file")
	var logMessage string
	if hasPrimaryUniqueKey && isRetry {
		logMessage = "retry table has primary/unique key, start importing data in sql mode"
	} else {
		if hasPrimaryUniqueKey {
			logMessage = "table start importing data in csv mode"
		} else {
			logMessage = "table has no primary/unique key, start importing data in csv mode"
		}
	}
	_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportRunning, logMessage),
		LogLevel:  log.LogInfo,
	})
	if err != nil {
		log.Errorf("data import step, create progress log detail record failed: %v", err)
	}

	lightningConfigFile, lightningLogFile, lightningStdOutLogFile, err := CreateAndSaveTaskLightningTomlFile(channelInfo, taskInfo, tidbDatabaseSource, csvParam, isRetry, hasPrimaryUniqueKey, table)
	if err != nil {
		errorLogMessage = "create lightning configuration failed"
		log.Errorf("create and save task lightning toml file failed, err:%v", err)
		return err
	}

	cancelCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	progressHelper := buildProgressHelperWithTicker(channelId, taskId, table.SchemaNameS, table.TableNameS, time.NewTicker(time.Second*10), constants.MigrationLogTypeCSV)
	progressHelper.SetLightningLogFile(lightningLogFile)
	go progressHelper.TailAndSaveProgressInTimeLoop(cancelCtx)
	defer progressHelper.Close()
	defer progressHelper.TailAndSaveProgress(ctx)

	startTime := time.Now()
	execErr := ExecCommands(csvParam.GetLightningBinaryPath(), lightningStdOutLogFile, "-config", lightningConfigFile)
	if execErr != nil {
		errorLogMessage = "exec lightning command failed, " + execErr.Error()
		log.Errorf("exec command failed, err:%v", execErr)
		return execErr
	}
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportRunning, "import finished, time consuming: "+time.Since(startTime).String()),
		LogLevel:  log.LogInfo,
	})
	return nil
}

func (i *CSVMigrationTask) importTableChunksByLightning(ctx context.Context, summary *migration.TableMigrationSummary) error {
	channelId, taskId := i.ChannelId, i.TaskId
	var errorLogMessage string
	defer func() {
		if errorLogMessage != "" {
			createFailedStatusProgress(ctx, channelId, taskId, errorLogMessage)
		}
	}()

	var targetDataSource *datasource.Datasource
	var err error

	if i.CSVParam.GetTiKVImporterBackend() == "tidb" {
		tidbDataSource, err := models.GetDatasourceReaderWriter().Get(ctx, i.ChannelInfo.DatasourceIdT)
		if err != nil {
			errorLogMessage = "get tidb datasource failed"
			log.Errorf("get tidb datasource failed, taskId:%d, err:%v", taskId, err)
			return err
		}
		if tidbDataSource.ProxySourceId == 0 { // not proxy, use tidb datasource
			log.Infof("csv task, which backend is tidb, but proxy_source_id is empty, use tidb directly, taskId:%d", taskId)
			targetDataSource = tidbDataSource
		} else {
			log.Infof("csv task, which backend is tidb, proxy_source_id is not empty, use proxy datasource, taskId:%d", taskId)
			targetDataSource, err = models.GetDatasourceReaderWriter().Get(ctx, tidbDataSource.ProxySourceId)
			if err != nil {
				errorLogMessage = "get proxy datasource failed"
				log.Errorf("get proxy datasource failed, taskId:%d, err:%v", taskId, err)
				return err
			}
		}
	} else if i.CSVParam.GetTiKVImporterBackend() == "local" {
		log.Infof("csv task, which backend is local, use tidb/tikv directly, taskId:%d", taskId)
		targetDataSource, err = models.GetDatasourceReaderWriter().Get(ctx, i.ChannelInfo.DatasourceIdT)
		if err != nil {
			errorLogMessage = "get tidb datasource failed"
			log.Errorf("get tidb datasource failed, taskId:%d, err:%v", taskId, err)
			return err
		}
	} else {
		return errors.New("invalid tikv importer backend, allowed values: tidb, local")
	}

	log.Infof("validate schema table pk/uk, taskId:%d, isRetry:%v", taskId, i.IsRetry)
	table, err := models.GetChannelReaderWriter().GetChannelSchemaTableByChannelTaskSchemaSTableS(ctx, channelId, taskId, summary.SchemaNameS, summary.TableNameS)
	if err != nil {
		errorLogMessage = "get channel schema table failed"
		log.Errorf("get channel schema table failed, taskId:%d, err:%v", taskId, err)
		return err
	}

	logMessage := fmt.Sprintf("start importing data in csv mode, taskId:%d, schemaName:%s, tableName:%s, isRetry:%v", taskId, table.SchemaNameS, table.TableNameS, i.IsRetry())
	_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportRunning, logMessage),
		LogLevel:  log.LogInfo,
	})
	if err != nil {
		log.Errorf("data import step, create progress log detail record failed: taskId:%d, err:%v", taskId, err)
		return err
	}

	schemaTable := SchemaTable{SchemaNameS: summary.SchemaNameS, TableNameS: summary.TableNameS, SchemaNameT: table.SchemaNameT, TableNameT: table.TableNameT}
	helper := NewLightningHelper(channelId, taskId, i.isRetry)
	importChunks := &ImportChunks{AllChunks: true}
	lightningFiles, getErr := helper.buildLightningFileConfByDetails(schemaTable, targetDataSource, i.CSVParam, importChunks)
	if getErr != nil {
		log.Errorf("get lightning file conf failed, taskId:%d, err:%v", i.TaskId, getErr)
		return getErr
	}

	makeErr := i.makeDirAndFiles(ctx, schemaTable, lightningFiles, i.CSVParam)
	if makeErr != nil {
		log.Errorf("makeDirAndFiles failed, taskId:%d, err:%v", i.TaskId, makeErr)
		return makeErr
	}

	//metaErr := deleteLightningTableMetasByTable(ctx, i.TaskInfo, summary.SchemaNameS, summary.TableNameS)
	//if metaErr != nil {
	//	errorLogMessage = "delete lightning table metas failed"
	//	log.Errorf("delete lightning table metas failed, taskId:%d, err:%v", taskId, metaErr)
	//}

	cancelCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	progressHelper := buildProgressHelperWithTicker(channelId, taskId, summary.SchemaNameS, summary.TableNameS, time.NewTicker(time.Second*10), constants.MigrationLogTypeCSV)
	progressHelper.SetLightningLogFile(lightningFiles.LogFile)
	go progressHelper.TailAndSaveProgressInTimeLoop(cancelCtx)
	defer progressHelper.Close()
	defer progressHelper.TailAndSaveProgress(ctx)

	startTime := time.Now()
	execErr := ExecCommands(i.CSVParam.GetLightningBinaryPath(), lightningFiles.LogStdOutFile, "-config", lightningFiles.TomlFile)
	if execErr != nil {
		errorLogMessage = "exec lightning command failed, " + execErr.Error()
		log.Errorf("exec command failed, taskId:%d, err:%v", taskId, execErr)
		return execErr
	}
	lastLogMessage := fmt.Sprintf("%s.%s table import finished, time consuming: %v", table.SchemaNameS, table.TableNameS, time.Since(startTime).String())
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportFinish, lastLogMessage),
		LogLevel:  log.LogInfo,
	})
	return nil
}
