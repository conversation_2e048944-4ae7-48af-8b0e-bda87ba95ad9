package migration

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	fileutil "gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/pingcap/errors"
	"github.com/samber/lo"
)

type CSVMigrationTask struct {
	ChannelId int
	TaskId    int

	TaskInfo    *task.Task
	ChannelInfo *channel.ChannelInformation
	DataSources *MigrationDBSources

	tableConfig       map[structs.SchemaTablePair]*task.TaskTableConfig
	ExecuteMode       ExecuteMode
	isRetry           bool
	MakeChunksFactory MakeChunksFactory
	CSVParam          *structs.CSVMigrationConfigParam
}

func NewCSVMigrationTask(channelInfo *channel.ChannelInformation, taskInfo *task.Task, csvParam *structs.CSVMigrationConfigParam, ds *MigrationDBSources, tableConfig map[structs.SchemaTablePair]*task.TaskTableConfig) *CSVMigrationTask {
	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID
	return &CSVMigrationTask{
		ChannelId:         channelId,
		TaskId:            taskId,
		TaskInfo:          taskInfo,
		ChannelInfo:       channelInfo,
		ExecuteMode:       ExecuteModeSingle,
		CSVParam:          csvParam,
		tableConfig:       tableConfig,
		DataSources:       ds,
		MakeChunksFactory: MakeChunksFactory{channelId: channelId, taskId: taskId},
	}
}

func (i *CSVMigrationTask) SetExecuteMode(mode ExecuteMode) {
	i.ExecuteMode = mode
}

func (i *CSVMigrationTask) SetIsRetry() {
	i.isRetry = true
}

func (i *CSVMigrationTask) IsRetry() bool {
	return i.isRetry
}

// SetTaskRunningStatus 设置任务状态为运行中
func (i *CSVMigrationTask) SetTaskRunningStatus(ctx context.Context) error {
	_, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, i.TaskId, task.Task{
		StartTime:  time.Now(),
		TaskStatus: constants.TASK_STATUS_RUNNING,
	})
	if updateErr != nil {
		log.Errorf("task exec failed, update taskId [%v] status [%d] failed: %v", i.TaskId, constants.TASK_STATUS_RUNNING, updateErr)
		return updateErr
	}
	return nil
}

// Process 任务执行入口
func (i *CSVMigrationTask) Process(ctx context.Context) {
	defer func() {
		// 核心流程执行完成后，根据任务执行产生的元数据，判断当前任务是否执行成功
		// 根据任务执行产生的元数据，判断当前任务是否执行成功
		i.SetTaskStatusByExecuteResult(ctx)
	}()

	// 如果第一步设置状态就出错，证明META库有问题，属于致命错误
	setErr := i.SetTaskRunningStatus(ctx)
	if setErr != nil {
		return
	}

	// 初始化迁移日志，当前属于transferdb的日志
	i.InitCSVLogger()

	// 开始执行任务
	i.ExecuteTask(ctx)
}

func (i *CSVMigrationTask) ProcessRetry(ctx context.Context, summary *migration.TableMigrationSummary, csvStage *migration.CSVStage) error {
	// 如果第一步设置状态就出错，证明META库有问题，属于致命错误
	setErr := i.SetTaskRunningStatus(ctx)
	if setErr != nil {
		return setErr
	}

	// 初始化迁移日志，当前属于transferdb的日志
	i.InitCSVLogger()

	i.SetIsRetry()

	retryErr := i.RetryCSVTask(ctx, summary, csvStage)
	if retryErr != nil {
		return retryErr
	}
	return nil
}

func (i *CSVMigrationTask) SetTaskStatusByExecuteResult(ctx context.Context) error {
	log.Infof("executeCSVMigrationTask exec finish, fetch details and summaries to judge task status, taskId:%d", i.TaskId)
	hasErr, determinateErr := UpdateTaskToFailedIfContainsFailedDetailsOrSummaries(ctx, i.TaskId)
	if determinateErr != nil {
		log.Errorf("task exec finish, set final status failed, taskId:%d, err:%v", i.TaskId, determinateErr)
		return determinateErr
	}

	if !hasErr {
		if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, i.TaskId, task.Task{
			EndTime:     time.Now(),
			TaskStatus:  constants.TASK_STATUS_FINISH,
			ErrorDetail: "",
		}); err != nil {
			log.Errorf("task exec finish, update taskId [%v] status [%v] failed: %v", i.TaskId, constants.TASK_STATUS_FINISH, err)
			return err
		}
	}
	return nil
}

// ExecuteTask 执行CSV任务，正常流程
func (i *CSVMigrationTask) ExecuteTask(ctx context.Context) {
	// start task
	taskErr := i.executeCSVMigrationTask(ctx)

	// handle error, update task status to failed, and update endtime
	if taskErr != nil {
		i.SetTaskFailedStatus(ctx, taskErr)
	}
}

func (i *CSVMigrationTask) SetTaskFailedStatus(ctx context.Context, taskErr error) {
	taskId := i.TaskId
	log.Errorf("executeCSVMigrationTask exec failed, taskId:%d, err:%v", taskId, taskErr)
	if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
		EndTime:     time.Now(),
		TaskStatus:  constants.TASK_STATUS_FAILED,
		ErrorDetail: fmt.Sprintf("[error][%v][%s]: %s", timeutil.GetNowTime().Format("2006-01-02T15:04:05-07:00"), constants.MigrationLogTypeCSV, taskErr.Error()),
	}); err != nil {
		log.Errorf("task exec failed, update taskId [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FAILED, err)
	}
}

// InitCSVLogger 初始化迁移日志，当前属于transferdb的日志
func (i *CSVMigrationTask) InitCSVLogger() {
	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeCSV, i.ChannelId, i.TaskId)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)
}

// executeCSVMigrationTask 单机版本 ｜ CSV数据迁移任务的核心逻辑，1. 构造参数 2. 切片 3. 导出
// 对于single模式执行taskId+[1,2,3]
func (i *CSVMigrationTask) executeCSVMigrationTask(ctx context.Context) error {
	channelId, taskId := i.ChannelId, i.TaskId

	_, _, _, channelSchemaObject, getErr := GetMigrationMetadata(ctx, channelId, taskId)
	if getErr != nil {
		log.Errorf("get migration metadata failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return getErr
	}

	// 校验ClearData值的正确性，并根据ClearData值决定是否清理数据
	clearData, validateErr := i.clearDataIfNecessary(ctx, i.TaskInfo, channelSchemaObject, i.DataSources)
	if validateErr != nil {
		log.Errorf("validate clearData flag or clear data failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, validateErr)
		return validateErr
	}

	options := []ChunkMigrateParamOptFunc{
		WithChannelInfo(i.ChannelInfo),
		WithTaskInfo(i.TaskInfo),
		WithClearData(clearData),
		WithCSVMigrationConfigParam(i.CSVParam),
	}

	sourceDBType := i.DataSources.GetSourceDS().DbType
	otherOptions, buildOptErr := i.buildOptionsByDBType(ctx, sourceDBType)
	if buildOptErr != nil {
		log.Errorf("build options by db type failed, channelId: %d, taskId: %d, dbType: %s, err: %v", channelId, taskId, sourceDBType, buildOptErr)
		return buildOptErr
	}
	options = append(options, otherOptions...)

	// chunk相关前置步骤所需参数
	chunkMigrateParam := i.buildChunkMigrateParam(options...)

	makeChunksErr := i.makeChunks(ctx, chunkMigrateParam)
	if makeChunksErr != nil {
		log.Errorf("make chunks failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, makeChunksErr)
		return makeChunksErr
	}

	if sourceDBType == constants.DB_TYPE_CSV {
		migrateErr := i.ImportChunkByStep(ctx, chunkMigrateParam)
		if migrateErr != nil {
			log.Errorf("import by lightning failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, migrateErr)
			return migrateErr
		}
	} else {
		setSCNErr := i.settingTaskSCN(ctx, chunkMigrateParam)
		if setSCNErr != nil {
			log.Errorf("setting task scn failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setSCNErr)
			return setSCNErr
		}

		mkdirErr := i.mkdirOutputDataDir(i.CSVParam)
		if mkdirErr != nil {
			log.Errorf("mkdir output data dir failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, mkdirErr)
			return mkdirErr
		}

		// export chunk to csv and import by lightning
		migrateErr := i.ExportAndImportChunks(ctx, chunkMigrateParam)
		if migrateErr != nil {
			log.Errorf("export chunk to csv and import by lightning failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, migrateErr)
			return migrateErr
		}
	}

	return nil
}

func (i *CSVMigrationTask) buildOptionsByDBType(ctx context.Context, sourceDBType string) ([]ChunkMigrateParamOptFunc, error) {
	log.Infof("determinating run way, current source dbType is %s", sourceDBType)
	channelId, taskId := i.ChannelId, i.TaskId
	options := make([]ChunkMigrateParamOptFunc, 0)
	if sourceDBType == constants.DB_TYPE_CSV {
		targetConns, setUpErr := SetUpTargetConns(ctx, i.ChannelInfo, taskId)
		if setUpErr != nil {
			log.Errorf("setup target conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
			return nil, setUpErr
		}
		options = append(options, WithMigrationDBConns(targetConns))
		options = append(options, WithSourceDBType(sourceDBType))
	} else {
		dbConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, i.ChannelInfo, taskId)
		if setUpErr != nil {
			log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
			return nil, setUpErr
		}
		options = append(options, WithMigrationDBConns(dbConns))
		options = append(options, WithSourceDBType(sourceDBType))
	}
	return options, nil
}

func (i *CSVMigrationTask) clearDataIfNecessary(ctx context.Context, taskInfo *task.Task, channelSchemaObject *channel.ChannelSchemaObject, migrationDBSources *MigrationDBSources) (bool, error) {
	if taskInfo.ParentTaskID != 0 {
		return false, fmt.Errorf("task [%d] parent taskId [%d] is not 0, means a subtask which can't execute split, please check", taskInfo.TaskID, taskInfo.ParentTaskID)
	}

	clearData, checkErr := validateChannelAndGetClearFlag(channelSchemaObject)
	if checkErr != nil {
		log.Errorf("validate channel and get clear flag failed, channelId: %d, taskId: %d, err: %v", taskInfo.ChannelId, taskInfo.TaskID, checkErr)
		return false, checkErr
	}
	if clearData {
		sourceDBType := migrationDBSources.GetSourceDS().DbType
		if sourceDBType == constants.DB_TYPE_CSV {
			log.Infof("clear data flag is true, dbType is %s, reset data before migration, channelId: %d, taskId: %d", sourceDBType, taskInfo.ChannelId, taskInfo.TaskID)
			_ = models.GetFullDataMigrationReaderWriter().ResetCSVStageForImportByTaskId(ctx, taskInfo.ChannelId, taskInfo.TaskID)
			_ = models.GetFullDataMigrationReaderWriter().ResetTableMigrationDetailForImportByTaskId(ctx, taskInfo.TaskID)
			_ = models.GetFullDataMigrationReaderWriter().ResetTableMigrationSummaryForImportByTaskId(ctx, taskInfo.TaskID)
		} else {
			log.Infof("clear data flag is true, dbType is %s, clear data before migration, channelId: %d, taskId: %d", sourceDBType, taskInfo.ChannelId, taskInfo.TaskID)
			_ = models.GetFullDataMigrationReaderWriter().BatchDeleteCSVStageByTaskId(ctx, taskInfo.ChannelId, taskInfo.TaskID)
			_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetailByTaskIds(ctx, []int{taskInfo.TaskID})
			_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummaryByTaskIds(ctx, []int{taskInfo.TaskID})
		}
	}
	return clearData, nil
}

func (i *CSVMigrationTask) buildChunkMigrateParam(options ...ChunkMigrateParamOptFunc) ChunkMigrateParam {
	makeChunksParam := ChunkMigrateParam{}
	for _, option := range options {
		option(&makeChunksParam)
	}
	return makeChunksParam
}

func (i *CSVMigrationTask) makeChunks(ctx context.Context, makeChunksParam ChunkMigrateParam) error {
	channelId, taskId := makeChunksParam.GetChannelId(), makeChunksParam.GetTaskId()
	makeChunksFunc, makeChunksErr := i.MakeChunksFactory.GetMakeChunksFunc(makeChunksParam.GetSourceDBType())
	if makeChunksErr != nil {
		log.Errorf("get make chunks func failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, makeChunksErr)
		return makeChunksErr
	}
	makeChunksFuncErr := makeChunksFunc(ctx, makeChunksParam)
	if makeChunksFuncErr != nil {
		log.Errorf("split or fetch or split task table into chunks failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, makeChunksFuncErr)
		return makeChunksFuncErr
	}
	return nil
}

func (i *CSVMigrationTask) settingTaskSCN(ctx context.Context, makeChunksParam ChunkMigrateParam) error {
	migrationParam := makeChunksParam.GetMigrationParam()
	taskInfo := makeChunksParam.GetTaskInfo()
	dbConns := makeChunksParam.GetMigrationDBConns()

	if migrationParam.GetConsistentRead() {
		globalScn, getScnErr := dbConns.GetSourceDB().GetOracleCurrentSnapshotSCN()
		if getScnErr != nil {
			log.Errorf("get oracle current snapshot scn failed, taskId:%d, err:%v", taskInfo.TaskID, getScnErr)
			return getScnErr
		}
		updateErr := models.GetTaskReaderWriter().SetTaskScnNumber(ctx, taskInfo.TaskID, globalScn)
		if updateErr != nil {
			log.Errorf("set global scn from oracle success, but set task scn number failed, taskId:%d, err:%v", taskInfo.TaskID, updateErr)
			return updateErr
		}
		taskInfo.ScnNumber = fmt.Sprintf("%d", globalScn)
		log.Infof("export csv data with consistent read, taskId %d, scn number %d", taskInfo.TaskID, globalScn)
	} else {
		updateErr := models.GetTaskReaderWriter().ResetTaskScnNumber(ctx, taskInfo.TaskID)
		if updateErr != nil {
			log.Errorf("reset task scn number failed, taskId:%d, err:%v", taskInfo.TaskID, updateErr)
			return updateErr
		}
		taskInfo.ScnNumber = ""
	}
	return nil
}

func (i *CSVMigrationTask) RemoveTaskTableCSVFileWithSchemaTableName(ctx context.Context, schemaNameS, tableNameS string) error {
	taskInfo := i.TaskInfo
	channelId := i.TaskInfo.ChannelId
	taskId := i.TaskInfo.TaskID

	log.Infof("task finish, remove csv file, channelId: %d, taskId: %d", channelId, taskId)
	schemaTables, getErr := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if getErr != nil {
		log.Errorf("get channel schema tables failed, taskId:%d, err:%v", taskId, getErr)
		return getErr
	}

	csvFileDir := i.CSVParam.GetOutputDataDir(taskInfo.ChannelId, taskInfo.TaskID)

	for _, table := range schemaTables {
		if table.SchemaNameS != schemaNameS {
			continue
		}
		if table.TableNameS != tableNameS {
			continue
		}
		removeCSVFiles := make([]string, 0)
		logMessage := fmt.Sprintf("%s.%s table, migrate success, remove csv file", table.SchemaNameS, table.TableNameS)
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: taskInfo.ChannelId,
			TaskID:    taskInfo.TaskID,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportRunning, logMessage),
			LogLevel:  log.LogWarn,
		})

		var f structs.MyDumperFile
		if config.GetGlobalConfig().IsClusterMode() {
			f = getClusterModeCSVFilePattern(table.SchemaNameT, table.TableNameT)
		} else {
			f = getSingleModeCSVFilePattern(table.SchemaNameT, table.TableNameT)
		}
		log.Infof("remove csv file from %s, pattern:%s, taskId:%d", csvFileDir, f.Pattern, taskId)

		csvFilePatternStr := f.Pattern
		csvFilePattern, compileErr := regexp.Compile(csvFilePatternStr)
		if compileErr != nil {
			log.Errorf("compile csv file pattern failed, pattern:%s, err:%v", csvFilePatternStr, compileErr)
			return compileErr
		}

		walkErr := filepath.WalkDir(csvFileDir, func(path string, info os.DirEntry, err error) error {
			if info.IsDir() {
				return nil
			}
			if csvFilePattern.MatchString(path) {
				removeCSVFiles = append(removeCSVFiles, path)
			} else {
				log.Debugf("skip remove csv file, file path:%s", path)
			}
			return nil
		})
		if walkErr != nil {
			log.Errorf("scanning csv file for delete failed, taskId:%d, err:%v", taskId, walkErr)
			return walkErr
		}
		log.Infof("task finish, remove csv file, channelId: %d, taskId: %d, table %s, removeCSVFiles:%d", channelId, taskId, table.TableNameS, len(removeCSVFiles))
		log.Debugf("csvFilePatternStr:%v, removeCSVFiles:%v", csvFilePatternStr, removeCSVFiles)
		for _, removeCSVFile := range removeCSVFiles {
			if removeErr := os.Remove(removeCSVFile); removeErr != nil {
				log.Errorf("remove csv file failed, file path:%s, err:%v", removeCSVFile, removeErr)
				return removeErr
			}
		}
	}

	return nil
}

// ExportAndImportChunks export and import chunks
func (i *CSVMigrationTask) ExportAndImportChunks(ctx context.Context, chunkMigrateParam ChunkMigrateParam) error {
	channelId, taskId := i.ChannelId, i.TaskId
	migrationParam := chunkMigrateParam.GetMigrationParam()

	log.Infof("export chunk to csv, channelId:%d, taskId:%d, migrationParam:%s", channelId, taskId, migrationParam.String())
	migrationSummaries, getSummaryErr := getWaitingAndFailedMigrationSummariesV2(ctx, taskId)
	if getSummaryErr != nil {
		log.Errorf("get migration summaries failed, taskId:%d, err:%v", taskId, getSummaryErr)
		return getSummaryErr
	}

	if len(migrationSummaries) == 0 {
		logMessage := fmt.Sprintf("migrate skip, no table objects found, taskId:%d, tableNum:%d", taskId, len(migrationSummaries))
		log.Warn(logMessage)
		i.createSkipLog(ctx, logMessage)
		return nil
	} else {
		log.Infof("start migrate, taskId:%d, tableNum:%d", taskId, len(migrationSummaries))
	}

	gSummary := buildErrGroupGoroutineLimit(migrationParam.GetTableThreads())

	for summaryIdx := range migrationSummaries {
		migrationSummary := migrationSummaries[summaryIdx]
		schemaNameS := migrationSummary.SchemaNameS
		tableNameS := migrationSummary.TableNameS

		gSummary.Go(func() error {
			exportErr := i.ExportChunksByTable(ctx, migrationSummary, chunkMigrateParam)
			if exportErr != nil {
				log.Errorf("ExportChunksByTable failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaNameS, tableNameS, exportErr)
				return exportErr
			}
			importErr := i.ImportChunksByTable(ctx, migrationSummary, chunkMigrateParam)
			if importErr != nil {
				log.Errorf("ImportChunksByTable failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaNameS, tableNameS, importErr)
				return importErr
			}
			return nil
		})
	}
	log.Infof("waiting for schema migration finish, taskId:%d, summaryLen:%d", taskId, len(migrationSummaries))
	if waitErr := gSummary.Wait(); waitErr != nil {
		log.Errorf("migrateCSVDataByChunks failed, taskId:%d, err:%v", taskId, waitErr)
		return waitErr
	}
	return nil
}

// ImportChunkByStep import chunks by step
func (i *CSVMigrationTask) ImportChunkByStep(ctx context.Context, chunkMigrateParam ChunkMigrateParam) error {
	channelId, taskId := i.ChannelId, i.TaskId
	migrationParam := chunkMigrateParam.GetMigrationParam()

	summaries, getErr := getTaskWaitingAndFailedMigrationSummaries(ctx, taskId)
	if getErr != nil {
		log.Errorf("get task migration summaries failed, taskId:%d, err:%v", taskId, getErr)
		return getErr
	}
	if len(summaries) == 0 {
		return i.createMigrationSkipWarning(ctx, taskId, channelId)
	}

	// 在开始任务级别的导入之前，清理lightning的配置文件目录和日志目录
	helper := NewLightningHelper(i.ChannelId, i.TaskId, i.isRetry)
	lightningBasicDir, getDirErr := helper.buildBasicDir(chunkMigrateParam.GetMigrationParam())
	if getDirErr != nil {
		log.Errorf("get lightning dirs conf failed, taskId:%d, err:%v", i.TaskId, getDirErr)
		return getDirErr
	}
	_ = fileutil.RemoveIfExist(lightningBasicDir.GetTomlConfDir())
	_ = fileutil.RemoveIfExist(lightningBasicDir.GetLogDir())

	gSummary := buildErrGroupGoroutineLimit(migrationParam.GetTableThreads())

	for summaryIdx := range summaries {
		summary := summaries[summaryIdx]
		log.Infof("ImportChunkByStep, ready to import table, taskId:%d, schemaName:%s, tableName:%s", taskId, summary.SchemaNameS, summary.TableNameS)
		gSummary.Go(func() error {
			importErr := i.StepImportChunksByTable(ctx, summary, chunkMigrateParam)
			if importErr != nil {
				log.Errorf("StepImportChunksByTable failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summary.ServiceNameS, summary.TableNameS, importErr)
				return importErr
			}
			return nil
		})
	}
	log.Infof("waiting for all summary migration finish, taskId:%d, summaryLen:%d", taskId, len(summaries))
	if waitErr := gSummary.Wait(); waitErr != nil {
		log.Errorf("ImportChunkByStep failed, taskId:%d, err:%v", taskId, waitErr)
		return waitErr
	}
	return nil
}

func (i *CSVMigrationTask) createMigrationSkipWarning(ctx context.Context, taskId int, channelId int) error {
	log.Warnf("migrate skip, no table objects found, taskId:%d", taskId)
	_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableSkip, "skipping migration, no tables or chunks to execute"),
		LogLevel:  log.LogWarn,
	})
	if createErr != nil {
		log.Errorf("create progress log failed, taskId:%d, err:%v", taskId, createErr)
	}
	return createErr
}

func (i *CSVMigrationTask) ExportChunksByTable(ctx context.Context, migrationSummary *migration.TableMigrationSummary, chunkMigrateParam ChunkMigrateParam) error {
	startExecTime := time.Now()
	channelId, taskId := i.ChannelId, i.TaskId
	summaryId := migrationSummary.ID
	schemaNameS := migrationSummary.SchemaNameS
	tableNameS := migrationSummary.TableNameS

	if setRunningErr := i.setCSVMigrationSummaryRunning(ctx, migrationSummary, true); setRunningErr != nil {
		log.Errorf("set migration running failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, setRunningErr)
		return setRunningErr
	}
	_, getStageErr := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, taskId, schemaNameS, tableNameS)
	if getStageErr != nil {
		log.Errorf("get csv stage failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, getStageErr)
		return getStageErr
	}

	log.Infof("start migrate, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s", migrationSummary.ID, taskId, schemaNameS, tableNameS)
	details, getDetailErr := getWaitingAndFailedMigrationDetails(ctx, migrationSummary)
	if getDetailErr != nil {
		log.Errorf("get migration details failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, getDetailErr)
		return getDetailErr
	}
	log.Infof("start migrate, detailsNum:%d, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s", len(details), migrationSummary.ID, taskId, schemaNameS, tableNameS)

	progressHelper := buildProgressHelper(channelId, taskId, schemaNameS, tableNameS, constants.MigrationLogTypeCSV)
	progressHelper.SetTotal(len(details))
	progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
	defer progressHelper.Close()
	defer progressHelper.DisplayAndSaveProgress(ctx)

	dbConns := chunkMigrateParam.GetMigrationDBConns()
	migrationParam := chunkMigrateParam.GetMigrationParam()
	taskInfo := chunkMigrateParam.GetTaskInfo()

	tableMigrateCustomConf, buildConfErr := i.buildTableMigrateCustomConf(ctx, schemaNameS, tableNameS, dbConns)
	if buildConfErr != nil {
		log.Errorf("buildTableMigrateCustomConf failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, buildConfErr)
		return buildConfErr
	}

	g := buildErrGroupGoroutineLimit(migrationParam.GetSqlThreads())

	for detailIdx := range details {
		migrationDetail := &(details[detailIdx])

		tableMigrateCustomConf.ApplyToTableChunk(migrationDetail)

		// 虽然这里可以优化，将函数提取到外层，但是因为错误处理需要回填到detail中，比较麻烦
		// 再加上整个CSV任务最耗时的阶段其实是导出和导入，所以这里不做优化了
		getColumnsSQL := BuildGetColumnSQL(migrationDetail)
		columnNameS, getColumnErr := dbConns.GetSourceDB().GetOracleTableRowsColumnCSV(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[dbConns.GetSourceDS().Charset],
			transfercommon.StringUPPER(migrationParam.GetCharset()))
		if getColumnErr != nil {
			progressHelper.IncreaseFailedNum()
			setErr := i.setChunkMetaGetColumnFailed(ctx, migrationSummary, migrationDetail, getColumnErr, getColumnsSQL, startExecTime)
			if setErr != nil {
				return setErr
			}
			continue
		}

		rows, buildReqErr := buildCSVDataMigrationReq(ctx, taskInfo, migrationDetail, dbConns, migrationParam, columnNameS)
		if buildReqErr != nil {
			progressHelper.IncreaseFailedNum()
			err := i.setChunkMetaBuildMigrationReqFailed(ctx, migrationSummary, migrationDetail, buildReqErr, startExecTime)
			if err != nil {
				return err
			}
			continue
		}

		g.Go(func() error {
			return doDataMigration(ctx, transferDBMigrateCSVFunc, channelId, migrationDetail, rows, progressHelper, constants.MigrationLogTypeCSV)
		})
	}
	log.Infof("waiting for details migration finish, taskId:%d, summaryId:%d, chunkNum:%d, schemaNameS:%s, tableNameS:%s",
		taskId, summaryId, len(details), schemaNameS, tableNameS)

	_ = g.Wait() // 忽略errgroup的错误，因为不管错误与否，都需要继续执行下面的逻辑
	progressHelper.Close()

	summaryStatus, errMessage, setErr := trySetCSVMigrationSummaryFailed(ctx, migrationSummary, taskInfo, len(details), startExecTime)
	if setErr != nil {
		log.Errorf("process and save data migration results failed, taskId:%d, summaryId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			taskId, summaryId, schemaNameS, tableNameS, setErr)
		return setErr
	}

	if summaryStatus == transfercommon.TaskStatusFailed {
		log.Errorf("summary status is failed, taskId:%d, summaryId:%d, schemaNameS:%s, tableNameS:%s, skip lightning import and exit",
			taskId, summaryId, schemaNameS, tableNameS)
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table export failed, skip lightning import and exit", schemaNameS, tableNameS)),
			LogLevel:  log.LogError,
		})
		return errors.New(errMessage)
	}

	updateErr := models.GetFullDataMigrationReaderWriter().UpdateCSVStageBySchemaSTableS(ctx, channelId, taskId, schemaNameS, tableNameS, map[string]interface{}{
		"export_to_csv": true,
		"error_message": "",
	})
	if updateErr != nil {
		log.Errorf("update csv stage failed, taskId:%d, summaryId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			taskId, summaryId, schemaNameS, tableNameS, updateErr)
		return updateErr
	}

	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, export data finished, time consuming: %v", schemaNameS, tableNameS, time.Since(startExecTime).String())),
		LogLevel:  log.LogInfo,
	})

	return nil
}

// ImportChunksByTable import chunks by table(summary means a table)
func (i *CSVMigrationTask) ImportChunksByTable(ctx context.Context, migrationSummary *migration.TableMigrationSummary, chunkMigrateParam ChunkMigrateParam) error {
	startExecTime := time.Now()
	_, taskId := i.ChannelId, i.TaskId
	schemaNameS := migrationSummary.SchemaNameS
	tableNameS := migrationSummary.TableNameS

	if setRunningErr := i.setCSVMigrationSummaryRunning(ctx, migrationSummary, false); setRunningErr != nil {
		log.Errorf("set migration running failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, setRunningErr)
		return setRunningErr
	}
	// 防御性查询
	_, getStageErr := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, taskId, schemaNameS, tableNameS)
	if getStageErr != nil {
		log.Errorf("get csv stage failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, getStageErr)
		return getStageErr
	}

	log.Infof("summary status is success, start lightning importing, taskId:%d, schemaNameS:%s, tableNameS:%s, isRetry:%v", taskId, schemaNameS, tableNameS, i.isRetry)
	importErr := i.importTableChunksByLightning(ctx, migrationSummary)
	if importErr != nil {
		log.Errorf("start csv data import task failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, importErr)
		i.SetCSVImportStageFailed(ctx, migrationSummary, importErr.Error())
		i.SetSummaryStatus(ctx, migrationSummary, transfercommon.TaskStatusFailed, startExecTime)
		return nil
	}
	updateErr := i.SetCSVImportStageFinish(ctx, migrationSummary)
	if updateErr != nil {
		log.Errorf("update csv stage failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, updateErr)
		i.SetSummaryStatus(ctx, migrationSummary, transfercommon.TaskStatusFailed, startExecTime)
		return updateErr
	}
	i.SetSummaryStatus(ctx, migrationSummary, transfercommon.TaskStatusSuccess, startExecTime)

	mp := chunkMigrateParam.GetMigrationParam()
	if mp.ShouldRemoveCSV(chunkMigrateParam.SourceDBType) {
		removeErr := i.RemoveTaskTableCSVFileWithSchemaTableName(ctx, schemaNameS, tableNameS)
		if removeErr != nil {
			log.Errorf("remove csv file failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, removeErr)
		}
	}

	return nil
}

// StepImportChunksByTable import chunks by table summary
func (i *CSVMigrationTask) StepImportChunksByTable(ctx context.Context, migrationSummary *migration.TableMigrationSummary, chunkMigrateParam ChunkMigrateParam) error {
	startExecTime := time.Now()
	_, taskId := i.ChannelId, i.TaskId
	schemaNameS := migrationSummary.SchemaNameS
	tableNameS := migrationSummary.TableNameS

	if setRunningErr := i.setCSVMigrationSummaryRunning(ctx, migrationSummary, false); setRunningErr != nil {
		log.Errorf("set migration running failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, setRunningErr)
		return setRunningErr
	}

	// 不是Retry并且需要清理数据，才进行清理
	if !i.IsRetry() && chunkMigrateParam.GetClearData() {
		log.Infof("clear data before import, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
		err := i.clearData(ctx, chunkMigrateParam.GetMigrationDBConns().GetTargetDB().MySQLDB, migrationSummary.SchemaNameT, migrationSummary.TableNameT)
		if err != nil {
			log.Errorf("clear data failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, err)
			return err
		}
	}

	csvStage, getStageErr := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, taskId, schemaNameS, tableNameS)
	if getStageErr != nil {
		log.Errorf("get csv stage failed, summaryId:%d, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", migrationSummary.ID, taskId, schemaNameS, tableNameS, getStageErr)
		return getStageErr
	}

	validateErr, shouldReturn := i.validateCSVStage(csvStage, taskId, schemaNameS, tableNameS)
	if validateErr != nil {
		log.Errorf("validate csv stage failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, validateErr)
	}
	if shouldReturn {
		return nil
	}

	notImportedDetails, getErr := models.GetFullDataMigrationReaderWriter().BatchGetWaitingAndFailedTableMigrationDetail(ctx, taskId, schemaNameS, tableNameS)
	if getErr != nil {
		log.Errorf("get not imported details failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, getErr)
		return getErr
	}

	// 将等待执行的Chunk按照BatchImportSize进行分组，组之间串行，组内由一个Lightning进程调用
	batchSize := chunkMigrateParam.GetMigrationParam().GetBatchImportSize()
	importChunkList := i.splitDetailsIntoChunks(notImportedDetails, batchSize)
	schemaTable := i.buildSchemaTable(migrationSummary)

	log.Infof("StepImportChunksByTable, found %d details for import, we will split into %d group, taskId:%d, schemaNameS:%s, tableNameS:%s",
		len(notImportedDetails), len(importChunkList), taskId, schemaNameS, tableNameS)

	errList := make([]error, 0)
	for _, importChunk := range importChunkList {
		log.Infof("StepImportChunksByTable, start importChunksByTableDetails, taskId:%d, schemaNameS:%s, tableNameS:%s, currentStep:%d, totalStep:%d", taskId, schemaNameS, tableNameS, importChunk.CurrentStep, importChunk.TotalStep)
		_ = i.updateDetailInstallStatusToProcessing(ctx, importChunk.Details)

		importErr := i.importChunksByTableDetails(ctx, schemaTable, importChunk, chunkMigrateParam)

		if importErr != nil {
			log.Errorf("importChunksByTableDetails failed, taskId:%d, schemaNameS:%s, tableNameS:%s, currentStep:%d, totalStep:%d, err:%v", taskId, schemaNameS, tableNameS, importChunk.CurrentStep, importChunk.TotalStep, importErr)
			errList = append(errList, importErr)
			_ = i.updateDetailInstallStatusToFailed(ctx, importChunk.Details, importErr)
		} else {
			_ = i.updateDetailInstallStatusToSuccess(ctx, importChunk.Details)
		}
	}

	var updateErr error
	if len(errList) != 0 {
		updateErr = i.SetCSVImportStageFailed(ctx, migrationSummary, errList[0].Error())
		i.SetSummaryStatus(ctx, migrationSummary, transfercommon.TaskStatusFailed, startExecTime)
	} else {
		updateErr = i.SetCSVImportStageFinish(ctx, migrationSummary)
		i.SetSummaryStatus(ctx, migrationSummary, transfercommon.TaskStatusSuccess, startExecTime)
	}
	if updateErr != nil {
		log.Errorf("update csv stage failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, updateErr)
		i.SetSummaryStatus(ctx, migrationSummary, transfercommon.TaskStatusFailed, startExecTime)
		return updateErr
	}

	// remove_csv模式在db_type=CSV的情况下，不起作用
	//taskInfo, migrationParam := chunkMigrateParam.GetTaskInfo(), chunkMigrateParam.GetMigrationParam()
	//if migrationParam.ShouldRemoveCSV() {
	//	removeErr := RemoveTaskTableCSVFileWithSchemaTableName(ctx, taskInfo, migrationParam, schemaNameS, tableNameS)
	//	if removeErr != nil {
	//		log.Errorf("remove csv file failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, schemaNameS, tableNameS, removeErr)
	//	}
	//}

	return nil
}

func (i *CSVMigrationTask) buildSchemaTable(migrationSummary *migration.TableMigrationSummary) SchemaTable {
	return SchemaTable{SchemaNameT: migrationSummary.SchemaNameT, TableNameT: migrationSummary.TableNameT, SchemaNameS: migrationSummary.SchemaNameS, TableNameS: migrationSummary.TableNameS}
}

func (i *CSVMigrationTask) updateDetailInstallStatusToProcessing(ctx context.Context, details []*migration.TableMigrationDetail) error {
	ids := make([]uint, 0)
	for _, detail := range details {
		ids = append(ids, detail.ID)
	}
	return models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailImportStatusByIDs(ctx, i.TaskId, ids, ChunkStatusRunning, "")
}

func (i *CSVMigrationTask) updateDetailInstallStatusToSuccess(ctx context.Context, details []*migration.TableMigrationDetail) error {
	ids := make([]uint, 0)
	for _, detail := range details {
		ids = append(ids, detail.ID)
	}
	return models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailImportStatusByIDs(ctx, i.TaskId, ids, ChunkStatusSuccess, "")
}

func (i *CSVMigrationTask) updateDetailInstallStatusToFailed(ctx context.Context, details []*migration.TableMigrationDetail, err error) error {
	ids := make([]uint, 0)
	for _, detail := range details {
		ids = append(ids, detail.ID)
	}
	return models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailImportStatusByIDs(ctx, i.TaskId, ids, ChunkStatusFailed, err.Error())
}

func (i *CSVMigrationTask) validateCSVStage(csvStage *migration.CSVStage, taskId int, schemaNameS string, tableNameS string) (error, bool) {
	if csvStage == nil {
		err := fmt.Errorf("csvStage is nil, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
		return err, true
	}
	if csvStage.ExportToCSV == false {
		err := fmt.Errorf("import chunks failed, exportToCSV is false, not ready to import, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
		return err, true
	}
	if csvStage.ImportToDB == true {
		log.Warnf("import chunks failed, importToDB is true, already imported, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
		return nil, true
	}
	return nil, false
}

// splitDetailsIntoChunks split details into chunks
// e.g detail.FileName may have diff dest dir, we should group them into one batch with certain size
func (i *CSVMigrationTask) splitDetailsIntoChunks(notImportedDetails []*migration.TableMigrationDetail, batchSize int) []*ImportChunks {
	csvFileDirReverseMap := make(map[string][]*migration.TableMigrationDetail)
	for _, detail := range notImportedDetails {
		csvFileDir := path.Dir(detail.CSVFile)
		if _, ok := csvFileDirReverseMap[csvFileDir]; !ok {
			csvFileDirReverseMap[csvFileDir] = make([]*migration.TableMigrationDetail, 0)
		}
		csvFileDirReverseMap[csvFileDir] = append(csvFileDirReverseMap[csvFileDir], detail)
	}

	var (
		currentStep int
		retArr      []*ImportChunks
	)
	for csvFileDir := range csvFileDirReverseMap {
		details := csvFileDirReverseMap[csvFileDir]
		detailChunks := lo.Chunk(details, batchSize)
		for _, detailChunk := range detailChunks {
			currentStep++
			retArr = append(retArr, &ImportChunks{
				Details:     detailChunk,
				CurrentStep: currentStep,
				DataDir:     csvFileDir,
			})
		}
	}

	for idx := range retArr {
		retArr[idx].TotalStep = len(retArr)
	}
	return retArr
}

func (i *CSVMigrationTask) buildTableMigrateCustomConf(ctx context.Context, schemaNameS string, tableNameS string, dbConns *MigrationDBConns) (*TableMigrateCustomConf, error) {
	var (
		chunkColumnDetail        string
		chunkColumnDetailChanged bool
		tablePartition           string
		tablePartitionChanged    bool
	)

	taskTableConfigMap := i.tableConfig

	if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
		SchemaName: schemaNameS,
		TableName:  tableNameS,
	}]; ok {
		if taskTableConfig.OperatorTag == "Y" {
			oracleCollation := dbConns.GetOracleCollation()
			oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, dbConns, schemaNameS, tableNameS, oracleCollation, taskTableConfigMap)
			if err != nil {
				return nil, err
			}
			chunkColumnDetail = strings.Join(oracleCols, ",")
			chunkColumnDetailChanged = true
		}
		if taskTableConfig.TablePartition != "" {
			tablePartition = taskTableConfig.TablePartition
			tablePartitionChanged = true
		}
	}

	return &TableMigrateCustomConf{
		chunkColumnDetail:        chunkColumnDetail,
		chunkColumnDetailChanged: chunkColumnDetailChanged,
		tablePartition:           tablePartition,
		tablePartitionChanged:    tablePartitionChanged,
	}, nil
}

func (i *CSVMigrationTask) setChunkMetaBuildMigrationReqFailed(ctx context.Context, migrationSummary *migration.TableMigrationSummary, migrationDetail *migration.TableMigrationDetail, buildReqErr error, startExecTime time.Time) error {
	channelId, taskId := i.ChannelId, i.TaskId
	summaryId, chunkId, chunk, schemaNameS, tableNameS := migrationSummary.ID, migrationDetail.ID, migrationDetail.ChunkDetailS, migrationSummary.SchemaNameS, migrationSummary.TableNameS

	errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildReqErr.Error(), chunkId, chunk)

	log.Errorf("buildCSVDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s, err:%v",
		taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS, buildReqErr)
	errC := SetMigrationChunkFailed(ctx, channelId, taskId, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
	if errC != nil {
		log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s, err:%v",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS, errC)
		return errC
	} else {
		log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS)
	}
	_, errS := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, channelId, startExecTime, constants.MigrationLogTypeCSV)
	if errS != nil {
		log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s, err:%v",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS, errS)
		return errS
	} else {
		log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS)
	}
	return nil
}

func (i *CSVMigrationTask) setChunkMetaGetColumnFailed(ctx context.Context,
	migrationSummary *migration.TableMigrationSummary, migrationDetail *migration.TableMigrationDetail, getColumnErr error,
	getColumnsSQL string, startExecTime time.Time,
) error {
	channelId, taskId := i.ChannelId, i.TaskId
	summaryId, chunkId, chunk, schemaNameS, tableNameS := migrationSummary.ID, migrationDetail.ID, migrationDetail.ChunkDetailS, migrationSummary.SchemaNameS, migrationSummary.TableNameS
	errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)

	log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s, getColumnsSQL:%v, err:%v",
		taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS, getColumnsSQL, getColumnErr)
	errC := SetMigrationChunkFailed(ctx, channelId, taskId, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
	if errC != nil {
		log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s, err:%v",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS, errC)
		return errC
	} else {
		log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS)
	}
	_, errS := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, channelId, startExecTime, constants.MigrationLogTypeCSV)
	if errS != nil {
		log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s, err:%v",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS, errS)
		return errS
	} else {
		log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaNameS:%s, tableNameS:%s",
			taskId, summaryId, chunkId, chunk, schemaNameS, tableNameS)
	}
	return nil
}

func (i *CSVMigrationTask) mkdirOutputDataDir(migrationParam *structs.CSVMigrationConfigParam) error {
	outputDataDir := migrationParam.GetOutputDataDir(i.ChannelId, i.TaskId)
	mkdirErr := fileutil.CreateIfNotExist(outputDataDir)
	if mkdirErr != nil {
		log.Errorf("create output data dir failed, taskId:%d, err:%v", i.TaskId, mkdirErr)
		return fmt.Errorf("create output data dir failed: %v", mkdirErr)
	}
	return nil
}

func (i *CSVMigrationTask) importChunksByTableDetails(ctx context.Context, table SchemaTable, importChunks *ImportChunks, param ChunkMigrateParam) error {
	tidbDatabaseSource := param.GetMigrationDBConns().GetTargetDS()

	helper := NewLightningHelper(i.ChannelId, i.TaskId, i.isRetry)
	lightningFiles, getErr := helper.buildLightningFileConfByDetails(table, tidbDatabaseSource, param.GetMigrationParam(), importChunks)
	if getErr != nil {
		log.Errorf("get lightning file conf failed, taskId:%d, err:%v", i.TaskId, getErr)
		return getErr
	}

	makeErr := i.makeDirAndFiles(ctx, table, lightningFiles, param.GetMigrationParam())
	if makeErr != nil {
		log.Errorf("makeDirAndFiles failed, taskId:%d, err:%v", i.TaskId, makeErr)
		return makeErr
	}

	cancelCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	progressHelper := buildProgressHelperWithTicker(i.ChannelId, i.TaskId, table.SchemaNameS, table.TableNameS, time.NewTicker(time.Second*10), constants.MigrationLogTypeCSV)
	progressHelper.SetLightningLogFile(lightningFiles.LogFile)
	go progressHelper.TailAndSaveProgressInTimeLoop(cancelCtx)
	defer progressHelper.Close()
	defer progressHelper.TailAndSaveProgress(ctx)

	startTime := time.Now()
	execErr := ExecCommands(param.GetMigrationParam().GetLightningBinaryPath(), lightningFiles.LogStdOutFile, "-config", lightningFiles.TomlFile)
	if execErr != nil {
		log.Errorf("exec command failed, taskId:%d, err:%v", i.TaskId, execErr)
		return execErr
	}
	lastLogMessage := fmt.Sprintf("%s.%s table import finished, time consuming: %v", table.SchemaNameS, table.TableNameS, time.Since(startTime).String())
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: i.ChannelId,
		TaskID:    i.TaskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportFinish, lastLogMessage),
		LogLevel:  log.LogInfo,
	})
	return nil
}

func (i *CSVMigrationTask) makeDirAndFiles(ctx context.Context, table SchemaTable, lightningFiles LightningFileConf, param *structs.CSVMigrationConfigParam) error {
	// create baseDir is not exists
	createDirErr := fileutil.CreateIfNotExist(lightningFiles.GetBaseDir())
	if createDirErr != nil {
		log.Errorf("create baseDir failed, dir:%s, taskId:%d, err:%v", lightningFiles.GetBaseDir(), i.TaskId, createDirErr)
		return createDirErr
	}

	// remove sortedKvDir if checkpoint is disabled
	if !param.GetCheckpointEnable() {
		//remove sortedKvDir if exists
		removeErr := fileutil.RemoveIfExist(lightningFiles.GetSortedKvDir())
		if removeErr != nil {
			log.Errorf("remove sortedKvDir failed, dir:%s, taskId:%d, err:%v", lightningFiles.Dirs.SortedKvDir, i.TaskId, removeErr)
			return removeErr
		}
		//remove checkpoint file if exists
		removeFileErr := fileutil.RemoveIfExist(lightningFiles.TomlFileContent.Checkpoint.Dsn)
		if removeFileErr != nil {
			log.Errorf("remove checkpoint dsn failed, dsn:%s, taskId:%d, err:%v", lightningFiles.TomlFileContent.Checkpoint.Dsn, i.TaskId, removeFileErr)
			return removeFileErr
		}
	}

	//create sortedKvDir is not exists
	createSortErr := fileutil.CreateIfNotExist(filepath.Dir(lightningFiles.Dirs.SortedKvDir))
	if createSortErr != nil {
		log.Errorf("create sortedKvDir failed, dir:%s, taskId:%d, err:%v", filepath.Dir(lightningFiles.Dirs.SortedKvDir), i.TaskId, createSortErr)
		return createSortErr
	}

	createLogErr := fileutil.CreateIfNotExist(lightningFiles.GetLogDir())
	if createLogErr != nil {
		log.Errorf("create import_csv log folder failed, dir:%s, taskId:%d, err:%v", lightningFiles.GetLogDir(), i.TaskId, createLogErr)
		return createLogErr
	}

	createPbDirErr := fileutil.CreateIfNotExist(lightningFiles.GetPbDir())
	if createPbDirErr != nil {
		log.Errorf("create pb_dir folder failed, dir:%s, taskId:%d, err:%v", lightningFiles.GetPbDir(), i.TaskId, createPbDirErr)
		return createPbDirErr
	}

	createTomlDirErr := fileutil.CreateIfNotExist(lightningFiles.GetTomlConfDir())
	if createTomlDirErr != nil {
		log.Errorf("create toml_conf folder failed, dir:%s, taskId:%d, err:%v", lightningFiles.GetTomlConfDir(), i.TaskId, createTomlDirErr)
		return createTomlDirErr
	}

	confStr, marshalErr := lightningFiles.TomlFileContent.MarshalTOML()
	if marshalErr != nil {
		log.Errorf("marshal conf as toml failed, taskId:%d, err:%v", i.TaskId, marshalErr)
		return marshalErr
	}

	log.Infof("create lightning config file: %s, taskId:%d", lightningFiles.TomlFile, i.TaskId)
	createErr := fileutil.CreateOrReplaceFile(lightningFiles.TomlFile, confStr)
	if createErr != nil {
		log.Errorf("create conf file failed, taskId:%d, err:%v", i.TaskId, createErr)
		return createErr
	}

	log.Infof("create lightning log file: %s, taskId:%d", lightningFiles.LogFile, i.TaskId)
	createLogFileErr := fileutil.CreateOrReplaceFile(lightningFiles.LogFile, "")
	if createLogFileErr != nil {
		log.Errorf("create empty log file failed, taskId:%d, err:%v", i.TaskId, createLogFileErr)
		return createLogFileErr
	}

	log.Infof("delete lightning table metas, taskId:%d, schemaName:%s, tableName:%s", i.TaskId, table.SchemaNameS, table.TableNameS)
	metaErr := deleteLightningTableMetasByTable(ctx, i.TaskInfo, table.SchemaNameS, table.TableNameS)
	if metaErr != nil {
		log.Errorf("delete lightning table metas failed, taskId:%d, err:%v", i.TaskId, metaErr)
		return metaErr
	}
	return nil
}

func (i *CSVMigrationTask) clearData(ctx context.Context, mysqlDB *sql.DB, schemaName, tableName string) error {
	if err := models.GetFullDataMigrationReaderWriter().TruncateTargetSchemaTable(ctx, mysqlDB, schemaName, tableName); err != nil {
		log.Errorf("truncate table %s.%s failed, err:%v", schemaName, tableName, err)
		return err
	}
	return nil
}
