package migration

import (
	"bytes"
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	transferconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	tmso2t "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/o2t"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
)

// StartFullDataMigrationTask single模式下使用的全量数据迁移任务接口
func StartFullDataMigrationTask(ctx context.Context, channelId, taskId int) {
	go func(Ctx context.Context, channelId, taskId int) {
		_, updateTaskErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(Ctx, taskId, task.Task{
			StartTime:  time.Now(),
			TaskStatus: constants.TASK_STATUS_RUNNING,
		})
		if updateTaskErr != nil {
			log.Errorf("task exec failed, update taskId [%v] status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, updateTaskErr)
			return
		}

		// init transferdb logger
		logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeFullData, channelId, taskId)
		additionalLoggerHelper.InitAdditionalLogger(logFileName)

		// start task
		processErr := CreateFullDataMigrationTaskInSingleMode(Ctx, channelId, taskId, structs.SingleModeMigrationInvokeOption)

		// handle error, update task status to failed, and update endtime
		if processErr != nil {
			log.Errorf("CreateFullDataMigrationTaskInSingleMode exec failed, err: %v", processErr)
			if _, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(Ctx, taskId, task.Task{
				EndTime:     time.Now(),
				TaskStatus:  constants.TASK_STATUS_FAILED,
				ErrorDetail: fmt.Sprintf("task exec failed, update taskId [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FAILED, processErr),
			}); updateErr != nil {
				log.Errorf("task exec failed, update taskId [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FAILED, updateErr)
			}
			//return
		}
		log.Infof("CreateFullDataMigrationTaskInSingleMode exec finish, fetch details and summaries to judge task status, taskId:%d", taskId)

		hasFailedDetailOrSummaries, determinateErr := UpdateTaskToFailedIfContainsFailedDetailsOrSummaries(Ctx, taskId)
		if determinateErr != nil {
			log.Errorf("task exec finish, set final status failed, taskId:%d, err:%v", taskId, determinateErr)
			return
		}

		if !hasFailedDetailOrSummaries {
			if _, err := models.GetTaskReaderWriter().UpdateTaskByTaskID(Ctx, taskId, task.Task{
				EndTime:     time.Now(),
				TaskStatus:  constants.TASK_STATUS_FINISH,
				ErrorDetail: "",
			}); err != nil {
				log.Errorf("task exec finish, update taskId [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FINISH, err)
			}
		}
	}(ctx, channelId, taskId)
}

// CreateFullDataMigrationTaskInClusterMode 数据迁移任务的核心逻辑，1. 构造参数 2. Chunk切片 3. Chunk数据迁移
//
//	对于cluster模式执行parentTaskId+[1,2]或者subTaskId+[1,3]
func CreateFullDataMigrationTaskInClusterMode(ctx context.Context, channelId, taskId int, invokeOption structs.InvokeMigrationOption) error {

	log.Infof("start create full data migration task, channelId: %d, taskId: %d, invokeOption: %v", channelId, taskId, invokeOption.String())
	taskInfo, channelInfo, taskParamTempConfig, channelSchemaObject, getErr := GetMigrationMetadata(ctx, channelId, taskId)
	if getErr != nil {
		log.Errorf("get migration metadata failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return getErr
	}

	// init migration param
	log.Infof("build migration config params, channelId: %d, taskId: %d", channelId, taskId)
	migrationParam, buildParamErr := BuildMigrationConfigParams(ctx, channelId, taskId, taskParamTempConfig.TaskparamTemplateID)
	if buildParamErr != nil {
		log.Errorf("build migration config params failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildParamErr)
		return buildParamErr
	}

	// init datasource and conns
	log.Infof("setup database conns, channelId: %d, taskId: %d", channelId, taskId)
	dbConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
		return setUpErr
	}

	clearData, checkErr := validateChannelAndGetClearFlag(channelSchemaObject)
	if checkErr != nil {
		log.Errorf("validate channel and get clear flag failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, checkErr)
		return checkErr
	}
	if clearData {
		_ = models.GetFullDataMigrationReaderWriter().BatchDeleteCSVStageByTaskId(ctx, channelId, taskId)
		_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetailByTaskIds(ctx, []int{taskId})
		_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummaryByTaskIds(ctx, []int{taskId})
	}

	// split table into chunks, and save migration details / summaries, and sub tasks in cluster mode
	if invokeOption.DoSplit {
		if taskInfo.ParentTaskID != 0 {
			return fmt.Errorf("task [%d] parent taskId[%d] is not 0, means subtask which can't execute split, please check", taskInfo.TaskID, taskInfo.ParentTaskID)
		}

		var splitErr error
		if dbConns.GetSourceDS().DbType == constants.DB_TYPE_ORACLE {
			log.Infof("split task table into chunks, channelId: %d, taskId: %d", channelId, taskId)
			splitErr = splitTaskTablesIntoFullDataChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns, channelSchemaObject, clearData)
		} else if dbConns.GetSourceDS().DbType == constants.DB_TYPE_ORACLE_ADG {
			log.Infof("fetch table chunks from oracle-adg, channelId: %d, taskId: %d", channelId, taskId)
			splitErr = fetchTaskTablesIntoFullDataChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns, channelSchemaObject, clearData)
		} else {
			splitErr = fmt.Errorf("not support db type: %s for csv migration, taskId:%d", dbConns.GetSourceDS().DbType, taskId)
		}
		if splitErr != nil {
			log.Errorf("split or fetch task table chunks info failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, splitErr)
			return splitErr
		}
	}

	// migrate table data in chunks
	if invokeOption.DoMigrate {
		log.Infof("migrate table data by chunks, channelId: %d, taskId: %d", channelId, taskId)
		migrateErr := migrateTableDataByChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns)
		if migrateErr != nil {
			log.Errorf("migrate table data by chunks failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, migrateErr)
			return migrateErr
		}
	}

	return nil
}

// CreateFullDataMigrationTaskInSingleMode 数据迁移任务的核心逻辑，1. 构造参数 2. Chunk切片 3. Chunk数据迁移
//
//	对于single模式执行taskId+[1,2,3]
func CreateFullDataMigrationTaskInSingleMode(ctx context.Context, channelId, taskId int, invokeOption structs.InvokeMigrationOption) error {
	log.Infof("start create full data migration task, channelId: %d, taskId: %d, invokeOption: %v", channelId, taskId, invokeOption.String())
	taskInfo, channelInfo, taskParamTempConfig, channelSchemaObject, getErr := GetMigrationMetadata(ctx, channelId, taskId)
	if getErr != nil {
		log.Errorf("get migration metadata failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return getErr
	}

	// init migration param
	log.Infof("build migration config params, channelId: %d, taskId: %d", channelId, taskId)
	migrationParam, buildParamErr := BuildMigrationConfigParams(ctx, channelId, taskId, taskParamTempConfig.TaskparamTemplateID)
	if buildParamErr != nil {
		log.Errorf("build migration config params failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildParamErr)
		return buildParamErr
	}

	// init datasource and conns
	log.Infof("setup database conns , channelId: %d, taskId: %d", channelId, taskId)
	dbConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
		return setUpErr
	}
	log.Infof("setup database conns success, channelId: %d, taskId: %d", channelId, taskId)

	// split table into chunks, and save migration details / summaries, and sub tasks in cluster mode
	if taskInfo.ParentTaskID != 0 {
		err := fmt.Errorf("task [%d] parent taskId[%d] is not 0, means subtask which can't execute split, please check", taskInfo.TaskID, taskInfo.ParentTaskID)
		log.Error(err.Error())
		return err
	}

	clearData, checkErr := validateChannelAndGetClearFlag(channelSchemaObject)
	if checkErr != nil {
		log.Errorf("validate channel and get clear flag failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, checkErr)
		return checkErr
	}
	if clearData {
		_ = models.GetFullDataMigrationReaderWriter().BatchDeleteCSVStageByTaskId(ctx, channelId, taskId)
		_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetailByTaskIds(ctx, []int{taskId})
		_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummaryByTaskIds(ctx, []int{taskId})
	}

	var splitErr error
	if dbConns.GetSourceDS().DbType == constants.DB_TYPE_ORACLE {
		log.Infof("split task table into chunks, channelId: %d, taskId: %d", channelId, taskId)
		splitErr = splitTaskTablesIntoFullDataChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns, channelSchemaObject, clearData)
	} else if dbConns.GetSourceDS().DbType == constants.DB_TYPE_ORACLE_ADG {
		log.Infof("fetch table chunks from oracle-adg, channelId: %d, taskId: %d", channelId, taskId)
		splitErr = fetchTaskTablesIntoFullDataChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns, channelSchemaObject, clearData)
	} else {
		splitErr = fmt.Errorf("not support db type: %s for csv migration, taskId:%d", dbConns.GetSourceDS().DbType, taskId)
	}
	if splitErr != nil {
		log.Errorf("split or fetch task table chunks info failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, splitErr)
		return splitErr
	}

	if migrationParam.GetConsistentRead() {
		log.Infof("export full data with consistent read, getting snapshot scn, channelId: %d, taskId: %d", channelId, taskId)
		globalScn, scnErr := dbConns.GetSourceDB().GetOracleCurrentSnapshotSCN()
		if scnErr != nil {
			log.Errorf("get oracle current snapshot scn failed, task_id %d, err:%v", taskInfo.TaskID, scnErr)
			return scnErr
		}
		updateErr := models.GetTaskReaderWriter().SetTaskScnNumber(ctx, taskId, globalScn)
		if updateErr != nil {
			log.Errorf("set global scn from oracle success, but set task scn number failed, task_id %d, err:%v", taskInfo.TaskID, updateErr)
			return updateErr
		}
		taskInfo.ScnNumber = fmt.Sprintf("%d", globalScn)
		log.Infof("export full data with consistent read, channelId: %d, taskId: %d, scn number %d", channelId, taskId, globalScn)
	} else {
		updateErr := models.GetTaskReaderWriter().ResetTaskScnNumber(ctx, taskId)
		if updateErr != nil {
			log.Errorf("reset task scn number failed, task_id %d, err:%v", taskInfo.TaskID, updateErr)
			return updateErr
		}
		taskInfo.ScnNumber = ""
	}

	// migrate table data in chunks
	log.Infof("migrate table data by chunks, channelId: %d, taskId: %d", channelId, taskId)
	migrateErr := migrateTableDataByChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns)
	if migrateErr != nil {
		log.Errorf("migrate table data by chunks failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, migrateErr)
		return migrateErr
	}

	return nil
}

// StartFullDataMigrateByWorker invoke by worker, taskId means sub taskId, not parent taskId!!!
func StartFullDataMigrateByWorker(ctx context.Context, channelId, taskId int) error {
	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		log.Errorf("get task info error, taskId :%d, err:%v", taskId, getTaskErr)
		return getTaskErr
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		progressLogs, getErr := models.GetProgressLogReaderWriter().BatchGetProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
		})
		if getErr != nil {
			log.Errorf("batch get progress log failed, taskId: %d, err: %v", taskId, getErr)
			return getErr
		}
		nowTime := time.Now()
		for idx := range progressLogs {
			progressLogs[idx].CreatedAt = nowTime
		}
		if len(progressLogs) != 0 {
			if _, updateErr := models.GetProgressLogReaderWriter().SaveProgressLogDetails(transactionCtx, progressLogs); updateErr != nil {
				log.Errorf("task exec failed, update progress logs failed, taskId: %d, err: %v", taskId, updateErr)
				return updateErr
			}
		}

		if _, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(transactionCtx, taskId, task.Task{
			StartTime:  time.Now(),
			TaskStatus: constants.TASK_STATUS_RUNNING,
		}); updateErr != nil {
			log.Errorf("task exec failed, update taskId [%v] to status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, updateErr)
			return updateErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("task exec failed, update taskId [%v] to status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, trxErr)
		return trxErr
	}

	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeFullData, channelId, taskId)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)

	var execErr error
	// run param 不为空，走retry逻辑
	if taskInfo.RunParams != "" {
		log.Infof("start full data migration retry task, taskId:%d, runParam:%s", taskId, taskInfo.RunParams)

		// 如果runParam不是以summary或者detail开头的，报错并设置任务状态为Failed
		if validateErr := validateMigrationTaskRunParam(taskInfo.RunParams); validateErr != nil {
			log.Errorf("get task info success, but runParam is invalid, err:%v", validateErr)
			if _, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
				StartTime:  time.Now(),
				TaskStatus: constants.TASK_STATUS_RUNNING,
			}); updateErr != nil {
				log.Errorf("task exec failed, update taskId [%v] to status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, updateErr)
				return updateErr
			}
			return validateErr
		}

		if strings.HasPrefix(taskInfo.RunParams, "summary:") {
			execErr = retryFullDataMigrationBySummaries(ctx, taskInfo, taskId)
		} else if strings.HasPrefix(taskInfo.RunParams, "detail:") {
			execErr = retryFullDataMigrationByDetails(ctx, taskInfo)
		}
	} else {
		log.Infof("start full data migration task, taskId:%d, runParam:%s", taskId, taskInfo.RunParams)
		execErr = startFullDataMigrationByWorker(ctx, channelId, taskId)
	}

	_, determinateErr := UpdateTaskToFailedIfContainsFailedDetailsOrSummaries(ctx, taskId)
	if determinateErr != nil {
		// 仅做记录
		log.Errorf("task exec finish, set final status failed, taskId: %d, err: %v", taskId, determinateErr)
	}

	if execErr != nil {
		log.Errorf("start data migration task failed, taskId:%d, runParam:%s, err:%v", taskId, taskInfo.RunParams, execErr)
		return execErr
	}

	return nil
}

func retryFullDataMigrationBySummaries(ctx context.Context, taskInfo *task.Task, taskId int) error {
	log.Infof("start retry full data migration failed summaries, taskId:%d, runParam:%s", taskId, taskInfo.RunParams)
	summaries, parseErr := parseTaskRunParamAndGetSummaries(ctx, taskInfo)
	if parseErr != nil {
		log.Errorf("parse task run param and get summaries failed, taskId:%d, runParam:%s, err:%v", taskId, taskInfo.RunParams, parseErr)
		return parseErr
	}
	log.Infof("retry full data migration failed summaries, taskId:%d, runParam:%s, len:%d", taskId, taskInfo.RunParams, len(summaries))
	retryErr := BatchRetryFullDataMigrationByTableFailed(ctx, taskInfo, summaries)
	if retryErr != nil {
		log.Errorf("retry full data migration failed chunks failed, taskId:%d, runParam:%s, err:%v", taskId, taskInfo.RunParams, retryErr)
		return retryErr
	}
	return nil
}

func retryFullDataMigrationByDetails(ctx context.Context, taskInfo *task.Task) error {
	detailList, parseErr := parseTaskRunParamAndGetDetails(ctx, taskInfo)
	if parseErr != nil {
		log.Errorf("parse task run param and get details failed, taskId:%d, runParam:%s, err:%v", taskInfo.TaskID, taskInfo.RunParams, parseErr)
		return parseErr
	}

	//runParam下的schema+table应该是一致的，这里防止可能出现的问题
	schemaTableChunksMapping := make(map[structs.SchemaTablePair][]migration.TableMigrationDetail)
	for _, detail := range detailList {
		schemaTablePair := structs.SchemaTablePair{
			SchemaName: detail.SchemaNameS,
			TableName:  detail.TableNameS,
		}
		schemaTableChunksMapping[schemaTablePair] = append(schemaTableChunksMapping[schemaTablePair], detail)
	}

	for schemaTablePair, details := range schemaTableChunksMapping {
		log.Infof("start retry full data migration failed chunks, taskId:%d, runParam:%s, schema:%s, table:%s", taskInfo.TaskID, taskInfo.RunParams, schemaTablePair.SchemaName, schemaTablePair.TableName)
		req := FailedChunkBatchRetryReq{
			TaskID:      taskInfo.TaskID,
			SchemaNameS: schemaTablePair.SchemaName,
			TableNameS:  schemaTablePair.TableName,
		}
		retryErr := RetryFullDataMigrationFailedChunks(ctx, req, taskInfo, details)
		if retryErr != nil {
			log.Errorf("retry full data migration failed chunks failed, taskId:%d, runParam:%s, schema:%s, table:%s, err:%v", taskInfo.TaskID, taskInfo.RunParams, schemaTablePair.SchemaName, schemaTablePair.TableName, retryErr)
			return retryErr
		}
	}
	return nil
}

// 由于是worker执行，所以这时的taskId是子任务的ID
func startFullDataMigrationByWorker(ctx context.Context, channelId int, taskId int) error {
	return CreateFullDataMigrationTaskInClusterMode(ctx, channelId, taskId, structs.ClusterModeMigrationDoMigrate)
}

// splitTaskTablesIntoFullDataChunks 切片元信息，taskId只支持parentId或者单机版的taskId
func splitTaskTablesIntoFullDataChunks(ctx context.Context, channelInfo *channel.ChannelInformation, taskInfo *task.Task, migrationParams *structs.FullMigrationConfigParam, dbConns *MigrationDBConns, channelSchemaObject *channel.ChannelSchemaObject, clearData bool) error {

	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		log.Errorf("build task table config map failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, buildErr)
		return buildErr
	}

	// 1. 初始化任务
	// 2. 任务运行
	startTime := time.Now()

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		return err
	}
	log.Infof("get channel schema tables, channelId:%d, taskId:%d, len:%d", channelId, taskId, len(channelSchemaTables))

	channelSchemaTableIds := make([]int, 0)
	channelSchemaTableIdMap := make(map[int]*channel.ChannelSchemaTable)
	for _, channelSchemaTable := range channelSchemaTables {
		channelSchemaTableIds = append(channelSchemaTableIds, channelSchemaTable.ChannelSchtableId)
		channelSchemaTableIdMap[channelSchemaTable.ChannelSchtableId] = channelSchemaTable
	}

	log.Infof("get table exist status from oracle and tidb, channelId:%d, taskId:%d", channelId, taskId)
	lowerCase := strings.ToLower
	existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, getTableStatusErr :=
		commonpkg.GetTableStatusFromDataSource(ctx, channelSchemaTableIds, taskInfo, dbConns.GetSourceDS(), dbConns.GetTargetDS(), lowerCase, lowerCase)
	if getTableStatusErr != nil {
		log.Errorf("get table status failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, getTableStatusErr)
		return getTableStatusErr
	}
	log.Info("start full_data migration, taskId:%d, channelId:%d, oracleNotExistTables:%d, tidbNotExistTables:%d, existTables:%d",
		taskInfo.TaskID, taskInfo.ChannelId, len(oracleNotExistTables), len(tidbNotExistTables), len(existTables))

	saveTableStatusErr := saveNotExistTablesToSummaryAndChunks(ctx, "CSV", taskInfo, oracleNotExistTables, tidbNotExistTables, dbConns, migrationParams.GetChunkSize(), taskTableConfigMap)
	if saveTableStatusErr != nil {
		log.Errorf("saveNotExistTablesToSummaryAndChunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, saveTableStatusErr)
		return saveTableStatusErr
	}

	gInit := &errgroup.Group{}
	gInit.SetLimit(migrationParams.GetTaskThreads())

	tableTypeMap := make(map[string]map[string]string)

	for _, tableID := range existTablesIds {
		channelSchemaTable := channelSchemaTableIdMap[tableID]

		if tableTypeMap[channelSchemaTable.SchemaNameS] == nil {
			tablesMap, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableType(ctx, dbConns.GetSourceDB().OracleDB, channelSchemaTable.SchemaNameS)
			if err != nil {
				log.Errorf("get oracle table type failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
			}
			tableTypeMap[channelSchemaTable.SchemaNameS] = tablesMap
		}

		log.Infof("start split oracle table [%s.%s] int chunks, channelId:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelId, taskId)

		// 在这个函数里面，完成单个表的数据迁移任务的初始化，包含切片(TableMigrationDetail)，汇总记录(TableMigrationSummary)等
		if config.GetGlobalConfig().IsClusterMode() {
			gInit.Go(func() error {
				return SplitFullDataChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, dbConns, migrationParams, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		} else {
			gInit.Go(func() error {
				return SplitFullDataChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, dbConns, migrationParams, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		}
	}
	if waitErr := gInit.Wait(); waitErr != nil {
		log.Errorf("split oracle table into chunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, waitErr)
		return waitErr
	}
	log.Infof("meta database data init done, cost [%v], channelId:%d, taskId:%d", time.Now().Sub(startTime).String(), channelId, taskId)
	return nil
}

// fetchTaskTablesIntoFullDataChunks 切片元信息，taskId只支持parentId或者单机版的taskId
func fetchTaskTablesIntoFullDataChunks(ctx context.Context, channelInfo *channel.ChannelInformation, taskInfo *task.Task, migrationParams *structs.FullMigrationConfigParam, migrationDBConns *MigrationDBConns, channelSchemaObject *channel.ChannelSchemaObject, clearData bool) error {

	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		log.Errorf("build task table config map failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, buildErr)
		return buildErr
	}

	// 1. 初始化任务
	// 2. 任务运行
	startTime := time.Now()

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		return err
	}
	log.Infof("get channel schema tables, channelId:%d, taskId:%d, len:%d", channelId, taskId, len(channelSchemaTables))

	var channelSchemaTableIds []int
	channelSchemaTableIdMap := make(map[int]*channel.ChannelSchemaTable)
	for _, channelSchemaTable := range channelSchemaTables {
		channelSchemaTableIds = append(channelSchemaTableIds, channelSchemaTable.ChannelSchtableId)
		channelSchemaTableIdMap[channelSchemaTable.ChannelSchtableId] = channelSchemaTable
	}

	log.Infof("get table exist status from oracle and tidb, channelId:%d, taskId:%d", channelId, taskId)
	lowerCase := strings.ToLower
	existTables, existTablesIds, oracleNotExistTables, tidbNotExistTables, getTableStatusErr :=
		commonpkg.GetTableStatusFromDataSource(ctx, channelSchemaTableIds, taskInfo, migrationDBConns.GetSourceDS(), migrationDBConns.GetTargetDS(), lowerCase, lowerCase)
	if getTableStatusErr != nil {
		log.Errorf("get table status failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, getTableStatusErr)
		return getTableStatusErr
	}
	log.Info("start full_data migration, taskId:%d, channelId:%d, oracleNotExistTables:%d, tidbNotExistTables:%d, existTables:%d",
		taskInfo.TaskID, taskInfo.ChannelId, len(oracleNotExistTables), len(tidbNotExistTables), len(existTables))

	saveTableStatusErr := saveNotExistTablesToSummaryAndChunks(ctx, "CSV", taskInfo, oracleNotExistTables, tidbNotExistTables, migrationDBConns, ADG_DEFAULT_CHUNK_SIZE, taskTableConfigMap)
	if saveTableStatusErr != nil {
		log.Errorf("saveNotExistTablesToSummaryAndChunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, saveTableStatusErr)
		return saveTableStatusErr
	}

	gInit := &errgroup.Group{}
	gInit.SetLimit(migrationParams.GetTaskThreads())

	tableTypeMap := make(map[string]map[string]string)

	for _, tableID := range existTablesIds {
		channelSchemaTable := channelSchemaTableIdMap[tableID]

		if tableTypeMap[channelSchemaTable.SchemaNameS] == nil {
			tablesMap, err := models.GetDatasourceReaderWriter().GetOracleSchemaTableType(ctx, migrationDBConns.GetSourceDB().OracleDB, channelSchemaTable.SchemaNameS)
			if err != nil {
				log.Errorf("get oracle table type failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, err)
			}
			tableTypeMap[channelSchemaTable.SchemaNameS] = tablesMap
		}

		log.Infof("start fetch oracle table [%s.%s] chunks, channelId:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelId, taskId)

		// 在这个函数里面，完成单个表的数据迁移任务的初始化，包含切片(TableMigrationDetail)，汇总记录(TableMigrationSummary)等
		if config.GetGlobalConfig().IsClusterMode() {
			gInit.Go(func() error {
				return FetchFullDataChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, migrationDBConns, migrationParams, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		} else {
			gInit.Go(func() error {
				return FetchFullDataChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, migrationDBConns, migrationParams, tableTypeMap[channelSchemaTable.SchemaNameS][channelSchemaTable.TableNameS])
			})
		}
	}
	if waitErr := gInit.Wait(); waitErr != nil {
		log.Errorf("split oracle table into chunks failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, waitErr)
		return waitErr
	}
	log.Infof("meta database data init done, cost [%v], channelId:%d, taskId:%d", time.Now().Sub(startTime).String(), channelId, taskId)
	return nil
}

func migrateTableDataByChunks(ctx context.Context, channelInfo *channel.ChannelInformation, taskInfo *task.Task, migrationParam *structs.FullMigrationConfigParam, migrationDBConns *MigrationDBConns) error {
	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID

	sourceSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, channelId, taskId)
	if getSchemaErr != nil {
		log.Errorf("get task target schema info failed, taskId:%d, err:%v", taskId, getSchemaErr)
		return fmt.Errorf("get task target schema info failed, taskId:%d, err:%v", taskId, getSchemaErr)
	}

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	log.Infof("start migrate table data in chunks, taskId:%d, allSourceSchemas: %v, migrationParam: %s", taskId, sourceSchemas, migrationParam.String())
	for _, sourceSchema := range sourceSchemas {
		log.Infof("start migrate, taskId:%d, schemaName:%s", taskId, sourceSchema)
		migrationSummaries, getErr := getWaitingAndFailedMigrationSummaries(ctx, taskId, sourceSchema)
		if getErr != nil {
			log.Errorf("get migration summaries failed, taskId:%d, schemaName:%s, err:%v", taskId, sourceSchema, getErr)
			return getErr
		}

		log.Infof("start migrate, taskId:%d, schemaName:%s, summaryLen:%d, ", taskId, sourceSchema, len(migrationSummaries))
		if len(migrationSummaries) == 0 {
			log.Warnf("migrate skip, taskId:%d, schemaName:%s, summaryLen:%d", taskId, sourceSchema, len(migrationSummaries))
			_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
				ChannelID:   channelId,
				TaskID:      taskId,
				SchemaNameS: sourceSchema,
				Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableSkip, fmt.Sprintf("%s database, skipped, no tables or chunks to execute", sourceSchema)),
				LogLevel:    log.LogWarn,
			})
			if createErr != nil {
				log.Errorf("create progress log failed, taskId:%d, schemaName:%s, err:%v", taskId, sourceSchema, createErr)
				return createErr
			}
			continue
		}

		gSummary := &errgroup.Group{}
		gSummary.SetLimit(migrationParam.GetTableThreads())

		for summaryIdx := range migrationSummaries {

			migrationSummary := migrationSummaries[summaryIdx]

			summaryId := migrationSummary.ID
			schemaName := migrationSummary.SchemaNameS
			tableName := migrationSummary.TableNameS

			gSummary.Go(func() error {
				startExecTime := time.Now()

				if setRunningErr := setFullDataMigrationSummaryRunning(ctx, migrationSummary, channelId, constants.MigrationLogTypeFullData); setRunningErr != nil {
					log.Errorf("set migration running failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, schemaName, tableName, setRunningErr)
					return setRunningErr
				}

				log.Infof("start migrate, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s",
					taskId, summaryId, schemaName, tableName)
				details, getDetailErr := getWaitingAndFailedMigrationDetails(ctx, migrationSummary)
				if getDetailErr != nil {
					log.Errorf("get migration details failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, schemaName, tableName, getDetailErr)
					return getDetailErr
				}
				log.Infof("start migrate, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, chunkNum:%d",
					taskId, summaryId, schemaName, tableName, len(details))

				progressHelper := buildProgressHelper(channelId, taskId, migrationSummary.SchemaNameS, migrationSummary.TableNameS, constants.MigrationLogTypeFullData)
				progressHelper.SetTotal(len(details))
				progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
				defer progressHelper.Close()
				defer progressHelper.DisplayAndSaveProgress(ctx)

				g := &errgroup.Group{}
				g.SetLimit(migrationParam.GetSqlThreads())

				for detailIdx := range details {
					migrationDetail := &(details[detailIdx])

					chunkId := migrationDetail.ID
					chunk := migrationDetail.ChunkDetailS

					getColumnsSQL := BuildGetColumnSQL(migrationDetail)
					columnNameS, getColumnErr := migrationDBConns.GetSourceDB().GetOracleTableRowsColumn(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[migrationDBConns.sourceDS.Charset],
						transfercommon.StringUPPER(migrationDBConns.targetDS.Charset))
					if getColumnErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)
						log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeFullData)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						continue // 记录日志，但是不返回，保证所有details都能被处理
					}

					if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
						SchemaName: schemaName,
						TableName:  tableName,
					}]; ok {
						if taskTableConfig.OperatorTag == "Y" {
							oracleCollation := migrationDBConns.GetOracleCollation()
							oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
							if err != nil {
								log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
								return err
							}
							migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
						}
						if taskTableConfig.TablePartition != "" {
							migrationDetail.TablePartition = taskTableConfig.TablePartition
						}
					}

					stmt, req, prepareSQL, buildErr := buildFullDataMigrationReq(ctx, taskInfo, migrationDetail, migrationDBConns, migrationParam, columnNameS, false)
					if buildErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)
						log.Errorf("buildFullDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeFullData)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						continue
					}

					g.Go(doDataMigrationWithStmt(ctx, transferDBMigrateFullDataFunc, channelId, migrationDetail, req, progressHelper, constants.MigrationLogTypeFullData, stmt, prepareSQL))
				}
				log.Infof("waiting for details migration finish, taskId:%d, summaryId:%d, chunkNum:%d, schemaName:%s, tableName:%s",
					taskId, migrationSummary.ID, len(details), migrationSummary.SchemaNameS, migrationSummary.TableNameS)

				_ = g.Wait() // 忽略errgroup的错误，因为不管错误与否，都需要继续执行下面的逻辑

				_, setErr := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
				if setErr != nil {
					log.Errorf("process and save data migration results failed, summaryId:%d, taskId:%d, schemaName:%s, tableName:%s, err:%v", migrationSummary.ID, taskId, sourceSchema, migrationSummary.TableNameS, setErr)
					return setErr
				}
				return nil
			})
		}
		log.Infof("waiting for schema migration finish, taskId:%d, schemaName:%s, summaryLen:%d", taskId, sourceSchema, len(migrationSummaries))
		if waitErr := gSummary.Wait(); waitErr != nil {
			log.Errorf("migrateTableDataByChunks failed, taskId:%d, err:%v", taskId, waitErr)
			return waitErr
		}
	}
	return nil
}

func buildFullDataMigrationReq(ctx context.Context, taskInfo *task.Task, migrationDetail *migration.TableMigrationDetail, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, columnNameS []string, isRetry bool) (*sql.Stmt, *tmso2t.Rows, string, error) {
	sqlStr00 := tmso2t.GenMySQLTablePrepareStmt(transfercommon.StringUPPER(migrationDetail.SchemaNameT), migrationDetail.TableNameT, columnNameS, migrationParam.GetInsertBatchSize(), migrationParam.GetSQLStatementType(isRetry))
	log.Debugf("buildFullDataMigrationReq, sqlStr:%s", sqlStr00)
	stmt, prepareErr := migrationDBConns.GetTargetDB().MySQLDB.PrepareContext(ctx, sqlStr00)
	if prepareErr != nil {
		log.Errorf("prepare target db stmt failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskInfo.TaskID, migrationDetail.SchemaNameT, migrationDetail.TableNameT, prepareErr)
		return nil, nil, "", prepareErr
	}

	var globalScn uint64
	var parseErr error

	if migrationParam.GetConsistentRead() {
		log.Infof("buildFullDataMigrationReq, consistent read is on, taskId:%d, chunkId:%d, scn:%s", taskInfo.TaskID, migrationDetail.ID, taskInfo.ScnNumber)
		if taskInfo.ScnNumber == "" {
			log.Errorf("buildFullDataMigrationReq, task_id %d,consistent read is on, but scn number is empty", taskInfo.TaskID)
			return nil, nil, "", errors.New("consistent read is on, but scn number is empty")
		}
		globalScn, parseErr = strconv.ParseUint(taskInfo.ScnNumber, 10, 64)
		if parseErr != nil {
			log.Errorf("buildFullDataMigrationReq, task_id %d, parse scn number failed, scn:%s, err:%v", taskInfo.TaskID, taskInfo.ScnNumber, parseErr)
			return nil, nil, "", parseErr
		}
	}

	tableNameS := fmt.Sprintf(`"%v"`, migrationDetail.TableNameS)
	if migrationDetail.TablePartition != "" {
		tableNameS = fmt.Sprintf(`"%v" PARTITION(%v)`, migrationDetail.TableNameS, migrationDetail.TablePartition)
	}

	return stmt, tmso2t.NewRows(ctx,
		meta.FullSyncMeta{
			SchemaNameS:    fmt.Sprintf(`"%v"`, migrationDetail.SchemaNameS),
			TableNameS:     tableNameS,
			SchemaNameT:    migrationDetail.SchemaNameT,
			TableNameT:     migrationDetail.TableNameT,
			ConsistentRead: stringutil.BoolToYesOrNo(migrationParam.ConsistentRead),
			SQLHint:        migrationDetail.SqlHintS,
			ColumnDetailS:  migrationDetail.ColumnDetailS,
			ChunkDetailS:   migrationDetail.ChunkDetailS,
			GlobalScnS:     globalScn,
		},
		migrationDBConns.GetSourceDB(),
		migrationDBConns.GetTargetDB(),
		stmt,
		transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(migrationDBConns.GetSourceDS().Charset)],
		transfercommon.StringUPPER(migrationDBConns.GetTargetDS().Charset),
		migrationParam.GetApplyThreads(),
		migrationParam.GetInsertBatchSize(),
		migrationParam.GetCallTimeout(),
		migrationParam.GetSQLStatementType(isRetry),
		columnNameS,
	), sqlStr00, nil
}

func BuildFullDataReplayReq(ctx context.Context, taskInfo *task.Task, chunk *migration.TableMigrationDetail, rowIdRange string, batchSize int, dbConns *MigrationDBConns, params *structs.FullMigrationConfigParam, columnNameS []string, isRetry bool) (*sql.Stmt, *tmso2t.Rows, string, error) {
	sqlStr00 := tmso2t.GenMySQLTablePrepareStmt(transfercommon.StringUPPER(chunk.SchemaNameT), chunk.TableNameT, columnNameS, batchSize, params.GetSQLStatementType(isRetry))
	stmt, prepareErr := dbConns.GetTargetDB().MySQLDB.PrepareContext(ctx, sqlStr00)
	if prepareErr != nil {
		log.Errorf("prepare target db stmt failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskInfo.TaskID, chunk.SchemaNameT, chunk.TableNameT, prepareErr)
		return nil, nil, "", prepareErr
	}

	var globalScn uint64
	var parseErr error

	if params.GetConsistentRead() {
		log.Infof("buildFullDataReplayReq, consistent read is on, taskId:%d, chunkId:%d, scn:%s", taskInfo.TaskID, chunk.ID, taskInfo.ScnNumber)
		if taskInfo.ScnNumber == "" {
			log.Errorf("buildFullDataReplayReq, task_id %d,consistent read is on, but scn number is empty", taskInfo.TaskID)
			return nil, nil, "", errors.New("consistent read is on, but scn number is empty")
		}
		globalScn, parseErr = strconv.ParseUint(taskInfo.ScnNumber, 10, 64)
		if parseErr != nil {
			log.Errorf("buildCSVDataMigrationReq, task_id %d, parse scn number failed, scn:%s, err:%v", taskInfo.TaskID, taskInfo.ScnNumber, parseErr)
			return nil, nil, "", parseErr
		}
	}

	return stmt, tmso2t.NewRows(ctx,
		meta.FullSyncMeta{
			SchemaNameS:    fmt.Sprintf(`"%v"`, chunk.SchemaNameS),
			TableNameS:     fmt.Sprintf(`"%v"`, chunk.TableNameS),
			SchemaNameT:    chunk.SchemaNameT,
			TableNameT:     chunk.TableNameT,
			ConsistentRead: stringutil.BoolToYesOrNo(params.GetConsistentRead()),
			SQLHint:        chunk.SqlHintS,
			ColumnDetailS:  chunk.ColumnDetailS,
			ChunkDetailS:   rowIdRange,
			GlobalScnS:     globalScn,
		},
		dbConns.GetSourceDB(),
		dbConns.GetTargetDB(),
		stmt,
		transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(dbConns.GetSourceDS().Charset)],
		transfercommon.StringUPPER(dbConns.GetTargetDS().Charset),
		params.GetApplyThreads(),
		params.GetInsertBatchSize(),
		params.GetCallTimeout(),
		params.GetSQLStatementType(isRetry),
		columnNameS,
	), sqlStr00, nil
}

// createSingleFullMigrationDetailAndSummary create single full migration detail and update summary
func createSingleFullMigrationDetailAndSummary(ctx context.Context,
	schemaTable *channel.ChannelSchemaTable,
	sourceDS *datasource.Datasource,
	sourceColumnInfo string,
	taskInfo *task.Task,
	customConf TableMigrationCustomConf,
	tableTypeS string,
) error {

	whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())
	log.Infof("table [%s.%s], disable split or statistics is empty or chunk size is zero, stop splitting, save record and return, taskId:%d, enableSplit:%v, wherePrefix:%s, whereRange:%s", schemaTable.SchemaNameS, schemaTable.TableNameS, taskInfo.TaskID, customConf.GetEnableSplit(), customConf.GetWherePrefix(), whereRange)

	err := models.Transaction(ctx, func(txnCtx context.Context) error {
		detail := buildFullDataTableMigrationDetail(taskInfo, schemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)

		summary := buildMigrationSummary(taskInfo, sourceDS, schemaTable, NO_CHUNK_DEFAULT_CHUNK_SIZE, tableTypeS)
		summary.TableTypeS = tableTypeS

		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(txnCtx, detail); createErr != nil {
			return createErr
		}
		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(txnCtx, summary); createErr != nil {
			return createErr
		}
		return nil
	})
	return err

}

// createSingleFullMigrationDetailAndSummaryInClusterMode create single full migration detail and update summary
func createSingleFullMigrationDetailAndSummaryInClusterMode(ctx context.Context, schemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, sourceDS *datasource.Datasource, sourceColumnInfo string, taskInfo *task.Task, customConf TableMigrationCustomConf, tableTypeS string) error {
	whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())
	log.Infof("table [%s.%s], disable split or statistics is empty or chunk size is zero, stop splitting, save record and return, taskId:%d, enableSplit:%v, wherePrefix:%s, whereRange:%s", schemaTable.SchemaNameS, schemaTable.TableNameS, taskInfo.TaskID, customConf.GetEnableSplit(), customConf.GetWherePrefix(), whereRange)

	trxErr := models.Transaction(ctx, func(txnCtx context.Context) error {
		// 子任务的
		subTask := buildSubTasks(taskInfo, schemaTable)
		if _, err := models.GetTaskReaderWriter().CreateTask(txnCtx, subTask); err != nil {
			return err
		}

		subTaskChannelSchemaTable := buildSubTaskChannelSchemaTable(subTask, schemaTable)
		subTaskChannelSchemaObject := buildSubTaskChannelSchemaObject(subTask, channelSchemaObject)
		summary := buildMigrationSummary(subTask, sourceDS, schemaTable, NO_CHUNK_DEFAULT_CHUNK_SIZE, tableTypeS)
		detail := buildFullDataTableMigrationDetail(subTask, schemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)

		if _, err := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(txnCtx, summary); err != nil {
			return err
		}
		if _, err := models.GetChannelReaderWriter().SaveChannelSchemaTable(txnCtx, subTaskChannelSchemaTable); err != nil {
			return err
		}
		if _, err := models.GetChannelReaderWriter().CreateChannelSchemaObject(txnCtx, subTaskChannelSchemaObject); err != nil {
			return err
		}
		if _, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(txnCtx, detail); createErr != nil {
			return createErr
		}
		return nil
	})
	return trxErr
}

// SplitFullDataChunksInSingleMode 划分chunk块信息，仅支持单机版
func SplitFullDataChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	splitErr := splitFullDataChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, migrationDBConns, migrationParam, tableTypeS)
	if splitErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], split full data chunks in single mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr, tableTypeS)

	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeSplit, taskTableConfigMap, channelSchemaTable, migrationDBConns, taskInfo, splitErr, tableTypeS)
	return splitErr
}

func splitFullDataChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	sourceDB := migrationDBConns.GetSourceDB()
	targetDB := migrationDBConns.GetTargetDB()
	sourceDS := migrationDBConns.GetSourceDS()
	oracleCollation := migrationDBConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail 以及下游真实表记录，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInSingleMode(ctx, taskInfo, channelSchemaTable, targetDB); clearErr != nil {
			log.Errorf("table [%s.%s], clear migration old data failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, clearErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, clearErr)
			return clearErr
		}
	}

	tableSummaries, tableDetails, getInfoErr := getTableMigrationMetasBySchemaTable(ctx, channelSchemaTable)
	if getInfoErr != nil {
		log.Errorf("table [%s.%s], get table migration metas failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getInfoErr)
		createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, getInfoErr)
		return getInfoErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
	if len(tableSummaries) != 0 || len(tableDetails) != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v, taskId:%d",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, migrationParam.GetChunkSize(), sourceDS)

	im := buildO2TFullDataConfig(ctx, channelSchemaTable, sourceDS, migrationDBConns, sourceDB)

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		if strings.Contains(adjustErr.Error(), "column info cann't be null") {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			createErr := createTableNotExistMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf)
			if createErr != nil {
				createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
				return createErr
			}
			return nil
		} else {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, adjustErr)
			return adjustErr
		}
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, getStatisticsErr)
		return getStatisticsErr
	}

	// 退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleFullMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 || !customConf.GetEnableSplit() {
		createErr := createSingleFullMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	chunkRes, chunkNum, splitErr := createSplitTaskAndGetChunkResults(sourceDB, channelSchemaTable, customConf.GetChunkSize(), migrationParam.GetCallTimeout(), migrationParam.GetRemoveSplitTask())
	if splitErr != nil {
		log.Errorf("table [%s.%s], split task and get chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleFullMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	migrationDetails := buildFullDataMigrationDetails(chunkNum, chunkRes, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf)

	migrationSummary := buildDefaultMigrationSummaryWithChunkTotalNum(taskInfo, sourceDS, channelSchemaTable, int64(len(migrationDetails)), customConf.GetChunkSize(), tableTypeS)

	// 元数据库信息 开启事务写入
	trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
		_, err := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(trxCtx, migrationDetails, migrationParam.GetInsertBatchSize())
		if err != nil {
			return err
		}
		_, err = models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(trxCtx, migrationSummary)
		if err != nil {
			return err
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("table [%s.%s], meta database data init failed, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, trxErr)
		createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, trxErr)
		return trxErr
	}
	return nil
}

// FetchFullDataChunksInSingleMode 划分chunk块信息
func FetchFullDataChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	fetchErr := fetchFullDataChunksInSingleMode(ctx, clearData, channelSchemaTable, taskTableConfigMap, taskInfo, migrationDBConns, migrationParam, tableTypeS)
	if fetchErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], fetch full data chunks in single mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, fetchErr)

	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeFetch, taskTableConfigMap, channelSchemaTable, migrationDBConns, taskInfo, fetchErr, tableTypeS)
	return fetchErr
}

func fetchFullDataChunksInSingleMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	sourceDB := migrationDBConns.GetSourceDB()
	targetDB := migrationDBConns.GetTargetDB()
	sourceDS := migrationDBConns.GetSourceDS()
	oracleCollation := migrationDBConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail 以及下游真实表记录，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInSingleMode(ctx, taskInfo, channelSchemaTable, targetDB); clearErr != nil {
			log.Errorf("table [%s.%s], clear migration old data failed, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, clearErr)
			return clearErr
		}
	}

	tableSummaries, tableDetails, getInfoErr := getTableMigrationMetasBySchemaTable(ctx, channelSchemaTable)
	if getInfoErr != nil {
		log.Errorf("table [%s.%s], get table migration metas failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getInfoErr)
		return getInfoErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
	if len(tableSummaries) != 0 || len(tableDetails) != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v, taskId:%d",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(tableSummaries), len(tableDetails), taskInfo.TaskID)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, ADG_DEFAULT_CHUNK_SIZE, sourceDS)

	im := buildO2TFullDataConfig(ctx, channelSchemaTable, sourceDS, migrationDBConns, sourceDB)

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		if strings.Contains(adjustErr.Error(), "column info cann't be null") {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			createErr := createTableNotExistMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf)
			if createErr != nil {
				return createErr
			}
			return nil
		} else {
			log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
			return adjustErr
		}
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		return getStatisticsErr
	}

	// 退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleFullMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 {
		createErr := createSingleFullMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	chunkRes, chunkNum, splitErr := fetchTableChunkResults(sourceDB, channelSchemaTable)
	if splitErr != nil {
		log.Errorf("table [%s.%s], fetch table chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleFullMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	migrationDetails := buildFullDataMigrationDetails(chunkNum, chunkRes, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf)

	migrationSummary := buildDefaultMigrationSummaryWithChunkTotalNum(taskInfo, sourceDS, channelSchemaTable, int64(len(migrationDetails)), ADG_DEFAULT_CHUNK_SIZE, tableTypeS)

	// 元数据库信息 开启事务写入
	trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
		_, err := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(trxCtx, migrationDetails, migrationParam.GetInsertBatchSize())
		if err != nil {
			return err
		}
		_, err = models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(trxCtx, migrationSummary)
		if err != nil {
			return err
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("table [%s.%s], meta database data init failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, trxErr)
		return trxErr
	}
	return nil
}

func buildFullDataMigrationDetails(chunkNum int, chunkRes []map[string]string, taskInfo *task.Task, channelSchemaTable *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, sourceColumnInfo string, customConf TableMigrationCustomConf) []*migration.TableMigrationDetail {
	migrationDetails := make([]*migration.TableMigrationDetail, 0, chunkNum)
	for _, res := range chunkRes {
		_, combineWhereRange := buildChunkWhereCondition(res, customConf.GetWherePrefix())
		detail := buildFullDataTableMigrationDetail(taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), combineWhereRange)
		migrationDetails = append(migrationDetails, detail)
	}
	return migrationDetails
}

func buildO2TFullDataConfig(ctx context.Context, channelSchemaTable *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, migrationDBConns *MigrationDBConns, sourceDB *oracle.Oracle) *tmso2t.Migrate {
	im := &tmso2t.Migrate{
		Ctx: ctx,
		Cfg: &transferconfig.Config{SchemaConfig: transferconfig.SchemaConfig{SourceSchema: channelSchemaTable.SchemaNameS},
			OracleConfig: transferconfig.OracleConfig{Charset: sourceDS.Charset},
			MySQLConfig:  transferconfig.MySQLConfig{Charset: migrationDBConns.targetDS.Charset},
		},
		Oracle: sourceDB,
	}
	return im
}

func SplitFullDataChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	splitErr := splitFullDataChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, migrationDBConns, migrationParam, tableTypeS)
	if splitErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], split full data chunks in cluster mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)

	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeSplit, taskTableConfigMap, channelSchemaTable, migrationDBConns, taskInfo, splitErr, tableTypeS)
	return splitErr
}

func splitFullDataChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	sourceDB := migrationDBConns.GetSourceDB()
	targetDB := migrationDBConns.GetTargetDB()
	sourceDS := migrationDBConns.GetSourceDS()
	oracleCollation := migrationDBConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail 以及下游真实表记录，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInClusterMode(ctx, taskInfo, channelSchemaTable, targetDB); clearErr != nil {
			log.Errorf("table [%s.%s], clear migration old data failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, clearErr)
			return clearErr
		}
	}
	tableDetailCount, tableSummaryCount, countErr := models.GetFullDataMigrationReaderWriter().CountSubTaskMigrationSummaryAndDetailByTaskID(ctx, taskInfo.TaskID, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if countErr != nil {
		log.Errorf("table [%s.%s], count sub task migration summary and detail by taskID failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, countErr)
		return countErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount, taskInfo.TaskID)
	if tableSummaryCount != 0 || tableDetailCount != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v, taskId:%d",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount, taskInfo.TaskID)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, migrationParam.GetChunkSize(), sourceDS)

	im := &tmso2t.Migrate{
		Ctx: ctx,
		Cfg: &transferconfig.Config{
			SchemaConfig: transferconfig.SchemaConfig{SourceSchema: channelSchemaTable.SchemaNameS},
			OracleConfig: transferconfig.OracleConfig{Charset: sourceDS.Charset},
			MySQLConfig:  transferconfig.MySQLConfig{Charset: migrationDBConns.targetDS.Charset}},
		Oracle: sourceDB,
	}

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
		return adjustErr
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		return getStatisticsErr
	}

	// 退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleFullMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		if createErr != nil {
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 || !customConf.GetEnableSplit() {
		createErr := createSingleFullMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	chunkRes, chunkNum, splitErr := createSplitTaskAndGetChunkResults(sourceDB, channelSchemaTable, customConf.GetChunkSize(), migrationParam.GetCallTimeout(), migrationParam.GetRemoveSplitTask())
	if splitErr != nil {
		log.Errorf("table [%s.%s], split task and get chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleFullMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	migrationDetails := make([]*migration.TableMigrationDetail, 0, chunkNum)
	for _, res := range chunkRes {
		_, combineWhereRange := buildChunkWhereCondition(res, customConf.GetWherePrefix())
		migrationDetails = append(migrationDetails,
			buildFullDataTableMigrationDetail(taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), combineWhereRange))
	}

	createErr := createSubTasksAndForkMetadata(ctx, channelSchemaTable, taskInfo, chunkNum, migrationParam, migrationDetails, sourceDS, channelSchemaObject, customConf.GetChunkSize())
	if createErr != nil {
		log.Errorf("table [%s.%s], create sub tasks and fork metadata failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		return createErr
	}
	return nil
}

func RetryFullDataMigrationFailedChunks(ctx context.Context, req FailedChunkBatchRetryReq, taskInfo *task.Task, failedDetails []migration.TableMigrationDetail) error {
	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeFullData, taskInfo.ChannelId, taskInfo.TaskID)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)

	taskId := taskInfo.TaskID
	channelId := taskInfo.ChannelId

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	detailChannelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, taskId:%d, channelId:%d, err:%v", taskId, channelId, getChannelErr)
		return fmt.Errorf("get channel info failed, taskId:%d, channelId:%d, err:%v", taskId, channelId, getChannelErr)
	}
	taskParamTempConfig, getTemplateErr := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if getTemplateErr != nil {
		log.Errorf("get task template info failed, taskId:%d, taskParamTemplateId:%d, err:%v", taskId, taskInfo.TaskParamTmplateId, getTemplateErr)
		return fmt.Errorf("get task template info failed, taskId:%d, taskParamTemplateId:%d, err:%v", taskId, taskInfo.TaskParamTmplateId, getTemplateErr)
	}

	// init migration param
	migrationParam, buildParamErr := BuildMigrationConfigParams(ctx, taskInfo.ChannelId, taskInfo.TaskID, taskParamTempConfig.TaskparamTemplateID)
	if buildParamErr != nil {
		log.Errorf("build migration param failed, taskId:%d, err:%v", taskId, buildParamErr)
		return buildParamErr
	}

	// create database conn
	migrationDBConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, detailChannelInfo, taskInfo.TaskID)
	if setUpErr != nil {
		log.Errorf("set up database conns and collation failed, taskId:%d, err:%v", taskId, setUpErr)
		return setUpErr
	}

	// 获取失败 chunk
	startExecTime := time.Now()
	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      req.TaskID,
		SchemaNameS: req.SchemaNameS,
		TableNameS:  req.TableNameS,
	})
	if getSummaryErr != nil {
		log.Errorf("get table migration summary failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", req.TaskID, req.SchemaNameS, req.TableNameS, getSummaryErr)
		return getSummaryErr
	}
	if len(tableSummaries) == 0 {
		return fmt.Errorf("table migration summary not found, taskId:%d, schemaName:%s, tableName:%s", req.TaskID, req.SchemaNameS, req.TableNameS)
	}
	d := tableSummaries[0]

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		if !strings.EqualFold(d.TaskStatus, transfercommon.TaskStatusRunning) {
			updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
				TaskID:      req.TaskID,
				SchemaNameS: req.SchemaNameS,
				TableNameS:  req.TableNameS,
			}, map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusRunning,
			})
			if updateErr != nil {
				return updateErr
			}
		}

		_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
			ChannelID:   taskInfo.ChannelId,
			TaskID:      req.TaskID,
			SchemaNameS: req.SchemaNameS,
			TableNameS:  req.TableNameS,
			Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, migration retry initiated", req.SchemaNameS, req.TableNameS)),
			LogLevel:    log.LogInfo,
		})
		if createErr != nil {
			return fmt.Errorf("create progress log detail record failed: %v", createErr)
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("update migration summary status and create logs in transaction failed, err:%v", trxErr)
		return trxErr
	}

	g := &errgroup.Group{}
	g.SetLimit(migrationParam.GetSqlThreads())

	progressHelper := buildProgressHelper(taskInfo.ChannelId, taskInfo.TaskID, d.SchemaNameS, d.TableNameS, constants.MigrationLogTypeFullData)
	progressHelper.SetTotal(len(failedDetails))
	progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
	defer progressHelper.Close()
	defer progressHelper.DisplayAndSaveProgress(ctx)

	summaryId := d.ID
	schemaName := d.SchemaNameS
	tableName := d.TableNameS

	for idx := range failedDetails {
		migrationDetail := &(failedDetails[idx])

		chunkId := migrationDetail.ID
		chunk := migrationDetail.ChunkDetailS

		g.Go(func() error {

			getColumnsSQL := BuildGetColumnSQL(migrationDetail)
			columnNameS, getColumnErr := migrationDBConns.GetSourceDB().GetOracleTableRowsColumn(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[migrationDBConns.sourceDS.Charset],
				transfercommon.StringUPPER(migrationDBConns.targetDS.Charset))
			if getColumnErr != nil {
				progressHelper.IncreaseFailedNum()
				errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)
				log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
					taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
				errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeFullData)
				if errC != nil {
					log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
					return errC
				} else {
					log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
				if errS != nil {
					log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
					return errS
				} else {
					log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				return getColumnErr
			}

			if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
				SchemaName: schemaName,
				TableName:  tableName,
			}]; ok {
				if taskTableConfig.OperatorTag == "Y" {
					oracleCollation := migrationDBConns.GetOracleCollation()
					oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
					if err != nil {
						log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
						return err
					}
					migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
				}
				if taskTableConfig.TablePartition != "" {
					migrationDetail.TablePartition = taskTableConfig.TablePartition
				}
			}

			stmt, migrationReq, preparedSQL, buildErr := buildFullDataMigrationReq(ctx, taskInfo, migrationDetail, migrationDBConns, migrationParam, columnNameS, true)
			if buildErr != nil {
				progressHelper.IncreaseFailedNum()
				errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)
				log.Errorf("buildFullDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
					taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
				errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeFullData)
				if errC != nil {
					log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
					return errC
				} else {
					log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
				if errS != nil {
					log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
					return errS
				} else {
					log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				return buildErr
			}

			migrateErr := doDataMigrationWithStmt(ctx, transferDBMigrateFullDataFunc, taskInfo.ChannelId, migrationDetail, migrationReq, progressHelper, constants.MigrationLogTypeFullData, stmt, preparedSQL)()
			if migrateErr != nil {
				log.Errorf("doDataMigrationWithStmt failed, migrate retry failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err: %v",
					taskId, summaryId, chunkId, chunk, schemaName, tableName, migrateErr)
				return migrateErr
			}
			return nil
		})
	}
	if waitErr := g.Wait(); waitErr != nil {
		log.Errorf("RetryFullDataMigrationFailedChunks failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, waitErr)
		return waitErr
	}
	// 执行以后重新查询是否有失败的
	failedStatus := []string{transfercommon.TaskStatusFailed, constants.MigrationStatusInvalid}
	failedAgainChunkDetails, getFailedDetailErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetailWithStatus(ctx, &migration.TableMigrationDetail{
		TaskID:      d.TaskID,
		SchemaNameS: d.SchemaNameS,
		TableNameS:  d.TableNameS,
		SchemaNameT: d.SchemaNameT,
		TableNameT:  d.TableNameT,
	}, failedStatus)
	if getFailedDetailErr != nil {
		log.Errorf("after retry failed chunks, get failed chunks failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, getFailedDetailErr)
		return getFailedDetailErr
	}

	if len(failedAgainChunkDetails) == 0 {
		log.Infof("all failed chunks retry success, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s", taskId, summaryId, schemaName, tableName)
		trxErr2 := models.Transaction(ctx, func(transactionCtx context.Context) error {
			updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
				&migration.TableMigrationSummary{
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
				}, map[string]interface{}{
					"Task_Status":        transfercommon.TaskStatusSuccess,
					"Chunk_Success_Nums": d.ChunkTotalNums,
					"Chunk_Failed_Nums":  0,
					"Duration":           time.Now().Sub(startExecTime).Seconds(),
				})
			if updateErr != nil {
				return updateErr
			}

			_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
				ChannelID:   detailChannelInfo.ChannelId,
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
				Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, migration retry succeeded", req.SchemaNameS, req.TableNameS)),
				LogLevel:    log.LogInfo,
			})
			if createErr != nil {
				return fmt.Errorf("create progress log detail record failed: %v", createErr)
			}
			return nil
		})
		if trxErr2 != nil {
			log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, trxErr2)
			return trxErr2
		}
		return nil
	}
	log.Infof("failed chunks retry failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, failed chunks len:%d", taskId, summaryId, schemaName, tableName, len(failedAgainChunkDetails))
	trxErr2 := models.Transaction(ctx, func(transactionCtx context.Context) error {
		updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
			&migration.TableMigrationSummary{
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
			}, map[string]interface{}{
				"Task_Status":        transfercommon.TaskStatusFailed,
				"Chunk_Success_Nums": d.ChunkTotalNums - int64(len(failedAgainChunkDetails)),
				"Chunk_Failed_Nums":  len(failedAgainChunkDetails),
				"Duration":           time.Now().Sub(startExecTime).Seconds(),
			})
		if updateErr != nil {
			return updateErr
		}
		_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
			ChannelID:   detailChannelInfo.ChannelId,
			TaskID:      d.TaskID,
			SchemaNameS: d.SchemaNameS,
			TableNameS:  d.TableNameS,
			Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, migration retry failed", req.SchemaNameS, req.TableNameS)),
			LogLevel:    log.LogError,
		})
		if createErr != nil {
			return fmt.Errorf("create progress log detail record failed: %v", createErr)
		}
		return nil
	})
	if trxErr2 != nil {
		log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, trxErr2)
		return trxErr2
	}

	return nil
}

func BatchRetryFullDataMigrationByTableFailed(ctx context.Context, taskInfo *task.Task, summaries []*migration.TableMigrationSummary) error {
	taskId := taskInfo.TaskID
	channelId := taskInfo.ChannelId

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeFullData, channelId, taskId)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)

	detailChannelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, taskId:%d, channelId:%d, err:%v", taskId, channelId, getChannelErr)
		return fmt.Errorf("get channel info failed, taskId:%d, channelId:%d, err:%v", taskId, channelId, getChannelErr)
	}
	taskParamTempConfig, getTemplateErr := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if getTemplateErr != nil {
		log.Errorf("get task template info failed, taskId:%d, taskParamTemplateId:%d, err:%v", taskId, taskInfo.TaskParamTmplateId, getTemplateErr)
		return fmt.Errorf("get task template info failed, taskId:%d, taskParamTemplateId:%d, err:%v", taskId, taskInfo.TaskParamTmplateId, getTemplateErr)
	}

	// init migration param
	migrationParam, buildParamErr := BuildMigrationConfigParams(ctx, channelId, taskId, taskParamTempConfig.TaskparamTemplateID)
	if buildParamErr != nil {
		log.Errorf("build migration param failed, taskId:%d, err:%v", taskId, buildParamErr)
		return buildParamErr
	}

	// init datasource and conns
	migrationDBConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, detailChannelInfo, taskInfo.TaskID)
	if setUpErr != nil {
		log.Errorf("set up database conns and collation failed, taskId:%d, err:%v", taskId, setUpErr)
		return setUpErr
	}

	// 重运行失败表 chunk 的任务 failed
	gS := &errgroup.Group{}
	gS.SetLimit(migrationParam.GetTableThreads())

	for _, summary := range summaries {
		d := summary

		summaryId := d.ID
		schemaName := d.SchemaNameS
		tableName := d.TableNameS

		gS.Go(func() error {
			startExecTime := time.Now()

			failedDetails, getDetailErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
				TaskStatus:  transfercommon.TaskStatusFailed,
			})
			if getDetailErr != nil {
				return getDetailErr
			}

			progressHelper := buildProgressHelper(taskInfo.ChannelId, taskInfo.TaskID, d.SchemaNameS, d.TableNameS, constants.MigrationLogTypeFullData)
			progressHelper.SetTotal(len(failedDetails))
			progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
			defer progressHelper.Close()
			defer progressHelper.DisplayAndSaveProgress(ctx)

			originSummary, getSummaryErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
				SchemaNameT: d.SchemaNameT,
				TableNameT:  d.TableNameT,
			})
			if getSummaryErr != nil {
				return getSummaryErr
			}

			trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
				if !strings.EqualFold(d.TaskStatus, transfercommon.TaskStatusRunning) {
					updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status": transfercommon.TaskStatusRunning,
					})
					if updateErr != nil {
						return updateErr
					}
				}

				_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
					ChannelID:   taskInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, migration retry initiated", d.SchemaNameS, d.TableNameS)),
					LogLevel:    log.LogInfo,
				})
				if createErr != nil {
					return fmt.Errorf("create progress log detail record failed: %v", createErr)
				}
				return nil
			})
			if trxErr != nil {
				return trxErr
			}

			if len(failedDetails) == 0 {
				// TODO 要不要增加日志
				return nil
			}
			g := &errgroup.Group{}
			g.SetLimit(migrationParam.GetSqlThreads())

			for idx := range failedDetails {
				migrationDetail := &(failedDetails[idx])

				chunkId := migrationDetail.ID
				chunk := migrationDetail.ChunkDetailS

				g.Go(func() error {
					getColumnsSQL := BuildGetColumnSQL(migrationDetail)
					columnNameS, getColumnErr := migrationDBConns.GetSourceDB().GetOracleTableRowsColumn(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[migrationDBConns.sourceDS.Charset],
						transfercommon.StringUPPER(migrationDBConns.targetDS.Charset))
					if getColumnErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)

						log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeFullData)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						return getColumnErr
					}

					if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
						SchemaName: schemaName,
						TableName:  tableName,
					}]; ok {
						if taskTableConfig.OperatorTag == "Y" {
							oracleCollation := migrationDBConns.GetOracleCollation()
							oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
							if err != nil {
								log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
								return err
							}
							migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
						}
						if taskTableConfig.TablePartition != "" {
							migrationDetail.TablePartition = taskTableConfig.TablePartition
						}
					}

					stmt, req, preparedSQL, buildErr := buildFullDataMigrationReq(ctx, taskInfo, migrationDetail, migrationDBConns, migrationParam, columnNameS, true)
					if buildErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)
						log.Errorf("buildFullDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeFullData)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeFullData)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						return buildErr
					}

					migrateErr := doDataMigrationWithStmt(ctx, transferDBMigrateFullDataFunc, channelId, migrationDetail, req, progressHelper, constants.MigrationLogTypeFullData, stmt, preparedSQL)()
					if migrateErr != nil {
						log.Errorf("doDataMigrationWithStmt failed, migrate retry failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err: %v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, migrateErr)
						return migrateErr
					}
					return nil
				})
			}
			if waitErr := g.Wait(); waitErr != nil {
				log.Errorf("BatchRetryFullDataMigrationByTableFailed failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, waitErr)
				return waitErr
			}

			// 更新详情以及日志
			failedChunkDetails, getFailedChunkErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
				TaskID:      d.TaskID,
				SchemaNameS: d.SchemaNameS,
				TableNameS:  d.TableNameS,
				SchemaNameT: d.SchemaNameT,
				TableNameT:  d.TableNameT,
				TaskStatus:  transfercommon.TaskStatusFailed,
			})
			if getFailedChunkErr != nil {
				return getFailedChunkErr
			}
			if len(failedChunkDetails) == 0 {
				trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
					updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
						&migration.TableMigrationSummary{
							TaskID:      d.TaskID,
							SchemaNameS: d.SchemaNameS,
							TableNameS:  d.TableNameS,
						}, map[string]interface{}{
							"Task_Status":        transfercommon.TaskStatusSuccess,
							"Chunk_Success_Nums": originSummary[0].ChunkTotalNums,
							"Chunk_Failed_Nums":  0,
							"Duration":           time.Now().Sub(startExecTime).Seconds(),
						})
					if updateErr != nil {
						return updateErr
					}

					_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
						ChannelID:   detailChannelInfo.ChannelId,
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
						Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, migration retry succeeded", d.SchemaNameS, d.TableNameS)),
						LogLevel:    log.LogInfo,
					})
					if createErr != nil {
						return fmt.Errorf("create progress log detail record failed: %v", createErr)
					}
					return nil
				})
				if trxErr != nil {
					log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, trxErr)
					return trxErr
				}
				return nil
			}
			trxErr2 := models.Transaction(ctx, func(transactionCtx context.Context) error {
				updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx,
					&migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status":        transfercommon.TaskStatusFailed,
						"Chunk_Success_Nums": originSummary[0].ChunkTotalNums - int64(len(failedChunkDetails)),
						"Chunk_Failed_Nums":  len(failedChunkDetails),
						"Duration":           time.Now().Sub(startExecTime).Seconds(),
					})
				if updateErr != nil {
					return updateErr
				}
				_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
					ChannelID:   detailChannelInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, migration retry failed", d.SchemaNameS, d.TableNameS)),
					LogLevel:    log.LogError,
				})
				if createErr != nil {
					return fmt.Errorf("create progress log detail record failed: %v", createErr)
				}
				return nil
			})
			if trxErr2 != nil {
				log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, trxErr2)
				return trxErr2
			}
			return nil
		})
	}

	if waitErr := gS.Wait(); waitErr != nil {
		log.Errorf("BatchRetryFullDataMigrationByTableFailed failed, taskId:%d, summaryLen:%d, err:%v", taskId, len(summaries), waitErr)
		return waitErr
	}
	return nil
}

func FetchFullDataChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	fetchErr := fetchFullDataChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, migrationDBConns, migrationParam, tableTypeS)
	if fetchErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], fetch full data chunks in cluster mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, fetchErr)

	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeFetch, taskTableConfigMap, channelSchemaTable, migrationDBConns, taskInfo, fetchErr, tableTypeS)
	return fetchErr
}

func fetchFullDataChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, migrationDBConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, tableTypeS string) error {
	sourceDB := migrationDBConns.GetSourceDB()
	targetDB := migrationDBConns.GetTargetDB()
	sourceDS := migrationDBConns.GetSourceDS()
	oracleCollation := migrationDBConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail 以及下游真实表记录，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInClusterMode(ctx, taskInfo, channelSchemaTable, targetDB); clearErr != nil {
			log.Errorf("table [%s.%s], clear migration old data failed, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, clearErr)
			return clearErr
		}
	}
	tableDetailCount, tableSummaryCount, countErr := models.GetFullDataMigrationReaderWriter().CountSubTaskMigrationSummaryAndDetailByTaskID(ctx, taskInfo.TaskID, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if countErr != nil {
		log.Errorf("table [%s.%s], count sub task migration summary and detail by taskID failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, countErr)
		return countErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v]",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount)
	if tableSummaryCount != 0 || tableDetailCount != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, ADG_DEFAULT_CHUNK_SIZE, sourceDS)

	im := &tmso2t.Migrate{
		Ctx: ctx,
		Cfg: &transferconfig.Config{
			SchemaConfig: transferconfig.SchemaConfig{SourceSchema: channelSchemaTable.SchemaNameS},
			OracleConfig: transferconfig.OracleConfig{Charset: sourceDS.Charset},
			MySQLConfig:  transferconfig.MySQLConfig{Charset: migrationDBConns.targetDS.Charset}},
		Oracle: sourceDB,
	}

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
		return adjustErr
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		return getStatisticsErr
	}

	//退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleFullMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createFullDataErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 {
		createErr := createSingleFullMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		log.Errorf("table [%s.%s], create single full migration detail and summary in cluster mode failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		return createErr
	}

	chunkRes, chunkNum, splitErr := fetchTableChunkResults(sourceDB, channelSchemaTable)
	if splitErr != nil {
		log.Errorf("table [%s.%s], fetch table chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleFullMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		log.Errorf("table [%s.%s], create single full migration detail and summary in cluster mode failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		return createErr
	}

	migrationDetails := make([]*migration.TableMigrationDetail, 0, chunkNum)
	for _, res := range chunkRes {
		_, combineWhereRange := buildChunkWhereCondition(res, customConf.GetWherePrefix())
		migrationDetails = append(migrationDetails,
			buildFullDataTableMigrationDetail(taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), combineWhereRange))
	}

	createErr := createSubTasksAndForkMetadata(ctx, channelSchemaTable, taskInfo, chunkNum, migrationParam, migrationDetails, sourceDS, channelSchemaObject, ADG_DEFAULT_CHUNK_SIZE)
	if createErr != nil {
		log.Errorf("table [%s.%s], create sub tasks and fork metadata failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		return createErr
	}
	return nil
}

func GetCreateChunkSql(ctx context.Context, channelId, taskId int) (string, error) {
	log.Infof("start create full data migration task, channelId: %d, taskId: %d", channelId, taskId)
	taskInfo, channelInfo, taskParamTempConfig, _, getErr := GetMigrationMetadata(ctx, channelId, taskId)
	if getErr != nil {
		log.Errorf("get migration metadata failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return "", getErr
	}

	// init migration param
	log.Infof("build migration config params, channelId: %d, taskId: %d", channelId, taskId)
	migrationParam, buildParamErr := BuildMigrationConfigParams(ctx, channelId, taskId, taskParamTempConfig.TaskparamTemplateID)
	if buildParamErr != nil {
		log.Errorf("build migration config params failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildParamErr)
		return "", buildParamErr
	}

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		log.Errorf("build task table config map failed, channelId:%d, taskId:%d, err: %v", channelId, taskId, buildErr)
		return "", buildErr
	}

	// split table into chunks, and save migration details / summaries, and sub tasks in cluster mode
	if taskInfo.ParentTaskID != 0 {
		err := fmt.Errorf("task [%d] parent taskId[%d] is not 0, means subtask which can't execute split, please check", taskInfo.TaskID, taskInfo.ParentTaskID)
		log.Error(err.Error())
		return "", err
	}

	return getSqlSplitTaskTablesIntoFullDataChunks(ctx, channelInfo, taskInfo, migrationParam, taskTableConfigMap), nil
}

func getSqlSplitTaskTablesIntoFullDataChunks(ctx context.Context, channelInfo *channel.ChannelInformation, taskInfo *task.Task, migrationParams *structs.FullMigrationConfigParam, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig) string {

	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID
	var sqlBuffer bytes.Buffer

	// 1. 初始化任务
	// 2. 任务运行
	startTime := time.Now()

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		return ""
	}

	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		log.Errorf("get source datasource failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getSourceErr)
		return ""
	}

	log.Infof("get channel schema tables, channelId:%d, taskId:%d, len:%d", channelId, taskId, len(channelSchemaTables))

	for _, channelSchemaTable := range channelSchemaTables {
		customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, migrationParams.GetChunkSize(), sourceDS)
		log.Infof("start split oracle table [%s.%s] int chunks, channelId:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelId, taskId)
		sqlStr := GetSqlStartOracleCreateChunkByRowID(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, parse.FormatInt(customConf.GetChunkSize()))
		sqlBuffer.WriteString(sqlStr)
	}
	log.Infof("meta database data init done, cost [%v], channelId:%d, taskId:%d", time.Now().Sub(startTime).String(), channelId, taskId)
	return sqlBuffer.String()
}

func GetSqlStartOracleCreateChunkByRowID(schemaName, tableName string, chunkSize string) string {
	chunkSQL := transfercommon.StringsBuilder(`

BEGIN
DBMS_PARALLEL_EXECUTE.DROP_TASK('tmstask_`, schemaName, `_`, tableName, `');
END;
/

SET serveroutput on;
set linesize 1000;
DECLARE
 l_task VARCHAR2(30):='tmstask_`, schemaName, `_`, tableName, `';
BEGIN
  -- Create the TASK
  DBMS_PARALLEL_EXECUTE.create_task(task_name => l_task);
  -- Chunk the table by the ROWID
  BEGIN
  DBMS_OUTPUT.put_line('Start create chunk table:'||'`, schemaName, `'||'.'||'`, tableName, `');
  DBMS_PARALLEL_EXECUTE.CREATE_CHUNKS_BY_ROWID (task_name   => l_task,
                                               table_owner => '`, schemaName, `',
                                               table_name  => '`, tableName, `',
                                               by_row      => TRUE,
                                               chunk_size  => `, chunkSize, `);
  DBMS_OUTPUT.put_line('Complete create chunk table:'||'`, schemaName, `'||'.'||'`, tableName, `');                                                                                              
  EXCEPTION WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('Error in the code :' || SQLERRM);
  END;
END;
/

`)
	return chunkSQL
}

func GetDropChunkSql(ctx context.Context, channelId, taskId int) (string, error) {
	log.Infof("start create full data migration task, channelId: %d, taskId: %d", channelId, taskId)
	taskInfo, channelInfo, taskParamTempConfig, _, getErr := GetMigrationMetadata(ctx, channelId, taskId)
	if getErr != nil {
		log.Errorf("get migration metadata failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return "", getErr
	}

	// init migration param
	log.Infof("build migration config params, channelId: %d, taskId: %d", channelId, taskId)
	migrationParam, buildParamErr := BuildMigrationConfigParams(ctx, channelId, taskId, taskParamTempConfig.TaskparamTemplateID)
	if buildParamErr != nil {
		log.Errorf("build migration config params failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildParamErr)
		return "", buildParamErr
	}

	// init datasource and conns
	log.Infof("setup database conns , channelId: %d, taskId: %d", channelId, taskId)
	dbConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
		return "", setUpErr
	}
	log.Infof("setup database conns success, channelId: %d, taskId: %d", channelId, taskId)

	// split table into chunks, and save migration details / summaries, and sub tasks in cluster mode
	if taskInfo.ParentTaskID != 0 {
		err := fmt.Errorf("task [%d] parent taskId[%d] is not 0, means subtask which can't execute split, please check", taskInfo.TaskID, taskInfo.ParentTaskID)
		log.Error(err.Error())
		return "", err
	}

	return getSqlDropFullDataChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns), nil
}

func getSqlDropFullDataChunks(ctx context.Context, channelInfo *channel.ChannelInformation, taskInfo *task.Task, migrationParams *structs.FullMigrationConfigParam, dbConns *MigrationDBConns) string {

	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID
	var sqlBuffer bytes.Buffer

	// 1. 初始化任务
	// 2. 任务运行
	startTime := time.Now()

	channelSchemaTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		return ""
	}
	log.Infof("get channel schema tables, channelId:%d, taskId:%d, len:%d", channelId, taskId, len(channelSchemaTables))

	for _, channelSchemaTable := range channelSchemaTables {
		log.Infof("start split oracle table [%s.%s] int chunks, channelId:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelId, taskId)

		sql := GetSqlDropChunkByRowID(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
		sqlBuffer.WriteString(sql)
	}
	log.Infof("meta database data init done, cost [%v], channelId:%d, taskId:%d", time.Now().Sub(startTime).String(), channelId, taskId)
	return sqlBuffer.String()
}

func GetSqlDropChunkByRowID(schemaName, tableName string) string {
	chunkSQL := transfercommon.StringsBuilder(`

BEGIN
DBMS_PARALLEL_EXECUTE.DROP_TASK('tmstask_`, schemaName, `_`, tableName, `');
END;
/
`)
	return chunkSQL
}

func GetColumnNamesOracleByTaskTableConfig(ctx context.Context, migrationDBConns *MigrationDBConns, schemaNameS, tableNameS string, oraCollation bool, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig) ([]string, error) {

	columnsINFO, err := models.GetTaskReaderWriter().GetOracleSchemaTableColumn(ctx, migrationDBConns.GetSourceDB().OracleDB, schemaNameS, tableNameS, oraCollation)
	if err != nil {
		return nil, err
	}

	var columnNames []string

	for _, rowCol := range columnsINFO {
		columnName := rowCol["COLUMN_NAME"]
		// 以 utf8mb4 字符集存储 meta
		convertUtf8Raw, err := transfercommon.CharsetConvert([]byte(columnName), transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(migrationDBConns.GetSourceDS().Charset)], transfercommon.CharsetUTF8MB4)
		if err != nil {
			return nil, fmt.Errorf("column [%s] charset convert failed, %v", columnName, err)
		}

		columnName = string(convertUtf8Raw)

		//检查columnName对应的列是否需要unistr
		isUnistr := false
		if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
			SchemaName: schemaNameS,
			TableName:  tableNameS,
		}]; ok {
			if taskTableConfig.OperatorTag == "Y" {
				tableCfgColS := strings.Split(taskTableConfig.ColumnslistOracle, ",")
				for _, cfgColS := range tableCfgColS {
					if columnName == cfgColS {
						isUnistr = true
						break
					}
				}
			}
		}
		//log.Debugf("----columnName:%s, type:%s, isUnistr:%v", columnName, strings.ToUpper(rowCol["DATA_TYPE"]), isUnistr)

		switch strings.ToUpper(rowCol["DATA_TYPE"]) {
		// 数字
		case "NUMBER":
			columnNames = append(columnNames, transfercommon.StringsBuilder(`"`, columnName, `"`))
		case "DECIMAL", "DEC", "DOUBLE PRECISION", "FLOAT", "INTEGER", "INT", "REAL", "NUMERIC", "BINARY_FLOAT", "BINARY_DOUBLE", "SMALLINT":
			columnNames = append(columnNames, transfercommon.StringsBuilder(`"`, columnName, `"`))
		// 字符
		case "VARCHAR", "CHAR", "VARCHAR2":
			if isUnistr {
				columnNames = append(columnNames, transfercommon.StringsBuilder(`unistr("`, columnName, `") as "`, columnName, `"`))
			} else {
				columnNames = append(columnNames, transfercommon.StringsBuilder(`"`, columnName, `"`))
			}
		case "BFILE", "CHARACTER", "LONG", "NCHAR VARYING", "ROWID", "UROWID", "NCHAR", "NVARCHAR2", "NCLOB", "CLOB":
			columnNames = append(columnNames, transfercommon.StringsBuilder(`"`, columnName, `"`))
		// XMLTYPE
		case "XMLTYPE":
			columnNames = append(columnNames, fmt.Sprintf(` XMLSERIALIZE(CONTENT "%s" AS CLOB) AS "%s"`, columnName, columnName))
		// 二进制
		case "BLOB", "LONG RAW", "RAW":
			columnNames = append(columnNames, transfercommon.StringsBuilder(`"`, columnName, `"`))
		// 时间
		case "DATE":
			columnNames = append(columnNames, transfercommon.StringsBuilder(`TO_CHAR("`, columnName, `",'yyyy-MM-dd HH24:mi:ss') AS "`, columnName, `"`))
		// 默认其他类型
		default:
			if strings.Contains(rowCol["DATA_TYPE"], "INTERVAL") {
				columnNames = append(columnNames, transfercommon.StringsBuilder(`TO_CHAR("`, columnName, `") AS "`, columnName, `"`))
			} else if strings.Contains(rowCol["DATA_TYPE"], "TIMESTAMP") {
				dataScale, err := strconv.Atoi(rowCol["DATA_SCALE"])
				if err != nil {
					return nil, fmt.Errorf("aujust oracle timestamp datatype scale [%s] strconv.Atoi failed: %v", rowCol["DATA_SCALE"], err)
				}
				if dataScale == 0 {
					columnNames = append(columnNames, transfercommon.StringsBuilder(`TO_CHAR("`, columnName, `",'yyyy-mm-dd hh24:mi:ss') AS "`, columnName, `"`))
				} else if dataScale < 0 && dataScale <= 6 {
					columnNames = append(columnNames, transfercommon.StringsBuilder(`TO_CHAR("`, columnName,
						`",'yyyy-mm-dd hh24:mi:ss.ff`, rowCol["DATA_SCALE"], `') AS "`, columnName, `"`))
				} else {
					columnNames = append(columnNames, transfercommon.StringsBuilder(`TO_CHAR("`, columnName, `",'yyyy-mm-dd hh24:mi:ss.ff6') AS "`, columnName, `"`))
				}

			} else {
				columnNames = append(columnNames, transfercommon.StringsBuilder(`"`, columnName, `"`))
			}
		}

	}

	return columnNames, nil
}
