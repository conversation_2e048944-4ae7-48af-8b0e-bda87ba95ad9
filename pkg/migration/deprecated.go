// Deprecated: This file and all its contents are deprecated. Use single mode (csv/sql migration) instead.
package migration

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	fileutil "gitee.com/pingcap_enterprise/tms/util/file"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"golang.org/x/sync/errgroup"
)

func StartCSVDataTaskByWorker(ctx context.Context, channelId, taskId int) error {
	log.Infof("prepare to start csv task, loading csv stage")
	csvStage, getErr := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskId(ctx, taskId)
	if getErr != nil {
		log.Errorf("get csv stage by taskId failed, err:%v", getErr)
		return getErr
	}

	var taskInfo *task.Task
	var getTaskErr error
	_ = models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskInfo, getTaskErr = models.GetTaskReaderWriter().GetTask(transactionCtx, taskId)
		if getTaskErr != nil {
			log.Errorf("csv task start, get task info failed, err:%v", getTaskErr)
			return getTaskErr
		}
		taskInfo.TaskStatus = constants.TASK_STATUS_RUNNING
		taskInfo.StartTime = time.Now()
		taskInfo.EndTime = timeutil.GetTMSNullTime()
		_, updateErr := models.GetTaskReaderWriter().UpdateTask(transactionCtx, taskInfo)
		if updateErr != nil {
			log.Errorf("update task status failed, err:%v", updateErr)
			return updateErr
		}
		return nil
	})

	log.Infof("prepare to start csv task, csvStage, taskId:%d, ExportToCSV:%v, ImportStatus:%v", taskId, csvStage.ExportToCSV, csvStage.ImportToDB)
	if !csvStage.ExportToCSV {
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, "prepare to start csv export task"),
			LogLevel:  log.LogInfo,
		})

		exportErr := StartCSVDataMigrateByWorker(ctx, channelId, taskId)
		if exportErr != nil {
			log.Errorf("start csv data migrate task failed, err:%v", exportErr)
			return exportErr
		}

		updateErr := models.GetFullDataMigrationReaderWriter().UpdateCSVStage(ctx, channelId, taskId, map[string]interface{}{
			"export_to_csv": true,
			"error_message": "",
		})
		if updateErr != nil {
			log.Errorf("StartCSVDataMigrateByWorker success, but update ExportToCSV to true failed, err:%v", updateErr)
			return updateErr
		}
	} else {
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableSkip, "csv export task has been completed, skip execution."),
			LogLevel:  log.LogWarn,
		})
	}

	if !csvStage.ImportToDB {
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportBegin, "prepare to start csv lightning import task"),
			LogLevel:  log.LogInfo,
		})
		importErr := StartCSVDataImportByWorker(ctx, channelId, taskId)
		if importErr != nil {
			log.Errorf("start csv data import task failed, err:%v", importErr)
			return importErr
		}

		updateErr := models.GetFullDataMigrationReaderWriter().UpdateCSVStage(ctx, channelId, taskId, map[string]interface{}{
			"import_to_db":  true,
			"error_message": "",
		})
		if updateErr != nil {
			log.Errorf("StartCSVDataMigrateByWorker success, but update ImportStatus to true failed, err:%v", updateErr)
			return updateErr
		}

		csvParam, buildErr := BuildCSVMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
		if buildErr != nil {
			log.Errorf("build csv migration param failed, taskId:%d, err:%v", taskInfo.TaskID, buildErr)
			return buildErr
		}
		if csvParam.ShouldRemoveCSV("oracle") {
			log.Infof("StartCSVDataTaskByWorker -> RemoveTaskTableCSVFile, task_id:%d", taskId)
			removeErr := RemoveTaskTableCSVFile(ctx, taskInfo, csvParam)
			if removeErr != nil {
				log.Errorf("remove csv file failed, taskId:%d, err:%v", taskInfo.TaskID, removeErr)
			}
		}
	} else {
		_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
			Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportSkip, "csv lightning import task has been completed, skip execution."),
			LogLevel:  log.LogWarn,
		})
	}

	_ = models.Transaction(ctx, func(transactionCtx context.Context) error {
		taskInfo, getErr := models.GetTaskReaderWriter().GetTask(transactionCtx, taskId)
		if getErr != nil {
			log.Errorf("csv task finish, get task info failed, err:%v", getErr)
			return getErr
		}
		taskInfo.TaskStatus = constants.TASK_STATUS_FINISH
		taskInfo.EndTime = time.Now()
		_, updateErr := models.GetTaskReaderWriter().UpdateTask(transactionCtx, taskInfo)
		if updateErr != nil {
			log.Errorf("update task status failed, err:%v", updateErr)
			return updateErr
		}
		return nil
	})

	return nil
}

// StartCSVDataMigrateByWorker invoke by worker, taskId means sub taskId, not parent taskId !!!
func StartCSVDataMigrateByWorker(ctx context.Context, channelId, taskId int) error {
	startTime := time.Now()
	taskInfo, getErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getErr != nil {
		log.Errorf("get task info error, taskId :%d, err:%v", taskId, getErr)
		return getErr
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		progressLogs, batchGetErr := models.GetProgressLogReaderWriter().BatchGetProgressLogDetail(ctx, &common.ProgressLogDetail{
			ChannelID: channelId,
			TaskID:    taskId,
		})
		if batchGetErr != nil {
			log.Errorf("batch get progress log failed: %v", batchGetErr)
			return batchGetErr
		}
		nowTime := time.Now()
		for idx := range progressLogs {
			progressLogs[idx].CreatedAt = nowTime
		}
		if len(progressLogs) != 0 {
			if _, updateErr := models.GetProgressLogReaderWriter().SaveProgressLogDetails(transactionCtx, progressLogs); updateErr != nil {
				log.Errorf("task exec failed, update progress logs failed: %v", updateErr)
				return updateErr
			}
		}

		if _, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(transactionCtx, taskId, task.Task{
			StartTime:  time.Now(),
			TaskStatus: constants.TASK_STATUS_RUNNING,
		}); updateErr != nil {
			log.Errorf("task exec failed, update taskId [%v] to status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, updateErr)
			return updateErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("task exec failed, update taskId [%v] to status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, trxErr)
		return trxErr
	}

	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeCSV, channelId, taskId)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)

	var execErr error

	// run param 不为空，走retry逻辑
	if taskInfo.RunParams != "" {
		log.Infof("start retry csv data migration task, taskId:%d, runParam:%s", taskId, taskInfo.RunParams)

		// 如果runParam不是以summary或者detail开头的，报错并设置任务状态为Failed
		if validateErr := validateMigrationTaskRunParam(taskInfo.RunParams); validateErr != nil {
			log.Errorf("get task info success, but runParam is invalid, err:%v", validateErr)
			if _, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
				StartTime:  time.Now(),
				TaskStatus: constants.TASK_STATUS_RUNNING,
			}); updateErr != nil {
				log.Errorf("task exec failed, update taskId [%v] to status [%d] failed: %v", taskId, constants.TASK_STATUS_RUNNING, updateErr)
				return updateErr
			}
			return validateErr
		}

		if strings.HasPrefix(taskInfo.RunParams, "summary:") {
			execErr = retryCSVDataMigrationBySummaries(ctx, taskInfo, taskId)
		}

		if strings.HasPrefix(taskInfo.RunParams, "detail:") {
			execErr = retryCSVDataMigrationByDetails(ctx, taskInfo)
		}
	} else {
		execErr = startCSVDataMigrationByWorker(ctx, channelId, taskId)
	}

	hasFailedChunks, determinateErr := UpdateTaskToFailedIfContainsFailedDetailsOrSummaries(ctx, taskId)
	if determinateErr != nil {
		// 仅做记录
		log.Errorf("task exec finish, set final status failed, err:%v", determinateErr)
	}

	if execErr != nil {
		log.Errorf("start data migration task failed, taskId:%d, runParam:%s, err:%v", taskId, taskInfo.RunParams, execErr)
		return execErr
	}

	if hasFailedChunks {
		return fmt.Errorf("task exec finish, but has failed chunks, taskId:%d, runParam:%s", taskId, taskInfo.RunParams)
	}

	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID: channelId,
		TaskID:    taskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeFullData, constants.MigrationStepMigrateTableFinish, "export finished, time consuming: "+time.Since(startTime).String()),
		LogLevel:  log.LogInfo,
	})
	return nil
}

func retryCSVDataMigrationBySummaries(ctx context.Context, taskInfo *task.Task, taskId int) error {
	summaries, parseErr := parseTaskRunParamAndGetSummaries(ctx, taskInfo)
	if parseErr != nil {
		log.Errorf("parse task runParam and get summaries failed, taskId:%d, runParam:%s, err:%v", taskId, taskInfo.RunParams, parseErr)
		return parseErr
	}
	retryErr := batchRetryCSVMigrationByTableFailed(ctx, taskInfo, summaries)
	if retryErr != nil {
		log.Errorf("retry csv data migration failed chunks by summaries failed, taskId:%d, runParam:%s, err:%v", taskId, taskInfo.RunParams, retryErr)
		return retryErr
	}
	return nil
}

func batchRetryCSVMigrationByTableFailed(ctx context.Context, taskInfo *task.Task, summaries []*migration.TableMigrationSummary) error {

	taskId := taskInfo.TaskID
	channelId := taskInfo.ChannelId

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeCSV, channelId, taskId)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, taskId:%d, err:%v", taskId, getChannelErr)
		return fmt.Errorf("get channel info failed: %v", getChannelErr)
	}

	csvParam, buildParamErr := BuildCSVMigrationParam(ctx, taskInfo, taskInfo.TaskParamTmplateId)
	if buildParamErr != nil {
		log.Errorf("build csv migration param failed, taskId:%d, err:%v", taskId, buildParamErr)
		return buildParamErr
	}

	sourceDBConns, setUpErr := SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("set up oracle database conns failed, taskId:%d, err:%v", taskId)
		return setUpErr
	}

	migrationDBConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
		return setUpErr
	}

	outputDataDir := csvParam.GetOutputDataDir(channelId, taskId)
	mkdirErr := fileutil.CreateIfNotExist(outputDataDir)
	if mkdirErr != nil {
		log.Errorf("create output data dir failed, taskId:%d, outputDataDir:%s, err:%v", taskId, outputDataDir, mkdirErr)
		return fmt.Errorf("create output data dir failed, outputDataDir:%s, err:%v", outputDataDir, mkdirErr)
	}

	// 重运行失败表 chunk 的任务 failed
	gS := &errgroup.Group{}
	gS.SetLimit(csvParam.GetTableThreads())

	for _, summary := range summaries {
		d := summary

		summaryId := d.ID
		schemaName := d.SchemaNameS
		tableName := d.TableNameS

		gS.Go(func() error {
			startExecTime := time.Now()

			waitingAndFailedDetails, err := getWaitingAndFailedMigrationDetails(ctx, summary)
			if err != nil {
				log.Errorf("get waiting and failed migration details failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summaryId, schemaName, tableName, err)
				return err
			}

			progressHelper := buildProgressHelper(taskInfo.ChannelId, taskInfo.TaskID, d.SchemaNameS, d.TableNameS, constants.MigrationLogTypeCSV)
			progressHelper.SetTotal(len(waitingAndFailedDetails))
			progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
			defer progressHelper.Close()
			defer progressHelper.DisplayAndSaveProgress(ctx)

			err = models.Transaction(ctx, func(transactionCtx context.Context) error {
				if !strings.EqualFold(d.TaskStatus, transfercommon.TaskStatusRunning) {
					err = models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
						TaskID:      d.TaskID,
						SchemaNameS: d.SchemaNameS,
						TableNameS:  d.TableNameS,
					}, map[string]interface{}{
						"Task_Status": transfercommon.TaskStatusRunning,
					})
					if err != nil {
						return err
					}
				}

				_, err = models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
					ChannelID:   taskInfo.ChannelId,
					TaskID:      d.TaskID,
					SchemaNameS: d.SchemaNameS,
					TableNameS:  d.TableNameS,
					Detail:      constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, migration retry initiated", d.SchemaNameS, d.TableNameS)),
					LogLevel:    log.LogInfo,
				})
				if err != nil {
					return fmt.Errorf("create progress log detail record failed: %v", err)
				}
				return nil
			})
			if err != nil {
				log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
				return err
			}

			if len(waitingAndFailedDetails) == 0 {
				log.Warnf("table [%s.%s], task:%d, all failed chunks retry success", d.SchemaNameS, d.TableNameS, taskInfo.TaskID)
				return nil
			}
			g := &errgroup.Group{}
			g.SetLimit(csvParam.GetSqlThreads())

			for idx, detail := range waitingAndFailedDetails {
				migrationDetail := &(waitingAndFailedDetails[idx])

				chunkId := migrationDetail.ID
				chunk := migrationDetail.ChunkDetailS

				g.Go(func() error {
					chunkTime := time.Now()

					errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetailById(ctx, &migration.TableMigrationDetail{
						ID: detail.ID,
					}, map[string]interface{}{
						"Task_Status": transfercommon.TaskStatusRunning,
						"Duration":    time.Now().Sub(chunkTime).Seconds(),
					})
					if errU != nil {
						return fmt.Errorf("UpdateTableMigrationDetailById [%v] chunk status failed: %v", detail.String(), errU)
					}

					getColumnsSQL := BuildGetColumnSQL(migrationDetail)
					columnNameS, getColumnErr := sourceDBConns.GetSourceDB().GetOracleTableRowsColumnCSV(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[sourceDBConns.GetSourceDS().Charset],
						transfercommon.StringUPPER(csvParam.GetCharset()))
					if getColumnErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)
						log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						return nil
					}

					if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
						SchemaName: schemaName,
						TableName:  tableName,
					}]; ok {
						if taskTableConfig.OperatorTag == "Y" {
							oracleCollation := migrationDBConns.GetOracleCollation()
							oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
							if err != nil {
								log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
								return err
							}
							migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
						}
						if taskTableConfig.TablePartition != "" {
							migrationDetail.TablePartition = taskTableConfig.TablePartition
						}
					}

					rows, buildErr := buildCSVDataMigrationReq(ctx, taskInfo, migrationDetail, sourceDBConns, csvParam, columnNameS)
					if buildErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)

						log.Errorf("buildFullDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						return nil
					}

					migrateErr := doDataMigration(ctx, transferDBMigrateCSVFunc, taskInfo.ChannelId, migrationDetail, rows, progressHelper, constants.MigrationLogTypeCSV)
					if migrateErr != nil {
						log.Errorf("migration retry failed, taskId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err: %v",
							taskId, chunkId, chunk, schemaName, tableName, migrateErr)
						progressHelper.IncreaseFailedNum()
						log.Errorf("get oracle table rows column failed, summaryId:%d, taskId:%d, schemaName:%s, tableName:%s, err:%v", d.ID, d.TaskID, d.SchemaNameS, d.TableNameS, err)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, migrateErr.Error(), constants.MigrationLogTypeCSV)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						return nil
					}
					return nil
				})
			}
			if waitErr := g.Wait(); waitErr != nil {
				log.Errorf("BatchRetryCSVMigrationTaskTable failed, taskId:%d, err:%v", taskInfo.TaskID, waitErr)
				return waitErr
			}
			// 更新详情以及日志
			_, _, tryErr := trySetCSVMigrationSummaryFailed(ctx, d, taskInfo, int(d.ChunkTotalNums), startExecTime)
			if tryErr != nil {
				log.Errorf("trySetCSVMigrationSummaryFailed failed, taskId:%d, err:%v", taskInfo.TaskID, tryErr)
				return tryErr
			}
			return nil
		})
	}

	if waitErr := gS.Wait(); waitErr != nil {
		log.Errorf("BatchRetryCSVMigrationTaskTable failed, taskId:%d, err:%v", taskInfo.TaskID, waitErr)
		return waitErr
	}
	return nil
}

func retryCSVDataMigrationByDetails(ctx context.Context, taskInfo *task.Task) error {
	details, parseErr := parseTaskRunParamAndGetDetails(ctx, taskInfo)
	if parseErr != nil {
		return parseErr
	}

	//runParam下的schema+table应该是一致的，这里防止可能出现的问题
	schemaTableChunksMapping := make(map[structs.SchemaTablePair][]migration.TableMigrationDetail)
	for _, detail := range details {
		schemaTablePair := structs.SchemaTablePair{
			SchemaName: detail.SchemaNameS,
			TableName:  detail.TableNameS,
		}
		schemaTableChunksMapping[schemaTablePair] = append(schemaTableChunksMapping[schemaTablePair], detail)
	}

	for schemaTablePair, details := range schemaTableChunksMapping {
		log.Infof("start retry csv data migration failed chunks, taskId:%d, runParam:%s, schema:%s, table:%s", taskInfo.TaskID, taskInfo.RunParams, schemaTablePair.SchemaName, schemaTablePair.TableName)
		req := FailedChunkBatchRetryReq{
			TaskID:      taskInfo.TaskID,
			SchemaNameS: schemaTablePair.SchemaName,
			TableNameS:  schemaTablePair.TableName,
		}
		retryErr := RetryCSVMigrationFailedChunks(ctx, req, taskInfo, details)
		if retryErr != nil {
			log.Errorf("retry csv data migration failed chunks failed, taskId:%d, runParam:%s, schema:%s, table:%s, err:%v", taskInfo.TaskID, taskInfo.RunParams, schemaTablePair.SchemaName, schemaTablePair.TableName, retryErr)
			return retryErr
		}
	}
	return nil
}

// 由于是worker执行，所以这时的taskId是子任务的ID
func startCSVDataMigrationByWorker(ctx context.Context, channelId int, taskId int) error {
	return CreateCSVMigrationTaskInClusterMode(ctx, channelId, taskId, structs.ClusterModeMigrationDoMigrate)
}

func SplitCSVChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	splitErr := splitCSVChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeS)
	if splitErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], split csv data chunks in cluster mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeSplit, taskTableConfigMap, channelSchemaTable, dbConns, taskInfo, splitErr, tableTypeS)
	return splitErr
}

func splitCSVChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	sourceDB := dbConns.GetSourceDB()
	sourceDS := dbConns.GetSourceDS()
	oracleCollation := dbConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail 以及下游真实表记录，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInClusterMode(ctx, taskInfo, channelSchemaTable, dbConns.GetTargetDB()); clearErr != nil {
			return clearErr
		}
	}

	tableDetailCount, tableSummaryCount, countErr := models.GetFullDataMigrationReaderWriter().CountSubTaskMigrationSummaryAndDetailByTaskID(ctx, taskInfo.TaskID, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if countErr != nil {
		log.Errorf("table [%s.%s], meta database data init, count table_migration_summaries and table_migration_details failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, countErr)
		return countErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount, taskInfo.TaskID)
	if tableSummaryCount != 0 || tableDetailCount != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v, taskId:%d",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount, taskInfo.TaskID)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, migrationParam.GetChunkSize(), sourceDS)

	im := buildO2TCSVConfig(ctx, channelSchemaTable, sourceDS, migrationParam, sourceDB)

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
		return adjustErr
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		return getStatisticsErr
	}
	//退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleCSVMigrationDetailAndSummary(ctx, channelSchemaTable, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 || !customConf.GetEnableSplit() {
		createErr := createSingleCSVMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		log.Errorf("table [%s.%s], create single csv migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		if createErr != nil {
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		}
		return createErr
	}

	log.Infof("table [%s.%s], split table, chunk size [%v]", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, customConf.GetChunkSize())
	chunkRes, chunkNum, splitErr := createSplitTaskAndGetChunkResults(sourceDB, channelSchemaTable, customConf.GetChunkSize(), migrationParam.GetCallTimeout(), migrationParam.GetRemoveSplitTask())
	if splitErr != nil {
		log.Errorf("table [%s.%s], split task and get chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleCSVMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		return createErr
	}

	migrationDetails := make([]*migration.TableMigrationDetail, 0, chunkNum)

	chunkNumDigits := parse.CountDigits(chunkNum)

	for idx, res := range chunkRes {
		_, combineWhereRange := buildChunkWhereCondition(res, customConf.GetWherePrefix())

		paddedIndex := parse.ZeroPad(idx, chunkNumDigits)
		detail := buildCSVMigrationDetail(paddedIndex, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), combineWhereRange)

		migrationDetails = append(migrationDetails, detail)
	}

	createErr := createSubTasksAndForkMetadata(ctx, channelSchemaTable, taskInfo, chunkNum, migrationParam, migrationDetails, sourceDS, channelSchemaObject, customConf.GetChunkSize())
	if createErr != nil {
		log.Errorf("table [%s.%s], create sub tasks and fork metadata failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		return createErr
	}
	return nil
}

func RetryCSVMigrationFailedChunks(ctx context.Context, msg FailedChunkBatchRetryReq, taskInfo *task.Task, failedDetails []migration.TableMigrationDetail) error {
	taskId := taskInfo.TaskID
	channelId := taskInfo.ChannelId

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	// init transferdb logger
	logFileName := config.BuildMigrationLogFileName(constants.MigrationTypeCSV, channelId, taskId)
	additionalLoggerHelper.InitAdditionalLogger(logFileName)

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, channelId:%d, taskId:%d, err:%v", channelId, taskId, getChannelErr)
		return fmt.Errorf("get channel info failed, channelId:%d, taskId:%d, err:%v", channelId, taskId, getChannelErr)
	}
	taskParamTempConfig, getTemplateErr := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if getTemplateErr != nil {
		log.Errorf("get task template info failed, channelId:%d, taskId:%d, err:%v", channelId, taskId, getTemplateErr)
		return fmt.Errorf("get task template info failed: %v", getTemplateErr)
	}

	csvParam, buildPramErr := BuildCSVMigrationParam(ctx, taskInfo, taskParamTempConfig.TaskparamTemplateID)
	if buildPramErr != nil {
		log.Errorf("build csv migration param failed, taskId:%d, err:%v", taskId, buildPramErr)
		return buildPramErr
	}

	sourceDBConns, setUpErr := SetUpOracleDatabaseConns(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("set up oracle database conns failed, taskId:%d, err:%v", taskId, setUpErr)
		return setUpErr
	}

	migrationDBConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
		return setUpErr
	}

	outputDataDir := csvParam.GetOutputDataDir(channelId, taskId)
	mkdirErr := fileutil.CreateIfNotExist(outputDataDir)
	if mkdirErr != nil {
		log.Errorf("create output data dir failed, outputDataDir:%s, err:%v", outputDataDir, mkdirErr)
		return fmt.Errorf("create output data dir failed, outputDataDir:%s, err:%v", outputDataDir, mkdirErr)
	}

	// 获取失败 chunk
	startExecTime := time.Now()
	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      msg.TaskID,
		SchemaNameS: msg.SchemaNameS,
		TableNameS:  msg.TableNameS,
	})
	if getSummaryErr != nil {
		log.Errorf("get table migration summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v", taskId, msg.SchemaNameS, msg.TableNameS, getSummaryErr)
		return getSummaryErr
	}
	d := tableSummaries[0]

	summaryId := d.ID
	schemaName := d.SchemaNameS
	tableName := d.TableNameS

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		if !strings.EqualFold(d.TaskStatus, transfercommon.TaskStatusRunning) {
			updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(transactionCtx, &migration.TableMigrationSummary{
				TaskID:      msg.TaskID,
				SchemaNameS: msg.SchemaNameS,
				TableNameS:  msg.TableNameS,
			}, map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusRunning,
			})
			if updateErr != nil {
				return updateErr
			}
		}

		_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
			ChannelID:   taskInfo.ChannelId,
			TaskID:      msg.TaskID,
			SchemaNameS: msg.SchemaNameS,
			TableNameS:  msg.TableNameS,
			Detail:      constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, migration retry initiated", msg.SchemaNameS, msg.TableNameS)),
			LogLevel:    log.LogInfo,
		})
		if createErr != nil {
			return fmt.Errorf("create progress log detail record failed: %v", createErr)
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("update migration summary status and create logs in transaction failed, taskId:%d, err:%v", taskInfo.TaskID, trxErr)
		return trxErr
	}

	g := &errgroup.Group{}
	g.SetLimit(csvParam.GetSqlThreads())

	progressHelper := buildProgressHelper(taskInfo.ChannelId, taskInfo.TaskID, d.SchemaNameS, d.TableNameS, constants.MigrationLogTypeCSV)
	progressHelper.SetTotal(len(failedDetails))
	progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
	defer progressHelper.Close()
	defer progressHelper.DisplayAndSaveProgress(ctx)

	for idx := range failedDetails {
		migrationDetail := &(failedDetails[idx])

		chunkId := migrationDetail.ID
		chunk := migrationDetail.ChunkDetailS

		g.Go(func() error {
			getColumnsSQL := BuildGetColumnSQL(migrationDetail)
			columnNameS, getColumnErr := sourceDBConns.GetSourceDB().GetOracleTableRowsColumnCSV(getColumnsSQL, transfercommon.MigrateOracleCharsetStringConvertMapping[sourceDBConns.GetSourceDS().Charset],
				transfercommon.StringUPPER(csvParam.GetCharset()))
			if getColumnErr != nil {
				progressHelper.IncreaseFailedNum()
				errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)

				log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
					taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
				errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
				if errC != nil {
					log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
					return errC
				} else {
					log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
				if errS != nil {
					log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
					return errS
				} else {
					log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				return nil
			}

			if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
				SchemaName: schemaName,
				TableName:  tableName,
			}]; ok {
				if taskTableConfig.OperatorTag == "Y" {
					oracleCollation := migrationDBConns.GetOracleCollation()
					oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, migrationDBConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
					if err != nil {
						log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
						return err
					}
					migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
				}
				if taskTableConfig.TablePartition != "" {
					migrationDetail.TablePartition = taskTableConfig.TablePartition
				}
			}

			rows, buildErr := buildCSVDataMigrationReq(ctx, taskInfo, migrationDetail, sourceDBConns, csvParam, columnNameS)
			if buildErr != nil {
				progressHelper.IncreaseFailedNum()
				errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)
				log.Errorf("buildCSVDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
					taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
				errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
				if errC != nil {
					log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
					return errC
				} else {
					log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
				if errS != nil {
					log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
					return errS
				} else {
					log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				return nil
			}

			migrateErr := doDataMigration(ctx, transferDBMigrateCSVFunc, taskInfo.ChannelId, migrationDetail, rows, progressHelper, constants.MigrationLogTypeCSV)
			if migrateErr != nil {
				log.Errorf("migration retry failed, taskId:%d, chunkId:%d, chunk:%s, schemaName:%d, tableName:%s, err:%v",
					taskId, chunkId, chunk, schemaName, tableName, migrateErr)
				progressHelper.IncreaseFailedNum()
				errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, migrateErr.Error(), constants.MigrationLogTypeCSV)
				if errC != nil {
					log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
					return errC
				} else {
					log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				_, errS := SetMigrationSummaryFinishOrFailed(ctx, d, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
				if errS != nil {
					log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
					return errS
				} else {
					log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
						taskId, summaryId, chunkId, chunk, schemaName, tableName)
				}
				return nil
			}
			return nil
		})
	}
	if waitErr := g.Wait(); waitErr != nil {
		log.Errorf("RetryFullDataMigrationFailedChunks failed, taskId:%d, err:%v", taskInfo.TaskID, waitErr)
		return waitErr
	}

	_, _, tryErr := trySetCSVMigrationSummaryFailed(ctx, d, taskInfo, int(d.ChunkTotalNums), startExecTime)
	if tryErr != nil {
		log.Errorf("trySetCSVMigrationSummaryFailed failed, taskId:%d, err:%v", taskInfo.TaskID, tryErr)
		return tryErr
	}
	return nil
}

func FetchCSVChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	fetchErr := fetchCSVChunksInClusterMode(ctx, clearData, channelSchemaTable, channelSchemaObject, taskTableConfigMap, taskInfo, dbConns, migrationParam, tableTypeS)
	if fetchErr == nil {
		return nil
	}
	log.Errorf("table [%s.%s], fetch csv data chunks in cluster mode failed, create INVALID summary and detail records, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, fetchErr)
	createChunkGenerateErrorDetailAndSummary(ctx, GenerateChunkTypeFetch, taskTableConfigMap, channelSchemaTable, dbConns, taskInfo, fetchErr, tableTypeS)
	return fetchErr
}

func fetchCSVChunksInClusterMode(ctx context.Context, clearData bool, channelSchemaTable *channel.ChannelSchemaTable, channelSchemaObject *channel.ChannelSchemaObject, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, taskInfo *task.Task, dbConns *MigrationDBConns, migrationParam *structs.CSVMigrationConfigParam, tableTypeS string) error {
	sourceDB := dbConns.GetSourceDB()
	sourceDS := dbConns.GetSourceDS()
	oracleCollation := dbConns.GetOracleCollation()

	// 无论任务是否运行成功或者部分成功，删除 summary、detail 以及下游真实表记录，任务全部重新运行
	if clearData {
		if clearErr := clearMigrationOldDataAndTruncateTableInClusterMode(ctx, taskInfo, channelSchemaTable, dbConns.GetTargetDB()); clearErr != nil {
			return clearErr
		}
	}

	tableDetailCount, tableSummaryCount, countErr := models.GetFullDataMigrationReaderWriter().CountSubTaskMigrationSummaryAndDetailByTaskID(ctx, taskInfo.TaskID, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if countErr != nil {
		log.Errorf("table [%s.%s], meta database data init, count table_migration_summaries and table_migration_details failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, countErr)
		return countErr
	}
	log.Infof("table [%s.%s], meta database data init, table_migration_summaries records counts [%v], table_migration_details records counts [%v], taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount, taskInfo.TaskID)
	if tableSummaryCount != 0 || tableDetailCount != 0 {
		log.Warnf("table [%s.%s], migration stop! tableSummaries len: %v, tableDetails len: %v",
			channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableSummaryCount, tableDetailCount)
		return nil
	}

	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, ADG_DEFAULT_CHUNK_SIZE, sourceDS)

	im := buildO2TCSVConfig(ctx, channelSchemaTable, sourceDS, migrationParam, sourceDB)

	sourceColumnInfo, adjustErr := im.AdjustTableSelectColumn(channelSchemaTable.TableNameS, oracleCollation)
	if adjustErr != nil {
		log.Errorf("table [%s.%s], adjust table select column failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, adjustErr)
		return adjustErr
	}

	tableRowsByStatistics, getStatisticsErr := sourceDB.GetOracleTableRowsByStatistics(channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	if getStatisticsErr != nil {
		log.Errorf("table [%s.%s], get table rows by statistics failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, getStatisticsErr)
		return getStatisticsErr
	}

	// 退出条件0：IOT表不划分chunk
	if tableTypeS == "IOT" {
		createErr := createSingleCSVMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		if createErr != nil {
			log.Errorf("table [%s.%s], create single full migration detail and summary failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
			createCSVErrorProgressLogDetail(ctx, taskInfo, channelSchemaTable, createErr)
		} else {
			log.Infof("table [%s.%s], create single full migration detail and summary success, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
		}
		return createErr
	}

	// 退出条件一：表数据量小于等于 0，或者不分片
	log.Infof("table [%s.%s], table rows by statistics [%v], enable split [%v], taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, tableRowsByStatistics, customConf.GetEnableSplit(), taskInfo.TaskID)
	if tableRowsByStatistics == 0 {
		createErr := createSingleCSVMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		return createErr
	}

	log.Infof("table [%s.%s], fetch table chunks results, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID)
	chunkRes, chunkNum, splitErr := fetchTableChunkResults(sourceDB, channelSchemaTable)
	if splitErr != nil {
		log.Errorf("table [%s.%s], fetch table chunk results failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, splitErr)
		return splitErr
	}

	// 退出条件二：分片数据量小于等于 0
	log.Infof("table [%s.%s], table chunk rows by chunkResLen:%d, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, chunkNum, taskInfo.TaskID)
	if len(chunkRes) == 0 {
		createErr := createSingleCSVMigrationDetailAndSummaryInClusterMode(ctx, channelSchemaTable, channelSchemaObject, sourceDS, sourceColumnInfo, taskInfo, customConf, tableTypeS)
		return createErr
	}

	migrationDetails := make([]*migration.TableMigrationDetail, 0, chunkNum)

	chunkNumDigits := parse.CountDigits(chunkNum)

	for idx, res := range chunkRes {
		_, combineWhereRange := buildChunkWhereCondition(res, customConf.GetWherePrefix())
		paddedIndex := parse.ZeroPad(idx, chunkNumDigits)
		detail := buildCSVMigrationDetail(paddedIndex, taskInfo, channelSchemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), combineWhereRange)

		migrationDetails = append(migrationDetails, detail)
	}

	createErr := createSubTasksAndForkMetadata(ctx, channelSchemaTable, taskInfo, chunkNum, migrationParam, migrationDetails, sourceDS, channelSchemaObject, ADG_DEFAULT_CHUNK_SIZE)
	if createErr != nil {
		log.Errorf("table [%s.%s], create sub tasks and fork metadata failed, taskId:%d, err: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, createErr)
		return createErr
	}
	return nil
}

// CreateCSVMigrationTaskInClusterMode 集群版 ｜ CSV数据迁移任务的核心逻辑，1. 构造参数 2. 切片 3. 导出
// 由上层函数进行Lightning导入
//
// 对于cluster模式执行parentTaskId+[1,2]或者subTaskId+[1,3]
func CreateCSVMigrationTaskInClusterMode(ctx context.Context, channelId, taskId int, invokeOption structs.InvokeMigrationOption) error {

	log.Infof("start create csv data migration task, channelId: %d, taskId: %d, invokeOption: %v", channelId, taskId, invokeOption.String())
	taskInfo, channelInfo, taskParamTempConfig, channelSchemaObject, getErr := GetMigrationMetadata(ctx, channelId, taskId)
	if getErr != nil {
		log.Errorf("get migration metadata failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, getErr)
		return getErr
	}

	// init csv migration param
	migrationParam, buildErr := BuildCSVMigrationParam(ctx, taskInfo, taskParamTempConfig.TaskparamTemplateID)
	if buildErr != nil {
		log.Errorf("build migration config params failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, buildErr)
		return buildErr
	}

	// init datasource and conns
	dbConns, setUpErr := SetUpDatabaseConnsAndCollation(ctx, channelInfo, taskId)
	if setUpErr != nil {
		log.Errorf("setup database conns failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, setUpErr)
		return setUpErr
	}

	// split csv into chunks, and save migration details / summaries
	if invokeOption.DoSplit {
		if taskInfo.ParentTaskID != 0 {
			return fmt.Errorf("task [%d] parent taskId [%d] is not 0, means a subtask which can't execute split, please check", taskInfo.TaskID, taskInfo.ParentTaskID)
		}

		clearData, checkErr := validateChannelAndGetClearFlag(channelSchemaObject)
		if checkErr != nil {
			return checkErr
		}
		if clearData {
			_ = models.GetFullDataMigrationReaderWriter().BatchDeleteCSVStageByTaskId(ctx, channelId, taskId)
			_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetailByTaskIds(ctx, []int{taskId})
			_ = models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummaryByTaskIds(ctx, []int{taskId})
		}

		var splitErr error
		makeChunksParam := ChunkMigrateParam{
			ChannelInfo:         channelInfo,
			TaskInfo:            taskInfo,
			MigrationParam:      migrationParam,
			DBConns:             dbConns,
			ChannelSchemaObject: channelSchemaObject,
			ClearData:           clearData,
		}
		if dbConns.GetSourceDS().DbType == constants.DB_TYPE_ORACLE {
			log.Infof("split task table into chunks, channelId: %d, taskId: %d", channelId, taskId)
			splitErr = splitTaskTablesIntoCSVChunks(ctx, makeChunksParam)
		} else if dbConns.GetSourceDS().DbType == constants.DB_TYPE_ORACLE_ADG {
			log.Infof("fetch table chunks from oracle-adg, channelId: %d, taskId: %d", channelId, taskId)
			splitErr = fetchTableChunksIntoCSVChunks(ctx, makeChunksParam)
		} else {
			return fmt.Errorf("not support db type: %s for csv migration, taskId:%d", dbConns.GetSourceDS().DbType, taskId)
		}
		if splitErr != nil {
			log.Errorf("split or fetch task table into chunks failed, channelId: %d, taskId: %d, err: %v", channelId, taskId, splitErr)
			return splitErr
		}
	}

	// migrate csv data in chunks
	if invokeOption.DoMigrate {
		log.Infof("migrate table csv data by chunks, channelId: %d, taskId: %d", channelId, taskId)
		migrateErr := migrateCSVDataByChunks(ctx, channelInfo, taskInfo, migrationParam, dbConns)
		if migrateErr != nil {
			return migrateErr
		}
	}
	return nil
}

// createSingleCSVMigrationDetailAndSummaryInClusterMode create single csv migration detail and update summary
func createSingleCSVMigrationDetailAndSummaryInClusterMode(ctx context.Context, schemaTable *channel.ChannelSchemaTable, channelSchema *channel.ChannelSchemaObject, sourceDS *datasource.Datasource, sourceColumnInfo string, taskInfo *task.Task, customConf TableMigrationCustomConf, tableTypeS string) error {
	whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())
	log.Infof("table [%s.%s], disable split or statistics is empty or chunk size is zero, stop splitting, save record and return, taskId:%d, enableSplit:%v, wherePrefix:%s, whereRange:%s", schemaTable.SchemaNameS, schemaTable.TableNameS, taskInfo.TaskID, customConf.GetEnableSplit(), customConf.GetWherePrefix(), whereRange)

	trxErr := models.Transaction(ctx, func(txnCtx context.Context) error {

		// 子任务的
		subTask := buildSubTasks(taskInfo, schemaTable)
		if _, err := models.GetTaskReaderWriter().CreateTask(txnCtx, subTask); err != nil {
			return err
		}

		subTaskChannelSchemaTable := buildSubTaskChannelSchemaTable(subTask, schemaTable)
		subTaskChannelSchemaObject := buildSubTaskChannelSchemaObject(subTask, channelSchema)
		summary := buildMigrationSummary(subTask, sourceDS, schemaTable, customConf.GetChunkSize(), tableTypeS)

		if _, err := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(txnCtx, summary); err != nil {
			return err
		}
		if _, err := models.GetChannelReaderWriter().SaveChannelSchemaTable(txnCtx, subTaskChannelSchemaTable); err != nil {
			return err
		}
		if _, err := models.GetChannelReaderWriter().CreateChannelSchemaObject(txnCtx, subTaskChannelSchemaObject); err != nil {
			return err
		}

		_, createErr := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(txnCtx,
			buildCSVMigrationDetail("0", subTask, schemaTable, sourceDS, sourceColumnInfo, customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange),
		)
		if createErr != nil {
			return createErr
		}
		return nil
	})
	return trxErr
}

func migrateCSVDataByChunks(ctx context.Context, channelInfo *channel.ChannelInformation, taskInfo *task.Task, migrationParam *structs.CSVMigrationConfigParam, dbConns *MigrationDBConns) error {
	channelId := channelInfo.ChannelId
	taskId := taskInfo.TaskID

	taskTableConfigMap, buildErr := BuildTaskTableConfigMap(ctx, channelId, taskId)
	if buildErr != nil {
		return buildErr
	}

	sourceSchemas, getSchemaErr := models.GetChannelReaderWriter().GetChannelSchemaTablesDistinctSchemaSByChannelTaskId(ctx, channelId, taskId)
	if getSchemaErr != nil {
		log.Errorf("get task target schema info failed, taskId:%d, err:%v", taskId, getSchemaErr)
		return fmt.Errorf("get task target schema info failed: %v", getSchemaErr)
	}

	outputDataDir := migrationParam.GetOutputDataDir(taskInfo.ChannelId, taskInfo.TaskID)
	mkdirErr := fileutil.CreateIfNotExist(outputDataDir)
	if mkdirErr != nil {
		log.Errorf("create output data dir failed, taskId:%d, err:%v", taskId, mkdirErr)
		return fmt.Errorf("create output data dir failed: %v", mkdirErr)
	}

	log.Infof("start migrate csv data in chunks, taskId:%d, allSourceSchemas: %v, migrationParam: %s", taskId, sourceSchemas, migrationParam.String())
	for _, sourceSchema := range sourceSchemas {
		log.Infof("start migrate, taskId:%d, schemaName:%s", taskId, sourceSchema)
		migrationSummaries, getErr := getWaitingAndFailedMigrationSummaries(ctx, taskId, sourceSchema)
		if getErr != nil {
			log.Errorf("get migration summaries failed, taskId:%d, schemaName:%s, err:%v", taskId, sourceSchema, getErr)
			return getErr
		}

		log.Infof("start migrate, taskId:%d, schemaName:%s, summaryLen:%d, ", taskId, sourceSchema, len(migrationSummaries))
		if len(migrationSummaries) == 0 {
			log.Warnf("migrate skip, taskId:%d, schemaName:%s, summaryLen:%d", taskId, sourceSchema, len(migrationSummaries))
			_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
				ChannelID:   channelId,
				TaskID:      taskId,
				SchemaNameS: sourceSchema,
				Detail:      constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableSkip, fmt.Sprintf("skipping %s database, no tables or chunks to execute", sourceSchema)),
				LogLevel:    log.LogWarn,
			})
			if createErr != nil {
				log.Errorf("create progress log failed, taskId:%d, schemaName:%s, err:%v", taskId, sourceSchema, createErr)
				return createErr
			}
			continue
		}

		gSummary := &errgroup.Group{}
		gSummary.SetLimit(migrationParam.GetTableThreads())

		for summaryIdx := range migrationSummaries {
			migrationSummary := migrationSummaries[summaryIdx]

			summaryId := migrationSummary.ID
			schemaName := migrationSummary.SchemaNameS
			tableName := migrationSummary.TableNameS

			gSummary.Go(func() error {
				startExecTime := time.Now()

				if setRunningErr := setCSVMigrationSummaryRunning(ctx, migrationSummary, channelId, constants.MigrationLogTypeCSV); setRunningErr != nil {
					log.Errorf("set migration running failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, schemaName, tableName, setRunningErr)
					return setRunningErr
				}
				_, getCsvStageErr := models.GetFullDataMigrationReaderWriter().GetCSVStageByTaskIdSchemaSTableS(ctx, taskId, migrationSummary.SchemaNameS, migrationSummary.TableNameS)
				if getCsvStageErr != nil {
					log.Errorf("get csv stage failed, summaryId:%d, taskId:%d, schemaName:%s, tableName:%s, err:%v", migrationSummary.ID, taskId, sourceSchema, migrationSummary.TableNameS, getCsvStageErr)
					return getCsvStageErr
				}

				log.Infof("start migrate, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s",
					taskId, summaryId, schemaName, tableName)
				details, getDetailErr := getWaitingAndFailedMigrationDetails(ctx, migrationSummary)
				if getDetailErr != nil {
					log.Errorf("get migration details failed, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, err:%v",
						taskId, summaryId, schemaName, tableName, getDetailErr)
					return getDetailErr
				}
				log.Infof("start migrate, taskId:%d, summaryId:%d, schemaName:%s, tableName:%s, chunkNum:%d",
					taskId, summaryId, schemaName, tableName, len(details))

				progressHelper := buildProgressHelper(channelId, taskId, migrationSummary.SchemaNameS, migrationSummary.TableNameS, constants.MigrationLogTypeCSV)
				progressHelper.SetTotal(len(details))
				progressHelper.DisplayAndSaveProgressInTimeLoop(ctx)
				defer progressHelper.Close()
				defer progressHelper.DisplayAndSaveProgress(ctx)

				g := &errgroup.Group{}
				g.SetLimit(migrationParam.GetSqlThreads())

				for detailIdx := range details {
					migrationDetail := &(details[detailIdx])

					chunkId := migrationDetail.ID
					chunk := migrationDetail.ChunkDetailS

					getColumnsSQL := BuildGetColumnSQL(migrationDetail)
					columnNameS, getColumnErr := dbConns.GetSourceDB().GetOracleTableRowsColumnCSV(
						getColumnsSQL,
						transfercommon.MigrateOracleCharsetStringConvertMapping[dbConns.GetSourceDS().Charset],
						transfercommon.StringUPPER(migrationParam.GetCharset()),
					)
					if getColumnErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", getColumnErr.Error(), chunkId, chunk)

						log.Errorf("get oracle table rows column failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, getColumnsSQL:%v, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, getColumnsSQL, getColumnErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						continue
					}

					if taskTableConfig, ok := taskTableConfigMap[structs.SchemaTablePair{
						SchemaName: schemaName,
						TableName:  tableName,
					}]; ok {
						if taskTableConfig.OperatorTag == "Y" {
							oracleCollation := dbConns.GetOracleCollation()
							oracleCols, err := GetColumnNamesOracleByTaskTableConfig(ctx, dbConns, schemaName, tableName, oracleCollation, taskTableConfigMap)
							if err != nil {
								log.Errorf("GetColumnNamesOracleByTaskTableConfig failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, schemaName, tableName, err)
								return err
							}
							migrationDetail.ColumnDetailS = strings.Join(oracleCols, ",")
						}
						if taskTableConfig.TablePartition != "" {
							migrationDetail.TablePartition = taskTableConfig.TablePartition
						}
					}

					rows, buildErr := buildCSVDataMigrationReq(ctx, taskInfo, migrationDetail, dbConns, migrationParam, columnNameS)
					if buildErr != nil {
						progressHelper.IncreaseFailedNum()
						errMessage := fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", buildErr.Error(), chunkId, chunk)
						log.Errorf("buildFullDataMigrationReq failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
							taskId, summaryId, chunkId, chunk, schemaName, tableName, buildErr)
						errC := SetMigrationChunkFailed(ctx, taskInfo.ChannelId, taskInfo.TaskID, migrationDetail, errMessage, constants.MigrationLogTypeCSV)
						if errC != nil {
							log.Errorf("SetMigrationChunkFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errC)
							return errC
						} else {
							log.Infof("SetMigrationChunkFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						_, errS := SetMigrationSummaryFinishOrFailed(ctx, migrationSummary, taskInfo.ChannelId, startExecTime, constants.MigrationLogTypeCSV)
						if errS != nil {
							log.Errorf("SetMigrationSummaryFinishOrFailed failed, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s, err:%v",
								taskId, summaryId, chunkId, chunk, schemaName, tableName, errS)
							return errS
						} else {
							log.Infof("SetMigrationSummaryFinishOrFailed success, taskId:%d, summaryId:%d, chunkId:%d, chunk:%s, schemaName:%s, tableName:%s",
								taskId, summaryId, chunkId, chunk, schemaName, tableName)
						}
						continue
					}

					g.Go(func() error {
						return doDataMigration(ctx, transferDBMigrateCSVFunc, channelId, migrationDetail, rows, progressHelper, constants.MigrationLogTypeCSV)
					})
				}
				log.Infof("waiting for details migration finish, taskId:%d, summaryId:%d, chunkNum:%d, schemaName:%s, tableName:%s",
					taskId, migrationSummary.ID, len(details), migrationSummary.SchemaNameS, migrationSummary.TableNameS)

				_ = g.Wait() // 忽略errgroup的错误，因为不管错误与否，都需要继续执行下面的逻辑

				_, _, setErr := trySetCSVMigrationSummaryFailed(ctx, migrationSummary, taskInfo, len(details), startExecTime)
				if setErr != nil {
					log.Errorf("process and save data migration results failed, summaryId:%d, taskId:%d, schemaName:%s, tableName:%s, err:%v", migrationSummary.ID, taskId, sourceSchema, migrationSummary.TableNameS, setErr)
					return setErr
				}
				return nil
			})
		}
		log.Infof("waiting for schema migration finish, taskId:%d, schemaName:%s", taskId, sourceSchema)
		if waitErr := gSummary.Wait(); waitErr != nil {
			log.Errorf("migrateCSVDataByChunks failed, taskId:%d, schemaName:%s, err:%v", taskId, sourceSchema, waitErr)
			return waitErr
		}
	}
	return nil
}

func setCSVMigrationSummaryRunning(ctx context.Context, s *migration.TableMigrationSummary, channelId int, progressLogType string) error {
	return models.Transaction(ctx, func(txnCtx context.Context) error {
		errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(txnCtx,
			&migration.TableMigrationSummary{
				TaskID:      s.TaskID,
				SchemaNameS: s.SchemaNameS,
				TableNameS:  s.TableNameS,
			}, map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusRunning,
			})
		if errU != nil {
			return errU
		}

		_, errC := models.GetProgressLogReaderWriter().CreateProgressLogDetail(txnCtx, &common.ProgressLogDetail{
			ChannelID:   channelId,
			TaskID:      s.TaskID,
			SchemaNameS: s.SchemaNameS,
			TableNameS:  s.TableNameS,
			Detail:      constants.BuildProgressLog(progressLogType, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, export data started", s.SchemaNameS, s.TableNameS)),
			LogLevel:    log.LogInfo,
		})
		if errC != nil {
			return fmt.Errorf("create progress log detail record failed: %v", errC)
		}
		return nil
	})
}
