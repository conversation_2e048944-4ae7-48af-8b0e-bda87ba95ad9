package migration

import (
	"context"
	"database/sql"
	"fmt"
	"path/filepath"
	"regexp"
	"sort"
	"strconv"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	transferconfig "gitee.com/pingcap_enterprise/tms/lib/transferdb/config"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/mysql"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/logger"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate"
	csvpublic "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/csv/oracle/public"
	tmso2t "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/o2t"
	sqlpublic "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/public"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	commonmodel "gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/config"
	utildatabase "gitee.com/pingcap_enterprise/tms/util/database"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/parse"
	stringUtil "gitee.com/pingcap_enterprise/tms/util/string"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/BurntSushi/toml"
	"github.com/pingcap/errors"
	"github.com/samber/lo"

	"strings"
	"sync"
	"time"
)

const (
	ADG_DEFAULT_CHUNK_SIZE                        = int64(-1)
	NO_CHUNK_DEFAULT_CHUNK_SIZE                   = int64(-1)
	GenerateChunkTypeSplit      GenerateChunkType = 0
	GenerateChunkTypeFetch      GenerateChunkType = 1
)

type TransferDBMigrateFunc func(ex migrate.Migrator) error
type GenerateChunkType int

var errSQLRegex = regexp.MustCompile("sql \\[(?s).*] execute")
var transferDBMigrateFullDataFunc = sqlpublic.IMigrate
var transferDBMigrateCSVFunc = csvpublic.IMigrate

type MigrationDBConns struct {
	sourceDS        *datasource.Datasource
	targetDS        *datasource.Datasource
	sourceDB        *oracle.Oracle
	targetDB        *mysql.MySQL
	oracleCollation bool
}

type MigrationDBSources struct {
	sourceDS *datasource.Datasource
	targetDS *datasource.Datasource
}

func (i MigrationDBSources) GetSourceDS() *datasource.Datasource {
	return i.sourceDS
}

func (i MigrationDBSources) GetTargetDS() *datasource.Datasource {
	return i.targetDS
}

type MigrationSourceDBConns struct {
	sourceDS        *datasource.Datasource
	sourceDB        *oracle.Oracle
	oracleCollation bool
}

func (i *MigrationSourceDBConns) GetSourceDS() *datasource.Datasource {
	return i.sourceDS
}

func (i *MigrationSourceDBConns) GetSourceDB() *oracle.Oracle {
	return i.sourceDB
}

func (i *MigrationSourceDBConns) GetOracleCollation() bool {
	return i.oracleCollation
}

// Close closes the source database connection
func (i *MigrationSourceDBConns) Close() error {
	if i.sourceDB != nil && i.sourceDB.OracleDB != nil {
		return i.sourceDB.OracleDB.Close()
	}
	return nil
}

func (i *MigrationDBConns) GetOracleCollation() bool {
	return i.oracleCollation
}

type AdditionalLoggerHelper struct {
	sync.RWMutex
	memo map[string]bool
}

var additionalLoggerHelper AdditionalLoggerHelper

func (i *AdditionalLoggerHelper) InitAdditionalLogger(logName string) {
	i.Lock()
	defer i.Unlock()
	if i.memo == nil {
		i.memo = make(map[string]bool)
	}
	_, ok := i.memo[logName]
	if ok {
		return
	}
	globalCfg := config.GetGlobalConfig()
	logger.NewZapLogger(&transferconfig.Config{LogConfig: transferconfig.LogConfig{
		LogLevel:   globalCfg.LogConfig.LogLevel,
		LogFile:    filepath.Join(globalCfg.LogConfig.LogFileRoot, logName),
		MaxSize:    globalCfg.LogConfig.LogMaxSize,
		MaxDays:    globalCfg.LogConfig.LogMaxAge,
		MaxBackups: globalCfg.LogConfig.LogMaxBackups,
	}})
	i.memo[logName] = true
}

type MigrationSourceConns interface {
	GetSourceDS() *datasource.Datasource
	GetSourceDB() *oracle.Oracle
}

func (i *MigrationDBConns) GetSourceDS() *datasource.Datasource {
	return i.sourceDS
}

func (i *MigrationDBConns) GetTargetDS() *datasource.Datasource {
	return i.targetDS
}

func (i *MigrationDBConns) GetSourceDB() *oracle.Oracle {
	return i.sourceDB
}

func (i *MigrationDBConns) GetTargetDB() *mysql.MySQL {
	return i.targetDB
}

func GetMigrationMetadata(ctx context.Context, channelId int, taskId int) (*task.Task, *channel.ChannelInformation, *task.TaskparamTemplateConfig, *channel.ChannelSchemaObject, error) {
	log.Infof("get migratoin task meta data, channelId: %d, taskId: %d", channelId, taskId)
	// pre-get
	taskInfo, getTaskErr := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if getTaskErr != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task info failed: %v", getTaskErr)
	}
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if getChannelErr != nil {
		return nil, nil, nil, nil, fmt.Errorf("get channel info failed: %v", getChannelErr)
	}
	taskParamTempConfig, getTempalteErr := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if getTempalteErr != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task template info failed: %v", getTempalteErr)
	}
	channelSchemaObject, getObjectErr := models.GetChannelReaderWriter().GetChannelSchemaObjectByTaskId(ctx, taskId)
	if getObjectErr != nil {
		return nil, nil, nil, nil, fmt.Errorf("get task schema object info failed: %v", getObjectErr)
	}
	_, checkErr := validateChannelAndGetClearFlag(channelSchemaObject)
	if checkErr != nil {
		return nil, nil, nil, nil, fmt.Errorf("validate channel schema object failed: %v", checkErr)
	}

	return taskInfo, channelInfo, taskParamTempConfig, channelSchemaObject, nil
}

func validateChannelAndGetClearFlag(channelObj *channel.ChannelSchemaObject) (bool, error) {
	var clearData = false
	if strings.EqualFold(channelObj.ReloadData, "Y") && strings.EqualFold(channelObj.LoadData, "N") {
		clearData = true
	}

	if strings.EqualFold(channelObj.ReloadData, "Y") && strings.EqualFold(channelObj.LoadData, "Y") {
		return false, fmt.Errorf("get chennel schema objetc load_data [Y] and reload_data [Y] paramter failed")
	}

	if strings.EqualFold(channelObj.ReloadData, "N") && strings.EqualFold(channelObj.LoadData, "N") {
		return false, fmt.Errorf("get chennel schema objetc load_data [N] and reload_data [N] paramter failed")
	}

	log.Infof("clear data option, reload data is [%v], load data is [%v], clear data [%v], channelId:%d", channelObj.ReloadData, channelObj.LoadData, clearData, channelObj.ChannelId)
	return clearData, nil
}

// SetUpDatabaseConnsAndCollation set up database conn by channel info and check database collation
func SetUpDatabaseConnsAndCollation(ctx context.Context, channelInfo *channel.ChannelInformation, taskId int) (*MigrationDBConns, error) {

	databaseSources, getErr := GetDatabaseSources(ctx, channelInfo, taskId)
	if getErr != nil {
		return nil, fmt.Errorf("get database sources failed: %v", getErr)
	}
	sourceDS, targetDS := databaseSources.sourceDS, databaseSources.targetDS

	// meta database is META
	// source database is ORACLE
	// target database is TiDB

	if !strings.EqualFold(channelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		return nil, fmt.Errorf("channel type [%s] isn' support, please agagin choose", channelInfo.ChannelType)
	}

	log.Infof("database connection charset, taskID [%v], source conn charset is [%v] datasourceID [%v], target conn charset is [%v] datasourceID [%v] ", taskId, sourceDS.Charset, sourceDS.DatasourceId, targetDS.Charset, targetDS.DatasourceId)

	checkDB, newOracleErr := oracle.NewOracleDBEngine(ctx, transferconfig.OracleConfig{
		Username:    sourceDS.UserName,
		Password:    sourceDS.PasswordValue,
		Host:        sourceDS.HostIp,
		Port:        sourceDS.HostPort,
		ServiceName: datasourcepkg.GetServiceName(sourceDS),
	}, "")
	if newOracleErr != nil {
		return nil, fmt.Errorf("create source datasource conn failed: %v", newOracleErr)
	}
	// 数据库字符集
	charset, getCharsetErr := checkDB.GetOracleDBCharacterSet()
	if getCharsetErr != nil {
		return nil, getCharsetErr
	}
	sourceDBCharset := strings.Split(charset, ".")[1]
	if !strings.EqualFold(sourceDS.Charset, sourceDBCharset) {
		log.Warnf("oracle server charset [%v] and oracle config charset [%v] aren't different, would be ignore, running with oracle server charset [%v]", sourceDBCharset, sourceDS.Charset, sourceDBCharset)
	}

	if _, ok := transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(sourceDBCharset)]; !ok {
		return nil, fmt.Errorf("oracle current charset [%v] isn't support, support charset [%v]", sourceDBCharset, transfercommon.MigrateOracleCharsetStringConvertMapping)
	}
	if !strings.EqualFold(transfercommon.MYSQLCharsetUTF8MB4, targetDS.Charset) {
		return nil, fmt.Errorf("tidb current config charset [%v] isn't support, tidb only support charset [%v]", targetDS.Charset, transfercommon.MYSQLCharsetUTF8MB4)
	}

	sourceDB, newSourceErr := oracle.NewOracleDBEngine(ctx, transferconfig.OracleConfig{
		Username:      sourceDS.UserName,
		Password:      sourceDS.PasswordValue,
		Host:          sourceDS.HostIp,
		Port:          sourceDS.HostPort,
		ServiceName:   datasourcepkg.GetServiceName(sourceDS),
		ConnectParams: sourceDS.ConnectParams,
		Charset:       sourceDBCharset,
	}, "")
	if newSourceErr != nil {
		return nil, fmt.Errorf("create source datasource conn failed: %v", newSourceErr)
	}

	targetDB, newTargetErr := NewMySQLDBEngine(ctx, transferconfig.MySQLConfig{
		Username:      targetDS.UserName,
		Password:      targetDS.PasswordValue,
		Host:          targetDS.HostIp,
		Port:          targetDS.HostPort,
		ConnectParams: targetDS.ConnectParams,
		Charset:       targetDS.Charset,
		//TableOption:   targetDS.TableOption,
	})
	if newTargetErr != nil {
		log.Errorf("create target datasource conn failed, taskId:%d, error:%v", taskId, newTargetErr)
		return nil, fmt.Errorf("create target datasource conn failed: %v", newTargetErr)
	}

	oracleDBVersion, getVersionErr := sourceDB.GetOracleDBVersion()
	if getVersionErr != nil {
		return nil, getVersionErr
	}
	if transfercommon.VersionOrdinal(oracleDBVersion) < transfercommon.VersionOrdinal(transfercommon.RequireOracleDBVersion) {
		return nil, fmt.Errorf("oracle db version [%v] is less than 11g, can't be using transferdb tools", oracleDBVersion)
	}
	oracleCollation := false
	if transfercommon.VersionOrdinal(oracleDBVersion) >= transfercommon.VersionOrdinal(transfercommon.OracleTableColumnCollationDBVersion) {
		oracleCollation = true
	}
	if _, setModuleErr := sourceDB.OracleDB.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
		return nil, setModuleErr
	}

	return &MigrationDBConns{
		sourceDS:        sourceDS,
		targetDS:        targetDS,
		sourceDB:        sourceDB,
		targetDB:        targetDB,
		oracleCollation: oracleCollation,
	}, nil
}

// SetUpTargetConns set up database conn by channel info
func SetUpTargetConns(ctx context.Context, channelInfo *channel.ChannelInformation, taskId int) (*MigrationDBConns, error) {

	databaseSources, getErr := GetDatabaseSources(ctx, channelInfo, taskId)
	if getErr != nil {
		return nil, fmt.Errorf("get database sources failed: %v", getErr)
	}
	_, targetDS := databaseSources.sourceDS, databaseSources.targetDS

	targetDB, newTargetErr := NewMySQLDBEngine(ctx, transferconfig.MySQLConfig{
		Username:      targetDS.UserName,
		Password:      targetDS.PasswordValue,
		Host:          targetDS.HostIp,
		Port:          targetDS.HostPort,
		ConnectParams: targetDS.ConnectParams,
		Charset:       targetDS.Charset,
		//TableOption:   targetDS.TableOption,
	})
	if newTargetErr != nil {
		log.Errorf("create target datasource conn failed, taskId:%d, error:%v", taskId, newTargetErr)
		return nil, fmt.Errorf("create target datasource conn failed: %v", newTargetErr)
	}

	return &MigrationDBConns{
		targetDS: targetDS,
		targetDB: targetDB,
	}, nil
}

// GetDatabaseSources get database sources
func GetDatabaseSources(ctx context.Context, channelInfo *channel.ChannelInformation, taskId int) (*MigrationDBSources, error) {
	log.Infof("get database sources, channelId: %d, taskId: %d", channelInfo.ChannelId, taskId)

	// create database conn
	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", getSourceErr)
	}
	targetDS, getTargetErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if getTargetErr != nil {
		return nil, fmt.Errorf("get task target datasource info failed: %v", getTargetErr)
	}

	return &MigrationDBSources{
		sourceDS: sourceDS,
		targetDS: targetDS,
	}, nil
}

// SetUpTargetDatabaseConn set up database conn
func SetUpTargetDatabaseConn(ctx context.Context, channelInfo *channel.ChannelInformation) (*mysql.MySQL, error) {
	targetDS, getTargetErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if getTargetErr != nil {
		return nil, fmt.Errorf("get task target datasource info failed: %v", getTargetErr)
	}

	targetDB, newTargetErr := NewMySQLDBEngine(ctx, transferconfig.MySQLConfig{
		Username:      targetDS.UserName,
		Password:      targetDS.PasswordValue,
		Host:          targetDS.HostIp,
		Port:          targetDS.HostPort,
		ConnectParams: targetDS.ConnectParams,
		Charset:       targetDS.Charset,
		//TableOption:   targetDS.TableOption,
	})
	if newTargetErr != nil {
		log.Errorf("create target datasource conn failed, taskId:%d, error:%v", newTargetErr)
		return nil, fmt.Errorf("create target datasource conn failed: %v", newTargetErr)
	}
	return targetDB, nil
}

func NewMySQLDBEngine(ctx context.Context, mysqlCfg transferconfig.MySQLConfig) (*mysql.MySQL, error) {
	// Build MySQL configuration for unified connection
	config := utildatabase.MySQLConfig{
		User:          mysqlCfg.Username,
		Password:      mysqlCfg.Password,
		Host:          mysqlCfg.Host,
		Port:          mysqlCfg.Port,
		Database:      "", // Empty for root connection
		Charset:       mysqlCfg.Charset,
		ConnectParams: mysqlCfg.ConnectParams,
		Timezone:      time.Local,
	}

	// Prepare connection options to match original behavior
	opts := []utildatabase.Option{
		utildatabase.WithMaxIdleConns(256),
		utildatabase.WithMaxOpenConns(256),
		utildatabase.WithConnMaxLifetime(transfercommon.MySQLConnMaxLifeTime),
		utildatabase.WithPingTimeout(5),
	}

	// Create MySQL connector and open connection
	connector := utildatabase.NewMySQLConnector(config)
	mysqlDB, openErr := utildatabase.OpenMySQL(connector, opts...)
	if openErr != nil {
		return nil, fmt.Errorf("error on open mysql database connection: %v", openErr)
	}

	// Set additional connection settings to match original behavior
	mysqlDB.SetConnMaxIdleTime(transfercommon.MySQLConnMaxIdleTime)

	return &mysql.MySQL{
		Ctx:     ctx,
		MySQLDB: mysqlDB,
	}, nil
}

// SetUpOracleDatabaseConns set up oracle database conn by channel info
func SetUpOracleDatabaseConns(ctx context.Context, channelInfo *channel.ChannelInformation, taskId int) (*MigrationSourceDBConns, error) {
	// create database conn
	sourceDS, getSourceErr := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdS)
	if getSourceErr != nil {
		return nil, fmt.Errorf("get task source datasource info failed: %v", getSourceErr)
	}

	// meta database is META
	// source database is ORACLE
	// target database is TiDB

	if !strings.EqualFold(channelInfo.ChannelType, constants.CHANNEL_TYPE_O2T) {
		return nil, fmt.Errorf("channel type [%s] isn' support, please agagin choose", channelInfo.ChannelType)
	}

	log.Infof("database connection charset, taskID [%v], source conn charset is [%v] datasourceID [%v]", taskId, sourceDS.Charset, sourceDS.DatasourceId)

	checkDB, newDBErr := oracle.NewOracleDBEngine(ctx, transferconfig.OracleConfig{
		Username:    sourceDS.UserName,
		Password:    sourceDS.PasswordValue,
		Host:        sourceDS.HostIp,
		Port:        sourceDS.HostPort,
		ServiceName: datasourcepkg.GetServiceName(sourceDS),
	}, "")
	if newDBErr != nil {
		return nil, fmt.Errorf("create source datasource conn failed: %v", newDBErr)
	}
	// 数据库字符集
	charset, getCharsetErr := checkDB.GetOracleDBCharacterSet()
	if getCharsetErr != nil {
		return nil, getCharsetErr
	}
	sourceDBCharset := strings.Split(charset, ".")[1]
	if !strings.EqualFold(sourceDS.Charset, sourceDBCharset) {
		log.Warnf("oracle server charset [%v] and oracle config charset [%v] aren't different, would be ignore, running with oracle server charset [%v]", sourceDBCharset, sourceDS.Charset, sourceDBCharset)
	}

	if _, ok := transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(sourceDBCharset)]; !ok {
		return nil, fmt.Errorf("oracle current charset [%v] isn't support, support charset [%v]", sourceDBCharset, transfercommon.MigrateOracleCharsetStringConvertMapping)
	}

	sourceDB, newSourceDBErr := oracle.NewOracleDBEngine(ctx, transferconfig.OracleConfig{
		Username:      sourceDS.UserName,
		Password:      sourceDS.PasswordValue,
		Host:          sourceDS.HostIp,
		Port:          sourceDS.HostPort,
		ServiceName:   datasourcepkg.GetServiceName(sourceDS),
		ConnectParams: sourceDS.ConnectParams,
		Charset:       sourceDS.Charset,
	}, "")
	if newSourceDBErr != nil {
		return nil, fmt.Errorf("create source datasource conn failed: %v", newSourceDBErr)
	}

	oracleDBVersion, getVersionErr := sourceDB.GetOracleDBVersion()
	if getVersionErr != nil {
		return nil, getVersionErr
	}

	if transfercommon.VersionOrdinal(oracleDBVersion) < transfercommon.VersionOrdinal(transfercommon.RequireOracleDBVersion) {
		return nil, fmt.Errorf("oracle db version [%v] is less than 11g, can't be using transferdb tools", oracleDBVersion)
	}

	oracleCollation := false
	if transfercommon.VersionOrdinal(oracleDBVersion) >= transfercommon.VersionOrdinal(transfercommon.OracleTableColumnCollationDBVersion) {
		oracleCollation = true
	}

	if _, setModuleErr := sourceDB.OracleDB.Exec(constants.SET_TMS_MODULE_SQL); setModuleErr != nil {
		return nil, setModuleErr
	}

	return &MigrationSourceDBConns{
		sourceDS:        sourceDS,
		sourceDB:        sourceDB,
		oracleCollation: oracleCollation,
	}, nil
}

func BuildMigrationConfigParams(ctx context.Context, channelId, taskId, templateId int) (*structs.FullMigrationConfigParam, error) {
	var (
		chunkSize           int
		insertBatchSize     int
		taskThread          int
		tableThread         int
		sqlThread           int
		applyThread         int
		taskCountPerTable   int
		callTimeout         int
		consistentRead      bool
		removeSplitTask     bool
		sqlMigrateStatement int
		sqlRetryStatement   int
	)
	taskParamMap := make(map[string]string)

	// task params merge
	defaultTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, templateId)
	if getParamErr != nil {
		return nil, fmt.Errorf("get task params detail info failed: %v", getParamErr)
	}

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, getParamsErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           channelId,
		TaskID:              taskId,
		TaskparamTemplateID: templateId,
	})
	if getParamsErr != nil {
		return nil, fmt.Errorf("get task params info failed: %v", getParamsErr)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}

	var err error
	if value, ok := taskParamMap[constants.ParamsFullMigrationChunkSize]; ok {
		chunkSize, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt chunkSize failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationInsertBatchSize]; ok {
		insertBatchSize, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt chunkSize failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationTaskThreads]; ok {
		taskThread, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt taskThread failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationTableThreads]; ok {
		tableThread, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt tableThread failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationSqlThreads]; ok {
		sqlThread, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt sqlThread failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationApplyThreads]; ok {
		applyThread, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt applyThread failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationTaskCountPerTable]; ok {
		taskCountPerTable, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt taskCountPerTable failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationCallTimeout]; ok {
		callTimeout, err = parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt callTimeout failed: %v", err)
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationConsistentRead]; ok {
		consistentRead = parseParamValueToBool(value)
	}
	if value, ok := taskParamMap[constants.ParamsFullRemoveSplitTask]; ok {
		removeSplitTask = parseParamValueToBoolDefault(value, true)
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationSQLMigrateStatement]; ok {
		sqlMigrateStatement, err = parse.ParseInt(value)
		if err != nil {
			sqlMigrateStatement = 1
		}
	}
	if value, ok := taskParamMap[constants.ParamsFullMigrationSQLRetryStatement]; ok {
		sqlRetryStatement, err = parse.ParseInt(value)
		if err != nil {
			sqlRetryStatement = 1
		}
	}
	return &structs.FullMigrationConfigParam{
		ApplyThreads:        applyThread,
		ChunkSize:           int64(chunkSize),
		InsertBatchSize:     insertBatchSize,
		SqlThreads:          sqlThread,
		TableThreads:        tableThread,
		TaskThreads:         taskThread,
		TaskCountPerTable:   taskCountPerTable,
		CallTimeout:         callTimeout,
		ConsistentRead:      consistentRead,
		SQLRetryStatement:   sqlRetryStatement,
		SQLMigrateStatement: sqlMigrateStatement,
		RemoveSplitTask:     removeSplitTask,
	}, nil
}

func BuildCSVMigrationParam(ctx context.Context, taskInfo *task.Task, templateId int) (*structs.CSVMigrationConfigParam, error) {
	log.Infof("build migration config params, channelId: %d, taskId: %d, templateId:%d", taskInfo.ChannelId, taskInfo.TaskID, templateId)

	migrationParam := &structs.CSVMigrationConfigParam{}
	taskParamMap := make(map[string]string)

	// task params merge
	defaultTaskCfgs, getDetailErr := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, templateId)
	if getDetailErr != nil {
		return nil, fmt.Errorf("get task params detail info failed: %v", getDetailErr)
	}

	for _, taskCfg := range defaultTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueDefault
	}

	customTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.ChannelId,
		TaskID:              taskInfo.TaskID,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if getParamErr != nil {
		return nil, fmt.Errorf("get task params info failed: %v", getParamErr)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}

	if value, ok := taskParamMap[constants.ParamsCsvHeader]; ok {
		migrationParam.SetHeader(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvSeparator]; ok {
		migrationParam.SetSeparator(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTerminator]; ok {
		value = strings.ReplaceAll(value, `\n`, "\n")
		value = strings.ReplaceAll(value, `\t`, "\t")
		value = strings.ReplaceAll(value, `\r`, "\r")
		migrationParam.SetTerminator(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvCharset]; ok {
		migrationParam.SetCharset(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvDelimiter]; ok {
		migrationParam.SetDelimiter(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvEscapeBackslash]; ok {
		migrationParam.SetEscapeBackslash(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvChunkSize]; ok {
		v, err := parse.ParseInt64(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt64 chunkSize failed: %v", err)
		}
		migrationParam.SetChunkSize(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvInsertBatchSize]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt insertBatchSize failed: %v", err)
		}
		migrationParam.SetInsertBatchSize(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvOutputDir]; ok {
		migrationParam.SetOutputDir(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvSortedKVDir]; ok {
		migrationParam.SetSortedKVDir(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTaskThreads]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt taskThreads failed: %v", err)
		}
		migrationParam.SetTaskThreads(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTableThreads]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt tableThreads failed: %v", err)
		}
		migrationParam.SetTableThreads(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvSqlThreads]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt sqlThreads failed: %v", err)
		}
		migrationParam.SetSqlThreads(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTaskCountPerTable]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt taskCountPerTable failed: %v", err)
		}
		migrationParam.SetTaskCountPerTable(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvCallTimeout]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt callTimeout failed: %v", err)
		}
		migrationParam.SetCallTimeout(v)
	}

	if value, ok := taskParamMap[constants.ParamsCsvConsistentRead]; ok {
		migrationParam.SetConsistentRead(parseParamValueToBool(value))
	}

	if value, ok := taskParamMap[constants.ParamsCsvRemoveSplitTask]; ok {
		migrationParam.SetRemoveSplitTask(parseParamValueToBoolDefault(value, true))
	}

	// lightning 相关参数
	if value, ok := taskParamMap[constants.ParamsCsvTiDBStatusPort]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt statusPort failed: %v", err)
		}
		migrationParam.SetTiDBStatusPort(v)
	}

	if value, ok := taskParamMap[constants.ParamsCsvBatchImportSize]; ok {
		size, _ := parse.ParseInt(value)
		if size <= 0 {
			size = 8
		}
		migrationParam.SetBatchImportSize(size)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningBinaryPath]; ok {
		migrationParam.SetLightningBinaryPath(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvCronSwitchMode]; ok {
		migrationParam.SetCronSwitchMode(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvRemoveCSV]; ok {
		migrationParam.SetLightningRemoveCSV(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiDBPdAddr]; ok {
		migrationParam.SetTiDBPDAddr(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiDBLogLevel]; ok {
		migrationParam.SetTiDBLogLevel(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvPostRestoreAnalyze]; ok {
		migrationParam.SetPostRestoreAnalyze(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvPostRestoreChecksum]; ok {
		migrationParam.SetPostRestoreChecksum(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterDiskQuota]; ok {
		migrationParam.SetTiKVImporterDiskQuota(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterStoreWriteBWLimit]; ok {
		migrationParam.SetTiKVImporterStoreWriteBWLimit(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterRangeConcurrency]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt rangeConcurrency failed: %v", err)
		}
		migrationParam.SetTiKVImporterRangeConcurrency(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterDuplicateResolution]; ok {
		migrationParam.SetTiKVImporterDuplicateResolution(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterBackend]; ok {
		migrationParam.SetTiKVImporterBackend(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterOnDuplicate]; ok {
		migrationParam.SetTiKVImporterOnDuplicate(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvCheckpointDriver]; ok {
		migrationParam.SetCheckpointDriver(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningLevel]; ok {
		migrationParam.SetLightningLogLevel(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningCheckRequirements]; ok {
		migrationParam.SetLightningCheckRequirements(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningIndexConcurrency]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt index-concurrency failed: %v", err)
		}
		migrationParam.SetLightningIndexConcurrency(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningTableConcurrency]; ok {
		v, err := parse.ParseInt(value)
		if err != nil {
			return nil, fmt.Errorf("parse.ParseInt table-concurrency failed: %v", err)
		}
		migrationParam.SetLightningTableConcurrency(v)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMetaSchemaName]; ok {
		migrationParam.SetLightningMetaSchemaName(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvCheckpointEnable]; ok {
		migrationParam.SetCheckpointEnable(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvTiKVImporterIncrementalImport]; ok {
		migrationParam.SetTiKVImporterIncrementalImport(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperCsvTrimLastSeparator]; ok {
		migrationParam.SetMydumperCsvTrimLastSeparator(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperCsvNull]; ok {
		migrationParam.SetMydumperCsvNull(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperCsvNotNull]; ok {
		migrationParam.SetMydumperCsvNotNull(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperCsvHeaderSchemaMatch]; ok {
		migrationParam.SetMydumperCsvHeaderSchemaMatch(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperFilter]; ok {
		arrObj := struct {
			FakeArr []string `toml:"fake_arr"`
		}{}
		err := toml.Unmarshal([]byte("fake_arr = "+value), &arrObj)
		if err != nil {
			return nil, fmt.Errorf("unmarshal mydumper filter failed: %v", err)
		}
		migrationParam.SetMydumperFilter(arrObj.FakeArr)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperStrictFormat]; ok {
		migrationParam.SetMydumperStrictFormat(parseParamValueToBool(value))
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperDataInvalidCharReplace]; ok {
		migrationParam.SetMydumperDataInvalidCharReplace(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperCharacterSet]; ok {
		migrationParam.SetMydumperCharacterSet(value)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperBatchImportRatio]; ok {
		floatVal, err := strconv.ParseFloat(value, 64)
		if err != nil {
			return nil, fmt.Errorf("strconv.ParseFloat BatchImportRatio failed: %v", err)
		}
		migrationParam.SetMydumperBatchImportRatio(floatVal)
	}
	if value, ok := taskParamMap[constants.ParamsCsvLightningMydumperReadBlockSize]; ok {
		migrationParam.SetMydumperReadBlockSize(value)
	}

	log.Infof("build csv migration param success, taskId:%d, %v", taskInfo.TaskID, migrationParam.String())
	return migrationParam, nil
}

func GetCSVTaskDirParamValues(ctx context.Context, taskInfo *task.Task, templateId int) (string, string, error) {
	taskParamMap := make(map[string]string)

	outputDirParam, getErr1 := models.GetTaskReaderWriter().GetParamDetail(ctx, templateId, constants.ParamsCsvOutputDir)
	if getErr1 != nil {
		return "", "", fmt.Errorf("get task params detail info failed: %v", getErr1)
	}

	sortedKvDirParam, getErr2 := models.GetTaskReaderWriter().GetParamDetail(ctx, templateId, constants.ParamsCsvSortedKVDir)
	if getErr2 != nil {
		return "", "", fmt.Errorf("get task params detail info failed: %v", getErr2)
	}

	taskParamMap[outputDirParam.ParamName] = outputDirParam.ParamValueDefault
	taskParamMap[sortedKvDirParam.ParamName] = sortedKvDirParam.ParamValueDefault

	customTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.ChannelId,
		TaskID:              taskInfo.TaskID,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if getParamErr != nil {
		return "", "", fmt.Errorf("get task params info failed: %v", getParamErr)
	}
	for _, taskCfg := range customTaskCfgs {
		taskParamMap[taskCfg.ParamName] = taskCfg.ParamValueCurrent
	}

	return taskParamMap[constants.ParamsCsvOutputDir], taskParamMap[constants.ParamsCsvSortedKVDir], nil
}

func parseParamValueToBoolDefault(value string, defaultValue bool) bool {
	if strings.EqualFold(value, "") {
		return defaultValue
	}
	return parseParamValueToBool(value)
}

func parseParamValueToBool(value string) bool {
	value = strings.TrimSpace(value)
	return strings.EqualFold(value, "Y") || strings.EqualFold(value, "yes") || strings.EqualFold(value, "true")
}

func BuildTaskTableConfigMap(ctx context.Context, channelId int, taskId int) (map[structs.SchemaTablePair]*task.TaskTableConfig, error) {
	taskTableConfigMap := make(map[structs.SchemaTablePair]*task.TaskTableConfig)
	taskTableConfigs, getConfigErr := models.GetTaskReaderWriter().GetTaskTableConfigsByTaskIdAndChannelId(ctx, channelId, taskId)
	if getConfigErr != nil {
		return nil, fmt.Errorf("get task table config failed: %v", getConfigErr)
	}
	for _, t := range taskTableConfigs {
		taskTableConfigMap[structs.SchemaTablePair{
			SchemaName: t.SchemaNameS,
			TableName:  t.TableNameS,
		}] = t
	}
	log.Infof("task table config num: %v, map size: %d, channelId: %d, taskId: %d", len(taskTableConfigs), len(taskTableConfigMap), channelId, taskId)
	return taskTableConfigMap, nil
}

func getWaitingAndFailedMigrationDetails(ctx context.Context, s *migration.TableMigrationSummary) ([]migration.TableMigrationDetail, error) {
	details, getDetailErr := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(
		ctx,
		s.TaskID,
		s.SchemaNameS,
		s.TableNameS,
		[]string{transfercommon.TaskStatusWaiting, transfercommon.TaskStatusFailed},
	)
	if getDetailErr != nil {
		return nil, getDetailErr
	}
	log.Infof("get waiting and failed migration details, taskId:%d, schema:%s, table:%s", s.TaskID, s.SchemaNameS, s.TableNameS)
	return details, nil
}

func getWaitingAndFailedMigrationSummaries(ctx context.Context, taskId int, sourceSchema string) ([]*migration.TableMigrationSummary, error) {
	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryByTaskIdSchemaAndStatusList(ctx,
		taskId,
		sourceSchema,
		[]string{transfercommon.TaskStatusWaiting, transfercommon.TaskStatusFailed},
	)
	log.Infof("get waiting and failed migration summaries, taskId:%d, schema:%s, count:%d", taskId, sourceSchema, len(tableSummaries))
	return tableSummaries, getSummaryErr
}

func getWaitingAndFailedMigrationSummariesV2(ctx context.Context, taskId int) ([]*migration.TableMigrationSummary, error) {
	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryByStatus(ctx,
		taskId,
		[]string{transfercommon.TaskStatusWaiting, transfercommon.TaskStatusFailed},
	)
	log.Infof("get waiting and failed migration summaries, taskId:%d, count:%d", taskId, len(tableSummaries))
	return tableSummaries, getSummaryErr
}

func getTaskWaitingAndFailedMigrationSummaries(ctx context.Context, taskId int) ([]*migration.TableMigrationSummary, error) {
	tableSummaries, getSummaryErr := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryByTaskIdAndStatusList(ctx,
		taskId,
		[]string{transfercommon.TaskStatusWaiting, transfercommon.TaskStatusFailed},
	)
	log.Infof("get waiting and failed migration summaries, taskId:%d, count:%d", taskId, len(tableSummaries))
	return tableSummaries, getSummaryErr
}

func trySetCSVMigrationSummaryFailed(ctx context.Context, s *migration.TableMigrationSummary, taskInfo *task.Task, totalChunkNum int, startExecTime time.Time) (string, string, error) {
	// 找出失败的切片任务
	failedChunkDetails, errS := getFailedChunkDetails(ctx, s)
	if errS != nil {
		log.Errorf("table [%s.%s], get failed chunk details failed: %v", s.SchemaNameS, s.TableNameS, errS)
		return transfercommon.TaskStatusFailed, "get chunks from meta db failed", errS
	}
	log.Infof("table [%s.%s], failed chunk details num: %d, taskId:%d, summary id:%d", s.SchemaNameS, s.TableNameS, len(failedChunkDetails), s.TaskID, s.ID)

	var errMessage string
	var taskStatus string
	var chunkSuccessNums int
	var chunkFailedNums int
	var progressDetail string
	var logLevel string

	if len(failedChunkDetails) != 0 {
		taskStatus = transfercommon.TaskStatusFailed
		chunkSuccessNums = totalChunkNum - len(failedChunkDetails)
		chunkFailedNums = len(failedChunkDetails)
		progressDetail = constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, export data failed, success %d, failed %d", s.SchemaNameS, s.TableNameS, chunkSuccessNums, chunkFailedNums))
		logLevel = log.LogError

		for _, detail := range failedChunkDetails {
			if errMessage == "" {
				errMessage = detail.ErrorDetail
			} else {
				break
			}
		}

		trxErr := models.Transaction(ctx, func(txnCtx context.Context) error {
			updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(txnCtx,
				&migration.TableMigrationSummary{
					TaskID:      s.TaskID,
					SchemaNameS: s.SchemaNameS,
					TableNameS:  s.TableNameS,
				}, map[string]interface{}{
					"Task_Status":        taskStatus,
					"Chunk_Success_Nums": chunkSuccessNums,
					"Chunk_Failed_Nums":  chunkFailedNums,
					"Duration":           time.Now().Sub(startExecTime).Seconds(),
				})
			if updateErr != nil {
				return updateErr
			}
			_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(txnCtx, &common.ProgressLogDetail{
				ChannelID:   taskInfo.ChannelId,
				TaskID:      s.TaskID,
				SchemaNameS: s.SchemaNameS,
				TableNameS:  s.TableNameS,
				Detail:      progressDetail,
				LogLevel:    logLevel,
			})
			if createErr != nil {
				return fmt.Errorf("migrate finish. create progress log detail record failed: %v", createErr)
			}
			return nil
		})
		if trxErr != nil {
			log.Errorf("table [%s.%s], update migration summary and create progress log failed: %v", s.SchemaNameS, s.TableNameS, trxErr)
			return transfercommon.TaskStatusFailed, "update migration status failed", trxErr
		}
	}
	return taskStatus, errMessage, nil
}

func SetMigrationSummaryFinishOrFailed(ctx context.Context, s *migration.TableMigrationSummary, channelId int, startExecTime time.Time, progressLogType string) (string, error) {
	// 找出失败的切片任务
	detailSummary, errS := models.GetFullDataMigrationReaderWriter().GetTableMigrationDetailSummary(ctx, &migration.TableMigrationDetail{
		TaskID:      s.TaskID,
		SchemaNameS: s.SchemaNameS,
		TableNameS:  s.TableNameS,
		SchemaNameT: s.SchemaNameT,
		TableNameT:  s.TableNameT,
	})
	if errS != nil {
		log.Errorf("table [%s.%s], get chunk details summary failed: %v", s.SchemaNameS, s.TableNameS, errS)
		return transfercommon.TaskStatusFailed, errS
	}
	log.Infof("table [%s.%s], failed chunk details num: %d, taskId:%d, summary id:%d", s.SchemaNameS, s.TableNameS, detailSummary.FailedChunks, s.TaskID, s.ID)

	var taskStatus string
	var chunkSuccessNums int
	var chunkFailedNums int
	var progressDetail string
	var logLevel string

	chunkSuccessNums = detailSummary.SuccessChunks
	chunkFailedNums = detailSummary.FailedChunks
	if detailSummary.FailedChunks == 0 {
		taskStatus = transfercommon.TaskStatusSuccess
		progressDetail = constants.BuildProgressLog(progressLogType, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, migration successful", s.SchemaNameS, s.TableNameS))
		logLevel = log.LogInfo
	} else {
		taskStatus = transfercommon.TaskStatusFailed
		progressDetail = constants.BuildProgressLog(progressLogType, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, migration failed, success %d, failed %d", s.SchemaNameS, s.TableNameS, chunkSuccessNums, chunkFailedNums))
		logLevel = log.LogError
	}

	trxErr := models.Transaction(ctx, func(txnCtx context.Context) error {
		updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(txnCtx,
			&migration.TableMigrationSummary{
				TaskID:      s.TaskID,
				SchemaNameS: s.SchemaNameS,
				TableNameS:  s.TableNameS,
			}, map[string]interface{}{
				"Task_Status":        taskStatus,
				"Chunk_Success_Nums": chunkSuccessNums,
				"Chunk_Failed_Nums":  chunkFailedNums,
				"Duration":           time.Now().Sub(startExecTime).Seconds(),
			})
		if updateErr != nil {
			return updateErr
		}
		_, createErr := models.GetProgressLogReaderWriter().CreateProgressLogDetail(txnCtx, &common.ProgressLogDetail{
			ChannelID:   channelId,
			TaskID:      s.TaskID,
			SchemaNameS: s.SchemaNameS,
			TableNameS:  s.TableNameS,
			Detail:      progressDetail,
			LogLevel:    logLevel,
		})
		if createErr != nil {
			return fmt.Errorf("migrate finish. create progress log detail record failed: %v", createErr)
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("table [%s.%s], update migration summary and create progress log failed: %v", s.SchemaNameS, s.TableNameS, trxErr)
		return transfercommon.TaskStatusFailed, trxErr
	}
	return taskStatus, nil
}

func getFailedChunkDetails(ctx context.Context, s *migration.TableMigrationSummary) ([]migration.TableMigrationDetail, error) {
	failedChunkDetails, errS := models.GetFullDataMigrationReaderWriter().DetailTableMigrationDetail(ctx, &migration.TableMigrationDetail{
		TaskID:      s.TaskID,
		SchemaNameS: s.SchemaNameS,
		TableNameS:  s.TableNameS,
		SchemaNameT: s.SchemaNameT,
		TableNameT:  s.TableNameT,
		TaskStatus:  transfercommon.TaskStatusFailed,
	})
	return failedChunkDetails, errS
}

func IsOracleDatasource(ds *datasource.Datasource) bool {
	return ds.DbType == constants.DB_TYPE_ORACLE_ADG || ds.DbType == constants.DB_TYPE_ORACLE
}

func getCustomMigrationConf(taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, channelSchemaTable *channel.ChannelSchemaTable, taskChunkSize int64, sourceDS *datasource.Datasource) TableMigrationCustomConf {
	// 自定义迁移配置
	var (
		sourceSqlHint  string
		targetSqlHint  string
		wherePrefix    string
		enableSplitStr string
		enableSplit    bool
		tableChunkSize int64
	)
	if val, ok := taskTableConfigMap[structs.SchemaTablePair{
		SchemaName: channelSchemaTable.SchemaNameS,
		TableName:  channelSchemaTable.TableNameS,
	}]; ok {
		sourceSqlHint = val.SqlhintOracle
		targetSqlHint = val.SqlhintTidb
		wherePrefix = val.FilterClauseOracle
		enableSplitStr = val.EnableChunkSplit
		log.Debugf("table [%s.%s], found custom config", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	} else {
		log.Debugf("table [%s.%s], no custom config", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	}

	enableSplit = func() bool {
		if strings.EqualFold(enableSplitStr, "Y") || enableSplitStr == "1" || enableSplitStr == "True" || enableSplitStr == "" {
			return true
		}
		return false
	}()

	if tableChunkSize <= 0 {
		tableChunkSize = taskChunkSize
	}

	if IsOracleDatasource(sourceDS) {
		if sourceSqlHint == "" {
			if wherePrefix != "" {
				sourceSqlHint = `/*+ OPT_PARAM('_B_TREE_BITMAP_PLANS','FALSE') parallel(` + channelSchemaTable.TableNameS + `,4) ROWID(` + channelSchemaTable.TableNameS + `)*/ `
			} else {
				sourceSqlHint = `/*+ OPT_PARAM('_B_TREE_BITMAP_PLANS','FALSE') parallel(` + channelSchemaTable.TableNameS + `,4)*/ `
			}
		}
	}

	log.Infof("table [%s.%s], custom migration conf, chunkSize:%d, enableSplit:%v, wherePrefix:%s, sourceSqlHint:%s, targetSqlHint:%s, taskId:%d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS,
		tableChunkSize, enableSplit, wherePrefix, sourceSqlHint, targetSqlHint, channelSchemaTable.TaskId)

	return TableMigrationCustomConf{
		sourceSqlHint: sourceSqlHint,
		targetSqlHint: targetSqlHint,
		wherePrefix:   wherePrefix,
		enableSplit:   enableSplit,
		chunkSize:     tableChunkSize,
	}
}

func createSplitTaskAndGetChunkResults(sourceDB *oracle.Oracle, channelSchemaTable *channel.ChannelSchemaTable, chunkSize int64, callTimeOut int, removeSplitTask bool) ([]map[string]string, int, error) {
	taskName := fmt.Sprintf("tms_%d_%s_%d", channelSchemaTable.TaskId, channelSchemaTable.TableNameS, time.Now().Unix())

	log.Infof("table [%s.%s], start oracle table chunk create task [%s], chunkSize: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskName, chunkSize)
	if err := sourceDB.StartOracleChunkCreateTask(taskName); err != nil {
		return nil, 0, err
	}

	if err := sourceDB.StartOracleCreateChunkByRowID(taskName, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, parse.FormatInt(chunkSize), callTimeOut); err != nil {
		return nil, 0, err
	}

	chunkRes, getChunksErr := sourceDB.GetOracleTableChunksByRowID(taskName)
	if getChunksErr != nil {
		return nil, 0, getChunksErr
	}
	if removeSplitTask {
		log.Infof("table [%s.%s], remove oracle table chunk task [%s]", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskName)
		if closeErr := sourceDB.CloseOracleChunkTask(taskName); closeErr != nil {
			return nil, 0, closeErr
		}
	} else {
		log.Infof("table [%s.%s], keep oracle table chunk task [%s]", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskName)
	}
	return chunkRes, len(chunkRes), nil
}

func fetchTableChunkResults(sourceDB *oracle.Oracle, channelSchemaTable *channel.ChannelSchemaTable) ([]map[string]string, int, error) {
	log.Infof("table [%s.%s], fetch oracle table chunk, taskId:%d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, channelSchemaTable.TaskId)

	ctx := context.Background()

	var taskName string

	distinctSQL := fmt.Sprintf(
		`
select distinct a.task_owner,a.task_name,a.chunk_type
from sys.dba_parallel_execute_tasks a,sys.DBMS_PARALLEL_EXECUTE_CHUNKS$ b
where a.task_name=b.task_name and a.table_owner='%s' and a.table_name='%s'
`, channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)

	_, rets, queryErr := oracle.Query(ctx, sourceDB.OracleDB, distinctSQL)
	if queryErr != nil {
		return nil, 0, queryErr
	}
	if len(rets) == 0 {
		return nil, 0, nil
	}
	if len(rets) > 1 {
		return nil, 0, fmt.Errorf("table %s.%s, chunk task count is more than 1", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
	}
	taskName = rets[0]["TASK_NAME"]
	chunkRes, getChunkErr := sourceDB.GetOracleTableChunksByRowID(taskName)
	if getChunkErr != nil {
		return nil, 0, getChunkErr
	}
	return chunkRes, len(chunkRes), nil
}

func CheckTableHasPrimaryUniqueKey(channelSchemaTable *channel.ChannelSchemaTable) bool {
	sourceTablePk := channelSchemaTable.PkS
	sourceTableUk := channelSchemaTable.UkS

	if sourceTablePk == "" && sourceTableUk == "" { // 兼容老的记录，全部视做有PK处理
		return true
	}

	hasPk := sourceTablePk == "Y"
	hasUk := sourceTableUk == "Y"

	return hasPk || hasUk
}

// 开启事务的，执行子任务的创建，chunk与子任务ID的映射，其他schemas/tables元数据的复制和持久化
func createSubTasksAndForkMetadata(ctx context.Context, channelSchemaTable *channel.ChannelSchemaTable, taskInfo *task.Task, chunkNum int, commonParam structs.CommonParam, migrationDetails []*migration.TableMigrationDetail, sourceDS *datasource.Datasource, channelSchemaObject *channel.ChannelSchemaObject, chunkSize int64) error {
	log.Infof("table [%s.%s], fork schema table/objects, migration details/summaries from parent task [%d], chunkNum: %d, taskCountPerTable: %d",
		channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, taskInfo.TaskID, chunkNum, commonParam.GetTaskCountPerTable())

	customTaskCfgs, getParamErr := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           taskInfo.ChannelId,
		TaskID:              taskInfo.TaskID,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if getParamErr != nil {
		log.Errorf("table [%s.%s], get task custom params info failed: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, getParamErr)
		return getParamErr
	}

	subTaskNum := lo.Min([]int{chunkNum, commonParam.GetTaskCountPerTable()}) // 至少有一个子任务
	if subTaskNum <= 0 {
		subTaskNum = 1
		subTaskNum = 1
	}

	if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
		if !CheckTableHasPrimaryUniqueKey(channelSchemaTable) {
			log.Warnf("table [%s.%s], has no primary key or unique key, force sub task num to 1", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS)
			subTaskNum = 1
		} else {
			log.Infof("table [%s.%s], has primary key or unique key, sub task num: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, subTaskNum)
		}
	}

	// 每张表划分多少个任务由模版参数 task_count_per_table 决定
	subTasks := lo.Times(subTaskNum, func(idx int) *task.Task {
		taskName := fmt.Sprintf("%s_%s_%d", taskInfo.TaskName, channelSchemaTable.TableNameT, idx)
		return buildSubTasksWithTaskName(taskInfo, taskName)
	})

	trxErr := models.Transaction(ctx, func(trxCtx context.Context) error {
		newSubTasks, createTaskErr := models.GetTaskReaderWriter().BatchCreateTask(trxCtx, subTasks)
		if createTaskErr != nil {
			return createTaskErr
		}

		// 构造chunk与子任务ID的映射
		// iterate migrationDetails and set taskId by subTasks sequentially
		subTaskChunkNumMap := make(map[int]int64)
		for i := range migrationDetails {
			migrationDetails[i].TaskID = newSubTasks[i%subTaskNum].TaskID
			subTaskChunkNumMap[newSubTasks[i%subTaskNum].TaskID]++
		}

		// 复制channel schema table和channel schema object，并保存到数据库
		subTaskMigrationSummaries := buildSubTaskMigrationSummaries(newSubTasks, sourceDS, channelSchemaTable, subTaskChunkNumMap, chunkSize)
		subTaskChannelSchemaTables := buildSubTaskChannelSchemaTables(newSubTasks, channelSchemaTable)
		subTaskChannelSchemaObjects := buildSubTaskChannelSchemaObjects(newSubTasks, channelSchemaObject)
		subTaskCustomParamConfigs := buildSubTaskCustomParamConfigs(newSubTasks, customTaskCfgs)

		log.Debugf("table [%s.%s], create task custom param configs, length: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(subTaskCustomParamConfigs))
		if len(subTaskCustomParamConfigs) != 0 {
			_, createCustomConfigErr := models.GetTaskReaderWriter().SaveTaskParamConfigs(trxCtx, subTaskCustomParamConfigs)
			if createCustomConfigErr != nil {
				return createCustomConfigErr
			}
		}
		log.Debugf("table [%s.%s], create channel schema object, length: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(subTaskChannelSchemaObjects))
		if len(subTaskChannelSchemaObjects) != 0 {
			_, createObjErr := models.GetChannelReaderWriter().CreateChannelSchemaObjects(trxCtx, subTaskChannelSchemaObjects)
			if createObjErr != nil {
				return createObjErr
			}
		}
		log.Debugf("table [%s.%s], create channel schema table, length: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(subTaskChannelSchemaTables))
		if len(subTaskChannelSchemaTables) != 0 {
			_, createTableErr := models.GetChannelReaderWriter().SaveChannelSchemaTables(trxCtx, subTaskChannelSchemaTables)
			if createTableErr != nil {
				return createTableErr
			}
		}
		log.Debugf("table [%s.%s], create migration summary, length: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(subTaskMigrationSummaries))
		if len(subTaskMigrationSummaries) != 0 {
			createSummaryErr := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationSummary(trxCtx, subTaskMigrationSummaries, commonParam.GetInsertBatchSize())
			if createSummaryErr != nil {
				return createSummaryErr
			}
		}
		log.Debugf("table [%s.%s], create migration detail, length: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(migrationDetails))
		if len(migrationDetails) != 0 {
			_, createDetailErr := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(trxCtx, migrationDetails, commonParam.GetInsertBatchSize())
			if createDetailErr != nil {
				return createDetailErr
			}
		}

		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			log.Debugf("table [%s.%s], create csv stage, length: %d", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, len(newSubTasks))
			createStageErr := models.GetFullDataMigrationReaderWriter().BatchSaveCSVStage(trxCtx, buildSubTaskCSVStages(newSubTasks, channelSchemaTable))
			if createStageErr != nil {
				return createStageErr
			}
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("table [%s.%s], create sub tasks and fork metadata failed, error: %v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, trxErr)
		return trxErr
	}

	return nil
}

func buildSubTaskCustomParamConfigs(tasks []*task.Task, cfgs []*task.TaskParamConfig) []*task.TaskParamConfig {
	subTaskCustomParamConfigs := make([]*task.TaskParamConfig, 0, len(tasks)*len(cfgs))
	for _, t := range tasks {
		for _, c := range cfgs {
			subTaskCustomParamConfigs = append(subTaskCustomParamConfigs, &task.TaskParamConfig{
				ChannelID:           t.ChannelId,
				TaskID:              t.TaskID,
				ParamName:           c.ParamName,
				ParamValueCurrent:   c.ParamValueCurrent,
				ParamValueDefault:   c.ParamValueDefault,
				TaskparamTemplateID: t.TaskParamTmplateId,
				HasSubParams:        c.HasSubParams,
				Entity: &common.Entity{
					Comment: c.Comment,
				},
			})
		}
	}
	return subTaskCustomParamConfigs
}

func setFullDataMigrationSummaryRunning(ctx context.Context, s *migration.TableMigrationSummary, channelId int, progressLogType string) error {
	return models.Transaction(ctx, func(txnCtx context.Context) error {
		errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(txnCtx,
			&migration.TableMigrationSummary{
				TaskID:      s.TaskID,
				SchemaNameS: s.SchemaNameS,
				TableNameS:  s.TableNameS,
			}, map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusRunning,
			})
		if errU != nil {
			return errU
		}

		_, errC := models.GetProgressLogReaderWriter().CreateProgressLogDetail(txnCtx, &common.ProgressLogDetail{
			ChannelID:   channelId,
			TaskID:      s.TaskID,
			SchemaNameS: s.SchemaNameS,
			TableNameS:  s.TableNameS,
			Detail:      constants.BuildProgressLog(progressLogType, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, migration started", s.SchemaNameS, s.TableNameS)),
			LogLevel:    log.LogInfo,
		})
		if errC != nil {
			return fmt.Errorf("create progress log detail record failed: %v", errC)
		}
		return nil
	})
}

// clearMigrationOldDataAndTruncateTableInSingleMode clear migration old data, include table_migration_details and table_migration_summaries record, truncate target table
func clearMigrationOldDataAndTruncateTableInSingleMode(ctx context.Context, taskInfo *task.Task, t *channel.ChannelSchemaTable, targetDB *mysql.MySQL) error {
	log.Infof("meta database data clear, cond task_id [%v] schema_name_s [%v] table_name_s [%v], delete table_migration_details and table_migration_summaries record, truncate target table [%v.%v], taskId:%d", t.TaskId, t.SchemaNameS, t.TableNameS, t.SchemaNameT, t.TableNameT, t.TaskId)
	errDel := models.Transaction(ctx, func(txnCtx context.Context) error {
		if err := models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetail(txnCtx, &migration.TableMigrationDetail{
			TaskID:      t.TaskId,
			SchemaNameS: t.SchemaNameS,
			TableNameS:  t.TableNameS,
		}); err != nil {
			return err
		}
		if err := models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummary(txnCtx, &migration.TableMigrationSummary{
			TaskID:      t.TaskId,
			SchemaNameS: t.SchemaNameS,
			TableNameS:  t.TableNameS,
		}); err != nil {
			return err
		}
		if err := models.GetTaskReaderWriter().ResetTaskScnNumber(txnCtx, t.TaskId); err != nil {
			return err
		}

		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			if err := models.GetFullDataMigrationReaderWriter().DeleteCSVStage(txnCtx, t.TaskId, t.SchemaNameS, t.TableNameS); err != nil {
				return err
			}
			if err := models.GetFullDataMigrationReaderWriter().DeleteLightningProgressByTaskId(txnCtx, t.TaskId); err != nil {
				return err
			}
		}
		if err := models.GetFullDataMigrationReaderWriter().TruncateTargetSchemaTable(txnCtx, targetDB.MySQLDB, t.SchemaNameT, t.TableNameT); err != nil {
			if strings.Contains(err.Error(), "Error 1146") {
				log.Errorf("table [%s.%s], target table not exists, skip truncate", t.SchemaNameT, t.TableNameT)
			} else {
				return err
			}
		}
		return nil
	})
	if errDel != nil {
		return errDel
	}
	return nil
}

// clearMigrationOldDataAndTruncateTableInClusterMode clear migration old data, include table_migration_details and table_migration_summaries record, truncate target table
func clearMigrationOldDataAndTruncateTableInClusterMode(ctx context.Context, taskInfo *task.Task, t *channel.ChannelSchemaTable, targetDB *mysql.MySQL) error {
	log.Infof("meta database data clear, cond task_id [%v] schema_name_s [%v] table_name_s [%v], delete table_migration_details and table_migration_summaries record, truncate target table [%v.%v]",
		t.TaskId, t.SchemaNameS, t.TableNameS, t.SchemaNameT, t.TableNameT)

	errDel := models.Transaction(ctx, func(txnCtx context.Context) error {
		subTaskIds, getSubTaskErr := models.GetTaskReaderWriter().GetSubTaskIds(txnCtx, t.ChannelId, t.TaskId)
		if getSubTaskErr != nil {
			return getSubTaskErr
		}
		if err := models.GetFullDataMigrationReaderWriter().DeleteTableMigrationDetailByTaskIdsAndSchemaTable(txnCtx, subTaskIds, t.SchemaNameS, t.TableNameS); err != nil {
			return err
		}
		if err := models.GetFullDataMigrationReaderWriter().DeleteTableMigrationSummaryByTaskIdsAndSchemaTable(txnCtx, subTaskIds, t.SchemaNameS, t.TableNameS); err != nil {
			return err
		}

		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			if err := models.GetFullDataMigrationReaderWriter().DeleteCSVStage(txnCtx, t.TaskId, t.SchemaNameS, t.TableNameS); err != nil {
				return err
			}
			if err := models.GetFullDataMigrationReaderWriter().DeleteLightningProgressByTaskId(txnCtx, t.TaskId); err != nil {
				return err
			}
		}

		if err := models.GetFullDataMigrationReaderWriter().TruncateTargetSchemaTable(txnCtx, targetDB.MySQLDB, t.SchemaNameT, t.TableNameT); err != nil {
			if strings.Contains(err.Error(), "Error 1146") {
				log.Errorf("table [%s.%s], target table not exists, skip truncate", t.SchemaNameT, t.TableNameT)
			} else {
				return err
			}
		}
		return nil
	})
	if errDel != nil {
		return errDel
	}
	return nil
}

func buildDefaultMigrationSummaryWithChunkTotalNum(taskInfo *task.Task, sourceDS *datasource.Datasource, channelSchemaTable *channel.ChannelSchemaTable, chunkTotalNums, chunkSize int64, tableTypeS string) *migration.TableMigrationSummary {
	return &migration.TableMigrationSummary{
		TaskID:           taskInfo.TaskID,
		ServiceNameS:     sourceDS.ServiceName,
		DBNameS:          channelSchemaTable.DbNameS,
		SchemaNameS:      channelSchemaTable.SchemaNameS,
		TableNameS:       channelSchemaTable.TableNameS,
		TableTypeS:       tableTypeS,
		SchemaNameT:      channelSchemaTable.SchemaNameT,
		TableNameT:       channelSchemaTable.TableNameT,
		TaskStatus:       transfercommon.TaskStatusWaiting,
		ChunkTotalNums:   chunkTotalNums,
		ChunkSuccessNums: 0,
		ChunkFailedNums:  0,
		ChunkSize:        chunkSize,
	}
}

func buildMigrationSummary(taskInfo *task.Task, sourceDS *datasource.Datasource, channelSchemaTable *channel.ChannelSchemaTable, chunkSize int64, tableTypeS string) *migration.TableMigrationSummary {
	return buildDefaultMigrationSummaryWithChunkTotalNum(taskInfo, sourceDS, channelSchemaTable, 1, chunkSize, tableTypeS)
}

func buildSubTaskMigrationSummaries(subTasks []*task.Task, sourceDS *datasource.Datasource, channelSchemaTable *channel.ChannelSchemaTable, subTaskChunkNumMap map[int]int64, chunkSize int64) []*migration.TableMigrationSummary {
	return lo.Map(subTasks, func(subTask *task.Task, _ int) *migration.TableMigrationSummary {
		return &migration.TableMigrationSummary{
			TaskID:           subTask.TaskID,
			ServiceNameS:     sourceDS.ServiceName,
			DBNameS:          channelSchemaTable.DbNameS,
			SchemaNameS:      channelSchemaTable.SchemaNameS,
			TableNameS:       channelSchemaTable.TableNameS,
			SchemaNameT:      channelSchemaTable.SchemaNameT,
			TableNameT:       channelSchemaTable.TableNameT,
			TaskStatus:       transfercommon.TaskStatusWaiting,
			ChunkTotalNums:   subTaskChunkNumMap[subTask.TaskID],
			ChunkSuccessNums: 0,
			ChunkFailedNums:  0,
			ChunkSize:        chunkSize,
		}
	})
}

func buildSubTaskCSVStages(subTasks []*task.Task, table *channel.ChannelSchemaTable) []*migration.CSVStage {
	return lo.Map(subTasks, func(subTask *task.Task, _ int) *migration.CSVStage {
		return &migration.CSVStage{
			ChannelId:    subTask.ChannelId,
			TaskId:       subTask.TaskID,
			ParentTaskId: subTask.ParentTaskID,
			SchemaNameS:  table.SchemaNameS,
			TableNameS:   table.TableNameS,
			ExportToCSV:  false,
			ImportToDB:   false,
		}
	})
}

func buildSubTaskChannelSchemaTables(subTasks []*task.Task, item *channel.ChannelSchemaTable) []*channel.ChannelSchemaTable {
	return lo.Map(subTasks, func(subTask *task.Task, _ int) *channel.ChannelSchemaTable {
		return buildSubTaskChannelSchemaTable(subTask, item)
	})
}

func buildSubTaskChannelSchemaTable(subTask *task.Task, item *channel.ChannelSchemaTable) *channel.ChannelSchemaTable {
	return &channel.ChannelSchemaTable{
		ChannelId:             item.ChannelId,
		TaskId:                subTask.TaskID,
		TaskType:              item.TaskType,
		DbNameS:               item.DbNameS,
		SchemaNameS:           item.SchemaNameS,
		TableNameS:            item.TableNameS,
		PartitioningTypeS:     item.PartitioningTypeS,
		PartitioningCountS:    item.PartitioningCountS,
		SubPartitioningTypeS:  item.SubPartitioningTypeS,
		SubPartitioningCountS: item.SubPartitioningCountS,
		UkS:                   item.UkS,
		PkS:                   item.PkS,
		PkT:                   item.PkT,
		DbNameT:               item.DbNameT,
		SchemaNameT:           item.SchemaNameT,
		TableNameT:            item.TableNameT,
		PartitioningTypeT:     item.PartitioningTypeT,
		ClusterTypeT:          item.ClusterTypeT,
		Entity:                &common.Entity{Comment: item.Comment},
	}
}

func buildSubTaskChannelSchemaObjects(subTasks []*task.Task, item *channel.ChannelSchemaObject) []*channel.ChannelSchemaObject {
	return lo.Map(subTasks, func(subTask *task.Task, _ int) *channel.ChannelSchemaObject {
		return buildSubTaskChannelSchemaObject(subTask, item)
	})
}

func buildSubTaskChannelSchemaObject(subTask *task.Task, item *channel.ChannelSchemaObject) *channel.ChannelSchemaObject {
	return &channel.ChannelSchemaObject{
		TaskId:            subTask.TaskID,
		ChannelId:         item.ChannelId,
		OnlyTableandindex: item.OnlyTableandindex,
		OnlyTable:         item.OnlyTable,
		OnlyIndex:         item.OnlyIndex,
		Partitiontable:    item.Partitiontable,
		AppendData:        item.AppendData,
		ReloadData:        item.ReloadData,
		LoadData:          item.LoadData,
		Entity:            &common.Entity{Comment: item.Comment},
	}
}

// buildSubTasks 根据父任务构建子任务
func buildSubTasks(parentTask *task.Task, table *channel.ChannelSchemaTable) *task.Task {
	return buildSubTasksWithTaskName(parentTask, parentTask.TaskName+"_"+table.TableNameT+"_"+"0")
}

// buildSubTasks 根据父任务构建子任务
func buildSubTasksWithTaskName(parentTask *task.Task, taskName string) *task.Task {
	return &task.Task{
		TaskType:               parentTask.TaskType,
		TaskName:               taskName,
		StartTime:              timeutil.GetTMSNullTime(),
		EndTime:                timeutil.GetTMSNullTime(),
		TaskStatus:             constants.TASK_STATUS_NOT_RUNNING,
		ServerId:               "", // 置空
		TaskSeq:                0,
		RunParams:              "", // 置空，防止意外
		ChannelId:              parentTask.ChannelId,
		TaskParamTmplateId:     parentTask.TaskParamTmplateId,
		TabcolmapTmplateId:     parentTask.TabcolmapTmplateId,
		ColdefaultmapTmplateId: parentTask.ColdefaultmapTmplateId,
		ObjmapTmplateId:        parentTask.ObjmapTmplateId,
		SqlmapTmplateId:        parentTask.SqlmapTmplateId,
		ErrorDetail:            "",
		TaskObjRef:             parentTask.TaskObjRef,
		TaskReftask:            parentTask.TaskReftask,
		TaskWarning:            parentTask.TaskWarning,
		OnlyIncompatibleDetail: parentTask.OnlyIncompatibleDetail,
		ParentTaskID:           parentTask.TaskID,
		Entity:                 &common.Entity{Comment: parentTask.Comment},
	}
}

func BuildGetColumnSQL(migrationDetail *migration.TableMigrationDetail) string {
	getColumnsSQL := tmso2t.StringsBuilder(
		`SELECT *`,
		` FROM "`,
		migrationDetail.SchemaNameS,
		`"."`,
		migrationDetail.TableNameS,
		`" WHERE ROWNUM = 1`)
	return getColumnsSQL
}

func UpdateTaskToFailedIfContainsFailedDetailsOrSummaries(ctx context.Context, taskId int) (bool, error) {

	failedStatusList := []string{transfercommon.TaskStatusFailed, constants.MigrationStatusInvalid}
	failedImportStatusList := []string{ChunkStatusFailed}

	// 判断表任务是否存在 FAILED
	failedSummaries, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryByStatus(ctx, taskId, failedStatusList)
	if err != nil {
		log.Errorf("get failed summaries failed, taskId:%d, err:%v", taskId, err)
		return false, err
	}
	failedDetails, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByStatus(ctx, taskId, failedStatusList, failedImportStatusList)
	if err != nil {
		log.Errorf("get failed details failed, taskId:%d, err:%v", taskId, err)
		return false, err
	}

	if len(failedDetails) == 0 && len(failedSummaries) == 0 {
		return false, nil
	}

	var (
		latestErrorDetail string
		latestImportError string
		errorMessage      string
	)

	sort.Slice(failedDetails, func(i, j int) bool {
		return failedDetails[i].ID < failedDetails[j].ID
	})
	for _, failedDetail := range failedDetails {
		latestErrorDetail = failedDetail.ErrorDetail
		latestImportError = failedDetail.ImportError
	}

	errorMessage = "task execution failed"
	if latestErrorDetail == "" && latestImportError == "" {
		errorMessage = errorMessage + ", but no error message was returned"
	}
	if latestErrorDetail != "" {
		errorMessage = errorMessage + ", chunkErr:" + latestErrorDetail
	}
	if latestImportError != "" {
		errorMessage = errorMessage + ", importErr:" + latestImportError
	}

	if _, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
		EndTime:     time.Now(),
		TaskStatus:  constants.TASK_STATUS_FAILED,
		ErrorDetail: fmt.Sprintf("[error][%v][%s]: %s", timeutil.GetNowTime().Format("2006-01-02T15:04:05-07:00"), constants.MigrationLogTypeCSV, errorMessage),
	}); updateErr != nil {
		log.Errorf("task execution failed, contains failed details or summaries, update taskId [%v] status [%v] failed: %v", taskId, constants.TASK_STATUS_FAILED, updateErr)
	}
	return true, nil
}

// validate if runParam has prefix [summary:,detail:]
func validateMigrationTaskRunParam(param string) error {
	if strings.HasPrefix(param, "summary:") || strings.HasPrefix(param, "detail:") {
		return nil
	}
	return fmt.Errorf("run param [%s] is not supported", param)
}

func parseTaskRunParamAndGetDetails(ctx context.Context, taskInfo *task.Task) ([]migration.TableMigrationDetail, error) {
	detailIdStrs := strings.Split(taskInfo.RunParams[len("detail:"):], ",")
	detailIDs := make([]uint, 0)
	for _, detailIDStr := range detailIdStrs {
		detailID, err := parse.ParseInt(detailIDStr)
		if err != nil {
			log.Errorf("parse runparams for detail id failed, taskId:%d, runparam:%s, err:%v", taskInfo.TaskID, taskInfo.RunParams, err)
			return nil, err
		}
		detailIDs = append(detailIDs, uint(detailID))
	}

	details, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByIds(ctx, detailIDs)
	if err != nil {
		log.Errorf("get table migration details failed, taskId:%d, detailIDs:%v, err:%v", taskInfo.TaskID, detailIDs, err)
		return nil, err
	}
	return details, nil
}

func parseTaskRunParamAndGetSummaries(ctx context.Context, taskInfo *task.Task) ([]*migration.TableMigrationSummary, error) {
	summaryIdStrs := strings.Split(taskInfo.RunParams[len("summary:"):], ",")
	summaryIDs := make([]uint, 0)
	for _, summaryIdStr := range summaryIdStrs {
		summaryId, err := parse.ParseInt(summaryIdStr)
		if err != nil {
			log.Errorf("parse runparams for summary id failed, taskId:%d, runparam:%s, err:%v", taskInfo.TaskID, taskInfo.RunParams, err)
			return nil, err
		}
		summaryIDs = append(summaryIDs, uint(summaryId))
	}

	summaries, getErr := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationSummaryBySummaryIds(ctx, summaryIDs)
	if getErr != nil {
		log.Errorf("get table migration summary failed, taskId:%d, summaryIDs:%v, err:%v", taskInfo.TaskID, summaryIDs, getErr)
		return nil, getErr
	}
	return summaries, nil
}

// doDataMigration 执行真正的数据迁移任务
func doDataMigration(ctx context.Context, migrateFunc TransferDBMigrateFunc, channelId int, migrationDetail *migration.TableMigrationDetail, migrator migrate.Migrator, progressHelper *ProgressHelper, progressLogType string) error {
	log.Infof("start migrate table [%s.%s], task [%d], chunk detail [%d][%s], update detail running status",
		migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.TaskID,
		migrationDetail.ID, migrationDetail.ChunkDetailS)

	progressHelper.IncreaseRunningNum()
	defer progressHelper.DecreaseRunningNum()
	startMigrateTime := time.Now()

	updateErr := updateDetailToRunningAndCreateProgressLog(ctx, migrationDetail, startMigrateTime, channelId, progressLogType)
	if updateErr != nil {
		progressHelper.IncreaseFailedNum()
		return updateErr
	}

	// 执行数据迁移
	log.Infof("start migrate table [%s.%s], task [%d], chunk detail [%d][%s], migrating...", migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.TaskID, migrationDetail.ID, migrationDetail.ChunkDetailS)
	migrateErr := migrateFunc(migrator)

	if migrateErr != nil {
		errSQL, errMsg := extractOriginSQLAndFillChunkErrorMessage(migrateErr, migrationDetail)
		progressHelper.IncreaseFailedNum()

		log.Errorf("migrate table failed, table [%s.%s], task [%d], chunk detail id [%d], errMsg: %v",
			migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.TaskID, migrationDetail.ID, migrateErr)

		trxErr := createFailedLogAndUpdateDetailStatusInTransaction(ctx, channelId, migrationDetail, errSQL, errMsg, startMigrateTime, progressLogType)
		if trxErr != nil {
			log.Errorf("migrate failed, chunk %d, create progress log or update detail status failed, err:%v", migrationDetail.ID, trxErr)
			return trxErr
		}

		return fmt.Errorf("migrate table failed, table [%s.%s], chunk %d, err: %v", migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.ID, migrateErr)
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		updateDetailErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetail(ctx, migrationDetail.ID,
			map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusSuccess,
				"Duration":    time.Now().Sub(startMigrateTime).Seconds(),
			})
		if updateDetailErr != nil {
			log.Errorf("migrate success, chunk %d, update oracle schema table detail [%v] to success status failed: %v", migrationDetail.ID, migrationDetail.String(), updateDetailErr)
			return updateDetailErr
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("migrate success, chunk %d, create progress log or update detail status failed, err:%v", migrationDetail.ID, trxErr)
		return trxErr
	}
	progressHelper.IncreaseFinishNum()

	return nil
}

// doDataMigration 执行真正的数据迁移任务
func doDataMigrationWithStmt(ctx context.Context, migrateFunc TransferDBMigrateFunc, channelId int, migrationDetail *migration.TableMigrationDetail, migrator migrate.Migrator, progressHelper *ProgressHelper, progressLogType string, stmt *sql.Stmt, prepareSQL string) func() error {
	return func() error {
		defer stmt.Close()
		log.Infof("start migrate table, update detail running status, taskId:%d, schema:%s, table:%s, chunkId:%d, chunk:%s",
			migrationDetail.TaskID, migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.ID, migrationDetail.ChunkDetailS)

		progressHelper.IncreaseRunningNum()
		defer progressHelper.DecreaseRunningNum()
		startMigrateTime := time.Now()

		updateErr := updateDetailToRunningAndCreateProgressLog(ctx, migrationDetail, startMigrateTime, channelId, progressLogType)
		if updateErr != nil {
			progressHelper.IncreaseFailedNum()
			return updateErr
		}

		// 执行数据迁移
		log.Infof("start migrate table [%s.%s], task [%d], chunk detail [%d][%s], migrating...", migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.TaskID, migrationDetail.ID, migrationDetail.ChunkDetailS)
		migrateErr := migrateFunc(migrator)

		if migrateErr != nil {
			_, errMsg := extractOriginSQLAndFillChunkErrorMessage(migrateErr, migrationDetail)
			progressHelper.IncreaseFailedNum()

			log.Errorf("migrate table failed, table [%s.%s], task [%d], chunk detail id [%d], errMsg: %v",
				migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.TaskID, migrationDetail.ID, migrateErr)

			createErr := createFailedLogAndUpdateDetailStatusInTransaction(ctx, channelId, migrationDetail, prepareSQL, errMsg, startMigrateTime, progressLogType)
			if createErr != nil {
				log.Errorf("migrate failed, chunk %d, create progress log or update detail status failed, err:%v", migrationDetail.ID, createErr)
				return createErr
			}

			return fmt.Errorf("migrate table failed, table [%s.%s], chunk %d, err: %v", migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.ID, migrateErr)
		}

		trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
			errf := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetail(ctx, migrationDetail.ID,
				map[string]interface{}{
					"Task_Status": transfercommon.TaskStatusSuccess,
					"Duration":    time.Now().Sub(startMigrateTime).Seconds(),
				})
			if errf != nil {
				log.Errorf("migrate success, chunk %d, update oracle schema table detail [%v] to success status failed: %v", migrationDetail.ID, migrationDetail.String(), errf)
				return errf
			}
			return nil
		})
		if trxErr != nil {
			log.Errorf("migrate success, chunk %d, create progress log or update detail status failed, err:%v", migrationDetail.ID, trxErr)
			return trxErr
		}
		progressHelper.IncreaseFinishNum()

		return nil
	}
}

func SetMigrationChunkFailed(ctx context.Context, channelId, taskId int, migrationDetail *migration.TableMigrationDetail, errMsg string, progressLogType string) error {
	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		errU := models.GetFullDataMigrationReaderWriter().SetTableMigrationDetailFailed(transactionCtx, migrationDetail.ID, errMsg)
		if errU != nil {
			return fmt.Errorf("update oracle schema table [%v] chunk status failed: %v", migrationDetail.String(), errU)
		}
		_, errC := models.GetProgressLogReaderWriter().CreateProgressLogDetail(transactionCtx, &common.ProgressLogDetail{
			ChannelID:   channelId,
			TaskID:      taskId,
			SchemaNameS: migrationDetail.SchemaNameS,
			TableNameS:  migrationDetail.TableNameS,
			Detail:      constants.BuildProgressLog(progressLogType, constants.MigrationStepMigrateTableFinish, fmt.Sprintf("%s.%s table, chunk %d migration failed", migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.ID)),
			LogLevel:    log.LogError,
		})
		if errC != nil {
			return fmt.Errorf("create progress log detail failed: %v", errC)
		}
		return nil
	})
	return trxErr
}

func createTableChunkMigrateFailedLog(ctx context.Context, channelId, taskId int, migrationDetail *migration.TableMigrationDetail, progressLogType string) error {
	_, errC := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &common.ProgressLogDetail{
		ChannelID:   channelId,
		TaskID:      taskId,
		SchemaNameS: migrationDetail.SchemaNameS,
		TableNameS:  migrationDetail.TableNameS,
		Detail:      constants.BuildProgressLog(progressLogType, constants.MigrationStepMigrateTableChunkBegin, fmt.Sprintf("%s.%s table, chunk %d migration failed", migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.ID)),
		LogLevel:    log.LogInfo,
	})
	if errC != nil {
		return fmt.Errorf("create progress log detail failed: %v", errC)
	}
	return nil
}

func extractOriginSQLAndFillChunkErrorMessage(migrateErr error, chunk *migration.TableMigrationDetail) (string, string) {
	var (
		originSQL string
		errMsg    string
	)
	// migrateErr格式 -> "target sql [%v] execute failed: %v", querySql, err
	if errSQLRegex.MatchString(migrateErr.Error()) {
		originSQL = errSQLRegex.FindStringSubmatch(migrateErr.Error())[0]
		errMsg = errSQLRegex.ReplaceAllString(migrateErr.Error(), "sql execute")
	} else {
		errMsg = migrateErr.Error()
	}
	errMsg = fmt.Sprintf("[Error]:%s, Current ChunkID:%d, Row ID Range:%s", errMsg, chunk.ID, chunk.ChunkDetailS)
	return originSQL, errMsg
}

func createFailedLogAndUpdateDetailStatusInTransaction(ctx context.Context, channelId int, migrationDetail *migration.TableMigrationDetail, originSQL string, errMsg string, startMigrateTime time.Time, progressLogType string) error {
	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		createErr := createTableChunkMigrateFailedLog(transactionCtx, channelId, migrationDetail.TaskID, migrationDetail, progressLogType)
		if createErr != nil {
			log.Errorf("migrate failed, create progress log failed, table [%s.%s], task [%d], chunk detail id [%d], err: %v",
				migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.TaskID, migrationDetail.ID, createErr)
			return createErr
		}
		md5SqlStr := stringUtil.MD5(originSQL)
		log.Warnf("migrate database data chunk failed, taskId [%d], SchemaNameS [%s], TableNameS  [%s], chunk [%v], sql hash [%v], origin sql [%v], error [%v]", migrationDetail.TaskID, migrationDetail.SchemaNameS, migrationDetail.TableNameS, migrationDetail.ChunkDetailS, md5SqlStr, originSQL, errMsg)
		updateErr := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetail(transactionCtx, migrationDetail.ID,
			map[string]interface{}{
				"Task_Status":     transfercommon.TaskStatusFailed,
				"Origin_SQL_Hash": md5SqlStr,
				"Error_Detail":    errMsg,
				"Duration":        time.Now().Sub(startMigrateTime).Seconds(),
			})
		if updateErr != nil {
			log.Errorf("migrate failed, update oracle schema table detail [%v] to failed status failed, taskId:%d, err:%v", migrationDetail.String(), migrationDetail.TaskID, updateErr)
			return updateErr
		}

		return nil
	})
	return trxErr
}

func updateDetailToRunningAndCreateProgressLog(ctx context.Context, migrationDetail *migration.TableMigrationDetail, chunkTime time.Time, channelId int, progressLogType string) error {
	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationDetail(transactionCtx, migrationDetail.ID,
			map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusRunning,
				"Duration":    time.Now().Sub(chunkTime).Seconds(),
			})
		if errU != nil {
			return fmt.Errorf("update oracle schema table [%v] chunk status failed: %v", migrationDetail.String(), errU)
		}
		return nil
	})
	return trxErr
}

func buildFullDataTableMigrationDetail(taskInfo *task.Task, t *channel.ChannelSchemaTable, sourceDS *datasource.Datasource, sourceColumnInfo string, sourceSqlHint string, targetSqlHint string, whereRange string) *migration.TableMigrationDetail {
	return &migration.TableMigrationDetail{
		TaskID:        taskInfo.TaskID,
		ServiceNameS:  sourceDS.ServiceName,
		DBNameS:       t.DbNameS,
		SchemaNameS:   t.SchemaNameS,
		TableNameS:    t.TableNameS,
		SchemaNameT:   t.SchemaNameT,
		TableNameT:    t.TableNameT,
		ColumnDetailS: sourceColumnInfo,
		SqlHintS:      sourceSqlHint,
		SqlHintT:      targetSqlHint,
		ChunkDetailS:  whereRange,
		TaskStatus:    transfercommon.TaskStatusWaiting,
	}
}

func saveNotExistTablesToSummaryAndChunks(ctx context.Context, logGroup string, taskInfo *task.Task, oracleNotExistTables []*channel.ChannelSchemaTable, tidbNotExistTables []*channel.ChannelSchemaTable, dbConns *MigrationDBConns, chunkSize int64, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig) error {
	totalLen := len(oracleNotExistTables) + len(tidbNotExistTables)
	if totalLen == 0 {
		return nil
	}
	summaries := make([]*migration.TableMigrationSummary, 0)
	chunks := make([]*migration.TableMigrationDetail, 0)
	dcLogs := make([]*task.TaskLogDetail, 0, totalLen)
	csvStages := make([]*migration.CSVStage, 0)

	for _, notExistTable := range oracleNotExistTables {
		customConf := getCustomMigrationConf(taskTableConfigMap, notExistTable, chunkSize, dbConns.GetSourceDS())
		whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())

		summary := buildMigrationSummary(taskInfo, dbConns.GetSourceDS(), notExistTable, customConf.GetChunkSize(), "")
		summary.TaskStatus = constants.MigrationStatusInvalid

		detail := buildCSVMigrationDetail("0", taskInfo, notExistTable, dbConns.GetSourceDS(), "", customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)
		detail.ErrorDetail = fmt.Sprintf("table [%s.%s] not exist in oracle.", notExistTable.SchemaNameS, notExistTable.TableNameS)
		detail.TaskStatus = constants.MigrationStatusInvalid

		csvStage := buildCSVStage(taskInfo, notExistTable)

		dcLog := &task.TaskLogDetail{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			LogGroup:   logGroup,
			LogTime:    time.Now(),
			LogLevel:   log.LogError,
			LogMessage: fmt.Sprintf("table [%s.%s] not exist in oracle.", notExistTable.SchemaNameS, notExistTable.TableNameS),
		}

		summaries = append(summaries, summary)
		chunks = append(chunks, detail)
		csvStages = append(csvStages, csvStage)
		dcLogs = append(dcLogs, dcLog)
	}

	for _, notExistTable := range tidbNotExistTables {
		customConf := getCustomMigrationConf(taskTableConfigMap, notExistTable, chunkSize, dbConns.GetSourceDS())
		whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())

		summary := buildMigrationSummary(taskInfo, dbConns.GetTargetDS(), notExistTable, customConf.GetChunkSize(), "")
		summary.TaskStatus = constants.MigrationStatusInvalid

		detail := buildCSVMigrationDetail("0", taskInfo, notExistTable, dbConns.GetTargetDS(), "", customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)
		detail.ErrorDetail = fmt.Sprintf("table [%s.%s] not exist in tidb.", notExistTable.SchemaNameT, notExistTable.TableNameT)
		detail.TaskStatus = constants.MigrationStatusInvalid

		csvStage := buildCSVStage(taskInfo, notExistTable)

		dcLog := &task.TaskLogDetail{
			ChannelId:  taskInfo.ChannelId,
			TaskId:     taskInfo.TaskID,
			LogGroup:   logGroup,
			LogTime:    time.Now(),
			LogLevel:   log.LogError,
			LogMessage: fmt.Sprintf("table [%s.%s] not exist in tidb.", notExistTable.SchemaNameT, notExistTable.TableNameT),
		}

		summaries = append(summaries, summary)
		chunks = append(chunks, detail)
		csvStages = append(csvStages, csvStage)
		dcLogs = append(dcLogs, dcLog)
	}

	trxErr := models.Transaction(ctx, func(transactionCtx context.Context) error {
		if taskInfo.TaskType == constants.TASK_TYPE_MIGRATE_CSV_DATA {
			if err := models.GetFullDataMigrationReaderWriter().BatchSaveCSVStage(transactionCtx, csvStages); err != nil {
				log.Errorf("batch save csv stage failed, err:%v", err)
				return err
			}
		}
		if err := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationSummary(transactionCtx, summaries, 20); err != nil {
			log.Errorf("batch create table migration summary failed, err:%v", err)
			return err
		}
		if _, err := models.GetFullDataMigrationReaderWriter().BatchCreateTableMigrationDetail(transactionCtx, chunks, 20); err != nil {
			log.Errorf("batch create table migration detail failed, err:%v", err)
			return err
		}
		if err := models.GetTaskReaderWriter().BatchCreateTaskLogDetail(transactionCtx, dcLogs); err != nil {
			log.Errorf("batch create task log detail failed, err:%v", err)
			return err
		}
		return nil
	})
	if trxErr != nil {
		log.Errorf("save not exist tables to summary and chunks failed, taskId:%d, err:%v", taskInfo.TaskID, trxErr)
		return trxErr
	}
	return nil
}

// ValidateRetrySummary 检查重试任务表（Summary）数量是否正确，状态是否正确
func ValidateRetrySummary(ctx context.Context, taskId int, schemaNameS, tableNameS string) error {
	log.Infof("validate retry summary, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
	_, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		log.Errorf("get task info failed, taskId:%d, err: %v", taskId, err)
		return fmt.Errorf("get task info failed: %v", err)
	}
	summaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      taskId,
		SchemaNameS: schemaNameS,
		TableNameS:  tableNameS,
	})
	if err != nil {
		log.Errorf("get csv migration summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err: %v", taskId, schemaNameS, tableNameS, err)
		return err
	}
	if len(summaries) == 0 {
		err = errors.New("no csv migration summary match")
		log.Warnf("no csv migration summary match, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
		return err
	}
	if len(summaries) >= 2 {
		err = errors.New("more than one csv migration summary match")
		log.Warnf("more than one csv migration summary match, taskId:%d, schemaNameS:%s, tableNameS:%s", taskId, schemaNameS, tableNameS)
		return err
	}
	summary := summaries[0]
	if summary.TaskStatus == constants.MigrationStatusInvalid {
		return fmt.Errorf("table [%s.%s] or [%s.%s] may not exist or split chunks failed, please check again",
			summary.SchemaNameS, summary.TableNameS,
			summary.SchemaNameT, summary.TableNameT,
		)
	}
	if summary.TaskStatus == transfercommon.TaskStatusSuccess {
		return fmt.Errorf("table [%s.%s] has been migrated successfully, please check again",
			summary.SchemaNameS, summary.TableNameS,
		)
	}
	return nil
}

func createChunkGenerateErrorDetailAndSummary(ctx context.Context, generateChunkType GenerateChunkType, taskTableConfigMap map[structs.SchemaTablePair]*task.TaskTableConfig, channelSchemaTable *channel.ChannelSchemaTable, migrationDBConns *MigrationDBConns, taskInfo *task.Task, splitErr error, tableTypeS string) {
	customConf := getCustomMigrationConf(taskTableConfigMap, channelSchemaTable, NO_CHUNK_DEFAULT_CHUNK_SIZE, migrationDBConns.GetSourceDS())
	whereRange := buildSingleChunkWhereCondition(customConf.GetWherePrefix())

	summary := buildMigrationSummary(taskInfo, migrationDBConns.GetSourceDS(), channelSchemaTable, customConf.GetChunkSize(), tableTypeS)
	summary.TaskStatus = constants.MigrationStatusInvalid

	switch generateChunkType {
	case GenerateChunkTypeSplit:
		detail := buildCSVMigrationDetail("0", taskInfo, channelSchemaTable, migrationDBConns.GetSourceDS(), "", customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)
		detail.ErrorDetail = fmt.Sprintf("table [%s.%s] get columns or split chunk failed, err:%v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, splitErr)
		detail.TaskStatus = constants.MigrationStatusInvalid

		if _, err := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(ctx, summary); err != nil {
			log.Errorf("get columns or split data chunk failed, create table migration summary failed, err:%v", err)
		}
		if _, err := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(ctx, detail); err != nil {
			log.Errorf("get columns or split data chunk failed, create table migration detail failed, err:%v", err)
		}
	case GenerateChunkTypeFetch:
		detail := buildCSVMigrationDetail("0", taskInfo, channelSchemaTable, migrationDBConns.GetSourceDS(), "", customConf.GetSourceSqlHint(), customConf.GetTargetSqlHint(), whereRange)
		detail.ErrorDetail = fmt.Sprintf("table [%s.%s] get columns or fetch chunk failed, err:%v", channelSchemaTable.SchemaNameS, channelSchemaTable.TableNameS, splitErr)
		detail.TaskStatus = constants.MigrationStatusInvalid

		if _, err := models.GetFullDataMigrationReaderWriter().CreateTableMigrationSummary(ctx, summary); err != nil {
			log.Errorf("get columns or fetch data chunk failed, create table migration summary failed, err:%v", err)
		}
		if _, err := models.GetFullDataMigrationReaderWriter().CreateTableMigrationDetail(ctx, detail); err != nil {
			log.Errorf("get columns or fetch data chunk failed, create table migration detail failed, err:%v", err)
		}
	}
}

type TableMigrateCustomConf struct {
	chunkColumnDetail        string
	chunkColumnDetailChanged bool
	tablePartition           string
	tablePartitionChanged    bool
}

func (i TableMigrateCustomConf) ApplyToTableChunk(d *migration.TableMigrationDetail) {
	if i.chunkColumnDetailChanged {
		d.ColumnDetailS = i.chunkColumnDetail
	}
	if i.tablePartitionChanged {
		d.TablePartition = i.tablePartition
	}
	return
}

func GetTableMigrationSummary(ctx context.Context, taskId int, schemaNameS, tablaNameS string) (*migration.TableMigrationSummary, error) {
	summaries, err := models.GetFullDataMigrationReaderWriter().DetailTableMigrationSummary(ctx, &migration.TableMigrationSummary{
		TaskID:      taskId,
		SchemaNameS: schemaNameS,
		TableNameS:  tablaNameS,
	})
	if err != nil {
		return nil, err
	}
	if len(summaries) == 0 {
		err = errors.New("no csv migration summary match")
		return nil, err
	}
	if len(summaries) >= 2 {
		err = errors.New("more than one csv migration summary match")
		return nil, err
	}

	summary := summaries[0]
	return summary, nil
}

func (i *CSVMigrationTask) RetryCSVTask(ctx context.Context, summary *migration.TableMigrationSummary, csvStage *migration.CSVStage) error {

	log.Infof("retry csv migration failed chunk detail, task id:%d, schema name:%s, table name:%s", i.TaskId, summary.SchemaNameS, summary.TableNameS)

	taskId := i.TaskInfo.TaskID
	channelId := i.TaskInfo.ChannelId

	options := []ChunkMigrateParamOptFunc{
		WithChannelInfo(i.ChannelInfo),
		WithTaskInfo(i.TaskInfo),
		WithClearData(false), // 重试时不清理数据
		WithCSVMigrationConfigParam(i.CSVParam),
	}

	sourceDBType := i.DataSources.GetSourceDS().DbType
	otherOptions, buildOptErr := i.buildOptionsByDBType(ctx, sourceDBType)
	if buildOptErr != nil {
		log.Errorf("build options by db type failed, channelId: %d, taskId: %d, dbType: %s, err: %v", channelId, taskId, sourceDBType, buildOptErr)
		return buildOptErr
	}
	options = append(options, otherOptions...)
	chunkMigrateParam := i.buildChunkMigrateParam(options...)

	// FIXME 没有主键应该是客户要保证的问题，而不是工具自己处理
	//truncateErr := i.truncateTableIfNoPKFound(ctx, summary)
	//if truncateErr != nil {
	//	log.Errorf("truncate table failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summary.SchemaNameS, summary.TableNameS, truncateErr)
	//	return truncateErr
	//}

	if csvStage.ExportToCSV {
		logMessage := fmt.Sprintf("%s.%s table, csv export task has been completed, skip execution.", summary.SchemaNameS, summary.TableNameS)
		i.createSkipLog(ctx, logMessage)
	} else {
		logMessage := fmt.Sprintf("%s.%s table, prepare to retry csv export task", summary.SchemaNameS, summary.TableNameS)
		i.createBeginLog(ctx, logMessage)

		retryExportErr := i.ExportChunksByTable(ctx, summary, chunkMigrateParam)
		if retryExportErr != nil {
			log.Errorf("retry csv export failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summary.SchemaNameS, summary.TableNameS, retryExportErr)
			return retryExportErr
		}
	}

	if csvStage.ImportToDB {
		logMessage := fmt.Sprintf("%s.%s table, csv lightning import task has been completed, skip execution.", summary.SchemaNameS, summary.TableNameS)
		i.createSkipLog(ctx, logMessage)
	} else {
		var retryImportErr error
		logMessage := fmt.Sprintf("%s.%s table, prepare to start csv lightning import task, dbType: %s", summary.SchemaNameS, summary.TableNameS, sourceDBType)
		i.createBeginLog(ctx, logMessage)

		if sourceDBType == constants.DB_TYPE_CSV {
			retryImportErr = i.StepImportChunksByTable(ctx, summary, chunkMigrateParam)
		} else {
			retryImportErr = i.ImportChunksByTable(ctx, summary, chunkMigrateParam)
		}
		if retryImportErr != nil {
			log.Errorf("retry csv import failed, taskId:%d, schemaName:%s, tableName:%s, dbType: %s, err:%v", taskId, summary.SchemaNameS, summary.TableNameS, sourceDBType, retryImportErr)
			return retryImportErr
		}
	}
	return nil
}

func (i *CSVMigrationTask) createSkipLog(ctx context.Context, logMessage string) {
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &commonmodel.ProgressLogDetail{
		ChannelID: i.ChannelId,
		TaskID:    i.TaskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableSkip, logMessage),
		LogLevel:  log.LogWarn,
	})
}

//func (i *CSVMigrationTask) retryExportCSV(ctx context.Context, summary *migration.TableMigrationSummary, channelId int, taskId int, startExecTime time.Time) error {
//	// TODO 需要重构
//	retryErr := i.BatchRetryCSVMigrationByTableFailed(ctx, []*migration.TableMigrationSummary{summary})
//	if retryErr != nil {
//		log.Errorf("batch retry csv migration by table failed failed: %v", retryErr)
//		SetSummaryStatus(ctx, summary, transfercommon.TaskStatusFailed, startExecTime)
//		return retryErr
//	}
//
//	updateErr := models.GetFullDataMigrationReaderWriter().UpdateCSVStageBySchemaSTableS(ctx, channelId, taskId, summary.SchemaNameS, summary.TableNameS, map[string]interface{}{
//		"export_to_csv": true,
//		"error_message": "",
//	})
//	if updateErr != nil {
//		log.Errorf("update csv stage failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summary.SchemaNameS, summary.TableNameS, updateErr)
//		return updateErr
//	}
//	return nil
//}

func (i *CSVMigrationTask) createBeginLog(ctx context.Context, logMessage string) {
	_, _ = models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &commonmodel.ProgressLogDetail{
		ChannelID: i.ChannelId,
		TaskID:    i.TaskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, logMessage),
		LogLevel:  log.LogInfo,
	})
}

func (i *CSVMigrationTask) truncateTableIfNoPKFound(ctx context.Context, summary *migration.TableMigrationSummary) error {

	channelId, taskId := i.TaskInfo.ChannelId, i.TaskInfo.TaskID

	channelSchemaTable, err := models.GetChannelReaderWriter().GetChannelSchemaTableByChannelTaskSchemaSTableS(
		ctx, channelId, taskId, summary.SchemaNameS, summary.TableNameS)
	if err != nil {
		log.Errorf("get channel schema table failed, taskId:%d, schemaName:%s, tableName:%s, err:%v", taskId, summary.SchemaNameS, summary.TableNameS, err)
		return fmt.Errorf("get channel schema table failed: %v", err)
	}

	hasPrimaryUniqueKey := CheckTableHasPrimaryUniqueKey(channelSchemaTable)

	log.Infof("check %s.%s table has primary unique key, channelId: %d, taskId: %d, hasPrimaryUniqueKey: %v", summary.SchemaNameS, summary.TableNameS, channelId, taskId, hasPrimaryUniqueKey)
	if !hasPrimaryUniqueKey {
		log.Infof("setup database conns, channelId: %d, taskId: %d", channelId, taskId)
		targetDBConn, setUpErr := SetUpTargetDatabaseConn(ctx, i.ChannelInfo)
		if setUpErr != nil {
			log.Errorf("setup database conns failed, taskId:%d, err:%v", taskId, setUpErr)
			return setUpErr
		}

		// 没有PK/UK的表，truncate 数据
		log.Warnf("table %s.%s has no primary key or unique key, truncate table when retry", summary.SchemaNameT, summary.TableNameT)
		if err := models.GetFullDataMigrationReaderWriter().TruncateTargetSchemaTable(ctx, targetDBConn.MySQLDB, summary.SchemaNameT, summary.TableNameT); err != nil {
			log.Errorf("truncate table %s.%s failed, err:%v", summary.SchemaNameT, summary.TableNameT, err)
			return err
		}
		logMessage := fmt.Sprintf("%s.%s table has no primary/unique key, truncate before retry", summary.SchemaNameT, summary.TableNameT)
		i.createWarnLog(ctx, logMessage)
	}
	return nil
}

func (i *CSVMigrationTask) createWarnLog(ctx context.Context, logMessage string) {
	_, err := models.GetProgressLogReaderWriter().CreateProgressLogDetail(ctx, &commonmodel.ProgressLogDetail{
		ChannelID: i.ChannelId,
		TaskID:    i.TaskId,
		Detail:    constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepLightningImportBegin, logMessage),
		LogLevel:  log.LogWarn,
	})
	if err != nil {
		log.Errorf("data import step, create progress log detail record failed: %v", err)
	}
}

func (i *CSVMigrationTask) setCSVMigrationSummaryRunning(ctx context.Context, s *migration.TableMigrationSummary, isExport bool) error {
	return models.Transaction(ctx, func(txnCtx context.Context) error {
		errU := models.GetFullDataMigrationReaderWriter().UpdateTableMigrationSummary(txnCtx,
			&migration.TableMigrationSummary{
				TaskID:      i.TaskId,
				SchemaNameS: s.SchemaNameS,
				TableNameS:  s.TableNameS,
			}, map[string]interface{}{
				"Task_Status": transfercommon.TaskStatusRunning,
			})
		if errU != nil {
			return errU
		}

		var detail string
		if isExport {
			detail = constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, export data started", s.SchemaNameS, s.TableNameS))
		} else {
			detail = constants.BuildProgressLog(constants.MigrationLogTypeCSV, constants.MigrationStepMigrateTableBegin, fmt.Sprintf("%s.%s table, import data started", s.SchemaNameS, s.TableNameS))
		}

		_, errC := models.GetProgressLogReaderWriter().CreateProgressLogDetail(txnCtx, &common.ProgressLogDetail{
			ChannelID:   i.ChannelId,
			TaskID:      i.TaskId,
			SchemaNameS: s.SchemaNameS,
			TableNameS:  s.TableNameS,
			Detail:      detail,
			LogLevel:    log.LogInfo,
		})
		if errC != nil {
			return fmt.Errorf("create progress log detail record failed: %v", errC)
		}
		return nil
	})
}
