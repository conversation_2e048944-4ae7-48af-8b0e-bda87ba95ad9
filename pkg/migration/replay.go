package migration

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"strconv"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	transfercommon "gitee.com/pingcap_enterprise/tms/lib/transferdb/common"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/meta"
	tmso2t "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/o2t"
	sqlpublic "gitee.com/pingcap_enterprise/tms/lib/transferdb/module/migrate/sql/oracle/public"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/migration"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
)

// ChunkReplayConfig 重放配置
type ChunkReplayConfig struct {
	TaskID      int
	ChannelID   int
	SchemaNameS string
	TableNameS  string
	BatchSize   int
	ExecuteIDs  []uint
	ExecuteAll  bool
}

// ChunkReplayProgress 重放进度
type ChunkReplayProgress struct {
	Total      int64
	Success    int64
	Failed     int64
	Processing int64
	StartTime  time.Time
	EndTime    *time.Time
}

// ReplayProgressLogger 统一的进度日志管理器
type ReplayProgressLogger struct {
	requestID         string
	lastLogTime       time.Time
	logInterval       time.Duration
	mu                sync.RWMutex
	
	// 进度统计
	totalPages        int
	currentPage       int
	totalBatches      int
	currentBatch      int
	successfulBatches int
	failedBatches     int
}

// NewReplayProgressLogger 创建进度日志管理器
func NewReplayProgressLogger(requestID string) *ReplayProgressLogger {
	return &ReplayProgressLogger{
		requestID:   requestID,
		logInterval: 10 * time.Second,
		lastLogTime: time.Now(),
	}
}

// UpdatePageProgress 更新页面进度
func (l *ReplayProgressLogger) UpdatePageProgress(current, total int) {
	l.mu.Lock()
	l.currentPage = current
	l.totalPages = total
	l.mu.Unlock()
	
	l.tryLogProgress()
}

// UpdateBatchProgress 更新批次进度
func (l *ReplayProgressLogger) UpdateBatchProgress(current int) {
	l.mu.Lock()
	l.currentBatch = current
	if current > l.totalBatches {
		l.totalBatches = current
	}
	l.mu.Unlock()
	
	l.tryLogProgress()
}

// RecordBatchSuccess 记录批次成功
func (l *ReplayProgressLogger) RecordBatchSuccess(size int) {
	l.mu.Lock()
	l.successfulBatches++
	l.mu.Unlock()
	
	l.tryLogProgress()
}

// RecordBatchFailure 记录批次失败
func (l *ReplayProgressLogger) RecordBatchFailure() {
	l.mu.Lock()
	l.failedBatches++
	l.mu.Unlock()
	
	l.tryLogProgress()
}

// shouldLog 检查是否应该打印日志（内部使用，假设已持有锁）
func (l *ReplayProgressLogger) shouldLog() bool {
	return time.Since(l.lastLogTime) >= l.logInterval
}

// tryLogProgress 尝试打印进度日志
func (l *ReplayProgressLogger) tryLogProgress() {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	if l.shouldLog() {
		l.logProgressUnsafe()
		l.lastLogTime = time.Now()
	}
}

// ForceLogProgress 强制打印进度日志（用于重要节点）
func (l *ReplayProgressLogger) ForceLogProgress() {
	l.mu.Lock()
	defer l.mu.Unlock()
	
	l.logProgressUnsafe()
	l.lastLogTime = time.Now()
}

// logProgressUnsafe 打印进度日志（内部使用，假设已持有锁）
func (l *ReplayProgressLogger) logProgressUnsafe() {
	if l.totalPages > 0 && l.currentPage > 0 {
		// 有页面进度信息
		if l.totalBatches > 0 {
			log.Infof("[%s] Progress: Page %d/%d, Batch %d (Success: %d, Failed: %d)", 
				l.requestID, l.currentPage, l.totalPages, l.currentBatch, 
				l.successfulBatches, l.failedBatches)
		} else {
			log.Infof("[%s] Progress: Page %d/%d", l.requestID, l.currentPage, l.totalPages)
		}
	} else if l.totalBatches > 0 {
		// 只有批次进度信息
		log.Infof("[%s] Progress: Batch %d (Success: %d, Failed: %d)", 
			l.requestID, l.currentBatch, l.successfulBatches, l.failedBatches)
	}
}

// ChunkReplayOrchestrator 编排器，负责整体流程控制
type ChunkReplayOrchestrator struct {
	config         *ChunkReplayConfig
	dbConns        *MigrationDBConns
	executor       *ChunkReplayExecutor
	fetcher        *ChunkDataFetcher
	taskInfo       *task.Task
	channelInfo    *channel.ChannelInformation
	migrationParam *structs.FullMigrationConfigParam
	progressLogger *ReplayProgressLogger
}

// NewChunkReplayOrchestrator 创建编排器
func NewChunkReplayOrchestrator(config *ChunkReplayConfig) *ChunkReplayOrchestrator {
	return &ChunkReplayOrchestrator{
		config: config,
	}
}

// Initialize 初始化编排器
func (o *ChunkReplayOrchestrator) Initialize(ctx context.Context) error {
	// 获取任务信息
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, o.config.TaskID)
	if err != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}
	o.taskInfo = taskInfo

	// 获取任务模板配置
	taskParamTempConfig, err := models.GetTaskReaderWriter().GetTaskTemplate(ctx, taskInfo.TaskParamTmplateId)
	if err != nil {
		log.Errorf("get task template failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}

	// 构建迁移参数
	migrationParam, err := BuildMigrationConfigParams(ctx, o.config.ChannelID, o.config.TaskID, taskParamTempConfig.TaskparamTemplateID)
	if err != nil {
		log.Errorf("build migration config params failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}
	o.migrationParam = migrationParam

	// 获取通道信息
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, o.config.ChannelID)
	if err != nil {
		log.Errorf("get channel info failed, taskId:%d, channelId:%d, err:%v", o.config.TaskID, o.config.ChannelID, err)
		return err
	}
	o.channelInfo = channelInfo

	// 设置数据库连接
	dbConns, err := SetUpDatabaseConnsAndCollation(ctx, channelInfo, o.config.TaskID)
	if err != nil {
		log.Errorf("set up database conns failed, taskId:%d, err:%v", o.config.TaskID, err)
		return err
	}
	o.dbConns = dbConns

	// 初始化子组件
	o.fetcher = NewChunkDataFetcher(o.config)
	o.executor = NewChunkReplayExecutor(o.dbConns, o.migrationParam, o.taskInfo)

	return nil
}

// ValidateAndPrepare 验证并准备重放
func (o *ChunkReplayOrchestrator) ValidateAndPrepare(ctx context.Context) error {
	// 检查参数
	if !o.config.ExecuteAll && len(o.config.ExecuteIDs) == 0 {
		return errors.NewError(errors.TIMS_PARAMETER_INVALID, "executeIds and executeAll can't be empty at the same time")
	}

	// 获取并检查状态
	summary, err := models.GetFullDataMigrationReaderWriter().GetChunkDataAnalyzeSummary(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)
	if err != nil {
		log.Errorf("get chunk data analyze summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return err
	}

	if summary.Status == constants.ChunkDataStatusReplaying || summary.Status == constants.ChunkDataStatusFetching {
		return errors.NewError(errors.TIMS_PARAMETER_INVALID, "chunk data is replaying or fetching")
	}

	// 检查是否有失败的chunk
	details, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS,
		[]string{transfercommon.TaskStatusFailed})
	if err != nil {
		log.Errorf("get table migration detail failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return err
	}
	if len(details) == 0 {
		return errors.NewError(errors.TMS_MIGRATION_CHUNK_NOT_FOUND, "no failed chunks")
	}

	// 检查是否有数据需要重放
	count, err := models.GetFullDataMigrationReaderWriter().CountChunkDataAnalyze(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)
	if err != nil {
		log.Errorf("count chunk data analyze failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return err
	}
	if count == 0 {
		return errors.NewError(errors.TMS_MIGRATION_CHUNK_ANALYZE_NOT_FOUND, "no chunk analyze data")
	}

	return nil
}

// UpdateStatusToReplaying 更新状态为重放中
func (o *ChunkReplayOrchestrator) UpdateStatusToReplaying(ctx context.Context) error {
	return models.Transaction(ctx, func(transactionCtx context.Context) error {
		// 更新汇总状态
		err := models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeSummary(
			ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, constants.ChunkDataStatusReplaying)
		if err != nil {
			log.Errorf("update chunk data analyze summary failed, taskId:%d, schemaName:%s, tableName:%s, err:%v",
				o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
			return err
		}

		// 更新具体的chunk状态
		if len(o.config.ExecuteIDs) != 0 {
			err = models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeStatusByIds(
				ctx, o.config.ExecuteIDs, "RUNNING", "")
			if err != nil {
				log.Errorf("update chunk data analyze status by ids failed, taskId:%d, schemaNameS:%s, tableNameS:%s, executeIds:%v, err:%v",
					o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, o.config.ExecuteIDs, err)
				return err
			}
		} else {
			err = models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeStatus(
				ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, "RUNNING", "")
			if err != nil {
				log.Errorf("update chunk data analyze status by status failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
					o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
				return err
			}
		}
		return nil
	})
}

// ExecuteAsync 异步执行重放
func (o *ChunkReplayOrchestrator) ExecuteAsync(ctx context.Context) {
	// 使用独立的context避免被取消
	bgCtx := context.Background()

	go func() {
		requestID := fmt.Sprintf("replay_%d_%s_%s_%d", o.config.TaskID, o.config.SchemaNameS,
			o.config.TableNameS, time.Now().Unix())
		
		// 初始化进度日志管理器
		o.progressLogger = NewReplayProgressLogger(requestID)
		
		log.Infof("[%s] Starting chunk data replay, taskId:%d, schemaNameS:%s, tableNameS:%s",
			requestID, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS)

		startTime := time.Now()
		defer func() {
			// 更新状态为已完成
			err := models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeSummary(
				bgCtx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, constants.ChunkDataStatusReplayed)
			if err != nil {
				log.Errorf("[%s] update chunk data analyze summary failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
					requestID, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
			}

			duration := time.Since(startTime)
			log.Infof("[%s] Chunk data replay completed, taskId:%d, schemaNameS:%s, tableNameS:%s, duration:%v",
				requestID, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, duration)
		}()

		// 执行重放逻辑
		o.doReplay(bgCtx, requestID)
	}()
}

// doReplay 执行重放核心逻辑
func (o *ChunkReplayOrchestrator) doReplay(ctx context.Context, requestID string) {
	// 获取失败的chunk详情（用于获取列信息）
	details, err := models.GetFullDataMigrationReaderWriter().BatchGetTableMigrationDetailByTaskIdSchemaTableAndStatusList(
		ctx, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS,
		[]string{transfercommon.TaskStatusFailed})
	if err != nil || len(details) == 0 {
		log.Errorf("[%s] get table migration detail failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			requestID, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return
	}
	chunk := &details[0]

	// 获取列信息
	columnNameS, err := o.executor.GetTableColumns(ctx, chunk)
	if err != nil {
		log.Errorf("[%s] get table columns failed, taskId:%d, schemaNameS:%s, tableNameS:%s, err:%v",
			requestID, o.config.TaskID, o.config.SchemaNameS, o.config.TableNameS, err)
		return
	}

	// 创建数据获取通道
	chunkDataChan := make(chan []migration.ChunkDataAnalyze)

	// 启动数据获取
	go o.fetcher.FetchData(ctx, chunkDataChan, requestID, o.progressLogger)

	// 处理数据
	currentBatch := 1
	for chunkDataSlice := range chunkDataChan {
		// 更新批次进度
		o.progressLogger.UpdateBatchProgress(currentBatch)

		// 尝试批量执行
		success := o.executor.ExecuteBatch(ctx, chunkDataSlice, chunk, columnNameS, requestID, o.progressLogger)

		// 如果批量失败，逐条重试
		if !success {
			log.Warnf("[%s] Batch execution failed, retrying one by one", requestID)
			o.executor.ExecuteOneByOne(ctx, chunkDataSlice, chunk, columnNameS, requestID, o.progressLogger)
		}

		currentBatch++
	}
	
	// 强制打印最终进度
	o.progressLogger.ForceLogProgress()
}

// ChunkDataFetcher 数据获取器
type ChunkDataFetcher struct {
	config *ChunkReplayConfig
}

// NewChunkDataFetcher 创建数据获取器
func NewChunkDataFetcher(config *ChunkReplayConfig) *ChunkDataFetcher {
	return &ChunkDataFetcher{config: config}
}

// FetchData 获取数据
func (f *ChunkDataFetcher) FetchData(ctx context.Context, dataChan chan []migration.ChunkDataAnalyze, requestID string, progressLogger *ReplayProgressLogger) {
	defer close(dataChan)

	if len(f.config.ExecuteIDs) != 0 {
		// 按ID获取
		log.Infof("[%s] Fetching chunk data by ids, count:%d", requestID, len(f.config.ExecuteIDs))
		data, err := models.GetFullDataMigrationReaderWriter().FetchChunkDataAnalyzeByIds(
			ctx, f.config.TaskID, f.config.SchemaNameS, f.config.TableNameS, f.config.ExecuteIDs)
		if err != nil {
			log.Errorf("[%s] fetch chunk data analyze by ids failed, err:%v", requestID, err)
			return
		}
		if len(data) > 0 {
			dataChan <- data
		}
	} else {
		// 分页获取所有数据
		count, err := models.GetFullDataMigrationReaderWriter().CountChunkDataAnalyze(
			ctx, f.config.TaskID, f.config.SchemaNameS, f.config.TableNameS)
		if err != nil {
			log.Errorf("[%s] count chunk data analyze failed, err:%v", requestID, err)
			return
		}

		totalPages := (int(count) + f.config.BatchSize - 1) / f.config.BatchSize
		for i := 0; i < int(count); i += f.config.BatchSize {
			page := i/f.config.BatchSize + 1
			
			// 更新页面进度
			progressLogger.UpdatePageProgress(page, totalPages)

			data, _, err := models.GetFullDataMigrationReaderWriter().ListChunkDataAnalyzePage(
				ctx, f.config.TaskID, f.config.SchemaNameS, f.config.TableNameS, page, f.config.BatchSize)
			if err != nil {
				log.Errorf("[%s] list chunk data analyze page failed, page:%d, err:%v", requestID, page, err)
				return
			}
			if len(data) > 0 {
				dataChan <- data
			}
		}
	}
}

// ChunkReplayExecutor 执行器
type ChunkReplayExecutor struct {
	dbConns        *MigrationDBConns
	migrationParam *structs.FullMigrationConfigParam
	taskInfo       *task.Task
	stmtCache      map[string]*sql.Stmt
}

// NewChunkReplayExecutor 创建执行器
func NewChunkReplayExecutor(dbConns *MigrationDBConns, migrationParam *structs.FullMigrationConfigParam, taskInfo *task.Task) *ChunkReplayExecutor {
	return &ChunkReplayExecutor{
		dbConns:        dbConns,
		migrationParam: migrationParam,
		taskInfo:       taskInfo,
		stmtCache:      make(map[string]*sql.Stmt),
	}
}

// GetTableColumns 获取表列信息
func (e *ChunkReplayExecutor) GetTableColumns(ctx context.Context, chunk *migration.TableMigrationDetail) ([]string, error) {
	getColumnsSQL := BuildGetColumnSQL(chunk)
	columnNameS, err := e.dbConns.GetSourceDB().GetOracleTableRowsColumn(
		getColumnsSQL,
		transfercommon.MigrateOracleCharsetStringConvertMapping[e.dbConns.GetSourceDS().Charset],
		transfercommon.StringUPPER(e.dbConns.GetTargetDS().Charset))
	if err != nil {
		return nil, err
	}
	return columnNameS, nil
}

// ExecuteBatch 批量执行
func (e *ChunkReplayExecutor) ExecuteBatch(ctx context.Context, chunkDataSlice []migration.ChunkDataAnalyze,
	chunk *migration.TableMigrationDetail, columnNameS []string, requestID string, progressLogger *ReplayProgressLogger) bool {

	batchSize := len(chunkDataSlice)
	if batchSize == 0 {
		return true
	}

	// 收集ROWID和ID
	rowIDs := make([]string, 0, batchSize)
	chunkDataIDs := make([]uint, 0, batchSize)
	for _, chunkData := range chunkDataSlice {
		rowIDs = append(rowIDs, chunkData.RowIDStr)
		chunkDataIDs = append(chunkDataIDs, chunkData.ID)
	}

	// 构建批量请求（使用参数化查询）
	stmt, req, err := e.buildBatchReplayRequest(ctx, chunk, rowIDs, columnNameS, false)
	if err != nil {
		log.Errorf("[%s] build batch replay request failed, err:%v", requestID, err)
		progressLogger.RecordBatchFailure()
		return false
	}
	defer stmt.Close()

	// 执行批量迁移
	err = sqlpublic.IMigrate(req)
	if err != nil {
		log.Errorf("[%s] batch migration failed, size:%d, err:%v", requestID, batchSize, err)
		progressLogger.RecordBatchFailure()
		return false
	}

	// 更新状态为成功
	err = models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeStatusByIds(ctx, chunkDataIDs, "SUCCESS", "")
	if err != nil {
		log.Errorf("[%s] update chunk data analyze status failed, err:%v", requestID, err)
	}

	// 记录批次成功
	progressLogger.RecordBatchSuccess(batchSize)
	return true
}

// ExecuteOneByOne 逐条执行
func (e *ChunkReplayExecutor) ExecuteOneByOne(ctx context.Context, chunkDataSlice []migration.ChunkDataAnalyze,
	chunk *migration.TableMigrationDetail, columnNameS []string, requestID string, progressLogger *ReplayProgressLogger) {

	for _, chunkData := range chunkDataSlice {
		// 构建单条请求（使用参数化查询）
		stmt, req, err := e.buildSingleReplayRequest(ctx, chunk, chunkData.RowIDStr, columnNameS, false)
		if err != nil {
			log.Errorf("[%s] build single replay request failed, rowid:%s, err:%v",
				requestID, chunkData.RowIDStr, err)
			progressLogger.RecordBatchFailure()
			continue
		}

		// 执行迁移
		err = sqlpublic.IMigrate(req)
		stmt.Close() // 立即关闭，避免资源累积

		if err != nil {
			// 更新为失败状态
			updateErr := models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeStatusByIds(
				ctx, []uint{chunkData.ID}, "FAILED", err.Error())
			if updateErr != nil {
				log.Errorf("[%s] update chunk data status to failed error, id:%d, err:%v",
					requestID, chunkData.ID, updateErr)
			}
			progressLogger.RecordBatchFailure()
		} else {
			// 更新为成功状态
			updateErr := models.GetFullDataMigrationReaderWriter().UpdateChunkDataAnalyzeStatusByIds(
				ctx, []uint{chunkData.ID}, "SUCCESS", "")
			if updateErr != nil {
				log.Errorf("[%s] update chunk data status to success error, id:%d, err:%v",
					requestID, chunkData.ID, updateErr)
			}
			progressLogger.RecordBatchSuccess(1)
		}
	}
}

// buildBatchReplayRequest 构建批量重放请求
func (e *ChunkReplayExecutor) buildBatchReplayRequest(ctx context.Context, chunk *migration.TableMigrationDetail,
	rowIDs []string, columnNameS []string, isRetry bool) (*sql.Stmt, *tmso2t.Rows, error) {

	// 构建ROWID条件，使用绑定变量防止SQL注入
	// Oracle支持的绑定变量格式：ROWID IN (:1, :2, :3, ...)
	rowIDCondition := fmt.Sprintf(`ROWID IN ('%s')`, strings.Join(rowIDs, `','`))

	// 准备目标表的SQL语句
	sqlStr := tmso2t.GenMySQLTablePrepareStmt(
		transfercommon.StringUPPER(chunk.SchemaNameT),
		chunk.TableNameT,
		columnNameS,
		len(rowIDs),
		e.migrationParam.GetSQLStatementType(isRetry))

	stmt, err := e.dbConns.GetTargetDB().MySQLDB.PrepareContext(ctx, sqlStr)
	if err != nil {
		return nil, nil, err
	}

	// 构建查询元数据
	var globalScn uint64
	if e.migrationParam.GetConsistentRead() && e.taskInfo.ScnNumber != "" {
		globalScn, _ = parseUint64(e.taskInfo.ScnNumber)
	}

	meta := meta.FullSyncMeta{
		SchemaNameS:    fmt.Sprintf(`"%v"`, chunk.SchemaNameS),
		TableNameS:     fmt.Sprintf(`"%v"`, chunk.TableNameS),
		SchemaNameT:    chunk.SchemaNameT,
		TableNameT:     chunk.TableNameT,
		ConsistentRead: stringutil.BoolToYesOrNo(e.migrationParam.GetConsistentRead()),
		SQLHint:        chunk.SqlHintS,
		ColumnDetailS:  chunk.ColumnDetailS,
		ChunkDetailS:   rowIDCondition,
		GlobalScnS:     globalScn,
	}

	rows := tmso2t.NewRows(ctx, meta,
		e.dbConns.GetSourceDB(),
		e.dbConns.GetTargetDB(),
		stmt,
		transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(e.dbConns.GetSourceDS().Charset)],
		transfercommon.StringUPPER(e.dbConns.GetTargetDS().Charset),
		e.migrationParam.GetApplyThreads(),
		e.migrationParam.GetInsertBatchSize(),
		e.migrationParam.GetCallTimeout(),
		e.migrationParam.GetSQLStatementType(isRetry),
		columnNameS)

	return stmt, rows, nil
}

// buildSingleReplayRequest 构建单条重放请求
func (e *ChunkReplayExecutor) buildSingleReplayRequest(ctx context.Context, chunk *migration.TableMigrationDetail,
	rowID string, columnNameS []string, isRetry bool) (*sql.Stmt, *tmso2t.Rows, error) {

	// 构建ROWID条件
	rowIDCondition := fmt.Sprintf(`ROWID = '%s'`, rowID)

	// 准备目标表的SQL语句
	sqlStr := tmso2t.GenMySQLTablePrepareStmt(
		transfercommon.StringUPPER(chunk.SchemaNameT),
		chunk.TableNameT,
		columnNameS,
		1,
		e.migrationParam.GetSQLStatementType(isRetry))

	stmt, err := e.dbConns.GetTargetDB().MySQLDB.PrepareContext(ctx, sqlStr)
	if err != nil {
		return nil, nil, err
	}

	// 构建查询元数据
	var globalScn uint64
	if e.migrationParam.GetConsistentRead() && e.taskInfo.ScnNumber != "" {
		globalScn, _ = parseUint64(e.taskInfo.ScnNumber)
	}

	meta := meta.FullSyncMeta{
		SchemaNameS:    fmt.Sprintf(`"%v"`, chunk.SchemaNameS),
		TableNameS:     fmt.Sprintf(`"%v"`, chunk.TableNameS),
		SchemaNameT:    chunk.SchemaNameT,
		TableNameT:     chunk.TableNameT,
		ConsistentRead: stringutil.BoolToYesOrNo(e.migrationParam.GetConsistentRead()),
		SQLHint:        chunk.SqlHintS,
		ColumnDetailS:  chunk.ColumnDetailS,
		ChunkDetailS:   rowIDCondition,
		GlobalScnS:     globalScn,
	}

	rows := tmso2t.NewRows(ctx, meta,
		e.dbConns.GetSourceDB(),
		e.dbConns.GetTargetDB(),
		stmt,
		transfercommon.MigrateOracleCharsetStringConvertMapping[transfercommon.StringUPPER(e.dbConns.GetSourceDS().Charset)],
		transfercommon.StringUPPER(e.dbConns.GetTargetDS().Charset),
		e.migrationParam.GetApplyThreads(),
		e.migrationParam.GetInsertBatchSize(),
		e.migrationParam.GetCallTimeout(),
		e.migrationParam.GetSQLStatementType(isRetry),
		columnNameS)

	return stmt, rows, nil
}

// parseUint64 安全地解析uint64
func parseUint64(s string) (uint64, error) {
	if s == "" {
		return 0, fmt.Errorf("empty string")
	}
	return strconv.ParseUint(s, 10, 64)
}

// Close 清理资源
func (e *ChunkReplayExecutor) Close() {
	for _, stmt := range e.stmtCache {
		if stmt != nil {
			stmt.Close()
		}
	}
}
