package sqlanalyze

import (
	"context"
	"fmt"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/structs"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
)

// ExecuteSQLAnalyzerByDefault 默认执行SQL分析任务，使用DBSource
func ExecuteSQLAnalyzerByDefault(ctx context.Context, channelId int, taskId int) error {
	return ExecuteSQLAnalyzer(ctx, channelId, taskId)
}

// ExecuteSQLAnalyzer 单机版执行SQL分析任务
func ExecuteSQLAnalyzer(ctx context.Context, channelId int, taskId int) error {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		log.Errorf("get task info failed, channelId: %d, taskId: %d, error: %v", channelId, taskId, err)
		return err
	}
	channelSchemas, err := models.GetChannelReaderWriter().GetChannelSchemas(ctx, channelId)
	if err != nil {
		log.Errorf("get channel info failed, channelId: %d, taskId: %d, error: %v", channelId, taskId, err)
		return err
	}
	mapper := structs.NewChannelSchemaMapper()
	for _, schema := range channelSchemas {
		mapper.SetSchemaMapping(schema.SchemaNameS, schema.SchemaNameT)
	}

	runConfig, err := BuildSQLAnalyzeParameter(ctx, channelId, taskId)
	if err != nil {
		log.Errorf("Init failed, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}

	coordinator, buildErr := BuildSQLReplayCoordinator(taskInfo, runConfig, mapper)
	if buildErr != nil {
		log.Errorf("sql coordinator init failed. taskId:%d, err:%s", taskId, buildErr)
		return buildErr
	}

	resetErr := coordinator.ResetMetadata(ctx)
	if resetErr != nil {
		log.Errorf("reset metadata failed. taskId:%d, err:%s", taskId, resetErr)
		return resetErr
	}

	// Update task start time before beginning execution
	_, updateStartErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
		StartTime: time.Now(),
	})
	if updateStartErr != nil {
		log.Errorf("update task start time failed. taskId:%d, err:%s", taskId, updateStartErr)
		// Continue execution even if updating start time fails
	}

	go func() {
		executeErr := coordinator.executeSQLAnalyzerTask(ctx)
		if executeErr != nil {
			_, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
				TaskStatus:  constants.TASK_STATUS_FAILED,
				EndTime:     time.Now(),
				ErrorDetail: fmt.Sprintf("[error][%v][%s]: %s", timeutil.GetNowTime().Format("2006-01-02T15:04:05-07:00"), constants.SQLAnalyzeTaskLogType, executeErr.Error()),
			})
			if updateErr != nil {
				log.Errorf("update task failed. taskId:%d, err:%s", taskId, updateErr)
			}
		} else {
			_, updateErr := models.GetTaskReaderWriter().UpdateTaskByTaskID(ctx, taskId, task.Task{
				TaskStatus:  constants.TASK_STATUS_FINISH,
				EndTime:     time.Now(),
				ErrorDetail: "",
			})
			if updateErr != nil {
				log.Errorf("update task failed. taskId:%d, err:%s", taskId, updateErr)
			}
		}
	}()
	return nil
}
