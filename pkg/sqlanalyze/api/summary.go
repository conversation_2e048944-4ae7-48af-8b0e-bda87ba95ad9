package api

import (
	"context"
	"fmt"
	"time"

	sqlanalyzepkg "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze"
	"gitee.com/pingcap_enterprise/tms/server/service/common"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

func GetSqlAnalyzerSummary(ctx context.Context, req *message.GetTaskDetailResultReq) (*message.GetTaskDetailResultResp, error) {
	log.Infof("start GetSqlAnalyzerSummary taskId:%d, channelId:%d", req.TaskId, req.ChannelId)
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, req.TaskId)
	if err != nil {
		log.Errorf("get task info failed, taskId:%v, err: %v", req.TaskId, err)
		return nil, err
	}
	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, channelId:%v, err: %v", taskInfo.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	param, buildErr := sqlanalyzepkg.BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, taskInfo.TaskID)
	if buildErr != nil {
		log.Errorf("build sql analyze parameter failed, err: %v", buildErr)
		return nil, buildErr
	}

	// 初始化统计数据
	var (
		succNums     = 0
		failNums     = 0
		timeoutNums  = 0
		totalNums    = 0
		sqlStartTime = taskInfo.StartTime
		sqlStopTime  = taskInfo.EndTime
		sqlAnalyzer  []message.SqlAnalyzerSummaryDetail
	)

	if param.IsRunVersionV1() {
		// 获取SQL执行结果
		sumSqlList, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtExecResultByStatus(ctx, req.TaskId, taskInfo.StartTime, "")
		if err != nil {
			log.Errorf("get sql stmt exec result failed, err:%v", err)
			return nil, fmt.Errorf("failed to get sql stmt exec result: %w", err)
		}

		// 处理SQL执行结果
		for _, v := range sumSqlList {
			succNums += v.Successnum
			failNums += v.Failnum
			timeoutNums += v.Timeoutnum
			totalNums += v.Count

			// 更新时间范围
			if v.Starttime.Before(sqlStartTime) {
				sqlStartTime = v.Starttime
			}
			if sqlStopTime.Before(v.Endtime) {
				sqlStopTime = v.Endtime
			}

			// 计算成功率并添加分析详情
			successRatio := 0.0
			if v.Count > 0 {
				successRatio = float64(v.Successnum) / float64(v.Count) * 100
			}
			sqlAnalyzer = append(sqlAnalyzer, message.SqlAnalyzerSummaryDetail{
				TaskId:       v.TaskId,
				Schema:       v.OraParsingSchemaName,
				TotalNums:    v.Count,
				SuccessNums:  v.Successnum,
				FailedNums:   v.Failnum,
				SuccessRatio: fmt.Sprintf("%.2f%%", successRatio),
				StartTime:    v.Starttime,
				EndTime:      v.Endtime,
				Duration:     v.Endtime.Sub(v.Starttime).String(),
			})
		}

		if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
			sqlStopTime = time.Now()
		}
		return &message.GetTaskDetailResultResp{
			TaskId:                  req.TaskId,
			StartTime:               taskInfo.StartTime,
			TotalTables:             totalNums,
			TotalDuration:           sqlStopTime.Sub(sqlStartTime).String(),
			TaskDetailChartDataList: buildChartData(totalNums, succNums, failNums, timeoutNums),
			TaskDetailSchemaData:    message.TaskDetailSchemaData{SqlAnalyzer: sqlAnalyzer},
		}, nil
	} else {
		oracleConn, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
		if setUpErr != nil {
			log.Errorf("set up source database conn failed, taskId:%v, err: %v", req.TaskId, setUpErr)
			return nil, fmt.Errorf("failed to set up source database conn: %w", setUpErr)
		}

		sqlsetName := param.SQLFactor.GetSQLSetName()
		sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
		schemas := param.GetIncludingSchemasBySQLSource()


		if param.IsFileMode() {
			sqlsetOwner = param.AppSQLsFilename
			schemas = param.GetIncludingSchemasBySQLSource()
		}


		summary, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsSummaries(ctx, oracleConn.OracleDB, req.TaskId, sqlsetName, sqlsetOwner, schemas)
		if getErr != nil {
			log.Errorf("get sql set statements summaries failed, taskId:%v, err: %v", req.TaskId, getErr)
			return nil, fmt.Errorf("failed to get sql set statements summaries: %w", getErr)
		}

		for _, s := range summary {
			totalNums += int(s.TotalNum)
			succNums += int(s.SuccessNum)
			failNums += int(s.FailedNum)
			timeoutNums += int(s.TimeoutNum)

			successRatio := 0.0
			if s.TotalNum > 0 {
				successRatio = float64(s.SuccessNum) / float64(s.TotalNum) * 100
			}
			sqlAnalyzer = append(sqlAnalyzer, message.SqlAnalyzerSummaryDetail{
				TaskId:       req.TaskId,
				Schema:       s.SQLSetOwner,
				TotalNums:    int(s.TotalNum),
				SuccessNums:  int(s.SuccessNum),
				FailedNums:   int(s.FailedNum + s.TimeoutNum),
				SuccessRatio: fmt.Sprintf("%.2f%%", successRatio),
			})
		}

		if taskInfo.TaskStatus == constants.TASK_STATUS_RUNNING {
			sqlStopTime = time.Now()
		}
		return &message.GetTaskDetailResultResp{
			TaskId:                  req.TaskId,
			StartTime:               taskInfo.StartTime,
			TotalTables:             totalNums,
			TotalDuration:           sqlStopTime.Sub(sqlStartTime).String(),
			TaskDetailChartDataList: buildChartData(totalNums, succNums, failNums, timeoutNums),
			TaskDetailSchemaData: message.TaskDetailSchemaData{
				SqlAnalyzer: sqlAnalyzer,
			},
		}, nil
	}
}

// buildChartData 构建图表数据
func buildChartData(total, success, failed, timeout int) []message.TaskDetailChartData {
	calculatePct := func(count, total int) string {
		if total == 0 {
			return "0.00%"
		}
		return fmt.Sprintf("%.2f%%", float64(count)/float64(total)*100)
	}

	return []message.TaskDetailChartData{
		{
			Total: total,
			Type:  constants.TiDBExplainSuccess,
			Count: success,
			Pct:   calculatePct(success, total),
		},
		{
			Total: total,
			Type:  constants.TiDBExplainFailed,
			Count: failed,
			Pct:   calculatePct(failed, total),
		},
		{
			Total: total,
			Type:  constants.TiDBExplainTimeout,
			Count: timeout,
			Pct:   calculatePct(timeout, total),
		},
	}
}
