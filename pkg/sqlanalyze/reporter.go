package sqlanalyze

import (
	"context"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/common/constants"
	tmserrors "gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	commonpkg "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/message"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/config"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"html/template"
	"os"
	"sort"
	"strings"
)

type SQLReporter struct {
	taskId         int
	schemas        []string
	reportType     string
	reportFileName string
	reportTitle    string
	param          *structs.Parameter
}

func NewSQLReporter(taskId int, schemas []string, reportType string, reportFileName string, reportTitle string, param *structs.Parameter) *SQLReporter {
	return &SQLReporter{
		taskId:         taskId,
		schemas:        schemas,
		reportType:     reportType,
		reportFileName: reportFileName,
		reportTitle:    reportTitle,
		param:          param,
	}
}

func (i *SQLReporter) GenerateSQLAnalyzeReport(ctx context.Context) error {
	// 创建目录
	reportFileNameToken := strings.Split(i.reportFileName, "/")
	reportFile := reportFileNameToken[len(reportFileNameToken)-1]
	reportPath := strings.Replace(i.reportFileName, reportFile, "", 1)

	log.Infof("start generate html, taskId:%d, schemas:%s, reportPath:%s, reportFile:%s", i.taskId, i.schemas, reportPath, reportFile)

	err := i.mkdirReportPathIfNotExist(reportPath)
	if err != nil {
		log.Errorf("start generate html failed, mkdir report path failed, taskId:%d, path:%s, err:%v", i.taskId, reportPath, err)
		return err
	}

	log.Infof("start generate html, get report data start, taskId:%d, channelSchemas: %v", i.taskId, i.schemas)

	htmlData, templateName, err := i.getHtmlContextAndTemplateName(ctx)
	if err != nil {
		log.Errorf("start generate html failed, get html context failed, taskId:%d, err:%v", i.taskId, err)
		return err
	}

	// 生成html报告
	templateFilename := fmt.Sprintf("%ssql_%s_template.html", reportPath, i.reportType)
	err = i.checkOrCreateTemplateFile(ctx, templateFilename)
	if err != nil {
		log.Errorf("start generate html failed, checkOrCreateTemplateFile failed, taskId:%d, templateFilename:%s, %v", i.taskId, templateFilename, err)
		return err
	}

	log.Infof("generate html start, reportName: %s", i.reportFileName)
	err = i.applyHtmlContentIntoTemplate(htmlData, templateName, templateFilename)
	if err != nil {
		log.Errorf("start generate html failed, applyHtmlContentIntoTemplate failed, taskId:%d, err:%v", i.taskId, err)
		return fmt.Errorf("applyHtmlContentIntoTemplate failed, taskId:%d, err:%v", i.taskId, err)
	}
	return nil
}

func (i *SQLReporter) GenerateSQLAnalyzeReportZip(ctx context.Context) error {
	log.Infof("start generate html, get report data start, taskId:%d, channelSchemas: %v", i.taskId, i.schemas)

	if i.param.RunVersion == "v1" {
		htmlData, _, err := i.getHtmlContextAndTemplateName(ctx)
		if err != nil {
			log.Errorf("start generate html failed, get html context failed, taskId:%d, err:%v", i.taskId, err)
			return err
		}
		reportPath := fmt.Sprintf("%s/sqlanalyzer/sqlanalyzer_%d", config.GetGlobalConfig().DataDir, i.taskId)
		zipName := fmt.Sprintf("%s/sqlanalyzer/sqlanalyzer_%d.zip", config.GetGlobalConfig().DataDir, i.taskId)
		title := fmt.Sprintf("sql_%s report", i.reportType)
		rptType := commonpkg.SQL_COMPATIBILITY_REPORT
		if strings.EqualFold(i.reportType, "performance") {
			rptType = commonpkg.SQL_PERFORMANCE_REPORT
		}
		err = commonpkg.GenerateReport(ctx, i.taskId, rptType, title, reportPath, zipName, &structs.SqlAnalyzerReportAdapter{Data: htmlData})
		if err != nil {
			log.Errorf("generateReport err: %v", err)
		}
	} else {
		htmlData, _, err := i.getHtmlContextAndTemplateNameV2(ctx)
		if err != nil {
			log.Errorf("start generate html failed, get html context failed, taskId:%d, err:%v", i.taskId, err)
			return err
		}
		reportPath := fmt.Sprintf("%s/sqlanalyzer/sqlanalyzer_%d", config.GetGlobalConfig().DataDir, i.taskId)
		zipName := fmt.Sprintf("%s/sqlanalyzer/sqlanalyzer_%d.zip", config.GetGlobalConfig().DataDir, i.taskId)
		title := fmt.Sprintf("sql_%s report", i.reportType)
		rptType := commonpkg.SQL_COMPATIBILITY_REPORT_V2
		if strings.EqualFold(i.reportType, "performance") {
			rptType = commonpkg.SQL_PERFORMANCE_REPORT
		}
		err = commonpkg.GenerateReport(ctx, i.taskId, rptType, title, reportPath, zipName, &datasource.SqlAnalyzerReportAdapterV2{Data: htmlData})
		if err != nil {
			log.Errorf("generateReport err: %v", err)
		}
	}

	return nil
}

// DownloadSqlAnalyzeReportBySchema 下载SQL分析报告
func DownloadSqlAnalyzeReportBySchema(ctx context.Context, req *message.DownloadSqlReportBySchemaReq) (*message.DownloadSqlReportBySchemaResp, error) {
	var schemas []string
	schemas = req.Schema
	schemas = lo.Filter(schemas, func(item string, _ int) bool {
		return len(strings.TrimSpace(item)) != 0
	})
	if len(schemas) == 0 {
		err := tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "schema is empty")
		log.Errorf("DownloadSqlAnalyzeReportBySchema failed, taskId:%d, schema:%v, err:%v", req.TaskId, req.Schema, err)
		return nil, err
	}

	param, buildErr := BuildSQLAnalyzeParameter(ctx, req.ChannelId, req.TaskId)
	if buildErr != nil {
		log.Errorf("DownloadSqlAnalyzeReportBySchema failed, taskId:%d, buildErr:%v", req.TaskId, buildErr)
		return nil, buildErr
	}

	// 生成检查结果文件
	reporter := NewSQLReporter(req.TaskId, schemas, req.ReportType, req.ReportFilePath, req.Title, param)
	log.Infof("start generate html, schemas: %v, reportFileName: %v", req.Schema, req.ReportFilePath)
	genErr := reporter.GenerateSQLAnalyzeReport(ctx)
	if genErr != nil {
		log.Errorf("generateSqlAnalyzeReport failed, taskId:%d, err:%v", req.TaskId, genErr)
		return nil, genErr
	}

	return &message.DownloadSqlReportBySchemaResp{}, nil
}

func DownloadSqlAnalyzeReportZipBySchema(ctx context.Context, req *message.DownloadSqlReportBySchemaReq) (*message.DownloadSqlReportBySchemaResp, error) {
	var schemas []string
	schemas = req.Schema
	schemas = lo.Filter(schemas, func(item string, _ int) bool {
		return len(strings.TrimSpace(item)) != 0
	})
	if len(schemas) == 0 {
		err := tmserrors.NewError(tmserrors.TIMS_PARAMETER_INVALID, "schema is empty")
		log.Errorf("DownloadSqlAnalyzeReportBySchema failed, taskId:%d, schema:%v, err:%v", req.TaskId, req.Schema, err)
		return nil, err
	}

	param, buildErr := BuildSQLAnalyzeParameter(ctx, req.ChannelId, req.TaskId)
	if buildErr != nil {
		log.Errorf("DownloadSqlAnalyzeReportBySchema failed, taskId:%d, buildErr:%v", req.TaskId, buildErr)
		return nil, buildErr
	}

	// 生成检查结果文件
	reporter := NewSQLReporter(req.TaskId, schemas, req.ReportType, req.ReportFilePath, req.Title, param)

	log.Infof("start generate html, schemas: %v, reportFileName: %v", req.Schema, req.ReportFilePath)
	genErr := reporter.GenerateSQLAnalyzeReportZip(ctx)
	if genErr != nil {
		log.Errorf("generateSqlAnalyzeReport failed, taskId:%d, err:%v", req.TaskId, genErr)
		return nil, genErr
	}

	return &message.DownloadSqlReportBySchemaResp{ExportFilePath: req.ReportFilePath}, nil
}

// getHtmlContextAndTemplateName 获取HTML上下文和模板名称
func (i *SQLReporter) getHtmlContextAndTemplateName(ctx context.Context) (*structs.SqlAnalyzerReport, string, error) {
	var htmlData *structs.SqlAnalyzerReport
	var templateName string
	var err error
	if i.reportType == "compatibility" {
		templateName = "sql_compatibility_template"
		log.Infof("start generate html, getSqlExecTableData start, channelSchemas: %v, reportType: %v", i.schemas, i.reportType)
		htmlData, err = i.getSQLExecTableDataCompatibility(ctx)
		if err != nil {
			log.Errorf("getSQLExecTableDataCompatibility err:%s", err)
			return nil, "", err
		}
	} else if i.reportType == "compatibility_fingerprint" {
		templateName = "sql_compatibility_fingerprint_template"
		log.Infof("start generate html, getSqlExecTableData start, channelSchemas: %v, reportType: %v", i.schemas, i.reportType)
		htmlData, err = i.getSQLExecTableDataCompatibility(ctx)
		if err != nil {
			log.Errorf("getSQLExecTableDataCompatibility err:%s", err)
			return nil, "", err
		}
	} else if i.reportType == "performance" {
		templateName = "sql_performance_template"
		log.Infof("start generate html, getSqlExecTableData start, channelSchemas: %v, reportType: %v", i.schemas, i.reportType)
		htmlData, err = i.getSQLExecTableDataPerformance(ctx)
		if err != nil {
			log.Errorf("getSQLExecTableDataCompatibility err:%s", err)
			return nil, "", err
		}
	} else {
		log.Errorf("unknown reportType:%s", i.reportType)
		return nil, "", tmserrors.NewError(tmserrors.TMS_UNKOWN_REPORT_TYPE, i.reportType)
	}
	return htmlData, templateName, nil
}

func (i *SQLReporter) getHtmlContextAndTemplateNameV2(ctx context.Context) (*datasource.SqlAnalyzerReportV2, string, error) {
	var htmlData *datasource.SqlAnalyzerReportV2
	var templateName string
	var err error
	if i.reportType == "compatibility" {
		templateName = "sql_compatibility_fingerprint_template"
		log.Infof("start generate html, getSqlExecTableData start, channelSchemas: %v, reportType: %v", i.schemas, i.reportType)
		htmlData, err = i.getSQLExecTableDataCompatibilityV2(ctx)
		if err != nil {
			log.Errorf("getSQLExecTableDataCompatibility err:%s", err)
			return nil, "", err
		}
	}
	return htmlData, templateName, nil
}

// mkdirReportPathIfNotExist 创建报告目录
func (i *SQLReporter) mkdirReportPathIfNotExist(reportPath string) error {
	if reportPath != "" {
		_, err := os.Stat(reportPath)
		if err != nil {
			log.Infof("report path not exists, start create it. %v", reportPath)
			if mkdirErr := os.MkdirAll(reportPath, os.ModePerm); mkdirErr != nil {
				log.Errorf("create path failed. err:%s", mkdirErr)
				return mkdirErr
			}
		}
	}
	return nil
}

// checkOrCreateTemplateFile 检查或创建模板文件
func (i *SQLReporter) checkOrCreateTemplateFile(ctx context.Context, templateFilename string) error {
	log.Infof("generate html template start, templateFilename name:%s ", templateFilename)
	_, err := os.Stat(templateFilename)
	if err == nil {
		log.Infof("generate html template skip, template already exist, file:%s", templateFilename)
		return nil
	}
	if errors.Is(err, os.ErrNotExist) {
		log.Infof("generate html template not exist, start to create, file:%s", templateFilename)
		htmlTemplates, err := models.GetSqlAnalyzerReaderWriter().GetHtmlTemplateDetail(ctx, fmt.Sprintf("O2T_SQL_ANALYZER_%s", strings.ToUpper(i.reportType)))
		if err != nil {
			log.Infof("get templateFilename from db failed, err:%v", err)
			return err
		}
		htmlTemplateDetail := ""
		for _, v := range htmlTemplates {
			htmlTemplateDetail = v.Detail
			break
		}
		file, err := os.Create(templateFilename)
		if err != nil {
			log.Errorf("generate html template, create templateFilename failed, err:%v", err)
			return err
		}
		_, err = file.WriteString(htmlTemplateDetail)
		if err != nil {
			log.Errorf("generate html template, write templateFilename failed, err:%v", err)
			return err
		}
		err = file.Close()
		if err != nil {
			log.Errorf("generate html template, write templateFilename success, close fd failed, err:%v", err)
			return err
		}
	}
	return nil
}

// getSQLExecTableDataCompatibility 获取SQL兼容性分析数据
func (i *SQLReporter) getSQLExecTableDataCompatibility(ctx context.Context) (*structs.SqlAnalyzerReport, error) {
	sqlExecData, err := i.fetchSQLExecData(ctx)
	if err != nil {
		return nil, err
	}

	detailsWithUserOps, err := i.fetchAndProcessSQLDetailsWithUserOperations(ctx)
	if err != nil {
		return nil, err
	}

	overviewData := i.processCompatibilityOverviewWithUserOps(sqlExecData, detailsWithUserOps)
	incompatibleErrors := i.processIncompatibleErrors(sqlExecData)

	return &structs.SqlAnalyzerReport{
		Title:              i.reportTitle,
		CompatibleOverview: overviewData.overview,
		IncompatibleError:  incompatibleErrors,
		TimeoutError:       []*structs.TimeoutError{{TotalNums: overviewData.timeoutCount}},
		SuccessDetail:      detailsWithUserOps.normal.success,
		TimeoutDetail:      detailsWithUserOps.normal.timeout,
		ErrorDetail:        detailsWithUserOps.normal.failed,
		OtherDetail:        detailsWithUserOps.normal.other,
		// Ignored sections
		IgnoredSuccessDetail: detailsWithUserOps.ignored.success,
		IgnoredTimeoutDetail: detailsWithUserOps.ignored.timeout,
		IgnoredErrorDetail:   detailsWithUserOps.ignored.failed,
		IgnoredOtherDetail:   detailsWithUserOps.ignored.other,
		// Resolved sections
		ResolvedSuccessDetail: detailsWithUserOps.resolved.success,
		ResolvedTimeoutDetail: detailsWithUserOps.resolved.timeout,
		ResolvedErrorDetail:   detailsWithUserOps.resolved.failed,
		ResolvedOtherDetail:   detailsWithUserOps.resolved.other,
	}, nil
}

func (i *SQLReporter) getSQLExecTableDataCompatibilityV2(ctx context.Context) (*datasource.SqlAnalyzerReportV2, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, i.taskId)
	if err != nil {
		log.Errorf("get task info failed, taskId:%v, err: %v", i.taskId, err)
		return nil, err
	}
	param, buildErr := BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, i.taskId)
	if buildErr != nil {
		log.Errorf("build sql analyze parameter failed, err: %v", buildErr)
		return nil, buildErr
	}
	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := param.GetIncludingSchemasBySQLSource()

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, channelId:%v, err: %v", taskInfo.ChannelId, getChannelErr)
		return nil, getChannelErr
	}

	oracleConn, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if setUpErr != nil {
		log.Errorf("set up source database conn failed, taskId:%v, err: %v", i.taskId, setUpErr)
		return nil, fmt.Errorf("failed to set up source database conn: %w", setUpErr)
	}

	if param.IsFileMode() {
		sqlsetOwner = param.AppSQLsFilename
		schemas = param.GetIncludingSchemasBySQLSource()
	}


	totalSummaries, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsSummaries(ctx, oracleConn.OracleDB, i.taskId, sqlsetName, sqlsetOwner, schemas)
	if getErr != nil {
		log.Errorf("get sql set statements summaries failed, taskId:%v, err: %v", i.taskId, getErr)
	}

	failedSummaries, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsFailedSummaries(ctx, oracleConn.OracleDB, i.taskId, sqlsetName, sqlsetOwner, schemas)
	if getErr != nil {
		log.Errorf("get sql set statements failed summaries failed, taskId:%v, err: %v", i.taskId, getErr)
	}

	timeoutSummariesOld, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsTimeoutSummaries(ctx, oracleConn.OracleDB, i.taskId, sqlsetName, sqlsetOwner, schemas)
	if getErr != nil {
		log.Errorf("get sql set statements timeout summaries failed, taskId:%v, err: %v", i.taskId, getErr)
	}

	// 转换为新的超时统计结构
	var timeoutSummaries datasource.SQLSetStatementsTimeoutSummaries
	for _, oldSummary := range timeoutSummariesOld {
		timeoutSummaries = append(timeoutSummaries, datasource.SQLSetStatementsTimeoutSummary{
			TotalNum:           oldSummary.TotalNum,
			IgnoredTimeoutNum:  0, // 初始化为0，稍后计算
			ResolvedTimeoutNum: 0, // 初始化为0，稍后计算
		})
	}

	// 获取所有SQL详情
	allDetails, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsList(ctx, oracleConn.OracleDB, i.taskId, sqlsetName, sqlsetOwner, schemas)
	if getErr != nil {
		log.Errorf("get sql set statements list failed, taskId:%v, err: %v", i.taskId, getErr)
		allDetails = datasource.SQLSetStatements{}
	}

	// 获取用户操作数据并合并
	detailsWithUserOps := i.mergeOracleDataWithUserOperationsV2(ctx, allDetails)

	// 计算ignored/resolved统计信息
	i.calculateIgnoredResolvedStatistics(totalSummaries, failedSummaries, &timeoutSummaries, detailsWithUserOps)

	return &datasource.SqlAnalyzerReportV2{
		Title:            i.reportTitle,
		TotalSummaries:   totalSummaries,
		FailedSummaries:  failedSummaries,
		TimeoutSummaries: timeoutSummaries,
		// Normal details
		ErrorDetails:     detailsWithUserOps.normal.failed,
		SuccessDetails:   detailsWithUserOps.normal.success,
		TimeoutDetails:   detailsWithUserOps.normal.timeout,
		NotReplayDetails: detailsWithUserOps.normal.notReplay,
		AiFixDetails:     detailsWithUserOps.normal.aiFix,
		// Ignored details
		IgnoredErrorDetails:     detailsWithUserOps.ignored.failed,
		IgnoredSuccessDetails:   detailsWithUserOps.ignored.success,
		IgnoredTimeoutDetails:   detailsWithUserOps.ignored.timeout,
		IgnoredNotReplayDetails: detailsWithUserOps.ignored.notReplay,
		// Resolved details
		ResolvedErrorDetails:     detailsWithUserOps.resolved.failed,
		ResolvedSuccessDetails:   detailsWithUserOps.resolved.success,
		ResolvedTimeoutDetails:   detailsWithUserOps.resolved.timeout,
		ResolvedNotReplayDetails: detailsWithUserOps.resolved.notReplay,
	}, nil
}

// fetchSQLExecData 获取SQL执行数据
func (i *SQLReporter) fetchSQLExecData(ctx context.Context) ([]*sqlanalyzer.SqlStmtExecResultByErrCode, error) {
	data, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtExecResultByErrCode(ctx, i.taskId, i.schemas)
	if err != nil {
		log.Errorf("get GetSqlAnalyzerSummaryByTaskId failed, err: %v", err)
	}
	return data, err
}

// overviewStats 统计概览数据
type overviewStats struct {
	overview     []*structs.CompatibleOverview
	timeoutCount int
}

// processCompatibilityOverview 处理兼容性概览数据
func (i *SQLReporter) processCompatibilityOverview(sqlExecData []*sqlanalyzer.SqlStmtExecResultByErrCode) overviewStats {
	stats := newSQLStats()

	// 统计数据
	for _, v := range sqlExecData {
		stats.total[v.TidbSqlType] += v.Count
		switch v.TidbExecStatus {
		case constants.TiDBExplainSuccess:
			stats.success[v.TidbSqlType] += v.Count
		case constants.TiDBExplainFailed:
			stats.failed[v.TidbSqlType] += v.Count
			stats.errCount[v.TidbExecCode] += v.Count
			stats.errMsg[v.TidbExecCode] = v.TidbExecMsg
		case constants.TiDBExplainTimeout:
			stats.timeout[v.TidbSqlType] += v.Count
		}
	}

	return stats.generateOverview()
}

func (i *SQLReporter) processCompatibilityOverviewWithUserOps(sqlExecData []*sqlanalyzer.SqlStmtExecResultByErrCode, detailsWithUserOps sqlDetailsWithUserOperations) overviewStats {
	stats := newSQLStats()

	// 统计数据
	for _, v := range sqlExecData {
		stats.total[v.TidbSqlType] += v.Count
		switch v.TidbExecStatus {
		case constants.TiDBExplainSuccess:
			stats.success[v.TidbSqlType] += v.Count
		case constants.TiDBExplainFailed:
			stats.failed[v.TidbSqlType] += v.Count
			stats.errCount[v.TidbExecCode] += v.Count
			stats.errMsg[v.TidbExecCode] = v.TidbExecMsg
		case constants.TiDBExplainTimeout:
			stats.timeout[v.TidbSqlType] += v.Count
		}
	}

	return stats.generateOverviewWithUserOps(detailsWithUserOps)
}

// sqlStats SQL统计数据结构
type sqlStats struct {
	total    map[string]int
	success  map[string]int
	failed   map[string]int
	timeout  map[string]int
	errCount map[string]int
	errMsg   map[string]string
}

func newSQLStats() sqlStats {
	return sqlStats{
		total:    make(map[string]int),
		success:  make(map[string]int),
		failed:   make(map[string]int),
		timeout:  make(map[string]int),
		errCount: make(map[string]int),
		errMsg:   make(map[string]string),
	}
}

// generateOverview 生成兼容性概览
func (s sqlStats) generateOverview() overviewStats {
	var overview []*structs.CompatibleOverview
	totalNum, succNum, failNum, timeoutNum := 0, 0, 0, 0

	keys := lo.Keys(s.total)
	sort.Strings(keys)

	for _, k := range keys {
		total := s.total[k]
		succ := s.success[k]
		totalNum += total
		succNum += succ
		failNum += s.failed[k]
		timeoutNum += s.timeout[k]

		overview = append(overview, &structs.CompatibleOverview{
			Type:        k,
			TotalNums:   total,
			SuccNums:    succ,
			FailNums:    s.failed[k],
			TimeoutNums: s.timeout[k],
			Pct:         fmt.Sprintf("%.2f%%", float64(succ)/float64(total)*100),
		})
	}

	overview = append(overview, &structs.CompatibleOverview{
		Type:        "TOTAL",
		TotalNums:   totalNum,
		SuccNums:    succNum,
		FailNums:    failNum,
		TimeoutNums: timeoutNum,
		Pct:         fmt.Sprintf("%.2f%%", float64(succNum)/float64(totalNum)*100),
	})

	return overviewStats{overview: overview, timeoutCount: timeoutNum}
}

// generateOverviewWithUserOps 生成包含用户操作统计的兼容性概览
func (s sqlStats) generateOverviewWithUserOps(detailsWithUserOps sqlDetailsWithUserOperations) overviewStats {
	var overview []*structs.CompatibleOverview
	totalNum, succNum, failNum, timeoutNum := 0, 0, 0, 0

	// 统计ignored和resolved数量
	ignoredCount := len(detailsWithUserOps.ignored.success) + len(detailsWithUserOps.ignored.failed) +
		len(detailsWithUserOps.ignored.timeout) + len(detailsWithUserOps.ignored.other)
	resolvedCount := len(detailsWithUserOps.resolved.success) + len(detailsWithUserOps.resolved.failed) +
		len(detailsWithUserOps.resolved.timeout) + len(detailsWithUserOps.resolved.other)

	// 按类型统计ignored和resolved数量
	ignoredByType := make(map[string]int)
	resolvedByType := make(map[string]int)
	ignoredSuccessByType := make(map[string]int)
	ignoredFailedByType := make(map[string]int)
	resolvedSuccessByType := make(map[string]int)
	resolvedFailedByType := make(map[string]int)

	// 统计ignored的SQL详情，需要根据SQL类型分组（这里简化处理，按常见SQL类型分组）
	for range detailsWithUserOps.ignored.success {
		sqlType := "SELECT" // 简化处理，实际应该解析SQL获取类型
		ignoredByType[sqlType]++
		ignoredSuccessByType[sqlType]++
	}
	for range detailsWithUserOps.ignored.failed {
		sqlType := "SELECT" // 简化处理
		ignoredByType[sqlType]++
		ignoredFailedByType[sqlType]++
	}
	for range detailsWithUserOps.resolved.success {
		sqlType := "SELECT" // 简化处理
		resolvedByType[sqlType]++
		resolvedSuccessByType[sqlType]++
	}
	for range detailsWithUserOps.resolved.failed {
		sqlType := "SELECT" // 简化处理
		resolvedByType[sqlType]++
		resolvedFailedByType[sqlType]++
	}

	keys := lo.Keys(s.total)
	sort.Strings(keys)

	for _, k := range keys {
		total := s.total[k]
		originalSucc := s.success[k]
		originalFailed := s.failed[k]

		// 调整成功和失败数量，排除已忽略/已解决的
		adjustedSucc := originalSucc - ignoredSuccessByType[k] - resolvedSuccessByType[k]
		adjustedFailed := originalFailed - ignoredFailedByType[k] - resolvedFailedByType[k]

		// 确保不为负数
		if adjustedSucc < 0 {
			adjustedSucc = 0
		}
		if adjustedFailed < 0 {
			adjustedFailed = 0
		}

		totalNum += total
		succNum += adjustedSucc
		failNum += adjustedFailed
		timeoutNum += s.timeout[k]

		overview = append(overview, &structs.CompatibleOverview{
			Type:         k,
			TotalNums:    total,
			SuccNums:     adjustedSucc,
			FailNums:     adjustedFailed,
			TimeoutNums:  s.timeout[k],
			IgnoredNums:  ignoredByType[k],
			ResolvedNums: resolvedByType[k],
			Pct:          fmt.Sprintf("%.2f%%", float64(adjustedSucc)/float64(total)*100),
		})
	}

	overview = append(overview, &structs.CompatibleOverview{
		Type:         "TOTAL",
		TotalNums:    totalNum,
		SuccNums:     succNum,
		FailNums:     failNum,
		TimeoutNums:  timeoutNum,
		IgnoredNums:  ignoredCount,
		ResolvedNums: resolvedCount,
		Pct:          fmt.Sprintf("%.2f%%", float64(succNum)/float64(totalNum)*100),
	})

	return overviewStats{overview: overview, timeoutCount: timeoutNum}
}

// processIncompatibleErrors 处理不兼容错误
func (i *SQLReporter) processIncompatibleErrors(sqlExecData []*sqlanalyzer.SqlStmtExecResultByErrCode) []*structs.IncompatibleError {
	errCount := make(map[string]int)
	errMsg := make(map[string]string)

	for _, v := range sqlExecData {
		if v.TidbExecStatus == constants.TiDBExplainFailed {
			errCount[v.TidbExecCode] += v.Count
			errMsg[v.TidbExecCode] = v.TidbExecMsg
		}
	}

	var errors []*structs.IncompatibleError
	keys := lo.Keys(errCount)
	sort.Strings(keys)

	for _, k := range keys {
		errors = append(errors, &structs.IncompatibleError{
			ErrCode:   k,
			TotalNums: errCount[k],
			ErrMsg:    errMsg[k],
		})
	}
	return errors
}

// sqlDetails SQL详情分类
type sqlDetails struct {
	success []*structs.CompatibleDetail
	failed  []*structs.CompatibleDetail
	timeout []*structs.CompatibleDetail
	other   []*structs.CompatibleDetail
}

type sqlDetailsWithUserOperations struct {
	normal   sqlDetails
	ignored  sqlDetails
	resolved sqlDetails
}

// fetchAndProcessSQLDetails 获取并处理SQL详情
func (i *SQLReporter) fetchAndProcessSQLDetails(ctx context.Context) (sqlDetails, error) {
	data, err := models.GetSqlAnalyzerReaderWriter().ListSqlStmtExecResultBySchemaAndTopN(ctx,
		i.taskId, i.schemas, i.param.SQLFactor.GetTiDBExecStatusFilter(), i.param.SQLFactor.GetTopNPerStatus())
	if err != nil {
		log.Errorf("get ListSqlStmtExecResultBySchemaAndTopN failed, taksId:%d, err: %v", i.taskId, err)
		return sqlDetails{}, err
	}

	var details sqlDetails
	for _, v := range data {
		detail := i.createCompatibleDetail(v)
		switch v.TidbExecStatus {
		case constants.TiDBExplainSuccess:
			details.success = append(details.success, detail)
		case constants.TiDBExplainFailed:
			details.failed = append(details.failed, detail)
		case constants.TiDBExplainTimeout:
			details.timeout = append(details.timeout, detail)
		default:
			details.other = append(details.other, detail)
		}
	}
	return details, nil
}

// fetchAndProcessSQLDetailsWithUserOperations 获取并处理包含用户操作状态的SQL详情
func (i *SQLReporter) fetchAndProcessSQLDetailsWithUserOperations(ctx context.Context) (sqlDetailsWithUserOperations, error) {
	// Get task and channel information to access Oracle data
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, i.taskId)
	if err != nil {
		log.Errorf("get task info failed, taskId:%v, err: %v", i.taskId, err)
		return sqlDetailsWithUserOperations{}, err
	}

	param, buildErr := BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, i.taskId)
	if buildErr != nil {
		log.Errorf("build sql analyze parameter failed, err: %v", buildErr)
		return sqlDetailsWithUserOperations{}, buildErr
	}

	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := param.GetIncludingSchemasBySQLSource()

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, channelId:%v, err: %v", taskInfo.ChannelId, getChannelErr)
		return sqlDetailsWithUserOperations{}, getChannelErr
	}

	oracleConn, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if setUpErr != nil {
		log.Errorf("set up source database conn failed, taskId:%v, err: %v", i.taskId, setUpErr)
		return sqlDetailsWithUserOperations{}, fmt.Errorf("failed to set up source database conn: %w", setUpErr)
	}

	// Get Oracle data from source database
	allDetails, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsList(ctx, oracleConn.OracleDB, i.taskId, sqlsetName, sqlsetOwner, schemas)
	if getErr != nil {
		log.Errorf("get sql set statements list failed, taskId:%v, err: %v", i.taskId, getErr)
		return sqlDetailsWithUserOperations{}, getErr
	}

	// Merge Oracle data with user operations and convert to V1 format
	return i.mergeOracleDataWithUserOperationsV1(ctx, allDetails), nil
}

// mergeOracleDataWithUserOperationsV1 合并Oracle数据与用户操作数据并转换为V1格式
func (i *SQLReporter) mergeOracleDataWithUserOperationsV1(ctx context.Context, allDetails datasource.SQLSetStatements) sqlDetailsWithUserOperations {
	var result sqlDetailsWithUserOperations

	// 初始化各个分类
	result.normal = sqlDetails{
		success: make([]*structs.CompatibleDetail, 0),
		failed:  make([]*structs.CompatibleDetail, 0),
		timeout: make([]*structs.CompatibleDetail, 0),
		other:   make([]*structs.CompatibleDetail, 0),
	}
	result.ignored = sqlDetails{
		success: make([]*structs.CompatibleDetail, 0),
		failed:  make([]*structs.CompatibleDetail, 0),
		timeout: make([]*structs.CompatibleDetail, 0),
		other:   make([]*structs.CompatibleDetail, 0),
	}
	result.resolved = sqlDetails{
		success: make([]*structs.CompatibleDetail, 0),
		failed:  make([]*structs.CompatibleDetail, 0),
		timeout: make([]*structs.CompatibleDetail, 0),
		other:   make([]*structs.CompatibleDetail, 0),
	}

	// 如果没有数据，直接返回
	if len(allDetails) == 0 {
		return result
	}

	// 构建用户操作查询键
	keys := make([]sqlanalyzer.SqlStmtUserOperationV2Key, 0, len(allDetails))
	for _, detail := range allDetails {
		keys = append(keys, sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               int(detail.TaskID),
			OraSqlId:             detail.SQLID,
			OraParsingSchemaName: detail.ParsingSchemaName,
		})
	}

	// 批量获取用户操作数据
	userOpsMap, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtUserOperationV2ByKeys(ctx, i.taskId, keys)
	if err != nil {
		log.Errorf("get user operations v2 failed, taskId:%d, err: %v", i.taskId, err)
		userOpsMap = make(map[string]*sqlanalyzer.SqlStmtUserOperationV2)
	}

	// 合并数据并按状态分类
	for _, detail := range allDetails {
		// 查找对应的用户操作数据
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               int(detail.TaskID),
			OraSqlId:             detail.SQLID,
			OraParsingSchemaName: detail.ParsingSchemaName,
		}

		// 设置默认状态
		userOperateStatus := constants.UserOperateStatusNormal

		// 设置用户操作数据到Oracle数据结构中
		if userOp, exists := userOpsMap[key.String()]; exists && userOp != nil {
			detail.UserOperateStatus = userOp.UserOperateStatus
			detail.UserOperateBy = userOp.UserOperateBy
			detail.UserOperateAt = userOp.UserOperateAt
			detail.UserOperateRemark = userOp.UserOperateRemark
			userOperateStatus = userOp.UserOperateStatus
		} else {
			detail.UserOperateStatus = userOperateStatus
		}

		// 转换为V1格式的CompatibleDetail
		v1Detail := i.convertOracleDataToV1Detail(detail)

		// 按用户操作状态和replay状态分类
		switch userOperateStatus {
		case constants.UserOperateStatusIgnored:
			i.categorizeByReplayStatusV1(&result.ignored, v1Detail, detail.ReplayStatus)
		case constants.UserOperateStatusResolved:
			i.categorizeByReplayStatusV1(&result.resolved, v1Detail, detail.ReplayStatus)
		default:
			i.categorizeByReplayStatusV1(&result.normal, v1Detail, detail.ReplayStatus)
		}
	}

	return result
}

// convertOracleDataToV1Detail 将Oracle数据转换为V1格式的CompatibleDetail
func (i *SQLReporter) convertOracleDataToV1Detail(stmt datasource.SQLSetStatement) *structs.CompatibleDetail {
	return &structs.CompatibleDetail{
		OraSqlId:             stmt.SQLID,
		OraParsingSchemaName: stmt.ParsingSchemaName,
		TidbExecCode:         stmt.ReplayExecCode,
		TidbSqlText:          cleanSQLText(stmt.RewriteSQLText),
		OraModule:            stmt.Module,
		TidbElapsedTimeMs:    0, // Oracle数据没有TiDB执行时间
		TidbEngineTimeMs:     0, // Oracle数据没有TiDB引擎时间
		OraExecutions:        int(stmt.Executions),
		OraElapsedTimeMs:     float64(stmt.ElapsedTime),
		OraLastExecStartTime: "", // 需要从Oracle数据中获取
		OraSqlText:           cleanSQLText(stmt.SQLText),
	}
}

// categorizeByReplayStatusV1 按replay状态分类V1版本的SQL详情
func (i *SQLReporter) categorizeByReplayStatusV1(details *sqlDetails, v1Detail *structs.CompatibleDetail, replayStatus string) {
	switch replayStatus {
	case "success":
		details.success = append(details.success, v1Detail)
	case "failed":
		details.failed = append(details.failed, v1Detail)
	case "timeout":
		details.timeout = append(details.timeout, v1Detail)
	default:
		details.other = append(details.other, v1Detail)
	}
}

// processSQLDetailsByStatus 按状态处理SQL详情数据
func (i *SQLReporter) processSQLDetailsByStatus(data []*sqlanalyzer.SqlStmtExecResult) sqlDetails {
	var details sqlDetails
	for _, v := range data {
		detail := i.createCompatibleDetail(v)
		switch v.TidbExecStatus {
		case constants.TiDBExplainSuccess:
			details.success = append(details.success, detail)
		case constants.TiDBExplainFailed:
			details.failed = append(details.failed, detail)
		case constants.TiDBExplainTimeout:
			details.timeout = append(details.timeout, detail)
		default:
			details.other = append(details.other, detail)
		}
	}
	return details
}

// performanceResults 性能数据结果
type performanceResults struct {
	better []*structs.PerformanceBetter
	worse  []*structs.PerformanceWorse
}

type performanceResultsWithUserOperations struct {
	normal   performanceResults
	ignored  performanceResults
	resolved performanceResults
}

// fetchAndProcessPerformanceDataWithUserOperations 获取并处理包含用户操作状态的性能数据
func (i *SQLReporter) fetchAndProcessPerformanceDataWithUserOperations(ctx context.Context) (performanceResultsWithUserOperations, error) {
	// Get task and channel information to access Oracle data
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, i.taskId)
	if err != nil {
		log.Errorf("get task info failed, taskId:%v, err: %v", i.taskId, err)
		return performanceResultsWithUserOperations{}, err
	}

	param, buildErr := BuildSQLAnalyzeParameter(ctx, taskInfo.ChannelId, i.taskId)
	if buildErr != nil {
		log.Errorf("build sql analyze parameter failed, err: %v", buildErr)
		return performanceResultsWithUserOperations{}, buildErr
	}

	sqlsetName := param.SQLFactor.GetSQLSetName()
	sqlsetOwner := param.GetOracleConnStr().GetUpperUser()
	schemas := param.GetIncludingSchemasBySQLSource()

	channelInfo, getChannelErr := models.GetChannelReaderWriter().GetChannel(ctx, taskInfo.ChannelId)
	if getChannelErr != nil {
		log.Errorf("get channel info failed, channelId:%v, err: %v", taskInfo.ChannelId, getChannelErr)
		return performanceResultsWithUserOperations{}, getChannelErr
	}

	oracleConn, _, setUpErr := common.SetUpSourceDatabaseConnFunc(ctx, channelInfo)
	if setUpErr != nil {
		log.Errorf("set up source database conn failed, taskId:%v, err: %v", i.taskId, setUpErr)
		return performanceResultsWithUserOperations{}, fmt.Errorf("failed to set up source database conn: %w", setUpErr)
	}

	// Get Oracle data from source database (only successful ones for performance analysis)
	allDetails, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsListByReplayStatus(ctx, oracleConn.OracleDB, i.taskId, sqlsetName, sqlsetOwner, "success", schemas)
	if getErr != nil {
		log.Errorf("get successful sql set statements failed, taskId:%v, err: %v", i.taskId, getErr)
		return performanceResultsWithUserOperations{}, getErr
	}

	// Merge Oracle data with user operations and convert to V1 performance format
	return i.mergeOracleDataWithUserOperationsForPerformance(ctx, allDetails), nil
}

// mergeOracleDataWithUserOperationsForPerformance 合并Oracle数据与用户操作数据并转换为V1性能格式
func (i *SQLReporter) mergeOracleDataWithUserOperationsForPerformance(ctx context.Context, allDetails []datasource.SQLSetStatement) performanceResultsWithUserOperations {
	var result performanceResultsWithUserOperations

	// 初始化各个分类
	result.normal = performanceResults{
		better: make([]*structs.PerformanceBetter, 0),
		worse:  make([]*structs.PerformanceWorse, 0),
	}
	result.ignored = performanceResults{
		better: make([]*structs.PerformanceBetter, 0),
		worse:  make([]*structs.PerformanceWorse, 0),
	}
	result.resolved = performanceResults{
		better: make([]*structs.PerformanceBetter, 0),
		worse:  make([]*structs.PerformanceWorse, 0),
	}

	// 对于性能数据，我们需要从metadb获取TiDB执行结果
	// 因为Oracle数据没有TiDB的性能信息
	allSuccessData, err := models.GetSqlAnalyzerReaderWriter().ListSqlStmtExecResultBySchemaAndTopN(ctx,
		i.taskId, i.schemas, []string{constants.TiDBExplainSuccess}, 1000)
	if err != nil {
		log.Errorf("get successful SQL execution results failed, taskId:%d, err: %v", i.taskId, err)
		return result
	}

	// 如果没有数据，直接返回
	if len(allSuccessData) == 0 {
		return result
	}

	// 构建用户操作查询键
	keys := make([]sqlanalyzer.SqlStmtUserOperationV2Key, 0, len(allSuccessData))
	for _, data := range allSuccessData {
		keys = append(keys, sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               data.TaskId,
			OraSqlId:             data.OraSqlId,
			OraParsingSchemaName: data.OraParsingSchemaName,
		})
	}

	// 批量获取用户操作数据
	userOpsMap, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtUserOperationV2ByKeys(ctx, i.taskId, keys)
	if err != nil {
		log.Errorf("get user operations v2 failed, taskId:%d, err: %v", i.taskId, err)
		userOpsMap = make(map[string]*sqlanalyzer.SqlStmtUserOperationV2)
	}

	// 合并数据并按状态分类
	for _, sqlResult := range allSuccessData {
		// 查找对应的用户操作数据
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               sqlResult.TaskId,
			OraSqlId:             sqlResult.OraSqlId,
			OraParsingSchemaName: sqlResult.OraParsingSchemaName,
		}

		// 设置默认状态
		userOperateStatus := constants.UserOperateStatusNormal

		// 设置用户操作数据
		if userOp, exists := userOpsMap[key.String()]; exists && userOp != nil {
			userOperateStatus = userOp.UserOperateStatus
		}

		// 处理性能数据
		perfData := i.processPerformanceDataItem(sqlResult)
		if perfData != nil {
			// 按用户操作状态分类
			switch userOperateStatus {
			case constants.UserOperateStatusIgnored:
				i.categorizeByPerformanceV1(&result.ignored, perfData)
			case constants.UserOperateStatusResolved:
				i.categorizeByPerformanceV1(&result.resolved, perfData)
			default:
				i.categorizeByPerformanceV1(&result.normal, perfData)
			}
		}
	}

	return result
}

// processPerformanceDataItem 处理单个性能数据项
func (i *SQLReporter) processPerformanceDataItem(v *sqlanalyzer.SqlStmtExecResult) interface{} {
	if v.TidbExecStatus != constants.TiDBExplainSuccess {
		return nil
	}

	sqlText := ""
	if len(v.OraSqlText) > 300 {
		sqlText = cleanSQLText(v.OraSqlText[1:300])
	} else {
		sqlText = cleanSQLText(v.OraSqlText)
	}

	if v.OraElapsedTimeMs <= v.TidbEngineTimeMs {
		return &structs.PerformanceBetter{
			OraSqlId:           v.OraSqlId,
			OraModule:          v.OraModule,
			OraExecutions:      v.OraExecutions,
			OraElapsedTimeMs:   v.OraElapsedTimeMs,
			TidbElapsedTimeMs:  v.TidbElapsedTimeMs,
			TidbEngineTimeMs:   v.TidbEngineTimeMs,
			ElapsedTimeDiffMs:  v.TidbEngineTimeMs - v.OraElapsedTimeMs,
			ElapsedTimeDiffPct: fmt.Sprintf("%.2f%%", float64(v.TidbEngineTimeMs-v.OraElapsedTimeMs)/float64(v.OraElapsedTimeMs)*100),
			OraSqlText:         sqlText,
			TidbPlan:           v.TidbPlan,
		}
	} else {
		return &structs.PerformanceWorse{
			OraSqlId:           v.OraSqlId,
			OraModule:          v.OraModule,
			OraExecutions:      v.OraExecutions,
			OraElapsedTimeMs:   v.OraElapsedTimeMs,
			TidbElapsedTimeMs:  v.TidbElapsedTimeMs,
			TidbEngineTimeMs:   v.TidbEngineTimeMs,
			ElapsedTimeDiffMs:  v.TidbEngineTimeMs - v.OraElapsedTimeMs,
			ElapsedTimeDiffPct: fmt.Sprintf("%.2f%%", float64(v.TidbEngineTimeMs-v.OraElapsedTimeMs)/float64(v.OraElapsedTimeMs)*100),
			OraSqlText:         sqlText,
			TidbPlan:           v.TidbPlan,
		}
	}
}

// categorizeByPerformanceV1 按性能分类V1版本的性能数据
func (i *SQLReporter) categorizeByPerformanceV1(results *performanceResults, perfData interface{}) {
	switch data := perfData.(type) {
	case *structs.PerformanceBetter:
		results.better = append(results.better, data)
	case *structs.PerformanceWorse:
		results.worse = append(results.worse, data)
	}
}

// processPerformanceData 处理性能数据
func (i *SQLReporter) processPerformanceData(data []*sqlanalyzer.SqlStmtExecResult) performanceResults {
	var results performanceResults

	for _, v := range data {
		if v.TidbExecStatus != constants.TiDBExplainSuccess {
			continue
		}

		sqlText := ""
		if len(v.OraSqlText) > 300 {
			sqlText = cleanSQLText(v.OraSqlText[1:300])
		} else {
			sqlText = cleanSQLText(v.OraSqlText)
		}

		if v.OraElapsedTimeMs <= v.TidbEngineTimeMs {
			results.better = append(results.better, &structs.PerformanceBetter{
				OraSqlId:           v.OraSqlId,
				OraModule:          v.OraModule,
				OraExecutions:      v.OraExecutions,
				OraElapsedTimeMs:   v.OraElapsedTimeMs,
				TidbElapsedTimeMs:  v.TidbElapsedTimeMs,
				TidbEngineTimeMs:   v.TidbEngineTimeMs,
				ElapsedTimeDiffMs:  v.TidbEngineTimeMs - v.OraElapsedTimeMs,
				ElapsedTimeDiffPct: fmt.Sprintf("%.2f%%", float64(v.TidbEngineTimeMs-v.OraElapsedTimeMs)/float64(v.OraElapsedTimeMs)*100),
				OraSqlText:         sqlText,
				TidbPlan:           v.TidbPlan,
			})
		} else {
			results.worse = append(results.worse, &structs.PerformanceWorse{
				OraSqlId:           v.OraSqlId,
				OraModule:          v.OraModule,
				OraExecutions:      v.OraExecutions,
				OraElapsedTimeMs:   v.OraElapsedTimeMs,
				TidbElapsedTimeMs:  v.TidbElapsedTimeMs,
				TidbEngineTimeMs:   v.TidbEngineTimeMs,
				ElapsedTimeDiffMs:  v.TidbEngineTimeMs - v.OraElapsedTimeMs,
				ElapsedTimeDiffPct: fmt.Sprintf("%.2f%%", float64(v.TidbEngineTimeMs-v.OraElapsedTimeMs)/float64(v.OraElapsedTimeMs)*100),
				OraSqlText:         sqlText,
				TidbPlan:           v.TidbPlan,
			})
		}
	}

	return results
}

// sqlDetailsV2 用于存储按状态分类的V2版本SQL详情
type sqlDetailsV2 struct {
	failed    datasource.SQLSetStatements
	success   datasource.SQLSetStatements
	timeout   datasource.SQLSetStatements
	notReplay datasource.SQLSetStatements
	aiFix     datasource.SQLSetStatements
}

// sqlDetailsWithUserOperationsV2 包含用户操作状态的V2版本SQL详情
type sqlDetailsWithUserOperationsV2 struct {
	normal   sqlDetailsV2
	ignored  sqlDetailsV2
	resolved sqlDetailsV2
}

// mergeOracleDataWithUserOperationsV2 合并Oracle数据与用户操作数据
func (i *SQLReporter) mergeOracleDataWithUserOperationsV2(ctx context.Context, allDetails datasource.SQLSetStatements) sqlDetailsWithUserOperationsV2 {
	var result sqlDetailsWithUserOperationsV2

	// 初始化各个分类
	result.normal = sqlDetailsV2{
		failed:    make(datasource.SQLSetStatements, 0),
		success:   make(datasource.SQLSetStatements, 0),
		timeout:   make(datasource.SQLSetStatements, 0),
		notReplay: make(datasource.SQLSetStatements, 0),
		aiFix:     make(datasource.SQLSetStatements, 0),
	}
	result.ignored = sqlDetailsV2{
		failed:    make(datasource.SQLSetStatements, 0),
		success:   make(datasource.SQLSetStatements, 0),
		timeout:   make(datasource.SQLSetStatements, 0),
		notReplay: make(datasource.SQLSetStatements, 0),
		aiFix:     make(datasource.SQLSetStatements, 0),
	}
	result.resolved = sqlDetailsV2{
		failed:    make(datasource.SQLSetStatements, 0),
		success:   make(datasource.SQLSetStatements, 0),
		timeout:   make(datasource.SQLSetStatements, 0),
		notReplay: make(datasource.SQLSetStatements, 0),
		aiFix:     make(datasource.SQLSetStatements, 0),
	}

	// 如果没有数据，直接返回
	if len(allDetails) == 0 {
		return result
	}

	// 构建用户操作查询键
	keys := make([]sqlanalyzer.SqlStmtUserOperationV2Key, 0, len(allDetails))
	for _, detail := range allDetails {
		keys = append(keys, sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               int(detail.TaskID),
			OraSqlId:             detail.SQLID,
			OraParsingSchemaName: detail.ParsingSchemaName,
		})
	}

	// 批量获取用户操作数据
	userOpsMap, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtUserOperationV2ByKeys(ctx, i.taskId, keys)
	if err != nil {
		log.Errorf("get user operations v2 failed, taskId:%d, err: %v", i.taskId, err)
		userOpsMap = make(map[string]*sqlanalyzer.SqlStmtUserOperationV2)
	}

	// 合并数据并按状态分类
	for _, detail := range allDetails {
		// 查找对应的用户操作数据
		key := sqlanalyzer.SqlStmtUserOperationV2Key{
			TaskId:               int(detail.TaskID),
			OraSqlId:             detail.SQLID,
			OraParsingSchemaName: detail.ParsingSchemaName,
		}

		// 设置默认状态
		userOperateStatus := constants.UserOperateStatusNormal

		// 如果有用户操作数据，更新状态
		if userOp, exists := userOpsMap[key.String()]; exists && userOp != nil {
			detail.UserOperateStatus = userOp.UserOperateStatus
			detail.UserOperateBy = userOp.UserOperateBy
			detail.UserOperateAt = userOp.UserOperateAt
			detail.UserOperateRemark = userOp.UserOperateRemark
			userOperateStatus = userOp.UserOperateStatus
		} else {
			detail.UserOperateStatus = userOperateStatus
		}

		// 按用户操作状态分类
		switch userOperateStatus {
		case constants.UserOperateStatusIgnored:
			i.categorizeByReplayStatusV2(&result.ignored, detail)
		case constants.UserOperateStatusResolved:
			i.categorizeByReplayStatusV2(&result.resolved, detail)
		default:
			i.categorizeByReplayStatusV2(&result.normal, detail)
		}
	}

	return result
}

// categorizeByReplayStatusV2 按replay状态分类V2版本的SQL详情
func (i *SQLReporter) categorizeByReplayStatusV2(details *sqlDetailsV2, stmt datasource.SQLSetStatement) {
	switch stmt.ReplayStatus {
	case "failed":
		details.failed = append(details.failed, stmt)
	case "success":
		details.success = append(details.success, stmt)
	case "timeout":
		details.timeout = append(details.timeout, stmt)
	default:
		if stmt.Replay != "Y" {
			details.notReplay = append(details.notReplay, stmt)
		}
		// AI fix details would need additional logic based on specific criteria
	}
}

// cleanSQLText 清理SQL文本，移除空行和多余的换行符
func cleanSQLText(sqlText string) string {
	if sqlText == "" {
		return sqlText
	}
	
	// 将多个连续的换行符替换为单个换行符
	lines := strings.Split(sqlText, "\n")
	var cleanedLines []string
	for _, line := range lines {
		// 移除所有不可见字符，包括Unicode空白字符
		trimmedLine := strings.TrimSpace(line)
		
		// 额外处理其他可能的不可见字符
		// 包括：零宽空格(U+200B)、零宽非连接符(U+200C)、零宽连接符(U+200D)、字节顺序标记(U+FEFF)等
		trimmedLine = strings.ReplaceAll(trimmedLine, "\u200B", "") // 零宽空格
		trimmedLine = strings.ReplaceAll(trimmedLine, "\u200C", "") // 零宽非连接符
		trimmedLine = strings.ReplaceAll(trimmedLine, "\u200D", "") // 零宽连接符
		trimmedLine = strings.ReplaceAll(trimmedLine, "\uFEFF", "") // BOM
		trimmedLine = strings.ReplaceAll(trimmedLine, "\u00A0", " ") // 非断行空格转为普通空格
		
		// 只保留非空行或包含有效内容的行
		if trimmedLine != "" {
			cleanedLines = append(cleanedLines, line)
		}
	}
	
	return strings.Join(cleanedLines, "\n")
}

// createCompatibleDetail 创建兼容性详情
func (i *SQLReporter) createCompatibleDetail(v *sqlanalyzer.SqlStmtExecResult) *structs.CompatibleDetail {
	return &structs.CompatibleDetail{
		OraSqlId:             v.OraSqlId,
		OraParsingSchemaName: v.OraParsingSchemaName,
		TidbExecCode:         v.TidbExecCode,
		TidbSqlText:          cleanSQLText(v.TidbSqlText),
		OraModule:            v.OraModule,
		TidbElapsedTimeMs:    v.TidbElapsedTimeMs,
		TidbEngineTimeMs:     v.TidbEngineTimeMs,
		OraExecutions:        v.OraExecutions,
		OraElapsedTimeMs:     v.OraElapsedTimeMs,
		OraLastExecStartTime: v.OraLastExecStartTime,
		OraSqlText:           cleanSQLText(v.OraSqlText),
	}
}

// applyHtmlContentIntoTemplate 将指标数据应用到HTML模版中
func (i *SQLReporter) applyHtmlContentIntoTemplate(dataMap *structs.SqlAnalyzerReport, templateName string, templateFile string) error {
	file, err := os.OpenFile(i.reportFileName, os.O_WRONLY|os.O_CREATE|os.O_APPEND|os.O_TRUNC, 0666)
	if err != nil {
		log.Errorf("applyHtmlContentIntoTemplate, open html file failed, err:%v", err)
	}
	defer file.Close()

	log.Infof("applyHtmlContentIntoTemplate, parse templateFile: %s", templateFile)
	tf, err := template.ParseFiles(templateFile)
	if err != nil {
		log.Errorf("applyHtmlContentIntoTemplate, parse templateFile failed: %v", err)
		return fmt.Errorf("parse template failed: %v", err)
	}

	log.Infof("applyHtmlContentIntoTemplate, apply data into templateFile:%s", templateFile)
	err = tf.ExecuteTemplate(file, templateName, dataMap)
	if err != nil {
		log.Errorf("applyHtmlContentIntoTemplate, template FS Execute [report1] template HTML failed: %v", err)
		return fmt.Errorf("template FS Execute [report1] template HTML failed: %v", err)
	}
	return nil
}

// getSQLExecTableDataPerformance 获取SQL性能分析数据
func (i *SQLReporter) getSQLExecTableDataPerformance(ctx context.Context) (*structs.SqlAnalyzerReport, error) {
	performanceWithUserOps, err := i.fetchAndProcessPerformanceDataWithUserOperations(ctx)
	if err != nil {
		return nil, err
	}

	betterNums := len(performanceWithUserOps.normal.better)
	worseNums := len(performanceWithUserOps.normal.worse)

	sqlPerformanceOverview := make([]*structs.PerformanceOverview, 0)
	sqlPerformanceOverview = append(sqlPerformanceOverview, &structs.PerformanceOverview{
		Type:       "TOTAL:",
		WorseNums:  worseNums,
		BetterNums: betterNums,
	})

	return &structs.SqlAnalyzerReport{
		Title:               i.reportTitle,
		PerformanceOverview: sqlPerformanceOverview,
		PerformanceWorse:    performanceWithUserOps.normal.worse,
		PerformanceBetter:   performanceWithUserOps.normal.better,
		// Ignored sections
		IgnoredPerformanceWorse:  performanceWithUserOps.ignored.worse,
		IgnoredPerformanceBetter: performanceWithUserOps.ignored.better,
		// Resolved sections
		ResolvedPerformanceWorse:  performanceWithUserOps.resolved.worse,
		ResolvedPerformanceBetter: performanceWithUserOps.resolved.better,
	}, nil
}

// calculateIgnoredResolvedStatistics 计算ignored/resolved统计信息
func (i *SQLReporter) calculateIgnoredResolvedStatistics(
	totalSummaries datasource.SQLSetStatementsSummaries,
	failedSummaries datasource.SQLSetStatementsFailedSummaries,
	timeoutSummaries *datasource.SQLSetStatementsTimeoutSummaries,
	detailsWithUserOps sqlDetailsWithUserOperationsV2) {

	// 统计ignored和resolved数量
	ignoredCount := uint64(len(detailsWithUserOps.ignored.failed)) +
		uint64(len(detailsWithUserOps.ignored.success)) +
		uint64(len(detailsWithUserOps.ignored.timeout)) +
		uint64(len(detailsWithUserOps.ignored.notReplay))

	resolvedCount := uint64(len(detailsWithUserOps.resolved.failed)) +
		uint64(len(detailsWithUserOps.resolved.success)) +
		uint64(len(detailsWithUserOps.resolved.timeout)) +
		uint64(len(detailsWithUserOps.resolved.notReplay))

	// 统计按类型分组的ignored和resolved数量
	ignoredSuccessCount := uint64(len(detailsWithUserOps.ignored.success))
	ignoredFailedCount := uint64(len(detailsWithUserOps.ignored.failed))
	resolvedSuccessCount := uint64(len(detailsWithUserOps.resolved.success))
	resolvedFailedCount := uint64(len(detailsWithUserOps.resolved.failed))

	// 更新总体统计，添加ignored和resolved信息，并调整SuccessNum和FailedNum
	for idx := range totalSummaries {
		summary := &totalSummaries[idx]

		// 保存原始值
		originalSuccessNum := summary.SuccessNum
		originalFailedNum := summary.FailedNum

		// 设置ignored和resolved总数
		summary.IgnoredNum = ignoredCount
		summary.ResolvedNum = resolvedCount

		// 调整兼容和不兼容的数量，排除已处理的SQL
		// 兼容的SQL数目 = 原始兼容数 - 被忽略的兼容SQL - 被解决的兼容SQL
		summary.SuccessNum = originalSuccessNum - ignoredSuccessCount - resolvedSuccessCount

		// 不兼容的SQL数目 = 原始不兼容数 - 被忽略的不兼容SQL - 被解决的不兼容SQL
		summary.FailedNum = originalFailedNum - ignoredFailedCount - resolvedFailedCount

		// 确保数字不为负数
		if summary.SuccessNum < 0 {
			summary.SuccessNum = 0
		}
		if summary.FailedNum < 0 {
			summary.FailedNum = 0
		}
	}

	// 按错误码分组统计ignored/resolved失败SQL数量
	ignoredFailedByErrorCode := make(map[string]uint64)
	resolvedFailedByErrorCode := make(map[string]uint64)

	// 统计ignored的失败SQL按错误码分组
	for _, stmt := range detailsWithUserOps.ignored.failed {
		errorCode := stmt.ReplayExecCode
		ignoredFailedByErrorCode[errorCode]++
	}

	// 统计resolved的失败SQL按错误码分组
	for _, stmt := range detailsWithUserOps.resolved.failed {
		errorCode := stmt.ReplayExecCode
		resolvedFailedByErrorCode[errorCode]++
	}

	// 为每个failedSummary设置对应错误码的精确统计
	for idx := range failedSummaries {
		failedSummary := &failedSummaries[idx]
		errorCode := failedSummary.ReplayExecCode

		failedSummary.IgnoredNum = ignoredFailedByErrorCode[errorCode]
		failedSummary.ResolvedNum = resolvedFailedByErrorCode[errorCode]
	}

	// 更新超时统计，添加ignored/resolved信息
	if timeoutSummaries != nil {
		if len(*timeoutSummaries) == 0 {
			// 如果为空，创建一个
			timeoutSummary := datasource.SQLSetStatementsTimeoutSummary{
				TotalNum:           uint64(len(detailsWithUserOps.normal.timeout)),
				IgnoredTimeoutNum:  uint64(len(detailsWithUserOps.ignored.timeout)),
				ResolvedTimeoutNum: uint64(len(detailsWithUserOps.resolved.timeout)),
			}
			*timeoutSummaries = append(*timeoutSummaries, timeoutSummary)
		} else {
			// 更新现有的超时统计
			for idx := range *timeoutSummaries {
				timeoutSummary := &(*timeoutSummaries)[idx]
				timeoutSummary.IgnoredTimeoutNum = uint64(len(detailsWithUserOps.ignored.timeout))
				timeoutSummary.ResolvedTimeoutNum = uint64(len(detailsWithUserOps.resolved.timeout))
			}
		}
	}
}
