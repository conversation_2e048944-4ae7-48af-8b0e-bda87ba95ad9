package deploy

import (
	"context"
	"database/sql"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/pingcap/errors"
)

var defaultSQLAnalyzeDeployTasks = []EnvironmentDeployTask{
	// local mode
	checkPermission(constants.SQLAnalyzeTaskModeLocal),
	createSQLSet(constants.SQLAnalyzeTaskModeLocal),
	createSQLDataDictionary(constants.SQLAnalyzeTaskModeLocal),
	createCurrentSQLProgram(constants.SQLAnalyzeTaskModeLocal),
	createHistorySQLProgram(constants.SQLAnalyzeTaskModeLocal),
	createUserFunction(constants.SQLAnalyzeTaskModeLocal),
	createCurrentSQLJob(constants.SQLAnalyzeTaskModeLocal),
	createHistorySQLJob(constants.SQLAnalyzeTaskModeLocal),

	// not local mode
	checkPermission(constants.SQLAnalyzeTaskModeNotLocal),
	createSQLSet(constants.SQLAnalyzeTaskModeNotLocal),
	createSQLDataDictionary(constants.SQLAnalyzeTaskModeNotLocal),
	createCurrentSQLProgram(constants.SQLAnalyzeTaskModeNotLocal),
	createHistorySQLProgram(constants.SQLAnalyzeTaskModeNotLocal),
	createUserFunction(constants.SQLAnalyzeTaskModeNotLocal),
	createCurrentSQLJob(constants.SQLAnalyzeTaskModeNotLocal),
	createHistorySQLJob(constants.SQLAnalyzeTaskModeNotLocal),
}

func GetDefaultSQLAnalyzeDeployTasks() []EnvironmentDeployTask {
	localCounter := 1
	notLocalCounter := 1

	tasks := make([]EnvironmentDeployTask, len(defaultSQLAnalyzeDeployTasks))
	for idx, deployTask := range defaultSQLAnalyzeDeployTasks {
		if deployTask.TaskMode == constants.SQLAnalyzeTaskModeLocal {
			deployTask.TaskNumber = localCounter
			localCounter++
		} else {
			deployTask.TaskNumber = notLocalCounter
			notLocalCounter++
		}
		tasks[idx] = deployTask
	}
	return tasks
}

func checkPermission(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	t := EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCheckSourceDataAccessPermission,
		SQLModel:     constants.DeployModeCheckSQL,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameUpperCase,
		DeployJob: DeployJob{
			taskSQL: `select count(*) from dba_sys_privs a where a.privilege='CREATE JOB' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='CREATE ANY PROCEDURE' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='ADMINISTER SQL TUNING SET' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='EXECUTE ANY PROCEDURE' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='SELECT ANY DICTIONARY' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='SELECT ANY TABLE' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='CREATE PROCEDURE' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='UNLIMITED TABLESPACE' and a.grantee='${USERNAME}';
select count(*) from dba_sys_privs a where a.privilege='ADMINISTER ANY SQL TUNING SET' and a.grantee='${USERNAME}';`,
			execSQLFailureMessage: "权限检查失败",
			execSQLSuccessMessage: "权限检查通过",
		},
	}

	t.DeployPreCheckSteps = []DeployPreCheck{
		{
			preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
				username = strings.TrimSpace(strings.ToUpper(username))
				checkPrivilegeSQLs := preCheckSQLs
				for _, checkPrivilegeSQL := range checkPrivilegeSQLs {
					checkPrivilegeSQL = stringutil.FormatSingleSQLForOracle(checkPrivilegeSQL)
					checkPrivilegeSQL = strings.ReplaceAll(checkPrivilegeSQL, "${USERNAME}", username)
					checkPrivilegeSQL = strings.ReplaceAll(checkPrivilegeSQL, "${SQLSET_NAME}", param.GetSQLFactor().GetSQLSetName())
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkPrivilegeSQL), t.TaskName, t.SQLModel, t.UsernameCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkPrivilegeSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
						return false, errors.New("权限检查失败，结果为空")
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total == 0 {
						privilege := strings.ReplaceAll(checkPrivilegeSQL, `select count(*) from dba_sys_privs a where a.privilege='`, "")
						privilege = strings.ReplaceAll(privilege, `' and a.grantee='`+username+`'`, "")

						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("权限检查失败，缺少" + privilege)
						} else {
							return false, errors.New("权限检查失败，缺少" + privilege)
						}
					}
				}
				return false, nil
			},
			onSuccess: "权限检查通过",
			onFailure: "权限检查失败",
			onSkip:    "权限检查跳过",
		},
	}

	return t
}

func createSQLSet(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateSQLSet,
		SQLModel:     constants.DeployModeSQL,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameDefaultCase,
		DeployJob: DeployJob{
			execSQLFailureMessage: "SQLSET创建失败",
			execSQLSuccessMessage: "SQLSET创建成功",
			taskSQL:               `call dbms_sqltune.create_sqlset(sqlset_name => '${SQLSET_NAME}',sqlset_owner => upper('${USERNAME}'));`,
		},
		DeployPreCheckSteps: []DeployPreCheck{
			{
				preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
					username = strings.TrimSpace(strings.ToUpper(username))
					checkSQL := `select count(*) from dba_sqlset where name='${SQLSET_NAME}' and owner='` + username + `'`
					checkSQL = strings.ReplaceAll(checkSQL, "${SQLSET_NAME}", param.GetSQLFactor().GetSQLSetName())
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkSQL), constants.SQLAnalyzeCreateSQLSet, constants.DeployModeSQL, constants.UsernameDefaultCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
						return false, errors.New("SQLSET检查失败，结果为空")
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total == 1 {
						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("SQLSET已经创建，部署任务中止")
						} else {
							return true, errors.New("SQLSET已经创建，部署任务中止")
						}
					}
					return false, nil
				},
				onSuccess: "对象未创建，创建中...",
				onFailure: "",
				onSkip:    "当前用户下SQLSET已存在，该步骤跳过",
			},
		},
	}
}

func createSQLDataDictionary(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateDataDictionary,
		SQLModel:     constants.DeployModeSQLs,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameDefaultCase,
		DeployJob: DeployJob{
			execSQLFailureMessage: "TMS数据字典表创建失败",
			execSQLSuccessMessage: "TMS数据字典表创建成功",
			ignoreDBErrorCode:     []string{"ORA-00955", "ORA-02260"},
			taskSQL: `CREATE TABLE TMS_SQLSET
(
  task_id		  NUMBER not null,
  name            VARCHAR2(128) not null,
  owner           VARCHAR2(512),
  description     VARCHAR2(256),
  created         DATE,
  last_modified   DATE,
  statement_count NUMBER
);
CREATE INDEX IND_TMS_SQLSET_1 ON TMS_SQLSET (TASK_ID, NAME, OWNER);
CREATE TABLE TMS_SQLSET_BAK
(
  task_id		  NUMBER not null,
  name            VARCHAR2(128) not null,
  owner           VARCHAR2(512),
  description     VARCHAR2(256),
  created         DATE,
  last_modified   DATE,
  statement_count NUMBER
);
CREATE INDEX IND_TMS_SQLSET_BAK_1 ON TMS_SQLSET_BAK (TASK_ID, NAME, OWNER);
CREATE TABLE TMS_SQLSET_STATEMENTS
(
  task_id		      NUMBER not null,
  sqlset_name         VARCHAR2(128) not null,
  sqlset_owner        VARCHAR2(512),
  sqlset_id           NUMBER not null,
  digest_id           VARCHAR2(64),
  sql_id              VARCHAR2(13) not null,
  sql_text            CLOB,
  parsing_schema_name VARCHAR2(128),
  plan_hash_value     NUMBER not null,
  bind_data           RAW(2000),
  command_type        NUMBER not null,
  module              VARCHAR2(64),
  action              VARCHAR2(64),
  elapsed_time        NUMBER,
  executions          NUMBER,
  replay              CHAR(1),
  replay_status       VARCHAR2(10),
  replay_finish_time  DATE,
  replay_exec_code    VARCHAR2(20),
  rewrite_sql_text    CLOB,
  rewrite_prompts     VARCHAR(1000),
  message_log         VARCHAR2(2000),
  last_copy_time      DATE
);
CREATE INDEX IND_SQLSET_1 ON TMS_SQLSET_STATEMENTS (TASK_ID, SQLSET_NAME, SQLSET_OWNER, SQL_ID);
CREATE INDEX IND_SQLSET_2 ON TMS_SQLSET_STATEMENTS (DIGEST_ID);
CREATE INDEX IND_SQLSET_3 ON TMS_SQLSET_STATEMENTS (SQL_ID);
CREATE INDEX IND_SQLSET_4 ON TMS_SQLSET_STATEMENTS (PLAN_HASH_VALUE);
CREATE INDEX IND_SQLSET_5 ON TMS_SQLSET_STATEMENTS (EXECUTIONS);
CREATE INDEX IND_SQLSET_6 ON TMS_SQLSET_STATEMENTS (PARSING_SCHEMA_NAME);
CREATE INDEX IND_SQLSET_7 ON TMS_SQLSET_STATEMENTS (SQLSET_NAME, SQLSET_OWNER, SQLSET_ID, LAST_COPY_TIME);
CREATE TABLE TMS_SQLSET_STATEMENTS_BAK
(
  task_id		      NUMBER not null,
  sqlset_name         VARCHAR2(128) not null,
  sqlset_owner        VARCHAR2(512),
  sqlset_id           NUMBER not null,
  digest_id           VARCHAR2(64),
  sql_id              VARCHAR2(13) not null,
  sql_text            CLOB,
  parsing_schema_name VARCHAR2(128),
  plan_hash_value     NUMBER not null,
  bind_data           RAW(2000),
  command_type        NUMBER not null,
  module              VARCHAR2(64),
  action              VARCHAR2(64),
  elapsed_time        NUMBER,
  executions          NUMBER,
  replay              CHAR(1),
  replay_status       VARCHAR2(10),
  replay_finish_time  DATE,
  replay_exec_code    VARCHAR2(20),
  rewrite_sql_text    CLOB,
  rewrite_prompts     VARCHAR(1000),
  message_log         VARCHAR2(2000),
  last_copy_time      DATE
);
CREATE INDEX IND_SQLSET_1 ON TMS_SQLSET_STATEMENTS_BAK (TASK_ID, SQLSET_NAME, SQLSET_OWNER, SQL_ID);
CREATE INDEX IND_SQLSET_2 ON TMS_SQLSET_STATEMENTS_BAK (DIGEST_ID);
CREATE INDEX IND_SQLSET_3 ON TMS_SQLSET_STATEMENTS_BAK (SQL_ID);
CREATE INDEX IND_SQLSET_4 ON TMS_SQLSET_STATEMENTS_BAK (PLAN_HASH_VALUE);
CREATE INDEX IND_SQLSET_5 ON TMS_SQLSET_STATEMENTS_BAK (EXECUTIONS);
CREATE INDEX IND_SQLSET_6 ON TMS_SQLSET_STATEMENTS_BAK (PARSING_SCHEMA_NAME);
CREATE INDEX IND_SQLSET_7 ON TMS_SQLSET_STATEMENTS_BAK (SQLSET_NAME, SQLSET_OWNER, SQLSET_ID, LAST_COPY_TIME);`,
		},
		DeployPreCheckSteps: []DeployPreCheck{
			{
				preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
					checkSQL := `SELECT COUNT(*) FROM USER_TABLES WHERE TABLE_NAME IN ('TMS_SQLSET','TMS_SQLSET_BAK','TMS_SQLSET_STATEMENTS','TMS_SQLSET_STATEMENTS_BAK')`
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkSQL), constants.SQLAnalyzeCreateSQLSet, constants.DeployModeSQL, constants.UsernameDefaultCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
						return false, errors.New("TMS数据字典表检查失败，结果为空")
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total == 4 {
						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("TMS数据字典表已经创建，部署任务中止")
						} else {
							return true, errors.New("TMS数据字典表已经创建，部署任务中止")
						}
					}
					return false, nil
				},
				onSuccess: "对象未创建，创建中...",
				onFailure: "",
				onSkip:    "当前用户下TMS数据字典表已存在，该步骤跳过",
			},
		},
	}
}

func createCurrentSQLProgram(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateGetCurrentSQLProgram,
		SQLModel:     constants.DeployModeProcedure,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameDefaultCase,
		DeployPreCheckSteps: []DeployPreCheck{
			{
				preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
					username = strings.TrimSpace(strings.ToUpper(username))
					checkSQL := `select count(*) from all_objects where object_name='SPA_SQLCOLLECT_CURSOR' and owner='` + username + `'`
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkSQL), constants.SQLAnalyzeCreateGetCurrentSQLProgram, constants.DeployModeProcedure, constants.UsernameDefaultCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
						return false, errors.New("对象检查失败，结果为空")
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total == 1 {
						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("对象已经创建，部署任务中止")
						} else {
							return true, errors.New("对象已经创建，部署任务中止")
						}
					}
					return false, nil
				},
				onSuccess: "对象未创建，创建中...",
				onFailure: "对象已创建",
				onSkip:    "当前用户下SQL程序已存在，该步骤跳过",
			},
		},
		DeployJob: DeployJob{
			validateParam:         true,
			execSQLSuccessMessage: "对象创建成功",
			execSQLFailureMessage: "创建对象失败",
			taskSQL: `create or replace procedure spa_sqlcollect_cursor as
    l_cursor DBMS_SQLTUNE.sqlset_cursor;
BEGIN
    OPEN l_cursor FOR
        SELECT VALUE(a)
        FROM TABLE (DBMS_SQLTUNE.select_cursor_cache('parsing_schema_name in ('VARSCHEMA')', null, null, null, null, 1,
                                                     null, 'ALL')) a;
    DBMS_SQLTUNE.load_sqlset(sqlset_name => '${SQLSET_NAME}',
                             populate_cursor => l_cursor,
                             load_option => 'MERGE',
                             commit_rows => 10);
    close l_cursor;
    commit;
Exception
    when others then
        raise;
END;`,
		},
	}
}

func createHistorySQLProgram(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateGetHistorySQLProgram,
		SQLModel:     constants.DeployModeProcedure,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameDefaultCase,
		DeployPreCheckSteps: []DeployPreCheck{
			{
				preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
					username = strings.TrimSpace(strings.ToUpper(username))
					checkSQL := `select count(*) from all_objects where object_name='SPA_SQLCOLLECT_AWR' and owner='` + username + `'`
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkSQL), constants.SQLAnalyzeCreateGetHistorySQLProgram, constants.DeployModeProcedure, constants.UsernameDefaultCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total == 1 {
						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("对象已经创建，部署任务中止")
						} else {
							return true, errors.New("对象已经创建，部署任务中止")
						}
					}
					return false, nil
				},
				onSuccess: "对象未创建，创建中...",
				onFailure: "对象已创建",
				onSkip:    "当前用户下SQL程序已存在，该步骤跳过",
			},
		},
		DeployJob: DeployJob{
			validateParam:         true,
			execSQLSuccessMessage: "对象创建成功",
			execSQLFailureMessage: "创建对象失败",
			taskSQL: `create or replace procedure spa_sqlcollect_awr as
  l_cursor DBMS_SQLTUNE.sqlset_cursor;
BEGIN
  FOR DP IN (select snapst, snaped, bit, eit
               from (select snap_id as snaped,
                            lag(snap_id, 1) OVER(ORDER BY snap_id) as snapst,
                            to_char(begin_interval_time, 'yyyymmddhh24miss') as bit,
                            to_char(end_interval_time, 'yyyymmddhh24miss') as eit
                       from dba_hist_snapshot
                       where end_interval_time>trunc(sysdate)-7) 
              where (snapst is not null) and (snaped-snapst)=1
              order by snapst) LOOP
    OPEN l_cursor FOR
      SELECT VALUE(a)
        FROM TABLE(dbms_sqltune.select_workload_repository(DP.snapst,
                                                           DP.snaped,
                                                           'parsing_schema_name in ('VARSCHEMA')',
                                                           NULL,
                                                           NULL,
                                                           NULL,
                                                           NULL,
                                                           1,
                                                           NULL,
                                                           'ALL')) a;
 
    DBMS_SQLTUNE.load_sqlset(sqlset_name     => '${SQLSET_NAME}',
                             populate_cursor => l_cursor,
                             load_option     => 'MERGE',
                             commit_rows     => 10);
    close l_cursor;
    commit;
  END LOOP;
Exception
  when others then
    raise;
END;`,
		},
	}
}

func createUserFunction(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateUserFunction,
		SQLModel:     constants.DeployModeProcedure,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameDefaultCase,
		DeployJob: DeployJob{
			execSQLFailureMessage: "自定义函数创建失败",
			execSQLSuccessMessage: "自定义函数创建成功",
			taskSQL: `create or replace function getData( p_x in sys.anyData )
return varchar2
as
l_num number;
l_date date;
l_ts timestamp;
l_varchar2 varchar2(4000);
begin
 case p_x.gettypeName
    when 'SYS.NUMBER' then
       if ( p_x.getNumber( l_num ) = dbms_types.success )
          then
         l_varchar2 := l_num;
       end if;
    when 'SYS.DATE' then
    if ( p_x.getDate( l_date ) = dbms_types.success )
      then
            l_varchar2 := ''''||l_date||'''';
       end if;
          when 'SYS.TIMESTAMP' then
    if ( p_x.getTIMESTAMP( l_ts ) = dbms_types.success )
      then
            l_varchar2 := ''''||l_ts||'''';
       end if;
     when 'SYS.VARCHAR2' then
      if ( p_x.getVarchar2( l_varchar2 ) = dbms_types.success )
         then
             l_varchar2 := ''''||l_varchar2||'''';
       end if;
    else
        l_varchar2 := '''** unknown **''';
  end case;
    return l_varchar2;
 end;`,
		},
	}
}

func createCurrentSQLJob(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateAutoGetCurrentSQLJob,
		SQLModel:     constants.DeployModeProcedure,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameLowerCase,
		DeployPreCheckSteps: []DeployPreCheck{
			{
				preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
					username = strings.TrimSpace(strings.ToLower(username))
					checkSQL := `select count(*) from dba_jobs where what like '%` + username + `.spa_sqlcollect_cursor%'`
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkSQL), constants.SQLAnalyzeCreateAutoGetCurrentSQLJob, constants.DeployModeProcedure, constants.UsernameLowerCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
						return false, errors.New("对象检查失败，结果为空")
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total >= 1 {
						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("对象已经创建，部署任务中止")
						} else {
							return true, errors.New("对象已经创建，部署任务中止")
						}
					}
					return false, nil
				},
				onSuccess: "对象未创建，创建中...",
				onFailure: "对象已创建",
				onSkip:    "当前用户下JOB已存在，该步骤跳过",
			},
		},
		DeployJob: DeployJob{
			FetchJobSQL:           "select job from dba_jobs where what like '%${USERNAME}.spa_sqlcollect_cursor%' order by job desc",
			execSQLSuccessMessage: "对象创建成功",
			execSQLFailureMessage: "创建对象或获取对象ID失败",
			taskSQL: `DECLARE JOB NUMBER;
BEGIN
     DBMS_JOB.SUBMIT(
          JOB=>JOB,
          WHAT=>'${USERNAME}.spa_sqlcollect_cursor;',
          NEXT_DATE=>sysdate+1/48,
          INTERVAL=>'sysdate+1/48');
COMMIT;
END;`,
		},
	}
}

func createHistorySQLJob(taskMode constants.SQLAnalyzeTaskMode) EnvironmentDeployTask {
	return EnvironmentDeployTask{
		TaskName:     constants.SQLAnalyzeCreateAutoGetHistorySQLJob,
		SQLModel:     constants.DeployModeProcedure,
		TaskMode:     taskMode,
		UsernameCase: constants.UsernameLowerCase,
		DeployPreCheckSteps: []DeployPreCheck{
			{
				preCheckFunc: func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error) {
					username = strings.TrimSpace(strings.ToLower(username))
					checkSQL := `select count(*) from dba_jobs where what like '%` + username + `.spa_sqlcollect_awr%'`
					log.Infof("exec precheck sql:%s, taskName:%s, sqlModel:%s, usernameCase:%v", stringutil.RemoveSpecialLetterForLog(checkSQL), constants.SQLAnalyzeCreateAutoGetHistorySQLJob, constants.DeployModeProcedure, constants.UsernameLowerCase)
					_, rets, err := oracle.Query(ctx, oracleConn, checkSQL)
					if err != nil {
						return false, err
					}
					if len(rets) == 0 {
						return false, errors.New("对象检查失败，结果为空")
					}
					total, _ := strconv.Atoi(rets[0]["COUNT(*)"])
					if total >= 1 {
						if runMode == constants.ExecuteRunModeRun {
							return false, errors.New("对象已经创建，部署任务中止")
						} else {
							return true, errors.New("对象已经创建，部署任务中止")
						}
					}
					return false, nil
				},
				onSuccess: "对象未创建，创建中...",
				onFailure: "对象已创建",
				onSkip:    "当前用户下JOB已存在，该步骤跳过",
			},
		},
		DeployJob: DeployJob{
			FetchJobSQL:           "select job from dba_jobs where what like '%${USERNAME}.spa_sqlcollect_awr%' order by job desc",
			execSQLSuccessMessage: "对象创建成功",
			execSQLFailureMessage: "创建对象或获取对象ID失败",
			taskSQL: `DECLARE JOB NUMBER;
BEGIN
     DBMS_JOB.SUBMIT(
          JOB=>JOB,
          WHAT=>'${USERNAME}.spa_sqlcollect_awr;',
          NEXT_DATE=>sysdate+1/2,
          INTERVAL=>'sysdate+1/2');
COMMIT;
END;`,
		},
	}
}
