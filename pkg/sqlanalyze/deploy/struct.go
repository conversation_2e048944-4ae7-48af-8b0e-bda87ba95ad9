package deploy

import (
	"context"
	"database/sql"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
)

type EnvironmentDeployTask struct {
	TaskNumber          int
	TaskName            string
	TaskMode            constants.SQLAnalyzeTaskMode
	SQLModel            string
	DeployPreCheckSteps []DeployPreCheck
	DeployJob           DeployJob
	UsernameCase        int
}

func (i EnvironmentDeployTask) GetDeployPreCheckSteps() []DeployPreCheck {
	return i.DeployPreCheckSteps
}

func (i EnvironmentDeployTask) GetTaskSQL() string {
	return i.DeployJob.taskSQL
}

func (i EnvironmentDeployTask) GetIgnoreDBErrorCodes() []string {
	return i.DeployJob.ignoreDBErrorCode
}

func (i EnvironmentDeployTask) ShouldIgnoreError(err error) bool {
	errMessage := err.Error()

	for _, code := range i.DeployJob.ignoreDBErrorCode {
		if strings.Contains(errMessage, code) {
			return true
		}
	}
	return false
}

func (i EnvironmentDeployTask) GetFetchJobSQL(username string) string {
	un := username
	if i.UsernameCase == constants.UsernameLowerCase {
		un = strings.ToLower(username)
	} else if i.UsernameCase == constants.UsernameUpperCase {
		un = strings.ToUpper(username)
	}
	return strings.ReplaceAll(i.DeployJob.FetchJobSQL, common.VAR_USERNAME, un)
}

func (i EnvironmentDeployTask) GetExecSQLFailureMessage() string {
	return i.DeployJob.execSQLFailureMessage
}

func (i EnvironmentDeployTask) GetSqlSuccessMessage() string {
	return i.DeployJob.execSQLSuccessMessage
}

func (i EnvironmentDeployTask) HasToValidateParam() bool {
	return i.DeployJob.validateParam
}

func (i EnvironmentDeployTask) HasPreCheckSQLs() bool {
	return len(i.DeployPreCheckSteps) > 0
}

type DeployPreCheck struct {
	onSuccess string
	onFailure string
	onSkip    string

	preCheckFunc func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error)
}

type PreCheckFunc func(ctx context.Context, oracleConn *sql.DB, runMode int, username string, preCheckSQLs []string, param *structs.Parameter) (shouldSkip bool, err error)

func (i DeployPreCheck) GetPreCheckFunc() PreCheckFunc {
	return i.preCheckFunc
}

func (i DeployPreCheck) GetPreCheckSuccessMessage() string {
	return i.onSuccess
}

func (i DeployPreCheck) GetPreCheckSkipMessage() string {
	return i.onSkip
}

func (i DeployPreCheck) GetPreCheckFailureMessage() string {
	return i.onFailure
}

type DeployJob struct {
	taskSQL               string
	FetchJobSQL           string
	execSQLFailureMessage string
	execSQLSuccessMessage string

	validateParam     bool
	ignoreDBErrorCode []string
}
