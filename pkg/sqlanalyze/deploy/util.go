package deploy

import (
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	"github.com/samber/lo"
	"strings"
)

// SubstituteSQL replaces placeholders in SQL with provided parameters and returns processed SQL statements.
func SubstituteSQL(task *sqlanalyzer.EnvDeployTask, parameter *structs.Parameter, deployTask EnvironmentDeployTask) []string {
	// Prepare grantee with quotes and uppercase username
	username := parameter.GetUsername()
	grantee := "'" + strings.ToUpper(username) + "'"

	// Process schemas: trim, filter, and format
	schemas := lo.Filter(lo.Map(parameter.SQLFactor.IncludingSchemas, func(s string, _ int) string {
		return strings.TrimSpace(s)
	}), func(s string, _ int) bool {
		return len(s) > 0
	})

	var schema string
	switch len(schemas) {
	case 1:
		schema = "''" + schemas[0] + "''"
	case 0:
		// No schema provided, leave empty
	default:
		schema = "''" + strings.Join(schemas, "'',''") + "''"
	}

	// Replace placeholders in task SQL
	taskSQL := task.TaskSQL
	if username != "" {
		taskSQL = strings.ReplaceAll(taskSQL, common.VAR_GRANTEE, grantee)
	}
	if schema != "" {
		taskSQL = strings.ReplaceAll(taskSQL, common.VAR_SCHEMA, schema)
	}

	// Adjust username case based on deployTask settings
	switch deployTask.UsernameCase {
	case constants.UsernameLowerCase:
		username = strings.ToLower(username)
	case constants.UsernameUpperCase:
		username = strings.ToUpper(username)
	}
	taskSQL = strings.ReplaceAll(taskSQL, common.VAR_USERNAME, username)
	taskSQL = strings.ReplaceAll(taskSQL, common.VAR_SQLSET_NAME, parameter.GetSQLFactor().GetSQLSetName())

	// Split and process SQL statements based on deployment mode
	var taskSQLs []string
	switch deployTask.SQLModel {
	case constants.DeployModeProcedure:
		taskSQLs = []string{taskSQL}
	case constants.DeployModeSQLs:
		taskSQLs = strings.Split(taskSQL, ";")
	default:
		taskSQLs = lo.Map(strings.Split(taskSQL, "\n"), func(s string, _ int) string {
			s = strings.TrimSpace(s)
			if strings.HasSuffix(s, ";") {
				s = s[:len(s)-1]
			}
			return s
		})

	}

	// Filter out empty statements
	return lo.Filter(taskSQLs, func(s string, _ int) bool {
		return len(s) > 0
	})
}

func FindDeployTask(task *sqlanalyzer.EnvDeployTask) (EnvironmentDeployTask, bool) {
	var defaultTask EnvironmentDeployTask
	for _, dTask := range GetDefaultSQLAnalyzeDeployTasks() {
		if dTask.TaskName == task.TaskName && dTask.TaskMode.String() == task.TaskMode {
			return dTask, true
		}
	}
	return defaultTask, false
}
