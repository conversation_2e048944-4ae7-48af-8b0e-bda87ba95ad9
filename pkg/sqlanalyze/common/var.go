package common

const VAR_SCHEMA = "'VARSCHEMA'"
const VAR_GRANTEE = "'VARTMS'"
const VAR_USERNAME = `${USERNAME}`
const VAR_COMMAND_TYPE_LIST = `${COMMAND_TYPE_LIST}`
const VAR_SQLSET_OWNER_LIST = `${SQLSET_OWNER_LIST}`
const VAR_SQLSET_NAME = `${SQLSET_NAME}`
const VAR_SQLSET_OWNER = `${SQLSET_OWNER}`
const VAR_SCHEMA_NAME = `${SCHEMA_NAME}`
const VAR_AND_TRUE = `AND 1=1`
const VAR_INCLUDING_SCHEMAS = `${INCLUDING_SCHEMAS}`
const VAR_OFFSET = `${OFFSET}`
const VAR_LIMIT_OFFSET = `${LIMIT_OFFSET}`
const VAR_MAX_ROWS = `${MAX_ROWS}`
const VAR_TASK_ID = `${TASK_ID}`
const VAR_EXEC_CODES = `${EXEC_CODES}`

type DigestType int

const (
	DIGEST_TYPE_ORACLE DigestType = iota
	DIGEST_TYPE_TiDB
	DIGEST_TYPE_Mixed
)
