package sqlanalyze

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/pkg/ai"
	"gitee.com/pingcap_enterprise/tms/pkg/ai/provider"
	sqlcommon "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/tool"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/common"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"

	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
)

// SQLReplayCoordinator serves as the task coordinator
type SQLReplayCoordinator struct {
	channelID       int
	taskID          int
	taskInfo        *task.Task
	sourceType      sqlcommon.SourceType
	schemaMapper    *structs.ChannelSchemaMapper
	parameter       *structs.Parameter
	dbConnector     *tool.DatabaseConnector
	sqlFetcher      *SQLFetcher
	sqlAnalyzer     *SQLAnalyzer
	progressTracker *tool.ProgressTracker
}

// BuildSQLReplayCoordinator creates an instance of SQLReplayCoordinator
func BuildSQLReplayCoordinator(taskInfo *task.Task, param *structs.Parameter, schemaMapper *structs.ChannelSchemaMapper) (*SQLReplayCoordinator, error) {
	tor := &SQLReplayCoordinator{
		channelID:       taskInfo.ChannelId,
		taskID:          taskInfo.TaskID,
		taskInfo:        taskInfo,
		sourceType:      param.GetAppSQLsSource(),
		schemaMapper:    schemaMapper,
		parameter:       param,
		sqlFetcher:      NewSQLFetcher(taskInfo.TaskID, param),
		sqlAnalyzer:     NewSQLAnalyzer(taskInfo.TaskID, param, sqlcommon.DIGEST_TYPE_Mixed),
		progressTracker: nil,
	}

	initErr := tor.init(param)
	if initErr != nil {
		log.Errorf("Failed to initialize, channelId %d, taskId %d, error: %v", tor.channelID, tor.taskID, initErr)
		return nil, initErr
	}

	return tor, nil
}

// init initializes parameters
func (i *SQLReplayCoordinator) init(runConfig *structs.Parameter) error {
	channelId, taskId := i.channelID, i.taskID
	if i.sourceType == sqlcommon.FileSource {
		if initErr := runConfig.InitSQLFileSource(); initErr != nil {
			log.Errorf("Failed to initialize SQL file source, channelId %d, taskId %d, error: %v", channelId, taskId, initErr)
			return errors.NewError(errors.TMS_SQLANALYZE_INIT_PARAM_FAILED, initErr.Error())
		}
	}
	i.dbConnector = tool.NewDatabaseConnector(runConfig.OracleConnStr, runConfig.TidbConnStr)
	return nil
}

// executeSQLAnalyzerTask executes the SQL analysis task
func (i *SQLReplayCoordinator) executeSQLAnalyzerTask(ctx context.Context) error {
	channelId, taskId := i.channelID, i.taskID
	log.Infof("Starting SQL analysis task, channelId:%d, taskId:%d", channelId, taskId)

	if err := tool.CreateSQLAnalyzerStartLog(ctx, channelId, taskId); err != nil {
		log.Errorf("Failed to create start log for SQL analysis task, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}

	// Legacy version execution logic
	if i.parameter.IsRunVersionV1() {
		if err := i.processSQLAnalyze(ctx); err != nil {
			log.Errorf("Failed to process SQL analysis, channelId %d, taskId %d, error: %v", channelId, taskId, err)
			return err
		}
	} else {
		conn, getConnErr := tool.NewDatabaseConnector(i.parameter.OracleConnStr, nil).OpenOracle()
		if getConnErr != nil {
			log.Errorf("Failed to open Oracle database, taskId:%d, err:%v", taskId, getConnErr)
			return getConnErr
		}

		clearErr := i.clearTemporaryData(ctx, conn)
		if clearErr != nil {
			log.Errorf("Failed to clear temporary data, channelId %d, taskId %d, error: %v", channelId, taskId, clearErr)
			return clearErr
		}

		schemaSQLSetStatus, getStatusErr := i.getSQLSetStatus(ctx, conn)
		if getStatusErr != nil {
			log.Errorf("Failed to retrieve SQLSet status, channelId %d, taskId %d, error: %v", channelId, taskId, getStatusErr)
			return getStatusErr
		}

		transferErr := i.transferSQLSetData(ctx, conn, schemaSQLSetStatus)
		if transferErr != nil {
			log.Errorf("Failed to transfer SQLSet data, channelId %d, taskId %d, error: %v", channelId, taskId, transferErr)
			return transferErr
		}

		pruneErr := i.pruneSQLSetData(ctx, conn, schemaSQLSetStatus)
		if pruneErr != nil {
			log.Errorf("Failed to prune SQLSet data, channelId %d, taskId %d, error: %v", channelId, taskId, pruneErr)
			return pruneErr
		}

		mergeErr := i.mergeSQLSetData(ctx, conn, schemaSQLSetStatus)
		if mergeErr != nil {
			log.Errorf("Failed to merge SQLSet data, channelId %d, taskId %d, error: %v", channelId, taskId, mergeErr)
			return mergeErr
		}

		generateErr := i.generateSQLDigestID(ctx, conn)
		if generateErr != nil {
			log.Errorf("Failed to generate SQL Digest ID, channelId %d, taskId %d, error: %v", channelId, taskId, generateErr)
			return generateErr
		}

		pruneDigestErr := i.pruneSQLSetDataByDigest(ctx, conn)
		if pruneDigestErr != nil {
			log.Errorf("Failed to prune SQLSet data by Digest ID, channelId %d, taskId %d, error: %v", channelId, taskId, pruneDigestErr)
			return pruneDigestErr
		}

		replayErr := i.replaySQLSetData(ctx, conn)
		if replayErr != nil {
			log.Errorf("Failed to replay SQLSet data, channelId %d, taskId %d, error: %v", channelId, taskId, replayErr)
			return replayErr
		}

		rewriteErr := i.rewriteSQLSyntax(ctx)
		if rewriteErr != nil {
			log.Errorf("Failed to rewrite SQL syntax, channelId %d, taskId %d, error: %v", channelId, taskId, rewriteErr)
		}
	}

	if err := tool.CreateSQLAnalyzerFinishLog(ctx, channelId, taskId); err != nil {
		log.Errorf("Failed to create finish log for SQL analysis task, err:%v", err)
	}
	return nil
}

func (i *SQLReplayCoordinator) ResetMetadata(ctx context.Context) error {
	channelId, taskId := i.channelID, i.taskID
	if err := models.GetSqlAnalyzerReaderWriter().DeleteSqlStmtExecResultByChannelIdTaskId(ctx, channelId, taskId); err != nil {
		log.Errorf("Failed to delete SQL statement execution results, channelId:%d, taskId:%d, err:%s", channelId, taskId, err)
		return err
	}
	if err := models.GetSqlAnalyzerReaderWriter().RemoveSqlExecSummary(ctx, taskId); err != nil {
		log.Errorf("Failed to delete SQL execution summary, taskId:%d, err:%s", taskId, err)
		return err
	}
	if err := models.GetProgressLogReaderWriter().DeleteProgressLogDetail(ctx, &common.ProgressLogDetail{ChannelID: channelId, TaskID: taskId}); err != nil {
		log.Errorf("Failed to delete progress log details, channelId:%d, taskId:%d, err:%s", channelId, taskId, err)
		return err
	}
	if err := models.GetTaskReaderWriter().DeleteTaskLogs(ctx, taskId); err != nil {
		log.Errorf("Failed to delete task logs, taskId:%d, err:%s", taskId, err)
		return err
	}
	return nil
}

// processSQLAnalyze coordinates the SQL analysis process
func (i *SQLReplayCoordinator) processSQLAnalyze(ctx context.Context) error {
	startTime := time.Now()
	channelId, taskId := i.channelID, i.taskID

	tidbConn, err := i.dbConnector.OpenTiDB()
	if err != nil {
		log.Errorf("Failed to open TiDB database, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}
	defer tidbConn.Close()

	sqlNumStat, err := i.sqlFetcher.FetchSQL(ctx)
	if err != nil {
		log.Errorf("Failed to fetch SQL statements, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}

	log.Infof("Starting SQL rewrite and execution, total SQLs:%d", sqlNumStat.TotalNum)
	if err := tool.CreateSQLAnalyzerRunningLog(ctx, channelId, taskId, fmt.Sprintf("Starting SQL replay, total SQLs %d, evaluating SQLs", sqlNumStat.TotalNum)); err != nil {
		log.Errorf("Failed to create running log for SQL analysis, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}

	i.progressTracker = tool.NewProgressTracker(channelId, taskId, sqlNumStat.TotalNum)
	i.progressTracker.Start(ctx)
	defer i.progressTracker.Close()

	// Get ignored and resolved SQL IDs for filtering
	filteredSQLs, err := i.getIgnoredAndResolvedSQLIds(ctx)
	if err != nil {
		log.Errorf("Failed to get ignored and resolved SQL IDs, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}

	// Start channel closing in background
	var fetchErr error
	closeDone := make(chan struct{})
	go func() {
		defer close(closeDone)
		if closeErr := i.sqlFetcher.CloseChanIfNeed(ctx); closeErr != nil {
			fetchErr = closeErr
			log.Errorf("Error occurred during SQL fetching, channelId %d, taskId %d, error: %v", channelId, taskId, closeErr)
		}
	}()

	// 通过chan进行SQLStat的传递
	if err := i.matchAndExplainSQLInTiDB(ctx, tidbConn, filteredSQLs); err != nil {
		log.Errorf("Failed to match and explain SQL in TiDB, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return errors.NewError(errors.TMS_SQLANALYZE_EXPLAIN_SQL_IN_TIDB_FAILED, err.Error())
	}

	// Wait for channel closing to complete and check for errors
	<-closeDone
	if fetchErr != nil {
		return errors.NewError(errors.TMS_SQLANALYZE_GET_ORACLE_SQL_FAILED, fetchErr.Error())
	}

	if err := tool.CreateSQLAnalyzerRunningLog(ctx, channelId, taskId, fmt.Sprintf("Replaying SQLs, total SQLs %d, duplicated %d, filtered %d, success %d, failed %d, schema mismatch %d, timeout %d",
		i.progressTracker.GetTotal(), i.progressTracker.GetDuplicateNum(), i.progressTracker.GetFilteredNum(), i.progressTracker.GetSuccessNum(), i.progressTracker.GetFailedNum(),
		i.progressTracker.GetSkipSchemaNum(), i.progressTracker.GetTimeoutNum())); err != nil {
		log.Errorf("Failed to create running log for SQL analysis, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}
	if err := i.progressTracker.SaveSummary(ctx); err != nil {
		log.Errorf("Failed to save SQL execution summary, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return err
	}

	log.Infof("Completed SQL rewrite and execution, total SQLs:%d, normal SQLs:%d, explained SQLs:%d, duration:%v", sqlNumStat.TotalNum, sqlNumStat.ValidNum, i.progressTracker.GetExplainedNum(), time.Since(startTime))
	if err := UpdateTaskStatus(ctx, constants.TASK_STATUS_FINISH, i.taskInfo); err != nil {
		log.Errorf("Failed to update task status to success, channelId %d, taskId %d, error: %v", channelId, taskId, err)
		return errors.NewError(errors.TIMS_UPDATE_TASK_FAILED, err.Error())
	}
	return nil
}

// matchAndExplainSQLInTiDB processes SQLs in parallel
func (i *SQLReplayCoordinator) matchAndExplainSQLInTiDB(ctx context.Context, tidbConn *sql.DB, filteredSQLs map[string]bool) error {
	visitor := &tool.SchemaTableVisitor{}

	// Deduplication logic: collect unique SQLs by OraSqlId
	uniqueSQLs := make(map[string]*structs.SpaSqlStmt)
	duplicateCount := 0
	filteredCount := 0

	for stmt := range i.sqlFetcher.oracleSQLChan {
		// Skip if SQL is ignored or resolved
		if filteredSQLs[stmt.OraSqlId] {
			filteredCount++
			continue
		}

		if existing, exists := uniqueSQLs[stmt.OraSqlId]; exists {
			duplicateCount++
			i.progressTracker.IncreaseDuplicateNum()
			// Keep the SQL with higher execution count
			if stmt.OraExecutions > existing.OraExecutions {
				uniqueSQLs[stmt.OraSqlId] = stmt
			}
		} else {
			uniqueSQLs[stmt.OraSqlId] = stmt
		}
	}

	log.Infof("SQL deduplication completed, channelId %d, taskId %d, total SQLs: %d, unique SQLs: %d, duplicated: %d, filtered (ignored/resolved): %d",
		i.channelID, i.taskID, len(uniqueSQLs)+duplicateCount+filteredCount, len(uniqueSQLs), duplicateCount, filteredCount)

	// Set filtered count in progress tracker
	i.progressTracker.SetFilteredNum(filteredCount)

	// Process unique SQLs in parallel
	g, ctx := errgroup.WithContext(ctx)
	g.SetLimit(i.parameter.GetSQLFactor().GetAnalyzeThread())

	for _, stmt := range uniqueSQLs {
		stmt := stmt
		g.Go(func() error {
			return i.processSingleSQL(ctx, stmt, visitor, tidbConn)
		})
	}
	return g.Wait()
}

// processSingleSQL processes a single SQL
func (i *SQLReplayCoordinator) processSingleSQL(ctx context.Context, stmt *structs.SpaSqlStmt, visitor *tool.SchemaTableVisitor, tidbConn *sql.DB) error {
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	defer i.progressTracker.IncreaseExplainedNum()

	i.sqlAnalyzer.RewriteSQL(stmt)

	if i.sourceType == sqlcommon.DBSource && !i.sqlAnalyzer.MatchSchema(stmt, visitor) {
		log.Debugf("Skipping SQL due to schema mismatch, ora sql id:%s, expected schemas:%v, actual schemas:%v, tidb sql:%s", stmt.OraSqlId, i.parameter.GetIncludingSchemasBySQLSource(), visitor.GetSchemaNames(), stringutil.RemoveSpecialLetterForLog(stmt.TidbSqlText))
		i.progressTracker.IncreaseSkipSchemaNum()
		return nil
	}

	result := i.sqlAnalyzer.ExplainSQL(ctx, stmt, tidbConn, i.schemaMapper)
	stmt = result.Stmt
	i.progressTracker.Update(stmt.TidbExecStatus)

	log.Debugf("Processed SQL, ora sql id:%s, tidb sql:%s, status:%s, elapsed time:%dms",
		stmt.OraSqlId, stringutil.RemoveSpecialLetterForLog(stmt.TidbSqlText), stmt.TidbExecStatus, stmt.TidbElapsedTimeMs)

	if err := i.insertSQLExecInfo(context.Background(), i.channelID, i.taskID, stmt); err != nil {
		log.Errorf("Failed to insert SQL execution information, oracleSchema:%s, sqlId:%s, err:%v",
			stmt.OraParsingSchemaName, stmt.OraSqlId, err)
		return err
	}
	return nil
}

// insertSQLExecInfo inserts SQL execution information
func (i *SQLReplayCoordinator) insertSQLExecInfo(ctx context.Context, channelId, taskId int, sqlExec *structs.SpaSqlStmt) error {
	if sqlExec.OraPlanTimestamp.IsZero() {
		sqlExec.OraPlanTimestamp = timeutil.GetTMSNullTime()
	}
	return models.Transaction(ctx, func(trxCtx context.Context) error {
		stmt := buildSqlStmtExecResultModel(channelId, taskId, sqlExec)
		stmtStr, _ := json.Marshal(stmt)
		log.Debug("Inserting SQL execution information", zap.String("stmt", string(stmtStr)))
		return models.GetSqlAnalyzerReaderWriter().MergeSqlStmtExecResult(trxCtx, stmt)
	})
}

// getSQLSetStatus retrieves the current status of SQLSet and compares differences, compatible with Oracle and file sources
func (i *SQLReplayCoordinator) getSQLSetStatus(ctx context.Context, oracleConn *sql.DB) ([]*tool.SchemaSQLSetStatus, error) {
	_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Retrieving SQLSet status")

	sourceSchemaStatusMap := make(map[string]*tool.SchemaSQLSetStatus)

	sqlsetName := i.parameter.SQLFactor.GetSQLSetName()
	dbaSQLSets := make([]datasource.SQLSet, 0, 100)
	tmsSQLSets := make([]datasource.SQLSet, 0, 100)
	var getErr error

	if i.sourceType == sqlcommon.FileSource {
		log.Infof("Retrieving SQLSet status, sourceType:%s, sqlsetName:%s, file SQLSet", i.sourceType, sqlsetName)
		fileSQLSet := i.generateSQLSetFromFile()
		dbaSQLSets = append(dbaSQLSets, fileSQLSet)
		tmsSQLSets, getErr = models.GetDatasourceReaderWriter().GetTMSSQLSets(ctx, oracleConn, i.taskID, sqlsetName, []string{fileSQLSet.Owner})
		if getErr != nil {
			log.Errorf("Failed to retrieve TMS SQLSet, err:%v", getErr)
			return nil, getErr
		}

		sourceSchemaStatusMap[fileSQLSet.Owner] = &tool.SchemaSQLSetStatus{
			SchemaName: fileSQLSet.Owner,
			Status:     constants.SQLSetEmpty,
		}
	} else {
		schemaOwner := strings.ToUpper(i.parameter.OracleConnStr.User)
		log.Infof("Retrieving SQLSet status, sourceType:%s, sqlsetName:%s, DBA SQLSet", i.sourceType.String(), sqlsetName)
		dbaSQLSets, getErr = models.GetDatasourceReaderWriter().GetDBASQLSets(ctx, oracleConn, sqlsetName, []string{schemaOwner})
		if getErr != nil {
			log.Errorf("Failed to retrieve DBA SQLSet, err:%v", getErr)
			return nil, getErr
		}
		tmsSQLSets, getErr = models.GetDatasourceReaderWriter().GetTMSSQLSets(ctx, oracleConn, i.taskID, sqlsetName, []string{schemaOwner})
		if getErr != nil {
			log.Errorf("Failed to retrieve TMS SQLSet, err:%v", getErr)
			return nil, getErr
		}

		sourceSchemaStatusMap[schemaOwner] = &tool.SchemaSQLSetStatus{
			SchemaName: schemaOwner,
			Status:     constants.SQLSetEmpty,
		}
	}

	for idx, dbaSQLSet := range dbaSQLSets {
		sourceSchemaStatusMap[dbaSQLSet.Owner].DBASQLSet = &dbaSQLSets[idx]
	}
	for idx, tmsSQLSet := range tmsSQLSets {
		sourceSchemaStatusMap[tmsSQLSet.Owner].TMSSQLSet = &tmsSQLSets[idx]
	}

	for key, sourceSchemaStatus := range sourceSchemaStatusMap {
		log.Infof("SQLSet status retrieved, schemaName:%s, dbaSQLSet:%s, tmsSQLSet:%s", key, sourceSchemaStatus.GetDBASQLSetString(), sourceSchemaStatus.GetTMSSQLSetString())

		// Special handling for file source
		if i.sourceType == sqlcommon.FileSource {
			// For file source, always check if TMS table has data
			if sourceSchemaStatus.TMSSQLSet == nil || sourceSchemaStatus.TMSSQLSet.StatementCount == 0 {
				// TMS table is empty, use Empty status to save data to main table
				sourceSchemaStatusMap[key].Status = constants.SQLSetEmpty
				log.Infof("File source: TMS table is empty, using SQLSetEmpty status")
			} else {
				// TMS table has data, use FullReset to clear and reload
				sourceSchemaStatusMap[key].Status = constants.SQLSetFullReset
				log.Infof("File source: TMS table has data (count=%d), using SQLSetFullReset status", sourceSchemaStatus.TMSSQLSet.StatementCount)
			}
			continue
		}

		// Original logic for non-file sources
		// If DBA SQLSet does not exist, it means the schema's SQLSet has not been run, treated as breakpoint resume
		if sourceSchemaStatus.DBASQLSet == nil {
			sourceSchemaStatusMap[key].Status = constants.SQLSetBreakpoint
			continue
		}
		// If TMSSQLSet does not exist, it means it has not been run, requiring data initialization
		if sourceSchemaStatus.TMSSQLSet == nil {
			sourceSchemaStatusMap[key].Status = constants.SQLSetEmpty
			continue
		}
		// If creation times differ, it indicates the DBA SQLSet was recreated
		if sourceSchemaStatus.DBASQLSet.IsCreatedModified(sourceSchemaStatus.TMSSQLSet) {
			sourceSchemaStatusMap[key].Status = constants.SQLSetFullReset
			continue
		}
		// If creation times are the same but LAST_MODIFIED or STATEMENT_COUNT changes, it indicates incremental data
		if sourceSchemaStatus.DBASQLSet.HasIncrementalData(sourceSchemaStatus.TMSSQLSet) {
			sourceSchemaStatusMap[key].Status = constants.SQLSetIncremental
			continue
		}
		// If creation times are the same and no changes in LAST_MODIFIED or STATEMENT_COUNT, attempt to resume from breakpoint
		if sourceSchemaStatus.DBASQLSet.Equals(sourceSchemaStatus.TMSSQLSet) {
			sourceSchemaStatusMap[key].Status = constants.SQLSetBreakpoint
			continue
		}
	}

	vs := lo.Values(sourceSchemaStatusMap)

	for _, v := range vs {
		log.Infof("SQLSet status summary, schemaName:%s, status:%s", v.SchemaName, v.Status.String())
	}

	return vs, nil
}

func (i *SQLReplayCoordinator) generateSQLSetFromFile() datasource.SQLSet {
	sqlsetName := i.parameter.SQLFactor.GetSQLSetName()
	sqlFileContent := i.parameter.GetSQLFileContent()
	fileSQLSet := datasource.SQLSet{
		TaskId:         i.taskID,
		Name:           sqlsetName,
		Owner:          sqlFileContent.FileName, // filename as schema
		Description:    "from file",
		Created:        sqlFileContent.InodeAsTime(), // Use inode as creation time since Linux cannot retrieve file creation time
		LastModified:   sqlFileContent.ModifyTime,
		StatementCount: uint64(len(sqlFileContent.Contents)),
	}
	return fileSQLSet
}

func (i *SQLReplayCoordinator) generateSQLSetStatementsFromFile() []datasource.SQLSetStatement {
	sqlsetName := i.parameter.SQLFactor.GetSQLSetName()
	sqlFileContent := i.parameter.GetSQLFileContent()

	// Check if file content exists
	if sqlFileContent == nil {
		log.Errorf("SQL file content is nil for taskId: %d", i.taskID)
		return []datasource.SQLSetStatement{}
	}

	log.Infof("Processing SQL file: %s, SQL count: %d", sqlFileContent.FileName, len(sqlFileContent.Contents))

	sqlsetStatements := make([]datasource.SQLSetStatement, 0, len(sqlFileContent.Contents))

	// Get first available target schema for file mode
	defaultSchema := sqlFileContent.FileName

	for idx, sqlLine := range sqlFileContent.Contents {
		digestID := i.sqlAnalyzer.generateDigestID(sqlLine)
		sqlID := digestID
		if len(digestID) > 13 {
			sqlID = digestID[:13]
		}

		sqlsetStatements = append(sqlsetStatements, datasource.SQLSetStatement{
			TaskID:            int64(i.taskID),
			SQLSetName:        sqlsetName,
			SQLSetOwner:       defaultSchema, // Use mapped schema instead of filename
			DigestID:          digestID,
			SQLID:             sqlID,
			SQLText:           sqlLine,
			ParsingSchemaName: defaultSchema, // Use mapped schema instead of filename
			PlanHashValue:     int64(idx + 1),
			CommandType:       3, // Use 3 (SELECT) as default for better compatibility
			Module:            "TMS_FILE",
			ElapsedTime:       0, // Set to 0 to indicate not executed yet
			Executions:        1,
			Action:            "TMS_FILE_REPLAY",
		})
	}
	return sqlsetStatements
}

// clearTemporaryData clears temporary BAK data
func (i *SQLReplayCoordinator) clearTemporaryData(ctx context.Context, oracleConn *sql.DB) error {
	tErr := models.GetDatasourceReaderWriter().TruncateTMSSQLSetBak(ctx, oracleConn, i.taskID)
	if tErr != nil {
		log.Errorf("Failed to truncate TMS_SQLSET_BAK table, taskId:%d, err:%v", i.taskID, tErr)
		return tErr
	}
	tErr = models.GetDatasourceReaderWriter().TruncateTMSSQLSetStatementsBak(ctx, oracleConn, i.taskID)
	if tErr != nil {
		log.Errorf("Failed to truncate TMS_SQLSET_STATEMENTS_BAK table, taskId:%d, err:%v", i.taskID, tErr)
		return tErr
	}
	return nil
}

// transferSQLSetData transfers SQLSet data from DBA_SQLSET to TMS_SQLSET or TMS_SQLSET_BAK
func (i *SQLReplayCoordinator) transferSQLSetData(ctx context.Context, oracleConn *sql.DB, sqlsetStatusArr []*tool.SchemaSQLSetStatus) error {
	isFileSource := i.sourceType == sqlcommon.FileSource

	for _, sqlsetStatus := range sqlsetStatusArr {
		schemaName, sqlsetName := sqlsetStatus.SchemaName, i.parameter.SQLFactor.GetSQLSetName()

		errorHandler := tool.NewTransferErrorHandler(ctx, oracleConn, i.taskID, schemaName, sqlsetName)

		if isFileSource {
			i.transferSQLSetDataByFile(sqlsetStatus, errorHandler)
		} else {
			i.transferSQLSetDataByOracle(sqlsetStatus, errorHandler)
		}

		if err := errorHandler.Error(); err != nil {
			log.Errorf("Failed to transfer SQLSet data, channelId %d, taskId %d, schemaName:%s, sqlsetName:%s, error: %v",
				i.channelID, i.taskID, schemaName, sqlsetName, err)
			return err
		}
	}

	return nil
}

func (i *SQLReplayCoordinator) transferSQLSetDataByOracle(sqlsetStatus *tool.SchemaSQLSetStatus, errorHandler *tool.TransferErrorHandler) {
	switch sqlsetStatus.Status {
	case constants.SQLSetEmpty:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Transferring SQL data, status [Empty]")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().InsertIntoTMSSQLSet, "InsertIntoTMSSQLSet")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().InsertIntoTMSSQLSetStatements, "InsertIntoTMSSQLSetStatements")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsLastCopyTime, "UpdateTMSSQLSetStatementsLastCopyTime")
	case constants.SQLSetIncremental:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Transferring incremental SQL data, status [Incremental]")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().InsertIntoTMSSQLSetBak, "InsertIntoTMSSQLSetBak")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().InsertIntoTMSSQLSetStatementsBak, "InsertIntoTMSSQLSetStatementsBak")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsBakLastCopyTime, "UpdateTMSSQLSetStatementsBakLastCopyTime")
	case constants.SQLSetFullReset:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Resetting SQL data, status [FullReset]")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().DeleteTMSSQLSet, "DeleteTMSSQLSet")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().DeleteTMSSQLSetStatements, "DeleteTMSSQLSetStatements")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().InsertIntoTMSSQLSet, "InsertIntoTMSSQLSet")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().InsertIntoTMSSQLSetStatements, "InsertIntoTMSSQLSetStatements")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsLastCopyTime, "UpdateTMSSQLSetStatementsLastCopyTime")
	case constants.SQLSetBreakpoint:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Resuming SQL replay from breakpoint, status [Breakpoint]")
	}
}

func (i *SQLReplayCoordinator) transferSQLSetDataByFile(sqlsetStatus *tool.SchemaSQLSetStatus, errorHandler *tool.TransferErrorHandler) {
	sqlset := i.generateSQLSetFromFile()
	sqlsetStatements := i.generateSQLSetStatementsFromFile()

	// Log generated statements count for debugging
	log.Infof("Generated SQL statements from file, count: %d, fileName: %s", len(sqlsetStatements), sqlset.Owner)

	switch sqlsetStatus.Status {
	case constants.SQLSetEmpty:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Transferring SQL file data")
		errorHandler.SaveSQLSet(models.GetDatasourceReaderWriter().SaveTMSSQLSet, "SaveTMSSQLSet", sqlset)
		errorHandler.SaveStatement(models.GetDatasourceReaderWriter().SaveTMSSQLSetStatements, "SaveTMSSQLSetStatements", sqlsetStatements)
		errorHandler.Transfer(models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsLastCopyTime, "UpdateTMSSQLSetStatementsLastCopyTime")
	case constants.SQLSetIncremental:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Transferring incremental SQL file data")
		errorHandler.SaveSQLSet(models.GetDatasourceReaderWriter().SaveTMSSQLSetBak, "SaveTMSSQLSetBak", sqlset)
		errorHandler.SaveStatement(models.GetDatasourceReaderWriter().SaveTMSSQLSetStatementsBak, "SaveTMSSQLSetStatementsBak", sqlsetStatements)
		errorHandler.Transfer(models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsBakLastCopyTime, "UpdateTMSSQLSetStatementsBakLastCopyTime")
	case constants.SQLSetFullReset:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Resetting SQL file data")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().DeleteTMSSQLSet, "DeleteTMSSQLSet")
		errorHandler.Transfer(models.GetDatasourceReaderWriter().DeleteTMSSQLSetStatements, "DeleteTMSSQLSetStatements")
		log.Infof("Saving SQLSet and %d statements to main table after reset", len(sqlsetStatements))
		errorHandler.SaveSQLSet(models.GetDatasourceReaderWriter().SaveTMSSQLSet, "SaveTMSSQLSet", sqlset)
		errorHandler.SaveStatement(models.GetDatasourceReaderWriter().SaveTMSSQLSetStatements, "SaveTMSSQLSetStatements", sqlsetStatements)
		errorHandler.Transfer(models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsLastCopyTime, "UpdateTMSSQLSetStatementsLastCopyTime")
	case constants.SQLSetBreakpoint:
		_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Resuming SQL file replay from breakpoint")
	}
}

// generateSQLDigestID generates SQL Digest ID
func (i *SQLReplayCoordinator) generateSQLDigestID(ctx context.Context, oracleConn *sql.DB) error {
	sqlsetSummary, fetchErr := i.sqlFetcher.FetchSQLV2(ctx, BuildFetchParam{
		WithReplay:    true,
		WithExecCodes: false,
		Step:          "Generating digestID",
	})
	if fetchErr != nil {
		log.Errorf("Failed to fetch SQL statements (V2), channelId %d, taskId %d, error: %v", i.channelID, i.taskID, fetchErr)
		return fetchErr
	}
	log.Infof("Starting SQL Digest ID generation, channelId:%d, taskId:%d, totalNum:%d, notDigestNum:%d",
		i.channelID, i.taskID, sqlsetSummary.TotalNum, sqlsetSummary.NotDigestNum)
	go i.sqlFetcher.CloseChanIfNeed(ctx)

	// Initialize progress tracker
	tracker := tool.BuildProgressHelperV2(i.channelID, i.taskID, sqlsetSummary.NotDigestNum)
	tracker.DisplayAndSaveDigestingProgress(ctx)
	defer tracker.CloseDigestTracker(ctx)

	// Get concurrency thread count (assumed from parameters, default 4)
	workerCount := i.parameter.GetSQLFactor().GetAnalyzeThread()
	oracleConn.SetMaxOpenConns(workerCount * 2)
	oracleConn.SetMaxIdleConns(workerCount)

	// Get ignored and resolved SQL IDs for filtering
	filteredSQLs, err := i.getIgnoredAndResolvedSQLIds(ctx)
	if err != nil {
		log.Errorf("Failed to get ignored and resolved SQL IDs, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, err)
		return err
	}

	// Use WaitGroup to control all workers' completion
	var wg sync.WaitGroup
	// Channel to collect errors
	errChan := make(chan error, workerCount)

	// Start a fixed number of worker goroutines
	for j := 0; j < workerCount; j++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			if err := i.processDigestWorker(ctx, i.sqlFetcher.oracleSQLChan, oracleConn, tracker, filteredSQLs); err != nil {
				errChan <- fmt.Errorf("SQL digest worker %d failed: %v", workerID, err)
			}
		}(j)
	}

	// Wait for all workers to complete and close error channel
	go func() {
		wg.Wait()
		close(errChan)
	}()

	// Check for any errors
	for err := range errChan {
		if err != nil {
			log.Errorf("SQL Digest ID generation aborted due to error: %v", err)
			return err // Return immediately on error
		}
	}

	return nil
}

// processDigestWorker processes a single SQL Digest
func (i *SQLReplayCoordinator) processDigestWorker(ctx context.Context,
	oracleSQLChan <-chan *structs.SpaSqlStmt,
	oracleConn *sql.DB,
	tracker *tool.ProgressTrackerV2,
	filteredSQLs map[string]bool) error {

	for oracleSQL := range oracleSQLChan {
		if oracleSQL.OraDigestId != "" {
			continue
		}

		// Increment processed count (thread-safe)
		tracker.IncreaseDigestedNum()

		// Generate Digest ID
		digestID := i.sqlAnalyzer.GetSQLDigestID(oracleSQL)

		// Update main table Digest ID
		updateDigestErr := models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsDigestID(ctx, oracleConn, i.taskID, oracleSQL.OraSqlId, digestID)
		if updateDigestErr != nil {
			log.Errorf("Failed to update Digest ID in TMS_SQLSET_STATEMENTS, channelId %d, taskId %d, sqlId:%s, error: %v",
				i.channelID, i.taskID, oracleSQL.OraSqlId, updateDigestErr)
			return updateDigestErr // Return error, stop current worker
		}

		// Update backup table Digest ID
		updateDigestErr = models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsBakDigestID(ctx, oracleConn, i.taskID, oracleSQL.OraSqlId, digestID)
		if updateDigestErr != nil {
			log.Errorf("Failed to update Digest ID in TMS_SQLSET_STATEMENTS_BAK, channelId %d, taskId %d, sqlId:%s, error: %v",
				i.channelID, i.taskID, oracleSQL.OraSqlId, updateDigestErr)
			return updateDigestErr // Return error, stop current worker
		}
	}
	return nil
}

// pruneSQLSetData prunes data, filtering and deduplicating
func (i *SQLReplayCoordinator) pruneSQLSetData(ctx context.Context, oracleConn *sql.DB, sqlsetStatusArr []*tool.SchemaSQLSetStatus) error {
	hasNoEmptyOrFullResetStatus, hasIncrementalStatus := i.verifyStatus(sqlsetStatusArr)
	errorHandler := tool.NewTransferErrorHandler(ctx, oracleConn, i.taskID, "", "")

	rw := models.GetDatasourceReaderWriter()

	// Process non-backup table operations
	if hasNoEmptyOrFullResetStatus {
		i.pruneStatements(errorHandler, rw)
		i.deleteStatements(errorHandler, rw)
	}

	// Process backup table operations
	if hasIncrementalStatus {
		i.pruneStatementsBak(errorHandler, rw)
		i.deleteStatementsBak(errorHandler, rw)
	}

	if err := errorHandler.Error(); err != nil {
		log.Errorf("Failed to prune SQLSet data, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, err)
		return err
	}

	return nil
}

// pruneStatements handles pruning operations for non-backup tables
func (i *SQLReplayCoordinator) pruneStatements(errorHandler *tool.TransferErrorHandler, rw datasource.ReaderWriter) {
	_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Deduplicating by planHashValue, sqlID, and executions")
	errorHandler.Prune(rw.PruneTMSSQLSetStatementsByPlanHashValue, "PruneTMSSQLSetStatementsByPlanHashValue")
	errorHandler.Prune(rw.PruneTMSSQLSetStatementsBySQLID, "PruneTMSSQLSetStatementsBySQLID")
	errorHandler.Prune(rw.PruneTMSSQLSetStatementsByExecutions, "PruneTMSSQLSetStatementsByExecutions")
}

// pruneStatementsBak handles pruning operations for backup tables
func (i *SQLReplayCoordinator) pruneStatementsBak(errorHandler *tool.TransferErrorHandler, rw datasource.ReaderWriter) {
	_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Deduplicating bak by planHashValue, sqlID, and executions")
	errorHandler.Prune(rw.PruneTMSSQLSetStatementsBakByPlanHashValue, "PruneTMSSQLSetStatementsBakByPlanHashValue")
	errorHandler.Prune(rw.PruneTMSSQLSetStatementsBakBySQLID, "PruneTMSSQLSetStatementsBakBySQLID")
	errorHandler.Prune(rw.PruneTMSSQLSetStatementsBakByExecutions, "PruneTMSSQLSetStatementsBakByExecutions")
}

// deleteStatements handles deletion operations for non-backup tables
func (i *SQLReplayCoordinator) deleteStatements(errorHandler *tool.TransferErrorHandler, rw datasource.ReaderWriter) {
	_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Deleting based on configuration parameters")

	// delete other schemas
	if schema := i.parameter.GetIncludingSchemasBySQLSource(); len(schema) > 0 {
		arr := lo.Map(schema, func(schema string, _ int) any { return schema })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBySchemas, arr, "DeleteTMSSQLSetStatementsBySchemas")
	}
	// delete other sqltypes
	if sqlTypes := i.parameter.SQLFactor.GetIncludingSQLTypeList(); len(sqlTypes) > 0 {
		arr := lo.Map(sqlTypes, func(sqlType int, _ int) any { return sqlType })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBySQLTypes, arr, "DeleteTMSSQLSetStatementsBySQLTypes")
	}
	// delete other modules
	if modules := i.parameter.SQLFactor.GetExcludeModulesList(); len(modules) > 0 {
		arr := lo.Map(modules, func(module string, _ int) any { return module })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBySQLModules, arr, "DeleteTMSSQLSetStatementsBySQLModules")
	}
	// delete other sqlids
	if sqlIDs := i.parameter.SQLFactor.GetExcludeSQLIds(); len(sqlIDs) > 0 {
		arr := lo.Map(sqlIDs, func(sqlID string, _ int) any { return sqlID })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBySQLIDs, arr, "DeleteTMSSQLSetStatementsBySQLIDs")
	}
	// delete other sqltexts
	if sqlTexts := i.parameter.SQLFactor.GetExcludeSQLList(); len(sqlTexts) > 0 {
		arr := lo.Map(sqlTexts, func(sql string, _ int) any { return sql })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBySQLTexts, arr, "DeleteTMSSQLSetStatementsBySQLTexts")
	}
}

// deleteStatementsBak handles deletion operations for backup tables
func (i *SQLReplayCoordinator) deleteStatementsBak(errorHandler *tool.TransferErrorHandler, rw datasource.ReaderWriter) {
	_ = tool.CreateSQLAnalyzerPreparingLog(context.Background(), i.channelID, i.taskID, "Deleting based on configuration parameters")

	// delete other schemas
	if schema := i.parameter.GetIncludingSchemasBySQLSource(); len(schema) > 0 {
		arr := lo.Map(schema, func(schema string, _ int) any { return schema })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBakBySchemas, arr, "DeleteTMSSQLSetStatementsBakBySchemas")
	}
	// delete other sqltypes
	if sqlTypes := i.parameter.SQLFactor.GetIncludingSQLTypeList(); len(sqlTypes) > 0 {
		arr := lo.Map(sqlTypes, func(sqlType int, _ int) any { return sqlType })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBakBySQLTypes, arr, "DeleteTMSSQLSetStatementsBakBySQLTypes")
	}
	// delete other modules
	if modules := i.parameter.SQLFactor.GetExcludeModulesList(); len(modules) > 0 {
		arr := lo.Map(modules, func(module string, _ int) any { return module })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBakBySQLModules, arr, "DeleteTMSSQLSetStatementsBakBySQLModules")
	}
	// delete other sqlids
	if sqlIDs := i.parameter.SQLFactor.GetExcludeSQLIds(); len(sqlIDs) > 0 {
		arr := lo.Map(sqlIDs, func(sqlID string, _ int) any { return sqlID })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBakBySQLIDs, arr, "DeleteTMSSQLSetStatementsBakBySQLIDs")
	}
	// delete other sqltexts
	if sqlTexts := i.parameter.SQLFactor.GetExcludeSQLList(); len(sqlTexts) > 0 {
		arr := lo.Map(sqlTexts, func(sql string, _ int) any { return sql })
		errorHandler.Delete(rw.DeleteTMSSQLSetStatementsBakBySQLTexts, arr, "DeleteTMSSQLSetStatementsBakBySQLTexts")
	}
}

// pruneSQLSetDataByDigest prunes data based solely on DIGEST ID
func (i *SQLReplayCoordinator) pruneSQLSetDataByDigest(ctx context.Context, oracleConn *sql.DB) error {
	errorHandler := tool.NewTransferErrorHandler(ctx, oracleConn, i.taskID, "", "")

	_ = tool.CreateSQLAnalyzerPreparingLog(ctx, i.channelID, i.taskID, "Deduplicating by digestID")
	errorHandler.Prune(models.GetDatasourceReaderWriter().PruneTMSSQLSetStatementsByDigestID, "PruneTMSSQLSetStatementsByDigestID")
	errorHandler.Prune(models.GetDatasourceReaderWriter().PruneTMSSQLSetStatementsBakByDigestID, "PruneTMSSQLSetStatementsBakByDigestID")

	if err := errorHandler.Error(); err != nil {
		log.Errorf("Failed to prune SQLSet data by Digest ID, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, err)
		return err
	}

	return nil
}

func (i *SQLReplayCoordinator) verifyStatus(sqlsetStatusArr []*tool.SchemaSQLSetStatus) (bool, bool) {
	var hasNoEmptyOrFullResetStatus bool
	var hasIncrementalStatus bool

	for _, sqlsetStatus := range sqlsetStatusArr {
		if sqlsetStatus.Status == constants.SQLSetEmpty || sqlsetStatus.Status == constants.SQLSetFullReset {
			hasNoEmptyOrFullResetStatus = true
		}
		if sqlsetStatus.Status == constants.SQLSetIncremental {
			hasIncrementalStatus = true
		}
	}
	return hasNoEmptyOrFullResetStatus, hasIncrementalStatus
}

func (i *SQLReplayCoordinator) mergeSQLSetData(ctx context.Context, oracleConn *sql.DB, sqlsetStatusArr []*tool.SchemaSQLSetStatus) error {
	for _, sqlsetStatus := range sqlsetStatusArr {
		schemaName, sqlsetName := sqlsetStatus.SchemaName, i.parameter.SQLFactor.GetSQLSetName()
		errorHandler := tool.NewTransferErrorHandler(ctx, oracleConn, i.taskID, schemaName, sqlsetName)

		switch sqlsetStatus.Status {
		case constants.SQLSetEmpty:
		case constants.SQLSetBreakpoint:
		case constants.SQLSetIncremental:
			_ = tool.CreateSQLAnalyzerPreparingLog(ctx, i.channelID, i.taskID, "Merging data for schema: "+sqlsetStatus.SchemaName)
			errorHandler.Transfer(models.GetDatasourceReaderWriter().MergeTMSSQLSetStatements, "MergeTMSSQLSetStatements")
			errorHandler.Update(models.GetDatasourceReaderWriter().UpdateTMSSQLSet, sqlsetStatus.DBASQLSet.Created, sqlsetStatus.DBASQLSet.LastModified, sqlsetStatus.DBASQLSet.StatementCount, "UpdateTMSSQLSet")
		case constants.SQLSetFullReset:
		}

		if errorHandler.Error() != nil {
			log.Errorf("Failed to merge SQLSet data, channelId %d, taskId %d, schemaName:%s, sqlsetName:%s, error: %v",
				i.channelID, i.taskID, schemaName, sqlsetName, errorHandler.Error())
			return errorHandler.Error()
		}
	}

	return nil
}

func (i *SQLReplayCoordinator) replaySQLSetData(ctx context.Context, oracleConn *sql.DB) error {
	sqlFetcher := NewSQLFetcher(i.taskID, i.parameter)

	statementsSummary, fetchErr := sqlFetcher.FetchSQLV2(ctx, BuildFetchParam{
		WithReplay:    true,
		WithExecCodes: false,
		Step:          "Replaying SQL",
	})
	if fetchErr != nil {
		log.Errorf("Failed to fetch SQL statements (V2), channelId %d, taskId %d, error: %v", i.channelID, i.taskID, fetchErr)
		return fetchErr
	}
	go sqlFetcher.CloseChanIfNeed(ctx)

	log.Infof("Starting SQLSet data replay, channelId:%d, taskId:%d, totalNum:%d, totalNotReplayNum:%d",
		i.channelID, i.taskID, statementsSummary.TotalNum, statementsSummary.NotReplayNum)

	tidbConn, err := i.dbConnector.OpenTiDB()
	if err != nil {
		log.Errorf("Failed to open TiDB database, channelId %d, taskId %d, error: %v", i.channelID, i.taskInfo.TaskID, err)
		return err
	}
	defer tidbConn.Close()

	// Get concurrency thread count
	explainThread := i.parameter.GetSQLFactor().GetAnalyzeThread()
	oracleConn.SetMaxOpenConns(explainThread * 2)
	oracleConn.SetMaxIdleConns(explainThread * 2)

	tracker := tool.BuildProgressHelperV2(i.channelID, i.taskID, statementsSummary.TotalNum)
	tracker.SetReplayNums(statementsSummary.NotReplayNum)
	tracker.DisplayAndSaveReplayProgress(ctx)
	defer tracker.CloseReplayTracker(ctx)

	// Get ignored and resolved SQL IDs for filtering
	filteredSQLs, err := i.getIgnoredAndResolvedSQLIds(ctx)
	if err != nil {
		log.Errorf("Failed to get ignored and resolved SQL IDs, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, err)
		return err
	}

	// Use WaitGroup to control all workers' completion
	var wg sync.WaitGroup

	// Start a fixed number of worker goroutines
	for j := 0; j < explainThread; j++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			i.processSQLWorker(ctx, sqlFetcher.oracleSQLChan, tidbConn, oracleConn, tracker, filteredSQLs)
		}()
	}

	// Wait for all workers to complete
	wg.Wait()

	return nil
}

func (i *SQLReplayCoordinator) rewriteSQLSyntax(ctx context.Context) error {
	tracker := tool.BuildEmptyProgressHelperV2(i.channelID, i.taskID)

	if !i.parameter.EnableSQLRewrite {
		tracker.SaveSQLSyntaxRewriteSkipProgress(ctx)
		return nil
	} else {
		tracker.SaveSQLSyntaxRewriteEnableProgress(ctx)
	}

	sqlFetcher := NewSQLFetcher(i.taskID, i.parameter)

	statementsSummary, fetchErr := sqlFetcher.FetchSQLV2(ctx, BuildFetchParam{
		WithReplay:    false,
		WithExecCodes: true,
		Step:          "Rewriting SQL",
	})
	if fetchErr != nil {
		log.Errorf("Failed to fetch SQL statements (V2), channelId %d, taskId %d, error: %v", i.channelID, i.taskID, fetchErr)
		return fetchErr
	}
	go sqlFetcher.CloseChanIfNeed(ctx)

	log.Infof("Starting SQL syntax rewrite, channelId:%d, taskId:%d, totalNum:%d",
		i.channelID, i.taskID, statementsSummary.TotalNum)

	oracleConn, err := i.dbConnector.OpenOracle()
	if err != nil {
		log.Errorf("Failed to open Oracle database, channelId %d, taskId %d, error: %v", i.channelID, i.taskInfo.TaskID, err)
		return err
	}
	defer oracleConn.Close()

	aiProxy := ai.NewAIConvertorProxy(i.taskID, i.parameter.LLMProviderAPIConfig)

	tracker.DisplayAndSaveRewriteProgress(ctx)
	defer tracker.CloseRewriteTracker(ctx)

	// Get ignored and resolved SQL IDs for filtering
	filteredSQLs, err := i.getIgnoredAndResolvedSQLIds(ctx)
	if err != nil {
		log.Errorf("Failed to get ignored and resolved SQL IDs, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, err)
		return err
	}

	rewriteResultChan := make(chan *structs.SpaSqlStmt, 100)

	go i.invokeAIConvert(ctx, sqlFetcher, aiProxy, rewriteResultChan, tracker, filteredSQLs)

	saveErr := i.saveAIConvertResults(ctx, oracleConn, rewriteResultChan, tracker)
	if saveErr != nil {
		log.Errorf("Failed to save AI rewrite results, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, saveErr)
		return saveErr
	}

	return nil
}

func (i *SQLReplayCoordinator) saveAIConvertResults(ctx context.Context, oracleConn *sql.DB, rewriteResultChan chan *structs.SpaSqlStmt, tracker *tool.ProgressTrackerV2) error {
	for rewriteResult := range rewriteResultChan {
		if rewriteResult.RewriteSQLText == "" {
			continue
		}

		// Update main table AI rewrite SQL
		updateErr := models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsRewriteInfo(ctx, oracleConn, i.taskID, rewriteResult.OraSqlId, rewriteResult.RewriteSQLText, rewriteResult.RewritePrompts)
		if updateErr != nil {
			log.Errorf("Failed to update rewrite info in TMS_SQLSET_STATEMENTS, channelId %d, taskId %d, sqlId:%s, error: %v",
				i.channelID, i.taskID, rewriteResult.OraSqlId, updateErr)
			return updateErr
		}
	}
	return nil
}

func (i *SQLReplayCoordinator) invokeAIConvert(ctx context.Context, sqlFetcher *SQLFetcher, aiProxy *ai.AIConvertorProxy, rewriteResultChan chan *structs.SpaSqlStmt, tracker *tool.ProgressTrackerV2, filteredSQLs map[string]bool) {
	for oracleSQL := range sqlFetcher.oracleSQLChan {
		// Skip if SQL is ignored or resolved
		if filteredSQLs[oracleSQL.OraSqlId] {
			continue
		}

		i.sqlAnalyzer.RewriteSQL(oracleSQL)

		item := &provider.RewriteSQLItem{
			SQL:          oracleSQL.TidbSqlText,
			TiDBExecCode: oracleSQL.TidbExecCode,
		}
		rewriteSQL, prompts, convertErr := aiProxy.Convert(ctx, item)
		if convertErr != nil {
			log.Errorf("Failed to rewrite SQL statement, taskId:%d, sql:%s, err:%v", i.taskID, item.SQL, convertErr)
			tracker.IncreaseRewriteFailedNum()
			continue
		}

		oracleSQL.RewriteSQLText = rewriteSQL
		oracleSQL.RewritePrompts = strings.Join(prompts, ";")

		tracker.IncreaseRewriteSuccessNum()
		rewriteResultChan <- oracleSQL
	}
	close(rewriteResultChan)
}

// processSQLWorker processes a single SQL
func (i *SQLReplayCoordinator) processSQLWorker(ctx context.Context,
	oracleSQLChan <-chan *structs.SpaSqlStmt,
	tidbConn *sql.DB,
	oracleConn *sql.DB,
	tracker *tool.ProgressTrackerV2,
	filteredSQLs map[string]bool,
) {
	for oracleSQL := range oracleSQLChan {
		// Skip if SQL is ignored or resolved
		if filteredSQLs[oracleSQL.OraSqlId] {
			tracker.IncreaseFilteredNum()
			continue
		}

		// Rewrite SQL
		i.sqlAnalyzer.RewriteSQL(oracleSQL)

		// Analyze SQL
		explainResult := i.sqlAnalyzer.ExplainSQLV2(ctx, oracleSQL, tidbConn, i.schemaMapper)
		if explainResult.IsTimeout {
			tracker.IncreaseReplayTimeoutNum()
		} else if explainResult.Err != nil {
			tracker.IncreaseReplayFailedNum()
		} else {
			tracker.IncreaseReplaySuccessNum()
		}

		// Update replay status
		updateReplayStatusErr := models.GetDatasourceReaderWriter().UpdateTMSSQLSetStatementsReplayStatus(ctx,
			oracleConn,
			i.taskID,
			oracleSQL.OraSqlId,
			explainResult.GetReplayStatus(),
			explainResult.GetReplayExecCode(),
			explainResult.GetFinishTime(),
			explainResult.GetErrorMessage(),
		)
		if updateReplayStatusErr != nil {
			log.Errorf("Failed to update replay status in TMS_SQLSET_STATEMENTS, channelId %d, taskId %d, sqlId:%s, error: %v",
				i.channelID, i.taskID, oracleSQL.OraSqlId, updateReplayStatusErr)
			continue
		}
	}
}

// getIgnoredAndResolvedSQLIds retrieves SQL IDs that have been marked as ignored or resolved
func (i *SQLReplayCoordinator) getIgnoredAndResolvedSQLIds(ctx context.Context) (map[string]bool, error) {
	statusList := []string{constants.UserOperateStatusIgnored, constants.UserOperateStatusResolved}
	userOps, err := models.GetSqlAnalyzerReaderWriter().GetSqlStmtUserOperationV2ByTaskIdAndStatus(ctx, i.taskID, statusList)
	if err != nil {
		log.Errorf("Failed to get ignored and resolved SQL IDs, channelId %d, taskId %d, error: %v", i.channelID, i.taskID, err)
		return nil, err
	}

	filteredSQLs := make(map[string]bool)
	ignoredCount := 0
	resolvedCount := 0

	for oraSqlId, userOp := range userOps {
		filteredSQLs[oraSqlId] = true
		if userOp.UserOperateStatus == constants.UserOperateStatusIgnored {
			ignoredCount++
		} else if userOp.UserOperateStatus == constants.UserOperateStatusResolved {
			resolvedCount++
		}
	}

	log.Infof("Retrieved %d ignored/resolved SQL IDs for filtering (ignored: %d, resolved: %d), channelId %d, taskId %d",
		len(filteredSQLs), ignoredCount, resolvedCount, i.channelID, i.taskID)
	return filteredSQLs, nil
}

func buildSqlStmtExecResultModel(channelId int, taskId int, sqlStmt *structs.SpaSqlStmt) *sqlanalyzer.SqlStmtExecResult {
	fields := strings.Fields(sqlStmt.TidbSqlText)
	var sqlType string
	if len(fields) != 0 {
		sqlType = strings.Fields(sqlStmt.TidbSqlText)[0]
	} else {
		sqlType = ""
	}
	return &sqlanalyzer.SqlStmtExecResult{
		ChannelId: channelId,
		TaskId:    taskId,
		//TotalSQLNum:          totalSQLNum,
		OraSqlsetName:        sqlStmt.OraSqlsetName,
		OraSqlsetOwner:       sqlStmt.OraSqlsetOwner,
		OraModule:            sqlStmt.OraModule,
		OraSqlId:             sqlStmt.OraSqlId,
		OraSqlText:           sqlStmt.OraSqlText,
		OraParsingSchemaName: sqlStmt.OraParsingSchemaName,
		OraPlanHashValue:     sqlStmt.OraPlanHashValue,
		OraParameterPosition: sqlStmt.OraParameterPosition,
		OraParameterValue:    sqlStmt.OraParameterValue,
		OraExecutions:        sqlStmt.OraExecutions,
		OraElapsedTimeMs:     sqlStmt.OraElapsedTimeMs,
		OraLastExecStartTime: sqlStmt.OraLastExecStartTime,
		OraPlanTimestamp:     sqlStmt.OraPlanTimestamp,
		OraCommandType:       sqlStmt.OraCommandType,
		TidbSqlType:          strings.ToUpper(sqlType),
		TidbSqlText:          sqlStmt.TidbSqlText,
		TidbExecStatus:       sqlStmt.TidbExecStatus,
		TidbExecCode:         sqlStmt.TidbExecCode,
		TidbExecMsg:          sqlStmt.TidbExecMsg,
		TidbPlan:             sqlStmt.TidbPlan,
		TidbElapsedTimeMs:    sqlStmt.TidbElapsedTimeMs,
		TidbEngineTimeMs:     sqlStmt.TidbEngineTimeMs,
		TidbExecTime:         sqlStmt.TidbExecTime,
		TidbDigest:           sqlStmt.TidbDigest,
		TidbUseWhereFunc:     sqlStmt.TidbUseWhereFunc,
		TidbUseNotExists:     sqlStmt.TidbUseNotExists,
		TidbPlanNotGood:      sqlStmt.TidbPlanNotGood,
		TmsExecMsg:           sqlStmt.TmsExecMsg,
	}
}
