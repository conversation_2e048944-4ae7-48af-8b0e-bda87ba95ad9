package sqlanalyze

import (
	"context"
	"regexp"
	"strings"

	"gitee.com/pingcap_enterprise/tms/server/crypto"
	"gitee.com/pingcap_enterprise/tms/util/parse"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	datasourcepkg "gitee.com/pingcap_enterprise/tms/pkg/datasource"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// BuildSQLAnalyzeParameter 构建SQL分析参数
func BuildSQLAnalyzeParameter(ctx context.Context, channelId int, taskId int) (*structs.Parameter, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		log.Errorf("get task info failed, taskId:%d, err:%v", taskId, err)
		return nil, err
	}
	channel, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		log.Errorf("get channel failed, channelId:%d, err:%v", channelId, err)
		return nil, err
	}
	sourceDB, err := models.GetDatasourceReaderWriter().Get(ctx, channel.DatasourceIdS)
	if err != nil {
		log.Errorf("get datasource failed, Datasource id is %d, error: %v", channel.DatasourceIdS, err)
		return &structs.Parameter{}, err
	}

	defaultSubParams, getSubParamsErr := models.GetTaskReaderWriter().BatchGetTaskSubParamConfigs(ctx, 0, 0)
	if getSubParamsErr != nil {
		log.Errorf("get default sub param list by TaskparamTemplateId failed, err:%v", getSubParamsErr)
		return nil, getSubParamsErr
	}
	taskSubParams, getTaskSubParamsErr := models.GetTaskReaderWriter().BatchGetTaskSubParamConfigs(ctx, channelId, taskId)
	if getTaskSubParamsErr != nil {
		log.Errorf("get task sub param list by TaskparamTemplateId and channelId, taskId  failed, err:%v", getTaskSubParamsErr)
		return nil, getTaskSubParamsErr
	}

	mergedSubParamMapping := make(map[string][]*task.TaskSubParamConfig)
	defaultSubParamMapping := make(map[string][]*task.TaskSubParamConfig)
	taskSubParamMapping := make(map[string][]*task.TaskSubParamConfig)
	for _, subParam := range defaultSubParams {
		if _, ok := defaultSubParamMapping[subParam.ParamName]; !ok {
			defaultSubParamMapping[subParam.ParamName] = make([]*task.TaskSubParamConfig, 0)
		}
		defaultSubParamMapping[subParam.ParamName] = append(defaultSubParamMapping[subParam.ParamName], subParam)
	}
	for _, subParam := range taskSubParams {
		if _, ok := taskSubParamMapping[subParam.ParamName]; !ok {
			taskSubParamMapping[subParam.ParamName] = make([]*task.TaskSubParamConfig, 0)
		}
		taskSubParamMapping[subParam.ParamName] = append(taskSubParamMapping[subParam.ParamName], subParam)
	}
	for k, v := range defaultSubParamMapping {
		mergedSubParamMapping[k] = v
	}
	for k, v := range taskSubParamMapping {
		mergedSubParamMapping[k] = v
	}

	dbS := &structs.DBConfigOracle{
		Host:     sourceDB.HostIp,
		Port:     sourceDB.HostPort,
		User:     sourceDB.UserName,
		Password: sourceDB.PasswordValue,
		Sid:      datasourcepkg.GetServiceName(sourceDB),
	}

	targetDB, err := models.GetDatasourceReaderWriter().Get(ctx, channel.DatasourceIdT)
	if err != nil {
		log.Errorf("get datasource failed, Datasource id is %d, error: %v", channel.DatasourceIdT, err)
		return &structs.Parameter{}, err
	}
	dbT := &structs.DBConfigTiDB{
		Host:     targetDB.HostIp,
		Port:     targetDB.HostPort,
		User:     targetDB.UserName,
		Password: targetDB.PasswordValue,
		Database: targetDB.DbName,
	}

	templateParamInfos, err := models.GetTaskReaderWriter().ListTemplateParams(ctx, taskInfo.TaskParamTmplateId)
	if err != nil {
		log.Errorf("list templates param detail failed. err: %v", err)
		return nil, err
	}
	customTaskCfgs, err := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           channelId,
		TaskID:              taskId,
		TaskparamTemplateID: taskInfo.TaskParamTmplateId,
	})
	if err != nil {
		log.Errorf("get BatchGetTaskParam info failed. err: %v", err)
		return nil, err
	}

	mergeParamMapping := make(map[string]paramExt)
	for _, templateParamInfo := range templateParamInfos {
		mergeParamMapping[templateParamInfo.ParamName] = paramExt{
			ParamValue:   templateParamInfo.ParamValueDefault,
			HasSubParams: templateParamInfo.HasSubParams,
		}
	}

	for _, customTaskCfg := range customTaskCfgs {
		// 跳过，避免因为旧数据导致参数类型出错
		if defaultParam, ok := mergeParamMapping[customTaskCfg.ParamName]; ok {
			if defaultParam.HasSubParams == true && customTaskCfg.HasSubParams == false {
				continue
			}
		}

		mergeParamMapping[customTaskCfg.ParamName] = paramExt{
			ParamValue:   customTaskCfg.ParamValueCurrent,
			HasSubParams: customTaskCfg.HasSubParams,
		}
	}

	log.Infof("get config param detail finish, total: %d, customTotal: %d", len(mergeParamMapping), len(customTaskCfgs))

	parameter, buildErr := buildSQLAnalyzeParameter(channelId, taskId, dbT, dbS, mergeParamMapping, mergedSubParamMapping)
	if buildErr != nil {
		return nil, buildErr
	}

	return parameter, nil
}

type paramExt struct {
	ParamValue   string
	HasSubParams bool
}

func getSubParamValues(cc []*task.TaskSubParamConfig) []string {
	if len(cc) == 0 {
		return []string{}
	}
	arr := make([]string, 0)
	for _, c := range cc {
		val := strings.TrimSpace(c.ParamValue)
		if val == "" {
			continue
		}
		if val == constants.FakeParamValue {
			continue
		}
		arr = append(arr, val)
	}
	return arr
}

func buildSQLAnalyzeParameter(channelId, taskId int, dbT *structs.DBConfigTiDB, dbS *structs.DBConfigOracle, mergeParamMapping map[string]paramExt, mergedSubParamMapping map[string][]*task.TaskSubParamConfig) (*structs.Parameter, error) {
	parameter := &structs.Parameter{
		ChannelID:     channelId,
		TaskID:        taskId,
		TidbConnStr:   dbT,
		OracleConnStr: dbS,
	}
	sqlFactor := &structs.SQLFactor{}

	for paramName, ext := range mergeParamMapping {
		switch paramName {
		case constants.ParamsSQLAnalyzeExcludeSQLText:
			sqlFactor.SetExcludeSQLTxts(getSubParamValues(mergedSubParamMapping[paramName]))
		case constants.ParamsSQLAnalyzeExcludeSQLId:
			sqlFactor.SetExcludeSQLIDs(getSubParamValues(mergedSubParamMapping[paramName]))
		case constants.ParamsSQLAnalyzeExcludeMode:
			sqlFactor.SetExcludeModules(getSubParamValues(mergedSubParamMapping[paramName]))
		case constants.ParamsSQLAnalyzeIncludingSchema:
			sqlFactor.SetIncludingSchemas(getSubParamValues(mergedSubParamMapping[paramName]))
		case constants.ParamsSQLAnalyzeIncludingSQLType:
			sqlFactor.SetIncludingSQLTypes(getSubParamValues(mergedSubParamMapping[paramName]))
		case constants.ParamsSQLAnalyzeTiDBExecStatusFilter:
			sqlFactor.SetExecStatusFilter(getSubParamValues(mergedSubParamMapping[paramName]))
		case constants.ParamsSQLAnalyzeExplainTimeout, constants.ParamsSQLAnalyzeAnalyzeTimeout: // 新版本是analyze_timeout, 为了兼容老版本
			sqlFactor.SetAnalyzeTimeout(parse.ParsePositiveIntWithDefault(ext.ParamValue, 5))
		case constants.ParamsSQLAnalyzeAppSQLsSource:
			parameter.SetAppSQLsSource(ext.ParamValue)
		case constants.ParamsSQLAnalyzeAppSQLsFilename:
			parameter.SetAppSQLsFilename(ext.ParamValue)
		case constants.ParamsSQLAnalyzeIgnoreSQLIdsFilename:
			parameter.SetIgnoreSQLIDsFilename(ext.ParamValue)
		case constants.ParamsSQLAnalyzeExplainOnly:
			parameter.SetExplainOnly(ext.ParamValue)
		case constants.ParamsSQLAnalyzeSQLOrderBy:
			sqlFactor.SetSQLOrderBy(ext.ParamValue)
		case constants.ParamsSQLAnalyzeSQLRowLimit:
			sqlFactor.SetSQLRowLimit(parse.ParsePositiveIntWithDefault(ext.ParamValue, 10000))
		case constants.ParamsSQLAnalyzeAnalyzeThread:
			sqlFactor.SetAnalyzeThread(parse.ParsePositiveIntWithDefault(ext.ParamValue, 8))
		case constants.ParamsSQLAnalyzeTopNPerStatus:
			sqlFactor.SetTopNPerStatus(parse.ParsePositiveIntWithDefault(ext.ParamValue, 1000))
		case constants.ParamsSQLAnalyzeSQLSetName:
			sqlFactor.SetSQLSetName(ext.ParamValue)
		case constants.ParamsSQLAnalyzeRunVersion:
			parameter.SetRunVersion(ext.ParamValue)
		case constants.ParamsSQLAnalyzeOpenAIKey:
			// 使用正则匹配（不区分大小写）
			matched, _ := regexp.MatchString(`(?i)^ENC_`, ext.ParamValue)
			if matched { //已加密
				decryptKey := crypto.AesDefaultDecrypt(ext.ParamValue[len("ENC_"):], crypto.DefaultKey)
				parameter.SetAPIKey(decryptKey)
			} else { //未加密
				parameter.SetAPIKey(ext.ParamValue)
			}

		case constants.ParamsSQLAnalyzeOpenAIModel:
			parameter.SetModel(ext.ParamValue)
		case constants.ParamsSQLAnalyzeOpenAIEndpoint:
			parameter.SetEndpoint(ext.ParamValue)
		case constants.ParamsSQLAnalyzeOpenAISource:
			parameter.SetSource(ext.ParamValue)
		case constants.ParamsSQLAnalyzeEnableOpenAISuggestion, constants.ParamsSQLAnalyzeEnableSQLRewrite:
			parameter.SetEnableSQLRewrite(parse.ParseBoolWithDefault(ext.ParamValue, false))
		case constants.ParamsObjectParserLLMMaxTokens:
			intVal, _ := parse.ParseInt(ext.ParamValue)
			parameter.SetMaxTokens(intVal)
		case constants.ParamsObjectParserLLMTimeout:
			intVal, _ := parse.ParseInt(ext.ParamValue)
			parameter.SetTimeout(intVal)
		case constants.ParamsObjectParserLLMTemperature:
			float32Val, _ := parse.ParseFloat32(ext.ParamValue)
			parameter.SetTemperature(float32Val)
		case constants.ParamsObjectParserLLMStream:
			parameter.SetStream(parse.ParseBoolWithDefault(ext.ParamValue, false))
		case constants.ParamsObjectParserLLMMaxTokensName:
			parameter.SetMaxTokensName(ext.ParamValue)
		case "db_connection_str":
		default:
			log.Errorf("param name is not valid. taskId:%d, param name:%s, value:%v", taskId, paramName, ext)
		}
	}

	parameter.SQLFactor = sqlFactor
	initErr := parameter.InitParameter()
	if initErr != nil {
		log.Errorf("Init failed. taskId:%d, err:%v", taskId, initErr)
		return nil, errors.NewError(errors.TMS_SQLANALYZE_BUILD_PARAM_FAILED, initErr.Error())
	}

	validateErr := parameter.ValidateOpenAIConfig()
	if validateErr != nil {
		log.Errorf("validate openai config failed. taskId:%d, err:%v", taskId, validateErr)
		return nil, errors.NewError(errors.TMS_SQLANALYZE_BUILD_PARAM_FAILED, validateErr.Error())
	}

	log.Infof("build parameter success, taskId:%d, config:%v", taskId, parameter.String())

	return parameter, nil
}
