package sqlanalyze

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"errors"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	perror "github.com/pingcap/errors"
	"github.com/pingcap/tidb/pkg/parser"

	"regexp"
	"strconv"
	"strings"
	"time"

	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/tool"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"github.com/go-sql-driver/mysql"
)

// SQLAnalyzer 处理 SQL 分析和改写
type SQLAnalyzer struct {
	parameter  *structs.Parameter
	digestType common.DigestType
	taskId     int
}

func NewSQLAnalyzer(taskId int, param *structs.Parameter, digestType common.DigestType) *SQLAnalyzer {
	return &SQLAnalyzer{
		taskId:     taskId,
		parameter:  param,
		digestType: digestType,
	}
}

func (sa *SQLAnalyzer) RewriteSQL(stmt *structs.SpaSqlStmt) {
	sqlStmtStr := stmt.OraSqlText
	sqlStmtParaPos := stmt.OraParameterPosition
	sqlStmtParaVal := stmt.OraParameterValue
	var paraPosLst, paraValLst []string
	paraValDict := make(map[string]string)
	if sqlStmtParaPos != "" {
		paraPosLst = strings.Split(sqlStmtParaPos, ";,")
		paraValLst = strings.Split(sqlStmtParaVal, ";,")
		for i, pos := range paraPosLst {
			paraValDict[pos] = paraValLst[i]
		}
	}
	sqlParameters := pickSQLParameters(sqlStmtStr)
	sqlStmtStr = remoteHintNode(sqlStmtStr)
	sqlStmtTiDB := rewriteOracleSQLToTiDB(sqlStmtStr, sqlParameters, paraPosLst, paraValLst)
	sqlStmtTiDB = replaceLimitNode(sqlStmtTiDB)
	stmt.TidbSqlText = sqlStmtTiDB
	fields := strings.Fields(sqlStmtTiDB)
	if len(fields) != 0 {
		stmt.TidbSqlType = fields[0]
	}
	stmt.TidbUseNotExists = extractNotExistNode(sqlStmtTiDB)
	stmt.TidbUseWhereFunc = extractWhereFuncNode(sqlStmtTiDB)
	if stmt.TidbDigest == "" {
		stmt.TidbDigest = parser.DigestNormalized(parser.Normalize(sqlStmtTiDB, perror.RedactLogEnable)).String()
	}
	return
}

func (sa *SQLAnalyzer) GetSQLDigestID(stmt *structs.SpaSqlStmt) string {
	sqlStmtStr := stmt.OraSqlText
	sqlStmtParaPos := stmt.OraParameterPosition
	sqlStmtParaVal := stmt.OraParameterValue
	var paraPosLst, paraValLst []string
	if sqlStmtParaPos != "" {
		paraPosLst = strings.Split(sqlStmtParaPos, ";,")
		paraValLst = strings.Split(sqlStmtParaVal, ";,")
	}
	sqlParameters := pickSQLParameters(sqlStmtStr)
	sqlStmtStr = remoteHintNode(sqlStmtStr)
	sqlStmtTiDB := rewriteOracleSQLToTiDB(sqlStmtStr, sqlParameters, paraPosLst, paraValLst)
	sqlStmtTiDB = replaceLimitNode(sqlStmtTiDB)
	stmt.TidbSqlText = sqlStmtTiDB
	fields := strings.Fields(sqlStmtTiDB)
	if len(fields) != 0 {
		stmt.TidbSqlType = fields[0]
	}
	return sa.generateDigestID(sqlStmtTiDB)
}

func (sa *SQLAnalyzer) generateDigestID(sqlStmtTiDB string) string {
	var val string

	switch sa.digestType {
	case common.DIGEST_TYPE_TiDB:
		val = parser.DigestNormalized(parser.Normalize(sqlStmtTiDB, perror.RedactLogEnable)).String()
	case common.DIGEST_TYPE_ORACLE:
		val = tool.DigestNormalized(tool.Normalize(sqlStmtTiDB))
	case common.DIGEST_TYPE_Mixed:
		tidbVal := parser.DigestNormalized(parser.Normalize(sqlStmtTiDB, perror.RedactLogEnable)).String()
		oracleVal := tool.DigestNormalized(tool.Normalize(sqlStmtTiDB))

		// 结合 tidbVal 和 oracleVal 生成 64 字符哈希
		combined := tidbVal + "|" + oracleVal // 用分隔符连接，避免歧义
		hash := sha256.Sum256([]byte(combined))
		hashStr := hex.EncodeToString(hash[:]) // SHA-256 生成 64 字符十六进制字符串

		// 确保长度正好为 64 字符（SHA-256 已满足，但为清晰起见保留逻辑）
		if len(hashStr) > 64 {
			val = hashStr[:64]
		} else if len(hashStr) < 64 {
			val = fmt.Sprintf("%-64s", hashStr) // 左对齐填充空格
		} else {
			val = hashStr
		}
	}

	return val
}
func (sa *SQLAnalyzer) MatchSchema(stmt *structs.SpaSqlStmt, visitor *tool.SchemaTableVisitor) bool {
	p := parser.New()
	tidbSQL := strings.ReplaceAll(stmt.TidbSqlText, "#", "_")
	sqlRoot, err := p.ParseOneStmt(tidbSQL, "", "")
	if err != nil {
		return true
	}
	visitor.Reset()
	sqlRoot.Accept(visitor)
	return sa.parameter.MatchAtLeastOneSchema(visitor.GetSchemaNames())
}

func (sa *SQLAnalyzer) ExplainSQL(ctx context.Context, stmt *structs.SpaSqlStmt, tidbConn *sql.DB, mapper *structs.ChannelSchemaMapper) *structs.ExplainResult {
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(sa.parameter.SQLFactor.GetAnalyzeTimeout())*time.Second)
	defer cancel()
	stmt.TidbExecTime = time.Now()
	ch := make(chan *structs.ExplainResult)
	go func() {
		ch <- sa.explainSQLInTiDB(timeoutCtx, stmt, tidbConn, mapper)
	}()

	select {
	case <-timeoutCtx.Done():
		stmt.TidbExecStatus = constants.TiDBExplainTimeout
		return &structs.ExplainResult{Stmt: stmt}
	case ret := <-ch:
		return ret
	}
}

func (sa *SQLAnalyzer) ExplainSQLV2(ctx context.Context, stmt *structs.SpaSqlStmt, tidbConn *sql.DB, mapper *structs.ChannelSchemaMapper) *structs.ExplainResult {
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(sa.parameter.SQLFactor.AnalyzeTimeout)*time.Second)
	defer cancel()
	stmt.TidbExecTime = time.Now()
	ch := make(chan *structs.ExplainResult)
	go func() {
		ch <- sa.explainSQLInTiDB(timeoutCtx, stmt, tidbConn, mapper)
	}()

	select {
	case <-timeoutCtx.Done():
		stmt.TidbExecStatus = constants.TiDBExplainTimeout
		return &structs.ExplainResult{Stmt: stmt, IsTimeout: true, Err: errors.New("replay timeout"), FinishTime: time.Now()}
	case ret := <-ch:
		return ret
	}
}

func (sa *SQLAnalyzer) explainSQLInTiDB(ctx context.Context, stmt *structs.SpaSqlStmt, tidbConn *sql.DB, mapper *structs.ChannelSchemaMapper) *structs.ExplainResult {
	sqlPrefix := getSQLExecPrefix(sa.parameter)
	if err := tool.NewDatabaseConnector(sa.parameter.OracleConnStr, sa.parameter.TidbConnStr).SetTiDBSchema(sa.parameter, stmt, tidbConn, mapper); err != nil {
		return &structs.ExplainResult{Stmt: stmt, Err: err, FinishTime: time.Now()}
	}
	startTime := time.Now()
	rows, err := tidbConn.Query(fmt.Sprintf("%s %s", sqlPrefix, stmt.TidbSqlText))
	stmt.TidbElapsedTimeMs = time.Since(startTime).Milliseconds()
	if err != nil {
		sa.handleExplainError(stmt, err)
	} else {
		defer rows.Close()
		stmt.TidbPlan = sa.processExplainRows(rows, sqlPrefix, stmt)
		if stmt.TidbExecStatus == "" {
			stmt.TidbExecStatus = constants.TiDBExplainSuccess
		}
	}
	return &structs.ExplainResult{Stmt: stmt, Err: err, FinishTime: time.Now()}
}

func (sa *SQLAnalyzer) handleExplainError(stmt *structs.SpaSqlStmt, err error) {
	stmt.TidbExecStatus = constants.TiDBExplainFailed
	var mysqlErr *mysql.MySQLError
	if errors.As(err, &mysqlErr) {
		stmt.TidbExecCode = strconv.FormatUint(uint64(mysqlErr.Number), 10)
	}
	stmt.TidbExecMsg = err.Error()
	if len(stmt.TidbExecMsg) > 2000 {
		stmt.TidbExecMsg = stmt.TidbExecMsg[:2000]
	}
}

func (sa *SQLAnalyzer) processExplainRows(rows *sql.Rows, sqlPrefix string, stmt *structs.SpaSqlStmt) string {
	var expPlan strings.Builder
	expId := 0
	for rows.Next() {
		expId++
		planLine, engineTimeMs, err := sa.scanExplainRow(rows, sqlPrefix, expId)
		if err != nil {
			log.Errorf("rows.Scan failed, sqlPrefix:%s, err:%s", sqlPrefix, err)
			continue
		}
		if expId == 1 && engineTimeMs > 0 {
			stmt.TidbEngineTimeMs = engineTimeMs
		}
		expPlan.WriteString(planLine)
	}
	return expPlan.String()
}

func (sa *SQLAnalyzer) scanExplainRow(rows *sql.Rows, sqlPrefix string, expId int) (string, float64, error) {
	var id, estRows, expTask string
	var actRows, execInfo, memory, disk string
	var accessObj, opeInfo sql.NullString
	if sqlPrefix == "explain analyze" {
		err := rows.Scan(&id, &estRows, &actRows, &expTask, &accessObj, &execInfo, &opeInfo, &memory, &disk)
		if err != nil {
			return "", 0, err
		}
		if strings.HasPrefix(execInfo, "time:") {
			return fmt.Sprintf("%d | %s | %s |\n", expId, id, estRows), calculateMs(execInfo), nil
		}
	} else {
		err := rows.Scan(&id, &estRows, &expTask, &accessObj, &opeInfo)
		if err != nil {
			return "", 0, err
		}
	}
	return fmt.Sprintf("%d | %s | %s |\n", expId, id, estRows), 0, nil
}

func calculateMs(execInfo string) float64 {
	execInfo = strings.Replace(execInfo, "time:", "", 1)

	hrRegexp := regexp.MustCompile(`\d+h`)
	hrList := hrRegexp.FindAllString(execInfo, -1)

	miRegexp := regexp.MustCompile(`\d+m\d`)
	miList := miRegexp.FindAllString(execInfo, -1)

	seRegexp := regexp.MustCompile(`[\d.]+s`)
	seList := seRegexp.FindAllString(execInfo, -1)

	msRegexp := regexp.MustCompile(`[\d.]+ms`)
	msList := msRegexp.FindAllString(execInfo, -1)

	usRegexp := regexp.MustCompile(`[\d.]+µs`)
	usList := usRegexp.FindAllString(execInfo, -1)

	nsRegexp := regexp.MustCompile(`[\d.]+ns`)
	nsList := nsRegexp.FindAllString(execInfo, -1)

	var ms float64

	if len(hrList) > 0 {
		hr, _ := strconv.ParseFloat(hrList[0][:len(hrList[0])-1], 64)
		ms += hr * 3600 * 1000
		return ms
	}
	if len(miList) > 0 {
		miRegexp2 := regexp.MustCompile(`\d+m`)
		mi2List := miRegexp2.FindAllString(miList[0], -1)
		mi, _ := strconv.ParseFloat(mi2List[0][:len(mi2List[0])-1], 64)
		ms += mi * 60 * 1000
		return ms
	}
	if len(seList) > 0 {
		se, _ := strconv.ParseFloat(seList[0][:len(seList[0])-1], 64)
		ms += se * 1000
		return ms
	}
	if len(msList) > 0 {
		msVal, _ := strconv.ParseFloat(msList[0][:len(msList[0])-2], 64)
		ms += msVal
		return ms
	}
	if len(usList) > 0 {
		usVal, _ := strconv.ParseFloat(strings.ReplaceAll(usList[0], "µs", ""), 64)
		ms += usVal / 1000
		return ms
	}
	if len(nsList) > 0 {
		nsVal, _ := strconv.ParseFloat(nsList[0][:len(nsList[0])-2], 64)
		ms += nsVal / 1000 / 1000
		return ms
	}

	return ms
}
