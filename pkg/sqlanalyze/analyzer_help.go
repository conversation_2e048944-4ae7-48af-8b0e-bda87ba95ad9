package sqlanalyze

import (
	"context"
	"regexp"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"github.com/samber/lo"
)

var (
	sysRegex         = regexp.MustCompile(`:[\w:]+\b|:SYS_.+?\b`)
	hintRegex        = regexp.MustCompile(`/\*.+?\*/`)
	fetchRowRegex    = regexp.MustCompile(`(?i)(fetch +first )(.*)(rows +only)`)
	andRowNumRegex   = regexp.MustCompile(`(?i)(and +rownum[<>= ]+)([\d]+)`)
	whereRowNumRegex = regexp.MustCompile(`(?i)(where +rownum[<>= ]+)([\d]+)`)
	notExistRegex    = regexp.MustCompile(`(?i)not +exists|not +in`)
	whereFuncRegex   = regexp.MustCompile(`(?i)(and|or|where)( +\w+\([\w.+-, /*%]+\))`)
)

func UpdateTaskStatus(ctx context.Context, taskStatus int, taskInfo *task.Task) error {
	log.Infof("start update task[%d] status to %d.", taskInfo.TaskID, taskStatus)
	// task start
	if taskStatus == constants.TASK_STATUS_RUNNING {
		taskInfo.TaskStatus = taskStatus
		taskInfo.StartTime = timeutil.GetNowTime()
		taskInfo.EndTime = timeutil.GetTMSNullTime()
		taskInfo.ErrorDetail = ""
	} else {
		taskInfo.TaskStatus = taskStatus
		taskInfo.EndTime = timeutil.GetNowTime()
	}
	_, err := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		log.Errorf("update task[%d] status to %d failed.", taskInfo.TaskID, taskStatus)
		return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to %d failed.", taskInfo.TaskID, taskStatus)
	}
	return nil
}

func getSQLExecPrefix(scriptParam *structs.Parameter) string {
	var sqlExecType = "explain analyze"
	if strings.ToLower(scriptParam.ExplainOnly) == "yes" {
		sqlExecType = "explain"
	}
	return sqlExecType
}

func extractNotExistNode(sql string) string {
	//vals := notExistRegex.FindAllString(sql, -1)
	//return strings.Join(vals, ",")
	return notExistRegex.FindString(sql)
}

func extractWhereFuncNode(sql string) string {
	//vals := whereFuncRegex.FindAllString(sql, -1)
	//return strings.Join(vals, ",")
	return whereFuncRegex.FindString(sql)
}

// pickSQLParameters 从字符串中提取:开头的变量名称
func pickSQLParameters(sql string) []string {
	if ok, _ := regexp.MatchString(":\"SYS_.+?\"", sql); ok {
		sql = strings.Replace(sql, "\"", "", -1)
	}
	parList := sysRegex.FindAllString(sql, -1)

	// para list
	parDelList := make([]string, 0)
	for _, par := range parList {
		tmpList := regexp.MustCompile(`:[\w]+`).FindAllString(par, -1)
		if len(tmpList) == 1 {
			parDelList = append(parDelList, par)
		}
	}

	return parDelList
}

// replaceLimitNode 移除rownum等limit语法
func replaceLimitNode(sql string) string {
	tmpSQL := sql
	tmpSQL = fetchRowRegex.ReplaceAllString(tmpSQL, `limit $2`)
	tmpSQL = andRowNumRegex.ReplaceAllString(tmpSQL, `limit $2`)
	tmpSQL = whereRowNumRegex.ReplaceAllString(tmpSQL, `limit $2`)
	return tmpSQL
}

// remoteHintNode 移除hint
func remoteHintNode(sql string) string {
	re := hintRegex
	hintList := re.FindAllString(sql, -1)
	for _, k := range hintList {
		sql = strings.Replace(sql, k, "", -1)
	}
	sql = strings.Replace(sql, "\n", " ", -1)
	sql = strings.Replace(sql, "\t", " ", -1)
	sql = strings.Replace(sql, "\"", "", -1)
	return sql
}

// formatOracleTimestamp 格式化Oracle时间
// 01-JUN-22 12.00.00.000000 AM to
// YYYY-MM-DD HH24.MI.SI.SSSSSS
func formatOracleTimestamp(ts string) string {
	mon_dict := map[string]string{
		"JAN": "01", "FEB": "02", "MAR": "03", "APR": "04",
		"MAY": "05", "JUN": "06", "JUL": "07", "AUG": "08",
		"SEP": "09", "OCT": "10", "NOV": "11", "DEC": "12",
	}
	ts_lst := strings.Split(strings.Replace(ts, "'", "", -1), " ")
	ts_dt := strings.Split(ts_lst[0], "-")
	ts_dt2 := "20" + ts_dt[2] + "-" + mon_dict[ts_dt[1]] + "-" + ts_dt[0]
	ts_tm2 := "00:00:00"
	if ts_lst[2] == "PM" {
		ts_tm := strings.Split(ts_lst[1], ".")
		if i, _ := strconv.Atoi(ts_tm[0]); i < 12 {
			ts_tm[0] = strconv.Itoa(i + 12)
		}
		ts_tm2 = strings.Join(ts_tm, ".")
	} else {
		ts_tm2 = ts_lst[1]
	}

	return "'" + ts_dt2 + ts_tm2 + "'"
}

func rewriteOracleSQLToTiDB(sql string, paraAll []string, paraPos []string, paraVal []string) string {
	for i := len(paraAll) - 1; i >= 0; i-- {
		k := paraAll[i]
		kPos := strconv.Itoa(i + 1)
		if lo.Contains(paraPos, kPos) {
			v1Str := paraVal[lo.IndexOf(paraPos, kPos)]
			if !strings.Contains(v1Str, ":") {
				if matched, _ := regexp.MatchString(`\d{2}-[A-Z]{3}-\d{2}[\d. ]+[AP]M`, v1Str); matched {
					v1Str = formatOracleTimestamp(v1Str)
				}
				sql = strings.Replace(sql, k, v1Str, 1)
			}
		} else {
			sql = strings.Replace(sql, k, "''", 1)
		}
	}
	for i := len(paraAll) - 1; i >= 0; i-- {
		k := paraAll[i]
		kPos := strconv.Itoa(i + 1)
		if lo.Contains(paraPos, kPos) {
			value := paraVal[lo.IndexOf(paraPos, kPos)]
			if strings.Contains(value, ":") {
				sql = strings.Replace(sql, k, value, 1)
			}
		}
	}
	return sql
}
