package sqlanalyze

import (
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/tool"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"testing"
)

func TestSQLAnalyzer_generateDigestID(t *testing.T) {
	log.InitTestLogger()

	digestType := common.DIGEST_TYPE_TiDB

	type fields struct {
		parameter  *structs.Parameter
		cache      *tool.AnalyzeCache
		digestType common.DigestType
	}
	type args struct {
		sqlStmtTiDB string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name:   "sys.obj",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( :1 ,:2  )",
			},
			want: "e5540b909507f7638fb57d169dae61d0730cebeac443863a984a7f14b4465693",
		},
		{
			name:   "sys.obj2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( ?,?  )",
			},
			want: "e5540b909507f7638fb57d169dae61d0730cebeac443863a984a7f14b4465693",
		},
		{
			name:   "sys.obj3",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( 1,2 )",
			},
			want: "e5540b909507f7638fb57d169dae61d0730cebeac443863a984a7f14b4465693",
		},
		{
			name:   "sys.obj4",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( '1','2' )",
			},
			want: "e5540b909507f7638fb57d169dae61d0730cebeac443863a984a7f14b4465693",
		},
		{
			name:   "sys.obj_1",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj#
from sys.obj$
where owner# = :1 
  and status > 1
  and name not like 'BIN$%'`,
			},
			want: "87970b4e904a0f791aab079458ec6f53bba8080355d765137f6bd7114c814635",
		},
		{
			name:   "sys.obj_2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj#
from sys.obj$
where owner# = ? 
  and status > 1
  and name not like 'BIN$%'`,
			},
			want: "87970b4e904a0f791aab079458ec6f53bba8080355d765137f6bd7114c814635",
		},
		{
			name:   "sys.obj_3",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj#
from sys.obj$
where owner# = ? 
  and status > 1
  and name not like 'BIN$%'`,
			},
			want: "87970b4e904a0f791aab079458ec6f53bba8080355d765137f6bd7114c814635",
		},
		{
			name:   "sys.obj_!1",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj# from sys.obj$ where owner# = 108    and status > 1   and name not like 'BIN$%'`,
			},
			want: "8f0c42b2593d9069cc3ac4301ffbf8a0c198f2323907530ba338774ad04b7602",
		},
		{
			name:   "sys.obj_!2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj# object_id, mtime modified_timestamp from sys.obj$ where obj# in ( 207392 ,207393  )`,
			},
			want: "8f0c42b2593d9069cc3ac4301ffbf8a0c198f2323907530ba338774ad04b7602",
		},
		{
			name:   "sql_replacement1",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `SELECT * FROM tms.sql_replacement WHERE sql_id = :1 AND sql_text = :2`,
			},
			want: "05d19e73039fe2185ef16f6d4b2e6ab9b8ce61d07dc6b61e37f102ad96c2e880",
		},
		{
			name:   "sql_replacement2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `SELECT * FROM tms.sql_replacement WHERE sql_id = ? AND sql_text = ?`,
			},
			want: "033f0c1a6c2c7024453c92a6aa0171ed0dbec485552302d1cda595bebb8f2837",
		},
		{
			name:   "sql_replacement3",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `SELECT * FROM tms.sql_replacement WHERE sql_id = 12 AND sql_text = "xx"`,
			},
			want: "033f0c1a6c2c7024453c92a6aa0171ed0dbec485552302d1cda595bebb8f2837",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sa := &SQLAnalyzer{
				parameter:  tt.fields.parameter,
				digestType: digestType,
			}
			if got := sa.generateDigestID(tt.args.sqlStmtTiDB); got != tt.want {
				t.Errorf("generateDigestID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSQLAnalyzer_generateDigestID_ORACLE(t *testing.T) {
	log.InitTestLogger()

	type fields struct {
		parameter  *structs.Parameter
		cache      *tool.AnalyzeCache
		digestType common.DigestType
	}
	type args struct {
		sqlStmtTiDB string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name:   "sys.obj",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( :1 ,:2  )",
			},
			want: "57f1bf4f185550784cc051c6d4242ff6",
		},
		{
			name:   "sys.obj2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( ?,?  )",
			},
			want: "57f1bf4f185550784cc051c6d4242ff6",
		},
		{
			name:   "sys.obj3",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( 1,2 )",
			},
			want: "57f1bf4f185550784cc051c6d4242ff6",
		},
		{
			name:   "sys.obj4",
			fields: fields{},
			args: args{
				sqlStmtTiDB: "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( '1','2' )",
			},
			want: "69321dad6b661dd2d9dd4d45a619777b",
		},
		{
			name:   "sys.obj_1",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj#
from sys.obj$
where owner# = :1 
  and status > 1
  and name not like 'BIN$%'`,
			},
			want: "58f9e21677ce83d3975ce371c18c2dd8",
		},
		{
			name:   "sys.obj_2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj#
from sys.obj$
where owner# = ? 
  and status > 1
  and name not like 'BIN$%'`,
			},
			want: "a1b292b16048dd5057ea683f0eaf8fe1",
		},
		{
			name:   "sys.obj_3",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj#
from sys.obj$
where owner# = ? 
  and status > 1
  and name not like 'BIN$%'`,
			},
			want: "a1b292b16048dd5057ea683f0eaf8fe1",
		},
		{
			name:   "sys.obj_!1",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj# from sys.obj$ where owner# = 108    and status > 1   and name not like 'BIN$%'`,
			},
			want: "4dc23bc47d857a2ee11e5e236746cce4",
		},
		{
			name:   "sys.obj_!2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `select obj# object_id, mtime modified_timestamp from sys.obj$ where obj# in ( 207392 ,207393  )`,
			},
			want: "57f1bf4f185550784cc051c6d4242ff6",
		},
		{
			name:   "sql_replacement1",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `SELECT * FROM tms.sql_replacement WHERE sql_id = :1 AND sql_text = :2`,
			},
			want: "5a3472a217f95ff1b3aa5f7f14cd6a29",
		},
		{
			name:   "sql_replacement2",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `SELECT * FROM tms.sql_replacement WHERE sql_id = ? AND sql_text = ?`,
			},
			want: "5a3472a217f95ff1b3aa5f7f14cd6a29",
		},
		{
			name:   "sql_replacement3",
			fields: fields{},
			args: args{
				sqlStmtTiDB: `SELECT * FROM tms.sql_replacement WHERE sql_id = 12 AND sql_text = "xx"`,
			},
			want: "5a3472a217f95ff1b3aa5f7f14cd6a29",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sa := &SQLAnalyzer{
				parameter: tt.fields.parameter,
			}
			if got := sa.generateDigestID(tt.args.sqlStmtTiDB); got != tt.want {
				t.Errorf("generateDigestID() = %v, want %v", got, tt.want)
			}
		})
	}
}
