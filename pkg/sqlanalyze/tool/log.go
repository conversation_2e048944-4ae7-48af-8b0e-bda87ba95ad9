package tool

import (
	"context"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	commonlog "gitee.com/pingcap_enterprise/tms/pkg/common"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// CreateSQLAnalyzerOperationLog creates an operation log for the SQL analysis task
func CreateSQLAnalyzerOperationLog(ctx context.Context, channelId int, taskId int, logLevel, message string) error {
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogGroup:   commonlog.TaskLogGroupSQLAnalyzer.String(),
		LogTime:    time.Now(),
		LogLevel:   logLevel,
		LogMessage: constants.BuildProgressLog(constants.SQLAnalyzeTaskLogType, constants.SQLAnalyzeTaskLogStepExplainOperation, message),
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("Failed to create TaskLogDetail, err: %v", err)
		return err
	}
	return nil
}

// CreateSQLAnalyzerStartLog creates a log for the start of the SQL analysis task
func CreateSQLAnalyzerStartLog(ctx context.Context, channelId int, taskId int) error {
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogGroup:   commonlog.TaskLogGroupSQLAnalyzer.String(),
		LogTime:    time.Now(),
		LogLevel:   log.LogInfo,
		LogMessage: constants.BuildProgressLog(constants.SQLAnalyzeTaskLogType, constants.SQLAnalyzeTaskLogStepExplainStart, "Starting execution"),
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("Failed to create TaskLogDetail, err: %v", err)
		return err
	}
	return nil
}

// CreateSQLAnalyzerRunningLog creates a log for the SQL analysis task in progress
func CreateSQLAnalyzerRunningLog(ctx context.Context, channelId int, taskId int, message string) error {
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogGroup:   commonlog.TaskLogGroupSQLAnalyzer.String(),
		LogTime:    time.Now(),
		LogLevel:   log.LogInfo,
		LogMessage: constants.BuildProgressLog(constants.SQLAnalyzeTaskLogType, constants.SQLAnalyzeTaskLogStepExplainRunning, message),
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("Failed to create TaskLogDetail, err: %v", err)
		return err
	}
	return nil
}

// CreateSQLAnalyzerPreparingLog creates a log for the SQL analysis task in preparation
func CreateSQLAnalyzerPreparingLog(ctx context.Context, channelId int, taskId int, message string) error {
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogGroup:   commonlog.TaskLogGroupSQLAnalyzer.String(),
		LogTime:    time.Now(),
		LogLevel:   log.LogInfo,
		LogMessage: constants.BuildProgressLog(constants.SQLAnalyzeTaskLogType, constants.SQLAnalyzeTaskLogStepExplainPreparing, message),
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("Failed to create TaskLogDetail, err: %v", err)
		return err
	}
	return nil
}

// CreateSQLAnalyzerFinishLog creates a log for the successful completion of the SQL analysis task
func CreateSQLAnalyzerFinishLog(ctx context.Context, channelId int, taskId int) error {
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogGroup:   commonlog.TaskLogGroupSQLAnalyzer.String(),
		LogTime:    time.Now(),
		LogLevel:   log.LogInfo,
		LogMessage: constants.BuildProgressLog(constants.SQLAnalyzeTaskLogType, constants.SQLAnalyzeTaskLogStepExplainFinish, "Execution completed successfully"),
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("Failed to create TaskLogDetail, err: %v", err)
		return err
	}
	return nil
}

// CreateSQLAnalyzerFailedLog creates a log for the failure of the SQL analysis task
func CreateSQLAnalyzerFailedLog(ctx context.Context, channelId int, taskId int) error {
	logData := &task.TaskLogDetail{
		ChannelId:  channelId,
		TaskId:     taskId,
		LogGroup:   commonlog.TaskLogGroupSQLAnalyzer.String(),
		LogTime:    time.Now(),
		LogLevel:   log.LogError,
		LogMessage: constants.BuildProgressLog(constants.SQLAnalyzeTaskLogType, constants.SQLAnalyzeTaskLogStepExplainFailed, "Execution failed"),
	}
	err := models.GetTaskReaderWriter().CreateTaskLogDetail(ctx, logData)
	if err != nil {
		log.Errorf("Failed to create TaskLogDetail, err: %v", err)
		return err
	}
	return nil
}
