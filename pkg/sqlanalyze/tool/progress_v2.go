package tool

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/util/log"
)

type ProgressTrackerV2 struct {
	lock sync.RWMutex

	channelId int
	taskId    int

	totalNum     uint64
	digestedNum  uint64
	duplicateNum uint64

	notReplayTotalNum uint64

	rewriteSuccessNum uint64
	rewriteFailedNum  uint64

	replaySuccessNum uint64
	replayFailedNum  uint64
	replayTimeoutNum uint64
	filteredNum      uint64

	digestTicker  *time.Ticker
	replayTicker  *time.Ticker
	rewriteTicker *time.Ticker
}

func BuildProgressHelperV2(channelId, taskId int, totalNum uint64) *ProgressTrackerV2 {
	return &ProgressTrackerV2{
		lock:          sync.RWMutex{},
		taskId:        taskId,
		channelId:     channelId,
		totalNum:      totalNum,
		digestTicker:  time.NewTicker(3 * time.Second),
		replayTicker:  time.NewTicker(3 * time.Second),
		rewriteTicker: time.NewTicker(10 * time.Second),
	}
}

func BuildEmptyProgressHelperV2(channelId, taskId int) *ProgressTrackerV2 {
	return &ProgressTrackerV2{
		lock:          sync.RWMutex{},
		taskId:        taskId,
		channelId:     channelId,
		digestTicker:  time.NewTicker(3 * time.Second),
		replayTicker:  time.NewTicker(3 * time.Second),
		rewriteTicker: time.NewTicker(10 * time.Second),
	}
}

func (i *ProgressTrackerV2) SetReplayNums(notReplayTotalNum uint64) {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.notReplayTotalNum = notReplayTotalNum
}

func (i *ProgressTrackerV2) DisplayAndSaveDigestingProgress(ctx context.Context) {
	i.displayDigestingProgress(ctx)
	go func() {
		for {
			select {
			case <-i.digestTicker.C:
				i.displayDigestingProgress(ctx)
				i.digestTicker.Reset(3 * time.Second)
			}
		}
	}()
}

func (i *ProgressTrackerV2) displayDigestingProgress(ctx context.Context) {
	log.Infof("Generating SQL Digest ID, channelId %d, task %d, totalNum %d, digestedNum %d",
		i.channelId, i.taskId, i.totalNum, i.digestedNum)
	err := CreateSQLAnalyzerPreparingLog(ctx, i.channelId, i.taskId, fmt.Sprintf(
		"Generating Digest ID, total SQLs: %d, completed: %d",
		i.totalNum, i.digestedNum))
	if err != nil {
		log.Errorf("Failed to create progress log for SQL Digest ID generation, channelId %d, task %d, err %v", i.channelId, i.taskId, err)
	}
}

func (i *ProgressTrackerV2) DisplayAndSaveReplayProgress(ctx context.Context) {
	i.displayReplayProgress(ctx)
	go func() {
		for {
			select {
			case <-i.replayTicker.C:
				i.displayReplayProgress(ctx)
				i.replayTicker.Reset(3 * time.Second)
			}
		}
	}()
}

func (i *ProgressTrackerV2) DisplayAndSaveRewriteProgress(ctx context.Context) {
	i.displayRewriteProgress(ctx)
	go func() {
		for {
			select {
			case <-i.rewriteTicker.C:
				i.displayRewriteProgress(ctx)
				i.rewriteTicker.Reset(10 * time.Second)
			}
		}
	}()
}

func (i *ProgressTrackerV2) SaveSQLSyntaxRewriteSkipProgress(ctx context.Context) {
	err := CreateSQLAnalyzerRunningLog(ctx, i.channelId, i.taskId, "SQL syntax rewrite disabled, skipping")
	if err != nil {
		log.Errorf("Failed to create progress log for SQL syntax rewrite, channelId %d, task %d, err %v", i.channelId, i.taskId, err)
	}
}

func (i *ProgressTrackerV2) SaveSQLSyntaxRewriteEnableProgress(ctx context.Context) {
	err := CreateSQLAnalyzerRunningLog(ctx, i.channelId, i.taskId, "SQL syntax rewrite enabled, processing")
	if err != nil {
		log.Errorf("Failed to create progress log for SQL syntax rewrite, channelId %d, task %d, err %v", i.channelId, i.taskId, err)
	}
}

func (i *ProgressTrackerV2) displayReplayProgress(ctx context.Context) {
	log.Infof("Replaying SQL, channelId %d, task %d, totalNum %d, duplicated %d, notReplayTotalNum %d, replaySuccessNum %d, replayFailedNum %d, replayTimeoutNum %d, filteredNum %d",
		i.channelId, i.taskId, i.totalNum, i.duplicateNum, i.notReplayTotalNum, i.replaySuccessNum, i.replayFailedNum, i.replayTimeoutNum, i.filteredNum)
	err := CreateSQLAnalyzerPreparingLog(ctx, i.channelId, i.taskId, fmt.Sprintf(
		"Replaying SQLs, total: %d, duplicated: %d, not replayed: %d, successful: %d, failed: %d, timed out: %d, filtered: %d",
		i.totalNum, i.duplicateNum, i.notReplayTotalNum, i.replaySuccessNum, i.replayFailedNum, i.replayTimeoutNum, i.filteredNum))
	if err != nil {
		log.Errorf("Failed to create progress log for SQL replay, channelId %d, task %d, err %v", i.channelId, i.taskId, err)
	}
}

func (i *ProgressTrackerV2) displayRewriteProgress(ctx context.Context) {
	log.Infof("Rewriting SQL syntax, channelId %d, task %d, rewriteSuccessNum %d, rewriteFailedNum %d",
		i.channelId, i.taskId, i.rewriteSuccessNum, i.rewriteFailedNum)
	err := CreateSQLAnalyzerPreparingLog(ctx, i.channelId, i.taskId,
		fmt.Sprintf("Rewriting SQL syntax, successful: %d, failed: %d", i.rewriteSuccessNum, i.rewriteFailedNum))
	if err != nil {
		log.Errorf("Failed to create progress log for SQL syntax rewrite, channelId %d, task %d, err %v", i.channelId, i.taskId, err)
	}
}

func (i *ProgressTrackerV2) IncreaseDigestedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.digestedNum++
}

func (i *ProgressTrackerV2) IncreaseRewriteSuccessNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.rewriteSuccessNum++
}

func (i *ProgressTrackerV2) IncreaseRewriteFailedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.rewriteFailedNum++
}

func (i *ProgressTrackerV2) IncreaseReplaySuccessNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.replaySuccessNum++
	i.notReplayTotalNum--
}

func (i *ProgressTrackerV2) IncreaseReplayFailedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.replayFailedNum++
	i.notReplayTotalNum--
}

func (i *ProgressTrackerV2) IncreaseReplayTimeoutNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.replayTimeoutNum++
	i.notReplayTotalNum--
}

func (i *ProgressTrackerV2) CloseDigestTracker(ctx context.Context) {
	i.lock.Lock()
	defer i.lock.Unlock()
	if i.digestTicker != nil {
		i.displayDigestingProgress(ctx)
		i.digestTicker.Stop()
	}
}

func (i *ProgressTrackerV2) CloseReplayTracker(ctx context.Context) {
	i.lock.Lock()
	defer i.lock.Unlock()
	if i.replayTicker != nil {
		i.displayReplayProgress(ctx)
		i.replayTicker.Stop()
	}
}

func (i *ProgressTrackerV2) CloseRewriteTracker(ctx context.Context) {
	i.lock.Lock()
	defer i.lock.Unlock()
	if i.rewriteTicker != nil {
		i.displayRewriteProgress(ctx)
		i.rewriteTicker.Stop()
	}
}

func (i *ProgressTrackerV2) IncreaseDuplicateNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.duplicateNum++
}

func (i *ProgressTrackerV2) GetDuplicateNum() uint64 {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.duplicateNum
}

func (i *ProgressTrackerV2) IncreaseFilteredNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.filteredNum++
	i.notReplayTotalNum--
}
