package tool

import (
	"crypto/md5"
	"fmt"
	"regexp"
	"strings"
)

// DigestNormalized 生成标准化的 SQL 指纹
func DigestNormalized(sqlText string) string {
	// 标准化 SQL
	normalized := strings.ToUpper(sqlText)                                   // 统一大写
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ") // 统一空格

	// 处理 IN 子句，替换为单一占位符 IN (?)
	normalized = regexp.MustCompile(`IN\s*\(([^)]+)\)`).ReplaceAllString(normalized, "IN (?)")

	// 处理其他参数值（= 后面的值）
	normalized = regexp.MustCompile(`(\w+\s*=)\s*('[^']*'|"[^"]*"|[0-9]+|:[0-9A-Za-z_]+)`).ReplaceAllString(normalized, "$1 ?")

	// 生成 MD5 指纹
	hash := md5.Sum([]byte(normalized))
	return strings.ToLower(fmt.Sprintf("%x", hash)) // 返回小写指纹，与测试用例一致
}

// Normalize 辅助函数：返回标准化后的 SQL 文本（用于调试）
func Normalize(sqlText string) string {
	normalized := strings.ToUpper(sqlText)
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")
	normalized = regexp.MustCompile(`IN\s*\(([^)]+)\)`).ReplaceAllString(normalized, "IN (?)")
	normalized = regexp.MustCompile(`(\w+\s*=)\s*('[^']*'|"[^"]*"|[0-9]+|:[0-9A-Za-z_]+)`).ReplaceAllString(normalized, "$1 ?")
	return normalized
}
