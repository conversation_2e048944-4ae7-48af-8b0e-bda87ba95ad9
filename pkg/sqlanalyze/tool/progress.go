package tool

import (
	"context"
	"encoding/json"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"sync"
	"time"

	"gitee.com/pingcap_enterprise/tms/server/models"
	sqlanalyzermodel "gitee.com/pingcap_enterprise/tms/server/models/sqlanalyzer"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

type ProgressHelper struct {
	lock sync.RWMutex

	channelId int
	taskId    int

	total         int
	skipNum       int
	skipSchemaNum int
	digestHitNum  int
	explainedNum  int
	duplicateNum  int
	filteredNum   int

	successNum int
	failedNum  int
	timeoutNum int

	ticker *time.Ticker

	summary *sqlanalyzermodel.SqlStmtExecSummary
}

func buildProgressHelper(channelId, taskId int) *ProgressHelper {
	return &ProgressHelper{
		lock:      sync.RWMutex{},
		taskId:    taskId,
		channelId: channelId,
		ticker:    time.NewTicker(5 * time.Second),
	}
}

func (i *ProgressHelper) DisplayAndSaveProgressInTimeLoop(ctx context.Context) {
	go func() {
		defer func() {
			// Clean up ticker when goroutine exits
			if i.ticker != nil {
				i.ticker.Stop()
			}
		}()
		
		for {
			select {
			case <-ctx.Done():
				// Context cancelled, exit goroutine gracefully
				log.Infof("Progress tracking stopped for channelId %d, taskId %d due to context cancellation", i.channelId, i.taskId)
				// Save final summary before exiting
				if err := i.SaveSQLExecSummary(context.Background()); err != nil {
					log.Errorf("Failed to save final SQL execution summary, channelId %d, taskId %d, err %v", i.channelId, i.taskId, err)
				}
				return
			case <-i.ticker.C:
				log.Infof("SQL analysis in progress, channelId %d, task %d, total SQLs %d, duplicated %d, filtered %d, explained %d, successful %d, failed %d, timed out %d",
					i.channelId, i.taskId, i.total, i.duplicateNum, i.filteredNum, i.explainedNum, i.successNum, i.failedNum, i.timeoutNum)
				err := CreateSQLAnalyzerRunningLog(ctx, i.channelId, i.taskId, fmt.Sprintf(
					"Analyzing SQLs, total: %d, duplicated: %d, filtered: %d, successful: %d, failed: %d, schema mismatch: %d, timed out: %d",
					i.total, i.duplicateNum, i.filteredNum, i.successNum, i.failedNum, i.skipSchemaNum, i.timeoutNum))
				if err != nil {
					log.Errorf("Failed to create progress log for SQL analysis, channelId %d, task %d, err %v", i.channelId, i.taskId, err)
				}
				summaryErr := i.SaveSQLExecSummary(ctx)
				if summaryErr != nil {
					log.Errorf("Failed to save SQL execution summary, channelId %d, task %d, err %v", i.channelId, i.taskId, summaryErr)
				}
				i.ticker.Reset(10 * time.Second)
			}
		}
	}()
}

func (i *ProgressHelper) Close() {
	i.lock.Lock()
	defer i.lock.Unlock()
	if i.ticker != nil {
		i.ticker.Stop()
	}
}

func (i *ProgressHelper) SaveSQLExecSummary(ctx context.Context) error {
	i.lock.Lock()
	defer i.lock.Unlock()

	summary := i.summary
	if summary == nil {
		summary = &sqlanalyzermodel.SqlStmtExecSummary{}
	}
	summary.ChannelId = i.channelId
	summary.TaskId = i.taskId
	summary.TotalNum = i.total - i.duplicateNum - i.filteredNum
	summary.SuccessNum = i.successNum
	summary.TimeoutNum = i.timeoutNum
	summary.SchemaNotMatchNum = i.skipSchemaNum
	summary.FailedNum = i.failedNum

	summaryBytes, _ := json.Marshal(summary)

	var saveErr error
	log.Infof("Saving SQL execution summary, channelId:%d, taskId:%d, summary:%v", i.channelId, i.taskId, string(summaryBytes))
	summary, saveErr = models.GetSqlAnalyzerReaderWriter().SaveSqlExecSummary(ctx, summary)
	if saveErr != nil {
		log.Errorf("Failed to save SQL execution summary, err:%v", saveErr)
		return saveErr
	}
	i.summary = summary
	return nil
}

func (i *ProgressHelper) SetTotal(total int) {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.total = total
}

func (i *ProgressHelper) GetTotal() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.total
}

func (i *ProgressHelper) IncreaseSkipNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.skipNum++
}

func (i *ProgressHelper) IncreaseSkipSchemaNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.skipSchemaNum++
}

func (i *ProgressHelper) IncreaseDigestHitNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.digestHitNum++
}

func (i *ProgressHelper) GetIgnoreNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.skipNum
}

func (i *ProgressHelper) GetSkipSchemaNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.skipSchemaNum
}

func (i *ProgressHelper) GetDigestHitNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.digestHitNum
}

func (i *ProgressHelper) GetFailedNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.failedNum
}

func (i *ProgressHelper) GetTimeoutNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.timeoutNum
}

func (i *ProgressHelper) GetSuccessNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.successNum
}

func (i *ProgressHelper) GetExplainedNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.explainedNum
}

func (i *ProgressHelper) IncreaseExplainedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.explainedNum++

	halfTotal := i.total / 2
	if i.explainedNum == halfTotal {
		CreateSQLAnalyzerRunningLog(context.Background(), i.channelId, i.taskId, fmt.Sprintf(
			"Analyzing SQLs, total: %d, duplicated: %d, filtered: %d, successful: %d, failed: %d, schema mismatch: %d, timed out: %d",
			i.total, i.duplicateNum, i.filteredNum, i.successNum, i.failedNum, i.skipSchemaNum, i.timeoutNum))
	}
}

func (i *ProgressHelper) IncreaseSuccessNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.successNum++
}

func (i *ProgressHelper) IncreaseFailedNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.failedNum++
}

func (i *ProgressHelper) IncreaseTimeoutNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.timeoutNum++
}

func (i *ProgressHelper) IncreaseDuplicateNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.duplicateNum++
}

func (i *ProgressHelper) GetDuplicateNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.duplicateNum
}

func (i *ProgressHelper) SetFilteredNum(count int) {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.filteredNum = count
}

func (i *ProgressHelper) IncreaseFilteredNum() {
	i.lock.Lock()
	defer i.lock.Unlock()
	i.filteredNum++
}

func (i *ProgressHelper) GetFilteredNum() int {
	i.lock.RLock()
	defer i.lock.RUnlock()
	return i.filteredNum
}

// ProgressTracker manages progress tracking
type ProgressTracker struct {
	channelID int
	taskID    int
	helper    *ProgressHelper
}

func NewProgressTracker(channelID, taskID, total int) *ProgressTracker {
	helper := buildProgressHelper(channelID, taskID)
	helper.SetTotal(total)
	return &ProgressTracker{channelID: channelID, taskID: taskID, helper: helper}
}

func (pt *ProgressTracker) Start(ctx context.Context) {
	pt.helper.DisplayAndSaveProgressInTimeLoop(ctx)
}

func (pt *ProgressTracker) Update(status string) {
	switch status {
	case constants.TiDBExplainTimeout:
		pt.helper.IncreaseTimeoutNum()
	case constants.TiDBExplainSuccess:
		pt.helper.IncreaseSuccessNum()
	case constants.TiDBExplainFailed:
		pt.helper.IncreaseFailedNum()
	}
}

func (pt *ProgressTracker) SaveSummary(ctx context.Context) error {
	return pt.helper.SaveSQLExecSummary(ctx)
}

func (pt *ProgressTracker) Close() {
	pt.helper.Close()
}

func (pt *ProgressTracker) IncreaseExplainedNum() {
	pt.helper.IncreaseExplainedNum()
}

func (pt *ProgressTracker) IncreaseSkipSchemaNum() {
	pt.helper.IncreaseSkipSchemaNum()
}

func (pt *ProgressTracker) IncreaseDigestHitNum() {
	pt.helper.IncreaseDigestHitNum()
}

func (pt *ProgressTracker) GetTotal() int {
	return pt.helper.GetTotal()
}

func (pt *ProgressTracker) GetSuccessNum() int {
	return pt.helper.GetSuccessNum()
}

func (pt *ProgressTracker) GetFailedNum() int {
	return pt.helper.GetFailedNum()
}

func (pt *ProgressTracker) GetSkipSchemaNum() int {
	return pt.helper.GetSkipSchemaNum()
}

func (pt *ProgressTracker) GetDigestHitNum() int {
	return pt.helper.GetDigestHitNum()
}

func (pt *ProgressTracker) GetTimeoutNum() int {
	return pt.helper.GetTimeoutNum()
}

func (pt *ProgressTracker) GetExplainedNum() int {
	return pt.helper.GetExplainedNum()
}

func (pt *ProgressTracker) IncreaseDuplicateNum() {
	pt.helper.IncreaseDuplicateNum()
}

func (pt *ProgressTracker) GetDuplicateNum() int {
	return pt.helper.GetDuplicateNum()
}

func (pt *ProgressTracker) SetFilteredNum(count int) {
	pt.helper.SetFilteredNum(count)
}

func (pt *ProgressTracker) IncreaseFilteredNum() {
	pt.helper.IncreaseFilteredNum()
}

func (pt *ProgressTracker) GetFilteredNum() int {
	return pt.helper.GetFilteredNum()
}
