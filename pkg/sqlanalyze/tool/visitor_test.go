package tool

import (
	"strings"
	"testing"

	"github.com/pingcap/tidb/pkg/parser"
	"github.com/pingcap/tidb/pkg/parser/ast"
	"github.com/pingcap/tidb/pkg/parser/format"
	_ "github.com/pingcap/tidb/pkg/types/parser_driver"
	"github.com/stretchr/testify/assert"
)

func TestSchemaTableName(t *testing.T) {
	p := parser.New()

	var root ast.StmtNode
	var err, restoreErr error
	var sb strings.Builder
	var txt string
	v := SchemaTableVisitor{}
	flag := format.DefaultRestoreFlags | format.RestoreNameLowercase | format.RestoreStringWithoutDefaultCharset
	restoreCtx := format.NewRestoreCtx(flag, &sb)

	txt = `select obj# from sys.obj$ where owner# = 85 and status > 1 and name not like 'BIN$%'`
	txt = strings.ReplaceAll(txt, "#", "_")
	root, err = p.ParseOneStmt(txt, "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	restoreErr = root.Restore(restoreCtx)
	assert.Nil(t, restoreErr)
	assert.Equal(t, "SELECT SYS_CONTEXT('userenv', 'current_schema') AS `x`", sb.String())
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{}, v.tableNames)

	txt = `SELECT SYS_XMLGEN(VALUE(KU$), XMLFORMAT.createFormat2('TABLE_T', '7')), KU$.OBJ_NUM FROM SYS.KU$_HTABLE_VIEW KU$ WHERE NOT (BITAND (KU$.PROPERTY,8192)=8192) AND NOT BITAND(KU$.SCHEMA_OBJ.FLAGS,128)!=0 AND KU$.SCHEMA_OBJ.NAME='ORDERS' AND KU$.SCHEMA_OBJ.OWNER_NAME='SOE'	`
	txt = strings.ReplaceAll(txt, "#", "_")
	txt = strings.ReplaceAll(txt, "$", "_")
	root, err = p.ParseOneStmt(txt, "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	restoreErr = root.Restore(restoreCtx)
	assert.Nil(t, restoreErr)
	assert.Equal(t, "SELECT SYS_CONTEXT('userenv', 'current_schema') AS `x`", sb.String())
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{}, v.tableNames)

	root, err = p.ParseOneStmt(`select sys_context('userenv', 'current_schema') x from dual	`, "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	restoreErr = root.Restore(restoreCtx)
	assert.Nil(t, restoreErr)
	assert.Equal(t, "SELECT SYS_CONTEXT('userenv', 'current_schema') AS `x`", sb.String())
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{}, v.tableNames)

	root, err = p.ParseOneStmt("with U as ( select user_id, username from sys.dba_users where user_id in ( 85 ) /* union select 1 as user_id, 'PUBLIC' as username from dual */ ) select U.username as synonym_schema_name, O.obj# as synonym_id, O.name as synonym_name, O.ctime as created_timestamp, O.mtime as modified_timestamp, S.node as origin_db_link, S.owner as origin_schema_name, S.name as origin_object_name from sys.obj$ O, sys.syn$ S, U where U.user_id = O.owner# and O.type# = 5 and O.name not like '%/%' and O.obj# = S.obj# and O.mtime >= '2024-03-0410.24.48.000000' order by O.obj#", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{"sys"}, v.schemaNames)
	assert.Equal(t, []string{"dba_users"}, v.tableNames)

	root, err = p.ParseOneStmt("update soe.orders set a=c where 1=2", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{"soe"}, v.schemaNames)
	assert.Equal(t, []string{"orders"}, v.tableNames)

	root, err = p.ParseOneStmt("SELECT ATTACHED_SESSIONS FROM SYS.USER_DATAPUMP_JOBS WHERE JOB_NAME = 'SYS_IMPORT_TABLE_01'", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{"SYS"}, v.schemaNames)
	assert.Equal(t, []string{"USER_DATAPUMP_JOBS"}, v.tableNames)

	root, err = p.ParseOneStmt("update orders set a=c where 1=2", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{"orders"}, v.tableNames)

	root, err = p.ParseOneStmt("DELETE FROM orders where 1=2", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{"orders"}, v.tableNames)

	root, err = p.ParseOneStmt("INSERT INTO soe.orders values(1,2,3)", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{"soe"}, v.schemaNames)
	assert.Equal(t, []string{"orders"}, v.tableNames)

	root, err = p.ParseOneStmt("INSERT INTO orders2 values(1,2,3)", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{"orders2"}, v.tableNames)

	root, err = p.ParseOneStmt("INSERT INTO orders2(a,c,d) values(1,2,3)", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{"orders2"}, v.tableNames)

	root, err = p.ParseOneStmt("DELETE FROM xxa.orders where 1=2", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{"xxa"}, v.schemaNames)
	assert.Equal(t, []string{"orders"}, v.tableNames)

	root, err = p.ParseOneStmt("select count(*) from soe.orders where 1=2", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{"soe"}, v.schemaNames)
	assert.Equal(t, []string{"orders"}, v.tableNames)

	root, err = p.ParseOneStmt("select * from oRders where 1=2", "", "")
	assert.Nil(t, err)
	v.Reset()
	root.Accept(&v)
	assert.Equal(t, []string{}, v.schemaNames)
	assert.Equal(t, []string{"oRders"}, v.tableNames)

}
