package tool

import (
	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
)

type SchemaSQLSetStatus struct {
	SchemaName string
	Status     constants.SQLSetStatus
	TMSSQLSet  *datasource.SQLSet
	DBASQLSet  *datasource.SQLSet
}

func (i *SchemaSQLSetStatus) GetTMSSQLSetString() string {
	if i.TMSSQLSet == nil {
		return "nil"
	}
	return i.TMSSQLSet.String()
}

func (i *SchemaSQLSetStatus) GetDBASQLSetString() string {
	if i.DBASQLSet == nil {
		return "nil"
	}
	return i.DBASQLSet.String()
}
