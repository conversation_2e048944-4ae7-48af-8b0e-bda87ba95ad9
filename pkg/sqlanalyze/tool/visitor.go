package tool

import (
	"github.com/pingcap/tidb/pkg/parser/ast"
	_ "github.com/pingcap/tidb/pkg/types/parser_driver"
)

// SchemaTableVisitor schema / table visitor
type SchemaTableVisitor struct {
	schemaNames []string
	tableNames  []string
}

func (c *SchemaTableVisitor) GetSchemaNames() []string {
	return c.schemaNames
}

func (c *SchemaTableVisitor) GetTableNames() []string {
	return c.tableNames

}
func (c *SchemaTableVisitor) Reset() {
	c.schemaNames = []string{}
	c.tableNames = []string{}
}

func (c *SchemaTableVisitor) Leave(in ast.Node) (ast.Node, bool) {
	return in, true
}

// Enter visitor enter
func (c *SchemaTableVisitor) Enter(in ast.Node) (ast.Node, bool) {
	if v, ok := in.(*ast.TableName); ok {
		if v.Schema.String() != "" {
			c.schemaNames = append(c.schemaNames, v.Schema.String())
		}
		c.tableNames = append(c.tableNames, v.Name.String())
	}
	return in, false
}
