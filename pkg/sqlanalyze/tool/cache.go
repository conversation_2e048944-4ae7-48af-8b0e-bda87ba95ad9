package tool

import (
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"sync"
)

// AnalyzeCache 缓存 Explain 结果
type AnalyzeCache struct {
	*sync.RWMutex
	cache map[string]*structs.ExplainResult
}

func NewAnalyzeCache() *AnalyzeCache {
	return &AnalyzeCache{
		RWMutex: &sync.RWMutex{},
		cache:   make(map[string]*structs.ExplainResult),
	}
}

func (ac *AnalyzeCache) GetLastExplainResult(digest string) (*structs.ExplainResult, bool) {
	ac.RLock()
	defer ac.RUnlock()
	stmt, ok := ac.cache[digest]
	return stmt, ok
}

func (ac *AnalyzeCache) SetLastExplainResult(digest string, stmt *structs.ExplainResult) {
	ac.Lock()
	defer ac.Unlock()
	ac.cache[digest] = stmt
}
