package tool

import (
	"context"
	"database/sql"
	"fmt"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"strings"
	"time"
)

type TransferErrorHandler struct {
	ctx        context.Context
	taskId     int
	oracleConn *sql.DB
	schemaName string
	sqlsetName string
	err        error
}

func NewTransferErrorHandler(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) *TransferErrorHandler {
	return &TransferErrorHandler{
		ctx:        ctx,
		oracleConn: db,
		schemaName: sourceSchema,
		sqlsetName: sqlsetName,
		taskId:     taskId,
		err:        nil,
	}
}

type transferFunc func(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string) error
type pruneFunc func(ctx context.Context, db *sql.DB, taskId int) error
type deleteFunc func(ctx context.Context, db *sql.DB, taskId int, vals []any) (int64, error)

type updateFunc func(ctx context.Context, db *sql.DB, taskId int, sourceSchema, sqlsetName string, created time.Time, lastModified time.Time, statementCount uint64) (int64, error)
type saveSQLSetFunc func(ctx context.Context, db *sql.DB, sqlset datasource.SQLSet) error
type saveSQLSetStatementFunc func(ctx context.Context, db *sql.DB, statements []datasource.SQLSetStatement) error

func (i *TransferErrorHandler) SaveSQLSet(function saveSQLSetFunc, funcName string, sqlset datasource.SQLSet) {
	if i.err != nil {
		return
	}
	log.Infof("start execute save sqlset function, functionName:%s, schemaName:%s, sqlsetName:%s", funcName, i.schemaName, i.sqlsetName)
	i.err = function(context.Background(), i.oracleConn, sqlset)
	if i.err != nil {
		log.Errorf("execute save sqlset function failed, functionName:%s, err:%v", funcName, i.err)
	}
}

func (i *TransferErrorHandler) SaveStatement(function saveSQLSetStatementFunc, funcName string, statements []datasource.SQLSetStatement) {
	if i.err != nil {
		return
	}
	log.Infof("start execute save sqlset statement function, functionName:%s, schemaName:%s, sqlsetName:%s", funcName, i.schemaName, i.sqlsetName)
	i.err = function(context.Background(), i.oracleConn, statements)
	if i.err != nil {
		log.Errorf("execute save sqlset statement function failed, functionName:%s, err:%v", funcName, i.err)
	}
}

func (i *TransferErrorHandler) Transfer(function transferFunc, funcName string) {
	if i.err != nil {
		return
	}
	log.Infof("start execute transfer function, functionName:%s, schemaName:%s, sqlsetName:%s", funcName, i.schemaName, i.sqlsetName)
	i.err = function(context.Background(), i.oracleConn, i.taskId, i.schemaName, i.sqlsetName)
	if i.err != nil {
		log.Errorf("execute transfer function failed, functionName:%s, err:%v", funcName, i.err)
	}
}

func (i *TransferErrorHandler) Prune(function pruneFunc, funcName string) {
	if i.err != nil {
		return
	}
	log.Infof("start execute prune function, functionName:%s", funcName)
	i.err = function(context.Background(), i.oracleConn, i.taskId)
	if i.err != nil {
		log.Errorf("execute prune function failed, functionName:%s, err:%v", funcName, i.err)
	}
}

func (i *TransferErrorHandler) Delete(function deleteFunc, vals []any, funcName string) {
	if i.err != nil {
		return
	}
	var affected int64
	affected, i.err = function(context.Background(), i.oracleConn, i.taskId, vals)
	log.Infof("after execute delete function, functionName:%s, affected:%d, vals:%s", funcName, affected, i.formatVals(vals))
	if i.err != nil {
		log.Errorf("execute prune function failed, functionName:%s, err:%v", funcName, i.err)
	}
}

func (i *TransferErrorHandler) Update(function updateFunc, created time.Time, lastModified time.Time, statementCount uint64, funcName string) {
	if i.err != nil {
		return
	}
	var affected int64
	affected, i.err = function(context.Background(), i.oracleConn, i.taskId, i.schemaName, i.sqlsetName, created, lastModified, statementCount)
	log.Infof("after execute update function, functionName:%s, taskId:%d, affected:%d, schemaName:%s, sqlsetName:%s", funcName, i.taskId, affected, i.schemaName, i.sqlsetName)
	if i.err != nil {
		log.Errorf("execute update function failed, functionName:%s, err:%v", funcName, i.err)
	}
}

func (i *TransferErrorHandler) Error() error {
	return i.err
}

func (i *TransferErrorHandler) formatVals(vals []any) string {
	if len(vals) == 0 {
		return "[]"
	}

	var builder strings.Builder
	builder.WriteString("[")
	for idx, val := range vals {
		if idx > 0 {
			builder.WriteString(", ")
		}
		builder.WriteString(fmt.Sprint(val))
	}
	builder.WriteString("]")
	return builder.String()
}
