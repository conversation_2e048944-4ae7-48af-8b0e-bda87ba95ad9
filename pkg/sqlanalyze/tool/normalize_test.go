package tool

import (
	"testing"
)

func TestNormalize(t *testing.T) {
	tests := []struct {
		name string
		sql  string
		want string
	}{
		// sys.obj 系列（IN 子句）
		{
			name: "sys.obj",
			sql:  "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( :1 ,:2  )",
			want: "SELECT OBJ# OBJECT_ID, MTIME MODIFIED_TIMESTAMP FROM SYS.OBJ$ WHERE OBJ# IN (?)",
		},
		{
			name: "sys.obj2",
			sql:  "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( ?,?  )",
			want: "SELECT OBJ# OBJECT_ID, MTIME MODIFIED_TIMESTAMP FROM SYS.OBJ$ WHERE OBJ# IN (?)",
		},
		{
			name: "sys.obj3",
			sql:  "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( 1,2 )",
			want: "SELECT OBJ# OBJECT_ID, MTIME MODIFIED_TIMESTAMP FROM SYS.OBJ$ WHERE OBJ# IN (?)",
		},
		{
			name: "sys.obj4",
			sql:  "select obj# object_id, mtime modified_timestamp\nfrom sys.obj$\nwhere obj# in ( '1','2' )",
			want: "SELECT OBJ# OBJECT_ID, MTIME MODIFIED_TIMESTAMP FROM SYS.OBJ$ WHERE OBJ# IN (?)",
		},
		{
			name: "sys.obj_!2",
			sql:  "select obj# object_id, mtime modified_timestamp from sys.obj$ where obj# in ( 207392 ,207393  )",
			want: "SELECT OBJ# OBJECT_ID, MTIME MODIFIED_TIMESTAMP FROM SYS.OBJ$ WHERE OBJ# IN (?)",
		},

		// sys.obj_ 系列（多条件）
		{
			name: "sys.obj_1",
			sql: `select obj#
from sys.obj$
where owner# = :1 
  and status > 1
  and name not like 'BIN$%'`,
			want: `SELECT OBJ# FROM SYS.OBJ$ WHERE OWNER# = ? AND STATUS > 1 AND NAME NOT LIKE 'BIN$%'`,
		},
		{
			name: "sys.obj_2",
			sql: `select obj#
from sys.obj$
where owner# = ? 
  and status > 1
  and name not like 'BIN$%'`,
			want: "SELECT OBJ# FROM SYS.OBJ$ WHERE OWNER# = ? AND STATUS > 1 AND NAME NOT LIKE 'BIN$%'",
		},
		{
			name: "sys.obj_3",
			sql: `select obj#
from sys.obj$
where owner# = ? 
  and status > 1
  and name not like 'BIN$%'`,
			want: "SELECT OBJ# FROM SYS.OBJ$ WHERE OWNER# = ? AND STATUS > 1 AND NAME NOT LIKE 'BIN$%'",
		},
		{
			name: "sys.obj_!1",
			sql:  "select obj# from sys.obj$ where owner# = 108    and status > 1   and name not like 'BIN$%'",
			want: "SELECT OBJ# FROM SYS.OBJ$ WHERE OWNER# = ? AND STATUS > 1 AND NAME NOT LIKE 'BIN$%'",
		},

		// sql_replacement 系列（多参数）
		{
			name: "sql_replacement1",
			sql:  "SELECT * FROM tms.sql_replacement WHERE sql_id = :1 AND sql_text = :2",
			want: "SELECT * FROM TMS.SQL_REPLACEMENT WHERE SQL_ID = ? AND SQL_TEXT = ?",
		},
		{
			name: "sql_replacement2",
			sql:  "SELECT * FROM tms.sql_replacement WHERE sql_id = ? AND sql_text = ?",
			want: "SELECT * FROM TMS.SQL_REPLACEMENT WHERE SQL_ID = ? AND SQL_TEXT = ?",
		},
		{
			name: "sql_replacement3",
			sql:  "SELECT * FROM tms.sql_replacement WHERE sql_id = 12 AND sql_text = \"xx\"",
			want: "SELECT * FROM TMS.SQL_REPLACEMENT WHERE SQL_ID = ? AND SQL_TEXT = ?",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Normalize(tt.sql); got != tt.want {
				t.Errorf("Normalize() = %v, want %v", got, tt.want)
			}
		})
	}
}
