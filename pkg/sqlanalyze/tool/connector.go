package tool

import (
	"database/sql"
	"fmt"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/util/log"
)

// DatabaseConnector 管理数据库连接
type DatabaseConnector struct {
	oracleConfig *structs.DBConfigOracle
	tidbConfig   *structs.DBConfigTiDB
}

func NewDatabaseConnector(oracleCfg *structs.DBConfigOracle, tidbCfg *structs.DBConfigTiDB) *DatabaseConnector {
	return &DatabaseConnector{oracleConfig: oracleCfg, tidbConfig: tidbCfg}
}

func (dc *DatabaseConnector) OpenOracle() (*sql.DB, error) {
	conn, err := models.OpenOracle(models.BuildOracleConfigFromSQLAnalyzeConf(dc.oracleConfig, ""))
	if err != nil {
		log.Errorf("open oracle db failed, err:%s", err)
		return nil, err
	}

	conn.SetMaxIdleConns(10)
	conn.SetMaxOpenConns(10)
	return conn, err
}

func (dc *DatabaseConnector) OpenTiDB() (*sql.DB, error) {
	p := dc.tidbConfig
	conn, err := models.OpenMysql(p.User, p.Password, p.Host, p.Port, p.Database)
	if err != nil {
		log.Errorf("open tidb failed, err:%v", err)
		return nil, err
	}

	// Set connection pool settings for SQL analyzer workload
	conn.SetMaxIdleConns(10)
	conn.SetMaxOpenConns(10)
	return conn, nil
}

func (dc *DatabaseConnector) SetTiDBSchema(analyzeParameter *structs.Parameter, stmt *structs.SpaSqlStmt, conn *sql.DB, mapper *structs.ChannelSchemaMapper) error {
	var targetSchema string
	if analyzeParameter.AppSQLsSource == common.FileSource {
		targetSchema = mapper.GetTargetSchemaForFile(dc.oracleConfig.GetUpperUser())
	} else {
		targetSchema = mapper.GetTargetSchema(stmt.OraParsingSchemaName, dc.oracleConfig.GetUpperUser())
	}

	if targetSchema == "" {
		log.Warnf("no target schema found for sqlset owner: %s", stmt.OraSqlsetOwner)
		return nil
	}

	useSQL := fmt.Sprintf("use %s", targetSchema)
	_, err := conn.Exec(useSQL)
	if err != nil {
		log.Errorf("tidbConn.Exec failed, sql:%s, err:%s", useSQL, err)
	}
	return err
}
