package sqlanalyze

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"

	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/transferdb/database/oracle"
	sqlcommon "gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/common"
	"gitee.com/pingcap_enterprise/tms/pkg/sqlanalyze/tool"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/datasource"
	servicecommon "gitee.com/pingcap_enterprise/tms/server/service/common"
	"gitee.com/pingcap_enterprise/tms/util/log"
	stringutil "gitee.com/pingcap_enterprise/tms/util/string"
	"github.com/google/uuid"
	"github.com/samber/lo"
)

// SQLFetcher handles SQL retrieval
type SQLFetcher struct {
	taskId          int
	sourceType      sqlcommon.SourceType
	parameter       *structs.Parameter
	oracleSQL<PERSON>han   chan *structs.SpaSqlStmt
	queryFinish<PERSON>han chan bool
	err<PERSON>han         chan error
}

func NewSQLFetcher(taskId int, param *structs.Parameter) *SQLFetcher {
	return &SQLFetcher{
		taskId:          taskId,
		sourceType:      param.GetAppSQLsSource(),
		parameter:       param,
		oracleSQLChan:   make(chan *structs.SpaSqlStmt, 1000),
		queryFinishChan: make(chan bool),
		errChan:         make(chan error),
	}
}

func (sf *SQLFetcher) FetchSQL(ctx context.Context) (*sqlcommon.SQLNumStat, error) {
	channelId, taskId := sf.parameter.ChannelID, sf.parameter.TaskID
	var sqlNumStat *sqlcommon.SQLNumStat
	var err error

	defer func() {
		if err != nil {
			err = errors.NewError(errors.TMS_SQLANALYZE_GET_ORACLE_SQL_FAILED, err.Error())
		}
	}()

	if sf.sourceType == sqlcommon.FileSource {
		err = tool.CreateSQLAnalyzerRunningLog(ctx, channelId, taskId, "Retrieving SQL statements from file")
		if err != nil {
			return nil, err
		}
		sqlNumStat, err = sf.fetchFromFile(ctx)
	} else {
		err = tool.CreateSQLAnalyzerRunningLog(ctx, channelId, taskId, "Retrieving all SQL statements")
		if err != nil {
			return nil, err
		}
		conn, err := tool.NewDatabaseConnector(sf.parameter.OracleConnStr, nil).OpenOracle()
		if err != nil {
			return nil, err
		}
		sqlNumStat, err = sf.fetchFromOracle(ctx, conn)
	}
	return sqlNumStat, err
}

func (sf *SQLFetcher) FetchSQLV2(ctx context.Context, fetchParam BuildFetchParam) (*datasource.SQLSetStatementsSummary, error) {
	channelId, taskId := sf.parameter.ChannelID, sf.parameter.TaskID
	var sqlsetSummary *datasource.SQLSetStatementsSummary
	var err error

	defer func() {
		if err != nil {
			err = errors.NewError(errors.TMS_SQLANALYZE_GET_ORACLE_SQL_FAILED, err.Error())
		}
	}()

	err = tool.CreateSQLAnalyzerRunningLog(ctx, channelId, taskId, fmt.Sprintf("Retrieving all SQL statements for %s", fetchParam.Step))
	if err != nil {
		return nil, err
	}
	conn, err := tool.NewDatabaseConnector(sf.parameter.OracleConnStr, nil).OpenOracle()
	if err != nil {
		return nil, err
	}
	sqlsetSummary, err = sf.fetchFromOracleV2(ctx, conn, fetchParam)
	return sqlsetSummary, err
}

func (sf *SQLFetcher) fetchFromOracle(ctx context.Context, conn *sql.DB) (*sqlcommon.SQLNumStat, error) {
	isVersion19, err := sf.checkOracleVersion(ctx, conn)
	if err != nil {
		conn.Close() // Close immediately if version check fails
		return nil, err
	}
	total, err := sf.countSQLStatements(ctx, conn)
	if err != nil {
		conn.Close() // Close immediately if counting fails
		return nil, err
	}
	// Do not use defer, hand over to goroutine for closing
	go sf.querySQLStatementToChan(ctx, conn, isVersion19)
	return &sqlcommon.SQLNumStat{TotalNum: total}, nil
}

func (sf *SQLFetcher) fetchFromOracleV2(ctx context.Context, conn *sql.DB, fetchParam BuildFetchParam) (*datasource.SQLSetStatementsSummary, error) {
	summary, err := sf.countSQLStatementsV2(ctx, conn)
	if err != nil {
		conn.Close() // Close immediately if counting fails
		return nil, err
	}
	// Do not use defer, hand over to goroutine for closing
	go sf.querySQLStatementToChanV2(ctx, conn, fetchParam)
	return summary, nil
}

func (sf *SQLFetcher) fetchFromFile(ctx context.Context) (*sqlcommon.SQLNumStat, error) {
	total := len(sf.parameter.GetFileSQLs())
	stat := &sqlcommon.SQLNumStat{TotalNum: total, ValidNum: total}
	
	// Start a goroutine to write data and close channel properly
	go func() {
		// Write all SQL statements to channel
		for idx, s := range sf.parameter.GetFileSQLs() {
			sf.oracleSQLChan <- &structs.SpaSqlStmt{
				OraSqlId:             "file_" + strconv.Itoa(idx+1),
				OraSqlText:           s,
				OraParsingSchemaName: sf.parameter.AppSQLsFilename,
			}
		}
		// Signal that query is finished for file source mode
		sf.queryFinishChan <- true
	}()
	
	return stat, nil
}

func (sf *SQLFetcher) checkOracleVersion(ctx context.Context, conn *sql.DB) (bool, error) {
	_, rets, err := oracle.Query(ctx, conn, `SELECT BANNER FROM v$version`)
	if err != nil || len(rets) == 0 {
		log.Errorf("Failed to retrieve Oracle version, err:%v", err)
		return false, errors.NewErrorf(errors.TMS_FETCH_STATEMENT_FAILED, "Failed to retrieve Oracle version")
	}
	return strings.HasPrefix(rets[0]["BANNER"], "Oracle Database 19c"), nil
}

func (sf *SQLFetcher) countSQLStatements(ctx context.Context, conn *sql.DB) (int, error) {
	countSQL := sf.buildCountSQL()
	log.Infof("Counting Oracle SQL statements, sql:%s", stringutil.RemoveSpecialLetterForLog(countSQL))
	_, rets, err := oracle.Query(ctx, conn, countSQL)
	if err != nil || len(rets) == 0 {
		log.Errorf("Failed to count SQLSet statements, err:%v", err)
		return 0, errors.NewErrorf(errors.TMS_FETCH_STATEMENT_FAILED, "Failed to count SQLSet statements")
	}
	total, _ := strconv.Atoi(rets[0]["COUNT"])
	return total, nil
}

func (sf *SQLFetcher) countSQLStatementsV2(ctx context.Context, conn *sql.DB) (*datasource.SQLSetStatementsSummary, error) {
	sqlsetOwner := sf.parameter.GetOracleConnStr().GetUpperUser()
	sourceSchemas := sf.parameter.GetIncludingSchemasBySQLSource()
	sqlsetName := sf.parameter.SQLFactor.GetSQLSetName()

	if sf.parameter.IsFileMode() {
		sqlsetOwner = sf.parameter.AppSQLsFilename
		sourceSchemas = sf.parameter.GetIncludingSchemasBySQLSource()
	}

	summaries, getErr := models.GetDatasourceReaderWriter().GetSQLSetStatementsSummaries(ctx, conn, sf.taskId, sqlsetName, sqlsetOwner, sourceSchemas)
	if getErr != nil {
		log.Errorf("Failed to retrieve SQLSet statements summaries, err:%v", getErr)
		return nil, errors.NewErrorf(errors.TMS_FETCH_STATEMENT_FAILED, "Failed to retrieve SQLSet statements summaries")
	}

	return summaries.GetTotal(), nil
}

func (sf *SQLFetcher) buildCountSQL() string {
	p := sf.parameter
	schemas := lo.Map(p.GetIncludingSchemasBySQLSource(), func(s string, _ int) string {
		return "'" + strings.TrimSpace(strings.ToUpper(s)) + "'"
	})
	countSQL := servicecommon.CountAllSchemaSQLSetSQLTemplate
	countSQL = strings.ReplaceAll(countSQL, sqlcommon.VAR_SQLSET_NAME, p.GetSQLFactor().GetSQLSetName())
	countSQL = strings.ReplaceAll(countSQL, sqlcommon.VAR_USERNAME, strings.TrimSpace(strings.ToUpper(p.GetUsername())))
	countSQL = strings.ReplaceAll(countSQL, sqlcommon.VAR_COMMAND_TYPE_LIST, strings.Join(p.GetIncludingSQLTypeStringList(), ","))
	countSQL = strings.ReplaceAll(countSQL, sqlcommon.VAR_INCLUDING_SCHEMAS, strings.Join(schemas, ","))
	countSQL = strings.ReplaceAll(countSQL, sqlcommon.VAR_AND_TRUE, p.BuildAndCondition(""))
	return countSQL
}

func (sf *SQLFetcher) querySQLStatementToChan(ctx context.Context, conn *sql.DB, isVersion19 bool) {
	defer conn.Close() // Close conn when goroutine ends

	const FILL_FAKE_DATA = false
	if FILL_FAKE_DATA {
		// for i in range 100000
		for i := 0; i < 100; i++ {
			sf.oracleSQLChan <- &structs.SpaSqlStmt{
				OraSqlsetName:        "fake_" + strconv.Itoa(i+1),
				OraSqlsetOwner:       "TEST",
				OraSqlId:             "fake_" + strconv.Itoa(i+1),
				OraSqlText:           "select * from " + uuid.NewString()[:1],
				OraParsingSchemaName: "TEST",
			}
		}
	} else {
		querySQL := sf.buildQuerySQL(isVersion19)
		log.Infof("Executing SPA query for SQL statements, sql:%s", stringutil.RemoveSpecialLetterForLog(querySQL))
		rows, err := conn.QueryContext(ctx, querySQL)
		if err != nil {
			log.Errorf("Failed to query Oracle database for SQL statements, err:%s", err)
			sf.errChan <- err
			return
		}
		defer rows.Close()
		for rows.Next() {
			stmt, err := sf.scanSQLStatement(rows)
			if err != nil {
				sf.errChan <- err
				return
			}
			sf.oracleSQLChan <- stmt
		}
	}
	sf.queryFinishChan <- true
}

type BuildFetchParam struct {
	WithReplay    bool
	WithExecCodes bool
	Step          string
}

func (sf *SQLFetcher) querySQLStatementToChanV2(ctx context.Context, conn *sql.DB, fetchParam BuildFetchParam) {
	defer conn.Close() // Close conn when goroutine ends

	querySQL := sf.buildQuerySQLV2(fetchParam)
	log.Infof("Executing SPA v2 query for SQL statements, sql:%s", stringutil.RemoveSpecialLetterForLog(querySQL))
	rows, err := conn.QueryContext(ctx, querySQL)
	if err != nil {
		log.Errorf("Failed to query Oracle database for SQL statements (V2), err:%s", err)
		sf.errChan <- err
		return
	}
	defer rows.Close()
	for rows.Next() {
		stmt, err := sf.scanSQLStatementV2(rows)
		if err != nil {
			sf.errChan <- err
			return
		}
		sf.oracleSQLChan <- stmt
	}
	sf.queryFinishChan <- true
}

func (sf *SQLFetcher) buildQuerySQL(isVersion19 bool) string {
	p := sf.parameter
	schemas := lo.Map(p.GetIncludingSchemasBySQLSource(), func(s string, _ int) string {
		return "'" + strings.TrimSpace(strings.ToUpper(s)) + "'"
	})
	querySQL := servicecommon.OracleSqlQuery
	if isVersion19 {
		querySQL = servicecommon.OracleSqlQueryV19
	}
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_SQLSET_NAME, p.GetSQLFactor().GetSQLSetName())
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_INCLUDING_SCHEMAS, strings.Join(schemas, ","))
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_USERNAME, strings.TrimSpace(strings.ToUpper(p.GetUsername())))
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_AND_TRUE, p.BuildAndCondition("t1"))
	if p.HasOrderBy() {
		querySQL += fmt.Sprintf(" ORDER BY %s", p.SQLFactor.SQLOrderBy)
	}
	return querySQL
}

func (sf *SQLFetcher) buildQuerySQLV2(fetchParam BuildFetchParam) string {
	p := sf.parameter

	includingSchemas := p.GetIncludingSchemasBySQLSource()
	var querySQL string
	if fetchParam.WithReplay {
		querySQL = servicecommon.OracleSqlQueryV2
	} else if fetchParam.WithExecCodes {
		querySQL = servicecommon.OracleSqlQueryV2WithExecCodes
	}

	owner := sf.parameter.GetOracleConnStr().GetUpperUser()

	if sf.parameter.IsFileMode() {
		owner = sf.parameter.AppSQLsFilename
	}

	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_SQLSET_OWNER, `'`+owner+`'`)
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_INCLUDING_SCHEMAS, `'`+strings.Join(includingSchemas, "','")+`'`)
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_EXEC_CODES, `'1064'`)
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_SQLSET_NAME, p.GetSQLFactor().GetSQLSetName())
	querySQL = strings.ReplaceAll(querySQL, sqlcommon.VAR_TASK_ID, fmt.Sprintf("%d", p.TaskID))
	if p.HasOrderBy() {
		querySQL += fmt.Sprintf(" ORDER BY %s", p.SQLFactor.SQLOrderBy)
	}
	return querySQL
}

func (sf *SQLFetcher) scanSQLStatement(rows *sql.Rows) (*structs.SpaSqlStmt, error) {
	var stmt structs.SpaSqlStmt
	var planTimestamp sql.NullTime
	var oraModule, paramPos, paramVal, lastExecStart, oraCmdType sql.NullString
	var rid int
	err := rows.Scan(&rid, &stmt.OraSqlsetName, &stmt.OraSqlsetOwner, &oraModule, &stmt.OraSqlId,
		&stmt.OraSqlText, &stmt.OraParsingSchemaName, &stmt.OraPlanHashValue, &paramPos, &paramVal,
		&stmt.OraExecutions, &stmt.OraElapsedTimeMs, &lastExecStart, &planTimestamp, &oraCmdType)
	if err != nil {
		log.Errorf("Failed to scan Oracle database rows, err:%s", err)
		return nil, err
	}
	if rid == 1 {
		stmt.OraPlanTimestamp = planTimestamp.Time
		if oraModule.Valid {
			stmt.OraModule = oraModule.String
		}
		if paramPos.Valid {
			stmt.OraParameterPosition = paramPos.String
		}
		if paramVal.Valid {
			stmt.OraParameterValue = paramVal.String
		}
		if lastExecStart.Valid {
			stmt.OraLastExecStartTime = lastExecStart.String
		}
		if oraCmdType.Valid {
			stmt.OraCommandType = oraCmdType.String
		}
	}
	return &stmt, nil
}

func (sf *SQLFetcher) scanSQLStatementV2(rows *sql.Rows) (*structs.SpaSqlStmt, error) {
	var stmt structs.SpaSqlStmt
	var planTimestamp sql.NullTime
	var oraModule, paramPos, paramVal, lastExecStart, oraCmdType sql.NullString
	var rid int
	err := rows.Scan(&rid,
		&stmt.OraSqlsetName,
		&stmt.OraSqlsetOwner,
		&oraModule,
		&stmt.OraSqlId,
		&stmt.OraDigestId,
		&stmt.OraSqlText,
		&stmt.OraParsingSchemaName,
		&stmt.OraPlanHashValue,
		&stmt.OraExecutions,
		&stmt.TidbExecCode,
		&stmt.RewriteSQLText,
		&stmt.RewritePrompts,
		&paramPos,
		&paramVal,
		&stmt.OraElapsedTimeMs)
	if err != nil {
		log.Errorf("Failed to scan Oracle database rows (V2), err:%s", err)
		return nil, err
	}
	if rid == 1 {
		stmt.OraPlanTimestamp = planTimestamp.Time
		if oraModule.Valid {
			stmt.OraModule = oraModule.String
		}
		if paramPos.Valid {
			stmt.OraParameterPosition = paramPos.String
		}
		if paramVal.Valid {
			stmt.OraParameterValue = paramVal.String
		}
		if lastExecStart.Valid {
			stmt.OraLastExecStartTime = lastExecStart.String
		}
		if oraCmdType.Valid {
			stmt.OraCommandType = oraCmdType.String
		}
	}
	return &stmt, nil
}

func (sf *SQLFetcher) CloseChanIfNeed(ctx context.Context) error {
	select {
	case <-sf.queryFinishChan:
		log.Infof("Successfully closed channels after query completion")
		close(sf.oracleSQLChan)
		close(sf.errChan)
		close(sf.queryFinishChan)
		return nil
	case err := <-sf.errChan:
		log.Errorf("Failed to close channels due to error: %v", err)
		close(sf.oracleSQLChan)
		close(sf.queryFinishChan)
		close(sf.errChan)
		return err
	case <-ctx.Done():
		log.Warnf("Context cancelled while waiting for channel closure")
		close(sf.oracleSQLChan)
		close(sf.queryFinishChan)
		close(sf.errChan)
		return ctx.Err()
	}
}
