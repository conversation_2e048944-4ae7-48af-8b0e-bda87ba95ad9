package dm

// WorkerConfigDTO 表示工作节点配置的 DTO 结构
type WorkerConfigDTO struct {
	// 工作节点名称
	Name string `yaml:"name" json:"name" toml:"name"`

	// 日志级别
	LogLevel string `yaml:"log-level" json:"log_level" toml:"log-level"`

	// 日志文件路径
	LogFile string `yaml:"log-file" json:"log_file" toml:"log-file"`

	// 日志格式
	LogFormat string `yaml:"log-format" json:"log_format" toml:"log-format"`

	// 日志轮转配置
	LogRotate string `yaml:"log-rotate" json:"log_rotate" toml:"log-rotate"`

	// 加入集群的地址列表
	Join string `yaml:"join" json:"join" toml:"join"`

	// 工作节点监听地址
	WorkerAddr string `yaml:"worker-addr" json:"worker_addr" toml:"worker-addr"`

	// 对外广播地址
	AdvertiseAddr string `yaml:"advertise-addr" json:"advertise_addr" toml:"advertise-addr"`

	// 配置文件路径
	ConfigFile string `yaml:"config-file" json:"config_file" toml:"config-file"`

	// 保活时间（秒）
	KeepaliveTTL int64 `yaml:"keepalive-ttl" json:"keepalive_ttl" toml:"keepalive-ttl"`

	// Relay 保活时间（秒）
	RelayKeepaliveTTL int64 `yaml:"relay-keepalive-ttl" json:"relay_keepalive_ttl" toml:"relay-keepalive-ttl"`

	// Relay 目录
	RelayDir string `yaml:"relay-dir" json:"relay_dir" toml:"relay-dir"`

	// SSL CA 证书路径
	SSLCA string `yaml:"ssl-ca" json:"ssl_ca" toml:"ssl-ca"`

	// SSL 证书路径
	SSLCert string `yaml:"ssl-cert" json:"ssl_cert" toml:"ssl-cert"`

	// SSL 私钥路径
	SSLKey string `yaml:"ssl-key" json:"ssl_key" toml:"ssl-key"`

	// SSL CA 证书 Base64 编码
	SSLCABase64 string `yaml:"ssl-ca-base64" json:"ssl_ca_base64" toml:"ssl-ca-base64"`

	// SSL 私钥 Base64 编码
	SSLKeyBase64 string `yaml:"ssl-key-base64" json:"ssl_key_base64" toml:"ssl-key-base64"`

	// SSL 证书 Base64 编码
	SSLCertBase64 string `yaml:"ssl-cert-base64" json:"ssl_cert_base64" toml:"ssl-cert-base64"`
}

// SourceConfigDTO 表示数据源配置的 DTO 结构
type SourceConfigDTO struct {
	// 数据源 ID
	SourceID string `yaml:"source-id" json:"source_id" toml:"source-id"`

	// 是否开启 GTID
	EnableGTID bool `yaml:"enable-gtid" json:"enable_gtid" toml:"enable-gtid"`

	// 是否开启 relay log
	EnableRelay bool `yaml:"enable-relay" json:"enable_relay" toml:"enable-relay"`

	// 拉取上游 binlog 的起始文件名
	RelayBinlogName string `yaml:"relay-binlog-name" json:"relay_binlog_name" toml:"relay-binlog-name"`

	// 拉取上游 binlog 的起始 GTID
	RelayBinlogGTID string `yaml:"relay-binlog-gtid" json:"relay_binlog_gtid" toml:"relay-binlog-gtid"`

	// 存储 relay log 的目录，默认值为 "relay-dir"。从 v6.1 版本起该配置标记为弃用，被 worker 配置中的同名参数取代
	RelayDir string `yaml:"relay-dir" json:"relay_dir" toml:"relay-dir"`

	// 上游数据库连接配置
	From DatabaseConfig `yaml:"from" json:"from" toml:"from"`

	// 清理配置
	Purge *PurgeConfig `yaml:"purge,omitempty" json:"purge,omitempty" toml:"purge,omitempty"`

	// 检查器配置
	Checker *CheckerConfig `yaml:"checker,omitempty" json:"checker,omitempty" toml:"checker,omitempty"`

	// 是否区分大小写
	CaseSensitive bool `yaml:"case-sensitive" json:"case_sensitive" toml:"case-sensitive"`

	// Binlog 事件过滤器
	Filters []EventFilter `yaml:"filters,omitempty" json:"filters,omitempty" toml:"filters,omitempty"`
}

// DatabaseConfig 表示数据库连接配置
type DatabaseConfig struct {
	// 数据库主机地址
	Host string `yaml:"host" json:"host" toml:"host"`

	// 数据库端口
	Port int `yaml:"port" json:"port" toml:"port"`

	// 数据库用户名
	User string `yaml:"user" json:"user" toml:"user"`

	// 数据库密码（推荐使用 dmctl 对上游数据库的用户密码加密之后的密码）
	Password string `yaml:"password" json:"password" toml:"password"`

	// 上游数据库 TLS 相关配置
	Security *SecurityConfig `yaml:"security,omitempty" json:"security,omitempty" toml:"security,omitempty"`
}

// SecurityConfig 表示安全配置
type SecurityConfig struct {
	// SSL CA 证书路径
	SSLCa string `yaml:"ssl-ca" json:"ssl_ca" toml:"ssl-ca"`

	// SSL 证书路径
	SSLCert string `yaml:"ssl-cert" json:"ssl_cert" toml:"ssl-cert"`

	// SSL 密钥路径
	SSLKey string `yaml:"ssl-key" json:"ssl_key" toml:"ssl-key"`
}

// PurgeConfig 表示清理配置
type PurgeConfig struct {
	// 清理间隔时间（秒）
	Interval int64 `yaml:"interval" json:"interval" toml:"interval"`

	// 过期时间
	Expires int64 `yaml:"expires" json:"expires" toml:"expires"`

	// 剩余空间（GB）
	RemainSpace int64 `yaml:"remain-space" json:"remain_space" toml:"remain-space"`
}

// CheckerConfig 表示检查器配置
type CheckerConfig struct {
	// 是否启用检查
	CheckEnable bool `yaml:"check-enable" json:"check_enable" toml:"check-enable"`

	// 回滚延迟时间
	BackoffRollback string `yaml:"backoff-rollback" json:"backoff_rollback" toml:"backoff-rollback"`

	// 最大延迟时间（不能小于 1s）
	BackoffMax string `yaml:"backoff-max" json:"backoff_max" toml:"backoff-max"`
}

// EventFilter 表示 Binlog 事件过滤器
type EventFilter struct {
	// 数据库模式匹配
	SchemaPattern string `yaml:"schema-pattern" json:"schema_pattern" toml:"schema-pattern"`

	// 表名匹配
	TablePattern string `yaml:"table-pattern" json:"table_pattern" toml:"table-pattern"`

	// 事件类型列表
	Events []string `yaml:"events" json:"events" toml:"events"`

	// SQL 模式匹配
	SQLPattern []string `yaml:"sql-pattern" json:"sql_pattern" toml:"sql-pattern"`

	// 操作类型
	Action string `yaml:"action" json:"action" toml:"action"`
}

// MasterConfigDTO 表示主节点配置的 DTO 结构
type MasterConfigDTO struct {
	// 日志级别
	LogLevel string `yaml:"log-level" json:"log_level" toml:"log-level"`

	// 日志文件路径
	LogFile string `yaml:"log-file" json:"log_file" toml:"log-file"`

	// 日志格式
	LogFormat string `yaml:"log-format" json:"log_format" toml:"log-format"`

	// 日志轮转配置
	LogRotate string `yaml:"log-rotate" json:"log_rotate" toml:"log-rotate"`

	// RPC 超时时间
	RPCTimeout string `yaml:"rpc-timeout" json:"rpc_timeout" toml:"rpc-timeout"`

	// RPC 限流配置
	RPCRateLimit float64 `yaml:"rpc-rate-limit" json:"rpc_rate_limit" toml:"rpc-rate-limit"`

	// RPC 突发限流配置
	RPCRateBurst int `yaml:"rpc-rate-burst" json:"rpc_rate_burst" toml:"rpc-rate-burst"`

	// 主节点监听地址
	MasterAddr string `yaml:"master-addr" json:"master_addr" toml:"master-addr"`

	// 对外广播地址
	AdvertiseAddr string `yaml:"advertise-addr" json:"advertise_addr" toml:"advertise-addr"`

	// 配置文件路径
	ConfigFile string `yaml:"config-file" json:"config_file" toml:"config-file"`

	// 节点名称
	Name string `yaml:"name" json:"name" toml:"name"`

	// 数据目录
	DataDir string `yaml:"data-dir" json:"data_dir" toml:"data-dir"`

	// Peer URLs
	PeerURLs string `yaml:"peer-urls" json:"peer_urls" toml:"peer-urls"`

	// 对外广播 Peer URLs
	AdvertisePeerURLs string `yaml:"advertise-peer-urls" json:"advertise_peer_urls" toml:"advertise-peer-urls"`

	// 初始集群配置
	InitialCluster string `yaml:"initial-cluster" json:"initial_cluster" toml:"initial-cluster"`

	// 初始集群状态
	InitialClusterState string `yaml:"initial-cluster-state" json:"initial_cluster_state" toml:"initial-cluster-state"`

	// 加入集群地址
	Join string `yaml:"join" json:"join" toml:"join"`

	// 最大事务操作数
	MaxTxnOps int64 `yaml:"max-txn-ops" json:"max_txn_ops" toml:"max-txn-ops"`

	// 最大请求字节数
	MaxRequestBytes int64 `yaml:"max-request-bytes" json:"max_request_bytes" toml:"max-request-bytes"`

	// 自动压缩模式
	AutoCompactionMode string `yaml:"auto-compaction-mode" json:"auto_compaction_mode" toml:"auto-compaction-mode"`

	// 自动压缩保留时间
	AutoCompactionRetention string `yaml:"auto-compaction-retention" json:"auto_compaction_retention" toml:"auto-compaction-retention"`

	// 后端存储配额
	QuotaBackendBytes int64 `yaml:"quota-backend-bytes" json:"quota_backend_bytes" toml:"quota-backend-bytes"`

	// 是否开启 OpenAPI
	OpenAPI bool `yaml:"openapi" json:"openapi" toml:"openapi"`

	// V1 源路径
	V1SourcesPath string `yaml:"v1-sources-path" json:"v1_sources_path" toml:"v1-sources-path"`

	// SSL CA 证书路径
	SSLCA string `yaml:"ssl-ca" json:"ssl_ca" toml:"ssl-ca"`

	// SSL 证书路径
	SSLCert string `yaml:"ssl-cert" json:"ssl_cert" toml:"ssl-cert"`

	// SSL 私钥路径
	SSLKey string `yaml:"ssl-key" json:"ssl_key" toml:"ssl-key"`

	// SSL CA 证书 Base64 编码
	SSLCABase64 string `yaml:"ssl-ca-base64" json:"ssl_ca_base64" toml:"ssl-ca-base64"`

	// SSL 私钥 Base64 编码
	SSLKeyBase64 string `yaml:"ssl-key-base64" json:"ssl_key_base64" toml:"ssl-key-base64"`

	// SSL 证书 Base64 编码
	SSLCertBase64 string `yaml:"ssl-cert-base64" json:"ssl_cert_base64" toml:"ssl-cert-base64"`

	// 密钥路径
	SecretKeyPath string `yaml:"secret-key-path" json:"secret_key_path" toml:"secret-key-path"`

	// 实验性配置
	Experimental map[string]interface{} `yaml:"experimental" json:"experimental" toml:"experimental"`
}

// TaskConfigDTO 表示任务配置的 DTO 结构
type TaskConfigDTO struct {
	// 任务名称
	Name string `yaml:"name" json:"name"`

	// 任务模式
	TaskMode string `yaml:"task-mode" json:"task_mode"`

	// 是否分片
	IsSharding bool `yaml:"is-sharding" json:"is_sharding"`

	// 分片模式
	ShardMode string `yaml:"shard-mode" json:"shard_mode"`

	// 严格乐观分片模式
	StrictOptimisticShardMode bool `yaml:"strict-optimistic-shard-mode" json:"strict_optimistic_shard_mode"`

	// 忽略检查项
	IgnoreCheckingItems []string `yaml:"ignore-checking-items" json:"ignore_checking_items"`

	// 元数据库名
	MetaSchema string `yaml:"meta-schema" json:"meta_schema"`

	// 启用心跳
	EnableHeartbeat bool `yaml:"enable-heartbeat" json:"enable_heartbeat"`

	// 心跳更新间隔
	HeartbeatUpdateInterval int `yaml:"heartbeat-update-interval" json:"heartbeat_update_interval"`

	// 心跳报告间隔
	HeartbeatReportInterval int `yaml:"heartbeat-report-interval" json:"heartbeat_report_interval"`

	// 时区
	Timezone string `yaml:"timezone" json:"timezone"`

	// 大小写敏感
	CaseSensitive bool `yaml:"case-sensitive" json:"case_sensitive"`

	// 排序规则兼容性
	CollationCompatible string `yaml:"collation_compatible" json:"collation_compatible"`

	// 目标数据库配置
	TargetDatabase *TaskTargetDatabase `yaml:"target-database" json:"target_database"`

	// MySQL 实例配置
	MySQLInstances []*MySQLInstance `yaml:"mysql-instances" json:"mysql_instances"`

	// 在线 DDL
	OnlineDDL bool `yaml:"online-ddl" json:"online_ddl"`

	// 影子表规则
	ShadowTableRules []string `yaml:"shadow-table-rules" json:"shadow_table_rules"`

	// 垃圾表规则
	TrashTableRules []string `yaml:"trash-table-rules" json:"trash_table_rules"`

	// 在线 DDL 方案
	OnlineDDLScheme string `yaml:"online-ddl-scheme" json:"online_ddl_scheme"`

	// 路由规则
	Routes map[string]*RouteRule `yaml:"routes" json:"routes"`

	// 过滤器
	Filters map[string]*BinlogEventRule `yaml:"filters" json:"filters"`

	// 列映射
	ColumnMappings map[string]*ColumnRule `yaml:"column-mappings" json:"column_mappings"`

	// 表达式过滤器
	ExpressionFilter map[string]*ExpressionFilter `yaml:"expression-filter" json:"expression_filter"`

	// 黑白名单（已废弃）
	BlackWhiteList map[string]*BlockAllowListRule `yaml:"black-white-list" json:"black_white_list"`

	// 阻止允许列表
	BlockAllowList map[string]*BlockAllowListRule `yaml:"block-allow-list" json:"block_allow_list"`

	// Mydumper 配置
	Mydumpers map[string]*MydumperConfig `yaml:"mydumpers" json:"mydumpers"`

	// Loader 配置
	Loaders map[string]*LoaderConfig `yaml:"loaders" json:"loaders"`

	// Syncer 配置
	Syncers map[string]*SyncerConfig `yaml:"syncers" json:"syncers"`

	// 验证器配置
	Validators map[string]*ValidatorConfig `yaml:"validators" json:"validators"`

	// 清理转储文件
	CleanDumpFile bool `yaml:"clean-dump-file" json:"clean_dump_file"`

	// ANSI 引号
	AnsiQuotes bool `yaml:"ansi-quotes" json:"ansi_quotes"`

	// 移除元数据
	RemoveMeta bool `yaml:"remove-meta" json:"remove_meta"`

	// 实验性配置
	Experimental *ExperimentalConfig `yaml:"experimental" json:"experimental"`
}

// TaskTargetDatabase 表示目标数据库配置
type TaskTargetDatabase struct {
	Host             string            `yaml:"host" json:"host"`
	Port             int               `yaml:"port" json:"port"`
	User             string            `yaml:"user" json:"user"`
	Password         string            `yaml:"password" json:"password"`
	MaxAllowedPacket interface{}       `yaml:"max-allowed-packet" json:"max_allowed_packet"`
	Session          map[string]string `yaml:"session" json:"session"`
	Security         *SecurityConfig   `yaml:"security" json:"security"`
}

// MySQLInstance 表示 MySQL 实例配置
type MySQLInstance struct {
	SourceID            string          `yaml:"source-id" json:"source_id"`
	Meta                *Meta           `yaml:"meta" json:"meta"`
	FilterRules         []string        `yaml:"filter-rules" json:"filter_rules"`
	ColumnMappingRules  []string        `yaml:"column-mapping-rules" json:"column_mapping_rules"`
	RouteRules          []string        `yaml:"route-rules" json:"route_rules"`
	ExpressionFilters   []string        `yaml:"expression-filters" json:"expression_filters"`
	BlackWhiteList      string          `yaml:"black-white-list" json:"black_white_list"`
	BlockAllowList      string          `yaml:"block-allow-list" json:"block_allow_list"`
	MydumperConfigName  string          `yaml:"mydumper-config-name" json:"mydumper_config_name"`
	Mydumper            *MydumperConfig `yaml:"mydumper" json:"mydumper"`
	MydumperThread      int             `yaml:"mydumper-thread" json:"mydumper_thread"`
	LoaderConfigName    string          `yaml:"loader-config-name" json:"loader_config_name"`
	Loader              *LoaderConfig   `yaml:"loader" json:"loader"`
	LoaderThread        int             `yaml:"loader-thread" json:"loader_thread"`
	SyncerConfigName    string          `yaml:"syncer-config-name" json:"syncer_config_name"`
	Syncer              *SyncerConfig   `yaml:"syncer" json:"syncer"`
	SyncerThread        int             `yaml:"syncer-thread" json:"syncer_thread"`
	ValidatorConfigName string          `yaml:"validator-config-name" json:"validator_config_name"`
}

// RouteRule 表示路由规则
type RouteRule struct {
	SchemaPattern string `yaml:"schema-pattern" json:"schema_pattern"`
	TablePattern  string `yaml:"table-pattern" json:"table_pattern"`
	TargetSchema  string `yaml:"target-schema" json:"target_schema"`
	TargetTable   string `yaml:"target-table" json:"target_table"`
}

// BlockAllowListRule 表示阻止允许列表规则
type BlockAllowListRule struct {
	DoTables     []*TableRef `yaml:"do-tables" json:"do_tables"`
	DoDbs        []string    `yaml:"do-dbs" json:"do_dbs"`
	IgnoreTables []*TableRef `yaml:"ignore-tables" json:"ignore_tables"`
	IgnoreDbs    []string    `yaml:"ignore-dbs" json:"ignore_dbs"`
}

// TableRef 表示表引用
type TableRef struct {
	DbName  string `yaml:"db-name" json:"db_name"`
	TblName string `yaml:"tbl-name" json:"tbl_name"`
}

// MydumperConfig 表示 Mydumper 配置
type MydumperConfig struct {
	MydumperPath  string `yaml:"mydumper-path" json:"mydumper_path"`
	Threads       int    `yaml:"threads" json:"threads"`
	ChunkFilesize string `yaml:"chunk-filesize" json:"chunk_filesize"`
	StatementSize int    `yaml:"statement-size" json:"statement_size"`
	Rows          int    `yaml:"rows" json:"rows"`
	Where         string `yaml:"where" json:"where"`
	SkipTzUtc     bool   `yaml:"skip-tz-utc" json:"skip_tz_utc"`
	ExtraArgs     string `yaml:"extra-args" json:"extra_args"`
}

// LoaderConfig 表示 Loader 配置
type LoaderConfig struct {
	PoolSize            int    `yaml:"pool-size" json:"pool_size"`
	Dir                 string `yaml:"dir" json:"dir"`
	SortingDirPhysical  string `yaml:"sorting-dir-physical" json:"sorting_dir_physical"`
	ImportMode          string `yaml:"import-mode" json:"import_mode"`
	OnDuplicate         string `yaml:"on-duplicate" json:"on_duplicate"`
	OnDuplicateLogical  string `yaml:"on-duplicate-logical" json:"on_duplicate_logical"`
	OnDuplicatePhysical string `yaml:"on-duplicate-physical" json:"on_duplicate_physical"`
	DiskQuotaPhysical   int64  `yaml:"disk-quota-physical" json:"disk_quota_physical"`
	ChecksumPhysical    string `yaml:"checksum-physical" json:"checksum_physical"`
	Analyze             string `yaml:"analyze" json:"analyze"`
	RangeConcurrency    int    `yaml:"range-concurrency" json:"range_concurrency"`
	CompressKVPairs     string `yaml:"compress-kv-pairs" json:"compress_kv_pairs"`
	PdAddr              string `yaml:"pd-addr" json:"pd_addr"`
}

// SyncerConfig 表示 Syncer 配置
type SyncerConfig struct {
	MetaFile                string `yaml:"meta-file" json:"meta_file"`
	WorkerCount             int    `yaml:"worker-count" json:"worker_count"`
	Batch                   int    `yaml:"batch" json:"batch"`
	QueueSize               int    `yaml:"queue-size" json:"queue_size"`
	CheckpointFlushInterval int    `yaml:"checkpoint-flush-interval" json:"checkpoint_flush_interval"`
	Compact                 bool   `yaml:"compact" json:"compact"`
	MultipleRows            bool   `yaml:"multiple-rows" json:"multiple_rows"`
	MaxRetry                int    `yaml:"max-retry" json:"max_retry"`
	AutoFixGtid             bool   `yaml:"auto-fix-gtid" json:"auto_fix_gtid"`
	EnableGtid              bool   `yaml:"enable-gtid" json:"enable_gtid"`
	DisableDetect           bool   `yaml:"disable-detect" json:"disable_detect"`
	SafeMode                bool   `yaml:"safe-mode" json:"safe_mode"`
	SafeModeDuration        string `yaml:"safe-mode-duration" json:"safe_mode_duration"`
	EnableAnsiQuotes        bool   `yaml:"enable-ansi-quotes" json:"enable_ansi_quotes"`
}

// ValidatorConfig 表示验证器配置
type ValidatorConfig struct {
	Mode               string `yaml:"mode" json:"mode"`
	WorkerCount        int    `yaml:"worker-count" json:"worker_count"`
	ValidateInterval   string `yaml:"validate-interval" json:"validate_interval"`
	CheckInterval      string `yaml:"check-interval" json:"check_interval"`
	RowErrorDelay      string `yaml:"row-error-delay" json:"row_error_delay"`
	MetaFlushInterval  string `yaml:"meta-flush-interval" json:"meta_flush_interval"`
	BatchQuerySize     int    `yaml:"batch-query-size" json:"batch_query_size"`
	MaxPendingRowSize  string `yaml:"max-pending-row-size" json:"max_pending_row_size"`
	MaxPendingRowCount int64  `yaml:"max-pending-row-count" json:"max_pending_row_count"`
}

// ExperimentalConfig 表示实验性配置
type ExperimentalConfig struct {
	AsyncCheckpointFlush bool `yaml:"async-checkpoint-flush" json:"async_checkpoint_flush"`
}

// ExpressionFilter 表示表达式过滤器
type ExpressionFilter struct {
	Schema             string `yaml:"schema" json:"schema"`
	Table              string `yaml:"table" json:"table"`
	InsertValueExpr    string `yaml:"insert-value-expr" json:"insert_value_expr"`
	UpdateOldValueExpr string `yaml:"update-old-value-expr" json:"update_old_value_expr"`
	UpdateNewValueExpr string `yaml:"update-new-value-expr" json:"update_new_value_expr"`
	DeleteValueExpr    string `yaml:"delete-value-expr" json:"delete_value_expr"`
}
