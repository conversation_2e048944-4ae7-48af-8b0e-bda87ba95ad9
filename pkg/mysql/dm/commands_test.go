package dm

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

const (
	VALID_MASTER_ADDR   = "10.2.103.15:8261"
	INVALID_MASTER_ADDR = "127.0.0.1:18877"
)

func TestNewDMAdaptor(t *testing.T) {
	masterAddr := INVALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.Error(t, err, "NewDMAdaptor should return error when masterAddr is invalid")
	assert.Nil(t, adaptor, "NewDMAdaptor should return nil when masterAddr is invalid")
}

func TestNewDMAdaptorWithValidMasterAddr(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")
}

// TestListMember tests the ListMember method of DMAdaptor
func TestListMember(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	members, err := adaptor.ListMember(context.Background())
	assert.NoError(t, err, "ListMember should return no error")
	assert.NotNil(t, members, "ListMember should return a non-nil slice of members")

	// 将 members 结构体序列化为 JSON 并输出日志，便于调试和查看内容
	jsonBytes, err := json.MarshalIndent(members, "", "  ")
	if err != nil {
		t.Errorf("Failed to marshal members to JSON: %v", err)
	} else {
		t.Logf("ListMember response (JSON):\n%s", string(jsonBytes))
	}
}

// Test_ShowSource tests the ShowSource method of DMAdaptor
func Test_ShowSource(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	sources, err := adaptor.ShowSource(context.Background(), []string{})
	assert.NoError(t, err, "ShowSource should return no error")
	assert.NotNil(t, sources, "ShowSource should return a non-nil slice of sources")

	jsonBytes, err := json.MarshalIndent(sources, "", "  ")
	if err != nil {
		t.Errorf("Failed to marshal sources to JSON: %v", err)
	} else {
		t.Logf("ShowSource response (JSON):\n%s", string(jsonBytes))
	}
}

// TestGetSourceConfig tests the GetSourceConfig method of DMAdaptor
func TestGetSourceConfig(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	// 首先获取所有数据源列表
	sources, err := adaptor.ShowSource(context.Background(), []string{})
	assert.NoError(t, err, "ShowSource should return no error")
	assert.NotNil(t, sources, "ShowSource should return a non-nil slice of sources")

	// 从数据源列表中选择第一个进行配置获取测试
	if len(sources.Sources) > 0 {
		firstSourceName := sources.Sources[0].Source
		sourceConfig, err := adaptor.GetSourceConfig(context.Background(), firstSourceName)
		assert.NoError(t, err, "GetSourceConfig should return no error")
		assert.NotNil(t, sourceConfig, "GetSourceConfig should return a non-nil source config")
	} else {
		t.Skip("No sources available for testing GetSourceConfig")
	}

	// 将 sources 结构体序列化为 JSON 并输出日志，便于调试和查看内容
	jsonBytes, err := json.MarshalIndent(sources, "", "  ")
	if err != nil {
		t.Errorf("Failed to marshal sources to JSON: %v", err)
	} else {
		t.Logf("GetSourceConfig response (JSON):\n%s", string(jsonBytes))
	}
}

// TestGetWorkerConfig tests the GetWorkerConfig method of DMAdaptor
func TestGetWorkerConfig(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	// 首先获取所有成员列表
	members, err := adaptor.ListMember(context.Background())
	assert.NoError(t, err, "ListMember should return no error")
	assert.NotNil(t, members, "ListMember should return a non-nil member list")

	// 从成员列表中选择第一个 worker 进行配置获取测试
	if len(members.Workers) > 0 {
		firstWorkerName := members.Workers[0].Name
		workerConfig, err := adaptor.GetWorkerConfig(context.Background(), firstWorkerName)
		assert.NoError(t, err, "GetWorkerConfig should return no error")
		assert.NotNil(t, workerConfig, "GetWorkerConfig should return a non-nil worker config")

		// 验证配置内容不为空
		assert.NotEmpty(t, workerConfig.Name, "Worker config name should not be empty")
		assert.NotEmpty(t, workerConfig.WorkerAddr, "Worker config worker-addr should not be empty")
		assert.NotEmpty(t, workerConfig.AdvertiseAddr, "Worker config advertise-addr should not be empty")
		assert.NotEmpty(t, workerConfig.Join, "Worker config join address should not be empty")

		// 将 worker 配置序列化为 JSON 并输出日志，便于调试和查看内容
		jsonBytes, err := json.MarshalIndent(workerConfig, "", "  ")
		if err != nil {
			t.Errorf("Failed to marshal worker config to JSON: %v", err)
		} else {
			t.Logf("GetWorkerConfig response for worker '%s' (JSON):\n%s", firstWorkerName, string(jsonBytes))
		}
	} else {
		t.Skip("No workers available for testing GetWorkerConfig")
	}
}

// TestGetMasterConfig tests the GetMasterConfig method of DMAdaptor
func TestGetMasterConfig(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	// 首先获取所有成员列表
	members, err := adaptor.ListMember(context.Background())
	assert.NoError(t, err, "ListMember should return no error")
	assert.NotNil(t, members, "ListMember should return a non-nil member list")

	// 从成员列表中选择第一个 master 进行配置获取测试
	if len(members.Masters) > 0 {
		firstMasterName := members.Masters[0].Name

		// 测试主节点配置获取

		masterConfig, err := adaptor.GetMasterConfig(context.Background(), firstMasterName)
		assert.NoError(t, err, "GetMasterConfig should return no error")
		assert.NotNil(t, masterConfig, "GetMasterConfig should return a non-nil master config")

		// 验证配置内容不为空
		assert.NotEmpty(t, masterConfig.Name, "Master config name should not be empty")
		assert.NotEmpty(t, masterConfig.MasterAddr, "Master config master-addr should not be empty")
		assert.NotEmpty(t, masterConfig.AdvertiseAddr, "Master config advertise-addr should not be empty")

		// 将 master 配置序列化为 JSON 并输出日志，便于调试和查看内容
		jsonBytes, err := json.MarshalIndent(masterConfig, "", "  ")
		if err != nil {
			t.Errorf("Failed to marshal master config to JSON: %v", err)
		} else {
			t.Logf("GetMasterConfig response for master '%s' (JSON):\n%s", firstMasterName, string(jsonBytes))
		}
	} else {
		t.Skip("No masters available for testing GetMasterConfig")
	}
}

// TestGetTaskConfig tests the GetTaskConfig method of DMAdaptor
func TestGetTaskConfig(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	// 直接使用已知的任务名进行测试
	taskName := "tms-dm-task-3_1" // 使用一个已知存在的任务

	t.Logf("Testing with task: %s", taskName)

	// 测试任务配置获取

	taskConfig, err := adaptor.GetTaskConfig(context.Background(), taskName)
	assert.NoError(t, err, "GetTaskConfig should return no error")
	assert.NotNil(t, taskConfig, "GetTaskConfig should return a non-nil task config")

	// 验证配置内容不为空
	assert.NotEmpty(t, taskConfig.Name, "Task config name should not be empty")
	assert.NotEmpty(t, taskConfig.TaskMode, "Task config task-mode should not be empty")
	assert.NotNil(t, taskConfig.TargetDatabase, "Task config target-database should not be nil")

	// 将 task 配置序列化为 JSON 并输出日志，便于调试和查看内容
	jsonBytes, err := json.MarshalIndent(taskConfig, "", "  ")
	if err != nil {
		t.Errorf("Failed to marshal task config to JSON: %v", err)
	} else {
		t.Logf("GetTaskConfig response for task '%s' (JSON):\n%s", taskName, string(jsonBytes))
	}
}

// TestEnhancedQueryStatusFunctions tests the new enhanced QueryStatus functions
func TestEnhancedQueryStatusFunctions(t *testing.T) {
	masterAddr := VALID_MASTER_ADDR
	adaptor, err := NewDMAdaptor(masterAddr)
	assert.NoError(t, err, "NewDMAdaptor should return no error when masterAddr is valid")
	assert.NotNil(t, adaptor, "NewDMAdaptor should return a non-nil DMAdaptor when masterAddr is valid")

	// Test QueryStatusWithMore
	t.Run("QueryStatusWithMore", func(t *testing.T) {
		// Test with showMore = true
		statusWithMore, err := adaptor.QueryStatusWithMore(context.Background(), "", nil, true)
		assert.NoError(t, err, "QueryStatusWithMore should return no error")
		t.Logf("QueryStatusWithMore (detailed): %+v", statusWithMore)

		// Test with showMore = false
		statusSimple, err := adaptor.QueryStatusWithMore(context.Background(), "", nil, false)
		assert.NoError(t, err, "QueryStatusWithMore should return no error")
		t.Logf("QueryStatusWithMore (simple): %+v", statusSimple)
	})

	// Test QueryAllTasksStatus
	t.Run("QueryAllTasksStatus", func(t *testing.T) {
		allTasksStatus, err := adaptor.QueryAllTasksStatus(context.Background())
		assert.NoError(t, err, "QueryAllTasksStatus should return no error")
		assert.NotNil(t, allTasksStatus, "QueryAllTasksStatus should return a non-nil response")
		t.Logf("QueryAllTasksStatus: %+v", allTasksStatus)
	})

	// Test QueryTaskStatusSummary
	t.Run("QueryTaskStatusSummary", func(t *testing.T) {
		taskName := "tms-dm-task-3_1" // Use a known task
		taskSummary, err := adaptor.QueryTaskStatusSummary(context.Background(), taskName)
		assert.NoError(t, err, "QueryTaskStatusSummary should return no error")
		assert.Equal(t, taskName, taskSummary.TaskName, "TaskName should match input")
		t.Logf("QueryTaskStatusSummary for task '%s': %+v", taskName, taskSummary)
	})

	// Test QueryValidationStatus
	t.Run("QueryValidationStatus", func(t *testing.T) {
		taskName := "tms-dm-task-3_1"
		validationStatus, err := adaptor.QueryValidationStatus(context.Background(), taskName, "")
		assert.NoError(t, err, "QueryValidationStatus should return no error")
		assert.NotNil(t, validationStatus, "QueryValidationStatus should return a non-nil response")
		t.Logf("QueryValidationStatus for task '%s': %+v", taskName, validationStatus)
	})

	// Test QueryValidationError
	t.Run("QueryValidationError", func(t *testing.T) {
		taskName := "tms-dm-task-3_1"
		validationErrors, err := adaptor.QueryValidationError(context.Background(), taskName, "")
		assert.NoError(t, err, "QueryValidationError should return no error")
		assert.NotNil(t, validationErrors, "QueryValidationError should return a non-nil response")
		t.Logf("QueryValidationError for task '%s': %+v", taskName, validationErrors)
	})
}
