package dm

type LeaderInfo struct {
	Message string `json:"message"`
	Name    string `json:"name"`
	Addr    string `json:"addr"`
}

// MasterInfo represents DM-master information.
type MasterInfo struct {
	Name       string   `json:"name"`
	MemberID   uint64   `json:"memberID"`
	Alive      bool     `json:"alive"`
	PeerURLs   []string `json:"peerURLs"`
	ClientURLs []string `json:"clientURLs"`
}

// WorkerInfo represents DM-worker information.
type WorkerInfo struct {
	Name   string `json:"name"`
	Addr   string `json:"addr"`
	Stage  string `json:"stage"`
	Source string `json:"source"`
}

// ListMemberResponseDTO is the DTO for pb.ListMemberResponse.
type ListMemberResponseDTO struct {
	Result  bool          `json:"result"`
	Msg     string        `json:"msg"`
	Leader  *LeaderInfo   `json:"leader"`
	Masters []*MasterInfo `json:"masters"`
	Workers []*WorkerInfo `json:"workers"`
}

// CommonWorkerResponseDTO represents a single worker's response in source operations.
type CommonWorkerResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
	Source string `json:"source"`
	Worker string `json:"worker"`
}

// OperateSourceResponseDTO is the DTO for pb.OperateSourceResponse.
type OperateSourceResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// StartTaskResponseDTO is the DTO for pb.StartTaskResponse.
type StartTaskResponseDTO struct {
	Result      bool                       `json:"result"`
	Msg         string                     `json:"msg"`
	Sources     []*CommonWorkerResponseDTO `json:"sources"`
	CheckResult string                     `json:"checkResult"`
}

// OperateTaskResponseDTO is the DTO for pb.OperateTaskResponse.
type OperateTaskResponseDTO struct {
	Op      string                     `json:"op"`
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// UpdateTaskResponseDTO is the DTO for pb.UpdateTaskResponse.
type UpdateTaskResponseDTO struct {
	Result      bool                       `json:"result"`
	Msg         string                     `json:"msg"`
	Sources     []*CommonWorkerResponseDTO `json:"sources"`
	CheckResult string                     `json:"checkResult"`
}

// OperateLeaderResponseDTO is the DTO for pb.OperateLeaderResponse.
type OperateLeaderResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// OperateWorkerRelayResponseDTO is the DTO for pb.OperateWorkerRelayResponse.
type OperateWorkerRelayResponseDTO struct {
	Op      string                     `json:"op"`
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// PurgeWorkerRelayResponseDTO is the DTO for pb.PurgeWorkerRelayResponse.
type PurgeWorkerRelayResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// OperateRelayResponseDTO is the DTO for pb.OperateRelayResponse.
type OperateRelayResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// OperateSchemaResponseDTO is the DTO for pb.OperateSchemaResponse.
type OperateSchemaResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// OfflineMemberResponseDTO is the DTO for pb.OfflineMemberResponse.
type OfflineMemberResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// QueryStatusResponseDTO is the DTO for pb.QueryStatusListResponse.
type QueryStatusResponseDTO struct {
	Result  bool                 `json:"result"`
	Msg     string               `json:"msg"`
	Sources []*QuerySourceStatus `json:"sources"`
}

// QuerySourceStatus represents the status of a single source in query-status response
type QuerySourceStatus struct {
	Result        bool             `json:"result"`
	Msg           string           `json:"msg,omitempty"`
	SourceStatus  *SourceStatus    `json:"sourceStatus,omitempty"`
	SubTaskStatus []*SubTaskStatus `json:"subTaskStatus,omitempty"`
}

// SourceStatus represents source information in query-status
type SourceStatus struct {
	Source string `json:"source"`
	Worker string `json:"worker"`
}

// SubTaskStatus represents subtask status information
type SubTaskStatus struct {
	Name       string             `json:"name"`
	Stage      int                `json:"stage"`
	Unit       int                `json:"unit"`
	Result     *SubTaskResult     `json:"result,omitempty"`
	Status     *SubTaskStatusInfo `json:"Status,omitempty"`
	Validation *ValidationStatus  `json:"validation,omitempty"`
}

// SubTaskResult represents the result information of a subtask
type SubTaskResult struct {
	IsCanceled bool            `json:"isCanceled,omitempty"`
	Errors     []*SubTaskError `json:"errors,omitempty"`
}

// SubTaskError represents error information in subtask result
type SubTaskError struct {
	ErrCode    int    `json:"ErrCode"`
	ErrClass   string `json:"ErrClass"`
	ErrScope   string `json:"ErrScope"`
	ErrLevel   string `json:"ErrLevel"`
	Message    string `json:"Message"`
	Workaround string `json:"Workaround,omitempty"`
}

// SubTaskStatusInfo represents the status information of a subtask
type SubTaskStatusInfo struct {
	Sync *SyncStatus `json:"sync,omitempty"`
}

// SyncStatus represents sync status information
type SyncStatus struct {
	TotalEvents      int64  `json:"totalEvents"`
	TotalTps         int64  `json:"totalTps,omitempty"`
	MasterBinlog     string `json:"masterBinlog"`
	MasterBinlogGtid string `json:"masterBinlogGtid"`
	SyncerBinlog     string `json:"syncerBinlog"`
	SyncerBinlogGtid string `json:"syncerBinlogGtid"`
	Synced           bool   `json:"synced,omitempty"`
	BinlogType       string `json:"binlogType"`
	TotalRows        int64  `json:"totalRows"`
	TotalRps         int64  `json:"totalRps,omitempty"`
}

// ValidationStatus represents validation status information
type ValidationStatus struct {
	Task                string `json:"task"`
	Source              string `json:"source"`
	Mode                string `json:"mode"`
	Stage               int    `json:"stage"`
	ValidatorBinlog     string `json:"validatorBinlog,omitempty"`
	ValidatorBinlogGtid string `json:"validatorBinlogGtid,omitempty"`
	ProcessedRowsStatus string `json:"processedRowsStatus"`
	PendingRowsStatus   string `json:"pendingRowsStatus"`
	ErrorRowsStatus     string `json:"errorRowsStatus"`
}

// CheckTaskResponseDTO is the DTO for pb.CheckTaskResponse.
type CheckTaskResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// GetConfigResponseDTO is the DTO for pb.GetCfgResponse.
type GetConfigResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
	Config string `json:"config"`
}

// ShowDDLLocksResponseDTO is the DTO for pb.ShowDDLLocksResponse.
type ShowDDLLocksResponseDTO struct {
	Result bool       `json:"result"`
	Msg    string     `json:"msg"`
	Locks  []*DDLLock `json:"locks"`
}

// UnlockDDLLockResponseDTO is the DTO for pb.UnlockDDLLockResponse.
type UnlockDDLLockResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// TaskSummaryDTO represents simplified task status for overview
type TaskSummaryDTO struct {
	TaskName   string   `json:"taskName"`
	TaskStatus string   `json:"taskStatus"`
	Sources    []string `json:"sources"`
	ErrorMsg   string   `json:"errorMsg,omitempty"`
}

// TaskSummaryResponseDTO is the response for task summary queries
type TaskSummaryResponseDTO struct {
	Result bool              `json:"result"`
	Msg    string            `json:"msg"`
	Tasks  []*TaskSummaryDTO `json:"tasks"`
}

// QueryValidationStatusResponseDTO is the response for validation status queries
type QueryValidationStatusResponseDTO struct {
	Result bool                    `json:"result"`
	Msg    string                  `json:"msg"`
	Status *ValidationStatusDetail `json:"status"`
}

// QueryValidationErrorResponseDTO is the response for validation error queries
type QueryValidationErrorResponseDTO struct {
	Result bool               `json:"result"`
	Msg    string             `json:"msg"`
	Errors []*ValidationError `json:"errors"`
}

// HandleErrorResponseDTO is the response for error handling operations
type HandleErrorResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// TransferSourceResponseDTO is the DTO for pb.TransferSourceResponse.
type TransferSourceResponseDTO struct {
	Result bool   `json:"result"`
	Msg    string `json:"msg"`
}

// StartValidationResponseDTO is the DTO for pb.StartValidationResponse.
type StartValidationResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// StopValidationResponseDTO is the DTO for pb.StopValidationResponse.
type StopValidationResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// UpdateValidationResponseDTO is the DTO for pb.UpdateValidationResponse.
type UpdateValidationResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// OperateValidationErrorResponseDTO is the DTO for pb.OperateValidationErrorResponse.
type OperateValidationErrorResponseDTO struct {
	Result  bool                       `json:"result"`
	Msg     string                     `json:"msg"`
	Sources []*CommonWorkerResponseDTO `json:"sources"`
}

// GetValidationErrorResponseDTO is the DTO for pb.GetValidationErrorResponse.
type GetValidationErrorResponseDTO struct {
	Result bool               `json:"result"`
	Msg    string             `json:"msg"`
	Error  []*ValidationError `json:"error"`
}

// GetValidationStatusResponseDTO is the DTO for pb.GetValidationStatusResponse.
type GetValidationStatusResponseDTO struct {
	Result bool                    `json:"result"`
	Msg    string                  `json:"msg"`
	Status *ValidationStatusDetail `json:"status"`
}

// DDLLock represents a DDL lock
type DDLLock struct {
	ID       string   `json:"ID"`
	Task     string   `json:"task"`
	Mode     string   `json:"mode"`
	Owner    string   `json:"owner"`
	DDLs     []string `json:"DDLs"`
	Synced   []string `json:"synced"`
	Unsynced []string `json:"unsynced"`
}

// ValidationStatusDetail represents detailed validation status
type ValidationStatusDetail struct {
	Validators    []*ValidationStatus      `json:"validators"`
	TableStatuses []*ValidationTableStatus `json:"tableStatuses"`
}

// ValidationTableStatus represents validation status for a table
type ValidationTableStatus struct {
	Source        string `json:"source"`
	SchemaName    string `json:"schemaName"`
	TableName     string `json:"tableName"`
	Stage         string `json:"stage"`
	Message       string `json:"message"`
	ProcessedRows int64  `json:"processedRows"`
	PendingRows   int64  `json:"pendingRows"`
	FailedRows    int64  `json:"failedRows"`
}

// ValidationError represents a validation error
type ValidationError struct {
	ID         string `json:"id"`
	Source     string `json:"source"`
	SchemaName string `json:"schemaName"`
	TableName  string `json:"tableName"`
	Stage      string `json:"stage"`
	Message    string `json:"message"`
	Timestamp  int64  `json:"timestamp"`
}

// BinlogEventRule represents binlog event filtering rules
type BinlogEventRule struct {
	SchemaPattern string   `json:"schemaPattern"`
	TablePattern  string   `json:"tablePattern"`
	Events        []string `json:"events"`
	SQLPattern    []string `json:"sqlPattern"`
	Action        string   `json:"action"`
}

// ColumnRule represents column mapping rules
type ColumnRule struct {
	SchemaPattern string   `json:"schemaPattern"`
	TablePattern  string   `json:"tablePattern"`
	SourceColumn  string   `json:"sourceColumn"`
	TargetColumn  string   `json:"targetColumn"`
	Expression    string   `json:"expression"`
	Arguments     []string `json:"arguments"`
}

// Meta represents binlog position metadata
type Meta struct {
	BinLogName string `yaml:"binlog-name" json:"binlog_name"`
	BinLogPos  uint32 `yaml:"binlog-pos" json:"binlog_pos"`
	BinLogGTID string `yaml:"binlog-gtid" json:"binlog_gtid"`
}
