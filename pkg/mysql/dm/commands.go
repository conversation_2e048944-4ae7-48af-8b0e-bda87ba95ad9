package dm

import (
	"context"
	"fmt"
	"time"

	"github.com/pingcap/tiflow/dm/config/security"

	"github.com/pingcap/tiflow/dm/ctl/common"
	"github.com/pingcap/tiflow/dm/pb"
)

const (
	defaultRPCTimeout = "10m"

	// Master specifies member master type.
	Master = "master"
	// Worker specifies member worker type.
	Worker = "worker"

	dialTimeout      = 3 * time.Second
	keepaliveTimeout = 3 * time.Second
	keepaliveTime    = 3 * time.Second

	// DefaultErrorCnt represents default count of errors to display for check-task.
	DefaultErrorCnt = 10
	// DefaultWarnCnt represents count of warns to display for check-task.
	DefaultWarnCnt = 10
)

type DMAdaptor struct {
	dtoBuilder     *DTOBuilder
	yamlDTOBuilder *YamlDTOBuilder
}

func NewDMAdaptor(masterAddr string) (*DMAdaptor, error) {
	initErr := common.InitClient(masterAddr, security.Security{})
	if initErr != nil {
		return nil, initErr
	}
	return &DMAdaptor{
		dtoBuilder:     NewDTOBuilder(),
		yamlDTOBuilder: NewYamlDTOBuilder(),
	}, nil
}

func (a DMAdaptor) ListMember(ctx context.Context) (ListMemberResponseDTO, error) {
	resp := &pb.ListMemberResponse{}
	err := common.SendRequest(ctx,
		"ListMember",
		&pb.ListMemberRequest{
			Leader: true,
			Master: true,
			Worker: true,
		},
		&resp,
	)
	return a.dtoBuilder.FromListMemberPB(resp), err
}

func (a DMAdaptor) CreateSource(ctx context.Context, configs []string, workerName string) (OperateSourceResponseDTO, error) {
	resp := &pb.OperateSourceResponse{}
	err := common.SendRequest(ctx,
		"OperateSource",
		&pb.OperateSourceRequest{
			Op:         pb.SourceOp_StartSource,
			Config:     configs,
			WorkerName: workerName,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSourcePB(resp), err
}

func (a DMAdaptor) StopSource(ctx context.Context, sourceIDs []string) (OperateSourceResponseDTO, error) {
	resp := &pb.OperateSourceResponse{}
	err := common.SendRequest(ctx,
		"OperateSource",
		&pb.OperateSourceRequest{
			Op:       pb.SourceOp_StopSource,
			SourceID: sourceIDs,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSourcePB(resp), err
}

func (a DMAdaptor) ShowSource(ctx context.Context, sourceIDs []string) (OperateSourceResponseDTO, error) {
	resp := &pb.OperateSourceResponse{}
	err := common.SendRequest(ctx,
		"OperateSource",
		&pb.OperateSourceRequest{
			Op:       pb.SourceOp_ShowSource,
			SourceID: sourceIDs,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSourcePB(resp), err
}

func (a DMAdaptor) StartTask(ctx context.Context, taskConfig string, sources []string, removeMeta bool, startTime string) (StartTaskResponseDTO, error) {
	resp := &pb.StartTaskResponse{}
	err := common.SendRequest(ctx,
		"StartTask",
		&pb.StartTaskRequest{
			Task:       taskConfig,
			Sources:    sources,
			RemoveMeta: removeMeta,
			StartTime:  startTime,
		},
		&resp,
	)
	return a.dtoBuilder.FromStartTaskPB(resp), err
}

func (a DMAdaptor) StopTask(ctx context.Context, taskName string, sources []string) (OperateTaskResponseDTO, error) {
	resp := &pb.OperateTaskResponse{}
	err := common.SendRequest(ctx,
		"OperateTask",
		&pb.OperateTaskRequest{
			Op:      pb.TaskOp_Stop,
			Name:    taskName,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateTaskPB(resp), err
}

func (a DMAdaptor) PauseTask(ctx context.Context, taskName string, sources []string) (OperateTaskResponseDTO, error) {
	resp := &pb.OperateTaskResponse{}
	err := common.SendRequest(ctx,
		"OperateTask",
		&pb.OperateTaskRequest{
			Op:      pb.TaskOp_Pause,
			Name:    taskName,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateTaskPB(resp), err
}

func (a DMAdaptor) ResumeTask(ctx context.Context, taskName string, sources []string) (OperateTaskResponseDTO, error) {
	resp := &pb.OperateTaskResponse{}
	err := common.SendRequest(ctx,
		"OperateTask",
		&pb.OperateTaskRequest{
			Op:      pb.TaskOp_Resume,
			Name:    taskName,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateTaskPB(resp), err
}

func (a DMAdaptor) UpdateTask(ctx context.Context, taskConfig string, sources []string) (UpdateTaskResponseDTO, error) {
	resp := &pb.UpdateTaskResponse{}
	err := common.SendRequest(ctx,
		"UpdateTask",
		&pb.UpdateTaskRequest{
			Task:    taskConfig,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromUpdateTaskPB(resp), err
}

func (a DMAdaptor) EvictLeader(ctx context.Context) (OperateLeaderResponseDTO, error) {
	resp := &pb.OperateLeaderResponse{}
	err := common.SendRequest(ctx,
		"OperateLeader",
		&pb.OperateLeaderRequest{
			Op: pb.LeaderOp_EvictLeaderOp,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateLeaderPB(resp), err
}

func (a DMAdaptor) CancelEvictLeader(ctx context.Context) (OperateLeaderResponseDTO, error) {
	resp := &pb.OperateLeaderResponse{}
	err := common.SendRequest(ctx,
		"OperateLeader",
		&pb.OperateLeaderRequest{
			Op: pb.LeaderOp_CancelEvictLeaderOp,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateLeaderPB(resp), err
}

func (a DMAdaptor) StartRelay(ctx context.Context, source string, workers []string) (OperateRelayResponseDTO, error) {
	resp := &pb.OperateRelayResponse{}
	err := common.SendRequest(ctx,
		"OperateRelay",
		&pb.OperateRelayRequest{
			Op:     pb.RelayOpV2_StartRelayV2,
			Source: source,
			Worker: workers,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateRelayPB(resp), err
}

func (a DMAdaptor) StopRelay(ctx context.Context, source string, workers []string) (OperateRelayResponseDTO, error) {
	resp := &pb.OperateRelayResponse{}
	err := common.SendRequest(ctx,
		"OperateRelay",
		&pb.OperateRelayRequest{
			Op:     pb.RelayOpV2_StopRelayV2,
			Source: source,
			Worker: workers,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateRelayPB(resp), err
}

func (a DMAdaptor) PauseRelay(ctx context.Context, sources []string) (OperateWorkerRelayResponseDTO, error) {
	resp := &pb.OperateWorkerRelayResponse{}
	err := common.SendRequest(ctx,
		"OperateWorkerRelay",
		&pb.OperateWorkerRelayRequest{
			Op:      pb.RelayOp_PauseRelay,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateWorkerRelayPB(resp), err
}

func (a DMAdaptor) ResumeRelay(ctx context.Context, sources []string) (OperateWorkerRelayResponseDTO, error) {
	resp := &pb.OperateWorkerRelayResponse{}
	err := common.SendRequest(ctx,
		"OperateWorkerRelay",
		&pb.OperateWorkerRelayRequest{
			Op:      pb.RelayOp_ResumeRelay,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateWorkerRelayPB(resp), err
}

func (a DMAdaptor) PurgeRelay(ctx context.Context, sources []string, inactive bool, time int64, filename string, subDir string) (PurgeWorkerRelayResponseDTO, error) {
	resp := &pb.PurgeWorkerRelayResponse{}
	err := common.SendRequest(ctx,
		"PurgeWorkerRelay",
		&pb.PurgeWorkerRelayRequest{
			Sources:  sources,
			Inactive: inactive,
			Time:     time,
			Filename: filename,
			SubDir:   subDir,
		},
		&resp,
	)
	return a.dtoBuilder.FromPurgeWorkerRelayPB(resp), err
}

func (a DMAdaptor) StopWorkerRelay(ctx context.Context, sources []string) (OperateWorkerRelayResponseDTO, error) {
	resp := &pb.OperateWorkerRelayResponse{}
	err := common.SendRequest(ctx,
		"OperateWorkerRelay",
		&pb.OperateWorkerRelayRequest{
			Op:      pb.RelayOp_StopRelay,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateWorkerRelayPB(resp), err
}

func (a DMAdaptor) GetSchema(ctx context.Context, taskName string, sources []string, database string, table string) (OperateSchemaResponseDTO, error) {
	resp := &pb.OperateSchemaResponse{}
	err := common.SendRequest(ctx,
		"OperateSchema",
		&pb.OperateSchemaRequest{
			Op:       pb.SchemaOp_GetSchema,
			Task:     taskName,
			Sources:  sources,
			Database: database,
			Table:    table,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSchemaPB(resp), err
}

func (a DMAdaptor) SetSchema(ctx context.Context, taskName string, sources []string, database string, table string, schema string, flush bool, sync bool, fromSource bool, fromTarget bool) (OperateSchemaResponseDTO, error) {
	resp := &pb.OperateSchemaResponse{}
	err := common.SendRequest(ctx,
		"OperateSchema",
		&pb.OperateSchemaRequest{
			Op:         pb.SchemaOp_SetSchema,
			Task:       taskName,
			Sources:    sources,
			Database:   database,
			Table:      table,
			Schema:     schema,
			Flush:      flush,
			Sync:       sync,
			FromSource: fromSource,
			FromTarget: fromTarget,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSchemaPB(resp), err
}

func (a DMAdaptor) RemoveSchema(ctx context.Context, taskName string, sources []string, database string, table string) (OperateSchemaResponseDTO, error) {
	resp := &pb.OperateSchemaResponse{}
	err := common.SendRequest(ctx,
		"OperateSchema",
		&pb.OperateSchemaRequest{
			Op:       pb.SchemaOp_RemoveSchema,
			Task:     taskName,
			Sources:  sources,
			Database: database,
			Table:    table,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSchemaPB(resp), err
}

func (a DMAdaptor) ListSchema(ctx context.Context, taskName string, sources []string, database string) (OperateSchemaResponseDTO, error) {
	resp := &pb.OperateSchemaResponse{}
	err := common.SendRequest(ctx,
		"OperateSchema",
		&pb.OperateSchemaRequest{
			Op:       pb.SchemaOp_ListSchema,
			Task:     taskName,
			Sources:  sources,
			Database: database,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSchemaPB(resp), err
}

func (a DMAdaptor) ListTable(ctx context.Context, taskName string, sources []string, database string) (OperateSchemaResponseDTO, error) {
	resp := &pb.OperateSchemaResponse{}
	err := common.SendRequest(ctx,
		"OperateSchema",
		&pb.OperateSchemaRequest{
			Op:       pb.SchemaOp_ListTable,
			Task:     taskName,
			Sources:  sources,
			Database: database,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSchemaPB(resp), err
}

func (a DMAdaptor) ListMigrateTargets(ctx context.Context, taskName string, sources []string) (OperateSchemaResponseDTO, error) {
	resp := &pb.OperateSchemaResponse{}
	err := common.SendRequest(ctx,
		"OperateSchema",
		&pb.OperateSchemaRequest{
			Op:      pb.SchemaOp_ListMigrateTargets,
			Task:    taskName,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateSchemaPB(resp), err
}

func (a DMAdaptor) OfflineMember(ctx context.Context, memberType string, memberName string) (OfflineMemberResponseDTO, error) {
	resp := &pb.OfflineMemberResponse{}
	err := common.SendRequest(ctx,
		"OfflineMember",
		&pb.OfflineMemberRequest{
			Type: memberType,
			Name: memberName,
		},
		&resp,
	)
	return a.dtoBuilder.FromOfflineMemberPB(resp), err
}

func (a DMAdaptor) QueryStatus(ctx context.Context, taskName string, sources []string) (QueryStatusResponseDTO, error) {
	resp := &pb.QueryStatusListResponse{}
	err := common.SendRequest(ctx,
		"QueryStatus",
		&pb.QueryStatusListRequest{
			Name:    taskName,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromQueryStatusPB(resp), err
}

// QueryStatusWithMore 获取任务状态信息（支持详细模式）
func (a DMAdaptor) QueryStatusWithMore(ctx context.Context, taskName string, sources []string, showMore bool) (QueryStatusResponseDTO, error) {
	resp := &pb.QueryStatusListResponse{}
	err := common.SendRequest(ctx,
		"QueryStatus",
		&pb.QueryStatusListRequest{
			Name:    taskName,
			Sources: sources,
		},
		&resp,
	)

	result := a.dtoBuilder.FromQueryStatusPB(resp)

	// If showMore is false, simplify the response by removing detailed status info
	if !showMore {
		result = a.dtoBuilder.simplifyQueryStatusResponse(result)
	}

	return result, err
}

// QueryAllTasksStatus 获取所有任务的状态汇总
func (a DMAdaptor) QueryAllTasksStatus(ctx context.Context) (TaskSummaryResponseDTO, error) {
	// Query status for all tasks by not specifying task name
	resp := &pb.QueryStatusListResponse{}
	err := common.SendRequest(ctx,
		"QueryStatus",
		&pb.QueryStatusListRequest{
			Name:    "", // Empty name gets status for all tasks
			Sources: nil,
		},
		&resp,
	)

	if err != nil {
		return TaskSummaryResponseDTO{
			Result: false,
			Msg:    fmt.Sprintf("Failed to query all tasks status: %v", err),
			Tasks:  nil,
		}, err
	}

	return a.dtoBuilder.FromQueryStatusToTaskSummary(resp), nil
}

// QueryTaskStatusSummary 获取指定任务的状态汇总
func (a DMAdaptor) QueryTaskStatusSummary(ctx context.Context, taskName string) (TaskSummaryDTO, error) {
	resp := &pb.QueryStatusListResponse{}
	err := common.SendRequest(ctx,
		"QueryStatus",
		&pb.QueryStatusListRequest{
			Name:    taskName,
			Sources: nil,
		},
		&resp,
	)

	if err != nil {
		return TaskSummaryDTO{
			TaskName:   taskName,
			TaskStatus: "Unknown",
			Sources:    nil,
			ErrorMsg:   fmt.Sprintf("Failed to query task status: %v", err),
		}, err
	}

	return a.dtoBuilder.FromQueryStatusToSingleTaskSummary(resp, taskName), nil
}

// QueryValidationStatus 获取数据校验状态信息
func (a DMAdaptor) QueryValidationStatus(ctx context.Context, taskName string, filterStage string) (QueryValidationStatusResponseDTO, error) {
	// Use query-status to get validation information
	resp := &pb.QueryStatusListResponse{}
	err := common.SendRequest(ctx,
		"QueryStatus",
		&pb.QueryStatusListRequest{
			Name:    taskName,
			Sources: nil,
		},
		&resp,
	)

	if err != nil {
		return QueryValidationStatusResponseDTO{
			Result: false,
			Msg:    fmt.Sprintf("Failed to query validation status: %v", err),
			Status: nil,
		}, err
	}

	return a.dtoBuilder.FromQueryStatusToValidationStatus(resp, filterStage), nil
}

// QueryValidationError 获取数据校验错误信息
func (a DMAdaptor) QueryValidationError(ctx context.Context, taskName string, errorState string) (QueryValidationErrorResponseDTO, error) {
	// Use query-status to get validation error information
	resp := &pb.QueryStatusListResponse{}
	err := common.SendRequest(ctx,
		"QueryStatus",
		&pb.QueryStatusListRequest{
			Name:    taskName,
			Sources: nil,
		},
		&resp,
	)

	if err != nil {
		return QueryValidationErrorResponseDTO{
			Result: false,
			Msg:    fmt.Sprintf("Failed to query validation errors: %v", err),
			Errors: nil,
		}, err
	}

	return a.dtoBuilder.FromQueryStatusToValidationErrors(resp, errorState), nil
}

// SkipError 跳过错误事件
func (a DMAdaptor) SkipError(ctx context.Context, taskName string, sources []string, binlogPos string) (HandleErrorResponseDTO, error) {
	return a.handleError(ctx, taskName, sources, "skip", binlogPos, nil)
}

// ReplaceError 替换错误事件
func (a DMAdaptor) ReplaceError(ctx context.Context, taskName string, sources []string, binlogPos string, sqls []string) (HandleErrorResponseDTO, error) {
	return a.handleError(ctx, taskName, sources, "replace", binlogPos, sqls)
}

// RevertError 回滚错误事件
func (a DMAdaptor) RevertError(ctx context.Context, taskName string, sources []string, binlogPos string) (HandleErrorResponseDTO, error) {
	return a.handleError(ctx, taskName, sources, "revert", binlogPos, nil)
}

// InjectError 注入错误事件
func (a DMAdaptor) InjectError(ctx context.Context, taskName string, sources []string, binlogPos string, sqls []string) (HandleErrorResponseDTO, error) {
	return a.handleError(ctx, taskName, sources, "inject", binlogPos, sqls)
}

// HandleError 处理错误事件（跳过/替换/回滚/注入）
func (a DMAdaptor) handleError(ctx context.Context, taskName string, sources []string, operation string, binlogPos string, sqls []string) (HandleErrorResponseDTO, error) {
	resp := &pb.HandleErrorResponse{}

	// Convert string operation to pb.ErrorOp
	var op pb.ErrorOp
	switch operation {
	case "skip":
		op = pb.ErrorOp_Skip
	case "replace":
		op = pb.ErrorOp_Replace
	case "revert":
		op = pb.ErrorOp_Revert
	case "inject":
		op = pb.ErrorOp_Inject
	default:
		return HandleErrorResponseDTO{
			Result:  false,
			Msg:     fmt.Sprintf("Invalid operation: %s. Valid operations: skip, replace, revert, inject", operation),
			Sources: nil,
		}, fmt.Errorf("invalid operation: %s", operation)
	}

	err := common.SendRequest(ctx,
		"HandleError",
		&pb.HandleErrorRequest{
			Op:        op,
			Task:      taskName,
			Sources:   sources,
			BinlogPos: binlogPos,
			Sqls:      sqls,
		},
		&resp,
	)

	return a.dtoBuilder.FromHandleErrorPB(resp), err
}

// ListBinlogEvents 列出指定binlog位置的错误处理命令
func (a DMAdaptor) ListBinlogEvents(ctx context.Context, taskName string, binlogPos string) (HandleErrorResponseDTO, error) {
	resp := &pb.HandleErrorResponse{}
	err := common.SendRequest(ctx,
		"HandleError",
		&pb.HandleErrorRequest{
			Op:        pb.ErrorOp_List,
			Task:      taskName,
			BinlogPos: binlogPos,
		},
		&resp,
	)

	return a.dtoBuilder.FromHandleErrorPB(resp), err
}

func (a DMAdaptor) CheckTask(ctx context.Context, taskConfig string, errCnt int64, warnCnt int64, startTime string) (CheckTaskResponseDTO, error) {
	resp := &pb.CheckTaskResponse{}
	err := common.SendRequest(ctx,
		"CheckTask",
		&pb.CheckTaskRequest{
			Task:      taskConfig,
			ErrCnt:    errCnt,
			WarnCnt:   warnCnt,
			StartTime: startTime,
		},
		&resp,
	)
	return a.dtoBuilder.FromCheckTaskPB(resp), err
}

// GetSourceConfig 获取数据源配置信息
func (a DMAdaptor) GetSourceConfig(ctx context.Context, name string) (*SourceConfigDTO, error) {
	// 获取原始配置响应
	configResp, err := a.getConfig(ctx, "source", name)
	if err != nil {
		return nil, fmt.Errorf("failed to get source config: %w", err)
	}

	// 检查响应是否为空
	if configResp.Config == "" {
		return nil, fmt.Errorf("source config is empty for name: %s", name)
	}

	// 使用 yaml_dto_builder 将配置文本转换为 SourceConfigDTO
	sourceConfig, err := a.yamlDTOBuilder.BuildSourceConfigFromYAML(configResp.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse source config YAML: %w", err)
	}

	// 验证配置的有效性
	if err := a.yamlDTOBuilder.ValidateSourceConfig(sourceConfig); err != nil {
		return nil, fmt.Errorf("invalid source config: %w", err)
	}

	return sourceConfig, nil
}

// GetWorkerConfig 获取工作节点配置信息
func (a DMAdaptor) GetWorkerConfig(ctx context.Context, name string) (*WorkerConfigDTO, error) {
	// 获取原始配置响应
	configResp, err := a.getConfig(ctx, "worker", name)
	if err != nil {
		return nil, fmt.Errorf("failed to get worker config: %w", err)
	}

	// 检查响应是否为空
	if configResp.Config == "" {
		return nil, fmt.Errorf("worker config is empty for name: %s", name)
	}

	// 使用 yaml_dto_builder 将配置文本转换为 WorkerConfigDTO
	workerConfig, err := a.yamlDTOBuilder.BuildWorkerConfigFromYAML(configResp.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse worker config YAML: %w", err)
	}

	// 验证配置的有效性
	if err := a.yamlDTOBuilder.ValidateWorkerConfig(workerConfig); err != nil {
		return nil, fmt.Errorf("invalid worker config: %w", err)
	}

	return workerConfig, nil
}

// GetMasterConfig 获取主节点配置信息
func (a DMAdaptor) GetMasterConfig(ctx context.Context, name string) (*MasterConfigDTO, error) {
	// 获取原始配置响应
	configResp, err := a.getConfig(ctx, "master", name)
	if err != nil {
		return nil, fmt.Errorf("failed to get master config: %w", err)
	}

	// 检查响应是否为空
	if configResp.Config == "" {
		return nil, fmt.Errorf("master config is empty for name: %s", name)
	}

	// 使用 yaml_dto_builder 将配置文本转换为 MasterConfigDTO
	masterConfig, err := a.yamlDTOBuilder.BuildMasterConfigFromTOML(configResp.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse master config TOML: %w", err)
	}

	// 验证配置的有效性
	if err := a.yamlDTOBuilder.ValidateMasterConfig(masterConfig); err != nil {
		return nil, fmt.Errorf("invalid master config: %w", err)
	}

	return masterConfig, nil
}

// GetTaskConfig 获取任务配置信息
func (a DMAdaptor) GetTaskConfig(ctx context.Context, name string) (*TaskConfigDTO, error) {
	// 获取原始配置响应
	configResp, err := a.getConfig(ctx, "task", name)
	if err != nil {
		return nil, fmt.Errorf("failed to get task config: %w", err)
	}

	// 检查响应是否为空
	if configResp.Config == "" {
		return nil, fmt.Errorf("task config is empty for name: %s", name)
	}

	// 使用 yaml_dto_builder 将配置文本转换为 TaskConfigDTO
	taskConfig, err := a.yamlDTOBuilder.BuildTaskConfigFromYAML(configResp.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse task config YAML: %w", err)
	}

	// 验证配置的有效性
	if err := a.yamlDTOBuilder.ValidateTaskConfig(taskConfig); err != nil {
		return nil, fmt.Errorf("invalid task config: %w", err)
	}

	return taskConfig, nil
}

func (a DMAdaptor) getConfig(ctx context.Context, cfgType string, name string) (GetConfigResponseDTO, error) {
	resp := &pb.GetCfgResponse{}

	// Convert string type to pb.CfgType
	var pbCfgType pb.CfgType
	switch cfgType {
	case "task":
		pbCfgType = pb.CfgType_TaskType
	case "master":
		pbCfgType = pb.CfgType_MasterType
	case "worker":
		pbCfgType = pb.CfgType_WorkerType
	case "source":
		pbCfgType = pb.CfgType_SourceType
	default:
		pbCfgType = pb.CfgType_InvalidType
	}

	err := common.SendRequest(ctx,
		"GetCfg",
		&pb.GetCfgRequest{
			Type: pbCfgType,
			Name: name,
		},
		&resp,
	)
	return a.dtoBuilder.FromGetConfigPB(resp), err
}

func (a DMAdaptor) ShowDDLLocks(ctx context.Context, taskName string, sources []string) (ShowDDLLocksResponseDTO, error) {
	resp := &pb.ShowDDLLocksResponse{}
	err := common.SendRequest(ctx,
		"ShowDDLLocks",
		&pb.ShowDDLLocksRequest{
			Task:    taskName,
			Sources: sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromShowDDLLocksPB(resp), err
}

func (a DMAdaptor) UnlockDDLLock(ctx context.Context, lockID string, replaceOwner string, forceRemove bool, sources []string, database string, table string, action string) (UnlockDDLLockResponseDTO, error) {
	resp := &pb.UnlockDDLLockResponse{}

	// Convert string action to pb.UnlockDDLLockOp
	var op pb.UnlockDDLLockOp
	switch action {
	case "exec":
		op = pb.UnlockDDLLockOp_ExecLock
	case "skip":
		op = pb.UnlockDDLLockOp_SkipLock
	default:
		op = pb.UnlockDDLLockOp_SkipLock // default to skip
	}

	err := common.SendRequest(ctx,
		"UnlockDDLLock",
		&pb.UnlockDDLLockRequest{
			ID:           lockID,
			ReplaceOwner: replaceOwner,
			ForceRemove:  forceRemove,
			Sources:      sources,
			Database:     database,
			Table:        table,
			Op:           op,
		},
		&resp,
	)
	return a.dtoBuilder.FromUnlockDDLLockPB(resp), err
}

func (a DMAdaptor) TransferSource(ctx context.Context, sourceID string, workerID string) (TransferSourceResponseDTO, error) {
	resp := &pb.TransferSourceResponse{}
	err := common.SendRequest(ctx,
		"TransferSource",
		&pb.TransferSourceRequest{
			Source: sourceID,
			Worker: workerID,
		},
		&resp,
	)
	return a.dtoBuilder.FromTransferSourcePB(resp), err
}

// StartValidation starts validation with optional mode and start time parameters
func (a DMAdaptor) StartValidation(ctx context.Context, taskName string, sources []string, mode string, startTime string) (StartValidationResponseDTO, error) {
	req := &pb.StartValidationRequest{
		TaskName: taskName,
		Sources:  sources,
	}

	// Add mode if specified
	if mode != "" {
		req.Mode = &pb.StartValidationRequest_ModeValue{ModeValue: mode}
	}

	// Add start time if specified
	if startTime != "" {
		req.StartTime = &pb.StartValidationRequest_StartTimeValue{StartTimeValue: startTime}
	}

	resp := &pb.StartValidationResponse{}
	err := common.SendRequest(ctx, "StartValidation", req, &resp)
	return a.dtoBuilder.FromStartValidationPB(resp), err
}

// StopValidation stops validation for specified tasks
func (a DMAdaptor) StopValidation(ctx context.Context, taskName string, sources []string) (StopValidationResponseDTO, error) {
	resp := &pb.StopValidationResponse{}
	err := common.SendRequest(ctx,
		"StopValidation",
		&pb.StopValidationRequest{
			TaskName: taskName,
			Sources:  sources,
		},
		&resp,
	)
	return a.dtoBuilder.FromStopValidationPB(resp), err
}

// UpdateValidation updates validation configuration with binlog position or GTID
func (a DMAdaptor) UpdateValidation(ctx context.Context, taskName string, sources []string, binlogPos string, binlogGTID string) (UpdateValidationResponseDTO, error) {
	resp := &pb.UpdateValidationResponse{}
	err := common.SendRequest(ctx,
		"UpdateValidation",
		&pb.UpdateValidationRequest{
			TaskName:   taskName,
			Sources:    sources,
			BinlogPos:  binlogPos,
			BinlogGTID: binlogGTID,
		},
		&resp,
	)
	return a.dtoBuilder.FromUpdateValidationPB(resp), err
}

// operateValidationError is the base method for validation error operations
func (a DMAdaptor) operateValidationError(ctx context.Context, operation string, taskName string, errorID uint64, isAllError bool) (OperateValidationErrorResponseDTO, error) {
	// Convert string operation to pb.ValidationErrOp
	var op pb.ValidationErrOp
	switch operation {
	case "ignore":
		op = pb.ValidationErrOp_IgnoreErrOp
	case "resolve":
		op = pb.ValidationErrOp_ResolveErrOp
	case "clear":
		op = pb.ValidationErrOp_ClearErrOp
	default:
		return OperateValidationErrorResponseDTO{
			Result:  false,
			Msg:     fmt.Sprintf("Invalid operation: %s. Valid operations: ignore, resolve, clear", operation),
			Sources: nil,
		}, fmt.Errorf("invalid operation: %s", operation)
	}

	resp := &pb.OperateValidationErrorResponse{}
	err := common.SendRequest(ctx,
		"OperateValidationError",
		&pb.OperateValidationErrorRequest{
			Op:         op,
			TaskName:   taskName,
			ErrId:      errorID,
			IsAllError: isAllError,
		},
		&resp,
	)
	return a.dtoBuilder.FromOperateValidationErrorPB(resp), err
}

// IgnoreValidationError ignores validation errors by ID or all errors
func (a DMAdaptor) IgnoreValidationError(ctx context.Context, taskName string, errorID uint64, isAllError bool) (OperateValidationErrorResponseDTO, error) {
	return a.operateValidationError(ctx, "ignore", taskName, errorID, isAllError)
}

// ResolveValidationError resolves validation errors by ID or all errors
func (a DMAdaptor) ResolveValidationError(ctx context.Context, taskName string, errorID uint64, isAllError bool) (OperateValidationErrorResponseDTO, error) {
	return a.operateValidationError(ctx, "resolve", taskName, errorID, isAllError)
}

// ClearValidationError clears validation errors by ID or all errors
func (a DMAdaptor) ClearValidationError(ctx context.Context, taskName string, errorID uint64, isAllError bool) (OperateValidationErrorResponseDTO, error) {
	return a.operateValidationError(ctx, "clear", taskName, errorID, isAllError)
}

// GetValidationError retrieves validation errors with optional state filtering
func (a DMAdaptor) GetValidationError(ctx context.Context, taskName string, errorState string) (GetValidationErrorResponseDTO, error) {
	// Convert string error state to pb.ValidateErrorState
	var pbErrState pb.ValidateErrorState
	switch errorState {
	case "all":
		pbErrState = pb.ValidateErrorState_InvalidErr
	case "ignored":
		pbErrState = pb.ValidateErrorState_IgnoredErr
	case "unprocessed":
		pbErrState = pb.ValidateErrorState_NewErr
	case "resolved":
		pbErrState = pb.ValidateErrorState_ResolvedErr
	default:
		pbErrState = pb.ValidateErrorState_NewErr // default to unprocessed
	}

	resp := &pb.GetValidationErrorResponse{}
	err := common.SendRequest(ctx,
		"GetValidationError",
		&pb.GetValidationErrorRequest{
			ErrState: pbErrState,
			TaskName: taskName,
		},
		&resp,
	)
	return a.dtoBuilder.FromGetValidationErrorPB(resp), err
}

// GetValidationStatus retrieves validation status with optional stage filtering
func (a DMAdaptor) GetValidationStatus(ctx context.Context, taskName string, filterStage string) (GetValidationStatusResponseDTO, error) {
	// Convert string stage to pb.Stage
	var pbStage pb.Stage
	switch filterStage {
	case "running":
		pbStage = pb.Stage_Running
	case "stopped":
		pbStage = pb.Stage_Stopped
	case "":
		pbStage = pb.Stage_InvalidStage // use invalid stage to represent 'all' stages
	default:
		pbStage = pb.Stage_InvalidStage
	}

	resp := &pb.GetValidationStatusResponse{}
	err := common.SendRequest(ctx,
		"GetValidationStatus",
		&pb.GetValidationStatusRequest{
			TaskName:     taskName,
			FilterStatus: pbStage,
		},
		&resp,
	)
	return a.dtoBuilder.FromGetValidationStatusPB(resp), err
}
