package dm

import (
	"fmt"
	"os"

	"github.com/BurntSushi/toml"
	"gopkg.in/yaml.v3"
)

type YamlDTOBuilder struct{}

func NewYamlDTOBuilder() *YamlDTOBuilder {
	return &YamlDTOBuilder{}
}

// BuildWorkerConfigFromYAML 从 YAML/TOML 字符串构建 WorkerConfigDTO
func (i *YamlDTOBuilder) BuildWorkerConfigFromYAML(yamlContent string) (*WorkerConfigDTO, error) {
	var config WorkerConfigDTO

	// 先尝试 TOML 解析（DM 配置通常为 TOML 格式）
	err := toml.Unmarshal([]byte(yamlContent), &config)
	if err == nil {
		return &config, nil
	}

	// 如果 TOML 解析失败，再尝试 YAML 解析（向后兼容）
	err = yaml.Unmarshal([]byte(yamlContent), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal worker config as TOML or YAML: %w", err)
	}

	return &config, nil
}

// BuildWorkerConfigFromYAMLFile 从 YAML 文件构建 WorkerConfigDTO
func (i *YamlDTOBuilder) BuildWorkerConfigFromYAMLFile(filePath string) (*WorkerConfigDTO, error) {
	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read worker config YAML file: %w", err)
	}

	// 解析 YAML 内容
	return i.BuildWorkerConfigFromYAML(string(content))
}

// BuildWorkerConfigFromYAMLBytes 从 YAML 字节数组构建 WorkerConfigDTO
func (i *YamlDTOBuilder) BuildWorkerConfigFromYAMLBytes(yamlBytes []byte) (*WorkerConfigDTO, error) {
	var config WorkerConfigDTO

	// 解析 YAML 字节数组到结构体
	err := yaml.Unmarshal(yamlBytes, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal worker config YAML bytes: %w", err)
	}

	return &config, nil
}

// ValidateWorkerConfig 验证 WorkerConfigDTO 的必填字段
func (i *YamlDTOBuilder) ValidateWorkerConfig(config *WorkerConfigDTO) error {
	if config == nil {
		return fmt.Errorf("worker config cannot be nil")
	}

	// 验证工作节点名称必填
	if config.Name == "" {
		return fmt.Errorf("worker name is required")
	}

	// 验证工作节点地址必填
	if config.WorkerAddr == "" {
		return fmt.Errorf("worker-addr is required")
	}

	// 验证广播地址必填
	if config.AdvertiseAddr == "" {
		return fmt.Errorf("advertise-addr is required")
	}

	// 验证加入集群地址必填
	if config.Join == "" {
		return fmt.Errorf("join address is required")
	}

	return nil
}

// BuildSourceConfigFromYAML 从 YAML/TOML 字符串构建 SourceConfigDTO
func (i *YamlDTOBuilder) BuildSourceConfigFromYAML(yamlContent string) (*SourceConfigDTO, error) {
	var config SourceConfigDTO

	// 先尝试 TOML 解析（DM 配置通常为 TOML 格式）
	err := toml.Unmarshal([]byte(yamlContent), &config)
	if err == nil {
		return &config, nil
	}

	// 如果 TOML 解析失败，再尝试 YAML 解析（向后兼容）
	err = yaml.Unmarshal([]byte(yamlContent), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal source config as TOML or YAML: %w", err)
	}

	return &config, nil
}

// BuildSourceConfigFromYAMLFile 从 YAML 文件构建 SourceConfigDTO
func (i *YamlDTOBuilder) BuildSourceConfigFromYAMLFile(filePath string) (*SourceConfigDTO, error) {
	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read YAML file: %w", err)
	}

	// 解析 YAML 内容
	return i.BuildSourceConfigFromYAML(string(content))
}

// BuildSourceConfigFromYAMLBytes 从 YAML 字节数组构建 SourceConfigDTO
func (i *YamlDTOBuilder) BuildSourceConfigFromYAMLBytes(yamlBytes []byte) (*SourceConfigDTO, error) {
	var config SourceConfigDTO

	// 解析 YAML 字节数组到结构体
	err := yaml.Unmarshal(yamlBytes, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal YAML bytes: %w", err)
	}

	return &config, nil
}

// ValidateSourceConfig 验证 SourceConfigDTO 的必填字段
func (i *YamlDTOBuilder) ValidateSourceConfig(config *SourceConfigDTO) error {
	if config == nil {
		return fmt.Errorf("source config cannot be nil")
	}

	// 验证 source-id 必填
	if config.SourceID == "" {
		return fmt.Errorf("source-id is required")
	}

	// 验证数据库连接配置
	if err := i.validateDatabaseConfig(&config.From); err != nil {
		return fmt.Errorf("invalid database config: %w", err)
	}

	return nil
}

// validateDatabaseConfig 验证数据库配置的必填字段
func (i *YamlDTOBuilder) validateDatabaseConfig(dbConfig *DatabaseConfig) error {
	if dbConfig == nil {
		return fmt.Errorf("database config cannot be nil")
	}

	if dbConfig.Host == "" {
		return fmt.Errorf("database host is required")
	}

	if dbConfig.Port <= 0 {
		return fmt.Errorf("database port must be greater than 0")
	}

	if dbConfig.User == "" {
		return fmt.Errorf("database user is required")
	}

	if dbConfig.Password == "" {
		return fmt.Errorf("database password is required")
	}

	return nil
}

// BuildMasterConfigFromTOML 从 TOML 字符串构建 MasterConfigDTO
func (i *YamlDTOBuilder) BuildMasterConfigFromTOML(tomlContent string) (*MasterConfigDTO, error) {
	var config MasterConfigDTO

	// 解析 TOML 内容到结构体（Master 配置通常为 TOML 格式）
	err := toml.Unmarshal([]byte(tomlContent), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal master config TOML: %w", err)
	}

	return &config, nil
}

// ValidateMasterConfig 验证 MasterConfigDTO 的必填字段
func (i *YamlDTOBuilder) ValidateMasterConfig(config *MasterConfigDTO) error {
	if config == nil {
		return fmt.Errorf("master config cannot be nil")
	}

	// 验证节点名称必填
	if config.Name == "" {
		return fmt.Errorf("master name is required")
	}

	// 验证主节点地址必填
	if config.MasterAddr == "" {
		return fmt.Errorf("master-addr is required")
	}

	// 验证广播地址必填
	if config.AdvertiseAddr == "" {
		return fmt.Errorf("advertise-addr is required")
	}

	return nil
}

// BuildTaskConfigFromYAML 从 YAML 字符串构建 TaskConfigDTO
func (i *YamlDTOBuilder) BuildTaskConfigFromYAML(yamlContent string) (*TaskConfigDTO, error) {
	var config TaskConfigDTO

	// 解析 YAML 内容到结构体（Task 配置通常为 YAML 格式）
	err := yaml.Unmarshal([]byte(yamlContent), &config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal task config YAML: %w", err)
	}

	return &config, nil
}

// ValidateTaskConfig 验证 TaskConfigDTO 的必填字段
func (i *YamlDTOBuilder) ValidateTaskConfig(config *TaskConfigDTO) error {
	if config == nil {
		return fmt.Errorf("task config cannot be nil")
	}

	// 验证任务名称必填
	if config.Name == "" {
		return fmt.Errorf("task name is required")
	}

	// 验证任务模式必填
	if config.TaskMode == "" {
		return fmt.Errorf("task-mode is required")
	}

	// 验证目标数据库配置
	if config.TargetDatabase == nil {
		return fmt.Errorf("target-database is required")
	}

	// 验证目标数据库基本配置
	if config.TargetDatabase.Host == "" {
		return fmt.Errorf("target database host is required")
	}

	if config.TargetDatabase.Port <= 0 {
		return fmt.Errorf("target database port must be greater than 0")
	}

	if config.TargetDatabase.User == "" {
		return fmt.Errorf("target database user is required")
	}

	return nil
}

// ToYAML 将 SourceConfigDTO 转换为 YAML 字符串
func (i *YamlDTOBuilder) ToYAML(config *SourceConfigDTO) (string, error) {
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		return "", fmt.Errorf("failed to marshal to YAML: %w", err)
	}

	return string(yamlBytes), nil
}

// ToYAMLFile 将 SourceConfigDTO 保存为 YAML 文件
func (i *YamlDTOBuilder) ToYAMLFile(config *SourceConfigDTO, filePath string) error {
	yamlBytes, err := yaml.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal to YAML: %w", err)
	}

	err = os.WriteFile(filePath, yamlBytes, 0644)
	if err != nil {
		return fmt.Errorf("failed to write YAML file: %w", err)
	}

	return nil
}
