package dm

import "github.com/pingcap/tiflow/dm/pb"

type DTOBuilder struct{}

func NewDTOBuilder() *DTOBuilder {
	return &DTOBuilder{}
}

// FromListMemberPB 从 pb.ListMemberResponse 创建一个新的 ListMemberResponseDTO
func (i *DTOBuilder) FromListMemberPB(pbResp *pb.ListMemberResponse) ListMemberResponseDTO {
	r := ListMemberResponseDTO{}

	// 遍历 pbResp.Members，分别处理 leader、master、worker 信息
	for _, member := range pbResp.Members {
		switch m := member.Member.(type) {
		case *pb.Members_Leader:
			// 处理 leader 信息
			if m.Leader != nil {
				r.Leader = &LeaderInfo{
					Name:    m.Leader.Name,
					Addr:    m.Leader.Addr,
					Message: m.Leader.Msg,
				}
			}
		case *pb.Members_Master:
			// 处理 masters 信息
			if m.Master != nil {
				for _, master := range m.Master.Masters {
					// memberID 需要转为 uint64，JSON 序列化时会自动转为字符串
					r.Masters = append(r.Masters, &MasterInfo{
						Name:       master.Name,
						MemberID:   master.MemberID,
						Alive:      master.Alive,
						PeerURLs:   append([]string{}, master.PeerURLs...),   // 拷贝 slice，防止引用问题
						ClientURLs: append([]string{}, master.ClientURLs...), // 拷贝 slice，防止引用问题
					})
				}
			}
		case *pb.Members_Worker:
			// 处理 workers 信息
			if m.Worker != nil {
				for _, worker := range m.Worker.Workers {
					r.Workers = append(r.Workers, &WorkerInfo{
						Name:   worker.Name,
						Addr:   worker.Addr,
						Stage:  worker.Stage,
						Source: worker.Source,
					})
				}
			}
		}
	}
	return r
}

// FromOperateSourcePB creates a new OperateSourceResponseDTO from a pb.OperateSourceResponse.
func (i *DTOBuilder) FromOperateSourcePB(pbResp *pb.OperateSourceResponse) OperateSourceResponseDTO {
	r := OperateSourceResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromStartTaskPB creates a new StartTaskResponseDTO from a pb.StartTaskResponse.
func (i *DTOBuilder) FromStartTaskPB(pbResp *pb.StartTaskResponse) StartTaskResponseDTO {
	r := StartTaskResponseDTO{
		Result:      pbResp.Result,
		Msg:         pbResp.Msg,
		CheckResult: pbResp.CheckResult,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromOperateTaskPB creates a new OperateTaskResponseDTO from a pb.OperateTaskResponse.
func (i *DTOBuilder) FromOperateTaskPB(pbResp *pb.OperateTaskResponse) OperateTaskResponseDTO {
	r := OperateTaskResponseDTO{
		Op:     pbResp.Op.String(),
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromUpdateTaskPB creates a new UpdateTaskResponseDTO from a pb.UpdateTaskResponse.
func (i *DTOBuilder) FromUpdateTaskPB(pbResp *pb.UpdateTaskResponse) UpdateTaskResponseDTO {
	r := UpdateTaskResponseDTO{
		Result:      pbResp.Result,
		Msg:         pbResp.Msg,
		CheckResult: pbResp.CheckResult,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromOperateLeaderPB creates a new OperateLeaderResponseDTO from a pb.OperateLeaderResponse.
func (i *DTOBuilder) FromOperateLeaderPB(pbResp *pb.OperateLeaderResponse) OperateLeaderResponseDTO {
	return OperateLeaderResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}
}

// FromOperateWorkerRelayPB creates a new OperateWorkerRelayResponseDTO from a pb.OperateWorkerRelayResponse.
func (i *DTOBuilder) FromOperateWorkerRelayPB(pbResp *pb.OperateWorkerRelayResponse) OperateWorkerRelayResponseDTO {
	r := OperateWorkerRelayResponseDTO{
		Op:     pbResp.Op.String(),
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromPurgeWorkerRelayPB creates a new PurgeWorkerRelayResponseDTO from a pb.PurgeWorkerRelayResponse.
func (i *DTOBuilder) FromPurgeWorkerRelayPB(pbResp *pb.PurgeWorkerRelayResponse) PurgeWorkerRelayResponseDTO {
	r := PurgeWorkerRelayResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromOperateRelayPB creates a new OperateRelayResponseDTO from a pb.OperateRelayResponse.
func (i *DTOBuilder) FromOperateRelayPB(pbResp *pb.OperateRelayResponse) OperateRelayResponseDTO {
	r := OperateRelayResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromOperateSchemaPB creates a new OperateSchemaResponseDTO from a pb.OperateSchemaResponse.
func (i *DTOBuilder) FromOperateSchemaPB(pbResp *pb.OperateSchemaResponse) OperateSchemaResponseDTO {
	r := OperateSchemaResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromOfflineMemberPB creates a new OfflineMemberResponseDTO from a pb.OfflineMemberResponse.
func (i *DTOBuilder) FromOfflineMemberPB(pbResp *pb.OfflineMemberResponse) OfflineMemberResponseDTO {
	return OfflineMemberResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}
}

// FromQueryStatusPB creates a new QueryStatusResponseDTO from a pb.QueryStatusListResponse.
func (i *DTOBuilder) FromQueryStatusPB(pbResp *pb.QueryStatusListResponse) QueryStatusResponseDTO {
	// Since the pb types are complex and different from our DTOs,
	// we'll keep the sources as the original pb.QueryStatusResponse
	return QueryStatusResponseDTO{
		Result:  pbResp.Result,
		Msg:     pbResp.Msg,
		Sources: i.convertPBSourcesToDTO(pbResp.Sources),
	}
}

// convertPBSourcesToDTO converts pb.QueryStatusResponse to our DTO format
func (i *DTOBuilder) convertPBSourcesToDTO(pbSources []*pb.QueryStatusResponse) []*QuerySourceStatus {
	sources := make([]*QuerySourceStatus, 0, len(pbSources))
	for _, s := range pbSources {
		source := &QuerySourceStatus{
			Result: s.Result,
			Msg:    s.Msg,
		}

		// Convert SourceStatus
		if s.SourceStatus != nil {
			source.SourceStatus = &SourceStatus{
				Source: s.SourceStatus.Source,
				Worker: s.SourceStatus.Worker,
			}
		}

		// Convert SubTaskStatus array
		if s.SubTaskStatus != nil {
			source.SubTaskStatus = make([]*SubTaskStatus, 0, len(s.SubTaskStatus))
			for _, st := range s.SubTaskStatus {
				subTask := &SubTaskStatus{
					Name:  st.Name,
					Stage: int(st.Stage),
					Unit:  int(st.Unit),
				}

				// Convert result if present
				if st.Result != nil {
					subTask.Result = &SubTaskResult{
						IsCanceled: st.Result.IsCanceled,
					}
					if st.Result.Errors != nil {
						subTask.Result.Errors = make([]*SubTaskError, 0, len(st.Result.Errors))
						for _, e := range st.Result.Errors {
							subTask.Result.Errors = append(subTask.Result.Errors, &SubTaskError{
								ErrCode:    int(e.ErrCode),
								ErrClass:   e.ErrClass,
								ErrScope:   e.ErrScope,
								ErrLevel:   e.ErrLevel,
								Message:    e.Message,
								Workaround: e.Workaround,
							})
						}
					}
				}

				// Convert sync status if present
				// Status is a oneof field, we need to check the concrete type
				if st.GetSync() != nil {
					sync := st.GetSync()
					subTask.Status = &SubTaskStatusInfo{
						Sync: &SyncStatus{
							TotalEvents:      sync.TotalEvents,
							TotalTps:         sync.TotalTps,
							MasterBinlog:     sync.MasterBinlog,
							MasterBinlogGtid: sync.MasterBinlogGtid,
							SyncerBinlog:     sync.SyncerBinlog,
							SyncerBinlogGtid: sync.SyncerBinlogGtid,
							Synced:           sync.Synced,
							BinlogType:       sync.BinlogType,
							TotalRows:        sync.TotalRows,
							TotalRps:         sync.TotalRps,
						},
					}
				}

				// Convert validation if present
				if st.Validation != nil {
					subTask.Validation = &ValidationStatus{
						Task:                st.Validation.Task,
						Source:              st.Validation.Source,
						Mode:                st.Validation.Mode,
						Stage:               int(st.Validation.Stage),
						ValidatorBinlog:     st.Validation.ValidatorBinlog,
						ValidatorBinlogGtid: st.Validation.ValidatorBinlogGtid,
						ProcessedRowsStatus: st.Validation.ProcessedRowsStatus,
						PendingRowsStatus:   st.Validation.PendingRowsStatus,
						ErrorRowsStatus:     st.Validation.ErrorRowsStatus,
					}
				}

				source.SubTaskStatus = append(source.SubTaskStatus, subTask)
			}
		}

		sources = append(sources, source)
	}
	return sources
}

// FromCheckTaskPB creates a new CheckTaskResponseDTO from a pb.CheckTaskResponse.
func (i *DTOBuilder) FromCheckTaskPB(pbResp *pb.CheckTaskResponse) CheckTaskResponseDTO {
	return CheckTaskResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}
}

// FromGetConfigPB creates a new GetConfigResponseDTO from a pb.GetCfgResponse.
func (i *DTOBuilder) FromGetConfigPB(pbResp *pb.GetCfgResponse) GetConfigResponseDTO {
	return GetConfigResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Config: pbResp.Cfg,
	}
}

// FromShowDDLLocksPB creates a new ShowDDLLocksResponseDTO from a pb.ShowDDLLocksResponse.
func (i *DTOBuilder) FromShowDDLLocksPB(pbResp *pb.ShowDDLLocksResponse) ShowDDLLocksResponseDTO {
	locks := make([]*DDLLock, 0, len(pbResp.Locks))
	for _, l := range pbResp.Locks {
		lock := &DDLLock{
			ID:       l.ID,
			Task:     l.Task,
			Mode:     l.Mode,
			Owner:    l.Owner,
			DDLs:     l.DDLs,
			Synced:   l.Synced,
			Unsynced: l.Unsynced,
		}
		locks = append(locks, lock)
	}

	return ShowDDLLocksResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Locks:  locks,
	}
}

// FromUnlockDDLLockPB creates a new UnlockDDLLockResponseDTO from a pb.UnlockDDLLockResponse.
func (i *DTOBuilder) FromUnlockDDLLockPB(pbResp *pb.UnlockDDLLockResponse) UnlockDDLLockResponseDTO {
	return UnlockDDLLockResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}
}

// simplifyQueryStatusResponse simplifies the QueryStatusResponseDTO for non-detailed view
func (i *DTOBuilder) simplifyQueryStatusResponse(response QueryStatusResponseDTO) QueryStatusResponseDTO {
	// For simplified view, we could extract basic task info and remove detailed status
	// This is a placeholder implementation - in practice you might want to
	// extract specific fields from the complex Sources interface{}
	return response
}

// FromQueryStatusToTaskSummary converts QueryStatusListResponse to TaskSummaryResponseDTO
func (i *DTOBuilder) FromQueryStatusToTaskSummary(pbResp *pb.QueryStatusListResponse) TaskSummaryResponseDTO {
	result := TaskSummaryResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Tasks:  []*TaskSummaryDTO{},
	}

	// TODO: Parse pbResp.Sources (interface{}) to extract task summaries
	// This would require understanding the actual structure of the sources
	// For now, return empty tasks list with basic response info

	return result
}

// FromQueryStatusToSingleTaskSummary converts QueryStatusListResponse to a single TaskSummaryDTO
func (i *DTOBuilder) FromQueryStatusToSingleTaskSummary(pbResp *pb.QueryStatusListResponse, taskName string) TaskSummaryDTO {
	result := TaskSummaryDTO{
		TaskName:   taskName,
		TaskStatus: "Unknown",
		Sources:    []string{},
		ErrorMsg:   "",
	}

	if !pbResp.Result {
		result.ErrorMsg = pbResp.Msg
		return result
	}

	// TODO: Parse pbResp.Sources to extract task status information
	// This would require understanding the actual structure

	return result
}

// FromQueryStatusToValidationStatus converts QueryStatusListResponse to validation status
func (i *DTOBuilder) FromQueryStatusToValidationStatus(pbResp *pb.QueryStatusListResponse, filterStage string) QueryValidationStatusResponseDTO {
	result := QueryValidationStatusResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Status: nil,
	}

	if !pbResp.Result {
		return result
	}

	// Extract validation status from pbResp.Sources
	statusDetail := &ValidationStatusDetail{
		Validators:    make([]*ValidationStatus, 0),
		TableStatuses: make([]*ValidationTableStatus, 0),
	}

	// Convert sources to validation status
	for _, source := range pbResp.Sources {
		if source.SubTaskStatus != nil {
			for _, subTask := range source.SubTaskStatus {
				if subTask.Validation != nil {
					validator := &ValidationStatus{
						Task:                subTask.Validation.Task,
						Source:              subTask.Validation.Source,
						Mode:                subTask.Validation.Mode,
						Stage:               int(subTask.Validation.Stage),
						ValidatorBinlog:     subTask.Validation.ValidatorBinlog,
						ValidatorBinlogGtid: subTask.Validation.ValidatorBinlogGtid,
						ProcessedRowsStatus: subTask.Validation.ProcessedRowsStatus,
						PendingRowsStatus:   subTask.Validation.PendingRowsStatus,
						ErrorRowsStatus:     subTask.Validation.ErrorRowsStatus,
					}
					statusDetail.Validators = append(statusDetail.Validators, validator)
				}
			}
		}
	}

	result.Status = statusDetail

	return result
}

// FromQueryStatusToValidationErrors converts QueryStatusListResponse to validation errors
func (i *DTOBuilder) FromQueryStatusToValidationErrors(pbResp *pb.QueryStatusListResponse, errorState string) QueryValidationErrorResponseDTO {
	result := QueryValidationErrorResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Errors: nil,
	}

	if !pbResp.Result {
		return result
	}

	// Extract validation errors from pbResp.Sources
	errors := make([]*ValidationError, 0)

	// For now, create empty errors array since we need to parse actual validation errors
	// from the sources when we have proper validation error information
	result.Errors = errors

	return result
}

// FromHandleErrorPB creates a new HandleErrorResponseDTO from a pb.HandleErrorResponse
func (i *DTOBuilder) FromHandleErrorPB(pbResp *pb.HandleErrorResponse) HandleErrorResponseDTO {
	r := HandleErrorResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromTransferSourcePB creates a new TransferSourceResponseDTO from a pb.TransferSourceResponse.
func (i *DTOBuilder) FromTransferSourcePB(pbResp *pb.TransferSourceResponse) TransferSourceResponseDTO {
	return TransferSourceResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}
}

// FromStartValidationPB creates a new StartValidationResponseDTO from a pb.StartValidationResponse.
func (i *DTOBuilder) FromStartValidationPB(pbResp *pb.StartValidationResponse) StartValidationResponseDTO {
	r := StartValidationResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromStopValidationPB creates a new StopValidationResponseDTO from a pb.StopValidationResponse.
func (i *DTOBuilder) FromStopValidationPB(pbResp *pb.StopValidationResponse) StopValidationResponseDTO {
	r := StopValidationResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromUpdateValidationPB creates a new UpdateValidationResponseDTO from a pb.UpdateValidationResponse.
func (i *DTOBuilder) FromUpdateValidationPB(pbResp *pb.UpdateValidationResponse) UpdateValidationResponseDTO {
	r := UpdateValidationResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
	}

	// Convert CommonWorkerResponse array
	for _, source := range pbResp.Sources {
		r.Sources = append(r.Sources, &CommonWorkerResponseDTO{
			Result: source.Result,
			Msg:    source.Msg,
			Source: source.Source,
			Worker: source.Worker,
		})
	}

	return r
}

// FromOperateValidationErrorPB creates a new OperateValidationErrorResponseDTO from a pb.OperateValidationErrorResponse.
func (i *DTOBuilder) FromOperateValidationErrorPB(pbResp *pb.OperateValidationErrorResponse) OperateValidationErrorResponseDTO {
	return OperateValidationErrorResponseDTO{
		Result:  pbResp.Result,
		Msg:     pbResp.Msg,
		Sources: nil, // OperateValidationErrorResponse doesn't have Sources field
	}
}

// FromGetValidationErrorPB creates a new GetValidationErrorResponseDTO from a pb.GetValidationErrorResponse.
func (i *DTOBuilder) FromGetValidationErrorPB(pbResp *pb.GetValidationErrorResponse) GetValidationErrorResponseDTO {
	errors := make([]*ValidationError, 0, len(pbResp.Error))
	for _, e := range pbResp.Error {
		// Map fields from pb.ValidationError to our ValidationError DTO
		// Since pb.ValidationError has different fields, we'll map what we can
		err := &ValidationError{
			ID:         e.Id,
			Source:     e.Source,
			SchemaName: e.SrcTable,  // Using SrcTable as SchemaName
			TableName:  e.DstTable,  // Using DstTable as TableName
			Stage:      e.ErrorType, // Using ErrorType as Stage
			Message:    e.Message,
			Timestamp:  0, // Will need to parse e.Time if needed
		}
		errors = append(errors, err)
	}

	return GetValidationErrorResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Error:  errors,
	}
}

// FromGetValidationStatusPB creates a new GetValidationStatusResponseDTO from a pb.GetValidationStatusResponse.
func (i *DTOBuilder) FromGetValidationStatusPB(pbResp *pb.GetValidationStatusResponse) GetValidationStatusResponseDTO {
	// Convert to ValidationStatusDetail
	tableStatuses := make([]*ValidationTableStatus, 0, len(pbResp.TableStatuses))
	for _, ts := range pbResp.TableStatuses {
		// Map pb fields to our DTO fields
		tableStatus := &ValidationTableStatus{
			Source:        ts.Source,
			SchemaName:    ts.SrcTable,       // Using SrcTable as SchemaName
			TableName:     ts.DstTable,       // Using DstTable as TableName
			Stage:         ts.Stage.String(), // Convert enum to string
			Message:       ts.Message,
			ProcessedRows: 0, // These fields don't exist in pb, setting defaults
			PendingRows:   0,
			FailedRows:    0,
		}
		tableStatuses = append(tableStatuses, tableStatus)
	}

	// Convert validators
	validators := make([]*ValidationStatus, 0, len(pbResp.Validators))
	for _, v := range pbResp.Validators {
		validator := &ValidationStatus{
			Task:                v.Task,
			Source:              v.Source,
			Mode:                v.Mode,
			Stage:               int(v.Stage),
			ValidatorBinlog:     v.ValidatorBinlog,
			ValidatorBinlogGtid: v.ValidatorBinlogGtid,
			ProcessedRowsStatus: v.ProcessedRowsStatus,
			PendingRowsStatus:   v.PendingRowsStatus,
			ErrorRowsStatus:     v.ErrorRowsStatus,
		}
		validators = append(validators, validator)
	}

	status := &ValidationStatusDetail{
		Validators:    validators,
		TableStatuses: tableStatuses,
	}

	return GetValidationStatusResponseDTO{
		Result: pbResp.Result,
		Msg:    pbResp.Msg,
		Status: status,
	}
}
