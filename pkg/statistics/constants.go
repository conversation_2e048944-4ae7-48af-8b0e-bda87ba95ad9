package statistics

import (
	"database/sql"

	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/util/versioninfo"
	"github.com/coreos/go-semver/semver"
)

var defaultPriority = 99

var watermarkVersion = semver.Version{
	Major: 5,
	Minor: 4,
}

type RunStatsTablesParam struct {
	channelId         int
	taskId            int
	tidbServerInfo    *versioninfo.ServerInfo
	schemaName        string
	tableName         string
	sampleRate        float64
	tidbConn          *sql.DB
	statsConfigParams *structs.StatsConfigParams
}
