package statistics

import (
	"context"
	"database/sql"
	"fmt"

	commonlog "gitee.com/pingcap_enterprise/tms/pkg/common"

	"strconv"
	"time"

	"gitee.com/pingcap_enterprise/tms/common/constants"
	"gitee.com/pingcap_enterprise/tms/common/errors"
	"gitee.com/pingcap_enterprise/tms/common/structs"
	"gitee.com/pingcap_enterprise/tms/lib/o2t-sync-diff/pkg/dbutil"
	"gitee.com/pingcap_enterprise/tms/server/models"
	"gitee.com/pingcap_enterprise/tms/server/models/channel"
	"gitee.com/pingcap_enterprise/tms/server/models/statistics"
	"gitee.com/pingcap_enterprise/tms/server/models/task"
	"gitee.com/pingcap_enterprise/tms/util/log"
	"gitee.com/pingcap_enterprise/tms/util/timeutil"
	"gitee.com/pingcap_enterprise/tms/util/versioninfo"
	"github.com/samber/lo"
)

// ExecuteTask executes the task.
func ExecuteTask(ctx context.Context, channelId int, taskId int) error {
	log.Infof("TiDBStatsCtl start, channelid:%d, taskid:%d", channelId, taskId)

	statLogger := commonlog.InitStatisticTaskLogContextHandler(ctx, channelId, taskId)
	statLogger.Infof("TiDBStatsCtl start")

	// 1. 获取任务信息并且初始化TiDB连接/版本信息
	taskInfo, tidbConn, tidbServerInfo, err := getTaskInfoAndTargetTiDB(ctx, channelId, taskId, statLogger)
	if err != nil {
		return err
	}
	defer tidbConn.Close()

	// 2. 更新任务状态
	err = updateStatTaskStatus(ctx, constants.TASK_STATUS_RUNNING, taskInfo)
	if err != nil {
		statLogger.Errorf("update task status failed, task id is %d, error: %v", taskId, err)
		return err
	}

	// 3. 初始化表分析记录
	err = InitStatsTables(ctx, channelId, taskId)
	if err != nil {
		statLogger.Errorf("run InitStatsTables failed, task id is %d, error: %v", taskId, err)
		return err
	}

	// 4. 获取执行列表
	waitingTables, err := models.GetStatisticsReaderWriter().BatchGetTaskStats(ctx, &statistics.TaskStatistics{
		ChannelId:     channelId,
		TaskId:        taskId,
		AnalyzeStatus: constants.StatStatusWaiting.String(),
	})
	if err != nil {
		statLogger.Errorf("get BatchGetTaskStats failed, error: %v", err)
	}
	statLogger.Infof("get BatchGetTaskStats finish, total tables: %v", len(waitingTables))

	// 5. 获取执行参数
	statsConfigParams, err := buildStatsConfigParams(ctx, channelId, taskId, taskInfo.TaskParamTmplateId)
	if err != nil {
		statLogger.Errorf("buildStatsConfigParams failed, error: %v", err)
	}

	// 6. 并发执行analyze
	runParam := RunStatsTablesParam{
		channelId:         channelId,
		taskId:            taskId,
		tidbServerInfo:    tidbServerInfo,
		tidbConn:          tidbConn,
		statsConfigParams: statsConfigParams,
	}
	RunStatsJobInParallel(ctx, waitingTables, runParam, statLogger)

	log.Infof("get NewWorkerPool finish")
	statLogger.Infof("get NewWorkerPool finish")

	// 7. 更新任务状态
	err = updateStatTaskStatus(ctx, constants.TASK_STATUS_FINISH, taskInfo)
	if err != nil {
		log.Errorf("update task status failed, task id is %d, error: %v", taskId, err)
		return err
	}
	return nil
}

// RunStatsJobInParallel runs stats job in parallel.
func RunStatsJobInParallel(ctx context.Context, waitingTables []*statistics.TaskStatistics, parallelRunParam RunStatsTablesParam, statLogger *commonlog.Handler) {
	taskId := parallelRunParam.taskId
	pool := NewWorkerPool(parallelRunParam.statsConfigParams.GetStatsThread(), "worker-pool")

	for _, v := range waitingTables {
		schemaName := v.SchemaName
		tableName := v.TableName
		sampleRate := v.Samplerate
		runParam := RunStatsTablesParam{
			channelId:         parallelRunParam.channelId,
			taskId:            parallelRunParam.taskId,
			tidbServerInfo:    parallelRunParam.tidbServerInfo,
			schemaName:        schemaName,
			tableName:         tableName,
			sampleRate:        sampleRate,
			tidbConn:          parallelRunParam.tidbConn,
			statsConfigParams: parallelRunParam.statsConfigParams,
		}
		pool.Apply(func() {
			err := RunStatsTables(ctx, runParam)
			if err != nil {
				statLogger.Errorf("RunStatsTables failed, taskid:%v, tablename:%v.%v, err:%v", taskId, schemaName, tableName, err)
			} else {
				statLogger.Infof("RunStatsTables success, taskid:%v, tablename:%v.%v", taskId, schemaName, tableName)
			}
		})
	}
	pool.WaitFinished()
}

// getTaskInfoAndTargetTiDB query task info and target tidb info by channel id and task id.
func getTaskInfoAndTargetTiDB(ctx context.Context, channelId int, taskId int, statLogger *commonlog.Handler) (*task.Task, *sql.DB, *versioninfo.ServerInfo, error) {
	taskInfo, err := models.GetTaskReaderWriter().GetTask(ctx, taskId)
	if err != nil {
		statLogger.Errorf("get task info failed, task id is %d, error: %v", taskId, err)
		return nil, nil, nil, err
	}
	channelInfo, err := models.GetChannelReaderWriter().GetChannel(ctx, channelId)
	if err != nil {
		statLogger.Errorf("get channel failed, channel id is %d, error: %v", channelId, err)
		return nil, nil, nil, err
	}
	statLogger.Infof("Get channel datasource info, sourcedb:%d, tartgetdb:%d", channelInfo.DatasourceIdS, channelInfo.DatasourceIdT)

	targetDB, err := models.GetDatasourceReaderWriter().Get(ctx, channelInfo.DatasourceIdT)
	if err != nil {
		statLogger.Errorf("get databsource failed, Datasource id is %d, error: %v", channelInfo.DatasourceIdS, err)
		return nil, nil, nil, err
	}
	statLogger.Infof("Get channel datasource info, sourcedb:%d %v", channelInfo.DatasourceIdS, targetDB.HostIp)

	// 获取TiDB版本
	tidbConn, err := models.OpenMysql(targetDB.UserName, targetDB.PasswordValue, targetDB.HostIp, targetDB.HostPort, "")
	if err != nil {
		statLogger.Errorf("do assess task failed, open tims db failed. err:%s", err)
		statLogger.Errorf("dbConfig:%v", targetDB)
		return nil, nil, nil, err
	}
	tidbVersionStr, err := versioninfo.FetchVersion(ctx, tidbConn)
	if err != nil {
		statLogger.Warnf("do select version() failed. err:%s", err)
	}
	tidbServerInfo := versioninfo.ParseServerInfo(tidbVersionStr)
	statLogger.Infof("get tidb version:%s", tidbVersionStr)
	return taskInfo, tidbConn, &tidbServerInfo, nil
}

// InitStatsTables diff statTables and configTables, insert or delete to TaskStatistics
func InitStatsTables(ctx context.Context, channelId int, taskId int) error {
	log.Infof("InitStatsTables start, channelid:%d, taskid:%d", channelId, taskId)
	statLogger := commonlog.InitStatisticTaskLogContextHandler(ctx, channelId, taskId)
	statLogger.Infof("TiDBStatsCtl start, channelid:%d, taskid:%d", channelId, taskId)

	// get task tables from ChannelSchemaTables to TaskStatistics
	nullTime := timeutil.GetTMSNullTime()
	configTables, err := models.GetChannelReaderWriter().GetChannelSchemaTablesByTaskId(ctx, taskId)
	if err != nil {
		statLogger.Errorf("get configTables failed, error: %v", err)
		return err
	}
	statLogger.Infof("get %d Tables from ChannelSchemaTables.", len(configTables))
	statsTables, err := models.GetStatisticsReaderWriter().GetTaskStatsByTaskId(ctx, taskId)
	if err != nil {
		statLogger.Errorf("get statsTables failed, error: %v", err)
		return err
	}
	statLogger.Infof("get %d Tables from TaskStats.", len(statsTables))

	configTableSet := lo.Map(configTables, func(v *channel.ChannelSchemaTable, _ int) structs.SchemaTablePair {
		return structs.SchemaTablePair{SchemaName: v.SchemaNameT, TableName: v.TableNameT}
	})
	statTableSet := lo.Map(statsTables, func(v *statistics.TaskStatistics, _ int) structs.SchemaTablePair {
		return structs.SchemaTablePair{SchemaName: v.SchemaName, TableName: v.TableName}
	})

	toSaveTables, toDeleteTables := lo.Difference(configTableSet, statTableSet)
	for _, k := range toSaveTables {
		schemaName := k.SchemaName
		tableName := k.TableName
		statLogger.Infof("start insert stats table:%s.%s.", schemaName, tableName)
		err := models.GetStatisticsReaderWriter().CreateTaskStats(ctx, &statistics.TaskStatistics{
			ChannelId:            channelId,
			TaskId:               taskId,
			SchemaName:           schemaName,
			TableName:            tableName,
			AnalyzeStatus:        constants.StatStatusWaiting.String(),
			Priority:             defaultPriority,
			LastAnalyzeStarttime: nullTime,
			LastAnalyzeEndtime:   nullTime,
		})
		if err != nil {
			statLogger.Errorf("CreateTaskStats failed, taskid:%d, table:%s.%s, error: %v", taskId, schemaName, tableName, err)
		}
	}
	for _, k := range toDeleteTables {
		schemaName := k.SchemaName
		tableName := k.TableName
		statLogger.Infof("start delete stats table:%s.%s.", schemaName, tableName)
		err := models.GetStatisticsReaderWriter().DeleteTaskStatsByTaskTable(ctx, taskId, schemaName, tableName)
		if err != nil {
			statLogger.Errorf("DeleteTaskStatsByTaskTable failed, taskid:%d, table:%s.%s, error: %v", taskId, schemaName, tableName, err)
		}
	}
	logMessage := fmt.Sprintf("create table statistics finished, task id is %d, total tables: %d, add :%d, delete: %d", taskId, len(configTableSet), len(toSaveTables), len(toDeleteTables))
	log.Info(logMessage)
	statLogger.Info(logMessage)
	return nil
}

// buildStatsConfigParams build stats config params via template and custom params
func buildStatsConfigParams(ctx context.Context, channelId int, taskId int, templateId int) (*structs.StatsConfigParams, error) {

	statLogger := commonlog.InitStatisticTaskLogContextHandler(ctx, channelId, taskId)
	statLogger.Infof("TiDBStatsCtl start, channelid:%d, taskid:%d", channelId, taskId)

	statLogger.Infof("buildStatsConfigParams start, channelId:%d, taskId:%d, templateId:%d", channelId, taskId, templateId)
	defaultTaskCfgs, err := models.GetTaskReaderWriter().BatchGetParamDetail(ctx, templateId)
	if err != nil {
		statLogger.Errorf("get BatchGetParamDetail info failed: %v", err)
		return nil, fmt.Errorf("get BatchGetParamDetail info failed: %v", err)
	}
	statLogger.Infof("get BatchGetParamDetail info finished, total params: %d", len(defaultTaskCfgs))
	paramMap := make(map[string]string, 0)
	for _, v := range defaultTaskCfgs {
		paramMap[v.ParamName] = v.ParamValueDefault
	}
	customTaskCfgs, err := models.GetTaskReaderWriter().BatchGetTaskParam(ctx, &task.TaskParamConfig{
		ChannelID:           channelId,
		TaskID:              taskId,
		TaskparamTemplateID: templateId,
	})
	if err != nil {
		statLogger.Errorf("get BatchGetTaskParam info failed: %v", err)
		return nil, fmt.Errorf("get BatchGetTaskParam info failed: %v", err)
	}
	statLogger.Infof("get BatchGetTaskParam info finished, total params: %d", len(customTaskCfgs))
	for _, v := range customTaskCfgs {
		paramMap[v.ParamName] = v.ParamValueCurrent
	}

	var castErr error
	retParams := &structs.StatsConfigParams{}
	for paramName, paramValue := range paramMap {
		switch paramName {
		case "task_thread":
			retParams.TaskThread, castErr = strconv.Atoi(paramValue)
			if castErr != nil {
				retParams.TaskThread = 1
				statLogger.Errorf("cast param failed: paramName:%v, paramValue:%v, err:%v", paramName, paramValue, castErr)
			}
		case "max_execution_time":
			retParams.MaxExecutionTime = paramValue
		case "tidb_mem_quota_query":
			retParams.TiDBMemQuotaQuery = paramValue
		case "tidb_distsql_scan_concurrency":
			retParams.TiDBDistSQLScanConcurrency = paramValue
		case "tidb_index_serial_scan_concurrency":
			retParams.TiDBIndexSerialScanConcurrency = paramValue
		case "tidb_build_stats_concurrency":
			retParams.TiDBBuildStatsConcurrency = paramValue
		}
	}
	return retParams, nil
}

// RunStatsTables analyze table and save stats info
func RunStatsTables(ctx context.Context, param RunStatsTablesParam) error {
	channelId := param.channelId
	taskId := param.taskId
	schemaName := param.schemaName
	tableName := param.tableName
	sqlAnalyzeParams := param.statsConfigParams

	statLogger := commonlog.InitStatisticTaskLogContextHandler(ctx, channelId, taskId)
	statLogger.Infof("RunStatsTables start, schema:%v, table:%v", schemaName, tableName)

	// 更新任务状态
	if updateErr := models.GetStatisticsReaderWriter().UpdateTaskStatsStatusStartTimeByTable(ctx, &statistics.TaskStatistics{
		TaskId:     taskId,
		SchemaName: schemaName,
		TableName:  tableName,

		AnalyzeStatus:        constants.StatStatusRunning.String(),
		LastAnalyzeStarttime: time.Now(),
		Message:              "",
	}); updateErr != nil {
		statLogger.Errorf("do dbTims.Exec failed. err:%s", updateErr)
		return updateErr
	}

	// 执行analyze
	var (
		tableRowsCount uint64
		analyzeMsg     string
		analyzeSql     string
		analyzeStatus  = constants.StatStatusSuccess.String()
	)

	analyzeSql = buildAnalyzeSQL(schemaName, tableName, param.tidbServerInfo, param.sampleRate)
	statLogger.Infof("do analyze command:%s", analyzeSql)

	tx, _ := param.tidbConn.Begin()
	tx.Exec(sqlAnalyzeParams.GetMaxExecutionTimeSQL())
	tx.Exec(sqlAnalyzeParams.GetTiDBMemQuotaQuerySQL())
	tx.Exec(sqlAnalyzeParams.GetTiDBDistSQLScanConcurrencySQL())
	tx.Exec(sqlAnalyzeParams.GetTiDBIndexSerialScanConcurrencySQL())
	tx.Exec(sqlAnalyzeParams.GetTiDBBuildStatsConcurrencySQL())
	_, err := tx.Exec(analyzeSql)
	if err != nil {
		statLogger.Errorf("do analyze failed. table:%s.%s, err:%s", schemaName, tableName, err)
		analyzeMsg = fmt.Sprintf("do analyze failed. err:%s", err)
		analyzeStatus = constants.StatStatusFailed.String()
	}
	tx.Commit()

	tableRowsCount, err = dbutil.GetTidbTableEstimateRows(ctx, param.tidbConn, schemaName, tableName)
	if err != nil {
		statLogger.Errorf("get table_rows failed. table:%s.%s, err:%s", schemaName, tableName, err)
	}

	// 更新任务状态
	if updateErr := models.GetStatisticsReaderWriter().UpdateTaskStatsStatusEndTimeByTable(ctx, &statistics.TaskStatistics{
		TaskId:     taskId,
		SchemaName: schemaName,
		TableName:  tableName,

		AnalyzeStatus:      analyzeStatus,
		Message:            analyzeMsg,
		LastAnalyzedRows:   tableRowsCount,
		LastAnalyzeEndtime: time.Now(),
	}); updateErr != nil {
		log.Errorf("do dbTims.Exec failed. err:%s", updateErr)
		return updateErr
	}
	return nil
}

// updateStatTaskStatus update task status
func updateStatTaskStatus(ctx context.Context, taskStatus int, taskInfo *task.Task) error {
	log.Infof("start update task[%d] status to %d.", taskInfo.TaskID, taskStatus)

	nullTime := timeutil.GetTMSNullTime()
	nowTime := timeutil.GetNowTime()
	// task start
	if taskStatus == constants.TASK_STATUS_RUNNING {
		taskInfo.TaskStatus = taskStatus
		taskInfo.StartTime = nowTime
		taskInfo.EndTime = nullTime
		taskInfo.ErrorDetail = ""
	} else {
		taskInfo.TaskStatus = taskStatus
		taskInfo.EndTime = nowTime
	}
	_, err := models.GetTaskReaderWriter().UpdateTask(ctx, taskInfo)
	if err != nil {
		log.Errorf("update task[%d] status to %d failed.", taskInfo.TaskID, taskStatus)
		return errors.NewErrorf(errors.TIMS_TASK_EXCUTION_FAILED, "update task[%d] status to %d failed.", taskInfo.TaskID, taskStatus)
	}
	return nil
}

// buildAnalyzeSQL build analyze sql, if tidb version >= watermarkVersion, use sampleRate
func buildAnalyzeSQL(schemaName string, tableName string, tidbServerInfo *versioninfo.ServerInfo, sampleRate float64) string {
	var analyzeSql string
	// 比较版本号，如果版本号大于watermarkVersion，则使用采样率
	if !tidbServerInfo.ServerVersion.LessThan(watermarkVersion) && sampleRate > 0 {
		analyzeSql = fmt.Sprintf("analyze table %s.%s with %f samplerate", schemaName, tableName, sampleRate)
	} else {
		analyzeSql = fmt.Sprintf("analyze table %s.%s", schemaName, tableName)
	}
	return analyzeSql
}
