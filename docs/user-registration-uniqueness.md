# 用户注册唯一性约束实现

## 概述

为了确保用户注册时用户名和邮箱的唯一性，我们对系统进行了以下修改：

## 修改内容

### 1. 数据库层面

#### 用户模型更新 (`server/models/users/user_entity.go`)
- 为 `Username` 字段添加了唯一性索引：`uniqueIndex:idx_users_username`
- 为 `Email` 字段添加了唯一性索引：`uniqueIndex:idx_users_email`

#### 数据库迁移脚本 (`scripts/migrations/add_user_unique_constraints.sql`)
- 提供了为现有数据库添加唯一性约束的SQL脚本
- 包含检查重复数据的查询语句
- 添加唯一性约束的DDL语句

### 2. 应用层面

#### 用户数据访问层 (`server/models/users/`)
- 在 `ReaderWriter` 接口中添加了 `GetUserByEmail` 方法
- 在 `UserReaderWriter` 实现中添加了 `GetUserByEmail` 方法

#### 用户服务层 (`server/service/users/user_service.go`)
- 在 `Register` 方法中添加了用户名和邮箱的唯一性检查
- 添加了数据库级别唯一性约束错误的处理逻辑
- 添加了输入参数验证（用户名、密码、邮箱格式）
- 提供了友好的中文错误提示

## 实现细节

### 唯一性检查流程

1. **应用层检查**：在创建用户之前，先查询数据库检查用户名和邮箱是否已存在
2. **数据库层约束**：通过唯一性索引确保数据库层面的约束
3. **错误处理**：捕获数据库唯一性约束错误，提供友好的错误信息

### 错误处理

系统会处理以下情况：
- 用户名不能为空：返回 "用户名不能为空"
- 用户名长度不符合要求：返回 "用户名长度必须在3-50个字符之间"
- 密码不能为空：返回 "密码不能为空"
- 密码长度不足：返回 "密码长度不能少于6个字符"
- 邮箱不能为空：返回 "邮箱不能为空"
- 邮箱格式不正确：返回 "邮箱格式不正确"
- 用户名已存在：返回 "用户名已存在"
- 邮箱已被注册：返回 "邮箱已被注册"
- 其他数据库错误：返回原始错误信息

## 部署说明

### 新部署
对于新部署的系统，GORM会自动创建包含唯一性约束的表结构。

### 现有系统升级
对于现有系统，需要执行以下步骤：

1. 检查现有数据是否有重复：
   ```sql
   -- 检查重复用户名
   SELECT username, COUNT(*) as count 
   FROM users 
   WHERE deleted_at IS NULL 
   GROUP BY username 
   HAVING COUNT(*) > 1;
   
   -- 检查重复邮箱
   SELECT email, COUNT(*) as count 
   FROM users 
   WHERE deleted_at IS NULL 
   GROUP BY email 
   HAVING COUNT(*) > 1;
   ```

2. 如果有重复数据，需要先清理重复数据

3. 执行迁移脚本：
   ```sql
   ALTER TABLE users ADD UNIQUE KEY idx_users_username (username);
   ALTER TABLE users ADD UNIQUE KEY idx_users_email (email);
   ```

## 测试建议

1. **正常注册测试**：使用新的用户名和邮箱进行注册
2. **重复用户名测试**：尝试使用已存在的用户名注册
3. **重复邮箱测试**：尝试使用已存在的邮箱注册
4. **并发测试**：同时使用相同的用户名或邮箱进行注册
5. **数据库约束测试**：直接操作数据库验证唯一性约束是否生效

## 注意事项

1. 唯一性检查是大小写敏感的
2. 软删除的用户（`deleted_at` 不为空）不会影响唯一性检查
3. 建议在生产环境部署前先在测试环境验证
4. 如果现有系统有大量重复数据，需要制定数据清理策略 