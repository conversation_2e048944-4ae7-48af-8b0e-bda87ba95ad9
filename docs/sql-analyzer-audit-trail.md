# SQL Analyzer User Operation Audit Trail

## Overview
This document describes the audit trail functionality added to track user operations on SQL analysis results.

## Features

### 1. History Tracking
- All user operations (marking SQL as ignored/resolved) are tracked in a history table
- Tracks who made the change, when, and what the previous status was
- Supports audit requirements and compliance

### 2. Database Schema

#### History Table: `sql_stmt_exec_result_history`
```sql
- id: Primary key
- sql_exec_id: Reference to the SQL execution result
- task_id: Task ID for filtering
- operation_type: CREATE or UPDATE
- previous_status: Status before the change
- new_status: Status after the change
- operate_by: User who performed the operation
- operate_at: Timestamp of the operation
- operate_remark: Optional comment
- client_ip: Client IP address (future enhancement)
```

### 3. API Endpoints

#### Get Operation History
```
GET /api/v1/sql-analyzer/results/{taskId}/history?sqlExecId={sqlExecId}
```

Parameters:
- `taskId` (required): The task ID
- `sqlExecId` (optional): Filter by specific SQL execution ID

Response:
```json
{
  "code": 0,
  "data": {
    "history": [
      {
        "id": 1,
        "sqlExecId": 123,
        "taskId": 456,
        "operationType": "UPDATE",
        "previousStatus": "normal",
        "newStatus": "ignored",
        "operateBy": "<EMAIL>",
        "operateAt": "2025-06-27T10:00:00Z",
        "operateRemark": "False positive"
      }
    ]
  }
}
```

### 4. Implementation Details

#### Transaction Safety
- Updates to SQL results and history creation are wrapped in a database transaction
- Ensures consistency between current state and history records

#### Performance Considerations
- Indexed on `sql_exec_id`, `task_id`, and `operate_at` for efficient queries
- Composite index on `(task_id, sql_exec_id)` for common query patterns

### 5. Usage Examples

#### Track User Operations
When a user marks SQL entries as ignored or resolved:
1. The main record is updated with the new status
2. A history record is automatically created
3. Both operations happen in a single transaction

#### Audit Trail Query
To see all changes for a specific SQL result:
```bash
curl -X GET "http://localhost:8082/api/v1/sql-analyzer/results/123/history?sqlExecId=456"
```

To see all changes for a task:
```bash
curl -X GET "http://localhost:8082/api/v1/sql-analyzer/results/123/history"
```

### 6. Future Enhancements
- Add client IP tracking from HTTP context
- Add role-based access control for viewing history
- Add retention policies for history records
- Add export functionality for compliance reports