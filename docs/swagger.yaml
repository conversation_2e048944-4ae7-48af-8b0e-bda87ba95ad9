basePath: /api/v1/
definitions:
  dm.CheckerConfig:
    properties:
      backoff_max:
        description: 最大延迟时间（不能小于 1s）
        type: string
      backoff_rollback:
        description: 回滚延迟时间
        type: string
      check_enable:
        description: 是否启用检查
        type: boolean
    type: object
  dm.EventFilter:
    properties:
      action:
        description: 操作类型
        type: string
      events:
        description: 事件类型列表
        items:
          type: string
        type: array
      schema_pattern:
        description: 数据库模式匹配
        type: string
      sql_pattern:
        description: SQL 模式匹配
        items:
          type: string
        type: array
      table_pattern:
        description: 表名匹配
        type: string
    type: object
  dm.PurgeConfig:
    properties:
      expires:
        description: 过期时间
        type: integer
      interval:
        description: 清理间隔时间（秒）
        type: integer
      remain_space:
        description: 剩余空间（GB）
        type: integer
    type: object
  dm.SecurityConfig:
    properties:
      ssl_ca:
        description: SSL CA 证书路径
        type: string
      ssl_cert:
        description: SSL 证书路径
        type: string
      ssl_key:
        description: SSL 密钥路径
        type: string
    type: object
  message.AbstractSyntaxTreeNode:
    properties:
      children:
        items:
          $ref: '#/definitions/message.AbstractSyntaxTreeNode'
        type: array
      key:
        type: string
      stmtName:
        type: string
      stmtType:
        type: string
      stmtValue:
        type: string
    type: object
  message.ActiveTmsSession:
    properties:
      event:
        description: 当前等待事件
        type: string
      machine:
        description: 机器名
        type: string
      program:
        description: 程序名
        type: string
      serial:
        description: 会话序列号
        type: string
      sid:
        description: 会话 ID
        type: string
      sqlExecStart:
        description: SQL 执行开始时间
        type: string
      sqlId:
        description: 当前 SQL 的 SQL_ID
        type: string
      status:
        description: 会话状态
        type: string
      username:
        description: 用户名
        type: string
    type: object
  message.BatchDeleteChannelSchemasResp:
    type: object
  message.BatchDeleteDataSourceReq:
    properties:
      dataSourceIds:
        items:
          type: integer
        type: array
    required:
    - dataSourceIds
    type: object
  message.BatchDeleteDataSourceResp:
    type: object
  message.BatchDeleteTaskParamTemplatesReq:
    properties:
      taskId:
        type: integer
      taskParamTemplateIDs:
        items:
          type: integer
        type: array
    type: object
  message.BatchDeleteTaskParamTemplatesResp:
    type: object
  message.BatchDeleteTasksReq:
    properties:
      taskIDs:
        items:
          type: integer
        type: array
    type: object
  message.BatchDeleteTasksResp:
    type: object
  message.BatchDeleteTemplateInfosReq:
    properties:
      templateIds:
        items:
          type: integer
        type: array
    type: object
  message.BatchDeleteTemplateInfosResp:
    type: object
  message.BatchDeleteTemplateParamDetailsReq:
    properties:
      taskId:
        minimum: 1
        type: integer
      templateIds:
        items:
          type: integer
        type: array
    required:
    - taskId
    type: object
  message.BatchDeleteTemplateParamDetailsResp:
    type: object
  message.BatchUpdateSourceTableColumnsResp:
    properties:
      affected:
        type: integer
    type: object
  message.CSVFileInfo:
    properties:
      err_message:
        type: string
      file_name:
        type: string
      file_path:
        type: string
      file_size:
        type: integer
      is_valid:
        type: boolean
      table_property:
        $ref: '#/definitions/message.CSVTableProperty'
    type: object
  message.CSVTableMigrationDetail:
    properties:
      createdAt:
        type: string
      file_name:
        type: string
      file_path:
        type: string
      file_size:
        type: integer
      id:
        type: integer
      import_error:
        type: string
      import_status:
        type: string
      schema_name_s:
        type: string
      schema_name_t:
        type: string
      table_name_s:
        type: string
      table_name_t:
        type: string
      task_id:
        type: integer
      task_status:
        type: string
    type: object
  message.CSVTableProperty:
    properties:
      csv_file_num:
        type: integer
      err_message:
        type: string
      is_valid:
        type: boolean
      schema_name_t:
        type: string
      table_name_t:
        type: string
    type: object
  message.CallSpaCollectReq:
    properties:
      callType:
        enum:
        - 0
        - 1
        - 2
        type: integer
    required:
    - callType
    type: object
  message.CallSpaCollectResp:
    properties:
      message:
        type: string
    type: object
  message.Channel:
    properties:
      channelId:
        type: integer
      channelMode:
        maxLength: 10
        type: string
      channelName:
        maxLength: 100
        type: string
      channelType:
        enum:
        - O2T
        - M2T
        - C2T
        - O2M
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dataCompare:
        enum:
        - "Y"
        - "N"
        type: string
      dataCompareEnvStatus:
        maxLength: 10
        type: string
      datasourceIdS:
        type: integer
      datasourceIdT:
        type: integer
      datasourceNameS:
        maxLength: 100
        type: string
      datasourceNameT:
        maxLength: 100
        type: string
      increment:
        enum:
        - "Y"
        - "N"
        type: string
      migrateCsvData:
        enum:
        - "Y"
        - "N"
        type: string
      migrateFullData:
        enum:
        - "Y"
        - "N"
        type: string
      migrateStructure:
        enum:
        - "Y"
        - "N"
        type: string
      objAssessment:
        enum:
        - "Y"
        - "N"
        type: string
      objParser:
        type: string
      sqlAnalyzerEnvStatus:
        maxLength: 10
        type: string
      sqlAssessment:
        description: ObjParser            string `json:"objParser" validate:"required,oneof=Y
          N" enums:"Y,N"`
        enum:
        - "Y"
        - "N"
        type: string
      taskCreated:
        enum:
        - "Y"
        - "N"
        maxLength: 1
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelName
    - channelType
    - dataCompare
    - datasourceIdS
    - datasourceIdT
    - increment
    - migrateCsvData
    - migrateFullData
    - migrateStructure
    - objAssessment
    - sqlAssessment
    type: object
  message.ChannelSchema:
    properties:
      channelId:
        type: integer
      channelSchemaId:
        type: integer
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbNameS:
        maxLength: 100
        type: string
      dbNameT:
        maxLength: 200
        type: string
      schemaNameS:
        maxLength: 200
        minLength: 1
        type: string
      schemaNameT:
        maxLength: 200
        minLength: 1
        type: string
      taskId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - schemaNameS
    - schemaNameT
    type: object
  message.ChannelSchemaObject:
    properties:
      appendData:
        enum:
        - "Y"
        - "N"
        type: string
      channelId:
        type: integer
      channelObjectId:
        type: integer
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      loadData:
        enum:
        - "Y"
        - "N"
        type: string
      onlyIndex:
        enum:
        - "Y"
        - "N"
        type: string
      onlyTable:
        enum:
        - "Y"
        - "N"
        type: string
      onlyTableandindex:
        enum:
        - "Y"
        - "N"
        type: string
      partitiontable:
        enum:
        - "Y"
        - "N"
        type: string
      reloadData:
        enum:
        - "Y"
        - "N"
        type: string
      taskId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - taskId
    type: object
  message.ChannelSchemaTable:
    properties:
      channelId:
        type: integer
      channelSchtableId:
        type: integer
      clusterTypeT:
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbNameS:
        maxLength: 200
        minLength: 1
        type: string
      dbNameT:
        maxLength: 200
        minLength: 1
        type: string
      partitioningCountS:
        type: integer
      partitioningTypeS:
        type: string
      partitioningTypeT:
        type: string
      pkS:
        type: string
      pkT:
        type: string
      schemaNameS:
        maxLength: 200
        minLength: 1
        type: string
      schemaNameT:
        maxLength: 200
        minLength: 1
        type: string
      subPartitioningCountS:
        type: integer
      subPartitioningTypeS:
        type: string
      tableNameS:
        maxLength: 200
        minLength: 1
        type: string
      tableNameT:
        maxLength: 200
        minLength: 1
        type: string
      tableSizeM:
        type: number
      taskId:
        type: integer
      taskType:
        type: integer
      ukS:
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - dbNameS
    - dbNameT
    - schemaNameS
    - schemaNameT
    - tableNameS
    - tableNameT
    - taskId
    - taskType
    type: object
  message.ChannelTaskSchemaStatisticsDuplicate:
    properties:
      analyzeDuration:
        type: string
      analyzeStatus:
        type: string
      channelId:
        type: integer
      channelSchtableId:
        type: integer
      duplicateCount:
        type: integer
      duplicateTasks:
        type: string
      lastAnalyzeEndtime:
        type: string
      lastAnalyzeStarttime:
        type: string
      lastAnalyzedRows:
        type: integer
      message:
        type: string
      priority:
        type: integer
      samplerate:
        type: number
      schemaName:
        type: string
      tableName:
        type: string
      tableRows:
        type: integer
      taskId:
        type: integer
    type: object
  message.Chunk:
    properties:
      channelId:
        type: integer
      checksum:
        type: string
      chunkId:
        type: integer
      chunkStr:
        type: string
      instanceId:
        type: string
      message:
        type: string
      range:
        type: string
      schema:
        type: string
      sourceCount:
        type: integer
      sourceTime:
        type: string
      state:
        type: string
      table:
        type: string
      targetCount:
        type: integer
      targetTime:
        type: string
      taskId:
        type: integer
      updateTime:
        type: string
    type: object
  message.ChunkDataAnalyze:
    properties:
      channelId:
        type: integer
      chunkId:
        type: integer
      comment:
        type: string
      createdAt:
        type: string
      errorMessage:
        type: string
      id:
        type: integer
      rowIDMD5:
        type: string
      rowIDStr:
        type: string
      schemaNameS:
        type: string
      status:
        type: string
      tableNameS:
        type: string
      taskId:
        type: integer
      updatedAt:
        type: string
    type: object
  message.ClearCacheResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.ColDefaultMapRule:
    properties:
      colDefaultMapRuleId:
        type: integer
      colTypeDefaultValueS:
        maxLength: 50
        minLength: 0
        type: string
      colTypeDefaultValueT:
        maxLength: 50
        minLength: 0
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isEquivalent:
        enum:
        - "Y"
        - "N"
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isEquivalent
    type: object
  message.CommonResp:
    type: object
  message.CommonResult:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  message.ConvertObject:
    properties:
      depth:
        type: integer
      objectUUID:
        type: string
    required:
    - depth
    - objectUUID
    type: object
  message.ConvertPLSQLToJavaInManualResp:
    properties:
      javaCode:
        type: string
      prompts:
        items:
          type: string
        type: array
    type: object
  message.ConvertPLSQLToJavaResp:
    type: object
  message.CreateChunkReq:
    properties:
      channelId:
        type: integer
      taskId:
        type: integer
    type: object
  message.CreateChunkResp:
    type: object
  message.CreateDefaultTasksResp:
    type: object
  message.CreateOrUpdateChannelReq:
    properties:
      channelId:
        type: integer
      channelMode:
        maxLength: 10
        type: string
      channelName:
        maxLength: 100
        type: string
      channelType:
        enum:
        - O2T
        - M2T
        - C2T
        - O2M
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dataCompare:
        enum:
        - "Y"
        - "N"
        type: string
      dataCompareEnvStatus:
        maxLength: 10
        type: string
      datasourceIdS:
        type: integer
      datasourceIdT:
        type: integer
      datasourceNameS:
        maxLength: 100
        type: string
      datasourceNameT:
        maxLength: 100
        type: string
      increment:
        enum:
        - "Y"
        - "N"
        type: string
      migrateCsvData:
        enum:
        - "Y"
        - "N"
        type: string
      migrateFullData:
        enum:
        - "Y"
        - "N"
        type: string
      migrateStructure:
        enum:
        - "Y"
        - "N"
        type: string
      objAssessment:
        enum:
        - "Y"
        - "N"
        type: string
      objParser:
        type: string
      sqlAnalyzerEnvStatus:
        maxLength: 10
        type: string
      sqlAssessment:
        description: ObjParser            string `json:"objParser" validate:"required,oneof=Y
          N" enums:"Y,N"`
        enum:
        - "Y"
        - "N"
        type: string
      taskCreated:
        enum:
        - "Y"
        - "N"
        maxLength: 1
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelName
    - channelType
    - dataCompare
    - datasourceIdS
    - datasourceIdT
    - increment
    - migrateCsvData
    - migrateFullData
    - migrateStructure
    - objAssessment
    - sqlAssessment
    type: object
  message.CreateOrUpdateChannelResp:
    properties:
      channelId:
        type: integer
    type: object
  message.CreateOrUpdateChannelSchemasReq:
    properties:
      channelSchemas:
        items:
          $ref: '#/definitions/message.ChannelSchema'
        type: array
    type: object
  message.CreateOrUpdateChannelSchemasResp:
    type: object
  message.CreateOrUpdateColDefaultMapRuleReq:
    properties:
      colDefaultMapRuleId:
        type: integer
      colTypeDefaultValueS:
        maxLength: 50
        minLength: 0
        type: string
      colTypeDefaultValueT:
        maxLength: 50
        minLength: 0
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isEquivalent:
        enum:
        - "Y"
        - "N"
        type: string
      templateId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isEquivalent
    type: object
  message.CreateOrUpdateColDefaultMapRuleResp:
    type: object
  message.CreateOrUpdateDataSourceReq:
    properties:
      asSysDBA:
        type: integer
      asmDBIp:
        type: string
      asmDBName:
        type: string
      asmDBPasswd:
        type: string
      asmDBPasswdEncrypt:
        type: string
      asmDBPort:
        type: integer
      asmDBUser:
        type: string
      asmHome:
        type: string
      asmOracleHome:
        type: string
      asmSid:
        type: string
      charset:
        maxLength: 100
        type: string
      comment:
        maxLength: 1000
        type: string
      connectParams:
        maxLength: 100
        type: string
      connectionStatus:
        enum:
        - Y(success)
        - N(failed)
        - S(not test)
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dataSourceId:
        type: integer
      dataSourceName:
        maxLength: 100
        minLength: 1
        type: string
      dbConnectionMode:
        type: string
      dbName:
        maxLength: 100
        type: string
      dbType:
        enum:
        - oracle
        - mysql
        - tidb
        - tidb-proxy
        - oracle-adg csv
        type: string
      dbVersion:
        type: string
      enableIncrementSync:
        type: boolean
      hostIp:
        type: string
      hostList:
        type: string
      hostPort:
        type: integer
      linkFlag:
        type: string
      logFileStoreMode:
        type: string
      oracleHome:
        type: string
      oracleSID:
        type: string
      password:
        maxLength: 100
        type: string
      passwordEncrypt:
        maxLength: 1000
        type: string
      pdbDBName:
        type: string
      pdbFlag:
        type: string
      pdbName:
        type: string
      proxySourceId:
        type: integer
      serviceName:
        maxLength: 100
        type: string
      sidName:
        type: string
      tableOption:
        maxLength: 100
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      user:
        maxLength: 100
        type: string
    required:
    - connectionStatus
    - dataSourceName
    - dbType
    - hostIp
    - hostPort
    - user
    type: object
  message.CreateOrUpdateDataSourceResp:
    type: object
  message.CreateOrUpdateObjMapRuleReq:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isCompatibility:
        enum:
        - "Y"
        - "N"
        type: string
      isConvertible:
        enum:
        - "Y"
        - "N"
        type: string
      objMapRuleId:
        type: integer
      objectType:
        maxLength: 100
        minLength: 1
        type: string
      objectTypeName:
        maxLength: 100
        minLength: 1
        type: string
      templateId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isCompatibility
    - isConvertible
    - objectType
    - objectTypeName
    type: object
  message.CreateOrUpdateObjMapRuleResp:
    type: object
  message.CreateOrUpdateSqlMapRuleReq:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isCompatibility:
        enum:
        - "Y"
        - "N"
        type: string
      isConvertible:
        enum:
        - "Y"
        - "N"
        type: string
      keywords:
        maxLength: 20
        minLength: 1
        type: string
      sqlMapRuleId:
        type: integer
      templateId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isCompatibility
    - isConvertible
    - keywords
    type: object
  message.CreateOrUpdateSqlMapRuleResp:
    type: object
  message.CreateOrUpdateTabColMapRuleReq:
    properties:
      colTypeNameS:
        maxLength: 100
        minLength: 1
        type: string
      colTypeNameT:
        maxLength: 100
        minLength: 1
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isEquivalent:
        enum:
        - "Y"
        - "N"
        type: string
      tabColMapRuleId:
        type: integer
      templateId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - colTypeNameS
    - colTypeNameT
    - dbTypeS
    - dbTypeT
    - isEquivalent
    type: object
  message.CreateOrUpdateTabColMapRuleResp:
    type: object
  message.CreateOrUpdateTaskParamTemplateReq:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      defaultTag:
        enum:
        - "Y"
        - "N"
        type: string
      paramTemplateName:
        maxLength: 100
        minLength: 1
        type: string
      taskParamTemplateID:
        type: integer
      taskType:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      templateType:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - defaultTag
    - paramTemplateName
    - taskType
    type: object
  message.CreateOrUpdateTaskParamTemplateResp:
    properties:
      taskParamTemplateID:
        type: integer
    type: object
  message.CreateOrUpdateTemplateInfoReq:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      defaultTag:
        enum:
        - "Y"
        - "N"
        type: string
      templateId:
        type: integer
      templateName:
        maxLength: 100
        minLength: 1
        type: string
      templateType:
        enum:
        - object_map
        - sql_map
        - col_default_map
        - tab_col_map
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - defaultTag
    - templateName
    - templateType
    type: object
  message.CreateOrUpdateTemplateInfoResp:
    type: object
  message.CreateOrUpdateTemplateParamDetailReq:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      hasSubParams:
        type: boolean
      paramName:
        type: string
      paramValueDefault:
        type: string
      subParamValuesDefault:
        items:
          type: string
        type: array
      taskparamTemplateId:
        minimum: 1
        type: integer
      templateId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - paramName
    - taskparamTemplateId
    type: object
  message.CreateOrUpdateTemplateParamDetailResp:
    type: object
  message.CreateTaskReq:
    properties:
      channelId:
        type: integer
      colDefaultMapTemplateId:
        type: integer
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      endTime:
        type: string
      errorDetail:
        type: string
      incrementId:
        type: integer
      objmapTemplateId:
        type: integer
      onlyIncompatibleDetail:
        type: integer
      parentTaskId:
        type: integer
      progress:
        type: number
      runParams:
        type: string
      scnNumber:
        type: string
      serverId:
        type: string
      sqlmapTemplateId:
        type: integer
      startTime:
        type: string
      tabcolmapTemplateId:
        type: integer
      taskId:
        type: integer
      taskName:
        maxLength: 100
        minLength: 1
        type: string
      taskObjRef:
        enum:
        - channel
        - tasks
        type: string
      taskParamTemplateId:
        type: integer
      taskReftask:
        type: integer
      taskSeq:
        type: integer
      taskStatus:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        type: integer
      taskType:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      taskWarning:
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - taskName
    - taskStatus
    - taskType
    type: object
  message.CreateTaskResp:
    type: object
  message.CreateTaskTabCfgByCsvReq:
    properties:
      channelId:
        type: integer
      fileName:
        type: string
      taskId:
        type: integer
    required:
    - fileName
    type: object
  message.CreateTaskTableConfigsReq:
    properties:
      taskTableConfigs:
        items:
          $ref: '#/definitions/message.TaskTableConfig'
        type: array
    type: object
  message.CreateTaskTableConfigsResp:
    type: object
  message.CustomDatabaseConfig:
    properties:
      host:
        type: string
      password:
        type: string
      password_encrypt:
        type: string
      port:
        type: integer
      security:
        $ref: '#/definitions/dm.SecurityConfig'
      user:
        type: string
    type: object
  message.CustomSourceConfigDTO:
    properties:
      case_sensitive:
        type: boolean
      checker:
        $ref: '#/definitions/dm.CheckerConfig'
      enable_gtid:
        type: boolean
      enable_relay:
        type: boolean
      filters:
        items:
          $ref: '#/definitions/dm.EventFilter'
        type: array
      from:
        $ref: '#/definitions/message.CustomDatabaseConfig'
      purge:
        $ref: '#/definitions/dm.PurgeConfig'
      relay_binlog_gtid:
        type: string
      relay_binlog_name:
        type: string
      relay_dir:
        type: string
      source_id:
        type: string
    type: object
  message.DSGTableDefine:
    properties:
      full:
        type: string
      map:
        type: string
      real:
        type: string
    type: object
  message.DataCompareReExecutionReq:
    properties:
      channelId:
        type: integer
      ids:
        items:
          type: integer
        type: array
      taskId:
        type: integer
    type: object
  message.DataCompareReExecutionResp:
    type: object
  message.DataCompareSchemaReq:
    properties:
      schema:
        type: string
      taskId:
        type: integer
    type: object
  message.DataCompareSchemaResp:
    properties:
      StartTime:
        type: string
      compareDuration:
        type: string
      endTime:
        type: string
      failedNums:
        type: integer
      runningNums:
        type: integer
      schema:
        type: string
      successNums:
        type: integer
      successRatio:
        type: string
      taskId:
        type: integer
      totalNums:
        type: integer
      waitingNums:
        type: integer
    type: object
  message.DataCompareSchemaStateReq:
    properties:
      page:
        type: integer
      pageSize:
        type: integer
      schema:
        type: string
      state:
        enum:
        - success
        - failed
        - error
        - checking
        - not_checked
        - ignore
        - invalid
        type: string
      taskId:
        type: integer
    type: object
  message.DataCompareSchemaStateResp:
    properties:
      schema:
        type: string
      state:
        type: string
      tableDetail:
        items:
          $ref: '#/definitions/message.SummaryWithError'
        type: array
      tablecount:
        type: integer
      taskId:
        type: integer
    type: object
  message.DataCompareSummaryDetail:
    properties:
      StartTime:
        type: string
      compareDuration:
        type: string
      endTime:
        type: string
      failedNums:
        type: integer
      runningNums:
        type: integer
      schema:
        type: string
      successNums:
        type: integer
      successRatio:
        type: string
      taskId:
        type: integer
      totalNums:
        type: integer
      waitingNums:
        type: integer
    type: object
  message.DataCompareTableChunkReq:
    properties:
      schema:
        type: string
      state:
        type: string
      table:
        type: string
      taskId:
        type: integer
    type: object
  message.DataCompareTableChunkResp:
    properties:
      chunkCount:
        type: integer
      chunkDetail:
        items:
          $ref: '#/definitions/message.Chunk'
        type: array
      schema:
        type: string
      state:
        type: string
      table:
        type: string
      taskId:
        type: integer
    type: object
  message.DataSource:
    properties:
      asSysDBA:
        type: integer
      asmDBIp:
        type: string
      asmDBName:
        type: string
      asmDBPasswd:
        type: string
      asmDBPasswdEncrypt:
        type: string
      asmDBPort:
        type: integer
      asmDBUser:
        type: string
      asmHome:
        type: string
      asmOracleHome:
        type: string
      asmSid:
        type: string
      charset:
        maxLength: 100
        type: string
      comment:
        maxLength: 1000
        type: string
      connectParams:
        maxLength: 100
        type: string
      connectionStatus:
        enum:
        - Y(success)
        - N(failed)
        - S(not test)
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dataSourceId:
        type: integer
      dataSourceName:
        maxLength: 100
        minLength: 1
        type: string
      dbConnectionMode:
        type: string
      dbName:
        maxLength: 100
        type: string
      dbType:
        enum:
        - oracle
        - mysql
        - tidb
        - tidb-proxy
        - oracle-adg csv
        type: string
      dbVersion:
        type: string
      enableIncrementSync:
        type: boolean
      hostIp:
        type: string
      hostList:
        type: string
      hostPort:
        type: integer
      linkFlag:
        type: string
      logFileStoreMode:
        type: string
      oracleHome:
        type: string
      oracleSID:
        type: string
      password:
        maxLength: 100
        type: string
      passwordEncrypt:
        maxLength: 1000
        type: string
      pdbDBName:
        type: string
      pdbFlag:
        type: string
      pdbName:
        type: string
      proxySourceId:
        type: integer
      serviceName:
        maxLength: 100
        type: string
      sidName:
        type: string
      tableOption:
        maxLength: 100
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      user:
        maxLength: 100
        type: string
    required:
    - connectionStatus
    - dataSourceName
    - dbType
    - hostIp
    - hostPort
    - user
    type: object
  message.Datum:
    properties:
      time:
        type: string
      value:
        type: number
    type: object
  message.DefinitionAndDetail:
    properties:
      definition:
        $ref: '#/definitions/message.GetOracleObjectDefinition'
      detail:
        $ref: '#/definitions/message.OracleObjectDefinitionAnalyzeDetail'
    type: object
  message.DeleteChannelResp:
    type: object
  message.DeleteColDefaultMapRuleByIdResp:
    type: object
  message.DeleteDsgTaskResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.DeleteHostReq:
    properties:
      macId:
        type: integer
    type: object
  message.DeleteHostResp:
    type: object
  message.DeleteObjMapRuleByIdResp:
    type: object
  message.DeletePromptResp:
    type: object
  message.DeleteSourceTableColumnsResp:
    properties:
      affectedColumns:
        type: integer
      affectedSummary:
        type: integer
    type: object
  message.DeleteSqlMapRuleByIdResp:
    type: object
  message.DeleteStatsTaskConflictTablesReq:
    properties:
      statsTables:
        items:
          $ref: '#/definitions/message.StatsTable'
        type: array
      taskId:
        type: integer
    type: object
  message.DeleteStatsTaskConflictTablesResp:
    type: object
  message.DeleteTabColMapRuleByIdResp:
    type: object
  message.DeleteTabcolCustMapRulesReq:
    properties:
      tabcolMapruleId:
        type: integer
    type: object
  message.DeleteTaskObjectPromptRelationResp:
    type: object
  message.DeleteTaskTableConfigsReq:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  message.DeleteTaskTableConfigsResp:
    type: object
  message.DetailDataSourceResp:
    properties:
      dataSource:
        $ref: '#/definitions/message.DataSource'
    type: object
  message.DetailTemplateInfoResp:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      defaultTag:
        enum:
        - "Y"
        - "N"
        type: string
      templateId:
        type: integer
      templateName:
        maxLength: 100
        minLength: 1
        type: string
      templateType:
        enum:
        - object_map
        - sql_map
        - col_default_map
        - tab_col_map
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - defaultTag
    - templateName
    - templateType
    type: object
  message.DownloadFixSQLReq:
    properties:
      schema_name_s:
        items:
          type: string
        type: array
      table_name:
        type: string
      task_id:
        type: integer
    type: object
  message.DownloadFixSQLResp:
    properties:
      exportFilePath:
        type: string
    type: object
  message.DownloadJavaCodesResp:
    properties:
      filePath:
        type: string
    type: object
  message.DownloadObjectAssessResultReportBySchemaReq:
    properties:
      channelId:
        type: integer
      schema:
        items:
          type: string
        type: array
      taskId:
        type: integer
      title:
        type: string
    type: object
  message.DownloadObjectAssessResultReportBySchemaResp:
    type: object
  message.DownloadObjectAssessResultReportResp:
    type: object
  message.DownloadSqlReportBySchemaReq:
    properties:
      channelId:
        type: integer
      reportType:
        enum:
        - compatibility
        - performance
        - compatibility_fingerprint
        - compatibility_v2
        type: string
      schema:
        items:
          type: string
        type: array
      taskId:
        type: integer
      title:
        type: string
    required:
    - channelId
    - reportType
    - schema
    - taskId
    type: object
  message.DownloadSqlReportBySchemaResp:
    properties:
      exportFilePath:
        type: string
    type: object
  message.DownloadSupplementalLogReq:
    properties:
      taskId:
        type: integer
    required:
    - taskId
    type: object
  message.DownloadSupplementalLogResp:
    properties:
      exportFilePath:
        type: string
    type: object
  message.DsgFileStat:
    properties:
      delayTime:
        type: number
      fileNo:
        type: integer
      nodeName:
        type: string
      notLoadNum:
        type: integer
      qno:
        type: integer
      realSpeed:
        type: number
      sizeDsg:
        type: integer
      usedTime:
        type: number
    type: object
  message.DsgOperationLog:
    properties:
      beginTime: {}
      businessType:
        type: integer
      endTime: {}
      errorMsg: {}
      id:
        type: integer
      jsonResult:
        type: string
      limit:
        type: integer
      offset:
        type: integer
      operationId:
        type: integer
      operationIp:
        type: string
      operationMethod:
        type: string
      operationName:
        type: string
      operationParam:
        type: string
      operationSource:
        type: string
      operationStatus:
        type: integer
      operationTime:
        type: string
      operationTitle:
        type: string
      operationUrl:
        type: string
      requestMethod:
        type: string
      roleName:
        type: string
      sortName: {}
      tmsOperationTitle:
        type: string
      userId: {}
      usergroupName:
        type: string
    type: object
  message.DsgSyncStat:
    properties:
      delaySecond:
        type: number
      initialSourceSyncTime:
        type: string
      initialSyncTime:
        type: string
      lastSourceSyncTime:
        type: string
      lastSyncTime:
        type: string
      lastTargetLoadSCNTime:
        type: string
      lastTargetLoadTime:
        type: string
      sourceCurrentScn:
        type: string
      sourceCurrentTimestamp:
        type: string
      sourceDatabaseScn:
        type: string
      sourceDatabaseTimestamp:
        type: string
    type: object
  message.DsgTable:
    properties:
      schemaNameS:
        type: string
      schemaNameT:
        type: string
      tableNameS:
        type: string
      tableNameT:
        type: string
    type: object
  message.DsgTablePerformance:
    properties:
      items:
        items:
          $ref: '#/definitions/message.DsgTablePerformanceItem'
        type: array
      schemaName:
        type: string
      tableName:
        type: string
    type: object
  message.DsgTablePerformanceItem:
    properties:
      ddlNum:
        type: integer
      deleteNum:
        type: integer
      insertNum:
        type: integer
      intervalMinute:
        type: string
      totalChangeNum:
        type: integer
      updateNum:
        type: integer
    type: object
  message.DsgTableSummary:
    properties:
      schemaName:
        type: string
      tableName:
        type: string
      totalChangeNum:
        type: integer
      totalDdlNum:
        type: integer
      totalDeleteNum:
        type: integer
      totalInsertNum:
        type: integer
      totalUpdateNum:
        type: integer
    type: object
  message.DsgTableTopPerformance:
    properties:
      item:
        $ref: '#/definitions/message.DsgTablePerformanceItem'
      schemaName:
        type: string
      tableName:
        type: string
    type: object
  message.DsgTask:
    properties:
      analysisPort:
        type: string
      delStatus:
        type: string
      installStatus:
        type: string
      receivePort:
        type: string
      sourPath:
        type: string
      tarPath:
        type: string
      taskId:
        type: integer
      taskMethod:
        type: string
      taskModel:
        type: string
      taskName:
        type: string
      taskParam:
        type: string
      taskStatus:
        type: string
      taskType:
        type: string
    type: object
  message.DsgTaskTime:
    properties:
      installBinaryTime:
        type: string
      pauseMigrateTime:
        type: string
      resumeMigrateTime:
        type: string
      startBinaryTime:
        type: string
      startMigrateTime:
        type: string
      stopBinaryTime:
        type: string
      stopMigrateTime:
        type: string
    type: object
  message.EnvDeployTask:
    properties:
      SQLModel:
        type: string
      comment:
        type: string
      dataCompareEnvDeployId:
        type: integer
      isIgnore:
        type: string
      lastRunTime:
        type: string
      taskId:
        type: integer
      taskLog:
        type: string
      taskName:
        type: string
      taskNumber:
        type: integer
      taskSQL:
        type: string
      taskStatus:
        type: integer
    type: object
  message.EvaluateContextItem:
    properties:
      featureKey:
        type: string
      featureType:
        type: string
      score:
        type: integer
    type: object
  message.ExecuteDataCompareEnvDeployTaskReq:
    properties:
      envDeployIds:
        items:
          type: integer
        type: array
      runMode:
        type: integer
    type: object
  message.ExecuteDataCompareEnvDeployTaskResp:
    type: object
  message.ExecuteSQLAnalyzeEnvDeployTaskReq:
    properties:
      runMode:
        type: integer
      sqlAnalyzeIds:
        items:
          type: integer
        type: array
      taskModeInt:
        type: integer
    type: object
  message.ExecuteSQLAnalyzeEnvDeployTaskResp:
    type: object
  message.ExecutionPreCheckReq:
    properties:
      precheckInfors:
        items:
          $ref: '#/definitions/message.PrecheckInfo'
        type: array
    required:
    - precheckInfors
    type: object
  message.ExecutionPreCheckResp:
    type: object
  message.ExportSourceTableColumnsToCSVReq:
    properties:
      channelId:
        type: integer
    type: object
  message.ExportSourceTableColumnsToCSVResp:
    properties:
      exportCSVPath:
        type: string
    type: object
  message.FetchChunkDataReq:
    properties:
      schemaNameS:
        type: string
      tableNameS:
        type: string
      taskId:
        type: integer
    required:
    - schemaNameS
    - tableNameS
    - taskId
    type: object
  message.FetchChunkDataResp:
    type: object
  message.FixSQLFileInfo:
    properties:
      file_path:
        description: 文件路径
        type: string
      file_size:
        description: 文件大小
        type: integer
      filename:
        description: 文件名
        type: string
      mod_time:
        description: 修改时间
        type: string
      schema:
        description: Schema名称
        type: string
      table:
        description: 表名
        type: string
    type: object
  message.GenKeyResp:
    properties:
      timsKey:
        type: string
    type: object
  message.GetActiveTmsSessionsResp:
    properties:
      sessions:
        items:
          $ref: '#/definitions/message.ActiveTmsSession'
        type: array
    type: object
  message.GetArchiveDataResp:
    properties:
      dayData:
        items:
          $ref: '#/definitions/message.Datum'
        type: array
      graphData:
        $ref: '#/definitions/message.GraphData'
      hourData:
        items:
          $ref: '#/definitions/message.Datum'
        type: array
    type: object
  message.GetArchiveTimesResp:
    properties:
      dayData:
        items:
          $ref: '#/definitions/message.Datum'
        type: array
      graphData:
        $ref: '#/definitions/message.GraphData'
      hourData:
        items:
          $ref: '#/definitions/message.Datum'
        type: array
    type: object
  message.GetChannelResp:
    properties:
      channelId:
        type: integer
      channelMode:
        maxLength: 10
        type: string
      channelName:
        maxLength: 100
        type: string
      channelType:
        enum:
        - O2T
        - M2T
        - C2T
        - O2M
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dataCompare:
        enum:
        - "Y"
        - "N"
        type: string
      dataCompareEnvStatus:
        maxLength: 10
        type: string
      datasourceIdS:
        type: integer
      datasourceIdT:
        type: integer
      datasourceNameS:
        maxLength: 100
        type: string
      datasourceNameT:
        maxLength: 100
        type: string
      increment:
        enum:
        - "Y"
        - "N"
        type: string
      migrateCsvData:
        enum:
        - "Y"
        - "N"
        type: string
      migrateFullData:
        enum:
        - "Y"
        - "N"
        type: string
      migrateStructure:
        enum:
        - "Y"
        - "N"
        type: string
      objAssessment:
        enum:
        - "Y"
        - "N"
        type: string
      objParser:
        type: string
      sqlAnalyzerEnvStatus:
        maxLength: 10
        type: string
      sqlAssessment:
        description: ObjParser            string `json:"objParser" validate:"required,oneof=Y
          N" enums:"Y,N"`
        enum:
        - "Y"
        - "N"
        type: string
      taskCreated:
        enum:
        - "Y"
        - "N"
        maxLength: 1
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelName
    - channelType
    - dataCompare
    - datasourceIdS
    - datasourceIdT
    - increment
    - migrateCsvData
    - migrateFullData
    - migrateStructure
    - objAssessment
    - sqlAssessment
    type: object
  message.GetChannelSchemasResp:
    properties:
      channelSchemas:
        items:
          $ref: '#/definitions/message.ChannelSchema'
        type: array
    type: object
  message.GetChunkDataSummaryReq:
    properties:
      schemaNameS:
        type: string
      tableNameS:
        type: string
      taskId:
        type: integer
    required:
    - schemaNameS
    - tableNameS
    - taskId
    type: object
  message.GetChunkDataSummaryResp:
    properties:
      rowIdCount:
        type: integer
      schemaNameS:
        type: string
      status:
        type: string
      tableNameS:
        type: string
      taskID:
        type: integer
    type: object
  message.GetColDefaultMapRuleByIdResp:
    properties:
      colDefaultMapRuleId:
        type: integer
      colTypeDefaultValueS:
        maxLength: 50
        minLength: 0
        type: string
      colTypeDefaultValueT:
        maxLength: 50
        minLength: 0
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isEquivalent:
        enum:
        - "Y"
        - "N"
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isEquivalent
    type: object
  message.GetColumnNamesReq:
    properties:
      schemaNames:
        items:
          type: string
        type: array
      tableNames:
        items:
          type: string
        type: array
    type: object
  message.GetColumnNamesResp:
    properties:
      columnNames:
        items:
          $ref: '#/definitions/structs.ColumnName'
        type: array
    type: object
  message.GetConfigurationReq:
    type: object
  message.GetConfigurationResp:
    properties:
      SQLFactor:
        $ref: '#/definitions/message.SQLFactor'
      appSQLsFilename:
        type: string
      appSQLsSource:
        type: string
      explainOnly:
        type: string
      ignoreSQLIDsFilename:
        type: string
      sqlFileType:
        type: string
    type: object
  message.GetDataSourceSchemaTablesReq:
    properties:
      dataSourceId:
        type: integer
      schemas:
        items:
          type: string
        type: array
    type: object
  message.GetDataSourceSchemaTablesResp:
    properties:
      children:
        items:
          $ref: '#/definitions/structs.SchemaInfo'
        type: array
    type: object
  message.GetDataSourceSchemasResp:
    properties:
      children:
        items:
          $ref: '#/definitions/structs.SchemaInfo'
        type: array
    type: object
  message.GetDefinitionsFromMetadataResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.DefinitionAndDetail'
        type: array
    type: object
  message.GetDependencyFromMetadataResp:
    properties:
      dependencies:
        items:
          $ref: '#/definitions/structs.DependencyTreeNode'
        type: array
      displayGraph:
        $ref: '#/definitions/structs.DependencyGraphVO'
      displayTree:
        items:
          $ref: '#/definitions/structs.DependencyTreeVO'
        type: array
    type: object
  message.GetDsgTaskMonitorInfoResp:
    properties:
      dsgTaskName:
        type: string
      incrementId:
        type: integer
      message:
        type: string
      resourceInfo:
        type: string
      resourceInfoRate:
        $ref: '#/definitions/message.ResourceInfoRate'
      startMigrateTime:
        type: string
      statFlag:
        type: integer
      stopMigrateTime:
        type: string
      syncInfo:
        type: string
      syncStatus:
        type: string
    type: object
  message.GetDsgTaskOperationLogsResp:
    properties:
      logs:
        items:
          $ref: '#/definitions/message.DsgOperationLog'
        type: array
    type: object
  message.GetDsgTaskPerformanceStatDetailResp:
    properties:
      dsgTaskName:
        type: string
      incrementId:
        type: integer
      message:
        type: string
      statFlag:
        type: integer
      tablePerformances:
        items:
          $ref: '#/definitions/message.DsgTablePerformance'
        type: array
    type: object
  message.GetDsgTaskPerformanceStatResp:
    properties:
      dsgTaskName:
        type: string
      incrementId:
        type: integer
      message:
        type: string
      statFlag:
        type: integer
      tableSummaries:
        items:
          $ref: '#/definitions/message.DsgTableSummary'
        type: array
    type: object
  message.GetDsgTaskPerformanceStatTopResp:
    properties:
      dsgTaskName:
        type: string
      incrementId:
        type: integer
      message:
        type: string
      statFlag:
        type: integer
      tableTopPerformances:
        items:
          $ref: '#/definitions/message.DsgTableTopPerformance'
        type: array
    type: object
  message.GetDsgTaskResp:
    properties:
      dsgTask:
        $ref: '#/definitions/message.DsgTask'
      fileStat:
        $ref: '#/definitions/message.DsgFileStat'
      syncOption:
        $ref: '#/definitions/message.IncrementTaskSyncOption'
      syncStat:
        $ref: '#/definitions/message.DsgSyncStat'
      taskTime:
        $ref: '#/definitions/message.DsgTaskTime'
    type: object
  message.GetDsgTaskWarningInfoResp:
    properties:
      dsgTaskName:
        type: string
      incrementId:
        type: integer
      message:
        type: string
      statFlag:
        type: integer
      warningInfos:
        $ref: '#/definitions/message.WarningInfos'
    type: object
  message.GetFixSQLContentReq:
    properties:
      file_path:
        description: '相对路径，如: SCHEMA.TABLE.sql 或 subdir/SCHEMA.TABLE.sql'
        type: string
      filter:
        allOf:
        - $ref: '#/definitions/message.SQLContentFilter'
        description: 可选的内容过滤器
      page:
        description: 页码（默认1）
        type: integer
      page_size:
        description: 每页行数（默认1000，最大10000）
        type: integer
      task_id:
        type: integer
    required:
    - file_path
    - task_id
    type: object
  message.GetFixSQLContentResp:
    properties:
      file_path:
        type: string
      file_size:
        type: integer
      lines:
        description: 当前页的内容
        items:
          $ref: '#/definitions/message.SQLLine'
        type: array
      summary:
        allOf:
        - $ref: '#/definitions/message.SQLSummary'
        description: 文件统计摘要
      task_id:
        type: integer
    type: object
  message.GetHostUsedPortsReq:
    properties:
      hostIp:
        type: string
    required:
    - hostIp
    type: object
  message.GetHostUsedPortsResp:
    properties:
      message:
        type: string
      ports:
        items:
          $ref: '#/definitions/message.HostPort'
        type: array
      statFlag:
        type: integer
      usedPortMax:
        type: integer
      usedPortMin:
        type: integer
    type: object
  message.GetHotFileInformationResp:
    properties:
      files:
        description: 热文件信息列表
        items:
          $ref: '#/definitions/message.HotFileInfo'
        type: array
    type: object
  message.GetIncompatibleFeatureScoringResp:
    properties:
      histogram:
        $ref: '#/definitions/message.WeightedScoring'
      incompatibleFeature:
        $ref: '#/definitions/message.WeightedScoring'
      total:
        $ref: '#/definitions/message.WeightedScoring'
    type: object
  message.GetInstallDsgTaskStatusResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
      success:
        type: string
    type: object
  message.GetInstallationInfoResp:
    properties:
      autoMaticEngineBootPort:
        type: integer
      externalAutoMaticEngineBootPort:
        type: integer
      externalHost:
        type: string
      externalPort:
        type: integer
      host:
        type: string
      port:
        type: integer
      sourceIp:
        type: string
      targetIp:
        type: string
    type: object
  message.GetLicenseInfoResp:
    properties:
      expiredTime:
        type: integer
      remainingDays:
        type: integer
    type: object
  message.GetLogVolumePerSecondResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.TransactionDataBlock'
        type: array
      graphData:
        $ref: '#/definitions/message.GraphData'
    type: object
  message.GetMergedTaskParamListByTaskparamTemplateIdResp:
    properties:
      taskParams:
        items:
          $ref: '#/definitions/message.TaskParam'
        type: array
    type: object
  message.GetObjMapRuleByIdResp:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isCompatibility:
        enum:
        - "Y"
        - "N"
        type: string
      isConvertible:
        enum:
        - "Y"
        - "N"
        type: string
      objMapRuleId:
        type: integer
      objectType:
        maxLength: 100
        minLength: 1
        type: string
      objectTypeName:
        maxLength: 100
        minLength: 1
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isCompatibility
    - isConvertible
    - objectType
    - objectTypeName
    type: object
  message.GetObjectAssessResultDetailResp:
    properties:
      objectAssessResultDetail:
        $ref: '#/definitions/message.ObjectCompatibleALL'
    type: object
  message.GetObjectAssessResultSummaryBySchemaResp:
    properties:
      listGetObjectCompatibleCountBySchema:
        items:
          $ref: '#/definitions/message.GetObjectCompatibleCountBySchema'
        type: array
    type: object
  message.GetObjectAssessResultSummaryResp:
    properties:
      assessTotal:
        type: integer
      checkTime:
        type: string
      dbName:
        type: string
      listCompatibleCount:
        items:
          $ref: '#/definitions/message.GetObjectCompatibleCount'
        type: array
    type: object
  message.GetObjectCompatibleCount:
    properties:
      compatibleCount:
        type: integer
      compatiblePct:
        type: string
      compatibleType:
        type: string
    type: object
  message.GetObjectCompatibleCountBySchema:
    properties:
      assessTotal:
        type: string
      checkTime:
        type: string
      compatible:
        type: string
      convertible:
        type: string
      dbName:
        type: string
      inCompatible:
        type: string
      inConvertible:
        type: string
      inValid:
        type: string
      schema:
        type: string
    type: object
  message.GetObjectDetailResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.DefinitionAndDetail'
        type: array
    type: object
  message.GetOracleObjectDefinition:
    properties:
      dependencyObjectKey:
        type: string
      objectName:
        type: string
      objectPackageName:
        type: string
      objectSchema:
        type: string
      objectText:
        type: string
      objectTextHighlightLine:
        type: string
      objectType:
        type: string
    required:
    - objectName
    - objectSchema
    - objectType
    type: object
  message.GetPLSQLToJavaHistoryResultsResp:
    properties:
      javaCode:
        items:
          $ref: '#/definitions/message.OracleToJavaResult'
        type: array
    type: object
  message.GetPLSQLToJavaLogsResp:
    properties:
      logs:
        items:
          $ref: '#/definitions/message.OracleToJavaLog'
        type: array
    type: object
  message.GetPLSQLToJavaResultsResp:
    properties:
      javaCode:
        items:
          $ref: '#/definitions/message.OracleToJavaResult'
        type: array
    type: object
  message.GetPLSQLToJavaSummaryResp:
    properties:
      channelId:
        type: integer
      failedObjectNum:
        type: integer
      id:
        type: integer
      successObjectNum:
        type: integer
      taskId:
        type: integer
      totalObjectNum:
        type: integer
    type: object
  message.GetPhysicalFileIOStatsResp:
    properties:
      stats:
        items:
          $ref: '#/definitions/message.PhysicalFileIOStats'
        type: array
    type: object
  message.GetPreCheckInfoByIdResp:
    properties:
      channelId:
        type: integer
      checkIgnore:
        enum:
        - Y(ignored)
        - N(not ignored)
        type: string
      checkObject:
        enum:
        - A(object compatible check priority)
        - B(object migration priority)
        - C(data migration priority)
        - D(data compare priority)
        - E(target privilege check)
        - F(no index table)
        - G(no pk/uk table)
        - H(table have dependency relation)
        type: string
      checkResultInfor:
        maxLength: 1000
        type: string
      checkStatus:
        enum:
        - C(checking)
        - P(pass)
        - N(not pass)
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      precheckId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - checkIgnore
    - checkObject
    - checkStatus
    type: object
  message.GetPreCheckInfosByChannelIdResp:
    properties:
      precheckInfors:
        items:
          $ref: '#/definitions/message.PrecheckInfo'
        type: array
    type: object
  message.GetSQLSetStatementsResp:
    properties:
      statements:
        items:
          $ref: '#/definitions/message.SQLSetStatement'
        type: array
    type: object
  message.GetSQLSetsResp:
    properties:
      sqlSets:
        items:
          $ref: '#/definitions/message.SQLSet'
        type: array
    type: object
  message.GetSelectedTaskSchemaTablesReq:
    properties:
      channelId:
        type: integer
      clusterTypeT:
        items:
          type: string
        type: array
      orderKeys:
        items:
          type: string
        type: array
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      partitioningTypeS:
        items:
          type: string
        type: array
      partitioningTypeT:
        items:
          type: string
        type: array
      pks:
        type: string
      pkt:
        type: string
      schemaNames:
        items:
          type: string
        type: array
      tableNamePrefix:
        type: string
      taskId:
        type: integer
      uks:
        type: string
    required:
    - page
    - pageSize
    type: object
  message.GetSelectedTaskSchemaTablesResp:
    properties:
      tables:
        items:
          $ref: '#/definitions/message.ChannelSchemaTable'
        type: array
    type: object
  message.GetSourceConfigResp:
    properties:
      vm:
        type: string
    type: object
  message.GetSourceTableColumnsResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.TableColumnCustomMapRule'
        type: array
    type: object
  message.GetSqlMapRuleByIdResp:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isCompatibility:
        enum:
        - "Y"
        - "N"
        type: string
      isConvertible:
        enum:
        - "Y"
        - "N"
        type: string
      keywords:
        maxLength: 20
        minLength: 1
        type: string
      sqlMapRuleId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isCompatibility
    - isConvertible
    - keywords
    type: object
  message.GetSqlResultHistoryReq:
    properties:
      oraSqlId:
        description: Primary identifier
        type: string
      sqlExecId:
        description: 'Optional: for backwards compatibility'
        type: integer
      taskId:
        type: integer
    type: object
  message.GetSqlResultHistoryResp:
    properties:
      history:
        items:
          $ref: '#/definitions/message.SqlResultHistoryItem'
        type: array
    type: object
  message.GetSqlResultStatisticsResp:
    properties:
      statistics:
        additionalProperties:
          type: integer
        type: object
    type: object
  message.GetSyncSourceTableColumnsStatusResp:
    properties:
      channelId:
        type: integer
      finishedColumnNum:
        type: integer
      finishedTableNum:
        type: integer
      status:
        type: string
      tableProgress:
        type: number
      totalTableNum:
        type: integer
    type: object
  message.GetTabColMapRuleResp:
    properties:
      colTypeNameS:
        maxLength: 100
        minLength: 1
        type: string
      colTypeNameT:
        maxLength: 100
        minLength: 1
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isEquivalent:
        enum:
        - "Y"
        - "N"
        type: string
      tabColMapRuleId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - colTypeNameS
    - colTypeNameT
    - dbTypeS
    - dbTypeT
    - isEquivalent
    type: object
  message.GetTabcolCustMapRulesReq:
    properties:
      channelId:
        type: integer
      ruleType:
        type: integer
      taskId:
        type: integer
    type: object
  message.GetTableMappingResp:
    properties:
      define:
        $ref: '#/definitions/message.DSGTableDefine'
      mappings:
        items:
          $ref: '#/definitions/message.DsgTable'
        type: array
    type: object
  message.GetTargetConfigResp:
    properties:
      tmsYLoaderWrapper:
        $ref: '#/definitions/message.YLoaderWrapper'
      yloader:
        $ref: '#/definitions/message.YLoader'
      yloader_content:
        type: string
    type: object
  message.GetTaskDeployStatusResp:
    properties:
      isDeployed:
        type: boolean
    type: object
  message.GetTaskDetailResultReq:
    properties:
      channelId:
        type: integer
      taskId:
        type: integer
    type: object
  message.GetTaskDetailResultResp:
    properties:
      dbName:
        type: string
      startTime:
        type: string
      taskDetailChartDataLst:
        items:
          $ref: '#/definitions/message.TaskDetailChartData'
        type: array
      taskDetailSchemaData:
        $ref: '#/definitions/message.TaskDetailSchemaData'
      taskId:
        type: integer
      totalDuration:
        type: string
      totalTables:
        type: integer
    type: object
  message.GetTaskInfoAndChannelSchemaObjectByIdResp:
    properties:
      channelSchemaObject:
        $ref: '#/definitions/message.ChannelSchemaObject'
      task:
        $ref: '#/definitions/message.Task'
    type: object
  message.GetTaskParamTemplateListByTaskTypeResp:
    properties:
      taskParamTemplateList:
        items:
          $ref: '#/definitions/message.TaskParamTemplate'
        type: array
    type: object
  message.GetTaskParamTemplateReq:
    properties:
      taskparamTemplateID:
        type: integer
    type: object
  message.GetTaskParamTemplateResp:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      defaultTag:
        enum:
        - "Y"
        - "N"
        type: string
      paramTemplateName:
        maxLength: 100
        minLength: 1
        type: string
      taskParamTemplateID:
        type: integer
      taskType:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      templateType:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - defaultTag
    - paramTemplateName
    - taskType
    type: object
  message.GetTaskProgressReq:
    properties:
      channelId:
        type: integer
      startTime:
        type: string
      taskId:
        type: integer
    type: object
  message.GetTaskProgressResp:
    properties:
      failedNums:
        type: integer
      incompatibleNums:
        type: integer
      lastUpdateTime:
        type: string
      otherFiles:
        additionalProperties:
          type: string
        type: object
      partialSuccessNums:
        type: integer
      progress:
        type: number
      progressLog:
        $ref: '#/definitions/message.TaskProgressLogInfo'
      runningChunkInfo:
        $ref: '#/definitions/message.TaskProgressChunkInfo'
      runningChunkNums:
        type: integer
      runningNums:
        type: integer
      schemaNotMatchNums:
        type: integer
      startTime:
        type: string
      successChunkNums:
        type: integer
      successNums:
        type: integer
      taskId:
        type: integer
      taskLogFile:
        type: string
      timeoutNums:
        type: integer
      totalChunkNums:
        type: integer
      totalDuration:
        type: string
      totalNums:
        type: integer
    type: object
  message.GetTaskTableConfigsByTaskIdAndChannelIdResp:
    properties:
      taskTableConfigs:
        items:
          $ref: '#/definitions/message.TaskTableConfig'
        type: array
    type: object
  message.GetTidbStatsTableCountReq:
    properties:
      channelId:
        type: integer
      statsTables:
        items:
          $ref: '#/definitions/message.StatsTable'
        type: array
      taskId:
        type: integer
    type: object
  message.GetTidbStatsTableCountResp:
    type: object
  message.GetTmsSessionProgressResp:
    properties:
      progress:
        items:
          $ref: '#/definitions/message.TmsSessionProgress'
        type: array
    type: object
  message.GetTmsSessionRatioResp:
    properties:
      tmsPct:
        type: number
    type: object
  message.GetTokenResp:
    properties:
      token:
        type: string
    type: object
  message.GetTransactionDataBlocksResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.TransactionDataBlock'
        type: array
      graphData:
        $ref: '#/definitions/message.GraphData'
    type: object
  message.GetTransactionLogVolumeResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.TransactionDataBlock'
        type: array
      graphData:
        $ref: '#/definitions/message.GraphData'
    type: object
  message.GetTransactionPerSecondResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.TransactionDataBlock'
        type: array
      graphData:
        $ref: '#/definitions/message.GraphData'
    type: object
  message.GetUnSelectedTaskSchemaTablesReq:
    properties:
      channelId:
        type: integer
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      schemaNames:
        items:
          type: string
        type: array
      tableNamePrefix:
        type: string
      taskType:
        type: integer
    required:
    - page
    - pageSize
    - taskType
    type: object
  message.GetUnSelectedTaskSchemaTablesResp:
    properties:
      tables:
        items:
          $ref: '#/definitions/message.ChannelSchemaTable'
        type: array
    type: object
  message.GetUnselectedDataSourceSchemasResp:
    properties:
      children:
        items:
          $ref: '#/definitions/structs.SchemaInfo'
        type: array
    type: object
  message.GraphData:
    properties:
      times:
        items:
          type: string
        type: array
      titles:
        items:
          type: string
        type: array
      values:
        items:
          items:
            type: number
          type: array
        type: array
    type: object
  message.HistogramOptions:
    properties:
      archiveData:
        type: boolean
      archiveTimes:
        type: boolean
      logVolumePerSecond:
        type: boolean
      transactionDataBlocks:
        type: boolean
      transactionLogVolume:
        type: boolean
      transactionPerSecond:
        type: boolean
    type: object
  message.HostPort:
    properties:
      describe:
        type: string
      notes:
        type: string
      port:
        type: integer
    type: object
  message.HotFileInfo:
    properties:
      cs:
        description: 单块读平均耗时（毫秒）
        type: number
      fileName:
        description: 文件名
        type: string
      readTimeS:
        description: 读操作总耗时（秒）
        type: number
      tablespaceName:
        description: 表空间名
        type: string
      writeTimeS:
        description: 写操作总耗时（秒）
        type: number
    type: object
  message.ImportSourceTableColumnByCSVResp:
    properties:
      columnNum:
        type: integer
      tableNum:
        type: integer
      updatedColumnNum:
        type: integer
    type: object
  message.IncompatibleFeature:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      featureDesc:
        type: string
      featureKey:
        type: string
      featureScore:
        type: integer
      featureType:
        type: string
      id:
        type: integer
      taskId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    type: object
  message.IncrementTaskSyncOption:
    properties:
      dsgTaskName:
        type: string
      scnType:
        type: string
      scnUserInputValue:
        type: string
      scnValue:
        type: string
      taskId:
        type: integer
      timeValue:
        type: string
    type: object
  message.InitTidbStatisticsReq:
    properties:
      channelId:
        type: integer
      taskId:
        type: integer
    type: object
  message.InitTidbStatisticsResp:
    type: object
  message.InstallDsgTaskResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.Leader:
    properties:
      addr:
        type: string
      message:
        type: string
      name:
        type: string
    type: object
  message.ListAllDsgTaskResp:
    properties:
      tasks:
        items:
          $ref: '#/definitions/message.DsgTask'
        type: array
      total:
        type: integer
    type: object
  message.ListAnalyzeDetailResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.OracleObjectDefinitionAnalyzeDetail'
        type: array
    type: object
  message.ListBasicIncompatibleFeatureResp:
    properties:
      features:
        items:
          $ref: '#/definitions/message.IncompatibleFeature'
        type: array
    type: object
  message.ListChannelsReq:
    properties:
      channelName:
        maxLength: 100
        minLength: 1
        type: string
      channelType:
        enum:
        - O2T
        - M2T
        - C2T
        - O2M
        type: string
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
    required:
    - page
    - pageSize
    type: object
  message.ListChunkDataReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      rowExecStatus:
        type: string
      schemaNameS:
        type: string
      tableNameS:
        type: string
      taskId:
        type: integer
    required:
    - page
    - pageSize
    - schemaNameS
    - tableNameS
    - taskId
    type: object
  message.ListChunkDataResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.ChunkDataAnalyze'
        type: array
    type: object
  message.ListColDefaultMapRulesReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      templateId:
        type: integer
    required:
    - page
    - pageSize
    - templateId
    type: object
  message.ListColDefaultMapRulesResp:
    properties:
      colDefaultMapRules:
        items:
          $ref: '#/definitions/message.ColDefaultMapRule'
        type: array
      templateId:
        type: integer
    type: object
  message.ListDataCompareEnvDeployTaskResp:
    properties:
      tasks:
        items:
          $ref: '#/definitions/message.EnvDeployTask'
        type: array
    type: object
  message.ListDataSourceHostReq:
    properties:
      hostIp:
        type: string
      hostName:
        type: string
      osName:
        type: string
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
    required:
    - page
    - pageSize
    type: object
  message.ListDataSourcesReq:
    properties:
      connectionStatus:
        enum:
        - Y(success)
        - N(failed)
        - S(not test)
        type: string
      dataSourceName:
        maxLength: 100
        minLength: 1
        type: string
      dbType:
        enum:
        - oracle
        - mysql
        - tidb
        - tidb-proxy
        - oracle-adg csv
        type: string
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
    required:
    - page
    - pageSize
    type: object
  message.ListFixSQLReq:
    properties:
      page:
        description: 页码
        type: integer
      pageSize:
        description: 每页大小
        type: integer
      schema_name_s:
        description: 过滤的Schema列表
        items:
          type: string
        type: array
      table_name:
        description: 过滤的表名
        type: string
      task_id:
        description: 任务ID
        type: integer
    type: object
  message.ListFixSQLResp:
    properties:
      sql_files:
        items:
          $ref: '#/definitions/message.FixSQLFileInfo'
        type: array
      task_id:
        type: integer
    type: object
  message.ListLicenseFeaturesReq:
    type: object
  message.ListMembersResp:
    properties:
      leader:
        $ref: '#/definitions/message.Leader'
      masters:
        items:
          $ref: '#/definitions/message.Master'
        type: array
      msg:
        type: string
      result:
        type: boolean
      workers:
        items:
          $ref: '#/definitions/message.Worker'
        type: array
    type: object
  message.ListObjMapRulesReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      templateId:
        type: integer
    required:
    - page
    - pageSize
    - templateId
    type: object
  message.ListObjMapRulesResp:
    properties:
      objMapRules:
        items:
          $ref: '#/definitions/message.ObjMapRule'
        type: array
      templateId:
        type: integer
    type: object
  message.ListPhysicalSubSystemResp:
    properties:
      physicalSubSystems:
        items:
          $ref: '#/definitions/message.PhysicalSubSystem'
        type: array
      total:
        type: integer
    type: object
  message.ListPromptResp:
    properties:
      prompts:
        items:
          $ref: '#/definitions/message.Prompt'
        type: array
    type: object
  message.ListSQLAnalyzeTaskJobsResp:
    properties:
      currentJob:
        $ref: '#/definitions/message.TaskDbmsJobStatus'
      historyJob:
        $ref: '#/definitions/message.TaskDbmsJobStatus'
      startJob:
        type: boolean
      stopJob:
        type: boolean
    type: object
  message.ListSQLAnalyzeTaskResp:
    properties:
      tasks:
        items:
          $ref: '#/definitions/message.SQLAnalyzerTask'
        type: array
    type: object
  message.ListSourcesResp:
    properties:
      msg:
        type: string
      result:
        type: boolean
      sources:
        items:
          $ref: '#/definitions/message.Source'
        type: array
    type: object
  message.ListSqlMapRulesReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      templateId:
        type: integer
    required:
    - page
    - pageSize
    - templateId
    type: object
  message.ListSqlMapRulesResp:
    properties:
      sqlMapRules:
        items:
          $ref: '#/definitions/message.SqlMapRule'
        type: array
      templateId:
        type: integer
    type: object
  message.ListSqlResultsWithPaginationResp:
    properties:
      current:
        type: integer
      pageSize:
        type: integer
      sqlResults:
        items:
          $ref: '#/definitions/message.SqlResultWithUserOperation'
        type: array
      total:
        type: integer
    type: object
  message.ListTabColMapRulesReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      templateId:
        type: integer
    required:
    - page
    - pageSize
    - templateId
    type: object
  message.ListTabColMapRulesResp:
    properties:
      tabColMapRules:
        items:
          $ref: '#/definitions/message.TabColMapRule'
        type: array
      templateId:
        type: integer
    type: object
  message.ListTaskIncompatibleFeatureResp:
    properties:
      features:
        items:
          $ref: '#/definitions/message.IncompatibleFeature'
        type: array
    type: object
  message.ListTaskObjectPromptRelationResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.PromptRelation'
        type: array
    type: object
  message.ListTaskParamTemplatesReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      paramTemplateName:
        type: string
    required:
    - page
    - pageSize
    type: object
  message.ListTaskParamTemplatesResp:
    properties:
      taskParamTemplates:
        items:
          $ref: '#/definitions/message.TaskParamTemplate'
        type: array
    type: object
  message.ListTasksByChannelIdResp:
    properties:
      tasks:
        items:
          $ref: '#/definitions/message.Task'
        type: array
    type: object
  message.ListTemplateParamDetailsReq:
    properties:
      taskparamTemplateId:
        type: integer
    type: object
  message.ListTemplateParamDetailsResp:
    properties:
      params:
        items:
          $ref: '#/definitions/message.ParamTemplateDetail'
        type: array
      total:
        type: integer
    type: object
  message.ListTemplatesReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      templateType:
        enum:
        - object_map
        - sql_map
        - col_default_map
        - tab_col_map
        type: string
    required:
    - page
    - pageSize
    - templateType
    type: object
  message.ListTemplatesResp:
    properties:
      templates:
        items:
          $ref: '#/definitions/message.TemplateInfo'
        type: array
    type: object
  message.LoginReq:
    properties:
      autoLogin:
        type: boolean
      password:
        type: string
      type:
        type: string
      username:
        type: string
    type: object
  message.LoginResp:
    properties:
      currentAuthority:
        type: string
      status:
        type: string
      type:
        type: string
    type: object
  message.Master:
    properties:
      alive:
        type: boolean
      clientURLs:
        items:
          type: string
        type: array
      memberID:
        type: integer
      name:
        type: string
      peerURLs:
        items:
          type: string
        type: array
    type: object
  message.MethodInvoke:
    properties:
      count:
        type: integer
      funcName:
        type: string
      isOwnerInReservedWord:
        type: boolean
      ownerName:
        type: string
      parents:
        items:
          $ref: '#/definitions/message.SQLParent'
        type: array
    type: object
  message.MigrationDDLDownloadReq:
    properties:
      schema_name_s:
        items:
          type: string
        type: array
      task_id:
        type: integer
    type: object
  message.MigrationDDLReRunReq:
    properties:
      fixed_ddl:
        description: request used for manual adjust ddl execute
        type: string
      is_compatible:
        type: string
      reverse_ddl:
        description: request used for manual adjust ddl execute
        type: string
      schema_name_s:
        type: string
      table_name_s:
        description: request used for get ddl detail page
        type: string
      table_type_s:
        type: string
      task_id:
        type: integer
      task_run_id:
        type: integer
    type: object
  message.MigrationDataBatchRetryReq:
    properties:
      query_status:
        enum:
        - SUCCESS
        - FAILED
        - WAITING
        type: string
      schema_name_s:
        type: string
      table_id:
        description: 其实这里是table_detail_summaries的PK ID
        items:
          type: integer
        type: array
      task_id:
        type: integer
    type: object
  message.MigrationDataChunkErrorReq:
    properties:
      id:
        type: integer
      schema_name_s:
        type: string
      table_name_s:
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationDataChunkErrorResp:
    properties:
      migration_data_failed_chunk_error_detail:
        items:
          $ref: '#/definitions/message.MigrationDataTableFailedAndWaitingChunkDetail'
        type: array
    type: object
  message.MigrationDataChunkFailedAndWaitingReq:
    properties:
      schema_name_s:
        type: string
      table_name_s:
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationDataChunkFailedAndWaitingResp:
    properties:
      migration_data_failed_and_waiting_chunk_detail:
        items:
          $ref: '#/definitions/message.MigrationDataTableFailedAndWaitingChunkDetail'
        type: array
    type: object
  message.MigrationDataFailedChunkBatchRetryReq:
    properties:
      schema_name_s:
        type: string
      table_name_s:
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationDataFailedChunkRetryReq:
    properties:
      chunk_id:
        items:
          type: integer
        type: array
      schema_name_s:
        type: string
      table_name_s:
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationDataProgressResp:
    properties:
      failed_chunk_nums:
        type: integer
      failed_migration_nums:
        type: integer
      failed_task_nums:
        type: integer
      lastUpdateTime:
        type: string
      progress_log:
        items:
          $ref: '#/definitions/message.MigrationLogDetail'
        type: array
      progress_start_time:
        type: string
      running_chunk_nums:
        type: integer
      running_migration_nums:
        type: integer
      running_task_nums:
        type: integer
      success_chunk_nums:
        type: integer
      success_migration_nums:
        type: integer
      success_task_nums:
        type: integer
      task_id:
        type: integer
      total_chunk_nums:
        type: integer
      total_migration_nums:
        type: integer
      total_task_nums:
        type: integer
    type: object
  message.MigrationDataSchemaDetail:
    properties:
      chunk_size:
        type: integer
      failed_chunks:
        type: integer
      id:
        type: integer
      is_csv_export_finished:
        type: boolean
      is_csv_import_finished:
        type: boolean
      lightning_import_progress:
        type: number
      migration_duration:
        type: string
      migration_message:
        type: string
      migration_result:
        type: string
      result:
        type: string
      running_chunks:
        type: integer
      schema_name_s:
        type: string
      service_name:
        type: string
      success_chunks:
        type: integer
      table_name_s:
        type: string
      table_type_s:
        type: string
      total_chunks:
        type: integer
      waiting_chunks:
        type: integer
    type: object
  message.MigrationDataSchemaDetailResp:
    properties:
      migration_data_schema_detail:
        items:
          $ref: '#/definitions/message.MigrationDataSchemaDetail'
        type: array
    type: object
  message.MigrationDataSchemaReq:
    properties:
      query_status:
        enum:
        - SUCCESS
        - FAILED
        - WAITING
        type: string
      schema_name_s:
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationDataSchemaSummaryResp:
    properties:
      migration_schema_summary:
        items:
          $ref: '#/definitions/message.MigrationDataTaskSummaryDetail'
        type: array
    type: object
  message.MigrationDataStatisticResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.SchemaTableWithCSV'
        type: array
    type: object
  message.MigrationDataTableFailedAndWaitingChunkDetail:
    properties:
      chunk_detail:
        type: string
      error_detail:
        type: string
      schema_name_s:
        type: string
      table_name_s:
        type: string
      task_id:
        type: integer
      task_status:
        type: string
    type: object
  message.MigrationDataTaskSummaryDetail:
    properties:
      failed_nums:
        type: integer
      migration_duration:
        type: string
      migration_nums:
        type: integer
      migration_result:
        type: string
      running_nums:
        type: integer
      schema_name:
        type: string
      service_name:
        type: string
      success_nums:
        type: integer
      success_ratio:
        type: string
      total_table_nums:
        type: integer
      waiting_nums:
        type: integer
    type: object
  message.MigrationDataTaskSummaryResp:
    properties:
      failed_migration_nums:
        type: integer
      migration_details:
        items:
          $ref: '#/definitions/message.MigrationDataTaskSummaryDetail'
        type: array
      running_migration_nums:
        type: integer
      success_migration_nums:
        type: integer
      task_id:
        type: integer
      total_migration_nums:
        type: integer
      total_table_nums:
        type: integer
      waiting_migration_nums:
        type: integer
    type: object
  message.MigrationLogDetail:
    properties:
      channel_id:
        type: integer
      comment:
        type: string
      created_at:
        type: string
      detail:
        type: string
      log_level:
        type: string
      progress_id:
        type: integer
      schema_name_s:
        type: string
      table_name_s:
        type: string
      task_id:
        type: integer
      updated_at:
        type: string
    type: object
  message.MigrationProgressReq:
    properties:
      channel_id:
        type: integer
      progress_start_time:
        description: request used for reverse progress bar and log
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationSchemaPageReq:
    properties:
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      schema_name_s:
        type: string
      status:
        type: integer
      table_name_s:
        description: request used for get ddl detail page
        type: string
      table_type_s:
        description: request used for get ddl detail page
        type: string
      task_id:
        type: integer
    required:
    - page
    - pageSize
    type: object
  message.MigrationSchemaReq:
    properties:
      schema_name_s:
        type: string
      table_name_s:
        description: request used for get ddl detail page
        type: string
      table_type_s:
        description: request used for get ddl detail page
        type: string
      task_id:
        type: integer
    type: object
  message.MigrationStructureDDLDetail:
    properties:
      incompatible_ddl:
        type: string
      reverse_ddl:
        type: string
      schema_name_s:
        type: string
    type: object
  message.MigrationStructureDDLDetailResp:
    properties:
      schema_ddls:
        items:
          $ref: '#/definitions/message.MigrationStructureDDLDetail'
        type: array
    type: object
  message.MigrationStructureDDLProgressResp:
    properties:
      failed_migration_nums:
        type: integer
      incompatible_migration_nums:
        type: integer
      lastUpdateTime:
        type: string
      partial_success_migration_nums:
        type: integer
      progress_log:
        items:
          $ref: '#/definitions/message.MigrationLogDetail'
        type: array
      progress_start_time:
        type: string
      running_migration_nums:
        type: integer
      success_migration_nums:
        type: integer
      task_id:
        type: integer
      total_migration_nums:
        type: integer
      waiting_migration_nums:
        type: integer
    type: object
  message.MigrationStructureDDLReRunResp:
    properties:
      fixed_ddl:
        type: string
      task_name:
        type: string
    type: object
  message.MigrationStructureSchemaSummary:
    properties:
      compatible_nums:
        type: integer
      failed_migration_nums:
        type: integer
      partial_success_migration_nums:
        type: integer
      running_migration_nums:
        type: integer
      schema_name_s:
        type: string
      service_name:
        type: string
      success_migration_nums:
        type: integer
      table_type_s:
        type: string
      table_type_t:
        type: string
      total_migration_nums:
        type: integer
      waiting_migration_nums:
        type: integer
    type: object
  message.MigrationStructureSchemaSummaryResp:
    properties:
      migration_schema_summary:
        items:
          $ref: '#/definitions/message.MigrationStructureSchemaSummary'
        type: array
    type: object
  message.MigrationStructureTableDetail:
    properties:
      comment:
        type: string
      created_at:
        type: string
      error_detail:
        type: string
      failed_count:
        type: integer
      fixed_ddl:
        type: string
      incompatible_count:
        type: integer
      object_name_s:
        type: string
      origin_sql:
        type: string
      reverse_sql:
        type: string
      schema_name_s:
        type: string
      schema_name_t:
        type: string
      service_name_s:
        type: string
      status:
        type: string
      success_count:
        type: integer
      table_name_s:
        type: string
      table_name_t:
        type: string
      table_type_s:
        type: string
      table_type_t:
        type: string
      task_run_id:
        type: integer
      updated_at:
        type: string
    type: object
  message.MigrationStructureTableDetailResp:
    properties:
      migration_structure_table_detail:
        items:
          $ref: '#/definitions/message.MigrationStructureTableDetail'
        type: array
    type: object
  message.MigrationStructureTaskSummaryDetail:
    properties:
      compatible_nums:
        type: integer
      failed_nums:
        type: integer
      migration_duration:
        type: string
      migration_nums:
        type: integer
      partial_success_nums:
        type: integer
      running_nums:
        type: integer
      schema_name:
        type: string
      service_name:
        type: string
      success_nums:
        type: integer
      success_ratio:
        type: string
      waiting_nums:
        type: integer
    type: object
  message.MigrationStructureTaskSummaryResp:
    properties:
      compatible_nums:
        type: integer
      failed_migration_nums:
        type: integer
      migration_details:
        items:
          $ref: '#/definitions/message.MigrationStructureTaskSummaryDetail'
        type: array
      running_migration_nums:
        type: integer
      success_migration_nums:
        type: integer
      task_id:
        type: integer
      total_migration_nums:
        type: integer
      waiting_migration_nums:
        type: integer
    type: object
  message.MigrationTaskReq:
    properties:
      task_id:
        type: integer
    type: object
  message.ObjMapRule:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isCompatibility:
        enum:
        - "Y"
        - "N"
        type: string
      isConvertible:
        enum:
        - "Y"
        - "N"
        type: string
      objMapRuleId:
        type: integer
      objectType:
        maxLength: 100
        minLength: 1
        type: string
      objectTypeName:
        maxLength: 100
        minLength: 1
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isCompatibility
    - isConvertible
    - objectType
    - objectTypeName
    type: object
  message.ObjectCompatibleALL:
    properties:
      listAllObjects:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listAllPartTables:
        items:
          $ref: '#/definitions/message.OraAllPartTables'
        type: array
      listDataBaseLink:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listDbaTabColumnsDefault:
        items:
          $ref: '#/definitions/message.OraDbaTabColumnsDefault'
        type: array
      listDbaTables:
        items:
          $ref: '#/definitions/message.OraDbaTables'
        type: array
      listFunction:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listFunctionBodyCode:
        items:
          $ref: '#/definitions/message.OraObjectCode'
        type: array
      listIndexSubpartition:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listJob:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listLobPartition:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listLobSubpartition:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listLobTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listObjectCompatibleByObject:
        items:
          $ref: '#/definitions/message.ObjectCompatibleByObject'
        type: array
      listObjectCompatibleBySchema:
        items:
          $ref: '#/definitions/message.ObjectCompatibleBySchema'
        type: array
      listObjectCompatibleBySchemaObject:
        items:
          $ref: '#/definitions/message.ObjectCompatibleBySchemaObject'
        type: array
      listObjectCompatibleOverview:
        items:
          $ref: '#/definitions/message.ObjectCompatibleOverview'
        type: array
      listOraDbaConstraint:
        items:
          $ref: '#/definitions/message.OraDbaConstraint'
        type: array
      listOraDbaIndex:
        items:
          $ref: '#/definitions/message.OraDbaIndex'
        type: array
      listOraDbaSequence:
        items:
          $ref: '#/definitions/message.OraDbaSequence'
        type: array
      listOraDbaTabColumns:
        items:
          $ref: '#/definitions/message.OraDbaTabColumns'
        type: array
      listOraDbaViews:
        items:
          $ref: '#/definitions/message.OraDbaViews'
        type: array
      listOraLobs:
        items:
          $ref: '#/definitions/message.OraLobs'
        type: array
      listOraSubPartitioning:
        items:
          $ref: '#/definitions/message.OraSubPartitioning'
        type: array
      listOraTableTypeCompatibles:
        items:
          $ref: '#/definitions/message.OraTableTypeCompatibles'
        type: array
      listOracleOverview:
        items:
          $ref: '#/definitions/message.OracleOverview'
        type: array
      listPackage:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listPackageBody:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listPackageBodyCode:
        items:
          $ref: '#/definitions/message.OraObjectCode'
        type: array
      listProcedure:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listProcedureBodyCode:
        items:
          $ref: '#/definitions/message.OraObjectCode'
        type: array
      listSchemaActiveSession:
        items:
          $ref: '#/definitions/message.SchemaActiveSession'
        type: array
      listSchemaCodeObject:
        items:
          $ref: '#/definitions/message.SchemaCodeObject'
        type: array
      listSchemaColumnTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaColumnTypeCompatibles'
        type: array
      listSchemaConstraintTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaConstraintTypeCompatibles'
        type: array
      listSchemaDatabaseLinkCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaDefaultValueCompatibles:
        items:
          $ref: '#/definitions/message.SchemaDefaultValueCompatibles'
        type: array
      listSchemaFunctionCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaIndexSubpartitionCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaIndexTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaIndexTypeCompatibles'
        type: array
      listSchemaJobCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaLobPartitionCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaLobSubpartitionCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaMaterializedViewObject:
        items:
          $ref: '#/definitions/message.SchemaMaterializedViewObject'
        type: array
      listSchemaObjectTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaPackageBodyCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaPackageCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaPartitionTableCountsCheck:
        items:
          $ref: '#/definitions/message.SchemaPartitionTableCountsCheck'
        type: array
      listSchemaPartitionTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaPartitionTypeCompatibles'
        type: array
      listSchemaProcedureCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaSequenceCompatibles:
        items:
          $ref: '#/definitions/message.SchemaSequenceCompatibles'
        type: array
      listSchemaSequenceNameLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaSequenceNameLengthCheck'
        type: array
      listSchemaSubPartitionTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaSubPartitionTypeCompatibles'
        type: array
      listSchemaSynonymCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaSynonymObject:
        items:
          $ref: '#/definitions/message.SchemaSynonymObject'
        type: array
      listSchemaTableAvgRowLengthTOP:
        items:
          $ref: '#/definitions/message.SchemaTableAvgRowLengthTOP'
        type: array
      listSchemaTableColumnCountsCheck:
        items:
          $ref: '#/definitions/message.SchemaTableColumnCountsCheck'
        type: array
      listSchemaTableColumnNameLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaTableColumnNameLengthCheck'
        type: array
      listSchemaTableIndexCountsCheck:
        items:
          $ref: '#/definitions/message.SchemaTableIndexCountsCheck'
        type: array
      listSchemaTableIndexNameLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaTableIndexNameLengthCheck'
        type: array
      listSchemaTableIndexRowLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaTableIndexRowLengthCheck'
        type: array
      listSchemaTableNameLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaTableNameLengthCheck'
        type: array
      listSchemaTableNumberTypeEqual0:
        items:
          $ref: '#/definitions/message.SchemaTableNumberTypeEqual0'
        type: array
      listSchemaTableRowLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaTableRowLengthCheck'
        type: array
      listSchemaTableRowsTOP:
        items:
          $ref: '#/definitions/message.SchemaTableRowsTOP'
        type: array
      listSchemaTableSizeData:
        items:
          $ref: '#/definitions/message.SchemaTableSizeData'
        type: array
      listSchemaTableTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaTableTypeCompatibles'
        type: array
      listSchemaTemporaryTableTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaTemporaryTableTypeCompatibles'
        type: array
      listSchemaTriggerCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaObjectTypeCompatibles'
        type: array
      listSchemaViewNameLengthCheck:
        items:
          $ref: '#/definitions/message.SchemaViewNameLengthCheck'
        type: array
      listSchemaViewTypeCompatibles:
        items:
          $ref: '#/definitions/message.SchemaViewTypeCompatibles'
        type: array
      listSynonym:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listTrigger:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listType:
        items:
          $ref: '#/definitions/message.OraDbaObjects'
        type: array
      listUsernameLengthCheck:
        items:
          $ref: '#/definitions/message.UsernameLengthCheck'
        type: array
      title:
        type: string
    type: object
  message.ObjectCompatibleByObject:
    properties:
      assessName:
        type: string
      assessTotal:
        type: string
      compatible:
        type: string
      convertible:
        type: string
      dbName:
        type: string
      inCompatible:
        type: string
      inConvertible:
        type: string
    type: object
  message.ObjectCompatibleBySchema:
    properties:
      assessTotal:
        type: string
      compatible:
        type: string
      convertible:
        type: string
      dbName:
        type: string
      inCompatible:
        type: string
      inConvertible:
        type: string
      schema:
        type: string
    type: object
  message.ObjectCompatibleBySchemaObject:
    properties:
      assessName:
        type: string
      assessTotal:
        type: string
      assessType:
        type: string
      compatible:
        type: string
      convertible:
        type: string
      dbName:
        type: string
      inCompatible:
        type: string
      inConvertible:
        type: string
      schema:
        type: string
    type: object
  message.ObjectCompatibleOverview:
    properties:
      assessTotal:
        type: string
      compatible:
        type: string
      convertible:
        type: string
      dbName:
        type: string
      inCompatible:
        type: string
      inConvertible:
        type: string
    type: object
  message.ObjectProperty:
    properties:
      package:
        type: string
      schema:
        type: string
      title:
        type: string
      type:
        type: string
    type: object
  message.OpMachineConf:
    properties:
      allocation:
        type: boolean
      basePath:
        type: string
      comment:
        maxLength: 1000
        type: string
      createTime:
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      hostIp:
        type: string
      hostName:
        type: string
      macId:
        type: integer
      osName:
        type: string
      portHasUsed:
        type: string
      portMax:
        type: integer
      portMin:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    type: object
  message.OperAllChannelSchemaTablesReq:
    properties:
      channelId:
        type: integer
      channelSchtableIds:
        items:
          type: integer
        type: array
      clusterTypeT:
        type: string
      partitionTypeT:
        type: string
      taskId:
        type: integer
    type: object
  message.OperAllChannelSchemaTablesResp:
    type: object
  message.OraAllPartTables:
    properties:
      channelId:
        type: integer
      dataId:
        type: integer
      isCompatible:
        type: string
      isConvertible:
        type: string
      owner:
        type: string
      partitionCount:
        type: string
      partitioningType:
        type: string
      subpartitioningType:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaConstraint:
    properties:
      channelId:
        type: integer
      constraintName:
        type: string
      constraintType:
        type: string
      isCompatible:
        type: string
      isConvertible:
        type: string
      oraconstraintId:
        type: integer
      owner:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaIndex:
    properties:
      channelId:
        type: integer
      indexName:
        type: string
      indexType:
        type: string
      isCompatible:
        type: string
      isConvertible:
        type: string
      oraIndexId:
        type: integer
      partitioned:
        type: string
      tableName:
        type: string
      tableOwner:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaObjects:
    properties:
      channelId:
        type: integer
      isCompatible:
        type: string
      isConvertible:
        type: string
      objectName:
        type: string
      objectType:
        type: string
      owner:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaSequence:
    properties:
      channelId:
        type: integer
      isCompatible:
        type: string
      isConvertible:
        type: string
      oraIndexId:
        type: integer
      sequenceName:
        type: string
      sequenceOwner:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaTabColumns:
    properties:
      OwnerName:
        type: string
      channelId:
        type: integer
      columnId:
        type: integer
      columnName:
        type: string
      dataDesc:
        type: string
      dataId:
        type: integer
      dataLength:
        type: integer
      dataType:
        type: string
      dataTypeT:
        type: string
      isEquivalent:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaTabColumnsDefault:
    properties:
      OwnerName:
        type: string
      channelId:
        type: integer
      columnName:
        type: string
      dataDefault:
        type: string
      dataDefaultT:
        type: string
      dataId:
        type: integer
      dataType:
        type: string
      isEquivalent:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
    type: object
  message.OraDbaTables:
    properties:
      channelId:
        type: integer
      dataId:
        type: integer
      duration:
        type: string
      isCompatible:
        type: string
      isConvertible:
        type: string
      owner:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
      temporary:
        type: string
    type: object
  message.OraDbaViews:
    properties:
      channelId:
        type: integer
      isCompatible:
        type: string
      isConvertible:
        type: string
      owner:
        type: string
      taskId:
        type: integer
      viewName:
        type: string
      viewType:
        type: string
      viewTypeOwner:
        type: string
    type: object
  message.OraLobs:
    properties:
      channelId:
        type: integer
      columnName:
        type: string
      dataType:
        type: string
      isCompatible:
        type: string
      isConvertible:
        type: string
      objectName:
        type: string
      objectType:
        type: string
      owner:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
    type: object
  message.OraObjectCode:
    properties:
      allText:
        type: string
      objectName:
        type: string
      objectType:
        type: string
      ownerName:
        type: string
      status:
        type: string
    type: object
  message.OraSubPartitioning:
    properties:
      channelId:
        type: integer
      isCompatible:
        type: string
      isConvertible:
        type: string
      owner:
        type: string
      subPartitioningType:
        type: string
      tableName:
        type: string
      taskId:
        type: integer
    type: object
  message.OraTableTypeCompatibles:
    properties:
      channelId:
        type: integer
      isCompatible:
        type: string
      isConvertible:
        type: string
      owner:
        type: string
      tableName:
        type: string
      tableType:
        type: string
      taskId:
        type: integer
    type: object
  message.OracleObjectDefinitionAnalyzeDetail:
    properties:
      channelId:
        type: integer
      databaseLinkCount:
        type: integer
      errorDetail:
        type: string
      id:
        type: integer
      identifierList:
        description: 属于函数调用
        items:
          $ref: '#/definitions/message.MethodInvoke'
        type: array
      incompatibleFeatureScoreContext:
        items:
          $ref: '#/definitions/message.EvaluateContextItem'
        type: array
      methodInvokeList:
        description: 属于函数调用
        items:
          $ref: '#/definitions/message.MethodInvoke'
        type: array
      objectName:
        type: string
      objectStatus:
        type: string
      objectType:
        type: string
      plsqlSegment:
        allOf:
        - $ref: '#/definitions/message.PLSQLSegment'
        description: sql片段
      reservedWordCount:
        type: integer
      reservedWordList:
        description: 关键字
        items:
          $ref: '#/definitions/message.ReservedWord'
        type: array
      schemaName:
        type: string
      schemaObjectKey:
        type: string
      status:
        type: string
      tableReferenceList:
        description: 被使用的表，其中，'@' in tableName，表示dblink
        items:
          $ref: '#/definitions/message.TableReference'
        type: array
      taskId:
        type: integer
    type: object
  message.OracleOverview:
    properties:
      block_size:
        type: string
      character_set:
        type: string
      check_time:
        type: string
      cluster_db:
        type: string
      cluster_db_instance:
        type: string
      db_name:
        type: string
      db_version:
        type: string
      global_db_name:
        type: string
      host_cpus:
        type: string
      host_mem:
        type: string
      host_name:
        type: string
      instance_name:
        type: string
      instance_number:
        type: string
      platform_name:
        type: string
      report_name:
        type: string
      report_user:
        type: string
      thread_number:
        type: string
      total_used_size:
        type: string
    type: object
  message.OracleToJavaLog:
    properties:
      channelId:
        type: integer
      createAt:
        type: string
      id:
        type: integer
      logLevel:
        type: string
      logMessage:
        type: string
      taskId:
        type: integer
    type: object
  message.OracleToJavaResult:
    properties:
      channelId:
        type: integer
      convertDuration:
        type: number
      convertErrMessage:
        type: string
      convertPrompts:
        items:
          type: string
        type: array
      convertSQL:
        type: string
      convertStatus:
        type: string
      convertTime:
        type: string
      depth:
        type: integer
      fileContent:
        type: string
      fileName:
        type: string
      filePackage:
        type: string
      id:
        type: integer
      objectName:
        type: string
      objectType:
        type: string
      objectUUID:
        type: string
      packageName:
        type: string
      schemaName:
        type: string
      taskId:
        type: integer
    type: object
  message.PLSQLSegment:
    properties:
      allPositions:
        items:
          $ref: '#/definitions/message.Position'
        type: array
      declares:
        items:
          $ref: '#/definitions/message.Segment'
        type: array
      functions:
        items:
          $ref: '#/definitions/message.Segment'
        type: array
      leftStartPos:
        type: integer
      procedures:
        items:
          $ref: '#/definitions/message.Segment'
        type: array
      rightStartPos:
        type: integer
      segmentPrefix:
        $ref: '#/definitions/message.Segment'
      segmentSuffix:
        $ref: '#/definitions/message.Segment'
    type: object
  message.ParamTemplateDetail:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      hasSubParams:
        type: boolean
      paramName:
        type: string
      paramValueDefault:
        type: string
      subParamValuesDefault:
        items:
          type: string
        type: array
      taskparamTemplateId:
        minimum: 1
        type: integer
      templateId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - paramName
    - taskparamTemplateId
    type: object
  message.PauseSyncDsgTaskResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.PhysicalFileIOStats:
    properties:
      br:
        type: string
      bw:
        type: string
      rTimes:
        type: string
      reads:
        type: string
      ts:
        type: string
      wTimes:
        type: string
      writes:
        type: string
    type: object
  message.PhysicalSubSystem:
    properties:
      createTime:
        type: string
      groupMachineDesc:
        type: string
      groupMachineName:
        type: string
      groupType:
        type: string
      id:
        type: integer
    type: object
  message.PlSQLToJSONResp:
    properties:
      root:
        $ref: '#/definitions/message.AbstractSyntaxTreeNode'
    type: object
  message.Position:
    properties:
      bp:
        type: integer
      ch:
        type: string
      np:
        type: integer
      sp:
        type: integer
      startPos:
        type: integer
      stringVal:
        type: string
      token:
        type: string
    type: object
  message.PrecheckInfo:
    properties:
      channelId:
        type: integer
      checkIgnore:
        enum:
        - Y(ignored)
        - N(not ignored)
        type: string
      checkObject:
        enum:
        - A(object compatible check priority)
        - B(object migration priority)
        - C(data migration priority)
        - D(data compare priority)
        - E(target privilege check)
        - F(no index table)
        - G(no pk/uk table)
        - H(table have dependency relation)
        type: string
      checkResultInfor:
        maxLength: 1000
        type: string
      checkStatus:
        enum:
        - C(checking)
        - P(pass)
        - N(not pass)
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      precheckId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - checkIgnore
    - checkObject
    - checkStatus
    type: object
  message.Prompt:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      id:
        type: integer
      isDefault:
        type: boolean
      promptText:
        type: string
      promptTitle:
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - promptText
    - promptTitle
    type: object
  message.PromptRelation:
    properties:
      channelId:
        type: integer
      dependencyUUID:
        type: string
      id:
        type: integer
      promptId:
        type: integer
      taskId:
        type: integer
    required:
    - dependencyUUID
    type: object
  message.QueryMigrationDetailsResp:
    properties:
      data:
        items:
          $ref: '#/definitions/message.CSVTableMigrationDetail'
        type: array
    type: object
  message.ReferenceTablesReq:
    properties:
      channelId:
        type: integer
      referenceTaskId:
        type: integer
      referenceType:
        enum:
        - channel
        - tasks
        type: string
      taskId:
        type: integer
    type: object
  message.ReferenceTablesResp:
    properties:
      conflictMessage:
        type: string
      isConflict:
        type: boolean
    type: object
  message.ReplayChunkDataReq:
    properties:
      batchSize:
        maximum: 1000
        minimum: 1
        type: integer
      channelId:
        type: integer
      executeAll:
        type: boolean
      executeIds:
        items:
          type: integer
        type: array
      schemaNameS:
        type: string
      tableNameS:
        type: string
      taskId:
        type: integer
    required:
    - batchSize
    - channelId
    - schemaNameS
    - tableNameS
    - taskId
    type: object
  message.ReplayChunkDataResp:
    type: object
  message.ReservedWord:
    properties:
      count:
        type: integer
      hasNonAlphaPrefix:
        type: boolean
      highlight:
        items:
          $ref: '#/definitions/message.ReservedWord'
        type: array
      value:
        type: string
    type: object
  message.ResourceInfoRate:
    properties:
      sourceCPU:
        type: string
      sourceDisk:
        type: string
      sourceMemory:
        type: string
      targetCPU:
        type: string
      targetDisk:
        type: string
      targetMemory:
        type: string
    type: object
  message.ResultWithPage:
    properties:
      code:
        type: integer
      current:
        type: integer
      data: {}
      message:
        type: string
      pageSize:
        type: integer
      total:
        type: integer
    type: object
  message.SCNType:
    enum:
    - scn
    - time
    type: string
    x-enum-varnames:
    - SCNType_SCN
    - SCNType_TIME
  message.SQLAnalyzerTask:
    properties:
      comment:
        type: string
      isIgnore:
        type: string
      lastRunTime:
        type: string
      sqlAnalyzeId:
        type: integer
      taskId:
        type: integer
      taskLog:
        type: string
      taskMode:
        type: string
      taskName:
        type: string
      taskNumber:
        type: integer
      taskSQL:
        type: string
      taskStatus:
        type: integer
    type: object
  message.SQLContentFilter:
    properties:
      case_sensitive:
        description: 是否大小写敏感
        type: boolean
      search_text:
        description: 搜索关键字
        type: string
      sql_types:
        description: '["INSERT", "DELETE", "UPDATE"]'
        items:
          type: string
        type: array
    type: object
  message.SQLFactor:
    properties:
      analyze_timeout:
        type: integer
      exclude_modules:
        items:
          type: string
        type: array
      exclude_sqlids:
        items:
          type: string
        type: array
      exclude_sqltxts:
        items:
          type: string
        type: array
      including_schemas:
        items:
          type: string
        type: array
      including_sql_types:
        items:
          type: string
        type: array
      sql_order_by:
        type: string
    type: object
  message.SQLLine:
    properties:
      content:
        type: string
      line_number:
        type: integer
      sql_type:
        description: INSERT/DELETE/UPDATE/COMMENT/OTHER
        type: string
      table_info:
        description: 提取的表信息
        type: string
    type: object
  message.SQLParent:
    properties:
      parentName:
        type: string
      parentType:
        type: string
    type: object
  message.SQLSet:
    properties:
      dataSourceName:
        type: string
      schemaName:
        type: string
      sqlCount:
        type: integer
      sqlSetName:
        type: string
      sqlSetOwner:
        type: string
    type: object
  message.SQLSetStatement:
    properties:
      executions:
        type: integer
      index:
        type: integer
      perExecTime:
        type: number
      schemaName:
        type: string
      sqlId:
        type: string
      sqlText:
        type: string
    type: object
  message.SQLSummary:
    properties:
      has_more:
        description: 是否还有更多内容
        type: boolean
      sql_count:
        additionalProperties:
          type: integer
        description: 各类SQL语句数量
        type: object
      total_lines:
        type: integer
    type: object
  message.SaveHostReq:
    properties:
      allocation:
        type: boolean
      basePath:
        type: string
      comment:
        type: string
      createTime:
        type: string
      hostIp:
        type: string
      hostName:
        type: string
      macId:
        type: integer
      osName:
        type: string
      portHasUsed:
        type: string
      portMax:
        type: integer
      portMin:
        type: integer
    type: object
  message.SaveHostResp:
    type: object
  message.SaveMigrationDataResp:
    properties:
      saved_channel_schema_num:
        type: integer
      saved_channel_schema_table_num:
        type: integer
      saved_migration_detail_num:
        type: integer
      saved_migration_summary_num:
        type: integer
      saved_table_num:
        type: integer
    type: object
  message.SavePromptResp:
    type: object
  message.SaveTaskObjectPromptRelationResp:
    properties:
      channelId:
        type: integer
      relationId:
        type: integer
      taskId:
        type: integer
      taskPromptId:
        type: integer
    type: object
  message.SaveTaskParamsReq:
    properties:
      taskParams:
        items:
          $ref: '#/definitions/message.TaskParam'
        type: array
    type: object
  message.SaveTaskParamsResp:
    type: object
  message.SchemaActiveSession:
    properties:
      dbid:
        type: string
      instance_number:
        type: string
      rownum:
        type: string
      sample_id:
        type: string
      sample_time:
        type: string
      session_counts:
        type: string
    type: object
  message.SchemaCodeObject:
    properties:
      lines:
        type: string
      object_name:
        type: string
      object_type:
        type: string
      schema:
        type: string
    type: object
  message.SchemaColumnTypeCompatibles:
    properties:
      column_type:
        type: string
      column_type_map:
        type: string
      is_equivalent:
        type: string
      max_data_length:
        type: string
      object_counts:
        type: string
      schema:
        type: string
    type: object
  message.SchemaConstraintTypeCompatibles:
    properties:
      constraint_type:
        type: string
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
    type: object
  message.SchemaDefaultValueCompatibles:
    properties:
      column_default_value:
        type: string
      default_value_map:
        type: string
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
    type: object
  message.SchemaIndexTypeCompatibles:
    properties:
      index_type:
        type: string
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
    type: object
  message.SchemaMaterializedViewObject:
    properties:
      fast_refreshable:
        type: string
      mview_name:
        type: string
      refresh_method:
        type: string
      refresh_mode:
        type: string
      rewrite_capability:
        type: string
      schema:
        type: string
    type: object
  message.SchemaObjectTypeCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      object_type:
        type: string
      schema:
        type: string
    type: object
  message.SchemaPartitionTableCountsCheck:
    properties:
      partition_counts:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaPartitionTypeCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      partition_type:
        type: string
      schema:
        type: string
    type: object
  message.SchemaSequenceCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
    type: object
  message.SchemaSequenceNameLengthCheck:
    properties:
      length:
        type: string
      order_flag:
        type: string
      schema:
        type: string
      sequence_name:
        type: string
    type: object
  message.SchemaSubPartitionTypeCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
      sub_partition_type:
        type: string
    type: object
  message.SchemaSynonymObject:
    properties:
      schema:
        type: string
      synonym_name:
        type: string
      table_name:
        type: string
      table_owner:
        type: string
    type: object
  message.SchemaTableAvgRowLengthTOP:
    properties:
      avg_row_length:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableColumnCountsCheck:
    properties:
      column_counts:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableColumnNameLengthCheck:
    properties:
      column_name:
        type: string
      length:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableIndexCountsCheck:
    properties:
      index_counts:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableIndexNameLengthCheck:
    properties:
      index_name:
        type: string
      length:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableIndexRowLengthCheck:
    properties:
      column_length:
        type: string
      index_name:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableNameLengthCheck:
    properties:
      length:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableNumberTypeEqual0:
    properties:
      column_name:
        type: string
      data_precision:
        type: string
      data_scale:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableRowLengthCheck:
    properties:
      avg_row_length:
        type: string
      schema:
        type: string
      table_name:
        type: string
    type: object
  message.SchemaTableRowsTOP:
    properties:
      schema:
        type: string
      table_name:
        type: string
      table_size:
        type: string
      table_type:
        type: string
    type: object
  message.SchemaTableSizeData:
    properties:
      all_tables_rows:
        type: string
      index_size:
        type: string
      lob_index_size:
        type: string
      lob_table_size:
        type: string
      schema:
        type: string
      table_size:
        type: string
    type: object
  message.SchemaTableTypeCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      object_size:
        type: string
      schema:
        type: string
      table_type:
        type: string
    type: object
  message.SchemaTableWithCSV:
    properties:
      csv_file_num:
        type: integer
      schema_name_t:
        type: string
      table_name_t:
        type: string
    type: object
  message.SchemaTemporaryTableTypeCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
      temporary_table_type:
        type: string
    type: object
  message.SchemaViewNameLengthCheck:
    properties:
      length:
        type: string
      read_only:
        type: string
      schema:
        type: string
      view_name:
        type: string
    type: object
  message.SchemaViewTypeCompatibles:
    properties:
      is_compatible:
        type: string
      is_convertible:
        type: string
      object_counts:
        type: string
      schema:
        type: string
      view_type:
        type: string
      view_type_owner:
        type: string
    type: object
  message.Segment:
    properties:
      endPos:
        type: integer
      name:
        type: string
      sql:
        type: string
      startPos:
        type: integer
    type: object
  message.Source:
    properties:
      config:
        $ref: '#/definitions/message.CustomSourceConfigDTO'
      msg:
        type: string
      result:
        type: boolean
      source:
        type: string
      worker:
        type: string
    type: object
  message.SplitSchemaTable:
    properties:
      schemaName:
        type: string
      tableName:
        type: string
      tableSizeM:
        type: number
      taskId:
        type: integer
      taskName:
        type: string
      taskType:
        type: integer
    type: object
  message.SplitTaskReq:
    properties:
      channelId:
        type: integer
      splitNum:
        maximum: 100
        minimum: 1
        type: integer
      taskIds:
        items:
          type: integer
        type: array
    required:
    - splitNum
    type: object
  message.SplitTaskResp:
    properties:
      isFailed:
        type: boolean
      message:
        type: string
    type: object
  message.SqlAnalyzerSummaryDetail:
    properties:
      StartTime:
        type: string
      duration:
        type: string
      endTime:
        type: string
      failedNums:
        type: integer
      runningNums:
        type: integer
      schema:
        type: string
      successNums:
        type: integer
      successRatio:
        type: string
      taskId:
        type: integer
      totalNums:
        type: integer
      waitingNums:
        type: integer
    type: object
  message.SqlMapRule:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isCompatibility:
        enum:
        - "Y"
        - "N"
        type: string
      isConvertible:
        enum:
        - "Y"
        - "N"
        type: string
      keywords:
        maxLength: 20
        minLength: 1
        type: string
      sqlMapRuleId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbTypeS
    - dbTypeT
    - isCompatibility
    - isConvertible
    - keywords
    type: object
  message.SqlResultHistoryItem:
    properties:
      clientIp:
        type: string
      id:
        type: integer
      newStatus:
        type: string
      operateAt:
        type: string
      operateRemark:
        type: string
      operationType:
        type: string
      previousStatus:
        type: string
      sqlExecId:
        type: integer
      taskId:
        type: integer
    type: object
  message.SqlResultWithUserOperation:
    properties:
      channelId:
        type: integer
      oraElapsedTimeMs:
        type: number
      oraExecutions:
        type: integer
      oraModule:
        type: string
      oraParsingSchemaName:
        type: string
      oraSqlId:
        type: string
      oraSqlText:
        type: string
      sqlExecId:
        type: integer
      taskId:
        type: integer
      tidbElapsedTimeMs:
        type: integer
      tidbExecCode:
        type: string
      tidbExecMsg:
        type: string
      tidbExecStatus:
        type: string
      tidbSqlText:
        type: string
      tidbSqlType:
        type: string
      userOperateAt:
        type: string
      userOperateRemark:
        type: string
      userOperateStatus:
        type: string
    type: object
  message.StartDsgTaskResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.StartSQLFileReplayReq:
    type: object
  message.StartSQLFileReplayResp:
    type: object
  message.StartSpaCollectReq:
    type: object
  message.StartSpaCollectResp:
    properties:
      message:
        type: string
    type: object
  message.StartSyncDsgTaskResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.StatisticsSchemaStateReq:
    properties:
      channelId:
        type: integer
      page:
        type: integer
      pageSize:
        type: integer
      schema:
        type: string
      state:
        enum:
        - success
        - failed
        - waiting
        - running
        type: string
      taskId:
        type: integer
    type: object
  message.StatisticsSchemaStateResp:
    properties:
      channelId:
        type: integer
      schema:
        type: string
      state:
        type: string
      tableDetail:
        items:
          $ref: '#/definitions/message.TaskStatisticsJoinDuplicate'
        type: array
      tablecount:
        type: integer
      taskId:
        type: integer
    type: object
  message.StatisticsSummaryDetail:
    properties:
      StartTime:
        type: string
      compareDuration:
        type: string
      duplicateNums:
        type: integer
      endTime:
        type: string
      failedNums:
        type: integer
      runningNums:
        type: integer
      schema:
        type: string
      successNums:
        type: integer
      successRatio:
        type: string
      taskId:
        type: integer
      totalNums:
        type: integer
      waitingNums:
        type: integer
    type: object
  message.StatisticsSummarySchemaReq:
    properties:
      channelId:
        type: integer
      schema:
        type: string
      taskId:
        type: integer
    type: object
  message.StatisticsSummarySchemaResp:
    properties:
      statsSchemaSummary:
        items:
          $ref: '#/definitions/message.StatisticsSummaryDetail'
        type: array
    type: object
  message.StatisticsTablesReq:
    properties:
      channelId:
        type: integer
      page:
        type: integer
      pageSize:
        type: integer
      schema:
        type: string
      table:
        type: string
      taskId:
        type: integer
    type: object
  message.StatisticsTablesResp:
    properties:
      channelId:
        type: integer
      schema:
        type: string
      tableDetail:
        items:
          $ref: '#/definitions/message.TaskStatisticsTable'
        type: array
      tablecount:
        type: integer
      taskId:
        type: integer
    type: object
  message.StatisticsTaskTableDuplReq:
    properties:
      channelId:
        type: integer
      page:
        type: integer
      pageSize:
        type: integer
      schema:
        type: string
      taskId:
        type: integer
    type: object
  message.StatisticsTaskTableDuplResp:
    properties:
      channelId:
        type: integer
      schema:
        type: string
      tableDetail:
        items:
          $ref: '#/definitions/message.ChannelTaskSchemaStatisticsDuplicate'
        type: array
      tablecount:
        type: integer
      taskId:
        type: integer
    type: object
  message.StatsTable:
    properties:
      schemaName:
        type: string
      tableName:
        type: string
    type: object
  message.StopDsgTaskResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.StopSpaCollectReq:
    type: object
  message.StopSpaCollectResp:
    properties:
      message:
        type: string
    type: object
  message.SubmitTaskSchemaTablesReq:
    properties:
      channelId:
        type: integer
      deleteSchemas:
        items:
          type: integer
        type: array
      deleteTables:
        items:
          type: integer
        type: array
      saveSchemas:
        items:
          $ref: '#/definitions/message.ChannelSchemaTable'
        type: array
      saveTables:
        items:
          $ref: '#/definitions/message.ChannelSchemaTable'
        type: array
      taskId:
        type: integer
    type: object
  message.SubmitTaskSchemaTablesResp:
    properties:
      conflictMessage:
        type: string
      isConflict:
        type: boolean
    type: object
  message.SummaryWithError:
    properties:
      channelId:
        type: integer
      channelSchtableId:
        type: integer
      checkFailedNum:
        type: integer
      checkIgnoreNum:
        type: integer
      checkSuccessNum:
        type: integer
      checkWaitingNum:
        type: integer
      chunkNum:
        type: integer
      chunkSize:
        type: integer
      configHash:
        type: string
      duration:
        type: string
      message:
        type: string
      schema:
        type: string
      startTime:
        type: string
      state:
        type: string
      table:
        type: string
      taskId:
        type: integer
      updateTime:
        type: string
    type: object
  message.SyncSourceTableColumnsResp:
    properties:
      affected:
        type: integer
    type: object
  message.TabColMapRule:
    properties:
      colTypeNameS:
        maxLength: 100
        minLength: 1
        type: string
      colTypeNameT:
        maxLength: 100
        minLength: 1
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbTypeS:
        enum:
        - oracle
        - mysql
        type: string
      dbTypeT:
        enum:
        - tidb
        type: string
      isEquivalent:
        enum:
        - "Y"
        - "N"
        type: string
      tabColMapRuleId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - colTypeNameS
    - colTypeNameT
    - dbTypeS
    - dbTypeT
    - isEquivalent
    type: object
  message.TableColumnCustomMapRule:
    properties:
      channelId:
        type: integer
      columnCommentS:
        type: string
      columnCommentT:
        type: string
      columnDigest:
        type: string
      columnID:
        type: integer
      columnNameS:
        type: string
      columnNameT:
        type: string
      dataDefaultS:
        type: string
      dataDefaultT:
        type: string
      dataLengthS:
        type: integer
      dataLengthT:
        type: integer
      dataPrecisionS:
        type: integer
      dataPrecisionT:
        type: integer
      dataScaleS:
        type: integer
      dataScaleT:
        type: integer
      dataTypeS:
        type: string
      dataTypeT:
        type: string
      nullableS:
        type: string
      nullableT:
        type: string
      schemaNameS:
        type: string
      tableNameS:
        type: string
    required:
    - columnDigest
    type: object
  message.TableReference:
    properties:
      count:
        type: integer
      isDatabaseLink:
        type: boolean
      statementName:
        type: string
      statementType:
        type: string
      tableName:
        type: string
    type: object
  message.Task:
    properties:
      channelId:
        type: integer
      colDefaultMapTemplateId:
        type: integer
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      endTime:
        type: string
      errorDetail:
        type: string
      incrementId:
        type: integer
      objmapTemplateId:
        type: integer
      onlyIncompatibleDetail:
        type: integer
      parentTaskId:
        type: integer
      progress:
        type: number
      runParams:
        type: string
      scnNumber:
        type: string
      serverId:
        type: string
      sqlmapTemplateId:
        type: integer
      startTime:
        type: string
      tabcolmapTemplateId:
        type: integer
      taskId:
        type: integer
      taskName:
        maxLength: 100
        minLength: 1
        type: string
      taskObjRef:
        enum:
        - channel
        - tasks
        type: string
      taskParamTemplateId:
        type: integer
      taskReftask:
        type: integer
      taskSeq:
        type: integer
      taskStatus:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        type: integer
      taskType:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      taskWarning:
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - taskName
    - taskStatus
    - taskType
    type: object
  message.TaskDbmsJobStatus:
    properties:
      broken:
        type: string
      jobId:
        type: string
      jobNextDate:
        type: string
    type: object
  message.TaskDetailChartData:
    properties:
      count:
        type: integer
      pct:
        type: string
      total:
        type: integer
      type:
        type: string
    type: object
  message.TaskDetailSchemaData:
    properties:
      dataCompare:
        items:
          $ref: '#/definitions/message.DataCompareSummaryDetail'
        type: array
      ddlMigration:
        items:
          $ref: '#/definitions/message.MigrationStructureTaskSummaryDetail'
        type: array
      fullDataMigration:
        items:
          $ref: '#/definitions/message.MigrationDataTaskSummaryDetail'
        type: array
      objectAssessment:
        items:
          type: string
        type: array
      sqlAnalyzer:
        items:
          $ref: '#/definitions/message.SqlAnalyzerSummaryDetail'
        type: array
      statistics:
        items:
          $ref: '#/definitions/message.StatisticsSummaryDetail'
        type: array
    type: object
  message.TaskExecutionPreCheckResp:
    properties:
      preCheckMessage:
        type: string
      preCheckPass:
        type: string
    type: object
  message.TaskExecutionResp:
    type: object
  message.TaskParam:
    properties:
      channelID:
        type: integer
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      hasSubParams:
        type: boolean
      incrementId:
        type: integer
      paramName:
        maxLength: 100
        minLength: 1
        type: string
      paramValueCurrent:
        maxLength: 500
        minLength: 1
        type: string
      paramValueDefault:
        maxLength: 500
        minLength: 1
        type: string
      subParamValuesCurrent:
        items:
          type: string
        type: array
      subParamValuesDefault:
        items:
          type: string
        type: array
      taskID:
        type: integer
      taskParamID:
        type: integer
      taskRefTask:
        type: integer
      taskparamTemplateID:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelID
    - paramName
    - paramValueCurrent
    - paramValueDefault
    - taskID
    - taskparamTemplateID
    type: object
  message.TaskParamTemplate:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      defaultTag:
        enum:
        - "Y"
        - "N"
        type: string
      paramTemplateName:
        maxLength: 100
        minLength: 1
        type: string
      taskParamTemplateID:
        type: integer
      taskType:
        enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        type: integer
      templateType:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - defaultTag
    - paramTemplateName
    - taskType
    type: object
  message.TaskProgressChunkInfo:
    properties:
      lastUpdateTime:
        type: string
      runningFinishChunk:
        type: integer
      runningStartTime:
        type: string
      runningTotalChunk:
        type: integer
      taskId:
        type: integer
    type: object
  message.TaskProgressLogDetail:
    properties:
      logLevel:
        type: string
      logMessage:
        type: string
      logTime:
        type: string
    type: object
  message.TaskProgressLogInfo:
    properties:
      logCount:
        type: integer
      logDetailList:
        items:
          $ref: '#/definitions/message.TaskProgressLogDetail'
        type: array
      logStartTime:
        type: string
      taskId:
        type: integer
    type: object
  message.TaskStatisticsJoinDuplicate:
    properties:
      analyzeDuration:
        type: string
      analyzeStatus:
        type: string
      channelId:
        type: integer
      duplicateCount:
        type: integer
      duplicateTasks:
        type: string
      lastAnalyzeEndtime:
        type: string
      lastAnalyzeStarttime:
        type: string
      lastAnalyzedRows:
        type: integer
      message:
        type: string
      priority:
        type: integer
      samplerate:
        type: number
      schemaName:
        type: string
      tableName:
        type: string
      tableRows:
        type: integer
      taskId:
        type: integer
      taskStatsId:
        type: integer
    type: object
  message.TaskStatisticsTable:
    properties:
      analyzeDuration:
        type: string
      analyzeStatus:
        type: string
      channelId:
        type: integer
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      lastAnalyzeEndtime:
        type: string
      lastAnalyzeStarttime:
        type: string
      lastAnalyzedRows:
        type: integer
      message:
        type: string
      priority:
        type: integer
      samplerate:
        type: number
      schemaName:
        type: string
      tableName:
        type: string
      tableRows:
        type: integer
      taskId:
        type: integer
      taskStatsId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    type: object
  message.TaskTableConfig:
    properties:
      channelId:
        type: integer
      chunkSize:
        maximum: 1000
        minimum: 1
        type: integer
      colmapOracle:
        maxLength: 1000
        minLength: 1
        type: string
      colmapTidb:
        maxLength: 1000
        minLength: 1
        type: string
      columnslistOracle:
        example: column1,column2,column3
        maxLength: 1000
        minLength: 1
        type: string
      columnslistTidb:
        example: column1,column2,column3
        maxLength: 1000
        minLength: 1
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      dbNameS:
        maxLength: 200
        minLength: 1
        type: string
      dbNameT:
        maxLength: 200
        minLength: 1
        type: string
      enableChunkSplit:
        type: string
      filterClauseOracle:
        example: where a>1
        maxLength: 1000
        minLength: 1
        type: string
      filterClauseTidb:
        example: where a>1
        maxLength: 1000
        minLength: 1
        type: string
      operatorTag:
        maxLength: 1
        minLength: 1
        type: string
      schemaNameS:
        maxLength: 200
        minLength: 1
        type: string
      schemaNameT:
        maxLength: 200
        minLength: 1
        type: string
      sqlhintOracle:
        maxLength: 1000
        minLength: 1
        type: string
      sqlhintTidb:
        maxLength: 1000
        minLength: 1
        type: string
      tableNameS:
        maxLength: 200
        minLength: 1
        type: string
      tableNameT:
        maxLength: 200
        minLength: 1
        type: string
      tablePartition:
        type: string
      taskId:
        type: integer
      taskTableId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - dbNameS
    - dbNameT
    - schemaNameS
    - schemaNameT
    - tableNameS
    - tableNameT
    type: object
  message.TemplateInfo:
    properties:
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      defaultTag:
        enum:
        - "Y"
        - "N"
        type: string
      templateId:
        type: integer
      templateName:
        maxLength: 100
        minLength: 1
        type: string
      templateType:
        enum:
        - object_map
        - sql_map
        - col_default_map
        - tab_col_map
        type: string
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - defaultTag
    - templateName
    - templateType
    type: object
  message.TestAIConnectResp:
    properties:
      errMessage:
        type: string
      success:
        type: boolean
      successMessage:
        type: string
    type: object
  message.TestConnectionResp:
    type: object
  message.TmsSessionProgress:
    properties:
      opname:
        description: 操作名称
        type: string
      progress:
        description: 执行进度，表示当前会话SQL操作的完成百分比，例如"50%"
        type: string
      serial:
        description: 会话序列号
        type: string
      sid:
        description: 会话 ID
        type: string
      sqlText:
        description: 执行SQL，当前会话正在执行的SQL语句
        type: string
      startTime:
        description: 开始时间，表示该SQL操作的开始时间
        type: string
      target:
        description: 目标
        type: string
      timeRemaining:
        description: 剩余秒数，表示该操作预计还需多少秒完成
        type: integer
      timeRemainingHours:
        description: 剩余小时，保留两位小数，便于前端展示
        type: number
      timeRemainingMinutes:
        description: 剩余分钟，保留两位小数，便于前端展示
        type: number
      username:
        description: 用户名
        type: string
    type: object
  message.TransactionDataBlock:
    properties:
      perSecondValue:
        type: number
      snapshotID:
        type: string
      snapshotTime:
        type: string
    type: object
  message.UpdateBasicIncompatibleFeatureResp:
    type: object
  message.UpdateDataCompareEnvDeployTaskReq:
    properties:
      comment:
        type: string
      envDeployId:
        type: integer
      taskId:
        type: integer
      taskSQL:
        type: string
    type: object
  message.UpdateDataCompareEnvDeployTaskResp:
    type: object
  message.UpdateLicenseFeaturesReq:
    properties:
      increaseOpFunctionNum:
        type: boolean
      increaseOpPackageNum:
        type: boolean
      increaseOpProcedureNum:
        type: boolean
    type: object
  message.UpdatePreCheckInfoReq:
    properties:
      channelId:
        type: integer
      checkIgnore:
        enum:
        - Y(ignored)
        - N(not ignored)
        type: string
      checkObject:
        enum:
        - A(object compatible check priority)
        - B(object migration priority)
        - C(data migration priority)
        - D(data compare priority)
        - E(target privilege check)
        - F(no index table)
        - G(no pk/uk table)
        - H(table have dependency relation)
        type: string
      checkResultInfor:
        maxLength: 1000
        type: string
      checkStatus:
        enum:
        - C(checking)
        - P(pass)
        - N(not pass)
        type: string
      comment:
        maxLength: 1000
        type: string
      createdAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
      precheckId:
        type: integer
      updatedAt:
        example: RFC3339,2022-06-10T09:09:51.123+08:00
        type: string
    required:
    - channelId
    - checkIgnore
    - checkObject
    - checkStatus
    type: object
  message.UpdatePreCheckInfoResp:
    type: object
  message.UpdateSQLAnalyzeTaskReq:
    properties:
      comment:
        type: string
      sqlAnalyzeId:
        type: integer
      taskId:
        type: integer
      taskSQL:
        type: string
    type: object
  message.UpdateSQLAnalyzeTaskResp:
    type: object
  message.UpdateSourceConfigResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.UpdateSqlResultUserOperationReq:
    properties:
      oraSqlIds:
        description: Unified for both V1 and V2
        items:
          type: string
        type: array
      taskId:
        type: integer
      userOperateRemark:
        type: string
      userOperateStatus:
        enum:
        - normal
        - ignored
        - resolved
        type: string
    required:
    - oraSqlIds
    - taskId
    - userOperateStatus
    type: object
  message.UpdateSqlResultUserOperationResp:
    properties:
      updatedCount:
        type: integer
    type: object
  message.UpdateTableMappingResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.UpdateTargetConfigResp:
    properties:
      message:
        type: string
      statFlag:
        type: integer
    type: object
  message.UpdateTaskAndChannelSchemaObjectByIdReq:
    properties:
      channelSchemaObject:
        $ref: '#/definitions/message.ChannelSchemaObject'
      task:
        $ref: '#/definitions/message.Task'
      taskParams:
        items:
          $ref: '#/definitions/message.TaskParam'
        type: array
    type: object
  message.UpdateTaskAndChannelSchemaObjectByIdResp:
    type: object
  message.UpdateTaskIncompatibleFeatureResp:
    type: object
  message.UpdateTaskTableConfigsReq:
    properties:
      taskTableConfigs:
        items:
          $ref: '#/definitions/message.TaskTableConfig'
        type: array
    type: object
  message.UpdateTaskTableConfigsResp:
    type: object
  message.UpdateTidbStatisticsReq:
    properties:
      channelId:
        type: integer
      statsTables:
        items:
          $ref: '#/definitions/message.TaskStatisticsTable'
        type: array
      taskId:
        type: integer
    type: object
  message.UpdateTidbStatisticsResp:
    type: object
  message.UploadSourceTableColumnCSVResp:
    properties:
      channelId:
        type: integer
      fileDir:
        type: string
      fileName:
        type: string
    type: object
  message.UsernameLengthCheck:
    properties:
      account_status:
        type: string
      created:
        type: string
      length:
        type: string
      schema:
        type: string
    type: object
  message.VerifyHostReq:
    properties:
      hostIp:
        type: string
    required:
    - hostIp
    type: object
  message.VerifyHostResp:
    properties:
      errMessage:
        type: string
      hostIp:
        type: string
      isValid:
        type: boolean
    type: object
  message.VerifySplitTaskReq:
    properties:
      channelId:
        type: integer
      page:
        minimum: 1
        type: integer
      pageSize:
        minimum: 1
        type: integer
      taskIds:
        items:
          type: integer
        type: array
    required:
    - page
    - pageSize
    type: object
  message.VerifySplitTaskResp:
    properties:
      diffTables:
        items:
          $ref: '#/definitions/message.SplitSchemaTable'
        type: array
      isFailed:
        type: boolean
      message:
        type: string
    type: object
  message.VerifyTableMappingResp:
    properties:
      dsgMissingTables:
        items:
          $ref: '#/definitions/message.DsgTable'
        type: array
      hasMissingDBPSView:
        type: boolean
      hasMissingOracleSupplementalLog:
        type: boolean
      hasMissingTables:
        type: boolean
      isTmsHasNoTables:
        type: boolean
      missingDBPSView:
        items:
          type: string
        type: array
      missingOracleSupplementalLogTables:
        items:
          $ref: '#/definitions/message.DsgTable'
        type: array
      tmsMissingTables:
        items:
          $ref: '#/definitions/message.DsgTable'
        type: array
    type: object
  message.VersionResp:
    properties:
      version:
        type: string
    type: object
  message.WalkCSVDirResp:
    properties:
      csv_files:
        items:
          $ref: '#/definitions/message.CSVFileInfo'
        type: array
      error_csv_file_num:
        type: integer
      tables:
        items:
          $ref: '#/definitions/message.CSVTableProperty'
        type: array
      total_csv_file_num:
        type: integer
      total_table_num:
        type: integer
      valid_csv_file_num:
        type: integer
      walk_paths:
        items:
          type: string
        type: array
    type: object
  message.Warning:
    properties:
      data_source_direction:
        type: string
      error_code:
        type: integer
      error_count:
        type: integer
      error_msg:
        type: string
      error_status:
        type: integer
      error_type:
        type: string
      msg_len:
        type: integer
      msg_md5:
        type: string
      node_name:
        type: string
      send_emal_flag:
        type: integer
      target_type:
        type: string
      time_stamp:
        type: string
      update_time:
        type: string
    type: object
  message.WarningInfos:
    properties:
      total:
        type: integer
      warnings:
        items:
          $ref: '#/definitions/message.Warning'
        type: array
    type: object
  message.WeightedScoring:
    properties:
      fullScoring:
        type: number
      scoring:
        type: number
      weight:
        type: number
      weightedScoring:
        type: number
    type: object
  message.Worker:
    properties:
      addr:
        type: string
      name:
        type: string
      source:
        type: string
      stage:
        type: string
    type: object
  message.YLoader:
    properties:
      addcol_rowid_column:
        type: string
      addcol_rowid_invisible:
        type: string
      addcol_rowid2uk:
        type: string
      cfg_filename:
        type: string
      countsum_detail:
        type: string
      countsum_detail_db:
        type: string
      countsum_full_db:
        type: string
      countsum_interval_db:
        type: string
      countsum_time:
        type: string
      countsum_total_db:
        type: string
      create_table_addcol:
        type: string
      create_table_fcol:
        type: string
      ctype_ifx_decimal:
        type: string
      ctype_number_adjust:
        type: string
      ctype_varchar2binary:
        type: string
      ctype_varchar2long:
        type: string
      data_format:
        type: string
      db_host:
        type: string
      db_lang:
        type: string
      db_name:
        type: string
      db_pwd:
        type: string
      db_type:
        type: string
      db_user:
        type: string
      defineConfig:
        type: string
      delay_interval:
        type: string
      encrypt_pwd:
        type: string
      error_retry:
        type: string
      error_retry_ddl:
        type: string
      filter_string:
        type: string
      filter_string_00:
        type: string
      ftable:
        type: string
      full_cindex_error_retry:
        type: string
      full_copy:
        type: string
      full_copy_size:
        type: string
      full_copy_unused_size:
        type: string
      full_ddl_filter:
        type: string
      full_ddl_ftable:
        type: string
      full_insert_err_fdelete:
        type: string
      full_insert_fdelete:
        type: string
      full_insert_ftable:
        type: string
      full_lang_from_force:
        type: string
      full_load_completed:
        type: string
      full_repair_real_quit:
        type: string
      full_single_parr:
        type: string
      full_sql_mode:
        type: string
      full_thread:
        type: string
      home:
        type: string
      idb_lang:
        type: string
      lang_gbk2gb18030:
        type: string
      map:
        type: string
      map_cname:
        type: string
      map_tname:
        type: string
      max_brows:
        type: string
      mc_schema:
        type: string
      output_log2db:
        type: string
      output_rba:
        type: string
      pack_for_java:
        type: string
      queue_name:
        type: string
      real_copy:
        type: string
      real_ddl_fobjn:
        type: string
      real_ddl_ftable:
        type: string
      real_delete_fobjn:
        type: string
      real_delete_ftable:
        type: string
      real_disp_fcount:
        type: string
      real_disp_fi:
        type: string
      real_disp_fwait:
        type: string
      real_disp_mode:
        type: string
      real_disp_rows:
        type: string
      real_disp_size:
        type: string
      real_insert_err_fdelete:
        type: string
      real_insert_fd_mode:
        type: string
      real_insert_fdelete:
        type: string
      real_insert_fobjn:
        type: string
      real_insert_ftable:
        type: string
      real_load_completed:
        type: string
      real_sql_mode:
        type: string
      real_thread:
        type: string
      real_update_fobjn:
        type: string
      real_update_ftable:
        type: string
      rowmap_type:
        type: string
      service:
        type: string
      ses_blen:
        type: string
      sql_err_change2bind:
        type: string
      sql_trans:
        type: string
      table_create_full:
        type: string
      table_create_full_constraint:
        type: string
      table_create_full_idx_end:
        type: string
      table_create_full_idx_par:
        type: string
      table_create_full_index:
        type: string
      table_create_full_index_thread:
        type: string
      table_create_inc_pk:
        type: string
      table_create_real:
        type: string
      table_create_real_constraint:
        type: string
      table_create_real_index:
        type: string
      table_exists_check:
        type: string
      table_exists_full_do:
        type: string
      table_exists_real_do:
        type: string
      trans:
        type: string
      unused_pkuk:
        type: string
      update_err_used_di:
        type: string
      update_filter_col:
        type: string
      update_nobefore:
        type: string
      update_pkuk_col:
        type: string
      update_used_di:
        type: string
      used_map:
        type: string
      used_quotes:
        type: string
      wait_free_size:
        type: string
      wait_retry:
        type: string
      xsql_rows:
        type: string
    type: object
  message.YLoaderWrapper:
    properties:
      tms_real_delete_bind_num:
        maximum: 1000
        minimum: 1
        type: integer
      tms_real_insert_bind_num:
        maximum: 1000
        minimum: 1
        type: integer
      tms_real_insert_mode:
        enum:
        - batch_sql_mode
        - sql_mode
        - copy_mode
        type: string
      tms_real_update_bind_num:
        maximum: 1000
        minimum: 1
        type: integer
    required:
    - tms_real_delete_bind_num
    - tms_real_insert_bind_num
    - tms_real_insert_mode
    - tms_real_update_bind_num
    type: object
  structs.ColumnName:
    properties:
      columnName:
        type: string
      dataType:
        type: string
      schemaName:
        type: string
      tableName:
        type: string
    type: object
  structs.DependencyGraphEdge:
    properties:
      source:
        type: string
      target:
        type: string
    type: object
  structs.DependencyGraphNode:
    properties:
      hasDbLink:
        type: boolean
      id:
        type: string
      isReferenced:
        type: boolean
      label:
        type: string
      status:
        type: string
      type:
        type: string
    type: object
  structs.DependencyGraphVO:
    properties:
      edges:
        items:
          $ref: '#/definitions/structs.DependencyGraphEdge'
        type: array
      nodes:
        items:
          $ref: '#/definitions/structs.DependencyGraphNode'
        type: array
    type: object
  structs.DependencyTreeNode:
    properties:
      children:
        items:
          $ref: '#/definitions/structs.DependencyTreeNode'
        type: array
      isCycle:
        type: boolean
      name:
        type: string
      owner:
        type: string
      packageName:
        type: string
      status:
        type: string
      type:
        type: string
    type: object
  structs.DependencyTreeVO:
    properties:
      children:
        items:
          $ref: '#/definitions/structs.DependencyTreeVO'
        type: array
      depth:
        type: integer
      hasDbLink:
        type: boolean
      isCycle:
        type: boolean
      isReferenced:
        type: boolean
      key:
        type: string
      package:
        type: string
      prompt:
        $ref: '#/definitions/structs.OracleObjectPrompt'
      schema:
        type: string
      status:
        type: string
      title:
        type: string
      type:
        type: string
      uuid:
        type: string
    type: object
  structs.ObjectType:
    properties:
      children:
        items:
          type: string
        type: array
      key:
        type: string
      objectCount:
        type: integer
      tables:
        items:
          $ref: '#/definitions/structs.TableInfo'
        type: array
    type: object
  structs.OracleObjectPrompt:
    properties:
      promptId:
        type: integer
      promptText:
        type: string
      promptTitle:
        type: string
      relationId:
        type: integer
    type: object
  structs.SchemaInfo:
    properties:
      accountStatus:
        type: string
      children:
        items:
          $ref: '#/definitions/structs.ObjectType'
        type: array
      key:
        type: string
      schemaNameT:
        type: string
    type: object
  structs.TableInfo:
    properties:
      partitionCount:
        type: integer
      partitioningType:
        type: string
      pk:
        type: string
      subPartitionCount:
        type: integer
      subPartitioningType:
        type: string
      tableName:
        type: string
      tableSizeM:
        type: number
      uk:
        type: string
    type: object
host: localhost:8082
info:
  contact:
    email: <EMAIL>
    name: carter
  description: TiMS UI API
  title: TiMS UI API
  version: "1.0"
paths:
  /assessment/channel/{channelId}/task/{taskId}/DownloadObjectAssessResultReport:
    get:
      consumes:
      - application/json
      description: Download O2T object assess-task result report
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadObjectAssessResultReportResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download O2T object assess-task result report
      tags:
      - assessment
  /assessment/channel/{channelId}/task/{taskId}/GetObjectAssessResultDetail/{schema}:
    get:
      consumes:
      - application/json
      description: Get O2T object assess-task result detail
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: schema
        in: path
        name: schema
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetObjectAssessResultDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Get O2T object assess-task result detail
      tags:
      - assessment
  /assessment/channel/{channelId}/task/{taskId}/GetObjectAssessResultSummary:
    get:
      consumes:
      - application/json
      description: Get O2T object assess-task result summary
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetObjectAssessResultSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Get O2T object assess-task result summary
      tags:
      - assessment
  /assessment/channel/{channelId}/task/{taskId}/GetObjectAssessResultSummaryBySchema:
    get:
      consumes:
      - application/json
      description: Get O2T object assess-task result summary BySchema
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetObjectAssessResultSummaryBySchemaResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Get O2T object assess-task result summary BySchema
      tags:
      - assessment
  /assessment/download:
    post:
      consumes:
      - application/json
      description: Download O2T object assess-task result report
      parameters:
      - description: download assess schema report request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DownloadObjectAssessResultReportBySchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadObjectAssessResultReportBySchemaResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download O2T object assess-task result report
      tags:
      - assessment
  /channel/:
    post:
      consumes:
      - application/json
      description: Create channel
      parameters:
      - description: create channel request parameter
        in: body
        name: channelInfo
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateChannelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateChannelResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create channel
      tags:
      - channel
  /channel/:channelId/performance/oracle/io-file-stats:
    get:
      consumes:
      - application/json
      description: get physical file I/O stats from Oracle database
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetPhysicalFileIOStatsResp'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get physical file I/O stats
      tags:
      - channel
  /channel/:channelId/performance/oracle/io-hot-files:
    get:
      consumes:
      - application/json
      description: get hot file information from Oracle database
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetHotFileInformationResp'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get hot file information
      tags:
      - channel
  /channel/:channelId/performance/oracle/sessions-active:
    get:
      consumes:
      - application/json
      description: get active TMS sessions from Oracle database
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetActiveTmsSessionsResp'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get active TMS sessions
      tags:
      - channel
  /channel/:channelId/performance/oracle/sessions-progress:
    get:
      consumes:
      - application/json
      description: get TMS session progress from Oracle database
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTmsSessionProgressResp'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get TMS session progress
      tags:
      - channel
  /channel/:channelId/performance/oracle/sessions-ratio:
    get:
      consumes:
      - application/json
      description: get TMS session ratio from Oracle database
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTmsSessionRatioResp'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get TMS session ratio
      tags:
      - channel
  /channel/:channelId/source-table/columns/csv/export:
    get:
      consumes:
      - application/json
      description: export source table columns to csv
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: export source table columns to csv request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ExportSourceTableColumnsToCSVReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ExportSourceTableColumnsToCSVResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: export source table columns to csv
      tags:
      - channel
  /channel/:channelId/source-table/columns/csv/import:
    post:
      consumes:
      - application/json
      description: import source table columns by csv
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: filename
        in: body
        name: fileName
        required: true
        schema:
          type: string
      - description: fileDir
        in: body
        name: fileDir
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ImportSourceTableColumnByCSVResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: import source table columns by csv
      tags:
      - channel
  /channel/:channelId/source-table/columns/csv/upload:
    post:
      consumes:
      - application/json
      description: upload source table columns csv
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UploadSourceTableColumnCSVResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: upload source table columns csv
      tags:
      - channel
  /channel/{channelId}:
    delete:
      consumes:
      - application/json
      description: delete a channel
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteChannelResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete a channel
      tags:
      - channel
  /channel/{channelId}/:
    get:
      consumes:
      - application/json
      description: query a channel information
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetChannelResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a channel information
      tags:
      - channel
  /channel/{channelId}/{taskId}/create-tasktabcfg-bycsv:
    post:
      consumes:
      - application/json
      description: create a new task
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: create a new task request parameter
        in: body
        name: task
        required: true
        schema:
          $ref: '#/definitions/message.CreateTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateTaskTabCfgByCsvReq'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create a new task
      tags:
      - channel
  /channel/{channelId}/{taskId}/upload:
    post:
      consumes:
      - application/json
      description: upload csv file for task_table_config
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: upload csv file for task_table_config
      tags:
      - channel
  /channel/{channelId}/column-names:
    post:
      consumes:
      - application/json
      description: get column name list
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: get column list request parameter
        in: body
        name: getColumnNamesReq
        required: true
        schema:
          $ref: '#/definitions/message.GetColumnNamesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetColumnNamesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get column name list
      tags:
      - channel
  /channel/{channelId}/column-names-ora:
    post:
      consumes:
      - application/json
      description: get column name list
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: get column list request parameter
        in: body
        name: getColumnNamesReq
        required: true
        schema:
          $ref: '#/definitions/message.GetColumnNamesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetColumnNamesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get column name list
      tags:
      - channel
  /channel/{channelId}/datasource/{datasourceId}:
    get:
      consumes:
      - application/json
      description: get channel schemas
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetUnselectedDataSourceSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get channel schemas
      tags:
      - channel
    post:
      consumes:
      - application/json
      description: get channel schemas
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetUnselectedDataSourceSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get channel schemas
      tags:
      - channel
  /channel/{channelId}/fix-task-status:
    post:
      consumes:
      - application/json
      description: task execution progress
      parameters:
      - description: get task progress request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.GetTaskProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: task execution progress
      tags:
      - channel
  /channel/{channelId}/pre-check:
    get:
      consumes:
      - application/json
      description: get channel pre-check infos
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetPreCheckInfosByChannelIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get channel pre-check infos
      tags:
      - channel
  /channel/{channelId}/pre-check/:preCheckId:
    get:
      consumes:
      - application/json
      description: get channel pre-check info by id
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetPreCheckInfoByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get channel pre-check info by id
      tags:
      - channel
  /channel/{channelId}/pre-check/execution:
    post:
      consumes:
      - application/json
      description: submit and execution pre-check infos
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: submit and execution pre-check infos request parameter
        in: body
        name: preCheckInfos
        required: true
        schema:
          $ref: '#/definitions/message.ExecutionPreCheckReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ExecutionPreCheckResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: submit and execution pre-check infos
      tags:
      - channel
  /channel/{channelId}/pre-check/update:
    post:
      consumes:
      - application/json
      description: update channel pre-check info
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: update channel pre-check info request parameter
        in: body
        name: preCheckInfo
        required: true
        schema:
          $ref: '#/definitions/message.UpdatePreCheckInfoReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdatePreCheckInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update channel pre-check info
      tags:
      - channel
  /channel/{channelId}/schemas/:
    get:
      consumes:
      - application/json
      description: get channel schemas
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetChannelSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get channel schemas
      tags:
      - channel
    post:
      consumes:
      - application/json
      description: Create channel schemas
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: create channel schemas request parameter
        in: body
        name: channelObjects
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateChannelSchemasReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateChannelSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create channel schemas
      tags:
      - channel
  /channel/{channelId}/schemas/delete:
    post:
      consumes:
      - application/json
      description: delete channel schemas
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchDeleteChannelSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete channel schemas
      tags:
      - channel
  /channel/{channelId}/schemas/update:
    post:
      consumes:
      - application/json
      description: Update channel schemas
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: update channel schemas request parameter
        in: body
        name: channelObjects
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateChannelSchemasReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateChannelSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Update channel schemas
      tags:
      - channel
  /channel/{channelId}/split-task:
    post:
      consumes:
      - application/json
      description: split task into multi task
      parameters:
      - description: split task request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.SplitTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SplitTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: split task into multi task
      tags:
      - channel
  /channel/{channelId}/split-task/verify:
    post:
      consumes:
      - application/json
      description: verify task split
      parameters:
      - description: verify split task request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.VerifySplitTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.VerifySplitTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: verify task split
      tags:
      - channel
  /channel/{channelId}/task:
    delete:
      consumes:
      - application/json
      description: batch delete tasks by ids
      parameters:
      - description: batch delete datasource request parameter
        in: body
        name: taskIds
        required: true
        schema:
          $ref: '#/definitions/message.BatchDeleteTasksReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchDeleteTasksResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch delete tasks by ids
      tags:
      - channel
  /channel/{channelId}/task-type/{taskType}/un-selected-tables:
    post:
      consumes:
      - application/json
      description: get un-selected channel tables
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskType
        in: path
        name: taskType
        required: true
        type: integer
      - description: get un-selected channel tables request parameter
        in: body
        name: queryBody
        required: true
        schema:
          $ref: '#/definitions/message.GetUnSelectedTaskSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetUnSelectedTaskSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get un-selected channel tables
      tags:
      - channel
  /channel/{channelId}/task/{taskId}:
    post:
      consumes:
      - application/json
      description: update task info and channel schema object
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: update task info and channel schema object  request parameter
        in: body
        name: taskReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateTaskAndChannelSchemaObjectByIdReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTaskAndChannelSchemaObjectByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update task info and channel schema object
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/:
    get:
      consumes:
      - application/json
      description: get task info and channel schema object
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskInfoAndChannelSchemaObjectByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get task info and channel schema object
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/add-all:
    get:
      consumes:
      - application/json
      description: add all channel schema tables from unselected tables interface
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OperAllChannelSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: add all channel schema tables from unselected tables interface
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/cluster:
    post:
      consumes:
      - application/json
      description: change selected table partition
      parameters:
      - description: create a new task request parameter
        in: body
        name: task
        required: true
        schema:
          $ref: '#/definitions/message.OperAllChannelSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: change selected table partition
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/config:
    post:
      consumes:
      - application/json
      description: update task info and channel schema object
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: update task info and channel schema object  request parameter
        in: body
        name: taskReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateTaskAndChannelSchemaObjectByIdReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTaskAndChannelSchemaObjectByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update task info and channel schema object
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/create_chunk/download:
    post:
      consumes:
      - application/json
      description: Download Reverse OBJ DDL By IncludingSchema
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.CreateChunkReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateChunkResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download Reverse OBJ DDL By IncludingSchema
      tags:
      - migration
  /channel/{channelId}/task/{taskId}/del-all:
    get:
      consumes:
      - application/json
      description: delete all channel schema tables from unselected tables
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OperAllChannelSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete all channel schema tables from unselected tables
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/detail-result:
    get:
      consumes:
      - application/json
      description: task execution result
      parameters:
      - description: get task detail request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.GetTaskDetailResultReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskDetailResultResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: task execution result
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/drop_chunk/download:
    post:
      consumes:
      - application/json
      description: Download Reverse OBJ DDL By IncludingSchema
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.CreateChunkReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateChunkResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download Reverse OBJ DDL By IncludingSchema
      tags:
      - migration
  /channel/{channelId}/task/{taskId}/execution:
    get:
      consumes:
      - application/json
      description: task execution
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.TaskExecutionResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: task execution
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/execution-precheck:
    post:
      consumes:
      - application/json
      description: task execution precheck
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.TaskExecutionPreCheckResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: task execution precheck
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/modify-channel-schema-tables:
    get:
      consumes:
      - application/json
      description: update channel schema tables to lower case
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update channel schema tables to lower case
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/partition:
    post:
      consumes:
      - application/json
      description: change selected table partition
      parameters:
      - description: create a new task request parameter
        in: body
        name: task
        required: true
        schema:
          $ref: '#/definitions/message.OperAllChannelSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: change selected table partition
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/progress:
    post:
      consumes:
      - application/json
      description: task execution progress
      parameters:
      - description: get task progress request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.GetTaskProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: task execution progress
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/reference-tables:
    post:
      consumes:
      - application/json
      description: reference tables
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: reference tables request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.ReferenceTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ReferenceTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: reference tables
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/schema-tables:
    post:
      consumes:
      - application/json
      description: submit task schemas and tables
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: submit task schemas and tables request parameter
        in: body
        name: tablesReq
        required: true
        schema:
          $ref: '#/definitions/message.SubmitTaskSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SubmitTaskSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: submit task schemas and tables
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/selected-tables:
    post:
      consumes:
      - application/json
      description: get selected channel tables
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: get selected channel tables request parameter
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/message.GetSelectedTaskSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSelectedTaskSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get selected channel tables
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/source-table/columns:
    delete:
      consumes:
      - application/json
      description: delete table columns by conditions
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: table column id
        in: body
        name: tableColumnId
        schema:
          type: integer
      - description: schema name
        in: body
        name: schemaName
        schema:
          type: string
      - description: table name
        in: body
        name: tableName
        schema:
          type: string
      - description: column name
        in: body
        name: columnName
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteSourceTableColumnsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete table columns by conditions
      tags:
      - channel
    get:
      consumes:
      - application/json
      description: get table columns by selected table, filterQueries in [SCHEMA_NAMES
        TABLE_NAME COLUMN_NAME DATA_TYPE DATA_DEFAULT DATA_LENGTH DATA_PRECISION DATA_SCALE
        NULLABLE COLUMN_COMMENT]
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: 'enable query condition, values in [SCHEMA_NAMES TABLE_NAME COLUMN_NAME
          DATA_TYPE DATA_DEFAULT DATA_LENGTH DATA_PRECISION DATA_SCALE NULLABLE COLUMN_COMMENT] '
        in: body
        name: filterQueries
        required: true
        schema:
          type: string
      - description: schemaNames, enable when filterQueries contains SCHEMA_NAMES
        in: body
        name: schemaNames
        schema:
          type: string
      - description: tableName, enable when filterQueries contains TABLE_NAME
        in: body
        name: tableName
        schema:
          type: string
      - description: columnName, enable when filterQueries contains COLUMN_NAME
        in: body
        name: columnName
        schema:
          type: string
      - description: dataType, enable when filterQueries contains DATA_TYPE
        in: body
        name: dataType
        schema:
          type: string
      - description: dataDefault, enable when filterQueries contains DATA_DEFAULT
        in: body
        name: dataDefault
        schema:
          type: string
      - description: dataPrecision, enable when filterQueries contains DATA_PRECISION
        in: body
        name: dataPrecision
        schema:
          type: integer
      - description: dataLength, enable when filterQueries contains DATA_LENGTH
        in: body
        name: dataLength
        schema:
          type: integer
      - description: dataScale, enable when filterQueries contains DATA_SCALE
        in: body
        name: dataScale
        schema:
          type: integer
      - description: nullable, enable when filterQueries contains NULLABLE
        in: body
        name: nullable
        schema:
          type: string
      - description: columnComment, enable when filterQueries contains COLUMN_COMMENT
        in: body
        name: columnComment
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSourceTableColumnsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get table columns by selected table
      tags:
      - channel
    post:
      consumes:
      - application/json
      description: batch update sourceTableColumns
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: all table columns data needed to update, and inner columnDigest
          is required to avoid sensitive issue
        in: body
        name: data
        required: true
        schema:
          items:
            $ref: '#/definitions/message.TableColumnCustomMapRule'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchUpdateSourceTableColumnsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch update sourceTableColumns
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/source-table/columns/sync:
    post:
      consumes:
      - application/json
      description: sync all table columns by all tables in channel, not just specified
        task
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SyncSourceTableColumnsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: sync all table columns by channel_schema_tables
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/source-table/columns/sync/status:
    get:
      consumes:
      - application/json
      description: get table columns sync status, status in [NODATA, FETCHING, FETCHED,
        FAILED]
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSyncSourceTableColumnsStatusResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get table columns sync status
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/task-params/{taskparamTemplateId}:
    get:
      consumes:
      - application/json
      description: get task param list by TaskparamTemplateId
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: taskparamTemplateId
        in: path
        name: taskparamTemplateId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetMergedTaskParamListByTaskparamTemplateIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get task param list by TaskparamTemplateId
      tags:
      - channel
    post:
      consumes:
      - application/json
      description: save task params
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: taskparamTemplateId
        in: path
        name: taskparamTemplateId
        required: true
        type: integer
      - description: save task params request parameter
        in: body
        name: tablesReq
        required: true
        schema:
          $ref: '#/definitions/message.SaveTaskParamsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SaveTaskParamsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: save task params
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/task-table:
    delete:
      consumes:
      - application/json
      description: delete task table configs
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: delete task table configs request parameter
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/message.DeleteTaskTableConfigsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteTaskTableConfigsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete task table configs
      tags:
      - channel
    post:
      consumes:
      - application/json
      description: create task table configs
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: create task table configs request parameter
        in: body
        name: createTaskTableConfigsReq
        required: true
        schema:
          $ref: '#/definitions/message.CreateTaskTableConfigsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateTaskTableConfigsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create task table configs
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/task-table/list:
    get:
      consumes:
      - application/json
      description: get task table config list
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskTableConfigsByTaskIdAndChannelIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get task table config list
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/task-table/update:
    post:
      consumes:
      - application/json
      description: update task table configs
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: update task table configs request parameter
        in: body
        name: updateTaskTableConfigsReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateTaskTableConfigsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTaskTableConfigsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update task table configs
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/to-lower:
    get:
      consumes:
      - application/json
      description: update channel schema tables to lower case
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OperAllChannelSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update channel schema tables to lower case
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/to-lower-schema:
    get:
      consumes:
      - application/json
      description: update channel schema tables to lower case
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OperAllChannelSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update channel schema tables to lower case
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/to-upper:
    get:
      consumes:
      - application/json
      description: update channel schema tables to upper case
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OperAllChannelSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update channel schema tables to upper case
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/to-upper-schema:
    get:
      consumes:
      - application/json
      description: update channel schema tables to upper case
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OperAllChannelSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update channel schema tables to upper case
      tags:
      - channel
  /channel/{channelId}/task/{taskId}/update-schema-tables:
    post:
      consumes:
      - application/json
      description: submit task schemas and tables
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: submit task schemas and tables request parameter
        in: body
        name: tablesReq
        required: true
        schema:
          $ref: '#/definitions/message.SubmitTaskSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SubmitTaskSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: submit task schemas and tables
      tags:
      - channel
  /channel/{channelId}/task/create-bycsv:
    post:
      consumes:
      - application/json
      description: create a new task
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: create a new task request parameter
        in: body
        name: task
        required: true
        schema:
          $ref: '#/definitions/message.CreateTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create a new task
      tags:
      - channel
  /channel/{channelId}/task/create-byref:
    post:
      consumes:
      - application/json
      description: create a new task
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: create a new task request parameter
        in: body
        name: task
        required: true
        schema:
          $ref: '#/definitions/message.CreateTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create a new task
      tags:
      - channel
  /channel/{channelId}/task/create-default:
    post:
      consumes:
      - application/json
      description: create default tasks
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateDefaultTasksResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create default tasks
      tags:
      - channel
  /channel/{channelId}/task/create-new:
    post:
      consumes:
      - application/json
      description: create a new task
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: create a new task request parameter
        in: body
        name: task
        required: true
        schema:
          $ref: '#/definitions/message.CreateTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create a new task
      tags:
      - channel
  /channel/{channelId}/task/list:
    get:
      consumes:
      - application/json
      description: list tasks by channelId
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: object query
        in: query
        name: objectQuery
        type: string
      - description: use for cluster mode to list subtasks
        in: query
        name: parentTaskId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListTasksByChannelIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list tasks by channelId
      tags:
      - channel
  /channel/{channelId}/update:
    post:
      consumes:
      - application/json
      description: Update a channel
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      - description: update channel request parameter
        in: body
        name: channelInfo
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateChannelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateChannelResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Update a channel
      tags:
      - channel
  /channel/{channelId}/upload:
    post:
      consumes:
      - application/json
      description: upload csv file
      parameters:
      - description: channelId
        in: path
        name: channelId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: upload csv file
      tags:
      - channel
  /channel/download/template:
    post:
      consumes:
      - application/json
      description: Download Reverse OBJ DDL By IncludingSchema
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDDLDownloadReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download Reverse OBJ DDL By IncludingSchema
      tags:
      - channel
  /channel/list:
    post:
      consumes:
      - application/json
      description: list channel
      parameters:
      - description: list channel request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListChannelsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/message.Channel'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list channel
      tags:
      - channel
  /channel/task/{taskType}:
    get:
      consumes:
      - application/json
      description: get task param template list by task type
      parameters:
      - description: taskType
        in: path
        name: taskType
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskParamTemplateListByTaskTypeResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get task param template list by task type
      tags:
      - channel
  /data-compare/detail-chunk/table:
    post:
      consumes:
      - application/json
      description: query data compare table chunk detail
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DataCompareTableChunkReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DataCompareTableChunkResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query data compare table chunk detail
      tags:
      - datacompare
  /data-compare/detail-table/schema:
    post:
      consumes:
      - application/json
      description: query data compare summary schema
      parameters:
      - description: get data compare request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DataCompareSchemaStateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.DataCompareSchemaStateResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query data compare summary schema
      tags:
      - datacompare
  /data-compare/env-deploy/{taskId}:
    get:
      consumes:
      - application/json
      description: list data compare env deploy tasks
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListDataCompareEnvDeployTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list data compare env deploy tasks
      tags:
      - datacompare
  /data-compare/env-deploy/{taskId}/{envDeployId}:
    post:
      consumes:
      - application/json
      description: update data compare env deploy task
      parameters:
      - description: update data compare env deploy task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateDataCompareEnvDeployTaskReq'
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: envDeployId
        in: path
        name: envDeployId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateDataCompareEnvDeployTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update data compare env deploy task
      tags:
      - datacompare
  /data-compare/env-deploy/{taskId}/execute:
    post:
      consumes:
      - application/json
      description: execute data compare env deploy tasks, runMode = [ 1,2 ], 1 means
        execute(ignore success task), 2 means re-execute(all task status)
      parameters:
      - description: execute data compare env deploy tasks request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ExecuteDataCompareEnvDeployTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ExecuteDataCompareEnvDeployTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: execute data compare env deploy tasks
      tags:
      - datacompare
  /data-compare/fix-sql/content:
    post:
      consumes:
      - application/json
      description: get fix sql file content with page-based pagination and optional
        filtering
      parameters:
      - description: get fix sql content request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.GetFixSQLContentReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.GetFixSQLContentResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get fix sql file content
      tags:
      - datacompare
  /data-compare/fix-sql/download:
    post:
      consumes:
      - application/json
      description: download fix sql
      parameters:
      - description: download fix sql parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DownloadFixSQLReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadFixSQLResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: download fix sql
      tags:
      - datacompare
  /data-compare/fix-sql/list:
    post:
      consumes:
      - application/json
      description: list fix sql files with conditions and pagination
      parameters:
      - description: list fix sql request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ListFixSQLReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListFixSQLResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list fix sql files with pagination
      tags:
      - datacompare
  /data-compare/re-execution:
    post:
      consumes:
      - application/json
      description: data compare task re-execution
      parameters:
      - description: data compare task re-execution request parameter
        in: body
        name: dataCompareReExecutionReq
        required: true
        schema:
          $ref: '#/definitions/message.DataCompareReExecutionReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DataCompareReExecutionResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: data compare task re-execution
      tags:
      - datacompare
  /data-compare/summary/schema:
    post:
      consumes:
      - application/json
      description: query data compare summary schema
      parameters:
      - description: get data compare request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DataCompareSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DataCompareSchemaResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query data compare summary schema
      tags:
      - datacompare
  /datasource/:
    post:
      consumes:
      - application/json
      description: create datasource
      parameters:
      - description: create datasource request parameter
        in: body
        name: datasource
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateDataSourceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateDataSourceResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create  datasource
      tags:
      - datasource
  /datasource/:dataSourceId/getPartitions:
    post:
      consumes:
      - application/json
      description: get datasource schemas
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDataSourceSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource schemas
      tags:
      - datasource
  /datasource/:dataSourceId/getPrivilegeCheckList:
    get:
      consumes:
      - application/json
      description: get datasource schemas
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDataSourceSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource schemas
      tags:
      - datasource
  /datasource/:dataSourceId/schema/tables:
    post:
      consumes:
      - application/json
      description: get datasource tables in schemas
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      - description: get datasource tables in schemas request parameter
        in: body
        name: schemas
        required: true
        schema:
          $ref: '#/definitions/message.GetDataSourceSchemaTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDataSourceSchemaTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource tables in schemas
      tags:
      - datasource
  /datasource/:dataSourceId/schemas:
    get:
      consumes:
      - application/json
      description: get datasource schemas
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDataSourceSchemasResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource schemas
      tags:
      - datasource
  /datasource/{dataSourceId}/:
    get:
      consumes:
      - application/json
      description: query a datasource detail
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DetailDataSourceResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a datasource detail
      tags:
      - datasource
  /datasource/{dataSourceId}/test:
    get:
      consumes:
      - application/json
      description: test  datasource connection
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.TestConnectionResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: test  datasource connection
      tags:
      - datasource
  /datasource/{dataSourceId}/update:
    post:
      consumes:
      - application/json
      description: update datasource
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      - description: update datasource request parameter
        in: body
        name: datasource
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateDataSourceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateDataSourceResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update  datasource
      tags:
      - datasource
  /datasource/delete:
    post:
      consumes:
      - application/json
      description: batch delete datasource
      parameters:
      - description: batch delete datasource request parameter
        in: body
        name: dataSourceIds
        required: true
        schema:
          $ref: '#/definitions/message.BatchDeleteDataSourceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchDeleteDataSourceResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch delete  datasource
      tags:
      - datasource
  /datasource/list:
    post:
      consumes:
      - application/json
      description: list  datasource
      parameters:
      - description: list datasource request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListDataSourcesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/message.DataSource'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list  datasource
      tags:
      - datasource
  /datasource/oracle/versionCode:
    post:
      consumes:
      - application/json
      description: get datasource version code
      parameters:
      - description: get datasource version code request parameter
        in: body
        name: datasource
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateDataSourceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateDataSourceResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource version code
      tags:
      - datasource
  /datasource/try:
    post:
      consumes:
      - application/json
      description: try datasource connection
      parameters:
      - description: dataSourceId
        in: path
        name: dataSourceId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.TestConnectionResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: try datasource connection
      tags:
      - datasource
  /datasource/version:
    post:
      consumes:
      - application/json
      description: get datasource version
      parameters:
      - description: get datasource version request parameter
        in: body
        name: datasource
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateDataSourceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateDataSourceResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource version
      tags:
      - datasource
  /genkey:
    get:
      consumes:
      - application/json
      description: get key
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GenKeyResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get key
      tags:
      - license
  /increment/dsg/config/physical-sub-systems:
    get:
      consumes:
      - application/json
      description: list all physical sub system
      parameters:
      - in: formData
        name: groupMachineDesc
        type: string
      - in: formData
        name: groupMachineName
        type: string
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListPhysicalSubSystemResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list all physical sub system
      tags:
      - increment
  /increment/dsg/config/supplemental-log:
    get:
      consumes:
      - application/json
      description: download supplement log sql
      parameters:
      - description: download supplement log sql parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DownloadSupplementalLogReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadSupplementalLogResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: download supplement log sql
      tags:
      - increment
  /increment/dsg/host/{macId}:
    get:
      consumes:
      - application/json
      description: get datasource host interface
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.OpMachineConf'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource host interface
      tags:
      - increment
  /increment/dsg/host/delete:
    delete:
      consumes:
      - application/json
      description: delete datasource host interface
      parameters:
      - description: delete datasource host request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.DeleteHostReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteHostResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete datasource host interface
      tags:
      - increment
  /increment/dsg/host/list:
    get:
      consumes:
      - application/json
      description: list  datasource
      parameters:
      - description: list datasource host request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListDataSourceHostReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/message.OpMachineConf'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list  datasource
      tags:
      - increment
  /increment/dsg/host/save:
    post:
      consumes:
      - application/json
      description: save datasource host interface
      parameters:
      - description: save datasource host request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.SaveHostReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SaveHostResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: save datasource host interface
      tags:
      - increment
  /increment/dsg/host/used-ports:
    get:
      consumes:
      - application/json
      description: get host used ports
      parameters:
      - description: get host used ports req
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.GetHostUsedPortsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetHostUsedPortsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get host used ports
      tags:
      - increment
  /increment/dsg/host/verify:
    get:
      consumes:
      - application/json
      description: verify host interface
      parameters:
      - description: verify host request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.VerifyHostReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.VerifyHostResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: verify host interface
      tags:
      - increment
  /increment/dsg/task/config/source:
    get:
      consumes:
      - application/json
      description: get source config [vm]
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSourceConfigResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get source config [vm]
      tags:
      - increment
    patch:
      consumes:
      - application/json
      description: update source config [vm]
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      - in: formData
        name: vm
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateSourceConfigResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update source config [vm]
      tags:
      - increment
  /increment/dsg/task/config/table/mapping:
    get:
      consumes:
      - application/json
      description: get dsg task table mapping
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTableMappingResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task table mapping
      tags:
      - increment
    patch:
      consumes:
      - application/json
      description: update dsg task table mapping
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTableMappingResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update dsg task table mapping
      tags:
      - increment
  /increment/dsg/task/config/table/mapping/verify:
    get:
      consumes:
      - application/json
      description: verify dsg task table mapping with tms
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.VerifyTableMappingResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: verify dsg task table mapping with tms
      tags:
      - increment
  /increment/dsg/task/config/target:
    get:
      consumes:
      - application/json
      description: get target config [yloader.ini]
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTargetConfigResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get target config [yloader.ini]
      tags:
      - increment
    patch:
      consumes:
      - application/json
      description: update target config [yloader.ini]
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTargetConfigResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update target config [yloader.ini]
      tags:
      - increment
  /increment/dsg/task/detail:
    get:
      consumes:
      - application/json
      description: get a dsg tasks
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get a dsg tasks
      tags:
      - increment
  /increment/dsg/task/installation/info:
    get:
      consumes:
      - application/json
      description: get installation info
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetInstallationInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get installation info
      tags:
      - increment
  /increment/dsg/task/operation/clear-cache:
    post:
      consumes:
      - application/json
      description: clear dsg task cache
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ClearCacheResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: clear dsg task cache
      tags:
      - increment
  /increment/dsg/task/operation/delete:
    delete:
      consumes:
      - application/json
      description: delete dsg task
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete dsg task
      tags:
      - increment
  /increment/dsg/task/operation/install-binary:
    post:
      consumes:
      - application/json
      description: install dsg task
      parameters:
      - in: formData
        name: sourceIp
        required: true
        type: string
      - in: formData
        name: sourcePath
        required: true
        type: string
      - in: formData
        name: targetIp
        required: true
        type: string
      - in: formData
        name: targetPath
        required: true
        type: string
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.InstallDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: install dsg task
      tags:
      - increment
  /increment/dsg/task/operation/install-status:
    get:
      consumes:
      - application/json
      description: get install dsg task status
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetInstallDsgTaskStatusResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get install dsg task status
      tags:
      - increment
  /increment/dsg/task/operation/logs:
    get:
      consumes:
      - application/json
      description: get dsg task operation logs
      parameters:
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskOperationLogsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task operation logs
      tags:
      - increment
  /increment/dsg/task/operation/monitor-info:
    get:
      consumes:
      - application/json
      description: get dsg task monitor info
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskMonitorInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task monitor info
      tags:
      - increment
  /increment/dsg/task/operation/pause-migrate:
    post:
      consumes:
      - application/json
      description: pause sync dsg task
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.PauseSyncDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: pause sync dsg task
      tags:
      - increment
  /increment/dsg/task/operation/performance-stat:
    get:
      consumes:
      - application/json
      description: get dsg task performance stat, including each table's performance
      parameters:
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskPerformanceStatResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task performance stat, including each table's performance
      tags:
      - increment
  /increment/dsg/task/operation/performance-stat/detail:
    get:
      consumes:
      - application/json
      description: get dsg task performance stat, including each table's performance
      parameters:
      - in: formData
        maximum: 1440
        minimum: 1
        name: avgMinute
        required: true
        type: integer
      - in: formData
        maximum: 168
        minimum: 1
        name: intervalHour
        required: true
        type: integer
      - in: formData
        name: schemaName
        type: string
      - in: formData
        name: tableName
        type: string
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskPerformanceStatDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task performance stat, including each table's performance
      tags:
      - increment
  /increment/dsg/task/operation/performance-stat/top:
    get:
      consumes:
      - application/json
      description: get dsg task performance top stat, including each table's performance
      parameters:
      - in: formData
        maximum: 168
        minimum: 1
        name: intervalHour
        required: true
        type: integer
      - in: formData
        name: schemaName
        type: string
      - enum:
        - INSERT
        - UPDATE
        - DELETE
        - DDL
        - TOTAL
        in: formData
        name: sortBy
        required: true
        type: string
      - in: formData
        name: tableName
        type: string
      - in: formData
        name: taskId
        required: true
        type: integer
      - in: formData
        maximum: 200
        minimum: 1
        name: topNum
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskPerformanceStatTopResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task performance top stat, including each table's performance
      tags:
      - increment
  /increment/dsg/task/operation/resume-migrate:
    post:
      consumes:
      - application/json
      description: resume sync dsg task
      parameters:
      - in: formData
        name: changeScn
        type: boolean
      - in: formData
        name: scnInputValue
        type: string
      - enum:
        - scn
        - time
        in: formData
        name: scnType
        type: string
        x-enum-varnames:
        - SCNType_SCN
        - SCNType_TIME
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.PauseSyncDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: resume sync dsg task
      tags:
      - increment
  /increment/dsg/task/operation/start-binary:
    post:
      consumes:
      - application/json
      description: start dsg task
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StartDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: start dsg task
      tags:
      - increment
  /increment/dsg/task/operation/stop-binary:
    post:
      consumes:
      - application/json
      description: stop dsg task
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StopDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: stop dsg task
      tags:
      - increment
  /increment/dsg/task/operation/stop-migrate:
    post:
      consumes:
      - application/json
      description: sync dsg task
      parameters:
      - enum:
        - scn
        - time
        in: formData
        name: scnType
        type: string
        x-enum-varnames:
        - SCNType_SCN
        - SCNType_TIME
      - in: formData
        name: scnValue
        required: true
        type: string
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StartSyncDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: sync dsg task
      tags:
      - increment
  /increment/dsg/task/operation/warning-info:
    get:
      consumes:
      - application/json
      description: get dsg task warning info
      parameters:
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDsgTaskWarningInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get dsg task warning info
      tags:
      - increment
  /increment/dsg/tasks:
    get:
      consumes:
      - application/json
      description: list all dsg tasks
      parameters:
      - in: formData
        name: dsgTaskId
        type: integer
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - in: formData
        name: taskName
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListAllDsgTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list all dsg tasks
      tags:
      - increment
  /increment/dsg/token:
    get:
      consumes:
      - application/json
      description: get token
      parameters:
      - in: formData
        name: acceptCharset
        type: string
      - in: formData
        name: acceptEncoding
        type: string
      - in: formData
        name: acceptLanguage
        type: string
      - in: formData
        name: enableSimulate
        type: boolean
      - in: formData
        name: userAgent
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTokenResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get token
      tags:
      - increment
  /license:
    get:
      consumes:
      - application/json
      description: get license expired time
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetLicenseInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get license expired time
      tags:
      - license
  /license/features:
    get:
      consumes:
      - application/json
      description: list license features
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListLicenseFeaturesReq'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list license features
      tags:
      - license
    post:
      consumes:
      - application/json
      description: update license features
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateLicenseFeaturesReq'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update license features
      tags:
      - license
  /migration/csvdata/chunk-meta/migration-data/details:
    post:
      consumes:
      - application/json
      description: query migration details
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.QueryMigrationDetailsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query migration details
      tags:
      - migration
  /migration/csvdata/chunk-meta/migration-data/statistic:
    post:
      consumes:
      - application/json
      description: get migration data statistic
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataStatisticResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get migration data statistic
      tags:
      - migration
  /migration/csvdata/chunk-meta/migration-data/update-detail:
    patch:
      consumes:
      - application/json
      description: update migration details
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.QueryMigrationDetailsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update migration details
      tags:
      - migration
  /migration/csvdata/chunk-meta/save-migration-data:
    post:
      consumes:
      - application/json
      description: save migration data by walk path
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SaveMigrationDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: save migration data by walk path
      tags:
      - migration
  /migration/csvdata/chunk-meta/walk-dir:
    post:
      consumes:
      - application/json
      description: walk csv dir for get all csv files
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.WalkCSVDirResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: walk csv dir for get all csv files
      tags:
      - migration
  /migration/csvdata/chunk/error:
    post:
      consumes:
      - application/json
      description: query a full data migration failed chunk error detail in a table
      parameters:
      - description: get full data migration failed chunk error detail request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataChunkErrorReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataChunkErrorResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration failed chunk error detail in a table
      tags:
      - migration
  /migration/csvdata/chunk/failed:
    post:
      consumes:
      - application/json
      description: query a full data migration failed chunk in a table
      parameters:
      - description: get full data migration failed chunk request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataChunkFailedAndWaitingReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataChunkFailedAndWaitingResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration failed chunk in a table
      tags:
      - migration
  /migration/csvdata/detail/schema:
    post:
      consumes:
      - application/json
      description: query a full data migration task schema detail
      parameters:
      - description: get full data migration task schema detail request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataSchemaDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task schema detail
      tags:
      - migration
  /migration/csvdata/progressbar:
    post:
      consumes:
      - application/json
      description: query a full data migration task progress bar
      parameters:
      - description: get a full data migration task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task progress bar
      tags:
      - migration
  /migration/csvdata/progresslog:
    post:
      consumes:
      - application/json
      description: query a full data migration task progress log
      parameters:
      - description: get a full data migration task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task progress log
      tags:
      - migration
  /migration/csvdata/retry/batch-table:
    post:
      consumes:
      - application/json
      description: batch retry a full data migration task by table
      parameters:
      - description: batch try a full data migration task by table request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataBatchRetryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch retry a full data migration task by table
      tags:
      - migration
  /migration/csvdata/retry/table:
    post:
      consumes:
      - application/json
      description: retry a csv data migration task some failed chunk in a table
      parameters:
      - description: try a csv data migration task some failed chunk by table request
          parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataFailedChunkBatchRetryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: retry a csv data migration task some failed chunk in a table
      tags:
      - migration
  /migration/csvdata/summary/{taskId}/:
    get:
      consumes:
      - application/json
      description: query a full data migration task summary
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataTaskSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task summary
      tags:
      - migration
  /migration/csvdata/summary/schema:
    post:
      consumes:
      - application/json
      description: query a full data migration task schema summary
      parameters:
      - description: get full data migration schema request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataSchemaSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task schema summary
      tags:
      - migration
  /migration/fulldata/chunk-data/fetch:
    post:
      consumes:
      - application/json
      description: fetch incorrect table data by schema name and table name
      parameters:
      - description: fetch incorrect table data by schema name and table name
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.FetchChunkDataReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.FetchChunkDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: fetch incorrect table data by schema name and table name
      tags:
      - migration
  /migration/fulldata/chunk-data/list:
    get:
      consumes:
      - application/json
      description: list incorrect table data by schema name and table name
      parameters:
      - description: list incorrect table data by schema name and table name
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ListChunkDataReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListChunkDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list incorrect table data by schema name and table name
      tags:
      - migration
  /migration/fulldata/chunk-data/query:
    post:
      consumes:
      - application/json
      description: list incorrect table data by schema name and table name
      parameters:
      - description: list incorrect table data by schema name and table name
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ListChunkDataReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListChunkDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list incorrect table data by schema name and table name
      tags:
      - migration
  /migration/fulldata/chunk-data/refetch:
    post:
      consumes:
      - application/json
      description: fetch incorrect table data by schema name and table name
      parameters:
      - description: fetch incorrect table data by schema name and table name
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.FetchChunkDataReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.FetchChunkDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: fetch incorrect table data by schema name and table name
      tags:
      - migration
  /migration/fulldata/chunk-data/replay:
    post:
      consumes:
      - application/json
      description: replay incorrect table data by schema name and table name
      parameters:
      - description: replay incorrect table data by schema name and table name
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ReplayChunkDataReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ReplayChunkDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: replay incorrect table data by schema name and table name
      tags:
      - migration
  /migration/fulldata/chunk-data/summary:
    get:
      consumes:
      - application/json
      description: get chunk data status, fetching or replaying or finished or waiting
      parameters:
      - description: list incorrect table data by schema name and table name
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.GetChunkDataSummaryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetChunkDataSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get chunk data status, fetching or replaying or finished or waiting
      tags:
      - migration
  /migration/fulldata/chunk/error:
    post:
      consumes:
      - application/json
      description: query a full data migration failed chunk error detail in a table
      parameters:
      - description: get full data migration failed chunk error detail request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataChunkErrorReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataChunkErrorResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration failed chunk error detail in a table
      tags:
      - migration
  /migration/fulldata/chunk/failed:
    post:
      consumes:
      - application/json
      description: query a full data migration failed chunk in a table
      parameters:
      - description: get full data migration failed chunk request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataChunkFailedAndWaitingReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataChunkFailedAndWaitingResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration failed chunk in a table
      tags:
      - migration
  /migration/fulldata/detail/schema:
    post:
      consumes:
      - application/json
      description: query a full data migration task schema detail
      parameters:
      - description: get full data migration task schema detail request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataSchemaDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task schema detail
      tags:
      - migration
  /migration/fulldata/progressbar:
    post:
      consumes:
      - application/json
      description: query a full data migration task progress bar
      parameters:
      - description: get a full data migration task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task progress bar
      tags:
      - migration
  /migration/fulldata/progresslog:
    post:
      consumes:
      - application/json
      description: query a full data migration task progress log
      parameters:
      - description: get a full data migration task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task progress log
      tags:
      - migration
  /migration/fulldata/retry/batch-table:
    post:
      consumes:
      - application/json
      description: batch retry a full data migration task by table
      parameters:
      - description: batch try a full data migration task by table request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataBatchRetryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch retry a full data migration task by table
      tags:
      - migration
  /migration/fulldata/retry/chunk:
    post:
      consumes:
      - application/json
      description: retry a full data migration task some failed chunk in a table
      parameters:
      - description: try a full data migration task some failed chunk by table request
          parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataFailedChunkRetryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: retry a full data migration task some failed chunk in a table
      tags:
      - migration
  /migration/fulldata/retry/table:
    post:
      consumes:
      - application/json
      description: retry a full data migration task some failed chunk in a table
      parameters:
      - description: try a full data migration task some failed chunk by table request
          parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataFailedChunkBatchRetryReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.CommonResult'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: retry a full data migration task some failed chunk in a table
      tags:
      - migration
  /migration/fulldata/summary/{taskId}/:
    get:
      consumes:
      - application/json
      description: query a full data migration task summary
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataTaskSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task summary
      tags:
      - migration
  /migration/fulldata/summary/schema:
    post:
      consumes:
      - application/json
      description: query a full data migration task schema summary
      parameters:
      - description: get full data migration schema request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDataSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationDataSchemaSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a full data migration task schema summary
      tags:
      - migration
  /migration/reverse/ddl/byschema:
    post:
      consumes:
      - application/json
      description: query a schema reverse object success detail
      parameters:
      - description: get reverse success obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object success detail
      tags:
      - migration
  /migration/reverse/ddl/compatible:
    post:
      consumes:
      - application/json
      description: query a schema reverse object compatible detail
      parameters:
      - description: get reverse compatible obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object compatible detail
      tags:
      - migration
  /migration/reverse/ddl/download:
    post:
      consumes:
      - application/json
      description: Download Reverse OBJ DDL By IncludingSchema
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDDLDownloadReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download Reverse OBJ DDL By IncludingSchema
      tags:
      - migration
  /migration/reverse/ddl/failed:
    post:
      consumes:
      - application/json
      description: query a schema reverse object failed detail
      parameters:
      - description: get reverse failed obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object failed detail
      tags:
      - migration
  /migration/reverse/ddl/oracleddl:
    post:
      consumes:
      - application/json
      description: query a schema reverse object success detail
      parameters:
      - description: get reverse success obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object success detail
      tags:
      - migration
  /migration/reverse/ddl/originddl:
    post:
      consumes:
      - application/json
      description: query a schema reverse object success detail
      parameters:
      - description: get reverse success obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object success detail
      tags:
      - migration
  /migration/reverse/ddl/rerun:
    post:
      consumes:
      - application/json
      description: run a reverse schema object ddl
      parameters:
      - description: reverse obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationDDLReRunReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLReRunResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: run a reverse schema object ddl
      tags:
      - migration
  /migration/reverse/ddl/schema:
    post:
      consumes:
      - application/json
      description: query a schema reverse object ddl detail
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object ddl detail
      tags:
      - migration
  /migration/reverse/ddl/success:
    post:
      consumes:
      - application/json
      description: query a schema reverse object success detail
      parameters:
      - description: get reverse success obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object success detail
      tags:
      - migration
  /migration/reverse/ddl/table:
    post:
      consumes:
      - application/json
      description: query a schema reverse object success detail
      parameters:
      - description: get reverse success obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaPageReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTableDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a schema reverse object success detail
      tags:
      - migration
  /migration/reverse/ddl/task:
    post:
      consumes:
      - application/json
      description: query a task reverse object ddl detail
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a task reverse object ddl detail
      tags:
      - migration
  /migration/reverse/progressbar:
    post:
      consumes:
      - application/json
      description: query a reverse object progress bar
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a reverse object progress bar
      tags:
      - migration
  /migration/reverse/progresslog:
    post:
      consumes:
      - application/json
      description: query a reverse object progress log
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationProgressReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureDDLProgressResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a reverse object progress log
      tags:
      - migration
  /migration/reverse/summary/{taskId}/:
    get:
      consumes:
      - application/json
      description: query a reverse object task summary
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureTaskSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a reverse object task summary
      tags:
      - migration
  /migration/reverse/summary/schema:
    post:
      consumes:
      - application/json
      description: query a reverse object schema detail
      parameters:
      - description: get reverse ddl obj request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.MigrationSchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.MigrationStructureSchemaSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a reverse object schema detail
      tags:
      - migration
  /mysql/dm/list-members:
    post:
      consumes:
      - application/json
      description: list dm members
      parameters:
      - in: formData
        name: masterAddr
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListMembersResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list dm members
      tags:
      - mysql
  /mysql/dm/list-sources:
    post:
      consumes:
      - application/json
      description: list dm sources
      parameters:
      - in: formData
        name: masterAddr
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListSourcesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list dm sources
      tags:
      - mysql
  /object-parser/ai/convert/plsql-to-java:
    post:
      consumes:
      - application/json
      description: convert plsql to java in manual
      parameters:
      - in: formData
        name: objectName
        type: string
      - in: formData
        name: objectType
        type: string
      - in: formData
        name: packageName
        type: string
      - in: formData
        name: prompt
        type: string
      - in: formData
        name: schemaName
        type: string
      - in: formData
        name: sql
        required: true
        type: string
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ConvertPLSQLToJavaInManualResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: convert plsql to java in manual
      tags:
      - objectParser
  /object-parser/ai/convert/plsql-to-java/history-download:
    post:
      consumes:
      - application/json
      description: download java code
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadJavaCodesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: download java code
      tags:
      - objectParser
  /object-parser/ai/convert/plsql-to-java/logs:
    post:
      consumes:
      - application/json
      description: get convert plsql to java logs
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetPLSQLToJavaLogsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get convert plsql to java logs
      tags:
      - objectParser
  /object-parser/ai/convert/plsql-to-java/results:
    post:
      consumes:
      - application/json
      description: get convert plsql to java results
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetPLSQLToJavaHistoryResultsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get convert plsql to java results
      tags:
      - objectParser
  /object-parser/ai/convert/plsql-to-java/summary:
    post:
      consumes:
      - application/json
      description: get convert plsql to java summary
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetPLSQLToJavaSummaryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get convert plsql to java summary
      tags:
      - objectParser
  /object-parser/ai/test-connect:
    post:
      consumes:
      - application/json
      description: test ai connect
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.TestAIConnectResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: test ai connect
      tags:
      - objectParser
  /object-parser/histogram/archive-data:
    post:
      consumes:
      - application/json
      description: get histogram of archive data
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetArchiveDataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get histogram of archive data
      tags:
      - objectParser
  /object-parser/histogram/archive-times:
    post:
      consumes:
      - application/json
      description: get histogram of archive times
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetArchiveTimesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get histogram of archive times
      tags:
      - objectParser
  /object-parser/histogram/log-volume-per-second:
    post:
      consumes:
      - application/json
      description: get histogram of log volume per second
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: divideBy
        type: number
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetLogVolumePerSecondResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get histogram of log volume per second
      tags:
      - objectParser
  /object-parser/histogram/transaction-data-blocks:
    post:
      consumes:
      - application/json
      description: get histogram of transaction data blocks
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: divideBy
        type: number
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTransactionDataBlocksResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get histogram of transaction data blocks
      tags:
      - objectParser
  /object-parser/histogram/transaction-log-volume:
    post:
      consumes:
      - application/json
      description: get histogram of transaction log volume
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: divideBy
        type: number
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTransactionLogVolumeResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get histogram of transaction log volume
      tags:
      - objectParser
  /object-parser/histogram/transaction-per-second:
    post:
      consumes:
      - application/json
      description: get histogram of transaction data blocks
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTransactionPerSecondResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get histogram of transaction data blocks
      tags:
      - objectParser
  /object-parser/incompatible-feature/basic:
    post:
      consumes:
      - application/json
      description: list basic incompatible feature
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListBasicIncompatibleFeatureResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list basic incompatible feature
      tags:
      - objectParser
  /object-parser/incompatible-feature/basic/update:
    post:
      consumes:
      - application/json
      description: update basic incompatible feature
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateBasicIncompatibleFeatureResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update basic incompatible feature
      tags:
      - objectParser
  /object-parser/incompatible-feature/scoring:
    post:
      consumes:
      - application/json
      description: get incompatible scoring
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetIncompatibleFeatureScoringResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get incompatible scoring
      tags:
      - objectParser
  /object-parser/incompatible-feature/task:
    post:
      consumes:
      - application/json
      description: list task incompatible feature
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListTaskIncompatibleFeatureResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list task incompatible feature
      tags:
      - objectParser
  /object-parser/incompatible-feature/task/update:
    post:
      consumes:
      - application/json
      description: update task incompatible feature
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTaskIncompatibleFeatureResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update task incompatible feature
      tags:
      - objectParser
  /object-parser/metadata/analyze/details:
    post:
      consumes:
      - application/json
      description: list analyze details by page
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListAnalyzeDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list analyze details by page
      tags:
      - objectParser
  /object-parser/metadata/definitions:
    post:
      consumes:
      - application/json
      description: get datasource package/procedure/function definition from metadata
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: dependencyUUIDs
        type: array
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDefinitionsFromMetadataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource package/procedure/function definition from metadata
      tags:
      - objectParser
  /object-parser/metadata/dependency:
    post:
      consumes:
      - application/json
      description: get datasource package/procedure dependency from metadata，fields
        control output field, 1 means dependencies(for backend only),2 means tree,3
        means graph
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: excludeInvalidObject
        type: boolean
      - collectionFormat: csv
        in: formData
        items:
          type: integer
        name: fields
        type: array
      - in: formData
        name: includePromptRelation
        type: boolean
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: objectKeyFilters
        type: array
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: objectUUIDFilters
        type: array
      - in: formData
        name: orderType
        type: integer
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: schemas
        type: array
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetDependencyFromMetadataResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get datasource package/procedure dependency from metadata
      tags:
      - objectParser
  /object-parser/metadata/plsql/json:
    post:
      consumes:
      - application/json
      description: parse plsql to json format
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: comment
        type: string
      - in: formData
        name: dependencyUUID
        type: string
      - in: formData
        name: plsql
        type: string
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.PlSQLToJSONResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: parse plsql to json format
      tags:
      - objectParser
  /object-parser/prompt/delete:
    delete:
      consumes:
      - application/json
      description: delete task prompt
      parameters:
      - in: formData
        name: promptID
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeletePromptResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete task prompt
      tags:
      - objectParser
  /object-parser/prompt/relation/binding:
    delete:
      consumes:
      - application/json
      description: delete task object prompt relation
      parameters:
      - in: formData
        name: relationId
        required: true
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteTaskObjectPromptRelationResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete task object prompt relation
      tags:
      - objectParser
    post:
      consumes:
      - application/json
      description: save task object prompt relation
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: dependencyUUID
        required: true
        type: string
      - in: formData
        name: relationId
        type: integer
      - in: formData
        name: taskId
        required: true
        type: integer
      - in: formData
        name: taskPromptId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SaveTaskObjectPromptRelationResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: save task object prompt relation
      tags:
      - objectParser
  /object-parser/prompt/relation/list:
    post:
      consumes:
      - application/json
      description: list task prompt
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListPromptResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list task prompt
      tags:
      - objectParser
  /object-parser/prompt/save:
    post:
      consumes:
      - application/json
      description: save task prompt
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.SavePromptResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: save task prompt
      tags:
      - objectParser
  /object-parser/test/object-detail:
    post:
      consumes:
      - application/json
      description: get object detail
      parameters:
      - in: formData
        name: channelId
        required: true
        type: integer
      - in: formData
        name: shouldCorrupt
        type: boolean
      - in: formData
        name: shouldEncode
        type: boolean
      - in: formData
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetObjectDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get object detail
      tags:
      - objectParser
  /rule/default-value/:
    post:
      consumes:
      - application/json
      description: Create a column default value rule
      parameters:
      - description: create a column default value rule request parameter
        in: body
        name: colDefaultMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateColDefaultMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateColDefaultMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create a column default value rule
      tags:
      - rule
  /rule/default-value/{colDefaultMapRuleId}:
    get:
      consumes:
      - application/json
      description: query column default value rule detail by id
      parameters:
      - description: colDefaultMapRuleId
        in: path
        name: colDefaultMapRuleId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetColDefaultMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query column default value rule detail by id
      tags:
      - rule
  /rule/default-value/{colDefaultMapRuleId}/update:
    post:
      consumes:
      - application/json
      description: update a column default value rule
      parameters:
      - description: colDefaultMapRuleId
        in: path
        name: colDefaultMapRuleId
        required: true
        type: integer
      - description: update a column default value rule request parameter
        in: body
        name: colDefaultMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateColDefaultMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateColDefaultMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update a column default value rule
      tags:
      - rule
  /rule/default-value/{templateId}/{colDefaultMapRuleId}:
    delete:
      consumes:
      - application/json
      description: delete a column default value rule
      parameters:
      - description: colDefaultMapRuleId
        in: path
        name: colDefaultMapRuleId
        required: true
        type: integer
      - description: templateId
        in: path
        name: templateId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteColDefaultMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete a column default value rule
      tags:
      - rule
  /rule/default-value/list:
    post:
      consumes:
      - application/json
      description: list column default value rules by template id
      parameters:
      - description: list column default value rules by template id request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListColDefaultMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListColDefaultMapRulesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list column default value rules by template id
      tags:
      - rule
  /rule/object/:
    post:
      consumes:
      - application/json
      description: Create a object map rule
      parameters:
      - description: create a object map rule request parameter
        in: body
        name: objMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateObjMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateObjMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create a object map rule
      tags:
      - rule
  /rule/object/{objMapRuleId}:
    get:
      consumes:
      - application/json
      description: query object map rule
      parameters:
      - description: objMapRuleId
        in: path
        name: objMapRuleId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetObjMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query object map rule detail
      tags:
      - rule
  /rule/object/{objMapRuleId}/update:
    post:
      consumes:
      - application/json
      description: update a object map rule
      parameters:
      - description: objMapRuleId
        in: path
        name: objMapRuleId
        required: true
        type: integer
      - description: update a object map rule request parameter
        in: body
        name: objMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateObjMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateObjMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update a object map rule
      tags:
      - rule
  /rule/object/{templateId}/{objMapRuleId}:
    delete:
      consumes:
      - application/json
      description: delete a object map rule
      parameters:
      - description: objMapRuleId
        in: path
        name: objMapRuleId
        required: true
        type: integer
      - description: templateId
        in: path
        name: templateId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteObjMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete a object map rule
      tags:
      - rule
  /rule/object/list:
    post:
      consumes:
      - application/json
      description: list object map rules
      parameters:
      - description: list object map rules by template id request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListObjMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListObjMapRulesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list object map rules
      tags:
      - rule
  /rule/sql/:
    post:
      consumes:
      - application/json
      description: Create sql map rule
      parameters:
      - description: create sql map rule request parameter
        in: body
        name: sqlMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateSqlMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateSqlMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create sql map rule
      tags:
      - rule
  /rule/sql/{sqlMapRuleId}:
    get:
      consumes:
      - application/json
      description: query sql map rule by id
      parameters:
      - description: sqlMapRuleId
        in: path
        name: sqlMapRuleId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSqlMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query sql map rule detail by id
      tags:
      - rule
  /rule/sql/{sqlMapRuleId}/update:
    post:
      consumes:
      - application/json
      description: update sql map rule
      parameters:
      - description: sqlMapRuleId
        in: path
        name: sqlMapRuleId
        required: true
        type: integer
      - description: update sql map rule request parameter
        in: body
        name: sqlMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateSqlMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateSqlMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update sql map rule
      tags:
      - rule
  /rule/sql/{templateId}/{sqlMapRuleId}:
    delete:
      consumes:
      - application/json
      description: delete sql map rule
      parameters:
      - description: sqlMapRuleId
        in: path
        name: sqlMapRuleId
        required: true
        type: integer
      - description: templateId
        in: path
        name: templateId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteSqlMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete sql map rule
      tags:
      - rule
  /rule/sql/list:
    post:
      consumes:
      - application/json
      description: list sql map rules by template id
      parameters:
      - description: list object map rules by template id request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListSqlMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListSqlMapRulesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list sql map rules by template id
      tags:
      - rule
  /rule/table-column:
    post:
      consumes:
      - application/json
      description: Create table column map rule
      parameters:
      - description: create table column map rule request parameter
        in: body
        name: tabColMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTabColMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTabColMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create table column map rule
      tags:
      - rule
  /rule/table-column/{tabColMapRuleId}:
    get:
      consumes:
      - application/json
      description: query a table column map rule detail by id
      parameters:
      - description: tabColMapRuleId
        in: path
        name: tabColMapRuleId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTabColMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a table column map rule detail by id
      tags:
      - rule
  /rule/table-column/{tabColMapRuleId}/update:
    post:
      consumes:
      - application/json
      description: update a table column map rule
      parameters:
      - description: tabColMapRuleId
        in: path
        name: tabColMapRuleId
        required: true
        type: integer
      - description: update a table column map rule request parameter
        in: body
        name: tabColMapRule
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTabColMapRuleReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTabColMapRuleResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update a table column map rule
      tags:
      - rule
  /rule/table-column/{templateId}/{tabColMapRuleId}:
    delete:
      consumes:
      - application/json
      description: delete a table column map rule by id
      parameters:
      - description: tabColMapRuleId
        in: path
        name: tabColMapRuleId
        required: true
        type: integer
      - description: templateId
        in: path
        name: templateId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteTabColMapRuleByIdResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: delete a table column map rule by id
      tags:
      - rule
  /rule/table-column/list:
    post:
      consumes:
      - application/json
      description: list table column map rules by template id
      parameters:
      - description: list table column map rules by template id request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListTabColMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListTabColMapRulesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list table column map rules by template id
      tags:
      - rule
  /sql-analyzer/{channelId}/{taskId}/call-spa-collect:
    post:
      consumes:
      - application/json
      description: call and exec dbms job , callType 0=all, 1=当前SQL任务, 2=历史SQL任务
      parameters:
      - description: empty
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.CallSpaCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CallSpaCollectResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: call and exec dbms job
      tags:
      - sqlanalyzer
  /sql-analyzer/{channelId}/{taskId}/configuration:
    post:
      consumes:
      - application/json
      description: get sqlanalyzer configuration
      parameters:
      - description: empty
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.GetConfigurationReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetConfigurationResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get sqlanalyzer configuration
      tags:
      - sqlanalyzer
  /sql-analyzer/{channelId}/{taskId}/start-spa-collect:
    post:
      consumes:
      - application/json
      description: start dbms job ,which job id is contains in task comment
      parameters:
      - description: empty
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StartSpaCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StartSpaCollectResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: start dbms job
      tags:
      - sqlanalyzer
  /sql-analyzer/{channelId}/{taskId}/start-sqlfile-replay:
    post:
      consumes:
      - application/json
      description: start sql replay by file
      parameters:
      - description: empty
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StartSQLFileReplayReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StartSQLFileReplayResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: start sql replay by file
      tags:
      - sqlanalyzer
  /sql-analyzer/{channelId}/{taskId}/stop-spa-collect:
    post:
      consumes:
      - application/json
      description: stop dbms job ,which job id is contains in task comment
      parameters:
      - description: empty
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StopSpaCollectReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StopSpaCollectResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: stop dbms job
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}:
    get:
      consumes:
      - application/json
      description: list sql analyzer task , taskModeInt = [ 0,1 ], which 0 means LOCAL,
        1 means 非LOCAL
      parameters:
      - in: formData
        name: taskModeInt
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListSQLAnalyzeTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list sql analyzer task
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/{sqlanalyzeId}:
    post:
      consumes:
      - application/json
      description: update sql analyzer task
      parameters:
      - description: update sql analyze task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateSQLAnalyzeTaskReq'
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: sqlanalyzeId
        in: path
        name: sqlanalyzeId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateSQLAnalyzeTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update sql analyzer task
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/execute:
    post:
      consumes:
      - application/json
      description: execute sql analyzer env deploy task,  taskModeInt = [ 0,1 ], which
        0 means LOCAL, 1 means 非LOCAL, runMode = [ 1,2 ], 1 means execute(ignore success
        task), 2 means re-execute(all task status)
      parameters:
      - description: execute sql analyze task request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.ExecuteSQLAnalyzeEnvDeployTaskReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ExecuteSQLAnalyzeEnvDeployTaskResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: execute sql analyzer env deploy task
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/jobs:
    get:
      consumes:
      - application/json
      description: list sql analyzer task's jobs
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListSQLAnalyzeTaskJobsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list sql analyzer task' jobs
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/sqlset/statement-count:
    get:
      consumes:
      - application/json
      description: get sqlsets
      parameters:
      - in: formData
        name: sourceType
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSQLSetsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get sqlsets
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/sqlset/statements:
    get:
      consumes:
      - application/json
      description: get sqlset statements
      parameters:
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - in: formData
        name: schemaName
        type: string
      - in: formData
        name: sourceType
        type: integer
      - in: formData
        name: sqlSetName
        type: string
      - in: formData
        name: sqlSetOwner
        type: string
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: sqlset name
        in: query
        name: sqlSetName
        required: true
        type: string
      - description: sqlset owner
        in: query
        name: sqlSetOwner
        required: true
        type: string
      - description: schema name
        in: query
        name: schemaName
        required: true
        type: string
      - description: pageSize
        in: query
        name: pageSize
        required: true
        type: integer
      - description: page
        in: query
        name: page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSQLSetStatementsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get sqlset statements
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/sqlsets:
    get:
      consumes:
      - application/json
      description: get sqlsets
      parameters:
      - in: formData
        name: sourceType
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSQLSetsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get sqlsets
      tags:
      - sqlanalyzer
  /sql-analyzer/analyze-task/{taskId}/task-deploy-status:
    get:
      consumes:
      - application/json
      description: get sql analyzer task's deployment status
      parameters:
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskDeployStatusResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get sql analyzer task's deployment status
      tags:
      - sqlanalyzer
  /sql-analyzer/report/download:
    post:
      consumes:
      - application/json
      description: Download SqlAnalyzer Report
      parameters:
      - description: download sql report by schema
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DownloadSqlReportBySchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadSqlReportBySchemaResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download SqlAnalyzer Report
      tags:
      - sqlanalyzer
  /sql-analyzer/report/downloadzip:
    post:
      consumes:
      - application/json
      description: Download SqlAnalyzer Report
      parameters:
      - description: download sql report by schema
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.DownloadSqlReportBySchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DownloadSqlReportBySchemaResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Download SqlAnalyzer Report
      tags:
      - sqlanalyzer
  /sql-analyzer/results/{taskId}/history:
    get:
      consumes:
      - application/json
      description: Get audit trail of user operations on SQL execution results
      parameters:
      - description: get sql result history request
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.GetSqlResultHistoryReq'
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - description: specific SQL execution ID
        in: query
        name: sqlExecId
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSqlResultHistoryResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Get SQL execution result operation history
      tags:
      - sqlanalyzer
  /sql-analyzer/results/{taskId}/paginated:
    post:
      consumes:
      - application/json
      description: Get paginated list of SQL execution results with filtering by status
        and user operation status
      parameters:
      - in: formData
        minimum: 1
        name: page
        required: true
        type: integer
      - in: formData
        minimum: 1
        name: pageSize
        required: true
        type: integer
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: schemas
        type: array
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: statusList
        type: array
      - in: formData
        name: taskId
        type: integer
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: userOperateStatus
        type: array
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - collectionFormat: csv
        description: schema names filter
        in: query
        items:
          type: string
        name: schemas
        type: array
      - collectionFormat: csv
        description: execution status filter
        in: query
        items:
          type: string
        name: statusList
        type: array
      - collectionFormat: csv
        description: user operation status filter
        in: query
        items:
          type: string
        name: userOperateStatus
        type: array
      - description: page number
        in: query
        name: page
        required: true
        type: integer
      - description: page size
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListSqlResultsWithPaginationResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: List SQL execution results with pagination
      tags:
      - sqlanalyzer
  /sql-analyzer/results/{taskId}/statistics:
    post:
      consumes:
      - application/json
      description: Get statistics including counts by execution status and user operation
        status
      parameters:
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: schemas
        type: array
      - in: formData
        name: taskId
        required: true
        type: integer
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      - collectionFormat: csv
        description: schema names filter
        in: query
        items:
          type: string
        name: schemas
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetSqlResultStatisticsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Get SQL execution result statistics
      tags:
      - sqlanalyzer
  /sql-analyzer/results/{taskId}/user-operations:
    post:
      consumes:
      - application/json
      description: Batch update user operation status (normal/ignored/resolved) for
        multiple SQL execution results
      parameters:
      - description: update sql result user operation request
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateSqlResultUserOperationReq'
      - description: taskId
        in: path
        name: taskId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateSqlResultUserOperationResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Update user operation status for SQL execution results
      tags:
      - sqlanalyzer
  /template/:
    post:
      consumes:
      - application/json
      description: Create template
      parameters:
      - description: create template request parameter
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTemplateInfoReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTemplateInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create template
      tags:
      - template
  /template/{templateId}/:
    get:
      consumes:
      - application/json
      description: query a template detail
      parameters:
      - description: templateId
        in: path
        name: templateId
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DetailTemplateInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a template detail
      tags:
      - template
  /template/{templateId}/update:
    post:
      consumes:
      - application/json
      description: Update a template
      parameters:
      - description: templateId
        in: path
        name: templateId
        required: true
        type: integer
      - description: update template request parameter
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTemplateInfoReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTemplateInfoResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Update a template
      tags:
      - template
  /template/cust/create:
    post:
      consumes:
      - application/json
      description: Create TabcolCustMapRules
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplateIds
        required: true
        schema:
          $ref: '#/definitions/message.DeleteTabcolCustMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create TabcolCustMapRules
      tags:
      - template
  /template/cust/delete:
    post:
      consumes:
      - application/json
      description: Delete TabcolCustMapRules by id
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplateIds
        required: true
        schema:
          $ref: '#/definitions/message.DeleteTabcolCustMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Delete TabcolCustMapRules by id
      tags:
      - template
  /template/cust/list:
    post:
      consumes:
      - application/json
      description: get TabcolCustMapRules list
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplateIds
        required: true
        schema:
          $ref: '#/definitions/message.GetTabcolCustMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get TabcolCustMapRules list
      tags:
      - template
  /template/cust/update:
    post:
      consumes:
      - application/json
      description: Update TabcolCustMapRules
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplateIds
        required: true
        schema:
          $ref: '#/definitions/message.DeleteTabcolCustMapRulesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CommonResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Update TabcolCustMapRules
      tags:
      - template
  /template/delete:
    post:
      consumes:
      - application/json
      description: batch delete templateInfos
      parameters:
      - description: batch delete templateInfos request parameter
        in: body
        name: templateIds
        required: true
        schema:
          $ref: '#/definitions/message.BatchDeleteTemplateInfosReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchDeleteTemplateInfosResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch delete templateInfos
      tags:
      - template
  /template/list:
    post:
      consumes:
      - application/json
      description: list template by type
      parameters:
      - description: list template by type request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListTemplatesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListTemplatesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list template by type
      tags:
      - template
  /template/param-detail:
    post:
      consumes:
      - application/json
      description: Create template param detail
      parameters:
      - description: create template param detail request parameter
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTemplateParamDetailReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTemplateParamDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Create template param detail
      tags:
      - template
  /template/param-detail/{taskparamTemplateId}/list:
    get:
      consumes:
      - application/json
      description: list template param detail
      parameters:
      - description: list template param detail request
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListTemplateParamDetailsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.ListTemplateParamDetailsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list template param detail
      tags:
      - template
  /template/param-detail/delete:
    post:
      consumes:
      - application/json
      description: batch delete template params by ids
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplateIds
        required: true
        schema:
          $ref: '#/definitions/message.BatchDeleteTemplateParamDetailsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchDeleteTemplateParamDetailsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch delete template params by ids
      tags:
      - template
  /template/param-detail/update:
    post:
      consumes:
      - application/json
      description: Update a template
      parameters:
      - description: update template request parameter
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTemplateParamDetailReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTemplateParamDetailResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Update a template
      tags:
      - template
  /template/task-param:
    post:
      consumes:
      - application/json
      description: create task param template
      parameters:
      - description: create task param template request parameter
        in: body
        name: taskParamTemplate
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTaskParamTemplateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTaskParamTemplateResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: create task param template
      tags:
      - template
  /template/task-param/{taskparamTemplateId}:
    get:
      consumes:
      - application/json
      description: get task param template by id
      parameters:
      - description: get task param templates request parameter
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.GetTaskParamTemplateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTaskParamTemplateResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get task param template by id
      tags:
      - template
  /template/task-param/delete:
    post:
      consumes:
      - application/json
      description: batch delete task param templates by ids
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplateIds
        required: true
        schema:
          $ref: '#/definitions/message.BatchDeleteTaskParamTemplatesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.BatchDeleteTaskParamTemplatesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch delete task param templates by ids
      tags:
      - template
  /template/task-param/list:
    post:
      consumes:
      - application/json
      description: list task param templates
      parameters:
      - description: list task param templates request parameter
        in: body
        name: page
        required: true
        schema:
          $ref: '#/definitions/message.ListTaskParamTemplatesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.ListTaskParamTemplatesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: list task param templates
      tags:
      - template
  /template/task-param/update:
    post:
      consumes:
      - application/json
      description: update task param template
      parameters:
      - description: update task param template request parameter
        in: body
        name: taskParamTemplate
        required: true
        schema:
          $ref: '#/definitions/message.CreateOrUpdateTaskParamTemplateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.CreateOrUpdateTaskParamTemplateResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update task param template
      tags:
      - template
  /tidb-statistics/detail-table/channel-schema-table/schema:
    post:
      consumes:
      - application/json
      description: query detail tables and channel schema by schema
      parameters:
      - description: tidb statistics detail and channel schema tables parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StatisticsSchemaStateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.StatisticsSchemaStateResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query detail and channel schema tables by schema
      tags:
      - statistics
  /tidb-statistics/detail-table/conflict:
    post:
      consumes:
      - application/json
      description: query conflict tables by schema
      parameters:
      - description: tidb statistics conflict tables parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StatisticsTaskTableDuplReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.StatisticsTaskTableDuplResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query conflict tables by schema
      tags:
      - statistics
  /tidb-statistics/detail-table/delete:
    delete:
      consumes:
      - application/json
      description: batch delete task tables by table name
      parameters:
      - description: batch delete task tables  request parameter
        in: body
        name: taskIds
        required: true
        schema:
          $ref: '#/definitions/message.DeleteStatsTaskConflictTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.DeleteStatsTaskConflictTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: batch delete task tables by table name
      tags:
      - statistics
  /tidb-statistics/detail-table/get-count:
    post:
      consumes:
      - application/json
      description: get tidb tables count
      parameters:
      - description: get tidb tables count request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.GetTidbStatsTableCountReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.GetTidbStatsTableCountResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: get tidb tables count
      tags:
      - statistics
  /tidb-statistics/detail-table/init:
    post:
      consumes:
      - application/json
      description: init tidb statistics tables
      parameters:
      - description: init tidb statistics tables request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.InitTidbStatisticsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.InitTidbStatisticsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: init tidb statistics tables
      tags:
      - statistics
  /tidb-statistics/detail-table/schema:
    post:
      consumes:
      - application/json
      description: query detail tables by schema
      parameters:
      - description: tidb statistics detail tables parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StatisticsTablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.ResultWithPage'
            - properties:
                data:
                  $ref: '#/definitions/message.StatisticsTablesResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query detail tables by schema
      tags:
      - statistics
  /tidb-statistics/detail-table/update:
    post:
      consumes:
      - application/json
      description: update tidb statistics tables
      parameters:
      - description: update tidb statistics tables request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.UpdateTidbStatisticsReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.UpdateTidbStatisticsResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: update tidb statistics tables
      tags:
      - statistics
  /tidb-statistics/summary/schema:
    post:
      consumes:
      - application/json
      description: query a tidb statisticsk schema summary
      parameters:
      - description: get tidb statistics schema request parameter
        in: body
        name: objReq
        required: true
        schema:
          $ref: '#/definitions/message.StatisticsSummarySchemaReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.StatisticsSummarySchemaResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: query a tidb statistics schema summary
      tags:
      - statistics
  /user/currentUser:
    get:
      consumes:
      - application/json
      description: get current user information
      produces:
      - application/json
      responses: {}
      summary: get current user information
      tags:
      - user
  /user/login:
    post:
      consumes:
      - application/json
      description: login
      parameters:
      - description: login info
        in: body
        name: loginInfo
        required: true
        schema:
          $ref: '#/definitions/message.LoginReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/message.LoginResp'
      summary: login
      tags:
      - user
  /version:
    get:
      consumes:
      - application/json
      description: Get Version
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/message.CommonResult'
            - properties:
                data:
                  $ref: '#/definitions/message.VersionResp'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/message.CommonResult'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/message.CommonResult'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/message.CommonResult'
      summary: Get Version
      tags:
      - license
swagger: "2.0"
