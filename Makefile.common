PROJECT=tims
GOPATH ?= $(shell go env GOPATH)
P=8

# Get systms info, include OS and ARCH
OS ?= $(shell uname -s)
ifeq ($(OS), Darwin)
    OS := osx
else ifeq ($(OS), darwin)
    OS := osx
else ifeq ($(OS), Linux)
    OS := linux
endif

ARCH ?= $(shell uname -m)
ifeq ($(ARCH), arm64)
    ARCH := x86_64
else ifeq ($(ARCH), amd64)
    ARCH := x86_64
else ifeq ($(ARCH), aarch64)
    ARCH := aarch_64
endif

# Ensure GOPATH is set before running build process.
ifeq "$(GOPATH)" ""
  $(error Please set the environment variable GOPATH before running `make`)
endif
FAIL_ON_STDOUT := awk '{ print } END { if (NR > 0) { exit 1 } }'

CURDIR := $(shell pwd)
TEST_DIR := /tmp/tidb_ms_test
CODECOV_BASH := /tmp/codecov_bash`
EXTERN_PATH := $(addsuffix /bin,$(subst :,/bin:,$(GOPATH)))
export PATH := $(EXTERN_PATH):$(PATH)

GO              := GO111MODULE=on go
GOBUILD         := $(GO) build $(BUILD_FLAG) -tags codes
#GOBUILDCOVERAGE := GOPATH=$(GOPATH) cd tidb-server; $(GO) test -coverpkg="../..." -c .
GOTEST          := $(GO) test -p $(P)
OVERALLS        := GO111MODULE=on overalls
STATICCHECK     := GO111MODULE=on staticcheck
TIMS_EDITION    ?= Enterprise

# Ensure TIMS_EDITION is set to Community or Enterprise before running build process.
ifneq "$(TIMS_EDITION)" "Enterprise"
  $(error Please set the correct environment variable TIMS_EDITION before running `make`)
endif

# use 'make <target> PREFIX=/path/for/prefix' to overwrite the PREFIX value
ifeq (${PREFIX},)
    PREFIX := /usr/local
endif

LINUX     := "Linux"
MAC       := "Darwin"

PACKAGE_LIST  := go list ./...| grep -vE "docs|common|message|test/mock|deprecated"
PACKAGES  ?= $$($(PACKAGE_LIST))
LINT_PACKAGE_LIST  := go list ./...| grep -vE "docs|proto|test"
LINT_PACKAGES  ?= $$($(LINT_PACKAGE_LIST))
#PACKAGE_DIRECTORIES := $(PACKAGE_LIST) | sed 's|github.com/pingcap/$(PROJECT)||'
PACKAGE_DIRECTORIES := $(PACKAGE_LIST)
FILES     := $$(find $$($(PACKAGE_DIRECTORIES)) -name "*.go")

FAILPOINT_ENABLE  := $$(find $$PWD/ -type d | grep -vE "(\.git)" | xargs bin/failpoint-ctl enable)
FAILPOINT_DISABLE := $$(find $$PWD/ -type d | grep -vE "(\.git)" | xargs bin/failpoint-ctl disable)

LDFLAGS += -X "gitee.com/pingcap_enterprise/tms/util/versioninfo.ReleaseVersion=$(shell git describe --tags --dirty --always)"
LDFLAGS += -X "gitee.com/pingcap_enterprise/tms/util/versioninfo.BuildTS=$(shell date -u '+%Y-%m-%d %H:%M:%S')"
LDFLAGS += -X "gitee.com/pingcap_enterprise/tms/util/versioninfo.GitHash=$(shell git rev-parse HEAD)"
LDFLAGS += -X "gitee.com/pingcap_enterprise/tms/util/versioninfo.GitBranch=$(shell git rev-parse --abbrev-ref HEAD)"


RACE_FLAG =
ifeq ("$(WITH_RACE)", "1")
	RACE_FLAG = -race
	GOBUILD   = GOPATH=$(GOPATH) $(GO) build
endif

CHECK_FLAG =
ifeq ("$(WITH_CHECK)", "1")
	CHECK_FLAG = $(TEST_LDFLAGS)
endif
